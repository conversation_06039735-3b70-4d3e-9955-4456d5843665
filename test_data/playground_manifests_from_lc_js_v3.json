[{"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "temperature": 1, "stream_usage": true}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "top_p": 0.3, "frequency_penalty": 0.5, "presence_penalty": 0.2, "n": 2, "logit_bias": {"0": 0}, "stop": ["bar"], "user": "x", "timeout": 2, "stream_usage": true, "max_tokens": 10, "logprobs": true, "top_logprobs": 2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "a": "b"}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "organization": "baz", "timeout": 2, "max_retries": 2, "default_headers": {"foo": "bar"}, "default_query": {"a": "b"}, "base_url": "https://foo.com/", "stream_usage": true}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "azure_openai", "AzureChatOpenAI"], "name": "AzureChatOpenAI", "kwargs": {"model": "gpt-4o-2024-08-06", "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "azure_endpoint": "https://langchain-ai.openai.azure.com/", "deployment_name": "gpt-4o-2024-08-06", "temperature": 1}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "temperature": 1, "max_tokens": 2048}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "temperature": 0.3, "max_tokens": 10, "top_k": 2, "top_p": 0.3, "max_retries": 3, "stop_sequences": ["bar"], "stream_usage": true, "anthropic_api_url": "https://foo.com/", "invocation_kwargs": {"a": "b"}, "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "timeout": 2}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "vertexai", "ChatVertexAI"], "name": "ChatVertexAI", "kwargs": {"model": "gemini-1.5-pro", "platform_type": "gcp", "temperature": 0.7, "max_tokens": 1024, "credentials": {"lc": 1, "type": "secret", "id": ["GOOGLE_VERTEX_AI_WEB_CREDENTIALS"]}}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_google_genai", "chat_models", "ChatGoogleGenerativeAI"], "name": "ChatGoogleGenerativeAI", "kwargs": {"model": "gemini-1.5-pro", "google_api_key": {"lc": 1, "type": "secret", "id": ["GOOGLE_API_KEY"]}}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "fireworks", "ChatFireworks"], "name": "ChatFireworks", "kwargs": {"model": "accounts/fireworks/models/llama-v3p1-405b-instruct", "stream_usage": false, "fireworks_api_key": {"lc": 1, "type": "secret", "id": ["FIREWORKS_API_KEY"]}, "model_name": "accounts/fireworks/models/llama-v3p1-405b-instruct", "temperature": 1}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "mist<PERSON><PERSON>", "ChatMistralAI"], "name": "ChatMistralAI", "kwargs": {"model": "mistral-large-latest", "mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "temperature": 0.7}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_groq", "chat_models", "ChatGroq"], "name": "ChatGroq", "kwargs": {"model": "llama-3.1-70b-versatile", "api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}, "temperature": 0.7, "groq_api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "BedrockChat", "kwargs": {"model_id": "anthropic.claude-3-5-sonnet-********-v1:0", "region_name": "west", "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "BedrockChat", "kwargs": {"model_id": "anthropic.claude-3-5-sonnet-********-v1:0", "max_tokens": 2, "temperature": 0.2, "region_name": "west", "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "top_p": 0.3, "frequency_penalty": 0.5, "presence_penalty": 0.2, "n": 2, "logit_bias": {"0": 0}, "stop": ["bar"], "user": "x", "timeout": 2, "stream_usage": true, "max_tokens": 10, "logprobs": true, "top_logprobs": 2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "a": "b"}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "temperature": 0.3, "max_tokens": 10, "top_k": 2, "top_p": 0.3, "max_retries": 3, "stop_sequences": ["bar"], "stream_usage": true, "anthropic_api_url": "https://foo.com/", "invocation_kwargs": {"a": "b"}, "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "timeout": 2}}, "options": {}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_groq", "chat_models", "ChatGroq"], "name": "ChatGroq", "kwargs": {"model": "llama-3.1-70b-versatile", "api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}, "temperature": 0.7, "groq_api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "top_p": 0.3, "frequency_penalty": 0.5, "presence_penalty": 0.2, "n": 2, "logit_bias": {"0": 0}, "stop": ["bar"], "user": "x", "timeout": 2, "stream_usage": true, "max_tokens": 10, "logprobs": true, "top_logprobs": 2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "a": "b"}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "top_p": 0.3, "frequency_penalty": 0.5, "presence_penalty": 0.2, "n": 2, "logit_bias": {"0": 0}, "stop": ["bar"], "user": "x", "timeout": 2, "stream_usage": true, "max_tokens": 10, "logprobs": true, "top_logprobs": 2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "a": "b"}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "BedrockChat", "kwargs": {"model_id": "anthropic.claude-3-5-sonnet-********-v1:0", "region_name": "west", "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "temperature": 1, "stream_usage": true}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "azure_openai", "AzureChatOpenAI"], "name": "AzureChatOpenAI", "kwargs": {"model": "gpt-4o-2024-08-06", "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "azure_endpoint": "https://langchain-ai.openai.azure.com/", "deployment_name": "gpt-4o-2024-08-06", "temperature": 1}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_groq", "chat_models", "ChatGroq"], "name": "ChatGroq", "kwargs": {"model": "llama-3.1-70b-versatile", "api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}, "temperature": 0.7, "groq_api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "temperature": 0.3, "max_tokens": 10, "top_k": 2, "top_p": 0.3, "max_retries": 3, "stop_sequences": ["bar"], "stream_usage": true, "anthropic_api_url": "https://foo.com/", "invocation_kwargs": {"a": "b"}, "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "timeout": 2}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "temperature": 0.3, "max_tokens": 10, "top_k": 2, "top_p": 0.3, "max_retries": 3, "stop_sequences": ["bar"], "stream_usage": true, "anthropic_api_url": "https://foo.com/", "invocation_kwargs": {"a": "b"}, "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "timeout": 2}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "top_p": 0.3, "frequency_penalty": 0.5, "presence_penalty": 0.2, "n": 2, "logit_bias": {"0": 0}, "stop": ["bar"], "user": "x", "timeout": 2, "stream_usage": true, "max_tokens": 10, "logprobs": true, "top_logprobs": 2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "a": "b"}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "top_p": 0.3, "frequency_penalty": 0.5, "presence_penalty": 0.2, "n": 2, "logit_bias": {"0": 0}, "stop": ["bar"], "user": "x", "timeout": 2, "stream_usage": true, "max_tokens": 10, "logprobs": true, "top_logprobs": 2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "a": "b"}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "BedrockChat", "kwargs": {"model_id": "anthropic.claude-3-5-sonnet-********-v1:0", "max_tokens": 2, "temperature": 0.2, "region_name": "west", "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "organization": "baz", "timeout": 2, "max_retries": 2, "default_headers": {"foo": "bar"}, "default_query": {"a": "b"}, "base_url": "https://foo.com/", "stream_usage": true}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "BedrockChat", "kwargs": {"model_id": "anthropic.claude-3-5-sonnet-********-v1:0", "region_name": "west", "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "azure_openai", "AzureChatOpenAI"], "name": "AzureChatOpenAI", "kwargs": {"model": "gpt-4o-2024-08-06", "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "azure_endpoint": "https://langchain-ai.openai.azure.com/", "deployment_name": "gpt-4o-2024-08-06", "temperature": 1}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "temperature": 0.3, "max_tokens": 10, "top_k": 2, "top_p": 0.3, "max_retries": 3, "stop_sequences": ["bar"], "stream_usage": true, "anthropic_api_url": "https://foo.com/", "invocation_kwargs": {"a": "b"}, "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "timeout": 2}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "temperature": 0.3, "max_tokens": 10, "top_k": 2, "top_p": 0.3, "max_retries": 3, "stop_sequences": ["bar"], "stream_usage": true, "anthropic_api_url": "https://foo.com/", "invocation_kwargs": {"a": "b"}, "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "timeout": 2}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "temperature": 1, "max_tokens": 2048}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "temperature": 0.2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "organization": "baz", "timeout": 2, "max_retries": 2, "default_headers": {"foo": "bar"}, "default_query": {"a": "b"}, "base_url": "https://foo.com/", "stream_usage": true}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "BedrockChat", "kwargs": {"model_id": "anthropic.claude-3-5-sonnet-********-v1:0", "max_tokens": 2, "temperature": 0.2, "region_name": "west", "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "mist<PERSON><PERSON>", "ChatMistralAI"], "name": "ChatMistralAI", "kwargs": {"model": "mistral-large-latest", "mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "temperature": 0.7}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "fireworks", "ChatFireworks"], "name": "ChatFireworks", "kwargs": {"model": "accounts/fireworks/models/llama-v3p1-405b-instruct", "stream_usage": false, "fireworks_api_key": {"lc": 1, "type": "secret", "id": ["FIREWORKS_API_KEY"]}, "model_name": "accounts/fireworks/models/llama-v3p1-405b-instruct", "temperature": 1}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"model": "gpt-4o", "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "temperature": 1, "stream_usage": true}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-********", "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "temperature": 1, "max_tokens": 2048}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "fireworks", "ChatFireworks"], "name": "ChatFireworks", "kwargs": {"model": "accounts/fireworks/models/llama-v3p1-405b-instruct", "stream_usage": false, "fireworks_api_key": {"lc": 1, "type": "secret", "id": ["FIREWORKS_API_KEY"]}, "model_name": "accounts/fireworks/models/llama-v3p1-405b-instruct", "temperature": 1}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "mist<PERSON><PERSON>", "ChatMistralAI"], "name": "ChatMistralAI", "kwargs": {"model": "mistral-large-latest", "mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "temperature": 0.7}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}], "tool_choice": "add"}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_google_genai", "chat_models", "ChatGoogleGenerativeAI"], "name": "ChatGoogleGenerativeAI", "kwargs": {"model": "gemini-1.5-pro", "google_api_key": {"lc": 1, "type": "secret", "id": ["GOOGLE_API_KEY"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "vertexai", "ChatVertexAI"], "name": "ChatVertexAI", "kwargs": {"model": "gemini-1.5-pro", "platform_type": "gcp", "temperature": 0.7, "max_tokens": 1024, "credentials": {"lc": 1, "type": "secret", "id": ["GOOGLE_VERTEX_AI_WEB_CREDENTIALS"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "vertexai", "ChatVertexAI"], "name": "ChatVertexAI", "kwargs": {"model": "gemini-1.5-pro", "platform_type": "gcp", "temperature": 0.7, "max_tokens": 1024, "credentials": {"lc": 1, "type": "secret", "id": ["GOOGLE_VERTEX_AI_WEB_CREDENTIALS"]}}}, "options": {}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "first int"}, "b": {"type": "number", "description": "second int"}}, "required": ["a", "b"]}}}], "tool_choice": "any"}]