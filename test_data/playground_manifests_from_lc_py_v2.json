[{"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"temperature": 0.7, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "openai_proxy": "", "max_retries": 2, "n": 1, "model": "gpt-4o", "stream_usage": true}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"temperature": 0.7, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "openai_proxy": "", "max_retries": 2, "n": 1, "model": "gpt-4o", "stream_usage": true}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"temperature": 0.2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "openai_api_base": "https://foo.com/", "openai_organization": "great org", "openai_proxy": "", "request_timeout": 2, "max_retries": 3, "presence_penalty": 0.2, "frequency_penalty": 0.5, "seed": 0, "logprobs": true, "top_logprobs": 2, "logit_bias": {"0": 0}, "n": 2, "top_p": 0.3, "max_tokens": 10, "tiktoken_model_name": "foo", "default_headers": {"a": "b"}, "stop": ["bar"], "extra_body": {"a": "b"}, "include_response_headers": true, "stream_usage": true, "a": "b", "model": "gpt-4o"}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "a": "b", "openai_api_base": "https://foo.com/", "openai_organization": "great org", "openai_proxy": "", "request_timeout": 2, "max_retries": 3, "presence_penalty": 0.2, "frequency_penalty": 0.5, "seed": 0, "logprobs": true, "top_logprobs": 2, "logit_bias": {"0": 0}, "n": 2, "top_p": 0.3, "tiktoken_model_name": "foo", "default_headers": {"a": "b"}, "extra_body": {"a": "b"}, "include_response_headers": true, "temperature": 0.2, "model": "gpt-4o", "max_tokens": 10, "stream_usage": true}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "openai", "ChatOpenAI"], "name": "ChatOpenAI", "kwargs": {"temperature": 0.2, "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]}, "openai_api_base": "https://foo.com/", "openai_organization": "great org", "openai_proxy": "", "request_timeout": 2, "max_retries": 3, "presence_penalty": 0.2, "frequency_penalty": 0.5, "seed": 0, "logprobs": true, "top_logprobs": 2, "logit_bias": {"0": 0}, "n": 2, "top_p": 0.3, "max_tokens": 10, "tiktoken_model_name": "foo", "default_headers": {"a": "b"}, "stop": ["bar"], "extra_body": {"a": "b"}, "include_response_headers": true, "stream_usage": true, "a": "b", "model": "gpt-4o"}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "azure_openai", "AzureChatOpenAI"], "name": "AzureChatOpenAI", "kwargs": {"model_name": "gpt-4o-2024-08-06", "temperature": 0.7, "openai_api_key": {"lc": 1, "type": "secret", "id": ["AZURE_OPENAI_API_KEY"]}, "max_retries": 2, "n": 1, "azure_endpoint": "https://langchain-ai.openai.azure.com/", "deployment_name": "gpt-4o-2024-08-06", "openai_api_version": "2024-08-01-preview", "openai_api_type": "azure", "validate_base_url": true}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "azure_openai", "AzureChatOpenAI"], "name": "AzureChatOpenAI", "kwargs": {"model_name": "gpt-4o-2024-08-06", "temperature": 0.7, "openai_api_key": {"lc": 1, "type": "secret", "id": ["AZURE_OPENAI_API_KEY"]}, "max_retries": 2, "n": 1, "azure_endpoint": "https://langchain-ai.openai.azure.com/", "deployment_name": "gpt-4o-2024-08-06", "openai_api_version": "2024-08-01-preview", "openai_api_type": "azure", "validate_base_url": true}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "azure_openai", "AzureChatOpenAI"], "name": "AzureChatOpenAI", "kwargs": {"model_name": "gpt-4o-2024-08-06", "temperature": 0.7, "openai_api_key": {"lc": 1, "type": "secret", "id": ["AZURE_OPENAI_API_KEY"]}, "request_timeout": 2, "max_retries": 3, "presence_penalty": 0.2, "frequency_penalty": 0.5, "seed": 0, "logprobs": true, "top_logprobs": 2, "logit_bias": {"0": 0}, "n": 2, "top_p": 0.3, "max_tokens": 10, "tiktoken_model_name": "foo", "default_headers": {"a": "b"}, "stop": ["bar"], "extra_body": {"a": "b"}, "include_response_headers": true, "azure_endpoint": "https://langchain-ai.openai.azure.com/", "deployment_name": "gpt-4o-2024-08-06", "openai_api_version": "2024-08-01-preview", "openai_api_type": "azure", "validate_base_url": true, "a": "b"}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "azure_openai", "AzureChatOpenAI"], "name": "AzureChatOpenAI", "kwargs": {"model_name": "gpt-4o-2024-08-06", "temperature": 0.7, "openai_api_key": {"lc": 1, "type": "secret", "id": ["AZURE_OPENAI_API_KEY"]}, "request_timeout": 2, "max_retries": 3, "presence_penalty": 0.2, "frequency_penalty": 0.5, "seed": 0, "logprobs": true, "top_logprobs": 2, "logit_bias": {"0": 0}, "n": 2, "top_p": 0.3, "max_tokens": 10, "tiktoken_model_name": "foo", "default_headers": {"a": "b"}, "stop": ["bar"], "extra_body": {"a": "b"}, "include_response_headers": true, "azure_endpoint": "https://langchain-ai.openai.azure.com/", "deployment_name": "gpt-4o-2024-08-06", "openai_api_version": "2024-08-01-preview", "openai_api_type": "azure", "validate_base_url": true, "a": "b"}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-20240620", "max_tokens": 1024, "max_retries": 2, "anthropic_api_url": "https://api.anthropic.com", "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "stream_usage": true}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-20240620", "max_tokens": 1024, "max_retries": 2, "anthropic_api_url": "https://api.anthropic.com", "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "stream_usage": true}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-20240620", "max_tokens": 10, "temperature": 0.2, "top_k": 2, "top_p": 0.3, "default_request_timeout": 2, "max_retries": 3, "stop_sequences": ["bar"], "anthropic_api_url": "https://foo.com/", "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "default_headers": {"a": "b"}, "stream_usage": true, "a": "b"}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "name": "ChatAnthropic", "kwargs": {"model": "claude-3-5-sonnet-20240620", "max_tokens": 10, "temperature": 0.2, "top_k": 2, "top_p": 0.3, "default_request_timeout": 2, "max_retries": 3, "stop_sequences": ["bar"], "anthropic_api_url": "https://foo.com/", "anthropic_api_key": {"lc": 1, "type": "secret", "id": ["ANTHROPIC_API_KEY"]}, "default_headers": {"a": "b"}, "stream_usage": true, "a": "b"}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "vertexai", "ChatVertexAI"], "name": "ChatVertexAI", "kwargs": {"project": "langchain-dev", "location": "us-central1", "request_parallelism": 5, "max_retries": 6, "default_metadata": [], "temperature": 0.2, "top_p": 0.3, "top_k": 2, "n": 2, "model_family": "2", "response_mime_type": "application/json", "model": "gemini-1.5-pro", "max_tokens": 10, "credentials": {"lc": 1, "type": "secret", "id": ["GOOGLE_VERTEX_AI_WEB_CREDENTIALS"]}}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "vertexai", "ChatVertexAI"], "name": "ChatVertexAI", "kwargs": {"project": "langchain-dev", "location": "us-central1", "request_parallelism": 5, "max_retries": 6, "default_metadata": [], "temperature": 0.2, "top_p": 0.3, "top_k": 2, "n": 2, "model_family": "2", "response_mime_type": "application/json", "model": "gemini-1.5-pro", "max_tokens": 10, "credentials": {"lc": 1, "type": "secret", "id": ["GOOGLE_VERTEX_AI_WEB_CREDENTIALS"]}}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_google_genai", "chat_models", "ChatGoogleGenerativeAI"], "name": "ChatGoogleGenerativeAI", "kwargs": {"model": "models/gemini-1.5-pro", "temperature": 0.2, "top_p": 0.3, "top_k": 2, "max_output_tokens": 10, "n": 1, "max_retries": 3, "timeout": 2, "transport": "rest", "additional_headers": {"a": "b"}, "safety_settings": {}, "default_metadata": [["a", "b"]], "max_tokens": 10, "google_api_key": {"lc": 1, "type": "secret", "id": ["GOOGLE_API_KEY"]}}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_google_genai", "chat_models", "ChatGoogleGenerativeAI"], "name": "ChatGoogleGenerativeAI", "kwargs": {"model": "models/gemini-1.5-pro", "temperature": 0.2, "top_p": 0.3, "top_k": 2, "max_output_tokens": 10, "n": 1, "max_retries": 3, "timeout": 2, "transport": "rest", "additional_headers": {"a": "b"}, "safety_settings": {}, "default_metadata": [["a", "b"]], "max_tokens": 10, "google_api_key": {"lc": 1, "type": "secret", "id": ["GOOGLE_API_KEY"]}}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "fireworks", "ChatFireworks"], "name": "ChatFireworks", "kwargs": {"model_name": "accounts/fireworks/models/llama-v3p1-405b-instruct", "fireworks_api_key": {"lc": 1, "type": "secret", "id": ["FIREWORKS_API_KEY"]}, "n": 1, "temperature": 0}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "fireworks", "ChatFireworks"], "name": "ChatFireworks", "kwargs": {"model_name": "accounts/fireworks/models/llama-v3p1-405b-instruct", "fireworks_api_key": {"lc": 1, "type": "secret", "id": ["FIREWORKS_API_KEY"]}, "n": 1, "temperature": 0}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "fireworks", "ChatFireworks"], "name": "ChatFireworks", "kwargs": {"model_name": "accounts/fireworks/models/llama-v3p1-405b-instruct", "temperature": 0.2, "stop": ["bar"], "fireworks_api_key": {"lc": 1, "type": "secret", "id": ["FIREWORKS_API_KEY"]}, "fireworks_api_base": "https://foo.com/", "request_timeout": 2, "n": 2, "max_tokens": 10, "max_retries": 3, "a": "b"}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "fireworks", "ChatFireworks"], "name": "ChatFireworks", "kwargs": {"model_name": "accounts/fireworks/models/llama-v3p1-405b-instruct", "temperature": 0.2, "stop": ["bar"], "fireworks_api_key": {"lc": 1, "type": "secret", "id": ["FIREWORKS_API_KEY"]}, "fireworks_api_base": "https://foo.com/", "request_timeout": 2, "n": 2, "max_tokens": 10, "max_retries": 3, "a": "b"}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "mist<PERSON><PERSON>", "ChatMistralAI"], "name": "ChatMistralAI", "kwargs": {"mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "endpoint": "https://api.mistral.ai/v1", "max_retries": 5, "timeout": 120, "max_concurrent_requests": 64, "model": "mistral-large-latest", "temperature": 0.7, "top_p": 1}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "mist<PERSON><PERSON>", "ChatMistralAI"], "name": "ChatMistralAI", "kwargs": {"mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "endpoint": "https://api.mistral.ai/v1", "max_retries": 5, "timeout": 120, "max_concurrent_requests": 64, "model": "mistral-large-latest", "temperature": 0.7, "top_p": 1}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "mist<PERSON><PERSON>", "ChatMistralAI"], "name": "ChatMistralAI", "kwargs": {"mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "endpoint": "https://foo.com/", "max_retries": 3, "timeout": 2, "max_concurrent_requests": 2, "model": "mistral-large-latest", "temperature": 0.2, "max_tokens": 10, "top_p": 0.3, "random_seed": 0, "safe_mode": true}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "mist<PERSON><PERSON>", "ChatMistralAI"], "name": "ChatMistralAI", "kwargs": {"mistral_api_key": {"lc": 1, "type": "secret", "id": ["MISTRAL_API_KEY"]}, "endpoint": "https://foo.com/", "max_retries": 3, "timeout": 2, "max_concurrent_requests": 2, "model": "mistral-large-latest", "temperature": 0.2, "max_tokens": 10, "top_p": 0.3, "random_seed": 0, "safe_mode": true}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_groq", "chat_models", "ChatGroq"], "name": "ChatGroq", "kwargs": {"temperature": 0.7, "groq_api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}, "max_retries": 2, "n": 1, "model": "llama-3.1-70b-versatile"}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_groq", "chat_models", "ChatGroq"], "name": "ChatGroq", "kwargs": {"temperature": 0.7, "groq_api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}, "max_retries": 2, "n": 1, "model": "llama-3.1-70b-versatile"}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_groq", "chat_models", "ChatGroq"], "name": "ChatGroq", "kwargs": {"temperature": 0.2, "stop": ["bar"], "groq_api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}, "groq_api_base": "https://foo.com/", "request_timeout": 2, "max_retries": 3, "n": 2, "max_tokens": 10, "default_headers": {"a": "b"}, "default_query": {}, "a": "b", "model": "llama-3.1-70b-versatile"}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain_groq", "chat_models", "ChatGroq"], "name": "ChatGroq", "kwargs": {"temperature": 0.2, "stop": ["bar"], "groq_api_key": {"lc": 1, "type": "secret", "id": ["GROQ_API_KEY"]}, "groq_api_base": "https://foo.com/", "request_timeout": 2, "max_retries": 3, "n": 2, "max_tokens": 10, "default_headers": {"a": "b"}, "default_query": {}, "a": "b", "model": "llama-3.1-70b-versatile"}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "ChatBedrock", "kwargs": {"region_name": "us-east-1", "model_id": "anthropic.claude-3-5-sonnet-20240620-v1:0", "provider_stop_sequence_key_name_map": {"anthropic": "stop_sequences", "amazon": "stopSequences", "ai21": "stop_sequences", "cohere": "stop_sequences", "mistral": "stop_sequences"}, "provider_stop_reason_key_map": {"anthropic": "stop_reason", "amazon": "completionReason", "ai21": "finishReason", "cohere": "finish_reason", "mistral": "stop_reason"}, "guardrails": {"trace": null, "guardrailIdentifier": null, "guardrailVersion": null}, "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "ChatBedrock", "kwargs": {"region_name": "us-east-1", "model_id": "anthropic.claude-3-5-sonnet-20240620-v1:0", "provider_stop_sequence_key_name_map": {"anthropic": "stop_sequences", "amazon": "stopSequences", "ai21": "stop_sequences", "cohere": "stop_sequences", "mistral": "stop_sequences"}, "provider_stop_reason_key_map": {"anthropic": "stop_reason", "amazon": "completionReason", "ai21": "finishReason", "cohere": "finish_reason", "mistral": "stop_reason"}, "guardrails": {"trace": null, "guardrailIdentifier": null, "guardrailVersion": null}, "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "ChatBedrock", "kwargs": {"region_name": "west", "config": {}, "provider": "anthropic", "model_id": "anthropic.claude-3-5-sonnet-20240620-v1:0", "endpoint_url": "https://foo.com/", "provider_stop_sequence_key_name_map": {"anthropic": "stop_sequences", "amazon": "stopSequences", "ai21": "stop_sequences", "cohere": "stop_sequences", "mistral": "stop_sequences"}, "provider_stop_reason_key_map": {"anthropic": "stop_reason", "amazon": "completionReason", "ai21": "finishReason", "cohere": "finish_reason", "mistral": "stop_reason"}, "guardrails": {"trace": null, "guardrailIdentifier": null, "guardrailVersion": null}, "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {"stop": null}}, {"manifest": {"lc": 1, "type": "constructor", "id": ["langchain", "chat_models", "bedrock", "ChatBedrock"], "name": "ChatBedrock", "kwargs": {"region_name": "west", "config": {}, "provider": "anthropic", "model_id": "anthropic.claude-3-5-sonnet-20240620-v1:0", "endpoint_url": "https://foo.com/", "provider_stop_sequence_key_name_map": {"anthropic": "stop_sequences", "amazon": "stopSequences", "ai21": "stop_sequences", "cohere": "stop_sequences", "mistral": "stop_sequences"}, "provider_stop_reason_key_map": {"anthropic": "stop_reason", "amazon": "completionReason", "ai21": "finishReason", "cohere": "finish_reason", "mistral": "stop_reason"}, "guardrails": {"trace": null, "guardrailIdentifier": null, "guardrailVersion": null}, "aws_access_key_id": {"lc": 1, "type": "secret", "id": ["AWS_ACCESS_KEY_ID"]}, "aws_secret_access_key": {"lc": 1, "type": "secret", "id": ["AWS_SECRET_ACCESS_KEY"]}, "aws_session_token": {"lc": 1, "type": "secret", "id": ["AWS_SESSION_TOKEN"]}}}, "options": {"stop": null}, "tools": [{"type": "function", "function": {"name": "add", "description": "Return a + b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int.", "type": "integer"}, "b": {"description": "second int.", "type": "integer"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply", "description": "Return a * b.", "parameters": {"type": "object", "properties": {"a": {"description": "first int", "type": "integer"}, "b": {"description": "second int", "type": "integer"}}, "required": ["a", "b"]}}}]}]