"""Clickhouse initialization and configuration."""

import asyncio
import logging
import random
import re
import ssl
from contextlib import asynccontextmanager
from enum import Enum
from typing import Any, AsyncGenerator, Dict, List, NamedTuple, Optional

import httpx
import structlog
from aiochclient import ChClient, ChClientError
from aiochclient.records import Record
from ddtrace import Span, tracer  # noqa: E402
from ddtrace.constants import SPAN_KIND  # noqa: E402
from ddtrace.ext import SpanKind  # noqa: E402
from httpcore import AsyncIOBackend
from lc_config.settings import shared_settings as settings
from lc_logging.audit_logs import audit_prefixed_query

logger = logging.getLogger(__name__)

SANITIZED_LB_CONTEXT_REGEX = re.compile(r"[^a-zA-Z0-9/]")


class ClickhouseClient(Enum):
    DEFAULT = "default"
    DEFAULT_SLOW = "default_slow"
    IN_APP_ANALYTICS = "in_app_analytics"
    IN_APP_ANALYTICS_SLOW = "in_app_analytics_slow"
    INTERNAL_ANALYTICS_SLOW = "internal_analytics_slow"
    IN_APP_STATS = "in_app_stats"
    IN_APP_STATS_SLOW = "in_app_stats_slow"

    @classmethod
    def from_string(cls, client_type_str: str) -> "ClickhouseClient":
        try:
            return cls(client_type_str)
        except ValueError:
            valid_values = [e.value for e in cls]
            raise ValueError(
                f"Invalid client type: '{client_type_str}'. Valid values are: {', '.join(valid_values)}"
            )


clickhouse_client_settings = {
    ClickhouseClient.DEFAULT: {
        "host": settings.CLICKHOUSE_HOST,
        "port": settings.CLICKHOUSE_PORT,
    },
    ClickhouseClient.DEFAULT_SLOW: {
        "host": settings.CLICKHOUSE_HOST,
        "port": settings.CLICKHOUSE_PORT,
        "max_connections": settings.CLICKHOUSE_SLOW_MAX_CONNECTIONS,
        "max_keeplive_connections": settings.CLICKHOUSE_SLOW_MAX_KEEPLIVE_CONNECTIONS,
        "read_timeout": settings.CLICKHOUSE_SLOW_READ_TIMEOUT,
        "write_timeout": settings.CLICKHOUSE_SLOW_WRITE_TIMEOUT,
    },
    ClickhouseClient.IN_APP_ANALYTICS: {
        "host": settings.CLICKHOUSE_IN_APP_ANALYTICS_HOST,
        "port": settings.CLICKHOUSE_IN_APP_ANALYTICS_PORT,
    },
    ClickhouseClient.IN_APP_ANALYTICS_SLOW: {
        "host": settings.CLICKHOUSE_IN_APP_ANALYTICS_HOST,
        "port": settings.CLICKHOUSE_IN_APP_ANALYTICS_PORT,
        "max_connections": settings.CLICKHOUSE_SLOW_MAX_CONNECTIONS,
        "max_keeplive_connections": settings.CLICKHOUSE_SLOW_MAX_KEEPLIVE_CONNECTIONS,
        "read_timeout": settings.CLICKHOUSE_SLOW_READ_TIMEOUT,
        "write_timeout": settings.CLICKHOUSE_SLOW_WRITE_TIMEOUT,
    },
    ClickhouseClient.INTERNAL_ANALYTICS_SLOW: {
        "host": settings.CLICKHOUSE_INTERNAL_ANALYTICS_HOST,
        "port": settings.CLICKHOUSE_INTERNAL_ANALYTICS_PORT,
        "max_connections": settings.CLICKHOUSE_SLOW_MAX_CONNECTIONS,
        "max_keeplive_connections": settings.CLICKHOUSE_SLOW_MAX_KEEPLIVE_CONNECTIONS,
        "read_timeout": settings.CLICKHOUSE_SLOW_READ_TIMEOUT,
        "write_timeout": settings.CLICKHOUSE_SLOW_WRITE_TIMEOUT,
    },
    ClickhouseClient.IN_APP_STATS: {
        "host": settings.CLICKHOUSE_IN_APP_STATS_HOST,
        "port": settings.CLICKHOUSE_IN_APP_STATS_PORT,
    },
    ClickhouseClient.IN_APP_STATS_SLOW: {
        "host": settings.CLICKHOUSE_IN_APP_STATS_HOST,
        "port": settings.CLICKHOUSE_IN_APP_STATS_PORT,
        "max_connections": settings.CLICKHOUSE_SLOW_MAX_CONNECTIONS,
        "max_keeplive_connections": settings.CLICKHOUSE_SLOW_MAX_KEEPLIVE_CONNECTIONS,
        "read_timeout": settings.CLICKHOUSE_SLOW_READ_TIMEOUT,
        "write_timeout": settings.CLICKHOUSE_SLOW_WRITE_TIMEOUT,
    },
}

RELATED_MATERIALIZED_VIEWS = {
    "runs": [
        "runs_metadata_kv",
        "runs_inputs_kv",
        "runs_outputs_kv",
        "runs_reference_example_id",
        "runs_run_id_v2",
        "runs_tags",
        "runs_token_counts",
        "runs_trace_id",
        "runs_sessions_agg_hourly",
        "runs_topk_hourly_agg",
    ],
    "feedbacks": [
        "feedbacks_rmt",
        "feedbacks_rmt_id",
    ],
}

RUNS_MATERIALIZED_VIEWS_RUN_ID = [
    "runs_metadata_kv",  # run_id
    "runs_inputs_kv",  # run_id
    "runs_outputs_kv",  # run_id
    "runs_tags",  # run_id
]

RUNS_MATERIALIZED_VIEWS_ID = [
    "runs_reference_example_id",  # id
    "runs_run_id_v2",  # id
    "runs_token_counts",  # id
    # This list does not include runs_trace_id because that join table is used for
    # trace deletes and thus can't be deleted immediately due to the async nature
    # of clickhouse deletes
]

# Do not retry these errors
CH_NON_RETRY_ERRORS = {
    # This one is a transient error due to replicas down, don't want to retry
    "Code: 341. DB::Exception: Mutation is not finished because some replicas are inactive right now",
    # This one is a memory limit error and not likely to be fixed by retry
    "Code: 241. DB::Exception: Memory limit (total) exceeded",
}


def construct_clickhouse_url(clickhouse_host: str, clickhouse_port: int) -> str:
    """Construct the clickhouse url."""
    scheme = "https" if settings.CLICKHOUSE_TLS else "http"
    return f"{scheme}://{clickhouse_host}:{clickhouse_port}"


_clients: Dict[ClickhouseClient, ChClient] = {}


def create_client(client_type: ClickhouseClient) -> ChClient:
    """Create the clickhouse http client."""
    if client_type not in clickhouse_client_settings:
        raise ValueError(f"Unknown clickhouse client_type: {client_type}")

    if client_type == ClickhouseClient.IN_APP_STATS and not settings.FF_CH_IN_APP_STATS:
        client_type = ClickhouseClient.IN_APP_ANALYTICS

    if (
        client_type == ClickhouseClient.IN_APP_STATS_SLOW
        and not settings.FF_CH_IN_APP_STATS
    ):
        client_type = ClickhouseClient.IN_APP_ANALYTICS_SLOW

    if (
        not settings.FF_CLICKHOUSE_USE_MULTISERVICE
        or (
            client_type == ClickhouseClient.IN_APP_ANALYTICS
            and not settings.FF_CH_IN_APP_ANALYTICS
        )
        or (
            client_type == ClickhouseClient.INTERNAL_ANALYTICS_SLOW
            and not settings.FF_CH_INTERNAL_ANALYTICS
        )
    ):
        client_type = (
            ClickhouseClient.DEFAULT_SLOW
            if "SLOW" in str(client_type)
            else ClickhouseClient.DEFAULT
        )

    # get the settings for the client
    clickhouse_host = clickhouse_client_settings[client_type]["host"]
    clickhouse_port = clickhouse_client_settings[client_type]["port"]

    # configure connection settings
    max_connections = clickhouse_client_settings[client_type].get(
        "max_connections", settings.CLICKHOUSE_MAX_CONNECTIONS
    )
    max_keepalive_connections = clickhouse_client_settings[client_type].get(
        "max_keepalive_connections", settings.CLICKHOUSE_MAX_KEEPLIVE_CONNECTIONS
    )
    read_timeout = clickhouse_client_settings[client_type].get(
        "read_timeout", settings.CLICKHOUSE_READ_TIMEOUT
    )
    write_timeout = clickhouse_client_settings[client_type].get(
        "write_timeout", settings.CLICKHOUSE_WRITE_TIMEOUT
    )

    transport = httpx.AsyncHTTPTransport(
        verify=ssl.create_default_context(),
        retries=5,  # this applies only to ConnectError, ConnectTimeout
        limits=httpx.Limits(
            max_connections=max_connections,
            max_keepalive_connections=max_keepalive_connections,
            keepalive_expiry=settings.CLICKHOUSE_KEEPALIVE_EXPIRY,
        ),
    )
    transport._pool._network_backend = AsyncIOBackend()
    return ChClient(
        session=httpx.AsyncClient(
            transport=transport,
            timeout=httpx.Timeout(
                read=read_timeout,
                write=write_timeout,
                connect=settings.CLICKHOUSE_CONNECT_TIMEOUT,
                pool=settings.CLICKHOUSE_POOL_TIMEOUT,
            ),
        ),
        url=construct_clickhouse_url(clickhouse_host, clickhouse_port),
        user=settings.CLICKHOUSE_USER,
        password=settings.CLICKHOUSE_PASSWORD,
        database=settings.CLICKHOUSE_DB,
    )


async def initialize_clickhouse_client(
    client_type: ClickhouseClient = ClickhouseClient.DEFAULT,
) -> None:
    """Initialize the clickhouse http client."""
    # close the clients if they already exist
    await close_clickhouse_client(client_type)

    global _clients
    _clients[client_type] = create_client(client_type)

    instrumented_client = InstrumentedChClient(_clients[client_type])
    try:
        await instrumented_client.execute("ping", "SELECT 1")
    except Exception as e:
        logger.exception("Error initializing clickhouse client")
        await close_clickhouse_client(client_type)
        raise e


async def close_clickhouse_client(
    client_type: ClickhouseClient = ClickhouseClient.DEFAULT,
) -> None:
    """Close the clickhouse http client."""
    global _clients
    try:
        if client_type not in clickhouse_client_settings:
            raise ValueError(f"Unknown clickhouse client_type: {client_type}")
        if client_type in _clients:
            await _clients[client_type].close()
    except NameError:
        pass


class InstrumentedChClient:
    def __init__(self, ch: ChClient) -> None:
        self.ch = ch

    def _add_settings_to_query(
        self,
        query: str,
        with_timeout: bool,
        query_timeout_when_enabled: int,
        max_threads: Optional[int],
    ) -> str:
        if with_timeout or max_threads is not None:
            settings_str = (
                " SETTINGS "
                if "settings" not in query.lower().split("\n")[-1]
                else ", "
            )
            query += settings_str

            if with_timeout:
                query += f"max_execution_time={query_timeout_when_enabled} "

            if max_threads is not None:
                query += f"{', ' if with_timeout else ''}max_threads={max_threads} "

        return query

    async def execute(
        self,
        name: str,
        query: str,
        *args: Any,
        params: Optional[dict[str, Any]] = None,
    ) -> None:
        structlog.contextvars.bind_contextvars(query_name=name)
        prefixed_query = audit_prefixed_query(
            query, override_enabled=True, allow_short_context=True
        )
        if not settings.DATADOG_ENABLED:
            return await self.ch.execute(prefixed_query, *args, params=params)

        with tracer.trace(
            "clickhouse.request",
            service="clickhouse",
            resource="execute: " + name,
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            span.set_tag_str("clickhouse.query", query)
            _set_context_tags(span, structlog.contextvars.get_contextvars(), params)

            return await self.ch.execute(prefixed_query, *args, params=params)

    async def fetch(
        self,
        name: str,
        query: str,
        *args: Any,
        params: Optional[dict[str, Any]] = None,
        with_timeout: bool = False,
        query_timeout_when_enabled: int = settings.CLICKHOUSE_FE_TIMEOUT,
        max_threads: Optional[int] = None,
        decode: bool = True,
        json: bool = False,
    ) -> List[Record]:
        structlog.contextvars.bind_contextvars(query_name=name)
        prefixed_query = audit_prefixed_query(
            query, override_enabled=True, allow_short_context=True
        )
        prefixed_query = self._add_settings_to_query(
            query=prefixed_query,
            with_timeout=with_timeout,
            query_timeout_when_enabled=query_timeout_when_enabled,
            max_threads=max_threads,
        )

        if not settings.DATADOG_ENABLED:
            return await self.ch.fetch(
                prefixed_query, *args, params=params, decode=decode, json=json
            )

        with tracer.trace(
            "clickhouse.request",
            service="clickhouse",
            resource="fetch: " + name,
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            span.set_tag_str("clickhouse.query", query)
            _set_context_tags(span, structlog.contextvars.get_contextvars(), params)

            return await self.ch.fetch(prefixed_query, *args, params=params)

    async def fetchval(
        self,
        name: str,
        query: str,
        *args: Any,
        params: Optional[dict[str, Any]] = None,
    ) -> Any:
        structlog.contextvars.bind_contextvars(query_name=name)
        prefixed_query = audit_prefixed_query(
            query, override_enabled=True, allow_short_context=True
        )
        if not settings.DATADOG_ENABLED:
            return await self.ch.fetchval(prefixed_query, *args, params=params)

        with tracer.trace(
            "clickhouse.request",
            service="clickhouse",
            resource="fetchval: " + name,
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            span.set_tag_str("clickhouse.query", query)
            _set_context_tags(span, structlog.contextvars.get_contextvars(), params)

            return await self.ch.fetchval(prefixed_query, *args, params=params)

    async def fetchrow(
        self,
        name: str,
        query: str,
        *args: Any,
        params: Optional[dict[str, Any]] = None,
        with_timeout: bool = False,
        query_timeout_when_enabled: int = settings.CLICKHOUSE_FE_TIMEOUT,
        max_threads: Optional[int] = None,
    ) -> Record:
        structlog.contextvars.bind_contextvars(query_name=name)
        prefixed_query = audit_prefixed_query(
            query, override_enabled=True, allow_short_context=True
        )
        prefixed_query = self._add_settings_to_query(
            query=prefixed_query,
            with_timeout=with_timeout,
            query_timeout_when_enabled=query_timeout_when_enabled,
            max_threads=max_threads,
        )
        if not settings.DATADOG_ENABLED:
            return await self.ch.fetchrow(prefixed_query, *args, params=params)

        with tracer.trace(
            "clickhouse.request",
            service="clickhouse",
            resource="fetchrow: " + name,
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            span.set_tag_str("clickhouse.query", prefixed_query)
            _set_context_tags(span, structlog.contextvars.get_contextvars(), params)

            return await self.ch.fetchrow(prefixed_query, *args, params=params)


@asynccontextmanager
async def _clickhouse_client(
    client_type: ClickhouseClient,
) -> AsyncGenerator[ChClient, None]:
    """Get the clickhouse http client."""
    # pytest does something funny with event loops,
    # so we can't use a global pool for tests
    if settings.LANGCHAIN_ENV == "local_test":
        client = create_client(client_type)
        try:
            yield client
        finally:
            await client.close()
    else:
        try:
            if client_type not in clickhouse_client_settings:
                raise ValueError(f"Unknown clickhouse client_type: {client_type}")
            if (
                client_type in _clients
                and not _clients[client_type]._http_client._session.is_closed
            ):
                found = True
            else:
                found = False
        except NameError:
            found = False
        if found:
            yield _clients[client_type]
        else:
            await initialize_clickhouse_client(client_type)
            yield _clients[client_type]


@asynccontextmanager
async def clickhouse_client(
    client_type: ClickhouseClient = ClickhouseClient.DEFAULT,
) -> AsyncGenerator[InstrumentedChClient, None]:
    """Get the instrumented clickhouse client."""
    async with _clickhouse_client(client_type) as ch:
        yield InstrumentedChClient(ch)


class ExecuteRequest(NamedTuple):
    """Execute request."""

    name: str
    query: str
    args: Optional[list[list[Any]]] = None
    params: Optional[dict[str, Any]] = None

    async def coro(self, ch: InstrumentedChClient) -> None:
        return await ch.execute(
            self.name, self.query, *(self.args or []), params=self.params
        )


async def multi_execute_single(
    *requests: ExecuteRequest,
    max_attempts: int = 5,
    use_slow_client: bool = False,
    client_type: ClickhouseClient = ClickhouseClient.DEFAULT,
) -> None:
    if use_slow_client:
        client_type = ClickhouseClient.DEFAULT_SLOW

    async with clickhouse_client(client_type) as ch:
        # schedule all queries for the first time
        tasks = {asyncio.create_task(req.coro(ch)): (req, 0) for req in requests}
        exceptions: list[Exception] = []
        # continue until all queries are done or max_attempts is reached for one
        while tasks:
            # wait for any query to finish
            done, _ = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            for task in done:
                # remove finished tasks from the list
                req, attempt = tasks.pop(task)
                try:
                    task.result()
                except (
                    ChClientError,
                    httpx.NetworkError,
                    httpx.PoolTimeout,
                    httpx.WriteTimeout,
                    httpx.ConnectTimeout,
                    httpx.RemoteProtocolError,
                ) as e:
                    logger.info(
                        f"Query failed (attempt {attempt + 1}): {req.query} with args {req.args} {req.params}",
                        exc_info=True,
                    )
                    if isinstance(e, ChClientError) and any(
                        error in str(e) for error in CH_NON_RETRY_ERRORS
                    ):
                        # Known transient clickhouse error, skipping
                        logger.warning("Known ch transient error, skipping: ")
                    # retry if we haven't reached max_attempts
                    elif attempt < max_attempts:
                        jitter = random.uniform(0, 1)
                        # 1st attempt will be 0 + jitter
                        timeout = 2**attempt - 1 + jitter
                        await asyncio.sleep(timeout)
                        tasks[asyncio.create_task(req.coro(ch))] = (req, attempt + 1)
                    else:
                        exceptions.append(e)
                except httpx.ReadTimeout as e:
                    exceptions.append(e)
                except Exception as e:
                    exceptions.append(e)
        if exceptions:
            raise ExceptionGroup("Failed multi_execute", exceptions)


def delete_materialized_view_queries(
    source_table: str,
    query: str,
    params: dict[str, Any],
    materialized_views: Optional[list[str]] = None,
) -> list[ExecuteRequest]:
    """Issue deletes for associated materialized views."""
    materialized_views = materialized_views or RELATED_MATERIALIZED_VIEWS.get(
        source_table
    )
    if not materialized_views:
        raise AssertionError(f"Materialized Views not found for {source_table}")
    if "{table}" not in query:
        raise AssertionError(f"Query {query} does not contain {{table}}")

    return [
        ExecuteRequest(
            f"delete_mv_{mv_table}",
            query.replace("{table}", mv_table),
            params=params,
        )
        for mv_table in materialized_views
    ]


def _set_context_tags(
    span: Span,
    contextvars: Optional[dict[str, Any]] = None,
    params: Optional[dict[str, Any]] = None,
):
    if not params:
        return

    try:
        # Used in audit
        if contextvars and contextvars.get("tenant_id"):
            span.set_tag_str("context.tenant_id", str(contextvars.get("tenant_id")))

        # Clickhouse query params
        tenant_id = params.get("tenant_id")
        session_id = params.get("session_id")
        session_ids = params.get("session_ids")
        trace_id = params.get("trace_id")

        if tenant_id:
            span.set_tag_str("clickhouse.tenant_id", str(tenant_id))
        if session_id:
            span.set_tag_str("clickhouse.session_id", str(session_id))
        elif session_ids:
            span.set_tag(
                "clickhouse.session_id", [str(session_id) for session_id in session_ids]
            )
        if trace_id:
            span.set_tag_str("clickhouse.trace_id", str(trace_id))

    except Exception:
        logger.exception("Error setting context tags")
