"""Database initialization and configuration."""

import logging
import string
from contextlib import asynccontextmanager
from typing import Any, Async<PERSON>enerator, Callable, NamedTuple

import asyncpg
import orjson
import structlog
from ddtrace import Span, tracer  # noqa: E402
from ddtrace.constants import SPAN_KIND  # noqa: E402
from ddtrace.ext import SpanKind  # noqa: E402
from google.cloud.sqlcommenter import generate_sql_comment
from lc_config.settings import SharedSettings
from lc_config.settings import shared_settings as settings
from lc_logging.audit_logs import audit_prefixed_query
from prometheus_client import Histogram

assert settings.SQLALCHEMY_DATABASE_URI is not None

logger = logging.getLogger(__name__)

POOL_STATS_BUCKETS = (1.0, 2.5, 5.0, 7.5, 10.0, 20.0, 40.0, 50.0, float("inf"))

METRIC_PG_POOL_IDLE = Histogram(
    "pg_pool_idle",
    "Number of idle connections in the pool.",
    [],
    namespace="langsmith",
    buckets=POOL_STATS_BUCKETS,
)

METRIC_PG_POOL_USED = Histogram(
    "pg_pool_used",
    "Number of used connections in the pool.",
    [],
    namespace="langsmith",
    buckets=POOL_STATS_BUCKETS,
)


def json_dumps(value):
    str_value = orjson.dumps(
        value, option=orjson.OPT_SERIALIZE_NUMPY | orjson.OPT_NON_STR_KEYS
    ).decode()
    # Remove unicode null characters, which are not valid in JSON
    return str_value.replace("\\u0000", "")


# ASYNCPG

_asyncpg_pool: asyncpg.Pool


class PydanticFriendlyRecord(asyncpg.Record):
    """Record class that allows accessing columns as attributes, needed for Pydantic."""

    def __getattr__(self, name):
        try:
            return self[name]
        except KeyError:
            raise AttributeError(name)


def log_query(query: asyncpg.connection.LoggedQuery) -> None:
    """Log a query and its parameters."""
    logger.debug(query)

    if "typeinfo_tree(" in query.query:
        logger.warning(
            "Detected asyncpg type instrospection query for types: %s. Query took %ims. Avoid this query by registering these data types in init_connection.",
            query.args,
            query.elapsed * 1000,
        )


DYNAMIC_CACHED_TYPES: list[dict[str, Any]] = []
"""Types created by migrations (eg. enums) are assigned a dynamic OID by Postgres.
Therefore we need to fetch them once by name on pool startup and cache them here,
to be used by every connection afterwards."""
NEEDED_DYNAMIC_TYPES: dict[str, dict[str, Any]] = {
    "citext": {
        "ns": "public",
        "name": "citext",
        "kind": "b",
        "basetype": None,
        "elemtype": 0,
        "elemdelim": None,
        "range_subtype": None,
        "attrtypoids": None,
        "attrnames": None,
        "depth": 0,
        "basetype_name": None,
        "elemtype_name": "-",
        "range_subtype_name": None,
    },
    "access_scope": {
        "ns": "public",
        "name": "auth_provider",
        "kind": "e",
        "basetype": None,
        "elemtype": 0,
        "elemdelim": None,
        "range_subtype": None,
        "attrtypoids": None,
        "attrnames": None,
        "depth": 0,
        "basetype_name": None,
        "elemtype_name": "-",
        "range_subtype_name": None,
    },
    "auth_provider": {
        "ns": "public",
        "name": "access_scope",
        "kind": "e",
        "basetype": None,
        "elemtype": 0,
        "elemdelim": None,
        "range_subtype": None,
        "attrtypoids": None,
        "attrnames": None,
        "depth": 0,
        "basetype_name": None,
        "elemtype_name": "-",
        "range_subtype_name": None,
    },
    "license_type": {
        "ns": "public",
        "name": "license_type",
        "kind": "e",
        "basetype": None,
        "elemtype": 0,
        "elemdelim": None,
        "range_subtype": None,
        "attrtypoids": None,
        "attrnames": None,
        "depth": 0,
        "basetype_name": None,
        "elemtype_name": "-",
        "range_subtype_name": None,
    },
    "resource_type": {
        "ns": "public",
        "name": "resource_type",
        "kind": "e",
        "basetype": None,
        "elemtype": 0,
        "elemdelim": None,
        "range_subtype": None,
        "attrtypoids": None,
        "attrnames": None,
        "depth": 0,
        "basetype_name": None,
        "elemtype_name": "-",
        "range_subtype_name": None,
    },
}


# Prefix all queries with query name and context variables if audit logging is enabled
class AuditLoggedConnection(asyncpg.Connection):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def cursor(self, query: str, *args, **kwargs):
        prefixed_query = audit_prefixed_query(query)
        return super().cursor(prefixed_query, *args, **kwargs)

    async def execute(self, query: str, *args, **kwargs):
        prefixed_query = audit_prefixed_query(query) if query != "ROLLBACK" else query
        return await super().execute(prefixed_query, *args, **kwargs)

    async def fetch(self, query: str, *args, **kwargs):
        prefixed_query = audit_prefixed_query(query)
        return await super().fetch(prefixed_query, *args, **kwargs)

    async def fetchrow(self, query: str, *args, **kwargs):
        prefixed_query = audit_prefixed_query(query)
        return await super().fetchrow(prefixed_query, *args, **kwargs)

    async def fetchval(self, query: str, *args, **kwargs):
        prefixed_query = audit_prefixed_query(query)
        return await super().fetchval(prefixed_query, *args, **kwargs)

    async def fetchmany(self, query: str, args, **kwargs):
        prefixed_query = audit_prefixed_query(query)
        return await super().fetchmany(prefixed_query, args, **kwargs)

    async def executemany(self, query: str, args, **kwargs):
        prefixed_query = audit_prefixed_query(query)
        return await super().executemany(prefixed_query, args, **kwargs)


def _set_context_tags(
    span: Span,
    contextvars: dict[str, Any],
) -> None:
    """Set context tags for a span."""
    if contextvars and contextvars.get("tenant_id"):
        span.set_tag_str("context.tenant_id", str(contextvars.get("tenant_id")))


async def init_connection(conn: AuditLoggedConnection) -> None:
    """You can add here any code that should be run once per connection."""

    # Add JSONB support
    conn.get_settings().add_python_codec(
        3802,
        "jsonb",
        "pg_catalog",
        [],
        "scalar",
        encoder=json_dumps,
        decoder=orjson.loads,
        format="text",
    )

    # Add JSON support
    conn.get_settings().add_python_codec(
        114,
        "json",
        "pg_catalog",
        [],
        "scalar",
        encoder=json_dumps,
        decoder=lambda x: x,
        format="text",
    )

    # This avoids asyncpg running a recursive query to get these types
    # every time a connection is created
    conn.get_settings().register_data_types(
        [
            {
                "oid": 2951,
                "ns": "pg_catalog",
                "name": "_uuid",
                "kind": "b",
                "basetype": None,
                "elemtype": 2950,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "uuid",
                "range_subtype_name": None,
            },
            {
                "oid": 199,
                "ns": "pg_catalog",
                "name": "_json",
                "kind": "b",
                "basetype": None,
                "elemtype": 114,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "json",
                "range_subtype_name": None,
            },
            {
                "oid": 3807,
                "ns": "pg_catalog",
                "name": "_jsonb",
                "kind": "b",
                "basetype": None,
                "elemtype": 3802,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "jsonb",
                "range_subtype_name": None,
            },
            {
                "oid": 1185,
                "ns": "pg_catalog",
                "name": "_timestamptz",
                "kind": "b",
                "basetype": None,
                "elemtype": 1184,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "timestamptz",
                "range_subtype_name": None,
            },
            {
                "oid": 1000,
                "ns": "pg_catalog",
                "name": "_bool",
                "kind": "b",
                "basetype": None,
                "elemtype": 16,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "bool",
                "range_subtype_name": None,
            },
            {
                "oid": 1001,
                "ns": "pg_catalog",
                "name": "_bytea",
                "kind": "b",
                "basetype": None,
                "elemtype": 17,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "bytea",
                "range_subtype_name": None,
            },
            {
                "oid": 1231,
                "ns": "pg_catalog",
                "name": "_numeric",
                "kind": "b",
                "basetype": None,
                "elemtype": 1700,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "numeric",
                "range_subtype_name": None,
            },
            {
                "oid": 1015,
                "ns": "pg_catalog",
                "name": "_varchar",
                "kind": "b",
                "basetype": None,
                "elemtype": 1043,
                "elemdelim": ",",
                "range_subtype": None,
                "attrtypoids": None,
                "attrnames": None,
                "depth": 0,
                "basetype_name": None,
                "elemtype_name": "character varying",
                "range_subtype_name": None,
            },
            *DYNAMIC_CACHED_TYPES,
        ]
    )

    # Set empty reset query to avoid asyncpg issuing an extra query
    # every time a connection is released back to the pool.
    # If we start using any of the following we can re-enable it:
    # - advisory locks
    #   https://www.postgresql.org/docs/current/explicit-locking.html#ADVISORY-LOCKS
    # - listen/notify
    #   https://www.postgresql.org/docs/current/sql-unlisten.html
    # - server-side cursors
    #   https://www.postgresql.org/docs/current/sql-close.html
    # - connection-level settings
    #   https://www.postgresql.org/docs/current/sql-reset.html
    conn._reset_query = ""

    if NEEDED_DYNAMIC_TYPES:
        # make a local copy
        needs = NEEDED_DYNAMIC_TYPES.copy()
        # clear so we don't fetch them again
        NEEDED_DYNAMIC_TYPES.clear()
        # fetch oids for dynamic types
        types = await conn.fetch(
            """SELECT t.oid, t.typname
        FROM pg_catalog.pg_type AS t
        INNER JOIN pg_catalog.pg_namespace AS n ON t.typnamespace = n.oid
        WHERE t.typname = ANY($1::text[]) AND n.nspname = $2""",
            list(needs.keys()),
            settings.POSTGRES_SCHEMA,
        )
        # cache them
        for type_ in types:
            DYNAMIC_CACHED_TYPES.append(
                {**needs[type_["typname"]], "oid": type_["oid"]}
            )
        # register them
        if DYNAMIC_CACHED_TYPES:
            conn.get_settings().register_data_types(DYNAMIC_CACHED_TYPES)

    if settings.LANGCHAIN_ENV in ("local_dev", "local_test"):
        # Enable query logging in local dev
        conn.add_query_logger(log_query)


async def create_asyncpg_pool(db_settings: SharedSettings = settings) -> asyncpg.Pool:
    return await asyncpg.create_pool(
        db_settings.ASYNCPG_DATABASE_URI,
        record_class=PydanticFriendlyRecord,
        init=init_connection,
        min_size=db_settings.ASYNCPG_POOL_MIN_SIZE,
        max_size=db_settings.ASYNCPG_POOL_MAX_SIZE,
        connection_class=AuditLoggedConnection,
        statement_cache_size=0
        if db_settings.PGBOUNCER_DATABASE_URI
        else db_settings.ASYNCPG_STATEMENT_CACHE_SIZE,
        max_inactive_connection_lifetime=db_settings.ASYNCPG_POOL_MAX_INACTIVE_CONNECTION_LIFETIME_SEC,
        # ====================== CONNECT KWARGS ==============================
        # Connect kwargs that can be used to set properties on every connection
        # See https://magicstack.github.io/asyncpg/current/api/index.html#connection-pools
        # for info on connect_kwargs and
        # https://magicstack.github.io/asyncpg/current/api/index.html#asyncpg.connection.connect
        # for available options set on each connection
        # ====================================================================
        command_timeout=db_settings.POSTGRES_DEFAULT_STATEMENT_TIMEOUT_SEC,
        # startup parameters are not supported in transaction mode
        # https://github.com/pgbouncer/pgbouncer/issues/89
        # https://github.com/pgbouncer/pgbouncer/pull/73
        server_settings=None
        if db_settings.PGBOUNCER_DATABASE_URI
        else {
            "lock_timeout": str(db_settings.ASYNCPG_LOCK_TIMEOUT_MS),
            "search_path": db_settings.POSTGRES_SCHEMA,
        },
    )


async def create_global_asyncpg_pool() -> None:
    global _asyncpg_pool
    _asyncpg_pool = await create_asyncpg_pool()


async def close_global_asyncpg_pool() -> None:
    global _asyncpg_pool
    await _asyncpg_pool.close()


@asynccontextmanager
async def asyncpg_pool() -> AsyncGenerator[asyncpg.Pool, None]:
    # pytest does something funny with event loops,
    # so we can't use a global pool for tests
    if settings.LANGCHAIN_ENV == "local_test":
        pool = await create_asyncpg_pool()
        try:
            yield pool
        finally:
            await pool.close()
    else:
        global _asyncpg_pool
        try:
            _asyncpg_pool
            found = True
        except NameError:
            found = False
        if found:
            yield _asyncpg_pool
        else:
            await create_global_asyncpg_pool()
            yield _asyncpg_pool


async def _acquire(pool: asyncpg.Pool) -> asyncpg.Connection:
    if settings.DATADOG_ENABLED:
        with tracer.trace(
            "postgres.connection_acquire",
            service="postgres",
            resource="acquire",
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            _set_context_tags(span, structlog.contextvars.get_contextvars())
            return await pool.acquire(timeout=settings.ASYNCPG_POOL_TIMEOUT_SEC)

    return await pool.acquire(timeout=settings.ASYNCPG_POOL_TIMEOUT_SEC)


@asynccontextmanager
async def _maybe_traced_conn(
    connection: asyncpg.Connection,
) -> AsyncGenerator[asyncpg.Connection, None]:
    if settings.DATADOG_ENABLED:
        # ignore exceptions in the connection hold span
        error = None
        with tracer.trace(
            "postgres.connection_hold",
            service="postgres",
            resource="connection_hold",
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            _set_context_tags(span, structlog.contextvars.get_contextvars())
            try:
                yield connection
            except Exception as e:
                error = e

        if error:
            raise error
    else:
        yield connection


@asynccontextmanager
async def _asyncpg_conn() -> AsyncGenerator[asyncpg.Connection, None]:
    async with asyncpg_pool() as pool:
        idle_size = pool.get_idle_size()
        METRIC_PG_POOL_IDLE.observe(idle_size)
        METRIC_PG_POOL_USED.observe(pool.get_size() - idle_size)
        conn = await _acquire(pool)
        try:
            async with _maybe_traced_conn(conn) as maybe_traced_conn:
                yield maybe_traced_conn
        finally:
            try:
                await pool.release(conn)
            except asyncpg.exceptions.InterfaceError:
                pass


@asynccontextmanager
async def asyncpg_conn(
    exception_mapper: Callable[[Exception], Exception] = lambda x: x,
) -> AsyncGenerator[asyncpg.Connection, None]:
    """If provided, map exceptions raised to desired output exceptions using"""
    async with _asyncpg_conn() as conn:
        try:
            yield conn
        except Exception as e:
            raise exception_mapper(e)


def kwargs_to_pgpos_batch(
    sqls: str | list[str | None],
    params: dict[str, Any],
    *,
    app: str | None = None,
    action: str | None = None,
) -> tuple[list[str], list[Any]]:
    """This function accepts a list of SQL template strings (with $named_params)
    and a dictionary of parameter keys and values.

    It returns a tuple of a list of SQL strings with the named parameters
    replaced by positional parameters as supported by Postgres ($1, $2, etc.)
    and a list of parameter values in the correct order.
    """
    # Create comment suffix
    meta = {"driver": "asyncpg"}
    if app is not None:
        meta["application"] = app
    if action is not None:
        meta["action"] = action
    suffix = generate_sql_comment(**meta)

    # Create SQL templates
    sqls = [sqls] if isinstance(sqls, str) else sqls
    templates = [string.Template(sql) for sql in sqls if sql is not None]

    # Assemble parameters
    used_keys = set(key for template in templates for key in template.get_identifiers())
    ordered_keys = [param for param in used_keys]
    kw_to_pg_pos = {key: f"${i + 1}" for i, key in enumerate(ordered_keys)}

    # Apply parameters and suffix to SQL templates
    return [template.substitute(**kw_to_pg_pos) + suffix for template in templates], [
        params[key] for key in ordered_keys
    ]


class SqlQuery(NamedTuple):
    sql: str
    args: list[Any]


def kwargs_to_pgpos(
    sql: str,
    params: dict[str, Any],
    *,
    app: str | None = None,
    action: str | None = None,
) -> SqlQuery:
    """This function accepts one SQL template string (with $named_params)
    and a dictionary of parameter keys and values.

    It returns a tuple of 1. SQL string with the named parameters
    replaced by positional parameters as supported by Postgres ($1, $2, etc.)
    and 2. a list of parameter values in the correct order.
    """
    sqls, param_list = kwargs_to_pgpos_batch(sql, params, app=app, action=action)
    assert len(sqls) == 1
    return SqlQuery(sqls[0], param_list)


def query_with_named_args(
    sql: str,
    params: dict[str, Any],
    *,
    app: str | None = None,
    action: str | None = None,
) -> tuple[str, ...]:
    """This function accepts one SQL template string (with $named_params)
    and a dictionary of parameter keys and values.

    It returns a tuple of 1. SQL string with the named parameters
    replaced by positional parameters as supported by Postgres ($1, $2, etc.)
    and 2. a list of parameter values in the correct order.
    """
    sqls, param_list = kwargs_to_pgpos_batch(sql, params, app=app, action=action)
    assert len(sqls) == 1
    return (sqls[0], *param_list)
