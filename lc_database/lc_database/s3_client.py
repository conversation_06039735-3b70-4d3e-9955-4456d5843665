"""s3 initialization and image upload"""

import asyncio
import hashlib
import re
import tempfile
import time
import typing
import uuid
from abc import ABC, abstractmethod
from concurrent.futures import Thread<PERSON>oolExecutor
from contextlib import asynccontextmanager
from functools import wraps
from typing import Any, AsyncGenerator, Coroutine, Literal, Optional, cast
from uuid import UUID

import jwt
import orjson
import pycurl
import requests
import structlog
from aiobotocore.session import AioSession, get_session
from botocore.config import Config
from ddtrace import tracer  # noqa: E402
from ddtrace.constants import SPAN_KIND  # noqa: E402
from ddtrace.ext import SpanKind  # noqa: E402
from google.api_core.exceptions import NotFound
from google.cloud import storage
from google.cloud.storage.blob import Blob
from lc_config.settings import shared_settings as settings
from lc_config.utils import arun_in_executor
from tenacity import (
    retry,
    retry_if_exception,
    stop_after_attempt,
    wait_exponential_jitter,
)
from tornado.httpclient import HTTPResponse

from lc_database.curl import HTTPClientError, internal_platform_request

if settings.S3_PROFILE:
    _session = AioSession(profile=settings.S3_PROFILE)
else:
    _session = get_session()

_s3_client = None
_global_s3_client_async_context = None

logger = structlog.stdlib.get_logger(__name__)

DEFER_CLOSE_WAIT_SEC = 180
PENDING_TASKS: set[asyncio.Task] = set()

IoType = Literal["inputs", "outputs", "attachments", "events", "extras"]

S3_SINGLE_REGION_BUCKETS = (
    [settings.S3_SINGLE_REGION_BUCKET_NAME]
    if settings.S3_SINGLE_REGION_BUCKET_NAME
    else []
)


def _create_task_done_callback(task: asyncio.Task) -> None:
    PENDING_TASKS.remove(task)
    try:
        if exc := task.exception():
            logger.exception("Background task failed", exc_info=exc)
    except asyncio.CancelledError:
        pass


T = typing.TypeVar("T")


def create_task(coro: Coroutine[Any, Any, T]) -> asyncio.Task[T]:
    """Create a new task in the current task group and return it."""
    task = asyncio.create_task(coro)
    PENDING_TASKS.add(task)
    task.add_done_callback(_create_task_done_callback)
    return task


def is_transient_s3_error(exception):
    logger.warning(f"S3 error {str(exception)}")
    error_message = str(exception).lower()

    # Throttling errors
    if "throttling" in error_message or "rate exceeded" in error_message:
        return True

    # Service interruptions
    if "serviceunavailable" in error_message:
        return True

    # list index issue due to aiohttp
    if "list index out of range" in error_message:
        return True

    # Error: File descriptor xxx is used by transport
    if "is used by transport" in error_message:
        return True

    if hasattr(exception, "response") and exception.response:
        if exception.response.get("HTTPStatusCode") in [503]:
            return True

        # Other AWS specific error codes
        error_code = exception.response.get("Error", {}).get("Code", "").lower()
        if error_code in ["internalerror", "serviceunavailable", "slowdown"]:
            return True

    if "session is closed" in error_message:
        return True

    if "timeout" in error_message:
        return True

    if "disconnected" in error_message:
        return True

    return False


retry_s3 = retry(
    reraise=True,
    retry=retry_if_exception(is_transient_s3_error),
    wait=wait_exponential_jitter(),
    stop=stop_after_attempt(2),
)


async def create_global_s3_client() -> None:
    """Create a global S3 client if one does not already exist."""
    global _s3_client, _global_s3_client_async_context
    if _global_s3_client_async_context is None:
        botocore_config = Config(
            max_pool_connections=settings.S3_MAX_CONNECTIONS,
            tcp_keepalive=settings.S3_KEEPALIVE,
            connect_timeout=settings.S3_CONNECT_TIMEOUT,
            read_timeout=settings.S3_READ_TIMEOUT,
        )
        if settings.S3_ACCESS_KEY_SECRET and settings.S3_ACCESS_KEY:
            _global_s3_client_async_context = _session.create_client(
                "s3",
                region_name=settings.AWS_REGION,
                endpoint_url=settings.S3_API_URL,
                aws_access_key_id=settings.S3_ACCESS_KEY,
                aws_secret_access_key=settings.S3_ACCESS_KEY_SECRET,
                config=botocore_config,
            )
        else:
            # Use default credentials. Useful for workload identity.
            _global_s3_client_async_context = _session.create_client(
                "s3",
                region_name=settings.AWS_REGION,
                endpoint_url=settings.S3_API_URL,
                config=botocore_config,
            )
        _s3_client = await _global_s3_client_async_context.__aenter__()


async def close_global_s3_client() -> None:
    """Close the global S3 client if it exists."""
    global _global_s3_client_async_context, _s3_client
    if _global_s3_client_async_context:
        logger.info("Closing global S3 client")
        await _global_s3_client_async_context.__aexit__(None, None, None)
        _global_s3_client_async_context = None
        _s3_client = None


def deferred_close_client() -> None:
    """Close the global S3 client if it exists, close it with deferred wait to not interrupt current requests."""
    global _global_s3_client_async_context, _s3_client
    if _global_s3_client_async_context:
        logger.info("Error with S3 client, closing deferred")
        create_task(deferred_close_s3_client_task(_global_s3_client_async_context))
    _global_s3_client_async_context = None
    _s3_client = None


async def deferred_close_s3_client_task(s3_client_async_content) -> None:
    """Close the S3 client but defer close."""
    await asyncio.sleep(DEFER_CLOSE_WAIT_SEC)
    logger.info("Closing deferred global S3 client")
    await s3_client_async_content.__aexit__(None, None, None)


@asynccontextmanager
async def s3_client() -> AsyncGenerator:
    """Provide an async context manager for the global S3 client."""
    global _s3_client
    if _s3_client is None:
        await create_global_s3_client()
    elif not _s3_client._endpoint.http_session._sessions:
        # this means the session is closed, so we need to recreate the client.
        logger.info("Recreating global S3 client")
        deferred_close_client()
        await create_global_s3_client()

    yield _s3_client


PREFIX_RUNS_ASSETS = "runs_assets"


def trace_s3(ff_enabled: bool = True):
    """
    Decorator to trace S3 requests.
    ff_enabled: Whether the feature flag is enabled.
                WARNING: Will not call method if ff_enabled is False.
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs) -> None:
            if not ff_enabled:
                return None
            if not settings.DATADOG_ENABLED:
                return await func(*args, **kwargs)

            # setup tracing
            with tracer.trace(
                "cloudstorage.request", service="cloudstorage", resource=func.__name__
            ) as span:
                span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
                if args and isinstance(args[0], bytes):
                    span.set_tag("payload_size", len(args[0]))

                return await func(*args, **kwargs)

        return wrapper

    return decorator


def get_signed_url_go(s3_path: str, download_as: str = "") -> str | None:
    if not settings.LANGCHAIN_PLATFORM_ENDPOINT:
        return None
    token = jwt.encode(
        {
            "path": s3_path,
            "exp": int(time.time() + settings.S3_PRESIGNED_URL_VALIDITY_TIME_SECONDS),
        },
        settings.API_KEY_SALT,
    )
    url = f"{settings.LANGCHAIN_PLATFORM_ENDPOINT}/public/download?jwt={token}"
    if download_as:
        url += f"&download_as={download_as}"
    return url


IO_TYPES = {
    "inputs",
    "outputs",
    "errors",
    "events",
    "attachments",
    "extras",
    "serialized",
    "multipart",
}


@trace_s3()
async def upload_run_data(
    payload: bytes,
    io_type: IoType,
    tenant_id: str,
    session_id: UUID,
    run_id: UUID | None,
    trace_tier: str | None,
    bucket: str | None,
    content_type: str = "application/json",
) -> str:
    if io_type not in IO_TYPES:
        raise ValueError(f"Invalid io_type: {io_type}")

    s3_path = mk_s3_key(
        tenant_id,
        session_id,
        str(run_id) if run_id else None,
        trace_tier,
        io_type,
        bucket,
    )

    if settings.DATADOG_ENABLED:
        if span := tracer.current_span():
            span.set_tag_str("s3_path", s3_path)

    # If the object does not exist, save it to the bucket
    await internal_platform_request(
        "POST",
        "/internal/upload",
        body=payload,
        headers={
            "Content-Length": str(len(payload)),
            "Content-Type": content_type,
            "X-Object-Key": s3_path,
        },
    )

    return s3_path


@trace_s3()
async def upload_run_data_to_path(payload: bytes, s3_path: str) -> None:
    if settings.DATADOG_ENABLED:
        if span := tracer.current_span():
            span.set_tag_str("s3_path", s3_path)

    await internal_platform_request(
        "POST",
        "/internal/upload",
        body=payload,
        headers={
            "Content-Length": str(len(payload)),
            "Content-Type": "application/json",
            "X-Object-Key": s3_path,
        },
    )


@trace_s3()
async def copy_object(source_key: str, dest_key: str) -> None:
    if not source_key or not dest_key:
        raise ValueError("Both source_key and dest_key are required")

    payload = orjson.dumps({"source_key": source_key, "dest_key": dest_key})
    await internal_platform_request(
        "POST",
        "/internal/copy",
        body=payload,
        headers={
            "Content-Length": str(len(payload)),
            "Content-Type": "application/json",
        },
        raise_error=True,
    )


async def get_run_data(s3_path: str) -> dict | None:
    res = await get_run_data_and_size(s3_path)
    return res[0] if res else None


@trace_s3()
async def download_run_data_to_file(
    s3_path: str, spool_file_path: tempfile.SpooledTemporaryFile
) -> HTTPResponse | None:
    """Get raw bytes from S3 by spooling the download to disk via pycurl."""
    try:
        if "#" in s3_path:
            s3_path, ranges = s3_path.split("#")
            start, end = ranges.split("-")
        else:
            start = None
            end = None

        # Define a callback to direct pycurl to write data to the file.
        def prepare_curl_callback(curl: pycurl.Curl) -> None:
            curl.setopt(pycurl.HTTP_VERSION, pycurl.CURL_HTTP_VERSION_2_PRIOR_KNOWLEDGE)
            curl.setopt(pycurl.WRITEFUNCTION, spool_file_path.write)

        res = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": s3_path,
                **(
                    {"start": start, "end": end}
                    if start is not None and end is not None
                    else {}
                ),
            },
            prepare_curl_callback=prepare_curl_callback,
        )

        return res

    except HTTPClientError as exc:
        if exc.code == 404:
            logger.exception(
                f"Object {s3_path} not found in bucket for run_io: {exc}",
                s3_path=s3_path,
            )
            return None
        elif exc.code == 422:
            logger.exception(
                f"Object {s3_path} is not a valid path: {exc}",
                s3_path=s3_path,
            )
            return None
        else:
            raise


@trace_s3()
async def get_run_data_bytes(s3_path: str) -> bytes | None:
    """
    Download the bytes stored at `s3_path`
    """
    try:
        if "#" in s3_path:  # handle pre-encoded range requests
            s3_path, ranges = s3_path.split("#")
            start, end = ranges.split("-")
        else:
            start = end = ""

        res = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": s3_path,
                **({"start": start, "end": end} if start and end else {}),
            },
        )

        return res.body

    except HTTPClientError as exc:
        if exc.code == 404:
            logger.exception(
                f"Object {s3_path} not found in bucket for run_io: {exc}",
                s3_path=s3_path,
            )
            return None
        elif exc.code == 422:
            logger.exception(
                f"Object {s3_path} is not a valid path: {exc}",
                s3_path=s3_path,
            )
            return None
        else:
            raise


@trace_s3()
async def get_run_data_and_size(s3_path: str) -> tuple[dict, int] | None:
    try:
        if "#" in s3_path:
            s3_path, ranges = s3_path.split("#")
            start, end = ranges.split("-")
        else:
            start = None
            end = None
        res = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": s3_path,
                **(
                    {"start": start, "end": end}
                    if start is not None and end is not None
                    else {}
                ),
            },
        )
        return await arun_in_executor(orjson.loads, res.body), len(res.body)
    except HTTPClientError as exc:
        if exc.code == 404:
            logger.exception(
                f"Object {s3_path} not found in bucket for run_io: {exc}",
                s3_path=s3_path,
            )
            return None
        elif exc.code == 422:
            logger.exception(
                f"Object {s3_path} is not a valid path: {exc}",
                s3_path=s3_path,
            )
            return None
        else:
            raise


PREFIX_RUNS_MANIFESTS = "runs_manifests"


# Fetch the object from run manifest storage
@trace_s3(ff_enabled=settings.FF_BLOB_STORAGE_ENABLED)
async def get_run_manifest(uid: UUID) -> dict | None:
    return await get_run_data(mk_s3_key_manifest(uid))


async def zero_out_or_delete_s3_objects(
    ctx: dict,
    paths_to_ranges: dict[str, list[tuple[int, int]]],
) -> None:
    MAX_S3_CONCURRENCY: int = 100

    await logger.ainfo(
        "Zeroing out or deleting S3 objects",
        paths_to_ranges=paths_to_ranges,
    )

    sem = asyncio.Semaphore(MAX_S3_CONCURRENCY)

    async def _process_item(s3_path: str, ranges: list[tuple[int, int]]) -> None:
        async with sem:
            # ----------------------------------------------------------------
            # Case 1 – Delete entire object
            # ----------------------------------------------------------------
            if not ranges:
                bucket, _ = split_bucket_key(s3_path)
                async with s3_client() as client:
                    await client.delete_object(Bucket=bucket, Key=s3_path)
                return

            # ----------------------------------------------------------------
            # Case 2 – Zero-out specified ranges
            # ----------------------------------------------------------------
            data = await get_run_data_bytes(s3_path)
            if data is None:
                return

            def _zero_ranges() -> bytes:
                ba = bytearray(data)
                for start, end in ranges:
                    if start < 0 or end > len(ba) or start > end:
                        logger.info(
                            "Invalid byte range",
                            start=start,
                            end=end,
                            s3_path=s3_path,
                            data_len=len(data),
                        )
                        raise ValueError(
                            f"Invalid byte range ({start}, {end}) for {s3_path}"
                        )
                    # Inclusive slice => end + 1
                    ba[start:end] = b"\x00" * (end - start)
                return bytes(ba)

            new_payload = await arun_in_executor(_zero_ranges)
            await upload_run_data_to_path(new_payload, s3_path)

    # Kick off work
    await asyncio.gather(
        *[asyncio.create_task(_process_item(p, r)) for p, r in paths_to_ranges.items()]
    )


class BaseCloudStorageDeletionManager(ABC):
    @abstractmethod
    async def delete_storage_prefix(self, s3_path: str, bucket: str) -> None:
        """
        Delete all objects with the given prefix in the cloud storage.
        """

    @classmethod
    def get_instance(cls) -> "BaseCloudStorageDeletionManager":
        if "googleapis" in settings.S3_API_URL:
            return GCSCloudStorageDeletionManager()
        else:
            return BotoCloudStorageDeletionManager()


class BotoCloudStorageDeletionManager(BaseCloudStorageDeletionManager):
    CONCURRENCY = 100
    MAX_QUEUE_SIZE = 100_000
    WAIT_TIMEOUT = 0.1
    BATCH_SIZE = 250

    async def delete_storage_prefix(self, s3_prefix_path: str, bucket: str) -> None:
        logger.info(
            f"Deleting prefix in cloud storage: {s3_prefix_path}",
            prefix=s3_prefix_path,
            bucket=bucket,
        )

        # validate match to ensure we're not deleting unintended objects
        #  [<bucket>/][ttl_<unit>/]<env>/<hex1>/<hex2>[/<anything>…]
        assert s3_prefix_path is not None
        prefix_pattern = re.compile(
            r"^(?:[A-Za-z0-9._-]+/)?(?:ttl_\w+/)?[\w-]+/[a-f0-9]+/[a-f0-9]+(?:/.*)?$"
        )
        assert prefix_pattern.match(s3_prefix_path), (
            f"Invalid S3 prefix path: {s3_prefix_path}"
        )

        done_listing: asyncio.Event = asyncio.Event()
        queue: asyncio.Queue = asyncio.Queue(maxsize=self.MAX_QUEUE_SIZE)
        tasks = []
        tasks.append(
            asyncio.create_task(
                self._list_directories_worker(s3_prefix_path, queue, done_listing)
            )
        )
        for _ in range(self.CONCURRENCY):
            tasks.append(asyncio.create_task(self._delete_worker(queue, done_listing)))

        responses = await asyncio.gather(*tasks, return_exceptions=True)
        delete_exceptions = [exc for exc in responses if isinstance(exc, BaseException)]
        if delete_exceptions:
            if all(isinstance(exc, Exception) for exc in delete_exceptions):
                raise ExceptionGroup(
                    "Exception during storage_assets deletes",
                    cast(list[Exception], delete_exceptions),
                )
            else:
                # cannot pass BaseException to ExceptionGroup
                raise delete_exceptions[0]

    async def _delete_batch(self, objects: list[str]) -> None:
        async with s3_client() as client:
            for s3_path in objects:
                await self._delete_object(s3_path, client)

    @retry_s3
    async def _delete_object(self, s3_path, client) -> None:
        bucket, _ = split_bucket_key(s3_path)
        await client.delete_object(Bucket=bucket, Key=s3_path)

    @retry_s3
    async def _list_directories_worker(
        self, s3_path, queue, done_listing: asyncio.Event
    ) -> None:
        bucket, _ = split_bucket_key(s3_path)
        async with s3_client() as client:
            paginator = client.get_paginator("list_objects_v2")
            asset_count = 0
            current_batch = []
            async for result in paginator.paginate(Bucket=bucket, Prefix=s3_path):
                for content in result.get("Contents", []):
                    key = content["Key"]
                    current_batch.append(key)
                    asset_count += 1
                    if len(current_batch) >= self.BATCH_SIZE:
                        await queue.put(current_batch)
                        current_batch = []

            if current_batch:
                await queue.put(current_batch)

        logger.info(
            "Processing assets for deletion", asset_count=asset_count, s3_path=s3_path
        )
        done_listing.set()

    async def _delete_worker(
        self, queue: asyncio.Queue, done_listing: asyncio.Event
    ) -> None:
        counter = 0

        while not queue.empty() or not done_listing.is_set():
            try:
                objects = await asyncio.wait_for(queue.get(), timeout=self.WAIT_TIMEOUT)
            except asyncio.CancelledError:
                break
            except asyncio.TimeoutError:
                continue
            await self._delete_batch(objects)
            counter += len(objects)

        if counter > 0:
            logger.info(
                "Worker coroutine completed, deleted assets",
                asset_count=counter,
                bucket=settings.S3_BUCKET_NAME,
            )


class GCSCloudStorageDeletionManager(BotoCloudStorageDeletionManager):
    """
    Boto Client does not support batch deletes in GCS. In this case we can delegate to GCS client delete in batch.
    """

    GCS_MAX_POOLSIZE = 128
    GCS_MAX_RETRIES = 3
    EXECUTOR_THREADS = 128

    def __init__(self):
        self.gcs_client = storage.Client(project=settings.GCP_PROJECT_ID)
        # see https://github.com/googleapis/python-storage/issues/253
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=self.GCS_MAX_POOLSIZE,
            pool_maxsize=self.GCS_MAX_POOLSIZE,
            max_retries=self.GCS_MAX_RETRIES,
            pool_block=True,
        )
        self.gcs_client._http.mount("https://", adapter)
        self.gcs_client._http._auth_request.session.mount("https://", adapter)
        self._bucket_cache: dict[str, storage.Bucket] = {}
        # increased executors since these are mainly I/O bound operations
        self.executor = ThreadPoolExecutor(max_workers=self.EXECUTOR_THREADS)
        super().__init__()

    def _get_bucket(self, name: str) -> storage.Bucket:
        """Lazily fetch & cache the GCS Bucket."""
        if name not in self._bucket_cache:
            self._bucket_cache[name] = self.gcs_client.bucket(name)
        return self._bucket_cache[name]

    async def _delete_batch(self, objects: list[str]) -> None:
        """
        Overriding this to delete in batch using gcs client.
        """
        await arun_in_executor(
            self._delete_batch_sync,
            objects,
            executor=self.executor,
        )

    def _delete_batch_sync(self, objects: list[str]) -> None:
        with self.gcs_client.batch():
            for blob_path in objects:
                bucket_name, key = split_bucket_key(blob_path, settings.S3_BUCKET_NAME)
                try:
                    Blob(key, bucket=self._get_bucket(bucket_name)).delete()
                except NotFound:
                    logger.warning("Blob not found during delete", key=blob_path)


def split_bucket_key(
    path: str, default_bucket: str = settings.S3_BUCKET_NAME
) -> tuple[str, str]:
    for candidate in S3_SINGLE_REGION_BUCKETS:
        if candidate == "":
            continue
        prefix = f"{candidate}/"
        if path.startswith(prefix):
            return candidate, path[len(prefix) :]
    return default_bucket, path


def strip_ttl_prefix(path: str) -> tuple[str | None, str]:
    bucket: str | None = None
    key = path
    for candidate in S3_SINGLE_REGION_BUCKETS:
        if candidate == "":
            continue
        prefix = f"{candidate}/"
        if path.startswith(prefix):
            bucket = candidate
            key = path[len(prefix) :]
            break

    first, sep, rest = key.partition("/")
    if first.startswith("ttl_") and sep:
        key_no_ttl = rest
    else:
        key_no_ttl = key

    return bucket, key_no_ttl


def mk_s3_key(
    tenant_id: UUID | str,
    session_id: UUID | str,
    run_id: UUID | str | None,
    trace_tier: str | None,
    io_type: IoType,
    bucket: Optional[str] = None,
) -> str:
    tenant_id_bytes = str(tenant_id).encode("utf-8")
    session_id_bytes = str(session_id).encode("utf-8")
    h_tenant_id = hashlib.sha256(tenant_id_bytes).hexdigest()
    h_session_id = hashlib.sha256(session_id_bytes).hexdigest()
    obj_id = uuid.uuid4()

    ttl_prefix = (
        settings.S3_TRACE_TIER_PREFIX_MAP[trace_tier] + "/" if trace_tier else ""
    )

    bucket_prefix = (
        f"{bucket}/"
        if bucket
        and (
            str(tenant_id) in settings.FF_SINGLE_REGION_BUCKET_ENABLED_TENANTS
            or "*" in settings.FF_SINGLE_REGION_BUCKET_ENABLED_TENANTS
        )
        else ""
    )

    # use hashed session id for obfuscation
    path = f"{bucket_prefix}{ttl_prefix}{io_type}/{h_tenant_id}/{h_session_id}"
    if run_id is not None:
        path += f"/{run_id}"
    return f"{path}/{str(obj_id)}"


def mk_s3_key_example(
    tenant_id: UUID | str,
    dataset_id: UUID | str,
    example_id: UUID | str,
    io_type: IoType,
) -> str:
    tenant_id_bytes = str(tenant_id).encode("utf-8")
    dataset_id_bytes = str(dataset_id).encode("utf-8")
    h_tenant_id = hashlib.sha256(tenant_id_bytes).hexdigest()
    h_dataset_id = hashlib.sha256(dataset_id_bytes).hexdigest()
    obj_id = uuid.uuid4()

    # used hashed tenant and dataset ids for obfuscation
    return f"{io_type}/{h_tenant_id}/{h_dataset_id}/{example_id}/{str(obj_id)}"


def mk_s3_key_manifest(manifest_id: UUID) -> str:
    return f"{PREFIX_RUNS_MANIFESTS}/{manifest_id}"
