AUTH_TYPE=none
POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test
ASYNCPG_POOL_MAX_SIZE=5
FF_WAITLIST_ENABLED=false
# Different database than local_dev to avoid cross-contamination
REDIS_DATABASE_URI=redis://localhost:6379/1
REDIS_MIN_VERSION=7.0.0
API_KEY_SALT=super-secret-salt
BASIC_AUTH_JWT_SECRET=basically-super-secret
SUPABASE_JWT_SECRET=super-secret
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_NATIVE_PORT=9000
CLICKHOUSE_USER=clickhouse_admin
CLICKHOUSE_PASSWORD=password
CLICKHOUSE_DB=langsmith_test
OAUTH_CLIENT_ID=44er1ak4lpunn3st0ni3gv3ggv
OAUTH_ISSUER_URL=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_DSEbLX0QF
LANGSMITH_LICENSE_KEY=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************.ZwciDM36lWndVrQZEhYBX48PqjTCbbXy_czEJ1Lhh2HRActSm8zzCt1PM7OHBIO0SeFqHpnIa6ODZMuAinPEQjMX3d8m-YCF5EnjH7R8s0fnjzPtga7KFkdJVWMVI71TnylkCDaCYgD_MBJcEfSTsOZuUKf4-HrB-HddfaPlHtq4xUUM_0C1pKj5Dc6caCwego6sE7904G7MVj9lmYw5IRF6gEL8bbW3WJHhCDlh0beY8yZOq2RHYcLFGBpYcYMiXQor20ROVWBm-_7KTuD2q2UtNv387Fd6DMHKbJy_ZlwLJHDbRZkjQ_IXqtvK2RueHU1yds0Xef7yUC34Ag2Bd8yqfYCUa8geupc6yqy0Nan91oFEc1m5_6cUCS-UyuS7F-RO7JiigBK6CNEfDIwu47iHs23FKmZ4Z_1BKAaWjbi9MhGInl-Lo0xLKuZf5GOTRBHomxPAnXvc8t654JBpLZHPDjTELZwVItzatWwF11W2DyCSIOlLcRAeIrl9fLR6oBkCw52d4p0C6Z8CC5GyBhLTzplA_KMia8qFIXrE-dE500zJcQWEsI8K7TULod0LT_RfI-MSErBIaYFyElmvmSl-fr8beIk6dWP0jf9iJjUSLCpJl5WJqWhFZVc7XMV-OcGkljmwb4cZHoUFquG3m8S1M9YxMvtI8eA19XB1GpE
S3_API_URL=http://127.0.0.1:9002
S3_ACCESS_KEY=minioadmin1
S3_ACCESS_KEY_SECRET=minioadmin1
AZURE_STORAGE_ACCOUNT_NAME=admin
AZURE_STORAGE_ACCOUNT_KEY=password
AZURE_STORAGE_CONTAINER_NAME=langsmith-images-test
AZURE_STORAGE_SERVICE_URL_OVERRIDE=http://127.0.0.1:10000/admin
FF_S3_URL_STORAGE_ENABLED=true
S3_BUCKET_NAME=langsmith-images-test
DEFAULT_FEATURE_FLAGS='{"hosted_langserve_enabled": true, "payment_enabled": true, "rbac_enabled": true, "pat_enabled": true, "langgraph_deploy_own_cloud_enabled": false}'
DEFAULT_ORG_FEATURE_FLAGS='{"hosted_langserve_enabled": true, "payment_enabled": true, "rbac_enabled": true, "pat_enabled": true, "langgraph_deploy_own_cloud_enabled": false}'
DEFAULT_ORG_FEATURE_CAN_USE_LANGGRAPH_CLOUD=true
FF_USAGE_LIMITS_ENABLED=true
LOG_LEVEL=INFO
TRANSACTION_INTERVAL_SEC=300
TRANSACTION_PROCESSING_DELAY_SEC=120
TRANSACTION_PROCESSING_LOCK_TIMEOUT_SEC=150
FF_PROCESS_BILLING_TRANSACTIONS=false
METRONOME_REPORTING_BATCH_SIZE=1
FF_METRONOME_BATCH_SEAT_REPORTING_ENABLED=true
LANGCHAIN_ENDPOINT='http://localhost:1984'
LANGCHAIN_PLATFORM_ENDPOINT='http://localhost:8080'
LANGSMITH_URL='http://localhost:3000'
RUN_RULES_CRON=''
FF_PAYMENT_ENABLED=false
METRONOME_FREE_PLAN_ID=3e20ba47-1ea9-4de6-b522-4855506987a6
METRONOME_DEV_PLAN_ID=e7a50973-4661-4c71-bdf3-317e72b57bf5
METRONOME_PLUS_PLAN_ID=d403194b-66d0-481e-815d-057865f1765d
METRONOME_DEV_LEGACY_PLAN_ID=ed1d75bf-3075-4404-8b60-5f01a87b77a3
METRONOME_PLUS_LEGACY_PLAN_ID=fc3a21ca-c68d-44b9-a4ea-c59eb9c9cec6
METRONOME_ENTERPRISE_LEGACY_PLAN_ID=1568658f-9574-439a-903e-82e4241d8c97
METRONOME_STARTUP_PLAN_ID=f86f7f3b-e43b-4a1c-8959-8086d87f34ff
METRONOME_PARTNER_PLAN_ID=a342acf5-f619-44f8-bb37-aa3993cb754c
METRONOME_PREMIER_PLAN_ID=9cc93cc4-a1d0-4aa8-bb14-1ac1572ab6db
METRONOME_MIDDLEWARE_ENABLED=true
STRIPE_SECRET_KEY=test
METRONOME_API_KEY=test
INGESTION_QUEUE=default
ADHOC_QUEUE=default
EXPORT_QUEUE=default
RUN_RULES_QUEUE=default
UPGRADES_QUEUE=default
HOST_QUEUE=default
GO_ENDPOINT=http://localhost:8080
GO_ACE_ENDPOINT=http://localhost:8081
GO_AUTH_PCT=100
GO_TENANTLESS_AUTH_PCT=100
FF_ENABLE_LOCK_RENEWAL=true
FF_TRACE_TIERS_ENABLED=true
RUN_RULES_QUERY_LIMIT=2
FF_UPGRADE_TRACE_TIER_ENABLED=true
CH_UPGRADE_QUEUE_DELAY_SEC=1
CH_UPGRADE_BATCH_LIMIT=1
CH_UPGRADE_BATCH_DELAY_SEC=1
X_SERVICE_AUTH_JWT_SECRET=super-secret-sts-jwt
X_SERVICE_AUTH_JWT_EXPIRATION_SECONDS=3600
RUN_RULES_SPREAD_SEC=0
DELETE_QUEUE_DELAY_SEC=1
TRACE_UPGRADE_MAX_SPREAD_SEC=0
AUTH_CACHE_TTL_SEC=0
METRONOME_CONFIG_CACHE_EXPIRY_SEC=0
CHARTS_CACHE_TTL_SEC=0
SERVED_DATASET_ENABLED=true
ELASTIC_URL=http://localhost:9200
SYNC_SERVED_DATASETS_CRON_ENABLED=false
BEACON_ENDPOINT=http://localhost:8080
RUN_RULES_FILTERING_ENABLED=true
ENABLE_BULK_EXPORT_CRON=false
PLAYGROUND_ENDPOINT=http://0.0.0.0:7233
SMITH_BACKEND_ENDPOINT=http://localhost:7234
ENABLE_ONBOARDING_EMAILS=false
# New naming convention now that we support both S3 and Azure
BLOB_STORAGE_ENGINE=S3
FF_BLOB_STORAGE_ENABLED=true
MIN_BLOB_STORAGE_SIZE_KB=1
LICENSE_PRIVATE_KEY_BASE64=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
LICENSE_KEY_ID=local
ONLINE_EVALS_BATCH_SIZE=1
HOST_WORKER_HEARTBEAT_CRON_ENABLED=false
HOST_WORKER_RECONCILIATION_CRON_ENABLED=false
HOST_WORKER_DELETE_UNUSED_PROJECTS_CRON_ENABLED=false
MAX_CONCURRENT_BULK_EXPORTS_PER_WS=5
BULK_EXPORT_MAX_CONCURRENT_RUNS=10
REDIS_FEEDBACK_UPDATE_TTL=15
# This attaches a tracer to the database pool in smith-go that captures all queries
DB_LOG_LEVEL=tests-capture
STATS_USE_SORTED_SESSION_AGGREGATION=true
HOSTED_K8S_ROOT_DOMAIN="test.langgraph.app"
AGGREGATE_ALERT_RULE_DELAY_MINUTES=0
REDIS_CLUSTER_DATABASE_URIS='["redis://localhost:30001", "redis://localhost:30002", "redis://localhost:30003"]'
REDIS_CLUSTER_ALERTS_ENABLED=false
REDIS_CLUSTER_ENABLED=false
REDIS_CLUSTER_INGESTION_GLOBAL_ENABLED=false
# CORS
CORS_ALLOWED_ORIGINS_REGEX='http://localhost.*|http://example.com'
CORS_ALWAYS_ALLOW_PATHS_REGEX='.*(/feedback/tokens/|/public/).*'
FF_RUN_STATS_GROUP_BY_ENABLED_ALL=true
FF_TRACER_SESSION_DEFAULT_DASHBOARD_ENABLED_ALL=true
RUN_TRACE_ID_CLEANUP_DELAY_SEC=1
FF_USE_PG_FOR_FEEDBACK_UPSERT_ENABLED_ALL=true
# Non-JSON list types
OAUTH_SCOPES='email,profile,openid'
DATADOG_IGNORE_ROUTES='/ok,/health'
HTTP_LOG_QUIET_ROUTES='/ok,/health'
FF_USE_PK_ORDER_BY=true
FF_USE_PG_FOR_FEEDBACK_CONFIGS_UPSERT_ENABLED_ALL=true
FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_ALL=true
