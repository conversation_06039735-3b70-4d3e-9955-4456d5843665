AUTH_TYPE=supabase
FF_WAITLIST_ENABLED=false
REDIS_MIN_VERSION=7.0.0
CLICKHOUSE_HOST=z73i4gum1x.us-central1.gcp.clickhouse.cloud
CLICKHOUSE_PORT=8443
CLICKHOUSE_NATIVE_PORT=9440
CLICKHOUSE_USER=default
CLICKHOUSE_DB=default
CLICKHOUSE_TLS=true
S3_API_URL=https://storage.googleapis.com
S3_BUCKET_NAME=dev-langsmith-static-assets
DEFAULT_FEATURE_FLAGS='{"hosted_langserve_enabled": true, "payment_enabled": true, "usage_reporting_enabled": true, "show_upgrade_billing_ui": true, "run_rules_enabled": true, "conversation_view_enabled": true, "rbac_enabled": true, "pat_enabled": true, "allow_backfill_rules": true, "playground_comparative": true, "compare_trace_enabled": true, "langgraph_deploy_own_cloud_enabled": false, "arbitrary_code_execution_enabled": true}'
DEFAULT_ORG_FEATURE_FLAGS='{"hosted_langserve_enabled": true, "payment_enabled": true, "usage_reporting_enabled": true, "show_upgrade_billing_ui": true, "run_rules_enabled": true, "conversation_view_enabled": true, "rbac_enabled": true, "pat_enabled": true, "allow_backfill_rules": true, "playground_comparative": true, "compare_trace_enabled": true, "langgraph_deploy_own_cloud_enabled": false, "arbitrary_code_execution_enabled": true}'
DEFAULT_ORG_FEATURE_CAN_USE_LANGGRAPH_CLOUD=true
DEFAULT_ORG_CAN_SERVE_DATASETS=true
FF_USAGE_LIMITS_ENABLED=true
DATADOG_ENABLED=true
DATADOG_PATCH=true
DATADOG_PROFILING_ENABLED=true
METRICS_ENABLED=false
METRONOME_DEV_PLAN_ID=fba098a1-7940-4270-b50d-2ed660ed8672
METRONOME_PLUS_PLAN_ID=d39e1616-d97a-426e-82a1-9583150eded6
METRONOME_DEV_LEGACY_PLAN_ID=d5cfd9a1-e125-4941-9f71-4c79b4b67cfb
METRONOME_PLUS_LEGACY_PLAN_ID=afa0ae72-c0b1-434a-9b10-b27fe5058643
METRONOME_FREE_PLAN_ID=ca8db50a-9eee-4e96-8fdf-4b7e394e9fbb
METRONOME_ENTERPRISE_LEGACY_PLAN_ID=4ffa8c74-ab94-4baa-b25c-ffef82c2747a
METRONOME_STARTUP_PLAN_ID=20f1022c-fc9e-4684-aff7-6870b792e3c3
METRONOME_PARTNER_PLAN_ID=f9785eba-73a4-4a00-8da5-5e4bdee18379
METRONOME_PREMIER_PLAN_ID=df1e3c9a-e729-433e-944d-02a753b9d75d
METRONOME_MIDDLEWARE_ENABLED=true
FF_PAYMENT_ENABLED=true
FF_METRONOME_TRACE_REPORTING_ENABLED=true
FF_METRONOME_NODES_REPORTING_ENABLED=true
FF_METRONOME_SEAT_REPORTING_ENABLED=true
LONGLIVED_TRACE_PROCESSING_ENABLED=true
TRANSACTION_INTERVAL_SEC=300
TRANSACTION_PROCESSING_DELAY_SEC=150
TRANSACTION_PROCESSING_LOCK_TIMEOUT_SEC=540
TRANSACTION_PROCESSING_JOB_TIMEOUT_SEC=540
TRANSACTION_PROCESSING_CRON="*/5 * * * *"
FF_USE_PAID_PLANS=true
GO_ENDPOINT="http://langsmith-platform-backend:1986"
GO_ACE_ENDPOINT="http://langsmith-ace-backend:1986"
FF_ENABLE_LOCK_RENEWAL=true
FF_TRACE_TIERS_ENABLED=true
FF_UPGRADE_TRACE_TIER_ENABLED=true
CH_UPGRADE_QUEUE_DELAY_SEC=60
CH_UPGRADE_BATCH_DELAY_SEC=5
X_SERVICE_AUTH_JWT_EXPIRATION_SECONDS=3600
SMITH_BACKEND_ENDPOINT="http://langsmith-backend:1984"
HOST_BACKEND_ENDPOINT="http://langsmith-host-backend:1985"
GITHUB_APP_ID="707377"
GITHUB_APP_PUBLIC_LINK="https://github.com/apps/hosted-langserve-dev"
GITHUB_CLIENT_ID="Iv1.c38f77aec56d7aa0"
SA_GROUP_ID="04k668n30nntp8h"
PRIVATE_GITHUB_BUILD_TRIGGER_ID="1ab0cec1-5b35-40b2-bfe3-8a60fc091cb9"
GCP_PROJECT_ID="langchain-dev"
GCP_PROJECT_NUMBER="858561540022"
LANGCHAIN_ENDPOINT="https://dev.api.smith.langchain.com"
HOST_LANGCHAIN_API_ENDPOINT="https://dev.api.smith.langchain.com"
LANGSMITH_URL="https://dev.smith.langchain.com"
IA_ROLLOUT_PCT=100
RUN_RULES_CRON="* * * * *"
SERVED_DATASET_ENABLED=true
SUPABASE_PROJECT_REF=esjezssntgpnwfflgroq
PLAYGROUND_ENDPOINT=http://langsmith-playground-python:1988
RUN_RULES_FILTERING_ENABLED=true
ENABLE_ONBOARDING_EMAILS=false
ENABLE_ORG_CHARTS_CRON=true
# New naming convention now that we support both S3 and Azure
BLOB_STORAGE_ENGINE=S3
FF_BLOB_STORAGE_ENABLED=true
MIN_BLOB_STORAGE_SIZE_KB=1
LICENSE_KEY_ID=dev
ENABLE_SELF_HOSTED_BILLING_CUSTOMER_CRON=true
HOSTED_K8S_ROOT_DOMAIN="dev.langgraph.app"
# LangGraph Cloud K8s infra
GCP_HOST_VPC=langsmith-network
HOST_COMPUTE_PLATFORM=k8s
HOST_DEPLOYMENT_REGION=us-central1
HOSTED_K8S_CLUSTER=langgraph-cloud-dev
HOSTED_K8S_ENDPOINT_URLS='{"langgraph-cloud-dev": "https://************:443", "langgraph-cloud-us-west1": "https://************:443"}'
HOSTED_K8S_CA_CERT_PATHS='{"langgraph-cloud-dev": "/etc/ssl/certs/langgraph_cloud_ca_cert.pem", "langgraph-cloud-us-west1": "/etc/ssl/certs/langgraph_cloud_us_west1_ca_cert.pem"}'
FF_TRACER_SESSION_DELETE_BATCHING=true
TRACER_SESSION_BATCH_DELETE_CRON="*/30 * * * *"
# run more frequently for testing
METRONOME_SEAT_HEARTBEAT_CRON="*/5 * * * *"
HOST_WORKER_TENANT_ID="d6684905-655c-429b-bd14-ba302d94c03b"
STATS_USE_SORTED_SESSION_AGGREGATION=true
# Non-JSON list types
OAUTH_SCOPES='email,profile,openid'
DATADOG_IGNORE_ROUTES='/ok,/health'
HTTP_LOG_QUIET_ROUTES='/ok,/health'
