{"name": "smith-frontend", "version": "0.1.0", "private": true, "packageManager": "yarn@1.22.22", "dependencies": {"@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.2.4", "@codemirror/lang-python": "^6.1.4", "@codemirror/legacy-modes": "^6.3.3", "@codemirror/lint": "6.8.1", "@codemirror/text": "^0.19.6", "@datadog/browser-logs": "^5.11.0", "@datadog/browser-rum": "^5.15.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/public-sans": "^4.5.12", "@headlessui/react": "^2.0.0", "@headlessui/tailwindcss": "^0.1.3", "@heroicons/react": "^2.0.18", "@langchain/codemirror-json-schema": "^0.7.9", "@langchain/community": "^0.3.34", "@langchain/core": "^0.3.49", "@langchain/langgraph": "^0.2.50", "@langchain/langgraph-sdk": "^0.0.74", "@langchain/untitled-ui-icons": "^1.0.5", "@lezer/yaml": "^1.0.2", "@microsoft/fetch-event-source": "^2.0.1", "@mui/icons-material": "^5.11.16", "@mui/joy": "5.0.0-alpha.84", "@mui/material": "^5.13.5", "@radix-ui/colors": "^1.0.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-collapsible": "1.0.3", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "2.0.6", "@radix-ui/react-hover-card": "1.0.7", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-portal": "^1.0.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-toast": "1.1.5", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.6", "@react-spring/web": "^9.7.3", "@segment/analytics-next": "^1.76.0", "@stripe/react-stripe-js": "^2.5.0", "@stripe/stripe-js": "^3.0.2", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-table": "^8.9.3", "@tiptap/core": "^2.1.12", "@tiptap/extension-document": "^2.1.12", "@tiptap/extension-dropcursor": "^2.1.12", "@tiptap/extension-history": "^2.3.0", "@tiptap/extension-image": "^2.1.12", "@tiptap/extension-link": "^2.11.2", "@tiptap/extension-mention": "^2.3.2", "@tiptap/extension-paragraph": "^2.1.12", "@tiptap/extension-text": "^2.1.12", "@tiptap/pm": "^2.1.12", "@tiptap/react": "^2.1.12", "@tiptap/suggestion": "^2.3.2", "@uiw/codemirror-theme-tokyo-night-day": "^4.20.0", "@uiw/codemirror-theme-tokyo-night-storm": "^4.20.0", "@uiw/react-codemirror": "^4.20.0", "@visx/glyph": "^3.3.0", "@visx/gradient": "^3.3.0", "@visx/pattern": "^3.3.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "@visx/tooltip": "^3.3.0", "@visx/xychart": "^3.8.0", "@vitest/coverage-v8": "^3.1.1", "babel-plugin-react-compiler": "^19.0.0-beta-37ed2a7-20241206", "clsx": "^1.2.1", "cmdk": "^1.0.0", "d3-color": "^3.1.0", "d3-dsv": "^3.0.1", "d3-interpolate": "^3.0.1", "d3-scale-chromatic": "^3.0.0", "dagre": "^0.8.5", "dayjs": "^1.11.9", "dedent": "^1.5.3", "dequal": "^2.0.3", "diff-match-patch": "^1.0.5", "dompurify": "^3.2.4", "edge-runtime": "^2.4.4", "eventsource-parser": "^1.0.0", "filesize": "^10.0.12", "generate-schema": "^2.6.0", "immer": "^10.1.1", "jose": "^4.15.5", "json-schema-library": "^10.0.0-rc2", "json5": "^2.2.3", "langchain": "^0.3.2", "linkify-it": "^5.0.0", "lodash-es": "^4.17.21", "lucide-react": "^0.378.0", "lz-string": "^1.5.0", "mdast-util-gfm": "^3.0.0", "micromark-extension-gfm": "^3.0.0", "mime": "^4.0.7", "moment": "^2.29.4", "mustache": "^4.2.0", "oidc-client-ts": "^2.2.5", "openevals": "^0.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-hotkeys-hook": "^4.5.0", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-resizable-panels": "^0.0.55", "react-router-dom": "^6.10.0", "react-syntax-highlighter": "^15.6.1", "react-vega": "^7.6.0", "react-virtuoso": "^4.7.8", "reactflow": "^11.11.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "replicate": "^0.14.1", "semver": "^7.7.1", "strip-ansi": "^7.1.0", "swr": "^2.1.5", "tailwind-merge": "^1.13.2", "unified": "^11.0.0", "use-debounce": "^9.0.4", "uuid": "^9.0.1", "vega": "^5.33.0", "vega-lite": "^5.14.1", "web-vitals": "^2.1.0", "yaml": "^2.3.2", "zod": "^3.22.4", "zustand": "^5.0.0-rc.2"}, "scripts": {"start": "vite --port 3000 --mode authnone", "start:tunnel": "concurrently --names \"VITE,TUNNEL\" -c \"bgBlue,bgMagenta\" \"vite --port 3000 --mode authnone-tunnel\" \"cloudflared tunnel --url http://localhost:3000\"", "start:auth": "vite --port 3000", "start:oauth": "vite --port 3000 --mode oauth", "start:basic": "vite --port 3000 --mode basic", "start:google-oauth": "vite --port 3000 --mode google-oauth", "start:graph": "vite --port 3333 --mode graph", "start:marketplace": "vite --port 3334 --mode marketplace", "gen:lang": "lezer-generator src/components/RunsTable/filter/lang.grammar -o src/components/RunsTable/filter/lang.ts && prettier --write src/components/RunsTable/filter/", "postinstall": "patch-package", "build": "vite build", "build:dynamic": "vite build --mode dynamic", "build:graph": "vite build --mode graph", "build:marketplace": "vite build --mode marketplace && mv build/marketplace/index.html build/index.html && rm -rf build/marketplace", "build:visualize": "vite build --mode visualize", "prepare": "cd .. && husky install smith-frontend/.husky", "format": "prettier --write src && eslint --fix src --ext ts,tsx", "lint": "prettier --check src && eslint src --ext ts,tsx --quiet && tsc", "lint:eslint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run", "test-watch": "vitest", "test-serialization": "vitest run playgroundUtilsSerialization"}, "devDependencies": {"@lezer/generator": "^1.6.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/d3-color": "^3.1.1", "@types/d3-dsv": "^3.0.1", "@types/d3-scale-chromatic": "^3.0.0", "@types/dagre": "^0.7.52", "@types/date-fns": "^2.6.0", "@types/diff": "^5.0.3", "@types/diff-match-patch": "^1.0.36", "@types/json-schema": "^7.0.15", "@types/lodash": "^4.17.15", "@types/lodash-es": "^4.17.12", "@types/lodash.clonedeep": "^4.5.9", "@types/lodash.escaperegexp": "^4.1.9", "@types/lodash.get": "^4.4.7", "@types/lodash.isequal": "^4.5.8", "@types/lodash.ismatch": "^4.4.7", "@types/lodash.partition": "^4.6.9", "@types/lodash.set": "^4.3.9", "@types/lodash.sortby": "^4.7.7", "@types/node": "^16.7.13", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.18", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "7", "@typescript-eslint/parser": "7", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.8.1", "autoprefixer": "^10.4.20", "canvas": "^3.1.0", "concurrently": "^8.2.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "husky": "^8.0.3", "jsdom": "^24.0.0", "lint-staged": "^13.2.1", "msw": "^2.7.5", "openai": "^4.52.0", "patch-package": "^8.0.0", "postcss": "^8.4.35", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "react-compiler-runtime": "^19.0.0-beta-37ed2a7-20241206", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "terser": "^5.18.2", "typescript": "5.4.5", "vite": "^6.3.5", "vite-plugin-legacy-swc": "^1.0.0", "vite-plugin-svgr": "^4.2.0", "vitest": "^3.0.9"}, "lint-staged": {"*.{tsx,js,css}": "prettier --write --ignore-unknown"}, "resolutions": {"@codemirror/lint": "6.8.1", "browserslist": "^4.24.0", "prismjs": "^1.30.0", "@babel/runtime": "7.26.10", "vite": "^6.3.5"}}