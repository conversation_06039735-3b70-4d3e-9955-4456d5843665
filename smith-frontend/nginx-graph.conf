server {
    listen       ${PORT};
    server_name  localhost;

    location = / {
        return 302 $scheme://$http_host${STUDIO_PATH_PREFIX}studio;
    }

    location ~ ${STUDIO_PATH_REDIRECT} {
        return 302 $scheme://$http_host${STUDIO_PATH_PREFIX}studio;
    }

    location / {
        root   /tmp/build;
        index  index.html index.htm;
        try_files $uri ${STUDIO_PATH_PREFIX}studio/index.html =404;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /tmp/build;
    }
}
