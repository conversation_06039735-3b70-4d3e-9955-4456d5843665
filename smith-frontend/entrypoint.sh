#!/bin/sh

ROOT_DIR=/usr/share/nginx/html
BUILD_DIR=$ROOT_DIR/build
# Use a subdirectory if VITE_SUBDOMAIN is set
if [ -n "$VITE_SUBDOMAIN" ]; then
  if [ -f /etc/nginx/templates/default.conf.template ]; then
    TMP_DIR=/tmp/build/$VITE_SUBDOMAIN
  else
    TMP_DIR=/tmp/$VITE_SUBDOMAIN
  fi
else
  TMP_DIR=/tmp/build
fi

mkdir -p $TMP_DIR
# Copy files to tmp directory
echo "Copying files to TMP_DIR: $TMP_DIR"
cp -r $BUILD_DIR/. $TMP_DIR

# Replace env vars in JavaScript files
echo "Replacing env constants in JS"
echo "VITE_BACKEND_URL: ${VITE_BACKEND_URL}"
echo "VITE_STUDIO_LOCAL_GRAPH_URL: ${VITE_STUDIO_LOCAL_GRAPH_URL}"
echo "VITE_OAUTH_CLIENT_ID: ${VITE_OAUTH_CLIENT_ID}"
echo "VITE_OAUTH_ISSUER_URL: ${VITE_OAUTH_ISSUER_URL}"
echo "VITE_OAUTH_GOOGLE_KEY: ${VITE_OAUTH_GOOGLE_KEY}"
echo "VITE_BACKEND_AUTH_TYPE: ${VITE_BACKEND_AUTH_TYPE}"
echo "VITE_BASIC_AUTH_ENABLED: ${VITE_BASIC_AUTH_ENABLED}"
echo "VITE_SUPABASE_URL: ${VITE_SUPABASE_URL}"
echo "VITE_SUPABASE_KEY: ${VITE_SUPABASE_KEY}"
echo "VITE_SINGLE_ORIGIN_ENABLED: ${VITE_SINGLE_ORIGIN_ENABLED:-1}"
echo "VITE_HOST_ENABLED: ${VITE_HOST_ENABLED:-0}"
echo "VITE_GRAPH_ENABLED: ${VITE_GRAPH_ENABLED:-0}"
echo "VITE_HOST_URL: ${VITE_HOST_URL}"
echo "VITE_HOST_PRIVATE_REPO_ENABLED: ${VITE_HOST_PRIVATE_REPO_ENABLED}"
echo "VITE_PLAYGROUND_BASE_URL: ${VITE_PLAYGROUND_BASE_URL:-/api/playground}"
echo "VITE_SUBDOMAIN: ${VITE_SUBDOMAIN:-''}"
echo "VITE_STRIPE_KEY: ${VITE_STRIPE_KEY:-''}"
echo "VITE_DATADOG_CLIENT_TOKEN: ${VITE_DATADOG_CLIENT_TOKEN:-''}"
echo "VITE_DATADOG_SITE: ${VITE_DATADOG_SITE:-''}"
echo "VITE_DATADOG_ENV: ${VITE_DATADOG_ENV:-''}"
echo "VITE_DATADOG_VERSION: ${VITE_DATADOG_VERSION:-''}"
echo "VITE_DATADOG_CLIENT_TOKEN_RUM: ${VITE_DATADOG_CLIENT_TOKEN_RUM:-''}"
echo "VITE_DATADOG_APP_TOKEN_RUM: ${VITE_DATADOG_APP_TOKEN_RUM:-''}"
echo "VITE_FF_ENABLE_FEW_SHOT_CORRECTIONS: ${VITE_FF_ENABLE_FEW_SHOT_CORRECTIONS:-1}"
echo "VITE_STUDIO_ANALYTICS_SUPABASE_URL: ${VITE_STUDIO_ANALYTICS_SUPABASE_URL:-''}"
echo "VITE_STUDIO_ANALYTICS_SUPABASE_KEY: ${VITE_STUDIO_ANALYTICS_SUPABASE_KEY:-''}"
echo "VITE_SEGMENT_WRITE_KEY: ${VITE_SEGMENT_WRITE_KEY:-''}"
echo "VITE_STUDIO_CUSTOM_HEADERS: ${VITE_STUDIO_CUSTOM_HEADERS:-''}"

for file in $TMP_DIR/assets/* $TMP_DIR/index.html $TMP_DIR/*/index.html;
do
  echo "Processing $file ...";

  # check if file exists
  if [ ! -f $file ]; then
    echo "File not found: $file"
    continue
  fi

  sed -i 's|"VITE_BACKEND_URL"|"'${VITE_BACKEND_URL}'"|g' $file
  sed -i 's|"VITE_STUDIO_LOCAL_GRAPH_URL"|"'${VITE_STUDIO_LOCAL_GRAPH_URL:-http://127.0.0.1:8123}'"|g' $file
  sed -i 's|"VITE_OAUTH_CLIENT_ID"|"'${VITE_OAUTH_CLIENT_ID}'"|g' $file
  sed -i 's|"VITE_OAUTH_ISSUER_URL"|"'${VITE_OAUTH_ISSUER_URL}'"|g' $file
  sed -i 's|"VITE_BACKEND_AUTH_TYPE"|"'${VITE_BACKEND_AUTH_TYPE}'"|g' $file
  sed -i 's|"VITE_BASIC_AUTH_ENABLED"|"'${VITE_BASIC_AUTH_ENABLED}'"|g' $file
  sed -i 's|"VITE_SUPABASE_URL"|"'${VITE_SUPABASE_URL}'"|g' $file
  sed -i 's|"VITE_SUPABASE_KEY"|"'${VITE_SUPABASE_KEY}'"|g' $file
  sed -i 's|"VITE_SINGLE_ORIGIN_ENABLED"|"'${VITE_SINGLE_ORIGIN_ENABLED:-1}'"|g' $file
  sed -i 's|"VITE_GRAPH_ENABLED"|"'${VITE_GRAPH_ENABLED:-0}'"|g' $file
  sed -i 's|"VITE_HOST_ENABLED"|"'${VITE_HOST_ENABLED:-0}'"|g' $file
  sed -i 's|"VITE_HOST_URL"|"'${VITE_HOST_URL}'"|g' $file
  sed -i 's|"VITE_HOST_PRIVATE_REPO_ENABLED"|"'${VITE_HOST_PRIVATE_REPO_ENABLED}'"|g' $file
  sed -i 's|"VITE_PLAYGROUND_BASE_URL"|"'${VITE_PLAYGROUND_BASE_URL:-/api/playground}'"|g' $file
  sed -i 's|"VITE_STRIPE_KEY"|"'${VITE_STRIPE_KEY:-''}'"|g' $file
  sed -i 's|"VITE_DATADOG_CLIENT_TOKEN"|"'${VITE_DATADOG_CLIENT_TOKEN:-''}'"|g' $file
  sed -i 's|"VITE_DATADOG_SITE"|"'${VITE_DATADOG_SITE:-''}'"|g' $file
  sed -i 's|"VITE_DATADOG_ENV"|"'${VITE_DATADOG_ENV:-''}'"|g' $file
  sed -i 's|"VITE_DATADOG_VERSION"|"'${VITE_DATADOG_VERSION:-''}'"|g' $file
  sed -i 's|"VITE_DATADOG_APP_TOKEN_RUM"|"'${VITE_DATADOG_APP_TOKEN_RUM:-''}'"|g' $file
  sed -i 's|"VITE_DATADOG_CLIENT_TOKEN_RUM"|"'${VITE_DATADOG_CLIENT_TOKEN_RUM:-''}'"|g' $file
  sed -i 's|"VITE_FF_ENABLE_FEW_SHOT_CORRECTIONS"|"'${VITE_FF_ENABLE_FEW_SHOT_CORRECTIONS:-0}'"|g' $file
  sed -i 's|"VITE_STUDIO_ANALYTICS_SUPABASE_URL"|"'${VITE_STUDIO_ANALYTICS_SUPABASE_URL:-''}'"|g' $file
  sed -i 's|"VITE_STUDIO_ANALYTICS_SUPABASE_KEY"|"'${VITE_STUDIO_ANALYTICS_SUPABASE_KEY:-''}'"|g' $file
  sed -i 's|"VITE_SEGMENT_WRITE_KEY"|"'${VITE_SEGMENT_WRITE_KEY:-''}'"|g' $file
  sed -i 's|"VITE_REACT_SCAN_ENABLED"|false|g' $file
  HEADERS=$(njs -c "console.log(\"'\" + JSON.stringify(JSON.parse(process.env.VITE_STUDIO_CUSTOM_HEADERS || '{}')) + \"'\")")
  sed -i 's|"VITE_STUDIO_CUSTOM_HEADERS"|'${HEADERS:-''}'|g' $file

  # Vite will replace BASE_URL with /${VITE_SUBDOMAIN} (note the leading slash)
  if [ -n "$VITE_SUBDOMAIN" ]; then
    sed -i 's|/VITE_SUBDOMAIN|/'${VITE_SUBDOMAIN}'|g' $file
  else
    echo "Removing VITE_SUBDOMAIN from $file"
    sed -i 's|/VITE_SUBDOMAIN||g' $file
  fi
done

# Only applied for `nginx-graph.conf`
if [ -f /etc/nginx/templates/default.conf.template ]; then
  export PORT=${PORT:-3968}

  # STUDIO_PATH_PREFIX = /${VITE_SUBDOMAIN}/ or /
  export STUDIO_PATH_PREFIX=${VITE_SUBDOMAIN:-''}
  if [[ ! "$STUDIO_PATH_PREFIX" =~ ^/ ]]; then
    export STUDIO_PATH_PREFIX="/$STUDIO_PATH_PREFIX"
  fi
  if [[ ! "$STUDIO_PATH_PREFIX" =~ /$ ]]; then
    export STUDIO_PATH_PREFIX="$STUDIO_PATH_PREFIX/"
  fi

  # STUDIO_PATH_REDIRECT = ^/${STUDIO_PATH_PREFIX}/?$ or ^/$
  export STUDIO_PATH_REDIRECT="$STUDIO_PATH_PREFIX"
  if [[ "$STUDIO_PATH_REDIRECT" == "/" ]]; then
    export STUDIO_PATH_REDIRECT="^/$"
  else
    export STUDIO_PATH_REDIRECT="^$STUDIO_PATH_REDIRECT?$"
  fi

  echo "Replacing env vars in nginx config: PORT=$PORT, STUDIO_PATH_PREFIX=$STUDIO_PATH_PREFIX, STUDIO_PATH_REDIRECT=$STUDIO_PATH_REDIRECT"
  envsubst '$PORT $STUDIO_PATH_PREFIX $STUDIO_PATH_REDIRECT' < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf
fi

echo "Starting Nginx"
nginx -g 'daemon off;'
