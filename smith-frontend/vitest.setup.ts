import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import { afterEach, beforeAll, vi } from 'vitest'

vi.mock('@/components/RichTextEditor/utils/fancyRenderContext', async (importOriginal) => {
  const actual = await importOriginal() as any;
  return {
    ...actual,
    FancyRenderingProvider: ({ children }: { children: React.ReactNode }) => children,
    useFancyRendering: () => ({ isFancyRendering: false })
  }
})

vi.mock('@/components/RichTextEditor/utils/fancyRenderContext', () => ({
  FancyRenderingProvider: ({ children }: { children: React.ReactNode }) => children,
  useFancyRendering: () => ({ isFancyRendering: false }),
  FancyRenderingContext: {
    Provider: ({ children }: { children: React.ReactNode }) => children,
  }
}))

beforeAll(() => {
  // Mock all common DOM events
  class MockEvent {
    constructor(type: string, eventInitDict?: EventInit) {
      Object.assign(this, eventInitDict, {
        type,
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
      })
    }
  }

  // @ts-ignore
  global.DataTransfer = class DataTransfer {
    private data: Record<string, string> = {}
    dropEffect = 'none'
    effectAllowed = 'all'
    files: FileList = [] as unknown as FileList
    items: DataTransferItemList = [] as unknown as DataTransferItemList
    types: string[] = []

    setData(format: string, data: string) {
      this.data[format] = data
      this.types.push(format)
    }
    
    getData(format: string) {
      return this.data[format] || ''
    }

    clearData(format?: string) {
      if (format) {
        delete this.data[format]
      } else {
        this.data = {}
      }
    }
  }

  const eventTypes = {
    ClipboardEvent: ['clipboardData'],
    DragEvent: ['dataTransfer'],
    MouseEvent: ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'screenX', 'screenY', 'shiftKey'],
    TouchEvent: ['touches', 'targetTouches', 'changedTouches'],
    FocusEvent: ['relatedTarget'],
    KeyboardEvent: ['altKey', 'code', 'ctrlKey', 'isComposing', 'key', 'locale', 'location', 'metaKey', 'repeat', 'shiftKey']
  }

  // Create mock event constructors
  Object.entries(eventTypes).forEach(([eventName, properties]) => {
    // @ts-ignore
    global[eventName] = class extends MockEvent {
      constructor(type: string, eventInitDict: Record<string, any> = {}) {
        super(type, eventInitDict)
        
        // Add specific properties for this event type
        properties.forEach(prop => {
          if (prop === 'dataTransfer') {
            this[prop] = new DataTransfer()
          } else if (!(prop in eventInitDict)) {
            this[prop] = null
          }
        })

        // Add common methods
        // @ts-ignore
        this.preventDefault = vi.fn()
        // @ts-ignore
        this.stopPropagation = vi.fn()
      }
    }
  })

  // Mock other DOM APIs

  // @ts-ignore
  global.ResizeObserver = class ResizeObserver {
    observe = vi.fn()
    unobserve = vi.fn()
    disconnect = vi.fn()
  }

  // @ts-ignore
  global.MutationObserver = class MutationObserver {
    observe = vi.fn()
    disconnect = vi.fn()
    takeRecords = vi.fn().mockReturnValue([])
  }
})

afterEach(() => {
  cleanup()
})
