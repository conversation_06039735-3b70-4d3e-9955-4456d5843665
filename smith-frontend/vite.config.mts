import react from '@vitejs/plugin-react';

import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import {
  type AppType,
  BuildOptions,
  PluginOption,
  defineConfig,
  loadEnv,
} from 'vite';
import legacy from 'vite-plugin-legacy-swc';
import svgr from 'vite-plugin-svgr';

const ReactCompilerConfig = {
  target: '18',
  sources: (filename) => {
    const excludedFiles = [
      // these are all related to tanstack table not supporting
      // react compiler.
      'src/components/DataGridVirtual.tsx',
      'src/components/DataGrid.tsx',
      'src/Pages/Settings/components/SessionRulesCrudForm.tsx',
      'src/Pages/Dashboards/DashboardsTable.tsx',
      'src/Pages/AnnotationQueue/AnnotationQueuesTable.tsx',
      'src/components/DatasetsTable/index.tsx',
      'src/components/FeedbackTable/index.tsx',
      'src/components/ExamplesTable/hooks/useExamplesTable.tsx',
      'src/components/ComparativeExperiments/ComparativeExperimentsDataGrid.tsx',
      'src/Pages/Conversation/ConversationList.tsx',
      'src/components/RunsTable/index.tsx',
      'src/components/HostProjectsTable/index.tsx',
      'src/components/HostRevisionsTable/HostRevisionsTable.tsx',
      'src/Pages/Settings/OrganizationShared.tsx',
      'src/components/Prompts/PromptsTable/index.tsx',
      'src/Pages/HubRepo/OptimizationRuns/OptimizationRunsTable.tsx',
      'src/components/SessionsTable/components/SessionsDataGrid.tsx',
      'src/Pages/HostProject/components/ThreadsPanel/index.tsx',
      'src/Pages/HostProject/components/AgentsPanel/index.tsx',
    ];
    return !excludedFiles.some((file) => filename.includes(file));
  },
};

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  let base: string | undefined = undefined;
  if (mode === 'dynamic' && env.VITE_SUBDOMAIN != null) {
    base = env.VITE_SUBDOMAIN;

    // this is done automatically by Vite
    // doing it manually to silence build warnings
    if (!base.startsWith('/')) base = `/${base}`;
  }

  let appType: AppType = 'spa';
  const plugins: PluginOption[] = [
    react({
      babel: {
        plugins: [['babel-plugin-react-compiler', ReactCompilerConfig]],
      },
    }),

    svgr(),
    legacy({
      modernPolyfills: [
        // matches polyfill.io es2021
        'es/aggregate-error',
        'es/promise/any',
        'es/string/replace-all',
        // matches polyfill.io es2022
        'es/array/at',
        'es/error', // es.error.cause
        'es/object/has-own',
        'es/string/at',
        'es/typed-array/at',
      ],
    }),
  ];

  if (mode === 'visualize') {
    plugins.push(
      visualizer({
        open: true,
        filename: 'bundle-stats.html',
        gzipSize: true,
        brotliSize: true,
      })
    );
  }

  const build: BuildOptions = {
    outDir: 'build',
    sourcemap: mode === 'dynamic' || mode === 'graph' || mode === 'marketplace',
  };

  if (mode === 'graph') {
    appType = 'mpa';
    plugins.unshift({
      name: 'rewrite-middleware',
      configureServer(serve) {
        serve.middlewares.use((req, res, next) => {
          if (req.url.startsWith('/studio')) {
            req.url = '/studio/';
          }
          next();
        });
      },
    });

    build.outDir = 'build/studio';
    build.rollupOptions = { input: 'studio/index.html' };
  }

  if (mode === 'marketplace') {
    appType = 'mpa';
    plugins.unshift({
      name: 'rewrite-middleware',
      configureServer(serve) {
        serve.middlewares.use((req, res, next) => {
          if (
            req.url === '/' ||
            req.url === '/index.html' ||
            req.url === '/login' ||
            req.url.match(
              /^\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i
            )
          ) {
            req.url = '/marketplace/index.html';
          }
          next();
        });
      },
    });

    build.rollupOptions = {
      input: path.resolve(__dirname, 'marketplace/index.html'),
    };
  }

  const delightedJsUrl = 'https://d2yyd1h5u9mauk.cloudfront.net';
  // it's helpful to disable cache when testing this locally
  // https://stackoverflow.com/a/7000899
  const cspScriptSrc = [
    "'self'",
    "'unsafe-eval'",
    'https://js.stripe.com', // Stripe
    'https://embed.launchnotes.io', // Release notes, https://www.launchnotes.com/
    'https://unpkg.com', // react-scan
    delightedJsUrl, // delighted.js
    'https://cdn.segment.com', // segment.com
    'https://www.datadoghq-browser-agent.com', // Datadog RUM
    'http://localhost:3000', // for local development
    'http://localhost:4173', // for local preview
    // inline scripts in index.html with vite start
    "'sha256-hlGfrXHDxR1I5Zm6qp0+FDKsUxPQXhnRH67WWXva4HQ='",
    "'sha256-8ZgGo/nOlaDknQkDUYiedLuFRSGJwIz6LAzsOrNxhmU='",
    "'sha256-ng26PyUg1j9yTbbE/Crz1EEYhLE5CPF3S6J1+4IDY2I='",
    "'sha256-kdktFOlCvWsAuPfJfYFZ9PDrjmlUqVSZtGgEhZZFXgM='",
    // inline scripts in index.html with vite preview (these should match the ones added to Cloud)
    "'sha256-gzRopuF+FObTczY/pMwGGM5XJBCJnQZEPhinNtsKKV0='",
    "'sha256-VA8O2hAdooB288EpSTrGLl7z3QikbWU9wwoebO/QaYk='",
    "'sha256-+5XkZFazzJo8n0iOP4ti/cLCMUudTf//Mzkb7xNPXIc='",
  ];
  const cspHeaders = [
    "frame-ancestors 'self'",
    "object-src 'none'",
    `form-action 'self' ${delightedJsUrl}`,
    // require-trusted-types-for breaks Vega charts
    // "require-trusted-types-for 'script'",
    `script-src ${cspScriptSrc.join(' ')};`,
    // work-src is required for React scan and must have a trailing semicolon
    "worker-src 'self' blob:;",
  ];

  return {
    base,
    appType,
    plugins,
    build,
    server: {
      // for verifying security headers locally. for cloud, this is set in frontend-config-map-*.yaml files in deployments repo.
      // if needed, comment out the CSP headers and uncomment the CSP report-only headers to see the warnings without breaking functionality locally.
      headers: {
        // 'Content-Security-Policy': cspHeaders.join('; '),
        'Content-Security-Policy-Report-Only': cspHeaders.join('; '),
        'X-Frame-Options': 'SAMEORIGIN',
        'X-Content-Type-Options': 'nosniff',
        'Strict-Transport-Security':
          'max-age=31536000; includeSubDomains; preload',
      },
      proxy: {
        '/api':
          env.BACKEND_ORIGIN && env.VITE_BACKEND_URL.endsWith('/api')
            ? {
                target: env.BACKEND_ORIGIN,
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, ''),
              }
            : undefined,
        '/api/playground': {
          target: env.VITE_DEV_PLAYGROUND_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/playground/, ''),
        },
      },
    },
    resolve: {
      alias: {
        '@': '/src',
      },
    },
    test: {
      environment: 'jsdom',
    },
  };
});
