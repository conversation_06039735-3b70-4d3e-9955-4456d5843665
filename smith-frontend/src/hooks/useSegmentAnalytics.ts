import { AnalyticsBrowser } from '@segment/analytics-next';

import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { isSelfHosted } from '@/utils/is-self-hosted';

import { useAuth } from './useAuth';
import { useGetLSUserIdFromUser } from './useGetLSUserIdFromUser';
import { useCurrentOrganization, useUser, useWorkspaceId } from './useSwr';

const writeKey =
  import.meta.env.VITE_SEGMENT_WRITE_KEY ?? 'IxOfmiLzN5vT9xJgQEzQvnHioslfvR9S';
const analytics = isSelfHosted ? null : AnalyticsBrowser.load({ writeKey });

export type SegmentInfo = {
  email?: string;
  name?: string;
  organization: { name?: string; id?: string; workspace_id?: string | null };
  user: { supabase_user_id?: string; ls_user_id?: string };
};

const useSegmentInfo = (): {
  data: SegmentInfo;
  isLoading: boolean;
} => {
  const { session } = useAuth();
  const { data: user, isLoading: userLoading } = useUser({
    revalidateOnFocus: false,
  });
  const currentOrg = useCurrentOrganization({
    revalidateOnFocus: false,
  });
  const workspaceId = useWorkspaceId();

  const { email } = session?.user ?? {};
  const { user: userData } = user?.data ?? {};
  const { user_metadata, id: userId } = userData ?? {};
  const { display_name: company, id: orgId, tier } = currentOrg.data ?? {};

  const { lsUserId, isLoading: lsUserIdLoading } =
    useGetLSUserIdFromUser(userId);

  const data = {
    email,
    name: user_metadata?.full_name,
    organization: { name: company, id: orgId, workspace_id: workspaceId, tier },
    user: {
      supabase_user_id: userId,
      ls_user_id: lsUserId,
    },
  };

  return {
    data,
    isLoading: userLoading || lsUserIdLoading,
  };
};

export const useSegmentAnalytics = () => {
  const location = useLocation();

  useEffect(() => {
    if (!analytics) return;
    analytics.page();
  }, [location.pathname]);

  const { data, isLoading } = useSegmentInfo();

  useEffect(() => {
    if (
      !data.user.supabase_user_id ||
      !data.user.ls_user_id ||
      !writeKey ||
      isLoading ||
      !analytics
    ) {
      return;
    }
    analytics.identify(data.user.ls_user_id, data);
  }, [data]);

  return !analytics ? null : { analytics, data };
};
