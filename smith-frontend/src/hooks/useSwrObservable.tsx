/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from 'react';
import useSWR, {
  Key,
  SWRConfiguration,
  unstable_serialize,
  useSWRConfig,
} from 'swr';

export interface ObservableValue<Data> {
  value: Data | undefined;
  done: boolean;
}

type MutatorCallback<Data = any> = (
  currentData?: ObservableValue<Data>
) => ObservableValue<Data> | undefined;

export type SWRObservableOptions<Data = any, Error = any> = {
  next: (
    error?: Error | null,
    data?: ObservableValue<Data> | MutatorCallback<Data>
  ) => void;
};

type SWRObservable<
  SWRSubKey extends Key = Key,
  Data = any,
  Error = any
> = SWRSubKey extends () => infer Arg | null | undefined | false
  ? (key: Arg, { next }: SWRObservableOptions<Data, Error>) => void
  : S<PERSON><PERSON><PERSON><PERSON>ey extends null | undefined | false
  ? never
  : S<PERSON>Sub<PERSON>ey extends infer Arg
  ? (key: Arg, { next }: SWRObservableOptions<Data, Error>) => void
  : never;

// hook for endpoints which stream a finite amount of data.
// unlike useSWRSubscription we support all SWR options,
// namely global mutate & interval polling.
export default function useSWRObservable<
  Data = any,
  Error = any,
  SWRSubKey extends Key = Key
>(
  key: SWRSubKey,
  subscribe: SWRObservable<any, Data, Error>,
  config: SWRConfiguration
) {
  const keepPreviousData = config.keepPreviousData || false;

  const { mutate: globalMutate, cache: globalCache } = useSWRConfig();

  const swr = useSWR(
    key,
    (params): Promise<ObservableValue<Data>> => {
      let resolveRef: (data: ObservableValue<Data>) => void;
      let rejectRef: (error: unknown) => void;
      let accumulatedDataRef = globalCache.get(unstable_serialize(params))
        ?.data as ObservableValue<Data>;

      const promise = new Promise<ObservableValue<Data>>((resolve, reject) => {
        resolveRef = resolve;
        rejectRef = reject;
      });

      const next: SWRObservableOptions<Data, Error>['next'] = (error, data) => {
        if (error != null) {
          rejectRef?.(error);
        } else {
          if (keepPreviousData && accumulatedDataRef) {
            // If keepPreviousData is true and we have previous data,
            // accumulate the streaming data but don't update cache until done
            if (typeof data === 'function') {
              // Handle function updates
              const dataFn = data as MutatorCallback<Data>;

              // This dataFn call assumes accumulatedDataRef is not nullish, otherwise
              // it will throw an error and return a nullish value under certain circumstances.
              // so we must prepopulate the accumulatedDataRef with value that exists in the cache.
              const result = dataFn(accumulatedDataRef);

              if (result) {
                accumulatedDataRef = result;
              }
            } else {
              // Handle direct data updates
              accumulatedDataRef = data as ObservableValue<Data>;
            }
            if (accumulatedDataRef?.done) {
              // Only update the cache when all data is received
              globalMutate(params, accumulatedDataRef, false).then(() => {
                resolveRef?.(accumulatedDataRef);
              });
            }
          } else {
            // Standard behavior: update the cache immediately
            globalMutate(params, data as any, false).then((data: unknown) => {
              // we can assume that the data is an ObservableValue<Data>
              // because params is always a valid key for a streamed endpoint.
              const typedData = data as ObservableValue<Data>;
              if (typedData?.done) resolveRef?.(typedData);
            });
          }
        }
      };

      subscribe(key, { next });
      return promise;
    },
    config
  );

  type SWRMutate = (
    data?: Data | Promise<Data | undefined>
  ) => Promise<Data | undefined>;

  const data = swr.data?.value;
  const mutate = useCallback<SWRMutate>(
    (data) => {
      if (data instanceof Promise) {
        return swr
          .mutate(data.then((value) => ({ value, done: true })))
          .then((a) => a?.value);
      }

      if (typeof data === 'undefined') {
        return swr.mutate(undefined, true).then((a) => a?.value);
      }

      return swr.mutate({ value: data, done: true }).then((a) => a?.value);
    },
    [swr]
  );

  return { ...swr, data, mutate };
}
