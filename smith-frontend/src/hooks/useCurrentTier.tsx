import { KeyedMutator } from 'swr';

import { OrganizationSchema, PaymentPlanTier } from '@/types/schema';
import { isSelfHosted } from '@/utils/is-self-hosted';

import { InstanceFlags, useInstanceFlagOrDefault } from './useInstanceFlag';
import { useCurrentOrganization } from './useSwr';

export const useOrgRequiresPayment = (): {
  requiresPayment: boolean;
  isLoading: boolean;
} => {
  const isPaymentEnabled = useInstanceFlagOrDefault(
    InstanceFlags.payment_enabled
  ) as boolean;
  const currentOrganization = useCurrentOrganization();
  return {
    requiresPayment:
      currentOrganization.data?.tier == 'no_plan' &&
      currentOrganization.data?.is_personal === false &&
      !currentOrganization.isLoading &&
      currentOrganization.data &&
      isPaymentEnabled &&
      !isSelfHosted,
    isLoading: currentOrganization.isLoading,
  };
};

export const useCurrentTier = (): {
  isEnterprise: boolean;
  isLegacy: boolean;
  isNoPlan: boolean;
  tier: PaymentPlanTier | null;
  isLoading: boolean;
  isFree: boolean;
  mutate: KeyedMutator<OrganizationSchema>;
} => {
  const orgQuery = useCurrentOrganization();
  return {
    isEnterprise: orgQuery.data?.tier?.includes('enterprise') ?? false,
    isLegacy:
      (orgQuery.data?.tier?.endsWith('_legacy') &&
        orgQuery.data?.tier !== 'enterprise_legacy') ??
      false,
    isNoPlan: orgQuery.data?.tier === 'no_plan',
    tier: orgQuery.data?.tier ?? null,
    isLoading: orgQuery.isLoading,
    isFree: orgQuery.data?.tier === 'free',
    mutate: orgQuery.mutate,
  };
};
