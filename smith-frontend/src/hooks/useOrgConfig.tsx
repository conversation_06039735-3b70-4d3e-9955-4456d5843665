import { useCurrentOrganization } from './useSwr';

export enum OrgConfigs {
  can_add_seats = 'can_add_seats',
  can_use_rbac = 'can_use_rbac',
  max_identities = 'max_identities',
  max_run_rules = 'max_run_rules',
  premier_plan_approval_date = 'premier_plan_approval_date',
  startup_plan_approval_date = 'startup_plan_approval_date',
  connected_to_metronome = 'connected_to_metronome',
  connected_to_stripe = 'connected_to_stripe',
  is_personal = 'is_personal',
  payment_method = 'payment_method',
  max_workspaces = 'max_workspaces',
  can_serve_datasets = 'can_serve_datasets',
  can_use_langgraph_cloud = 'can_use_langgraph_cloud',
  max_langgraph_cloud_deployments = 'max_langgraph_cloud_deployments',
  max_free_langgraph_cloud_deployments = 'max_free_langgraph_cloud_deployments',
  use_python_playground_service = 'use_python_playground_service',
  datadog_rum_session_sample_rate = 'datadog_rum_session_sample_rate',
  max_prompt_webhooks = 'max_prompt_webhooks',

  // Feature flags
  show_playground_prompt_canvas = 'show_playground_prompt_canvas',
  kv_dataset_message_support = 'kv_dataset_message_support',
  allow_custom_iframes = 'allow_custom_iframes',
  enable_langgraph_pricing = 'enable_langgraph_pricing',
  enable_thread_view_playground = 'enable_thread_view_playground',
  enable_org_usage_charts = 'enable_org_usage_charts',
  enable_select_all_traces = 'enable_select_all_traces',
  use_exact_search_for_prompts = 'use_exact_search_for_prompts',
  langgraph_deploy_own_cloud_enabled = 'langgraph_deploy_own_cloud_enabled',
  prompt_optimization_jobs_enabled = 'prompt_optimization_jobs_enabled',
  demo_lgp_new_graph_enabled = 'demo_lgp_new_graph_enabled',
  langgraph_remote_reconciler_enabled = 'langgraph_remote_reconciler_enabled',
  langsmith_alerts_poc_enabled = 'langsmith_alerts_poc_enabled',
  lgp_templates_enabled = 'lgp_templates_enabled',
  enable_prebuilt_dashboards = 'enable_prebuilt_dashboards',
  langsmith_alerts_legacy_poc_enabled = 'langsmith_alerts_legacy_poc_enabled',
  langsmith_experimental_search_enabled = 'langsmith_experimental_search_enabled',
  langgraph_platform_ga_enabled = 'langgraph_platform_ga_enabled',
  enable_align_evaluators = 'enable_align_evaluators',
  enable_monthly_usage_charts = 'enable_monthly_usage_charts',
}

const DEFAULT_ORG_CONFIGS = {
  can_add_seats: false,
  can_use_rbac: false,
  max_identities: 5,
  max_run_rules: 25,
  premier_plan_approval_date: '',
  startup_plan_approval_date: '',
  connected_to_metronome: false,
  connected_to_stripe: false,
  is_personal: false,
  payment_method: '',
  can_serve_datasets: false,
  can_use_langgraph_cloud: false,
  max_langgraph_cloud_deployments: 3,
  max_free_langgraph_cloud_deployments: 0,
  use_python_playground_service: false,

  show_playground_prompt_canvas: false,
  kv_dataset_message_support: false,
  allow_custom_iframes: false,
  enable_langgraph_pricing: false,
  enable_thread_view_playground: false,
  enable_org_usage_charts: false,
  enable_select_all_traces: false,
  use_exact_search_for_prompts: false,
  langgraph_deploy_own_cloud_enabled: false,
  demo_lgp_new_graph_enabled: false,
  langgraph_remote_reconciler_enabled: false,
  langsmith_alerts_poc_enabled: true,
  lgp_templates_enabled: false,
  enable_prebuilt_dashboards: false,
  langsmith_alerts_legacy_poc_enabled: false,
  langsmith_experimental_search_enabled: false,
  langgraph_platform_ga_enabled: false,
  enable_align_evaluators: false,
  max_prompt_webhooks: 1,
  enable_monthly_usage_charts: false,
};

export const useOrgConfig = (
  config: OrgConfigs
): {
  isLoading: boolean;
  value: string | number | boolean | null;
} => {
  const { data: organizationConfig, isLoading } = useCurrentOrganization();

  return {
    isLoading,
    value: isLoading
      ? null
      : organizationConfig?.config[config] ?? DEFAULT_ORG_CONFIGS[config],
  };
};

export const useOrgDisabled = (): {
  isDisabled: boolean;
  isLoading: boolean;
} => {
  const { data: organizationConfig, isLoading } = useCurrentOrganization();
  return {
    isLoading,
    isDisabled: !!organizationConfig?.disabled,
  };
};
