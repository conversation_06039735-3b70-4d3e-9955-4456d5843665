import { SerializedConstructor } from '@langchain/core/load/serializable';
import {
  UserAttributes,
  UserIdentity,
  UserResponse,
} from '@supabase/supabase-js';

import { groupBy, isMatch, sortBy } from 'lodash-es';
import { useMemo, useRef } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import useSWRBase, { SWRConfiguration, SWRResponse, mutate } from 'swr';
import useSWRInfinite, {
  SWRInfiniteConfiguration,
  SWRInfiniteKeyLoader,
} from 'swr/infinite';
import useSWRMutationBase, { SWRMutationConfiguration } from 'swr/mutation';

import { checkIsValidOrThrowError } from '@/components/ExampleCrudPane/utils';
import useToast from '@/components/Toast';
import {
  DEFAULT_INFINITE_OPTIONS,
  DEFAULT_OPTIONS,
} from '@/constants/swrDefaultOptions';
import { eventBus } from '@/data/eventBus';
import { SUPABASE_USER_FORBIDDEN } from '@/data/eventConstants';
import useSWRObservableBase from '@/hooks/useSwrObservable';
import {
  AlertRuleWithActions,
  AlertsResponse,
  CreateAlertRequest,
  TestAlertRequest,
  UpdateAlertRequest,
} from '@/types/alerts';
import { supabase } from '@/utils/backend-auth';
import { createEmptyOutput } from '@/utils/create-empty-output';

import {
  FetcherParams,
  SubscriberParams,
  fetcher,
  subscriber,
} from '../data/fetcher';
import {
  AceInvokeBody,
  AceResponseBody,
  AnnotationQueueRunSchema,
  AnnotationQueueSchema,
  AnnotationQueueSizeSchema,
  ApiKeySchema,
  BulkCreateExampleBody,
  CloneDatasetSchema,
  CommentSchema,
  CommitManifestResponse,
  ComparativeExperimentSchema,
  CreateCommentBody,
  CreateEventBody,
  CreateIdentityAnnotationQueueRunStatusBody,
  CreatePlaygroundSettingsBody,
  CreateRepoBody,
  CreateRepoCommitBody,
  CreateRepoCommitResponse,
  CustomChartCreate,
  CustomChartSchema,
  CustomChartUpdateBase,
  CustomChartsPreviewRequest,
  CustomChartsPreviewResponse,
  CustomChartsRequest,
  CustomChartsSectionCreate,
  CustomChartsSectionResponse,
  CustomChartsSectionSchema,
  CustomChartsSectionUpdate,
  DashboardsRequest,
  DatasetDataType,
  DatasetGroupRunsRequest,
  DatasetSchema,
  DatasetSplitsUpdateSchema,
  DatasetVersionPutBody,
  DatasetVersionsDiffSchema,
  DeleteAnnotationQueueRunsBody,
  ExampleSchema,
  ExampleSchemaWithRuns,
  ExampleUpdateSchema,
  ExampleValidateSchema,
  ExampleValidationResult,
  ExamplesGroup,
  FeedbackConfigCreateBody,
  FeedbackConfigSchema,
  FeedbackConfigsRequest,
  FeedbackSchema,
  FewShotSyncStatusSchema,
  FilterViewCreateBody,
  FilterViewSchema,
  FilterViewType,
  FilterViewUpdateBody,
  GetAnnotationQueueArchivedSizeQueryParams,
  GetAnnotationQueueRunsQueryParams,
  GetAnnotationQueuesQueryParams,
  GetComparativeExperimentsQueryParams,
  GetDatasetShareBody,
  GetDatasetVersionsDiffQueryParams,
  GetDatasetVersionsQueryParams,
  GetDatasetsQueryParams,
  GetExampleQueryParams,
  GetExampleRunsQueryParams,
  GetExamplesQueryParams,
  GetFeedbackQueryParams,
  GetRepoResponse,
  GetRuleLogsQueryParams,
  GetRunRulesQueryParams,
  GetRunsQueryParams,
  GetSessionsMetadataQueryParams,
  GetSessionsQueryParams,
  GetSingleDatasetVersionsQueryParams,
  GetSingleSessionQueryParams,
  GroupedDatasetVersionSchema,
  HealthInfoGetResponse,
  HostAccessTokenSchema,
  HostGetProjectsQueryParams,
  HostGithubInstallLinkSchema,
  HostGithubNamespaceSchema,
  HostGithubRepoSchema,
  HostProjectCreateBody,
  HostProjectPatchBody,
  HostProjectSchema,
  HostRevisionCreateBody,
  HostRevisionLogsRequest,
  HostRevisionLogsResponse,
  HostRevisionSchema,
  IdentitySchema,
  InvokePromptRequest,
  InvokePromptResponse,
  LikeRepoBody,
  LikeRepoResponse,
  LikeResponse,
  ListCommentsResponse,
  ListCommitsResponse,
  ListRepoTagsQueryParams,
  ListRepoTagsResponse,
  ListReposQueryParams,
  ListReposResponse,
  ModelPriceMapSchema,
  ModelPriceMapUpdateBody,
  MonitorQueryParams,
  MonitorResponse,
  NewApiKeyBody,
  NewApiKeySchema,
  OrgUsage,
  OrgUsageRequest,
  OrganizationBaseSchema,
  OrganizationBillingSchema,
  OrganizationChangePaymentPlanSchema,
  OrganizationCreateMemberRequest,
  OrganizationCreateRoleRequest,
  OrganizationDashboardSchema,
  OrganizationPatchMemberRequest,
  OrganizationPatchRoleRequest,
  OrganizationRoleSchema,
  OrganizationSchema,
  PaginationQueryParams,
  PatchAnnotationQueueBody,
  PatchAnnotationQueueRunBody,
  PatchFeedbackBody,
  PatchSessionBody,
  PatchWorkspaceBody,
  PermissionSchema,
  PlaygroundInvokeDatasetRequestSchema,
  PlaygroundInvokeRequestSchema,
  PlaygroundSettingsSchema,
  PopulateAnnotationQueueBody,
  PostAnnotationQueueBody,
  PostFeedbackBody,
  PostOrgMemberBody,
  PostOrganizationBody,
  PostTenantBody,
  PostWorkspaceBatchMemberBody,
  PostWorkspaceMemberBody,
  ProjectPlaygroundSchema,
  PromptOptimizationJob,
  PromptOptimizationJobWithLogs,
  PromptOptimizationRequest,
  PromptOptimizationResponse,
  QueryFeedbackDelta,
  RepoCommitTag,
  RepoCommitTagCreateBody,
  RepoCommitTagUpdateBody,
  ResourceTag,
  ResourceTagKey,
  ResourceTagKeyCreate,
  ResourceTagKeyUpdate,
  ResourceTagKeyWithValues,
  ResourceTagKeyWithValuesAndTaggings,
  ResourceTagValue,
  ResourceTagValueCreate,
  ResourceTagValueUpdate,
  ResourceTagging,
  ResourceTaggingCreate,
  ResourceType,
  RuleLogSchema,
  RuleMutateBody,
  RuleSchema,
  RuleTriggerBody,
  RunGroupStatsSchema,
  RunGroupsRequest,
  RunGroupsResponse,
  RunGroupsStatsRequest,
  RunSchema,
  RunStatsSchema,
  SSOEmailVerificationConfirmRequest,
  SSOEmailVerificationSendRequest,
  SSOEmailVerificationStatusRequest,
  SSOEmailVerificationStatusResponse,
  SSOLoginSettings,
  SSOSettings,
  SSOSettingsCreate,
  SSOSettingsUpdate,
  SearchDatasetRequest,
  SearchDatasetResponse,
  SessionFeedbackDelta,
  SessionMetadataResponse,
  SessionSchema,
  StripeBusinessInfo,
  StripePaymentInformation,
  StripeSetupIntentResponse,
  TTLSetting,
  TenantBulkUnshareRequest,
  TenantSchema,
  TenantSecret,
  TenantSharedEntityResponse,
  TenantUsageLimitsResponse,
  TenantsStatsResponse,
  UpdateFeedbackConfigSchema,
  UpdateOrganizationInfoBody,
  UpdatePlaygroundSettingsBody,
  UpdateRepoBody,
  UpdateSSOOnlyRequest,
  UpsertTTLSettingsRequest,
  UpsertUserConfiguredUsageLimit,
  UserConfiguredUsageLimitsResponse,
} from '../types/schema';
import {
  apiACEPath,
  apiAnnotationQueuesPath,
  apiChartsPath,
  apiChartsSectionPath,
  apiDatasetsPath,
  apiExamplesCountPath,
  apiExamplesPath,
  apiFeedbackConfigsPath,
  apiFeedbackPath,
  apiHealthPath,
  apiKeyPath,
  apiModelPriceMapPath,
  apiOrgTTLSettingsPath,
  apiOrganizationsPath,
  apiPlaygroundSettingsPath,
  apiPromptOptimizationJobPath,
  apiPromptsPath,
  apiPublicPath,
  apiRunsPath,
  apiSSOPath,
  apiSessionsPath,
  apiTTLSettingsPath,
  apiTenantsPath,
  apiUsageLimitsPath,
  apiWorkspacesPath,
  appIdentityAnnotationQueuesPath,
  backendAuthType,
  backendUrl,
  getAlertsPath,
  getPlaygroundPath,
  hostApiPath,
  hostApiProjectsPath,
  hubApiCommentsPath,
  hubApiCommitsPath,
  hubApiEventsPath,
  hubApiLikesPath,
  hubApiReposPath,
  hubApiSettingsPath,
} from '../utils/constants';
import { useAuth } from './useAuth';
import { useStoredOrganizationId } from './useStoredOrganizationId';

export function matchKeyPartial(...sources: FetcherParams[] | string[]) {
  return (key: FetcherParams | undefined | null) => {
    if (key == null) return false;
    return sources.some((s) => isMatch(key, s));
  };
}

export function matchKeyPartialWith(
  sources: FetcherParams[],
  customComparator: (key: FetcherParams, source: FetcherParams) => boolean
) {
  return (key: FetcherParams | undefined | null) => {
    if (key == null) return false;
    return sources.some((s) => customComparator(key, s));
  };
}

export function useOrganizationId() {
  const { organizationId } = useParams();
  const [searchParams] = useSearchParams();
  const rtn = organizationId ?? searchParams.get('organizationId');
  if (rtn === 'null') return null;
  return rtn;
}

// remove the above after migration to new hoook
export function useWorkspaceId() {
  const { organizationId } = useParams();
  const [searchParams] = useSearchParams();
  const rtn = organizationId ?? searchParams.get('organizationId');
  if (!rtn || rtn === 'null') return null;
  return rtn;
}

export function useSWR<Data, QueryParams = never, BodyParams = never>(
  params: FetcherParams<QueryParams, BodyParams> | null,
  options: SWRConfiguration<Data> = {},
  abortController?: AbortController
) {
  const tenantId = useOrganizationId();
  const innerTenantId = params?.headers?.['X-Tenant-Id'] ?? tenantId;

  const { orgId } = useStoredOrganizationId();
  const needsOrgAuth =
    params?.url.includes(apiOrganizationsPath) ||
    params?.url.includes(apiWorkspacesPath) ||
    params?.url.includes(hostApiPath);

  return useSWRBase<Data, Error, FetcherParams<QueryParams, BodyParams> | null>(
    params &&
      (innerTenantId ||
        params.skipTenantHeaders ||
        backendAuthType === 'desktop')
      ? {
          ...params,
          headers: {
            ...(!(params.skipTenantHeaders || backendAuthType === 'desktop')
              ? { 'X-Tenant-Id': innerTenantId }
              : {}),
            ...(needsOrgAuth &&
            !(params.skipOrgHeaders || backendAuthType === 'desktop')
              ? { 'X-Organization-Id': orgId }
              : {}),
            ...params.headers,
          },
        }
      : null,
    (urlParams) =>
      fetcher<Data, QueryParams, BodyParams>(urlParams, abortController),
    { ...DEFAULT_OPTIONS, ...options }
  );
}

function useSWRObservable<Data, QueryParams = never, BodyParams = never>(
  params: SubscriberParams<QueryParams, BodyParams> | null,
  options: SWRConfiguration<Data> = {}
) {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  const innerOrganizationId =
    params?.headers?.['X-Tenant-Id'] ?? organizationId;

  const key =
    params &&
    (innerOrganizationId ||
      params.skipTenantHeaders ||
      backendAuthType === 'desktop')
      ? {
          ...params,
          headers: {
            ...(!(params.skipTenantHeaders || backendAuthType === 'desktop')
              ? { 'X-Tenant-Id': innerOrganizationId }
              : {}),
            ...(!(params.skipOrgHeaders || backendAuthType === 'desktop')
              ? { 'X-Organization-Id': orgId }
              : {}),
            ...params.headers,
          },
        }
      : null;

  return useSWRObservableBase<
    Data,
    Error,
    SubscriberParams<QueryParams, BodyParams> | null
  >(key, subscriber, { ...DEFAULT_OPTIONS, ...options });
}

export const useWorkspaceList = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<TenantSchema[]>(
    options?.skip ? null : { url: `${apiWorkspacesPath}` },
    options
  );
};

export const useTenantList = (
  options?: SWRConfiguration & { skip?: boolean; skipCreate?: boolean }
) => {
  return useSWR<TenantSchema[], { skip_create?: boolean }>(
    options?.skip
      ? null
      : {
          url: `${apiTenantsPath}`,
          params: {
            skip_create: options?.skipCreate ?? true,
          },
          skipTenantHeaders: true,
        },
    options
  );
};

export const useOrgWorkspaces = () => {
  const auth = useAuth();
  const orgQuery = useCurrentOrganization();
  const tenantSwr = useWorkspaceList({ skip: !auth.authed });

  const tenants: TenantSchema[] = useMemo(() => {
    return (
      tenantSwr?.data?.filter(
        (tenant) =>
          // get personal tenant if no organizationId
          tenant.organization_id === orgQuery.data?.id
      ) ?? []
    );
  }, [orgQuery.data, tenantSwr.data]);

  tenants.sort((a, b) => (a.display_name < b.display_name ? -1 : 1));

  return {
    ...tenantSwr,
    data: tenants,
    isLoading: auth.loading || tenantSwr.isLoading || orgQuery.isLoading,
  };
};

export const useSelectedTenant = (skip?: boolean) => {
  const auth = useAuth();
  const organizationId = useOrganizationId();
  const tenantSwr = useTenantList({ skip: !auth.authed || skip });

  const tenant: TenantSchema | undefined = tenantSwr?.data?.find((tenant) =>
    // get personal tenant if no organizationId
    organizationId ? tenant.id === organizationId : tenant.is_personal
  );

  return {
    ...tenantSwr,
    data: tenant,
    isLoading: auth.loading || tenantSwr.isLoading,
  };
};

export const useSelectedWorkspace = (skip?: boolean) => {
  const auth = useAuth();
  const organizationId = useOrganizationId();
  const tenantSwr = useWorkspaceList({ skip: !auth.authed || skip });

  const tenant: TenantSchema | undefined = tenantSwr?.data?.find((tenant) =>
    // get personal tenant if no organizationId
    organizationId ? tenant.id === organizationId : tenant.is_personal
  );

  return {
    ...tenantSwr,
    data: tenant,
    isLoading: auth.loading || tenantSwr.isLoading,
  };
};
export const useWorkspaceStats = (
  params?: { tag_value_id?: string[] },
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<TenantsStatsResponse, { tag_value_id?: string[] }>(
    options?.skip
      ? null
      : { url: `${apiWorkspacesPath}/current/stats`, params },
    options
  );
};

export const useTenantUsageLimits = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<TenantUsageLimitsResponse>(
    options?.skip ? null : { url: `${apiWorkspacesPath}/current/usage_limits` },
    options
  );
};

export const usePatchTenantMember = (
  options?: SWRMutationConfiguration<
    never,
    Error,
    { identity_id: string; role_id: string }
  >
) => {
  const organizationId = useOrganizationId();
  return useSWRMutationBase<
    never,
    Error,
    FetcherParams<never, never>,
    { identity_id: string; role_id: string }
  >(
    {
      method: 'PATCH',
      url: `${apiWorkspacesPath}/current/members/identity_id`,
      headers: { 'X-Tenant-Id': organizationId },
    },
    (key, { arg: { identity_id, role_id } }) =>
      fetcher({
        ...key,
        url: `${apiWorkspacesPath}/current/members/${identity_id}`,
        json: { role_id },
      }),
    {
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${apiWorkspacesPath}/current/members/active`,
          })
        );
      },
    }
  );
};

export const useInviteWorkspaceMemberMutation = () => {
  const workspaceId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<
    void,
    Error,
    FetcherParams<never, never>,
    PostWorkspaceMemberBody[]
  >(
    { method: 'POST', url: `${apiWorkspacesPath}/current/members` },
    async (key, { arg }) => {
      // TODO: replace with proper bulk email invite
      await Promise.all(
        arg.map(({ user_id, role_id }) =>
          fetcher({
            ...key,
            url: `${apiWorkspacesPath}/current/members`,
            json: { user_id, role_id },
            headers: {
              'X-Tenant-Id': workspaceId,
              'X-Organization-Id': orgId,
            },
          })
        )
      );
    },
    {
      onSuccess: () =>
        mutate(
          matchKeyPartial({
            url: `${apiWorkspacesPath}/current/members/active`,
          })
        ),
    }
  );
};

export const useInviteWorkspaceMembersBatchMutation = () => {
  return useSWRMutation<never, PostWorkspaceBatchMemberBody[]>(
    { method: 'POST', url: `${apiWorkspacesPath}/current/members/batch` },

    {
      onSuccess: () =>
        mutate(
          matchKeyPartial({
            url: `${apiWorkspacesPath}/current/members/active`,
          })
        ),
    }
  );
};

export const usePendingTenants = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<TenantSchema[]>(
    options?.skip
      ? null
      : {
          url: `${apiWorkspacesPath}/pending`,
          skipOrgHeaders: true,
          skipTenantHeaders: true,
        },
    options
  );
};

export const usePendingTenantsDelete = () => {
  const organizationId = useOrganizationId();
  return useSWRMutationBase<never, Error, FetcherParams<never, never>, string>(
    { method: 'DELETE', url: `${apiWorkspacesPath}/pending/tenantId` },
    (key, { arg: tenantId }) =>
      fetcher({
        ...key,
        url: `${apiWorkspacesPath}/pending/${tenantId}`,
        headers: { 'X-Tenant-Id': organizationId },
      }),
    {
      onSuccess: () =>
        // invalidate org and workspace pending lists along with user-specific pending lists
        mutate(matchKeyPartial({ url: `/pending` })),
    }
  );
};

export const usePendingOrgInvites = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<OrganizationBaseSchema[]>(
    options?.skip
      ? null
      : {
          url: `${apiOrganizationsPath}/pending`,
          skipTenantHeaders: true,
          skipOrgHeaders: true,
        },
    options
  );
};

export const useDeletePendingOrgInvite = () => {
  return useSWRMutationBase<never, Error, FetcherParams<never, never>, string>(
    { method: 'DELETE', url: `${apiOrganizationsPath}/pending` },
    (key, { arg: orgId }) =>
      fetcher({
        ...key,
        url: `${apiOrganizationsPath}/pending/${orgId}`,
        headers: { 'X-Organization-Id': orgId },
      }),
    {
      onSuccess: () =>
        // invalidate org and workspace pending lists along with user-specific pending lists
        mutate(matchKeyPartial({ url: `/pending` })),
    }
  );
};

export const useClaimPendingOrgInvite = () => {
  const navigate = useNavigate();
  const tenantId = useOrganizationId();
  return useSWRMutationBase<
    IdentitySchema,
    Error,
    FetcherParams<never, never>,
    string
  >(
    { method: 'POST', url: `${apiOrganizationsPath}/pending/claim` },
    (key, { arg: orgId }) =>
      fetcher({
        ...key,
        url: `${apiOrganizationsPath}/pending/${orgId}/claim`,
        headers: { 'X-Organization-Id': orgId },
      }),
    {
      onSuccess: async (data) => {
        // invalidate org and workspace pending lists along with user-specific pending lists
        await mutate(matchKeyPartial({ url: `/pending` }));
        if (data.tenant_id && data.tenant_id !== tenantId) {
          navigate(`/${apiOrganizationsPath}/${data.tenant_id}`);
        }
      },
    }
  );
};

export const useCreateTenant = (options: {
  onSuccess?: (data: TenantSchema) => void;
}) => {
  const organizationId = useOrganizationId();
  return useSWRMutationBase<
    TenantSchema,
    Error,
    FetcherParams<never, never>,
    PostTenantBody
  >(
    { url: apiTenantsPath, method: 'POST' },
    async (key, { arg }) => {
      // create organization
      const tenant: TenantSchema = await fetcher({
        ...key,
        json: {
          display_name: arg.display_name,
          organization_id: arg.organization_id,
          is_personal: arg.is_personal,
        },
        headers: { 'X-Tenant-Id': organizationId },
      });

      await mutate(
        (key: FetcherParams) =>
          key?.url?.startsWith(apiTenantsPath) ||
          key?.url?.startsWith(apiWorkspacesPath)
      );

      return tenant;
    },
    { onSuccess: options.onSuccess }
  );
};

export const useCreateWorkspace = (options: {
  onSuccess?: (data: TenantSchema) => void;
}) => {
  const organizationId = useOrganizationId();
  return useSWRMutationBase<
    TenantSchema,
    Error,
    FetcherParams<never, never>,
    PostTenantBody
  >(
    { url: apiWorkspacesPath, method: 'POST' },
    async (key, { arg }) => {
      // create organization
      const tenant: TenantSchema = await fetcher({
        ...key,
        json: {
          display_name: arg.display_name,
          organization_id: arg.organization_id,
        },
        headers: {
          'X-Tenant-Id': organizationId,
          'X-Organization-Id': arg.organization_id,
        },
      });

      await mutate(
        (key: FetcherParams) =>
          key?.url?.startsWith(apiTenantsPath) ||
          key?.url?.startsWith(apiWorkspacesPath)
      );

      return tenant;
    },
    { onSuccess: options.onSuccess }
  );
};

export const useUpdateWorkspace = () => {
  const workspaceId = useOrganizationId();
  return useSWRMutation<never, PatchWorkspaceBody>(
    { method: 'PATCH', url: `${apiWorkspacesPath}/${workspaceId}` },
    {
      onSuccess: () => {
        mutate((key: FetcherParams) => key?.url?.startsWith(apiWorkspacesPath));
      },
    }
  );
};

export const useCreateOrganization = () => {
  return useSWRMutation<OrganizationSchema, PostOrganizationBody>(
    { method: 'POST', url: `${apiOrganizationsPath}` },
    {
      onSuccess: () => {
        mutate(
          (key: FetcherParams) =>
            key?.url?.startsWith(apiOrganizationsPath) ||
            key?.url?.startsWith(apiTenantsPath)
        );
      },
    }
  );
};

export const useCurrentOrganization = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  const { authed } = useAuth();
  const { orgId } = useStoredOrganizationId();
  return useSWR<OrganizationSchema, never, never>(
    options?.skip || !authed || !orgId
      ? null
      : {
          url: `${apiOrganizationsPath}/current/info`,
          method: 'GET',
          skipTenantHeaders: true,
        },
    { ...DEFAULT_OPTIONS, ...options }
  );
};

export const useCurrentOrganizationBilling = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<OrganizationBillingSchema, Error, never>(
    options?.skip
      ? null
      : {
          url: `${apiOrganizationsPath}/current/billing`,
          method: 'GET',
          skipTenantHeaders: true,
        },
    { ...DEFAULT_OPTIONS, ...options }
  );
};

export const useUpdateOrganization = () => {
  return useSWRMutation<never, UpdateOrganizationInfoBody>(
    { method: 'PATCH', url: `${apiOrganizationsPath}/current/info` },
    {
      onSuccess: () => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(`${apiOrganizationsPath}/current/info`)
        );
      },
    }
  );
};

export const useOrganizations = (
  options?: SWRConfiguration & { skip?: boolean; skipCreate?: boolean }
) => {
  return useSWR<OrganizationBaseSchema[], { skip_create?: boolean }>(
    options?.skip
      ? null
      : {
          url: apiOrganizationsPath,
          method: 'GET',
          skipOrgHeaders: true,
          skipTenantHeaders: true,
          params: {
            skip_create: options?.skipCreate ?? true,
          },
        },
    options
  );
};

export const useInviteOrgMembersMutation = (basicAuth: boolean) => {
  return useSWRMutation<IdentitySchema[], PostOrgMemberBody[]>(
    {
      method: 'POST',
      url: `${apiOrganizationsPath}/current/members/${
        basicAuth ? 'basic/' : ''
      }batch`,
    },
    {
      onSuccess: () => {
        mutate(
          matchKeyPartial({
            url: `${apiOrganizationsPath}/current/members/active`,
          })
        );
        mutate(
          matchKeyPartial({
            url: `${apiOrganizationsPath}/current/members/pending`,
          })
        );
      },
    }
  );
};

export const useEmbeddableDashboard = (
  dashboardType: 'invoices' | 'usage' | 'credits',
  dashboardColorScheme?: 'light' | 'dark',
  options?: SWRConfiguration & { skip?: boolean }
) => {
  const colorScheme = dashboardColorScheme
    ? `&color_scheme=${dashboardColorScheme}`
    : '';
  return useSWR<OrganizationDashboardSchema, Error, never>(
    options?.skip
      ? null
      : {
          url: `${apiOrganizationsPath}/current/dashboard?type=${dashboardType}${colorScheme}`,
          method: 'GET',
        },
    { ...DEFAULT_OPTIONS, ...options }
  );
};

export const useUserConfiguredUsageLimits = (
  options?: SWRConfiguration & { skip?: boolean; workspaceId?: string }
) => {
  return useSWR<UserConfiguredUsageLimitsResponse>(
    options?.skip
      ? null
      : {
          url: `${apiUsageLimitsPath}/`,
          method: 'GET',
          // This was added to allow for flexibility when retrieving usage limits for a workspace
          // for the new IA refactored workspace usage limit table.
          // By default, this uses the tenantId from useOrganizationId() in the url pa.
          ...(options?.workspaceId
            ? { headers: { 'X-Tenant-Id': options.workspaceId } }
            : {}),
        },
    { ...DEFAULT_OPTIONS, ...options }
  );
};

export const useOrgUserConfiguredUsageLimits = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWR<UserConfiguredUsageLimitsResponse>(
    options?.skip
      ? null
      : {
          url: `${apiUsageLimitsPath}/org`,
          method: 'GET',
          headers: { 'X-Organization-Id': orgId },
        },
    { ...DEFAULT_OPTIONS, ...options }
  );
};

export const useUpsertUserConfiguredUsageLimits = (
  options?: UseSWRMutationOptions<never, UpsertUserConfiguredUsageLimit> & {
    skipTenantHeaders?: boolean;
  }
) => {
  return useSWRMutation<never, UpsertUserConfiguredUsageLimit>(
    {
      method: 'PUT',
      url: `${apiUsageLimitsPath}/`,
      skipTenantHeaders: options?.skipTenantHeaders,
    },
    {
      ...options,
      onSuccess: (data, key, config) => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiUsageLimitsPath)
        );
        options?.onSuccess?.(data, key, config);
      },
    }
  );
};

export const useTTLSettings = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<TTLSetting[]>(
    options?.skip
      ? null
      : {
          url: `${apiTTLSettingsPath}`,
          method: 'GET',
        },
    { ...DEFAULT_OPTIONS, ...options }
  );
};

export const useUpsertTTLSettings = (
  options?: UseSWRMutationOptions<never, UpsertTTLSettingsRequest>
) => {
  return useSWRMutation<never, UpsertTTLSettingsRequest>(
    { method: 'PUT', url: `${apiTTLSettingsPath}` },
    {
      ...options,
      onSuccess: (data, key, config) => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiTTLSettingsPath)
        );
        options?.onSuccess?.(data, key, config);
      },
    }
  );
};

export const useUpsertOrgTTLSettings = (
  options?: UseSWRMutationOptions<never, UpsertTTLSettingsRequest>
) => {
  return useSWRMutation<never, UpsertTTLSettingsRequest>(
    { method: 'PUT', url: `${apiOrgTTLSettingsPath}` },
    {
      ...options,
      onSuccess: (data, key, config) => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiTTLSettingsPath)
        );
        options?.onSuccess?.(data, key, config);
      },
    }
  );
};

export const useSetupOrganizationPaymentMutation = (
  onSuccess?: (data: StripeSetupIntentResponse) => void
) => {
  return useSWRMutation<StripeSetupIntentResponse, never>(
    { method: 'POST', url: `${apiOrganizationsPath}/current/setup` },
    {
      onSuccess: (data) => {
        mutate(matchKeyPartial({ url: `${apiOrganizationsPath}/current` }));
        onSuccess?.(data);
      },
    }
  );
};

export const usePaymentMethodCreatedMutation = (
  onFinish?: (error?: Error) => void
) => {
  return useSWRMutation<never, StripePaymentInformation>(
    { method: 'POST', url: `${apiOrganizationsPath}/current/payment-method` },
    {
      onSuccess: () => {
        mutate(matchKeyPartial({ url: `${apiOrganizationsPath}/current` }));
        onFinish?.();
      },
      onError: (error) => {
        console.error(error);
        onFinish?.(error);
      },
    }
  );
};

export const useChangePaymentPlanMutation = (options?: {
  onSuccess?: () => void;
  onError?: (e: Error) => void;
}) => {
  return useSWRMutation<
    FetcherParams<Record<string, string[]>, never>,
    OrganizationChangePaymentPlanSchema
  >(
    { method: 'POST', url: `${apiOrganizationsPath}/current/plan` },
    {
      ...options,
      onSuccess: () => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiOrganizationsPath)
        );
        options?.onSuccess?.();
      },
      onError: (e) => {
        options?.onError?.(e);
      },
    }
  );
};

export const useBusinessInfo = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<StripeBusinessInfo>(
    options?.skip
      ? null
      : {
          url: `${apiOrganizationsPath}/current/business-info`,
          method: 'GET',
        },
    { ...DEFAULT_OPTIONS, ...options }
  );
};

export const useSetBusinessInfo = (
  options?: UseSWRMutationOptions<never, StripeBusinessInfo>
) => {
  return useSWRMutation<never, StripeBusinessInfo>(
    { method: 'POST', url: `${apiOrganizationsPath}/current/business-info` },
    {
      ...options,
      onSuccess: (...args) => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(`${apiOrganizationsPath}/current/business-info`)
        );
        options?.onSuccess?.(...args);
      },
      onError: (e, ...args) => {
        console.error(e);
        options?.onError?.(e, ...args);
      },
    }
  );
};

type onPaymentMethodSuccessParams = {
  paymentInfo: StripePaymentInformation;
  updatedInfo: StripeBusinessInfo;
  isBusiness: boolean;
};

export function useOnPaymentMethodSuccess() {
  const { trigger: setBusinessInfo, isMutating: settingBusinessInfo } =
    useSetBusinessInfo();

  const { trigger: createPaymentMethod, isMutating: creatingPaymentMethod } =
    usePaymentMethodCreatedMutation();

  const useMutationRtn = useSWRMutationBase<
    void,
    Error,
    FetcherParams<never, never>,
    onPaymentMethodSuccessParams
  >(
    {
      // this is only used as a key not as an url
      url: 'payment-method-created',
    },
    async (_, { arg }) => {
      const { paymentInfo, updatedInfo, isBusiness } = arg;
      await createPaymentMethod({
        json: paymentInfo,
      });

      if (isBusiness) {
        await setBusinessInfo({
          json: updatedInfo,
        });
      }
    }
  );

  return {
    ...useMutationRtn,
    isMutating: settingBusinessInfo || creatingPaymentMethod,
  };
}

type RunsSchemaWithOffsetPagination = RunSchema[] & {
  headers?: {
    'x-pagination-total': number;
    'x-pagination-has-more': string;
    'x-query-parsed'?: string;
    'x-query-run-id'?: string;
  };
};

type RunsSchemaWithCursorPagination = {
  runs: RunSchema[];
  cursors: {
    next: string | null;
    previous: string | null;
  };
  parsed_query?: string;
};

export const useRunsInfinite = (
  params: GetRunsQueryParams | null,
  shareToken?: { datasetShareToken?: string; runShareToken?: string },
  options?: SWRConfiguration
) => {
  const PAGE_SIZE = params?.limit ?? 15;
  const organizationId = useOrganizationId();

  const getKey = (
    _pageIndex,
    previousPageData: RunsSchemaWithOffsetPagination | null
  ) => {
    const requiredOneOf = [
      'session',
      'id',
      'parent_run',
      'trace',
      'reference_example',
    ];
    const hasRequiredOneOf = requiredOneOf.some((key) => key in (params ?? {}));

    const payload: FetcherParams<never, GetRunsQueryParams> | null =
      !params || !hasRequiredOneOf
        ? null
        : shareToken?.datasetShareToken
        ? {
            url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/runs/query`,
            json: params,
            method: 'POST',
            skipTenantHeaders: true,
          }
        : shareToken?.runShareToken
        ? {
            url: `${apiPublicPath}/${shareToken.runShareToken}/runs/query`,
            json: params,
            method: 'POST',
            skipTenantHeaders: true,
          }
        : {
            url: `${apiRunsPath}/query`,
            json: params,
            method: 'POST',
            headers: { 'X-Tenant-Id': organizationId },
          };

    if (payload == null) return null;

    const cursor =
      previousPageData?.headers?.['x-pagination-has-more'] !== 'false'
        ? previousPageData?.headers?.['x-pagination-has-more']
        : undefined;
    payload.json = {
      ...payload.json,
      limit: PAGE_SIZE,
      cursor: cursor ?? undefined,
    };
    return payload;
  };

  return useSWRInfinite<RunSchema[], Error, typeof getKey>(
    getKey,
    (arg) => {
      return fetcher(arg).then((res) => {
        const castRes = res as RunsSchemaWithCursorPagination;
        const runs: RunsSchemaWithOffsetPagination = castRes.runs;
        runs.headers = {
          'x-pagination-total': runs.length,
          'x-pagination-has-more': castRes.cursors.next ?? 'false',
          'x-query-parsed': castRes.parsed_query,
        };
        return runs;
      });
    },
    { ...DEFAULT_INFINITE_OPTIONS, ...options }
  );
};

export const useRunsQuery = (
  params: GetRunsQueryParams | null,
  shareToken?: { datasetShareToken?: string; runShareToken?: string },
  options?: SWRConfiguration
) => {
  const organizationId = useOrganizationId();
  const payload: FetcherParams<never, GetRunsQueryParams> | null = !params
    ? null
    : shareToken?.datasetShareToken
    ? {
        url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/runs/query`,
        json: params,
        method: 'POST',
        skipTenantHeaders: true,
      }
    : shareToken?.runShareToken
    ? {
        url: `${apiPublicPath}/${shareToken.runShareToken}/runs/query`,
        json: params,
        method: 'POST',
        skipTenantHeaders: true,
      }
    : {
        url: `${apiRunsPath}/query`,
        json: params,
        method: 'POST',
        headers: { 'X-Tenant-Id': organizationId },
      };

  return useSWR<
    RunsSchemaWithCursorPagination,
    never,
    GetRunsQueryParams | never
  >(payload, options);
};

export const useRunStats = (
  params: GetRunsQueryParams | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  const organizationId = useOrganizationId();
  const payload: FetcherParams<never, GetRunsQueryParams> | null = !params
    ? null
    : shareToken?.datasetShareToken
    ? {
        url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/runs/stats`,
        json: params,
        method: 'POST',
        skipTenantHeaders: true,
      }
    : {
        url: `${apiRunsPath}/stats`,
        json: params,
        method: 'POST',
        headers: { 'X-Tenant-Id': organizationId },
      };

  return useSWR<RunStatsSchema, never, GetRunsQueryParams | never>(
    payload,
    options
  );
};

export const useExampleRunsInfinite = (
  page_size: number,
  datasetId: string | null,
  params: GetExampleRunsQueryParams | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRInfiniteConfiguration
) => {
  const organizationId = useOrganizationId();
  const getKey = (pageIndex, previousPageData) => {
    if (previousPageData && !previousPageData.length) return null;
    return !(params && (datasetId || shareToken?.datasetShareToken))
      ? null
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/examples/runs`,
          method: 'POST',
          json: {
            ...params,
            limit: page_size,
            offset: pageIndex * page_size,
          },
          skipTenantHeaders: true,
        }
      : {
          url: `${apiDatasetsPath}/${datasetId}/runs`,
          method: 'POST',
          json: {
            ...params,
            limit: page_size,
            offset: pageIndex * page_size,
          },
          headers: { 'X-Tenant-Id': organizationId },
        };
  };

  return useSWRInfinite<
    ExampleSchemaWithRuns[] & {
      headers?: { 'x-pagination-total': number };
    },
    Error,
    typeof getKey
  >(getKey, fetcher, { ...DEFAULT_INFINITE_OPTIONS, ...options });
};

export const useDatasetFeedbackDelta = (
  datasetId: string | null,
  params: QueryFeedbackDelta | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  return useSWR<SessionFeedbackDelta, never, QueryFeedbackDelta>(
    !(params && (datasetId || shareToken?.datasetShareToken))
      ? null
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/runs/delta`,
          method: 'POST',
          json: params,
          skipTenantHeaders: true,
        }
      : {
          url: `${apiDatasetsPath}/${datasetId}/runs/delta`,
          method: 'POST',
          json: params,
        },
    options
  );
};

export const useChildRunsMap = (
  params: GetRunsQueryParams | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  const hook = useRunsQuery(params, shareToken, options);
  return useMemo(() => {
    return {
      ...hook,
      data: Object.entries(
        groupBy(hook.data?.runs ?? [], 'parent_run_id')
      ).reduce(
        (acc, [parent_run, val]) => ({
          ...acc,
          [parent_run]: sortBy(val, 'dotted_order'),
        }),
        {} as Record<string, RunSchema[]>
      ),
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hook.data]);
};

export interface RunContents {
  inputs: {
    data: RunSchema['inputs'];
    loading: boolean;
    error: unknown | undefined;
  };
  outputs: {
    data: RunSchema['outputs'];
    loading: boolean;
    error: unknown | undefined;
  };
  error: {
    data: RunSchema['error'];
    loading: boolean;
    error: unknown | undefined;
  };
}

// SWR does not have a mechanism to alter the stableHash
// function used for accessing the cache. Under the hood
// will SWR use Object.keys to serialise, thus we can
// mask undesired keys from cache by marking those as not
// enumerable.
export const getS3StableKey = (
  s3RunKey: string,
  signedUrl: string
): { s3RunKey: string; signedUrl: string } => {
  return Object.create(Object.prototype, {
    s3RunKey: {
      value: s3RunKey,
      enumerable: true,
      configurable: true,
    },
    signedUrl: {
      value: signedUrl,
      enumerable: false,
      configurable: false,
    },
  });
};

export const useS3RunInputOutputs = <
  T extends keyof Pick<RunSchema, 'inputs' | 'outputs' | 'error'>
>(
  params: { s3RunKey: string; signedUrl: string } | undefined | null
) => {
  return useSWRBase<RunSchema[T], Error>(
    params ?? null,
    ({ signedUrl }) => fetcher(signedUrl),
    {
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );
};

export function useRunContents({
  run,
  withContents,
}: {
  run: Pick<SWRResponse<RunSchema>, 'data' | 'isLoading'>;
  withContents: boolean;
}) {
  const inputSignedUrl = run.data?.inputs_s3_urls?.['ROOT']?.presigned_url;
  const outputSignedUrl = run.data?.outputs_s3_urls?.['ROOT']?.presigned_url;
  const errorSignedUrl = run.data?.s3_urls?.error?.presigned_url;

  const s3InputsKey =
    run.data?.id && inputSignedUrl ? `${run.data.id}-inputs` : undefined;
  const s3Inputs = useS3RunInputOutputs<'inputs'>(
    s3InputsKey && inputSignedUrl && withContents
      ? getS3StableKey(s3InputsKey, inputSignedUrl)
      : null
  );
  const s3InputsData = s3InputsKey ? s3Inputs.data : undefined;

  const s3OutputsKey =
    run.data?.id && outputSignedUrl ? `${run.data.id}-outputs` : undefined;
  const s3Outputs = useS3RunInputOutputs<'outputs'>(
    s3OutputsKey && outputSignedUrl && withContents
      ? getS3StableKey(s3OutputsKey, outputSignedUrl)
      : null
  );
  const s3OutputsData = s3OutputsKey ? s3Outputs.data : undefined;

  const s3ErrorKey =
    run.data?.id && errorSignedUrl ? `${run.data.id}-error` : undefined;
  const s3Error = useS3RunInputOutputs<'error'>(
    s3ErrorKey && errorSignedUrl && withContents
      ? getS3StableKey(s3ErrorKey, errorSignedUrl)
      : null
  );
  const s3ErrorData = s3ErrorKey ? s3Error.data : undefined;

  const contents = useMemo(() => {
    if (!withContents) return undefined;
    const runInputs = s3InputsData ?? run.data?.inputs;
    const runOutputs = s3OutputsData ?? run.data?.outputs;
    const runError = s3ErrorData ?? run.data?.error;

    return {
      inputs: {
        data: runInputs,
        loading: runInputs == null && (run.isLoading || s3Inputs.isLoading),
        error: s3Inputs.error,
      },
      outputs: {
        data: runOutputs,
        loading: runOutputs == null && (run.isLoading || s3Outputs.isLoading),
        error: s3Outputs.error,
      },
      error: {
        data: runError,
        loading: runError == null && (run.isLoading || s3Error.isLoading),
        error: s3Error.error,
      },
    };
  }, [
    withContents,
    s3InputsData,
    s3Inputs.isLoading,
    s3Inputs.error,
    run.data,
    run.isLoading,
    s3OutputsData,
    s3Outputs.isLoading,
    s3Outputs.error,
    s3ErrorData,
    s3Error.isLoading,
    s3Error.error,
  ]);

  return { contents };
}

export function useRun(params: {
  id: string | null | undefined;
  options?: SWRConfiguration<RunSchema>;
  shareToken?: { datasetShareToken?: string; runShareToken?: string };
  withContents?: boolean;
  excludeSerialized?: boolean;
  sessionId?: string;
  startTime?: string;
}): SWRResponse<RunSchema> & { contents?: RunContents } {
  const { id, options, shareToken } = params;

  const swrParams = {
    exclude_s3_stored_attributes: true,
    exclude_serialized: params.excludeSerialized ?? false,
    session_id: params.sessionId,
    start_time: params.startTime,
  };

  const run = useSWR<
    RunSchema,
    { exclude_s3_stored_attributes?: boolean; exclude_serialized?: boolean }
  >(
    !id && shareToken?.runShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.runShareToken}/run`,
          params: swrParams,
          skipTenantHeaders: true,
        }
      : !id
      ? null
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/runs/${id}`,
          params: swrParams,
          skipTenantHeaders: true,
        }
      : shareToken?.runShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.runShareToken}/run/${id}`,
          params: swrParams,
          skipTenantHeaders: true,
        }
      : {
          url: `${apiRunsPath}/${id}`,
          params: swrParams,
        },
    options
  );

  const { contents } = useRunContents({
    run,
    withContents: !!params.withContents,
  });

  const data = useMemo(() => {
    if (!params.withContents || !run.data) return run.data;
    return {
      ...run.data,
      inputs: contents?.inputs.data ?? run.data?.inputs,
      outputs: contents?.outputs.data ?? run.data?.outputs,
      error: contents?.error.data ?? run.data?.error,
    };
  }, [
    params.withContents,
    contents?.inputs.data,
    contents?.outputs.data,
    contents?.error.data,
    run.data,
  ]);

  return { ...run, data, contents };
}

export const useDataset = (
  id?: string,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  return useSWR<DatasetSchema>(
    shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets`,
          skipTenantHeaders: true,
        }
      : id
      ? { url: `${apiDatasetsPath}/${id}` }
      : null,
    options
  );
};

export const useDatasetVersionsInfinite = (
  page_size: number,
  dataset_id: string | null,
  params: GetDatasetVersionsQueryParams | null,
  options?: SWRInfiniteConfiguration
) => {
  const organizationId = useOrganizationId();
  const getKey: SWRInfiniteKeyLoader<
    GroupedDatasetVersionSchema[],
    FetcherParams<GetDatasetVersionsQueryParams, never> | null
  > = (pageIndex, previousPageData) => {
    if (previousPageData && !previousPageData.length) return null;
    return params
      ? {
          url: `${apiDatasetsPath}/${dataset_id}/versions`,
          params: {
            ...params,
            limit: page_size,
            offset: pageIndex * page_size,
          },
          headers: { 'X-Tenant-Id': organizationId },
        }
      : null;
  };
  return useSWRInfinite<
    GroupedDatasetVersionSchema[] & {
      headers?: { 'x-pagination-total': number };
    },
    Error,
    SWRInfiniteKeyLoader<
      GroupedDatasetVersionSchema[] & {
        headers?: { 'x-pagination-total': number };
      },
      FetcherParams<GetDatasetVersionsQueryParams, never> | null
    >
  >(getKey, fetcher, { ...DEFAULT_INFINITE_OPTIONS, ...options });
};

export const useDatasetSingleVersion = (
  dataset_id: string | null,
  params: GetSingleDatasetVersionsQueryParams | null,
  options?: SWRConfiguration
) => {
  return useSWR<
    GroupedDatasetVersionSchema,
    GetSingleDatasetVersionsQueryParams
  >(
    !dataset_id || !params
      ? null
      : { url: `${apiDatasetsPath}/${dataset_id}/version`, params },
    options
  );
};

export const useDatasetVersionMutation = (
  dataset_id: string,
  options?: UseSWRMutationOptions<never, DatasetVersionPutBody>
) => {
  return useSWRMutation<never, DatasetVersionPutBody>(
    {
      url: `${apiDatasetsPath}/${dataset_id}/tags`,
      method: 'PUT',
    },
    {
      ...options,
      onSuccess: (...args) => {
        mutate(
          matchKeyPartial({ url: `${apiDatasetsPath}/${dataset_id}/versions` })
        );
        options?.onSuccess?.(...args);
      },
    }
  );
};

export const useDatasetVersionsDiff = (
  dataset_id: string | null,
  params: GetDatasetVersionsDiffQueryParams | null,
  options?: SWRConfiguration
) => {
  return useSWR<DatasetVersionsDiffSchema, GetDatasetVersionsDiffQueryParams>(
    !dataset_id || !params
      ? null
      : { url: `${apiDatasetsPath}/${dataset_id}/versions/diff`, params },
    options
  );
};

export const useExample = (
  id: string | null,
  params?: GetExampleQueryParams,
  options?: SWRConfiguration
) => {
  return useSWR<ExampleSchema, GetExampleQueryParams>(
    id ? { url: `${apiExamplesPath}/${id}`, params: params ?? {} } : null,
    options
  );
};

export const useDeleteExample = (
  options?: UseSWRMutationOptions<void, void, { exampleId: string }>
) => {
  return useSWRMutation<void, void, { exampleId: string }>(
    { url: `${apiExamplesPath}/:exampleId`, method: 'DELETE' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: apiExamplesPath }));
        mutate(matchKeyPartial({ url: `${apiExamplesPath}/count` }));
      },
    }
  );
};

export const useExamples = (
  params: GetExamplesQueryParams | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  return useSWR<
    ExampleSchema[] & { headers?: { 'x-pagination-total': number } },
    GetExamplesQueryParams
  >(
    !params
      ? null
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/examples`,
          params,
          skipTenantHeaders: true,
        }
      : { url: apiExamplesPath, params },
    options
  );
};

export const useCountExamples = (
  params: GetExamplesQueryParams | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  return useSWR<number, GetExamplesQueryParams>(
    !params
      ? null
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/examples/count`,
          params,
          skipTenantHeaders: true,
        }
      : { url: apiExamplesCountPath, params },
    options
  );
};

export const useValidateExample = (
  params: { example?: ExampleValidateSchema },
  options?: SWRConfiguration
) => {
  return useSWR<ExampleValidationResult, never, ExampleValidateSchema>(
    params.example
      ? {
          url: `${apiExamplesPath}/validate`,
          method: 'POST',
          json: params.example,
        }
      : null,
    options
  );
};

export const useDatasetSplits = (
  as_of?: string,
  datasetId?: string,
  options?: SWRConfiguration
) => {
  return useSWR<
    string[] & { headers?: { 'x-pagination-total': number } },
    { as_of?: string }
  >(
    !datasetId
      ? null
      : { url: `${apiDatasetsPath}/${datasetId}/splits`, params: { as_of } },
    options
  );
};

export const useUpdateDatasetSplitsMutation = (
  datasetId?: string,
  options: Parameters<
    typeof useSWRMutation<string[], DatasetSplitsUpdateSchema>
  >[1] = {}
) => {
  return useSWRMutation<
    string[] & { headers?: { 'x-pagination-total': number } },
    DatasetSplitsUpdateSchema
  >(
    {
      url: `${apiDatasetsPath}/${datasetId}/splits`,
      method: 'PUT',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: apiExamplesPath }));
      },
    }
  );
};

export const useSession = (
  id: string | null,
  params?: GetSingleSessionQueryParams,
  options?: SWRConfiguration
) => {
  const observable = useSWRObservable<
    SessionSchema,
    GetSingleSessionQueryParams
  >(
    id
      ? {
          url: `${apiSessionsPath}/${id}`,
          subscriberParser: 'jsonpatch',
          params,
        }
      : null,
    { revalidateOnFocus: false, ...options }
  );

  // hacky way to get the default_dashboard_id from the extra metadata
  // and remove it from the extra metadata.
  // Be very careful about deleting values from observable since data is
  // not a brand new copy of the data
  const session = useMemo(() => {
    const data = observable.data
      ? {
          ...observable.data,
          extra: {
            ...(observable.data?.extra ?? {}),
            metadata: {
              ...(observable.data?.extra?.metadata ?? {}),
            },
          },
          default_dashboard_id:
            (observable.data?.extra?.metadata
              ?.default_dashboard_id as string) ?? undefined,
        }
      : undefined;
    delete data?.extra?.metadata?.default_dashboard_id;
    return data;
  }, [observable.data]);
  return {
    ...observable,
    data: session,
  };
};

export const useSessionNonStream = (
  id: string | null,
  params?: GetSingleSessionQueryParams,
  options?: SWRConfiguration
) => {
  return useSWR<SessionSchema, GetSingleSessionQueryParams>(
    id
      ? {
          url: `${apiSessionsPath}/${id}`,
          params,
        }
      : null,
    { revalidateOnFocus: false, ...options }
  );
};

export const useSessionUpdateMutation = (
  id: string,
  options?: UseSWRMutationOptions<SessionSchema, PatchSessionBody>
) => {
  const swrMutation = useSWRMutation<SessionSchema, PatchSessionBody>(
    { url: `${apiSessionsPath}/${id}`, method: 'PATCH' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return Promise.all([
          mutate(matchKeyPartial({ url: apiSessionsPath })),
          mutate(matchKeyPartial({ url: `${apiSessionsPath}/${id}` })),
        ]);
      },
    }
  );

  return {
    ...swrMutation,
    trigger: (body: { json: PatchSessionBody }) => {
      if (
        body.json.default_dashboard_id ||
        body.json.default_dashboard_id === null
      ) {
        body.json.extra = {
          ...body.json.extra,
          metadata: {
            ...(body.json.extra?.metadata ?? {}),
            default_dashboard_id: body.json.default_dashboard_id,
          },
        };
        delete body.json.default_dashboard_id;
      }
      return swrMutation.trigger(body);
    },
  };
};

export const useCloneDatasetMutation = (
  options: Parameters<
    typeof useSWRMutation<ExampleSchema[], CloneDatasetSchema>
  >[1] = {}
) => {
  return useSWRMutation<ExampleSchema[], CloneDatasetSchema>(
    { url: `${apiDatasetsPath}/clone`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: apiDatasetsPath }));
      },
    }
  );
};

export const useRunsMonitor = (
  params: MonitorQueryParams | null,
  options?: SWRConfiguration
) => {
  return useSWR<MonitorResponse, never, MonitorQueryParams>(
    params
      ? {
          method: 'POST',
          url: `${apiRunsPath}/monitor`,
          json: params,
        }
      : null,
    options
  );
};

export const useSessionMetadata = (
  params: GetSessionsMetadataQueryParams | null,
  session_id?: string,
  options?: SWRConfiguration
) => {
  return useSWR<SessionMetadataResponse, GetSessionsMetadataQueryParams>(
    params && session_id
      ? {
          url: `${apiSessionsPath}/${session_id}/metadata`,
          params,
        }
      : null,
    options
  );
};

export const useSessions = (
  params: GetSessionsQueryParams | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  return useSWRObservable<
    { total?: number; rows?: SessionSchema[]; excluding_empty?: boolean },
    GetSessionsQueryParams
  >(
    !params
      ? null
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/sessions`,
          params,
          subscriberParser: 'jsonpatch',
          skipTenantHeaders: true,
        }
      : { url: apiSessionsPath, params, subscriberParser: 'jsonpatch' },
    options
  );
};

export const usePublicDatasetSessionsBulk = (
  params: {
    share_tokens: string[];
  } | null,
  options?: SWRConfiguration
) => {
  return useSWR<SessionSchema[], { share_tokens: string[] }>(
    params
      ? {
          url: `${apiPublicPath}/datasets/sessions-bulk`,
          params,
          skipTenantHeaders: true,
        }
      : null,
    options
  );
};

export const useDatasets = (
  params: GetDatasetsQueryParams | null,
  skipTenantHeaders?: boolean,
  options?: SWRConfiguration
) => {
  return useSWR<
    DatasetSchema[] & { headers?: { 'x-pagination-total': number } },
    GetDatasetsQueryParams
  >(
    params ? { url: apiDatasetsPath, params: params, skipTenantHeaders } : null,
    options
  );
};

export const useComparativeExperiments = (
  datasetId: string | null,
  params: GetComparativeExperimentsQueryParams | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  return useSWR<
    ComparativeExperimentSchema[] & {
      headers?: { 'x-pagination-total': number };
    },
    GetComparativeExperimentsQueryParams
  >(
    shareToken?.datasetShareToken && params
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/comparative`,
          params,
          skipTenantHeaders: true,
        }
      : params && datasetId
      ? { url: `${apiDatasetsPath}/${datasetId}/comparative`, params: params }
      : null,
    options
  );
};

export const useFeedbacks = (
  params: GetFeedbackQueryParams | null,
  shareToken?: { runShareToken?: string; datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  return useSWR<
    FeedbackSchema[] & { headers?: { 'x-pagination-total': number } },
    GetFeedbackQueryParams
  >(
    !params
      ? null
      : shareToken?.runShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.runShareToken}/feedbacks`,
          params,
          skipTenantHeaders: true,
        }
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/feedback`,
          params,
          skipTenantHeaders: true,
        }
      : { url: apiFeedbackPath, params },
    options
  );
};

export const useFeedbacksInfinite = (
  page_size: number,
  params?: GetFeedbackQueryParams | null,
  options?: SWRInfiniteConfiguration
) => {
  const organizationId = useOrganizationId();
  const getKey: SWRInfiniteKeyLoader<
    FeedbackSchema[],
    FetcherParams<GetFeedbackQueryParams, never> | null
  > = (pageIndex, previousPageData) => {
    if (previousPageData && !previousPageData.length) return null;
    return {
      url: `${apiFeedbackPath}`,
      params: {
        ...params,
        limit: page_size,
        offset: pageIndex * page_size,
      },
      headers: { 'X-Tenant-Id': organizationId },
    };
  };
  return useSWRInfinite<
    FeedbackSchema[] & { headers?: { 'x-pagination-total': number } },
    Error,
    SWRInfiniteKeyLoader<
      FeedbackSchema[] & { headers?: { 'x-pagination-total': number } },
      FetcherParams<GetFeedbackQueryParams, never> | null
    >
  >(getKey, fetcher, { ...DEFAULT_INFINITE_OPTIONS, ...options });
};

// Right now only used to get single feedback by id to display user's username.
export const useFeedback = (
  id?: string | null,
  params?: { include_user_names?: boolean },
  options?: SWRConfiguration
) => {
  return useSWR<FeedbackSchema, { include_user_names?: boolean }>(
    id ? { url: `${apiFeedbackPath}/${id}`, params } : null,
    options
  );
};

export const useAnnotationQueues = (
  params: GetAnnotationQueuesQueryParams | null,
  options?: SWRConfiguration
) => {
  return useSWR<
    AnnotationQueueSchema[] & { headers?: { 'x-pagination-total': number } },
    GetAnnotationQueuesQueryParams
  >(params ? { url: apiAnnotationQueuesPath, params: params } : null, options);
};

export const useAnnotationQueue = (
  queue_id?: string,
  options?: SWRConfiguration
) => {
  return useSWR<AnnotationQueueSchema, never>(
    queue_id ? { url: `${apiAnnotationQueuesPath}/${queue_id}` } : null,
    options
  );
};

export const useAnnotationQueueSize = (
  queue_id: string | undefined,
  options?: SWRConfiguration
) => {
  return useSWR<AnnotationQueueSizeSchema, GetAnnotationQueuesQueryParams>(
    queue_id ? { url: `${apiAnnotationQueuesPath}/${queue_id}/size` } : null,
    options
  );
};

export const useAnnotationQueueTotalSize = (
  queue_id: string | undefined,
  options?: SWRConfiguration
) => {
  return useSWR<AnnotationQueueSizeSchema, never>(
    queue_id
      ? { url: `${apiAnnotationQueuesPath}/${queue_id}/total_size` }
      : null,
    options
  );
};

export const useAnnotationQueueTotalArchivedSize = (
  queue_id: string | undefined,
  params?: GetAnnotationQueueArchivedSizeQueryParams,
  options?: SWRConfiguration
) => {
  return useSWR<
    AnnotationQueueSizeSchema,
    GetAnnotationQueueArchivedSizeQueryParams
  >(
    queue_id
      ? { url: `${apiAnnotationQueuesPath}/${queue_id}/total_archived`, params }
      : null,
    options
  );
};

export const useAnnotationQueueRuns = (
  queue_id: string | undefined,
  params: GetAnnotationQueueRunsQueryParams | null,
  options?: SWRConfiguration
) => {
  return useSWR<
    AnnotationQueueRunSchema[] & { headers?: { 'x-pagination-total': number } },
    GetAnnotationQueueRunsQueryParams
  >(
    params && queue_id
      ? { url: `${apiAnnotationQueuesPath}/${queue_id}/runs`, params: params }
      : null,
    options
  );
};

export const useAnnotationQueueRunsInfinite = (
  page_size: number,
  queue_id: string | undefined,
  params?: GetAnnotationQueueRunsQueryParams,
  options?: SWRInfiniteConfiguration
) => {
  const organizationId = useOrganizationId();
  const getKey = (pageIndex) => {
    if (!queue_id) return null;
    return queue_id
      ? {
          url: `${apiAnnotationQueuesPath}/${queue_id}/runs`,
          params: {
            ...params,
            limit: page_size,
            offset: pageIndex * page_size,
          },
          headers: { 'X-Tenant-Id': organizationId },
        }
      : null;
  };

  return useSWRInfinite<
    AnnotationQueueRunSchema[] & { headers?: { 'x-pagination-total': number } },
    Error,
    typeof getKey
  >(getKey, fetcher, { ...DEFAULT_INFINITE_OPTIONS, ...options });
};

export const useAnnotationQueueRun = (
  queue_id: string | undefined,
  index: number,
  options?: SWRConfiguration
) => {
  const defaultOptions = {
    refreshInterval: 60000,
    ...options,
  };
  return useSWR<AnnotationQueueRunSchema, never>(
    queue_id
      ? { url: `${apiAnnotationQueuesPath}/${queue_id}/run/${index}` }
      : null,
    defaultOptions
  );
};

function replaceTemplateParams(
  url: string,
  templateParams: Record<string, string | null | undefined>,
  queryParams?: Record<string, string>
) {
  // Find all template parts (":something") and replace them
  let replacedUrl = url.replace(/:([\w-]+)(?=\/|$)/g, (_, key) => {
    if (key in templateParams && templateParams[key]) {
      return templateParams[key] || '';
    } else {
      throw new Error(
        `Template parameter "${key}" not provided or is null-ish`
      );
    }
  });

  // Append query parameters
  if (queryParams) {
    const searchParams = new URLSearchParams();
    Object.entries(queryParams).forEach(([key, value]) => {
      searchParams.append(key, value);
    });
    const queryString = searchParams.toString();
    replacedUrl += replacedUrl.includes('?') ? '&' : '?';
    replacedUrl += queryString;
  }
  return replacedUrl;
}

export type UseSWRMutationOptions<
  Res,
  ReqBody,
  TemplateParams extends Record<string, string> = Record<string, never>
> = SWRMutationConfiguration<
  Res,
  Error,
  Partial<FetcherParams<never, ReqBody>> & {
    templateUrlParams?: TemplateParams;
    templateQueryParams?: Record<string, string>;
  },
  FetcherParams<never, ReqBody>
>;
export function useSWRMutation<
  Res,
  ReqBody,
  TemplateParams extends Record<string, string> = Record<string, never>
>(
  params: FetcherParams<never, ReqBody>,
  options: UseSWRMutationOptions<Res, ReqBody, TemplateParams> = {},
  abortController?: AbortController | null
) {
  const tenantId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  const needsOrgAuth =
    params?.url.includes(apiOrganizationsPath) ||
    params?.url.includes(apiWorkspacesPath) ||
    params?.url.includes(hostApiPath);

  return useSWRMutationBase<
    Res,
    Error,
    FetcherParams<never, ReqBody>,
    Partial<FetcherParams<never, ReqBody>> & {
      templateUrlParams?: TemplateParams;
      templateQueryParams?: Record<string, string>;
    }
  >(
    params,
    (key, { arg }) => {
      let newUrl = key.url;
      if (arg.templateUrlParams) {
        const params = arg.templateUrlParams;
        const queryParams = arg.templateQueryParams;
        newUrl = replaceTemplateParams(key.url, params, queryParams);
        if (newUrl.includes(':')) {
          throw new Error('Template URL cannot contain template parameters');
        }
        delete arg.templateUrlParams;
        delete arg.templateQueryParams;
      }
      return fetcher(
        {
          ...key,
          url: newUrl,
          ...arg,
          headers: {
            ...(!(params.skipTenantHeaders || backendAuthType === 'desktop')
              ? { 'X-Tenant-Id': tenantId }
              : {}),
            ...(needsOrgAuth && orgId && backendAuthType !== 'desktop'
              ? { 'X-Organization-Id': orgId }
              : {}),
            ...key.headers,
            ...arg.headers,
          },
        },
        abortController ?? undefined
      );
    },
    options
  );
}

export const useCreateFeedback = (
  options: Parameters<
    typeof useSWRMutation<FeedbackSchema, PostFeedbackBody | PatchFeedbackBody>
  >[1] = {}
) => {
  return useSWRMutation<FeedbackSchema, PostFeedbackBody | PatchFeedbackBody>(
    { url: `${apiFeedbackPath}`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options.onSuccess?.(...args);
      },
    }
  );
};

export const useCreateAnnotationQueueMutation = (
  options: Parameters<
    typeof useSWRMutation<AnnotationQueueSchema, PostAnnotationQueueBody>
  >[1] = {}
) => {
  return useSWRMutation<AnnotationQueueSchema, PostAnnotationQueueBody>(
    { url: apiAnnotationQueuesPath, method: 'POST' },
    {
      ...options,
      onSuccess: () =>
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiAnnotationQueuesPath)
        ),
    }
  );
};

export const usePopulateAnnotationQueueMutation = (
  options: Parameters<
    typeof useSWRMutation<unknown, PopulateAnnotationQueueBody>
  >[1] = {}
) => {
  return useSWRMutation<unknown, PopulateAnnotationQueueBody>(
    { url: `${apiAnnotationQueuesPath}/populate`, method: 'POST' },
    options
  );
};

export const useUpdateAnnotationQueueMutation = (
  params: {
    queue_id: string;
  },
  options: Parameters<
    typeof useSWRMutation<never, PatchAnnotationQueueBody>
  >[1] = {}
) => {
  return useSWRMutation<never, PatchAnnotationQueueBody>(
    {
      url: `${apiAnnotationQueuesPath}/${params.queue_id}`,
      method: 'PATCH',
    },
    {
      ...options,
      onSuccess: () => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiAnnotationQueuesPath)
        );
      },
    }
  );
};

export const useCreateAnnotationQueueRunsMutation = (
  queue_id?: string,
  options: Parameters<
    typeof useSWRMutation<
      AnnotationQueueSchema,
      string[] | PatchAnnotationQueueRunBody
    >
  >[1] = {}
) => {
  return useSWRMutation<
    AnnotationQueueSchema,
    string[] | PatchAnnotationQueueRunBody
  >(
    { url: `${apiAnnotationQueuesPath}/${queue_id}/runs`, method: 'POST' },
    {
      ...options,
      onSuccess: () =>
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiAnnotationQueuesPath)
        ),
    }
  );
};

export const useDeleteAnnotationQueueRunsMutation = (
  queue_id?: string,
  options: Parameters<
    typeof useSWRMutation<never, DeleteAnnotationQueueRunsBody>
  >[1] = {}
) => {
  return useSWRMutation<never, DeleteAnnotationQueueRunsBody>(
    {
      url: `${apiAnnotationQueuesPath}/${queue_id}/runs/delete`,
      method: 'POST',
    },
    options
  );
};

export const useUpdateRunInAnnotationQueueMutation = (
  queue_id?: string,
  queue_run_id?: string,
  options: Parameters<
    typeof useSWRMutation<
      AnnotationQueueSchema,
      string[] | PatchAnnotationQueueRunBody
    >
  >[1] = {}
) => {
  return useSWRMutation<
    AnnotationQueueSchema,
    string[] | PatchAnnotationQueueRunBody
  >(
    {
      url: `${apiAnnotationQueuesPath}/${queue_id}/runs/${queue_run_id}`,
      method: 'PATCH',
    },
    options
  );
};

export const useDeleteRunFromAnnotationQueueMutation = (
  queue_id?: string,
  queue_run_id?: string,
  options?: UseSWRMutationOptions<unknown, unknown>
) => {
  return useSWRMutation<unknown, unknown>(
    {
      url: `${apiAnnotationQueuesPath}/${queue_id}/runs/${queue_run_id}`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: () =>
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiAnnotationQueuesPath)
        ),
    }
  );
};

export const useCreateIdentityAnnotationQueueStatusMutation = (
  queueId?: string,
  annotationQueueRunId?: string,
  goToNextRun?: () => Promise<boolean>,
  shouldMutateQueue?: boolean,
  options?: {
    onSuccess?: () => void;
    onError?: (error) => void;
  }
) => {
  const organizationId = useOrganizationId();
  const abortControllerRef = useRef<AbortController>();

  // In the fetcher
  if (abortControllerRef.current) {
    abortControllerRef.current.abort();
  }
  abortControllerRef.current = new AbortController();

  return useSWRMutationBase<
    void,
    Error,
    FetcherParams<CreateIdentityAnnotationQueueRunStatusBody, never>,
    CreateIdentityAnnotationQueueRunStatusBody
  >(
    {
      method: 'POST',
      url: `${apiAnnotationQueuesPath}/${appIdentityAnnotationQueuesPath}/${annotationQueueRunId}`,
    },
    async (key, { arg: body }) => {
      // Try to go to next run, but if we can't, we'll need to wait for mutation
      const shouldBlock = await goToNextRun?.();

      if (shouldBlock) {
        try {
          await fetcher({
            ...key,
            headers: { 'X-Tenant-Id': organizationId },
            json: body,
          });
          options?.onSuccess?.();
          if (shouldMutateQueue) {
            await mutate(
              (key: FetcherParams) =>
                key?.url === `${apiAnnotationQueuesPath}/${queueId}/size` ||
                key?.url === `${apiAnnotationQueuesPath}/${queueId}/runs`
            );
          }
        } catch (error) {
          options?.onError?.(error);
        }
      } else {
        fetcher({
          ...key,
          headers: { 'X-Tenant-Id': organizationId },
          json: body,
        })
          .then(() => {
            options?.onSuccess?.();
            if (shouldMutateQueue) {
              mutate(
                (key: FetcherParams) =>
                  key?.url === `${apiAnnotationQueuesPath}/${queueId}/size` ||
                  key?.url === `${apiAnnotationQueuesPath}/${queueId}/runs`
              );
            }
          })
          .catch((error) => {
            options?.onError?.(error);
          });
      }
    }
  );
};

export const useShareRunMutation = (id: string) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<unknown, unknown>(
    {
      url: `${apiRunsPath}/${id}/share`,
      method: 'PUT',
    },
    async (key) => {
      const resp = await fetcher({
        ...key,
        headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
      });

      await mutate((key: FetcherParams) =>
        key?.url?.startsWith(`${apiRunsPath}/${id}`)
      );
      return resp;
    }
  );
};

export const useUnshareRunMutation = (id: string) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<unknown, unknown>(
    {
      url: `${apiRunsPath}/${id}/share`,
      method: 'DELETE',
    },
    async (key) => {
      const resp = await fetcher({
        ...key,
        headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
      });

      await mutate((key: FetcherParams) =>
        key?.url?.startsWith(`${apiRunsPath}/${id}`)
      );
      return resp;
    }
  );
};

export const useShareDataset = (id?: string) => {
  return useSWR<GetDatasetShareBody>(
    id ? { url: `${apiDatasetsPath}/${id}/share` } : null
  );
};

export const useShareDatasetMutation = (id: string) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<unknown, unknown>(
    {
      url: `${apiDatasetsPath}/${id}/share`,
      method: 'PUT',
    },
    async (key) => {
      const resp = await fetcher({
        ...key,
        headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
      });

      await mutate((key: FetcherParams) =>
        key?.url?.startsWith(`${apiDatasetsPath}/${id}/share`)
      );
      return resp;
    }
  );
};

export const useUnshareDatasetMutation = (id: string) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<unknown, unknown>(
    {
      url: `${apiDatasetsPath}/${id}/share`,
      method: 'DELETE',
    },
    async (key) => {
      const resp = await fetcher({
        ...key,
        headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
      });

      await mutate((key: FetcherParams) =>
        key?.url?.startsWith(`${apiDatasetsPath}/${id}/share`)
      );
      return resp;
    }
  );
};

export const useApiKeys = () => {
  return useSWR<ApiKeySchema[]>({ url: apiKeyPath, method: 'GET' });
};

export const usePatKeys = () => {
  return useSWR<ApiKeySchema[]>({
    url: `${apiKeyPath}/current`,
    method: 'GET',
  });
};

export const useNewApiKeyMutation = (
  options?: UseSWRMutationOptions<NewApiKeySchema, NewApiKeyBody>
) => {
  return useSWRMutation<NewApiKeySchema, NewApiKeyBody>(
    {
      url: apiKeyPath,
      method: 'POST',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: apiKeyPath, method: 'GET' }));
        mutate(
          matchKeyPartial({ url: `${apiKeyPath}/current`, method: 'GET' })
        );
      },
    }
  );
};

export const useDeleteApiKeyMutation = (
  id: string,
  type: 'pat' | 'service',
  options?: UseSWRMutationOptions<unknown, unknown>
) => {
  return useSWRMutation<unknown, unknown>(
    {
      url:
        type === 'pat' ? `${apiKeyPath}/current/${id}` : `${apiKeyPath}/${id}`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);

        if (type === 'pat') {
          mutate(
            matchKeyPartial({ url: `${apiKeyPath}/current`, method: 'GET' })
          );
        } else {
          mutate(matchKeyPartial({ url: apiKeyPath, method: 'GET' }));
        }
      },
    }
  );
};

export const useGenericDeleteMutation = (
  params:
    | {
        endpoint: string;
        id: string;
        invalidationPrefixes?: string[];
      }
    | {
        endpoint: string;
        invalidationPrefixes?: string[];
      },
  options?: {
    onSuccess?: () => void;
    onError?: (error) => void;
  }
) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<void, Error, FetcherParams<never, never>, never>(
    {
      method: 'DELETE',
      url:
        'id' in params && params.id
          ? `${params.endpoint}/${params.id}`
          : params.endpoint,
    },
    async (key) => {
      await fetcher({
        ...key,
        headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
      });
      await mutate(
        (key: FetcherParams) =>
          key?.url?.startsWith(`${params.endpoint}`) ||
          (params.invalidationPrefixes || []).some(
            (prefix) =>
              key?.url?.startsWith(prefix) ||
              key?.url?.startsWith(prefix.replace('/', ''))
          )
      );
    },
    { onSuccess: options?.onSuccess, onError: options?.onError }
  );
};

export const useGenericMultiDeleteMutation = (
  params: {
    endpoint: string;
    idParamName: string;
    ids: string[];
    invalidationPrefixes?: string[];
  },
  options?: {
    onSuccess?: () => void;
    onError?: () => void;
  }
) => {
  const organizationId = useOrganizationId();
  return useSWRMutationBase<
    void,
    Error,
    FetcherParams<Record<string, string[]>, never>,
    never
  >(
    {
      method: 'DELETE',
      url: `${params.endpoint}`,
      params: { [params.idParamName]: params.ids },
    },
    async (key) => {
      await fetcher({
        ...key,
        headers: { 'X-Tenant-Id': organizationId },
      });
      await mutate((key: FetcherParams) => {
        return (
          key?.url?.startsWith(`${params.endpoint}`) ||
          (params.invalidationPrefixes || []).some(
            (prefix) =>
              key?.url?.startsWith(prefix) ||
              key?.url?.startsWith(prefix.replace('/', ''))
          )
        );
      });
    },
    { onSuccess: options?.onSuccess, onError: options?.onError }
  );
};

function useHubSWR<Data, QueryParams = never, BodyParams = never>(
  params: FetcherParams<QueryParams, BodyParams> | null,
  options: SWRConfiguration<Data> = {}
) {
  const auth = useAuth();
  const organizationId = useOrganizationId();

  const baseUrl = backendUrl;
  const url = params
    ? params.url.startsWith('http')
      ? params.url
      : `${baseUrl}${params.url}`
    : null;

  const innerParams =
    params && url && !auth.loading
      ? {
          ...params,
          url,
          headers: organizationId
            ? {
                ...params.headers,
                'X-Tenant-Id': organizationId,
              }
            : params.headers,
        }
      : null;

  return useSWRBase<Data, Error, FetcherParams<QueryParams, BodyParams> | null>(
    innerParams,
    fetcher,
    { ...DEFAULT_OPTIONS, ...options }
  );
}

function useHubSWRInfinite<
  Data extends { total: number },
  QueryParams extends PaginationQueryParams = never,
  BodyParams = never
>(
  params: FetcherParams<QueryParams, BodyParams> | null,
  options: SWRInfiniteConfiguration<Data> = {}
) {
  const auth = useAuth();
  const organizationId = useOrganizationId();

  const url = params
    ? params.url.startsWith('http')
      ? params.url
      : `${backendUrl}${params.url}`
    : null;

  const getKey: SWRInfiniteKeyLoader<
    Data,
    FetcherParams<QueryParams, BodyParams> | null
  > = (pageIndex, previousPageData) => {
    const limit = 20;
    const offset = pageIndex * limit;
    if (!url || !params || auth.loading) return null;
    if (previousPageData && previousPageData.total < offset) return null;
    const queryParams = params.params;
    const rtn: FetcherParams<QueryParams, BodyParams> = {
      ...params,
      url,
      params: { ...queryParams, limit, offset } as QueryParams,
      headers: organizationId
        ? { ...params.headers, 'X-Tenant-Id': organizationId }
        : { ...params },
    };
    return rtn;
  };

  return useSWRInfinite<
    Data,
    Error,
    SWRInfiniteKeyLoader<Data, FetcherParams<QueryParams, BodyParams> | null>
  >(getKey, fetcher, { ...DEFAULT_INFINITE_OPTIONS, ...options });
}

function useHubSWRMutation<
  Res,
  ReqBody,
  TemplateParams extends Record<string, string> = Record<string, never>
>(
  params: FetcherParams<never, never>,
  options: SWRMutationConfiguration<
    Res,
    Error,
    Partial<FetcherParams<never, ReqBody>> & {
      templateUrlParams?: TemplateParams;
      templateQueryParams?: Record<string, string>;
    },
    FetcherParams<never, never>
  > = {}
) {
  const organizationId = useOrganizationId();
  const baseUrl = backendUrl;

  return useSWRMutationBase<
    Res,
    Error,
    FetcherParams<never, never>,
    Partial<FetcherParams<never, ReqBody>> & {
      templateUrlParams?: TemplateParams;
      templateQueryParams?: Record<string, string>;
    }
  >(
    params,
    (key, { arg }) => {
      const url = arg.url || key.url;
      return fetcher({
        ...key,
        ...arg,
        url: url.startsWith('http') ? url : `${baseUrl}${url}`,
        headers: organizationId
          ? {
              'X-Tenant-Id': organizationId,
              ...key.headers,
              ...arg.headers,
            }
          : {
              ...key.headers,
              ...arg.headers,
            },
      });
    },
    options
  );
}

export const useRepos = (queryParams?: ListReposQueryParams | null) => {
  return useHubSWR<ListReposResponse, ListReposQueryParams>(
    queryParams === null
      ? null
      : {
          url: hubApiReposPath + '/',
          params: queryParams,
        }
  );
};

export const useReposInfinite = (queryParams?: ListReposQueryParams | null) => {
  return useHubSWRInfinite<ListReposResponse, ListReposQueryParams>(
    queryParams === null
      ? null
      : {
          url: `${hubApiReposPath}/`,
          params: queryParams,
        }
  );
};

export const useRepoTags = (queryParams?: ListRepoTagsQueryParams | null) => {
  return useHubSWR<ListRepoTagsResponse, ListRepoTagsQueryParams>(
    queryParams === null
      ? null
      : {
          url: hubApiReposPath + '/tags',
          params: queryParams,
        }
  );
};

export const useRepo = (
  owner: string | undefined,
  repo: string | undefined,
  options?: SWRConfiguration
) => {
  return useHubSWR<GetRepoResponse>(
    repo ? { url: `${hubApiReposPath}/${owner ?? '-'}/${repo}` } : null,
    options
  );
};

export const useCreateRepoMutation = (options?: SWRConfiguration) => {
  return useHubSWRMutation<GetRepoResponse, CreateRepoBody>({
    url: hubApiReposPath + '/',
    method: 'POST',
    ...options,
  });
};

export const useUpdateRepoMutation = (
  owner: string | undefined,
  repo: string
) => {
  const { mutate } = useRepo(owner, repo);
  return useHubSWRMutation<GetRepoResponse, UpdateRepoBody>(
    {
      url: `${hubApiReposPath}/${owner ?? '-'}/${repo}`,
      method: 'PATCH',
    },
    {
      onSuccess: (data) => {
        mutate(data);
      },
    }
  );
};

export const useForkRepoMutation = (
  owner: string | undefined,
  repo: string
) => {
  return useHubSWRMutation<GetRepoResponse, CreateRepoBody>({
    url: `${hubApiReposPath}/${owner ?? '-'}/${repo}/fork`,
    method: 'POST',
  });
};

export const useCommits = (
  owner: string | undefined,
  repo: string,
  queryParams?: PaginationQueryParams
) => {
  return useHubSWR<ListCommitsResponse, PaginationQueryParams>({
    url: `${hubApiCommitsPath}/${owner ?? '-'}/${repo}/`,
    params: queryParams,
  });
};

export const useCommitsInfinite = (
  owner: string | undefined,
  repo: string,
  queryParams?: PaginationQueryParams
) => {
  return useHubSWRInfinite<ListCommitsResponse, PaginationQueryParams>(
    queryParams === null
      ? null
      : {
          url: `${hubApiCommitsPath}/${owner ?? '-'}/${repo}/`,
          params: queryParams,
        }
  );
};

export const useCommit = ({
  owner,
  repo,
  sha,
  get_examples = false,
  include_model = true,
  options,
}: {
  owner: string | undefined;
  repo: string;
  sha: string | null | undefined;
  get_examples?: boolean;
  include_model?: boolean;
  options?: SWRConfiguration;
}) => {
  const innerOptions = {
    dedupingInterval: 1000 * 60 * 10, // 10 minutes
    ...options,
  };
  return useHubSWR<
    CommitManifestResponse,
    { get_examples: boolean; is_view: boolean; include_model: boolean }
  >(
    sha && repo
      ? {
          url: `${hubApiCommitsPath}/${owner ?? '-'}/${repo}/${sha}`,
          params: { get_examples, is_view: true, include_model },
        }
      : null,
    innerOptions
  );
};

export const useCreateCommitMutation = (
  owner: string | undefined,
  repo: string,
  options?: UseSWRMutationOptions<
    CreateRepoCommitResponse,
    CreateRepoCommitBody
  >
) => {
  return useHubSWRMutation<CreateRepoCommitResponse, CreateRepoCommitBody>(
    {
      url: `${hubApiCommitsPath}/${owner ?? '-'}/${repo}`,
      method: 'POST',
    },
    options
  );
};

export const useLikeRepoMutation = (
  owner: string | undefined,
  repo: string
) => {
  return useHubSWRMutation<LikeRepoResponse, LikeRepoBody>({
    url: `${hubApiLikesPath}/${owner ?? '-'}/${repo}`,
    method: 'POST',
  });
};

export const useDeleteRepoMutation = (
  repo: string,
  options?: {
    onSuccess?: () => void;
  }
) => {
  return useHubSWRMutation<null, never>(
    {
      url: `${hubApiReposPath}/-/${repo}`,
      method: 'DELETE',
    },
    {
      onSuccess: () => {
        options?.onSuccess?.();
      },
    }
  );
};

export const useSetTenantHandleMutation = () => {
  const { mutate } = useHubTenantSettings();
  const { mutate: mutateSelectedTenant } = useSelectedTenant();
  return useHubSWRMutation<TenantSchema, { tenant_handle: string }>(
    {
      url: `${hubApiSettingsPath}/handle`,
      method: 'POST',
    },
    {
      onSuccess: (data) => {
        if (data.tenant_handle) {
          mutate(data);
          mutateSelectedTenant();
        }
      },
    }
  );
};

export const useCreateEventMutation = () => {
  return useHubSWRMutation<null, CreateEventBody>({
    url: `${hubApiEventsPath}`,
    method: 'POST',
  });
};

export const useHubTenantSettings = (params?: { skip?: boolean }) => {
  return useHubSWR<TenantSchema>(
    params?.skip ? null : { url: hubApiSettingsPath }
  );
};

export const useHubCommentsInfinite = (
  params: {
    owner: string | undefined;
    repo: string;
    parentId: string | null;
  } | null
) => {
  return useHubSWRInfinite<ListCommentsResponse, PaginationQueryParams>(
    params
      ? {
          url: `${hubApiCommentsPath}/${params.owner ?? '-'}/${params.repo}${
            params.parentId ? `/${params.parentId}` : ''
          }`,
        }
      : null
  );
};

export const useCreateCommentMutation = (params: {
  owner: string | undefined;
  repo: string;
  parentId: string | null;
}) => {
  return useHubSWRMutation<CommentSchema, CreateCommentBody>({
    url: `${hubApiCommentsPath}/${params.owner ?? '-'}/${params.repo}${
      params.parentId ? `/${params.parentId}` : ''
    }`,
    method: 'POST',
  });
};

export const useHubCommentLikeMutation = (
  owner: string | undefined,
  repo: string,
  commentId: string
) => {
  return useHubSWRMutation<LikeResponse, never>({
    url: `${hubApiCommentsPath}/${owner ?? '-'}/${repo}/${commentId}/like`,
    method: 'POST',
  });
};

export const useHubCommentUnlikeMutation = (
  owner: string | undefined,
  repo: string,
  commentId: string
) => {
  return useHubSWRMutation<LikeResponse, never>({
    url: `${hubApiCommentsPath}/${owner ?? '-'}/${repo}/${commentId}/like`,
    method: 'DELETE',
  });
};

export const useHostCreateProjectMutation = () => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<
    HostProjectSchema,
    Error,
    FetcherParams<never, never>,
    HostProjectCreateBody
  >(
    {
      method: 'POST',
      url: hostApiProjectsPath,
      headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
    },
    (key, { arg }) => fetcher({ ...key, json: arg }),
    {
      onSuccess: () =>
        Promise.all([
          mutate(matchKeyPartial({ url: hostApiProjectsPath })),
          mutate(matchKeyPartial(...['orgDeployments'])),
        ]),
    }
  );
};

export const useHostCreateRevisionMutation = (hostProjectId: string) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<
    HostRevisionSchema,
    Error,
    FetcherParams<never, never>,
    HostRevisionCreateBody
  >(
    {
      method: 'POST',
      url: `${hostApiProjectsPath}/${hostProjectId}/revisions`,
      headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
    },
    (key, { arg }) => fetcher({ ...key, json: arg }),
    {
      onSuccess: () =>
        Promise.all([
          mutate(matchKeyPartial({ url: `${hostApiProjectsPath}` })),
          mutate(
            matchKeyPartial({ url: `${hostApiProjectsPath}/${hostProjectId}` })
          ),
          mutate(
            matchKeyPartial({
              url: `${hostApiProjectsPath}/${hostProjectId}/revisions`,
            })
          ),
        ]),
    }
  );
};

export const useHostPatchProjectMutation = (hostProjectId: string) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  const { createToast } = useToast();
  return useSWRMutationBase<
    HostProjectSchema,
    Error,
    FetcherParams<never, never>,
    HostProjectPatchBody
  >(
    {
      method: 'PATCH',
      url: `${hostApiProjectsPath}/${hostProjectId}`,
      headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
    },
    (key, { arg }) => fetcher({ ...key, json: arg }),
    {
      onSuccess: () =>
        mutate(
          matchKeyPartial({
            url: `${hostApiProjectsPath}/${hostProjectId}`,
          })
        ),
      onError: (error) => {
        createToast({
          title: 'Failed to update project',
          description: error.message,
          type: 'error',
        });
      },
    }
  );
};

export const useHostCreateAccessTokenMutation = (hostProjectId: string) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();

  return useSWRMutationBase<
    HostAccessTokenSchema,
    Error,
    FetcherParams<never, never>
  >(
    {
      method: 'POST',
      url: `${hostApiProjectsPath}/${hostProjectId}/access_token`,
      headers: {
        'X-Tenant-Id': organizationId,
        'X-Organization-Id': orgId,
      },
    },
    (key, { arg }) => fetcher({ ...key, json: arg })
  );
};

export const useHostProjects = (
  params: HostGetProjectsQueryParams | null,
  options?: SWRConfiguration & {
    headers?: Record<string, string>;
    baseUrl?: string;
  }
) => {
  return useSWR<
    HostProjectSchema[] & {
      headers?: { 'x-pagination-total': number };
    },
    HostGetProjectsQueryParams
  >(
    params
      ? {
          url: `${options?.baseUrl ?? hostApiPath}/projects`,
          params: params,
          headers: options?.headers,
          skipOrgHeaders: options?.headers != null,
          skipTenantHeaders: options?.headers != null,
        }
      : null,
    options
  );
};

export const useHostRevisions = (
  projectId: string | null,
  params: PaginationQueryParams | null,
  options?: SWRConfiguration<
    HostRevisionSchema[] & { headers?: { 'x-pagination-total': number } }
  >
) => {
  return useSWR<
    HostRevisionSchema[] & { headers?: { 'x-pagination-total': number } },
    PaginationQueryParams
  >(
    params && projectId
      ? { url: `${hostApiProjectsPath}/${projectId}/revisions`, params: params }
      : null,
    options
  );
};

export const useHostRevision = (
  params: {
    hostProjectId: string | undefined | null;
    revisionId: string | undefined | null;
  },
  options?: SWRConfiguration
) => {
  return useSWR<HostRevisionSchema>(
    params.hostProjectId && params.revisionId
      ? {
          url: `${hostApiProjectsPath}/${params.hostProjectId}/revisions/${params.revisionId}`,
        }
      : null,
    options
  );
};

export const useHostProject = (
  id: string | null,
  options?: SWRConfiguration
) => {
  return useSWR<HostProjectSchema>(
    id ? { url: `${hostApiProjectsPath}/${id}` } : null,
    options
  );
};

export const useProjectPlaygrounds = (
  projectId: string | null,
  options?: SWRConfiguration
) => {
  return useSWR<
    ProjectPlaygroundSchema[] & { headers?: { 'x-pagination-total': number } }
  >({ url: `${hostApiProjectsPath}/${projectId}/playgrounds` }, options);
};

export const useInterruptMutation = (
  params: {
    projectId: string;
    revisionId: string;
    invalidationPrefixes?: string[];
  },
  options?: {
    onSuccess?: () => void;
  }
) => {
  const organizationId = useOrganizationId();
  const { orgId } = useStoredOrganizationId();
  return useSWRMutationBase<void, Error, FetcherParams<never, never>, never>(
    {
      method: 'POST',
      url: `${hostApiProjectsPath}/${params.projectId}/revisions/${params.revisionId}/interrupt`,
    },
    async (key) => {
      await fetcher({
        ...key,
        headers: { 'X-Tenant-Id': organizationId, 'X-Organization-Id': orgId },
      });
      await mutate(
        (key: FetcherParams) =>
          key?.url?.startsWith(`${params.projectId}`) ||
          (params.invalidationPrefixes || []).some(
            (prefix) =>
              key?.url?.startsWith(prefix) ||
              key?.url?.startsWith(prefix.replace('/', ''))
          )
      );
    },
    { onSuccess: options?.onSuccess }
  );
};

export const useHostRevisionLogs = (
  type: 'deploy_logs' | 'build_logs',
  hostProjectId: string | undefined,
  revisionId: string | undefined,
  filter?: HostRevisionLogsRequest,
  options?: SWRConfiguration<HostRevisionLogsResponse>
) => {
  return useSWR<HostRevisionLogsResponse, never, HostRevisionLogsRequest>(
    hostProjectId && revisionId
      ? {
          method: 'POST',
          url: `${hostApiProjectsPath}/${hostProjectId}/revisions/${revisionId}/${type}`,
          json: filter,
        }
      : null,
    options
  );
};

export const useHostRevisionGithubLink = (
  options?: SWRConfiguration<HostGithubInstallLinkSchema>
) => {
  return useSWR<HostGithubInstallLinkSchema>(
    { url: `${hostApiPath}/integrations/github/install`, method: 'POST' },
    options
  );
};

export const useHostRevisionGithubRepos = (
  namespaceId: string | null,
  options?: SWRConfiguration<HostGithubRepoSchema[]>
) => {
  return useSWR<HostGithubRepoSchema[]>(
    namespaceId
      ? { url: `${hostApiPath}/integrations/github/${namespaceId}/repos` }
      : null,
    options
  );
};

export const useHostRevisionGithubNamespaces = (
  options?: SWRConfiguration<HostGithubNamespaceSchema[]>
) => {
  return useSWR<HostGithubNamespaceSchema[]>(
    { url: `${hostApiPath}/integrations/github/install` },
    options
  );
};

export const useFeedbackConfigs = (
  options?: SWRConfiguration & { skip?: boolean },
  params?: FeedbackConfigsRequest
) => {
  return useSWR<FeedbackConfigSchema[], FeedbackConfigsRequest | null>(
    options?.skip
      ? null
      : {
          url: `${apiFeedbackConfigsPath}/`,
          params: {
            key: params?.key?.slice(0, 50),
            read_after_write: params?.read_after_write,
          },
        },
    options
  );
};

export const useCreateFeedbackConfigMutation = (
  options?: UseSWRMutationOptions<
    FeedbackConfigSchema,
    FeedbackConfigCreateBody
  >
) => {
  return useSWRMutation<FeedbackConfigSchema, FeedbackConfigCreateBody>(
    {
      url: `${apiFeedbackConfigsPath}/`,
      method: 'POST',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(matchKeyPartial({ url: `${apiFeedbackConfigsPath}/` }));
      },
    }
  );
};

export const useUpdateFeedbackConfigMutation = (
  options?: UseSWRMutationOptions<never, UpdateFeedbackConfigSchema>
) => {
  return useSWRMutation<never, UpdateFeedbackConfigSchema>(
    {
      url: `${apiFeedbackConfigsPath}/`,
      method: 'PATCH',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(matchKeyPartial({ url: `${apiFeedbackConfigsPath}/` }));
      },
    }
  );
};

export const useGenerateQueryForRunsMutation = (
  options?: UseSWRMutationOptions<
    { filter: string; feedback_urls: Record<string, string> },
    { query: string; feedback_keys: string[] }
  >,
  shareToken?: { datasetShareToken?: string }
) => {
  return useSWRMutation<
    { filter: string; feedback_urls: Record<string, string> },
    { query: string; feedback_keys: string[] }
  >(
    shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/runs/generate-query`,
          method: 'POST',
          skipTenantHeaders: true,
        }
      : { url: `${apiRunsPath}/generate-query`, method: 'POST' },
    options
  );
};

export const useModelPriceMap = (options?: SWRConfiguration) => {
  return useSWR<ModelPriceMapSchema[]>(
    { url: `${apiModelPriceMapPath}/` },
    options
  );
};

export const useUpsertModelPriceMapMutation = (
  id: string | null | undefined,
  options?: UseSWRMutationOptions<ModelPriceMapSchema, ModelPriceMapUpdateBody>
) => {
  return useSWRMutation<ModelPriceMapSchema, ModelPriceMapUpdateBody>(
    id
      ? { url: `${apiModelPriceMapPath}/${id}`, method: 'PUT' }
      : { url: `${apiModelPriceMapPath}/`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(matchKeyPartial({ url: `${apiModelPriceMapPath}/` }));
      },
    }
  );
};

export function useRunRules(
  queryParams?: GetRunRulesQueryParams | null,
  options?: SWRConfiguration
) {
  return useSWR<RuleSchema[], GetRunRulesQueryParams>(
    queryParams !== null
      ? { url: `${apiRunsPath}/rules`, params: queryParams }
      : null,
    options
  );
}

export function useRunRulesLastApplied(
  ruleId: string | null,
  options?: SWRConfiguration
) {
  return useSWR<RuleLogSchema, never>(
    ruleId !== null
      ? { url: `${apiRunsPath}/rules/${ruleId}/last_applied` }
      : null,
    options
  );
}

export function useRunRuleLogsInfinite(
  ruleId: string | null | undefined,
  startTime: string | undefined,
  endTime: string | undefined,
  pageSize: number
) {
  const organizationId = useOrganizationId();
  const getKey: SWRInfiniteKeyLoader<
    RuleLogSchema[],
    FetcherParams<GetRuleLogsQueryParams, never> | null
  > = (pageIndex, previousPageData) => {
    if (previousPageData && !previousPageData.length) return null;
    return {
      url: `${apiRunsPath}/rules/${ruleId}/logs`,
      params: {
        limit: pageSize,
        offset: pageIndex * pageSize,
        start_time: startTime,
        end_time: endTime,
      },
      headers: { 'X-Tenant-Id': organizationId },
    };
  };
  return useSWRInfinite<
    RuleLogSchema[] & { headers?: { 'x-pagination-total': number } },
    Error,
    SWRInfiniteKeyLoader<
      RuleLogSchema[] & { headers?: { 'x-pagination-total': number } },
      FetcherParams<GetRuleLogsQueryParams, never> | null
    >
  >(getKey, fetcher, { ...DEFAULT_INFINITE_OPTIONS });
}

export function useRunRulesMutation(
  ruleId?: string | undefined | null,
  options?: UseSWRMutationOptions<RuleSchema, RuleMutateBody>
) {
  return useSWRMutation<RuleSchema, RuleMutateBody>(
    ruleId
      ? {
          url: `${apiRunsPath}/rules/${ruleId}`,
          method: 'PATCH',
        }
      : {
          url: `${apiRunsPath}/rules`,
          method: 'POST',
        },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(matchKeyPartial({ url: `${apiRunsPath}/rules` }));
      },
    }
  );
}

export function useTriggerRunRules(ruleIds?: string[]) {
  return useSWRMutation<null, RuleTriggerBody>({
    url: `${apiRunsPath}/rules/trigger`,
    json: { rule_ids: ruleIds ? ruleIds : [] },
    method: 'POST',
  });
}

export function useInvokeCustomCode() {
  return useSWRMutation<AceResponseBody, AceInvokeBody>({
    url: `${apiACEPath}/execute`,
    method: 'POST',
  });
}

export const useTenantSecrets = () => {
  return useSWR<TenantSecret[]>({
    url: `${apiWorkspacesPath}/current/secrets`,
  });
};

export const useTenantSecretsMutation = (
  options?: UseSWRMutationOptions<void, TenantSecret[]>
) => {
  return useSWRMutation<void, TenantSecret[]>(
    { url: `${apiWorkspacesPath}/current/secrets`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(
          matchKeyPartial({ url: `${apiWorkspacesPath}/current/secrets` })
        );
      },
    }
  );
};

export const useGroupRuns = (params: RunGroupsRequest) => {
  return useSWR<RunGroupsResponse, never, RunGroupsRequest>({
    url: `${apiRunsPath}/group`,
    method: 'POST',
    json: params,
  });
};

export const useGroupRunsStats = (
  params: RunGroupsStatsRequest | null,
  options?: SWRConfiguration
) => {
  const payload: FetcherParams<never, RunGroupsStatsRequest> | null = !params
    ? null
    : {
        url: `${apiRunsPath}/group/stats`,
        method: 'POST',
        json: params,
      };

  return useSWR<RunGroupStatsSchema, never, RunGroupsStatsRequest | never>(
    payload,
    options
  );
};

export const useTenantSharedEntities = (params: {
  limit?: number;
  offset?: number;
}) => {
  return useSWR<
    TenantSharedEntityResponse & {
      headers?: { 'x-pagination-total': number };
    },
    { limit?: number; offset?: number }
  >({
    url: `${apiWorkspacesPath}/current/shared`,
    params,
  });
};

export const useTenantBulkUnshareMutation = (
  options?: UseSWRMutationOptions<void, TenantBulkUnshareRequest>
) => {
  return useSWRMutation<void, TenantBulkUnshareRequest>(
    { url: `${apiWorkspacesPath}/current/shared`, method: 'DELETE' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(
          matchKeyPartial({ url: `${apiWorkspacesPath}/current/shared` })
        );
      },
    }
  );
};

export const useOrganizationCreateMember = (
  options?: UseSWRMutationOptions<void, OrganizationCreateMemberRequest>
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWRMutation<void, OrganizationCreateMemberRequest>(
    {
      url: `${apiOrganizationsPath}/current/members`,
      method: 'POST',
      headers: { 'X-Organization-Id': orgId },
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(
          matchKeyPartial({
            url: `${apiOrganizationsPath}/current/members/active`,
          })
        );
      },
    }
  );
};

export const useOrganizationPatchMember = (
  id: string | undefined,
  options?: UseSWRMutationOptions<never, OrganizationPatchMemberRequest>
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWRMutation<never, OrganizationPatchMemberRequest>(
    {
      method: 'PATCH',
      url: `${apiOrganizationsPath}/current/members/${id}`,
      headers: { 'X-Organization-Id': orgId },
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(
          matchKeyPartial({
            url: `${apiOrganizationsPath}/current/members/active`,
          })
        );
      },
    }
  );
};

export const useRoles = (scope?: 'workspace' | 'organization') => {
  const query = useSWR<OrganizationRoleSchema[]>({
    url: `${apiOrganizationsPath}/current/roles`,
  });

  const data = query.data?.filter((role) => {
    if (scope === 'workspace') {
      return (
        role.name.startsWith('WORKSPACE') || role.name.startsWith('CUSTOM')
      );
    } else if (scope === 'organization') {
      return role.name.startsWith('ORGANIZATION');
    }
    return true;
  });

  return {
    ...query,
    data,
  };
};

export const useOrganizationCreateRole = (
  options?: UseSWRMutationOptions<void, OrganizationCreateRoleRequest>
) => {
  return useSWRMutation<void, OrganizationCreateRoleRequest>(
    {
      url: `${apiOrganizationsPath}/current/roles`,
      method: 'POST',
    },
    {
      ...options,
      onSuccess: () => {
        return mutate(
          matchKeyPartial({ url: `${apiOrganizationsPath}/current/roles` })
        );
      },
    }
  );
};

export const useOrganizationPatchRole = (
  id: string | undefined,
  options?: UseSWRMutationOptions<void, OrganizationPatchRoleRequest>
) => {
  return useSWRMutation<void, OrganizationPatchRoleRequest>(
    {
      url: `${apiOrganizationsPath}/current/roles/${id}`,
      method: 'PATCH',
    },
    {
      ...options,
      onSuccess: () => {
        return mutate(
          matchKeyPartial({ url: `${apiOrganizationsPath}/current/roles` })
        );
      },
    }
  );
};

export const usePermissionsList = () => {
  return useSWR<PermissionSchema[]>({
    url: `${apiOrganizationsPath}/permissions`,
  });
};

export const useInvokePrompt = (
  options?: UseSWRMutationOptions<InvokePromptResponse, InvokePromptRequest>
) => {
  return useSWRMutation<InvokePromptResponse, InvokePromptRequest>(
    { url: `${apiPromptsPath}/invoke_prompt`, method: 'POST' },
    options
  );
};

export const useUser = (options?: SWRConfiguration & { skip?: boolean }) => {
  return useSWRBase(
    'user',
    options?.skip
      ? null
      : async () => {
          if (supabase) {
            const resp = await supabase.auth.getUser();
            if (resp.error && resp.error.status === 403) {
              throw resp.error;
            }
            return resp;
          }
        },
    {
      revalidateOnFocus: false,
      ...options,
      onError: (error, ...rest) => {
        options?.onError?.(error, ...rest);
        eventBus.emit(SUPABASE_USER_FORBIDDEN, error);
      },
    }
  );
};

export const useSetUser = () => {
  return useSWRMutationBase<
    UserResponse | undefined,
    Error,
    'user',
    UserAttributes
  >(
    'user',
    async (_, { arg }) => {
      if (supabase) {
        const resp = await supabase.auth.updateUser(arg);
        if (resp.error) throw new Error(resp.error.message);
        return resp;
      }
      throw new Error('supabase auth is not initialized');
    },
    {
      onSuccess: () => {
        return mutate('user');
      },
    }
  );
};
export const useUnlinkSocialLogin = () => {
  return useSWRMutationBase<undefined, Error, 'user', UserIdentity[]>(
    'user',
    async (_, { arg: identities }) => {
      if (!supabase) throw new Error('supabase auth is not initialized');

      const unlinkPromises = identities?.map((identity) => {
        if (supabase) {
          return supabase.auth.unlinkIdentity(identity);
        }
        return null;
      });
      const resp = await Promise.all(unlinkPromises);
      if (resp.some((r) => r?.error))
        throw new Error(resp.find((r) => r?.error)?.error?.message);
      return;
    },
    {
      onSuccess: () => {
        return mutate('user');
      },
    }
  );
};

export const useSSOLoginSettings = (ssoLoginSlug?: string) => {
  return useSWR<SSOLoginSettings[]>(
    ssoLoginSlug
      ? {
          url: `${apiSSOPath}/settings/${ssoLoginSlug}`,
          skipOrgHeaders: true,
          skipTenantHeaders: true,
        }
      : null
  );
};

export const useSetSSOOnly = (
  options?: UseSWRMutationOptions<void, UpdateSSOOnlyRequest>
) => {
  return useSWRMutation<void, UpdateSSOOnlyRequest>(
    {
      url: `${apiOrganizationsPath}/current/login-methods`,
      method: 'PATCH',
      skipTenantHeaders: true,
    },
    {
      ...options,
      onSuccess: () => {
        return mutate(
          matchKeyPartial({ url: `${apiOrganizationsPath}/current/info` })
        );
      },
    }
  );
};

export const useConfirmSSOEmailVerification = (
  options?: UseSWRMutationOptions<void, SSOEmailVerificationConfirmRequest>
) => {
  return useSWRMutation<void, SSOEmailVerificationConfirmRequest>(
    {
      url: `${apiSSOPath}/email-verification/confirm`,
      method: 'POST',
      skipTenantHeaders: true,
    },
    {
      ...options,
      onSuccess: () => {
        return mutate(
          matchKeyPartial({ url: `${apiSSOPath}/email-verification/status` })
        );
      },
    }
  );
};

export const useSSOEmailVerificationStatus = (
  payload?: SSOEmailVerificationStatusRequest,
  options?: SWRConfiguration
) => {
  return useSWR<
    SSOEmailVerificationStatusResponse,
    never,
    SSOEmailVerificationStatusRequest
  >(
    payload
      ? {
          url: `${apiSSOPath}/email-verification/status`,
          method: 'POST',
          json: payload,
          skipTenantHeaders: true,
        }
      : null,
    options
  );
};

export const useSendSSOEmailVerification = (
  options?: UseSWRMutationOptions<void, SSOEmailVerificationSendRequest>
) => {
  return useSWRMutation<void, SSOEmailVerificationSendRequest>(
    {
      url: `${apiSSOPath}/email-verification/send`,
      method: 'POST',
    },
    options
  );
};

export const useSSOSettings = () => {
  return useSWR<SSOSettings[]>({
    url: `${apiOrganizationsPath}/current/sso-settings`,
  });
};

export const useCreateSSOSettingsMutation = (
  options?: UseSWRMutationOptions<void, SSOSettingsCreate>
) => {
  return useSWRMutation<void, SSOSettingsCreate>(
    { url: `${apiOrganizationsPath}/current/sso-settings`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(
          matchKeyPartial({
            url: `${apiOrganizationsPath}/current/sso-settings`,
          })
        );
      },
    }
  );
};

export const useUpdateSSOSettingsMutation = (
  options?: UseSWRMutationOptions<void, SSOSettingsUpdate, { id: string }>
) => {
  return useSWRMutation<void, SSOSettingsUpdate, { id: string }>(
    {
      url: `${apiOrganizationsPath}/current/sso-settings/:id`,
      method: 'PATCH',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        return mutate(
          matchKeyPartial({
            url: `${apiOrganizationsPath}/current/sso-settings`,
          })
        );
      },
    }
  );
};

export const useDeleteSSOSettingsMutation = (
  options?: UseSWRMutationOptions<void, void, { id: string }>
) => {
  return useSWRMutation<void, void, { id: string }>(
    {
      url: `${apiOrganizationsPath}/current/sso-settings/:id`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${apiOrganizationsPath}/current/sso-settings`,
          })
        );
      },
      onError: (...args) => {
        options?.onError?.(...args);
      },
    }
  );
};

export const usePlaygroundSettings = (options?: SWRConfiguration) => {
  return useSWR<PlaygroundSettingsSchema[], unknown>(
    { url: `${apiPlaygroundSettingsPath}/` },
    options
  );
};

export const useCreatePlaygroundSettingsMutation = (
  options: Parameters<
    typeof useSWRMutation<
      PlaygroundSettingsSchema,
      CreatePlaygroundSettingsBody
    >
  >[1] = {}
) => {
  return useSWRMutation<PlaygroundSettingsSchema, CreatePlaygroundSettingsBody>(
    { url: `${apiPlaygroundSettingsPath}/`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: `${apiPlaygroundSettingsPath}/` }));
      },
      onError: (...args) => {
        options?.onError?.(...args);
      },
    }
  );
};

export const useDeletePlaygroundSettingsMutation = (
  id: string,
  options?: UseSWRMutationOptions<unknown, unknown>
) => {
  return useSWRMutation<unknown, unknown>(
    { url: `${apiPlaygroundSettingsPath}/${id}`, method: 'DELETE' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: `${apiPlaygroundSettingsPath}/` }));
      },
      onError: (...args) => {
        options?.onError?.(...args);
      },
    }
  );
};

export const useUpdatePlaygroundSettingsMutation = (
  id: string,
  options?: UseSWRMutationOptions<
    PlaygroundSettingsSchema,
    UpdatePlaygroundSettingsBody
  >
) => {
  return useSWRMutation<PlaygroundSettingsSchema, UpdatePlaygroundSettingsBody>(
    { url: `${apiPlaygroundSettingsPath}/${id}`, method: 'PATCH' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: `${apiPlaygroundSettingsPath}/` }));
      },
      onError: (...args) => {
        options?.onError?.(...args);
      },
    }
  );
};

export const useBulkExampleCreate = ({
  datasetDataType,
  onSuccess,
  onError,
}: {
  datasetDataType: DatasetDataType;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}) => {
  const organizationId = useOrganizationId();

  return useSWRMutationBase<
    void,
    Error,
    FetcherParams<never, never>,
    BulkCreateExampleBody
  >(
    { method: 'POST', url: `${apiExamplesPath}/bulk` },
    async (key, { arg }) => {
      const { dataset_id, examples } = arg;
      const parsedExamples = examples.map(({ inputs, outputs, metadata }) => {
        const parsedInputs = inputs ? JSON.parse(inputs) : undefined;
        checkIsValidOrThrowError(parsedInputs);

        let parsedOutputs = outputs ? JSON.parse(outputs) : undefined;
        if (
          (!parsedOutputs ||
            (Object.keys(parsedOutputs).length === 0 &&
              datasetDataType !== 'kv')) &&
          outputs != null
        ) {
          parsedOutputs = createEmptyOutput(datasetDataType);
        }
        checkIsValidOrThrowError(parsedOutputs);

        const parsedMetadata = metadata ? JSON.parse(metadata) : undefined;

        return {
          dataset_id: dataset_id,
          inputs: parsedInputs,
          outputs: parsedOutputs,
          metadata: parsedMetadata,
        };
      });

      return fetcher({
        ...key,
        url: `${apiExamplesPath}/bulk`,
        json: parsedExamples,
        headers: { 'X-Tenant-Id': organizationId },
      });
    },
    {
      onSuccess: () => {
        mutate(matchKeyPartial({ url: `${apiExamplesPath}` }));
        onSuccess?.();
      },
      onError: (error) => {
        onError?.(error.message);
      },
    }
  );
};

export const useBulkExampleUpdate = ({
  options,
}: {
  options?: UseSWRMutationOptions<
    { message: string },
    (ExampleUpdateSchema & { id?: string })[]
  >;
}) => {
  return useSWRMutation<
    { message: string },
    (ExampleUpdateSchema & { id?: string })[]
  >(
    { method: 'PATCH', url: `${apiExamplesPath}/bulk` },
    {
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(matchKeyPartial({ url: `${apiExamplesPath}` }));
      },
      ...options,
    }
  );
};

export const useListFilterViews = (
  sessionId?: string,
  type?: FilterViewType,
  options?: SWRConfiguration
) => {
  return useSWR<FilterViewSchema[], { type?: FilterViewType }>(
    sessionId
      ? {
          url: `${apiSessionsPath}/${sessionId}/views`,
          params: type ? { type } : undefined,
        }
      : null,
    options
  );
};

export const useGetFilterView = (
  sessionId?: string,
  viewId?: string,
  options?: SWRConfiguration
) => {
  return useSWR<FilterViewSchema>(
    sessionId && viewId
      ? { url: `${apiSessionsPath}/${sessionId}/views/${viewId}` }
      : null,
    options
  );
};

export const useCreateFilterViewMutation = (
  sessionId?: string,
  options?: UseSWRMutationOptions<FilterViewSchema, FilterViewCreateBody>
) => {
  return useSWRMutation<FilterViewSchema, FilterViewCreateBody>(
    {
      url: `${apiSessionsPath}/${sessionId}/views`,
      method: 'POST',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiSessionsPath}/${sessionId}/views` })
        );
      },
    }
  );
};

export const useUpdateFilterViewMutation = (
  sessionId?: string,
  viewId?: string,
  options?: UseSWRMutationOptions<FilterViewSchema, FilterViewUpdateBody>
) => {
  return useSWRMutation<FilterViewSchema, FilterViewUpdateBody>(
    {
      url: `${apiSessionsPath}/${sessionId}/views/${viewId}`,
      method: 'PATCH',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiSessionsPath}/${sessionId}/views` })
        );
        mutate(
          matchKeyPartial({
            url: `${apiSessionsPath}/${sessionId}/views/${viewId}`,
          })
        );
      },
    }
  );
};

export const useDeleteFilterViewMutation = (
  sessionId?: string,
  viewId?: string,
  options?: UseSWRMutationOptions<void, void>
) => {
  return useSWRMutation<void, void>(
    {
      url: `${apiSessionsPath}/${sessionId}/views/${viewId}`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiSessionsPath}/${sessionId}/views` })
        );
      },
    }
  );
};

export const useGetSyncStatus = (
  datasetId: string,
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<FewShotSyncStatusSchema>(
    options?.skip ? null : { url: `${apiDatasetsPath}/${datasetId}/index` },
    options
  );
};

export const useSyncDatasetForFewShotMutation = (
  datasetId: string,
  options?: UseSWRMutationOptions<void, { tag: string }>
) => {
  return useSWRMutation<void, { tag: string }>(
    {
      url: `${apiDatasetsPath}/${datasetId}/index`,
      method: 'POST',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${apiDatasetsPath}/${datasetId}/index`,
          })
        );
      },
    }
  );
};

export const useDesyncDatasetForFewShotMutation = (
  datasetId: string,
  options?: UseSWRMutationOptions<void, { tag: string }>
) => {
  return useSWRMutation<void, { tag: string }>(
    {
      url: `${apiDatasetsPath}/${datasetId}/index`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${apiDatasetsPath}/${datasetId}/index`,
          })
        );
      },
    }
  );
};
export const useSearchDatasetMutation = (
  datasetId: string,
  options?: UseSWRMutationOptions<SearchDatasetResponse, SearchDatasetRequest>
) => {
  return useSWRMutation<SearchDatasetResponse, SearchDatasetRequest>(
    {
      url: `${apiDatasetsPath}/${datasetId}/search`,
      method: 'POST',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
      },
    }
  );
};

// Custom charts related hooks

export const useCreateCustomChartsSectionMutation = (
  options?: UseSWRMutationOptions<
    CustomChartsSectionSchema,
    CustomChartsSectionCreate
  >,
  isOrgCharts = false
) => {
  return useSWRMutation<CustomChartsSectionSchema, CustomChartsSectionCreate>(
    {
      url: `${apiChartsSectionPath(isOrgCharts)}`,
      method: 'POST',
    },
    options
  );
};

export const useUpdateCustomChartsSectionMutation = (
  id: string,
  options?: UseSWRMutationOptions<
    CustomChartsSectionSchema,
    CustomChartsSectionUpdate
  >,
  isOrgCharts = false
) => {
  return useSWRMutation<CustomChartsSectionSchema, CustomChartsSectionUpdate>(
    {
      url: `${apiChartsSectionPath(isOrgCharts)}/${id}`,
      method: 'PATCH',
    },
    options
  );
};

export const useCreateCustomChartMutation = (
  options?: UseSWRMutationOptions<CustomChartSchema, CustomChartCreate>,
  isOrgCharts = false
) => {
  return useSWRMutation<CustomChartSchema, CustomChartCreate>(
    {
      url: `${apiChartsPath(isOrgCharts)}/create`,
      method: 'POST',
    },
    options
  );
};

export const useUpdateCustomChartMutation = (
  id: string,
  options?: UseSWRMutationOptions<CustomChartSchema, CustomChartUpdateBase>,
  isOrgCharts = false
) => {
  return useSWRMutation<CustomChartSchema, CustomChartUpdateBase>(
    {
      url: `${apiChartsPath(isOrgCharts)}/${id}`,
      method: 'PATCH',
    },
    options
  );
};

export const useSingleChartData = (
  params: CustomChartsRequest,
  id?: string,
  options?: SWRConfiguration,
  isOrgCharts = false
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWR<CustomChartSchema, Error, CustomChartsRequest>(
    id
      ? {
          url: `${apiChartsPath(isOrgCharts)}/${id}`,
          method: 'POST',
          json: params,
          headers: { 'X-Organization-Id': orgId },
        }
      : null,
    options
  );
};

export const useCustomChartsPreview = (
  payload: CustomChartsPreviewRequest | null,
  options?: SWRConfiguration,
  isOrgCharts = false
) => {
  return useSWR<CustomChartsPreviewResponse, Error, CustomChartsPreviewRequest>(
    payload
      ? {
          url: `${apiChartsPath(isOrgCharts)}/preview`,
          method: 'POST',
          json: payload,
        }
      : null,
    options
  );
};

export const useOrgUsage = (
  params: OrgUsageRequest | null,
  options?: SWRConfiguration
) => {
  return useSWR<OrgUsage[], OrgUsageRequest>(
    params
      ? {
          url: `${apiOrganizationsPath}/current/billing/usage`,
          method: 'GET',
          params,
        }
      : null,
    options
  );
};

export const useDashboards = (
  params?: DashboardsRequest | null,
  options?: SWRConfiguration,
  isOrgCharts = false
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWR<
    CustomChartsSectionResponse[] & {
      headers?: { 'x-pagination-total': number };
    },
    DashboardsRequest
  >(
    params === null
      ? null
      : {
          url: apiChartsSectionPath(isOrgCharts),
          params,
          headers: { 'X-Organization-Id': orgId },
        },
    options
  );
};

export const usePrebuiltDashboardForSession = (
  sessionId?: string,
  params?: CustomChartsRequest,
  options?: SWRConfiguration
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWR<CustomChartsSectionSchema, Error, CustomChartsRequest>(
    sessionId
      ? {
          url: `${apiSessionsPath}/${sessionId}/dashboard`,
          method: 'POST',
          json: params,
          headers: { 'X-Organization-Id': orgId },
        }
      : null,
    options
  );
};

export const usePrebuiltDashboardForSessionStream = (
  sessionId?: string,
  params?: CustomChartsRequest,
  options?: SWRConfiguration
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWRObservable<
    CustomChartsSectionSchema,
    Error,
    CustomChartsRequest
  >(
    sessionId
      ? {
          url: `${apiSessionsPath}/${sessionId}/dashboard`,
          method: 'POST',
          json: params,
          headers: { 'X-Organization-Id': orgId },
          subscriberParser: 'jsonpatch',
        }
      : null,
    options
  );
};

export const useSingleDashboard = (
  sectionId?: string,
  params?: CustomChartsRequest,
  options?: SWRConfiguration,
  isOrgCharts = false
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWR<CustomChartsSectionSchema, Error, CustomChartsRequest>(
    sectionId
      ? {
          url: `${apiChartsSectionPath(isOrgCharts)}/${sectionId}`,
          method: 'POST',
          json: params,
          headers: { 'X-Organization-Id': orgId },
        }
      : null,
    options
  );
};

// Resource tags related hooks
// Tag Keys
export const useCreateTagKey = (
  options?: UseSWRMutationOptions<ResourceTagKey, ResourceTagKeyCreate>
) => {
  return useSWRMutation<ResourceTagKey, ResourceTagKeyCreate>(
    { url: `${apiWorkspacesPath}/current/tag-keys`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiWorkspacesPath}/current/tag-keys` })
        );
        mutate(matchKeyPartial({ url: `${apiWorkspacesPath}/current/tags` }));
      },
    }
  );
};

export const useListTagKeys = (options?: SWRConfiguration) => {
  return useSWR<ResourceTagKey[]>(
    { url: `${apiWorkspacesPath}/current/tag-keys` },
    options
  );
};

export const useUpdateTagKey = (
  options?: UseSWRMutationOptions<
    ResourceTagKey,
    ResourceTagKeyUpdate,
    { tagKeyId: string }
  >
) => {
  return useSWRMutation<
    ResourceTagKey,
    ResourceTagKeyUpdate,
    { tagKeyId: string }
  >(
    {
      url: `${apiWorkspacesPath}/current/tag-keys/:tagKeyId`,
      method: 'PATCH',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiWorkspacesPath}/current/tag-keys` })
        );
        mutate(matchKeyPartial({ url: `${apiWorkspacesPath}/current/tags` }));
      },
    }
  );
};

export const useGetTagKey = (tagKeyId: string, options?: SWRConfiguration) => {
  return useSWR<ResourceTagKey>(
    { url: `${apiWorkspacesPath}/current/tag-keys/${tagKeyId}` },
    options
  );
};

export const useDeleteTagKey = (
  options?: UseSWRMutationOptions<void, void, { tagKeyId: string }>
) => {
  return useSWRMutation<void, void, { tagKeyId: string }>(
    {
      url: `${apiWorkspacesPath}/current/tag-keys/:tagKeyId`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiWorkspacesPath}/current/tag-keys` })
        );
        mutate(matchKeyPartial({ url: `${apiWorkspacesPath}/current/tags` }));
      },
    }
  );
};

// Tag Values
export const useCreateTagValue = (
  options?: UseSWRMutationOptions<
    ResourceTagValue,
    ResourceTagValueCreate,
    { tagKeyId: string }
  >
) => {
  return useSWRMutation<
    ResourceTagValue,
    ResourceTagValueCreate,
    { tagKeyId: string }
  >(
    {
      url: `${apiWorkspacesPath}/current/tag-keys/:tagKeyId/tag-values`,
      method: 'POST',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${apiWorkspacesPath}/current/taggings`,
          })
        );
        mutate(matchKeyPartial({ url: `${apiWorkspacesPath}/current/tags` }));
      },
    }
  );
};

export const useListTagValues = (
  tagKeyId: string,
  options?: SWRConfiguration
) => {
  return useSWR<ResourceTagValue[]>(
    { url: `${apiWorkspacesPath}/current/tag-keys/${tagKeyId}/tag-values` },
    options
  );
};

export const useGetTagValue = (
  tagKeyId?: string,
  tagValueId?: string,
  options?: SWRConfiguration
) => {
  return useSWR<ResourceTagValue>(
    tagKeyId && tagValueId
      ? {
          url: `${apiWorkspacesPath}/current/tag-keys/${tagKeyId}/tag-values/${tagValueId}`,
        }
      : null,
    options
  );
};

export const useUpdateTagValue = (
  options?: UseSWRMutationOptions<
    ResourceTagValue,
    ResourceTagValueUpdate,
    { tagKeyId: string; tagValueId: string }
  >
) => {
  return useSWRMutation<
    ResourceTagValue,
    ResourceTagValueUpdate,
    { tagKeyId: string; tagValueId: string }
  >(
    {
      url: `${apiWorkspacesPath}/current/tag-keys/:tagKeyId/tag-values/:tagValueId`,
      method: 'PATCH',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${apiWorkspacesPath}/current/taggings`,
          })
        );
        mutate(
          matchKeyPartial({
            url: `${apiWorkspacesPath}/current/tag-keys/${args[0].tag_key_id}/tag-values`,
          })
        );
        mutate(matchKeyPartial({ url: `${apiWorkspacesPath}/current/tags` }));
      },
    }
  );
};

export const useDeleteTagValue = (
  options?: UseSWRMutationOptions<
    void,
    void,
    { tagKeyId: string; tagValueId: string }
  >
) => {
  return useSWRMutation<void, void, { tagKeyId: string; tagValueId: string }>(
    {
      url: `${apiWorkspacesPath}/current/tag-keys/:tagKeyId/tag-values/:tagValueId`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${apiWorkspacesPath}/current/taggings`,
          })
        );
        mutate(matchKeyPartial({ url: `${apiWorkspacesPath}/current/tags` }));
      },
    }
  );
};

// Taggings
export const useCreateTagging = (
  options?: UseSWRMutationOptions<ResourceTagging, ResourceTaggingCreate>
) => {
  return useSWRMutation<ResourceTagging, ResourceTaggingCreate>(
    { url: `${apiWorkspacesPath}/current/taggings`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiWorkspacesPath}/current/taggings` })
        );
        mutate(
          matchKeyPartialWith(
            [{ url: `${apiWorkspacesPath}/current/tags` }],
            (key: FetcherParams, source: FetcherParams) => {
              return key.url?.includes(source.url);
            }
          )
        );
      },
    }
  );
};

export const useDeleteTagging = (
  options?: UseSWRMutationOptions<void, void, { taggingId: string }>
) => {
  return useSWRMutation<void, void, { taggingId: string }>(
    {
      url: `${apiWorkspacesPath}/current/taggings/:taggingId`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({ url: `${apiWorkspacesPath}/current/taggings` })
        );
        mutate(
          matchKeyPartialWith(
            [{ url: `${apiWorkspacesPath}/current/tags` }],
            (key: FetcherParams, source: FetcherParams) => {
              return key.url?.includes(source.url);
            }
          )
        );
      },
    }
  );
};

export const useListTags = (
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<ResourceTagKeyWithValues[]>(
    !options?.skip ? { url: `${apiWorkspacesPath}/current/tags` } : null,
    options
  );
};

export const useListTagsForResource = (
  resourceType?: ResourceType,
  resourceId?: string,
  options?: SWRConfiguration
) => {
  return useSWR<
    ResourceTagKeyWithValuesAndTaggings[],
    {
      resource_type: ResourceType;
      resource_id: string;
    }
  >(
    resourceType && resourceId
      ? {
          url: `${apiWorkspacesPath}/current/tags/resource`,
          params: {
            resource_type: resourceType,
            resource_id: resourceId,
          },
        }
      : null,
    options
  );
};

export const useListTaggings = (
  tagValueId?: string,
  options?: SWRConfiguration
) => {
  const url = tagValueId
    ? `${apiWorkspacesPath}/current/taggings?tag_value_id=${tagValueId}`
    : `${apiWorkspacesPath}/current/taggings`;
  return useSWR<ResourceTag[]>({ url }, options);
};

export const usePromptOptimization = (
  options?: UseSWRMutationOptions<
    PromptOptimizationResponse,
    PromptOptimizationRequest
  >
) => {
  return useSWRMutation<PromptOptimizationResponse, PromptOptimizationRequest>(
    { url: `${hubApiReposPath}/optimize-job`, method: 'POST' },
    options
  );
};

export const usePromptOptimizationJobWithLogs = (
  repoName: string,
  jobId: string,
  options?: SWRConfiguration
) => {
  return useSWR<PromptOptimizationJobWithLogs>(
    {
      url: `${hubApiReposPath}/-/${repoName}/${apiPromptOptimizationJobPath}/${jobId}`,
    },
    options
  );
};

export const usePromptOptimizationRuns = (
  repoName: string,
  options?: SWRConfiguration & { skip?: boolean }
) => {
  return useSWR<PromptOptimizationJob[]>(
    { url: `${hubApiReposPath}/-/${repoName}/${apiPromptOptimizationJobPath}` },
    options
  );
};

export const useInvokePlaygroundPrompt = (
  options?: UseSWRMutationOptions<PlaygroundInvokeRequestSchema, any>,
  abortController?: AbortController | null
) => {
  return useSWRMutation<any, PlaygroundInvokeRequestSchema>(
    { url: `${getPlaygroundPath(false)}/invoke`, method: 'POST' },
    options,
    abortController
  );
};

export const useInvokePlaygroundDataset = (
  options?: UseSWRMutationOptions<PlaygroundInvokeDatasetRequestSchema, any>,
  abortController?: AbortController | null
) => {
  return useSWRMutation<any, PlaygroundInvokeDatasetRequestSchema>(
    {
      url: `${getPlaygroundPath(true)}/playground_experiment/batch`,
      method: 'POST',
    },
    options,
    abortController
  );
};

export const useRepoCommitTags = (
  owner: string | undefined,
  repo: string | undefined,
  options?: SWRConfiguration
) => {
  return useSWR<RepoCommitTag[]>(
    { url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags` },
    options
  );
};

export const useRepoCommitTag = (
  owner: string | undefined,
  repo: string | undefined,
  tagName: string | undefined,
  options?: SWRConfiguration
) => {
  return useSWR<RepoCommitTag>(
    tagName
      ? { url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags/${tagName}` }
      : null,
    options
  );
};

export const useCreateRepoCommitTag = (
  owner: string | undefined,
  repo: string | undefined,
  req: RepoCommitTagCreateBody,
  options?: UseSWRMutationOptions<RepoCommitTag, RepoCommitTagCreateBody>
) => {
  return useSWRMutation<RepoCommitTag, RepoCommitTagCreateBody>(
    {
      url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags`,
      method: 'POST',
      json: req,
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags`,
          })
        );
      },
    }
  );
};

export const useUpdateRepoCommitTag = (
  owner: string | undefined,
  repo: string | undefined,
  tagName: string | undefined,
  req: RepoCommitTagUpdateBody,
  options?: UseSWRMutationOptions<RepoCommitTag, RepoCommitTagUpdateBody>
) => {
  return useSWRMutation<RepoCommitTag, RepoCommitTagUpdateBody>(
    {
      url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags/${tagName}`,
      method: 'PATCH',
      json: req,
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags`,
          })
        );
      },
    }
  );
};

export const useDeleteRepoCommitTag = (
  owner: string | undefined,
  repo: string | undefined,
  tagName: string | undefined,
  options?: UseSWRMutationOptions<void, void>
) => {
  return useSWRMutation<void, void>(
    {
      url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags/${tagName}`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate(
          matchKeyPartial({
            url: `${hubApiReposPath}/${owner || '-'}/${repo}/tags`,
          })
        );
      },
    }
  );
};

export const useCreateStripeCheckoutSession = (
  options?: UseSWRMutationOptions<any, any>
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWRMutation(
    {
      url: `${apiOrganizationsPath}/current/stripe_checkout_session`,
      method: 'POST',
      headers: { 'X-Organization-Id': orgId },
    },
    { ...options }
  );
};

export const useConfirmStripeCheckoutSession = (
  options?: UseSWRMutationOptions<any, any>
) => {
  const { orgId } = useStoredOrganizationId();
  return useSWRMutation(
    {
      url: `${apiOrganizationsPath}/current/confirm_checkout_session_completion`,
      method: 'POST',
      headers: { 'X-Organization-Id': orgId },
    },
    { ...options }
  );
};

export const useInstanceHealth = () => {
  return useSWR<HealthInfoGetResponse>(
    { url: apiHealthPath },
    // refresh every 10 minutes
    {
      ...DEFAULT_OPTIONS,
      refreshInterval: 1000 * 60 * 10,
      revalidateOnFocus: false,
    }
  );
};

export const useGroupByExampleRunsInfinite = (
  page_size: number,
  datasetId: string | null | undefined,
  params: DatasetGroupRunsRequest | null,
  shareToken?: { datasetShareToken?: string },
  options?: SWRConfiguration
) => {
  const organizationId = useOrganizationId();
  const getKey = (pageIndex, previousPageData) => {
    if (previousPageData && !previousPageData.groups.length) return null;

    return !params || !datasetId
      ? null
      : shareToken?.datasetShareToken
      ? {
          url: `${apiPublicPath}/${shareToken.datasetShareToken}/datasets/${datasetId}/group/runs`,
          method: 'POST',
          json: {
            ...params,
            limit: page_size,
            offset: pageIndex * page_size,
          },
          skipTenantHeaders: true,
        }
      : {
          url: `${apiDatasetsPath}/${datasetId}/group/runs`,
          method: 'POST',
          json: {
            ...params,
            limit: page_size,
            offset: pageIndex * page_size,
          },
          headers: { 'X-Tenant-Id': organizationId },
        };
  };

  return useSWRInfinite<
    {
      groups: ExamplesGroup[];
      headers?: { 'x-pagination-total': number };
    },
    Error,
    typeof getKey
  >(getKey, fetcher, { ...DEFAULT_INFINITE_OPTIONS, ...options });
};

export function useS3Serialized<T = SerializedConstructor>(
  s3Url?: string | null,
  options?: SWRConfiguration
) {
  const swr = useSWR<T>(
    s3Url
      ? {
          url: s3Url,
          skipTenantHeaders: true, // Skip tenant headers since this is a direct S3 URL
        }
      : null,
    options
  );

  return {
    ...swr,
    data: swr.data,
    isLoading: swr.isLoading,
  };
}

export const useAlerts = (
  sessionId: string | null,
  options?: SWRConfiguration
) => {
  return useSWR<AlertsResponse>(
    sessionId ? { url: `${getAlertsPath()}/${sessionId}` } : null,
    options
  );
};

export const useAlert = (
  sessionId: string | null,
  alertRuleId: string | null,
  options?: SWRConfiguration
) => {
  return useSWR<AlertRuleWithActions>(
    sessionId && alertRuleId
      ? { url: `${getAlertsPath()}/${sessionId}/${alertRuleId}` }
      : null,
    options
  );
};

export const useTestAlertMutation = (
  sessionId: string,
  onSuccess?: () => void,
  options?: UseSWRMutationOptions<{ message: string }, TestAlertRequest>
) => {
  return useSWRMutation<{ message: string }, TestAlertRequest>(
    { url: `${getAlertsPath()}/${sessionId}/test`, method: 'POST' },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        onSuccess?.();
      },
    }
  );
};

export const useCreateAlertMutation = (
  sessionId: string,
  onSuccess?: () => void,
  options?: UseSWRMutationOptions<AlertRuleWithActions, CreateAlertRequest>
) => {
  return useSWRMutation<AlertRuleWithActions, CreateAlertRequest>(
    { url: `${getAlertsPath()}/${sessionId}`, method: 'POST' },
    {
      ...options,
      onSuccess: () => {
        mutate(
          matchKeyPartial({
            url: `${getAlertsPath()}/${sessionId}`,
          })
        );
        onSuccess?.();
      },
    }
  );
};

export const useUpdateAlertMutation = (
  sessionId: string,
  alertRuleId: string,
  onSuccess?: () => void,
  options?: UseSWRMutationOptions<{ message: string }, UpdateAlertRequest>
) => {
  return useSWRMutation<{ message: string }, UpdateAlertRequest>(
    { url: `${getAlertsPath()}/${sessionId}/${alertRuleId}`, method: 'PATCH' },
    {
      ...options,
      onSuccess: () => {
        mutate(
          matchKeyPartial({
            url: `${getAlertsPath()}/${sessionId}`,
          })
        );
        onSuccess?.();
      },
    }
  );
};

export const useDeleteAlertMutation = (
  sessionId: string,
  alertRuleId: string,
  options?: UseSWRMutationOptions<{ message: string }, void>
) => {
  return useSWRMutation<{ message: string }, void>(
    { url: `${getAlertsPath()}/${sessionId}/${alertRuleId}`, method: 'DELETE' },
    options
  );
};
