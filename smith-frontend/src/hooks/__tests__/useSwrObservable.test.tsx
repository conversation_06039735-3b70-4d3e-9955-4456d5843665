import { act, renderHook, waitFor } from '@testing-library/react';

import React from 'react';
import { SWRConfig, unstable_serialize } from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import useSWRObservable, {
  ObservableValue,
  SWRObservableOptions,
} from '../useSwrObservable';

// mockGlobalMutate, mockGlobalCacheGet, mockGlobalCache, and mockSwrHookMutate are no longer needed
// as we are not mocking SWR internals directly in the same way.

// The vi.mock('swr', ...) block is removed.

// We will use the actual useSWR, so this aliasing is not needed in the same way.
// const useSWR = actualUseSWR as vi.Mock;

// --- Test-scoped variables ---
// currentSwrData, currentSwrError, currentIsValidating, currentIsLoading are no longer needed
// as SWR will manage its own state.

let mockUserSubscribe;
let capturedNextFn: SWRObservableOptions<any, any>['next'] | undefined;

// Helper to wrap hooks with SWRConfig
const renderHookWithSWRConfig = (
  hookCallback: () => any,
  swrConfigOptions: Record<string, any> = {}
) => {
  const cache = new Map(); // Create a new cache for each render
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <SWRConfig
      value={{
        provider: () => cache,
        dedupingInterval: 0,
        ...swrConfigOptions,
      }}
    >
      {children}
    </SWRConfig>
  );
  // Pass the wrapper to renderHook
  const utils = renderHook(hookCallback, { wrapper });
  return { ...utils, cache };
};

describe('useSWRObservable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    capturedNextFn = undefined;
    mockUserSubscribe = vi.fn((_key, { next }) => {
      capturedNextFn = next;
    });
    // Clear SWR cache globally before each test if necessary, or rely on the Map per renderHookWithSWRConfig
    // For a truly clean slate for SWR's global state if not using a provider override:
    // import { cache } from 'swr'; cache.clear();
    // However, our SWRConfig provider with a new Map should handle isolation.
  });

  it('should initialize correctly and call subscribe', async () => {
    const testKey = 'init-key-refactored';
    const initialProps = {
      key: testKey,
      subscribe: mockUserSubscribe,
      config: {},
    };

    renderHookWithSWRConfig(() =>
      useSWRObservable(
        initialProps.key,
        initialProps.subscribe,
        initialProps.config
      )
    );

    await waitFor(() => {
      expect(mockUserSubscribe).toHaveBeenCalledWith(
        testKey,
        expect.objectContaining({ next: expect.any(Function) })
      );
    });
    // We can also check if useSWR was called if we spy on it, but direct call to subscribe is a good start.
  });

  // Test 'should subscribe, stream data, and update SWR cache when keepPreviousData is false'
  it('should stream data and update when keepPreviousData is false', async () => {
    const testKey = 'test-key-stream-false-refactored';
    const config = { keepPreviousData: false };

    const { result } = renderHookWithSWRConfig(() =>
      useSWRObservable(testKey, mockUserSubscribe, config)
    );

    await waitFor(() => expect(capturedNextFn).toBeDefined());

    const chunk1: ObservableValue<string> = { value: 'data1', done: false };
    act(() => {
      capturedNextFn!(null, chunk1);
    });

    await waitFor(() => {
      expect(result.current.data).toBe('data1');
    });

    const chunkFinal: ObservableValue<string> = {
      value: 'finalData',
      done: true,
    };
    act(() => {
      capturedNextFn!(null, chunkFinal);
    });

    await waitFor(() => {
      expect(result.current.data).toBe('finalData');
    });
    expect(result.current.isLoading).toBe(false); // NOW isLoading should be false
    expect(result.current.error).toBeUndefined();
  });

  it('should stream data like keepPreviousData:false when cache is empty, even if keepPreviousData is true', async () => {
    const testKey = 'test-key-keep-true-empty-cache-refactored';
    const config = { keepPreviousData: true }; // Explicitly true

    const { result, cache } = renderHookWithSWRConfig(() =>
      useSWRObservable(testKey, mockUserSubscribe, config)
    );

    const serializedKey = unstable_serialize(testKey);
    // SWR creates an entry immediately, so it won't be undefined.
    // We can check that it doesn't have our specific data.value yet.
    await waitFor(() => {
      // Wait for SWR to initialize its cache entry
      const initialCacheEntry = cache.get(serializedKey);
      expect(initialCacheEntry).toBeDefined();
      // Initially, data within the SWR cache entry might be undefined or not have our 'value' field before the fetcher (observable) emits.
      // Depending on SWR version, initial data might be undefined or an object without 'value'
      // This assertion is less critical than the behavior after 'next' is called.
    });

    await waitFor(() => expect(capturedNextFn).toBeDefined());

    const chunk1: ObservableValue<string> = {
      value: 'data1-kptrue-empty',
      done: false,
    };
    act(() => {
      capturedNextFn!(null, chunk1);
    });

    // Since cache was empty, keepPreviousData:true should still update SWR immediately
    await waitFor(() => {
      expect(result.current.data).toBe('data1-kptrue-empty');
      // Also check if data is in SWR cache
      const cachedItem = cache.get(serializedKey);
      expect(cachedItem?.data?.value).toBe('data1-kptrue-empty');
      expect(cachedItem?.data?.done).toBe(false);
    });

    const chunkFinal: ObservableValue<string> = {
      value: 'finalData-kptrue-empty',
      done: true,
    };
    act(() => {
      capturedNextFn!(null, chunkFinal);
    });

    await waitFor(() => {
      expect(result.current.data).toBe('finalData-kptrue-empty');
      const cachedItem = cache.get(serializedKey);
      expect(cachedItem?.data?.value).toBe('finalData-kptrue-empty');
      expect(cachedItem?.data?.done).toBe(true);
    });
  });

  it('should accumulate data and only update SWR cache on done when keepPreviousData is true and cache is populated', async () => {
    const testKey = 'test-key-keep-true-populated-refactored';
    const serializedKey = unstable_serialize(testKey);
    const initialCachedObservableValue: ObservableValue<string> = {
      value: 'initial',
      done: false,
    };

    const config = { keepPreviousData: true };
    const cache = new Map([
      [serializedKey, { data: initialCachedObservableValue }],
    ]);
    const { result } = renderHookWithSWRConfig(
      () => useSWRObservable(testKey, mockUserSubscribe, config),
      {
        provider: () => cache,
      }
    );

    await waitFor(() => expect(capturedNextFn).toBeDefined());

    await waitFor(() => {
      expect(result.current.data).toBe('initial');
    });

    const swrCacheEntryAfterRender = cache.get(serializedKey);
    expect(swrCacheEntryAfterRender?.data?.value).toBe('initial');
    expect(swrCacheEntryAfterRender?.data?.done).toBe(false);

    // First data chunk from observable - should NOT update SWR cache yet
    const chunk1: ObservableValue<string> = { value: 'streamed1', done: false };
    act(() => {
      capturedNextFn!(null, chunk1);
    });

    // Hook data should still be 'initial' because SWR cache (our Map instance) hasn't been mutated by the hook for chunk1.
    expect(result.current.data).toBe('initial');
    const cacheAfterChunk1 = cache.get(serializedKey);
    expect(cacheAfterChunk1?.data?.value).toBe('initial'); // SWR cache should still be initial

    // Second data chunk (functional update)
    const chunk2Updater = (
      prev?: ObservableValue<string>
    ): ObservableValue<string> => {
      expect(prev?.value).toBe('streamed1');
      return { value: (prev?.value || '') + '-updated', done: false };
    };
    const expectedAccumulatedAfterChunk2 = 'streamed1-updated';

    act(() => {
      capturedNextFn!(null, chunk2Updater);
    });
    expect(result.current.data).toBe('initial');
    const cacheAfterChunk2 = cache.get(serializedKey);
    expect(cacheAfterChunk2?.data?.value).toBe('initial');

    // Final data chunk (done: true)
    const finalChunkDone: ObservableValue<string> = {
      value: expectedAccumulatedAfterChunk2,
      done: true,
    };
    act(() => {
      capturedNextFn!(null, finalChunkDone);
    });

    await waitFor(() => {
      expect(result.current.data).toBe(expectedAccumulatedAfterChunk2);
      const finalCachedItem = cache.get(serializedKey);
      expect(finalCachedItem?.data?.value).toBe(expectedAccumulatedAfterChunk2);
      expect(finalCachedItem?.data?.done).toBe(true);
    });
  });

  // Restore the rest of the tests (error handling and custom mutate) as they were before the erroneous deletion
  it('should handle errors from the subscribe function and update SWR state', async () => {
    const testKey = 'test-key-error-refactored';
    const config = {}; // Default config

    const { result } = renderHookWithSWRConfig(() =>
      useSWRObservable(testKey, mockUserSubscribe, config)
    );

    await waitFor(() => expect(capturedNextFn).toBeDefined());

    const streamError = new Error('Stream failed');
    act(() => {
      capturedNextFn!(streamError, undefined);
    });

    await waitFor(() => {
      expect(result.current.error).toBe(streamError);
      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('custom mutate function (with real SWR)', () => {
    const testKey = 'test-key-custom-mutate-refactored';

    it('should update SWR data with wrapped value for direct data mutation', async () => {
      const { result } = renderHookWithSWRConfig(() =>
        useSWRObservable(testKey, mockUserSubscribe, { dedupingInterval: 0 })
      );
      const newData = 'mutatedData';
      await waitFor(() => expect(capturedNextFn).toBeDefined());
      act(() => {
        result.current.mutate(newData);
      });
      await waitFor(() => {
        expect(result.current.data).toBe(newData);
      });
    });

    it('should update SWR data with wrapped value for promise data mutation', async () => {
      const { result } = renderHookWithSWRConfig(() =>
        useSWRObservable(testKey, mockUserSubscribe, { dedupingInterval: 0 })
      );
      await waitFor(() => expect(capturedNextFn).toBeDefined());
      const asyncData = 'asyncMutatedData';
      const promiseResolver = Promise.resolve(asyncData);
      act(() => {
        result.current.mutate(promiseResolver);
      });
      await waitFor(() => {
        expect(result.current.data).toBe(asyncData);
      });
    });

    it('should revalidate SWR data when calling mutate with undefined', async () => {
      const initialDataVal: ObservableValue<string> = {
        value: 'initialDataForRevalidate',
        done: true,
      };
      const revalidatedDataVal: ObservableValue<string> = {
        value: 'revalidatedData',
        done: true,
      };
      const serializedKeyLocal = unstable_serialize(testKey);

      const { result } = renderHookWithSWRConfig(
        () =>
          useSWRObservable(testKey, mockUserSubscribe, { dedupingInterval: 0 }),
        {
          provider: () =>
            new Map([[serializedKeyLocal, { data: initialDataVal }]]),
        }
      );
      await waitFor(() =>
        expect(result.current.data).toBe(initialDataVal.value)
      );
      await waitFor(() => expect(capturedNextFn).toBeDefined());
      mockUserSubscribe.mockImplementationOnce((_k, { next }) => {
        setTimeout(() => next(null, revalidatedDataVal), 0);
      });
      act(() => {
        result.current.mutate(undefined);
      });
      await waitFor(() => {
        expect(result.current.data).toBe(revalidatedDataVal.value);
      });
      expect(mockUserSubscribe).toHaveBeenCalledTimes(2);
    });
  });
});
