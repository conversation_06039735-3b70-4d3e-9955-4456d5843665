import { InformationCircleIcon } from '@heroicons/react/24/outline';
import Search from '@mui/icons-material/Search';
import {
  Autocomplete,
  CircularProgress,
  FormControl,
  FormHelperText,
  FormLabel,
  Grid,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Textarea,
  Tooltip,
  Typography,
} from '@mui/joy';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';

import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';

import { SetHandleModal } from '@/Pages/Hub/SetHandle';
import { ChooseWebhooks } from '@/Pages/Playground/components/ChooseWebhooks';
import { PromptWebhook } from '@/Pages/Prompts/components/Webhooks/types';
import { TAGS_CATEGORY, tagsByCategory } from '@/components/Hub/TagFilterPane';
import { useSelectedTenant } from '@/hooks/useSwr';
import { ResourceTag } from '@/types/schema';

import { ResourceTagCrudPaneEditor } from '../ResourceTagFilter/ResourceTagCrudPaneEditor';

type RepoType = {
  owner: string;
  repo_handle: string;
  description: string;
  readme: string;
  is_public: boolean;
  is_archived: boolean;
  tags: string[];
};
export function useRepoState(
  initialState?: RepoType
): [RepoType, (repo: Partial<RepoType>) => void] {
  const [state, setState] = useState<RepoType>(
    initialState ?? {
      owner: '',
      repo_handle: '',
      description: '',
      readme: '',
      is_public: false,
      is_archived: false,
      tags: [],
    }
  );
  const setRepo = (repo: Partial<RepoType>) => {
    setState((state) => ({ ...state, ...repo }));
  };

  const { data: selectedTenant } = useSelectedTenant();
  const handle = selectedTenant?.tenant_handle;
  useEffect(() => {
    if (handle && !state.owner) setRepo({ owner: handle });
  }, [handle, state.owner]);
  return [state, setRepo];
}

export function RepoForm({
  repo,
  setRepo,
  onSubmit,
  onCancel,
  isLoading,
  isValidatingPrompts,
  mode,
  error,
  hideTitle,
  minimal,
  tagsOnResource,
  setTagsOnResource,
  webhooks,
  excludedWebhookIds,
  setExcludedWebhookIds,
}: {
  repo: RepoType;
  setRepo: (repo: Partial<RepoType>) => void;
  tenantHandle: string | undefined;
  onSubmit: () => void;
  onCancel?: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit' | 'fork';
  error?: Error | null;
  isValidatingPrompts?: boolean;
  hideTitle?: boolean;
  minimal?: boolean;
  tagsOnResource?: ResourceTag[];
  setTagsOnResource?: Dispatch<SetStateAction<ResourceTag[]>>;
  webhooks?: PromptWebhook[];
  excludedWebhookIds?: string[];
  setExcludedWebhookIds?: Dispatch<SetStateAction<string[]>>;
}) {
  const tagsMap = useMemo(() => {
    const map = {};
    for (const tag of repo.tags) {
      const category =
        tagsByCategory.find((i) => i.tags.includes(tag))?.category ??
        TAGS_CATEGORY;
      map[category] = [...(map[category] ?? []), tag];
    }
    return map;
  }, [repo.tags]);

  const { data: selectedTenant } = useSelectedTenant();
  const isPersonal = selectedTenant?.is_personal;

  const [setHandleOpen, setSetHandleOpen] = useState(false);

  const innerOnSubmit = async (e) => {
    e.preventDefault();
    if (!repo.owner && repo.is_public) {
      setSetHandleOpen(true);
    } else {
      await onSubmit();
    }
  };

  return (
    <>
      <form onSubmit={innerOnSubmit}>
        <Box display="flex" alignItems={'center'} padding={3}>
          <Box flexGrow={1}></Box>
          <Grid
            container
            flexBasis={600}
            flexGrow={0}
            flexShrink={1}
            spacing={4}
            sx={{ textAlign: 'left' }}
          >
            {!hideTitle && (
              <Grid xs={12}>
                <Typography level="h2">
                  {mode === 'create'
                    ? 'Create a New Prompt'
                    : `${mode === 'fork' ? 'Fork' : 'Update'} ${
                        repo.owner ? `${repo.owner}/` : ''
                      }${repo.repo_handle}`}
                </Typography>
              </Grid>
            )}
            <Grid sx={{ width: '100%' }}>
              <FormControl error={!!error}>
                <FormLabel>
                  <div className="flex flex-row gap-2">
                    <Typography level="body1">Prompt name</Typography>
                    {mode === 'edit' && (
                      <Tooltip
                        title={'Prompt names are not editable after creation'}
                      >
                        <InformationCircleIcon className="my-auto h-4 w-4" />
                      </Tooltip>
                    )}
                  </div>
                </FormLabel>
                <Stack direction="row" spacing={0.5}>
                  {repo.owner && repo.is_public && (
                    <div className="my-auto">{repo.owner}/</div>
                  )}
                  <Input
                    data-testid="prompt-name-input"
                    fullWidth
                    value={repo.repo_handle}
                    onChange={(e) => {
                      setRepo({ repo_handle: e.target.value });
                    }}
                    disabled={mode === 'edit'}
                    readOnly={mode === 'edit'}
                    endDecorator={
                      isValidatingPrompts && <CircularProgress size="sm" />
                    }
                  />
                </Stack>
                {error && (
                  <FormHelperText>
                    <Typography color="danger" level="body2">
                      {error.message}
                    </Typography>
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            {tagsOnResource &&
              setTagsOnResource &&
              !repo.is_public &&
              !isPersonal && (
                <Grid sx={{ width: '100%' }}>
                  <FormControl>
                    <ResourceTagCrudPaneEditor
                      tagsOnResource={tagsOnResource}
                      setTagsOnResource={setTagsOnResource}
                    />
                  </FormControl>
                </Grid>
              )}
            <Grid xs={12} textAlign={'left'}>
              <RadioGroup
                value={repo.is_public}
                onChange={(e) =>
                  setRepo({ is_public: e.target.value === 'true' })
                }
              >
                <Radio
                  value={'false'}
                  label={
                    <>
                      <Typography level="body1">Private</Typography>{' '}
                      <Typography level="body2">
                        Only {isPersonal ? 'you' : 'this workspace'} can see
                        this
                      </Typography>
                    </>
                  }
                />
                <Radio
                  value={'true'}
                  label={
                    <>
                      <Typography level="body1">Public</Typography>{' '}
                      <Typography level="body2">
                        Everyone can see this
                      </Typography>
                    </>
                  }
                />
              </RadioGroup>
            </Grid>

            {webhooks &&
            webhooks.length > 0 &&
            excludedWebhookIds &&
            setExcludedWebhookIds ? (
              <div className="mb-8">
                <ChooseWebhooks
                  webhooks={webhooks}
                  excludedWebhookIds={excludedWebhookIds}
                  setExcludedWebhookIds={setExcludedWebhookIds}
                />
              </div>
            ) : null}

            {!minimal && (
              <>
                <Grid xs={12}>
                  <FormLabel>
                    <Typography level="body1">Description</Typography>
                  </FormLabel>
                  <Textarea
                    value={repo.description}
                    minRows={2}
                    onChange={(e) => setRepo({ description: e.target.value })}
                  />
                  <FormHelperText>
                    <Typography level="body2">
                      Words open doors, describe your work and be found
                    </Typography>
                  </FormHelperText>
                </Grid>

                <Grid xs={12}>
                  <FormLabel>
                    <Typography level="body1">Readme</Typography>
                  </FormLabel>
                  <Textarea
                    value={repo.readme}
                    minRows={4}
                    onChange={(e) => setRepo({ readme: e.target.value })}
                  />
                  <FormHelperText>
                    <Typography level="body2">
                      Use markdown for formatting
                    </Typography>
                  </FormHelperText>
                </Grid>

                {tagsByCategory
                  .filter((i) => i.category !== 'Type')
                  .map((i) => (
                    <Grid xs={12} key={i.category}>
                      <FormControl>
                        <FormLabel>
                          <Typography level="body1">{i.category}</Typography>
                        </FormLabel>

                        <Autocomplete
                          multiple
                          disableCloseOnSelect
                          placeholder={`Enter ${
                            i.category == 'Language' ? 'a ' : ''
                          }${i.category.toLowerCase()}...`}
                          startDecorator={<Search />}
                          options={i.tags}
                          value={tagsMap[i.category] ?? []}
                          freeSolo
                          slotProps={{
                            listbox: {
                              modifiers: [
                                {
                                  name: 'flip',
                                  enabled: false,
                                },
                              ],
                            },
                          }}
                          onChange={(_, newValue) => {
                            const prev = tagsMap[i.category] ?? [];
                            const added = newValue.filter(
                              (i) => !prev.includes(i)
                            );
                            const removed = prev.filter(
                              (i) => !newValue.includes(i)
                            );

                            setRepo({
                              tags: [...repo.tags, ...added].filter(
                                (i) => !removed.includes(i)
                              ),
                            });
                          }}
                        />
                      </FormControl>
                    </Grid>
                  ))}

                {repo.is_public && (
                  <>
                    <Grid xs={12} textAlign={'left'}>
                      <Typography level="h4">Sharing Settings</Typography>
                      <p>Manage who can see your repos</p>
                    </Grid>
                    <Grid xs={12} textAlign={'left'}>
                      <FormLabel>
                        <Typography level="body1">License</Typography>
                      </FormLabel>
                      <Autocomplete
                        placeholder="Select a license"
                        disabled
                        options={['MIT']}
                        value={'MIT'}
                      />
                    </Grid>
                  </>
                )}
              </>
            )}

            {mode === 'edit' && (
              <>
                <Grid xs={12} textAlign={'left'}>
                  <Typography level="h4">Archive Settings</Typography>
                  <p>Clean up prompts that aren't in use anymore</p>
                </Grid>
                <Grid xs={12} textAlign={'left'}>
                  <RadioGroup
                    value={repo.is_archived}
                    onChange={(e) =>
                      setRepo({ is_archived: e.target.value === 'true' })
                    }
                  >
                    <Radio
                      value={'false'}
                      label={
                        <>
                          <Typography level="body1">Normal</Typography>{' '}
                          <Typography level="body2">
                            This prompt is discoverable
                          </Typography>
                        </>
                      }
                    />
                    <Radio
                      value={'true'}
                      label={
                        <>
                          <Typography level="body1">Archived</Typography>{' '}
                          <Typography level="body2">
                            This prompt will not show up in default searches
                          </Typography>
                        </>
                      }
                    />
                  </RadioGroup>
                </Grid>
              </>
            )}
            <div className="flex w-full flex-row justify-end gap-2">
              {onCancel && (
                <Button
                  fullWidth
                  variant="outlined"
                  color="neutral"
                  onClick={onCancel}
                >
                  Cancel
                </Button>
              )}
              <Button
                fullWidth
                type="submit"
                disabled={isLoading || !!error || isValidatingPrompts}
              >
                {isLoading ? (
                  <CircularProgress />
                ) : mode === 'fork' ? (
                  'Fork'
                ) : (
                  'Save'
                )}
              </Button>
            </div>
          </Grid>
          <Box flexGrow={1}></Box>
        </Box>
      </form>
      <SetHandleModal
        open={setHandleOpen}
        onClose={() => setSetHandleOpen(false)}
      />
    </>
  );
}
