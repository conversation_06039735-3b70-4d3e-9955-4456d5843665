import { XMarkIcon } from '@heroicons/react/24/outline';
import CommitOutlined from '@mui/icons-material/CommitOutlined';
import { Checkbox, Modal, ModalDialog } from '@mui/joy';
import Button from '@mui/joy/Button';

import { useState } from 'react';

import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { useHostCreateRevisionMutation } from '@/hooks/useSwr';
import { HostEnvVarSchema, HostProjectImageSource } from '@/types/schema.ts';

import { singleOriginEnabled } from '../../utils/constants';
import { ExpandableErrorAlert } from '../ExpandableErrorAlert';
import { HostEnvVarsInput } from '../HostEnvVarsInput/HostEnvVarsInput';
import { TextField } from '../TextField';

interface RevisionCrudModalProps {
  hostProjectId?: string;
  imageSource: HostProjectImageSource;
  currentRepoPath?: string;
  currentRepoCommit?: string;
  currentImagePath?: string;
  currentEnvVars: HostEnvVarSchema[];
  currentShareable: boolean;
  currentCpuPerContainer?: number;
  currentMemMBPerContainer?: number;
  currentMinScale?: number;
  currentMaxScale?: number;
  isOpen: boolean;
  doClose: () => void;
}

export function RevisionCrudModal({
  hostProjectId,
  imageSource,
  currentRepoPath,
  currentRepoCommit,
  currentImagePath,
  currentEnvVars,
  currentShareable,
  currentCpuPerContainer,
  currentMemMBPerContainer,
  currentMinScale,
  currentMaxScale,
  isOpen,
  doClose,
}: RevisionCrudModalProps) {
  const [repoPath, setRepoPath] = useState<string | null>(
    currentRepoPath || null
  );
  const [imagePath, setImagePath] = useState<string | null>(
    currentImagePath || null
  );
  const [envVars, setEnvVars] = useState<HostEnvVarSchema[]>(currentEnvVars);
  const [shareable, setShareable] = useState<boolean>(currentShareable);
  const [cpuPerContainer, setCpuPerContainer] = useState<number | undefined>(
    currentCpuPerContainer
  );
  const [memMBPerContainer, setMemMBPerContainer] = useState<
    number | undefined
  >(currentMemMBPerContainer);
  const [minScale, setMinScale] = useState<number | undefined>(currentMinScale);
  const [maxScale, setMaxScale] = useState<number | undefined>(currentMaxScale);

  const create = useHostCreateRevisionMutation(hostProjectId || '');

  const { value: langGraphRemoteReconcilerEnabled } = useOrgConfig(
    OrgConfigs.langgraph_remote_reconciler_enabled
  );

  return (
    <Modal open={isOpen} onClose={doClose} title="New Revision">
      <ModalDialog variant="outlined" sx={{ maxHeight: '90vh' }}>
        <div className="flex h-full w-full flex-col overflow-y-hidden">
          <div className="flex flex-col items-start gap-3">
            <div className="flex w-full items-center justify-between">
              <div className="inline-flex items-center justify-center gap-2 rounded-full p-1">
                <div className="bg-brand-tertiaryr inline-flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-brand-green-500 to-cyan-400 text-white">
                  <CommitOutlined
                    className="fill-white"
                    style={{ width: '16px', height: '16px' }}
                  />
                </div>
                <h2 className="text-xl font-semibold">New Revision</h2>
              </div>

              <button
                type="button"
                className="shrink-0 rounded-md p-0.5 text-tertiary transition-all hover:bg-secondary active:bg-secondary"
                onClick={() => doClose()}
                aria-label="Close"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <span className="text-sm text-tertiary">
              Create a new revision to deploy a new image.
            </span>
          </div>

          <div className="h-5" />

          <form
            onSubmit={async (e) => {
              e.preventDefault();
              e.stopPropagation();
              const revision = await create.trigger({
                repo_path: repoPath ?? undefined,
                image_path: imagePath ?? undefined,
                env_vars: envVars ?? undefined,
                shareable,
                container_spec: {
                  cpu: cpuPerContainer,
                  memory_mb: memMBPerContainer,
                  min_scale: minScale,
                  max_scale: maxScale,
                },
              });

              if (revision) doClose();
            }}
            className="flex flex-col overflow-y-auto"
          >
            <div className="flex flex-col gap-4 overflow-y-auto overflow-x-hidden px-1">
              {imageSource === 'github' && (
                <div className="pt-4">
                  <p className="text-md text-tertiary">
                    <strong className="font-bold">Git Branch</strong>:{' '}
                    {currentRepoCommit}
                  </p>
                </div>
              )}
              {imageSource === 'github' && (
                <TextField
                  label="LangGraph API config file"
                  onChange={(e) => setRepoPath(e.target.value)}
                  value={repoPath ?? ''}
                />
              )}
              {['internal_docker', 'external_docker'].includes(imageSource) && (
                <TextField
                  label="Image Path"
                  onChange={(e) => setImagePath(e.target.value)}
                  value={imagePath ?? ''}
                />
              )}
              <Checkbox
                checked={shareable}
                onChange={(e) => setShareable(e.target.checked)}
                label="Shareable through LangGraph Studio"
              />

              {singleOriginEnabled === '1' ||
                (langGraphRemoteReconcilerEnabled === true && (
                  <div className="flex flex-col gap-3 pt-4">
                    <div className="font-semibold">Container Resources</div>
                    <div className="grid grid-cols-2 gap-4">
                      <TextField
                        label="CPU per container"
                        type="number"
                        onChange={(e) =>
                          setCpuPerContainer(parseInt(e.target.value, 10))
                        }
                        value={cpuPerContainer ?? undefined}
                        placeholder="1"
                      />
                      <TextField
                        label="Memory (MB) per container"
                        type="number"
                        onChange={(e) =>
                          setMemMBPerContainer(parseInt(e.target.value, 10))
                        }
                        value={memMBPerContainer ?? undefined}
                        placeholder="1024"
                      />
                      <TextField
                        label="Minimum scale"
                        type="number"
                        onChange={(e) =>
                          setMinScale(parseInt(e.target.value, 10))
                        }
                        value={minScale ?? undefined}
                        placeholder="1"
                      />
                      <TextField
                        label="Maximum scale"
                        type="number"
                        onChange={(e) =>
                          setMaxScale(parseInt(e.target.value, 10))
                        }
                        value={maxScale ?? undefined}
                        placeholder="1"
                      />
                    </div>
                  </div>
                ))}

              <div className="flex flex-col gap-3 pt-4">
                <div className="flex flex-col gap-1">
                  <div className="font-semibold">Environment Variables</div>

                  <p className="text-sm text-tertiary">
                    <strong className="font-bold">TIP</strong>: All environment
                    variables will be stored as secrets.
                    <br />
                    You can paste the
                    <strong> .env</strong> file contents in the name field.
                  </p>
                </div>

                <HostEnvVarsInput value={envVars} onChange={setEnvVars} />
              </div>
            </div>
            <div>
              {create.error && <ExpandableErrorAlert error={create.error} />}

              <div className="flex w-full justify-end gap-3 pt-4">
                <Button variant="outlined" color="neutral" onClick={doClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  loading={create.isMutating}
                >
                  Submit
                </Button>
              </div>
            </div>
          </form>
        </div>
      </ModalDialog>
    </Modal>
  );
}
