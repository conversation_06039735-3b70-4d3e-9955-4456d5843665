import { MagicWand02Icon } from '@langchain/untitled-ui-icons';

import { FC } from 'react';

import { Tooltip } from '@/components/Tooltip/Tooltip';
import { cn } from '@/utils/tailwind';

type TProps = {
  className?: string;
  setCanvasOpen: (open: boolean) => void;
};

const PromptCanvasButton: FC<TProps> = ({ className, setCanvasOpen }) => {
  return (
    <div className="flex items-center justify-end">
      <Tooltip title={'Open canvas'}>
        <button
          type="button"
          className={cn(
            'flex items-center justify-center rounded-full border border-primary p-1 transition duration-75 hover:bg-secondary-hover',
            className
          )}
          onClick={() => {
            setCanvasOpen(true);
          }}
        >
          <MagicWand02Icon className="h-4 w-4" />
        </button>
      </Tooltip>
    </div>
  );
};

export default PromptCanvasButton;
