import { XMarkIcon } from '@heroicons/react/24/outline';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import { Tooltip } from '@mui/joy';
import {
  ColumnDef,
  ExpandedState,
  RowSelectionState,
  createColumnHelper,
  getCoreRowModel,
  getExpandedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import {
  emulateNativeClick,
  emulateNativeMiddleClick,
  getTableSelectColumnDef,
  useDataGridSizingLocalStorage,
} from '@/components/DataGrid.utils';
import { DataGridColumnVisibilityPopover } from '@/components/DataGrid/DataGridColumnVisibilityPopover';
import { RunListActions, useRunListActions } from '@/components/RunListActions';
import { TableCellTextOverflow } from '@/components/Table';
import { DataGridStateManagement } from '@/hooks/useDataGridState';
import { useGetTrackedComparisonRun } from '@/hooks/useGetComparisonRun';
import { useRunDiffs } from '@/hooks/useRunDiffs';
import { useScrollSync } from '@/hooks/useScrollSync';
import { useSelectAllRuns } from '@/hooks/useSelectAllRuns';
import { useExamples, useRunStats, useRunsInfinite } from '@/hooks/useSwr';
import { useTraceCompare } from '@/hooks/useTraceCompare';
import BranchIcon from '@/icons/BranchIcon.svg?react';
import CheckCircleIcon from '@/icons/CheckCircleIcon.svg?react';
import DatasetIcon from '@/icons/DatasetIcon';
import {
  GetRunsQueryParams,
  RunSchema,
  RunSchemaWithExample,
} from '@/types/schema';
import { TableDisplay } from '@/types/table';
import { cn } from '@/utils/tailwind';

import { Run } from '../../Pages/Run';
import { RunType } from '../../Pages/Run/components/RunType';
import EditIcon from '../../icons/EditIcon.svg?react';
import {
  appPublicDatasetsPath,
  appPublicPath,
  appRunPath,
  appSessionPath,
} from '../../utils/constants';
import { getRootStartDate } from '../../utils/parse-dotted-order-date';
import { DataGridWithFooter } from '../DataGrid';
import { DateTimeRangePicker } from '../DateTimeRangePicker/DateTimeRangePicker.tsx';
import { ErrorBanner } from '../ErrorBanner';
import { FeedbackChips } from '../FeedbackChips';
import { FilterBar } from '../FilterBar/FilterBar';
import { FilterViewsSelect } from '../FilterBar/FilterViewsSelect';
import { RunLatencyChip } from '../RunLatencyChip';
import { RunStatusIcon } from '../RunStatus';
import { RunTags } from '../RunTags';
import { RunTimeToFirstToken } from '../RunTimeToFirstToken';
import { SplitViewPane } from '../SplitViewPane';
import useToast from '../Toast';
import { TruncatedTextWithTooltip } from '../TruncatedTextWithTooltip.tsx';
import { LoadMoreButton } from './LoadMoreButton';
import { NoRunsDisclaimer } from './NoRunsDisclaimer';
import SelectedRunsActions from './SelectedRunsActions';
import { CompareTraceLayout } from './components/CompareTraceLayout';
import { LoadLatestButton } from './components/LoadLatestButton';
import { MetadataKeyValuePairs } from './components/MetadataKeyValuePair';
import { ReferenceExampleCell } from './components/ReferenceExampleCell';
import { RunDetailsPanelHeader } from './components/RunDetailsPanelHeader';
import { BulkActionResult, SearchModel } from './types';
import { RUNS_TABLE_COLUMNS } from './utils/constants';
import { filterInputs, filterOutputs } from './utils/filterInputs';
import { findNewlySelectedRow } from './utils/findNewlySelectedRow';
import { getColumnsAndSizing } from './utils/getColumnsAndSizing';
import { sortColumns } from './utils/sortColumns';

export interface BaseRunsTableProps {
  withTypeCol?: boolean;
  withToolbar?: boolean;
  withPagination?: boolean;
  tableDisplay: TableDisplay;
  runsFilter: GetRunsQueryParams | null;
  defaultColumnVisibility?: {
    [key: string]: boolean;
  };
}

interface RunsTableProps extends BaseRunsTableProps, DataGridStateManagement {
  view?: 'llm' | 'all' | 'trace' | string;
  onViewChange?: (
    view: 'llm' | 'all' | 'trace' | string,
    maybeFilter?: string,
    maybeTraceFilter?: string,
    maybeTreeFilter?: string
  ) => void;

  virtual?: boolean;

  prioritizedColumns?: string[];
  searchModel: SearchModel;
  setSearchModel: React.Dispatch<Partial<SearchModel>>;
  debouncedRunsFilter: GetRunsQueryParams | null;
  navigateToRun?: (
    runId: string,
    sessionId?: string,
    trace_id?: string,
    root_start_time?: string
  ) => void;

  className?: string;

  onExperimentalSearchToggle?: (enabled: boolean) => void;
}

const REFRESH_INTERVAL = 30000; // 30 seconds

const columnHelper = createColumnHelper<RunSchemaWithExample>();

const formatter = new Intl.NumberFormat('en-US', {});
const usdFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
  maximumFractionDigits: 20,
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const COLUMNS_DEF: ColumnDef<RunSchemaWithExample, any>[] = [
  getTableSelectColumnDef({ variant: 'virtual' }),

  columnHelper.accessor('status', {
    id: RUNS_TABLE_COLUMNS.STATUS,
    meta: { name: 'Status', overflow: false },
    size: 44,
    header: () => <CheckCircleIcon className="m-0.5 h-5 w-5" />,
    cell: ({ row }) => {
      return <RunStatusIcon run={row.original} />;
    },
  }),
  columnHelper.accessor('name', {
    id: RUNS_TABLE_COLUMNS.NAME,
    header: 'Name',
    size: 180,
    cell: ({ row, table }) => {
      const { withTypeCol } =
        (table.options.meta as { withTypeCol?: boolean }) ?? {};
      return (
        <div className={withTypeCol ? 'flex flex-row gap-2' : ''}>
          {withTypeCol && <RunType run={row.original} />}
          <div className="flex">
            <TruncatedTextWithTooltip
              text={row.original.name}
              placement="left"
              className="font-medium"
            />
          </div>
        </div>
      );
    },
  }),
  columnHelper.accessor(
    (row) => (filterInputs(row.inputs) || row.inputs_preview)?.slice(0, 100),
    {
      id: RUNS_TABLE_COLUMNS.INPUTS,
      header: 'Input',
      size: 180,
    }
  ),
  columnHelper.accessor(
    (row) =>
      (filterOutputs(row.outputs, row.inputs) || row.outputs_preview)?.slice(
        0,
        100
      ),
    {
      id: RUNS_TABLE_COLUMNS.OUTPUTS,
      header: 'Output',
      cell: ({ getValue }) => (
        <TableCellTextOverflow>{getValue()}</TableCellTextOverflow>
      ),
    }
  ),
  columnHelper.accessor((row) => row.error?.slice(0, 100), {
    id: RUNS_TABLE_COLUMNS.ERROR,
    header: 'Error',
    cell: ({ row }) => {
      const interrupted = row.original.status === 'interrupted';
      return (
        <TableCellTextOverflow
          className={cn('text-error', interrupted && 'text-purple')}
        >
          {row.original.error}
        </TableCellTextOverflow>
      );
    },
  }),
  columnHelper.accessor('start_time', {
    id: RUNS_TABLE_COLUMNS.START_TIME,
    header: 'Start Time',
    cell: ({ row }) => new Date(row.original.start_time + 'Z').toLocaleString(),
    enableResizing: true,
    size: 164,
  }),
  columnHelper.accessor((row) => [row.start_time, row.end_time].join(), {
    id: RUNS_TABLE_COLUMNS.LATENCY,
    header: 'Latency',
    meta: { select: ['start_time', 'end_time'] },
    cell: ({ row }) => (
      <div
        data-testid="runs-table-latency-chip"
        className="flex items-center justify-start"
      >
        <RunLatencyChip run={row.original} />
      </div>
    ),
    size: 100,
  }),
  columnHelper.accessor('in_dataset', {
    id: RUNS_TABLE_COLUMNS.IN_DATASET,
    header: 'Dataset',
    cell: ({ row }) => (
      <div className="absolute inset-y-0 flex items-center overflow-hidden">
        {row.original.in_dataset ? (
          <Tooltip title="Run has been added to a dataset">
            <div
              className="inline-flex items-center justify-between gap-1.5 rounded border border-secondary px-1"
              data-testid="in-dataset-icon"
            >
              <CheckRoundedIcon className="h-3 w-3" />
              <DatasetIcon className="w-5" />
            </div>
          </Tooltip>
        ) : (
          <Tooltip title="Run has not yet been added to a dataset">
            <div
              className="inline-flex items-center justify-start gap-2.5 rounded border border-secondary px-1"
              data-testid="no-dataset-icon"
            >
              <div className="relative ml-1 h-3 w-3 rounded-sm border border-secondary" />
              <DatasetIcon className="w-5" />
            </div>
          </Tooltip>
        )}
      </div>
    ),
    size: 100,
  }),
  columnHelper.accessor('last_queued_at', {
    id: RUNS_TABLE_COLUMNS.LAST_QUEUED_AT,
    header: 'Annotation Queue',
    cell: ({ row }) => (
      <div className="absolute inset-y-0 flex items-center overflow-hidden">
        {row.original.last_queued_at ? (
          <Tooltip title="Run has been added to an annotation queue">
            <div
              className="inline-flex items-center justify-between gap-1.5 rounded border border-secondary px-1"
              data-testid="in-queue-icon"
            >
              <CheckRoundedIcon className="h-3 w-3" />
              <EditIcon className="my-0.5 h-5 w-5" />
            </div>
          </Tooltip>
        ) : (
          <Tooltip title="Run has not yet been added to an annotation queue">
            <div
              className="inline-flex items-center justify-start gap-2.5 rounded border border-secondary px-1"
              data-testid="no-queue-icon"
            >
              <div className="relative ml-1 h-3 w-3 rounded-sm border border-secondary" />
              <EditIcon className="my-0.5 h-5 w-5" />
            </div>
          </Tooltip>
        )}
      </div>
    ),
    size: 100,
  }),
  columnHelper.accessor('total_tokens', {
    id: RUNS_TABLE_COLUMNS.TOTAL_TOKENS,
    header: 'Tokens',
    cell: ({ getValue }) => (
      <span>{getValue() != null && formatter.format(getValue())}</span>
    ),
    size: 80,
  }),
  columnHelper.accessor('total_cost', {
    id: RUNS_TABLE_COLUMNS.TOTAL_COST,
    header: 'Cost',
    cell: ({ getValue }) => (
      <span>{getValue() != null && usdFormatter.format(getValue())}</span>
    ),
    size: 120,
  }),
  columnHelper.accessor(
    (row) => [row.start_time, row.first_token_time].join(),
    {
      id: RUNS_TABLE_COLUMNS.FIRST_TOKEN_TIME,
      header: 'First Token',
      meta: { select: ['start_time', 'first_token_time'] },
      cell: ({ row }) => {
        return <RunTimeToFirstToken run={row.original} />;
      },
      size: 128,
    }
  ),
  {
    id: RUNS_TABLE_COLUMNS.TAGS,
    header: 'Tags',
    cell: ({ row }) => (
      <div className="absolute inset-0 mx-2 flex items-center gap-1 overflow-x-auto py-4 no-scrollbar">
        <RunTags run={row.original} />
      </div>
    ),
  },
  {
    id: RUNS_TABLE_COLUMNS.METADATA,
    header: 'Metadata',
    meta: { select: ['extra'] },
    cell: ({ row }) => (
      <div className="absolute inset-0 mx-2 flex items-center gap-1 overflow-x-auto py-4 no-scrollbar">
        <MetadataKeyValuePairs run={row.original} />
      </div>
    ),
  },
  {
    id: RUNS_TABLE_COLUMNS.FEEDBACK_STATS,
    header: 'Feedback',
    cell: ({ row }) => (
      <div className="absolute inset-0 mx-2 flex items-center gap-2 overflow-x-auto overflow-y-hidden py-4 no-scrollbar">
        <FeedbackChips
          feedbackStats={row.original.feedback_stats}
          feedbackSourceRunId={row.original.id}
          testIdPrefix="runs-table"
          className="shrink-0"
          showErrorCount={true}
        />
      </div>
    ),
    minSize: 200,
    size: 200,
  },
  {
    id: RUNS_TABLE_COLUMNS.REFERENCE_EXAMPLE_ID,
    header: 'Reference Example',
    cell: ({ row }) => {
      return (
        <ReferenceExampleCell
          row={row.original}
          example={row.original.reference_example}
        />
      );
    },
  },
];

const PUBLIC_COLUMNS_DEF = COLUMNS_DEF.filter(
  (column) =>
    column.id !== RUNS_TABLE_COLUMNS.IN_DATASET &&
    column.id !== RUNS_TABLE_COLUMNS.LAST_QUEUED_AT &&
    column.id !== 'actions' &&
    column.id !== RUNS_TABLE_COLUMNS.SELECT &&
    column.id !== RUNS_TABLE_COLUMNS.REFERENCE_EXAMPLE_ID
);

const EMPTY_TABLE = [];

export const RunsTable = ({
  view,
  onViewChange,

  tableDisplay,
  runsFilter,
  debouncedRunsFilter,

  searchModel,
  setSearchModel,

  columnVisibilityModel,
  setColumnVisibilityModel,
  onColumnVisibilityResetClick,
  prioritizedColumns = [],

  timeModel,
  setTimeModel,

  navigateToRun,

  withToolbar = true,
  withPagination = true,
  withTypeCol = false,

  virtual = false,
  className,

  onExperimentalSearchToggle,
}: RunsTableProps) => {
  const navigate = useNavigate();
  const [queryGenfeedbackUrls, setQueryGenFeedbackUrls] = useState<
    Record<string, string>
  >({});
  const { annotationQueueId, sessionId, datasetShareToken } = useParams();
  // peek
  const [searchParams, setSearchParams] = useSearchParams();
  const peekedRunId = searchParams.get('peek');
  const peekedTraceId = searchParams.get('peeked_trace');

  const setPeekedRun = (runId: string | null) => {
    if (runId) {
      setSearchParams(
        (prev) => {
          prev.set('peek', runId);
          return prev;
        },
        { replace: true }
      );
    } else {
      setSearchParams(
        (prev) => {
          prev.delete('peek');
          return prev;
        },
        { replace: true }
      );
    }
  };

  const setPeekedTraceId = (traceId: string | null) => {
    if (traceId) {
      setSearchParams(
        (prev) => {
          prev.set('peeked_trace', traceId);
          return prev;
        },
        { replace: true }
      );
    } else {
      setSearchParams(
        (prev) => {
          prev.delete('peeked_trace');
          return prev;
        },
        { replace: true }
      );
    }
  };

  const [shouldRevalidateOnFocus, setShouldRevalidateOnFocus] = useState(true);

  // columns
  const columns: ColumnDef<RunSchema, unknown>[] = datasetShareToken
    ? PUBLIC_COLUMNS_DEF
    : COLUMNS_DEF;

  // Sort columns based on priority
  const sortedColumns = useMemo(
    () => sortColumns(columns, prioritizedColumns),
    [columns, prioritizedColumns]
  );

  const [_columnSizing, setColumnSizing] =
    useDataGridSizingLocalStorage('runs');

  const sharedOptions = {
    refreshInterval:
      debouncedRunsFilter?.filter || peekedRunId ? undefined : REFRESH_INTERVAL,
    keepPreviousData: true,
    errorRetryCount: 0,
    // Need to revalidate first page if run details page is open because
    // adding to dataset/annotation queue requires revalidation of all runs.
    revalidateFirstPage: shouldRevalidateOnFocus || Boolean(peekedRunId),
    revalidateOnFocus: shouldRevalidateOnFocus,
  };

  const fullFilter = useMemo((): GetRunsQueryParams => {
    let select = sortedColumns
      .flatMap(
        (c) =>
          (c.meta as any)?.select ??
          ('accessorKey' in c ? c.accessorKey : undefined) ??
          c.id
      )
      .filter(
        (c) =>
          !!c &&
          ((columnVisibilityModel[c] ?? true) ||
            c === RUNS_TABLE_COLUMNS.INPUTS ||
            c === RUNS_TABLE_COLUMNS.OUTPUTS)
      );

    if (select.includes(RUNS_TABLE_COLUMNS.INPUTS)) {
      select = select.filter((x) => x !== RUNS_TABLE_COLUMNS.INPUTS);
      select.push('inputs_preview');
    }

    if (select.includes(RUNS_TABLE_COLUMNS.OUTPUTS)) {
      select = select.filter((x) => x !== RUNS_TABLE_COLUMNS.OUTPUTS);
      select.push('outputs_preview');
    }

    return {
      ...(debouncedRunsFilter ?? {}),
      select,
      limit: 30,
      skip_prev_cursor: true,
    };
  }, [debouncedRunsFilter, columnVisibilityModel, sortedColumns]);

  const {
    data: runsPages,
    isValidating,
    isLoading: runsLoading,
    size,
    setSize,
    mutate: mutateInfiniteRuns,
    error,
  } = useRunsInfinite(fullFilter, { datasetShareToken }, { ...sharedOptions });

  const { runIds: allRunsIds, isLoading: isLoadingAllRuns } = useSelectAllRuns({
    params: { ...fullFilter, select: [], skip_prev_cursor: true },
  });

  const hasMoreRuns =
    (runsPages?.at(-1) as any)?.headers?.['x-pagination-has-more'] !== 'false';

  const isNextPageValidating =
    isValidating && size > 1 && runsPages?.length != size;

  const runs = useMemo(() => runsPages?.flatMap((page) => page), [runsPages]);

  useEffect(() => {
    if (runs && queryGenfeedbackUrls?.results_size) {
      fetch(`${queryGenfeedbackUrls.results_size}?score=${runs.length}`);
      setQueryGenFeedbackUrls((urls) => {
        const newUrls = { ...urls };
        delete newUrls.results_size;
        return newUrls;
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [runs]);

  const peekedRunIndex = peekedRunId
    ? runs?.findIndex((run) => {
        if (view === 'trace') {
          return run.id === peekedTraceId;
        } else {
          return run.id === peekedRunId;
        }
      })
    : null;

  const peekedRunInRuns =
    peekedRunIndex != null && peekedRunIndex >= 0
      ? runs?.[peekedRunIndex]
      : null;

  const peekedRunRootStartTime = peekedRunInRuns
    ? getRootStartDate(peekedRunInRuns.dotted_order).toISOString()
    : undefined;

  const compareProps = useTraceCompare();
  const {
    selectingCompare,
    compareRunId,
    handleSelectCompare,
    setSelectingCompare,
    isShowingDiff,
    isAutoTracking,
    isScrollingSynced,
  } = compareProps;
  const { refA: scrollRefPeeked, refB: scrollRefCompare } =
    useScrollSync(isScrollingSynced);

  const {
    baseRun: peekedRun,
    comparisonRun,
    handleUpdateAutoTrackingIndex,
  } = useGetTrackedComparisonRun({
    rootBaseRunId: peekedRunId ?? undefined,
    rootCompareRunId: compareRunId ?? undefined,
    setComparisonRunId: handleSelectCompare,
    setBaseRunId: setPeekedRun,
    isAutoTracking,
    baseRunSessionId: sessionId,
  });

  const {
    data: comparisonRunData,
    isValidating: comparisonRunIsValidating,
    isLoading: comparisonRunIsLoading,
  } = comparisonRun;
  const {
    data: peekedRunData,
    isValidating: peekedRunIsValidating,
    isLoading: peekedRunIsLoading,
  } = peekedRun;

  // columns and actions
  const [control, actions] = useRunListActions();

  const [rowSelection, _setRowSelection] = useState<RowSelectionState>({});
  const [expanded, setExpanded] = useState<ExpandedState>({});

  const setRowSelection: React.Dispatch<
    React.SetStateAction<RowSelectionState>
  > = useCallback(
    (action) => {
      if (selectingCompare) {
        const newlySelectedRowId = findNewlySelectedRow(action, rowSelection);
        if (newlySelectedRowId) {
          handleSelectCompare(newlySelectedRowId);
        }
      } else {
        _setRowSelection(action);
      }

      if (queryGenfeedbackUrls?.user_selected_run) {
        fetch(`${queryGenfeedbackUrls.user_selected_run}`);
        setQueryGenFeedbackUrls((urls) => {
          const newUrls = { ...urls };
          delete newUrls.user_selected_run;
          return newUrls;
        });
      }
    },
    [queryGenfeedbackUrls, selectingCompare, rowSelection]
  );

  const runStatsWithoutFilters = useRunStats(
    debouncedRunsFilter
      ? Object.fromEntries(
          Object.entries(debouncedRunsFilter).filter(
            ([key]) => key !== 'filter'
          )
        )
      : null,
    { datasetShareToken }
  );

  const { createToast } = useToast();

  const setBulkActionResult = (result: BulkActionResult) => {
    createToast({
      description: result.message,
      error: !result.success,
      title: result.title,
    });
  };

  const uniqueExampleIds = useMemo(
    () => [
      ...new Set(
        runs
          ?.map((run) => run.reference_example_id)
          .filter((id): id is string => !!id)
      ),
    ],
    [runs]
  );
  const examples = useExamples(
    uniqueExampleIds?.length ? { id: uniqueExampleIds } : null
  );
  const visibleRuns = useMemo(() => {
    return runs?.map((run) => ({
      ...run,
      reference_example: examples.data?.find(
        (example) => example.id === run.reference_example_id
      ),
    }));
  }, [examples.data, runs]);

  const hasSelectedRuns = Object.keys(rowSelection).length > 0;
  const selectedRuns = useMemo(
    () =>
      visibleRuns?.filter(
        (row) =>
          Object.keys(rowSelection).includes(row.id) && rowSelection[row.id]
      ) ?? [],
    [rowSelection, visibleRuns]
  );

  // If runs are being created as the user looks at the page, some may get pushed off the screen. Deselect those.
  useEffect(() => {
    if (!visibleRuns) return;
    const allRunsSelected = allRunsIds.every((id) => rowSelection[id]);
    if (allRunsSelected) return;

    const updatedRowSelectionState = { ...rowSelection };

    let hasDeleted = false;
    Object.keys(updatedRowSelectionState).forEach((id) => {
      if (!visibleRuns.find((run) => run.id === id)) {
        delete updatedRowSelectionState[id];
        hasDeleted = true;
      }
    });

    if (hasDeleted) {
      setRowSelection(updatedRowSelectionState);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowSelection, visibleRuns]);

  const onAddToDatasetSuccess = () => {
    mutateInfiniteRuns();
  };

  const { columnSizing, columnVisibilityModel: maybeSlicedVisibilityModel } =
    getColumnsAndSizing(
      columns,
      _columnSizing,
      columnVisibilityModel,
      peekedRunId
    );

  const table = useReactTable({
    data: visibleRuns ?? EMPTY_TABLE,
    columns: sortedColumns,
    state: {
      columnVisibility: maybeSlicedVisibilityModel,
      rowSelection:
        selectingCompare && peekedRunId
          ? {
              [peekedRunId]: true,
            }
          : rowSelection,
      expanded,
      columnSizing,
    },
    onExpandedChange: setExpanded,
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibilityModel,
    onColumnSizingChange: setColumnSizing,
    getRowId: (row) => row.id,
    meta: { tableDisplay, actions, withTypeCol },
    columnResizeMode: 'onChange',
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),

    enableRowSelection: (row) =>
      !selectingCompare || row.original.id !== peekedRunId,
    enableMultiRowSelection: !selectingCompare,
    enableSorting: false,
    enableExpanding: true,

    manualExpanding: true,
    manualFiltering: true,
    manualSorting: true,
    manualPagination: true,

    getRowCanExpand: (row) => (row.original.child_run_ids?.length ?? 0) > 0,
  });

  const onRowClick = ({
    row,
    event,
    emulateClick,
  }:
    | {
        row: RunSchema;
      } & (
        | {
            emulateClick: typeof emulateNativeMiddleClick;
            event: undefined;
          }
        | {
            emulateClick: typeof emulateNativeClick;
            event: MouseEvent;
          }
      )) => {
    const targetUrl = datasetShareToken
      ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${sessionId}/${appSessionPath}/${appRunPath}/${row.id}?trace_id=${row?.trace_id}&start_time=${row?.start_time}`
      : row.app_path;

    if (queryGenfeedbackUrls?.user_opened_run) {
      fetch(`${queryGenfeedbackUrls.user_opened_run}?value=${row.id}`);
      setQueryGenFeedbackUrls((urls) => {
        const newUrls = { ...urls };
        delete newUrls.user_opened_run;
        return newUrls;
      });
    }

    const emulateClickResult = event
      ? emulateClick(targetUrl, event)
      : emulateClick(targetUrl);

    if (!emulateClickResult) {
      if (selectingCompare) {
        if (peekedRunId !== row.id) {
          handleSelectCompare(row.id);
        }
      } else if (peekedRunId === row.id) {
        setPeekedRun(null);
        setPeekedTraceId(null);
        handleSelectCompare(null);
      } else {
        setPeekedRun(row.id);
        setPeekedTraceId(row.trace_id);
        handleSelectCompare(null);
      }
    }
  };

  const { getDiffValues } = useRunDiffs(
    peekedRunData,
    comparisonRunData,
    isShowingDiff,
    comparisonRunIsLoading,
    comparisonRunIsValidating,
    peekedRunIsValidating,
    peekedRunIsLoading
  );

  return (
    <div
      className={cn(
        'grid h-full grid-rows-[auto,auto,1fr] overflow-x-auto overflow-y-hidden',
        className
      )}
    >
      {(withToolbar || error) && (
        <div
          className={cn(
            'flex flex-col items-stretch justify-between gap-4',
            virtual ? 'border-b border-secondary p-4' : 'mb-2'
          )}
        >
          {withToolbar && (
            <div className="flex items-stretch justify-between gap-2">
              <div className="flex flex-1 items-center gap-2">
                <FilterBar
                  variant={'runs'}
                  stats={runStatsWithoutFilters.data}
                  value={searchModel}
                  onChange={setSearchModel}
                  feedbackUrls={queryGenfeedbackUrls}
                  setFeedbackUrls={setQueryGenFeedbackUrls}
                  onViewChange={onViewChange}
                  onExperimentalSearchToggle={onExperimentalSearchToggle}
                />

                <DateTimeRangePicker
                  value={timeModel ?? undefined}
                  onChange={(value) => setTimeModel(value ?? {})}
                  context="runsTable"
                />

                <FilterViewsSelect
                  view={view}
                  onViewChange={onViewChange}
                  sessionId={sessionId}
                  filterBarVariant={'runs'}
                  experimentalSearchEnabled={searchModel.useExperimentalSearch}
                />
              </div>

              <DataGridColumnVisibilityPopover
                table={table}
                onResetClick={onColumnVisibilityResetClick}
              />
            </div>
          )}

          {error && <ErrorBanner className="mb-0">{error.message}</ErrorBanner>}
        </div>
      )}
      <div>
        {selectingCompare ? (
          <div className="relative flex items-center justify-center gap-2 bg-brand-green-400 p-2 text-white">
            <BranchIcon className="h-4 w-4" />
            <span className="font-semibold">Start comparing</span>
            <span>
              Select one of the below traces in the table to start comparing
            </span>
            <button
              onClick={() => setSelectingCompare(false)}
              type="button"
              className="absolute right-6 rounded-md p-1 transition-colors hover:bg-ls-black/40"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
        ) : (
          !shouldRevalidateOnFocus && (
            <LoadLatestButton
              onClick={() => {
                // mutateInfiniteRuns can take parameters,
                // onClick by default passes event which messes up the
                // mutation and causes a bug.
                mutateInfiniteRuns();
              }}
              datasetShareToken={datasetShareToken}
              fullFilter={fullFilter}
              existingRuns={runs}
              runsLoading={runsLoading}
              hasPeeked={!!peekedRunId}
            />
          )
        )}
      </div>
      {!error && (
        <>
          <div>
            <DataGridWithFooter
              virtual={virtual}
              table={table}
              emptyState={null}
              isLoading={isValidating}
              onClick={(row, event) =>
                onRowClick({
                  row,
                  event: event.nativeEvent,
                  emulateClick: emulateNativeClick,
                })
              }
              onMiddleClick={(row) => {
                onRowClick({
                  row,
                  event: undefined,
                  emulateClick: emulateNativeMiddleClick,
                });
              }}
              highlightedIds={
                [
                  typeof peekedRunIndex === 'number'
                    ? runs?.[peekedRunIndex]?.id
                    : null,
                  compareRunId,
                ].filter(Boolean) as string[]
              }
              footer={
                <>
                  {!isValidating && table.getRowModel().rows.length <= 0 && (
                    <NoRunsDisclaimer />
                  )}

                  {hasMoreRuns &&
                    (runsPages?.length ?? 0 > 0) &&
                    withPagination && (
                      <div className="flex flex-col items-center border-t border-secondary">
                        <LoadMoreButton
                          isInitialLoad
                          isLoading={isNextPageValidating}
                          isVirtual={virtual}
                          onClick={() => {
                            setSize((size) => size + 1);
                            setShouldRevalidateOnFocus(false);
                          }}
                        />
                      </div>
                    )}
                </>
              }
            />
          </div>
          {hasSelectedRuns && (
            <SelectedRunsActions
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}
              allRunsIds={allRunsIds}
              isLoadingAllRuns={isLoadingAllRuns}
              selectedRuns={selectedRuns}
              mutateInfiniteRuns={mutateInfiniteRuns}
              setBulkActionResult={setBulkActionResult}
            />
          )}

          <RunListActions
            control={control}
            onAddToDatasetSuccess={onAddToDatasetSuccess}
          />

          {runs && (
            <SplitViewPane
              open={!!peekedRunId && !selectingCompare}
              sidePaneId="runsTable"
              onClose={() => {
                setPeekedRun(null);
                setPeekedTraceId(null);
                handleSelectCompare(null);
              }}
              onExpand={
                compareRunId
                  ? undefined
                  : () => {
                      if (navigateToRun && peekedRunId) {
                        navigateToRun(
                          peekedRunId,
                          peekedRunInRuns?.session_id,
                          peekedRunInRuns?.trace_id,
                          peekedRunRootStartTime
                        );
                      } else {
                        const run = peekedRunInRuns ?? peekedRunData;
                        if (run) {
                          let path = run.app_path;
                          if (annotationQueueId || datasetShareToken) {
                            path = `${appRunPath}/${run.id}?trace_id=${run.trace_id}&start_time=${run.start_time}`;
                          }
                          navigate(path);
                        }
                      }
                    }
              }
              onNext={
                typeof peekedRunIndex === 'number' &&
                peekedRunIndex < runs.length - 1 &&
                !compareRunId
                  ? () => {
                      if (peekedRunIndex < runs.length - 1) {
                        setPeekedRun(runs[peekedRunIndex + 1].id);
                        setPeekedTraceId(runs[peekedRunIndex + 1].trace_id);
                      }
                    }
                  : undefined
              }
              onPrevious={
                typeof peekedRunIndex === 'number' &&
                peekedRunIndex > 0 &&
                !compareRunId
                  ? () => {
                      if (peekedRunIndex > 0) {
                        setPeekedRun(runs[peekedRunIndex - 1].id);
                        setPeekedTraceId(runs[peekedRunIndex - 1].trace_id);
                      }
                    }
                  : undefined
              }
              className="relative"
              title={
                <RunDetailsPanelHeader
                  compareProps={compareProps}
                  peekedRunId={peekedRunId}
                  peekedRunData={peekedRunData}
                  compareRunId={compareRunId}
                  comparisonRunData={comparisonRunData}
                  handleUpdateAutoTrackingIndex={handleUpdateAutoTrackingIndex}
                  datasetShareToken={datasetShareToken}
                  isLoading={
                    peekedRunIsLoading ||
                    peekedRunIsValidating ||
                    comparisonRunIsLoading ||
                    comparisonRunIsValidating
                  }
                  onRefreshRun={() => {
                    peekedRun.mutate();
                    if (comparisonRunData) {
                      comparisonRun.mutate();
                    }
                  }}
                />
              }
            >
              <div className="absolute inset-0 flex flex-grow overflow-auto px-4">
                <CompareTraceLayout
                  peekedTraceView={
                    <Run
                      runId={peekedRunId}
                      traceId={peekedRunData?.trace_id}
                      runStartTime={
                        peekedRunRootStartTime ?? runsFilter?.start_time
                      }
                      runSessionId={peekedRunData?.session_id}
                      navigateToRun={(run) => {
                        setPeekedRun(run);
                        if (compareRunId && isAutoTracking) {
                          handleUpdateAutoTrackingIndex(run, 'peeked');
                        }
                      }}
                      shareTokenProp={{ datasetShareToken }}
                      comparePosition={compareRunId ? 'left' : undefined}
                      scrollRef={scrollRefPeeked}
                      // for peeked run, only show diffs that are removed
                      getDiffValues={(type) => getDiffValues(type, 'removed')}
                      forceRawRender={isShowingDiff ? 'raw' : undefined}
                      showAllRuns={!!compareRunId && isAutoTracking}
                    />
                  }
                  compareRunId={compareRunId}
                  compareTraceView={
                    <Run
                      runId={comparisonRunData?.id}
                      traceId={comparisonRunData?.trace_id}
                      runStartTime={
                        (comparisonRunData &&
                          getRootStartDate(
                            comparisonRunData?.dotted_order
                          ).toISOString()) ??
                        runsFilter?.start_time
                      }
                      navigateToRun={(run) => {
                        handleSelectCompare(run);
                        if (compareRunId && isAutoTracking) {
                          handleUpdateAutoTrackingIndex(run, 'compare');
                        }
                      }}
                      shareTokenProp={{ datasetShareToken }}
                      comparePosition="right"
                      scrollRef={scrollRefCompare}
                      // for compare run, only show diffs that are added
                      getDiffValues={(type) => getDiffValues(type, 'added')}
                      forceRawRender={isShowingDiff ? 'raw' : undefined}
                      showAllRuns={isAutoTracking}
                    />
                  }
                ></CompareTraceLayout>
              </div>
            </SplitViewPane>
          )}
        </>
      )}
    </div>
  );
};
