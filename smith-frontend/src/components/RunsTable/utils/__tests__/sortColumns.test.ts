import { ColumnDef } from '@tanstack/react-table';

import { describe, expect, it } from 'vitest';

import { RunSchema } from '@/types/schema';

import { RUNS_TABLE_COLUMNS } from '../constants';
import { sortColumns } from '../sortColumns';

describe('sortColumns', () => {
  // Helper to create a mock column
  const createMockColumn = (id: string): ColumnDef<RunSchema, unknown> => ({
    id,
    header: id,
    cell: () => null,
  });

  it('should keep fixed columns in correct order', () => {
    const columns = [
      createMockColumn(RUNS_TABLE_COLUMNS.NAME),
      createMockColumn(RUNS_TABLE_COLUMNS.SELECT),
      createMockColumn(RUNS_TABLE_COLUMNS.STATUS),
    ];

    const result = sortColumns(columns);

    expect(result.map((col) => col.id)).toEqual([
      RUNS_TABLE_COLUMNS.SELECT,
      RUNS_TABLE_COLUMNS.STATUS,
      RUNS_TABLE_COLUMNS.NAME,
    ]);
  });

  it('should put prioritized columns after fixed columns', () => {
    const columns = [
      createMockColumn(RUNS_TABLE_COLUMNS.NAME),
      createMockColumn(RUNS_TABLE_COLUMNS.SELECT),
      createMockColumn(RUNS_TABLE_COLUMNS.STATUS),
      createMockColumn(RUNS_TABLE_COLUMNS.LATENCY),
      createMockColumn(RUNS_TABLE_COLUMNS.TOTAL_TOKENS),
    ];

    const result = sortColumns(columns, [RUNS_TABLE_COLUMNS.TOTAL_TOKENS]);

    expect(result.map((col) => col.id)).toEqual([
      RUNS_TABLE_COLUMNS.SELECT,
      RUNS_TABLE_COLUMNS.STATUS,
      RUNS_TABLE_COLUMNS.NAME,
      RUNS_TABLE_COLUMNS.TOTAL_TOKENS,
      RUNS_TABLE_COLUMNS.LATENCY,
    ]);
  });

  it('should maintain order of multiple prioritized columns', () => {
    const columns = [
      createMockColumn(RUNS_TABLE_COLUMNS.NAME),
      createMockColumn(RUNS_TABLE_COLUMNS.SELECT),
      createMockColumn(RUNS_TABLE_COLUMNS.STATUS),
      createMockColumn(RUNS_TABLE_COLUMNS.LATENCY),
      createMockColumn(RUNS_TABLE_COLUMNS.TOTAL_TOKENS),
      createMockColumn(RUNS_TABLE_COLUMNS.TOTAL_COST),
    ];

    const result = sortColumns(columns, [
      RUNS_TABLE_COLUMNS.TOTAL_TOKENS,
      RUNS_TABLE_COLUMNS.TOTAL_COST,
    ]);

    expect(result.map((col) => col.id)).toEqual([
      RUNS_TABLE_COLUMNS.SELECT,
      RUNS_TABLE_COLUMNS.STATUS,
      RUNS_TABLE_COLUMNS.NAME,
      RUNS_TABLE_COLUMNS.TOTAL_TOKENS,
      RUNS_TABLE_COLUMNS.TOTAL_COST,
      RUNS_TABLE_COLUMNS.LATENCY,
    ]);
  });

  it('should handle missing fixed columns', () => {
    const columns = [
      createMockColumn(RUNS_TABLE_COLUMNS.LATENCY),
      createMockColumn(RUNS_TABLE_COLUMNS.TOTAL_TOKENS),
    ];

    const result = sortColumns(columns);

    expect(result.map((col) => col.id)).toEqual([
      RUNS_TABLE_COLUMNS.LATENCY,
      RUNS_TABLE_COLUMNS.TOTAL_TOKENS,
    ]);
  });

  it('should handle empty columns array', () => {
    const result = sortColumns([]);
    expect(result).toEqual([]);
  });

  it('should handle empty prioritized columns', () => {
    const columns = [
      createMockColumn(RUNS_TABLE_COLUMNS.NAME),
      createMockColumn(RUNS_TABLE_COLUMNS.SELECT),
      createMockColumn(RUNS_TABLE_COLUMNS.STATUS),
      createMockColumn(RUNS_TABLE_COLUMNS.LATENCY),
    ];

    const result = sortColumns(columns, []);

    expect(result.map((col) => col.id)).toEqual([
      RUNS_TABLE_COLUMNS.SELECT,
      RUNS_TABLE_COLUMNS.STATUS,
      RUNS_TABLE_COLUMNS.NAME,
      RUNS_TABLE_COLUMNS.LATENCY,
    ]);
  });
});
