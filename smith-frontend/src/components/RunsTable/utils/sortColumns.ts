import { ColumnDef } from '@tanstack/react-table';

import { RunSchema } from '@/types/schema';

import { RUNS_TABLE_COLUMNS, RunsTableColumnId } from './constants';

export function sortColumns<T extends RunSchema>(
  columns: ColumnDef<T, unknown>[],
  prioritizedColumns: string[] = []
): ColumnDef<T, unknown>[] {
  const excludedColumns = [
    RUNS_TABLE_COLUMNS.SELECT,
    RUNS_TABLE_COLUMNS.STATUS,
    RUNS_TABLE_COLUMNS.NAME,
  ] as const;

  // Get fixed position columns
  const fixedColumns = excludedColumns
    .map((id) => columns.find((col) => col.id === id))
    .filter((col): col is ColumnDef<T, unknown> => !!col);

  // Get remaining columns excluding fixed ones
  const remainingColumns = columns.filter(
    (col) =>
      !excludedColumns.includes(col.id as (typeof excludedColumns)[number])
  );

  // Split remaining columns into prioritized and others
  const [prioritizedCols, otherCols] = remainingColumns.reduce<
    [ColumnDef<T, unknown>[], ColumnDef<T, unknown>[]]
  >(
    ([prioritized, others], col) => {
      prioritizedColumns.includes(col.id as RunsTableColumnId)
        ? prioritized.push(col)
        : others.push(col);
      return [prioritized, others];
    },
    [[], []]
  );

  return [...fixedColumns, ...prioritizedCols, ...otherCols];
}
