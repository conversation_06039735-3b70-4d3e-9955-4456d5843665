export const RUNS_TABLE_COLUMNS = {
  SELECT: 'select',
  STATUS: 'status',
  NAME: 'name',
  INPUTS: 'inputs',
  OUTPUTS: 'outputs',
  ERROR: 'error',
  START_TIME: 'start_time',
  LATENCY: 'latency',
  IN_DATASET: 'in_dataset',
  LAST_QUEUED_AT: 'last_queued_at',
  TOTAL_TOKENS: 'total_tokens',
  TOTAL_COST: 'total_cost',
  FIRST_TOKEN_TIME: 'first_token_time',
  TAGS: 'tags',
  METADATA: 'metadata',
  FEEDBACK_STATS: 'feedback_stats',
  REFERENCE_EXAMPLE_ID: 'reference_example_id',
} as const;

export type RunsTableColumnId =
  (typeof RUNS_TABLE_COLUMNS)[keyof typeof RUNS_TABLE_COLUMNS];
