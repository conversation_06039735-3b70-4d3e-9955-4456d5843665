import {
  ChevronDownIcon,
  PlusIcon,
  Tag03Icon,
} from '@langchain/untitled-ui-icons';
import { Checkbox, CircularProgress, LinearProgress } from '@mui/joy';

import { Dispatch, SetStateAction, useMemo, useState } from 'react';

import { ResourceTagCrudPane } from '@/Pages/Settings/components/ResourceTagCrudPane';
import { usePermissions } from '@/hooks/usePermissions';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  ResourceTag,
  ResourceTagKeyWithValues,
  ResourceTagValue,
} from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { ExpandableErrorAlert } from '../ExpandableErrorAlert';
import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import { TextOverflowTooltip } from '../TextOverflowTooltip';
import { ResourceTagListSearch } from './ResourceTagCrudPaneEditor';
import { SingleKey, SingleResourceTag } from './SingleResourceTag';
import { insertAtExistingKey } from './utils/insertAtExistingKey';
import { useResourceTagKeysToShow } from './utils/useResourceTagKeysToShow';
import { useSingleResourceTags } from './utils/useSingleResourceTags';

function ResourceTagValueItem({
  tag,
  value,
  selectedTags,
  onItemClick,
  onCheckBoxClick,
}: {
  tag: { id: string; key: string };
  value: ResourceTagValue;
  selectedTags: ResourceTag[];
  onItemClick: (tag: ResourceTag) => void;

  onCheckBoxClick: (tag: ResourceTag) => void;
}) {
  const checked = selectedTags.some(
    (sTag) => sTag.tag_key === tag.key && sTag.tag_value === value.value
  );

  return (
    <div
      key={value.id}
      className="group flex w-full items-center gap-4 rounded-md p-2 text-left"
    >
      <span className="flex-1">
        <TextOverflowTooltip className="max-w-[130px] text-sm">
          {value.value}
        </TextOverflowTooltip>

        <TextOverflowTooltip className="max-w-[130px] text-sm text-secondary">
          {value.description}
        </TextOverflowTooltip>
      </span>
      <button
        type="button"
        onClick={() => {
          const mTag = {
            tag_key: tag.key,
            tag_value: value.value,
            tag_value_id: value.id,
            tag_key_id: tag.id,
          };

          onItemClick(mTag);
        }}
        className={cn(
          'flex-none rounded-lg px-3 py-1 text-xs opacity-0 transition-opacity group-hover:bg-secondary group-hover:opacity-100',
          '[.group:has(>:last-child:hover)_&]:opacity-0'
        )}
      >
        ONLY
      </button>
      <span className="w-5">
        <Checkbox
          data-testid={`${tag.key}-${value.value}-checkbox`}
          checked={checked}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onChange={(e) => {
            e.stopPropagation();
            onCheckBoxClick({
              tag_key: tag.key,
              tag_value: value.value,
              tag_value_id: value.id,
              tag_key_id: tag.id,
            });
          }}
        />
      </span>
    </div>
  );
}

function SingleKeyPicker(props: {
  tagKeyId: string;
  tagKey: string;
  resourceTags: ResourceTag[];
  onNewValue: (tag: { tag_key: string; tag_key_id: string }) => void;
  tagValues: ResourceTagValue[];
}) {
  const { resourceTags, onNewValue, tagKeyId, tagKey, tagValues } = props;
  const { selectedTags, setSelectedTags } = useStoredResourceTags();

  // const { data, isLoading } = useListTagValues(tagKeyId);
  const [isOpen, setOpen] = useState(false);

  const tagKeyForPanel = useMemo(
    () => ({ tag_key: tagKey, tag_key_id: tagKeyId }),
    [tagKey, tagKeyId]
  );

  const handleSelect = (tag: ResourceTag) => {
    setSelectedTags((prev) => {
      return insertAtExistingKey(prev, tag, true).filter(
        (v) =>
          v.tag_key_id !== tag.tag_key_id || v.tag_value_id === tag.tag_value_id
      );
    });
  };

  const handleDeselect = (tag: ResourceTag) => {
    setSelectedTags((prev) => {
      const newSelectedTags = prev.filter(
        (t) => t.tag_value_id !== tag.tag_value_id
      );
      return newSelectedTags;
    });
  };

  const handleCheckBoxClick = (tag: ResourceTag) => {
    if (selectedTags.some((t) => t.tag_value_id === tag.tag_value_id)) {
      handleDeselect(tag);
    } else {
      setSelectedTags((prev) => {
        return insertAtExistingKey(prev, tag);
      });
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setOpen}>
      <PopoverTrigger>
        <SingleKey
          key={tagKeyId}
          tag_key={tagKey}
          tags={resourceTags}
          values={tagValues ?? []}
          maxWidth={300}
          canSelect={true}
          onNewValue={() => {
            setOpen(true);
            onNewValue(tagKeyForPanel);
          }}
          onRemove={(tag) => {
            setSelectedTags((prev) =>
              prev.filter(
                (t) =>
                  t.tag_key !== tag.tag_key || t.tag_value !== tag.tag_value
              )
            );
          }}
        />
      </PopoverTrigger>
      <PopoverContent
        className="flex max-h-[500px] flex-col overflow-y-auto p-0"
        align="end"
      >
        <div className="flex items-center justify-between gap-2 pr-2">
          <span className="truncate p-2 text-sm font-semibold">{tagKey}</span>
        </div>
        {tagValues?.map((tagValue) => (
          <ResourceTagValueItem
            tag={{ key: tagKey, id: tagKeyId }}
            value={tagValue}
            selectedTags={selectedTags}
            onItemClick={handleSelect}
            onCheckBoxClick={handleCheckBoxClick}
            key={tagKeyId}
          />
        ))}
        <button
          type="button"
          className="flex justify-start p-2 text-sm hover:bg-tertiary"
          onClick={() => {
            props.onNewValue({
              tag_key: tagKey,
              tag_key_id: tagKeyId,
            });
          }}
        >
          + New value
        </button>
      </PopoverContent>
    </Popover>
  );
}

function ExtraTags(props: {
  tags: (ResourceTagKeyWithValues & {
    selected?: ResourceTag[];
    mismatched?: ResourceTag[];
  })[];
  inAssignMode?: boolean;
  tagResourceWithMismatched?: Dispatch<SetStateAction<ResourceTag[]>>;
  isApplying?: boolean;
}) {
  const { setSelectedTags } = useStoredResourceTags();
  const handleDeselect = (tag: ResourceTag) => {
    setSelectedTags((prev) => {
      const newSelectedTags = prev.filter(
        (t) => t.tag_key !== tag.tag_key || t.tag_value !== tag.tag_value
      );
      return newSelectedTags;
    });
  };

  return (
    <div className="flex flex-col gap-2 p-3">
      <div className="flex min-h-[30px] items-center justify-between gap-2">
        <span className="truncate text-sm font-semibold">Selected tags</span>
        {props.isApplying && <CircularProgress size="sm" />}
      </div>
      {props.tags.map((tagKey) => {
        return tagKey.selected?.map((tag) => {
          const isMismatched = tagKey.mismatched?.some(
            (mTag) =>
              mTag.tag_key_id === tag.tag_key_id &&
              mTag.tag_value_id === tag.tag_value_id
          );
          return (
            <div
              key={tag.tag_value_id}
              className="flex items-center justify-between gap-2"
            >
              <SingleResourceTag
                maxWidth={props.inAssignMode ? 200 : 230}
                key={tag.tag_key_id}
                tag_key={tag.tag_key}
                tag_value={tag.tag_value}
                onRemove={
                  props.inAssignMode
                    ? undefined
                    : () => {
                        handleDeselect(tag);
                      }
                }
                isMismatched={props.inAssignMode && isMismatched}
              />
              {props.inAssignMode &&
                props.tagResourceWithMismatched &&
                isMismatched && (
                  <button
                    type="button"
                    className={cn(
                      'border px-2 py-1 text-sm hover:bg-secondary',
                      'border-utility rounded-full'
                    )}
                    onClick={() => {
                      props.tagResourceWithMismatched?.((prev) => {
                        return [...prev, tag];
                      });
                    }}
                  >
                    Apply
                  </button>
                )}
            </div>
          );
        });
      })}
    </div>
  );
}

export function AssignTags(props: {
  resourceName: string;
  tagsOnResource: ResourceTag[];
  setTagsOnResource: Dispatch<SetStateAction<ResourceTag[]>>;
  onCreateTag: () => void;
  isMutating: boolean;
  error?: Error;
}) {
  const {
    resourceName,
    tagsOnResource,
    setTagsOnResource,
    onCreateTag,
    isMutating,
    error,
  } = props;

  const { authorize } = usePermissions('workspace');
  const canManageTags = authorize('workspaces:manage');

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div
          className={cn(
            'flex items-center gap-2 rounded-full border px-2 py-1 text-sm',
            'cursor-pointer border-primary text-primary hover:bg-tertiary'
          )}
        >
          <Tag03Icon className="h-4 w-4" />
          <div className="line-clamp-1">
            {!canManageTags
              ? 'View resource tags'
              : tagsOnResource.length > 0
              ? 'Edit resource tags'
              : 'Add resource tags'}
          </div>
          {tagsOnResource.length > 0 && (
            <div className="rounded-full bg-background px-1.5 text-xxs">
              {tagsOnResource.length}
            </div>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent align="end" className="relative w-[300px] p-0">
        <div className="p-2 text-sm font-semibold">{resourceName}</div>
        {canManageTags && (
          <div className="px-2 text-xs">
            Select tags to assign to {resourceName}
          </div>
        )}
        {isMutating && (
          <div className="relative w-full">
            <div className="absolute top-0 w-full">
              <LinearProgress />
            </div>
          </div>
        )}
        <ResourceTagListSearch
          tagsOnResource={tagsOnResource}
          setTagsOnResource={setTagsOnResource}
          canManageTags={canManageTags}
        />

        {error && (
          <div className="p-2">
            <ExpandableErrorAlert error={error} />
          </div>
        )}
        {canManageTags && (
          <div className="flex p-2 pt-0">
            <button
              type="button"
              className={cn(
                'flex-1 rounded-md border border-primary px-2 py-1 text-sm hover:bg-secondary'
              )}
              onClick={onCreateTag}
            >
              Create tag
            </button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}

export function ResourceTagFilter(props: { inAssignMode?: boolean }) {
  const {
    tags: tagsOnResource,
    setTagSelection: setTagsOnResource,
    isLoading,
    isMutating,
    resourceName,
    error,
  } = useSingleResourceTags();

  const { inAssignMode } = props;
  const [isOpen, setOpen] = useState(false);

  const [focusedTagKey, setFocusedTagKey] = useState<
    | {
        tag_key: string;
        tag_key_id: string;
      }
    | undefined
  >();

  const keysToShow = useResourceTagKeysToShow(tagsOnResource);

  const firstTwo = keysToShow.slice(0, 2);
  const rest = keysToShow.slice(2);

  const restTags = rest.flatMap((tagKey) => {
    return tagKey.selected ?? [];
  });

  if (isLoading) {
    return null;
  }

  if (inAssignMode) {
    return resourceName ? (
      <>
        <AssignTags
          isMutating={isMutating}
          resourceName={resourceName}
          tagsOnResource={tagsOnResource}
          setTagsOnResource={setTagsOnResource}
          onCreateTag={() => {
            setFocusedTagKey({ tag_key: '', tag_key_id: '' });
            setOpen(true);
          }}
          error={error}
        />
        <ResourceTagCrudPane
          isOpen={isOpen}
          setOpen={setOpen}
          resourceTag={focusedTagKey}
        />
      </>
    ) : null;
  }

  return (
    <div className="flex items-center gap-2">
      {firstTwo.map((tagKeyWithValues) => (
        <SingleKeyPicker
          key={tagKeyWithValues.id}
          tagKey={tagKeyWithValues.key}
          resourceTags={tagKeyWithValues.selected ?? []}
          tagKeyId={tagKeyWithValues.id}
          onNewValue={(tagKey) => {
            setOpen(true);
            setFocusedTagKey(tagKey);
          }}
          tagValues={tagKeyWithValues.values}
        />
      ))}

      {restTags.length > 0 && (
        <Popover>
          <PopoverTrigger className="flex items-center gap-2">
            <div
              className={cn(
                'flex items-center gap-2 rounded-full border px-2 py-1 text-sm',
                'border-primary bg-secondary'
              )}
            >
              {'+' + restTags.length}
              <ChevronDownIcon className="h-4 w-4" />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0">
            <ExtraTags tags={rest} isApplying={isMutating || isLoading} />
          </PopoverContent>
        </Popover>
      )}

      <button
        type="button"
        onClick={() => {
          setOpen(true);
        }}
        className="rounded-full border border-secondary p-1.5 text-secondary hover:bg-secondary"
      >
        {keysToShow.length > 0 ? (
          <PlusIcon
            className="h-4 w-4 text-tertiary"
            aria-label={'Open resource tags pane'}
          />
        ) : (
          <div className="px-1 text-xs">Setup resource tags</div>
        )}
      </button>

      <ResourceTagCrudPane
        isOpen={isOpen}
        setOpen={setOpen}
        resourceTag={focusedTagKey}
      />
    </div>
  );
}
