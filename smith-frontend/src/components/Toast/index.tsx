import { ExclamationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import * as ToastLib from '@radix-ui/react-toast';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import { cn } from '@/utils/tailwind';

import ErrorIcon from './icons/ErrorIcon.svg?react';
import SuccessIcon from './icons/SuccessIcon.svg?react';
import WarningIcon from './icons/WarningIcon.svg?react';

const ToastContext = React.createContext<{
  createToast: (titleOrConfig: ToastConfig | string) => void;
}>({
  createToast: () => {
    console.error('ToastContext not initialized');
  },
});

const ToastUnmount = (props: { onUnmount: () => void }) => {
  const ref = useRef(props.onUnmount);
  ref.current = props.onUnmount;

  useEffect(() => {
    return () => ref.current?.();
  }, []);

  return null;
};

export function MultiToastProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const counterRef = useRef(0);
  const [configs, setConfigs] = useState<
    Array<{ key: number; open: boolean; config: ToastConfig }>
  >([]);

  const createToast = useCallback((titleOrConfig: ToastConfig | string) => {
    const nextId = counterRef.current + 1;
    const config: ToastConfig = {
      // populate defaults before overwriting with preConfig
      duration: 5000,
      ...(typeof titleOrConfig === 'string'
        ? { title: titleOrConfig }
        : titleOrConfig),
    };

    setConfigs((prev) => [...prev, { key: nextId, open: true, config }]);
    counterRef.current = nextId;
  }, []);

  return (
    <ToastContext.Provider value={{ createToast }}>
      <ToastLib.Provider swipeDirection="right">
        <>{children}</>

        {configs.map(({ key, open, config }) => (
          <ToastLib.Root
            key={key}
            className={cn(
              'grid max-w-[400px] grid-cols-[1fr,auto] items-start gap-3',
              'rounded-xl border border-secondary bg-popover p-4 text-sm shadow-xl',
              'dark:border-[#26272B] dark:bg-[#1A1A1E]',
              'data-[swipe=cancel]:translate-x-0 data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=closed]:animate-hide data-[state=open]:animate-slideIn data-[swipe=end]:animate-swipeOut data-[swipe=cancel]:transition-[transform_200ms_ease-out]'
            )}
            duration={config.duration}
            open={open}
            onOpenChange={(open) => {
              setConfigs((prev) =>
                prev.map((c) => (c.key === key ? { ...c, open } : c))
              );
            }}
          >
            <ToastUnmount
              onUnmount={() => {
                setConfigs((prev) => prev.filter((c) => c.key !== key));
              }}
            />
            <div className="grid grid-cols-[auto,1fr] items-start gap-3">
              <div className="mt-[3px] flex-shrink-0 empty:hidden">
                {(config.type === 'error' || config.error) && <ErrorIcon />}
                {config.type === 'success' && <SuccessIcon />}
                {config.type === 'warning' && <WarningIcon />}
              </div>

              <div className="flex flex-col gap-1">
                <div className="flex flex-col gap-0.5">
                  {config.title && (
                    <ToastLib.Title
                      className={cn(
                        'font-semibold [grid-area:_title]',
                        'text-ls-black'
                      )}
                    >
                      {config.title}
                    </ToastLib.Title>
                  )}
                  {config.description && (
                    <ToastLib.Description asChild>
                      <span className="text-sm break-anywhere [grid-area:_description]">
                        {config.description}
                      </span>
                    </ToastLib.Description>
                  )}
                </div>
                {config.children && (
                  <div className="[grid-area:_actions]">{config.children}</div>
                )}
              </div>
            </div>
            <ToastLib.Close className="-translate-y-1 translate-x-1 rounded-md p-1 transition-colors hover:bg-secondary-hover active:bg-secondary">
              <XMarkIcon className="h-5 w-5" />
            </ToastLib.Close>
          </ToastLib.Root>
        ))}
        <ToastLib.Viewport className="fixed bottom-0 right-0 z-[**********] m-0 flex max-w-[100vw] list-none flex-col gap-[10px] p-[var(--viewport-padding)] outline-none [--viewport-padding:25px]" />
      </ToastLib.Provider>
    </ToastContext.Provider>
  );
}

// Provider wraps whole application
export function ToastProvider({
  children,
  variant = 'old',
}: {
  children: React.ReactNode;
  variant?: 'new' | 'old';
}) {
  // note this only supports one toast at a time currently
  const [open, setOpen] = React.useState(false);
  const [config, setConfig] = React.useState<ToastConfig | null>(null);
  const createToast = useCallback((titleOrConfig: ToastConfig | string) => {
    const preConfig =
      typeof titleOrConfig === 'string'
        ? { title: titleOrConfig }
        : titleOrConfig;
    const config: ToastConfig = {
      // populate defaults before overwriting with preConfig
      duration: 5000,
      ...preConfig,
    };

    setConfig(config);
  }, []);

  useEffect(() => {
    if (config) {
      setOpen(true);

      const timeout = setTimeout(() => {
        setOpen(false);
      }, config.duration);
      return () => clearTimeout(timeout);
    }
  }, [config]);

  const ToastComponent = variant === 'new' ? NewToast : Toast;

  return (
    <ToastContext.Provider value={{ createToast }}>
      <ToastLib.Provider swipeDirection="right">
        {children}
        <ToastComponent
          title={config?.title}
          description={config?.description}
          open={open}
          setOpen={setOpen}
          children={config?.children}
          error={config?.error}
          duration={config?.duration}
        />
        <ToastLib.Viewport
          className={cn(
            'fixed bottom-0 right-0 z-[**********] m-0 flex w-[390px] max-w-[100vw] list-none flex-col gap-[10px] p-[var(--viewport-padding)] outline-none [--viewport-padding:_25px]',
            config?.viewPortClassName
          )}
        />
      </ToastLib.Provider>
    </ToastContext.Provider>
  );
}

export type ToastConfig = {
  title: string | React.ReactNode;
  description?: React.ReactNode;
  children?: React.ReactNode;
  duration?: number;
  viewPortClassName?: string;
  error?: boolean;
  type?: 'error' | 'info' | 'warning' | 'success';
};

const ErrorIconComponent = ({ className }) => (
  <div className={cn('relative h-5 w-5 rounded-[20px]', className)}>
    <div className="absolute left-[-4px] top-[-4px] h-7 w-7 rounded-3xl border-2 border-ls-red-400 opacity-30" />
    <div className="absolute left-[-9px] top-[-9px] h-[38px] w-[38px] rounded-3xl border-2 border-ls-red-400 opacity-10" />
    <div className="absolute left-0 top-0 h-5 w-5" />
    <ExclamationCircleIcon className="absolute left-0 top-0 h-5 w-5 stroke-ls-red-400" />
  </div>
);

// toast component
function NewToast({
  title,
  open,
  setOpen,
  description,
  children,
  error,
  duration,
}: {
  title?: string | React.ReactNode;
  open: boolean;
  setOpen?: (open: boolean) => void;
  description?: React.ReactNode;
  children?: React.ReactNode;
  error?: boolean;
  duration?: number;
}) {
  return (
    <ToastLib.Root
      className={cn(
        'rounded-lg border-[#26272B] bg-[#1A1A1E]',
        'data-[swipe=cancel]:translate-x-0 data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=closed]:animate-hide data-[state=open]:animate-slideIn data-[swipe=end]:animate-swipeOut data-[swipe=cancel]:transition-[transform_200ms_ease-out]'
      )}
      open={open}
      onOpenChange={setOpen}
      duration={duration}
    >
      {error && <ErrorIconComponent className="[grid-area:_icon]" />}
      {title && (
        <ToastLib.Title
          className={cn('font-medium [grid-area:_title]', 'text-ls-gray-400')}
        >
          {title}
        </ToastLib.Title>
      )}
      {description && (
        <ToastLib.Description asChild>
          <span
            className={cn(
              'text-sm [grid-area:_description]',
              'text-ls-gray-200'
            )}
          >
            {description}
          </span>
        </ToastLib.Description>
      )}
      <div className="[grid-area:_actions]">{children}</div>
      {setOpen && <ToastLib.Close />}
    </ToastLib.Root>
  );
}

// toast component
function Toast({
  title,
  open,
  setOpen,
  description,
  children,
  error,
  duration,
}: {
  title?: string | React.ReactNode;
  open: boolean;
  setOpen?: (open: boolean) => void;
  description?: React.ReactNode;
  children?: React.ReactNode;
  error?: boolean;
  duration?: number;
}) {
  return (
    <ToastLib.Root
      className={cn(
        'grid grid-cols-[auto_1fr] items-center gap-2 gap-x-[15px] rounded-lg bg-black p-[15px] shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] dark:bg-white',
        "[grid-template-areas:_'icon_title'_'blank_description'_'blank_actions'] data-[swipe=cancel]:translate-x-0 data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=closed]:animate-hide data-[state=open]:animate-slideIn data-[swipe=end]:animate-swipeOut data-[swipe=cancel]:transition-[transform_200ms_ease-out]",
        error && 'border-2 border-error'
      )}
      open={open}
      onOpenChange={setOpen}
      duration={duration}
    >
      {error && <ErrorIconComponent className="[grid-area:_icon]" />}
      {title && (
        <ToastLib.Title
          className={cn(
            'mb-[5px] text-[15px] font-medium [grid-area:_title]',
            'text-white dark:text-black'
          )}
        >
          {title}
        </ToastLib.Title>
      )}
      {description && (
        <ToastLib.Description asChild>
          <span
            className={cn(
              'm-0 text-[13px] leading-[1.3] [grid-area:_description]',
              'text-white dark:text-black'
            )}
          >
            {description}
          </span>
        </ToastLib.Description>
      )}
      <div className="[grid-area:_actions]">{children}</div>
      {setOpen && <ToastLib.Close />}
    </ToastLib.Root>
  );
}

export default function useToast() {
  const { createToast } = React.useContext(ToastContext);
  return { createToast };
}
