import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { TemplateFormat } from '@langchain/core/prompts';
import { PlusIcon } from '@langchain/untitled-ui-icons';

import React, {
  ComponentProps,
  Dispatch,
  FC,
  MutableRefObject,
  ReactNode,
  SetStateAction,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Virtuoso, VirtuosoHandle } from 'react-virtuoso';
import { useDebouncedCallback } from 'use-debounce';

import { PlaygroundButton } from '@/Pages/Playground/PlaygroundHome/PlaygroundButtons';
import { useDeepMemo } from '@/hooks/useDeepMemo';
import DragIcon from '@/icons/DragIcon.svg?react';
import {
  MessageCapability,
  MessageFields,
  MessageUnionType,
  S3DataSchema,
} from '@/types/schema';
import { useMessageCapabilities } from '@/utils/MessageCapabilitiesContext';
import { useScrollParent } from '@/utils/VirtuosoCustomScrollParentContext/useScrollParent';
import {
  addToolCallToMessage,
  getMessageContent,
  getMessageFields,
  getMessageType,
  getOpenAIRoleFromLangSmithRole,
  setMessageContent,
  setMessageFields,
  setMessageType,
} from '@/utils/messages';
import { cn } from '@/utils/tailwind';

import { EditableMessage } from './EditableMessage';
import { EMessageType } from './EditableMessage/types';
import { RichTextEditorVariableMappingProps } from './EvaluatorCrudPane/types';
import { Message } from './Message';
import PromptTemplateFormatDropdown from './PromptTemplateFormatDropdown';
import { RichTextImageEditorVariant } from './RichTextEditor/MultimodalDropdown/types';
import { SuggestionsConfig } from './RichTextEditor/suggestions/Suggestions';

// capabilities are set on a prompt level, but these are the possible capabilities for each message type
const MESSAGE_TYPE_CAPABILITIES = {
  human: new Set([MessageCapability.IMAGE, MessageCapability.CANVAS]),
  system: new Set([MessageCapability.IMAGE, MessageCapability.CANVAS]),
  ai: new Set([MessageCapability.IMAGE, MessageCapability.CANVAS]),
  tool: new Set(),
  function: new Set(),
  placeholder: new Set(),
};
const DND_MODIFIERS = [restrictToVerticalAxis];

function blankMessage(
  type: string,
  field?: Partial<MessageFields>,
  format?: 'oai' | 'stored',
  prevMessage?: MessageUnionType,
  preserveMessageFormat?: boolean
): MessageUnionType {
  if (!preserveMessageFormat || !prevMessage) {
    if (format === 'stored') {
      if (type === 'function') {
        return { type, data: { content: '{}', ...field } };
      }

      if (type === 'tool') {
        return { type, data: { content: '{}', ...field } };
      }

      return { type, data: { content: '' } };
    } else {
      if (type === 'function') {
        return {
          role: 'assistant',
          content: null,
          function_call: {
            name: '',
            arguments: '{"arg":"value"}',
          },
        };
      }

      if (type === 'tool') {
        return {
          role: 'assistant',
          content: '',
          tool_calls: [
            {
              id: '',
              function: {
                arguments: '{"arg":"value"}',
                name: '',
              },
              type: 'function',
            },
          ],
        };
      }

      return {
        role: getOpenAIRoleFromLangSmithRole(type),
        content: '',
      };
    }
  } else {
    if (type === 'function') {
      return {
        role: 'assistant',
        content: null,
        function_call: {
          name: '',
          arguments: '{"arg":"value"}',
        },
      };
    }

    if (type === 'tool') {
      return {
        role: 'assistant',
        content: '',
        tool_calls: [
          {
            id: '',
            function: {
              arguments: '{"arg":"value"}',
              name: '',
            },
            type: 'function',
          },
        ],
      };
    }
    let newMessage: MessageUnionType = JSON.parse(
      JSON.stringify({ ...prevMessage })
    );
    newMessage =
      (setMessageType(newMessage, type) as MessageUnionType) ?? newMessage;
    newMessage = setMessageContent(newMessage, '') ?? newMessage;
    return newMessage;
  }
}

const SortableEditableMessage = memo(
  (
    props: Omit<ComponentProps<typeof EditableMessage>, 'onChange'> & {
      sortId: number;
      setSortIds: Dispatch<SetStateAction<number[]>>;
      sortDisabled: boolean | undefined;
      templateFormat: TemplateFormat | undefined;
      testId?: string;
      refreshContentRef?: MutableRefObject<(content: string) => void>;
      preserveMessageFormat?: boolean;
      index: number;
      setMessages: (
        messagesOrUpdater:
          | MessageUnionType[]
          | ((prev: MessageUnionType[]) => MessageUnionType[]),
        tf?: TemplateFormat
      ) => void;
      onDeleteSortId?: (sortId: number) => void;
    }
  ) => {
    const {
      setMessages,
      templateFormat,
      index,
      messageValue,
      setSortIds,
      sortId,
      onDeleteSortId,
    } = props;
    const sortable = useSortable({ id: sortId });

    const style = {
      transform: CSS.Translate.toString(sortable.transform),
      transition: sortable.transition,
    };

    const messageContent = getMessageContent(messageValue);
    const messageFields = getMessageFields(messageValue);
    const messageType = getMessageType(messageValue);

    const handleMessageChange = useCallback(
      (updatedMessage: MessageUnionType) => {
        setMessages((oldMessages) => {
          const newMessages = [...oldMessages];
          newMessages[index] = updatedMessage;
          return newMessages;
        }, templateFormat);
      },
      [templateFormat, setMessages, index]
    );

    const handleDeleteMessage = useCallback(() => {
      setMessages((oldMessages) => {
        const newMessages = [...oldMessages];
        newMessages.splice(index, 1);
        return newMessages;
      }, templateFormat);
      // remove the sortId of the deleted message
      setSortIds((prev) => {
        return prev.filter((id) => id !== sortId);
      });
      onDeleteSortId?.(sortId);
    }, [
      setMessages,
      templateFormat,
      index,
      sortId,
      setSortIds,
      onDeleteSortId,
    ]);

    const handleEdit = (setter) => (newValue) => {
      const newMessage = setter(messageValue, newValue);
      handleMessageChange(newMessage ?? messageValue);
    };

    const sortButton = useCallback(
      (className?: string) => {
        return !props.sortDisabled ? (
          <button
            type="button"
            className={cn(
              'flex cursor-grab items-center justify-center rounded-md border border-secondary p-1',
              sortable.isDragging && 'cursor-grabbing bg-secondary-hover',
              className
            )}
            {...sortable.listeners}
          >
            <DragIcon className="h-4 w-4" />
          </button>
        ) : null;
      },
      [sortable.isDragging, props.sortDisabled]
    );

    return (
      <div
        data-testid={props.testId}
        ref={sortable.setNodeRef}
        style={style}
        className={cn(sortable.isDragging && 'z-10 cursor-grabbing')}
        {...sortable.attributes}
      >
        {props.preserveMessageFormat ? (
          <Message
            attachments={props.attachments}
            content={messageContent ?? ''}
            fields={messageFields}
            role={messageType}
            onEditContent={handleEdit(setMessageContent)}
            onEditFields={handleEdit(setMessageFields)}
            onChangeRole={handleEdit(setMessageType)}
            actions={sortButton()}
            onDelete={handleDeleteMessage}
            onAddToolCall={() => {
              const withToolCall = addToolCallToMessage(messageValue, '', '', {
                param: 'value',
              });
              if (withToolCall) {
                handleMessageChange(withToolCall);
              }
            }}
          />
        ) : (
          <EditableMessage
            {...props}
            className={cn(
              props.className,
              // disable transition if collapsing is supported
              !props.toggleCollapsed && 'transition-all',
              sortable.isDragging &&
                'border-brand shadow-lg hover:border-brand-strong'
            )}
            onChange={handleMessageChange}
            onDelete={handleDeleteMessage}
            messageValue={messageValue}
            // to not break existing behavior, we only put the sort button on the outer actions if collapsing is supported
            {...(props.toggleCollapsed
              ? {
                  outerActions: sortButton('border-none p-0 pt-4'),
                  actions: undefined,
                }
              : {
                  actions: sortButton(),
                })}
          />
        )}
      </div>
    );
  }
);

const SortableEditableMessageWrapper = ({
  message: messageProp,
  index,
  capabilities,
  refreshContentRef,
  sortId,
  setSortIds,
  templateFormat,
  variant,
  readOnly,
  hideSyntaxHighlighting,
  attachments,
  useSuggestions,
  suggestions,
  setMessages,
  preserveMessageFormat,
  isCollapsed,
  toggleCollapsed,
  disabledMessageTypes,
  variableMappingProps,
}: {
  message: MessageUnionType;
  index: number;
  capabilities: MessageCapability[] | undefined;
  refreshContentRef: MutableRefObject<(content: string) => void>;
  sortId: number;
  setSortIds: Dispatch<SetStateAction<number[]>>;
  templateFormat: TemplateFormat | undefined;
  testId?: string;
  variant: RichTextImageEditorVariant;
  readOnly: boolean;
  hideSyntaxHighlighting: boolean;
  attachments: Record<string, S3DataSchema> | undefined;
  useSuggestions: boolean;
  suggestions: SuggestionsConfig | undefined;
  setMessages: (
    messagesOrUpdater:
      | MessageUnionType[]
      | ((prev: MessageUnionType[]) => MessageUnionType[]),
    tf?: TemplateFormat
  ) => void;
  preserveMessageFormat: boolean;
  isCollapsed: boolean;
  toggleCollapsed?: () => void;
  disabledMessageTypes?: EMessageType[];
  variableMappingProps?: RichTextEditorVariableMappingProps;
  onDeleteSortId?: (sortId: number) => void;
}) => {
  // this is so we have a stable reference of the message
  const message = useDeepMemo(messageProp);

  const messageType = getMessageType(message);

  const filteredCapabilities = useMemo(() => {
    if (!capabilities || !messageType) return [];
    const messageTypeCapabilities = MESSAGE_TYPE_CAPABILITIES[messageType];
    return capabilities?.filter((c) => {
      return messageTypeCapabilities?.has(c);
    });
  }, [capabilities, messageType]);

  return (
    <div key={sortId} className="py-1">
      <SortableEditableMessage
        testId={`editable-message-${index}`}
        index={index}
        key={sortId}
        sortId={sortId}
        setSortIds={setSortIds}
        sortDisabled={readOnly}
        variant={variant}
        messageValue={message}
        setMessages={setMessages}
        readOnly={readOnly}
        hideSyntaxHighlighting={hideSyntaxHighlighting}
        attachments={attachments}
        capabilities={filteredCapabilities}
        templateFormat={templateFormat}
        useSuggestions={useSuggestions}
        suggestions={suggestions}
        refreshContentRef={refreshContentRef}
        preserveMessageFormat={preserveMessageFormat}
        isCollapsed={isCollapsed}
        toggleCollapsed={toggleCollapsed}
        disabledMessageTypes={disabledMessageTypes}
        variableMappingProps={variableMappingProps}
      />
    </div>
  );
};

const EditableChatInputDndContextWrapper: FC<{
  children: ReactNode;
  sortIds: number[];
  setSortIds: Dispatch<SetStateAction<number[]>>;
  setMessages: (
    messagesOrUpdater:
      | MessageUnionType[]
      | ((prev: MessageUnionType[]) => MessageUnionType[]),
    tf?: TemplateFormat
  ) => void;
  templateFormat: TemplateFormat | undefined;
  readOnly?: boolean;
}> = ({
  children,
  sortIds,
  setSortIds,
  setMessages,
  templateFormat,
  readOnly,
}) => {
  const onDragEnd = useCallback(
    ({ active, over }) => {
      if (over != null && active.id !== over.id) {
        const activeIndex = sortIds.indexOf(active.id as number);
        const overIndex = sortIds.indexOf(over.id as number);

        const nextSortIds = arrayMove(sortIds, activeIndex, overIndex);
        setMessages((oldMessages) => {
          const nextMessages = arrayMove(oldMessages, activeIndex, overIndex);
          return nextMessages;
        }, templateFormat);
        setSortIds(nextSortIds);
      }
    },
    [sortIds, setMessages, templateFormat, setSortIds]
  );

  const pointerSensor = useSensor(PointerSensor);
  const keyboardSensor = useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  });

  const _sensors = useSensors(pointerSensor, keyboardSensor);
  const sensors = useDeepMemo(_sensors);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      modifiers={DND_MODIFIERS}
      onDragEnd={onDragEnd}
    >
      <SortableContext
        items={sortIds}
        strategy={verticalListSortingStrategy}
        disabled={readOnly}
      >
        {children}
      </SortableContext>
    </DndContext>
  );
};

export function EditableChatInput({
  variant = 'inline',
  readOnly,
  hideSyntaxHighlighting,
  templateFormat,
  actions,

  attachments,
  capabilities: capabilitiesProp,
  createOnEmpty,
  useSuggestions,
  suggestions,
  refreshContentRef,
  virtualizeItems,
  hideTemplateFormatSelector,
  messageFormat = 'stored',
  preserveMessageFormat,
  debounceUpdate,
  isAllCollapsed: _isAllCollapsed,
  setIsAllCollapsed,
  disableCollapse,
  setMessages: setMessagesProps,
  disabledMessageTypes,
  variableMappingProps,
  ...props
}: {
  variant?: RichTextImageEditorVariant;
  messages: MessageUnionType[];
  setMessages: (
    messages: MessageUnionType[],
    templateFormat?: TemplateFormat
  ) => void;
  readOnly?: boolean;
  hideSyntaxHighlighting?: boolean;
  templateFormat?: TemplateFormat;
  actions?: ReactNode;
  isHubPreview?: boolean;
  attachments?: Record<string, S3DataSchema>;
  capabilities?: MessageCapability[];
  createOnEmpty?: boolean;
  useSuggestions?: boolean;
  suggestions?: SuggestionsConfig;
  refreshContentRef?: MutableRefObject<
    ((index: number, content: string) => void) | null
  >;
  virtualizeItems?: boolean;
  hideTemplateFormatSelector?: boolean;
  preserveMessageFormat?: boolean;
  messageFormat?: 'oai' | 'stored';
  debounceUpdate?: boolean;
  disableCollapse?: boolean;
  // isAllCollapsed defined the desired state by the parent. actual collapse state is managed by this component
  isAllCollapsed?: boolean;
  setIsAllCollapsed?: (isAllCollapsed: boolean) => void;
  disabledMessageTypes?: EMessageType[];
  className?: string;
  variableMappingProps?: RichTextEditorVariableMappingProps;
}) {
  const [messagesState, setMessagesState] = useState(props.messages);
  const messages = debounceUpdate ? messagesState : props.messages;
  const setMessagesDebounced = useDebouncedCallback(setMessagesProps, 300);

  const disabledContext = useMessageCapabilities();

  const capabilities = useMemo(() => {
    if (disabledContext) {
      return capabilitiesProp?.filter(
        (c) => !disabledContext?.isCapabilityDisabled(c)
      );
    }
    return capabilitiesProp;
  }, [capabilitiesProp, disabledContext]);
  const setMessages = useCallback(
    (
      messagesOrUpdater:
        | MessageUnionType[]
        | ((prev: MessageUnionType[]) => MessageUnionType[]),
      tf?: TemplateFormat
    ) => {
      setMessagesState((prev) => {
        const newMessages =
          typeof messagesOrUpdater === 'function'
            ? messagesOrUpdater(prev)
            : messagesOrUpdater;

        if (debounceUpdate) {
          setMessagesDebounced(newMessages, tf);
        } else {
          setMessagesProps(newMessages, tf);
        }

        return newMessages;
      });
    },
    [debounceUpdate, setMessagesProps, setMessagesDebounced]
  );

  const virtuosoRef = useRef<VirtuosoHandle>(null);

  // assumptions:
  // - the logical content is not being changed externally even with a different reference
  // - instead, the content is only changed by invoking `setMessages` inside this component
  const sortIdState = useRef(1);
  const getNewId = () => {
    const id = sortIdState.current;
    sortIdState.current += 1;
    return id;
  };
  const initialSortIds = useMemo(
    () => messages.map(() => getNewId()),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const [rawSortIds, setRawSortIds] = useState(initialSortIds);

  // if in hub, the assumption is incorrect and we need to reset the sortable messages
  // TODO: replace EditableChatInput with a simplified view (similar to comparison view) in hub
  const sortIds = readOnly ? initialSortIds : rawSortIds;

  const newMessage = () => {
    const lastMessage = messages[messages.length - 1];
    const lastMessageType = getMessageType(lastMessage);

    const newMessages: MessageUnionType[] = [];
    const lastMessageKwargs = getMessageFields(lastMessage)?.additional_kwargs;

    if (lastMessageType === 'ai') {
      // ai (function_call) -> function
      // ai (tool_calls) -> tool
      if (lastMessageKwargs != null && 'function_call' in lastMessageKwargs) {
        newMessages.push(
          blankMessage(
            'function',
            {
              name: lastMessageKwargs.function_call?.name ?? '',
            },
            messageFormat,
            lastMessage,
            preserveMessageFormat
          )
        );
      } else if (
        lastMessageKwargs != null &&
        'tool_calls' in lastMessageKwargs
      ) {
        const toolCalls = Array.isArray(lastMessageKwargs.tool_calls)
          ? lastMessageKwargs.tool_calls
          : [];

        for (let i = 0; i < Math.max(1, toolCalls.length); i++) {
          newMessages.push(
            blankMessage(
              'tool',
              {
                tool_call_id: toolCalls[i]?.id ?? '',
                name: toolCalls[i]?.function?.name ?? '',
              },
              messageFormat,
              lastMessage,
              preserveMessageFormat
            )
          );
        }
      } else {
        newMessages.push(
          blankMessage(
            'human',
            undefined,
            messageFormat,
            lastMessage,
            preserveMessageFormat
          )
        );
      }
    } else {
      // human -> ai
      // ai -> human
      // system -> human
      newMessages.push(
        blankMessage(
          lastMessageType === 'human' ? 'ai' : 'human',
          undefined,
          messageFormat,
          lastMessage,
          preserveMessageFormat
        )
      );
    }

    setMessages([...messages, ...newMessages], templateFormat);
    setRawSortIds((sortIds) => [
      ...sortIds,
      ...newMessages.map(() => {
        return getNewId();
      }),
    ]);

    if (!scrollParentRef?.current && virtualizeItems) {
      // Only scroll when there's a scroll parent. When there isn't a scroll parent, the add message button
      // is locked to the bottom of the panel. So we need to scroll to the bottom of the panel.
      virtuosoRef.current?.scrollToIndex({
        index: [...messages, ...newMessages].length,
        behavior: 'auto',
      });
    } else {
      scrollRef?.current?.scrollTo({
        top: scrollRef?.current?.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // create message on mount if the list is empty
  // used in inputs section in prompt playground
  useEffect(() => {
    if (createOnEmpty && messages.length === 0) {
      newMessage();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createOnEmpty]);

  // Create an array of refs for each sortId
  const messageRefs = useRef<MutableRefObject<(content: string) => void>[]>([]);

  useEffect(() => {
    // Update refs array to match the length of sortIds
    messageRefs.current = sortIds.map(
      (_, index) => messageRefs.current[index] || React.createRef<() => void>()
    );
  }, [sortIds]);

  // Function to call all refs
  const callSpecificRef = (index: number, content: string) => {
    if (messageRefs.current[index]) {
      messageRefs.current[index].current?.(content);
    }
  };
  const scrollParentRef = useScrollParent();
  // Update parent ref to call all refs
  useEffect(() => {
    if (refreshContentRef) {
      refreshContentRef.current = callSpecificRef;
    }
  }, [refreshContentRef]);

  const scrollRef = useRef<HTMLDivElement>(null);

  const [collapsedMessageIds, setCollapsedMessageIds] = useState<Set<number>>(
    new Set()
  );

  const toggleCollapse = useCallback(
    (id: number) => {
      setCollapsedMessageIds((prev) => {
        const newSet = new Set(prev);
        newSet.has(id) ? newSet.delete(id) : newSet.add(id);
        if (newSet.size === sortIds.length) {
          setIsAllCollapsed?.(true);
        } else if (newSet.size === 0) {
          setIsAllCollapsed?.(false);
        }
        return newSet;
      });
    },
    [sortIds, setIsAllCollapsed]
  );

  const isCollapsed = useCallback(
    (id: number) => {
      return collapsedMessageIds.has(id);
    },
    [collapsedMessageIds]
  );

  // if parent state changes, update the collapse state
  useEffect(() => {
    if (_isAllCollapsed) {
      setCollapsedMessageIds(new Set(sortIds));
    } else {
      setCollapsedMessageIds(new Set());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [_isAllCollapsed]);

  const onDeleteSortId = useCallback(
    (deletedSortId: number) => {
      setCollapsedMessageIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(deletedSortId);
        return newSet;
      });
    },
    [setCollapsedMessageIds]
  );

  const renderChatItem = (sortId: number, index: number) => {
    const message = messages[index];
    return (
      <SortableEditableMessageWrapper
        key={sortId}
        message={message}
        index={index}
        sortId={sortId}
        setSortIds={setRawSortIds}
        setMessages={setMessages}
        templateFormat={templateFormat}
        testId={`editable-message-${index}`}
        variant={variant}
        readOnly={!!readOnly}
        hideSyntaxHighlighting={!!hideSyntaxHighlighting}
        attachments={attachments}
        capabilities={capabilities}
        useSuggestions={!!useSuggestions}
        suggestions={suggestions}
        preserveMessageFormat={!!preserveMessageFormat}
        refreshContentRef={messageRefs.current[index]}
        isCollapsed={isCollapsed(sortId)}
        toggleCollapsed={
          disableCollapse ? undefined : () => toggleCollapse(sortId)
        }
        disabledMessageTypes={disabledMessageTypes}
        variableMappingProps={variableMappingProps}
        onDeleteSortId={onDeleteSortId}
      />
    );
  };

  return (
    <EditableChatInputDndContextWrapper
      sortIds={sortIds}
      setSortIds={setRawSortIds}
      setMessages={setMessages}
      templateFormat={templateFormat}
      readOnly={readOnly}
    >
      <div
        className={cn(
          'flex flex-col items-stretch gap-2',
          !scrollParentRef?.current && virtualizeItems && 'flex-1',
          virtualizeItems && 'gap-0',
          !virtualizeItems && 'h-full overflow-y-hidden',
          props.className
        )}
      >
        {/* {chevronToggle} */}
        {virtualizeItems ? (
          <div className="empty:hidden">
            <Virtuoso
              ref={virtuosoRef}
              customScrollParent={scrollParentRef?.current ?? undefined}
              totalCount={sortIds.length}
              itemContent={(index) => {
                const sortId = sortIds[index];
                return renderChatItem(sortId, index);
              }}
            />
          </div>
        ) : (
          <div className="overflow-y-auto" ref={scrollRef}>
            {sortIds.map(renderChatItem)}
          </div>
        )}

        {!readOnly && (
          <div
            className={cn(
              'flex items-center gap-2',
              !disableCollapse && 'pl-5'
            )}
          >
            {!readOnly && (
              <PlaygroundButton
                text="Message"
                onClick={newMessage}
                startDecorator={<PlusIcon className="my-auto h-4 w-4" />}
                tooltipTitle="Add another message to the prompt"
              />
            )}
            {actions}
            {templateFormat && !hideTemplateFormatSelector && (
              <PromptTemplateFormatDropdown
                templateFormat={templateFormat}
                onTemplateFormatSwitch={(tf: TemplateFormat) => {
                  setMessages(messages, tf);
                }}
                disabled={readOnly}
              />
            )}
          </div>
        )}
      </div>
    </EditableChatInputDndContextWrapper>
  );
}
