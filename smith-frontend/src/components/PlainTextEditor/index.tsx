import { useLayoutEffect, useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';

import { cn } from '@/utils/tailwind';

import { MAX_CODE_LENGTH } from '../Code/constants';
import { TextWithLinks } from '../TextWithLinks';
import { useResizeTextEditor } from './hooks/useResizeTextEditor';

const COMMON_CLS = cn(
  'text-md col-[1] row-[1] m-0 w-full overflow-y-hidden whitespace-pre-wrap break-words border-none bg-transparent p-0 text-base'
);

export function PlainTextEditor(props: {
  id?: string;
  textRef?: React.RefObject<HTMLTextAreaElement>;
  value?: string | null | undefined;
  truncate?: boolean;
  placeholder?: string;
  className?: string;
  onChange?: (e: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  autoFocus?: boolean;
  readOnly?: boolean;
  cursorPointer?: boolean;
  textareaClassName?: string;
  resize?: boolean;
  defaultRows?: number;
  fullLength?: boolean;
  maxHeight?: number | string;
}) {
  // Internal state for immediate UI updates
  const [internalValue, setInternalValue] = useState(props.value ?? '');
  const { displayDivRef, textAreaRef, maxHeight } = useResizeTextEditor({
    maxHeight: props.maxHeight,
    internalValue,
    textRef: props.textRef,
  });

  useLayoutEffect(() => {
    setInternalValue(props.value ?? '');
  }, [props.value]);

  // Debounced callback for parent updates
  const debouncedOnChange = useDebouncedCallback((value: string) => {
    props.onChange?.(value);
  }, 100);

  let valueToDisplay = internalValue;
  let isTruncated = false;

  // https://react.dev/errors/31?invariant=31&args%5B%5D=object%2520with%2520keys%2520%257Bcontent%252C%2520additional_kwargs%252C%2520response_metadata%252C%2520type%252C%2520name%252C%2520id%252C%2520example%252C%2520tool_calls%252C%2520invalid_tool_calls%252C%2520usage_metadata%257D
  if (typeof valueToDisplay !== 'string' && valueToDisplay != null) {
    valueToDisplay = JSON.stringify(valueToDisplay);
  }

  if (props.truncate && (valueToDisplay?.length ?? 0) > MAX_CODE_LENGTH) {
    valueToDisplay = valueToDisplay?.slice(0, MAX_CODE_LENGTH);
    isTruncated = true;
  }

  return (
    <div className={cn('grid w-full', props.className)}>
      <textarea
        ref={textAreaRef}
        id={props.id}
        className={cn(
          COMMON_CLS,
          'text-transparent caret-black placeholder:text-quaternary dark:caret-slate-200',
          props.textareaClassName,
          !props.resize && 'resize-none'
        )}
        style={{
          maxHeight,
        }}
        value={valueToDisplay ?? ''}
        rows={props.defaultRows ?? 1}
        onChange={(e) => {
          const target = e.target as HTMLTextAreaElement;
          setInternalValue(target.value);
          debouncedOnChange(target.value);
        }}
        onFocus={props.onFocus}
        onBlur={props.onBlur}
        placeholder={props.placeholder}
        readOnly={props.readOnly}
        autoFocus={props.autoFocus && !props.readOnly}
        onKeyDown={props.onKeyDown}
      />
      {/* NOTE: this div displays the text in this editor, not the textarea */}
      <div
        ref={displayDivRef}
        aria-hidden
        className={cn(
          COMMON_CLS,
          'pointer-events-none select-none',
          props.textareaClassName
        )}
        style={{
          maxHeight,
        }}
      >
        <TextWithLinks
          text={valueToDisplay ?? ''}
          fullLength={props.fullLength}
        />{' '}
        {isTruncated && (
          <span className="text-gray-500">
            ... (text truncated due to length)
          </span>
        )}
      </div>
    </div>
  );
}
