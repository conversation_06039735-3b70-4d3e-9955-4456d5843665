import dayjs from 'dayjs';
import { useEffect } from 'react';

import { DegradedPerformanceBanner } from '@/components/DegradedPerformanceBanner';
import { InstanceHealthBanner } from '@/components/InstanceHealthBanner';
import { LicenseExpiredBanner } from '@/components/LicenseExpiredBanner';
import { TenantUsageLimitsBanner } from '@/components/TenantUsageLimitsBanner';
import { LICENSE_EXPIRED_BANNER_KEY } from '@/constants/expiredBannerConstants';
import { useInfo } from '@/hooks/useInstanceFlag';
import { useTenantUsageLimits } from '@/hooks/useSwr';
import { degradedPerformanceEnabled } from '@/utils/constants';
import { isSelfHosted } from '@/utils/is-self-hosted';
import { useLocalStorageState } from '@/utils/use-local-storage-state';
import { localTime } from '@/utils/utc-time';

export function PlatformInfoBanners() {
  const tenantUsageLimits = useTenantUsageLimits();

  const { data: instanceInfo } = useInfo();

  const [storedExpiredDateStr, setLicenseExpireTime] = useLocalStorageState<
    string | null
  >(LICENSE_EXPIRED_BANNER_KEY, null);

  useEffect(() => {
    setLicenseExpireTime(instanceInfo?.license_expiration_time ?? null);
  }, [instanceInfo?.license_expiration_time, setLicenseExpireTime]);

  const expireDateStr =
    instanceInfo?.license_expiration_time || storedExpiredDateStr;
  const expireDate = expireDateStr ? localTime(expireDateStr) : undefined;

  if (degradedPerformanceEnabled) {
    return <DegradedPerformanceBanner />;
  }

  if (expireDate && isSelfHosted) {
    const weekBeforeExpire = expireDate.subtract(7, 'day');
    if (weekBeforeExpire.unix() < dayjs().unix()) {
      return <LicenseExpiredBanner expireDate={expireDate} />;
    }
  }
  if (isSelfHosted) {
    return <InstanceHealthBanner />;
  }
  if (tenantUsageLimits?.data?.in_reject_set) {
    return (
      <TenantUsageLimitsBanner usageLimitsResponse={tenantUsageLimits.data} />
    );
  }

  return null;
}
