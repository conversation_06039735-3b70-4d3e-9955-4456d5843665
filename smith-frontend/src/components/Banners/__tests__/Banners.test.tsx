import { screen, waitFor } from '@testing-library/react';

import dayjs from 'dayjs';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { lsRender } from '@/__tests__/utils';
import { useCurrentTier } from '@/hooks/useCurrentTier';
import { useInfo } from '@/hooks/useInstanceFlag';
import { useInstanceHealth, useTenantUsageLimits } from '@/hooks/useSwr';

import { PlatformInfoBanners } from '../PlatformInfoBanners';

// Mock the hooks
let isSelfHostedValue = false;
vi.mock('@/utils/is-self-hosted', () => ({
  get isSelfHosted() {
    return isSelfHostedValue;
  },
}));

vi.mock('@/hooks/useInstanceFlag', () => ({
  useInfo: vi.fn(),
}));

vi.mock('@/hooks/useCurrentTier', () => ({
  useCurrentTier: vi.fn(),
}));

vi.mock('@/hooks/useSwr', async () => {
  const actual = await vi.importActual('@/hooks/useSwr');
  return {
    ...actual,
    useTenantUsageLimits: vi.fn(),
    useInstanceHealth: vi.fn(),
    useCurrentOrganization: vi.fn(),
  };
});

// Mock the constants module with a getter that can be updated during tests
let degradedPerformanceEnabled = false;
vi.mock('@/utils/constants', async () => {
  const actual = await vi.importActual('@/utils/constants');
  return {
    ...actual,
    get degradedPerformanceEnabled() {
      return degradedPerformanceEnabled;
    },
  };
});

describe('Banners Component', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  beforeEach(() => {
    // Default mock implementations
    isSelfHostedValue = false;
    (useInfo as any).mockReturnValue({
      data: { license_expiration_time: null },
    });
    (useTenantUsageLimits as any).mockReturnValue({
      data: { in_reject_set: false },
    });
    (useInstanceHealth as any).mockReturnValue({
      data: { clickhouse_disk_free_pct: 100 },
    });
    (useCurrentTier as any).mockReturnValue({
      isLegacy: false,
      isFree: false,
    });

    // Reset the mock constant
    degradedPerformanceEnabled = false;
  });

  it('should render nothing when no banners are needed', async () => {
    lsRender(<PlatformInfoBanners />);
    await waitFor(() => {
      expect(screen.queryByText(/degraded/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/license/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/clickhouse/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/usage limits/i)).not.toBeInTheDocument();
    });
  });

  it('should render DegradedPerformanceBanner when degradedPerformanceEnabled is true', async () => {
    // Override the degradedPerformanceEnabled constant for this test
    degradedPerformanceEnabled = true;
    lsRender(<PlatformInfoBanners />);
    await waitFor(() => {
      expect(
        screen.getByText(/degraded platform experience/i)
      ).toBeInTheDocument();
    });
  });

  it('should render InstanceHealthBanner when self-hosted and disk space is low', async () => {
    // Mock self-hosted
    isSelfHostedValue = true;

    // Mock instance health with low disk space
    (useInstanceHealth as any).mockReturnValue({
      data: {
        clickhouse_disk_free_pct: 5,
      },
    });

    lsRender(<PlatformInfoBanners />);
    await waitFor(() => {
      expect(
        screen.getByText(/clickhouse disk has less than 10% remaining/i)
      ).toBeInTheDocument();
    });
  });

  it('should render TenantUsageLimitsBanner when tenant usage limits are exceeded', async () => {
    // Mock tenant usage limits exceeded
    (useTenantUsageLimits as any).mockReturnValue({
      data: {
        in_reject_set: true,
        usage_limit_type: 'user_defined_monthly_traces',
        tenant_limit: 1000,
      },
    });

    lsRender(<PlatformInfoBanners />);
    await waitFor(() => {
      expect(
        screen.getByText(
          /you have exceeded your per-month total trace usage limits/i
        )
      ).toBeInTheDocument();
    });
  });

  it('should show and then hide LicenseExpiredBanner when license is renewed', async () => {
    // Mock self-hosted
    isSelfHostedValue = true;

    // Mock license expiration date (expired)
    const expiredDate = dayjs().subtract(10, 'day');

    // Mock instance info with expired license
    (useInfo as any).mockReturnValue({
      data: {
        license_expiration_time: expiredDate.toISOString(),
      },
    });

    const { rerender } = lsRender(<PlatformInfoBanners />);

    // Should show the banner initially
    await waitFor(() => {
      expect(screen.getByText(/license expired/i)).toBeInTheDocument();
    });

    // Update the instance info to show renewed license (null expiration time)
    (useInfo as any).mockReturnValue({
      data: {
        license_expiration_time: null,
      },
    });

    // Rerender to apply the changes
    rerender(<PlatformInfoBanners />);

    // Should no longer show the banner
    await waitFor(() => {
      expect(screen.queryByText(/license expired/i)).not.toBeInTheDocument();
    });
  });
});
