import * as TabPrimitive from '@headlessui/react';

import { Fragment, useCallback } from 'react';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';

import { cn } from '../utils/tailwind';

// Smith pages rely on removing all other query parameters when switching tabs (replace),
// whereas Hub pages include `organizationId` query parameter to identify selected tenant (merge)
export function useSetTab({
  method = 'replace',
  prefix = '',
  preserve = [],
}: {
  method?: 'merge' | 'replace' | 'routeParam' | 'none';
  prefix?: string;
  preserve?: string[];
} = {}) {
  const [searchParams, setSearchParams] = useSearchParams();
  return useCallback(
    (tab: number) => {
      if (method === 'none') {
        return;
      }
      const toPreserve = preserve.reduce((acc, key) => {
        const value = searchParams.get(prefix + key);
        if (value) {
          acc[prefix + key] = value;
        }
        return acc;
      }, {} as Record<string, string>);
      setSearchParams(
        method === 'merge'
          ? {
              ...Object.fromEntries(searchParams.entries()),
              [prefix + 'tab']: tab.toString(),
            }
          : { [prefix + 'tab']: tab.toString(), ...toPreserve },
        { replace: true }
      );
    },
    [method, searchParams, setSearchParams, prefix]
  );
}

export function useTab({
  defaultIndex,
  prefix = '',
}: {
  defaultIndex?: number;
  prefix?: string;
} = {}) {
  const [searchParams] = useSearchParams();
  const tabParam = searchParams.get(prefix + 'tab');
  return tabParam && !isNaN(+tabParam) ? +tabParam : defaultIndex ?? 0;
}

export function useSetTabWithRouteParam({
  routeParamName,
  tabIds,
}: {
  routeParamName: string;
  tabIds: string[];
}) {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const paramValue = params[routeParamName];

  return useCallback(
    (tab: number) => {
      if (!paramValue) {
        throw new Error(`Route param ${routeParamName} not found in URL`);
      }
      const tabId = tabIds[tab];
      // Create a new params object with the updated value
      const locationSplit = location.pathname.split('/');
      const paramIndex = locationSplit.lastIndexOf(paramValue);
      if (paramIndex === -1) {
        throw new Error(`Route param ${routeParamName} not found in URL`);
      }
      locationSplit[paramIndex] = tabId;
      navigate(locationSplit.join('/'));
    },
    [tabIds, routeParamName, location.pathname, navigate, paramValue]
  );
}

export function useTabWithRouteParam({
  routeParamName,
  tabIds,
  defaultIndex,
}: {
  routeParamName: string;
  tabIds: string[];
  defaultIndex?: number;
}) {
  const params = useParams();
  const routeParam = params[routeParamName];
  if (!routeParam) {
    return defaultIndex;
  }
  const tabIndex = tabIds.indexOf(routeParam);
  return tabIndex >= 0 ? tabIndex : defaultIndex;
}

type TabGroupPropBase = {
  children: React.ReactNode;
  vertical?: boolean;
  defaultIndex?: number;
  prefix?: string;
  preserve?: string[];
  setTabMethod?: 'merge' | 'replace' | 'routeParam' | 'none';
  onChange?: (index: number) => void;
};
type TabGroupRouteParamProps = TabGroupPropBase & {
  setTabMethod: 'routeParam';
  routeParamName: string;
  tabIds: string[];
};

type TabGroupOtherProps = TabGroupPropBase & {
  setTabMethod?: 'merge' | 'replace' | 'none';
};

type TabGroupProps = TabGroupRouteParamProps | TabGroupOtherProps;

function SearchParamTabGroup({
  children,
  vertical,
  defaultIndex,
  setTabMethod,
  prefix,
  preserve,
  onChange,
}: TabGroupPropBase) {
  const tab = useTab({ defaultIndex, prefix });
  const setTab = useSetTab({ method: setTabMethod, prefix, preserve });

  const handleChange = useCallback(
    (index: number) => {
      setTab(index);
      onChange?.(index);
    },
    [setTab, onChange]
  );

  return (
    <TabPrimitive.TabGroup
      selectedIndex={tab}
      onChange={handleChange}
      vertical={vertical}
      defaultIndex={defaultIndex}
      as={Fragment}
    >
      {children}
    </TabPrimitive.TabGroup>
  );
}

function ParamsTabGroup({
  routeParamName,
  tabIds,
  defaultIndex,
  vertical,
  children,
  onChange,
}: TabGroupRouteParamProps) {
  const tab = useTabWithRouteParam({
    routeParamName,
    tabIds,
    defaultIndex,
  });
  const setTab = useSetTabWithRouteParam({
    routeParamName,
    tabIds,
  });

  const handleChange = useCallback(
    (index: number) => {
      setTab(index);
      onChange?.(index);
    },
    [setTab, onChange]
  );

  return (
    <TabPrimitive.TabGroup
      selectedIndex={tab}
      onChange={handleChange}
      vertical={vertical}
      defaultIndex={defaultIndex}
      as={Fragment}
    >
      {children}
    </TabPrimitive.TabGroup>
  );
}

export function TabGroup(props: TabGroupProps) {
  const { children, vertical, defaultIndex, setTabMethod, onChange } = props;
  if (setTabMethod === 'routeParam') {
    return (
      <ParamsTabGroup
        routeParamName={props.routeParamName}
        tabIds={props.tabIds}
        defaultIndex={defaultIndex}
        vertical={vertical}
        setTabMethod={setTabMethod}
        onChange={onChange}
      >
        {children}
      </ParamsTabGroup>
    );
  }
  return <SearchParamTabGroup {...props}>{children}</SearchParamTabGroup>;
}

export function TabList({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <TabPrimitive.TabList
      className={cn(
        '-mx-4 mb-4 mt-4 flex gap-5 border-b border-secondary px-4 text-ls-black',
        className
      )}
    >
      {children}
    </TabPrimitive.TabList>
  );
}

export function TabLabel({
  children,
  className,
  disabled,
  dataTestId,
  onClick,
  onBlur,
}: {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  dataTestId?: string;
  onClick?: () => void;
  onBlur?: () => void;
}) {
  return (
    <TabPrimitive.Tab
      onClick={onClick}
      onBlur={onBlur}
      className={cn(
        'flex items-center justify-center gap-2 border-b-2 border-b-transparent pb-[12px] font-normal leading-none focus:outline-none ui-selected:border-[var(--gray-900)] ui-selected:font-semibold',
        'disabled:cursor-default disabled:text-secondary',
        className
      )}
      disabled={disabled}
      data-testid={dataTestId}
    >
      {children}
    </TabPrimitive.Tab>
  );
}

export const TabPanels = TabPrimitive.TabPanels;

export function TabPanel({
  children,
  className,
  panelRef,
  onScroll,
}: {
  children: React.ReactNode;
  className?: string;
  panelRef?: React.RefObject<HTMLDivElement>;
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void;
}) {
  return (
    <TabPrimitive.TabPanel
      className={cn('outline-none', className)}
      onScroll={onScroll}
      ref={panelRef}
    >
      {children}
    </TabPrimitive.TabPanel>
  );
}
