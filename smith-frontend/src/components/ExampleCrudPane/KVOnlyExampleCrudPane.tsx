import { Autocomplete, LinearProgress } from '@mui/joy';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Typography from '@mui/joy/Typography';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { ExampleMetadataDisplay } from '@/Pages/Example/components/ExampleMetadataDisplay';
import { getAttachments } from '@/Pages/Run/utils/multimodalRunUtils';
import { appDatasetsPath, appOrganizationPath } from '@/utils/constants';
import { useCodeMirrorTooltipFollowMouseManual } from '@/utils/use-codemirror-tooltip-follow-mouse';

import {
  useDataset,
  useDatasetSplits,
  useOrganizationId,
  useSession,
  useValidateExample,
} from '../../hooks/useSwr';
import { DatasetSchema, ExampleSchema, RunSchema } from '../../types/schema';
import { DatasetPicker } from '../DatasetPicker';
import { Pane } from '../Pane';
import { ScrollSpy } from '../ScrollSpy/ScrollSpy';
import {
  ScrollSpyElement,
  useScrollSpyState,
} from '../ScrollSpy/hooks/useScrollSpyState';
import useToast from '../Toast';
import { AttachmentEditor, TAttachmentWithFile } from './AttachmentEditor';
import { ExampleEditor } from './ExampleEditor';
import { TransformedExampleWarning } from './TransformedExampleWarning';
import { useExampleCrud, useExamplesMultipart } from './hooks';
import {
  getDefaultInputsObject,
  getDefaultOutputsObject,
  getExampleValidateFromRun,
} from './utils';

export interface BaseProps {
  run?: RunSchema;
  example?: ExampleSchema;
  dataset?: DatasetSchema;
  onSuccess?: () => void;
  color?: 'primary' | 'neutral';
  omitIcon?: boolean;
  trigger?: (onClick: () => void) => React.ReactNode;
}

interface PaneProps extends BaseProps {
  isOpen: boolean;
  doClose: () => void;
  asModal?: boolean;
}

function EditDatasetSplits({
  datasetId,
  splits,
  setSplits,
  error,
}: {
  datasetId: string | null;
  splits: string[];
  setSplits: (value: string[]) => void;
  error: string | null;
}) {
  const splitsSwr = useDatasetSplits('latest', datasetId ?? undefined);
  const splitsList = datasetId ? splitsSwr.data : [];
  const [searchSplitInput, setSearchSplitInput] = useState<string>('');
  const splitsListFiltered =
    searchSplitInput !== ''
      ? splitsList?.filter((split) => split.includes(searchSplitInput))
      : splitsList;

  return (
    <div className="col-span-2 flex flex-col gap-3">
      <Autocomplete
        autoFocus={false}
        sx={{
          padding: '3px',
          width: '100%',
          '& .MuiAutocomplete-clearIndicator': { marginRight: '3px' },
        }}
        limitTags={1}
        multiple
        freeSolo={true}
        data-testid="split-picker"
        placeholder={'Choose split…'}
        onChange={(_, values) => {
          setSplits(values);
        }}
        onInputChange={(_, value) => {
          setSearchSplitInput(value);
        }}
        clearOnBlur={false}
        value={Array.isArray(splits) ? splits : []}
        inputValue={searchSplitInput}
        options={splitsListFiltered ?? []}
      />

      {error && (
        <Box marginTop={1}>
          <Typography component="p" color="warning">
            {error}
          </Typography>
        </Box>
      )}
    </div>
  );
}

export const KVOnlyExampleCrudPane = (props: PaneProps) => {
  const title = props.example ? 'Edit Example' : 'Add Example to dataset';

  const [isOpenDebounced] = useDebounce(props.isOpen, 600);

  const { createToast } = useToast();
  const session = useSession(props.run?.session_id ?? null);

  const organizationId = useOrganizationId();

  const runAttachments = getAttachments(props.run?.s3_urls);
  const exampleAttachments =
    props.example?.attachment_urls &&
    Object.entries(props.example.attachment_urls).map(([name, urls]) => {
      return {
        name: name.replace(/^attachment\./, ''),
        url: urls.presigned_url,
        s3_url: urls.storage_url,
      };
    });

  const attachmentsToUse =
    (props.example ? exampleAttachments : runAttachments) ?? [];

  useEffect(() => {
    if (props.isOpen || isOpenDebounced) {
      setAttachments(attachmentsToUse);
    }
  }, [props.isOpen, isOpenDebounced]);

  const [datasetId, setDatasetID] = useState<string | undefined>(
    props.example?.dataset_id ?? props.dataset?.id ?? undefined
  );
  const datasetSwr = useDataset(datasetId); // Fetch dataset to get input/output schemas
  const datasetHasTransformations = datasetSwr.data?.transformations != null;

  const [inputs, setInputsState] = useState<RunSchema['inputs']>(
    getDefaultInputsObject(
      props.run,
      props.example,
      datasetSwr.data?.inputs_schema_definition
    )
  );

  const [outputs, setOutputsState] = useState<RunSchema['outputs']>(
    getDefaultOutputsObject(
      props.run,
      props.example,
      datasetSwr.data?.outputs_schema_definition
    )
  );

  const [splits, setSplits] = useState<string[]>(
    props.example ? props.example.metadata?.dataset_split : ['base']
  );

  const [attachments, setAttachments] =
    useState<TAttachmentWithFile[]>(attachmentsToUse);

  const validatedExample = useValidateExample(
    {
      example:
        !props.example && props.run && datasetId
          ? getExampleValidateFromRun(props.run, datasetId)
          : undefined,
    },
    { keepPreviousData: false }
  );

  const handleResetAttachments = () => {
    setAttachments(attachmentsToUse);
  };

  const {
    submitExamples,
    loading: multipartLoading,
    error: multipartError,
    setError: setMultipartError,
  } = useExamplesMultipart({
    handleClose: props.doClose,
    onSuccess: () => {
      createToast({
        title: 'Example added to dataset',
        description: (
          <Link
            to={`/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${datasetId}`}
            className="underline"
          >
            View the dataset
          </Link>
        ),
      });
      props.onSuccess?.();
    },
    onError: (error) => {
      setError(error);
    },
  });

  const { error, loading, onSubmit, setError } = useExampleCrud({
    example: props.example,
    datasetDataType: 'kv',
    handleClose: props.doClose,
    onSuccess: () => {
      createToast({
        title: props.example
          ? 'Example succesfully edited'
          : 'Example added to dataset',
        description: (
          <Link
            to={`/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${datasetId}`}
            className="underline"
          >
            View the dataset
          </Link>
        ),
      });
      props.onSuccess?.();
    },
  });

  const sourceRunAttachments =
    props.example && props.example.attachment_urls
      ? {
          use_source_run_attachments: Object.entries(
            props.example.attachment_urls
          ).map(([key, _]) => key.replace(/^attachment\./, '')),
        }
      : {};

  const hasValidatedExampleResult = props.example
    ? {
        inputs: props.example.inputs,
        outputs: props.example.outputs,
        ...sourceRunAttachments,
        source_run_id: props.example.source_run_id,
        datasetId: props.example.dataset_id,
      }
    : validatedExample.data || validatedExample.error;

  let inputValidationError: string[] | undefined;
  let outputValidationError: string[] | undefined;
  let validationError: string | undefined;
  try {
    const validationJsonError = JSON.parse(
      validatedExample.error?.message ?? '{}'
    );
    inputValidationError = validationJsonError?.input_errors;
    outputValidationError = validationJsonError?.output_errors;
  } catch (e) {
    validationError = validatedExample.error?.message;
  }

  const usingConvertedExample =
    datasetSwr.data?.transformations && validatedExample.data;

  const isTransformationError =
    datasetSwr.data?.transformations && validatedExample.error;

  const inputsSchema = datasetSwr.data?.inputs_schema_definition;
  const outputsSchema = datasetSwr.data?.outputs_schema_definition;
  const [showRawInputs, setShowRawInputs] = useState(false);
  const [showRawOutputs, setShowRawOutputs] = useState(false);
  const [metadata, setMetadata] = useState<ExampleSchema['metadata']>(
    props.example?.metadata ?? {}
  );

  // Clear inputs and outputs when the validated example or dataset id changes, or the pane is closed. This is to make sure the inputs/outputs are updated correctly
  // based on the dataset schema.
  const doesValidatedExampleExist = validatedExample.data == null;
  useEffect(() => {
    setInputsState(undefined);
    setOutputsState(undefined);
  }, [doesValidatedExampleExist, datasetId, props.isOpen]);

  useEffect(() => {
    if (datasetSwr.data && !inputs) {
      if (validatedExample.error && props.run) {
        setInputsState(props.run.inputs);
        setOutputsState(props.run.outputs);
      } else if (validatedExample.data) {
        setInputsState(validatedExample.data.inputs ?? undefined);
        setOutputsState(validatedExample.data.outputs ?? undefined);
      } else {
        setInputsState(
          getDefaultInputsObject(props.run, props.example, inputsSchema)
        );
        setOutputsState(
          getDefaultOutputsObject(props.run, props.example, outputsSchema)
        );
        setShowRawInputs(
          !(
            inputsSchema &&
            Object.keys(inputsSchema.properties ?? {}).length > 0
          ) &&
            !props.example &&
            !props.run
        );
        setShowRawOutputs(
          props.example
            ? !props.example.outputs
            : props.run
            ? !props.run.outputs
            : !(
                outputsSchema &&
                Object.keys(outputsSchema.properties ?? {}).length > 0
              )
        );
      }
    }
  }, [
    validatedExample.data,
    validatedExample.error,
    props.run,
    datasetSwr.data,
    inputs,
    props.example,
    inputsSchema,
    outputsSchema,
    outputs,
  ]);

  const isNewExample = !props.example && !props.run;

  const handleSubmit = () => {
    if (!datasetId) {
      setError('No dataset selected');
      return;
    }
    // This is creating a new example
    if (isNewExample && !datasetHasTransformations) {
      submitExamples([
        {
          id: crypto.randomUUID(),
          dataset_id: datasetId,
          inputs: JSON.stringify(inputs),
          outputs: JSON.stringify(outputs),
          ...(metadata ? { metadata: JSON.stringify(metadata) } : {}),
          ...(splits ? { split: splits } : {}),
          attachments: Object.fromEntries(
            attachments.filter((a) => a.file).map((a) => [a.name, a.file!])
          ),
        },
      ]);
    } else if (isNewExample) {
      onSubmit({
        inputs: JSON.stringify(inputs),
        outputs: JSON.stringify(outputs),
        dataset_id: datasetId,
        ...(metadata ? { metadata: JSON.stringify(metadata) } : {}),
      });
    } else {
      const isExampleValidatedWithoutError =
        props.example &&
        hasValidatedExampleResult &&
        !(hasValidatedExampleResult instanceof Error);
      const sourceRunId = isExampleValidatedWithoutError
        ? hasValidatedExampleResult.source_run_id ?? undefined
        : props.run?.id;

      // Prepare the attachment URLs or use source run attachments based on the example
      const getAttachmentsOperations = () => {
        if (props.example) {
          const retain: string[] = [];
          const rename: Record<string, string> = {};
          attachments.forEach((a) => {
            const originalAttachment = attachmentsToUse.find(
              (orig) => orig.s3_url === a.s3_url
            );
            if (originalAttachment && originalAttachment.name !== a.name) {
              rename[originalAttachment.name] = a.name;
            } else if (originalAttachment) {
              retain.push(a.name);
            }
          });
          return {
            retain: retain,
            rename: rename,
          };
        }
        return undefined;
      };
      const attachmentsOperations = getAttachmentsOperations();

      const useSourceRunAttachments = !props.example
        ? attachments.map((a) => a.name)
        : undefined;

      const newAttachments = attachments.filter((a) =>
        attachmentsToUse.every((orig) => orig.s3_url !== a.s3_url)
      );
      if (newAttachments.length > 0 && props.example) {
        // Need to use go endpoint when updating with new attachments
        submitExamples(
          [
            {
              id: props.example.id,
              dataset_id: datasetId,
              inputs: JSON.stringify(inputs),
              outputs: JSON.stringify(outputs),
              ...(splits ? { split: splits } : {}),
              ...(attachmentsOperations
                ? { attachments_operations: attachmentsOperations }
                : {}),
              ...(metadata ? { metadata: JSON.stringify(metadata) } : {}),
              attachments: Object.fromEntries(
                newAttachments
                  .filter((a) => a.file)
                  .map((a) => [a.name, a.file!])
              ),
            },
          ],
          'PATCH'
        );
      } else {
        // Use python endpoint when updating without new attachments
        // or when creating example from run
        onSubmit({
          inputs: JSON.stringify(inputs),
          outputs: JSON.stringify(outputs),
          dataset_id: datasetId,
          source_run_id: sourceRunId,
          ...(attachmentsOperations
            ? { attachments_operations: attachmentsOperations }
            : {}),
          ...(metadata ? { metadata: JSON.stringify(metadata) } : {}),
          ...(useSourceRunAttachments
            ? { use_source_run_attachments: useSourceRunAttachments }
            : {}),
          ...(splits ? { split: splits } : {}),
        });
      }
    }
  };

  const handleClose = () => {
    setMultipartError(null);
    setError(null);
    setInputsState(
      getDefaultInputsObject(
        props.run,
        props.example,
        datasetSwr.data?.inputs_schema_definition
      )
    );
    setOutputsState(
      getDefaultOutputsObject(
        props.run,
        props.example,
        datasetSwr.data?.outputs_schema_definition
      )
    );
    setAttachments([]);
    props.doClose();
  };

  const showDatasetPicker = !props.example?.dataset_id && !props.dataset?.id;
  const showIO = !!((hasValidatedExampleResult && !!inputs) || isNewExample);
  const exampleInfoRef = useRef<HTMLDivElement>(null);
  const inputElementRef = useRef<HTMLDivElement>(null);
  const outputElementRef = useRef<HTMLDivElement>(null);

  const inputCodeEditorRef = useRef<HTMLDivElement>(null);
  const outputCodeEditorRef = useRef<HTMLDivElement>(null);
  const ioBoundingRef = useRef<HTMLDivElement>(null);

  const attachmentsElementRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const metadataElementRef = useRef<HTMLDivElement>(null);

  const [showInputSchema, setShowInputSchema] = useState(false);
  const [showOutputSchema, setShowOutputSchema] = useState(false);

  useCodeMirrorTooltipFollowMouseManual(
    inputCodeEditorRef,
    outputCodeEditorRef,
    !!(showRawInputs && inputsSchema && isOpenDebounced),
    !!(showRawOutputs && outputsSchema && isOpenDebounced),
    ioBoundingRef
  );

  const scrollSpyElements: ScrollSpyElement[] = [
    {
      id: 'info',
      elementRef: exampleInfoRef,
      title: showDatasetPicker ? 'Example Info' : 'Dataset Splits',
    },
    {
      id: 'inputs',
      elementRef: inputElementRef,
      title: 'Inputs',
    },
    {
      id: 'outputs',
      elementRef: outputElementRef,
      title: 'Outputs',
    },
    {
      id: 'attachments',
      elementRef: attachmentsElementRef,
      title: 'Attachments',
    },
    {
      id: 'metadata',
      elementRef: metadataElementRef,
      title: 'Metadata',
    },
  ];
  const scrollSpyState = useScrollSpyState(
    scrollContainerRef,
    showIO && isOpenDebounced,
    scrollSpyElements
  );

  useEffect(() => {
    if (session.data?.default_dataset_id) {
      setDatasetID(session.data.default_dataset_id);
    }
  }, [session.data?.default_dataset_id]);

  const showLLMEditor =
    inputsSchema &&
    'properties' in inputsSchema &&
    typeof inputsSchema['properties'] === 'object' &&
    'tools' in (inputsSchema['properties'] as Record<string, unknown>);

  const runObject = useMemo(() => {
    return {
      id: props.run?.id ?? props.example?.id ?? 'new-example', // We use run id as a unique key for the component
      run_type: props.run?.run_type ?? showLLMEditor ? 'llm' : 'chain',
      inputs,
      outputs,
    };
  }, [inputs, outputs, props.run, props.example, showLLMEditor]);
  return (
    (isOpenDebounced || props.isOpen) && (
      <Pane
        open={props.isOpen}
        onClose={handleClose}
        title={title}
        scrollRef={scrollContainerRef}
        handleScroll={scrollSpyState.handleScroll}
        className="flex gap-[60px]"
        topBarRightElement={
          <div className="mr-5 flex items-center gap-2">
            <Button
              type="button"
              onClick={handleClose}
              color="neutral"
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading || multipartLoading}
              disabled={
                !datasetId && !(props.example?.dataset_id || props.dataset?.id)
              }
              onClick={handleSubmit}
              color="primary"
            >
              Submit
            </Button>
          </div>
        }
      >
        <div className="relative flex grow flex-col gap-6">
          {showDatasetPicker && (
            <div className="flex flex-col gap-2" ref={exampleInfoRef}>
              <div className="flex flex-col items-start">
                <strong className="font-semibold">Dataset</strong>
              </div>
              <div className="max-w-[800px]">
                <DatasetPicker
                  label={null}
                  filter={{ data_type: ['kv'] }}
                  datasetId={datasetId}
                  onChange={(value) => {
                    setDatasetID(value);
                    setError(null);
                  }}
                  baseDatasetOnRunType={props.run?.run_type}
                />
              </div>
              <div />
            </div>
          )}

          {validatedExample.isLoading && (
            <div>
              <LinearProgress />
            </div>
          )}
          {datasetSwr.data && datasetId && (
            <div className="flex flex-col gap-2">
              <div className="flex flex-col items-start">
                <strong className="font-semibold">Dataset Splits</strong>
              </div>
              <div>
                <EditDatasetSplits
                  datasetId={datasetId}
                  splits={splits ?? []}
                  setSplits={setSplits}
                  error={error || multipartError}
                />
              </div>
              {usingConvertedExample ? (
                <TransformedExampleWarning
                  className="col-span-1 col-start-2"
                  message="Original message has been transformed according to the dataset schema."
                />
              ) : (
                isTransformationError && (
                  <TransformedExampleWarning
                    className="col-span-1 col-start-2"
                    message="Failed to convert example to dataset schema automatically. Please edit the example manually."
                  />
                )
              )}
            </div>
          )}

          {showIO && (
            <>
              <div className="flex flex-col gap-6" ref={ioBoundingRef}>
                <ExampleEditor
                  elementRef={inputElementRef}
                  setShowRaw={setShowRawInputs}
                  showRaw={showRawInputs}
                  schema={inputsSchema}
                  showSchema={showInputSchema}
                  setShowSchema={setShowInputSchema}
                  example={props.example}
                  run={runObject}
                  title="Inputs"
                  setInputs={setInputsState}
                  setOutputs={setOutputsState}
                  isInput
                  codeEditorRef={inputCodeEditorRef}
                  schemaValidationError={
                    inputValidationError?.[0] ?? validationError
                  }
                />

                <ExampleEditor
                  elementRef={outputElementRef}
                  setShowRaw={setShowRawOutputs}
                  showRaw={showRawOutputs}
                  schema={outputsSchema}
                  showSchema={showOutputSchema}
                  setShowSchema={setShowOutputSchema}
                  example={props.example}
                  run={runObject}
                  title="Reference Outputs"
                  setInputs={setInputsState}
                  setOutputs={setOutputsState}
                  isInput={false}
                  codeEditorRef={outputCodeEditorRef}
                  schemaValidationError={
                    outputValidationError?.[0] ?? validationError
                  }
                />
              </div>

              <div ref={attachmentsElementRef}>
                <AttachmentEditor
                  attachments={attachments}
                  setAttachments={setAttachments}
                  onReset={handleResetAttachments}
                  isCreationMode={isNewExample}
                  datasetHasTransformations={datasetHasTransformations}
                  isFromRun={props.run !== undefined}
                />
              </div>

              <div ref={metadataElementRef}>
                {datasetSwr.data?.data_type && (
                  <ExampleMetadataDisplay
                    metadata={metadata}
                    dataType={datasetSwr.data?.data_type}
                    readOnly={false}
                    onMutate={setMetadata}
                  />
                )}
              </div>
            </>
          )}
        </div>
        <ScrollSpy
          {...scrollSpyState}
          elements={scrollSpyElements}
          className="top-[100px] shrink-0 grow-0"
        />
      </Pane>
    )
  );
};
