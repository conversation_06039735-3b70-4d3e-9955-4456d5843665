import { ArrowRightIcon } from '@langchain/untitled-ui-icons';

import { useEffect } from 'react';

import { REVISION_STATUS_TO_DISPLAY_NAME } from '@/Pages/HostProject/constants';
import { useLogsAvailableForRevision } from '@/Pages/HostProject/hooks/useLogsAvailableForRevision';
import { usePrevious } from '@/hooks/usePrevious';
import { useHostRevisions } from '@/hooks/useSwr';
import { HostRevisionStatus } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { getRevisionSimpleStatus } from '../HostRevisionStatus/HostRevisionStatus';

const Badge = ({
  simpleStatus,
  status,
  statusMessage,
}: {
  simpleStatus: 'pending' | 'error';
  statusMessage: string | undefined;
  status: HostRevisionStatus;
}) => {
  return (
    <div className={'flex items-center gap-1 overflow-hidden rounded-md'}>
      <div className="flex items-center gap-2 overflow-hidden">
        <div
          className={cn('flex items-center gap-1 rounded-lg bg-primary px-1', {
            'border border-warning': simpleStatus === 'pending',
            'border border-error': simpleStatus === 'error',
          })}
        >
          <div
            className={cn('rounded-full bg-error-primary p-1', {
              'bg-warning-primary': simpleStatus === 'pending',
              'bg-error-primary': simpleStatus === 'error',
            })}
          >
            <div
              className={cn('h-2 w-2 rounded-full', {
                'bg-warning-strong': simpleStatus === 'pending',
                'bg-error-strong': simpleStatus === 'error',
              })}
            />
          </div>
          <span
            className={cn('whitespace-nowrap text-error', {
              'text-warning': simpleStatus === 'pending',
              'text-error': simpleStatus === 'error',
            })}
          >
            {simpleStatus === 'pending'
              ? 'Revision in progress.'
              : 'Recent revision failed.'}
          </span>
        </div>
        <span className="overflow-hidden truncate text-ellipsis text-xs text-secondary">
          {statusMessage ??
            REVISION_STATUS_TO_DISPLAY_NAME[status ?? 'UNKNOWN']}
        </span>
      </div>
    </div>
  );
};

export const LatestRevisionWarningButton = ({
  hostProjectId,
  onRevisionPeekChange,
  onRevisionSuccess,
}: {
  hostProjectId: string | undefined;
  onRevisionPeekChange: (id: string) => void;
  onRevisionSuccess: () => void;
}) => {
  const { data: latestRevision, mutate: refetchLatestRevision } =
    useHostRevisions(
      hostProjectId ?? null,
      {
        limit: 1,
        offset: 0,
      },
      {
        onSuccess: (data) => {
          const simpleStatus = getRevisionSimpleStatus(
            data?.[0]?.status ?? 'UNKNOWN'
          );
          if (prevStatus === 'pending' && simpleStatus === 'success') {
            onRevisionSuccess();
          }
        },
      }
    );
  const simpleStatus = getRevisionSimpleStatus(
    latestRevision?.[0]?.status ?? 'UNKNOWN'
  );
  const prevStatus = usePrevious(simpleStatus);

  const { logsAvailable } = useLogsAvailableForRevision({
    hostProjectId,
    revisionId: latestRevision?.[0]?.id,
  });

  // this is a workaround to poll for the latest revision status when it is pending
  // for some reason trying to conditionally set the refreshInterval was not working
  useEffect(() => {
    let isCancelled = false;

    const poll = () => {
      if (simpleStatus === 'pending') {
        refetchLatestRevision();
      } else {
        isCancelled = true;
        // the onSuccess above seems flaky so call it here as well
        onRevisionSuccess();
      }

      if (!isCancelled) {
        setTimeout(poll, 3000);
      }
    };

    poll();

    return () => {
      isCancelled = true;
    };
  }, [simpleStatus, refetchLatestRevision, onRevisionSuccess]);

  const statusMessage = latestRevision?.[0]?.status_message;

  if (
    simpleStatus === 'success' ||
    simpleStatus === 'unknown' ||
    !latestRevision?.[0]
  )
    return null;

  return (
    <div
      className={cn(
        'flex w-fit max-w-full items-center gap-2 overflow-hidden rounded-lg bg-secondary p-1 text-sm font-medium text-secondary',
        {
          'border border-warning bg-warning-secondary':
            simpleStatus === 'pending',
          'border border-error bg-error-secondary': simpleStatus === 'error',
        }
      )}
    >
      <div className="overflow-hidden">
        <Badge
          simpleStatus={simpleStatus}
          status={latestRevision[0].status}
          statusMessage={statusMessage}
        />
      </div>
      {logsAvailable && (
        <button
          type="button"
          onClick={() => onRevisionPeekChange(latestRevision?.[0]?.id ?? '')}
          className={cn(
            'flex flex-shrink-0 items-center gap-1 rounded-md px-1 py-0.5',
            {
              'text-warning hover:bg-warning-primary':
                simpleStatus === 'pending',
              'text-error hover:bg-error-primary': simpleStatus === 'error',
            }
          )}
        >
          See logs
          <ArrowRightIcon className="size-4" />
        </button>
      )}
    </div>
  );
};
