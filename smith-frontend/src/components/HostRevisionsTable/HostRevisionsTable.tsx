import { File02Icon, XCircleIcon } from '@langchain/untitled-ui-icons';
import { CircularProgress, Tooltip } from '@mui/joy';
import {
  ColumnDef,
  OnChangeFn,
  PaginationState,
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useEffect, useMemo, useState } from 'react';

import { useLogsAvailableForRevision } from '@/Pages/HostProject/hooks/useLogsAvailableForRevision';
import { HostRevision } from '@/Pages/HostRevision/HostRevision';
import {
  HostRevisionStatusName,
  RevisionStatusIcon,
  getRevisionSimpleStatus,
} from '@/components/HostRevisionStatus/HostRevisionStatus';
import BranchIcon from '@/icons/BranchIcon.svg?react';
import DockerIcon from '@/icons/DockerIcon.svg?react';
import GithubIcon from '@/icons/GithubIcon.svg?react';
import { HostProjectSchema, HostRevisionSchema } from '@/types/schema';
import { formatDuration } from '@/utils/format-duration';
import { cn } from '@/utils/tailwind';

import { DataGrid, DataGridPagination } from '../DataGrid';
import { useDataGridSizingLocalStorage } from '../DataGrid.utils';
import { SplitViewPane } from '../SplitViewPane';
import { TextOverflowTooltip } from '../TextOverflowTooltip';
import { HostInterruptModal } from './HostInterruptModal';

const TERMINAL_REVISION_STATUSES = new Set<string>([
  'CREATE_FAILED',
  'BUILD_FAILED',
  'DEPLOY_FAILED',
  'DEPLOYED',
  'INTERRUPTED',
]);

interface TableProps {
  rows: HostRevisionSchema[] | undefined;
  isLoading: boolean;
  isValidating: boolean;
  paginationModel: PaginationState;
  setPaginationModel: OnChangeFn<PaginationState>;
  onRevisionPeekChange: (id: string | null) => void;
  revisionPeekId: string | null;
  hostProject?: HostProjectSchema;
  emptyState?: React.ReactNode;
  showActiveRevision?: boolean;
  hideHeader?: boolean;
  paginationTotal?: number;
}

const columnHelper = createColumnHelper<HostRevisionSchema>();

function RevisionStatus(props: {
  revision: HostRevisionSchema;
  lastActiveRevisionId: string | undefined | null;
  showActiveRevision?: boolean;
}) {
  const status = getRevisionSimpleStatus(props.revision.status);
  const isCurrentlyDeployed = props.lastActiveRevisionId === props.revision.id;

  let statusMessage = props.revision.status_message ?? (
    <HostRevisionStatusName status={props.revision.status} />
  );

  if (status === 'success') {
    statusMessage = isCurrentlyDeployed
      ? 'Currently deployed'
      : 'Previously deployed';
  } else if (status === 'error') {
    statusMessage = 'Details';
  }

  return (
    <div className="flex items-center gap-3">
      <RevisionStatusIcon
        className="flex-shrink-0"
        revision={props.revision}
        lastActiveRevisionId={props.lastActiveRevisionId}
      />
      <div className="min-w-0">
        <TextOverflowTooltip className="overflow-hidden text-ellipsis font-medium">
          {props.showActiveRevision ? 'Active Revision' : props.revision.id}
        </TextOverflowTooltip>

        <Tooltip
          title={props.revision.status_message?.trim()}
          sx={{ maxWidth: 400, whiteSpace: 'pre-wrap' }}
        >
          <p
            className={cn('overflow-hidden text-ellipsis', {
              ['text-warning']: status === 'pending',
              ['text-success']: status === 'success' && isCurrentlyDeployed,
              ['text-tertiary']:
                status === 'unknown' ||
                (status === 'success' && !isCurrentlyDeployed),
              ['text-error']: status === 'error',
            })}
          >
            {statusMessage}
          </p>
        </Tooltip>
      </div>
    </div>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const COLUMNS_DEF: ColumnDef<HostRevisionSchema, any>[] = [
  columnHelper.accessor('id', {
    id: 'revision',
    header: 'Revision',
    enableSorting: false,
    size: 250,
    cell: ({ cell, table }) => {
      const { showActiveRevision } = (table.options.meta ?? {}) as {
        showActiveRevision?: boolean;
      };
      const { lastActiveRevisionId } = (table.options.meta ?? {}) as {
        lastActiveRevisionId?: string;
      };

      return (
        <RevisionStatus
          revision={cell.row.original}
          lastActiveRevisionId={lastActiveRevisionId}
          showActiveRevision={showActiveRevision}
        />
      );
    },
  }),
  columnHelper.accessor('created_at', {
    header: 'Created',
    size: 200,
    cell: ({ row }) => {
      const date = new Date(row.original.created_at);
      const relativeDate = formatDuration(date);

      let display: string | undefined = '';
      if (row.original.image_path) {
        // deployed from Docker image
        const imageAndTag = row.original.image_path.split('/').pop();
        const image = imageAndTag?.split(':')[0];
        const tag = imageAndTag?.split(':')[1];
        if (tag) {
          display = `${image} (${tag})`;
        } else {
          display = image;
        }
      } else {
        // deployed from GitHub
        // format branch name and/or commit sha
        display = row.original.repo_commit;
        if (row.original.metadata.repo_commit_sha) {
          if (
            row.original.repo_commit === row.original.metadata.repo_commit_sha
          ) {
            // display commit sha
            display = row.original.repo_commit?.substring(0, 7);
          } else {
            // display branch name and commit sha
            display = `${
              row.original.repo_commit
            } (${row.original.metadata.repo_commit_sha.substring(0, 7)})`;
          }
        }
      }

      return (
        <div className="flex items-center gap-1">
          {row.original.image_path ? (
            <DockerIcon className="mb-[3px] mr-1 inline shrink-0" />
          ) : (
            <GithubIcon className="mb-[3px] mr-1 inline shrink-0" />
          )}
          <Tooltip title={date.toLocaleString()}>
            <span>{relativeDate} ago </span>
          </Tooltip>
          <TextOverflowTooltip
            className="overflow-hidden text-ellipsis font-medium"
            customTooltip={display}
          >
            <span>
              {row.original.repo_commit && (
                <BranchIcon className="mr-1 inline" />
              )}
              <a className="underline">{display}</a>
            </span>
          </TextOverflowTooltip>
        </div>
      );
    },
    minSize: 200,
    enableSorting: false,
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Actions',
    size: 200,
    cell: ({ row, table }) => {
      const { actions, getLogsAvailable } = (table.options.meta ?? {}) as {
        actions: {
          handleInterrupt: (id: string) => void;
          handleLogs: (id: string) => void;
        };
        getLogsAvailable: (status: string) => boolean;
      };

      const isTerminalRevision = TERMINAL_REVISION_STATUSES.has(
        row.original.status ?? ''
      );

      return (
        <div className="flex items-center gap-2">
          {getLogsAvailable(row.original.status ?? '') && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                actions.handleLogs(row.original.id);
              }}
              className="rounded-md bg-tertiary p-1 hover:bg-quaternary"
              disabled={false}
            >
              <div className="flex items-center gap-2">
                <File02Icon className="size-4 text-tertiary" />
                <span className="text-xs font-semibold text-tertiary">
                  Logs
                </span>
              </div>
            </button>
          )}

          {!isTerminalRevision ? (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                actions.handleInterrupt(row.original.id);
              }}
              className="rounded-md bg-tertiary p-1 hover:bg-quaternary"
              disabled={false}
            >
              <div className="flex items-center gap-2">
                <XCircleIcon className="size-4 text-tertiary" />
                <span className="text-xs font-semibold text-tertiary">
                  Interrupt
                </span>
              </div>
            </button>
          ) : null}
        </div>
      );
    },
  }),
];

const EMPTY_TABLE = [];

export const HostRevisionsTable = ({
  rows,
  isLoading,
  isValidating,
  paginationModel,
  setPaginationModel,
  onRevisionPeekChange,
  revisionPeekId,
  hostProject,
  emptyState,
  showActiveRevision,
  hideHeader = false,
  paginationTotal,
}: TableProps) => {
  const [interruptOpen, setInterruptOpen] = useState<string | null>(null);

  const [columnSizing, setColumnSizing] =
    useDataGridSizingLocalStorage('hostRevisions');

  const { logsAvailable: logsAvailableForRevisionPeek, getLogsAvailable } =
    useLogsAvailableForRevision({
      hostProjectId: hostProject?.id ?? '',
      revisionId: revisionPeekId ?? undefined,
    });
  // if page loads with revisions logs, clear it
  useEffect(() => {
    if (logsAvailableForRevisionPeek === undefined) return;
    if (!logsAvailableForRevisionPeek && revisionPeekId) {
      onRevisionPeekChange(null);
    }
  }, [logsAvailableForRevisionPeek, onRevisionPeekChange, revisionPeekId]);

  const columns = useMemo(() => {
    return [
      ...COLUMNS_DEF.slice(0, 1),
      ...(showActiveRevision
        ? [
            columnHelper.accessor('id', {
              id: 'revision_id',
              header: 'Revision ID',
              enableSorting: false,
              cell: ({ cell, table }) => {
                const { showActiveRevision } = (table.options.meta ?? {}) as {
                  showActiveRevision?: boolean;
                };
                if (!showActiveRevision) return null;
                return (
                  <TextOverflowTooltip className="overflow-hidden text-ellipsis font-medium">
                    {cell.row.original.id}
                  </TextOverflowTooltip>
                );
              },
            }),
          ]
        : []),
      ...COLUMNS_DEF.slice(1),
    ];
  }, [showActiveRevision]);

  const table = useReactTable({
    columns,
    data: rows ?? EMPTY_TABLE,
    state: { pagination: paginationModel, columnSizing },
    onPaginationChange: setPaginationModel,
    onColumnSizingChange: setColumnSizing,
    meta: {
      actions: {
        handleInterrupt: setInterruptOpen,
        handleLogs: onRevisionPeekChange,
      },
      getLogsAvailable,
      showActiveRevision,
      lastActiveRevisionId:
        hostProject?.resource?.latest_active_revision
          ?.hosted_langserve_revision_id,
    },

    getRowId: (row) => row.id,

    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),

    manualExpanding: true,
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,

    columnResizeMode: 'onChange',

    pageCount: Math.ceil(+(paginationTotal || 0) / paginationModel.pageSize),
  });

  return (
    <>
      <div className="flex w-full flex-col gap-4 overflow-y-auto">
        <DataGrid
          table={table}
          isLoading={isLoading || isValidating}
          emptyState={emptyState}
          cellHeight={72}
          cellSpacingVariant="wide"
          hideHeader={hideHeader}
          loadingRowCount={showActiveRevision ? 1 : 10}
        />
      </div>

      {!showActiveRevision && (
        <div className="mx-5 mb-5">
          <DataGridPagination table={table} />
        </div>
      )}

      {interruptOpen && hostProject && (
        <HostInterruptModal
          hostProject={hostProject}
          revisionId={interruptOpen}
          isOpen={!!interruptOpen}
          doClose={() => setInterruptOpen(null)}
        />
      )}

      <SplitViewPane
        open={!!revisionPeekId}
        sidePaneId="examplesTable"
        onClose={() => onRevisionPeekChange(null)}
        title={
          isLoading && (
            <>
              <div className="mx-2 h-[32px] self-stretch border-r border-r-secondary" />
              <CircularProgress
                size="sm"
                color="neutral"
                sx={{ opacity: 0.5, transform: 'scale(0.83)' }}
              />
            </>
          )
        }
        className="relative"
      >
        <div className="absolute inset-0 flex flex-grow flex-col gap-4 overflow-auto p-4 pt-0">
          {revisionPeekId && hostProject?.id ? (
            <HostRevision
              hostProjectId={hostProject?.id}
              revisionId={revisionPeekId}
            />
          ) : null}
        </div>
      </SplitViewPane>
    </>
  );
};
