import { Tooltip } from '@mui/joy';

import { ReactElement, forwardRef, useMemo } from 'react';
import { Link, Location } from 'react-router-dom';

import { cn, utils } from '@/utils/tailwind';

import { checkIsActive } from './utils/utils';

type ConditionalLinkProps = {
  href?: string;
  children: React.ReactNode;
  target?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement | HTMLDivElement>) => void;
  className?: string;
};

export const ConditionalLink = forwardRef<
  HTMLAnchorElement | HTMLDivElement,
  ConditionalLinkProps
>(({ href, ...props }, ref) => {
  if (href) {
    return (
      <Link to={href} {...props} ref={ref as React.Ref<HTMLAnchorElement>} />
    );
  }
  return <div {...props} ref={ref as React.Ref<HTMLDivElement>} />;
});

ConditionalLink.displayName = 'ConditionalLink';

export function NavLink({
  location,
  label,
  href,
  expanded,
  icon,
  count,
  onClick,
  target,
  useExactMatch = false,
}: {
  location: Location;
  label: string;
  href?: string;
  expanded: boolean;
  icon: ReactElement;
  count?: number;
  onClick?: (
    isActive: boolean,
    e: React.MouseEvent<HTMLAnchorElement | HTMLDivElement>
  ) => void;
  target?: string;
  useExactMatch?: boolean;
}) {
  const isActive = useMemo(
    () => checkIsActive(href, useExactMatch, location),
    [href, location, useExactMatch]
  );

  return expanded ? (
    <ConditionalLink
      key={label}
      href={href}
      target={target}
      onClick={(e) => onClick?.(isActive, e)}
      className={cn(
        utils.button,
        isActive && utils.buttonActive,
        'flex items-center justify-between gap-1.5 px-3 py-2 text-primary'
      )}
    >
      <div className="flex items-center gap-1.5">
        <div>{icon}</div>
        <div className={cn('line-clamp-1 text-xs font-medium')}>{label}</div>
      </div>
      {count !== undefined && (
        <div className="flex items-center justify-center rounded bg-tertiary px-1.5 text-center text-xxs">
          {count.toLocaleString()}
        </div>
      )}
    </ConditionalLink>
  ) : (
    <Tooltip title={label} placement="right">
      <ConditionalLink
        key={label}
        href={href}
        target={target}
        onClick={(e) => onClick?.(isActive, e)}
        className={cn(
          'flex items-center text-secondary',
          'flex-col justify-center'
        )}
      >
        <div
          className={cn(
            utils.button,
            isActive && utils.buttonActive,
            'rounded-md p-2 text-secondary'
          )}
        >
          {icon}
        </div>
      </ConditionalLink>
    </Tooltip>
  );
}
