import {
  BarChart10Icon,
  Database02Icon,
  DotsHorizontalIcon,
  Edit03Icon,
  File02Icon,
  GitBranch01Icon,
  HomeLineIcon,
  Mail01Icon,
  MessagePlusCircleIcon,
  PlayCircleIcon,
  Settings01Icon,
} from '@langchain/untitled-ui-icons';

import { Dispatch, ReactElement, SetStateAction } from 'react';
import { useLocation, useMatches } from 'react-router-dom';

import { NavLinksGraphConnectPopover } from '@/Pages/Graph/GraphConnectPopover';
import GraphFillIcon from '@/Pages/Graph/icons/GraphFillIcon.svg?react';
import {
  ANNOTATION_QUEUES_PAGE_TITLE,
  DASHBOARDS_PAGE_TITLE,
  DATASETS_PAGE_TITLE,
  PLAYGROUND_PAGE_TITLE,
  PROJECTS_PAGE_TITLE,
  PROMPTS_PAGE_TITLE,
} from '@/constants/pageTitlesAndDescriptionConstants';
import { Auth } from '@/hooks/useAuth';
import { useOrgRequiresPayment } from '@/hooks/useCurrentTier';
import { useOrgDisabled } from '@/hooks/useOrgConfig';
import { usePermissions } from '@/hooks/usePermissions';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import { useOrganizationId, useWorkspaceStats } from '@/hooks/useSwr';
import { addQueryParams } from '@/utils/add-query-params';
import {
  appAnnotationQueuesPath,
  appDashboardsIndexPath,
  appOrganizationPath,
  appPlaygroundPath,
  appPromptsIndexPath,
  deploymentsAppIndexPath,
  hostEnabled,
} from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import KeyboardShortcutLink from '../KeyboardShortcutLink';
import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import { ChangeLogModal } from './ChangeLogModal';
import { InviteNotification } from './InviteNotification';
import { NavColorThemeSwitchButton } from './NavColorThemeSwitchButton';
import { NavLink } from './NavLink';
import { DEFAULT_SIDE_NAV_SECTION_COUNT } from './constants';
import { useGroupedOrgInvites } from './useGroupedInvites';
import { labelToId, useOverflowItems } from './utils/useOverflowItems';

interface NavLinkType {
  label: string;
  shortLabel?: string;
  href?: string;
  target?: string;
  decorator: ReactElement;
  count?: number;
  enabled: boolean;
}

function Section({
  label,
  links,
  expanded,
  inOverflowPopover,
  hide = false,
}: {
  label: string;
  links: NavLinkType[];
  expanded: boolean;
  inOverflowPopover?: boolean;
  hide?: boolean;
}) {
  const loc = useLocation();
  return (
    <div
      key={label}
      className={cn('flex flex-col gap-1.5 pt-2', hide ? 'hidden' : '')}
      id={inOverflowPopover ? '' : labelToId(label)}
    >
      {expanded && (
        <div className="-mb-1 truncate pl-3 text-xxs font-medium text-quaternary">
          {label}
        </div>
      )}
      <div className={cn('flex flex-col', expanded ? 'gap-0' : 'gap-3')}>
        {links
          .filter(({ enabled }) => enabled)
          .map(({ label, href, decorator, count }) => (
            <NavLink
              key={label}
              location={loc}
              label={label}
              href={href}
              expanded={expanded}
              icon={decorator}
              count={count}
            />
          ))}
      </div>
    </div>
  );
}

export function SecondaryContent({
  expanded,
  inOverflowPopover = false,
  hide = false,
}: {
  expanded: boolean;
  inOverflowPopover?: boolean;
  hide?: boolean;
}) {
  const iconClassname = expanded || inOverflowPopover ? 'size-3' : 'size-4';
  const matches = useMatches();
  const pageHasKeyboardShortcuts = matches.some(
    (m) => (m.handle as any)?.hotkeys
  );

  const loc = useLocation();

  return (
    <div
      key="secondary"
      id={inOverflowPopover ? '' : labelToId('secondary')}
      className={cn(
        'flex flex-col pb-3',
        expanded || inOverflowPopover ? 'gap-0' : 'gap-1',
        hide ? 'hidden' : ''
      )}
    >
      {pageHasKeyboardShortcuts && (
        <KeyboardShortcutLink expanded={expanded || inOverflowPopover} />
      )}
      {hostEnabled !== '1' && (
        <NavLinksGraphConnectPopover expanded={expanded || inOverflowPopover} />
      )}
      <NavLink
        key="Documentation"
        location={loc}
        label="Documentation"
        href="https://docs.smith.langchain.com"
        expanded={expanded || inOverflowPopover}
        icon={<File02Icon className={iconClassname} />}
        target="_blank"
      />
      <ChangeLogModal
        location={loc}
        expanded={expanded || inOverflowPopover}
        iconClassname={iconClassname}
      />
      <NavLink
        key="Contact sales"
        location={loc}
        label="Contact Sales"
        href="https://www.langchain.com/contact-sales"
        expanded={expanded || inOverflowPopover}
        icon={<Mail01Icon className={iconClassname} />}
        target="_blank"
      />
      <InviteNotification expanded={expanded || inOverflowPopover} />

      <NavColorThemeSwitchButton expanded={expanded || inOverflowPopover} />
    </div>
  );
}

export function NavLinks({
  auth,
  expanded,
  scrollContainerRef,
}: {
  auth?: Auth;
  expanded: boolean;
  setExpanded: Dispatch<SetStateAction<boolean>>;
  scrollContainerRef?: React.RefObject<HTMLDivElement>;
}) {
  const organizationId = useOrganizationId();
  const loc = useLocation();

  const { authorize, isLoading: isPermsLoading } = usePermissions();

  const { selectedTags } = useStoredResourceTags();
  const stats = useWorkspaceStats(
    {
      tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
    },
    {
      keepPreviousData: false,
      skip: !auth?.authed,
    }
  );
  const { requiresPayment, isLoading: isRequiresPaymentLoading } =
    useOrgRequiresPayment();
  const { isDisabled, isLoading: isDisabledLoading } = useOrgDisabled();

  const pendingInvites = useGroupedOrgInvites({
    revalidateOnFocus: false,
    revalidateOnMount: false,
  });
  const hasInvites = pendingInvites.data && pendingInvites.data.length > 0;

  const elementsLoading =
    isRequiresPaymentLoading || isDisabledLoading || isPermsLoading;

  const orgPrefix = organizationId
    ? `/${appOrganizationPath}/${organizationId}`
    : '';

  const observabilityLinks: NavLinkType[] = [];
  const evaluationLinks: NavLinkType[] = [];
  const promptEngLinks: NavLinkType[] = [];
  const deploymentLinks: NavLinkType[] = [];

  const allLinks = [
    {
      label: 'Observability',
      links: observabilityLinks,
      id: 'observability-content',
    },
    { label: 'Evaluation', links: evaluationLinks, id: 'evaluation-content' },
    {
      label: 'Prompt Engineering',
      links: promptEngLinks,
      id: 'prompt-engineering-content',
    },
    {
      label: 'LangGraph Platform',
      links: deploymentLinks,
      id: 'langgraph-platform-content',
    },
  ].filter(Boolean);

  const iconClassname = expanded ? 'size-3' : 'size-4';

  if (!requiresPayment && !isDisabled) {
    if (authorize('projects:read')) {
      observabilityLinks.push({
        label: PROJECTS_PAGE_TITLE,
        href: `${orgPrefix}/projects`,
        decorator: <GitBranch01Icon className={iconClassname} />,
        count: stats?.data?.tracer_session_count,
        enabled: true,
      });
    }
    if (authorize('charts:read')) {
      observabilityLinks.push({
        label: DASHBOARDS_PAGE_TITLE,
        href: `${orgPrefix}/${appDashboardsIndexPath}`,
        decorator: <BarChart10Icon className={iconClassname} />,
        count: stats?.data?.dashboards_count,
        enabled: true,
      });
    }
    if (authorize('datasets:read')) {
      evaluationLinks.push({
        label: DATASETS_PAGE_TITLE,
        href: `${orgPrefix}/datasets`,
        decorator: <Database02Icon className={iconClassname} />,
        count: stats?.data?.dataset_count,
        enabled: true,
      });
    }
    if (authorize('annotation-queues:read')) {
      evaluationLinks.push({
        label: ANNOTATION_QUEUES_PAGE_TITLE,
        href: `${orgPrefix}/${appAnnotationQueuesPath}`,
        decorator: <Edit03Icon className={iconClassname} />,
        count: stats?.data?.annotation_queue_count,
        enabled: true,
      });
    }

    if (authorize('prompts:read')) {
      promptEngLinks.push({
        label: PROMPTS_PAGE_TITLE,
        href: addQueryParams(`/${appPromptsIndexPath}`, { organizationId }),
        decorator: <MessagePlusCircleIcon className={iconClassname} />,
        count: stats?.data?.repo_count,
        enabled: true,
      });
    }
    if (authorize('prompts:create')) {
      promptEngLinks.push({
        label: PLAYGROUND_PAGE_TITLE,
        href: `${orgPrefix}/${appPlaygroundPath}`,
        decorator: <PlayCircleIcon className={iconClassname} />,
        count: undefined,
        enabled: true,
      });
    }
    if (authorize('deployments:read')) {
      deploymentLinks.push({
        label: 'Deployments',
        href: `${orgPrefix}/${deploymentsAppIndexPath}`,
        decorator: <GraphFillIcon className={iconClassname} />,
        count: stats?.data?.deployment_count,
        enabled: hostEnabled === '1',
      });
    }
  }

  const shownDefaultSectionCount =
    DEFAULT_SIDE_NAV_SECTION_COUNT - (!auth?.authed ? 1 : 0);

  const expectedItems = elementsLoading
    ? allLinks.length + shownDefaultSectionCount
    : allLinks.filter(({ links }) => links.length > 0).length +
      shownDefaultSectionCount;

  const { overflowItems } = useOverflowItems(
    expanded,
    elementsLoading,
    // need to pass this in because number of sections rendered can change based on permissions
    expectedItems,
    scrollContainerRef
  );
  const isYOverflowing = overflowItems.length > 0;

  const popoverContent = overflowItems.map((item) => {
    if (item === labelToId('secondary')) {
      return (
        <SecondaryContent
          expanded={expanded}
          inOverflowPopover={true}
          key={'secondary'}
        />
      );
    }
    const links = allLinks.find(({ id }) => id === item);
    return (
      links && (
        <Section
          inOverflowPopover={true}
          key={item}
          label={links.label}
          links={links.links}
          expanded={expanded || isYOverflowing}
        />
      )
    );
  });

  if (elementsLoading) {
    return <div className="flex-grow" />;
  }

  return (
    <>
      {auth?.authed ? (
        <div className={cn('flex flex-col', expanded ? 'gap-1' : 'gap-0')}>
          <div id={labelToId('home')}>
            {organizationId && (
              <NavLink
                key={'home'}
                location={loc}
                label={'Home'}
                href={`/${appOrganizationPath}/${organizationId}`}
                expanded={expanded}
                icon={<HomeLineIcon className={iconClassname} />}
                count={undefined}
                useExactMatch={true}
              />
            )}
          </div>
          {allLinks
            .filter(({ links }) => links.length > 0)
            .map(({ label, links }) => (
              <Section
                key={label}
                label={label}
                links={links}
                expanded={expanded}
                hide={overflowItems.includes(labelToId(label))}
              />
            ))}
        </div>
      ) : null}

      <div className="flex-grow" />
      <div id={labelToId('settings')} className={cn(!expanded && 'pb-1')}>
        {organizationId && (
          <NavLink
            key={'settings'}
            location={loc}
            label={'Settings'}
            href={`${orgPrefix}/settings`}
            expanded={expanded}
            icon={<Settings01Icon className={iconClassname} />}
            count={undefined}
          />
        )}
      </div>
      {isYOverflowing && (
        <Popover>
          <PopoverTrigger className="relative mb-2">
            <NavLink
              location={loc}
              expanded={expanded}
              label={'More'}
              icon={<DotsHorizontalIcon className={iconClassname} />}
            />
            {hasInvites && (
              <div
                className={cn(
                  'absolute flex size-4 items-center justify-center rounded-md bg-red-500 text-white',
                  expanded ? 'right-3 top-2' : 'right-1 top-0'
                )}
              >
                <span className="text-xxs">{pendingInvites.data?.length}</span>
              </div>
            )}
          </PopoverTrigger>
          <PopoverContent
            className="flex flex-col gap-2 p-0"
            side="right"
            align="end"
          >
            {popoverContent}
          </PopoverContent>
        </Popover>
      )}

      <SecondaryContent
        expanded={expanded}
        inOverflowPopover={false}
        hide={overflowItems.includes(labelToId('secondary'))}
      />
    </>
  );
}
