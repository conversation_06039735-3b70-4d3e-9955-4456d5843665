import { ChevronDownIcon } from '@heroicons/react/24/outline';
import {
  AlertCircleIcon,
  CheckIcon,
  Pencil01Icon,
  SearchLgIcon,
  User02Icon,
} from '@langchain/untitled-ui-icons';
import { LinearProgress, Tooltip } from '@mui/joy';

import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { groupBy } from '@/Pages/Graph/src/utils';
import { LATEST_TENANT_STORAGE_KEY } from '@/Pages/NoMatch/constants';
import { CreateOrganizationModal } from '@/Pages/Settings/components/CreateTenantModal';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { useAuth, useUserSSOInfo } from '@/hooks/useAuth';
import { AuthSession } from '@/hooks/useAuth.utils';
import { InstanceFlags, useInstanceFlag } from '@/hooks/useInstanceFlag';
import { useLogout } from '@/hooks/useLogout';
import {
  useCurrentOrganization,
  useOrganizationId,
  useOrganizations,
  useTenantList,
  useUser,
} from '@/hooks/useSwr';
import { LogOutIcon } from '@/icons/logout';
import { OrganizationBaseSchema, TenantSchema } from '@/types/schema';
import { appOrganizationPath, appSettingsPath } from '@/utils/constants';
import { lowerCaseIncludes } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';
import { getTenantDisplayName } from '@/utils/tenants-format';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../Dropdown';
import { ProfileCircle } from '../ProfileCircle';
import { TextOverflowTooltip } from '../TextOverflowTooltip';
import { EditProfileModal } from './EditProfileModal';
import { useShowEmailChange } from './utils/useShowEmailChange';

const OrganizationIcon = (props: {
  org?: { is_personal: boolean; display_name: string };
  className?: string;
}) => {
  const { org, className } = props;

  return org?.is_personal ? (
    <ProfileCircle
      name={''}
      backupIcon={<User02Icon className="size-3 shrink-0 text-white" />}
      className={className}
    />
  ) : (
    <ProfileCircle name={org?.display_name ?? ''} className={className} />
  );
};

const WorkspaceDropdown = (props: {
  workspaces: TenantSchema[];
  onClose: () => void;
  className?: string;
}) => {
  const currentWorkspaceId = useOrganizationId();
  const orgId = props.workspaces[0].organization_id;

  const latestTenantId = window.localStorage.getItem(
    `${LATEST_TENANT_STORAGE_KEY}-${orgId}`
  );
  const selectedWorkspace = props.workspaces.find(
    (w) => w.id === currentWorkspaceId || w.id === latestTenantId
  );

  const navigate = useNavigate();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={cn(
          'flex flex-none items-center justify-between gap-1 rounded-lg border border-secondary px-2 py-1 text-xs',
          props.className
        )}
      >
        <div className="truncate">
          {selectedWorkspace?.display_name ?? props.workspaces[0].display_name}
        </div>
        <ChevronDownIcon className="h-4 w-4 shrink-0" />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="max-h-[300px] overflow-y-auto">
        {props.workspaces.map((w) => (
          <DropdownMenuItem
            className="flex items-center justify-between text-xs"
            key={w.id}
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/${appOrganizationPath}/${w.id}`);
              props.onClose();
            }}
          >
            <TextOverflowTooltip>{w.display_name}</TextOverflowTooltip>
            {w.id === selectedWorkspace?.id && (
              <CheckIcon className="h-4 w-4 shrink-0" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const OrganizationItem = (props: {
  org: OrganizationBaseSchema;
  workspaces?: TenantSchema[];
  selected: boolean;
  onClose: () => void;
}) => {
  const { org, workspaces, selected } = props;
  const navigate = useNavigate();

  const latestTenantId = window.localStorage.getItem(
    `${LATEST_TENANT_STORAGE_KEY}-${org.id}`
  );
  return (
    <button
      type="button"
      className={cn(
        'flex min-h-9 w-full cursor-pointer items-center justify-between gap-2 overflow-hidden px-4 py-1 text-xs',
        selected ? 'bg-secondary' : 'hover:bg-secondary'
      )}
      onClick={() => {
        if (workspaces && workspaces.length > 0) {
          navigate(
            `/${appOrganizationPath}/${latestTenantId ?? workspaces[0].id}`
          );
          props.onClose();
        }
      }}
    >
      <div className="flex min-w-0 flex-1 items-center gap-2">
        <OrganizationIcon org={org} className="flex-none" />
        <TextOverflowTooltip className="min-w-0">
          {org.display_name}
        </TextOverflowTooltip>
      </div>
      {!org.is_personal && workspaces && workspaces.length > 0 && (
        <WorkspaceDropdown
          workspaces={workspaces}
          onClose={props.onClose}
          className="w-[120px]"
        />
      )}
    </button>
  );
};

type Props = {
  onEditClick: () => void;
  session: AuthSession | null;
  orgName?: string;
  isPersonal?: boolean;
  disableEmailEdit?: boolean;
};

const NavMenuHeader = (props: Props) => {
  const { data: supabaseUser } = useUser();
  const userEmail =
    supabaseUser?.data.user?.email || props.session?.user.email || '';

  const hasEmailLogin = supabaseUser?.data?.user?.identities?.some(
    (id) => id.provider === 'email'
  );

  let tooltipTitle = 'Change email';
  if (props.disableEmailEdit) {
    tooltipTitle = '';
  } else if (!hasEmailLogin) {
    tooltipTitle = 'Sign up with this email or reset password to change email';
  }

  const showEmailUpdateNotification = useShowEmailChange();
  return (
    <div
      className={cn(
        'flex flex-col items-center gap-1 rounded-b-none rounded-t-xl p-4 pb-2',
        'focus-within:outline-none focus:outline-none'
      )}
    >
      <ProfileCircle
        name={''}
        backupIcon={<User02Icon className="size-4" />}
        className="h-8 w-8 text-xl "
      />

      <Tooltip title={tooltipTitle}>
        <div className="flex min-w-0 flex-1 items-center gap-1 overflow-hidden text-ellipsis text-left text-sm text-tertiary">
          {userEmail}
          {!props.disableEmailEdit && (
            <button
              onClick={() => {
                if (!props.disableEmailEdit && hasEmailLogin) {
                  props.onEditClick();
                }
              }}
              type="button"
              className={cn(
                'group relative flex size-4 items-center justify-center',
                !hasEmailLogin && 'cursor-not-allowed'
              )}
            >
              {showEmailUpdateNotification && (
                <AlertCircleIcon className="size-4 text-warning group-hover:opacity-0" />
              )}

              <Pencil01Icon
                className={cn(
                  'absolute size-4 opacity-0 transition-opacity group-hover:opacity-100',
                  !showEmailUpdateNotification && 'opacity-100'
                )}
              />
            </button>
          )}
        </div>
      </Tooltip>
    </div>
  );
};

const WorkspacePickerContent = (props: { onClose: () => void }) => {
  const [search, setSearch] = useState('');
  const logout = useLogout();

  const { session } = useAuth();
  const orgCreationDisabled = useInstanceFlag(
    InstanceFlags.org_creation_disabled
  );
  const tenants = useTenantList({ revalidateOnFocus: false });
  const currentOrg = useCurrentOrganization();
  const organizations = useOrganizations();
  const { isSSOLogin } = useUserSSOInfo();

  const navigate = useNavigate();
  // put current org at the top of the list
  const orgsList = useMemo(() => {
    if (!organizations.data) return [];

    return [
      organizations.data.find((org) => org.id === currentOrg.data?.id),
      ...organizations.data.filter((org) => org.id !== currentOrg.data?.id),
    ].filter(Boolean) as OrganizationBaseSchema[];
  }, [organizations.data, currentOrg.data?.id]);

  const filteredOrganizations = useMemo(() => {
    return orgsList.filter((org) =>
      lowerCaseIncludes(org.display_name, search)
    );
  }, [orgsList, search]);

  const groupTenantsByOrg = useMemo(() => {
    if (!tenants.data) return {};
    return groupBy(
      tenants.data,
      (tenant) => tenant.organization_id ?? 'default'
    );
  }, [tenants.data]);

  return (
    <div className="flex w-full flex-col gap-2 text-sm">
      <div className="flex items-center justify-center  px-4 pt-2">
        {session && (
          <EditProfileModal disabled={isSSOLogin}>
            {({ onClick }) => (
              <NavMenuHeader
                onEditClick={onClick}
                session={session}
                orgName={currentOrg.data?.display_name}
                disableEmailEdit={isSSOLogin}
              />
            )}
          </EditProfileModal>
        )}
      </div>

      <div className="flex items-center justify-between border-t border-secondary px-4 pt-2">
        <div className="text-sm">Organizations</div>
        {!orgCreationDisabled && !isSSOLogin && (
          <CreateOrganizationModal
            onSuccess={(tenant) => {
              if (tenant != null) {
                navigate(
                  `/${appOrganizationPath}/${tenant.id}/${appSettingsPath}`
                );
              }
            }}
          >
            {({ onClick }) => (
              <button
                type="button"
                className="border-b border-ls-black"
                onClick={onClick}
              >
                + New
              </button>
            )}
          </CreateOrganizationModal>
        )}
      </div>
      <div className="px-4">
        <div className="flex items-center gap-2 rounded-md bg-secondary p-2">
          <SearchLgIcon className="h-4 w-4"></SearchLgIcon>
          <input
            className="w-full border-none bg-transparent p-0 text-sm outline-none"
            type="text"
            placeholder="Search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </div>
      <div className="h-[200px] overflow-y-auto border-b border-secondary pb-2">
        {organizations.isLoading ? (
          <div className="px-4">
            <LinearProgress />
          </div>
        ) : filteredOrganizations.length === 0 ? (
          <div className="text-center text-sm text-secondary">
            No organizations found
          </div>
        ) : (
          filteredOrganizations?.map((org) => (
            <OrganizationItem
              key={org.id}
              org={org}
              workspaces={groupTenantsByOrg[org.id]}
              selected={org.id === currentOrg.data?.id}
              onClose={props.onClose}
            />
          ))
        )}
      </div>

      <button
        type="button"
        className="flex items-center gap-4 px-4 py-4 hover:bg-secondary"
        onClick={async () => {
          await logout();
          close();
        }}
      >
        <LogOutIcon />
        Log out
      </button>
    </div>
  );
};

export const WorkspacePicker = (props: {
  className?: string;
  expanded: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const { session } = useAuth();
  const orgQuery = useCurrentOrganization();
  const org = orgQuery.data;
  const { data: supabaseUser } = useUser();
  const userEmail = supabaseUser?.data.user?.email || session?.user.email || '';

  return (
    <div className={cn('w-full', props.className)}>
      <Popover
        open={open}
        onOpenChange={(open: boolean) => {
          setOpen(open);
        }}
      >
        <PopoverTrigger
          className={cn(
            'text-md flex w-full items-center text-xs transition-all hover:bg-secondary',
            props.expanded
              ? 'justify-between p-2'
              : 'mx-auto justify-center rounded-md'
          )}
        >
          <OrganizationIcon
            org={org}
            className={cn(
              'pointer-events-none',
              props.expanded ? 'h-8 w-8' : 'h-5 w-5'
            )}
          />

          {props.expanded && (
            <div className="ml-2 flex flex-1 flex-col items-start overflow-hidden">
              <div className="line-clamp-1 text-left">
                {org && getTenantDisplayName(org, org.is_personal)}
              </div>
              <div className="max-w-full overflow-hidden text-ellipsis opacity-60">
                {userEmail}
              </div>
            </div>
          )}
        </PopoverTrigger>
        <PopoverContent
          side="top"
          align="start"
          alignOffset={16}
          className="relative w-[300px] overflow-y-auto p-0"
        >
          <WorkspacePickerContent onClose={() => setOpen(false)} />
        </PopoverContent>
      </Popover>
    </div>
  );
};
