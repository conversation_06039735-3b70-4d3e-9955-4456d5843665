import { Moon01Icon, SunIcon } from '@langchain/untitled-ui-icons';
import { useColorScheme } from '@mui/joy';

import { cn, utils } from '@/utils/tailwind';

import * as Dropdown from '../Dropdown';

export function NavColorThemeSwitchButton({ expanded }: { expanded: boolean }) {
  const { mode, systemMode, setMode } = useColorScheme();

  const finalMode = (mode === 'system' ? systemMode : mode) ?? 'light';

  return (
    <Dropdown.DropdownMenu>
      <Dropdown.DropdownMenuTrigger
        className={cn(
          expanded && utils.button,
          'flex w-full select-none items-center outline-none',
          expanded
            ? 'justify-between gap-2 rounded-none px-3 py-1.5 text-xs'
            : 'flex-col justify-center gap-0'
        )}
      >
        <div
          className={cn(
            'flex items-center gap-1.5 text-primary',
            !expanded && utils.button,
            !expanded && 'rounded-md p-2'
          )}
        >
          {finalMode === 'light' ? (
            <SunIcon className={cn(expanded ? 'size-3' : 'size-4')} />
          ) : (
            <Moon01Icon className={cn(expanded ? 'size-3' : 'size-4')} />
          )}
          {expanded && <div className="line-clamp-1 font-medium">Theme</div>}
        </div>
        <div
          className={cn(
            'line-clamp-1 font-medium capitalize text-tertiary',
            expanded ? 'rounded bg-tertiary px-2 py-0.5' : 'hidden'
          )}
        >
          {finalMode}
        </div>
      </Dropdown.DropdownMenuTrigger>
      <Dropdown.DropdownMenuContent
        align="start"
        side="right"
        className={'text-xxs'}
      >
        <Dropdown.DropdownMenuItem onClick={() => setMode('light')}>
          Light
        </Dropdown.DropdownMenuItem>
        <Dropdown.DropdownMenuItem onClick={() => setMode('dark')}>
          Dark
        </Dropdown.DropdownMenuItem>
        <Dropdown.DropdownMenuItem onClick={() => setMode('system')}>
          System
        </Dropdown.DropdownMenuItem>
      </Dropdown.DropdownMenuContent>
    </Dropdown.DropdownMenu>
  );
}
