import { UserPlus01Icon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import {
  useClaimPendingOrgInvite,
  useDeletePendingOrgInvite,
  useOrganizationId,
} from '@/hooks/useSwr';
import { appOrganizationPath } from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import { NavLink } from './NavLink';
import { GroupedInviteSchema, useGroupedOrgInvites } from './useGroupedInvites';

type Props = GroupedInviteSchema & {
  type: 'workspace' | 'organization';
};

export function PendingInvite({
  id,
  display_name,
  workspaceInvites,
  showWorkspaces,
  type,
}: Props) {
  const deleteInvite = useDeletePendingOrgInvite();
  const claimInvite = useClaimPendingOrgInvite();

  return (
    <div
      key={id}
      className="flex flex-col gap-3 rounded-xl bg-zinc-100 p-4 dark:bg-[#25252D] dark:bg-opacity-100"
    >
      {showWorkspaces ? (
        <div className="text-xs font-medium">
          You were invited to workspaces{' '}
          <strong>
            {workspaceInvites?.map((w) => w.display_name).join(', ')}
          </strong>{' '}
          in the <strong>{display_name}</strong> organization
        </div>
      ) : (
        <div className="text-xs font-medium">
          You were invited to the <strong>{display_name}</strong> {type}
        </div>
      )}

      <div className="flex items-center gap-2">
        <Button
          size="sm"
          aria-label="decline"
          color="neutral"
          variant="outlined"
          onClick={() => deleteInvite.trigger(id)}
        >
          Decline
        </Button>
        <Button
          size="sm"
          aria-label="accept"
          color="primary"
          onClick={() => claimInvite.trigger(id)}
        >
          Accept
        </Button>
      </div>
    </div>
  );
}

export function InviteNotification(props: { expanded: boolean }) {
  const [open, setOpen] = useState(false);
  const iconClassname = props.expanded ? 'size-3' : 'size-4';
  const loc = useLocation();
  const pendingInvites = useGroupedOrgInvites({
    revalidateOnFocus: false,
  });

  const hasInvites = pendingInvites.data && pendingInvites.data.length > 0;
  const navigate = useNavigate();
  const organizationId = useOrganizationId();
  const orgPrefix = organizationId
    ? `/${appOrganizationPath}/${organizationId}`
    : '';

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger className="relative">
        <NavLink
          useExactMatch
          key="Invitations"
          location={loc}
          label="Invitations"
          onClick={(_, e) => {
            if (!hasInvites) {
              e.preventDefault();
              navigate(`${orgPrefix}/settings/members`);
            }
          }}
          expanded={props.expanded}
          icon={<UserPlus01Icon className={iconClassname} />}
        />
        {hasInvites && (
          <div
            className={cn(
              'absolute flex size-4 items-center justify-center rounded-md bg-red-500 text-white',
              props.expanded ? 'right-3 top-2' : 'right-1 top-0'
            )}
          >
            <span className="text-xxs">{pendingInvites.data?.length}</span>
          </div>
        )}
      </PopoverTrigger>
      <PopoverContent side="right" align="end" className="h-52 overflow-y-auto">
        <div className="mb-2">Invitations</div>
        <div className="flex flex-wrap gap-4">
          {pendingInvites.data?.map((t) => (
            <PendingInvite key={t.id} {...t} type="organization" />
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
