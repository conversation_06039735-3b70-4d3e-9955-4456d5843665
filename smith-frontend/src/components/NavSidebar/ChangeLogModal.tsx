import { Modal, ModalDialog, Tooltip } from '@mui/joy';

import { BellPlusIcon } from 'lucide-react';
import React, { useState } from 'react';
import { Location } from 'react-router-dom';

import { launchNotesEmbedToken } from '@/utils/constants';
import { cn, utils } from '@/utils/tailwind';

import { ConditionalLink } from './NavLink';

type ChangeLogModalProps = {
  location: Location;
  expanded: boolean;

  iconClassname?: string;
  handleClick?: (
    e: React.MouseEvent<HTMLAnchorElement | HTMLDivElement>
  ) => void;
};

export function ChangeLogModal({
  location,
  expanded,
  iconClassname,
  handleClick,
}: ChangeLogModalProps) {
  const [isChangelogModalOpen, setChangelogModalOpen] = useState(false);

  const href = 'https://docs.smith.langchain.com';
  const label = "What's New";
  const target = '_blank';

  const isActive = location.pathname.startsWith(href) && !!href;

  const handleModalOpen = (
    e: React.MouseEvent<HTMLAnchorElement | HTMLDivElement>
  ) => {
    e.preventDefault();
    setChangelogModalOpen(true);
    const dateInPST = new Date().toLocaleString('en-US', {
      timeZone: 'America/Los_Angeles',
    });
    localStorage.setItem('changelogModalLastView', dateInPST);
    handleClick?.(e);
  };

  const handleModalClose = () => {
    setChangelogModalOpen(false);
  };

  return (
    <>
      {expanded ? (
        <ConditionalLink
          href={href}
          target={target}
          onClick={handleModalOpen}
          className={cn(
            utils.button,
            isActive && utils.buttonActive,
            'flex items-center justify-between gap-1.5 px-3 py-2 text-primary'
          )}
        >
          <div className="flex items-center gap-1.5">
            <div>
              <BellPlusIcon className={iconClassname} />
            </div>
            <div className={cn('line-clamp-1 text-xs font-medium')}>
              {label}
            </div>
          </div>
        </ConditionalLink>
      ) : (
        <Tooltip title={label} placement="right">
          <ConditionalLink
            href={href}
            target={target}
            onClick={handleModalOpen}
            className={cn(
              'flex items-center text-secondary',
              'flex-col justify-center'
            )}
          >
            <div
              className={cn(
                utils.button,
                isActive && utils.buttonActive,
                'rounded-md p-2 text-secondary'
              )}
            >
              <BellPlusIcon className={iconClassname} />
            </div>
          </ConditionalLink>
        </Tooltip>
      )}
      <Modal open={isChangelogModalOpen} onClose={handleModalClose}>
        <ModalDialog
          className={cn(
            'w-2/3 overflow-auto',
            'max-h-[80vh] max-w-[800px]',
            '[&_.announcement-card]:bg-secondary',
            '[&_.announcement-card>h2]:text-secondary',
            '[&_.announcement-card>.timestamp]:text-tertiary',
            '[&_.announcement-card_.category]:text-tertiary',
            'dark:[&_.announcement-card]:bg-secondary',
            'dark:[&_.announcement-card>h2]:text-secondary',
            'dark:[&_.announcement-card>.timestamp]:text-tertiary',
            'dark:[&_.announcement-card_.category]:text-tertiary'
          )}
        >
          <launchnotes-embed-inline
            project="pro_3Ijp7fWHDFv2L"
            view="complete"
            token={launchNotesEmbedToken}
            limit={6}
          ></launchnotes-embed-inline>
        </ModalDialog>
      </Modal>
    </>
  );
}
