import {
  ArrowDownIcon,
  ArrowUpIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { AlertCircleIcon } from '@langchain/untitled-ui-icons';
import {
  Button,
  CircularProgress,
  Divider,
  IconButton,
  LinearProgress,
  Modal,
  ModalClose,
  ModalDialog,
  Tooltip,
} from '@mui/joy';

import {
  ReactNode,
  isValidElement,
  useCallback,
  useMemo,
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';
import useSWRMutation from 'swr/mutation';

import { BlankScore } from '@/Pages/DatasetSessionCompare/components/SingleExperimentColumns/FeedbackColumns/OnlyErrorsChip';
import { useFeedbackSourceRunIds } from '@/Pages/DatasetSessionCompare/components/hooks/useFeedbackSourceRunIds';
import { getRepetitionNumber } from '@/Pages/DatasetSessionCompare/utils/getRepetitionNumber';
import { fetcher } from '@/data/fetcher';
import { useFeedbackSourceUser } from '@/hooks/useFeedbackSourceUser';
import { useCreateFeedback, useOrganizationId } from '@/hooks/useSwr';
import useToastOn429Error from '@/hooks/useToastOn429Error';
import APIIcon from '@/icons/APIIcon.svg?react';
import ArrowRightIcon from '@/icons/ArrowRightIcon.svg?react';
import AutoEvalIcon from '@/icons/AutoEvalIcon.svg?react';
import ChainIcon from '@/icons/ChainIcon.svg?react';
import EditIcon from '@/icons/EditIcon.svg?react';
import ExternalLinkArrowIcon from '@/icons/ExternalLinkArrowIcon.svg?react';
import ModelIcon from '@/icons/ModelIcon.svg?react';
import MoreButton from '@/icons/MoreButton.svg?react';
import ProjectIcon from '@/icons/ProjectIcon';
import { apiFeedbackPath, apiRunsPath } from '@/utils/constants';
import { useClickLogger } from '@/utils/datadog/useClickLogger';
import { getColorByString } from '@/utils/get-color-by-string';
import { isEvaluatorRunExpired } from '@/utils/isEvaluatorRunExpired';
import { cn } from '@/utils/tailwind';

import { FeedbackSchema, FeedbackStatsField, RunSchema } from '../types/schema';
import { emulateNativeClick, emulateNativeMiddleClick } from './DataGrid.utils';
import { Popover, PopoverContent, PopoverTrigger } from './Popover';
import { ProfileCircle } from './ProfileCircle';
import { TextWithLinks } from './TextWithLinks';

const NUMBER_OF_CATEGORIES_TO_SHOW = 1;

const formatter = new Intl.NumberFormat('en-US', {
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

export function FeedbackChip({
  feedback_key,
  value,
  children,
  allowTruncation = false,
  iconType,
  hoverHighlight,
  n,
  className,
  hideFeedbackKey,
  startDecorator,
  isCategorical,
  valueClassname,
  loading,
  errors = 0,
  showErrorCount = false,
  numRuns,
  showErrorsOutside = false,
}: {
  feedback_key: string;
  children?: ReactNode;
  value?: ReactNode | number | ReactNode[] | number[] | null;
  n?: number;
  allowTruncation?: boolean;
  iconType?: 'run' | 'session';
  hoverHighlight?: boolean;
  className?: string;
  hideFeedbackKey?: boolean;
  isCategorical?: boolean;
  startDecorator?: ReactNode;
  valueClassname?: string;
  loading?: boolean;
  errors?: number;
  showErrorCount?: boolean;
  numRuns?: number;
  showErrorsOutside?: boolean;
}) {
  let valuesTemp = value;
  if (!Array.isArray(valuesTemp) && value != null) {
    valuesTemp = [value];
  }
  const values = valuesTemp as (number | ReactNode)[] | undefined;

  const formattedValues = values
    ?.map((v, idx) => {
      if (
        v == null ||
        (typeof v === 'number' && isNaN(v)) ||
        (typeof v !== 'string' && typeof v !== 'number' && !isValidElement(v))
      )
        return null;
      return (
        <span
          key={idx}
          className={cn(
            'shrink-0 font-medium',
            isCategorical &&
              typeof v === 'string' &&
              'line-clamp-1 max-h-[15px] max-w-[100px] shrink-0 overflow-hidden whitespace-normal break-all'
          )}
        >
          {typeof v === 'number' ? formatter.format(v) : v}
        </span>
      );
    })
    .filter(Boolean);

  return (
    <div
      className={cn(
        'flex items-center gap-2 whitespace-nowrap rounded-md border border-secondary bg-background px-2 py-[3px] text-xs',
        hoverHighlight && 'hover:bg-tertiary',
        className
      )}
    >
      {startDecorator ? (
        startDecorator
      ) : (
        <span
          className="h-2 w-2 min-w-2 shrink-0 rounded-sm"
          style={{ backgroundColor: getColorByString(feedback_key) }}
        />
      )}
      <span className="line-clamp-1 flex max-h-[300px] items-center gap-1 truncate break-words break-all font-semibold ">
        {iconType === 'run' ? (
          <ChainIcon className="h-3.5 w-3.5 fill-current" />
        ) : iconType === 'session' ? (
          <ProjectIcon className="-my-1 h-[18px] w-[18px] fill-current" />
        ) : null}
        {!hideFeedbackKey && (
          <span
            className={cn(
              'pr-1',
              allowTruncation &&
                'line-clamp-1 shrink whitespace-normal break-all'
            )}
          >
            {feedback_key}
          </span>
        )}
        {formattedValues &&
          formattedValues.length > 0 &&
          formattedValues.map((v, idx) => (
            <div
              key={idx}
              className={cn(
                'flex shrink-0 gap-1 rounded px-1',
                !hideFeedbackKey && 'bg-secondary',
                'font-medium lowercase text-tertiary',
                valueClassname
              )}
            >
              <span className="flex max-h-[100px] items-center gap-1 truncate break-words break-all">
                {n != null &&
                  n + errors > 1 &&
                  !hideFeedbackKey &&
                  !isCategorical && <span className="pb-0.5">µ</span>}
                {v}
                {n != null &&
                  n + errors > 1 &&
                  hideFeedbackKey &&
                  !isCategorical && <span className="pb-0.5">µ</span>}
              </span>
            </div>
          ))}
        {showErrorCount && errors > 0 && !showErrorsOutside && (
          <ErrorIcon
            errors={errors}
            numRuns={numRuns}
            valueClassname={valueClassname}
          />
        )}
        {loading && (
          <CircularProgress
            sx={{
              '--CircularProgress-size': '12px',
              '--CircularProgress-progressThickness': '2px',
              '--CircularProgress-trackThickness': '2px',
              transform: 'scaleX(-1)',
            }}
          />
        )}
      </span>
      {children}
    </div>
  );
}

export function ErrorIcon({
  errors,
  numRuns,
  valueClassname,
}: {
  errors: number;
  numRuns?: number;
  valueClassname?: string;
}) {
  return (
    <div className={cn('flex shrink-0 gap-1 rounded', valueClassname)}>
      <span
        className={cn(
          'flex max-h-[100px] items-center truncate break-words break-all font-medium text-red-500',
          (numRuns == undefined || numRuns > 1) && 'gap-1'
        )}
      >
        <AlertCircleIcon className="h-4 w-4" />
        <span
          className={cn(
            numRuns == undefined || numRuns > 1 ? '' : 'invisible w-0'
          )}
        >
          {errors}
        </span>
      </span>
    </div>
  );
}

function SingleFeedbackChipWithSourceRunActionInternal(props: {
  feedbackKey: string;
  feedbackSourceRunId?: string | undefined;
  feedbackSourceRuns?: RunSchema[];
  stdev?: number | null;
  avg?: number | null;
  n?: number;
  values?: {
    [key: string]: number | null;
  };
  errors?: number;
  showErrorCount?: boolean;
  maxNumberOfCategories?: number;
  allowTruncation?: boolean;
  showNotes?: boolean;
  showFeedbackArrow?: boolean;
  testIdPrefix?: string;
  iconType?: 'run' | 'session';
  className?: string;
  hideFeedbackKey?: boolean;
  startDecorator?: ReactNode;
  comparativeExperimentId?: string | null;
  onSort?: (feedbackKey: string, descending: boolean) => void;
  sortingByFeedbackKey?: string;
  sortingByDescending?: boolean;
  disablePopover?: boolean;
  onMutate?: () => void;
  loading?: boolean;
  hideDot?: boolean;
  onViewEvaluatorRun?: (runId: string) => void;
  chipClassName?: string;
  valueClassName?: string;
  tooltip?: string;
  showErrorsOutside?: boolean;
  hideEllipsis?: boolean;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) {
  const logClick = useClickLogger();
  const [isPopoverOpen, setIsPopoverOpenState] = useState(false);
  const feedbackSourceRunIds = useMemo(
    () =>
      props.feedbackSourceRunId
        ? [props.feedbackSourceRunId]
        : props.feedbackSourceRuns?.map((run) => run.id),
    [props.feedbackSourceRunId, props.feedbackSourceRuns]
  );
  const fetchFeedback = useFeedbackSourceRunIds(
    props.feedbackKey,
    feedbackSourceRunIds,
    props.comparativeExperimentId
  );

  const setIsPopoverOpen = useCallback(
    (isOpen: boolean) => {
      if (isOpen) {
        logClick('feedback_chip_popover', {
          feedbackKey: props.feedbackKey,
          feedbackSourceRunId: props.feedbackSourceRunId,
          clickType: 'popover_open',
        });
        if (!fetchFeedback.data && feedbackSourceRunIds) {
          fetchFeedback.trigger();
        }
      }
      setIsPopoverOpenState(isOpen);
    },
    [
      feedbackSourceRunIds,
      fetchFeedback,
      logClick,
      props.feedbackKey,
      props.feedbackSourceRunId,
    ]
  );

  const feedbackData = useMemo(() => {
    const feedbackRuns = props.feedbackSourceRuns;
    if (!feedbackRuns) return fetchFeedback.data;
    return fetchFeedback.data?.slice().sort((a, b) => {
      const aRepNum = getRepetitionNumber(feedbackRuns, a.run_id);
      const bRepNum = getRepetitionNumber(feedbackRuns, b.run_id);
      return aRepNum - bRepNum;
    });
  }, [fetchFeedback.data, props.feedbackSourceRuns]);

  let tooltipText: string | undefined = undefined;

  if (feedbackSourceRunIds?.length === 1) {
    tooltipText = feedbackData?.[0]?.comment;
  }

  if (props.hideFeedbackKey) {
    tooltipText = tooltipText
      ? `${props.feedbackKey}: ${tooltipText}`
      : props.feedbackKey;
  }
  if (props.loading) {
    tooltipText = 'Computing evaluator score, please wait';
  }
  if (props.tooltip) {
    tooltipText = props.tooltip;
  }

  const maxNumCategories =
    props.maxNumberOfCategories ?? NUMBER_OF_CATEGORIES_TO_SHOW;
  const valuesEntries = Object.entries(props.values ?? {});
  let valuesToDisplay: (ReactNode | number)[] = valuesEntries
    .sort((a, b) => (b[1] ?? 0) - (a[1] ?? 0))
    .slice(0, maxNumCategories)
    .map(([key, value], idx) => {
      return (
        <div className="flex items-center gap-1" key={idx}>
          <span className="line-clamp-1 max-w-[70px] shrink-0 overflow-hidden whitespace-normal break-all">
            {key}
          </span>
          {value && value > 1 && <span className="text-tertiary">{value}</span>}
        </div>
      );
    });
  if (valuesEntries.length > maxNumCategories) {
    valuesToDisplay.push(
      <div
        className={cn(
          'flex items-center',
          props.hideFeedbackKey && 'rounded bg-secondary px-1'
        )}
      >
        <span>+{valuesEntries.length - maxNumCategories}</span>
      </div>
    );
  }
  let isCategorical = true;
  if (valuesToDisplay.length === 0 && props.avg != null) {
    valuesToDisplay = [props.avg];
    isCategorical = false;
  }

  const disableAllFeedbackPopover =
    props.disablePopover ||
    props.onSort != null ||
    feedbackSourceRunIds == null;

  const disableValuesPopover = !(
    props.values &&
    valuesEntries.length &&
    valuesEntries.length > maxNumCategories
  );

  const mainComponent = (
    <Tooltip
      title={
        tooltipText ? (
          <div
            style={{
              maxHeight: '256px',
              maxWidth: '244px',
              overflowY: 'auto',
            }}
          >
            {tooltipText}
          </div>
        ) : null
      }
    >
      <button
        type="button"
        className={cn(
          'flex items-center gap-2',
          disableAllFeedbackPopover && disableValuesPopover && 'cursor-default',
          props.className
        )}
        data-testid={
          props.testIdPrefix &&
          `${props.testIdPrefix}-feedback-chip-${props.feedbackKey}`
        }
        onMouseEnter={async () => {
          if (
            !props.showNotes ||
            fetchFeedback.data ||
            !props.feedbackSourceRunId ||
            disableAllFeedbackPopover
          )
            return;
          await fetchFeedback.trigger();
        }}
      >
        {props.showErrorsOutside &&
        !valuesToDisplay.filter((v) => v != null).length ? (
          <BlankScore shouldShowHover className={props.chipClassName} />
        ) : (
          <FeedbackChip
            key={props.feedbackKey}
            feedback_key={props.feedbackKey}
            value={valuesToDisplay}
            isCategorical={isCategorical}
            valueClassname={cn(
              props.hideFeedbackKey && 'px-0.5',
              props.valueClassName
            )}
            n={props.n}
            errors={props.errors}
            showErrorCount={props.showErrorCount}
            allowTruncation={props.allowTruncation}
            iconType={props.iconType}
            hoverHighlight={!disableAllFeedbackPopover || !disableValuesPopover}
            hideFeedbackKey={props.hideFeedbackKey}
            startDecorator={props.startDecorator}
            loading={props.loading}
            numRuns={feedbackSourceRunIds?.length}
            className={props.chipClassName}
            showErrorsOutside={props.showErrorsOutside}
          >
            {props.onSort && (
              <div className="flex gap-1">
                <Tooltip title={`Sort by ${props.feedbackKey}, ascending`}>
                  <div
                    className={cn(
                      'cursor-pointer rounded-md border border-secondary p-1 hover:bg-secondary active:bg-tertiary',
                      props.sortingByFeedbackKey === props.feedbackKey &&
                        props.sortingByFeedbackKey &&
                        !props.sortingByDescending &&
                        'border-brand'
                    )}
                    onMouseUp={(e) => {
                      e.stopPropagation();
                      props.onSort?.(props.feedbackKey, false);
                    }}
                  >
                    <ArrowUpIcon className="h-3 w-3" />
                  </div>
                </Tooltip>
                <Tooltip title={`Sort by ${props.feedbackKey}, descending`}>
                  <div
                    className={cn(
                      'cursor-pointer rounded-md border border-secondary p-1 hover:bg-secondary active:bg-tertiary',
                      props.sortingByFeedbackKey === props.feedbackKey &&
                        props.sortingByFeedbackKey &&
                        props.sortingByDescending &&
                        'border-brand'
                    )}
                    onMouseUp={(e) => {
                      e.stopPropagation();
                      props.onSort?.(props.feedbackKey, true);
                    }}
                  >
                    <ArrowDownIcon className="h-3 w-3" />
                  </div>
                </Tooltip>
              </div>
            )}
            {(!disableAllFeedbackPopover || !disableValuesPopover) &&
              !props.hideEllipsis && (
                <MoreButton
                  className={cn('shrink-0 text-tertiary', props.valueClassName)}
                />
              )}
          </FeedbackChip>
        )}
        {props.showErrorCount &&
        props.showErrorsOutside &&
        props.errors &&
        props.errors > 0 ? (
          <ErrorIcon
            errors={props.errors}
            numRuns={feedbackSourceRunIds?.length}
            valueClassname={props.valueClassName}
          />
        ) : null}
      </button>
    </Tooltip>
  );

  if (disableAllFeedbackPopover) {
    if (!disableValuesPopover) {
      return (
        <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
          <PopoverTrigger
            asChild
            onClick={(e) => {
              e.stopPropagation();
              setIsPopoverOpen(true);
            }}
          >
            {mainComponent}
          </PopoverTrigger>
          <PopoverContent
            className="flex min-w-[400px] flex-col gap-2 divide-y divide-secondary p-0"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <div className="flex w-full items-center justify-between px-4 pt-2">
              <div className="flex items-center gap-3">
                <div className="pl-2 text-sm font-medium">
                  {props.feedbackKey.replace(/\b\w/g, (char) =>
                    char.toUpperCase()
                  )}
                </div>
              </div>
              <IconButton
                onClick={() => {
                  setIsPopoverOpen(false);
                }}
                size="sm"
                color="neutral"
                variant="plain"
              >
                <XMarkIcon className="h-4 w-4" />
              </IconButton>
            </div>
            <div className="flex max-h-[400px] flex-col gap-3 overflow-y-auto overflow-x-hidden py-5 pl-6 pr-4">
              {' '}
              <div className="grid grid-cols-[auto,1fr] gap-3">
                {valuesEntries.slice(0, 20).map((valueTuple, i) => {
                  return valueTuple[1] != null ? (
                    <FeedbackChipValuesPopoverItem
                      key={i}
                      value={valueTuple[0]}
                      count={valueTuple[1]}
                    />
                  ) : null;
                })}
              </div>
              {
                <div className="flex items-center justify-end gap-5">
                  <span className="text-tertiary">
                    {valuesEntries.length > 20
                      ? `...and ${valuesEntries.length - 20} more`
                      : null}
                  </span>
                </div>
              }
            </div>
          </PopoverContent>
        </Popover>
      );
    }
    return mainComponent;
  }

  return (
    <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
      <PopoverTrigger
        asChild
        onClick={(e) => {
          e.stopPropagation();
          setIsPopoverOpen(true);
        }}
      >
        {mainComponent}
      </PopoverTrigger>
      <PopoverContent
        className="flex min-w-[400px] flex-col gap-2 divide-y divide-secondary p-0"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className="flex w-full items-center justify-between px-4 pt-2">
          <div className="flex items-center gap-3">
            <div className="pl-2 text-sm font-medium">
              {props.feedbackKey.replace(/\b\w/g, (char) => char.toUpperCase())}
            </div>
            {(props.values == null || Object.keys(props.values).length == 0) &&
              props.avg != null &&
              !isNaN(props.avg) && (
                <>
                  <Tooltip title="Average">
                    <div className="flex gap-1 rounded-[5px] bg-tertiary px-1.5 text-sm font-medium">
                      {props.n && props.n > 1 && (
                        <span className="pb-0.5 lowercase text-tertiary">
                          µ
                        </span>
                      )}
                      <span>{formatter.format(props.avg)}</span>
                    </div>
                  </Tooltip>
                  {props.stdev != null &&
                    props.n &&
                    props.n - (props.errors ?? 0) > 1 &&
                    !isNaN(props.stdev) && (
                      <Tooltip title="Standard deviation">
                        <div className="flex gap-1 rounded-[5px] bg-tertiary px-1.5 text-sm font-medium">
                          <span className="pb-0.5 lowercase text-tertiary">
                            σ
                          </span>
                          <span>{formatter.format(props.stdev)}</span>
                        </div>
                      </Tooltip>
                    )}
                </>
              )}
          </div>
          <IconButton
            onClick={() => {
              setIsPopoverOpen(false);
            }}
            size="sm"
            color="neutral"
            variant="plain"
          >
            <XMarkIcon className="h-4 w-4" />
          </IconButton>
        </div>
        {fetchFeedback.isMutating ? (
          <div className="flex w-full flex-col justify-start">
            <div className="h-[72px] grow-0">
              <LinearProgress />
            </div>
          </div>
        ) : (
          <div className="grid max-h-[400px] grid-cols-[auto,1fr] gap-3 overflow-auto py-5 pl-6 pr-4">
            {feedbackData?.map((feedback, i) => {
              return (
                <FeedbackChipPopoverItem
                  key={i}
                  feedback={feedback}
                  feedbackSourceRuns={props.feedbackSourceRuns}
                  onMutate={() => {
                    props.onMutate?.();
                    if (feedbackSourceRunIds) {
                      fetchFeedback.trigger();
                    }
                  }}
                  onViewEvaluatorRun={props.onViewEvaluatorRun}
                  closePopover={() => setIsPopoverOpen(false)}
                  traceTier={props.traceTier}
                  experimentStartTime={props.experimentStartTime}
                />
              );
            })}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}

export function SingleFeedbackChipWithSourceRunAction(props: {
  feedbackKey: string;
  feedbackSourceRunId?: string | undefined;
  feedbackSourceRuns?: RunSchema[];
  stdev?: number | null;
  avg?: number | null;
  n?: number;
  values?: {
    [key: string]: number | null;
  };
  errors?: number;
  showErrorCount?: boolean;
  maxNumberOfCategories?: number;
  allowTruncation?: boolean;
  showNotes?: boolean;
  showFeedbackArrow?: boolean;
  testIdPrefix?: string;
  iconType?: 'run' | 'session';
  className?: string;
  hideFeedbackKey?: boolean;
  startDecorator?: ReactNode;
  comparativeExperimentId?: string | null;
  onSort?: (feedbackKey: string, descending: boolean) => void;
  sortingByFeedbackKey?: string;
  sortingByDescending?: boolean;
  disablePopover?: boolean;
  onMutate?: () => void;
  loading?: boolean;
  onViewEvaluatorRun?: (runId: string) => void;
  chipClassName?: string;
  valueClassName?: string;
  tooltip?: string;
  showErrorsOutside?: boolean;
  hideEllipsis?: boolean;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) {
  return (
    <SingleFeedbackChipWithSourceRunActionInternal
      {...props}
      key={props.feedbackKey}
    />
  );
}

function FeedbackChipValuesPopoverItem({
  value,
  count,
}: {
  value: string;
  count: number;
}) {
  return (
    <>
      <div className="flex min-w-0 items-center gap-3 pr-2">
        <span className="shrink overflow-x-auto overflow-y-hidden whitespace-nowrap no-scrollbar">
          {value}
        </span>
      </div>
      <div className="flex items-center justify-start gap-5">
        <div className="w-[1px] self-stretch bg-tertiary" />
        <span className="text-tertiary">{count}</span>
      </div>
    </>
  );
}

function FeedbackChipPopoverItem({
  feedback,
  feedbackSourceRuns,
  onMutate,
  onViewEvaluatorRun,
  closePopover,
  traceTier,
  experimentStartTime,
}: {
  feedback: FeedbackSchema;
  feedbackSourceRuns?: RunSchema[];
  onMutate?: () => void;
  onViewEvaluatorRun?: (runId: string) => void;
  closePopover?: () => void;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContents, setModalContents] = useState<string>();
  const repNumber =
    feedbackSourceRuns && feedbackSourceRuns.length > 1
      ? getRepetitionNumber(feedbackSourceRuns, feedback.run_id)
      : null;

  const correctionScore =
    typeof feedback.correction?.score === 'number'
      ? feedback.correction?.score
      : typeof feedback.correction?.score === 'string'
      ? parseFloat(feedback.correction?.score)
      : null;

  return (
    <>
      <div className="flex min-w-0 items-center gap-3">
        {repNumber && (
          <div className="shrink-0 text-sm font-medium uppercase text-tertiary">
            REP {repNumber}
          </div>
        )}
        <div className="overflow-none line-clamp-1 text-base font-semibold no-scrollbar">
          {feedback.value != null ? (
            <div
              className="max-h-28 cursor-pointer overflow-y-auto rounded p-1 hover:bg-tertiary"
              onClick={() => {
                setIsModalOpen(true);
                setModalContents(
                  typeof feedback.value === 'string'
                    ? feedback.value
                    : JSON.stringify(feedback.value, null, 2)
                );
              }}
            >
              {typeof feedback.value === 'string'
                ? feedback.value
                : JSON.stringify(feedback.value, null, 2)}
            </div>
          ) : correctionScore != null && feedback.score != null ? (
            <div className="flex items-center gap-2">
              <span className="text-tertiary line-through">
                {formatter.format(feedback.score)}
              </span>
              <span>{formatter.format(correctionScore)}</span>
            </div>
          ) : feedback.score != null ? (
            formatter.format(feedback.score)
          ) : feedback.comment != null ? (
            <div
              className="cursor-pointer rounded p-1 hover:bg-tertiary"
              onClick={() => {
                setIsModalOpen(true);
                setModalContents(feedback.comment);
              }}
            >
              {feedback.comment}
            </div>
          ) : (
            'N/A'
          )}
        </div>
      </div>
      <div className="flex items-center justify-between gap-3">
        <div className="flex gap-3">
          <div className="w-[1px] self-stretch bg-tertiary" />
          <FeedbackChipPopoverItemUserIcon
            source={feedback.feedback_source}
            feedbackId={feedback.id}
            onViewEvaluatorRun={onViewEvaluatorRun}
            closePopover={closePopover}
            traceTier={traceTier}
            experimentStartTime={experimentStartTime}
          />
        </div>
        {(feedback.feedback_source?.type === 'model' ||
          feedback.feedback_source?.type === 'auto_eval') &&
          feedback.score != null && (
            <CorrectFeedbackPopover feedback={feedback} onMutate={onMutate} />
          )}
        {feedback.extra?.error && (
          <Tooltip title="Evaluator run errored">
            <div className="flex h-8 w-8 items-center justify-center">
              <AlertCircleIcon className="h-4 w-4 text-error" />
            </div>
          </Tooltip>
        )}
      </div>
      <Modal open={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalDialog
          variant="outlined"
          sx={{ maxWidth: '42rem', width: '100%' }}
        >
          <ModalClose />
          <h2 className="mb-4 text-xl font-bold">Evaluator Feedback</h2>
          <pre className="max-h-[60vh] overflow-y-auto whitespace-pre-wrap break-words text-sm">
            <TextWithLinks text={modalContents?.replace(/\\n/g, '\n') ?? ''} />
          </pre>
        </ModalDialog>
      </Modal>
    </>
  );
}

function CorrectFeedbackPopover({
  feedback,
  onMutate,
}: {
  onMutate?: () => void;
  feedback: FeedbackSchema;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const mutate = useCreateFeedback();
  const { handle429Error } = useToastOn429Error();
  const defaultScore = feedback.correction?.score ?? feedback.score;
  const [inputText, setInputText] = useState(`${defaultScore}`);
  const [explanationText, setExplanationText] = useState('');
  const newFeedbackScore = parseFloat(inputText);
  const isUpdateDisabled =
    isNaN(newFeedbackScore) || newFeedbackScore === defaultScore;
  const onSubmit = useCallback(() => {
    if (!isUpdateDisabled) {
      return mutate
        .trigger({
          url: `${apiFeedbackPath}/${feedback.id}`,
          method: 'PATCH',
          json: {
            correction:
              newFeedbackScore === feedback.score
                ? {}
                : {
                    score: newFeedbackScore,
                    few_shot_explanation: explanationText,
                  },
          },
        })
        .then((feedback) => {
          onMutate?.();
          setIsOpen(false);
          return feedback;
        })
        .catch((e) => {
          handle429Error(e, 'feedback');
          return undefined;
        });
    }
  }, [
    explanationText,
    feedback.id,
    feedback.score,
    handle429Error,
    isUpdateDisabled,
    mutate,
    newFeedbackScore,
    onMutate,
  ]);
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <IconButton size="sm" color="neutral" variant="plain">
          <EditIcon className="h-4 w-4" />
        </IconButton>
      </PopoverTrigger>
      <PopoverContent className="w-[368px] p-0">
        <div className="flex flex-col">
          <div className="flex items-center justify-between py-2 pl-4 pr-2">
            <div className="text-sm font-medium ">
              {feedback.key.replace(/\b\w/g, (char) => char.toUpperCase())}
            </div>
            <IconButton
              onClick={() => {
                setIsOpen(false);
              }}
              size="sm"
              color="neutral"
              variant="plain"
            >
              <XMarkIcon className="h-4 w-4" />
            </IconButton>
          </div>
          <Divider />
          <form
            className="flex flex-col gap-3 px-6 py-5"
            onSubmit={(e) => {
              e.preventDefault();
              onSubmit();
            }}
          >
            <div className="text-sm font-semibold">Make correction</div>
            <div
              className={cn(
                'inline-flex divide-x divide-secondary rounded-md border border-secondary focus-within:border-brand'
              )}
            >
              <input
                value={inputText}
                onChange={(e) => {
                  setInputText(e.target.value);
                }}
                className={cn(
                  'border-none bg-transparent p-[5px] px-2 text-sm',
                  'w-full caret-black outline-none placeholder:text-tertiary dark:caret-slate-200'
                )}
              />
            </div>
            <div className="text-sm font-semibold">
              Add explanation (optional)
            </div>
            <div
              className={cn(
                'inline-flex divide-x divide-secondary rounded-md border border-secondary focus-within:border-brand'
              )}
            >
              <textarea
                value={explanationText}
                onChange={(e) => {
                  setExplanationText(e.target.value);
                }}
                placeholder="Explanation..."
                className={cn(
                  'border-none bg-transparent p-[5px] px-2 text-sm',
                  'w-full caret-black outline-none placeholder:text-tertiary dark:caret-slate-200'
                )}
                rows={3}
              />
            </div>
            <div className="text-[13px] font-normal text-tertiary">
              If your evaluator uses few-shot examples, you can insert this
              explanation into your prompt along with the updated score
            </div>
            <a
              href="https://docs.smith.langchain.com/evaluation/how_to_guides/audit_evaluator_scores"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1 text-secondary hover:underline"
            >
              <div className="text-sm font-normal ">
                Learn about corrections
              </div>
              <ArrowRightIcon className="h-4 w-4 flex-shrink-0 -rotate-45" />
            </a>
            <div className="self-end">
              <Button
                type="submit"
                color="primary"
                loading={mutate.isMutating}
                disabled={isUpdateDisabled}
              >
                Update
              </Button>
            </div>
          </form>
        </div>
      </PopoverContent>
    </Popover>
  );
}

function FeedbackChipPopoverItemUserIcon({
  source,
  feedbackId,
  onViewEvaluatorRun,
  closePopover,
  traceTier,
  experimentStartTime,
}: {
  source: FeedbackSchema['feedback_source'];
  feedbackId?: string;
  onViewEvaluatorRun?: (runId: string) => void;
  closePopover?: () => void;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) {
  const { isActiveUser, feedbackUser, isLoading } = useFeedbackSourceUser(
    source.user_id
  );

  const username = feedbackUser?.full_name ?? source.user_name ?? 'User';

  const logClick = useClickLogger();
  const navigate = useNavigate();

  const sourceRunId = source?.metadata?.__run?.run_id;
  const organizationId = useOrganizationId();

  const isRunExpired = useMemo(
    () => isEvaluatorRunExpired(traceTier, experimentStartTime),
    [traceTier, experimentStartTime]
  );

  const fetchRunUrl = useSWRMutation(
    sourceRunId && !isRunExpired
      ? {
          type: 'feedback-open-source',
          runId: sourceRunId,
        }
      : null,
    async (values) => {
      if (!values.runId) return null;
      const run = await fetcher<RunSchema>({
        url: `${apiRunsPath}/${values.runId}`,
        method: 'GET',
        headers: { 'X-Tenant-Id': organizationId },
      });

      return run;
    }
  );

  if (isLoading) {
    return <CircularProgress size="sm" />;
  }

  const displayText =
    source?.type === 'app'
      ? username
      : source?.type === 'api'
      ? 'API'
      : source?.type === 'auto_eval'
      ? 'AutoEval'
      : source?.type === 'model'
      ? 'Evaluator'
      : 'Unknown Source';

  const autoEvalIcon = <AutoEvalIcon className="h-3.5 w-3.5 text-tertiary" />;

  const evaluatorIcon = <ModelIcon className="h-3.5 w-3.5 text-tertiary" />;

  const apiSourceIcon = <APIIcon className="h-3.5 w-3.5 text-tertiary" />;

  const appFeedbackIcon = (
    <ProfileCircle active={isActiveUser} name={username} />
  );

  const mainComponent = (
    <div className="flex flex-row items-center justify-start gap-1.5">
      {source?.type === 'app' && appFeedbackIcon}
      {source?.type === 'api' && apiSourceIcon}
      {source?.type === 'auto_eval' && autoEvalIcon}
      {source?.type === 'model' && evaluatorIcon}
      <div className="text-xs font-normal leading-none text-tertiary">
        {displayText}
      </div>
      {sourceRunId &&
        !isEvaluatorRunExpired &&
        (fetchRunUrl.isMutating ? (
          <div className="flex h-4 w-4 items-center justify-center">
            <CircularProgress
              sx={{
                '--CircularProgress-size': '12px',
                '--CircularProgress-progressThickness': '2px',
                '--CircularProgress-trackThickness': '2px',
                transform: 'scaleX(-1)',
              }}
            />
          </div>
        ) : (
          <ExternalLinkArrowIcon className="h-4 w-4 text-tertiary" />
        ))}
    </div>
  );

  if (sourceRunId) {
    if (isRunExpired) {
      return (
        <Tooltip title="Evaluator run has expired">
          <div className="flex cursor-not-allowed items-center gap-2 rounded-md p-1 text-tertiary opacity-75">
            {mainComponent}
          </div>
        </Tooltip>
      );
    }

    return (
      <Tooltip title="View evaluator run">
        <button
          type="button"
          className="flex items-center gap-2 rounded-md p-1 hover:bg-secondary active:bg-tertiary"
          onClickCapture={async (e) => {
            e.stopPropagation();
            if (sourceRunId) {
              const run = await fetchRunUrl.trigger();
              logClick('evaluator_run_feedback_chip', {
                feedbackKey: feedbackId,
                feedbackSourceRunId: sourceRunId,
                clickType: 'left',
              });
              if (run) {
                if (onViewEvaluatorRun) {
                  onViewEvaluatorRun(run.id);
                } else if (!emulateNativeClick(run.app_path, e.nativeEvent)) {
                  navigate(run.app_path);
                }
                closePopover?.();
              }
            }
          }}
          onMouseUp={async (e) => {
            e.stopPropagation();
            if (e.button === 1 && sourceRunId) {
              const run = await fetchRunUrl.trigger();
              logClick('evaluator_run_feedback_chip', {
                feedbackKey: feedbackId,
                feedbackSourceRunId: sourceRunId,
                clickType: 'middle',
              });
              if (run) {
                if (onViewEvaluatorRun) {
                  onViewEvaluatorRun(run.id);
                } else if (!emulateNativeMiddleClick(run.app_path)) {
                  navigate(run.app_path);
                }
                closePopover?.();
              }
            }
          }}
        >
          {mainComponent}
        </button>
      </Tooltip>
    );
  }
  return mainComponent;
}

export function FeedbackChips({
  feedbackStats,
  feedbackSourceRunId,
  feedbackSourceRuns,
  comparativeExperimentId,
  sortMode = 'n',
  showErrorCount = false,
  allowTruncation = false,
  showNotes = false,
  testIdPrefix,
  maxChips = 20,
  iconType,
  className,
  hideFeedbackKey,
  onSort,
  sortingByFeedbackKey,
  sortingByDescending,
  onMutate,
  disablePopover,
  maxNumberOfCategories,
  expectedFeedbackKeys,
  onViewEvaluatorRun,
  hiddenFeedbackKeys,
  startDecorator,
  chipClassName,
  valueClassName,
  showErrorsOutside,
  hideEllipsis,
  firstFeedbackKey,
  traceTier,
  experimentStartTime,
}: {
  feedbackStats: FeedbackStatsField | undefined;
  feedbackSourceRunId?: string | undefined;
  feedbackSourceRuns?: RunSchema[];
  comparativeExperimentId?: string | null;
  sortMode?: 'n' | 'key';
  showErrorCount?: boolean;
  allowTruncation?: boolean;
  showNotes?: boolean;
  testIdPrefix?: string;
  maxChips?: number;
  iconType?: 'run' | 'session';
  className?: string;
  hideFeedbackKey?: boolean;
  onSort?: (feedbackKey: string, descending: boolean) => void;
  sortingByFeedbackKey?: string;
  sortingByDescending?: boolean;
  onMutate?: () => void;
  disablePopover?: boolean;
  maxNumberOfCategories?: number;
  expectedFeedbackKeys?: string[];
  onViewEvaluatorRun?: (runId: string) => void;
  hiddenFeedbackKeys?: string[];
  startDecorator?: ReactNode;
  chipClassName?: string;
  valueClassName?: string;
  showErrorsOutside?: boolean;
  hideEllipsis?: boolean;
  firstFeedbackKey?: string;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) {
  const feedbackSourceRunIds = useMemo(
    () =>
      feedbackSourceRunId
        ? [feedbackSourceRunId]
        : feedbackSourceRuns?.map((run) => run.id),
    [feedbackSourceRunId, feedbackSourceRuns]
  );

  const values = useMemo(
    () =>
      feedbackStats
        ? Object.entries(feedbackStats)
            .sort((a, b) => {
              if (firstFeedbackKey) {
                if (a[0] === firstFeedbackKey) return -1;
                if (b[0] === firstFeedbackKey) return 1;
              }
              if (sortMode === 'key') return a[0].localeCompare(b[0]);
              return a[1].n - b[1].n;
            })
            .filter(([key]) => !(hiddenFeedbackKeys ?? []).includes(key))
            .slice(0, maxChips)
        : [],
    [feedbackStats, sortMode, maxChips, hiddenFeedbackKeys, firstFeedbackKey]
  );

  const renderedExpectedFeedbackKeys = useMemo(
    () =>
      expectedFeedbackKeys
        ?.filter((key) => !values.find(([k]) => k === key))
        .slice(0, Math.max(maxChips - values.length, 0)),
    [expectedFeedbackKeys, values, maxChips]
  );

  if (
    !feedbackStats &&
    (!expectedFeedbackKeys || expectedFeedbackKeys.length === 0)
  )
    return null;

  return (
    <>
      {values.map(([key, value], i) => (
        <SingleFeedbackChipWithSourceRunAction
          hideFeedbackKey={hideFeedbackKey}
          key={`${i}-${key}-${feedbackSourceRunIds?.join('-')}`}
          n={value.n}
          values={value.values}
          errors={value.errors}
          showErrorCount={showErrorCount}
          feedbackKey={key}
          feedbackSourceRunId={feedbackSourceRunId}
          feedbackSourceRuns={feedbackSourceRuns}
          comparativeExperimentId={comparativeExperimentId}
          stdev={value.stdev}
          avg={value.avg}
          allowTruncation={allowTruncation}
          showNotes={showNotes}
          showFeedbackArrow={value.show_feedback_arrow}
          testIdPrefix={testIdPrefix}
          iconType={iconType}
          className={className}
          onSort={onSort}
          sortingByDescending={sortingByDescending}
          sortingByFeedbackKey={sortingByFeedbackKey}
          onMutate={onMutate}
          disablePopover={disablePopover}
          maxNumberOfCategories={maxNumberOfCategories}
          onViewEvaluatorRun={onViewEvaluatorRun}
          startDecorator={startDecorator}
          chipClassName={chipClassName}
          valueClassName={valueClassName}
          showErrorsOutside={showErrorsOutside}
          hideEllipsis={hideEllipsis}
          traceTier={traceTier}
          experimentStartTime={experimentStartTime}
        />
      ))}
      {renderedExpectedFeedbackKeys &&
        renderedExpectedFeedbackKeys.map((key, i) => {
          return (
            <SingleFeedbackChipWithSourceRunAction
              hideFeedbackKey={hideFeedbackKey}
              key={`${i}-${key}-expected`}
              feedbackKey={key}
              allowTruncation={allowTruncation}
              testIdPrefix={testIdPrefix}
              iconType={iconType}
              className={className}
              sortingByDescending={sortingByDescending}
              sortingByFeedbackKey={sortingByFeedbackKey}
              disablePopover={true}
              loading
              onViewEvaluatorRun={onViewEvaluatorRun}
              startDecorator={startDecorator}
              chipClassName={chipClassName}
              valueClassName={valueClassName}
              showErrorsOutside={showErrorsOutside}
              hideEllipsis={hideEllipsis}
              traceTier={traceTier}
              experimentStartTime={experimentStartTime}
            />
          );
        })}
    </>
  );
}
