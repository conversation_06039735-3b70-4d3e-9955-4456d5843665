import { ArrowUpRightIcon } from '@langchain/untitled-ui-icons';

import { useColorScheme } from '@/hooks/useColorScheme';
import { cn } from '@/utils/tailwind';

export interface OnboardingCardProps {
  title: string;
  description: string;
  onClick: () => void;
  helperText?: string;
  className?: string;
}

export function OnboardingCard({
  title,
  description,
  onClick,
  helperText,
  className,
}: OnboardingCardProps) {
  const { isDarkMode } = useColorScheme();
  return (
    <button
      type="button"
      onClick={onClick}
      className={cn(
        className,
        {
          'bg-tertiary': isDarkMode,
          'bg-opacity-88 bg-[#edebff] hover:border-[var(--brand-900)] hover:bg-[#e8e5ff]':
            !isDarkMode,
        },
        'bg-opacity-88 group flex h-80 w-80 flex-col justify-between gap-2.5 rounded-3xl border border-transparent p-3 transition-all transition-colors duration-300 ease-in-out '
      )}
    >
      <div className="flex flex-1 flex-col items-center justify-center gap-3 rounded-3xl border border-[var(--brand-900)] p-4">
        <div className="text-center text-xl font-medium leading-[22px] tracking-[-0.8px]">
          {title}
        </div>
        <div className="text-md text-center leading-[20px] tracking-[-0.64px] text-quaternary">
          {description}
        </div>
      </div>
      <div className="flex justify-end">
        <div className="flex items-center">
          {helperText && (
            <div className="text-sm text-brand-green-900 opacity-0 duration-300 ease-in-out group-hover:opacity-100">
              {helperText}
            </div>
          )}
          <div className="transition-transform duration-300 group-hover:-translate-y-1 group-hover:translate-x-1">
            <ArrowUpRightIcon className="h-14 w-14 font-medium [&_path]:stroke-[0.3px]" />
          </div>
        </div>
      </div>
    </button>
  );
}
