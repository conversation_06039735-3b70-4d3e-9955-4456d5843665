import { javascript } from '@codemirror/lang-javascript';
import { markdown } from '@codemirror/lang-markdown';
import { Button, FormControl, FormLabel, Input } from '@mui/joy';
import Box from '@mui/joy/Box';

import { useState } from 'react';

import { useNewApiKeyMutation } from '@/hooks/useSwr';
import { apiKeyPath } from '@/utils/constants';
import { createName } from '@/utils/create-name';
import { cn } from '@/utils/tailwind';

import { getAPIUrl } from '../../utils/get-api-url';
import { ButtonSwitchSimple } from '../ButtonSwitch';
import { Code, CodeCard } from '../Code/Code';
import { CopyIconButton } from '../CopyButton/CopyButton';
import { SETUP_CODE_EXAMPLES } from './constants';

const TracingQuickstartNewSessionSymbol = Symbol(
  'TracingQuickstartNewSessionSymbol'
);
const createProjectName = createName.bind(null, 'pr');

interface EnvQuickstartProps {
  sessionName?: string | typeof TracingQuickstartNewSessionSymbol;
  showCreateApiKey?: boolean;
  withLangChain?: 'withLangChain' | 'withoutLangChain';
}

interface ConfigureEnvironmentProps {
  apiKey: string;
  apiUrl: string;
  sessionName?: string | typeof TracingQuickstartNewSessionSymbol;
  idx: number;
}

export const CreateApiKey = (props: {
  newKey: string | null;
  setNewKey: (key: string | null) => void;
  idx?: number;
}) => {
  const createKey = useNewApiKeyMutation({});

  return (
    <div className={cn('mb-3 flex gap-3', props.newKey ?? 'items-center')}>
      {props.idx && `${props.idx}.`}
      {props.newKey ? (
        <p className="relative">
          New key created:{' '}
          <pre className="break-all">
            {props.newKey}{' '}
            <CopyIconButton copy={props.newKey} className="inline" />
          </pre>
        </p>
      ) : (
        <Button
          size="sm"
          onClick={async () => {
            const resp = await createKey.trigger({
              url: `${apiKeyPath}/current`,
              json: {
                description: 'Auto created during onboarding',
              },
            });
            if (resp?.key) {
              props.setNewKey(resp.key);
            }
          }}
          loading={createKey.isMutating}
        >
          Generate API Key
        </Button>
      )}
    </div>
  );
};

const InstallDependencies = (props: { code: string; idx: number }) => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between gap-3">
        {props.idx}. Install dependencies
      </div>
      <CodeCard>
        <Code
          language={markdown()}
          value={props.code}
          readOnly
          showCopyButton
        />
      </CodeCard>
    </div>
  );
};

export const ConfigureEnvironment = (props: ConfigureEnvironmentProps) => {
  const [inputName, setInputName] = useState(createProjectName);

  const envLines: string[] = [];
  envLines.push('LANGSMITH_TRACING=true');
  envLines.push(`LANGSMITH_ENDPOINT="${props.apiUrl}"`);
  envLines.push(`LANGSMITH_API_KEY="${props.apiKey}"`);

  const sessionName =
    props.sessionName === TracingQuickstartNewSessionSymbol
      ? inputName
      : props.sessionName;

  if (sessionName !== undefined && sessionName !== 'default') {
    envLines.push(`LANGSMITH_PROJECT="${sessionName}"`);
  }
  envLines.push(`OPENAI_API_KEY="<your-openai-api-key>"`);
  const env = envLines.join('\n').trim();

  return (
    <>
      {props.idx}. Configure environment to connect to LangSmith.
      {props.sessionName === TracingQuickstartNewSessionSymbol && (
        <FormControl>
          <FormLabel>Project Name</FormLabel>
          <Input
            placeholder="Project Name"
            value={inputName}
            onChange={(e) => setInputName(e.target.value)}
          />
        </FormControl>
      )}
      <CodeCard>
        <Code language={markdown()} value={env} readOnly showCopyButton />
      </CodeCard>
    </>
  );
};

const ExampleCode = (props: { code: string; idx: number }) => {
  return (
    <>
      {props.idx}. Run any LLM, Chat model, or Chain. Its trace will be sent to
      the set project.
      <CodeCard sx={{ marginBottom: 1 }}>
        <Code
          language={javascript()}
          value={props.code}
          readOnly
          showCopyButton
        />
      </CodeCard>
    </>
  );
};

const NextSteps = () => {
  return (
    <div className="mt-8 space-y-4">
      <p>
        🎉 Congratulations, on logging your first trace in LangSmith. Here are
        some topics to explore next:
      </p>
      <ul className="list-disc space-y-2 pl-5">
        <li>
          Read the{' '}
          <a
            href="https://docs.smith.langchain.com/observability/concepts"
            className="text-brand-secondary"
            target="_blank"
            rel="noopener noreferrer"
          >
            conceptual guide{' '}
          </a>{' '}
          to learn about runs, traces, projects and more.
        </li>
        <li>
          Set up tracing with{' '}
          <a
            href="https://docs.smith.langchain.com/observability/how_to_guides/trace_with_langchain"
            className="text-brand-secondary"
            target="_blank"
            rel="noopener noreferrer"
          >
            LangChain,{' '}
          </a>
          <a
            href="https://docs.smith.langchain.com/observability/how_to_guides/trace_with_langgraph"
            className="text-brand-secondary"
            target="_blank"
            rel="noopener noreferrer"
          >
            LangGraph,
          </a>{' '}
          or the other supported{' '}
          <a
            href="https://docs.smith.langchain.com/observability/how_to_guides#integrations"
            className="text-brand-secondary"
            target="_blank"
            rel="noopener noreferrer"
          >
            {' '}
            integration options.{' '}
          </a>
        </li>
        <li>
          If you prefer video tutorials, check out{' '}
          <a
            href="https://academy.langchain.com/pages/intro-to-langsmith-preview"
            className="text-brand-secondary"
            target="_blank"
            rel="noopener noreferrer"
          >
            Tracing Basics{' '}
          </a>{' '}
          from LangSmith Academy.
        </li>
      </ul>
    </div>
  );
};

const EnvQuickstart = ({
  sessionName = TracingQuickstartNewSessionSymbol,
  showCreateApiKey,
  withLangChain = 'withLangChain',
}: EnvQuickstartProps) => {
  const [newKey, setNewKey] = useState<string | null>(null);
  const apiUrl = getAPIUrl();
  const apiKey = newKey ?? '<your-api-key>';
  const [lang, setLang] = useState('py');
  const tabs = [
    {
      label: 'Python',
      value: 'py',
    },
    {
      label: 'TypeScript',
      value: 'ts',
    },
  ];

  const installDependenciesCode =
    SETUP_CODE_EXAMPLES[withLangChain][lang].installDependencies;
  const exampleCode = SETUP_CODE_EXAMPLES[withLangChain][lang].exampleCode;

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="stretch"
      width="700px"
      gap={2}
    >
      {showCreateApiKey && (
        <CreateApiKey newKey={newKey} setNewKey={setNewKey} idx={1} />
      )}
      <div className="relative">
        <InstallDependencies
          idx={showCreateApiKey ? 2 : 1}
          code={installDependenciesCode}
        />
        <ButtonSwitchSimple
          sx={{ position: 'absolute', right: 0, top: 0 }}
          value={lang}
          onChange={setLang}
          options={tabs}
        />
      </div>
      <ConfigureEnvironment
        apiKey={apiKey}
        apiUrl={apiUrl}
        sessionName={sessionName}
        idx={showCreateApiKey ? 3 : 2}
      />
      <ExampleCode code={exampleCode} idx={showCreateApiKey ? 4 : 3} />
      <NextSteps />
    </Box>
  );
};

export default EnvQuickstart;
