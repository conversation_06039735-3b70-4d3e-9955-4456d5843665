import { ArrowRightIcon } from '@langchain/untitled-ui-icons';

import { cn } from '@/utils/tailwind';

import { Logo } from '../Logo/Logo';
import { OnboardingCard, OnboardingCardProps } from './OnboardingCard';

export function OnboardingOptions({
  options,
  className,
  onSkip,
}: {
  onSkip: () => void;
  options: OnboardingCardProps[];
  className?: string;
}) {
  return (
    <div
      className={cn(
        className,
        `grid min-h-full w-full grid-rows-[1fr_auto_1fr] items-center justify-items-center gap-12 `
      )}
    >
      <div className="mt-8 flex flex-col items-center gap-10 self-end text-center">
        <Logo size="lg" className="text-brand-primary" />
        <div className="flex flex-col gap-2">
          <h1 className="mb-2 text-5xl font-normal tracking-[-1.92px] text-brand-primary">
            How would you like to start?
          </h1>
          <p className="text-lg font-normal tracking-[-0.72px] text-quaternary">
            Select a workflow to begin exploring <PERSON><PERSON><PERSON>'s capabilities
          </p>
        </div>
      </div>

      <div className="mx-auto grid grid-cols-1 gap-8 self-center px-4 sm:grid-cols-2 sm:gap-6 sm:px-6 min-[1325px]:grid-cols-4 min-[1325px]:gap-6">
        {options.map((option, index) => (
          <OnboardingCard {...option} key={index} />
        ))}
      </div>

      <div className="self-start">
        <button
          type="button"
          onClick={onSkip}
          className="flex items-center gap-1 border-b border-[var(--brand-900)]"
        >
          Skip <ArrowRightIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
