// Import the SVG file
import { useCallback, useEffect, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { useStoredOrganizationId } from '@/hooks/useStoredOrganizationId';
import {
  useCreateOrganization,
  useSessions,
  useTenantList,
} from '@/hooks/useSwr';
import { appOrganizationPath, appProjectsPath } from '@/utils/constants';

import { Logo } from '../Logo/Logo';
import { GettingStarted } from './GettingStarted';
import OnboardingBg from './OnboardingBg.webp';
import { OnboardingCardProps } from './OnboardingCard';
import { OnboardingOptions } from './OnboardingOptions';
import { SetupEvaluation } from './SetupEvaluation';
import { SetupObservability } from './SetupObservability';

enum OnboardingStep {
  GettingStarted = 0,
  Options = 1,
  SetupObservability = 2,
  SetupEvaluation = 3,
}

export function Onboarding() {
  const [searchParams, setSearchParams] = useSearchParams();

  const step = searchParams.get('step')
    ? parseInt(searchParams.get('step') ?? '0')
    : 0;

  const setStep = useCallback(
    (newStep: number) => {
      searchParams.set('step', newStep.toString());
      setSearchParams(searchParams);
    },
    [searchParams, setSearchParams]
  );

  const { trigger: createOrg, isMutating, error } = useCreateOrganization();
  const { setOrgId } = useStoredOrganizationId();
  const navigate = useNavigate();

  const tenantList = useTenantList();
  const newTenant = tenantList.data?.[0];
  const newTenantId = newTenant?.id;

  const sessions = useSessions({});
  const existingSessionId = sessions.data?.rows?.filter(
    (s) => s.name !== 'default'
  )?.[0]?.id;

  // Automatically navigate to the existing session if user follows instruction to send traces.
  useEffect(() => {
    if (existingSessionId && newTenantId) {
      navigate(
        `/${appOrganizationPath}/${newTenantId}/${appProjectsPath}/p/${existingSessionId}`
      );
    } else if (newTenantId && step === OnboardingStep.GettingStarted) {
      setSearchParams({
        organizationId: newTenantId,
        step: OnboardingStep.Options.toString(),
      });
    }
  }, [existingSessionId, newTenantId, navigate, setSearchParams, setStep]);

  const skip = useCallback(() => {
    if (newTenantId) {
      navigate(`/${appOrganizationPath}/${newTenantId}`);
    } else {
      navigate('/');
    }
  }, [newTenantId, navigate]);

  const onboardingOptions = useMemo<OnboardingCardProps[]>(() => {
    return [
      {
        title: 'Trace an Application',
        description: 'Analyze and debug applications using traces.',
        onClick: () => {
          if (newTenantId) {
            setSearchParams({
              organizationId: newTenantId,
              step: OnboardingStep.SetupObservability.toString(),
            });
          }
        },
        helperText: 'Start tracing',
      },
      {
        title: 'Test Prompts in the Playground',
        description:
          'Iterate and test on prompts across any model or provider.',
        onClick: () => {
          if (newTenantId) {
            navigate(`/${appOrganizationPath}/${newTenantId}/playground`);
          }
        },
        helperText: 'Start prompting',
      },
      {
        title: 'Run an Evaluation',
        description:
          'Measure app performance: identify failures, compare changes, ensure reliability.',
        onClick: () => {
          if (newTenantId) {
            setSearchParams({
              organizationId: newTenantId,
              step: OnboardingStep.SetupEvaluation.toString(),
            });
          }
        },
        helperText: 'Start evaluating',
      },
      {
        title: 'Build and Deploy Agents',
        description:
          'Deploy agentic applications to production with LangGraph Platform.',
        onClick: () => {
          if (newTenantId) {
            navigate(`/${appOrganizationPath}/${newTenantId}/host/deployments`);
          }
        },
        helperText: 'Start building',
      },
    ];
  }, [newTenantId, setSearchParams, navigate]);

  return (
    <div
      className="flex h-screen w-full flex-col overflow-y-auto bg-background/20 bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: step === 0 ? `url(${OnboardingBg})` : '',
      }}
    >
      {step === OnboardingStep.GettingStarted && (
        <GettingStarted
          onNext={async () => {
            const newOrg = await createOrg({
              json: {
                display_name: 'Personal',
                is_personal: true,
              },
            });
            if (newOrg) {
              setOrgId(newOrg.id);
            }
          }}
          className={'duration-300 ease-in-out animate-in fade-in'}
          isLoading={isMutating}
          error={error?.message}
        />
      )}
      {step === OnboardingStep.Options && (
        <OnboardingOptions onSkip={skip} options={onboardingOptions} />
      )}
      {step > OnboardingStep.Options && (
        <Logo size="lg" className="absolute ml-6 mt-8" />
      )}
      {step === OnboardingStep.SetupObservability && (
        <SetupObservability
          onNext={skip}
          className={'duration-300 ease-in-out animate-in fade-in'}
        />
      )}
      {step === OnboardingStep.SetupEvaluation && (
        <SetupEvaluation
          className="mt-[60px] duration-300 ease-in-out animate-in fade-in"
          onNext={skip}
        />
      )}
    </div>
  );
}
