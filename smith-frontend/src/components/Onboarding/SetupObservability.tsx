import { useState } from 'react';

import { cn } from '@/utils/tailwind';

import { ButtonSwitchSimple } from '../ButtonSwitch';
import EnvQuickstart from './TracingQuickstart';

export function SetupObservability(props: {
  className?: string;
  onNext?: () => void;
}) {
  const [withLang<PERSON>hain, setWithLangChain] = useState<
    'withLangChain' | 'withoutLangChain'
  >('withLangChain');

  return (
    <div className={cn('h-full overflow-y-auto', props.className)}>
      <div className="flex min-h-full flex-col items-center justify-between gap-4">
        <div className="mt-[60px] flex flex-col items-center justify-center gap-4">
          <p className="mt-4 text-sm uppercase">Get started with Lang<PERSON>mith</p>
          <h1 className="text-4xl tracking-tighter">Set up observability</h1>
          <p className="text-sm">Trace, debug and monitor your application</p>

          <ButtonSwitchSimple<'withLangChain' | 'withoutLangChain'>
            value={withLang<PERSON>hain}
            options={[
              { value: 'withLangChain', label: 'With LangChain' },
              { value: 'withoutLangChain', label: 'Without LangChain' },
            ]}
            onChange={setWithLangChain}
            className="px-4 py-2"
          />

          <div
            className={cn(
              'flex flex-col items-start justify-between rounded-3xl border border-secondary bg-primary p-8 text-left'
            )}
          >
            <EnvQuickstart
              showCreateApiKey={true}
              withLangChain={withLangChain}
            />
          </div>
        </div>
        {props.onNext && (
          <div className="pb-[60px]">
            <button
              type="button"
              className="border-b text-primary"
              onClick={props.onNext}
            >
              Skip for now
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
