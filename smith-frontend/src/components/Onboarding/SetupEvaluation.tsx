import { javascript } from '@codemirror/lang-javascript';
import { python } from '@codemirror/lang-python';
import { LinearProgress } from '@mui/joy';

import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

import { useDataset, useExamples } from '@/hooks/useSwr';
import { getAPIUrl } from '@/utils/get-api-url';
import { cn } from '@/utils/tailwind';

import { ButtonSwitchSimple } from '../ButtonSwitch';
import { Code, CodeCard } from '../Code/Code';
import { CreateApiKey } from './TracingQuickstart';
import {
  SETUP_EVAL_IN_DATASET_CODE_EXAMPLES,
  SETUP_FIRST_EVAL_CODE_EXAMPLES,
} from './constants';

const getConfigEnvCode = (apiKey?: string) => {
  const envLines: string[] = [];
  envLines.push('export LANGSMITH_TRACING=true');
  envLines.push(`export LANGSMITH_ENDPOINT="${getAPIUrl()}"`);
  envLines.push(
    `export LANGSMITH_API_KEY="${apiKey ?? '<your-langsmith-api-key>'}"`
  );
  envLines.push(`export OPENAI_API_KEY="<your-openai-api-key>"`);
  return envLines.join('\n').trim();
};

const firstEvalSections = [
  {
    step: 0,
    title: 'Prerequisites',
    codeKey: 'preRequisites',
  },
  {
    step: 1,
    title: 'Import dependencies',
    codeKey: 'import_dependencies',
  },
  {
    step: 2,
    title: 'Create a dataset',
    codeKey: 'create_dataset',
  },
  {
    step: 3,
    title: "Define what you're evaluating",
    codeKey: 'define_application_logic',
  },
  {
    step: 4,
    title: 'Define evaluator',
    codeKey: 'define_evaluator_logic',
  },
  {
    step: 5,
    title: 'Run and view results',
    codeKey: 'code_run_evals',
  },
];

const inDatasetEvalSections = [
  {
    step: 1,
    title: 'Install dependencies',
    codeKey: 'installDependencies',
  },
  {
    step: 2,
    title: 'Configure environment variables',
    codeKey: 'configureEnvironment',
  },
  {
    step: 3,
    title: 'Import dataset and evaluate',
    codeKey: 'code',
  },
];

function Section({
  step,
  title,
  code,
  language,
  isAccordion,
  newKey,
  setNewKey,
}) {
  const [isOpen, setIsOpen] = useState(true);
  return (
    <div className="flex flex-col">
      {isAccordion ? (
        <div className="flex flex-col">
          <div className="overflow-hidden">
            <button
              type="button"
              onClick={() => setIsOpen(!isOpen)}
              className={cn(
                'flex w-full cursor-pointer items-center justify-between text-left',
                'transition-colors duration-200'
              )}
            >
              <div className="flex items-center space-x-3">
                <h3 className="text-2xl">{title}</h3>
              </div>
              <ChevronDown
                className={cn(
                  'h-5 w-5 transition-transform duration-300',
                  isOpen ? 'rotate-180' : 'rotate-0'
                )}
              />
            </button>
            <div
              className={cn(
                'overflow-hidden transition-all duration-300 ease-in-out',
                isOpen ? 'mt-5 max-h-[1000px]' : 'max-h-0 py-0'
              )}
            >
              <CreateApiKey newKey={newKey} setNewKey={setNewKey} />
              <p className="px-1">
                Install dependencies and configure environment variables. The
                example uses OpenAI, but you can use other LLM providers.
              </p>
              <div className="mt-5 px-1">
                <CodeCard>
                  <Code
                    language={language === 'py' ? python() : javascript()}
                    value={code}
                    readOnly
                    showCopyButton
                  />
                </CodeCard>
              </div>
              <div className="mt-5 px-1 pb-1">
                <CodeCard>
                  <Code
                    language={javascript()}
                    value={getConfigEnvCode(newKey)}
                    readOnly
                    showCopyButton
                  />
                </CodeCard>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-5 pb-1">
          <div className="flex items-center justify-between text-2xl">
            {step}. {title}
          </div>
          <CodeCard>
            <Code
              language={language === 'py' ? python() : javascript()}
              value={code}
              readOnly
              showCopyButton
              omitMaxHeight={true}
            />
          </CodeCard>
        </div>
      )}
    </div>
  );
}

export function SetupEvaluation({
  datasetId,
  className,
  onNext,
}: {
  datasetId?: string;
  className?: string;
  onNext?: () => void;
}) {
  const [language, setLanguage] = useState<'py' | 'ts'>('py');

  const sections = datasetId ? inDatasetEvalSections : firstEvalSections;

  const { data: dataset, isLoading } = useDataset(datasetId);
  const { data: example, isLoading: isExampleLoading } = useExamples(
    datasetId ? { dataset: datasetId, limit: 1 } : null
  );

  const [newKey, setNewKey] = useState<string | null>(null);

  const getDatasetCode = (codeNoDataset: string) => {
    let code = codeNoDataset;

    code = code.replaceAll(
      '{{datasetName}}',
      dataset?.name ?? 'my first dataset'
    );
    if (dataset?.name) {
      if (language === 'py') {
        // Python pattern
        code = code.replace(
          /dataset = client\.clone_public_dataset\([^)]+\)/,
          ``
        );
      } else {
        // TypeScript pattern
        code = code.replace(
          /const dataset = await client\.clonePublicDataset\([^)]+\)/,
          ``
        );
      }
    }
    return code;
  };

  if (isLoading || isExampleLoading) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }

  return (
    <div className={cn(className, 'h-full overflow-y-auto')}>
      <div className="flex min-h-full flex-col items-center justify-between gap-4">
        <div className="flex w-full flex-col items-center justify-center gap-4">
          <p className="mt-4 text-sm uppercase">Get started with evaluation</p>
          <h1 className="text-4xl tracking-tighter">
            {datasetId
              ? 'Run an evaluation with your dataset'
              : 'Run your first evaluation'}
          </h1>
          <p>Build more reliable AI applications with evaluations. </p>
          <div className="flex items-center gap-2">
            <ButtonSwitchSimple
              sx={{ alignSelf: 'flex-start' }}
              value={language}
              onChange={setLanguage}
              options={[
                { value: 'py', label: 'Python' },
                { value: 'ts', label: 'TypeScript' },
              ]}
            />
          </div>
          <div className="flex w-[70%] flex-col gap-10 rounded-3xl border border-secondary p-8">
            {sections.map((section) => {
              let code = (
                datasetId
                  ? SETUP_EVAL_IN_DATASET_CODE_EXAMPLES
                  : SETUP_FIRST_EVAL_CODE_EXAMPLES
              )[language][section.codeKey];
              if (code && example && example[0]) {
                code = code.replace(
                  '["question"]',
                  `["${Object.keys(example[0].inputs)[0]}"]`
                );
              }
              if (section.codeKey === 'configureEnvironment' && datasetId) {
                code = getConfigEnvCode();
              } else if (section.codeKey === 'code' && datasetId) {
                code = getDatasetCode(code);
              }
              return (
                <Section
                  isAccordion={
                    section.codeKey === 'preRequisites' && !datasetId
                  }
                  key={section.codeKey}
                  language={language}
                  {...section}
                  code={code}
                  newKey={newKey}
                  setNewKey={setNewKey}
                />
              );
            })}
            <div className="flex flex-col gap-4">
              <div>
                <p>
                  🎉 Congratulations, on running your first evaluation! Here are
                  some topics you might want to explore next:{' '}
                </p>
              </div>
              <ul className="list-disc space-y-2 pl-5">
                <li>
                  Read the{' '}
                  <a
                    href="https://docs.smith.langchain.com/concepts/evaluation"
                    className="text-brand-secondary"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    evaluation concepts guide
                  </a>{' '}
                  to learn about dataset curation, evaluators, and more.{' '}
                </li>
                <li>
                  Check out the{' '}
                  <a
                    href="https://docs.smith.langchain.com/evaluation/tutorials"
                    className="text-brand-secondary"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    evaluation tutorials
                  </a>{' '}
                  for more guides on running evals.
                </li>
                <li>
                  If you prefer video tutorials, check out the{' '}
                  <a
                    href="https://academy.langchain.com/pages/intro-to-langsmith-preview"
                    className="text-brand-secondary"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Datasets, Evaluators and Experiments{' '}
                  </a>{' '}
                  sections in LangSmith Academy.
                </li>
              </ul>
            </div>
          </div>
        </div>
        {onNext && (
          <div className="pb-[60px]">
            <button
              type="button"
              className="border-b text-primary"
              onClick={onNext}
            >
              Skip for now
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
