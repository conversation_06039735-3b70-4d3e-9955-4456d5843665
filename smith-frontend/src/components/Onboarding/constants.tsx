export const HAS_CREATED_ORG_KEY = 'ls:ui:have_created_org';

export const SETUP_CODE_EXAMPLES = {
  withLangChain: {
    py: {
      installDependencies: `pip install -U langchain langchain-openai`,
      exampleCode: `from langchain_openai import ChatOpenAI

llm = ChatOpenAI()
llm.invoke("Hello, world!")`,
    },
    ts: {
      installDependencies: `npm install -S langchain`,
      exampleCode: `import { ChatOpenAI } from "langchain/chat_models/openai";

const llm = new ChatOpenAI();
await llm.invoke("Hello, world!");`,
    },
  },
  withoutLangChain: {
    py: {
      installDependencies: `pip install -U langsmith`,
      exampleCode: `import openai
from langsmith.wrappers import wrap_openai
from langsmith import traceable

# Auto-trace LLM calls in-context
client = wrap_openai(openai.Client())

@traceable # Auto-trace this function
def pipeline(user_input: str):
    result = client.chat.completions.create(
        messages=[{"role": "user", "content": user_input}],
        model="gpt-3.5-turbo"
    )
    return result.choices[0].message.content

pipeline("Hello, world!")
# Out:  Hello there! How can I assist you today?`,
    },
    ts: {
      installDependencies: `npm install langsmith`,
      exampleCode: `import { OpenAI } from "openai";
import { traceable } from "langsmith/traceable";
import { wrapOpenAI } from "langsmith/wrappers";

// Auto-trace LLM calls in-context
const client = wrapOpenAI(new OpenAI());
// Auto-trace this function
const pipeline = traceable(async (user_input) => {
  const result = await client.chat.completions.create({
    messages: [{ role: "user", content: user_input }],
    model: "gpt-3.5-turbo",
  });
  return result.choices[0].message.content;
});

await pipeline("Hello, world!");
// Out: Hello there! How can I assist you today?`,
    },
  },
};

export const SETUP_FIRST_EVAL_CODE_EXAMPLES = {
  py: {
    preRequisites: `pip install -U langsmith openevals openai`,
    import_dependencies: `from langsmith import Client, wrappers
from openevals.llm import create_llm_as_judge
from openevals.prompts import CORRECTNESS_PROMPT
from openai import OpenAI`,
    create_dataset: `# Define the input and reference output pairs that you'll use to evaluate your app
client = Client()

# Create the dataset
dataset = client.create_dataset(
    dataset_name="Sample dataset", description="A sample dataset in LangSmith."
)

# Create examples in the dataset. Examples consist of inputs and reference outputs 
examples = [
    {
        "inputs": {"question": "Which country is Mount Kilimanjaro located in?"},
        "outputs": {"answer": "Mount Kilimanjaro is located in Tanzania."},
    },
    {
        "inputs": {"question": "What is Earth's lowest point?"},
        "outputs": {"answer": "Earth's lowest point is The Dead Sea."},
    },
]

# Add the examples to the dataset
client.create_examples(dataset_id=dataset.id, examples=examples)`,

    define_evaluator_logic: `# Define an LLM as a judge evaluator to evaluate correctness of the output
# Import a prebuilt evaluator prompt from openevals (https://github.com/langchain-ai/openevals) and create an evaluator.
    
def correctness_evaluator(inputs: dict, outputs: dict, reference_outputs: dict):
    evaluator = create_llm_as_judge(
        prompt=CORRECTNESS_PROMPT,
        model="openai:o3-mini",
        feedback_key="correctness",
    )
    eval_result = evaluator(
        inputs=inputs,
        outputs=outputs,
        reference_outputs=reference_outputs
    )
    return eval_result`,
    define_application_logic: `# Wrap the OpenAI client for LangSmith tracing
openai_client = wrappers.wrap_openai(OpenAI())
      
# Define the application logic you want to evaluate inside a target function. For example, this may be one LLM call that includes the new prompt you are testing, a part of your application or your end to end application
# The SDK will automatically send the inputs from the dataset to your target function
def target(inputs: dict) -> dict:
    response = openai_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "Answer the following question accurately"},
            {"role": "user", "content": inputs["question"]},
        ],
    )
    return { "answer": response.choices[0].message.content.strip() }`,
    code_run_evals: `# After running the evaluation, a link will be provided to view the results in langsmith
experiment_results = client.evaluate(
    target,
    data="Sample dataset",
    evaluators=[
        correctness_evaluator,
        # you can add multiple evaluators here
    ],
    experiment_prefix="first-eval-in-langsmith",
    max_concurrency=2,
)`,
  },
  ts: {
    preRequisites: `npm install langsmith openevals openai`,
    import_dependencies: `import { Client } from "langsmith";
import { createLLMAsJudge, CORRECTNESS_PROMPT } from "openevals";
import { wrapOpenAI } from "langsmith/wrappers";
import OpenAI from "openai";
import { evaluate } from "langsmith/evaluation";`,
    create_dataset: `// Define example input and reference output pairs that you'll use to evaluate your app

const client = new Client();

const dataset = await client.createDataset("Sample dataset", {
  description: "A sample dataset in LangSmith.",
});

// Create inputs and reference outputs
const examples = [
  {
    inputs: { question: "Which country is Mount Kilimanjaro located in?" },
    outputs: { answer: "Mount Kilimanjaro is located in Tanzania." },
    dataset_id: dataset.id,
  },
  {
    inputs: { question: "What is Earth's lowest point?" },
    outputs: { answer: "Earth's lowest point is The Dead Sea." },
    dataset_id: dataset.id,
  },
];

// Add examples to the dataset
await client.createExamples(examples);`,
    define_evaluator_logic: `// Define an LLM as a judge evaluator to evaluate correctness of the output
// Import a prebuilt evaluator prompt from openevals (https://github.com/langchain-ai/openevals) and create an evaluator.

const correctnessEvaluator = async (params: {
  inputs: Record<string, unknown>;
  outputs: Record<string, unknown>;
  referenceOutputs?: Record<string, unknown>;
}) => {
  const evaluator = createLLMAsJudge({
    prompt: CORRECTNESS_PROMPT,
    model: "openai:o3-mini",
    feedbackKey: "correctness",
  });
  const evaluatorResult = await evaluator({
    inputs: params.inputs,
    outputs: params.outputs,
    referenceOutputs: params.referenceOutputs,
  });
  return evaluatorResult;
};`,
    define_application_logic: `// Wrap the OpenAI client for LangSmith tracing
const openai = wrapOpenAI(new OpenAI());

// Define the application logic you want to evaluate inside a target function
// For example, this may be one LLM call that includes the new prompt you are testing, a part of your application or your end to end application

// The SDK will automatically send the inputs from the dataset to your target function
async function target(inputs: {
  question: string;
}): Promise<{ answer: string }> {
  const response = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "Answer the following question accurately" },
      { role: "user", content: inputs.question },
    ],
  });
  return { answer: response.choices[0].message.content?.trim() || "" };
}`,
    code_run_evals: `// After running the evaluation, a link will be provided to view the results in langsmith
await evaluate(target, {
  data: "Sample dataset",
  evaluators: [
    correctnessEvaluator,
    // can add multiple evaluators here
  ],
  experimentPrefix: "first-eval-in-langsmith",
  maxConcurrency: 2,
});`,
  },
};

export const SETUP_EVAL_IN_DATASET_CODE_EXAMPLES = {
  py: {
    installDependencies: `pip install -U langsmith`,
    code: `from langsmith import evaluate, Client

# 1. Create and/or select your dataset
client = Client()
dataset_name = "{{datasetName}}"
dataset = client.clone_public_dataset("https://smith.langchain.com/public/a63525f9-bdf2-4512-83e3-077dc9417f96/d", dataset_name=dataset_name)
# 2. Define an evaluator
def exact_match(outputs: dict, reference_outputs: dict) -> bool:
    return outputs == reference_outputs

# 3. Run an evaluation
# For more info on evaluators, see: https://docs.smith.langchain.com/concepts/evaluation#evaluators

# To evaluate an LCEL chain, replace lambda with chain.invoke
# To evaluate a LangGraph graph, replace lambda with graph.invoke
evaluate(
    lambda x: x["question"] + "is a good question. I don't know the answer.",
    # chain.invoke
    # graph.invoke
    data=dataset_name,
    evaluators=[exact_match],
    experiment_prefix="{{datasetName}} experiment"
)`,
  },
  ts: {
    installDependencies: `npm install -S langsmith`,
    code: `import { evaluate } from "langsmith/evaluation";
import type { EvaluationResult } from "langsmith/evaluation";
import type { Run, Example } from "langsmith/schemas";

// 1. Define a dataset
const datasetName = "{{datasetName}}"
const dataset = await client.clonePublicDataset(
    "https://smith.langchain.com/public/a63525f9-bdf2-4512-83e3-077dc9417f96/d",
    { datasetName: datasetName }
)

// 2. Define an evaluator
function isConcise(rootRun: Run, example?: Example): EvaluationResult {
  const score = rootRun.outputs?.answer.length > example?.outputs?.answer.length;
  return { key: "is_concise", score: score };
}

// 3. Run an evaluation
// For more info on evaluators, see: https://docs.smith.langchain.com/concepts/evaluation#evaluators
await evaluate(
  (exampleInput: { question: string }) => {
    return {
      answer: exampleInput.question + " Good question. I don't know the answer"
    };
  }, {
  data: datasetName,
  evaluators: [isConcise],
  experimentPrefix: "{{datasetName}} experiment",
});`,
  },
};
