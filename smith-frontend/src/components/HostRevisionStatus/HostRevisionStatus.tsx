import { REVISION_STATUS_TO_DISPLAY_NAME } from '@/Pages/HostProject/constants';
import AlertCircleIcon from '@/icons/AlertCircleIcon.svg?react';
import CheckCircleIcon from '@/icons/CheckCircleIcon.svg?react';
import InfoCircleIcon from '@/icons/InfoCircleIcon.svg?react';
import ProgressCircleIcon from '@/icons/ProgressCircleIcon.svg?react';
import { HostRevisionSchema, HostRevisionStatus } from '@/types/schema';
import { cn } from '@/utils/tailwind';

const REVISION_ERROR = [
  'BUILD_FAILED',
  'CREATE_FAILED',
  'DEPLOY_FAILED',
  'INTERRUPTED',
];

export function HostRevisionStatusName(props: {
  status: HostRevisionSchema['status'];
}) {
  if (!props.status) return null;
  return REVISION_STATUS_TO_DISPLAY_NAME[props.status];
}

export function getRevisionSimpleStatus(
  status: HostRevisionStatus
): 'pending' | 'success' | 'error' | 'unknown' {
  if (status === 'DEPLOYED') {
    return 'success';
  }

  if (status && REVISION_ERROR.includes(status)) {
    return 'error';
  }

  if (status === 'UNKNOWN') {
    return 'unknown';
  }

  return 'pending';
}

export function RevisionStatusIcon(props: {
  revision: HostRevisionSchema;
  lastActiveRevisionId: string | undefined | null;
  className?: string;
}) {
  const status = getRevisionSimpleStatus(props.revision.status);
  const isCurrentlyDeployed = props.lastActiveRevisionId === props.revision.id;

  if (status === 'unknown') {
    return (
      <div className="rounded-full bg-secondary p-2 text-tertiary">
        <InfoCircleIcon className={props.className} />
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div
        className={cn('rounded-full', {
          ['bg-success-secondary p-2 text-success']: isCurrentlyDeployed,
          ['bg-secondary p-2 text-tertiary']: !isCurrentlyDeployed,
        })}
      >
        <CheckCircleIcon className={props.className} />
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="rounded-full bg-error-secondary p-2 text-error">
        <AlertCircleIcon className={props.className} />
      </div>
    );
  }

  return (
    <div className="rounded-full bg-brand-tertiary p-2 text-brand-primary">
      <ProgressCircleIcon className={cn('animate-spin', props.className)} />
    </div>
  );
}
