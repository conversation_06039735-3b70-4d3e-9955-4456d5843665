import { useEffect, useRef } from 'react';

import { IFrameTraceRenderingConfig } from '@/hooks/useIFrameTraceRendering';

export const IFrameTraceRenderer = ({
  config,
  payload,
  type,
  height,
  style,
}: {
  config: IFrameTraceRenderingConfig;
  payload: Record<string, any>;
  type: 'output' | 'reference';
  height?: string;
  style?: React.CSSProperties;
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const timeoutRef = useRef<number>();

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const sendMessage = () => {
    let attempts = 0;
    const maxAttempts = 6;
    const baseDelay = 100;

    const trySend = () => {
      if (attempts >= maxAttempts) return;

      iframeRef.current?.contentWindow?.postMessage(
        {
          type,
          data: payload,
        },
        '*'
      );

      attempts++;
      if (attempts < maxAttempts) {
        timeoutRef.current = window.setTimeout(
          trySend,
          baseDelay * Math.pow(2, attempts - 1)
        );
      }
    };

    trySend();
  };

  return (
    <iframe
      ref={iframeRef}
      src={config.url}
      style={style}
      width="100%"
      height={height ?? '600px'}
      title="Trace Renderer"
      sandbox="allow-scripts allow-same-origin"
      onLoad={sendMessage}
      data-output={JSON.stringify(payload)}
    />
  );
};
