import { CheckIcon } from '@langchain/untitled-ui-icons';
import { CircularProgress } from '@mui/joy';

import { useState } from 'react';
import { useDebounce, useDebouncedCallback } from 'use-debounce';

import { lowerCaseIncludes } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from './Command';
import { Popover, PopoverContent, PopoverTrigger } from './Popover';

export function PopoverDropdownMenu<T>(inputProps: {
  children: React.ReactNode;
  values: T[];
  topOfListComponent?: React.ReactNode;
  getTitle: (value: T) => string;
  getRowTitleComponent?: (value: T) => React.ReactNode; // If not provided, we will use getTitle
  getIsSelected: (value: T) => boolean;
  getIsTitleBold?: (value: T) => boolean;
  onSelect: (value: T) => void;
  getKey?: (value: T) => string;
  getIcon?: (value: T) => React.ReactNode;
  getSection?: (value: T) => string;
  getCustomCell?: (
    value: T,
    onSelect: () => void
  ) => React.ReactNode | undefined;
  itemClassName?: string;
  sectionsOrder?: string[];
  maxDropdownHeight?: number;
  headerRightDecorator?: React.ReactNode;

  title?: string;
  titleClassName?: string;
  getSubtitle?: (value: T) => string | null;
  getIsDisabled?: (value: T) => boolean;
  enableSearch?: boolean;
  customSearchComponent?: React.ReactNode;
  setSearchTerm?: (searchTerm: string) => void;
  controlledSearchTerm?: string;
  searchPlaceholder?: string;
  includeSubtitleInSearch?: boolean;
  includeTitleInSearch?: boolean;
  searchInputClassName?: string;
  emptyStateText?: string;
  disableTrigger?: boolean;
  currentSize?: number;
  hasMore?: boolean;
  onInfiniteScroll?: () => void;
  isLoading?: boolean;
  stayOpenOnSelect?: boolean;
  isOpen?: boolean;
  setIsOpen?: (isOpen: boolean) => void;
  align?: 'center' | 'end' | 'start';
  side?: 'top' | 'bottom' | 'left' | 'right';
  alignOffset?: number;
  sideOffset?: number;
  asChild?: boolean;
  showEmptySections?: boolean;
  contentWidth?: string;
  overrideContent?: React.ReactNode;
  getEndDecorator?: (value: T) => React.ReactNode;
  showEndDecoratorBeforeCheck?: boolean;
  getDataTestId?: (value: T) => string; // ADD THIS
}) {
  const { includeTitleInSearch = true, ...props } = inputProps;
  const [isOpenState, setIsOpenState] = useState(false);
  const isOpen = props.isOpen ?? isOpenState;
  const setIsOpen = props.setIsOpen ?? setIsOpenState;
  const [search, setSearch] = useState('');
  const [debouncedSearch] = useDebounce(search, 250, { leading: true });
  const [highestPageFetched, setHighestPageFetched] = useState(0);

  const handleInfiniteScroll = () => {
    if (
      props.hasMore &&
      props.currentSize !== highestPageFetched &&
      props.currentSize != null
    ) {
      props.onInfiniteScroll && props.onInfiniteScroll();
      setHighestPageFetched(props.currentSize);
    }
  };

  const infiniteScrollDebounced = useDebouncedCallback(
    handleInfiniteScroll,
    1000,
    { leading: true }
  );

  const valuesToDisplay =
    props.enableSearch && search !== '' && props.setSearchTerm == null
      ? props.values.filter((value) => {
          const subtitle = props.getSubtitle?.(value);
          return (
            (includeTitleInSearch &&
              lowerCaseIncludes(props.getTitle(value), debouncedSearch)) ||
            (props.includeSubtitleInSearch &&
              subtitle &&
              lowerCaseIncludes(subtitle, debouncedSearch))
          );
        })
      : props.values;

  const sections: { [section: string]: T[] } = {};
  if (props.showEmptySections) {
    props.sectionsOrder?.forEach((section) => {
      sections[section] = [];
    });
  }
  valuesToDisplay.forEach((value) => {
    const section = props.getSection?.(value) ?? '';
    if (!sections[section]) {
      sections[section] = [];
    }
    sections[section].push(value);
  });
  if (Object.keys(sections).length === 0) {
    sections[''] = [];
  }

  const sortedSections = (
    props.sectionsOrder
      ? Object.keys(sections).sort((a, b) => {
          const indexA = props.sectionsOrder?.indexOf(a) ?? -1;
          const indexB = props.sectionsOrder?.indexOf(b) ?? -1;
          return indexA - indexB;
        })
      : Object.keys(sections)
  ).filter(
    (section) =>
      props.showEmptySections || sections[section].length > 0 || section === ''
  );

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger disabled={props.disableTrigger} asChild={props.asChild}>
        {props.children}
      </PopoverTrigger>
      <PopoverContent
        style={{ width: props.contentWidth }}
        alignOffset={props.alignOffset ?? 5}
        align={props.align ?? 'start'}
        className="w-auto p-0"
        side={props.side ?? 'bottom'}
        sideOffset={props.sideOffset ?? 5}
      >
        {props.overrideContent ?? (
          <Command
            shouldFilter={false}
            className={cn(
              'overflow-hidden',
              !props.maxDropdownHeight && 'max-h-[400px]'
            )}
            style={{ maxHeight: props.maxDropdownHeight }}
          >
            <div className="overflow-y-auto overflow-x-hidden">
              {props.title && (
                <div
                  className={cn(
                    'inline-flex w-full items-center justify-between px-3 pt-3 text-sm font-semibold leading-normal',
                    props.titleClassName
                  )}
                >
                  {props.title}
                  {props.headerRightDecorator}
                </div>
              )}
              {props.enableSearch &&
                (props.customSearchComponent ?? (
                  <div className="my-1 px-3">
                    <CommandInput
                      value={props.controlledSearchTerm ?? search}
                      onValueChange={(s) => {
                        setSearch(s);
                        props.setSearchTerm?.(s);
                      }}
                      placeholder={
                        props.searchPlaceholder ?? `Search by name...`
                      }
                      className={cn(
                        'text-sm outline-none',
                        props.searchInputClassName
                      )}
                    />
                  </div>
                ))}
              {props.topOfListComponent}
              <CommandList
                onScroll={(e) => {
                  const { scrollTop, clientHeight, scrollHeight } =
                    e.currentTarget;
                  if (
                    scrollHeight - scrollTop <= clientHeight + 37 &&
                    props.hasMore &&
                    props.currentSize !== highestPageFetched &&
                    props.currentSize != null
                  ) {
                    infiniteScrollDebounced();
                  }
                }}
              >
                {sortedSections.map((section) => (
                  <CommandGroup
                    key={section}
                    className={cn(
                      'm-0 min-w-[250px] max-w-[400px] [&_[cmdk-group-heading]]:bg-secondary [&_[cmdk-group-heading]]:px-5 [&_[cmdk-group-items]]:py-1.5'
                    )}
                    style={{
                      maxWidth: props.contentWidth,
                      minWidth:
                        parseInt(props.contentWidth ?? '0') < 250
                          ? props.contentWidth
                          : '250px',
                    }}
                    heading={
                      section !== '' ? (
                        <span className="text-xs font-medium uppercase tracking-wide text-tertiary">
                          {section}
                        </span>
                      ) : undefined
                    }
                    data-testid="playground-tools-dropdown"
                  >
                    {props.isLoading ? (
                      <CommandItem
                        className="flex items-center justify-center px-0 py-3"
                        disabled={true}
                      >
                        <CircularProgress size={'sm'} />
                      </CommandItem>
                    ) : sections[section].length > 0 ? (
                      <>
                        {sections[section].map((value, idx) => {
                          const isDisabled = props.getIsDisabled
                            ? props.getIsDisabled(value)
                            : false;
                          const handleSelect = () => {
                            !props.stayOpenOnSelect && setIsOpen(false);
                            props.onSelect(value);
                          };
                          return (
                            props.getCustomCell?.(value, handleSelect) ?? (
                              <CommandItem
                                key={props.getKey?.(value) ?? idx}
                                className={cn(
                                  'mx-1.5 flex cursor-pointer flex-col items-center px-0 py-0',
                                  isDisabled && 'cursor-default'
                                )}
                                data-testid={props.getDataTestId?.(value)}
                                onSelect={handleSelect}
                                disabled={isDisabled}
                              >
                                <PopoverDropdownMenuItem
                                  title={
                                    props.getRowTitleComponent?.(value) ??
                                    props.getTitle(value)
                                  }
                                  subtitle={
                                    props.getSubtitle?.(value) ?? undefined
                                  }
                                  disabled={isDisabled}
                                  selected={props.getIsSelected(value)}
                                  titleBold={
                                    props.getIsTitleBold?.(value) ?? true
                                  }
                                  index={idx}
                                  icon={props.getIcon?.(value)}
                                  endDecorator={props.getEndDecorator?.(value)}
                                  showEndDecoratorBeforeCheck={
                                    props.showEndDecoratorBeforeCheck
                                  }
                                  className={props.itemClassName}
                                />
                              </CommandItem>
                            )
                          );
                        })}
                        {props.hasMore && props.onInfiniteScroll && (
                          <CommandItem
                            className="flex items-center justify-center px-0 py-3"
                            onSelect={props.onInfiniteScroll}
                            disabled={true}
                          >
                            <CircularProgress size={'sm'} />
                          </CommandItem>
                        )}
                      </>
                    ) : (
                      <div className="flex h-10 items-center pb-2">
                        <PopoverDropdownMenuItem
                          subtitle={props.emptyStateText ?? `No matches found`}
                          selected={false}
                          titleBold={true}
                        />
                      </div>
                    )}
                  </CommandGroup>
                ))}
              </CommandList>
            </div>
          </Command>
        )}
      </PopoverContent>
    </Popover>
  );
}

export function PopoverDropdownMenuItem(props: {
  title?: string | React.ReactNode;
  subtitle?: string;
  disabled?: boolean;
  selected: boolean;
  titleBold: boolean;
  index?: number;
  icon?: React.ReactNode;
  height?: number;
  endDecorator?: React.ReactNode;
  showEndDecoratorBeforeCheck?: boolean;
  className?: string;
}) {
  return (
    <div
      className={cn(
        'group inline-flex items-center justify-between gap-2 self-stretch rounded-lg py-2 pl-3.5 pr-2.5',
        props.disabled && 'opacity-50',
        props.icon && 'pl-1.5',
        !props.height && 'min-h-[40px]',
        props.className
      )}
      style={{ height: props.height }}
    >
      {props.icon && <div>{props.icon}</div>}
      <div className="inline-flex shrink grow basis-0 flex-col items-start justify-center gap-1 break-words break-all">
        {props.title && (
          <div className="inline-flex items-center justify-start self-stretch">
            <div
              className={cn(
                'line-clamp-1 text-xs leading-normal',
                props.titleBold ? 'font-semibold' : 'font-normal'
              )}
              title={typeof props.title === 'string' ? props.title : undefined}
            >
              {props.title}
            </div>
            {props.index != null && (
              <div className="text-[1px] text-transparent">{props.index}</div>
            )}
          </div>
        )}
        {props.subtitle && (
          <div className="inline-flex items-baseline justify-start gap-1.5 break-all">
            <div className="line-clamp-1 text-xxs font-normal leading-none text-secondary">
              {props.subtitle}
            </div>
          </div>
        )}
      </div>
      {props.showEndDecoratorBeforeCheck && props.endDecorator && (
        <div>{props.endDecorator}</div>
      )}
      {props.selected && (
        <CheckIcon className="size-5 text-[var(--brand-400)]" />
      )}
      {!props.showEndDecoratorBeforeCheck && props.endDecorator && (
        <div>{props.endDecorator}</div>
      )}
    </div>
  );
}
