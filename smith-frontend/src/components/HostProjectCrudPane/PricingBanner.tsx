import { useState } from 'react';

import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { HostProjectSchema } from '@/types/schema';
import { xCount } from '@/utils/stringUtils';

import { Banner } from '../Banner';

export const PricingBanner = ({
  freeDevDeployments,
  defaultExpanded = false,
  closeable = false,
  onClose,
  isCreatingProject = false,
}: {
  freeDevDeployments: HostProjectSchema[] | undefined;
  defaultExpanded?: boolean;
  closeable?: boolean;
  isCreatingProject?: boolean;
  onClose?: () => void;
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);
  const { value: maxFreeLangGraphCloudDeployments } = useOrgConfig(
    OrgConfigs.max_free_langgraph_cloud_deployments
  );
  const numRemainingFreeDevDeployments = freeDevDeployments
    ? (maxFreeLangGraphCloudDeployments as number) - freeDevDeployments.length
    : 0;

  const secondLine = () => {
    if (!isCreatingProject) {
      return (
        <span>
          This deployment will begin incurring charges starting on August 14,
          2025 at 00:00 UTC.
        </span>
      );
    }
    if (numRemainingFreeDevDeployments > 0) {
      return (
        <span>
          You have{' '}
          {xCount(
            'free remaining development deployment',
            numRemainingFreeDevDeployments
          )}
          .
        </span>
      );
    }
  };
  return (
    <Banner
      open={true}
      intent="warning"
      shadow={false}
      closeable={closeable}
      onClose={onClose}
      title="New Pricing Policy in Effect"
      description={
        <div className="flex flex-col gap-2 text-tertiary">
          <span>
            LangGraph Platform is officially out of beta and as a result, the
            pricing policy has been updated. {secondLine()}
          </span>
          {expanded ? (
            <>
              <span>
                There will now be a standby charge per minute for each
                deployment as well as a charge per node executed.
              </span>
              <span>
                Please see{' '}
                <a
                  href="https://www.langchain.com/pricing-langgraph-platform"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline"
                >
                  here
                </a>{' '}
                for more pricing details.
              </span>
              <button
                onClick={() => setExpanded(false)}
                type="button"
                className="w-fit text-primary underline"
              >
                <span>See less</span>
              </button>
            </>
          ) : (
            <button
              onClick={() => setExpanded(true)}
              type="button"
              className="w-fit text-primary underline"
            >
              <span>See more</span>
            </button>
          )}
        </div>
      }
    />
  );
};
