import { Settings01Icon } from '@langchain/untitled-ui-icons';
import Button, { ButtonTypeMap } from '@mui/joy/Button';

import { useState } from 'react';

import { Tooltip } from '@/components/Tooltip/Tooltip.tsx';
import { HostProjectSchema } from '@/types/schema.ts';

import { HostProjectSettingsModal } from './HostProjectSettingsModal';

export function HostProjectSettingsModalWithButton(props: {
  buttonProps?: ButtonTypeMap['props'];
  hostProject: HostProjectSchema;
  deploymentPlatformId: string;
}) {
  const [open, setIsOpen] = useState(false);
  return (
    <>
      <Button
        size="sm"
        variant="plain"
        color="neutral"
        onClick={() => setIsOpen(true)}
      >
        <Tooltip title="Deployment Settings">
          <Settings01Icon />
        </Tooltip>
      </Button>
      {open && (
        <HostProjectSettingsModal
          hostProject={props.hostProject}
          deploymentPlatformId={props.deploymentPlatformId}
          isOpen={open}
          doClose={() => setIsOpen(false)}
        />
      )}
    </>
  );
}
