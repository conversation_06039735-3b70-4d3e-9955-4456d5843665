import AddIcon from '@mui/icons-material/Add';
import Button from '@mui/joy/Button';

import { HostProjectCrudPane } from './HostProjectCrudPane';

export function HostProjectCrudPaneWithButton({
  open,
  setIsOpen,
}: {
  open: boolean;
  setIsOpen: (open: boolean) => void;
}) {
  return (
    <>
      <Button
        size="sm"
        onClick={() => setIsOpen(true)}
        startDecorator={<AddIcon />}
      >
        New Deployment
      </Button>
      <HostProjectCrudPane
        isOpen={open}
        doClose={() => setIsOpen(false)}
        setOpen={setIsOpen}
      />
    </>
  );
}
