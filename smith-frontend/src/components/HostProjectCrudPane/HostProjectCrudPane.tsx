import { ChevronDownIcon, PlusIcon } from '@heroicons/react/24/outline';
import { AlertTriangleIcon } from '@langchain/untitled-ui-icons';
import {
  Checkbox,
  FormControl,
  LinearProgress,
  Modal,
  ModalClose,
  ModalDialog,
} from '@mui/joy';
import Button from '@mui/joy/Button';

import { Fragment, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { useDeploymentFormType } from '@/Pages/HostProjects/hooks/useDeploymentFormType';
import { useDeploymentsInOrg } from '@/Pages/HostProjects/hooks/useDeploymentsInOrg';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import {
  useHostCreateProjectMutation,
  useHostRevisionGithubLink,
  useHostRevisionGithubNamespaces,
  useHostRevisionGithubRepos,
} from '@/hooks/useSwr';
import { useCreateTagsOnResource } from '@/hooks/useTagsOnResource';
import GithubIcon from '@/icons/GithubIcon.svg?react';
import LinkIcon from '@/icons/LinkIcon.svg?react';
import ProjectIcon from '@/icons/ProjectIcon';
import SearchIcon from '@/icons/SearchIcon.svg?react';
import TrashIcon from '@/icons/TrashIcon.svg?react';
import { hostPrivateRepoEnabled } from '@/utils/constants';
import { xCount } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import {
  DeploymentPlatformId,
  HostEnvVarSchema,
  HostGithubNamespaceSchema,
  HostGithubRepoSchema,
  ResourceType,
} from '../../types/schema';
import { singleOriginEnabled } from '../../utils/constants';
import { createName } from '../../utils/create-name';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '../Command';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../Dropdown';
import { ExpandableErrorAlert } from '../ExpandableErrorAlert';
import { HostEnvVarsInput } from '../HostEnvVarsInput/HostEnvVarsInput';
import { Pane } from '../Pane';
import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import { ResourceTagCrudPaneEditor } from '../ResourceTagFilter/ResourceTagCrudPaneEditor';
import { TextField } from '../TextField';
import { PricingBanner } from './PricingBanner';

interface SessionCrudModalProps {
  isOpen: boolean;
  doClose: () => void;
  setOpen: (open: boolean) => void;
}

const createProjectName = createName.bind(null, 'ht');

const DEPLOYMENT_TYPE_DISPLAY_NAMES = {
  dev_free: 'Development (free)',
  dev: 'Development',
  prod: 'Production',
};

function HostProjectGithubNamespaceDropdown(props: {
  namespaces: HostGithubNamespaceSchema[];
  value: HostGithubNamespaceSchema;
  onChange: (value: HostGithubNamespaceSchema) => void;
}) {
  const link = useHostRevisionGithubLink();

  const installUrl = link.data?.install_url;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        asChild
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <button
          className="group flex items-center gap-2 rounded-md border border-secondary bg-background p-1.5 px-2.5"
          type="button"
        >
          <span className="flex min-w-0 flex-shrink grow items-center gap-2 overflow-hidden text-ellipsis text-left">
            {props.value?.name ? (
              <span className="flex items-center gap-2">
                <GithubIcon className="h-6 w-6" />
                <span>{props.value.name}</span>
              </span>
            ) : (
              'Github Provider'
            )}
          </span>
          <ChevronDownIcon className="mr-0.5 h-5 w-5 transition-all group-data-[state=open]:rotate-180" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {props.namespaces?.map((item) => {
          return (
            <DropdownMenuItem
              key={item.id}
              onSelect={() => props.onChange(item)}
              className="flex items-center gap-2"
            >
              <GithubIcon className="h-6 w-6" />
              <span>{item.name}</span>
            </DropdownMenuItem>
          );
        })}

        {installUrl ? (
          <DropdownMenuItem
            key="add"
            onSelect={() => window.open(installUrl, '_blank')}
            className="flex items-center gap-2"
          >
            <PlusIcon className="h-6 w-6" />
            <span>Add new account</span>
          </DropdownMenuItem>
        ) : null}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function HostProjectGithubRepositoryList(props: {
  namespaces: HostGithubNamespaceSchema[];
  value: {
    namespace: HostGithubNamespaceSchema;
    repository: HostGithubRepoSchema;
  } | null;
  onChange: (value: {
    namespace: HostGithubNamespaceSchema;
    repository: HostGithubRepoSchema;
  }) => void;
}) {
  const [namespace, setNamespace] = useState<HostGithubNamespaceSchema>(
    props.namespaces[0]
  );

  const repos = useHostRevisionGithubRepos(namespace.id, {
    keepPreviousData: false,
  });
  const [open, setOpen] = useState(false);

  return (
    <div className="grid grid-cols-[auto,1fr] gap-2">
      <HostProjectGithubNamespaceDropdown
        namespaces={props.namespaces}
        value={namespace}
        onChange={(newNamespace: HostGithubNamespaceSchema) => {
          if (newNamespace.id !== namespace.id) {
            repos.mutate(undefined);
            setNamespace(newNamespace);
          }
        }}
      />
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <button
            type="button"
            className="flex items-center gap-3 rounded-md border border-secondary bg-background px-3 py-2 text-left"
          >
            <SearchIcon />
            <span>Select a repo... </span>
          </button>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="flex min-w-[420px] flex-col gap-0 p-0"
        >
          <Command className="bg-transparent">
            <CommandInput className="m-2 mb-0" />

            <CommandList>
              {repos.data != null ? (
                <>
                  <CommandGroup className="m-0 max-h-[50vh] overflow-auto p-2">
                    {repos.data?.map((repository) => {
                      return (
                        <CommandItem
                          key={repository.id}
                          className="flex items-center gap-2"
                          onSelect={() => {
                            props.onChange({ namespace, repository });
                            setOpen(false);
                          }}
                        >
                          <GithubIcon className="h-6 w-6 flex-shrink-0" />
                          <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                            {repository.name}
                          </span>
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>

                  <CommandEmpty className="py-4 text-center text-tertiary">
                    No repositories found
                  </CommandEmpty>
                </>
              ) : (
                <span className="py-4 pl-2 text-center text-tertiary">
                  Loading...
                </span>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

function HostProjectGithubIntegration(props: {
  onChange: (value: {
    namespace: HostGithubNamespaceSchema;
    repository: HostGithubRepoSchema;
  }) => void;
}) {
  const link = useHostRevisionGithubLink();
  const ns = useHostRevisionGithubNamespaces();

  const [state, setState] = useState<{
    namespace: HostGithubNamespaceSchema;
    repository: HostGithubRepoSchema;
  } | null>(null);

  if (!ns.data) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }

  if (ns.data.length === 0) {
    return (
      <div className="flex flex-col gap-2">
        <a
          className="flex items-center justify-center gap-2 rounded-lg bg-gray-950 px-3 py-3 text-center text-white transition-colors hover:bg-gray-800 active:bg-gray-900"
          href={link.data?.install_url}
          target="_blank"
        >
          <GithubIcon className="h-6 w-6" />
          <span className="font-medium">Import with Github</span>
        </a>
      </div>
    );
  }

  if (state != null) {
    return (
      <div className="flex items-center gap-2 rounded-lg border border-secondary bg-background p-2 pr-3">
        <GithubIcon className="h-10 w-10 flex-shrink-0" />

        <div className="flex min-w-0 flex-grow flex-col items-start overflow-hidden">
          <span className="flex-grow overflow-hidden text-ellipsis">
            {state.repository.owner}/{state.repository.name}
          </span>
          <a
            href={state.repository.url}
            target="_blank"
            rel="noreferrer noopener"
            className="text-sm text-tertiary hover:underline"
          >
            {state.repository.url}
          </a>
        </div>
        <button
          type="button"
          onClick={() => setState(null)}
          className="rounded-md p-1 transition-colors hover:bg-secondary-hover active:bg-secondary"
        >
          <TrashIcon className="h-5 w-5 text-tertiary" />
        </button>
      </div>
    );
  }

  return (
    <HostProjectGithubRepositoryList
      namespaces={ns.data}
      value={state}
      onChange={(value) => {
        setState(value);
        props.onChange(value);
      }}
    />
  );
}

export function HostProjectDeploymentTypeDropdown(props: {
  deploymentType: string;
  onChange: (deploymentType: string) => void;
  className?: string;
}) {
  const { value: maxFreeLangGraphCloudDeployments } = useOrgConfig(
    OrgConfigs.max_free_langgraph_cloud_deployments
  );

  const { deploymentsInOrg } = useDeploymentsInOrg();
  const freeDevDeployments = deploymentsInOrg?.filter(
    (d) => d.metadata.deployment_type === 'dev_free'
  );
  const numRemainingFreeDevDeployments = freeDevDeployments
    ? (maxFreeLangGraphCloudDeployments as number) - freeDevDeployments.length
    : 0;
  const deploymentTypes = {
    dev_free: {
      displayName: DEPLOYMENT_TYPE_DISPLAY_NAMES.dev_free,
      subtext: `Your organization has ${xCount(
        'free Development deployment',
        maxFreeLangGraphCloudDeployments as number
      )} remaining`,
    },
    dev: {
      displayName: DEPLOYMENT_TYPE_DISPLAY_NAMES.dev,
      subtext:
        '1 container (1 CPU, 1 GB memory), 10 GB of disk capacity, no storage backups',
    },
    prod: {
      displayName: DEPLOYMENT_TYPE_DISPLAY_NAMES.prod,
      subtext:
        'Autoscales up to 10 containers (2 CPU, 2 GB memory), highly available storage with automated backups and autoscaling disk capacity',
    },
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        asChild
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <button
          className={cn(
            'group flex items-center gap-2 rounded-md border border-secondary bg-background p-1.5 px-2.5',
            props.className
          )}
          type="button"
        >
          <span className="flex min-w-0 flex-shrink grow flex-col items-start overflow-hidden text-ellipsis text-left">
            <span>{deploymentTypes[props.deploymentType].displayName}</span>
            <span className="text-sm text-tertiary">
              {deploymentTypes[props.deploymentType].subtext}
            </span>
          </span>
          <ChevronDownIcon className="mr-0.5 h-5 w-5 transition-all group-data-[state=open]:rotate-180" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {Object.entries(deploymentTypes)
          .filter(([deploymentType, _]) => {
            if (
              deploymentType === 'dev_free' &&
              (!maxFreeLangGraphCloudDeployments ||
                numRemainingFreeDevDeployments <= 0)
            ) {
              return false;
            } else {
              return true;
            }
          })
          .map(([deploymentType, details]) => {
            return (
              <DropdownMenuItem
                key={deploymentType}
                onSelect={() => props.onChange(deploymentType)}
                className="flex items-center gap-2"
              >
                <span>
                  <span className="text-md">{details.displayName}</span>
                  <br />
                  <span className="text-sm text-tertiary">
                    {details.subtext}
                  </span>
                </span>
              </DropdownMenuItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function HostProjectCrudPane({
  isOpen,
  doClose,
  setOpen,
}: SessionCrudModalProps) {
  const { value: langGraphDeployOwnCloudEnabled } = useOrgConfig(
    OrgConfigs.langgraph_deploy_own_cloud_enabled
  );
  const { value: langGraphRemoteReconcilerEnabled } = useOrgConfig(
    OrgConfigs.langgraph_remote_reconciler_enabled
  );
  const { value: maxFreeLangGraphCloudDeployments } = useOrgConfig(
    OrgConfigs.max_free_langgraph_cloud_deployments
  );
  const { deploymentsInOrg } = useDeploymentsInOrg();
  const freeDevDeployments = useMemo(
    () =>
      deploymentsInOrg?.filter(
        (d) => d.metadata.deployment_type === 'dev_free'
      ),
    [deploymentsInOrg]
  );
  const numRemainingFreeDevDeployments = freeDevDeployments
    ? (maxFreeLangGraphCloudDeployments as number) - freeDevDeployments.length
    : 0;
  const navigate = useNavigate();
  const [hostIntegrationId, setHostIntegrationId] = useState<string | null>(
    null
  );
  const [projectName, setProjectName] = useState<string>(createProjectName);
  const [projectNameHelperText, setProjectNameHelperText] =
    useState<string>('');
  const [AWSRegionHelperText, setAWSRegionHelperText] = useState<string>('');
  const [AWSAccountIDHelperText, setAWSAccountIDHelperText] =
    useState<string>('');
  const [imagePathHelperText, setImagePathHelperText] = useState<string>('');
  const [repoUrl, setRepoUrl] = useState<string>();
  const [repoPath, setRepoPath] = useState<string | undefined>(
    singleOriginEnabled === '1' ||
      langGraphRemoteReconcilerEnabled ||
      langGraphDeployOwnCloudEnabled
      ? undefined
      : 'langgraph.json'
  );
  const [repoCommit, setRepoCommit] = useState<string | undefined>(
    singleOriginEnabled === '1' ||
      langGraphRemoteReconcilerEnabled ||
      langGraphDeployOwnCloudEnabled
      ? undefined
      : 'main'
  );
  const [buildOnPush, setBuildOnPush] = useState<boolean>(false);
  const [envVars, setEnvVars] = useState<HostEnvVarSchema[]>([]);
  const [deploymentType, setDeploymentType] = useState<string>('dev');
  const [shareable, setShareable] = useState<boolean>(false);
  const [imagePath, setImagePath] = useState<string>();
  const [awsRegion, setAWSRegion] = useState<string>(
    singleOriginEnabled === '1' || langGraphDeployOwnCloudEnabled
      ? 'us-west-1'
      : ''
  );
  const [awsAccountID, setAWSAccountID] = useState<string>();
  const [isPublic, setIsPublic] = useState<boolean>(false);
  const [cpuPerContainer, setCpuPerContainer] = useState<number | undefined>(
    undefined
  );
  const [memMBPerContainer, setMemMBPerContainer] = useState<
    number | undefined
  >(undefined);
  const [minScale, setMinScale] = useState<number | undefined>(undefined);
  const [maxScale, setMaxScale] = useState<number | undefined>(undefined);

  const create = useHostCreateProjectMutation();

  useEffect(() => {
    if (
      numRemainingFreeDevDeployments > 0 &&
      deploymentType === 'dev' &&
      isOpen
    ) {
      setDeploymentType('dev_free');
    } else if (
      numRemainingFreeDevDeployments <= 0 &&
      deploymentType === 'dev_free'
    ) {
      setDeploymentType('dev');
    }
  }, [numRemainingFreeDevDeployments, isOpen]);

  const {
    trigger: createTagsOnResource,
    tagsOnResource,
    setTagsOnResource,
  } = useCreateTagsOnResource(ResourceType.Deployment);

  const deploymentFormType = useDeploymentFormType();

  const handleConfirmSubmit = () => {
    if (deploymentType !== 'dev_free' && deploymentFormType === 'cloud') {
      handleShowPricingConfirmation();
    } else {
      handleSubmit();
    }
  };
  const handleSubmit = async () => {
    try {
      const project = await create.trigger({
        name: projectName,
        lc_hosted:
          singleOriginEnabled === '1' || langGraphRemoteReconcilerEnabled
            ? false
            : !langGraphDeployOwnCloudEnabled,
        repo_url: repoUrl ?? undefined,
        repo_path: repoPath ?? undefined,
        repo_commit: repoCommit ?? undefined,
        env_vars: envVars.filter((envVar) => envVar.name && envVar.value),
        host_integration_id: hostIntegrationId ?? undefined,
        deployment_type: deploymentType,
        shareable,
        image_path: imagePath ?? undefined,
        build_on_push: buildOnPush,
        platform: awsAccountID
          ? {
              deployment_platform: 'aws_ecs' as DeploymentPlatformId,
              region: awsRegion,
              aws_account_id: awsAccountID,
              public: isPublic,
            }
          : undefined,
        container_spec: {
          cpu: cpuPerContainer,
          memory_mb: memMBPerContainer,
          min_scale: minScale,
          max_scale: maxScale,
        },
      });

      if (project) {
        await createTagsOnResource(project.id);
        navigate(project.id);
      }
    } catch (error) {
      if (!isOpen) {
        setShowPricingModal(false);
        setOpen(true);
      }
    }
  };

  const handleProjectNameOnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const projectName = e.target.value;
    setProjectName(projectName);
    setProjectNameHelperText('');

    const projectNameRegex = /^[a-zA-Z][a-zA-Z0-9-]+$/;

    // project name validation
    if (!projectName) {
      setProjectNameHelperText('This field is required');
    } else if (projectName.length > 63) {
      setProjectNameHelperText('Name must be 63 characters or less');
    } else if (!projectNameRegex.test(projectName)) {
      setProjectNameHelperText(
        'Name must only contain alphanumeric characters and dashes and must start with an alphabetic character'
      );
    } else if (projectName.startsWith('-') || projectName.endsWith('-')) {
      setProjectNameHelperText('Name cannot start or end with a dash');
    }
  };

  const handleAWSRegionOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const awsRegion = e.target.value;
    setAWSRegion(awsRegion);
    setAWSRegionHelperText('');

    // aws region validation
    if (!awsRegion) {
      setAWSRegionHelperText('This field is required');
    }
  };

  const handleAWSAccountIDOnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const awsAccountId = e.target.value;
    setAWSAccountID(awsAccountId);
    setAWSAccountIDHelperText('');

    // aws account ID validation
    if (!awsAccountId) {
      setAWSAccountIDHelperText('This field is required');
    } else if (!/^[0-9]{12}$/.test(awsAccountId)) {
      setAWSAccountIDHelperText('AWS Account ID must be exactly 12 digits');
    }
  };

  const handleImagePathOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const imagePath = e.target.value;
    setImagePath(imagePath);
    setImagePathHelperText('');

    // image path validation
    if (!imagePath) {
      setImagePathHelperText('This field is required');
    }
  };

  const handlePublicOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isPublic = e.target.checked;
    setIsPublic(isPublic);
  };

  const [showPricingModal, setShowPricingModal] = useState(false);

  const handleShowPricingConfirmation = () => {
    setShowPricingModal(true);
    doClose();
  };

  const cancelAndSubmitButtons = (
    <div className="mr-4 flex items-center gap-2">
      <div>
        <Button
          type={'button'}
          onClick={doClose}
          color="neutral"
          variant="outlined"
        >
          Cancel
        </Button>
      </div>
      <div>
        <Button
          type="submit"
          color="primary"
          loading={create.isMutating}
          onClick={handleConfirmSubmit}
        >
          Submit
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <Pane
        open={isOpen}
        onClose={doClose}
        title="Create deployment"
        topBarRightElement={cancelAndSubmitButtons}
      >
        <div className="flex flex-col gap-1">
          <h2 className="text-2xl font-semibold">Create New Deployment</h2>
          <p className="text-sm text-tertiary">
            Launch a server that's publicly available in one-click
          </p>
        </div>

        <div className="h-4" />

        {create.error && <ExpandableErrorAlert error={create.error} />}

        {deploymentFormType === 'cloud' && (
          <PricingBanner
            freeDevDeployments={freeDevDeployments}
            isCreatingProject={true}
          />
        )}

        <form
          className="flex flex-col gap-8"
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleConfirmSubmit();
          }}
        >
          <div className="grid grid-cols-[minmax(120px,1fr),minmax(auto,800px),1fr] gap-x-8 gap-y-5">
            <hr className="col-start-1 col-end-4 border-tertiary" />

            {deploymentFormType === 'self_hosted' && (
              <Fragment>
                <div className="flex flex-col items-start">
                  <strong className="font-semibold">Deployment details</strong>
                  <span className="text-sm text-tertiary">
                    Name the deployment and specify the Docker image path
                  </span>
                </div>
                <div className="flex flex-col gap-4">
                  <TextField
                    label="Name"
                    helperText={projectNameHelperText}
                    onChange={handleProjectNameOnChange}
                    placeholder="Type name..."
                    value={projectName}
                    required
                    error={projectNameHelperText !== ''}
                  />
                  <TextField
                    label="Image Path"
                    helperText={imagePathHelperText}
                    onChange={handleImagePathOnChange}
                    value={imagePath ?? ''}
                    required
                    error={imagePathHelperText !== ''}
                  />
                </div>
                <div />
              </Fragment>
            )}
            {deploymentFormType === 'byoc_aws' && (
              <Fragment>
                <div className="flex flex-col items-start">
                  <strong className="font-semibold">Deployment details</strong>
                  <span className="text-sm text-tertiary">
                    Name it and link to AWS
                  </span>
                </div>
                <div className="flex flex-col gap-4">
                  <TextField
                    label="Name"
                    helperText={projectNameHelperText}
                    onChange={handleProjectNameOnChange}
                    placeholder="Type name..."
                    value={projectName}
                    required
                    error={projectNameHelperText !== ''}
                  />

                  <TextField
                    label="AWS Region"
                    helperText={AWSRegionHelperText}
                    onChange={handleAWSRegionOnChange}
                    value={awsRegion ?? ''}
                    required
                    error={AWSRegionHelperText !== ''}
                  />
                  <TextField
                    label="AWS Account ID"
                    helperText={AWSAccountIDHelperText}
                    onChange={handleAWSAccountIDOnChange}
                    value={awsAccountID ?? ''}
                    required
                    error={AWSAccountIDHelperText !== ''}
                  />
                  <TextField
                    label="Image Path"
                    helperText={imagePathHelperText}
                    onChange={handleImagePathOnChange}
                    value={imagePath ?? ''}
                    required
                    error={imagePathHelperText !== ''}
                  />
                  <Checkbox
                    checked={isPublic}
                    onChange={handlePublicOnChange}
                    label={
                      <span>
                        Publicly accessible (Create an internet-facing load
                        balancer). Will use LangSmith for{' '}
                        <a
                          href="https://langchain-ai.github.io/langgraph/concepts/auth/#langgraph-cloud"
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ color: 'blue', textDecoration: 'underline' }}
                        >
                          auth.
                        </a>
                      </span>
                    }
                  />
                </div>
                <div />
              </Fragment>
            )}
            {deploymentFormType === 'cloud' && (
              <Fragment>
                <div className="flex flex-col items-start">
                  <strong className="font-semibold">Deployment details</strong>
                  <p className="text-sm text-tertiary">
                    Name it and link to the GitHub repository and branch.
                  </p>
                  <br />
                  <p className="text-sm text-tertiary">
                    Deployments can be updated automatically when changes are
                    pushed to the branch.
                  </p>
                </div>
                <div className="flex flex-col gap-4">
                  {hostPrivateRepoEnabled === '1' ? (
                    <HostProjectGithubIntegration
                      onChange={({ namespace, repository }) => {
                        setRepoUrl(repository.url);
                        setRepoCommit(repository.default_branch);
                        setHostIntegrationId(namespace.id);
                      }}
                    />
                  ) : (
                    <TextField
                      label="GitHub Repo URL"
                      helperText={!repoUrl && 'This field is required'}
                      startDecorator={<LinkIcon className="h-4 w-4" />}
                      onChange={(e) => {
                        try {
                          const url = new URL(e.target.value);
                          if (url.hostname !== 'github.com') {
                            return setRepoUrl(e.target.value);
                          }

                          let pathname = url.pathname;
                          if (pathname.startsWith('/'))
                            pathname = pathname.slice(1);
                          const segments = pathname.split('/');

                          const suggestedName = segments[1];
                          if (!projectName && suggestedName) {
                            setProjectName(suggestedName);
                          }

                          if (segments[2] === 'tree' && segments.length > 3) {
                            const [suggestedGitRef, ...path] =
                              segments.slice(3);
                            const suggestedSubdirectory = path.join('/') || '.';

                            if (repoCommit === 'main' && suggestedGitRef) {
                              setRepoCommit(suggestedGitRef);
                            }

                            if (repoPath === '.' && suggestedSubdirectory) {
                              setRepoPath(suggestedSubdirectory);
                            }
                          }

                          if (!repoUrl) {
                            url.pathname = `/` + segments.slice(0, 2).join('/');
                            setRepoUrl(url.toString());
                          } else {
                            setRepoUrl(e.target.value);
                          }
                        } catch {
                          setRepoUrl(e.target.value);
                        }
                      }}
                      placeholder="Paste URL..."
                      value={repoUrl}
                      required
                    />
                  )}

                  <TextField
                    label="Name"
                    helperText={projectNameHelperText}
                    onChange={handleProjectNameOnChange}
                    placeholder="Type name..."
                    value={projectName}
                    required
                    error={projectNameHelperText !== ''}
                  />

                  <TextField
                    label="Git Branch"
                    onChange={(e) => setRepoCommit(e.target.value)}
                    value={repoCommit ?? ''}
                  />
                  <TextField
                    label="LangGraph API config file"
                    onChange={(e) => setRepoPath(e.target.value)}
                    value={repoPath ?? ''}
                  />
                  <Checkbox
                    checked={buildOnPush}
                    onChange={(e) => setBuildOnPush(e.target.checked)}
                    label="Automatically update deployment on push to branch"
                  />

                  <FormControl
                    required={false}
                    size={'md'}
                    color={'primary'}
                    error={tagsOnResource.length === 0}
                  >
                    <ResourceTagCrudPaneEditor
                      tagsOnResource={tagsOnResource}
                      setTagsOnResource={setTagsOnResource}
                    />
                  </FormControl>
                </div>
                <div />
              </Fragment>
            )}

            <hr className="col-start-1 col-end-4 border-tertiary" />

            {deploymentFormType !== 'self_hosted' ? (
              // Cloud SaaS deployments can only specify deployment types (dev, prod)
              <>
                <Fragment>
                  <div className="flex flex-col items-start">
                    <strong className="font-semibold">Deployment type</strong>
                    <p className="text-sm text-tertiary">
                      Deployment type cannot be changed.
                    </p>
                    <br />
                    <p className="text-sm text-tertiary">
                      Shareable deployments can be shared with LangSmith users
                      outside of your organization.
                    </p>
                  </div>
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col gap-2">
                      <HostProjectDeploymentTypeDropdown
                        deploymentType={deploymentType}
                        onChange={setDeploymentType}
                      />
                      <span className="text-sm text-tertiary">
                        For custom deployment types, please reach out to{' '}
                        <a
                          href="mailto:<EMAIL>"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline"
                        >
                          <EMAIL>
                        </a>
                      </span>
                    </div>
                    <Checkbox
                      checked={shareable}
                      onChange={(e) => setShareable(e.target.checked)}
                      label="Shareable through LangGraph Studio"
                    />
                  </div>
                  <div />
                </Fragment>

                <hr className="col-start-1 col-end-4 border-tertiary" />
              </>
            ) : (
              // Self-hosted deployments can only specify container resources
              <>
                <Fragment>
                  <div className="flex flex-col items-start">
                    <strong className="font-semibold">
                      Container Resources
                    </strong>
                    <p className="text-sm text-tertiary">
                      Specify CPU and memory requirements for each container and
                      the mininum and maximum number of containers (scale) for
                      your deployment.
                    </p>
                    <br />
                    <p className="text-sm text-tertiary">
                      Default values will be used if not specified.
                    </p>
                  </div>
                  <div className="flex flex-col gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <TextField
                        label="CPU per container"
                        type="number"
                        onChange={(e) =>
                          setCpuPerContainer(parseInt(e.target.value, 10))
                        }
                        value={cpuPerContainer ?? undefined}
                        placeholder="1"
                      />
                      <TextField
                        label="Memory (MB) per container"
                        type="number"
                        onChange={(e) =>
                          setMemMBPerContainer(parseInt(e.target.value, 10))
                        }
                        value={memMBPerContainer ?? undefined}
                        placeholder="1024"
                      />
                      <TextField
                        label="Minimum scale"
                        type="number"
                        onChange={(e) =>
                          setMinScale(parseInt(e.target.value, 10))
                        }
                        value={minScale ?? undefined}
                        placeholder="1"
                      />
                      <TextField
                        label="Maximum scale"
                        type="number"
                        onChange={(e) =>
                          setMaxScale(parseInt(e.target.value, 10))
                        }
                        value={maxScale ?? undefined}
                        placeholder="1"
                      />
                    </div>
                  </div>
                </Fragment>

                <hr className="col-start-1 col-end-4 border-tertiary" />
              </>
            )}

            <div className="flex flex-col items-start">
              <strong className="font-semibold">Environment Variables</strong>

              <div className="flex flex-col gap-2">
                <p className="text-sm text-tertiary">
                  Use environment variables to specify API keys to LLM providers
                  and other services.
                </p>
                <p className="text-sm text-tertiary">
                  <strong className="font-bold">TIP</strong>: All environment
                  variables will be stored as secrets. You can paste the
                  <strong> .env</strong> file contents in the name field.
                </p>
              </div>
            </div>
            <div className="flex flex-col gap-4">
              <HostEnvVarsInput value={envVars} onChange={setEnvVars} />
            </div>
            <div />

            <hr className="col-start-1 col-end-4 border-tertiary" />

            <div className="flex flex-col items-start">
              <strong className="font-semibold">Tracing Project</strong>
              <span className="text-sm text-tertiary">
                Each deployment automatically creates a tracing project
                containing all its traces.
              </span>
            </div>
            <div className="flex items-center gap-4 rounded-xl bg-secondary-hover px-6 py-4">
              <div className="rounded bg-brand-tertiary p-1 text-brand-green-400">
                <ProjectIcon />
              </div>
              <div className="flex flex-col">
                <span className="font-semibold">{projectName}</span>
                <span className="text-sm text-tertiary">
                  Tracing project for deployment
                </span>
              </div>
            </div>
            <div />
          </div>
        </form>
      </Pane>
      <Modal open={showPricingModal} onClose={() => setShowPricingModal(false)}>
        <ModalDialog style={{ maxWidth: '550px' }}>
          <div className="flex flex-col gap-5 text-tertiary">
            <div className="flex items-center justify-between">
              <div className="flex w-fit items-center gap-2 rounded-full bg-warning-secondary p-2 text-warning">
                <AlertTriangleIcon />
              </div>
              <ModalClose />
            </div>
            <div className="flex flex-col gap-1">
              <span className="font-semibold text-primary">
                Proceeding will create a paid deployment
              </span>
              <span className="text-sm text-tertiary">
                LangGraph Platform is officially out of beta. As a result, the
                pricing policy has been updated.
              </span>
            </div>
            <ul className="list-disc pl-4 text-sm text-secondary">
              <li className="font-semibold">
                {DEPLOYMENT_TYPE_DISPLAY_NAMES[deploymentType]} deployments are
                no longer free
              </li>
              <li className="py-4">
                You will be charged for standby time (per minute) and node
                executions
              </li>
            </ul>
            <a
              href="https://www.langchain.com/pricing-langgraph-platform"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm font-medium text-primary underline"
            >
              Learn about pricing
            </a>
          </div>
          <div className="flex flex-row justify-end gap-2 pt-8">
            <Button
              variant="outlined"
              color="neutral"
              onClick={() => {
                setShowPricingModal(false);
                setOpen(true);
              }}
            >
              Back
            </Button>
            <Button onClick={handleSubmit} loading={create.isMutating}>
              Create
            </Button>
          </div>
        </ModalDialog>
      </Modal>
    </>
  );
}
