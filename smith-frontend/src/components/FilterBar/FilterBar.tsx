import {
  CheckIcon,
  ChevronDownIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { FilterLinesIcon } from '@langchain/untitled-ui-icons';
import { Tooltip } from '@mui/joy';

import { useMemo, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';

import {
  OPENAI_MODEL_MANIFEST,
  useTenantSecrets,
} from '@/Pages/Playground/PlaygroundPrompt/hooks/useSecrets';
import { EvaluatorSecrets } from '@/Pages/Settings/components/EvaluatorSecrets';
import {
  InstanceFlags,
  useInstanceFlagOrDefault,
} from '@/hooks/useInstanceFlag';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import {
  useCreateFilterViewMutation,
  useGetFilterView,
  useRunStats,
  useUpdateFilterViewMutation,
} from '@/hooks/useSwr';
import CopyIcon from '@/icons/CopyIcon.svg?react';
import FilterIcon from '@/icons/FilterIcon.svg?react';
import InfoCircleIcon from '@/icons/InfoCircleIcon.svg?react';
import {
  FilterViewSchema,
  FilterViewType,
  RunSchema,
  RunStatsSchema,
} from '@/types/schema';
import { xCount } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import { CopyIconButton } from '../CopyButton/CopyButton';
import { useCopyAction } from '../CopyButton/useCopy';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../Dropdown';
import { ExpandableErrorAlert } from '../ExpandableErrorAlert';
import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import {
  astToString,
  findComparsion,
  flattenAst,
  stringToAst,
} from '../RunsTable/filter/ast';
import { useFilterBarOpen } from '../RunsTable/hooks/useFilterBarOpen';
import { SearchModel } from '../RunsTable/types';
import { Switch } from '../Switch/Switch';
import {
  getActiveFieldsCount,
  normalizeFilterString,
  useFilterAst,
} from './FilterBar.utils';
import {
  ClearFilterButton,
  DeleteSavedFilterButton,
} from './components/DeleteFilterButton';
import {
  EditFilterPopover,
  SaveFilterForm,
  SaveFilterFormData,
} from './components/EditFilterButton';
import { FilterBarGeneratedInput } from './components/FilterBarGeneratedInput';
import { FilterBarOperands } from './components/FilterBarOperands';
import { FilterBarRawInput } from './components/FilterBarRawInput';
import { FilterLabel } from './constants';
import { getRunFields } from './fields/runs';

const pluralRules = new Intl.PluralRules('en-US');

export function SaveFilterPopover(props: {
  filter?: string;
  traceFilter?: string;
  treeFilter?: string;
  filterView?: FilterViewSchema;
  onViewChange?: (
    view: 'llm' | 'all' | 'trace' | string,
    maybeFilter?: string,
    maybeTraceFilter?: string,
    maybeTreeFilter?: string
  ) => void;
  isLoading: boolean;
  saveFilterType: FilterViewType;
}) {
  const { sessionId } = useParams();

  const [isOpen, setIsOpen] = useState(false);
  const [showSaveForm, setShowSaveForm] = useState(false);

  const createFilter = useCreateFilterViewMutation(sessionId);

  const updateFilter = useUpdateFilterViewMutation(
    sessionId,
    props.filterView?.id
  );

  const hasChange =
    props.filterView &&
    (normalizeFilterString(props.filter) !==
      normalizeFilterString(props.filterView.filter_string) ||
      normalizeFilterString(props.traceFilter) !==
        normalizeFilterString(props.filterView.trace_filter_string) ||
      normalizeFilterString(props.treeFilter) !==
        normalizeFilterString(props.filterView.tree_filter_string));

  const handleSave = () => {
    if (
      sessionId &&
      (props.filter || props.traceFilter || props.treeFilter) &&
      props.filterView?.id
    ) {
      updateFilter.trigger({
        json: {
          filter_string: props.filter,
          trace_filter_string: props.traceFilter,
          tree_filter_string: props.treeFilter,
        },
      });
      setIsOpen(false);
    }
  };
  const handleCreate = async ({ name, description }: SaveFilterFormData) => {
    if (sessionId && (props.filter || props.traceFilter || props.treeFilter)) {
      const resp = await createFilter.trigger({
        json: {
          display_name: name,
          description,
          filter_string: props.filter,
          trace_filter_string: props.traceFilter,
          tree_filter_string: props.treeFilter,
          type: props.saveFilterType,
        },
      });
      props.onViewChange?.(
        resp?.id ?? '',
        resp?.filter_string,
        resp?.trace_filter_string,
        resp?.tree_filter_string
      );
      setIsOpen(false);
      setShowSaveForm(false);
    }
  };

  const handleSaveAs = () => {
    setShowSaveForm(true);
  };

  if (!hasChange && (props.filterView || props.isLoading)) return null;

  return (
    <Popover
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) {
          setShowSaveForm(false);
        }
      }}
    >
      <PopoverTrigger
        className={cn(
          'flex items-center gap-1 whitespace-nowrap rounded-md ' +
            'bg-brand-primary px-2 py-1 text-sm font-semibold text-white ' +
            'hover:bg-brand-primary-hover',
          showSaveForm &&
            'cursor-auto bg-tertiary text-tertiary hover:bg-tertiary'
        )}
      >
        Save
        {showSaveForm
          ? `${' '} as...`
          : props.filterView && <ChevronDownIcon className={cn('h-4 w-4')} />}
      </PopoverTrigger>
      <PopoverContent
        side={props.filterView && !showSaveForm ? 'bottom' : 'right'}
        align={props.filterView && !showSaveForm ? 'start' : 'center'}
        sideOffset={props.filterView && !showSaveForm ? 4 : 16}
        className={cn(props.filterView && !showSaveForm && 'p-0', 'w-fit')}
      >
        {props.filterView && !showSaveForm ? (
          <div className="w-[100px]">
            <button
              type="button"
              onClick={handleSave}
              className="w-full px-3 py-2 text-left text-sm hover:bg-secondary-hover focus:outline-none"
            >
              Save
            </button>
            <button
              type="button"
              onClick={handleSaveAs}
              className="w-full px-3 py-2 text-left text-sm hover:bg-secondary-hover focus:outline-none"
            >
              Save As
            </button>
          </div>
        ) : (
          <SaveFilterForm
            saveAs={showSaveForm}
            submitting={createFilter.isMutating}
            onSubmit={handleCreate}
            onCancel={() => setIsOpen(false)}
          />
        )}
      </PopoverContent>
    </Popover>
  );
}

interface FilterDialogContentProps {
  stats: RunStatsSchema | undefined;
  value: SearchModel;
  onChange: (value: SearchModel) => void;
  simpleFiltersOnly?: boolean;
  setFeedbackUrls?: (urls: Record<string, string>) => void;
  onViewChange?: (
    view: 'llm' | 'all' | 'trace' | string,
    maybeFilter?: string,
    maybeTraceFilter?: string,
    maybeTreeFilter?: string
  ) => void;
  setFilterBarOpen: (open: boolean) => void;
  showSaveFilters: boolean;

  hideAIQuery?: boolean;
  hideFilterTitle?: boolean;
  updateEveryChange?: boolean;
  onExperimentalSearchToggle?: (enabled: boolean) => void;
  alertFilteringEnabled?: boolean;
  disabledAlertRunFields?: FilterLabel[];
}

export function FilterDialogContent(props: FilterDialogContentProps) {
  const { sessionId } = useParams();
  const [searchParams] = useSearchParams();
  const filterViewId = props.onViewChange
    ? searchParams.get('custom_filter_view_id')
    : undefined;
  const { data: filterView, isLoading: filterViewLoading } = useGetFilterView(
    sessionId,
    filterViewId ?? undefined
  );

  const { onCopy, copied } = useCopyAction();

  // NOTE searchEnabled is used to determine whether to show filtering fields
  // on sensitive information. This is turned OFF for hybrid-deployments
  // where sensitive information is not stored in ClickHouse.
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;

  const { value: expSearchOrgConfigEnabled } = useOrgConfig(
    OrgConfigs.langsmith_experimental_search_enabled
  );

  const expSearchSupported = useInstanceFlagOrDefault(
    InstanceFlags.experimental_search_enabled
  ) as boolean;

  // expFilteringEnabledForTenantOrDeployment is used to determine whether to
  // show experimental filtering operands which populate the `searchFilter`
  // field in the search model.
  // This CAN be turned ON for hybrid-deployments.
  const expFilteringEnabledForTenantOrDeployment =
    expSearchSupported && (expSearchOrgConfigEnabled as boolean);

  const experimentalSearchEnabled = props.value.useExperimentalSearch ?? false;

  const runFields = getRunFields(
    searchEnabled,
    expFilteringEnabledForTenantOrDeployment && experimentalSearchEnabled,
    props.alertFilteringEnabled,
    props.disabledAlertRunFields
  );

  const [query, setQuery] = useFilterAst(props.value.filter, runFields);
  // Separate state for experimental search filter
  const [searchQuery, setSearchQuery] = useFilterAst(
    props.value.searchFilter,
    runFields
  );

  const generateAiQueryEnabled = useInstanceFlagOrDefault(
    InstanceFlags.generate_ai_query_enabled
  );
  const [traceQuery, setTraceQuery] = useFilterAst(
    props.value.traceFilter,
    runFields
  );
  const [treeQuery, setTreeQuery] = useFilterAst(
    props.value.treeFilter,
    runFields
  );

  const [showAdvanced, setShowAdvanced] = useState(
    () => traceQuery.length > 0 || treeQuery.length > 0
  );

  const [feedbackUrlsByOperand, setFeedbackUrlsByOperand] = useState<
    Record<string, Record<string, string>>
  >({});

  const queries = [
    props.value.filter,
    props.value.traceFilter,
    props.value.treeFilter,
  ].filter((x): x is string => x != null && !!x);

  const traceFilterEnabled = !query.some((group) =>
    group.comparisons.find(findComparsion('eq', 'is_root', true))
  );

  const hasDisabledFields = query.some(
    (group) => group.field && group.field.disabled?.({ runStats: props.stats })
  );

  const { openSecretsRef, missingSecret } = useTenantSecrets(
    OPENAI_MODEL_MANIFEST
  );

  const shouldRenderShowAdvancedButton =
    !experimentalSearchEnabled &&
    !(props.simpleFiltersOnly && props.hideAIQuery);

  const getShowAdvancedButtonText = () => {
    if (props.simpleFiltersOnly) {
      return showAdvanced ? 'Switch to AI Query' : 'Switch to Raw Query';
    }
    return showAdvanced ? 'Close advanced filters' : 'Advanced filters';
  };

  return (
    <div className="group flex flex-col items-stretch border-tertiary text-sm">
      {props.hideFilterTitle ? null : (
        <div className="flex flex-col p-3 pb-1">
          <div className="flex items-start justify-between">
            <span className="py font-medium">
              {filterView?.display_name ?? 'Filters'}
            </span>
            <div className="flex items-start gap-1 opacity-0 transition-all focus-within:opacity-100 group-hover:opacity-100">
              {filterView && (
                <EditFilterPopover
                  filterView={filterView}
                  popoverPlacement={{
                    side: 'top',
                    align: 'start',
                    sideOffset: 75,
                  }}
                />
              )}
              {filterView ? (
                <DeleteSavedFilterButton
                  filterView={filterView}
                  onToggleSwitch={() => {
                    props.onViewChange?.('trace');
                  }}
                  setFilterBarOpen={props.setFilterBarOpen}
                />
              ) : (
                <ClearFilterButton
                  queries={queries}
                  onDeleteFilterValues={() => {
                    if (experimentalSearchEnabled) {
                      // Only clear search query in experimental mode
                      setSearchQuery([]);
                      props.onChange({
                        ...props.value,
                        searchFilter: undefined,
                      });
                    } else {
                      // Clear all queries in normal mode
                      setQuery([]);
                      setTraceQuery([]);
                      setTreeQuery([]);
                      props.onChange({
                        ...props.value,
                        filter: undefined,
                        traceFilter: undefined,
                        treeFilter: undefined,
                      });
                    }
                  }}
                  disabled={hasDisabledFields}
                />
              )}
              {queries.length > 1 ? (
                <DropdownMenu>
                  <DropdownMenuTrigger className="flex h-6 w-6 items-center justify-center rounded-md border border-secondary p-0 outline-none">
                    {copied ? (
                      <CheckIcon className="h-4 w-4" />
                    ) : (
                      <CopyIcon className="h-4 w-4" />
                    )}
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {props.value.filter && (
                      <DropdownMenuItem
                        onSelect={() => onCopy(props.value.filter ?? '')}
                      >
                        Copy filters
                      </DropdownMenuItem>
                    )}
                    {props.value.traceFilter && (
                      <DropdownMenuItem
                        onSelect={() => onCopy(props.value.traceFilter ?? '')}
                      >
                        Copy trace filters
                      </DropdownMenuItem>
                    )}
                    {props.value.treeFilter && (
                      <DropdownMenuItem
                        onSelect={() => onCopy(props.value.treeFilter ?? '')}
                      >
                        Copy tree filters
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : queries.length > 0 ? (
                <CopyIconButton
                  className="h-6 w-6 p-0 "
                  copy={queries.at(0) ?? ''}
                />
              ) : null}
            </div>
          </div>
          {filterView?.description && (
            <span className="py text-xs text-tertiary">
              {filterView.description}
            </span>
          )}

          {/* Experimental Search toggle - only shown when both props are defined */}
          {expFilteringEnabledForTenantOrDeployment &&
            !props.simpleFiltersOnly &&
            props.onExperimentalSearchToggle !== undefined && (
              <div className="mt-2 flex items-center justify-between border-t border-secondary pt-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    Experimental Search
                  </span>
                  <Tooltip title="Use experimental full-text search capabilities">
                    <div>
                      <InfoCircleIcon className="h-4 w-4 text-tertiary" />
                    </div>
                  </Tooltip>
                </div>
                <div className="flex items-center">
                  <Switch
                    size="sm"
                    checked={experimentalSearchEnabled}
                    onChange={(checked) =>
                      props.onExperimentalSearchToggle?.(checked)
                    }
                    label=""
                  />
                </div>
              </div>
            )}
        </div>
      )}
      <div
        className={cn(
          'flex flex-col gap-2',
          !props.hideFilterTitle ? 'px-3' : ''
        )}
      >
        <FilterBarOperands
          fields={runFields}
          operandsWithFields={experimentalSearchEnabled ? searchQuery : query}
          handleChange={(value) => {
            if (experimentalSearchEnabled) {
              const newSearchQuery = setSearchQuery(value);
              props.onChange({
                ...props.value,
                searchFilter: astToString(newSearchQuery),
              });
            } else {
              const newQuery = setQuery(value);
              props.onChange({
                ...props.value,
                filter: astToString(newQuery),
              });
            }
          }}
          stats={props.stats}
          feedbackUrlsByOperand={feedbackUrlsByOperand}
          setFeedbackUrlsByOperand={setFeedbackUrlsByOperand}
          updateEveryChange={props.updateEveryChange}
        />

        <button
          type="button"
          className={cn(
            'flex cursor-pointer flex-row items-center gap-2 self-start rounded-md border border-secondary bg-primary p-1 px-2 transition-colors focus-within:bg-secondary hover:bg-secondary-hover active:bg-secondary',
            query.length === 0 &&
              !props.hideFilterTitle &&
              'mt-1 justify-center self-stretch py-1.5'
          )}
          onClick={() => {
            if (experimentalSearchEnabled) {
              const newSearchQueryAst = setSearchQuery([
                ...searchQuery,
                { comparisons: [{}] },
              ]);
              props.onChange({
                ...props.value,
                searchFilter: astToString(newSearchQueryAst),
              });
            } else {
              const newQueryAst = setQuery([...query, { comparisons: [{}] }]);
              props.onChange({
                ...props.value,
                filter: astToString(newQueryAst),
              });
            }
          }}
        >
          <PlusIcon className="h-4 w-4" />
          Add filter
        </button>
      </div>
      {showAdvanced &&
        !props.simpleFiltersOnly &&
        !experimentalSearchEnabled && (
          <div className="mt-3 flex flex-col gap-3 border-t border-secondary px-3 pt-3">
            <span className="py font-medium">Advanced Filters</span>

            <div className="flex flex-col gap-2 rounded-md border border-tertiary p-3">
              <div className="flex items-center gap-2">
                <span>Trace filters</span>
                <Tooltip title="Include a run if filter matches for the root run of the trace">
                  <span>
                    <InfoCircleIcon className="h-3.5 w-3.5 text-tertiary" />
                  </span>
                </Tooltip>
              </div>

              {!traceFilterEnabled && (
                <div className="mt-1">
                  <ExpandableErrorAlert
                    error="Cannot add trace filters when is_root=true filter is present"
                    color="warning"
                  />
                </div>
              )}

              <FilterBarOperands
                fields={runFields.filter(
                  (field) => !('id' in field && field.id === 'is_root')
                )}
                operandsWithFields={traceQuery}
                handleChange={(value) => {
                  const newQuery = setTraceQuery(value);
                  props.onChange({
                    ...props.value,
                    traceFilter: astToString(newQuery),
                  });
                }}
                stats={props.stats}
                updateEveryChange={props.updateEveryChange}
              />

              <button
                type="button"
                className={cn(
                  'mt-1 flex cursor-pointer flex-row items-center gap-2 self-start rounded-md border border-secondary bg-background p-1 px-2 transition-colors focus-within:bg-secondary hover:bg-secondary-hover active:bg-secondary disabled:opacity-50 disabled:hover:bg-transparent',
                  traceQuery.length === 0 &&
                    'mt-0 justify-center self-stretch py-1.5'
                )}
                disabled={!traceFilterEnabled}
                onClick={() => {
                  props.onChange({
                    ...props.value,
                    traceFilter: astToString(
                      setTraceQuery([...traceQuery, { comparisons: [{}] }])
                    ),
                  });
                }}
              >
                <PlusIcon className="h-4 w-4" />
                Add filter
              </button>
            </div>

            <div className="flex flex-col gap-2 rounded-md border border-tertiary p-3">
              <div className="flex items-center gap-2">
                <span>Tree filters</span>
                <Tooltip title="Include a run if filter matches for any run in a trace">
                  <span>
                    <InfoCircleIcon className="h-3.5 w-3.5 text-tertiary" />
                  </span>
                </Tooltip>
              </div>

              <FilterBarOperands
                fields={runFields.filter(
                  (field) => !('id' in field && field.id === 'is_root')
                )}
                operandsWithFields={treeQuery}
                handleChange={(value) => {
                  const newQuery = setTreeQuery(value);
                  props.onChange({
                    ...props.value,
                    treeFilter: astToString(newQuery),
                  });
                }}
                stats={props.stats}
                updateEveryChange={props.updateEveryChange}
              />

              <button
                type="button"
                className={cn(
                  'mt-1 flex cursor-pointer flex-row items-center gap-2 self-start rounded-md border border-secondary bg-background p-1 px-2 transition-colors focus-within:bg-secondary hover:bg-secondary-hover active:bg-secondary disabled:opacity-50 disabled:hover:bg-transparent',
                  traceQuery.length === 0 &&
                    'mt-0 justify-center self-stretch py-1.5'
                )}
                onClick={() => {
                  props.onChange({
                    ...props.value,
                    treeFilter: astToString(
                      setTreeQuery([...treeQuery, { comparisons: [{}] }])
                    ),
                  });
                }}
              >
                <PlusIcon className="h-4 w-4" />
                Add filter
              </button>
            </div>
          </div>
        )}
      {(showAdvanced && !props.hideAIQuery) || experimentalSearchEnabled ? (
        <div className="flex flex-col gap-2 px-3 pt-3">
          <span>Raw Query</span>
          <FilterBarRawInput
            onSuccess={(newQuery) => {
              const queryAst = flattenAst(stringToAst(newQuery)) ?? null;
              if (!queryAst) return false;

              if (experimentalSearchEnabled) {
                const newSearchQueryAst = setSearchQuery([
                  ...searchQuery,
                  { comparisons: queryAst.operands },
                ]);

                props.onChange({
                  ...props.value,
                  searchFilter: astToString(newSearchQueryAst),
                });
              } else {
                const newQueryAst = setQuery([
                  ...query,
                  { comparisons: queryAst.operands },
                ]);

                props.onChange({
                  ...props.value,
                  filter: astToString(newQueryAst),
                });
              }

              return true;
            }}
          />
        </div>
      ) : (
        generateAiQueryEnabled &&
        !props.hideAIQuery &&
        !experimentalSearchEnabled && (
          <div className="flex flex-col gap-2 px-3 pt-3">
            <div className="flex flex-row justify-between">
              <span>AI Query</span>
              <EvaluatorSecrets
                model={OPENAI_MODEL_MANIFEST}
                openSecretsRef={openSecretsRef}
                infoMessage="Your API keys are stored encrypted on our servers."
                triggerComponent={
                  <div className="my-auto flex cursor-pointer flex-row gap-1 text-sm underline hover:opacity-70">
                    API Key
                  </div>
                }
              />
            </div>

            <FilterBarGeneratedInput
              onSuccess={(newQuery, feedbackUrls) => {
                const queryAst = flattenAst(stringToAst(newQuery)) ?? null;
                if (feedbackUrls?.valid_filter) {
                  fetch(
                    `${feedbackUrls.valid_filter}?score=${queryAst ? 1 : 0}`
                  );
                  delete feedbackUrls.valid_filter;
                }
                if (!queryAst) {
                  return false;
                }

                const newQueryAst = setQuery([
                  ...query,
                  { comparisons: queryAst.operands },
                ]);

                props.onChange({
                  ...props.value,
                  filter: astToString(newQueryAst),
                });

                if (feedbackUrls) {
                  props.setFeedbackUrls?.(feedbackUrls);
                  setFeedbackUrlsByOperand(
                    queryAst.operands.reduce<
                      Record<string, Record<string, string>>
                    >((urlsByOp, op) => {
                      const str = astToString(op);
                      if (str) urlsByOp[str] = feedbackUrls;
                      return urlsByOp;
                    }, {})
                  );
                }
                return true;
              }}
              onSubmit={() => {
                if (missingSecret()) {
                  openSecretsRef.current?.();
                  return;
                }
              }}
            />
          </div>
        )
      )}
      <div className="flex items-center justify-between p-3">
        {shouldRenderShowAdvancedButton && (
          <button
            type="button"
            className="text-sm text-tertiary underline"
            onClick={() => setShowAdvanced((state) => !state)}
          >
            {getShowAdvancedButtonText()}
          </button>
        )}
        {props.showSaveFilters && !experimentalSearchEnabled && (
          <SaveFilterPopover
            filter={props.value.filter}
            traceFilter={props.value.traceFilter}
            treeFilter={props.value.treeFilter}
            filterView={filterView}
            onViewChange={props.onViewChange}
            isLoading={filterViewLoading}
            saveFilterType={FilterViewType.RUNS}
          />
        )}
      </div>
    </div>
  );
}

const variantStyles = {
  'run-details': {
    buttonClass: 'text-xs text-ls-black',
    showIcon: true,
    zeroFiltersText: 'Filter',
  },
  runs: {
    buttonClass: 'text-sm text-tertiary',
    showIcon: true,
    zeroFiltersText: 'Filters',
  },
  form: {
    buttonClass: 'text-tertiary px-3 py-[7px]',
    showIcon: true,
    zeroFiltersText: 'Filters',
  },
} as const;

export function FilterBar(props: {
  stats: RunStatsSchema | undefined;

  variant: 'runs' | 'form' | 'run-details';
  value: SearchModel;
  onChange: (value: SearchModel) => void;

  feedbackUrls?: Record<string, string>;
  setFeedbackUrls?: (urls: Record<string, string>) => void;

  onViewChange?: (
    view: 'llm' | 'all' | 'trace' | string,
    maybeFilter?: string,
    maybeTraceFilter?: string,
    maybeTreeFilter?: string,
    experimentalSearchEnabled?: boolean
  ) => void;

  onExperimentalSearchToggle?: (enabled: boolean) => void;
  hideAIQuery?: boolean;
  alertFilteringEnabled?: boolean;
  disabledAlertRunFields?: FilterLabel[];
}) {
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;
  const runFields = getRunFields(searchEnabled);

  const nActive = useMemo(
    () => getActiveFieldsCount(props.value, runFields),
    [props.value]
  );
  const variantStyle = variantStyles[props.variant];

  const { open, setOpen } = useFilterBarOpen(props.variant);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <button
          type="button"
          className={cn(
            'flex hover:bg-secondary data-[state=open]:bg-secondary ' +
              'flex-shrink-0 items-center gap-1.5 rounded-md border ' +
              'border-secondary px-2 py-1 font-medium transition-all ' +
              'active:bg-secondary',
            variantStyle.buttonClass,
            nActive > 0 &&
              'hover:bg-brand-secondary-hover border-brand-subtle ' +
                'active:bg-brand-secondary ' +
                'data-[state=open]:bg-brand-secondary ' +
                'bg-brand-tertiary text-brand-tertiary'
          )}
        >
          {variantStyle.showIcon && <FilterIcon />}
          {nActive === 0 ? (
            <span>{variantStyle.zeroFiltersText}</span>
          ) : (
            <span>
              {
                {
                  one: `1 filter`,
                  two: `${nActive} filters`,
                  few: `${nActive} filters`,
                  other: `${nActive} filters`,
                }[pluralRules.select(nActive)]
              }
            </span>
          )}
        </button>
      </PopoverTrigger>

      <PopoverContent
        align={props.variant === 'form' ? 'end' : 'start'}
        className={cn('z-10 w-auto min-w-[300px] overflow-hidden p-0')}
        avoidCollisions={false}
        side={'bottom'}
      >
        <FilterDialogContent
          stats={props.stats}
          value={props.value}
          onChange={props.onChange}
          simpleFiltersOnly={props.variant === 'run-details'}
          showSaveFilters={props.variant === 'runs'}
          setFeedbackUrls={props.setFeedbackUrls}
          onViewChange={props.onViewChange}
          setFilterBarOpen={setOpen}
          onExperimentalSearchToggle={props.onExperimentalSearchToggle}
          hideAIQuery={props.hideAIQuery}
          disabledAlertRunFields={props.disabledAlertRunFields}
          alertFilteringEnabled={props.alertFilteringEnabled}
        />
      </PopoverContent>
    </Popover>
  );
}

function RunDetailsFilterDialogContent(
  props: Omit<FilterDialogContentProps, 'stats'> & { rootRuns: RunSchema[] }
) {
  const { conversationId: paramConversationId } = useParams();
  const [searchParams] = useSearchParams();
  const conversationId =
    paramConversationId ?? searchParams.get('conversationId');

  const statsParams =
    props.rootRuns.length === 1
      ? { trace: props.rootRuns[0].id, session: [props.rootRuns[0].session_id] }
      : {
          filter: `and(in(metadata_key, ["session_id","conversation_id","thread_id"]), eq(metadata_value, "${conversationId}"))`,
          session: [props.rootRuns[0].session_id],
        };

  const stats = useRunStats(statsParams);

  return <FilterDialogContent {...props} stats={stats.data} />;
}

export function RunDetailsFilterBar(props: {
  rootRuns: RunSchema[];
  value: SearchModel;
  onChange: (value: SearchModel) => void;
  disabled?: boolean;
  size?: 'sm' | 'md';
  borderless?: boolean;
}) {
  const [open, setOpen] = useState(false);
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;
  const runFields = getRunFields(searchEnabled);

  const nActive = useMemo(
    () => getActiveFieldsCount(props.value, runFields),
    [props.value]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Tooltip title="Filter runs">
          <button
            type="button"
            disabled={props.disabled}
            className={cn(
              'flex flex-shrink-0 items-center gap-1.5 rounded-md border border-secondary px-2 py-1.5 font-medium transition-all hover:bg-secondary active:bg-secondary data-[state=open]:bg-tertiary',
              variantStyles['run-details'].buttonClass,
              nActive > 0 &&
                'border-brand hover:bg-brand-secondary ' +
                  'active:bg-brand-secondary ' +
                  'data-[state=open]:bg-brand-secondary ' +
                  'bg-brand-tertiary text-brand-tertiary',
              props.borderless && 'border-none'
            )}
          >
            {variantStyles['run-details'].showIcon && (
              <FilterLinesIcon className="size-4" />
            )}
            <FilterCountText size={props.size ?? 'md'} nActive={nActive} />
          </button>
        </Tooltip>
      </PopoverTrigger>

      <PopoverContent
        align="start"
        className="z-10 w-auto min-w-[300px] overflow-hidden p-0"
        avoidCollisions={false}
        side={'bottom'}
      >
        <RunDetailsFilterDialogContent
          rootRuns={props.rootRuns}
          value={props.value}
          onChange={props.onChange}
          simpleFiltersOnly
          showSaveFilters={false}
          setFeedbackUrls={undefined}
          onViewChange={undefined}
          setFilterBarOpen={setOpen}
        />
      </PopoverContent>
    </Popover>
  );
}

function FilterCountText(props: { size: 'sm' | 'md'; nActive: number }) {
  const { size, nActive } = props;

  if (size === 'sm') {
    return null;
  }

  if (nActive === 0) {
    return <span>{variantStyles['run-details'].zeroFiltersText}</span>;
  }

  const pluralText = xCount('filter', nActive);

  return <span>{pluralText}</span>;
}
