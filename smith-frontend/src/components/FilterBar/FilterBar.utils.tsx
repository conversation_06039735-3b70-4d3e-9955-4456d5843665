import { useState } from 'react';

import { RunStatsSchema } from '@/types/schema';
import { tryParseJSON } from '@/utils/try-parse-json';

import {
  Comparison,
  PartialRunsFilterAst,
  flattenAst,
  stringToAst,
} from '../RunsTable/filter/ast';
import { SearchModel } from '../RunsTable/types';
import {
  CustomFilterField,
  FilterAnyField,
  FilterComp,
  FilterField,
  FilterGroup,
  StandardFilterField,
  filterComps,
  filterCompsMap,
} from './constants';

export interface OperationInput {
  operator: 'and';
  operands: Array<Partial<Comparison>>;
}

export interface GroupedComparison {
  field: FilterAnyField | undefined;
  comparisons: Partial<Comparison>[];
}

const getDefaultField = (id: string) =>
  ({
    id,
    label: id,
    availableComps: filterComps,
    defaultComp: filterCompsMap.eq,
    valueType: 'string',
  } satisfies FilterField);

export function groupComparisonsByRunFields(
  ast: OperationInput | undefined,
  fields: FilterAnyField[]
): GroupedComparison[] {
  const basicFields = fields.filter((i): i is StandardFilterField => 'id' in i);
  const groupFields = fields.filter((i): i is FilterGroup => 'group' in i);
  const customFields = fields.filter(
    (i): i is CustomFilterField => 'name' in i
  );

  const groupKeyIdx: Record<string, number> = {};
  const groupOperands: Record<string, Partial<Comparison>[]> = {};

  const operandsWithFields: Array<GroupedComparison> = [];

  let count = 0;
  const operandsWithGroupOperandIdx =
    ast?.operands.map((operand, index) => {
      // Check if this operand is a group type field

      const isGroupField = groupFields.some((group) =>
        group.fields.some(
          (f) =>
            f.id === operand.identifier &&
            (operand.metadata?.isValue != null
              ? f.metadata?.isValue === operand.metadata?.isValue
              : true)
        )
      );

      if (!isGroupField) return operand;

      const identifier = operand.identifier ?? '';
      const currentGroupIdx = count;

      let groupField: FilterGroup | undefined;
      if (identifier.endsWith('_key')) {
        // Look ahead for corresponding value field
        const nextOperand = ast?.operands[index + 1];
        const hasCorrespondingValue =
          nextOperand &&
          (nextOperand.identifier?.endsWith('_value') ||
            nextOperand.identifier?.endsWith('_score'));

        if (hasCorrespondingValue) {
          groupField = groupFields.find((i) =>
            i.fields.some(
              (f) =>
                f.id === nextOperand.identifier &&
                f.metadata?.group === operand.metadata?.group
            )
          );
        } else {
          groupField = groupFields.find((i) =>
            i.fields.some(
              (f) =>
                f.id === operand.identifier &&
                f.metadata?.group === operand.metadata?.group
            )
          );
          count++;
        }
      } else if (
        identifier.endsWith('_value') ||
        identifier.endsWith('_score')
      ) {
        groupField = groupFields.find((i) =>
          i.fields.some((f) => f.id === operand.identifier)
        );
        count++;
      }

      return {
        ...operand,
        metadata: {
          ...operand.metadata,
          groupOperandIdx: currentGroupIdx,
          group: groupField?.group,
        },
      };
    }) ?? [];

  // need to walk through the operands to emulate pair handling of
  // feedback and metadata keys (matches the visitor behavior when translating filter query to SQL)
  for (
    let operandIndex = 0;
    operandIndex < operandsWithGroupOperandIdx.length;
    operandIndex++
  ) {
    const operand = operandsWithGroupOperandIdx[operandIndex];
    if (operand == null) {
      continue;
    }
    const customField = customFields.find((i) => i.name === operand.comparator);
    if (customField) {
      operandsWithFields.push({ field: customField, comparisons: [operand] });
      continue;
    }

    const groupField = groupFields.find((i) => {
      return i.fields.some(
        (f) =>
          f.id === operand.identifier &&
          (operand.metadata?.isValue != null
            ? f.metadata?.isValue === operand.metadata?.isValue
            : true) &&
          (operand.metadata?.group != null
            ? f.metadata?.group === operand.metadata?.group
            : true)
      );
    });

    if (groupField) {
      const key = `${groupField.group}.${operand.depth}.${operand.identifier}.${
        operand.metadata?.groupOperandIdx ?? ''
      }`;
      const nextId = groupKeyIdx[key] ?? 0;
      const groupKeyArr = [groupField.group, operand.depth, nextId];
      if (operand.metadata?.groupOperandIdx != null) {
        groupKeyArr.push(operand.metadata?.groupOperandIdx as string);
      }
      const groupKey = groupKeyArr.join('_');

      if (groupOperands[groupKey] == null) {
        // keeping the same object reference, later field updates
        // will also update the operands
        // in the operandsWithFields array
        const f = groupField.fields[0];
        groupOperands[groupKey] = [
          {
            identifier: f.id,
            comparator: f.defaultComp.id,
          },
        ];
        operandsWithFields.push({
          field: groupField,
          comparisons: groupOperands[groupKey],
        });
      }

      // figure index of the operand relative to the group
      const childFieldIdx = groupField.fields.findIndex(
        (f) => f.id === operand.identifier
      );

      if (groupField.group === 'feedback') {
        groupOperands[groupKey][childFieldIdx === 0 ? childFieldIdx : 1] =
          operand;
      } else {
        groupOperands[groupKey][childFieldIdx] = operand;
      }
      groupKeyIdx[key] = nextId + 1;

      continue;
    }

    if (operand.identifier != null) {
      operandsWithFields.push({
        field:
          basicFields.find((i) => i.id === operand.identifier) ??
          getDefaultField(operand.identifier),
        comparisons: [operand],
      });
      continue;
    }

    operandsWithFields.push({
      field: undefined,
      comparisons: [operand],
    });
  }

  return operandsWithFields;
}

export function getAstFromGroupedComparisons(
  groupedComparisons: Pick<GroupedComparison, 'comparisons'>[]
): PartialRunsFilterAst {
  type Operands = Exclude<PartialRunsFilterAst, undefined>;
  return {
    operator: 'and',
    operands: groupedComparisons.flatMap<Operands>(({ comparisons }) => {
      if (comparisons.reduce((acc, item) => Math.max(item.depth ?? 0, acc), 0))
        return [{ operator: 'and', operands: comparisons }];
      return comparisons;
    }),
  };
}

export function getActiveFieldsCount(
  value: SearchModel,
  fields: FilterAnyField[]
) {
  if (value.useExperimentalSearch) {
    if (!value.searchFilter) return 0;

    return groupComparisonsByRunFields(
      flattenAst(stringToAst(value.searchFilter)),
      fields
    ).length;
  }

  return [value.filter, value.traceFilter, value.treeFilter]
    .filter((x): x is string => x != null && x !== '')
    .reduce(
      (acc, query) =>
        acc +
        groupComparisonsByRunFields(flattenAst(stringToAst(query)), fields)
          .length,
      0
    );
}

export const useFilterAst = (
  value: string | undefined,
  fields: FilterAnyField[]
) => {
  const [ast, setAst] = useState<PartialRunsFilterAst>(() =>
    stringToAst(value)
  );

  function handleChange(values: Pick<GroupedComparison, 'comparisons'>[]) {
    const ast = getAstFromGroupedComparisons(values);
    setAst(ast);
    return ast;
  }

  const groupedComparisons = groupComparisonsByRunFields(
    flattenAst(ast),
    fields
  );

  return [groupedComparisons, handleChange] as const;
};

export function normalizeFilterString(
  value: string | undefined | null
): string {
  return value?.trim() || '';
}

export function getOperandFromRunFacets({
  runStats,
  comparisons,
}: {
  runStats: RunStatsSchema;
  comparisons: Partial<Comparison>[];
}) {
  return (
    runStats?.run_facets
      ?.map(({ key, value }) => {
        const operand = comparisons.find(
          ({ identifier, argument }) =>
            identifier != null &&
            typeof argument !== 'undefined' &&
            identifier !== key &&
            (key.startsWith(identifier) ||
              identifier.startsWith(key) ||
              (key === 'feedback_value' && identifier === 'feedback_key')) &&
            value.startsWith(`${argument} ==`)
        );

        const rawValue = tryParseJSON(
          value.slice(value.indexOf(' == ') + ' == '.length),
          true
        );

        return {
          value: operand != null ? rawValue : null,
        };
      })
      .filter((i) => i.value !== null) ?? []
  );
}

export function getComparatorLabel(
  comparator: FilterComp,
  fieldType: 'number' | 'string' | 'boolean'
) {
  if (fieldType === 'number') {
    return comparator.numericLabel ?? comparator.label;
  }
  return comparator.label;
}

export function getFeedbackType({
  runStats,
  comparison,
}: {
  runStats?: RunStatsSchema;
  comparison: Partial<Comparison>;
}): 'feedback_score' | 'feedback_value' | null {
  if (
    comparison.identifier === 'feedback_key' &&
    typeof comparison.argument === 'string' &&
    runStats
  ) {
    const feedbackKey = comparison.argument;
    const facet = runStats?.run_facets?.find(({ value }) => {
      return value.startsWith(`${feedbackKey} ==`);
    });
    if (facet) {
      return facet.key.includes('score') ? 'feedback_score' : 'feedback_value';
    }
  }
  return null;
}
export function fieldToComparison(field: FilterAnyField) {
  if ('group' in field) {
    return fieldToComparison(field.fields[0]);
  }

  if ('name' in field) {
    return {
      comparator: field.name,
    };
  }

  return {
    identifier: field.id,
    comparator: field.defaultComp.id,
    argument: undefined,
    metadata: field.metadata,
  };
}
