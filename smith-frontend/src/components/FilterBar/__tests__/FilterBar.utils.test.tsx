import { describe, expect, it } from 'vitest';

import {
  PartialRunsFilterAst,
  flattenAst,
} from '@/components/RunsTable/filter/ast';
import { RunStatsSchema } from '@/types/schema';

import {
  OperationInput,
  getFeedbackType,
  getOperandFromRunFacets,
  groupComparisonsByRunFields,
} from '../FilterBar.utils';
import {
  CustomFilterField,
  FilterAnyField,
  FilterGroup,
  FilterLabel,
  StandardFilterField,
  filterComps,
  filterCompsMap,
} from '../constants';
import { ALL_RUN_FIELDS } from '../fields/runs';

// Common test constants used across multiple describe blocks
const TEST_VALUE = 'test_value';

describe('groupComparisonsByRunFields', () => {
  // Test data setup
  const TEST_FIELD_ID = 'test_field';
  const TEST_FIELD_LABEL = 'Test Field';
  const CUSTOM_FIELD_NAME = 'custom_field';
  const CUSTOM_FIELD_LABEL = 'Custom Field';
  const GROUP_FIELD_1_ID = 'group_field_1';
  const GROUP_FIELD_2_ID = 'group_field_2';
  const GROUP_FIELD_1_LABEL = 'Group Field 1';
  const GROUP_FIELD_2_LABEL = 'Group Field 2';
  const TEST_GROUP = 'test_group';

  const basicField: StandardFilterField = {
    id: TEST_FIELD_ID,
    label: TEST_FIELD_LABEL,
    defaultComp: filterCompsMap.eq,
    availableComps: [filterCompsMap.eq],
    valueType: 'string',
  };

  const customField: CustomFilterField = {
    name: CUSTOM_FIELD_NAME,
    label: CUSTOM_FIELD_LABEL,
    fieldComponent: () => null,
  };

  const groupField: FilterGroup = {
    group: TEST_GROUP,
    label: FilterLabel.metadata,
    fields: [
      {
        id: GROUP_FIELD_1_ID,
        label: GROUP_FIELD_1_LABEL,
        defaultComp: filterCompsMap.eq,
        availableComps: [filterCompsMap.eq],
        valueType: 'string',
      },
      {
        id: GROUP_FIELD_2_ID,
        label: GROUP_FIELD_2_LABEL,
        defaultComp: filterCompsMap.eq,
        availableComps: [filterCompsMap.eq],
        valueType: 'string',
      },
    ],
  };

  it('should handle undefined AST', () => {
    const result = groupComparisonsByRunFields(undefined, [basicField]);
    expect(result).toEqual([]);
  });

  it('should handle empty fields array', () => {
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          identifier: TEST_FIELD_ID,
          comparator: 'eq',
        },
      ],
    };
    const result = groupComparisonsByRunFields(ast, []);
    expect(result).toHaveLength(1);
    expect(result[0].field).toEqual({
      id: TEST_FIELD_ID,
      label: TEST_FIELD_ID,
      defaultComp: filterCompsMap.eq,
      availableComps: filterComps,
      valueType: 'string',
    });
  });

  it('should handle custom fields', () => {
    const testValue = TEST_VALUE;
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          comparator: CUSTOM_FIELD_NAME,
          argument: testValue,
        },
      ],
    };
    const result = groupComparisonsByRunFields(ast, [customField]);
    expect(result).toHaveLength(1);
    expect(result[0].field).toEqual(customField);
    expect(result[0].comparisons).toHaveLength(1);
  });

  it('should handle group fields with metadata', () => {
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          identifier: GROUP_FIELD_1_ID,
          comparator: 'eq',
          metadata: { isValue: true },
        },
      ],
    };
    const groupFieldWithMetadata: FilterGroup = {
      ...groupField,
      fields: [
        {
          ...groupField.fields[0],
          metadata: { isValue: true },
        },
        groupField.fields[1],
      ],
    };
    const result = groupComparisonsByRunFields(ast, [groupFieldWithMetadata]);
    expect(result).toHaveLength(1);
    expect(result[0].field).toEqual(groupFieldWithMetadata);
  });

  it('should handle multiple group fields with same identifier', () => {
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          identifier: GROUP_FIELD_1_ID,
          comparator: 'eq',
        },
        {
          identifier: GROUP_FIELD_2_ID,
          comparator: 'eq',
        },
      ],
    };
    const duplicateGroupField: FilterGroup = {
      ...groupField,
      group: 'test_group_2',
    };
    const result = groupComparisonsByRunFields(ast, [
      groupField,
      duplicateGroupField,
    ]);
    expect(result).toHaveLength(1);
    expect(result[0].field).toEqual(groupField);
    expect(result[0].comparisons).toHaveLength(2);
  });

  it('should handle undefined field identifiers', () => {
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          comparator: 'eq',
          argument: TEST_VALUE,
        },
      ],
    };
    const result = groupComparisonsByRunFields(ast, [basicField]);
    expect(result).toHaveLength(1);
    expect(result[0].field).toBeUndefined();
  });

  it('should handle nested group fields with different depths', () => {
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          identifier: GROUP_FIELD_1_ID,
          comparator: 'eq',
          depth: 1,
        },
        {
          identifier: GROUP_FIELD_2_ID,
          comparator: 'eq',
          depth: 2,
        },
      ],
    };
    const result = groupComparisonsByRunFields(ast, [groupField]);
    expect(result).toHaveLength(2);
    expect(result[0].field).toEqual(groupField);
    expect(result[1].field).toEqual(groupField);
  });

  it('should handle non-sequential group field matches', () => {
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          identifier: GROUP_FIELD_2_ID,
          comparator: 'eq',
        },
        {
          identifier: GROUP_FIELD_1_ID,
          comparator: 'eq',
        },
      ],
    };
    const result = groupComparisonsByRunFields(ast, [groupField]);
    expect(result).toHaveLength(1);
    expect(result[0].field).toEqual(groupField);
    expect(result[0].comparisons).toHaveLength(2);
  });

  it('should handle missing metadata in field or operand', () => {
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          identifier: GROUP_FIELD_1_ID,
          comparator: 'eq',
        },
      ],
    };
    const groupFieldWithMetadata: FilterGroup = {
      ...groupField,
      fields: [
        {
          ...groupField.fields[0],
          metadata: { isValue: true },
        },
        groupField.fields[1],
      ],
    };
    const result = groupComparisonsByRunFields(ast, [groupFieldWithMetadata]);
    expect(result).toHaveLength(1);
    expect(result[0].field).toEqual(groupFieldWithMetadata);
  });

  it('should handle invalid field types', () => {
    const INVALID_FIELD_ID = 'invalid_field';
    const ast: OperationInput = {
      operator: 'and',
      operands: [
        {
          identifier: INVALID_FIELD_ID,
          comparator: 'eq',
        },
      ],
    };
    const invalidField = {
      label: 'Invalid Field',
    } as FilterAnyField;
    const result = groupComparisonsByRunFields(ast, [invalidField]);
    expect(result).toHaveLength(1);
    expect(result[0].field).toEqual({
      id: INVALID_FIELD_ID,
      label: INVALID_FIELD_ID,
      defaultComp: filterCompsMap.eq,
      availableComps: filterComps,
      valueType: 'string',
    });
  });

  it('should handle feedback group fields', () => {
    const ast: PartialRunsFilterAst = {
      operator: 'and',
      operands: [
        {
          operator: 'and',
          operands: [
            {
              identifier: 'feedback_key',
              comparator: 'eq',
              argument: 'conciseness',
              depth: 1,
              metadata: {
                group: 'feedback',
              },
            },
            {
              identifier: 'feedback_score',
              comparator: 'eq',
              depth: 1,
              metadata: {
                group: 'feedback',
              },
            },
          ],
        },
        {
          operator: 'and',
          operands: [
            {
              identifier: 'feedback_key',
              comparator: 'eq',
              argument: 'hallucination',
              depth: 1,
              metadata: {
                group: 'feedback',
              },
            },
            {
              identifier: 'feedback_value',
              comparator: 'eq',
              depth: 1,
              metadata: {
                group: 'feedback',
              },
            },
          ],
        },
      ],
    };
    const feedbackGroupField: FilterGroup = ALL_RUN_FIELDS[
      FilterLabel.feedback
    ] as FilterGroup;

    const result = groupComparisonsByRunFields(flattenAst(ast), [
      feedbackGroupField,
    ]);

    expect(result).toHaveLength(2);
    expect(result[0].field).toEqual(feedbackGroupField);
    expect(result[0].comparisons).toHaveLength(2);
    expect(result[0].comparisons[0]).toEqual({
      identifier: 'feedback_key',
      comparator: 'eq',
      argument: 'conciseness',
      metadata: {
        groupOperandIdx: 0,
        group: 'feedback',
      },
      depth: 1,
    });
    expect(result[0].comparisons[1]).toEqual({
      identifier: 'feedback_score',
      comparator: 'eq',
      metadata: {
        groupOperandIdx: 0,
        group: 'feedback',
      },
      depth: 1,
    });
    expect(result[1].field).toEqual(feedbackGroupField);
    expect(result[1].comparisons).toHaveLength(2);
    expect(result[1].comparisons[0]).toEqual({
      identifier: 'feedback_key',
      comparator: 'eq',
      argument: 'hallucination',
      metadata: {
        groupOperandIdx: 1,
        group: 'feedback',
      },
      depth: 1,
    });
    expect(result[1].comparisons[1]).toEqual({
      identifier: 'feedback_value',
      comparator: 'eq',
      metadata: {
        groupOperandIdx: 1,
        group: 'feedback',
      },
      depth: 1,
    });
  });
});

describe('getOperandFromRunFacets', () => {
  const TEST_KEY = 'test_key';
  const TEST_KEY_123 = 'test_key_123';
  const PARSED_VALUE = 'parsed_value';
  const DIFFERENT_VALUE = 'different_value';
  const FEEDBACK_KEY = 'feedback_key';
  const FEEDBACK_VALUE = 'feedback_value';

  const createValueString = (value: string) => `${TEST_VALUE} == "${value}"`;
  const createQueryString = (key: string) => `eq(${key}, ${TEST_VALUE})`;

  it('should handle undefined run_facets', () => {
    const result = getOperandFromRunFacets({
      runStats: { run_facets: undefined },
      comparisons: [],
    });
    expect(result).toEqual([]);
  });

  it('should handle empty run_facets', () => {
    const result = getOperandFromRunFacets({
      runStats: { run_facets: [] },
      comparisons: [],
    });
    expect(result).toEqual([]);
  });

  it('should match when key starts with identifier', () => {
    const result = getOperandFromRunFacets({
      runStats: {
        run_facets: [
          {
            key: TEST_KEY_123,
            value: createValueString(PARSED_VALUE),
            query: createQueryString(TEST_KEY_123),
          },
        ],
      },
      comparisons: [{ identifier: TEST_KEY, argument: TEST_VALUE }],
    });

    expect(result).toEqual([{ value: PARSED_VALUE }]);
  });

  it('should match when identifier starts with key', () => {
    const result = getOperandFromRunFacets({
      runStats: {
        run_facets: [
          {
            key: TEST_KEY,
            value: createValueString(PARSED_VALUE),
            query: createQueryString(TEST_KEY_123),
          },
        ],
      },
      comparisons: [{ identifier: TEST_KEY_123, argument: TEST_VALUE }],
    });

    expect(result).toEqual([{ value: PARSED_VALUE }]);
  });

  it('should match feedback key/value pair', () => {
    const result = getOperandFromRunFacets({
      runStats: {
        run_facets: [
          {
            key: FEEDBACK_VALUE,
            value: createValueString(PARSED_VALUE),
            query: createQueryString(FEEDBACK_KEY),
          },
        ],
      },
      comparisons: [{ identifier: FEEDBACK_KEY, argument: TEST_VALUE }],
    });

    expect(result).toEqual([{ value: PARSED_VALUE }]);
  });

  it('should not match when identifier equals key', () => {
    const result = getOperandFromRunFacets({
      runStats: {
        run_facets: [
          {
            key: TEST_KEY,
            value: createValueString(PARSED_VALUE),
            query: createQueryString(TEST_KEY),
          },
        ],
      },
      comparisons: [{ identifier: TEST_KEY, argument: TEST_VALUE }],
    });

    expect(result).toEqual([]);
  });

  it('should not match when argument is undefined', () => {
    const result = getOperandFromRunFacets({
      runStats: {
        run_facets: [
          {
            key: TEST_KEY,
            value: createValueString(PARSED_VALUE),
            query: createQueryString(TEST_KEY),
          },
        ],
      },
      comparisons: [{ identifier: TEST_KEY_123 }],
    });

    expect(result).toEqual([]);
  });

  it('should not match when value does not start with argument', () => {
    const result = getOperandFromRunFacets({
      runStats: {
        run_facets: [
          {
            key: TEST_KEY,
            value: createValueString(PARSED_VALUE),
            query: createQueryString(TEST_KEY),
          },
        ],
      },
      comparisons: [{ identifier: TEST_KEY_123, argument: DIFFERENT_VALUE }],
    });

    expect(result).toEqual([]);
  });

  it('should parse JSON values correctly', () => {
    const result = getOperandFromRunFacets({
      runStats: {
        run_facets: [
          {
            key: TEST_KEY,
            value: `${TEST_VALUE} == {"nested": {"value": 123}}`,
            query: createQueryString(TEST_KEY),
          },
        ],
      },
      comparisons: [{ identifier: TEST_KEY_123, argument: TEST_VALUE }],
    });

    expect(result).toEqual([{ value: { nested: { value: 123 } } }]);
  });

  it('should handle multiple facets and comparisons', () => {
    const query = 'eq(key1, value1)';
    const runStats = {
      run_facets: [
        { key: 'key1', value: 'value1 == "parsed1"', query },
        { key: 'key2', value: 'value2 == "parsed2"', query },
        { key: 'key3', value: 'value3 == "parsed3"', query },
      ],
    };
    const comparisons = [
      { identifier: 'key1_123', argument: 'value1' },
      { identifier: 'key2_123', argument: 'value2' },
      { identifier: 'key3_456', argument: 'value3' },
    ];

    const result = getOperandFromRunFacets({ runStats, comparisons });

    expect(result).toEqual([
      { value: 'parsed1' },
      { value: 'parsed2' },
      { value: 'parsed3' },
    ]);
  });

  it('should filter out null values', () => {
    const query = 'eq(key1, value1)';
    const runStats = {
      run_facets: [
        { key: 'key1', value: 'value1 == "parsed1"', query },
        { key: 'key2', value: 'invalid_json', query },
        { key: 'key3', value: 'value3 == "parsed3"', query },
      ],
    };
    const comparisons = [
      { identifier: 'key1_123', argument: 'value1' },
      { identifier: 'key2_123', argument: 'value2' },
      { identifier: 'key3_456', argument: 'value3' },
    ];

    const result = getOperandFromRunFacets({ runStats, comparisons });

    expect(result).toEqual([{ value: 'parsed1' }, { value: 'parsed3' }]);
  });
});

describe('getFeedbackType', () => {
  const FEEDBACK_SCORE = 'feedback_score';
  const FEEDBACK_VALUE = 'feedback_value';
  const SENTIMENT = 'sentiment';
  const POSITIVE = 'positive';
  const OTHER_FACET = 'other_facet';
  const SOMETHING_ELSE = 'something else';

  const mockRunStats: RunStatsSchema = {
    run_facets: [
      {
        key: FEEDBACK_SCORE,
        value: `${SENTIMENT} == 0.8`,
        query: `${SENTIMENT} == 0.8`,
      },
      {
        key: FEEDBACK_VALUE,
        value: `${SENTIMENT} == ${POSITIVE}`,
        query: `${SENTIMENT} == ${POSITIVE}`,
      },
      {
        key: OTHER_FACET,
        value: SOMETHING_ELSE,
        query: SOMETHING_ELSE,
      },
    ],
  } as RunStatsSchema;

  it('should return feedback_score when matching score facet is found', () => {
    const result = getFeedbackType({
      runStats: mockRunStats,
      comparison: { identifier: 'feedback_key', argument: SENTIMENT },
    });
    expect(result).toBe(FEEDBACK_SCORE);
  });

  it('should return feedback_value when matching value facet is found', () => {
    const mockStats: RunStatsSchema = {
      run_facets: [
        {
          key: FEEDBACK_VALUE,
          value: `${SENTIMENT} == ${POSITIVE}`,
          query: `${SENTIMENT} == ${POSITIVE}`,
        },
      ],
    } as RunStatsSchema;

    const result = getFeedbackType({
      runStats: mockStats,
      comparison: { identifier: 'feedback_key', argument: SENTIMENT },
    });
    expect(result).toBe(FEEDBACK_VALUE);
  });

  it('should return null when no matching facet is found', () => {
    const result = getFeedbackType({
      runStats: mockRunStats,
      comparison: { identifier: 'feedback_key', argument: 'nonexistent' },
    });
    expect(result).toBeNull();
  });

  it('should return null when run_facets is undefined', () => {
    const mockStats = {} as RunStatsSchema;
    const result = getFeedbackType({
      runStats: mockStats,
      comparison: { identifier: 'feedback_key', argument: SENTIMENT },
    });
    expect(result).toBeNull();
  });

  it('should return null when run_facets is empty', () => {
    const mockStats: RunStatsSchema = { run_facets: [] } as RunStatsSchema;
    const result = getFeedbackType({
      runStats: mockStats,
      comparison: { identifier: 'feedback_key', argument: SENTIMENT },
    });
    expect(result).toBeNull();
  });

  it('should return null when comparison identifier is not feedback_key', () => {
    const result = getFeedbackType({
      runStats: mockRunStats,
      comparison: { identifier: 'other_key', argument: SENTIMENT },
    });
    expect(result).toBeNull();
  });

  it('should return null when comparison argument is not a string', () => {
    const result = getFeedbackType({
      runStats: mockRunStats,
      comparison: { identifier: 'feedback_key', argument: 123 },
    });
    expect(result).toBeNull();
  });
});
