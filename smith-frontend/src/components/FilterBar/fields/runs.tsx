import { ALLOWED_ALERT_FILTERS } from '@/components/Alerts/utils';
import ChainIcon from '@/icons/ChainIcon.svg?react';
import ScoreSlidersIcon from '@/icons/ScoreSlidersIcon.svg?react';
import SearchIcon from '@/icons/SearchIcon.svg?react';

import { VariableInputForm } from '../../VariableInputForm/VariableInputForm';
import {
  FilterAnyField,
  FilterField,
  FilterLabel,
  filterCompsBool,
  filterCompsMap,
  filterCompsString,
  filterNumericRanges,
  filterNumericsExact,
} from '../constants';
import ErrorIcon from '../icons/ErrorIcon.svg?react';
import InputIcon from '../icons/InputIcon.svg?react';
import LatencyIcon from '../icons/LatencyIcon.svg?react';
import MetadataIcon from '../icons/MetadataIcon.svg?react';
import NameIcon from '../icons/NameIcon.svg?react';
import OutputIcon from '../icons/OutputIcon.svg?react';
import RunTypeIcon from '../icons/RunTypeIcon.svg?react';
import SourceIcon from '../icons/SourceIcon.svg?react';
import StatusIcon from '../icons/StatusIcon.svg?react';
import TagIcon from '../icons/TagIcon.svg?react';

export const ALL_RUN_FIELDS = {
  [FilterLabel.is_root]: {
    label: FilterLabel.is_root,
    id: 'is_root',
    icon: <ChainIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsBool,
    valueType: 'boolean',
  },
  [FilterLabel.name]: {
    label: FilterLabel.name,
    id: 'name',
    icon: <NameIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },
  [FilterLabel.inputs]: {
    label: FilterLabel.inputs,
    id: 'inputs',
    defaultComp: filterCompsMap.like,
    availableComps: [filterCompsMap.like, filterCompsMap.notlike],
    valueType: 'string',
    icon: <InputIcon className="h-4 w-4" />,
    fieldComponent: (props) => {
      let value =
        typeof props.value?.argument === 'string' ? props.value?.argument : '';

      if (value.startsWith('%')) value = value.slice(1);
      if (value.endsWith('%')) value = value.slice(0, -1);

      return (
        <VariableInputForm
          value={value}
          placeholder="Value..."
          onChange={(value) =>
            props.onChange({ comparator: 'like', argument: `%${value}%` })
          }
        />
      );
    },
  },
  [FilterLabel.outputs]: {
    label: FilterLabel.outputs,
    id: 'outputs',
    defaultComp: filterCompsMap.like,
    availableComps: [filterCompsMap.like, filterCompsMap.notlike],
    valueType: 'string',
    icon: <OutputIcon className="h-4 w-4" />,
    fieldComponent: (props) => {
      let value =
        typeof props.value?.argument === 'string' ? props.value?.argument : '';

      if (value.startsWith('%')) value = value.slice(1);
      if (value.endsWith('%')) value = value.slice(0, -1);

      return (
        <VariableInputForm
          value={value}
          placeholder="Value..."
          onChange={(value) =>
            props.onChange({ comparator: 'like', argument: `%${value}%` })
          }
        />
      );
    },
  },
  [FilterLabel.error]: {
    label: FilterLabel.error,
    id: 'error',
    icon: <ErrorIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.like,
    availableComps: [filterCompsMap.like, filterCompsMap.notlike],
    valueType: 'string',
  },
  [FilterLabel.input_kv]: {
    label: FilterLabel.input_kv,
    group: 'input_kv',
    omitKeyLabel: true,
    icon: <InputIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'input_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'input_kv',
        },
      },
      {
        label: FilterLabel.value,
        id: 'input_value',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'input_kv',
        },
      },
    ],
  },
  [FilterLabel.output_kv]: {
    label: FilterLabel.output_kv,
    group: 'output_kv',
    omitKeyLabel: true,
    icon: <OutputIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'output_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'output_kv',
        },
      },
      {
        label: FilterLabel.value,
        id: 'output_value',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'output_kv',
        },
      },
    ],
  },
  [FilterLabel.extra_kv]: {
    label: FilterLabel.extra_kv,
    group: 'extra_kv',
    omitKeyLabel: true,
    icon: <MetadataIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'extra_key',
        defaultComp: filterCompsMap.eq,
        availableComps: [filterCompsMap.eq],
        valueType: 'string',
        metadata: {
          group: 'extra_kv',
        },
      },
      {
        label: FilterLabel.value,
        id: 'extra_value',
        defaultComp: filterCompsMap.contains,
        availableComps: [filterCompsMap.contains],
        valueType: 'string',
        metadata: {
          group: 'extra_kv',
        },
      },
    ],
  },
  [FilterLabel.run_type]: {
    label: FilterLabel.run_type,
    id: 'run_type',
    icon: <RunTypeIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },
  [FilterLabel.latency]: {
    label: FilterLabel.latency,
    id: 'latency',
    suffix: 's',
    icon: <LatencyIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.gte,
    availableComps: filterNumericRanges,
    valueType: 'number',
  },
  [FilterLabel.status]: {
    label: FilterLabel.status,
    id: 'status',
    icon: <StatusIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: [filterCompsMap.eq, filterCompsMap.neq],
    valueType: 'string',
  },
  [FilterLabel.tag]: {
    label: FilterLabel.tag,
    id: 'tag',
    icon: <TagIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },
  [FilterLabel.metadata]: {
    label: FilterLabel.metadata,
    group: 'metadata',
    icon: <MetadataIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'metadata_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'metadata',
        },
      },
      {
        label: FilterLabel.value,
        id: 'metadata_value',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'metadata',
        },
      },
    ],
  },

  [FilterLabel.feedback_value]: {
    label: FilterLabel.feedback_value,
    id: 'feedback_value',
    group: 'feedback_value',
    icon: <ScoreSlidersIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'feedback_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'feedback_value',
        },
      },

      {
        label: FilterLabel.value,
        id: 'feedback_value',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'feedback_value',
        },
      },
    ],
  },
  [FilterLabel.feedback_score]: {
    label: FilterLabel.feedback_score,
    id: 'feedback_score',
    group: 'feedback_score',
    icon: <ScoreSlidersIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'feedback_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'feedback_score',
        },
      },
      {
        label: FilterLabel.score,
        id: 'feedback_score',
        defaultComp: filterCompsMap.eq,
        availableComps: [...filterNumericsExact, ...filterNumericRanges],
        valueType: 'number',
        metadata: {
          group: 'feedback_score',
        },
      },
    ],
  },
  [FilterLabel.feedback]: {
    label: FilterLabel.feedback,
    id: 'feedback',
    group: 'feedback',
    icon: <ScoreSlidersIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'feedback_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'feedback',
        },
      },
      {
        label: FilterLabel.score,
        id: 'feedback_score',
        defaultComp: filterCompsMap.eq,
        availableComps: [...filterNumericsExact, ...filterNumericRanges],
        valueType: 'number',
        metadata: {
          group: 'feedback',
        },
      },
      {
        label: FilterLabel.value,
        id: 'feedback_value',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'feedback',
        },
      },
    ],
  },
  [FilterLabel.feedback_source]: {
    label: FilterLabel.feedback_source,
    id: 'feedback_source',
    icon: <SourceIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: [filterCompsMap.eq, filterCompsMap.neq],
    valueType: 'string',
  },
  [FilterLabel.run_id]: {
    label: FilterLabel.run_id,
    id: 'id',
    icon: <ChainIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: [filterCompsMap.eq],
    valueType: 'string',
  },
  [FilterLabel.trace_id]: {
    label: FilterLabel.trace_id,
    id: 'trace_id',
    icon: <div>🦜</div>,
    defaultComp: filterCompsMap.eq,
    availableComps: [filterCompsMap.eq],
    valueType: 'string',
  },
  [FilterLabel.search]: {
    label: FilterLabel.search,
    name: 'search',
    icon: <SearchIcon className="h-4 w-4" />,
    fieldComponent: (props) => {
      const value =
        typeof props.value?.argument === 'string' ? props.value?.argument : '';
      return (
        <VariableInputForm
          value={value}
          onChange={(value) => props.onChange({ argument: value })}
          placeholder="Search"
        />
      );
    },
  } as FilterField,
};

const EXPERIMENTAL_SEARCH_ENABLED_RUN_FIELDS_LIST = [
  FilterLabel.is_root,
  FilterLabel.name,
  FilterLabel.status,
  FilterLabel.inputs,
  FilterLabel.outputs,
  FilterLabel.error,
  FilterLabel.input_kv,
  FilterLabel.output_kv,
  FilterLabel.extra_kv,
  FilterLabel.run_type,
  FilterLabel.search,
];

const CORE_RUN_FIELDS_LIST = [
  FilterLabel.is_root,
  FilterLabel.name,
  FilterLabel.run_type,
  FilterLabel.latency,
  FilterLabel.status,
  FilterLabel.tag,
  FilterLabel.metadata,
  FilterLabel.feedback,
  FilterLabel.feedback_source,
  FilterLabel.run_id,
  FilterLabel.trace_id,
];

export const SEARCH_ENABLED_RUN_FIELDS_LIST = [
  FilterLabel.search, // Search field
  FilterLabel.inputs, // Search field
  FilterLabel.outputs, // Search field
  FilterLabel.input_kv, // Search field
  FilterLabel.output_kv, // Search field
  FilterLabel.is_root,
  FilterLabel.name,
  FilterLabel.run_type,
  FilterLabel.latency,
  FilterLabel.status,
  FilterLabel.error, // Search field
  FilterLabel.tag,
  FilterLabel.metadata,
  FilterLabel.feedback,
  FilterLabel.feedback_source,
  FilterLabel.run_id,
  FilterLabel.trace_id,
];

export const getRunFields = ({
  searchEnabled,
  experimentalSearchEnabled = false,
  alertFilteringEnabled = false,
  disabledAlertRunFields,
}: {
  searchEnabled: boolean;
  experimentalSearchEnabled?: boolean;
  alertFilteringEnabled?: boolean;
  disabledAlertRunFields?: FilterLabel[];
}): FilterAnyField[] => {
  const fieldsList = experimentalSearchEnabled
    ? EXPERIMENTAL_SEARCH_ENABLED_RUN_FIELDS_LIST
    : searchEnabled
    ? SEARCH_ENABLED_RUN_FIELDS_LIST
    : CORE_RUN_FIELDS_LIST;

  if (alertFilteringEnabled) {
    return fieldsList
      .filter((label) => ALLOWED_ALERT_FILTERS.includes(label))
      .map((label) =>
        disabledAlertRunFields?.includes(label)
          ? {
              ...ALL_RUN_FIELDS[label],
              disabled: (_) => true,
              ...(label === FilterLabel.status && {
                disabledTooltip:
                  "This filter can't be altered because this is an Errored Runs alert.",
              }),
            }
          : ALL_RUN_FIELDS[label]
      );
  }

  return fieldsList.map((label) => ALL_RUN_FIELDS[label]);
};
