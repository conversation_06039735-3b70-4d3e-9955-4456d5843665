import ScoreSlidersIcon from '@/icons/ScoreSlidersIcon.svg?react';
import SearchIcon from '@/icons/SearchIcon.svg?react';

import { VariableInputForm } from '../../VariableInputForm/VariableInputForm';
import {
  FilterAnyField,
  FilterLabel,
  filterCompsMap,
  filterCompsString,
  filterNumericRanges,
  filterNumericsExact,
} from '../constants';
import ErrorIcon from '../icons/ErrorIcon.svg?react';
import InputIcon from '../icons/InputIcon.svg?react';
import LatencyIcon from '../icons/LatencyIcon.svg?react';
import MetadataIcon from '../icons/MetadataIcon.svg?react';
import NameIcon from '../icons/NameIcon.svg?react';
import OutputIcon from '../icons/OutputIcon.svg?react';
import RunTypeIcon from '../icons/RunTypeIcon.svg?react';
import SourceIcon from '../icons/SourceIcon.svg?react';
import StatusIcon from '../icons/StatusIcon.svg?react';
import TagIcon from '../icons/TagIcon.svg?react';
import TracesIcon from '../icons/TracesIcon.svg?react';

export const getConversationFields = (
  search_enabled: boolean
): FilterAnyField[] => [
  ...(search_enabled
    ? [
        {
          name: 'search',
          label: FilterLabel.search,
          icon: <SearchIcon className="h-4 w-4" />,
          fieldComponent: (props) => {
            const value =
              typeof props.value?.argument === 'string'
                ? props.value?.argument
                : '';
            return (
              <VariableInputForm
                value={value}
                onChange={(value) => props.onChange({ argument: value })}
                placeholder="Search"
              />
            );
          },
        },
      ]
    : []),
  {
    label: FilterLabel.trace_count,
    id: 'run_count',
    icon: <TracesIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.gte,
    availableComps: [...filterNumericsExact, ...filterNumericRanges],
    valueType: 'number',
  },
  ...(search_enabled
    ? [
        {
          label: FilterLabel.inputs,
          id: 'inputs',
          defaultComp: filterCompsMap.like,
          availableComps: [filterCompsMap.like],
          valueType: 'string' as const,
          icon: <InputIcon className="h-4 w-4" />,
          fieldComponent: (props) => {
            let value =
              typeof props.value?.argument === 'string'
                ? props.value?.argument
                : '';

            if (value.startsWith('%')) value = value.slice(1);
            if (value.endsWith('%')) value = value.slice(0, -1);

            return (
              <VariableInputForm
                value={value}
                placeholder="Value..."
                onChange={(value) =>
                  props.onChange({ comparator: 'like', argument: `%${value}%` })
                }
              />
            );
          },
        },
        {
          label: FilterLabel.outputs,
          id: 'outputs',
          defaultComp: filterCompsMap.like,
          availableComps: [filterCompsMap.like],
          valueType: 'string' as const,
          icon: <OutputIcon className="h-4 w-4" />,
          fieldComponent: (props) => {
            let value =
              typeof props.value?.argument === 'string'
                ? props.value?.argument
                : '';

            if (value.startsWith('%')) value = value.slice(1);
            if (value.endsWith('%')) value = value.slice(0, -1);

            return (
              <VariableInputForm
                value={value}
                placeholder="Value..."
                onChange={(value) =>
                  props.onChange({ comparator: 'like', argument: `%${value}%` })
                }
              />
            );
          },
        },
      ]
    : []),
  {
    label: FilterLabel.name,
    id: 'name',
    icon: <NameIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },
  ...(search_enabled
    ? [
        {
          label: FilterLabel.error,
          id: 'error',
          icon: <ErrorIcon className="h-4 w-4" />,
          defaultComp: filterCompsMap.like,
          availableComps: [filterCompsMap.like, filterCompsMap.notlike],
          valueType: 'string' as const,
        },
      ]
    : []),
  {
    label: FilterLabel.run_type,
    id: 'run_type',
    icon: <RunTypeIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },

  {
    label: FilterLabel.latency,
    id: 'latency',
    suffix: 's',
    icon: <LatencyIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.gte,
    availableComps: filterNumericRanges,
    valueType: 'number',
  },
  {
    label: FilterLabel.status,
    id: 'status',
    icon: <StatusIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },
  {
    label: FilterLabel.tag,
    id: 'tag',
    icon: <TagIcon className="h-4 w-4" />,
    disabled: ({ runStats }) =>
      (runStats?.run_facets ?? []).filter((i) => i.key === 'tag').length === 0,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },
  {
    label: FilterLabel.metadata,
    group: 'metadata',
    icon: <MetadataIcon className="h-4 w-4" />,
    disabled: ({ runStats }) =>
      (runStats?.run_facets ?? []).filter((i) => i.key === 'metadata_key')
        .length === 0,
    fields: [
      {
        label: FilterLabel.key,
        id: 'metadata_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'metadata',
        },
      },
      {
        label: FilterLabel.value,
        id: 'metadata_value',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string',
        metadata: {
          group: 'metadata',
        },
      },
    ],
  },
  {
    label: FilterLabel.feedback,
    id: 'feedback',
    group: 'feedback',
    icon: <ScoreSlidersIcon className="h-4 w-4" />,
    fields: [
      {
        label: FilterLabel.key,
        id: 'feedback_key',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string' as const,
        metadata: {
          group: 'feedback',
        },
      },
      {
        label: FilterLabel.score,
        id: 'feedback_score',
        defaultComp: filterCompsMap.eq,
        availableComps: [...filterNumericsExact, ...filterNumericRanges],
        valueType: 'number' as const,
        metadata: {
          group: 'feedback',
        },
      },
      {
        label: FilterLabel.value,
        id: 'feedback_value',
        defaultComp: filterCompsMap.eq,
        availableComps: filterCompsString,
        valueType: 'string' as const,
        metadata: {
          group: 'feedback',
        },
      },
    ],
  },

  {
    label: FilterLabel.feedback_source,
    id: 'feedback_source',
    disabled: ({ runStats }) =>
      (runStats?.run_facets ?? []).filter((i) => i.key === 'feedback_source')
        .length === 0,
    icon: <SourceIcon className="h-4 w-4" />,
    defaultComp: filterCompsMap.eq,
    availableComps: filterCompsString,
    valueType: 'string',
  },
];
