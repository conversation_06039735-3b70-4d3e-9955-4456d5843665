import { Dispatch, SetStateAction, useMemo, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';

import {
  InstanceFlags,
  useInstanceFlagOrDefault,
} from '@/hooks/useInstanceFlag';
import { useGetFilterView } from '@/hooks/useSwr';
import FilterIcon from '@/icons/FilterIcon.svg?react';
import { FilterViewType, RunStatsSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { CopyIconButton } from '../CopyButton/CopyButton';
import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import { astToString } from '../RunsTable/filter/ast';
import { SaveFilterPopover } from './FilterBar';
import { getActiveFieldsCount, useFilterAst } from './FilterBar.utils';
import { AddFilterButton } from './components/AddFilterButton';
import { ClearFilterButton } from './components/DeleteFilterButton';
import { FilterBarOperandsV2 } from './components/FilterBarOperands';
import { getConversationFields } from './fields/conversations';

const pluralRules = new Intl.PluralRules('en-US');

function FilterDialogContent(props: {
  stats: RunStatsSchema | undefined;
  value: string | undefined;
  onChange: (value: string | undefined) => void;

  setFeedbackUrls?: (urls: Record<string, string>) => void;

  onViewChange: (newView: string, filter?: string) => void;
}) {
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;
  const conversationFields = getConversationFields(searchEnabled);
  const [query, setQuery] = useFilterAst(props.value, conversationFields);

  const [feedbackUrlsByOperand, setFeedbackUrlsByOperand] = useState<
    Record<string, Record<string, string>>
  >({});

  const { sessionId } = useParams();
  const [searchParams] = useSearchParams();
  const filterViewId = searchParams.get('custom_filter_view_id');
  const { data: filterView, isLoading: filterViewLoading } = useGetFilterView(
    sessionId,
    filterViewId ?? undefined
  );

  return (
    <div className="group flex flex-col items-stretch border-tertiary">
      <div className="flex flex-col p-3 pb-1">
        <div className="flex items-start justify-between">
          <span className="text-sm font-medium">
            {filterView?.display_name ?? 'Filters'}
          </span>

          {props.value && (
            <div className="flex items-start gap-1 opacity-0 transition-all focus-within:opacity-100 group-hover:opacity-100 [&:has([data-state=open])]:opacity-100">
              {props.value && (
                <div className="flex justify-end gap-2">
                  <SaveFilterPopover
                    className="text-xxs"
                    filter={props.value}
                    filterView={filterView}
                    onViewChange={props.onViewChange}
                    isLoading={filterViewLoading}
                    saveFilterType={FilterViewType.THREADS}
                  />
                </div>
              )}
              <ClearFilterButton
                hasFilters={query.length > 0}
                onDeleteFilterValues={() => {
                  setQuery([]);
                  props.onChange(undefined);
                  props.onViewChange('');
                }}
              />

              <CopyIconButton
                className="h-6 w-6 p-0 opacity-0 transition-all focus-within:opacity-100 group-hover:opacity-100"
                copy={props.value ?? ''}
              />
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col gap-2 px-3 pb-3">
        <FilterBarOperandsV2
          fields={conversationFields}
          operandsWithFields={query}
          handleChange={(value) => {
            const newQuery = setQuery(value);
            props.onChange(astToString(newQuery));
          }}
          stats={props.stats}
          feedbackUrlsByOperand={feedbackUrlsByOperand}
          setFeedbackUrlsByOperand={setFeedbackUrlsByOperand}
        />

        <AddFilterButton
          variant="v2"
          hideFilterTitle={false}
          hasFilters={query.length > 0}
          onClick={() => {
            props.onChange(
              astToString(setQuery([...query, { comparisons: [{}] }]))
            );
          }}
        />
      </div>
    </div>
  );
}

export function ConversationFilterBar(props: {
  stats: RunStatsSchema | undefined;

  value: string | undefined;
  onChange: (value: string | undefined) => void;

  feedbackUrls?: Record<string, string>;
  setFeedbackUrls?: (urls: Record<string, string>) => void;

  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;

  onViewChange: (newView: string, filter?: string) => void;
}) {
  const { open, setOpen } = props;
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;

  const conversationFields = getConversationFields(searchEnabled);

  const nActive = useMemo(
    () => getActiveFieldsCount({ filter: props.value }, conversationFields),
    [props.value]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <button
          type="button"
          className={cn(
            'flex flex-shrink-0 items-center gap-1.5 rounded-md border border-secondary px-2 py-1 text-sm font-medium text-tertiary transition-all hover:bg-secondary active:bg-secondary data-[state=open]:bg-tertiary',
            nActive > 0 &&
              'border-brand bg-brand-tertiary text-brand-tertiary hover:bg-brand-secondary active:bg-brand-secondary data-[state=open]:bg-brand-secondary'
          )}
        >
          <FilterIcon />
          {nActive === 0 ? (
            <span>Filters</span>
          ) : (
            <span>
              {
                {
                  one: `1 filter`,
                  two: `${nActive} filters`,
                  few: `${nActive} filters`,
                  other: `${nActive} filters`,
                }[pluralRules.select(nActive)]
              }
            </span>
          )}
        </button>
      </PopoverTrigger>

      <PopoverContent
        align="start"
        className={cn('z-10 w-auto min-w-[300px] overflow-hidden p-0')}
        avoidCollisions={false}
        side={'bottom'}
      >
        <FilterDialogContent
          stats={props.stats}
          value={props.value}
          onChange={props.onChange}
          setFeedbackUrls={props.setFeedbackUrls}
          onViewChange={props.onViewChange}
        />
      </PopoverContent>
    </Popover>
  );
}
