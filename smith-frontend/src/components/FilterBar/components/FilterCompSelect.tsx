import { ChevronDownIcon } from '@langchain/untitled-ui-icons';

import { DisabledTooltip } from '@/components/DisabledTooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { RunStatsSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { FilterAnyField } from '../constants';

export function FilterCompSelect(props: {
  fields: FilterAnyField[];
  stats: RunStatsSchema | undefined;
  value: FilterAnyField | undefined;
  onChange: (value: FilterAnyField) => void;
  variant: 'v1' | 'v2';
  onRemove: () => void;
}) {
  const isDisabled = !!props.value?.disabled?.({
    runStats: props.stats,
  });

  return (
    <DropdownMenu
      defaultOpen={props.value?.label == null}
      onOpenChange={(open) => {
        if (!open && !props.value) {
          // used to remove filter clause if no filter type selected.
          props.onRemove();
        }
      }}
    >
      <DropdownMenuTrigger
        className={cn(
          'flex items-center gap-1.5 whitespace-nowrap rounded-md border border-tertiary bg-background px-2 py-1',
          { 'text-tertiary': props.value?.label == null },
          { 'opacity-50': isDisabled },
          {
            'border-none bg-tertiary hover:bg-quaternary':
              props.variant === 'v2',
          }
        )}
        disabled={isDisabled}
      >
        {props.value?.icon}
        <DisabledTooltip
          disabled={isDisabled}
          disabledMsg={props.value?.disabledTooltip}
        >
          <span>{props.value?.label ?? 'Field'}</span>
        </DisabledTooltip>
        {!isDisabled && <ChevronDownIcon className="h-3 w-3 text-tertiary" />}
      </DropdownMenuTrigger>
      <DropdownMenuPortal>
        <DropdownMenuContent
          align="start"
          className="max-h-[50vh] overflow-y-auto"
        >
          {props.fields.map((field, i) => (
            <DropdownMenuItem
              key={i}
              className={cn(
                'flex items-center gap-1.5 text-sm',
                props.variant === 'v2' && 'px-2 py-1 text-xxs'
              )}
              disabled={
                field.disabled
                  ? field.disabled({ runStats: props.stats })
                  : false
              }
              onClick={() => props.onChange(field)}
            >
              {field?.icon}
              <span>{field.label}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
}
