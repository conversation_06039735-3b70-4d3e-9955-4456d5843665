import { PlusIcon, XIcon } from '@langchain/untitled-ui-icons';

import { useEffect, useState } from 'react';

import { DisabledTooltip } from '@/components/DisabledTooltip';
import { Comparison } from '@/components/RunsTable/filter/ast';
import { RunStatsSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import {
  fieldToComparison,
  getFeedbackType,
  getOperandFromRunFacets,
} from '../FilterBar.utils';
import { FilterAnyField, FilterGroup, StandardFilterField } from '../constants';
import { FilterCompSelect } from './FilterCompSelect';
import { FilterFieldInput } from './FilterFieldInput';

export function FilterBarGroupOperands({
  fields,
  comparisons,
  field,
  stats,
  updateEveryChange,
  handleOperandsChange,
  handleRemove,
  isDisabled,
  groupOperandIdx,
}: {
  comparisons: Partial<Comparison>[];
  fields: FilterAnyField[];
  field: FilterGroup;
  stats?: RunStatsSchema;
  updateEveryChange?: boolean;
  handleOperandsChange: (
    value:
      | Partial<Comparison>[]
      | ((value: Partial<Comparison>[]) => Partial<Comparison>[])
  ) => void;
  handleRemove: () => void;
  isDisabled: boolean;
  groupOperandIdx: number;
}) {
  const [keyOperand, valueOperand] = comparisons;

  const [valueField, setValueField] = useState<StandardFilterField>(
    field.fields[1]
  );

  const [keyField] = field.fields;
  const handleChildChange = (
    value: Partial<Comparison>,
    operandIdx: number
  ) => {
    handleOperandsChange((operands) => {
      return operands
        .map((item, idx) =>
          idx === operandIdx
            ? { ...value, metadata: { groupOperandIdx } }
            : item
        )
        .slice(0, operandIdx + 1);
    });
  };

  useEffect(() => {
    const feedbackType = getFeedbackType({
      runStats: stats,
      comparison: keyOperand,
    });
    if (feedbackType) {
      setValueField(
        field.fields.find((f) => f.id === feedbackType) ?? field.fields[1]
      );
    }
  }, [keyOperand, stats, field.fields]);

  return (
    <div className="flex flex-col gap-y-2">
      <div className="flex flex-row items-center gap-x-2">
        <FilterCompSelect
          variant="v2"
          fields={fields}
          value={field}
          stats={stats}
          onChange={(field) => {
            handleOperandsChange([fieldToComparison(field)]);
          }}
          onRemove={handleRemove}
        />
        {!field.omitKeyLabel && (
          <span className="text-xs text-tertiary">{keyField.label}</span>
        )}

        <FilterFieldInput
          stats={stats}
          field={keyField}
          value={keyOperand}
          onChange={(value) => {
            handleChildChange(value, 0);
          }}
          updateEveryChange={updateEveryChange}
          variant="v2"
        />
        {valueOperand ? (
          <div className="flex flex-row items-center gap-x-2">
            <span className="text-xs text-tertiary">{valueField.label}</span>
            <FilterFieldInput
              stats={stats}
              field={valueField}
              value={valueOperand}
              onChange={(value) => handleChildChange(value, 1)}
              updateEveryChange={updateEveryChange}
              getArgumentValues={({ runStats }) => {
                if (runStats) {
                  return getOperandFromRunFacets({
                    runStats,
                    comparisons: comparisons,
                  });
                }
                return [];
              }}
              variant="v2"
            />
          </div>
        ) : (
          <button
            type="button"
            className={cn(
              'flex cursor-pointer flex-row items-center gap-2 self-start rounded-md bg-background px-2 py-1 transition-colors focus-within:bg-secondary hover:bg-secondary-hover active:bg-secondary disabled:opacity-50 disabled:hover:bg-transparent'
            )}
            onClick={() => {
              handleOperandsChange((operands) => [
                ...operands,
                {
                  identifier: valueField.id,
                  comparator: valueField.defaultComp.id,
                  metadata: { groupOperandIdx },
                },
              ]);
            }}
          >
            <PlusIcon className="h-4 w-4" />
            Value
          </button>
        )}
        <DisabledTooltip
          disabled={isDisabled}
          disabledMsg={field?.disabledTooltip}
        >
          <button
            type="button"
            className="rounded-md p-1 transition-all focus-within:bg-secondary active:bg-secondary enabled:hover:bg-secondary-hover disabled:pointer-events-none disabled:opacity-50"
            data-testid="remove-filter-button"
            disabled={isDisabled}
            onClick={() => {
              if (valueOperand) {
                handleOperandsChange((operands) =>
                  operands.filter(
                    ({ identifier }) => identifier !== valueField.id
                  )
                );
              } else {
                handleRemove();
              }
            }}
          >
            <XIcon className="h-4 w-4 cursor-pointer text-tertiary" />
          </button>
        </DisabledTooltip>
      </div>
    </div>
  );
}
