import { Trash04Icon } from '@langchain/untitled-ui-icons';

import { useParams } from 'react-router-dom';

import { DeleteConfirmationButton } from '@/components/Delete';
import { Tooltip } from '@/components/Tooltip/Tooltip';
import { useDeleteFilterViewMutation } from '@/hooks/useSwr';
import { FilterViewSchema } from '@/types/schema';

export function DeleteSavedFilterButton(props: {
  filterView: FilterViewSchema;
  onToggleSwitch?: () => void;
  setFilterBarOpen: (open: boolean) => void;
}) {
  const { sessionId } = useParams();
  const deleteFilter = useDeleteFilterViewMutation(
    sessionId,
    props.filterView?.id
  );

  const handleDelete = async () => {
    if (props.filterView?.id) {
      await deleteFilter.trigger({});
    }
    props.setFilterBarOpen(false);
    if (props.onToggleSwitch) {
      props.onToggleSwitch();
    }
  };

  return (
    <Tooltip title="Delete filter" placement="top" tooltipClassName="p-0">
      <DeleteConfirmationButton
        title="Confirm Deletion"
        description="Are you sure you want to delete this filter?"
        deleteButtonText="Delete"
        onDelete={handleDelete}
        isDeleting={deleteFilter.isMutating}
        trigger={
          <button
            type="button"
            className="flex items-center justify-center rounded-md p-1 text-xxs outline-none transition-all enabled:hover:bg-secondary disabled:pointer-events-none disabled:opacity-50"
          >
            <Trash04Icon className="h-4 w-4" />
          </button>
        }
      />
    </Tooltip>
  );
}

export function ClearFilterButton(props: {
  hasFilters: boolean;
  onDeleteFilterValues: () => void;
  disabled?: boolean;
}) {
  if (!props.hasFilters) {
    return null;
  }
  return (
    <Tooltip title="Clear filters" placement="top" tooltipClassName="p-0">
      <button
        type="button"
        className="flex items-center justify-center rounded-md p-1 text-xxs text-quaternary outline-none transition-all enabled:hover:bg-secondary-hover disabled:pointer-events-none disabled:opacity-50"
        onClick={props.onDeleteFilterValues}
        disabled={props.disabled}
      >
        <Trash04Icon className="h-4 w-4" />
      </button>
    </Tooltip>
  );
}
