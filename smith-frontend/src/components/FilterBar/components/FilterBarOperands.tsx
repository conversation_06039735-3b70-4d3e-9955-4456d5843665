import {
  ThumbsDownIcon,
  ThumbsUpIcon,
  XIcon,
} from '@langchain/untitled-ui-icons';

import { DisabledTooltip } from '@/components/DisabledTooltip';
import { Comparison, astToString } from '@/components/RunsTable/filter/ast';
import { RunStatsSchema } from '@/types/schema';

import { GroupedComparison, fieldToComparison } from '../FilterBar.utils';
import { FilterAnyField, FilterField } from '../constants';
import { FilterBarGroupOperands } from './FilterBarGroupOperands';
import { FilterCompSelect } from './FilterCompSelect';
import { FilterFieldInput } from './FilterFieldInput';

interface UserScoreButtonsProps {
  userScoreUrl: string;
  userScoreStrsToClear: string[];
  setFeedbackUrlsByOperand?: (
    updater: (
      urls: Record<string, Record<string, string>>
    ) => Record<string, Record<string, string>>
  ) => void;
}

function UserScoreButtons({
  userScoreUrl,
  userScoreStrsToClear,
  setFeedbackUrlsByOperand,
}: UserScoreButtonsProps) {
  return (
    <>
      <button
        type="button"
        className="rounded-md p-1 transition-all focus-within:bg-secondary hover:bg-secondary-hover active:bg-secondary"
        data-testid="filter-user-score-pos-button"
        onClick={() => {
          fetch(`${userScoreUrl}?score=1`);
          setFeedbackUrlsByOperand?.((urls) => {
            const newUrls = { ...urls };
            userScoreStrsToClear.forEach((str) => {
              delete newUrls[str].user_score;
            });
            return newUrls;
          });
        }}
      >
        <ThumbsUpIcon className="h-4 w-4 cursor-pointer text-tertiary" />
      </button>
      <button
        type="button"
        className="rounded-md p-1 transition-all focus-within:bg-secondary hover:bg-secondary-hover active:bg-secondary"
        data-testid="filter-user-score-neg-button"
        onClick={() => {
          fetch(`${userScoreUrl}?score=0`);
          setFeedbackUrlsByOperand?.((urls) => {
            const newUrls = { ...urls };
            userScoreStrsToClear.forEach((str) => {
              delete newUrls[str].user_score;
            });
            return newUrls;
          });
        }}
      >
        <ThumbsDownIcon className="h-4 w-4 cursor-pointer text-tertiary" />
      </button>
    </>
  );
}

function FilterBarOperandField(props: {
  fields: FilterAnyField[];
  field: FilterField;
  stats?: RunStatsSchema;
  onChange: (
    value:
      | Partial<Comparison>[]
      | ((value: Partial<Comparison>[]) => Partial<Comparison>[])
  ) => void;
  handleRemove: () => void;
  idx: number;
  comparisons: Partial<Comparison>[];
  updateEveryChange?: boolean;
  isDisabled: boolean;
  feedbackUrlsByOperand?: Record<string, Record<string, string>>;
}) {
  return (
    <div className="flex flex-row items-center gap-x-1">
      <FilterCompSelect
        variant="v2"
        fields={props.fields}
        value={props.field}
        stats={props.stats}
        onChange={(field) => {
          props.onChange([fieldToComparison(field)]);
        }}
        onRemove={props.handleRemove}
      />

      <FilterFieldInput
        key={props.idx}
        stats={props.stats}
        field={props.field}
        value={props.comparisons[0]}
        onChange={(entry) => {
          props.onChange([entry]);
        }}
        updateEveryChange={props.updateEveryChange}
        variant="v2"
      />

      <DisabledTooltip
        disabled={props.isDisabled}
        disabledMsg={props.field?.disabledTooltip}
      >
        <button
          type="button"
          className="rounded-md p-1 transition-all focus-within:bg-secondary active:bg-secondary enabled:hover:bg-secondary-hover disabled:pointer-events-none disabled:opacity-50"
          data-testid="remove-filter-button"
          disabled={props.isDisabled}
          onClick={() => {
            props.handleRemove();
          }}
        >
          <XIcon className="h-4 w-4 cursor-pointer text-tertiary" />
        </button>
      </DisabledTooltip>
    </div>
  );
}

export function FilterBarOperandsV2(props: {
  fields: FilterAnyField[];

  operandsWithFields: GroupedComparison[];
  handleChange: (value: Pick<GroupedComparison, 'comparisons'>[]) => void;

  feedbackUrlsByOperand?: Record<string, Record<string, string>>;
  setFeedbackUrlsByOperand?: (
    updater: (
      urls: Record<string, Record<string, string>>
    ) => Record<string, Record<string, string>>
  ) => void;

  stats?: RunStatsSchema;

  updateEveryChange?: boolean;
}) {
  if (!props.operandsWithFields.length) return null;
  return (
    <div className="flex flex-col gap-y-2 text-xxs">
      {props.operandsWithFields.map(({ field, comparisons }, idx) => {
        const isDisabled =
          field?.disabled?.({ runStats: props.stats }) ?? false;

        let userScoreUrl: string | null = null;
        const userScoreStrsToClear: string[] = [];
        for (const op of comparisons) {
          const str = astToString(op);
          if (str && props.feedbackUrlsByOperand?.[str]?.user_score) {
            userScoreStrsToClear.push(str);
            userScoreUrl = props.feedbackUrlsByOperand[str]?.user_score;
          }
        }

        const handleOperandsChange = (
          value:
            | Partial<Comparison>[]
            | ((value: Partial<Comparison>[]) => Partial<Comparison>[])
        ) => {
          let next = typeof value === 'function' ? value(comparisons) : value;

          let url: string | null = null;
          for (const op of comparisons) {
            const str = astToString(op);
            if (str && props.feedbackUrlsByOperand?.[str]?.user_edited) {
              url = props.feedbackUrlsByOperand?.[str]?.user_edited;
              break;
            }
          }
          if (url) {
            fetch(
              `${url}?value=${astToString({
                operator: 'and',
                operands: comparisons,
              })}&correction=${astToString({
                operator: 'and',
                operands: next,
              })}`
            );
          }

          next = next.filter((i) => 'comparator' in i);
          const depth = next.length > 1 ? 1 : 0;

          props.handleChange(
            props.operandsWithFields.map((item, i) =>
              i === idx
                ? { comparisons: next.map((i) => ({ ...i, depth })) }
                : item
            )
          );
        };

        const handleRemove = () => {
          comparisons.forEach((op) => {
            const str = astToString(op);
            const url = str
              ? props.feedbackUrlsByOperand?.[str]?.user_removed
              : null;
            if (url) {
              fetch(`${url}?value=${astToString(op)}`);
            }
          });

          props.handleChange(
            props.operandsWithFields.filter((_, i) => i !== idx)
          );
        };

        return (
          <div className="flex flex-col items-start gap-1 rounded-md">
            <div className="text-[10px] leading-tight text-quaternary">
              {idx === 0 ? 'Where' : 'And'}
            </div>
            {!field ? (
              <FilterCompSelect
                variant="v2"
                fields={props.fields}
                value={field}
                stats={props.stats}
                onChange={(field) => {
                  handleOperandsChange([fieldToComparison(field)]);
                }}
                onRemove={handleRemove}
              />
            ) : 'group' in field ? (
              <FilterBarGroupOperands
                key={`${field.label}-${idx}`}
                fields={props.fields}
                field={field}
                stats={props.stats}
                comparisons={comparisons}
                handleOperandsChange={handleOperandsChange}
                handleRemove={handleRemove}
                isDisabled={isDisabled}
                groupOperandIdx={idx}
              />
            ) : (
              <FilterBarOperandField
                key={`${field.label}-${idx}`}
                fields={props.fields}
                field={field}
                stats={props.stats}
                onChange={handleOperandsChange}
                handleRemove={handleRemove}
                idx={idx}
                comparisons={comparisons}
                isDisabled={isDisabled}
                feedbackUrlsByOperand={props.feedbackUrlsByOperand}
              />
            )}
            {userScoreUrl && (
              <UserScoreButtons
                userScoreUrl={userScoreUrl}
                userScoreStrsToClear={userScoreStrsToClear}
                setFeedbackUrlsByOperand={props.setFeedbackUrlsByOperand}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}
