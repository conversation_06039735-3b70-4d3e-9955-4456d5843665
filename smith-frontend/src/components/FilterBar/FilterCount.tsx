import { useMemo } from 'react';

import {
  InstanceFlags,
  useInstanceFlagOrDefault,
} from '@/hooks/useInstanceFlag';

import { getActiveFieldsCount } from './FilterBar.utils';
import { getRunFields } from './fields/runs';

const pluralRules = new Intl.PluralRules('en-US');

export function RunFilterCount(props: { value: string | undefined }) {
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;
  const runFields = getRunFields({
    searchEnabled: searchEnabled,
  });

  const nActive = useMemo(
    () => getActiveFieldsCount({ filter: props.value }, runFields),
    [props.value]
  );

  if (nActive === 0) return 'All runs';
  return {
    one: `1 filter`,
    two: `${nActive} filters`,
    few: `${nActive} filters`,
    other: `${nActive} filters`,
  }[pluralRules.select(nActive)];
}
