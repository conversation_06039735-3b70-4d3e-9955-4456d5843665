import { useMemo } from 'react';
import { useDebounce } from 'use-debounce';

import { COLUMN_VISIBILITY } from '@/Pages/Project/constants';
import { ErrorBoundedChartData } from '@/Pages/SingleDashboard/CustomChart';
import { ChartFormInput } from '@/Pages/SingleDashboard/CustomChartsCrudPane';
import { ChartWrapper } from '@/Pages/SingleDashboard/components/CustomChartComponents';
import { useCustomChartsPreviewData } from '@/Pages/SingleDashboard/hooks/useCustomChartsPreviewData';
import { METRICS } from '@/Pages/SingleDashboard/utils/constants';
import { useAlertState } from '@/components/Alerts/useAlertState';
import { DateTimeRangePicker } from '@/components/DateTimeRangePicker/DateTimeRangePicker';
import { AlertAttribute } from '@/types/alerts';
import {
  CustomChartPreviewSchema,
  CustomChartPreviewThresholdConfig,
} from '@/types/schema';

import { RunsTable } from '../RunsTable';
import { SearchModel } from '../RunsTable/types';
import { Skeleton } from '../Skeleton';
import {
  getChartMetric,
  getDenominatorFilter,
  validateThreshold,
} from './utils';

interface AlertPreviewProps {
  sessionId: string;
  withRunsTable?: boolean;
  hideHeader?: boolean;
}

const FEEDBACK_KEY_ERROR_REGEX = /feedback key is required/i;

export function AlertPreview({
  sessionId,
  withRunsTable,
  hideHeader,
}: AlertPreviewProps) {
  const {
    attribute,
    feedbackKey,
    threshold,
    operator,
    windowMinutes,
    aggregation,
    filter,
  } = useAlertState();

  const chartMetric = getChartMetric(attribute, aggregation);

  const chartFilter = useMemo<SearchModel>(() => {
    if (attribute !== AlertAttribute.feedback_score) {
      if (
        (attribute === AlertAttribute.error_count ||
          attribute === AlertAttribute.run_count) &&
        aggregation === 'pct' &&
        filter.filter
      ) {
        const newFilter = getDenominatorFilter(filter.filter);
        if (newFilter) {
          return { filter: newFilter };
        }
      }
      return filter;
    }
    return {};
  }, [attribute, aggregation, filter]);

  const chartFormInput = useMemo<ChartFormInput>(() => {
    const sessionIds = sessionId ? [sessionId] : [];

    return {
      title: '',
      description: '',
      filters: chartFilter,
      dataSeries: [],
      metric: chartMetric,
      chartType: 'line',
      sessionIds,
      feedbackKey:
        attribute === AlertAttribute.feedback_score ? feedbackKey : undefined,
      customStride: { minutes: windowMinutes === '5' ? 5 : 15 },
      timeRange: { duration: '3h' },
    };
  }, [chartMetric, attribute, chartFilter, sessionId, windowMinutes]);

  const {
    previewData,
    seriesPayload,
    previewTimeModel,
    setPreviewTimeModel,
    previewDataIsLoading,
    previewDataError,
    previewChartsParams,
  } = useCustomChartsPreviewData(chartFormInput);

  const chartData = useMemo<CustomChartPreviewSchema | null>(() => {
    if (
      previewDataIsLoading ||
      previewDataError ||
      !previewData ||
      !seriesPayload
    ) {
      return null;
    }

    return {
      chart_type: 'line' as const,
      title: chartFormInput.title,
      description: chartFormInput.description,
      series: seriesPayload.series,
      data: previewData,
    };
  }, [
    previewDataIsLoading,
    previewDataError,
    previewData,
    seriesPayload,
    chartFormInput,
  ]);

  const thresholdConfig =
    useMemo<CustomChartPreviewThresholdConfig | null>(() => {
      if (
        !threshold ||
        !validateThreshold(threshold) ||
        !chartFormInput.metric ||
        previewData.length === 0
      )
        return null;

      const thresholdValue = parseFloat(threshold);

      // the user can only select an "operator" when they are alerting on feedback_score, otherwise we
      // are either checking if latency >= <some_value> or err_run_count >= <some_value>
      const thresholdType =
        chartFormInput.metric === 'feedback_score_avg' ? operator : 'gte';

      return {
        data: {
          series_id: previewData[0].series_id,
          timestamp: previewData[0].timestamp,
          value: thresholdValue,
        },
        metric: chartFormInput.metric,
        type: thresholdType,
        color: 'rgba(255, 0, 0, 0.1)',
      };
    }, [threshold, operator, chartFormInput, previewData]);

  const runsFilterInput = useMemo(
    () => ({
      ...filter,
      session: [sessionId],
      start_time: previewChartsParams.start_time,
      end_time: previewChartsParams.end_time,
    }),
    [filter, sessionId, previewChartsParams]
  );

  const [debouncedRunsFilter] = useDebounce(runsFilterInput, 1000);

  return (
    <div className="flex flex-col gap-6 p-6">
      {!hideHeader && (
        <div className="flex flex-col gap-1">
          <h3 className="text-md font-medium">Alert Preview</h3>
          <p className="text-sm text-tertiary">
            See how the metric configured has performed over the time frame
            selected.
          </p>
        </div>
      )}

      <div className="flex flex-col gap-4">
        <ChartWrapper>
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <p className="text-md">{METRICS[chartMetric]}</p>
              <DateTimeRangePicker
                value={previewTimeModel}
                onChange={setPreviewTimeModel}
                align="end"
                hideAllTime
              />
            </div>
            {previewDataError &&
              (FEEDBACK_KEY_ERROR_REGEX.test(previewDataError.message) ? (
                <p className="text-sm text-tertiary">
                  Select a feedback key in order to see the alert preview.
                </p>
              ) : (
                <p className="text-sm text-red-500">
                  Error loading chart data: {previewDataError.message}
                </p>
              ))}

            {!previewDataError && previewDataIsLoading && (
              <Skeleton className="h-[400px]" />
            )}

            {!previewDataError && chartData && (
              <ErrorBoundedChartData
                chart={chartData}
                type={chartData.chart_type}
                timeFilter={{
                  timeModel: previewTimeModel,
                  setTimeModel: setPreviewTimeModel,
                  customChartsParams: previewChartsParams,
                }}
                thresholdConfig={thresholdConfig}
                hideLegend
                isLastColumn
                smoothCurve={attribute !== AlertAttribute.feedback_score}
                seriesColorsMap={{}}
              />
            )}
          </div>
        </ChartWrapper>
        {withRunsTable && (
          <RunsTable
            view="all"
            onViewChange={() => null}
            tableDisplay="list"
            runsFilter={{}}
            debouncedRunsFilter={debouncedRunsFilter}
            searchModel={{}}
            setSearchModel={() => null}
            columnVisibilityModel={COLUMN_VISIBILITY.default}
            setColumnVisibilityModel={() => null}
            onColumnVisibilityResetClick={() => null}
            timeModel={{}}
            setTimeModel={() => {}}
            navigateToRun={() => null}
            paginationModel={{ pageSize: 0, pageIndex: 0 }}
            setPaginationModel={() => {}}
            resetPaginationModel={() => {}}
            sortModel={[]}
            setSortModel={() => {}}
            filterModel={{ items: [] }}
            setFilterModel={() => {}}
            initialState={{}}
            withToolbar={false}
            className="max-h-[1000px]"
          />
        )}
      </div>
    </div>
  );
}
