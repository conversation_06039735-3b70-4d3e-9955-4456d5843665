import { Tooltip as Mu<PERSON><PERSON>ooltip } from '@mui/joy';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';

import { ReactNode } from 'react';

import { cn } from '@/utils/tailwind';

export function TooltipV2({
  title,
  description,
  children,
  className,
  tooltipClassName,
  placement = 'top',
}: {
  title?: string | ReactNode;
  description?: string;
  children: ReactNode;
  className?: string;
  tooltipClassName?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
}) {
  if (!title && !description) {
    return children;
  }

  return (
    <TooltipPrimitive.Root>
      <TooltipPrimitive.Trigger asChild>
        <div className={className}>{children}</div>
      </TooltipPrimitive.Trigger>
      <TooltipPrimitive.Portal>
        <TooltipPrimitive.Content
          side={placement}
          className={cn(
            'z-50 overflow-hidden rounded-md border border-secondary bg-background px-3 py-1.5 text-sm shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
            tooltipClassName
          )}
        >
          <div className="flex max-w-[320px] flex-col gap-2 text-xxs">
            {typeof title === 'string' ? (
              <p className="font-semibold">{title}</p>
            ) : (
              title
            )}
            {description && (
              <p
                className={cn('font-medium', title && 'text-[var(--gray-300)]')}
              >
                {description}
              </p>
            )}
          </div>
          <TooltipPrimitive.Arrow className="fill-background" />
        </TooltipPrimitive.Content>
      </TooltipPrimitive.Portal>
    </TooltipPrimitive.Root>
  );
}

export function Tooltip({
  title,
  description,
  children,
  className,
  tooltipClassName,
  placement,
}: {
  title?: string | ReactNode;
  description?: string;
  children: ReactNode;
  className?: string;
  tooltipClassName?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
}) {
  if (!title && !description) {
    return children;
  }

  return (
    <MuiTooltip
      title={
        <div
          className={cn(
            'flex max-w-[320px] flex-col gap-2 p-2 text-xxs',
            tooltipClassName
          )}
        >
          {typeof title === 'string' ? (
            <p className="font-semibold">{title}</p>
          ) : (
            title
          )}
          {description && (
            <p className={cn('font-medium', title && 'text-[var(--gray-300)]')}>
              {description}
            </p>
          )}
        </div>
      }
      placement={placement}
    >
      <div className={className}>{children}</div>
    </MuiTooltip>
  );
}
