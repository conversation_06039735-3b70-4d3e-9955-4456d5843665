import { CheckIcon, Copy06Icon } from '@langchain/untitled-ui-icons';
import Button from '@mui/joy/Button';
import { SxProps } from '@mui/joy/styles/types';

import React from 'react';

import { cn } from '@/utils/tailwind';

import { Tooltip } from '../Tooltip/Tooltip';
import { useCopy } from './useCopy';

export function CopyButton(props: {
  copy: string;
  variant?: 'full' | 'icon';
  sx?: SxProps;
  copyText?: string;
}) {
  const { copied, onCopy } = useCopy({ copy: props.copy });

  if (props.variant === 'icon') {
    return <CopyIconButton copy={props.copy} copyText={props.copyText} />;
  }

  return (
    <Button
      size={'sm'}
      sx={props.sx}
      startDecorator={copied ? <CheckIcon /> : <Copy06Icon />}
      color="neutral"
      variant="outlined"
      onClick={(event) => {
        event.stopPropagation();
        onCopy();
      }}
    >
      {copied ? 'Copied' : props.copyText ?? 'Copy'}
    </Button>
  );
}

export function CopyIconButton(props: {
  copy: string;
  className?: string;
  copyText?: React.ReactNode;
}) {
  const { copied, onCopy } = useCopy({ copy: props.copy });

  return (
    <Tooltip
      title={copied ? 'Copied' : props.copyText ?? 'Copy'}
      placement="top"
      tooltipClassName="p-0"
    >
      <button
        type="button"
        onClick={(e) => {
          e.stopPropagation();
          onCopy();
        }}
        className={cn(
          'flex items-center justify-center rounded-md p-1 transition duration-75 hover:bg-secondary',
          props.className
        )}
      >
        {copied ? (
          <CheckIcon className="h-4 w-4" />
        ) : (
          <Copy06Icon className="h-4 w-4" />
        )}
      </button>
    </Tooltip>
  );
}
