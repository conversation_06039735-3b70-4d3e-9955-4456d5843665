import { FC, ReactNode, RefObject } from 'react';

import { cn } from '@/utils/tailwind';

import { CopyButton } from './CopyButton/CopyButton';

type CollapsedMessageProps = {
  bodyRef?: RefObject<HTMLDivElement>;
  testid?: string;
  onCollapse: (collapsed: boolean) => void;
  role: string;
  toCopy: string;
  headerRight?: ReactNode;
  actions?: ReactNode;
  isSchemaEditorVisible: boolean;
  addToolCallComponent?: ReactNode;
  trashIcon?: ReactNode;
  expandToggle?: ReactNode;
  readOnly?: boolean;
};

const CollapsedMessage: FC<CollapsedMessageProps> = ({
  bodyRef,
  testid,
  onCollapse,
  role,
  toCopy,
  headerRight,
  actions,
  isSchemaEditorVisible,
  addToolCallComponent,
  trashIcon,
  expandToggle,
  readOnly,
}) => {
  return (
    <div ref={bodyRef} data-testid={testid}>
      <div
        className={cn(
          'group relative w-full rounded-lg border border-secondary bg-background',
          readOnly && 'bg-secondary text-tertiary'
        )}
      >
        <div
          className="flex w-full items-center justify-between gap-3 rounded-[7px] px-4 py-3 hover:bg-secondary"
          onClick={() => onCollapse(false)}
        >
          <div
            className="flex-none text-sm font-semibold uppercase text-tertiary"
            style={{ fontVariant: 'small-caps' }}
          >
            {role}
          </div>
          <div className="line-clamp-1 flex-1 break-all text-left text-sm">
            {toCopy}
          </div>
          <div className="flex flex-none items-center gap-1">
            <div
              className={cn(
                'flex flex-none items-center gap-2 opacity-0 transition-all focus-within:opacity-100 group-hover:opacity-100',
                isSchemaEditorVisible && 'hidden'
              )}
            >
              {headerRight}
              {addToolCallComponent}
              <CopyButton variant="icon" copy={toCopy} />
              {actions}
              {trashIcon}
            </div>
            {expandToggle}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollapsedMessage;
