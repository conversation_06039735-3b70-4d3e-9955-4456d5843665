import { SortingState } from '@tanstack/react-table';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { DEFAULT_10_PAGINATION_MODEL } from '@/constants/dataGridConstants';
import {
  GridPaginationModel,
  tryJsonParseSearchParam,
} from '@/hooks/useDataGridState';
import { useDebouncedSearchStringQueryParams } from '@/hooks/useDebouncedSearchStringQueryParams';
import { useStateFromSearchParams } from '@/hooks/useStateFromSearchParams';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';

import { useSessions } from '../../hooks/useSwr';
import { GetSessionsQueryParams } from '../../types/schema';
import { ErrorBanner } from '../ErrorBanner';
import { SessionsDataGrid } from './components/SessionsDataGrid';
import {
  IncompleteSessionFilter,
  SessionFilter,
  SessionFilterField,
} from './types/SessionsFilterModel';
import { parseQueryString } from './utils/parseQueryString';
import { sessionFilterModelToQueryLang } from './utils/sessionFilterModelToQueryLang';

interface TableProps {
  filter: GetSessionsQueryParams;
  datasetShareToken?: string;
  emptyState?: React.ReactNode;
  columnVisibility?: Record<string, boolean>;
  onRowClickPrefix?: string;
  onRowClickSuffix?: string;
  isDatasetPage?: boolean;
  isDashboardPage?: boolean;
  getCompareClickUrl?: (sessionIds: string[]) => string;
  sortableColumns?: string[];
  localStorageDisplayColumnsKey?: `ls:${string}`;
  hideColumnVisibilityToggle?: boolean;
  useApproxStats?: boolean;
}

export const SessionsTable = ({
  filter,
  datasetShareToken,
  emptyState,
  columnVisibility,
  onRowClickPrefix,
  onRowClickSuffix,
  getCompareClickUrl,
  isDatasetPage = false,
  isDashboardPage = false,
  sortableColumns,
  localStorageDisplayColumnsKey,
  hideColumnVisibilityToggle,
  useApproxStats = false,
}: TableProps) => {
  // navigation

  const [searchParams, setSearchParams] = useSearchParams();
  // pagination

  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>(
    tryJsonParseSearchParam(
      searchParams,
      'paginationModel',
      DEFAULT_10_PAGINATION_MODEL
    )
  );
  const { name, setName, debouncedName } = useDebouncedSearchStringQueryParams(
    'search',
    setSearchParams
  );

  let sortingState: SortingState;
  try {
    sortingState = JSON.parse(searchParams.get('sortingState') ?? '[]');
  } catch (e) {
    sortingState = [];
  }
  if (sortingState.length === 0) {
    sortingState = [
      { id: isDatasetPage ? 'start_time' : 'last_run_start_time', desc: true },
    ];
  }
  const setSortingState = (sortingStateUpdater) => {
    const newSortingState = sortingStateUpdater(sortingState);
    setSearchParams(
      (prev) => {
        prev.set('sortingState', JSON.stringify(newSortingState));
        return prev;
      },
      { replace: true }
    );
  };
  const { selectedTags } = useStoredResourceTags();

  let sortingColumn =
    debouncedName || selectedTags.length > 0 ? undefined : sortingState[0].id;
  const isSortingDesc = sortingState[0].desc;
  let feedbackSortKey: string | undefined = undefined;

  if (sortingColumn && sortingColumn.startsWith('feedback_stats_')) {
    sortingColumn = 'feedback';
    feedbackSortKey = sortingState?.[0]?.id.replace('feedback_stats_', '');
  }

  if (
    sortingColumn &&
    sortableColumns &&
    !sortableColumns?.includes(sortingColumn) &&
    !(
      sortingColumn.startsWith('feedback_stats_') &&
      sortableColumns?.includes('feedback')
    )
  ) {
    sortingColumn = 'start_time';
  }

  const [sessionFilters, setSessionFilters] = useStateFromSearchParams(
    'sessionFilters',
    ''
  );

  let parsedSessionFilters: IncompleteSessionFilter[] = [];
  try {
    parsedSessionFilters = JSON.parse(sessionFilters ?? '[]');
  } catch {
    parsedSessionFilters = [];
  }

  const setSessionFiltersWithParams = useCallback(
    (filters: IncompleteSessionFilter[]) => {
      setSessionFilters(JSON.stringify(filters));
    },
    [setSessionFilters]
  );

  let activeFilters: SessionFilter[] = useMemo(() => {
    return parsedSessionFilters.reduce((acc, filter) => {
      if (
        filter.field === SessionFilterField.FEEDBACK_SCORE &&
        filter.value != null &&
        filter.comparator != null &&
        filter.metric != null &&
        filter.key != null &&
        !isNaN(parseFloat(filter.value))
      ) {
        acc.push({
          field: filter.field,
          comparator: filter.comparator,
          value: parseFloat(filter.value),
          key: filter.key,
          metric: filter.metric,
        });
      } else if (
        filter.field === SessionFilterField.METADATA &&
        filter.value != null &&
        filter.comparator != null &&
        Object.keys(filter.value).length > 0
      ) {
        if (
          'num_repetitions' in filter.value &&
          !isNaN(parseFloat(filter.value.num_repetitions))
        ) {
          acc.push({
            field: filter.field,
            comparator: filter.comparator,
            value: {
              ...filter.value,
              num_repetitions: parseFloat(filter.value.num_repetitions),
            },
          });
        } else {
          acc.push({
            field: filter.field,
            comparator: filter.comparator,
            value: filter.value,
          });
        }
      }
      return acc;
    }, [] as SessionFilter[]);
  }, [parsedSessionFilters]);

  activeFilters = activeFilters.concat(parseQueryString(filter.filter));

  const [debouncedActiveFilters] = useDebounce(activeFilters, 500, {
    trailing: true,
    equalityFn: (prev, next) => JSON.stringify(prev) === JSON.stringify(next),
  });

  const activeFiltersStringified = useMemo(() => {
    return sessionFilterModelToQueryLang(debouncedActiveFilters);
  }, [debouncedActiveFilters]);

  useEffect(() => {
    setPaginationModel(() => DEFAULT_10_PAGINATION_MODEL);
    // Reset pagination when search term or tags change
  }, [debouncedName, selectedTags]);

  const {
    data: fetchedData,
    isLoading,
    isValidating,
    mutate,
    error,
  } = useSessions(
    {
      ...filter,
      filter: activeFiltersStringified,
      name_contains: debouncedName,
      offset: paginationModel.pageIndex * paginationModel.pageSize,
      limit: paginationModel.pageSize,
      sort_by: sortingColumn,
      sort_by_desc: isSortingDesc || selectedTags.length > 0,
      tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
      use_approx_stats: useApproxStats,
      ...(isDatasetPage
        ? {
            sort_by_feedback_key: feedbackSortKey,
          }
        : {}),
    },
    { datasetShareToken },
    {
      focusThrottleInterval: 10000,
      keepPreviousData: true,
    }
  );

  if (error) {
    return <ErrorBanner>{error.message}</ErrorBanner>;
  }
  if (!fetchedData?.rows && !isLoading) {
    return emptyState;
  }

  return (
    <SessionsDataGrid
      data={fetchedData}
      isLoading={isLoading}
      isValidating={isValidating}
      pagination={paginationModel}
      setPagination={setPaginationModel}
      name={name}
      setName={setName}
      isDatasetPage={isDatasetPage}
      isDashboardPage={isDashboardPage}
      emptyState={emptyState}
      columnVisibility={columnVisibility}
      onRowClickPrefix={onRowClickPrefix}
      onRowClickSuffix={onRowClickSuffix}
      getCompareClickUrl={getCompareClickUrl}
      handleSortingChange={setSortingState}
      sortingState={sortingState}
      mutateSessions={mutate}
      sortableColumns={sortableColumns}
      localStorageDisplayColumnsKey={localStorageDisplayColumnsKey}
      sessionFilters={parsedSessionFilters}
      setSessionFilters={setSessionFiltersWithParams}
      activeFilters={activeFilters}
      hideColumnVisibilityToggle={hideColumnVisibilityToggle}
    />
  );
};
