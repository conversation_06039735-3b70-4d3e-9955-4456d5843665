import {
  Axis,
  BarSeries,
  Grid,
  Tooltip,
  XYChart,
  darkTheme,
  lightTheme,
} from '@visx/xychart';

import { useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';

import { FeedbackChip } from '@/components/FeedbackChips';
import { Rate } from '@/components/Rate';
import { LatencyChip } from '@/components/RunLatencyChip';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';
import { useColorScheme } from '@/hooks/useColorScheme';
import { SessionSchema } from '@/types/schema';
import { generateDarkerHexColor } from '@/utils/generate-darker-hex-color';
import { getColorByString } from '@/utils/get-color-by-string';
import { cn } from '@/utils/tailwind';

import { getTickFormat } from '../utils/getTickFormat';
import { useChartData } from '../utils/useChartData';

const formatter = new Intl.NumberFormat('en-US', {});
const yAxisFormatter = new Intl.NumberFormat('en-US', {
  notation: 'compact',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});
const currencyFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});
const HEIGHT = 280;
const BASE_WIDTH = 360;
const TITLE_PADDING_X_AXIS = 64;

export function SessionsTableChart(props: {
  rowData: SessionSchema[];
  accessorFn: (row: SessionSchema) => number | null | undefined;
  yAxisLabel: string;
  color?: string;
  isFeedback?: boolean;
  isCurrency?: boolean;
  feedbackKey?: string;
  onClick?: (row: SessionSchema, event: any) => void;
  experimentMetadataPath?: string;
  setExperimentMetadataPath?: (value?: string) => void;
}) {
  const { rowData: rowDataProp, accessorFn } = props;
  const { isDarkMode } = useColorScheme();

  const { rowData } = useChartData(rowDataProp, accessorFn);

  const margin = {
    top: 20,
    bottom: 20 + TITLE_PADDING_X_AXIS,
    left: 40,
    right: 10,
  };

  const xPoint = (data: SessionSchema) => {
    const x = data.test_run_number;
    return x;
  };
  const yPoint = (data: SessionSchema) => {
    const y = Number(props.accessorFn(data)?.toFixed(2));
    return y;
  };
  const [hoveredDatumState, setHoveredDatumState] = useState<string | null>(
    null
  );
  const [hoveredDatum] = useDebounce(hoveredDatumState, 10);
  const chartColor = props.color ?? getColorByString(props.yAxisLabel);
  const hoverColor = generateDarkerHexColor(chartColor, 15);

  const isEmpty = !props.rowData.some((row) => props.accessorFn(row) != null);

  const numberOfDataPoints = props.rowData.filter(
    (row) => props.accessorFn(row) != null
  ).length;

  const longestLabelLength = Math.max(
    ...rowData.map((row) => `#${row.test_run_number ?? 0}`.length)
  );
  const calculatedWidth =
    BASE_WIDTH +
    Math.min(numberOfDataPoints, 20) * (10 + longestLabelLength * 1.2);

  // check if all values are between 0 and 1
  const containsValuesOutsideOfZeroAndOne = useMemo(
    () =>
      rowData
        .map((row) => accessorFn(row))
        .some((value) => !!value && (value < 0 || value > 1)),
    [rowData, accessorFn]
  );

  if (isEmpty) {
    return null;
  }

  return (
    <div
      className={cn(
        'relative mb-2 flex shrink-0 flex-col rounded-md border border-secondary p-4',
        !!hoveredDatum && 'cursor-pointer'
      )}
      style={{
        width: calculatedWidth,
        height: HEIGHT + TITLE_PADDING_X_AXIS,
      }}
    >
      <TextOverflowTooltip
        className="ml-3.5 text-xs font-semibold"
        tooltipMaxWidth={300}
      >
        {props.yAxisLabel}
      </TextOverflowTooltip>
      <div className="block h-full w-full">
        <XYChart
          xScale={{ type: 'band', padding: 0.7 }}
          yScale={{
            type: 'linear',
            zero: true,
            domain: containsValuesOutsideOfZeroAndOne
              ? [
                  Math.min(...rowData.map((row) => accessorFn(row) ?? 0)),
                  Math.max(...rowData.map((row) => accessorFn(row) ?? 0)),
                ]
              : [0, 1],
          }}
          onPointerUp={(e) => {
            const sessionObj = e.datum as SessionSchema;
            props.onClick?.(sessionObj, e.event);
          }}
          onPointerMove={async (datum) => {
            setHoveredDatumState((datum?.datum as SessionSchema)?.id);
            const sessionObj = datum?.datum as SessionSchema;
            document
              .querySelectorAll('.data-grid-row-component.bg-secondary')
              .forEach((el) => {
                el.classList.remove('bg-secondary');
              });
            document
              .querySelectorAll(`.data-grid-row-component-${sessionObj.id}`)
              .forEach((el) => {
                el.classList.add('bg-secondary');
              });
          }}
          onPointerOut={async () => {
            setHoveredDatumState(null);
            document
              .querySelectorAll('.data-grid-row-component')
              .forEach((el) => {
                el.classList.remove('bg-secondary');
              });
          }}
          theme={isDarkMode ? darkTheme : lightTheme}
          margin={margin}
        >
          <Grid
            numTicks={5}
            lineStyle={{
              stroke: isDarkMode
                ? 'hsl(228 6.17% 15.88%)'
                : 'hsl(212.7 26.8% 83.9%)',
            }}
            rows={true}
            columns={false}
          />
          <Axis
            orientation="bottom"
            numTicks={rowData.length}
            tickFormat={(tick, index) => {
              // Show every 3rd label for 30+ points, every 2nd label for 20+ points, or all ticks
              const skipInterval =
                numberOfDataPoints > 30 ? 3 : numberOfDataPoints > 20 ? 2 : 1;
              if (index % skipInterval !== 0) return '';

              return getTickFormat(
                tick,
                10,
                rowData,
                props.experimentMetadataPath,
                true
              );
            }}
            labelOffset={20}
            axisLineClassName="stroke-ls-gray-300"
            labelClassName="font-normal"
            tickLabelProps={() => ({
              fill: isDarkMode
                ? 'hsl(215 20.2% 65.1%)'
                : 'hsl(215.4, 16.3%, 46.9%)',
              fontSize: 12,
              fontWeight: '12px',
              angle: -45,
              textAnchor: 'end',
              dy: '0.3em',
              dx: '-0.5em',
            })}
            tickLineProps={{
              stroke: isDarkMode
                ? 'hsl(228 6.17% 15.88%)'
                : 'hsl(212.7 26.8% 83.9%)',
            }}
            labelProps={{
              fill: isDarkMode
                ? 'hsl(215 20.2% 65.1%)'
                : 'hsl(215.4, 16.3%, 46.9%)',
              fontSize: 12,
            }}
          />
          <Axis
            orientation="left"
            axisLineClassName="stroke-ls-gray-300"
            tickLineProps={{
              stroke: isDarkMode
                ? 'hsl(228 6.17% 15.88%)'
                : 'hsl(212.7 26.8% 83.9%)',
            }}
            tickFormat={(value) =>
              props.isCurrency
                ? currencyFormatter.format(value)
                : yAxisFormatter.format(value)
            }
            tickLabelProps={{
              fill: isDarkMode
                ? 'hsl(215 20.2% 65.1%)'
                : 'hsl(215.4, 16.3%, 46.9%)',
              fontSize: 12,
              fontWeight: '12px',
            }}
            numTicks={5}
            labelProps={{
              fill: isDarkMode
                ? 'hsl(215 20.2% 65.1%)'
                : 'hsl(215.4, 16.3%, 46.9%)',
              fontSize: 12,
            }}
            labelClassName="font-normal"
          />
          <BarSeries
            dataKey={props.yAxisLabel}
            data={rowData}
            xAccessor={xPoint}
            yAccessor={yPoint}
            colorAccessor={(data) => {
              return data.id === hoveredDatum ? hoverColor : chartColor;
            }}
            radiusTop
          />
          <Tooltip<SessionSchema>
            snapTooltipToDatumY
            snapTooltipToDatumX
            detectBounds
            showSeriesGlyphs={false}
            renderGlyph={undefined}
            renderTooltip={({ tooltipData }) => {
              let tooltipDataObj: SessionSchema | undefined;
              try {
                tooltipDataObj = tooltipData?.nearestDatum
                  ?.datum as SessionSchema;
                const value = props.accessorFn(tooltipDataObj);

                return value != null && hoveredDatum ? (
                  <SessionTooltipComponent
                    session={tooltipDataObj}
                    yAxisLabel={props.yAxisLabel}
                    value={value}
                    isCurrency={props.isCurrency}
                    isFeedback={props.isFeedback}
                    feedbackKey={props.feedbackKey}
                    isLatency={props.yAxisLabel
                      .toLocaleLowerCase()
                      .includes('latency')}
                    latencyType={props.yAxisLabel.split(' Latency')[0]}
                    isErrorRate={props.yAxisLabel
                      .toLocaleLowerCase()
                      .includes('error rate')}
                    isClickable={!!props.onClick}
                  />
                ) : null;
              } catch {
                return <div />;
              }
            }}
          />
        </XYChart>
      </div>
    </div>
  );
}

export function SessionTooltipComponent(props: {
  session: SessionSchema;
  yAxisLabel: string;
  value: number;
  isFeedback?: boolean;
  isSessionLevelFeedback?: boolean;
  feedbackKey?: string;
  isLatency?: boolean;
  latencyType?: string;
  isErrorRate?: boolean;
  isClickable?: boolean;
  isCurrency?: boolean;
}) {
  let valueElement = (
    <div className="flex items-end gap-2 text-xs">
      <div className="font-semibold">{props.yAxisLabel}:</div>
      <div className="font-normal">{formatter.format(props.value)}</div>
    </div>
  );
  if (props.isLatency) {
    valueElement = (
      <LatencyChip
        size="sm"
        latency={props.value}
        className="font-normal"
        label={`${props.latencyType} `}
        maximumFractionDigits={3}
      />
    );
  } else if (props.isCurrency) {
    valueElement = (
      <div className="flex items-end gap-2 text-xs">
        <div className="font-semibold">{props.yAxisLabel}:</div>
        <div className="font-normal">
          {currencyFormatter.format(props.value)}
        </div>
      </div>
    );
  } else if (
    (props.isFeedback || props.isSessionLevelFeedback) &&
    props.feedbackKey
  ) {
    valueElement = (
      <FeedbackChip
        feedback_key={props.feedbackKey}
        value={props.value}
        allowTruncation
        iconType={props.isSessionLevelFeedback ? 'session' : 'run'}
      />
    );
  } else if (props.isErrorRate) {
    valueElement = (
      <div className="flex items-center gap-2">
        <div className="text-xs font-semibold">{props.yAxisLabel}:</div>
        <Rate value={props.value} />
      </div>
    );
  }
  return (
    <div className="inline-flex h-full min-w-[250px] flex-col items-center text-black dark:text-white">
      <div className="-mx-2 inline-flex items-center justify-start gap-3 self-stretch border-b border-secondary px-6 py-3">
        <div className="line-clamp-2 w-[250px] break-words break-all text-xs font-[550] leading-normal">
          #{props.session.test_run_number}: {props.session.name}
        </div>
      </div>
      <div className="flex flex-col items-start justify-center gap-3 self-stretch px-6 py-4">
        <div className="text-xs font-medium uppercase leading-[15px] tracking-wide">
          {new Date(props.session.start_time + 'Z').toLocaleString()}
        </div>
        <div className="flex max-w-[300px] flex-col items-start justify-center gap-2">
          {valueElement}
        </div>
        {props.isClickable && (
          <div className=" text-xs font-normal leading-none text-tertiary">
            Click to see more details
          </div>
        )}
      </div>
    </div>
  );
}
