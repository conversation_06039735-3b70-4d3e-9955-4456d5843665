import {
  EllipsisVerticalIcon,
  MagnifyingGlassIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { AlertCircleIcon, Edit03Icon } from '@langchain/untitled-ui-icons';
import { Button, Tooltip } from '@mui/joy';
import { PopoverTrigger } from '@radix-ui/react-popover';
import {
  Column,
  InitialTableState,
  OnChangeFn,
  PaginationState,
  Row,
  RowSelectionState,
  SortingState,
  Table,
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { DEFAULT_PAGINATION_MODEL } from '@/Pages/Home/constants';
import { AddToAnnotationQueuePopoverContent } from '@/components/AnnotationQueuePopover/AddToAnnotationQueuePopoverContent';
import { AnnotationQueueCrudPane } from '@/components/AnnotationQueuePopover/AnnotationQueueCrudPane';
import { BannerWithIcon } from '@/components/BannerWithIcon';
import { DataGridColumnVisibilityPopover } from '@/components/DataGrid/DataGridColumnVisibilityPopover';
import { GithubChip } from '@/components/GithubChip';
import { MultiDeleteModal } from '@/components/MultiDelete';
import { Popover, PopoverContent } from '@/components/Popover';
import { SessionCrudPane } from '@/components/SessionCrudPane/SessionCrudPane';
import { Skeleton } from '@/components/Skeleton';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';
import useToast from '@/components/Toast';
import { EXPERIMENT_METADATA_PATH_LOCAL_STORAGE_PREFIX } from '@/constants/datasetConstants';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { usePermissions } from '@/hooks/usePermissions';
import { useStateFromSearchParams } from '@/hooks/useStateFromSearchParams';
import CompareIcon from '@/icons/CompareIcon.svg?react';
import EditIcon from '@/icons/EditIcon.svg?react';
import MetadataIcon from '@/icons/MetadataIcon.svg?react';
import PieSliceIcon from '@/icons/PieSliceIcon.svg?react';
import ProjectIcon from '@/icons/ProjectIcon';
import RepetitionsIcon from '@/icons/RepetitionsIcon.svg?react';
import { getColorByString } from '@/utils/get-color-by-string';
import { getHubLinkFromSessionName } from '@/utils/hub';
import { useLocalStorageState } from '@/utils/use-local-storage-state';
import { DATASET_VERSION_FORMATTER } from '@/utils/version-format';

import {
  useDatasets,
  useFeedbackConfigs,
  useOrganizationId,
  usePopulateAnnotationQueueMutation,
} from '../../../hooks/useSwr';
import { AnnotationQueueSchema, SessionSchema } from '../../../types/schema';
import {
  apiSessionsPath,
  appAnnotationQueuesPath,
  appHubIndexPath,
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
} from '../../../utils/constants';
import { DataGrid, DataGridPagination } from '../../DataGrid';
import {
  emulateNativeClick,
  emulateNativeMiddleClick,
  getTableSelectColumnDef,
  useDataGridSizingLocalStorage,
} from '../../DataGrid.utils';
import { DataGridSearchInput } from '../../DataGrid/DataGridSearchInput';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../Dropdown';
import { FeedbackChips } from '../../FeedbackChips';
import { Rate } from '../../Rate';
import { TableCellTextOverflow } from '../../Table';
import { SessionLatencyCell } from '../components/Cell';
import {
  ACCESSOR_FNS,
  DUMMY_CHART_PLACEHOLDER,
  EMPTY_TABLE,
  NUMBER_OF_FEEDBACK_COLUMNS_TO_SHOW,
  SORTING_NAMES,
  formatter,
  usdFormatter,
} from '../constants';
import {
  IncompleteSessionFilter,
  SessionFilter,
} from '../types/SessionsFilterModel';
import { getDisplayNameFromColumn } from '../utils/getDisplayNameFromColumn';
import {
  getChartableColumns,
  getDefaultSelectedColumns,
} from '../utils/selectChartsHelpers';
import { SelectChartsXAxisLabel } from './SelectChartsXAxisLabel';
import { SessionsDataGridChartPicker } from './SessionsDataGridChartPicker';
import { SessionsFilterBar } from './SessionsFilterBar';
import { SessionsTableChart } from './SessionsTableChart';

const columnHelper = createColumnHelper<SessionSchema>();

type SimpleModeProps =
  | {
      simple: true;
    }
  | {
      simple?: false;
      name: string;
      setName: (name: string) => void;
      pagination: PaginationState;
      setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
      handleSortingChange: OnChangeFn<SortingState>;
      sortingState: SortingState;
    };

export type SessionsData = {
  total?: number;
  rows?: SessionSchema[];
  excluding_empty?: boolean;
};

type SessionDataGridProps = {
  emptyState: React.ReactNode;
  columnVisibility?: Record<string, boolean>;
  onRowClickPrefix?: string;
  onRowClickSuffix?: string;
  isDatasetPage: boolean;
  isDashboardPage?: boolean;
  data?: SessionsData;
  isLoading: boolean;
  isValidating: boolean;
  getCompareClickUrl?: (sessionIds: string[]) => string;
  mutateSessions?: (data?: {
    total?: number | undefined;
    rows?: SessionSchema[] | undefined;
  }) => void;
  sortableColumns?: string[];
  localStorageDisplayColumnsKey?: `ls:${string}`;
  localStorageDataGridSizeKey?: string;

  sessionFilters?: IncompleteSessionFilter[];
  setSessionFilters?: (filters: IncompleteSessionFilter[]) => void;
  activeFilters?: SessionFilter[];

  hideColumnVisibilityToggle?: boolean;
} & SimpleModeProps;

const SessionEditFromDataGrid = (props: {
  id: string | null;
  rows: SessionSchema[] | undefined;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const session = props.rows?.find((x) => x.id === props.id);

  return (
    <SessionCrudPane
      isOpen={session != null}
      session={session}
      doClose={props.onClose}
      onSuccess={() => {
        props.onClose();
        props.onSuccess?.();
      }}
    />
  );
};

const PopulateAnnotationQueuePopover = ({
  handlePopulateAnnotationQueue,
  openNewAnnotationQueueModal,
  children,
  totalRuns,
  numSessions,
}: {
  handlePopulateAnnotationQueue: (aq: AnnotationQueueSchema) => void;
  openNewAnnotationQueueModal: () => void;
  totalRuns: number;
  numSessions: number;
  children: React.ReactNode;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const onPopulate = useCallback(
    (aq: AnnotationQueueSchema) => {
      handlePopulateAnnotationQueue(aq);
      setIsOpen(false);
    },
    [handlePopulateAnnotationQueue]
  );
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent className="w-[508px] p-0">
        <AddToAnnotationQueuePopoverContent
          handleSendToAnnotationQueue={onPopulate}
          openNewAnnotationQueueModal={openNewAnnotationQueueModal}
          title={`Select queue to annotate ${totalRuns} ${
            totalRuns === 1 ? 'run' : 'runs'
          } from ${numSessions} ${
            numSessions === 1 ? 'experiment' : 'experiments'
          }`}
        />
      </PopoverContent>
    </Popover>
  );
};

export const SessionsDataGrid = (props: SessionDataGridProps) => {
  const {
    emptyState,
    columnVisibility,
    onRowClickPrefix,
    onRowClickSuffix,
    isDatasetPage,
    isDashboardPage,
    data,
    isLoading,
    getCompareClickUrl,
    mutateSessions,
    sortableColumns,
    localStorageDisplayColumnsKey,
    localStorageDataGridSizeKey,
    simple,
    sessionFilters,
    setSessionFilters,
    activeFilters,
  } = props;

  const { value: enableAlignEvaluators } = useOrgConfig(
    OrgConfigs.enable_align_evaluators
  );
  const { datasetShareToken, datasetId } = useParams();
  const organizationId = useOrganizationId();
  const navigate = useNavigate();
  const { authorize } = usePermissions();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const rows = data?.rows;
  const feedbackKeys: { feedbackKey: string; rowIdx: number }[] =
    useMemo(() => {
      const fbKeys = rows?.flatMap((row, rowIdx) => {
        // Get regular feedback keys
        const regularFeedbackKeys = Object.keys(row.feedback_stats ?? {});

        // Get session feedback keys with prefix
        const sessionFeedbackKeys = Object.keys(
          row.session_feedback_stats ?? {}
        ).map((key) => `session_feedback_stats_${key}`);

        // Combine both sets of keys
        return [...regularFeedbackKeys, ...sessionFeedbackKeys].map((key) => ({
          feedbackKey: key,
          rowIdx,
        }));
      });
      return fbKeys ?? [];
    }, [rows]);

  const uniqueFeedbackKeys = useMemo(
    () => [...new Set(feedbackKeys.map((x) => x.feedbackKey))],
    [feedbackKeys]
  );

  const feedbackConfig = useFeedbackConfigs(
    {
      skip: !feedbackKeys || feedbackKeys.length === 0,
    },
    {
      key: uniqueFeedbackKeys,
    }
  );

  const datasetIds = useMemo(
    () => [
      ...new Set(
        rows
          ?.map((r) => r.reference_dataset_id ?? null)
          .filter((id): id is string => !!id)
      ),
    ],
    [rows]
  );
  const datasets = useDatasets(
    datasetIds.length ? { id: datasetIds } : null,
    false,
    {
      keepPreviousData: false,
    }
  );

  const allFeedbackTypes = new Set<string>();
  const feedbackCounts = new Map<string, number>();
  rows?.forEach((row) => {
    Object.keys(row.feedback_stats ?? {}).forEach((type) => {
      allFeedbackTypes.add(type);
      feedbackCounts.set(type, (feedbackCounts.get(type) ?? 0) + 1);
    });
  });

  const feedbackTypesArray = [...allFeedbackTypes].sort((a, b) => {
    const aCount = feedbackCounts.get(a) ?? 0;
    const bCount = feedbackCounts.get(b) ?? 0;
    return bCount - aCount;
  });

  const metadataAggregate: Record<string, string[]> = useMemo(() => {
    const metadataAggregate: Record<string, Set<string>> = {};
    rows?.forEach((row) => {
      Object.keys(row.extra?.metadata ?? {}).forEach((key) => {
        const metadataValue = row.extra?.metadata?.[key];
        if (typeof metadataValue === 'string') {
          (metadataAggregate[key] ??= new Set()).add(metadataValue);
        } else if (!(key in metadataAggregate) && metadataValue != null) {
          metadataAggregate[key] = new Set();
        }
      });
    });
    const metadataAggregateArrays: Record<string, string[]> = {};
    Object.entries(metadataAggregate).forEach(([key, value]) => {
      metadataAggregateArrays[key] = [...value];
    });
    return metadataAggregateArrays;
  }, [rows]);

  const metadataKeysAggregate = useMemo(
    () =>
      Object.keys(metadataAggregate).sort((a, b) => {
        if (a === 'dataset_splits') return -1;
        if (b === 'dataset_splits') return 1;
        return a.localeCompare(b);
      }),
    [metadataAggregate]
  );

  // columns and actions
  const [sessionsToDelete, setSessionsToDelete] = useState<
    { id: string; name: string }[] | null
  >(null);
  const [sessionToEdit, setSessionToEdit] = useState<string | null>(null);

  // Store the minimum and maximum value of each feedback type among all the rows - use row.feedback_stats.avg
  const [minMaxFeedbackValues, setMinMaxFeedbackValues] = useState(
    new Map<string, [number, number]>()
  );
  useEffect(() => {
    setMinMaxFeedbackValues((prev) => {
      const updatedMap = new Map(prev);
      feedbackKeys.forEach(({ feedbackKey: type, rowIdx }) => {
        const isSessionFeedback = type.startsWith('session_feedback_stats_');
        let lookupType = type;
        if (isSessionFeedback) {
          lookupType = type.replace('session_feedback_stats_', '');
        }
        const config = feedbackConfig.data?.find(
          (i) => i.feedback_key === lookupType
        )?.feedback_config;

        const configMin =
          config && 'min' in config && typeof config.min === 'number'
            ? config?.min
            : 0;

        const configMax =
          config && 'max' in config && typeof config.max === 'number'
            ? config?.max
            : 1;

        const avg = isSessionFeedback
          ? rows?.[rowIdx].session_feedback_stats?.[lookupType]?.avg
          : rows?.[rowIdx].feedback_stats?.[lookupType]?.avg;
        if (avg == null) return;
        const [minAvg, maxAvg] = updatedMap.get(type) ?? [configMin, configMax];
        updatedMap.set(type, [
          Math.min(configMin, minAvg, avg),
          Math.max(configMax, maxAvg, avg),
        ] as [number, number]);
      });
      return updatedMap;
    });
  }, [rows, feedbackConfig.data, feedbackKeys]);

  // Don't want columns to be rearranged while user is on the page so
  // make them based only on the first value of `rows`
  const [columns, initialState] = useMemo(() => {
    const nameColumn = columnHelper.accessor('name', {
      id: 'name',
      header: isDatasetPage
        ? 'Experiment'
        : isDashboardPage
        ? 'Tracing Project Name'
        : 'Name',
      enableSorting: true,
      sortingFn: 'text',
      size: 250,
      cell: ({ row }) => {
        const description = row.original.description;
        return (
          <div className="overflow-hidden">
            <TextOverflowTooltip tooltipMaxWidth={300}>
              {row.original.name}
            </TextOverflowTooltip>

            {description?.trim() && (
              <TextOverflowTooltip className="text-sm text-tertiary no-scrollbar">
                {description.trim()}
              </TextOverflowTooltip>
            )}
          </div>
        );
      },
    });

    const feedbackColumn = columnHelper.accessor('feedback_stats', {
      id: 'feedback_stats',
      header: 'Feedback (7D)',
      cell: ({ row }) => (
        <div className="absolute inset-0 mx-2 flex items-center gap-2 overflow-x-auto overflow-y-hidden py-4 no-scrollbar">
          <FeedbackChips
            feedbackStats={row.original.feedback_stats}
            disablePopover
            className="shrink-0"
            maxChips={5}
          />
        </div>
      ),
      enableSorting: false,
    });
    const runCountColumn = columnHelper.accessor('run_count', {
      id: 'run_count',
      header: isDatasetPage ? 'Run Count' : 'Run Count (7D)',
      enableSorting: false,
      maxSize: 100,
      enableResizing: true,
      cell: ({ getValue }) => {
        const value = getValue();
        return (
          value != null && (
            <div
              data-testid="run-count-chip"
              className="inline-flex h-5 items-center justify-center rounded bg-tertiary px-1.5"
            >
              <div className="text-center text-xs font-medium leading-none">
                {formatter.format(value)}
              </div>
            </div>
          )
        );
      },
      sortingFn: 'basic',
    });
    const errorRateColumn = columnHelper.accessor('error_rate', {
      id: 'error_rate',
      header: isDatasetPage ? 'Error Rate' : 'Error Rate (7D)',
      enableSorting:
        !sortableColumns || sortableColumns?.includes('error_rate'),
      maxSize: 100,
      enableResizing: true,
      cell: ({ getValue }) => {
        const value = getValue() ?? 0;
        return <Rate value={value} testId="error-rate-chip" />;
      },
      sortingFn: 'basic',
    });
    const streamingRateColumn = columnHelper.accessor('streaming_rate', {
      id: 'streaming_rate',
      header: isDatasetPage ? '% Streaming' : '% Streaming (7D)',
      enableSorting: false,
      maxSize: 100,
      enableResizing: true,
      cell: ({ getValue }) => {
        const value = getValue() ?? 0;
        return <Rate value={value} invert />;
      },
      sortingFn: 'basic',
    });
    const promptTokenColumn = columnHelper.accessor('prompt_tokens', {
      id: 'prompt_tokens',
      header: isDatasetPage ? 'Prompt Tokens' : 'Prompt Tokens (7D)',
      enableSorting: false,
      cell: ({ getValue }) => {
        const value = getValue();
        return <>{value != null && formatter.format(value)}</>;
      },
      sortingFn: 'basic',
    });
    const completionTokenColumn = columnHelper.accessor('completion_tokens', {
      id: 'completion_tokens',
      header: isDatasetPage ? 'Output Tokens' : 'Output Tokens (7D)',
      enableSorting: false,
      cell: ({ getValue }) => {
        const value = getValue();
        return <>{value != null && formatter.format(value)}</>;
      },
      sortingFn: 'basic',
    });
    const totalTokensColumn = columnHelper.accessor('total_tokens', {
      id: 'total_tokens',
      header: isDatasetPage ? 'Total Tokens' : 'Total Tokens (7D)',
      enableSorting: false,
      cell: ({ getValue }) => {
        const value = getValue();
        return <>{value != null && formatter.format(value)}</>;
      },
      sortingFn: 'basic',
    });
    const totalCostColumn = columnHelper.accessor('total_cost', {
      id: 'total_cost',
      header: isDatasetPage ? 'Total Cost' : 'Total Cost (7D)',
      enableSorting: false,
      cell: ({ getValue }) => {
        const value = getValue();
        return (
          <>
            {value != null && usdFormatter.format(Math.ceil(value * 100) / 100)}
          </>
        );
      },
      sortingFn: 'basic',
    });
    const latencyP50Column = columnHelper.accessor('latency_p50', {
      id: 'latency_p50',
      header: isDatasetPage ? 'P50 Latency' : 'P50 Latency (7D)',
      cell: ({ row }) => (
        <SessionLatencyCell
          row={row.original}
          metric="latency_p50"
          testId="latency-p50-chip"
        />
      ),
      enableSorting:
        !sortableColumns || sortableColumns.includes('latency_p50'),
      sortingFn: 'basic',
    });
    const latencyP99Column = columnHelper.accessor('latency_p99', {
      id: 'latency_p99',
      header: isDatasetPage ? 'P99 Latency' : 'P99 Latency (7D)',
      cell: ({ row }) => (
        <SessionLatencyCell
          row={row.original}
          metric="latency_p99"
          testId="latency-p99-chip"
        />
      ),
      enableSorting:
        !sortableColumns || sortableColumns.includes('latency_p99'),
      sortingFn: 'basic',
    });
    const mostRecentRunColumn = columnHelper.accessor('last_run_start_time', {
      id: 'last_run_start_time',
      header: isDatasetPage ? 'Most Recent Run' : 'Most Recent Run (7D)',
      cell: ({ row }) => {
        const largest = row.original.last_run_start_time;
        return largest
          ? new Date(
              largest.endsWith('Z') || largest.includes('+')
                ? largest
                : largest + 'Z'
            ).toLocaleString()
          : null;
      },
      enableSorting:
        !sortableColumns || sortableColumns.includes('last_run_start_time'),
      size: 200,
      sortingFn: 'datetime',
    });
    const creationTimeColumn = columnHelper.accessor('start_time', {
      id: 'start_time',
      header: 'Created At',
      cell: ({ row }) =>
        row.original.start_time
          ? new Date(row.original.start_time + 'Z').toLocaleString()
          : null,
      enableSorting: true,
      sortingFn: 'datetime',
      size: 200,
    });
    const endTimeColumn = columnHelper.accessor('end_time', {
      id: 'end_time',
      header: 'End Time',
      cell: ({ row }) =>
        row.original.end_time
          ? // No 'Z' needed here since the column includes the timezone
            new Date(row.original.end_time).toLocaleString()
          : null,
      enableSorting: false,
      sortingFn: 'datetime',
      size: 200,
    });
    const metadataColumns = metadataKeysAggregate.map((key) => {
      const title =
        key === 'revision_id'
          ? 'Revision ID'
          : key === 'dataset_splits'
          ? 'Splits'
          : key === 'num_repetitions'
          ? 'Repetitions'
          : key === 'git'
          ? 'Git'
          : key;
      return columnHelper.accessor(`metadata_${key}` as any, {
        id: `metadata_${key}`,
        meta: { name: title },
        header: () => {
          return (
            <div className="flex items-center gap-2">
              {key === 'dataset_splits' ? (
                <PieSliceIcon className="h-5 w-5" />
              ) : key === 'num_repetitions' ? (
                <RepetitionsIcon className="h-5 w-5" />
              ) : (
                <MetadataIcon className="h-5 w-5" />
              )}
              <TextOverflowTooltip
                tooltipMaxWidth={300}
                className="max-w-[200px]"
              >
                {title}
              </TextOverflowTooltip>
            </div>
          );
        },
        cell: ({ row }) => {
          const metadataValue = row.original.extra?.metadata?.[key];
          const textToDisplay = (
            key === 'dataset_version' && typeof metadataValue === 'string'
              ? (() => {
                  try {
                    return DATASET_VERSION_FORMATTER.format(
                      new Date(metadataValue)
                    );
                  } catch {
                    return metadataValue; // Return raw value if formatting fails
                  }
                })()
              : key === 'dataset_splits' && Array.isArray(metadataValue)
              ? metadataValue.join(', ')
              : typeof metadataValue === 'string'
              ? metadataValue
              : metadataValue != null
              ? JSON.stringify(metadataValue)
              : '--'
          ) as string;

          if (
            key === 'hub_slug' &&
            metadataValue &&
            typeof metadataValue === 'string'
          ) {
            const hubInfo = getHubLinkFromSessionName(metadataValue);
            const orgString = organizationId
              ? `?organizationId=${organizationId}`
              : '';
            const hubLink = hubInfo
              ? `/${appHubIndexPath}/${hubInfo.owner}/${hubInfo.repo}/${hubInfo.commit}${orgString}`
              : null;
            return (
              <Link
                to={hubLink ?? ''}
                onClick={(event) => event.stopPropagation()}
              >
                <div className="overflow-auto rounded-lg underline no-scrollbar hover:text-brand-green-400">
                  {textToDisplay}
                </div>
              </Link>
            );
          }

          if (key === 'git') {
            return (
              <GithubChip
                gitMetadata={metadataValue}
                fallbackText={textToDisplay}
              />
            );
          }

          return (
            <div className="overflow-auto py-3 no-scrollbar">
              {textToDisplay}
            </div>
          );
        },
        enableSorting: false,
        enableHiding: true,
        enableResizing: true,
      });
    });
    const actionsColumn = columnHelper.display({
      id: 'actions',
      cell: ({ row, table }) => {
        const { actions } = (table.options.meta ?? {}) as {
          actions?: {
            handleDelete?: (session: SessionSchema) => void;
            handleEdit?: (id: string) => void;
            deleteEnabled?: boolean;
            editEnabled?: boolean;
          };
        };

        if (!actions?.deleteEnabled && !actions?.editEnabled) return null;
        return (
          <TableCellTextOverflow className="justify-center">
            <DropdownMenu>
              <DropdownMenuTrigger
                onClick={(e) => e.stopPropagation()}
                className="flex h-6 w-6 items-center justify-center rounded-md transition-colors hover:bg-secondary focus:outline-none"
                data-testid="dropdown-menu-trigger"
              >
                <EllipsisVerticalIcon className="h-5 w-5" />
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="z-10"
                onClick={(e) => e.stopPropagation()}
              >
                {actions?.editEnabled && (
                  <DropdownMenuItem
                    onClick={() => actions?.handleEdit?.(row.original.id)}
                    className="flex items-center gap-2"
                  >
                    <EditIcon className="h-5 w-5" />
                    Edit
                  </DropdownMenuItem>
                )}
                {actions?.deleteEnabled && (
                  <DropdownMenuItem
                    onClick={() => actions?.handleDelete?.(row.original)}
                    className="flex items-center gap-2"
                  >
                    <TrashIcon className="h-5 w-5" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCellTextOverflow>
        );
      },
      enableSorting: false,
      enableHiding: false,
      enableResizing: false,
      size: 40,
    });

    const allSessionLevelFeedback = new Set<string>();
    const allSessionLevelFeedbackCounts = new Map<string, number>();
    rows?.forEach((row) => {
      Object.keys(row.session_feedback_stats ?? {}).forEach((type) => {
        allSessionLevelFeedback.add(type);
        allSessionLevelFeedbackCounts.set(
          type,
          (allSessionLevelFeedbackCounts.get(type) ?? 0) + 1
        );
      });
    });

    const sessionLevelFeedbackTypesArray = [...allSessionLevelFeedback].sort(
      (a, b) => {
        const aCount = allSessionLevelFeedbackCounts.get(a) ?? 0;
        const bCount = allSessionLevelFeedbackCounts.get(b) ?? 0;
        return bCount - aCount;
      }
    );

    const separateSessionLevelFeedbackColumns =
      sessionLevelFeedbackTypesArray.map((type) =>
        columnHelper.accessor(`session_feedback_stats_${type}` as any, {
          id: `session_feedback_stats_${type}`,
          meta: { name: type.replace(/\b\w/g, (char) => char.toUpperCase()) },
          header: () => {
            return (
              <div className="flex items-center gap-2">
                <ProjectIcon className="h-5 w-5" />
                <TextOverflowTooltip
                  tooltipMaxWidth={300}
                  className="max-w-[200px]"
                >
                  {type.replace(/\b\w/g, (char) => char.toUpperCase())}
                </TextOverflowTooltip>
              </div>
            );
          },
          cell: ({ row }) => {
            const minMax = minMaxFeedbackValues.get(
              `session_feedback_stats_${type}`
            );
            const avg = row.original.session_feedback_stats?.[type]?.avg ?? 0;
            const [min, max] = minMax ?? [avg, avg];
            const ratioAvg = (avg - min) / (max - min);

            const avgFeedback = ACCESSOR_FNS.session_feedback_stats(
              type,
              row.original
            );
            if (avgFeedback == null) return null;

            return (
              <div
                className="inline-flex h-5 items-center justify-start gap-1 rounded border border-secondary px-1 text-center text-xs font-medium leading-none text-tertiary"
                data-testid={`session-feedback-stats-${type}-chip`}
              >
                <span className="tabular-nums">{avgFeedback?.toFixed(2)}</span>
                <div className="h-1 w-10 overflow-hidden rounded-md bg-secondary">
                  <div
                    className="h-1"
                    style={{
                      width: `${ratioAvg * 100}%`,
                      background: getColorByString(type),
                    }}
                  />
                </div>
              </div>
            );
          },
          enableSorting: false,
          enableHiding: true,
        })
      );

    const separateFeedbackColumns = feedbackTypesArray.map((type) =>
      columnHelper.accessor(`feedback_stats_${type}` as any, {
        id: `feedback_stats_${type}`,
        meta: { name: type.replace(/\b\w/g, (char) => char.toUpperCase()) },
        size: 200,
        header: () => {
          return (
            <div
              className="flex items-center gap-2"
              data-testid={`sessions-table-header-feedback-${type}`}
            >
              <TextOverflowTooltip
                tooltipMaxWidth={300}
                className="max-w-[200px]"
              >
                {type.replace(/\b\w/g, (char) => char.toUpperCase())}
              </TextOverflowTooltip>
            </div>
          );
        },
        cell: ({ row }) => {
          const minMax = minMaxFeedbackValues.get(type);
          const avg = row.original.feedback_stats?.[type]?.avg ?? 0;
          const [minAvg, maxAvg] = minMax ?? [avg, avg];
          const ratioAvg = (avg - minAvg) / (maxAvg - minAvg);
          const errors = row.original.feedback_stats?.[type]?.errors ?? 0;
          const errorRate = row.original.feedback_stats?.[type]?.n
            ? errors / row.original.feedback_stats[type].n
            : 0;

          const avgFeedback = ACCESSOR_FNS.feedback_stats(type, row.original);
          const stdevFeedback = ACCESSOR_FNS.feedback_stats(
            type,
            row.original,
            'stdev'
          );
          const feedbackValues = row.original.feedback_stats?.[type]?.values;
          const feedbackHasNoValues =
            feedbackValues == null || Object.keys(feedbackValues).length === 0;
          if (
            (avgFeedback == null || stdevFeedback == null) &&
            feedbackHasNoValues
          )
            return null;

          if (avgFeedback == null && feedbackHasNoValues && errors == 0)
            return null;

          return (
            <div className="flex items-center gap-2">
              {!(avgFeedback == null && feedbackHasNoValues) ? (
                !feedbackHasNoValues ? (
                  <div className="flex items-center gap-2 overflow-x-auto overflow-y-hidden no-scrollbar">
                    {Object.entries(feedbackValues)
                      .sort((a, b) => b[1] - a[1])
                      .map((value, index) => (
                        <div
                          key={index}
                          className="inline-flex h-5 shrink-0 items-center justify-start gap-1 rounded border border-secondary px-1 text-center text-xs font-medium leading-none"
                          data-testid={`feedback-stats-${type}-chip`}
                        >
                          <span className="line-clamp-1 max-w-[100px] whitespace-normal break-all">
                            {value[0]}
                          </span>
                          <span className="tabular-nums">({value[1]})</span>
                        </div>
                      ))}
                  </div>
                ) : (
                  <Tooltip
                    title={`σ = ${stdevFeedback?.toFixed(2) ?? 'N/A'}`}
                    arrow
                  >
                    <div
                      className="inline-flex h-5 items-center justify-start gap-1 rounded border border-secondary px-1 text-center text-xs font-medium leading-none text-tertiary"
                      data-testid={`feedback-stats-${type}-chip`}
                    >
                      <span className="tabular-nums">
                        {avgFeedback?.toFixed(2)}
                      </span>
                      <div className="h-1 w-10 overflow-hidden rounded-md bg-secondary">
                        <div
                          className="h-1"
                          style={{
                            width: `${ratioAvg * 100}%`,
                            background: getColorByString(type),
                          }}
                        />
                      </div>
                    </div>
                  </Tooltip>
                )
              ) : null}
              {errorRate > 0 &&
                (errorRate < 1 ? (
                  <Tooltip title="Percent of evaluator runs that failed" arrow>
                    <div className="inline-flex h-5 items-center justify-center gap-1 rounded px-1.5 text-error">
                      <AlertCircleIcon className="h-4 w-4" />
                      <span className="text-xs font-medium">
                        {Math.round(errorRate * 100)}%
                      </span>
                    </div>
                  </Tooltip>
                ) : (
                  <div className="inline-flex h-5 items-center justify-center gap-1 rounded px-1.5 text-error">
                    <span className="text-xs font-medium">All Failed</span>
                  </div>
                ))}
            </div>
          );
        },
        enableSorting:
          isDatasetPage &&
          (!sortableColumns ||
            sortableColumns.includes(`feedback_stats_${type}`)),
        enableHiding: true,
        sortingFn: () => {
          return 0; // leave these in the order they are returned from the server
        },
      })
    );

    const getNumberFromSession = (session: Row<SessionSchema>) => {
      return `#${session.original.test_run_number?.toString()}`;
    };

    const testRunNumbers =
      (rows?.map((r) => r.test_run_number).filter(Boolean) as number[]) ?? [];
    const orderOfMagnitude = testRunNumbers
      ? Math.floor(Math.log10(Math.max(...testRunNumbers)) + 1) + 1
      : undefined;

    const columns = isDatasetPage
      ? [
          getTableSelectColumnDef({
            alternateText: getNumberFromSession,
            numCharsInAlternateTextBestGuess: orderOfMagnitude,
          }),
          nameColumn,
          ...metadataColumns.filter(
            (column) =>
              column.id === 'metadata_num_repetitions' ||
              column.id === 'metadata_dataset_splits'
          ),
          creationTimeColumn,
          ...separateSessionLevelFeedbackColumns,
          ...separateFeedbackColumns,
          totalCostColumn,
          latencyP50Column,
          latencyP99Column,
          mostRecentRunColumn,
          runCountColumn,
          errorRateColumn,
          endTimeColumn,
          promptTokenColumn,
          completionTokenColumn,
          totalTokensColumn,
          ...metadataColumns.filter(
            (column) =>
              column.id !== 'metadata_num_repetitions' &&
              column.id !== 'metadata_dataset_splits'
          ),
          ...(datasetShareToken ? [] : [actionsColumn]),
        ]
      : [
          mostRecentRunColumn,
          nameColumn,
          feedbackColumn,
          runCountColumn,
          errorRateColumn,
          latencyP50Column,
          latencyP99Column,
          streamingRateColumn,
          promptTokenColumn,
          completionTokenColumn,
          totalTokensColumn,
          totalCostColumn,
          creationTimeColumn,
          endTimeColumn,
          ...metadataColumns,
          actionsColumn,
        ];
    const initialState: InitialTableState = {
      columnVisibility: {
        start_time: isDatasetPage,
        end_time: false,
        prompt_tokens: false,
        completion_tokens: false,
        last_run_start_time: !isDatasetPage,
        ...metadataColumns.reduce((obj, key) => {
          if (!key.id) {
            return obj;
          }
          return { ...obj, [key.id]: isDatasetPage };
        }, {}),
        ...feedbackTypesArray
          .slice(NUMBER_OF_FEEDBACK_COLUMNS_TO_SHOW)
          .reduce((obj, key) => {
            return { ...obj, [`feedback_stats_${key}`]: false };
          }, {}),
        ...feedbackTypesArray
          .slice(0, NUMBER_OF_FEEDBACK_COLUMNS_TO_SHOW)
          .reduce((obj, key) => {
            return { ...obj, [`feedback_stats_${key}`]: true };
          }, {}),
        ...columnVisibility,
      },
    };
    return [columns, initialState];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    datasetShareToken,
    isDatasetPage,
    columnVisibility,
    rows,
    minMaxFeedbackValues,
  ]);

  const [columnVisibilityDebounced] = useDebounce(
    initialState.columnVisibility,
    500,
    {
      equalityFn: (prev, next) => JSON.stringify(prev) === JSON.stringify(next),
    }
  );

  const [localStorageColumnVisibility, _] = useLocalStorageState(
    localStorageDisplayColumnsKey ?? null,
    {}
  );

  const [manuallyHiddenOrShownColumns, setManuallyHiddenOrShownColumns] =
    useState<Record<string, boolean>>(
      localStorageDisplayColumnsKey ? localStorageColumnVisibility : {}
    );

  const simpleColumns = [...columns.slice(0, 7), columns[columns.length - 1]];

  const [columnsToDisplay, setColumnsToDisplay] = useState(
    props.simple ? simpleColumns : columns
  );

  // Keep the ordering the same of any columns in columnsToDisplay that begin with 'feedback_stats_'. Add any new feedback_stats_ columns
  // that are in columns but not in columnsToDisplay to the end of columnsToDisplay. This prevents the columns from jumping around
  // when the user sorts the table.

  const feedbackColumnHasData = useCallback(
    (column) => {
      // default include it if it made it this far but we need an id to check if it's a feedback column
      if (!column.id) return true;
      // check if the column is a feedback column, if not, include it
      if (
        !(
          column.id.startsWith('feedback_stats_') ||
          column.id.startsWith('session_feedback_stats_')
        )
      ) {
        return true;
      }

      if (column.id.startsWith('feedback_stats_')) {
        const name = column.id.replace('feedback_stats_', '');
        const hasSomeValue = data?.rows?.find(
          (d) =>
            d.feedback_stats &&
            name in d.feedback_stats &&
            (d.feedback_stats[name]?.avg != null ||
              (d.feedback_stats[name]?.errors ?? 0) > 0 ||
              (d.feedback_stats[name]?.values != null &&
                Object.keys(d.feedback_stats[name]?.values).length > 0))
        );

        return !!hasSomeValue;
      }

      if (column.id.startsWith('session_feedback_stats_')) {
        const name = column.id.replace('session_feedback_stats_', '');
        const hasSomeValue = data?.rows?.find(
          (d) =>
            d.session_feedback_stats &&
            name in d.session_feedback_stats &&
            d.session_feedback_stats[name]?.avg != null
        );

        return !!hasSomeValue;
      }

      // this should never happen but default include the column
      return true;
    },
    [data?.rows]
  );

  useEffect(() => {
    const feedbackColumnsToDisplay = columnsToDisplay.filter((column) =>
      column.id?.startsWith('feedback_stats_')
    );
    const feedbackColumns = columns.filter((column) =>
      column.id?.startsWith('feedback_stats_')
    );
    const feedbackColumnsToAdd = feedbackColumns.filter(
      (column) =>
        !feedbackColumnsToDisplay.some(
          (displayColumn) => displayColumn.id === column.id
        )
    );

    const sessionLevelFeedbackColumnsToDisplay = columnsToDisplay.filter(
      (column) => column.id?.startsWith('session_feedback_stats_')
    );
    const sessionLevelFeedbackColumns = columns.filter((column) =>
      column.id?.startsWith('session_feedback_stats_')
    );
    const sessionLevelFeedbackColumnsToAdd = sessionLevelFeedbackColumns.filter(
      (column) =>
        !sessionLevelFeedbackColumnsToDisplay.some(
          (displayColumn) => displayColumn.id === column.id
        )
    );

    const metadataColumnsToDisplay = columnsToDisplay.filter(
      (column) =>
        column.id?.startsWith('metadata_') &&
        column.id !== 'metadata_num_repetitions' &&
        column.id !== 'metadata_dataset_splits'
    );
    const metadataColumns = columns.filter(
      (column) =>
        column.id?.startsWith('metadata_') &&
        column.id !== 'metadata_num_repetitions' &&
        column.id !== 'metadata_dataset_splits'
    );
    const metadataColumnsToAdd = metadataColumns.filter(
      (column) =>
        !metadataColumnsToDisplay.some(
          (displayColumn) => displayColumn.id === column.id
        )
    );

    const performanceAndCostColumns = columns.filter(
      (column) =>
        !column.id?.startsWith('feedback_stats_') &&
        column.id !== 'actions' &&
        !column.id?.startsWith('metadata_') &&
        !column.id?.startsWith('session_feedback_stats_') &&
        column.id !== 'name' &&
        column.id !== 'creation_time' &&
        column.id !== 'select'
    );
    const coreInfoColumns = columns.filter(
      (column) =>
        column.id === 'name' ||
        column.id === 'creation_time' ||
        column.id === 'select' ||
        column.id === 'metadata_dataset_splits' ||
        column.id === 'metadata_num_repetitions'
    );
    const actionsColumn = columns.find((column) => column.id === 'actions');
    // Update the feedback and metadata columns to the newer version if they exist in the new columns, in case
    // anything changed
    const feedbackColumnsToDisplayUpdated = feedbackColumnsToDisplay.map(
      (column) => {
        return columns.find((c) => c.id === column.id) ?? column;
      }
    );
    const metadataColumnsToDisplayUpdated = metadataColumnsToDisplay.map(
      (column) => {
        return columns.find((c) => c.id === column.id) ?? column;
      }
    );
    const sessionLevelFeedbackColumnsToDisplayUpdated =
      sessionLevelFeedbackColumnsToDisplay.map((column) => {
        return columns.find((c) => c.id === column.id) ?? column;
      });
    const columnsToDisplayWithFeedback = [
      ...coreInfoColumns,
      ...sessionLevelFeedbackColumnsToDisplayUpdated,
      ...sessionLevelFeedbackColumnsToAdd,
      ...feedbackColumnsToDisplayUpdated,
      ...feedbackColumnsToAdd,
      ...performanceAndCostColumns,
      ...metadataColumnsToDisplayUpdated,
      ...metadataColumnsToAdd,
      ...(actionsColumn ? [actionsColumn] : []),
    ].filter((c) => feedbackColumnHasData(c)); // this just filters out feedback / session feedback columns that have no data

    const mColumns = props.simple
      ? [
          ...columnsToDisplayWithFeedback.slice(0, 7),
          ...(actionsColumn ? [actionsColumn] : []),
        ]
      : columnsToDisplayWithFeedback;
    setColumnsToDisplay(mColumns);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [columns]);

  const columnVisibilityToDisplay = {
    ...columnVisibilityDebounced,
    ...manuallyHiddenOrShownColumns,
  };

  //filter anything out of sortingState (an array) that is not in columnsToDisplay
  const sortingStateToDisplay = simple
    ? []
    : props.sortingState.filter((sort) =>
        columnsToDisplay.some((column) => column.id === sort.id)
      );
  if (
    sortableColumns &&
    sortingStateToDisplay.find((sort) => !sortableColumns.includes(sort.id))
  ) {
    sortingStateToDisplay[0].id = 'start_time';
  }

  const [columnSizing, setColumnSizing] = useDataGridSizingLocalStorage(
    localStorageDataGridSizeKey ?? 'sessions'
  );

  const { createToast } = useToast();

  const { trigger: populateQueueTrigger, isMutating: isPopulateQueueMutating } =
    usePopulateAnnotationQueueMutation();
  const handlePopulateAnnotationQueue = useCallback(
    (aq: AnnotationQueueSchema) => {
      populateQueueTrigger(
        {
          json: {
            session_ids: Object.keys(rowSelection),
            queue_id: aq.id,
          },
        },
        {
          onSuccess: () => {
            navigate(
              `/${appOrganizationPath}/${organizationId}/${appAnnotationQueuesPath}/${aq.id}`
            );
          },
          onError: (error) => {
            createToast({
              title: 'Error populating annotation queue',
              description: error.message,
              error: true,
            });
          },
        }
      );
    },
    [createToast, navigate, organizationId, populateQueueTrigger, rowSelection]
  );

  const table = useReactTable({
    columns: columnsToDisplay,
    data: rows ?? EMPTY_TABLE,
    state: {
      pagination: simple ? DEFAULT_PAGINATION_MODEL : props.pagination,
      rowSelection,
      sorting: sortingStateToDisplay,
      columnSizing,
      columnVisibility: columnVisibilityToDisplay,
    },
    initialState,
    meta: {
      datasets,
      actions: {
        handleDelete: (toDelete: SessionSchema) =>
          setSessionsToDelete([toDelete]),
        handleEdit: (toEdit: string) => setSessionToEdit(toEdit),
        deleteEnabled: authorize('annotation-queues:delete'),
        editEnabled: authorize('annotation-queues:update'),
      },
    },
    onColumnVisibilityChange: setManuallyHiddenOrShownColumns,
    onPaginationChange: !simple ? props.setPagination : undefined,
    onColumnSizingChange: setColumnSizing,

    getRowId: (row) => row.id,

    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onRowSelectionChange: setRowSelection,

    manualExpanding: true,
    manualPagination: true,
    manualFiltering: true,
    manualSorting: false,
    enableSorting: !simple,
    onSortingChange: !simple ? props.handleSortingChange : undefined,
    enableSortingRemoval: false,

    columnResizeMode: 'onChange',
    pageCount: simple
      ? 1
      : Math.ceil(+(data?.total || 0) / props.pagination.pageSize),
  });

  const onRowClickTargetUrlPrefix =
    onRowClickPrefix ??
    `/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/`;

  const defaultSortingColumn = isDatasetPage ? 'start_time' : 'name';
  const defaultSortingState = [{ id: defaultSortingColumn, desc: true }];
  const defaultSortingStateString = SORTING_NAMES[defaultSortingColumn];

  const [selectedChartingColumnRaw, setSelectedChartingColumnRaw] =
    useStateFromSearchParams('chartedColumn', null);

  const [localStorageChartColumns, setLocalStorageChartColumns] =
    useLocalStorageState<string | null>(
      datasetId ? `ls:selectedChartMetrics:${datasetId}` : null,
      null
    );

  const setSelectedChartingColumns = (columns: string[]) => {
    const value = columns.length ? columns.join(',') : DUMMY_CHART_PLACEHOLDER;
    setSelectedChartingColumnRaw(value);
    if (datasetId) {
      setLocalStorageChartColumns(value);
    }
  };

  const chartableColumns = getChartableColumns(
    columnsToDisplay as Column<SessionSchema, unknown>[]
  );

  // Prioritize URL params over localStorage, fall back to default columns
  const selectedChartingColumns =
    selectedChartingColumnRaw?.split(',') ??
    (datasetId ? localStorageChartColumns?.split(',') : null) ??
    getDefaultSelectedColumns(chartableColumns);

  const numChartsToDisplay = selectedChartingColumns.filter(
    (column) => column !== DUMMY_CHART_PLACEHOLDER
  ).length;

  const allAccessorFns: any = ACCESSOR_FNS;
  for (const column of chartableColumns) {
    if (column.startsWith('feedback_stats_')) {
      const type = column.split('feedback_stats_')[1];
      allAccessorFns[column] = (row) =>
        ACCESSOR_FNS['feedback_stats'](type, row);
    } else if (column.startsWith('session_feedback_stats_')) {
      const type = column.split('session_feedback_stats_')[1];
      allAccessorFns[column] = (row) =>
        ACCESSOR_FNS['session_feedback_stats'](type, row);
    } else {
      allAccessorFns[column] = (row) => row[column];
    }
  }

  const selectedChartsProperties = selectedChartingColumns
    .map((column) =>
      getChartPropertiesFromColumnName(column, allAccessorFns, table)
    )
    .filter((column) => column !== null);

  const onClick = (row, event) => {
    const targetUrl =
      isDatasetPage && getCompareClickUrl
        ? getCompareClickUrl([row.id])
        : `${onRowClickTargetUrlPrefix}${row.id}${onRowClickSuffix ?? ''}`;
    if (!emulateNativeClick(targetUrl, event.nativeEvent)) {
      navigate(targetUrl);
    }
  };

  const onMiddleClick = (row) => {
    const targetUrl =
      isDatasetPage && getCompareClickUrl
        ? getCompareClickUrl([row.id])
        : `${onRowClickTargetUrlPrefix}${row.id}${onRowClickSuffix ?? ''}`;

    if (!emulateNativeMiddleClick(targetUrl)) {
      navigate(targetUrl);
    }
  };

  const [experimentMetadataPath, setExperimentMetadataPath] =
    useLocalStorageState<string | undefined>(
      `${EXPERIMENT_METADATA_PATH_LOCAL_STORAGE_PREFIX}${datasetId}`,
      undefined
    );
  const [isAnnotationQueueCrudPaneOpen, setIsAnnotationQueueCrudPaneOpen] =
    useState(false);

  // Count the num_runs for all of the sessions in the row selection
  const numRunsSelected = useMemo(() => {
    return Object.keys(rowSelection).reduce((acc, id) => {
      const session = rows?.find((row) => row.id === id);
      return acc + (session?.run_count ?? 0);
    }, 0);
  }, [rowSelection, rows]);

  const sessionNamesSelected = useMemo(() => {
    return Object.keys(rowSelection)
      .map((id) => {
        const session = rows?.find((row) => row.id === id);
        return session?.name;
      })
      .filter(Boolean) as string[];
  }, [rowSelection, rows]);

  return (
    <>
      {!simple && (
        <div className="mb-2 flex gap-2" data-testid="sessions-table-header">
          <DataGridSearchInput
            value={props.name}
            onChange={props.setName}
            size={isDatasetPage ? 'sm' : 'md'}
          />

          {isDatasetPage && (
            <SessionsDataGridChartPicker
              table={table}
              chartableColumns={chartableColumns}
              onChange={setSelectedChartingColumns}
              selectedColumnIds={selectedChartingColumns ?? undefined}
              allAccessorFns={allAccessorFns}
              rowData={rows}
            />
          )}

          {isDatasetPage && (
            <SelectChartsXAxisLabel
              aggregateMetadataKeys={metadataKeysAggregate}
              experimentMetadataPath={experimentMetadataPath}
              setExperimentMetadataPath={setExperimentMetadataPath}
            />
          )}

          {sessionFilters &&
            activeFilters &&
            setSessionFilters &&
            isDatasetPage && (
              <SessionsFilterBar
                commonFeedback={feedbackTypesArray}
                commonMetadata={metadataAggregate}
                filters={sessionFilters}
                activeFilters={activeFilters}
                onChange={setSessionFilters}
              />
            )}

          {!props.hideColumnVisibilityToggle && (
            <DataGridColumnVisibilityPopover
              table={table}
              localStorageKey={localStorageDisplayColumnsKey}
              size={isDatasetPage ? 'sm' : 'md'}
            />
          )}
        </div>
      )}

      {isDatasetPage && numChartsToDisplay > 0 && (
        <div className="flex w-full gap-3 overflow-x-auto">
          {!rows
            ? Array.from({ length: numChartsToDisplay }).map((_, index) => (
                <Skeleton
                  key={`chart-skeleton-${index}`}
                  className="h-[336px] w-[450px] shrink-0 rounded-md border border-secondary"
                />
              ))
            : selectedChartsProperties.map((chartProperties) => {
                return (
                  chartProperties.selectedColumn && (
                    <SessionsTableChart
                      key={'chart-' + chartProperties.selectedColumn.id}
                      experimentMetadataPath={experimentMetadataPath}
                      setExperimentMetadataPath={setExperimentMetadataPath}
                      rowData={rows.slice().reverse()}
                      accessorFn={chartProperties.accessorFn}
                      yAxisLabel={chartProperties.yAxisLabel ?? ''}
                      onClick={onClick}
                      color={chartProperties.chartColor}
                      isCurrency={chartProperties.isCurrency ?? false}
                      isFeedback={
                        chartProperties.selectedColumn.id.startsWith(
                          'feedback_stats_'
                        ) ||
                        chartProperties.selectedColumn.id.startsWith(
                          'session_feedback_stats_'
                        )
                      }
                      feedbackKey={
                        chartProperties.selectedColumn.id.startsWith(
                          'feedback_stats_'
                        )
                          ? chartProperties.selectedColumn.id.split(
                              'feedback_stats_'
                            )[1]
                          : chartProperties.selectedColumn.id.split(
                              'session_feedback_stats_'
                            )[1]
                      }
                    />
                  )
                );
              })}
        </div>
      )}
      <div className="overflow-hidden rounded-lg border border-secondary">
        <DataGrid
          table={table}
          cellHeight={56}
          cellSpacingVariant="wide"
          isLoading={isLoading}
          emptyState={emptyState}
          onClick={onClick}
          onMiddleClick={onMiddleClick}
          stickyRightColumn={true}
        />
      </div>

      {!simple && (
        <div className="flex flex-row-reverse items-start justify-between gap-2">
          <DataGridPagination table={table} />
          {data?.excluding_empty &&
            (data?.rows?.length ?? 0) <
              table.getState().pagination.pageSize && (
              <div className="mt-3">
                <BannerWithIcon
                  className="bg-opacity-10 text-black dark:text-white"
                  noIcon
                >
                  <span>
                    Current sorting (
                    {props.sortingState[0].id.startsWith('feedback_stats_')
                      ? props.sortingState[0].id.split('feedback_stats_')[1]
                      : SORTING_NAMES[props.sortingState[0].id]}
                    ) excludes projects with no runs.{' '}
                    <a
                      href="#"
                      className="underline"
                      onClick={(e) => {
                        e.preventDefault();
                        props.handleSortingChange(() => defaultSortingState);
                      }}
                    >
                      Sort by {defaultSortingStateString}
                    </a>{' '}
                    to see all.
                  </span>
                </BannerWithIcon>
              </div>
            )}
        </div>
      )}

      <MultiDeleteModal
        endpoint={apiSessionsPath}
        items={sessionsToDelete ?? []}
        idParamName="session_ids"
        isOpen={!!sessionsToDelete && sessionsToDelete.length > 0}
        doClose={(didSucceed) => {
          if (didSucceed) {
            setRowSelection({});
            const dataRowsWithoutRowSelection = data?.rows?.filter(
              (s) => !rowSelection[s.id]
            );
            mutateSessions?.({
              rows: dataRowsWithoutRowSelection,
              total:
                (data?.rows?.length ?? 0) -
                (dataRowsWithoutRowSelection?.length ?? 0) +
                (data?.total ?? 0),
            });
          }
          setSessionsToDelete(null);
        }}
      />

      <SessionEditFromDataGrid
        rows={rows}
        id={sessionToEdit}
        onSuccess={mutateSessions}
        onClose={() => setSessionToEdit(null)}
      />

      <AnnotationQueueCrudPane
        open={isAnnotationQueueCrudPaneOpen}
        closeModal={() => setIsAnnotationQueueCrudPaneOpen(false)}
        onSuccess={(aq) => {
          navigate(
            `/${appOrganizationPath}/${organizationId}/${appAnnotationQueuesPath}/${aq.id}`
          );
        }}
        sessionIds={Object.keys(rowSelection)}
        numRunsToAnnotate={numRunsSelected}
        sessionNamesToAnnotate={sessionNamesSelected}
      />

      {Object.keys(rowSelection).length > 0 && (
        <div className="fixed bottom-3 left-1/2 z-50 -translate-x-1/2">
          <div className="flex items-center gap-4 rounded-md border border-secondary bg-popover p-2 pl-4 pr-2 shadow-md">
            <span className="text-md">
              {Object.keys(rowSelection).length === 1 ? (
                <>1 Experiment selected</>
              ) : (
                <>{Object.keys(rowSelection).length} Experiments selected</>
              )}
            </span>

            <div className="flex items-center gap-2">
              {/* Only show Make Evaluator button when exactly one experiment is selected */}
              {Object.keys(rowSelection).length === 1 &&
                enableAlignEvaluators && (
                  <div>
                    <Button
                      size="md"
                      color="warning"
                      className="grow"
                      startDecorator={
                        <MagnifyingGlassIcon className="h-5 w-5" />
                      }
                      onClick={() => {
                        const experimentId = Object.keys(rowSelection)[0];
                        navigate(
                          `/${appOrganizationPath}/${organizationId}/generate-evaluator/${experimentId}?datasetName=${datasets.data?.[0].name}`
                        );
                      }}
                    >
                      Make Evaluator
                    </Button>
                  </div>
                )}
              <div>
                <Button
                  size="md"
                  color="primary"
                  className="grow"
                  startDecorator={<CompareIcon className="h-5 w-5" />}
                  onClick={() => {
                    const targetUrl = getCompareClickUrl?.(
                      Object.keys(rowSelection)
                    );
                    targetUrl && navigate(targetUrl);
                  }}
                >
                  Compare
                </Button>
              </div>
              <div>
                <PopulateAnnotationQueuePopover
                  handlePopulateAnnotationQueue={handlePopulateAnnotationQueue}
                  openNewAnnotationQueueModal={() =>
                    setIsAnnotationQueueCrudPaneOpen(true)
                  }
                  totalRuns={numRunsSelected}
                  numSessions={Object.keys(rowSelection).length}
                >
                  <Button
                    size="md"
                    color="info"
                    className="grow"
                    startDecorator={<Edit03Icon className="h-4 w-4" />}
                    loading={isPopulateQueueMutating}
                  >
                    Annotate
                  </Button>
                </PopulateAnnotationQueuePopover>
              </div>
              {(!isDatasetPage || !datasetShareToken) && (
                <div>
                  <Button
                    size="md"
                    color="danger"
                    className="grow"
                    startDecorator={<TrashIcon className="h-5 w-5" />}
                    onClick={() => {
                      const selectedSessions: SessionSchema[] = Object.keys(
                        rowSelection
                      )
                        .map((id) => rows?.find((row) => row.id === id))
                        .filter(
                          (session): session is SessionSchema =>
                            session !== undefined
                        );

                      setSessionsToDelete(selectedSessions);
                    }}
                  >
                    Delete
                  </Button>
                </div>
              )}
              <Button
                size="sm"
                variant="plain"
                color="primary"
                onClick={() => setRowSelection({})}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const getChartPropertiesFromColumnName = (
  chartedColumn: string | null,
  allAccessorFns: any,
  table: Table<SessionSchema>
) => {
  const accessorFn = chartedColumn ? allAccessorFns[chartedColumn] : null;
  let selectedColumn: Column<SessionSchema, unknown> | undefined = undefined;
  if (
    chartedColumn &&
    table.getAllColumns().some((col) => col.id === chartedColumn)
  ) {
    selectedColumn = table.getColumn(chartedColumn);
  }
  const chartColor =
    selectedColumn &&
    (selectedColumn.id.startsWith('feedback_stats_') ||
      selectedColumn.id.startsWith('session_feedback_stats_'))
      ? getColorByString(getDisplayNameFromColumn(selectedColumn))
      : selectedColumn
      ? '#4499F7'
      : '';

  const yAxisLabel = selectedColumn && getDisplayNameFromColumn(selectedColumn);
  const isCurrency = selectedColumn?.id === 'total_cost';

  return {
    accessorFn,
    selectedColumn,
    chartColor,
    yAxisLabel,
    isCurrency,
  };
};
