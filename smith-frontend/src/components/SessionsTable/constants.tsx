import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

import { ReactNode } from 'react';

import MetadataIcon from '@/icons/MetadataIcon.svg?react';
import { SessionSchema } from '@/types/schema';

import {
  SessionFilterComparator,
  SessionFilterField,
} from './types/SessionsFilterModel';

export const FILTER_COMPARATOR_TO_LABEL: Record<
  SessionFilterComparator,
  string
> = {
  gt: '>',
  gte: '>=',
  lt: '<',
  lte: '<=',
  eq: '=',
  neq: '!=',
  exists: 'exists', // never used
};

export const FILTER_OPTION_TO_LABEL: Record<SessionFilterField, string> = {
  metadata: 'Metadata',
  feedback_score: 'Feedback Score',
  name: 'Name',
};

export const FILTER_OPTION_TO_ICON: Record<SessionFilterField, ReactNode> = {
  metadata: <MetadataIcon className="h-4 w-4" />,
  feedback_score: <MagnifyingGlassIcon className="h-4 w-4" />,
  name: <MagnifyingGlassIcon className="h-4 w-4" />,
};

export const SORTING_NAMES = {
  name: 'Name',
  start_time: 'Created At',
  last_run_start_time: 'Most Recent Run',
  latency_p50: 'P50 Latency',
  latency_p99: 'P99 Latency',
  error_rate: 'Error Rate',
};

// Not including feedback cols which have dynamic ids
export const BASE_CHARTABLE_COLUMN_IDS = [
  'latency_p50',
  'latency_p99',
  'error_rate',
  'streaming_rate',
  'prompt_tokens',
  'completion_tokens',
  'total_tokens',
  'total_cost',
];

// Map column id to accessor function
export const ACCESSOR_FNS = {
  feedback_stats: (
    type: string,
    row: SessionSchema,
    stat: 'avg' | 'stdev' = 'avg'
  ) => {
    return row?.feedback_stats?.[type]?.[stat];
  },
  session_feedback_stats: (
    type: string,
    row: SessionSchema,
    stat: 'avg' | 'stdev' = 'avg'
  ) => {
    return row?.session_feedback_stats?.[type]?.[stat];
  },
};

export const formatter = new Intl.NumberFormat('en-US', {});
export const usdFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
  maximumFractionDigits: 20,
});

export const EMPTY_TABLE = [];
export const NUMBER_OF_FEEDBACK_COLUMNS_TO_SHOW = 50;
export const DUMMY_CHART_PLACEHOLDER = 'placeholder';
