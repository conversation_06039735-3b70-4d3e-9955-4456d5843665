import {
  AlertTriangleIcon,
  CheckCircleIcon,
  InfoCircleIcon,
  XCircleIcon,
  XIcon,
} from '@langchain/untitled-ui-icons';

import { ReactNode, useState } from 'react';

import { cn } from '@/utils/tailwind';

type BannerProps = {
  title: ReactNode | string;
  description?: ReactNode | string;
  closeable?: boolean;
  open: boolean;
  className?: string;
  intent?: 'warning' | 'info' | 'success' | 'error';
  shadow?: boolean;
  onClose?: () => void;
};

export const Banner = ({
  title,
  description,
  open,
  closeable = false,
  className,
  intent = 'warning',
  shadow = true,
  onClose,
}: BannerProps) => {
  const [show, setShow] = useState(open);

  const renderIcon = () => {
    switch (intent) {
      case 'warning':
        return <AlertTriangleIcon className="h-4 w-4 shrink-0 text-warning" />;
      case 'info':
        return (
          <InfoCircleIcon className="h-4 w-4 shrink-0 text-brand-secondary" />
        );
      case 'success':
        return <CheckCircleIcon className="h-4 w-4 shrink-0 text-success" />;
      case 'error':
        return <XCircleIcon className="h-4 w-4 shrink-0 text-error" />;
      default:
        return null;
    }
  };

  if (!show) return null;
  return (
    <div
      className={cn(
        'm-1 flex flex-row rounded-md border p-4 py-3.5 text-sm',
        shadow && 'shadow-lg',
        intent === 'warning' && 'border-warning bg-warning-primary',
        intent === 'info' && 'br-brand-tertiary border-brand',
        intent === 'success' && 'border-success bg-success-primary',
        intent === 'error' && 'border-error bg-error-primary',
        className
      )}
    >
      <div className="flex flex-col gap-2">
        <div className="flex flex-row items-center gap-2">
          {renderIcon()}
          {typeof title === 'string' ? (
            <span className="text-xs font-semibold text-primary">{title}</span>
          ) : (
            title
          )}
        </div>
        {typeof description === 'string' ? (
          <span className="text-xs text-primary">{description}</span>
        ) : (
          description
        )}
      </div>
      {closeable && (
        <div className="ml-auto">
          <button
            type="button"
            className={cn(
              'rounded-md text-primary',
              intent === 'warning' &&
                'hover:bg-warning-strong hover:text-button-primary',
              intent === 'info' &&
                'hover:bg-brand-primary hover:text-button-primary',
              intent === 'success' &&
                'hover:bg-success-strong hover:text-button-primary',
              intent === 'error' &&
                'hover:bg-error-strong hover:text-button-primary'
            )}
            onClick={() => {
              setShow(false);
              onClose?.();
            }}
          >
            <XIcon className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default Banner;
