import { cn } from '@/utils/tailwind';

import { ScrollSpyElement, ScrollSpyState } from './hooks/useScrollSpyState';

export const ScrollSpy = (
  props: ScrollSpyState & {
    elements: ScrollSpyElement[];
    className?: string;
    horizontal?: boolean;
    elementClassName?: string;
  }
) => {
  const scrolledElementClassName = 'bg-brand-tertiary text-primary';

  return (
    props.needsScrollSpy && (
      <div
        className={cn(
          'sticky top-2 flex h-fit min-w-[125px] gap-2',
          props.horizontal ? 'flex-row' : 'flex-col',
          props.className
        )}
      >
        {props.elements.map((element) => (
          <button
            key={element.id}
            type="button"
            className={cn(
              'flex rounded-lg p-1 px-2 font-medium text-quaternary',
              props.scrolledElement === element.id
                ? scrolledElementClassName
                : 'hover:bg-secondary',
              props.elementClassName
            )}
            onClick={() => props.scrollToRef(element.elementRef)}
          >
            {element.title}
          </button>
        ))}
      </div>
    )
  );
};
