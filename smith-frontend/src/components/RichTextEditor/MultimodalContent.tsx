import {
  CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE,
  DATA_PREFIX,
  MIME_TYPES,
  MimeType,
} from './constants';

const titleMap = {
  [MIME_TYPES.PDF]: 'PDF Content',
  [MIME_TYPES.JSON]: 'JSON Content',
  [MIME_TYPES.TEXT]: 'Text Content',
  [MIME_TYPES.CSV]: 'CSV Content',
};

// Note: Do not allow text/html or other risky mime types.
// SVG is handled above in the IMAGE case.
const iframeTypes: MimeType[] = [
  MIME_TYPES.PDF,
  MIME_TYPES.JSON,
  MIME_TYPES.TEXT,
  MIME_TYPES.CSV,
];

export function MultimodalContent({
  contentType,
  url,
  contentName,
  unstyled,
  mimeSubtype,
}: {
  contentType: string | null;
  url: string | undefined;
  contentName: string;
  mimeSubtype?: string;
  unstyled?: boolean;
}) {
  if (!url || !contentType) return null;

  const contentTypeWithSubtype =
    mimeSubtype && !contentType.includes('/')
      ? `${contentType}/${mimeSubtype}`
      : contentType;

  const isBase64 = url.startsWith(DATA_PREFIX) || /^[A-Za-z0-9+/=]+$/.test(url);
  const srcUrl =
    isBase64 && !url.startsWith(DATA_PREFIX)
      ? `${CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE(contentTypeWithSubtype)}${url}`
      : url;

  if (contentTypeWithSubtype.startsWith(MIME_TYPES.IMAGE)) {
    return <img src={srcUrl} alt={contentName} style={{ maxWidth: '100%' }} />;
  }

  if (contentTypeWithSubtype.startsWith(MIME_TYPES.AUDIO)) {
    return (
      <audio controls>
        <source src={srcUrl} type={contentTypeWithSubtype} />
        Your browser does not support the audio element.
      </audio>
    );
  }

  if (contentTypeWithSubtype.startsWith(MIME_TYPES.VIDEO)) {
    return (
      <video controls style={{ maxWidth: '100%' }}>
        <source src={srcUrl} type={contentType} />
        Your browser does not support the video tag.
      </video>
    );
  }

  if (iframeTypes.includes(contentType as MimeType)) {
    return (
      <iframe
        src={srcUrl}
        title={contentName ?? titleMap[contentType]}
        style={unstyled ? {} : { width: '90vw', height: '80vh' }}
      />
    );
  }

  return (
    <div className="rounded-md border border-warning p-2 text-xs text-warning">
      Unsupported content type: {contentTypeWithSubtype}
    </div>
  );
}
