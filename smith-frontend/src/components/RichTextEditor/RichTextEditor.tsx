import { TemplateFormat } from '@langchain/core/prompts';
import { Node as TiptapCoreNode } from '@tiptap/core';
import Document from '@tiptap/extension-document';
import Dropcursor from '@tiptap/extension-dropcursor';
import { History } from '@tiptap/extension-history';
import Image from '@tiptap/extension-image';
import { Mention } from '@tiptap/extension-mention';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import { Fragment, Slice } from '@tiptap/pm/model';
import { Plugin } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';
import { EditorContent, Extension, mergeAttributes } from '@tiptap/react';

import {
  MutableRefObject,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useDebouncedCallback } from 'use-debounce';

import { PLAYGROUND_RESERVED_NAMES } from '@/Pages/Playground/constants';
import {
  MessageCapability,
  MessageContentPart,
  S3DataSchema,
} from '@/types/schema';
import { parseTextWithLinks } from '@/utils/parseTextLinks';
import { cn } from '@/utils/tailwind';

import { RichTextEditorVariableMappingProps } from '../EvaluatorCrudPane/types';
import { RichTextImageEditorVariant } from './MultimodalDropdown/types';
import {
  CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE,
  DATA_PREFIX,
  MIME_TYPES,
  MimeType,
  SUPPORTED_MIME_TYPES,
  SUPPORTED_SUBTYPES,
} from './constants';
import { Suggestions, SuggestionsConfig } from './suggestions/Suggestions';
import {
  convertFileToBase64,
  convertMessageToJSONContent,
  getMimeTypeFromDataUrl,
  getNodesFromTemplate,
  handleCopyOrCut,
  isBase64,
  parseAudioSource,
} from './utils/RichTextEditor.utils';
import { VariableHighlighter } from './utils/VariableHighlighter';
import { useEditor } from './utils/use-editor';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    customExtension: {
      /**
       * Updates variable mappings between template variables and dataset variables
       */
      updateVariableMappings: (
        variableMappings: Record<string, string> | null
      ) => ReturnType;

      /**
       * Updates available paths for variable mappings
       */
      updateAvailablePaths: (availablePaths: string[] | null) => ReturnType;

      /**
       * Updates the list of variables that cannot be edited
       */
      updateUneditableVariables: (uneditableVariables: string[]) => ReturnType;

      /**
       * Registers callbacks for updating variable mappings and active variable
       */
      registerUpdateCallbacks: (params: {
        setVariableMappings: (variableMappings: Record<string, string>) => void;
        setActiveVariable: (activeVariable: string) => void;
      }) => ReturnType;
    };
  }
}

export interface IframeOptions {
  allowFullscreen: boolean;
  HTMLAttributes: {
    [key: string]: any;
  };
}

const ArbitraryFileExtension = (templateFormat: TemplateFormat) =>
  TiptapCoreNode.create<IframeOptions>({
    name: 'file',
    group: 'block',
    atom: true,
    selectable: true, // Make the node selectable
    draggable: true, // Allow dragging

    addKeyboardShortcuts() {
      return {
        Backspace: () => {
          const { empty, $anchor } = this.editor.state.selection;
          const isFileNode = $anchor.node().type === this.type;

          if (!empty && isFileNode) {
            return this.editor.commands.deleteSelection();
          }
          return false;
        },
      };
    },

    addOptions() {
      return {
        allowFullscreen: true,
        HTMLAttributes: {
          class: 'iframe-wrapper',
        },
      };
    },

    addAttributes() {
      return {
        src: { default: null },
        type: { default: null },
        title: { default: null },
        frameborder: {
          default: 0,
        },
        allowfullscreen: {
          default: this.options.allowFullscreen,
          parseHTML: () => this.options.allowFullscreen,
        },
      };
    },

    parseHTML() {
      return [{ tag: 'iframe' }, { tag: 'audio' }, { tag: 'video' }];
    },

    renderHTML({ HTMLAttributes }) {
      if (!HTMLAttributes.src) {
        return ['div', { class: 'error' }, 'Invalid file data'];
      }
      const url = HTMLAttributes.src;

      const variable = getNodesFromTemplate(url, templateFormat);

      const replacedSrc = Object.entries(PLAYGROUND_RESERVED_NAMES).reduce(
        (acc, [key, value]) => {
          return acc.replace(key, value);
        },
        url
      );

      // Render as a variable if it is a template variable
      if (variable[0].type === 'variable' || !HTMLAttributes.type) {
        return [
          'div',
          {
            class: cn(
              'relative my-1 flex w-fit items-center justify-center gap-2 rounded-md border border-secondary bg-secondary-hover p-2 text-center text-brand-green-400'
            ),
          },
          ['span', replacedSrc],
        ];
      }

      // Otherwise, render as a file
      const contentType =
        HTMLAttributes.type ??
        (url.startsWith(DATA_PREFIX) &&
          url.split(DATA_PREFIX)[1].split(';')[0]);
      if (contentType == null) {
        return [
          'div',
          { class: 'error' },
          'Could not parse content type from file data.',
        ];
      }
      const isBase64Url = isBase64(url);

      const srcUrl =
        isBase64Url && !url.startsWith(DATA_PREFIX)
          ? `${CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE(contentType)}${url}`
          : url;

      if (contentType.startsWith(MIME_TYPES.VIDEO)) {
        return [
          MIME_TYPES.VIDEO,
          { controls: 'true' },
          ['source', { src: srcUrl, type: contentType }],
        ];
      }

      return [
        'div',
        {
          class: cn(
            'relative inline-block cursor-pointer select-none',
            this.editor &&
              !this.editor.isEditable &&
              '[&:has(iframe:focus)]:outline [&:has(iframe:focus)]:outline-1 [&:has(iframe:focus)]:outline-brand-green-400'
          ),
        },
        [
          'iframe',
          {
            src: srcUrl + (contentType === MIME_TYPES.PDF ? '#toolbar=0' : ''),
            title: HTMLAttributes.title,
            toolbar: 0,
            style: 'max-width: 400px; min-height: 200px;',
            tabindex: '-1',
          },
        ],
      ];
    },

    addCommands() {
      return {
        setIframe:
          (options: { src: string }) =>
          ({ tr, dispatch }) => {
            const { selection } = tr;
            const node = this.type.create(options);

            if (dispatch) {
              tr.replaceRangeWith(selection.from, selection.to, node);
            }

            return true;
          },
      } as any;
    },
  });

const AutoLinkExtension = Extension.create({
  name: 'autoLink',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        props: {
          decorations: (state) => {
            const decorations: Decoration[] = [];

            state.doc.descendants((node, pos) => {
              if (node.isText) {
                const parts = parseTextWithLinks(node.text ?? '');
                let currentPos = 0;

                parts.forEach((part) => {
                  if (part.type === 'link') {
                    const start = pos + currentPos;
                    const end = start + part.content.length;
                    decorations.push(
                      Decoration.inline(start, end, {
                        class: 'text-ls-blue underline cursor-pointer',
                        onclick: `window.open('${
                          part.url?.startsWith('http')
                            ? part.url
                            : `https://${part.url}`
                        }', '_blank', 'noopener,noreferrer')`,
                      })
                    );
                  }
                  currentPos += part.content.length;
                });
              }
            });
            return DecorationSet.create(state.doc, decorations);
          },
        },
      }),
    ];
  },
});

const AudioExtension = (templateFormat: TemplateFormat) =>
  TiptapCoreNode.create({
    name: 'audio',
    group: 'block',
    atom: true,
    selectable: true,
    draggable: true,

    addAttributes() {
      return {
        src: {
          default: null,
        },
        format: {
          default: undefined,
        },
        controls: {
          default: true,
        },
      };
    },

    parseHTML() {
      return [
        {
          tag: 'audio',
        },
      ];
    },

    renderHTML({ HTMLAttributes }) {
      if (!HTMLAttributes.src) {
        return ['div', { class: 'error' }, 'Invalid audio data'];
      }
      const url = HTMLAttributes.src;

      const variable = getNodesFromTemplate(url, templateFormat);

      const replacedSrc = Object.entries(PLAYGROUND_RESERVED_NAMES).reduce(
        (acc, [key, value]) => {
          return acc.replace(key, value);
        },
        url
      );

      // Render as a variable if it is a template variable
      if (variable[0].type === 'variable') {
        return [
          'div',
          {
            class: cn(
              'relative my-1 flex w-fit items-center justify-center gap-2 rounded-md border border-secondary bg-secondary-hover p-2 text-center text-brand-green-400'
            ),
          },
          ['span', replacedSrc],
        ];
      }

      // Otherwise, render as an audio element
      return ['audio', { controls: 'true', ...HTMLAttributes }];
    },
  });

const EXTENSIONS = (templateFormat: TemplateFormat) => [
  Document,
  Paragraph,
  AutoLinkExtension,
  ArbitraryFileExtension(templateFormat),
  AudioExtension(templateFormat),
  Extension.create({
    name: 'OverrideEscape',
    addKeyboardShortcuts() {
      return {
        Escape: () => this.editor.commands.blur(),
      };
    },
  }),
  Image.configure({ allowBase64: true }).extend({
    addAttributes() {
      return {
        src: {
          default: null,
        },
        alt: {
          default: null,
        },
        title: {
          default: null,
        },
        'data-s3-presigned': {
          default: null,
        },
      };
    },

    renderHTML({ HTMLAttributes }) {
      if (HTMLAttributes['src']) {
        const src: string = HTMLAttributes['src'];
        const variable = getNodesFromTemplate(src, templateFormat);

        const replacedSrc = Object.entries(PLAYGROUND_RESERVED_NAMES).reduce(
          (acc, [key, value]) => {
            return acc.replace(key, value);
          },
          src
        );

        if (variable[0].type === 'variable') {
          return [
            'div',
            {
              class: cn(
                'relative my-1 flex w-fit items-center justify-center gap-2 rounded-md border border-secondary bg-secondary-hover p-2 text-center text-brand-green-400'
              ),
            },
            ['span', replacedSrc],
          ];
        }
      }

      return [
        'img',
        mergeAttributes(
          this.options.HTMLAttributes,
          HTMLAttributes,
          HTMLAttributes['data-s3-presigned']
            ? { src: HTMLAttributes['data-s3-presigned'] }
            : {}
        ),
      ];
    },
  }),
  Text,
  Dropcursor,
  History,
];

const TEMPLATE_EXTENSIONS = ({
  templateFormat,
  hideSyntaxHighlighting,
  suggestions,
}: {
  templateFormat: TemplateFormat;
  hideSyntaxHighlighting: boolean;
  suggestions: SuggestionsConfig | null;
}) => [
  ...EXTENSIONS(templateFormat),
  ...(hideSyntaxHighlighting
    ? []
    : [
        VariableHighlighter({
          templateFormat: templateFormat,
          acceptedVariableList: suggestions?.items,
        }),
      ]),
  ...(suggestions
    ? [
        Mention.configure({
          suggestion: Suggestions({ suggestions }),
        }),
      ]
    : []),
];

export function RichTextEditor(props: {
  id?: string;
  initialValue?: string | Array<MessageContentPart> | null | undefined;
  className?: string;
  onChange?: (e: string | Array<MessageContentPart>) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  autoFocus?: boolean;
  readOnly?: boolean;
  hideSyntaxHighlighting?: boolean;
  variant?: RichTextImageEditorVariant;
  attachments?: Record<string, S3DataSchema>;
  uploadRef?: MutableRefObject<((type: MimeType, src: string) => void) | null>;
  capabilities?: MessageCapability[];
  templateFormat?: TemplateFormat;
  useSuggestions?: boolean;
  suggestions?: SuggestionsConfig;
  refreshContentRef?: MutableRefObject<(content: string) => void>;
  smallText?: boolean;
  // Used in smith-frontend/src/components/EvaluatorCrudPane/DatasetEvaluatorCrudPane.tsx
  // to update the variable mappings between template variable and dataset variable
  variableMappingProps?: RichTextEditorVariableMappingProps;
}) {
  // Internal state for immediate UI updates
  const [internalContent, setInternalContent] = useState<
    string | Array<MessageContentPart>
  >(props.initialValue ?? '');

  // Convert initial content for TipTap
  const content = convertMessageToJSONContent(
    internalContent,
    props.attachments
  );

  // Debounced callback for parent updates
  const debouncedOnChange = useDebouncedCallback(
    (value: string | Array<MessageContentPart>) => {
      props.onChange?.(value);
    },
    100
  );

  function insertImage(src: string, position?: number) {
    if (!editor) return;
    editor.view.dispatch(
      editor.view.state.tr.insert(
        position ?? editor.view.state.doc.content.size,
        editor.schema.nodes.image.create({ src })
      )
    );
  }

  function insertFile(src: string, position?: number) {
    if (!editor) return;
    editor.view.dispatch(
      editor.view.state.tr.insert(
        position ?? editor.view.state.doc.content.size,
        editor.schema.nodes.file.create({
          src: src,
          type: MIME_TYPES.PDF,
        })
      )
    );
  }

  function insertAudio(src: string, position?: number) {
    if (!editor) return;

    const { variableName, fileType } = parseAudioSource(src);

    editor.view.dispatch(
      editor.view.state.tr.insert(
        position ?? editor.view.state.doc.content.size,
        editor.schema.nodes.audio.create({
          src: variableName,
          format: fileType,
        })
      )
    );
  }

  if (props.uploadRef != null) {
    props.uploadRef.current = (type, src) => {
      // Map over supported mime types
      if (SUPPORTED_MIME_TYPES.includes(type)) {
        if (type === MIME_TYPES.IMAGE) {
          insertImage(src);
        } else if (type === MIME_TYPES.PDF) {
          insertFile(src);
        } else if (type === MIME_TYPES.AUDIO) {
          insertAudio(src);
        }
      } else {
        console.error(`Unsupported mime type: ${type}`);
      }
    };
  }

  const suggestionsListString = useMemo(() => {
    const suggestedItems = [...(props.suggestions?.items ?? [])];
    suggestedItems.sort((a, b) => a.localeCompare(b));
    return JSON.stringify(suggestedItems);
  }, [props.suggestions]);

  const extensions = useMemo(() => {
    return props.variant === 'template'
      ? TEMPLATE_EXTENSIONS({
          templateFormat: props.templateFormat ?? 'f-string',
          hideSyntaxHighlighting: !!props.hideSyntaxHighlighting,
          suggestions: props.useSuggestions ? props.suggestions ?? null : null,
        })
      : EXTENSIONS(props.templateFormat ?? 'f-string');
  }, [
    props.variant,
    props.templateFormat,
    props.hideSyntaxHighlighting,
    suggestionsListString,
  ]);

  const textSize = props.smallText ? 'text-sm' : 'text-md';

  const editor = useEditor(
    {
      extensions: extensions,
      content,
      editorProps: {
        attributes: {
          class: cn(
            'cursor-text whitespace-pre-wrap bg-transparent !break-anywhere focus:outline-none',
            textSize
          ),
        },
        handleDrop(view, event, _, moved) {
          if (!props.capabilities?.includes(MessageCapability.IMAGE)) return;
          if (
            !moved &&
            event.dataTransfer &&
            event.dataTransfer.files &&
            event.dataTransfer.files[0]
          ) {
            const [file] = event.dataTransfer.files;

            const coordinates = view.posAtCoords({
              left: event.clientX,
              top: event.clientY,
            });

            if (!coordinates) return false;
            convertFileToBase64(file).then((src) =>
              insertImage(src, coordinates.pos)
            );

            return true;
          }

          return false;
        },
        handlePaste(view, event) {
          const text = event.clipboardData?.getData('text/plain');
          if (!text) return false;

          const lines = text.split(/\r?\n/);

          const { state, dispatch } = view;
          const { schema } = state;

          const nodes = lines.map((line) => {
            return schema.nodes.paragraph.create(
              {},
              line ? schema.text(line) : null
            );
          });
          const slice = new Slice(Fragment.fromArray(nodes), 0, 0);
          const transaction = state.tr.replaceSelection(slice);
          dispatch(transaction);

          event.preventDefault();
          return true;
        },
        handleDOMEvents: {
          copy: handleCopyOrCut,
          cut: handleCopyOrCut,
        },
      },
      onUpdate: ({ editor }) => {
        const json = editor.getJSON();
        const values: Array<MessageContentPart> = [];

        for (const child of json.content ?? []) {
          switch (child.type) {
            case 'paragraph': {
              const text = (
                child.content
                  ?.map((i) => i.text)
                  .filter((x): x is string => x != null) ?? []
              ).join('');

              values.push({ type: 'text' as const, text });
              break;
            }
            case 'text': {
              if (child.text)
                values.push({ type: 'text' as const, text: child.text });
              break;
            }
            case 'image': {
              if (child.attrs?.src) {
                values.push({
                  type: 'image_url',
                  image_url: { url: child.attrs.src },
                });
              }
              break;
            }
            case 'file': {
              if (child.attrs?.src) {
                // if not a base64 url, we're likely dealing with a template variable, don't add the prefix
                const srcBase64 =
                  !isBase64(child.attrs.src) ||
                  child.attrs.src.startsWith(
                    CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE(MIME_TYPES.PDF)
                  )
                    ? child.attrs.src
                    : `${CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE(
                        MIME_TYPES.PDF
                      )}${child.attrs.src}`;

                values.push({
                  type: 'file',
                  file: {
                    file_data: srcBase64,
                    filename: child.attrs.title ?? 'file',
                  },
                });
              }
              break;
            }
            case 'audio': {
              if (child.attrs?.src) {
                const mimeType = getMimeTypeFromDataUrl(child.attrs.src);
                if (!mimeType) {
                  const format = child.attrs.format;
                  if (!format) {
                    console.error('No file type found in audio src');
                    break;
                  }
                  values.push({
                    type: 'input_audio',
                    input_audio: {
                      data: child.attrs.src,
                      format: format as 'wav' | 'mp3',
                    },
                  });
                  break;
                }
                const audioFormat = mimeType.split('/')[1];
                if (
                  SUPPORTED_SUBTYPES[MIME_TYPES.AUDIO]?.includes(audioFormat)
                ) {
                  values.push({
                    type: 'input_audio',
                    input_audio: {
                      data: child.attrs.src.split(',')[1],
                      format: audioFormat as 'wav' | 'mp3',
                    },
                  });
                } else {
                  console.error(
                    `Invalid audio format: ${audioFormat}. Only WAV and MP3 files are supported.`
                  );
                }
              }
              break;
            }
          }
        }

        let result: MessageContentPart[] | string = values;
        if (values.every((x) => x.type === 'text')) {
          result = values
            .filter(
              (i): i is { type: 'text'; text: string } => i.type === 'text'
            )
            .map((i) => i.text)
            .join('\n');
        }

        // Update internal state immediately
        setInternalContent(result);
        // Debounce the parent callback
        debouncedOnChange(result);
      },
      autofocus: props.autoFocus && !props.readOnly,
      editable: !props.readOnly,
      ...(props.onFocus ? { onFocus: props.onFocus } : {}),
      ...(props.onBlur ? { onBlur: props.onBlur } : {}),
    },
    [extensions, props.readOnly]
  );

  function refreshContent(content: string) {
    if (!editor) return;
    setInternalContent(content);

    editor.commands.setContent(
      convertMessageToJSONContent(content, props.attachments)
    );
  }

  if (props.refreshContentRef != null) {
    props.refreshContentRef.current = refreshContent;
  }

  const updateVariableMappings = useCallback(
    (variableMappings: Record<string, string>) => {
      if (
        !props.hideSyntaxHighlighting &&
        typeof editor?.commands.updateVariableMappings === 'function' &&
        variableMappings
      ) {
        editor?.commands.updateVariableMappings?.(variableMappings);
      }
    },
    [editor, props.hideSyntaxHighlighting]
  );
  const updateAvailablePaths = useCallback(
    (availablePaths?: string[]) => {
      if (
        !props.hideSyntaxHighlighting &&
        typeof editor?.commands.updateAvailablePaths === 'function' &&
        availablePaths
      ) {
        editor?.commands.updateAvailablePaths?.(availablePaths);
      }
    },
    [editor, props.hideSyntaxHighlighting]
  );
  const updateUneditableVariables = useCallback(
    (uneditableVariables?: string[]) => {
      if (
        !props.hideSyntaxHighlighting &&
        typeof editor?.commands.updateUneditableVariables === 'function' &&
        uneditableVariables
      ) {
        editor?.commands.updateUneditableVariables?.(uneditableVariables ?? []);
      }
    },
    [editor, props.hideSyntaxHighlighting]
  );

  useEffect(() => {
    if (
      !props.hideSyntaxHighlighting &&
      typeof editor?.commands.registerUpdateCallbacks === 'function' &&
      props.variableMappingProps
    ) {
      const { setVariableMappings, setActiveVariable } =
        props.variableMappingProps;
      editor?.commands.registerUpdateCallbacks?.({
        setVariableMappings,
        setActiveVariable,
      });
    }
  }, [editor, props.variableMappingProps, props.hideSyntaxHighlighting]);

  useEffect(() => {
    updateAvailablePaths(props.variableMappingProps?.availablePaths ?? []);
    updateUneditableVariables(
      props.variableMappingProps?.uneditableVariables ?? []
    );
  }, [
    props.variableMappingProps,
    updateAvailablePaths,
    updateUneditableVariables,
  ]);

  useEffect(() => {
    if (props.variableMappingProps?.variableMappings) {
      updateVariableMappings(props.variableMappingProps.variableMappings);
    }
  }, [props.variableMappingProps, updateVariableMappings]);

  return (
    <div
      className={cn(
        'grid w-full',
        !props.readOnly &&
          '[&_.ProseMirror-selectednode]:outline [&_.ProseMirror-selectednode]:outline-1 [&_.ProseMirror-selectednode]:outline-brand-green-400',
        props.className
      )}
    >
      <EditorContent editor={editor} />
    </div>
  );
}
