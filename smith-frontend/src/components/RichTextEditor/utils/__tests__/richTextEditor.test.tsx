import mustache from 'mustache';
import { describe, expect, it } from 'vitest';

import {
  Segment,
  convertFileToBase64,
  convertMessageToJSONContent,
  getMimeTypeFromDataUrl,
  getNodesFromTemplate,
  isBase64,
  parseAudioSource,
  parsedMustacheTemplateToNodes,
} from '../RichTextEditor.utils';
import { splitMarkdownCodeBlocks } from '../markdown';

describe('convertFileToBase64', () => {
  it('should convert an image file to base64 string', async () => {
    const file = new File(['file-content'], 'filename.png', {
      type: 'image/png',
    });
    const result = await convertFileToBase64(file);
    expect(result).toMatch(/^data:image\/png;base64,/);
  });

  it('should throw an error for non-image file', async () => {
    const file = new File(['file-content'], 'filename.txt', {
      type: 'text/plain',
    });
    await expect(convertFileToBase64(file)).rejects.toThrow(
      'Not a valid image'
    );
  });
});

describe('parsedMustacheTemplateToNodes', () => {
  it('should return variables for name, variable, partial, and comment tokens', () => {
    const template = 'Hello, {{name}}! {{&variable}}{{>partial}}{{!comment}}';
    const parsed = mustache.parse(template);
    const segments: Segment[] = parsedMustacheTemplateToNodes(
      parsed,
      template,
      true
    );
    expect(segments).toEqual([
      {
        type: 'literal',
        text: 'Hello, ',
        textWithDelimiters: 'Hello, ',
        cursor: 0,
      },
      {
        type: 'variable',
        text: 'name',
        textWithDelimiters: '{{name}}',
        cursor: 7,
      },
      { type: 'literal', text: '! ', textWithDelimiters: '! ', cursor: 15 },
      {
        type: 'variable',
        text: 'variable',
        textWithDelimiters: '{{&variable}}',
        cursor: 17,
      },
      {
        type: 'variable',
        text: 'partial',
        textWithDelimiters: '{{>partial}}',
        cursor: 30,
      },
      {
        type: 'literal',
        text: 'comment',
        textWithDelimiters: '{{!comment}}',
        cursor: 42,
      },
    ]);
  });

  it('should return the right segments for section tokens', () => {
    const template = '{{#section}}Section content{{/section}}';
    const parsed = mustache.parse(template);
    const segments: Segment[] = parsedMustacheTemplateToNodes(
      parsed,
      template,
      true
    );
    expect(segments).toEqual([
      {
        type: 'variable',
        text: 'section',
        textWithDelimiters: '{{#section}}',
        cursor: 0,
      },
      {
        type: 'literal',
        text: 'Section content',
        textWithDelimiters: 'Section content',
        cursor: 12,
      },
      {
        type: 'variable',
        text: 'section',
        textWithDelimiters: '{{/section}}',
        cursor: 27,
      },
    ]);
  });

  it('should return the right segments for inverted section tokens', () => {
    const template = '{{^inverted_section}}No data{{/inverted_section}}';
    const parsed = mustache.parse(template);
    const segments: Segment[] = parsedMustacheTemplateToNodes(
      parsed,
      template,
      true
    );
    expect(segments).toEqual([
      {
        type: 'variable',
        text: 'inverted_section',
        textWithDelimiters: '{{^inverted_section}}',
        cursor: 0,
      },
      {
        type: 'literal',
        text: 'No data',
        textWithDelimiters: 'No data',
        cursor: 21,
      },
      {
        type: 'variable',
        text: 'inverted_section',
        textWithDelimiters: '{{/inverted_section}}',
        cursor: 28,
      },
    ]);
  });

  it('should return the right segments for unknown tokens', () => {
    const template2 = '{{?unknown}}Unknown token';
    const parsed = mustache.parse(template2);
    const segments: Segment[] = parsedMustacheTemplateToNodes(
      parsed,
      template2,
      true
    );
    expect(segments).toEqual([
      {
        type: 'variable',
        text: '?unknown',
        textWithDelimiters: '{{?unknown}}',
        cursor: 0,
      },
      {
        type: 'literal',
        text: 'Unknown token',
        textWithDelimiters: 'Unknown token',
        cursor: 12,
      },
    ]);
  });
});

describe('getNodesFromTemplate', () => {
  it('should return the right segments for mustache template', () => {
    const template = '{{#section}}Section {{nested}}content{{/section}}';
    const segments: Segment[] = getNodesFromTemplate(template, 'mustache');
    expect(segments).toEqual([
      {
        type: 'variable',
        text: 'section',
        textWithDelimiters: '{{#section}}',
        cursor: 0,
      },
      {
        type: 'variable',
        text: 'section',
        textWithDelimiters: '{{/section}}',
        cursor: 37,
      },
    ]);
  });

  it('should return the right segments for mustache template without nested section', () => {
    const template = 'Hello, {{name}}!';
    const segments: Segment[] = getNodesFromTemplate(
      template,
      'mustache',
      true
    );
    expect(segments).toEqual([
      {
        type: 'literal',
        text: 'Hello, ',
        textWithDelimiters: 'Hello, ',
        cursor: 0,
      },
      {
        type: 'variable',
        text: 'name',
        textWithDelimiters: '{{name}}',
        cursor: 7,
      },
      { type: 'literal', text: '!', textWithDelimiters: '!', cursor: 15 },
    ]);
  });

  it('should return the right segments for f-string template', () => {
    const template = 'Hello, {name}!';
    const segments: Segment[] = getNodesFromTemplate(
      template,
      'f-string',
      true
    );
    expect(segments).toEqual([
      {
        type: 'literal',
        text: 'Hello, ',
        textWithDelimiters: 'Hello, ',
        cursor: 0,
      },
      {
        type: 'variable',
        text: 'name',
        textWithDelimiters: '{name}',
        cursor: 7,
      },
      { type: 'literal', text: '!', textWithDelimiters: '!', cursor: 13 },
    ]);
  });
});

describe('splitMarkdownCodeBlocks', () => {
  it('should return the right segments for markdown code blocks', () => {
    const input = `
You are responsible with querying a knowledge base for memories and documentsrelevant to a conversation between a support team and a user regarding a ticket.Use \`search_memory\` to find memories relevant to the dialogue below.

<transcript>
Human: <IssueMessage name="kendrick.tan" timestamp="2025-02-24T07:45:30.763Z" is_private="False" slack_user_id="U02K90E231V" email="<EMAIL>" slack_channel="customer-grab">Langsmith has been unsually unstable recently especially since we upgraded to 0.9, but we do not see anything out of sorts in our infra, Occasionally we see the redis being full. How should we go about identifying the root cause and stabilizing it.

cc: <@S011EAWRY87></IssueMessage>
AI: [{'id': 'toolu_01TsYG7DXtmAkLJqYzGaEWXm', 'input': {'response': "Hello Kendrick,\n\nI understand you're experiencing instability with LangSmith after upgrading to version 0.9, particularly with Redis filling up. Here are some steps to diagnose and potentially resolve the issue:\n\n1. Check Redis configuration:\n   - Ensure Redis is set up for in-memory storage only, not disk persistence.\n   - Run \`redis-cli --bigkeys\` and \`redis-cli MEMORY STATS\` to gather more information.\n\n2. Adjust Redis settings:\n   - Modify \`REDIS_RUNS_EXPIRY_SECONDS\` in your backend and queue services' configuration.\n   - Monitor Redis queue sizes and adjust TTLs based on your processing SLAs.\n\n3. Upgrade considerations:\n   - If you haven't already, consider upgrading to version 0.9.1, which includes fixes for some known issues.\n   - Ensure the 'langsmith-ace-backend' service is included in your deployment.\n\n4. Temporary fixes:\n   - Set 'LANGSMITH_TRACING=true' as a temporary workaround if issues persist.\n\nIf problems continue, consider switching to a fully managed Redis solution to avoid persistence-related issues. Let me know if you need any clarification on these steps.", 'note_to_support': "This response addresses the user's concerns about LangSmith instability after upgrading to version 0.9, focusing on Redis-related issues. It provides specific troubleshooting steps, configuration adjustments, and upgrade recommendations based on the information from our knowledge base. The response is concise and actionable, suitable for a Slack thread."}, 'name': 'createResponseDraft', 'type': 'tool_use'}]
Tool: 200 OK
AI: <PrivateIssueMessage name="Carl-bot)" timestamp="2025-02-24T07:49:43.494Z" is_private="True" slack_user_id="U07VC04F8SV">*Name:* Kendrick Tan | *Email:* <mailto:<EMAIL>|<EMAIL>> | *Paying Customer:* No  *Personal*  Response draft:  Hello Kendrick,  I understand you're experiencing instability with LangSmith after upgrading to version 0.9, particularly with Redis filling up. Here are some steps to diagnose and potentially resolve the issue:  1. Check Redis configuration:    - Ensure Redis is set up for in-memory storage only, not disk persistence.    - Run \`redis-cli --bigkeys\` and \`redis-cli MEMORY STATS\` to gather more information.  2. Adjust Redis settings:    - Modify \`REDIS_RUNS_EXPIRY_SECONDS\` in your backend and queue services' configuration.    - Monitor Redis queue sizes and adjust TTLs based on your processing SLAs.  3. Upgrade considerations:    - If you haven't already, consider upgrading to version 0.9.1, which includes fixes for some known issues.    - Ensure the 'langsmith-ace-backend' service is included in your deployment.  4. Temporary fixes:    - Set 'LANGSMITH_TRACING=true' as a temporary workaround if issues persist.  If problems continue, consider switching to a fully managed Redis solution to avoid persistence-related issues. Let me know if you need any clarification on these steps.  *<https://smith.langchain.com/prompts/customer-support-response/playground?organizationId=ebbaf2eb-769b-4505-aca2-d11de10372a4|:teacher: Edit prompt to fix future mistakes>*  Score this response:  *<https://smith.langchain.com/o/ebbaf2eb-769b-4505-aca2-d11de10372a4/projects/p/08fc5c9f-78ba-431d-ad95-a5823539ece1/r/1eff283a-8a0e-6b34-a7a9-68f9838e51a4?poll=true|:spider_web: See trace>*</PrivateIssueMessage>
Human: <IssueMessage name="will.ho" timestamp="2025-02-24T08:49:45.283Z" is_private="False" slack_user_id="WS8N18JDD" email="<EMAIL>" slack_channel="customer-grab">A few issues we’ve seen:
• Users not able to see traces, but platform components are all up.
• Redis disk bloat, not entirely clear why, but we’ve bumped it 64GiB which is 2x memory request
• Repeated error logs on platform-backend on bad s3 region
\`\`\`2025-02-24T08:34:58.10439677Z WARN Failed to download object service: "platform-backend" trace_id: "c05ff0fba30e0723febb87d68ab0f236" span_id: "658d508cd61a00099a3047f616cfc710" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2Fb0e88da8-9d25-42a7-9c2e-75e20ff7ba36%2Ff2696abc-a89a-421a-9081-02cc971b787f>" method: "GET" path: "/internal/download" remoteIP: "*************:39464" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-fft97/2xIMyzD1Yg-045880" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} err: operation error S3: GetObject, resolve auth scheme: resolve endpoint: endpoint rule error, Invalid region: region was not a valid DNS name. 
2025-02-24T08:34:58.104466851Z ERROR Response: 500 Server Error service: "platform-backend" trace_id: "c05ff0fba30e0723febb87d68ab0f236" span_id: "658d508cd61a00099a3047f616cfc710" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2Fb0e88da8-9d25-42a7-9c2e-75e20ff7ba36%2Ff2696abc-a89a-421a-9081-02cc971b787f>" method: "GET" path: "/internal/download" remoteIP: "*************:39464" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-fft97/2xIMyzD1Yg-045880" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} httpResponse: {status: 500 bytes: 40 elapsed: 0.437724}\`\`\`</IssueMessage>
AI: [{'id': 'toolu_01855SzBt4MWsyafPf1nhmDk', 'input': {'summary': 'LangSmith 0.9 instability issues after upgrade, including Redis disk bloat and S3 region errors', 'questions': ['Are there official release notes or documentation for LangSmith version 0.9?', 'What are the known issues or changes in LangSmith 0.9 compared to 0.8, particularly regarding Redis storage, S3 configuration, and trace visibility?', 'What are the recommended troubleshooting steps for Redis disk bloat and S3 region errors in LangSmith 0.9?'], 'response': "Hello Will and Kendrick,\n\nThank you for providing more details about the issues you're experiencing with LangSmith 0.9. We're investigating the specific problems related to Redis disk bloat, S3 region errors, and trace visibility in this version.\n\nIn the meantime, please try the following:\n\n1. For Redis disk bloat:\n   - Review your Redis configuration and ensure it's set for in-memory storage only.\n   - Monitor Redis queue sizes and adjust TTLs if necessary.\n\n2. For S3 region errors:\n   - Double-check your S3 region configuration in the LangSmith settings.\n   - Ensure the region specified is valid and matches your S3 bucket's location.\n\n3. For trace visibility:\n   - Verify that all components, including the 'langsmith-ace-backend' service, are up-to-date and running.\n   - Try setting 'LANGSMITH_TRACING=true' as a temporary workaround.\n\nWe're escalating this to our engineering team for further investigation. We'll get back to you with more specific guidance once we have additional information about LangSmith 0.9 and its potential issues.\n\nIs there any other information you can provide about when these issues started occurring or any recent changes to your configuration?"}, 'name': 'requestAssistance', 'type': 'tool_use'}]
Tool: 200 OK
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-24T11:17:54.115Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi all couple of questions:
1. When you say users are unable to see traces, are all network requests succeeding? Or are they seeing them after a delay?
2. The in-cluster Redis started using persistence as of v7 to prevent outages which is why the disk might be filling up every once in a while. It's also possible there may be more job errors in which case things may be held in redis for longer. Do you see any errors in the queue logs?
3. What provider are you using? Is this s3? I'm wondering if this is a hidden error as we've made a bunch of changes to blob. There is also a min size for putting things into blob storage so its possible that this is the reason for the flakiness
4. We've moved more endpoints to using the "platform-backend" service. What are the scale settings for the platform-backend?

Can read more about the infrastructure/potentially breaking changes for things here: <https://docs.smith.langchain.com/self_hosting/release_notes#week-of-august-26-2024---langsmith-v07>

Let us know if it would be helpful to hop on a call here as well!</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-25T09:35:43.881Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi all wanted to follow up and see if this issue was resolved/if you had any additional questions? </IssueMessage>
Human: <IssueMessage name="will.ho" timestamp="2025-02-26T04:13:03.732Z" is_private="False" slack_user_id="WS8N18JDD" email="<EMAIL>" slack_channel="customer-grab">Hi Mukil, will need to take some time to look into your response get back as the entire team is out of the country.

More critically, in case it is some misconfiguration on our side, can you post several potential areas that we can look at to solve the platform-backend Unauthorized issue?
I’ll explore them async.
• We’re using S3, IAM credentials provided to the Pod via IRSA.
• Platform backend scale settings are default
    • Min 3, Max 10, Current 3</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-26T12:48:24.531Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi Will,

Could you check if the platform-backend pods have these variables set (with region replaced by your region):

\`\`\`      AWS_STS_REGIONAL_ENDPOINTS:              regional
      AWS_DEFAULT_REGION:                      us-west-2
      AWS_REGION:                              us-west-2
      AWS_ROLE_ARN:                            &lt;role&gt;
      AWS_WEB_IDENTITY_TOKEN_FILE:             /var/run/secrets/eks.amazonaws.com/serviceaccount/token\`\`\`
I would generally expect these to be auto injected but some IRSA configurations do not do this (in which case you might have to set this manually). Might also be helpful to set <https://github.com/langchain-ai/helm/blob/main/charts/langsmith/values.yaml#L122> to something like 0kb to verify this since likely you don't see this on every trace right now due to size requirements.

I do not think the sizing of the platform backend settings is the problem for now at least but may be helpful to have more minimum replicas here. Are you seeing resource consumption issues on this service?</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-27T10:12:44.108Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi Will, wanted to follow up to see if you were able to try out any of the above?</IssueMessage>
Human: <IssueMessage name="kendrick.tan" timestamp="2025-03-05T00:52:11.270Z" is_private="False" slack_user_id="U02K90E231V" email="<EMAIL>" slack_channel="customer-grab">Will was checking our logs, is this something we should be concern about? The portal is still working fine but we are seeing these logs

\`\`\`2025-03-05T00:49:48.288485119Z ERROR Response: 500 Server Error service: "platform-backend" trace_id: "934f8387357913d1e6e5c09a5def6388" span_id: "3bf869b649ed5aea6d2a62fd405b024d" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2F2725ead2-ee26-4508-a1f0-361c6e4f6a0c%2F083fb23a-dcb3-49da-a4cf-df8ed3601dc0>" method: "GET" path: "/internal/download" remoteIP: "*************:50892" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-2lk5g/cKWKigfij6-5738151" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} httpResponse: {status: 500 bytes: 40 elapsed: 0.540069} 
2025-03-05T00:49:48.389255394Z WARN Failed to download object service: "platform-backend" trace_id: "1e5055fdf1ab647e49272e44ff115095" span_id: "25e28731a8f05b7ac932449c4b23e611" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2F5bbf2e2b-b8d1-4290-b2d6-fe6c06ed24bf%2F29eb8a62-0e4d-4260-a49c-6dc11ae63721>" method: "GET" path: "/internal/download" remoteIP: "*************:50892" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-2lk5g/cKWKigfij6-5738152" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} err: operation error S3: GetObject, resolve auth scheme: resolve endpoint: endpoint rule error, Invalid region: region was not a valid DNS name. 
2025-03-05T00:49:48.389329165Z\`\`\`</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-03-05T00:52:31.465Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">I think this is probably problematic, but you would only see it on a smaller percentage of traces as it only affects the ones over the minimumKB to be stored in blob</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-03-05T00:53:06.824Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">I suspect IRSA hasn't properly set all the variables? If you describe the pod what env vars do you see set? If you bump to the latest v9 release it should also block the pod from starting up if blob storage is configured incorrectly.</IssueMessage>
AI: [{'id': 'toolu_01UdGnQzqwb5YmgnJJugXH7Z', 'input': {'response': "Hi Kendrick and Will,\n\nBased on the logs you've shared, it appears there's an S3 region configuration issue with your LangSmith platform-backend. Here are some steps to resolve this:\n\n1. Verify platform-backend environment variables:\n   Set AWS_DEFAULT_REGION and AWS_REGION to match your S3 bucket's region.\n\n2. Check IRSA configuration:\n   Ensure the platform-backend service account has the correct annotations for AWS role assumption.\n\n3. Review S3 bucket settings:\n   Confirm the bucket region matches the environment variables.\n\n4. Update Helm chart:\n   Set BLOB_STORAGE_API_URL to the correct S3 endpoint for your region.\n\n5. Verify IAM permissions:\n   Ensure the assumed role has necessary S3 access permissions.\n\nCan you check these and let us know if the issue persists? If so, we may need to dive deeper into your configuration.", 'note_to_support': "This response addresses the S3 region configuration issues reported by the user, focusing on the platform-backend service. It provides specific steps to check and correct the configuration, which should help resolve the errors they're seeing in their logs."}, 'name': 'createResponseDraft', 'type': 'tool_use'}]
Tool: 200 OK
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-03-10T21:44:54.061Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi Will, Kendrick wanted to follow up to see if you were able to fix this? Also if you wanted to schedule some time to upgrade?</IssueMessage>
Human: <IssueMessage name="kendrick.tan" timestamp="2025-03-13T00:55:43.728Z" is_private="False" slack_user_id="U02K90E231V" email="<EMAIL>" slack_channel="customer-grab">hey sorry, we have been swarmed the past few days. Will let you know once we have scheduled the upgrade.</IssueMessage>
</transcript>

\`\`\`ts
const x = 1;
\`\`\`
`;
    const blocks = splitMarkdownCodeBlocks(input);
    expect(blocks).toEqual([
      `
You are responsible with querying a knowledge base for memories and documentsrelevant to a conversation between a support team and a user regarding a ticket.Use \`search_memory\` to find memories relevant to the dialogue below.

<transcript>
Human: <IssueMessage name="kendrick.tan" timestamp="2025-02-24T07:45:30.763Z" is_private="False" slack_user_id="U02K90E231V" email="<EMAIL>" slack_channel="customer-grab">Langsmith has been unsually unstable recently especially since we upgraded to 0.9, but we do not see anything out of sorts in our infra, Occasionally we see the redis being full. How should we go about identifying the root cause and stabilizing it.

cc: <@S011EAWRY87></IssueMessage>
AI: [{'id': 'toolu_01TsYG7DXtmAkLJqYzGaEWXm', 'input': {'response': "Hello Kendrick,\n\nI understand you're experiencing instability with LangSmith after upgrading to version 0.9, particularly with Redis filling up. Here are some steps to diagnose and potentially resolve the issue:\n\n1. Check Redis configuration:\n   - Ensure Redis is set up for in-memory storage only, not disk persistence.\n   - Run \`redis-cli --bigkeys\` and \`redis-cli MEMORY STATS\` to gather more information.\n\n2. Adjust Redis settings:\n   - Modify \`REDIS_RUNS_EXPIRY_SECONDS\` in your backend and queue services' configuration.\n   - Monitor Redis queue sizes and adjust TTLs based on your processing SLAs.\n\n3. Upgrade considerations:\n   - If you haven't already, consider upgrading to version 0.9.1, which includes fixes for some known issues.\n   - Ensure the 'langsmith-ace-backend' service is included in your deployment.\n\n4. Temporary fixes:\n   - Set 'LANGSMITH_TRACING=true' as a temporary workaround if issues persist.\n\nIf problems continue, consider switching to a fully managed Redis solution to avoid persistence-related issues. Let me know if you need any clarification on these steps.", 'note_to_support': "This response addresses the user's concerns about LangSmith instability after upgrading to version 0.9, focusing on Redis-related issues. It provides specific troubleshooting steps, configuration adjustments, and upgrade recommendations based on the information from our knowledge base. The response is concise and actionable, suitable for a Slack thread."}, 'name': 'createResponseDraft', 'type': 'tool_use'}]
Tool: 200 OK
AI: <PrivateIssueMessage name="Carl-bot)" timestamp="2025-02-24T07:49:43.494Z" is_private="True" slack_user_id="U07VC04F8SV">*Name:* Kendrick Tan | *Email:* <mailto:<EMAIL>|<EMAIL>> | *Paying Customer:* No  *Personal*  Response draft:  Hello Kendrick,  I understand you're experiencing instability with LangSmith after upgrading to version 0.9, particularly with Redis filling up. Here are some steps to diagnose and potentially resolve the issue:  1. Check Redis configuration:    - Ensure Redis is set up for in-memory storage only, not disk persistence.    - Run \`redis-cli --bigkeys\` and \`redis-cli MEMORY STATS\` to gather more information.  2. Adjust Redis settings:    - Modify \`REDIS_RUNS_EXPIRY_SECONDS\` in your backend and queue services' configuration.    - Monitor Redis queue sizes and adjust TTLs based on your processing SLAs.  3. Upgrade considerations:    - If you haven't already, consider upgrading to version 0.9.1, which includes fixes for some known issues.    - Ensure the 'langsmith-ace-backend' service is included in your deployment.  4. Temporary fixes:    - Set 'LANGSMITH_TRACING=true' as a temporary workaround if issues persist.  If problems continue, consider switching to a fully managed Redis solution to avoid persistence-related issues. Let me know if you need any clarification on these steps.  *<https://smith.langchain.com/prompts/customer-support-response/playground?organizationId=ebbaf2eb-769b-4505-aca2-d11de10372a4|:teacher: Edit prompt to fix future mistakes>*  Score this response:  *<https://smith.langchain.com/o/ebbaf2eb-769b-4505-aca2-d11de10372a4/projects/p/08fc5c9f-78ba-431d-ad95-a5823539ece1/r/1eff283a-8a0e-6b34-a7a9-68f9838e51a4?poll=true|:spider_web: See trace>*</PrivateIssueMessage>
Human: <IssueMessage name="will.ho" timestamp="2025-02-24T08:49:45.283Z" is_private="False" slack_user_id="WS8N18JDD" email="<EMAIL>" slack_channel="customer-grab">A few issues we’ve seen:
• Users not able to see traces, but platform components are all up.
• Redis disk bloat, not entirely clear why, but we’ve bumped it 64GiB which is 2x memory request
• Repeated error logs on platform-backend on bad s3 region
\`\`\`2025-02-24T08:34:58.10439677Z WARN Failed to download object service: "platform-backend" trace_id: "c05ff0fba30e0723febb87d68ab0f236" span_id: "658d508cd61a00099a3047f616cfc710" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2Fb0e88da8-9d25-42a7-9c2e-75e20ff7ba36%2Ff2696abc-a89a-421a-9081-02cc971b787f>" method: "GET" path: "/internal/download" remoteIP: "*************:39464" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-fft97/2xIMyzD1Yg-045880" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} err: operation error S3: GetObject, resolve auth scheme: resolve endpoint: endpoint rule error, Invalid region: region was not a valid DNS name. 
2025-02-24T08:34:58.104466851Z ERROR Response: 500 Server Error service: "platform-backend" trace_id: "c05ff0fba30e0723febb87d68ab0f236" span_id: "658d508cd61a00099a3047f616cfc710" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2Fb0e88da8-9d25-42a7-9c2e-75e20ff7ba36%2Ff2696abc-a89a-421a-9081-02cc971b787f>" method: "GET" path: "/internal/download" remoteIP: "*************:39464" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-fft97/2xIMyzD1Yg-045880" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} httpResponse: {status: 500 bytes: 40 elapsed: 0.437724}\`\`\`</IssueMessage>
AI: [{'id': 'toolu_01855SzBt4MWsyafPf1nhmDk', 'input': {'summary': 'LangSmith 0.9 instability issues after upgrade, including Redis disk bloat and S3 region errors', 'questions': ['Are there official release notes or documentation for LangSmith version 0.9?', 'What are the known issues or changes in LangSmith 0.9 compared to 0.8, particularly regarding Redis storage, S3 configuration, and trace visibility?', 'What are the recommended troubleshooting steps for Redis disk bloat and S3 region errors in LangSmith 0.9?'], 'response': "Hello Will and Kendrick,\n\nThank you for providing more details about the issues you're experiencing with LangSmith 0.9. We're investigating the specific problems related to Redis disk bloat, S3 region errors, and trace visibility in this version.\n\nIn the meantime, please try the following:\n\n1. For Redis disk bloat:\n   - Review your Redis configuration and ensure it's set for in-memory storage only.\n   - Monitor Redis queue sizes and adjust TTLs if necessary.\n\n2. For S3 region errors:\n   - Double-check your S3 region configuration in the LangSmith settings.\n   - Ensure the region specified is valid and matches your S3 bucket's location.\n\n3. For trace visibility:\n   - Verify that all components, including the 'langsmith-ace-backend' service, are up-to-date and running.\n   - Try setting 'LANGSMITH_TRACING=true' as a temporary workaround.\n\nWe're escalating this to our engineering team for further investigation. We'll get back to you with more specific guidance once we have additional information about LangSmith 0.9 and its potential issues.\n\nIs there any other information you can provide about when these issues started occurring or any recent changes to your configuration?"}, 'name': 'requestAssistance', 'type': 'tool_use'}]
Tool: 200 OK
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-24T11:17:54.115Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi all couple of questions:
1. When you say users are unable to see traces, are all network requests succeeding? Or are they seeing them after a delay?
2. The in-cluster Redis started using persistence as of v7 to prevent outages which is why the disk might be filling up every once in a while. It's also possible there may be more job errors in which case things may be held in redis for longer. Do you see any errors in the queue logs?
3. What provider are you using? Is this s3? I'm wondering if this is a hidden error as we've made a bunch of changes to blob. There is also a min size for putting things into blob storage so its possible that this is the reason for the flakiness
4. We've moved more endpoints to using the "platform-backend" service. What are the scale settings for the platform-backend?

Can read more about the infrastructure/potentially breaking changes for things here: <https://docs.smith.langchain.com/self_hosting/release_notes#week-of-august-26-2024---langsmith-v07>

Let us know if it would be helpful to hop on a call here as well!</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-25T09:35:43.881Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi all wanted to follow up and see if this issue was resolved/if you had any additional questions? </IssueMessage>
Human: <IssueMessage name="will.ho" timestamp="2025-02-26T04:13:03.732Z" is_private="False" slack_user_id="WS8N18JDD" email="<EMAIL>" slack_channel="customer-grab">Hi Mukil, will need to take some time to look into your response get back as the entire team is out of the country.

More critically, in case it is some misconfiguration on our side, can you post several potential areas that we can look at to solve the platform-backend Unauthorized issue?
I’ll explore them async.
• We’re using S3, IAM credentials provided to the Pod via IRSA.
• Platform backend scale settings are default
    • Min 3, Max 10, Current 3</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-26T12:48:24.531Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi Will,

Could you check if the platform-backend pods have these variables set (with region replaced by your region):

\`\`\`      AWS_STS_REGIONAL_ENDPOINTS:              regional
      AWS_DEFAULT_REGION:                      us-west-2
      AWS_REGION:                              us-west-2
      AWS_ROLE_ARN:                            &lt;role&gt;
      AWS_WEB_IDENTITY_TOKEN_FILE:             /var/run/secrets/eks.amazonaws.com/serviceaccount/token\`\`\`
I would generally expect these to be auto injected but some IRSA configurations do not do this (in which case you might have to set this manually). Might also be helpful to set <https://github.com/langchain-ai/helm/blob/main/charts/langsmith/values.yaml#L122> to something like 0kb to verify this since likely you don't see this on every trace right now due to size requirements.

I do not think the sizing of the platform backend settings is the problem for now at least but may be helpful to have more minimum replicas here. Are you seeing resource consumption issues on this service?</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-02-27T10:12:44.108Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi Will, wanted to follow up to see if you were able to try out any of the above?</IssueMessage>
Human: <IssueMessage name="kendrick.tan" timestamp="2025-03-05T00:52:11.270Z" is_private="False" slack_user_id="U02K90E231V" email="<EMAIL>" slack_channel="customer-grab">Will was checking our logs, is this something we should be concern about? The portal is still working fine but we are seeing these logs

\`\`\`2025-03-05T00:49:48.288485119Z ERROR Response: 500 Server Error service: "platform-backend" trace_id: "934f8387357913d1e6e5c09a5def6388" span_id: "3bf869b649ed5aea6d2a62fd405b024d" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2F2725ead2-ee26-4508-a1f0-361c6e4f6a0c%2F083fb23a-dcb3-49da-a4cf-df8ed3601dc0>" method: "GET" path: "/internal/download" remoteIP: "*************:50892" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-2lk5g/cKWKigfij6-5738151" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} httpResponse: {status: 500 bytes: 40 elapsed: 0.540069} 
2025-03-05T00:49:48.389255394Z WARN Failed to download object service: "platform-backend" trace_id: "1e5055fdf1ab647e49272e44ff115095" span_id: "25e28731a8f05b7ac932449c4b23e611" httpRequest: {url: "<http://langsmith-platform-backend:1986/internal/download?path=ttl_l%2Finputs%2Fd9686daff9ccfb70c0e503e6f2f32b92fc04fdfb6531d1d2c232af93cf34fac5%2F4a6ba7971f5ac276992664c331ae78126db6a9672b817aba0159e3e907ec7e59%2F5bbf2e2b-b8d1-4290-b2d6-fe6c06ed24bf%2F29eb8a62-0e4d-4260-a49c-6dc11ae63721>" method: "GET" path: "/internal/download" remoteIP: "*************:50892" proto: "HTTP/1.1" requestID: "langsmith-platform-backend-866cfc9bcd-2lk5g/cKWKigfij6-5738152" scheme: "http" header: {user-agent: "Mozilla/5.0 (compatible; pycurl)" accept: "*/*" accept-encoding: "gzip,deflate" x-service-key: "***"}} err: operation error S3: GetObject, resolve auth scheme: resolve endpoint: endpoint rule error, Invalid region: region was not a valid DNS name. 
2025-03-05T00:49:48.389329165Z\`\`\`</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-03-05T00:52:31.465Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">I think this is probably problematic, but you would only see it on a smaller percentage of traces as it only affects the ones over the minimumKB to be stored in blob</IssueMessage>
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-03-05T00:53:06.824Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">I suspect IRSA hasn't properly set all the variables? If you describe the pod what env vars do you see set? If you bump to the latest v9 release it should also block the pod from starting up if blob storage is configured incorrectly.</IssueMessage>
AI: [{'id': 'toolu_01UdGnQzqwb5YmgnJJugXH7Z', 'input': {'response': "Hi Kendrick and Will,\n\nBased on the logs you've shared, it appears there's an S3 region configuration issue with your LangSmith platform-backend. Here are some steps to resolve this:\n\n1. Verify platform-backend environment variables:\n   Set AWS_DEFAULT_REGION and AWS_REGION to match your S3 bucket's region.\n\n2. Check IRSA configuration:\n   Ensure the platform-backend service account has the correct annotations for AWS role assumption.\n\n3. Review S3 bucket settings:\n   Confirm the bucket region matches the environment variables.\n\n4. Update Helm chart:\n   Set BLOB_STORAGE_API_URL to the correct S3 endpoint for your region.\n\n5. Verify IAM permissions:\n   Ensure the assumed role has necessary S3 access permissions.\n\nCan you check these and let us know if the issue persists? If so, we may need to dive deeper into your configuration.", 'note_to_support': "This response addresses the S3 region configuration issues reported by the user, focusing on the platform-backend service. It provides specific steps to check and correct the configuration, which should help resolve the errors they're seeing in their logs."}, 'name': 'createResponseDraft', 'type': 'tool_use'}]
Tool: 200 OK
Human: <IssueMessage name="Mukil Loganathan" timestamp="2025-03-10T21:44:54.061Z" is_private="False" slack_user_id="U05SD6TR4TA" email="<EMAIL>" slack_channel="customer-grab">Hi Will, Kendrick wanted to follow up to see if you were able to fix this? Also if you wanted to schedule some time to upgrade?</IssueMessage>
Human: <IssueMessage name="kendrick.tan" timestamp="2025-03-13T00:55:43.728Z" is_private="False" slack_user_id="U02K90E231V" email="<EMAIL>" slack_channel="customer-grab">hey sorry, we have been swarmed the past few days. Will let you know once we have scheduled the upgrade.</IssueMessage>
</transcript>

`,
      `\`\`\`ts
const x = 1;
\`\`\``,
      `
`,
    ]);
  });
});

describe('isBase64', () => {
  it('should return false for non-base64 urls', () => {
    expect(isBase64('https://example.com/image.png')).toBe(false);
  });

  it('should return false for mustache template variables', () => {
    expect(isBase64('{{input}}')).toBe(false);
  });

  it('should return false for f-string template variables', () => {
    expect(isBase64('{input}')).toBe(false);
  });

  it('should return true for base64 urls with a prefix', () => {
    expect(
      isBase64(
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII='
      )
    ).toBe(true);
  });

  it('should return true for base64 urls without a prefix', () => {
    expect(
      isBase64(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII='
      )
    ).toBe(true);
  });
});

describe('parseAudioSource', () => {
  it('should parse double-braced template variables with file extension', () => {
    const result = parseAudioSource('{{myAudio.mp3}}');
    expect(result).toEqual({
      variableName: '{{myAudio}}',
      fileType: 'mp3',
    });
  });

  it('should parse single-braced template variables with file extension', () => {
    const result = parseAudioSource('{myAudio.wav}');
    expect(result).toEqual({
      variableName: '{myAudio}',
      fileType: 'wav',
    });
  });

  it('should parse double-braced template variables without file extension', () => {
    const result = parseAudioSource('{{myAudio}}');
    expect(result).toEqual({
      variableName: '{{myAudio}}',
      fileType: undefined,
    });
  });

  it('should parse single-braced template variables without file extension', () => {
    const result = parseAudioSource('{myAudio}');
    expect(result).toEqual({
      variableName: '{myAudio}',
      fileType: undefined,
    });
  });

  it('should handle paths without audio extensions', () => {
    const result = parseAudioSource('base64audiostring');
    expect(result).toEqual({
      variableName: 'base64audiostring',
      fileType: undefined,
    });
  });

  it('should handle empty strings', () => {
    const result = parseAudioSource('');
    expect(result).toEqual({
      variableName: '',
      fileType: undefined,
    });
  });
});

describe('convertMessageToJSONContent', () => {
  it('should convert a string message to JSON content', () => {
    const result = convertMessageToJSONContent('Hello, world!');
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'Hello, world!' }],
        },
      ],
    });
  });

  it('should handle null input', () => {
    const result = convertMessageToJSONContent(null);
    expect(result).toEqual({
      type: 'doc',
      content: [],
    });
  });

  it('should handle undefined input', () => {
    const result = convertMessageToJSONContent(undefined);
    expect(result).toEqual({
      type: 'doc',
      content: [],
    });
  });

  it('should handle text content part', () => {
    const result = convertMessageToJSONContent([
      { type: 'text', text: 'Hello, world!' },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'Hello, world!' }],
        },
      ],
    });
  });

  it('should handle image_url content part with string URL', () => {
    const result = convertMessageToJSONContent([
      { type: 'image_url', image_url: 'https://example.com/image.jpg' },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        { type: 'image', attrs: { src: 'https://example.com/image.jpg' } },
      ],
    });
  });

  it('should handle image_url content part with object URL', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'image_url',
        image_url: { url: 'https://example.com/image.jpg' },
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        { type: 'image', attrs: { src: 'https://example.com/image.jpg' } },
      ],
    });
  });

  it('should handle image_url content part with presigned URL', () => {
    const attachments = {
      'https://example.com/image.jpg': {
        presigned_url: 'https://presigned.example.com/image.jpg',
        storage_url: 'https://storage.example.com/image.jpg',
      },
    };
    const result = convertMessageToJSONContent(
      [{ type: 'image_url', image_url: 'https://example.com/image.jpg' }],
      attachments
    );
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'image',
          attrs: {
            src: 'https://example.com/image.jpg',
            'data-s3-presigned': 'https://presigned.example.com/image.jpg',
          },
        },
      ],
    });
  });

  it('should handle file content part', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'file',
        source_type: 'url',
        url: 'https://example.com/file.pdf',
        mime_type: 'application/pdf',
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'file',
          attrs: {
            src: 'https://example.com/file.pdf',
            type: 'application/pdf',
          },
        },
      ],
    });
  });

  it('should handle file content part with base64 data', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'file',
        source_type: 'base64',
        data: 'data:application/pdf;base64,ABC123',
        mime_type: 'application/pdf',
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'file',
          attrs: {
            src: 'data:application/pdf;base64,ABC123',
            type: 'application/pdf',
          },
        },
      ],
    });
  });

  it('should handle audio content part', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'input_audio',
        input_audio: {
          data: 'ABC123',
          format: 'mp3',
        },
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'audio',
          attrs: { src: 'data:audio/mp3;base64,ABC123', format: 'mp3' },
        },
      ],
    });
  });

  it('should handle audio content part with template variable', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'input_audio',
        input_audio: {
          data: '{{myAudio}}',
          format: 'mp3',
        },
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        { type: 'audio', attrs: { src: '{{myAudio}}', format: 'mp3' } },
      ],
    });
  });

  it('should handle multiple content parts', () => {
    const result = convertMessageToJSONContent([
      { type: 'text', text: 'Hello, ' },
      { type: 'text', text: 'world!' },
      { type: 'image_url', image_url: 'https://example.com/image.jpg' },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        { type: 'paragraph', content: [{ type: 'text', text: 'Hello, ' }] },
        { type: 'paragraph', content: [{ type: 'text', text: 'world!' }] },
        { type: 'image', attrs: { src: 'https://example.com/image.jpg' } },
      ],
    });
  });

  it('should handle LangChain multimodal content with both data and URL', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'file',
        source_type: 'base64',
        data: 'data:image/png;base64,ABC123',
        url: 'https://example.com/image.png',
        mime_type: 'image/png',
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'file',
          attrs: { src: 'data:image/png;base64,ABC123', type: 'image/png' },
        },
      ],
    });
  });

  it('should handle LangChain multimodal content with only URL', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'file',
        source_type: 'url',
        url: 'https://example.com/image.png',
        mime_type: 'image/png',
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'file',
          attrs: { src: 'https://example.com/image.png', type: 'image/png' },
        },
      ],
    });
  });

  it('should handle LangChain multimodal content with only data', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'file',
        source_type: 'base64',
        data: 'data:image/png;base64,ABC123',
        mime_type: 'image/png',
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'file',
          attrs: { src: 'data:image/png;base64,ABC123', type: 'image/png' },
        },
      ],
    });
  });

  it('should handle OpenAI file content with different file types', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'file',
        file: {
          filename: 'document.pdf',
          file_data: 'data:application/pdf;base64,ABC123',
        },
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'file',
          attrs: {
            src: 'data:application/pdf;base64,ABC123',
            type: 'application/pdf',
            title: 'document.pdf',
          },
        },
      ],
    });
  });

  it('should handle OpenAI audio content with different formats', () => {
    const result = convertMessageToJSONContent([
      {
        type: 'input_audio',
        input_audio: {
          data: 'ABC123',
          format: 'mp3',
        },
      },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        {
          type: 'audio',
          attrs: { src: 'data:audio/mp3;base64,ABC123', format: 'mp3' },
        },
      ],
    });
  });

  it('should handle multiple content parts with mixed types', () => {
    const result = convertMessageToJSONContent([
      { type: 'text', text: 'Hello, ' },
      { type: 'image_url', image_url: 'https://example.com/image.jpg' },
      {
        type: 'file',
        source_type: 'base64',
        data: 'data:image/png;base64,ABC123',
        mime_type: 'image/png',
      },
      {
        type: 'file',
        file: {
          filename: 'document.pdf',
          file_data: 'data:application/pdf;base64,ABC123',
        },
      },
      { type: 'input_audio', input_audio: { data: 'ABC123', format: 'mp3' } },
    ]);
    expect(result).toEqual({
      type: 'doc',
      content: [
        { type: 'paragraph', content: [{ type: 'text', text: 'Hello, ' }] },
        { type: 'image', attrs: { src: 'https://example.com/image.jpg' } },
        {
          type: 'file',
          attrs: { src: 'data:image/png;base64,ABC123', type: 'image/png' },
        },
        {
          type: 'file',
          attrs: {
            src: 'data:application/pdf;base64,ABC123',
            type: 'application/pdf',
            title: 'document.pdf',
          },
        },
        {
          type: 'audio',
          attrs: { src: 'data:audio/mp3;base64,ABC123', format: 'mp3' },
        },
      ],
    });
  });
});

describe('getMimeTypeFromDataUrl', () => {
  it('should return the correct mime type for a data URL', () => {
    const result = getMimeTypeFromDataUrl('data:audio/mp3;base64,ABC123');
    expect(result).toEqual('audio/mp3');
  });

  it('should return the correct mime type for a data URL with a file extension', () => {
    const result = getMimeTypeFromDataUrl('data:image/png;base64,ABC123');
    expect(result).toEqual('image/png');
  });

  it('should return the correct mime type for a data URL with a file extension', () => {
    const result = getMimeTypeFromDataUrl('data:image/png;base64,ABC123');
    expect(result).toEqual('image/png');
  });

  it('should return null for an invalid data URL', () => {
    const result = getMimeTypeFromDataUrl('invalid-data-url');
    expect(result).toEqual(null);
  });
});
