import { TemplateFormat, parseTemplate } from '@langchain/core/prompts';
import { EditorView } from '@tiptap/pm/view';
import { JSONContent } from '@tiptap/react';

import mime from 'mime/lite';
import mustache from 'mustache';

import {
  S3DataSchema,
  isMessageContentPartLangChainMultimodal,
  isMessageContentPartOpenAIAudio,
  isMessageContentPartOpenAIFile,
} from '@/types/schema';
import { MessageContentPart } from '@/types/schema';

import {
  CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE,
  DATA_PREFIX,
  SUPPORTED_MIME_TYPES,
} from '../constants';

export async function convertFileToBase64(file: File) {
  // TODO: match with the same file types as permitted by OAI
  if (
    !SUPPORTED_MIME_TYPES.some((mimeType) => file.type.startsWith(mimeType))
  ) {
    throw new Error(
      `Not a valid ${SUPPORTED_MIME_TYPES.join(', ')}: ${file.type}`
    );
  }
  return await new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result !== 'string')
        return reject('Not a Base64 response');
      resolve(reader.result);
    };

    reader.readAsDataURL(file);
  });
}

export function getDocIndexFromOffset(bounds: number[], needleIdx: number) {
  let left = 0;
  let right = bounds.length - 1;
  let offset = 0;

  while (left <= right) {
    const midIdx = left + Math.floor((right - left) / 2);
    const bound = bounds[midIdx];

    if (bound <= needleIdx) {
      offset = midIdx + 1;
      left = midIdx + 1;
    } else {
      right = midIdx - 1;
    }
  }

  return needleIdx + offset;
}

export interface Segment {
  type: 'variable' | 'literal';
  text: string;
  textWithDelimiters: string;
  cursor: number;
}

function generateFStringTemplateNodes(template: string): Segment[] {
  const parsedTemplate = parseTemplate(template, 'f-string');
  const [segments] = parsedTemplate.reduce<[Segment[], number]>(
    ([result, cursor], segment) => {
      if (segment.type === 'variable') {
        const textWithDelimiters = `{${segment.name}}`;
        result.push({
          type: 'variable',
          text: segment.name,
          textWithDelimiters,
          cursor,
        });
        return [result, cursor + textWithDelimiters.length];
      }
      // if the segment is a literal and the text is a { or }, we need to check if the next character in the template is a { or } (double curly brace case)
      if (segment.text === '{' || segment.text === '}') {
        const isOpen = segment.text === '{';
        const nextChar = template[cursor + 1];
        if (nextChar === segment.text) {
          result.push({
            type: 'literal',
            text: segment.text,
            textWithDelimiters: `${
              isOpen ? `{${segment.text}` : `${segment.text}}`
            }`,
            cursor,
          });
          return [result, cursor + 2];
        }
      }
      const item: Segment = {
        type: 'literal',
        text: segment.text,
        textWithDelimiters: segment.text,
        cursor,
      };
      result.push(item);
      return [result, cursor + item.textWithDelimiters.length];
    },
    [[], 0]
  );

  return segments;
}

export function parsedMustacheTemplateToNodes(
  parsed: mustache.TemplateSpans[],
  template: string,
  includeNested = false
): Segment[] {
  const nodes = parsed.map((span) => {
    switch (span[0]) {
      case 'name':
      case '&':
      case '>': {
        return {
          type: 'variable',
          text: span[1],
          textWithDelimiters: template.substring(span[2], span[3]),
          cursor: span[2],
        } as Segment;
      }
      case '#':
      case '^': {
        const innerNodes = parsedMustacheTemplateToNodes(span[4], template);
        return [
          {
            type: 'variable',
            text: span[1],
            textWithDelimiters: template.substring(span[2], span[3]),
            cursor: span[2],
          },
          ...(includeNested ? innerNodes : []),
          {
            type: 'variable',
            text: span[1],
            textWithDelimiters: template.substring(
              span[5],
              span[5] + span[3] - span[2]
            ),
            cursor: span[5],
          },
        ] as Segment[];
      }
      case '!':
      default: {
        return {
          type: 'literal',
          text: span[1],
          textWithDelimiters: template.substring(span[2], span[3]),
          cursor: span[2],
        } as Segment;
      }
    }
  });

  return nodes.flat();
}

function safeParseMustache(template: string): mustache.TemplateSpans[] {
  try {
    return mustache.parse(template);
  } catch {
    return [];
  }
}

export function generateMustacheTemplateNodes(
  template: string,
  includeNested = false
): Segment[] {
  const parsed = safeParseMustache(template);
  return parsedMustacheTemplateToNodes(parsed, template, includeNested);
}

// this takes in a template string and template format and returns an array of segments
// where each segment is either a variable or a literal and the corresponding text
// includeNested is used to include nested variables -- section variables and others can have nested variables
// ex. template: 'Hello, {{#section}}{{name}}{{/section}}!'
// includeNested: true will include name as a variable in the output, which we only currently want for syntax highlighting
export function getNodesFromTemplate(
  template: string,
  templateFormat: TemplateFormat,
  includeNested = false
): Segment[] {
  if (templateFormat === 'mustache') {
    return generateMustacheTemplateNodes(template, includeNested);
  } else {
    return generateFStringTemplateNodes(template);
  }
}

export function getInputVarsFromTemplate(
  template: string,
  templateFormat: TemplateFormat,
  includeNested = false
): string[] {
  let nodes: Segment[] = [];
  try {
    nodes = getNodesFromTemplate(template, templateFormat, includeNested);
  } catch {
    // pass
  }
  return nodes
    .filter((i) => i.type === 'variable')
    .map((i) => {
      return i.text;
    });
}

// tip tap attempts to parse the text as html, which leads to formatting issues, so we need to get the plain text
export const handleCopyOrCut = (view: EditorView, event: ClipboardEvent) => {
  const { type } = event;
  const { state, dispatch } = view;
  const { from, to } = state.selection;
  const slice = state.doc.slice(from, to);

  const lines: string[] = [];
  slice.content.forEach((node) => {
    // Each block-level node (like a paragraph) becomes its own line
    lines.push(node.textContent);
  });

  // Join all lines with a newline character, preserving empty lines
  const text = lines.join('\n');

  event.clipboardData?.setData('text/plain', text);
  event.preventDefault();
  if (type === 'cut') {
    dispatch(state.tr.deleteSelection());
  }
  return true;
};

export const isBase64 = (url: string) => {
  return url.startsWith(DATA_PREFIX) || /^[A-Za-z0-9+/=]+$/.test(url);
};

export function getMimeTypeFromDataUrl(dataUrl: string) {
  if (!dataUrl?.startsWith(DATA_PREFIX)) {
    return null;
  }
  const mimeType = dataUrl.split(DATA_PREFIX)[1].split(';')[0];
  if (!mimeType) {
    return null;
  }
  return mimeType;
}

function convertTextToJSONContent(content: string | null | undefined) {
  if (content == null) return [];
  const lines = content.split('\n');
  return lines.map(
    (line): JSONContent => ({
      type: 'paragraph',
      content: line ? [{ type: 'text', text: line }] : [],
    })
  );
}

export function convertMessageToJSONContent(
  content: string | Array<MessageContentPart> | null | undefined,
  attachments?: Record<string, S3DataSchema>
): JSONContent {
  if (content == null) return { type: 'doc', content: [] };
  if (typeof content === 'string') {
    return {
      type: 'doc',
      content: convertTextToJSONContent(content),
    };
  }

  return {
    type: 'doc',
    content: content.flatMap((i) => {
      if (i.type === 'text' && typeof i.text === 'string') {
        return convertTextToJSONContent(i.text);
      } else if (i.type === 'image_url') {
        if (i.image_url == null) {
          return [];
        }
        const src =
          typeof i.image_url === 'string' ? i.image_url : i.image_url.url;
        const presigned = attachments?.[src]?.presigned_url;

        if (presigned) {
          return [
            {
              type: 'image',
              attrs: { src, 'data-s3-presigned': presigned },
            },
          ];
        }
        return [{ type: 'image', attrs: { src } }];
      } else if (isMessageContentPartLangChainMultimodal(i)) {
        return [
          {
            type: 'file',
            attrs: {
              src: i.data ?? i.url,
              type: i.mime_type,
            },
          },
        ];
      } else if (isMessageContentPartOpenAIFile(i)) {
        const mimeType =
          mime.getType(i.file.filename) ||
          getMimeTypeFromDataUrl(i.file.file_data);
        return [
          {
            type: 'file',
            attrs: {
              src: i.file.file_data,
              type: mimeType,
              title: i.file.filename,
            },
          },
        ];
      } else if (isMessageContentPartOpenAIAudio(i)) {
        const mimeType = `audio/${i.input_audio.format}`;
        // only add data url prefix if it isn't a template variable and doesn't already have a data url prefix
        const isBase64Url = isBase64(i.input_audio.data);
        return [
          {
            type: 'audio',
            attrs: {
              src:
                !isBase64Url || i.input_audio.data.startsWith(DATA_PREFIX)
                  ? i.input_audio.data
                  : `${CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE(mimeType)}${
                      i.input_audio.data
                    }`,
              format: i.input_audio.format,
            },
          },
        ];
      }
      return [];
    }),
  };
}

export function parseAudioSource(src: string): {
  variableName: string;
  fileType?: 'wav' | 'mp3';
} {
  let variableName = src;
  let fileType: 'wav' | 'mp3' | undefined;

  if (src.startsWith('{{') && src.endsWith('}}')) {
    // Handle double braces
    const match = src.match(/\{\{.*?\.(mp3|wav)\}\}/);
    if (match) {
      fileType = match[1] as 'wav' | 'mp3';
    }
    variableName = src.replace(/\{\{(.*?)(?:\.(mp3|wav))?\}\}/, '{{$1}}');
  } else if (src.startsWith('{') && src.endsWith('}')) {
    // Handle single braces
    const match = src.match(/\{.*?\.(mp3|wav)\}/);
    if (match) {
      fileType = match[1] as 'wav' | 'mp3';
    }
    variableName = src.replace(/\{(.*?)(?:\.(mp3|wav))?\}/, '{$1}');
  } else {
    // just a base64 audio string
    fileType = undefined;
  }

  return { variableName, fileType };
}
