import {
  File02Icon,
  Image01Icon,
  VolumeMinIcon,
} from '@langchain/untitled-ui-icons';

export const DATA_PREFIX = 'data:';
const BASE64_SUFFIX = 'base64';
export const CREATE_DATA_URL_PREFIX_FROM_MIME_TYPE = (contentType: string) =>
  `${DATA_PREFIX}${contentType};${BASE64_SUFFIX},`;

export const EXTRACT_BASE64_FROM_DATA_URL = (dataUrl: string) => {
  if (dataUrl.startsWith(DATA_PREFIX)) {
    return dataUrl.split(DATA_PREFIX)[1].split(',')[1];
  }
  return dataUrl;
};

export const EXTRACT_MIME_TYPE_FROM_DATA_URL = (dataUrl: string) => {
  if (dataUrl.startsWith(DATA_PREFIX)) {
    return dataUrl.split(DATA_PREFIX)[1].split(',')[0];
  }
  return null;
};

export const MIME_TYPES = {
  IMAGE: 'image',
  PDF: 'application/pdf',
  JSON: 'application/json',
  VIDEO: 'video',
  AUDIO: 'audio',
  HTML: 'text/html',
  TEXT: 'text/plain',
  CSV: 'text/csv',
} as const;

export type MESSAGE_TYPE = 'file' | 'image_url' | 'input_audio' | 'audio';

export type MimeType = (typeof MIME_TYPES)[keyof typeof MIME_TYPES];

// If you add a new supported mime type, also add it to
// ICON_FROM_MIME_TYPE
// LABEL_FROM_MIME_TYPE
// MIME_TYPE_TO_MESSAGE_REPRESENTATION
export const SUPPORTED_MIME_TYPES: MimeType[] = [
  MIME_TYPES.IMAGE,
  MIME_TYPES.PDF,
  MIME_TYPES.AUDIO,
];

export const SUPPORTED_SUBTYPES: Partial<Record<MimeType, string[]>> = {
  [MIME_TYPES.AUDIO]: ['wav', 'mp3'],
};

export const ICON_FROM_MIME_TYPE = {
  [MIME_TYPES.IMAGE]: Image01Icon,
  [MIME_TYPES.PDF]: File02Icon,
  [MIME_TYPES.AUDIO]: VolumeMinIcon,
} as const;

export const LABEL_FROM_MIME_TYPE = {
  [MIME_TYPES.IMAGE]: 'Image',
  [MIME_TYPES.PDF]: 'PDF',
  [MIME_TYPES.AUDIO]: 'Audio',
} as const;

export const MESSAGE_TYPE_TO_MIME_TYPE: Record<MESSAGE_TYPE, MimeType> = {
  file: MIME_TYPES.PDF,
  image_url: MIME_TYPES.IMAGE,
  input_audio: MIME_TYPES.AUDIO,
  audio: MIME_TYPES.AUDIO,
} as const;

export const MIME_TYPE_TO_FILE_EXTENSION: Record<MimeType, string> = {
  [MIME_TYPES.IMAGE]:
    SUPPORTED_SUBTYPES[MIME_TYPES.IMAGE]
      ?.map((subtype) => `image/${subtype}`)
      .join(',') || 'image/*',
  [MIME_TYPES.PDF]: 'application/pdf',
  [MIME_TYPES.JSON]: 'application/json',
  [MIME_TYPES.VIDEO]:
    SUPPORTED_SUBTYPES[MIME_TYPES.VIDEO]
      ?.map((subtype) => `video/${subtype}`)
      .join(',') || 'video/*',
  [MIME_TYPES.AUDIO]:
    SUPPORTED_SUBTYPES[MIME_TYPES.AUDIO]
      ?.map((subtype) => `audio/${subtype}`)
      .join(',') || 'audio/*',
  [MIME_TYPES.HTML]: 'text/html',
  [MIME_TYPES.TEXT]: 'text/plain',
  [MIME_TYPES.CSV]: 'text/csv',
} as const;
