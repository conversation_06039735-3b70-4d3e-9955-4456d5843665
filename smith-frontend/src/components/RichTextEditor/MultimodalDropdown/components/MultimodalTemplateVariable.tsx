import { TemplateFormat } from '@langchain/core/prompts';
import { FolderIcon, PlusIcon } from '@langchain/untitled-ui-icons';
import { But<PERSON> } from '@mui/joy';

import { useState } from 'react';

import {
  INJECT_ALL_ATTACHMENTS_DELIMETER,
  INJECT_ALL_ATTACHMENTS_NAME,
} from '@/Pages/Playground/constants';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { cn } from '@/utils/tailwind';

import {
  ICON_FROM_MIME_TYPE,
  LABEL_FROM_MIME_TYPE,
  MIME_TYPES,
  SUPPORTED_MIME_TYPES,
} from '../../constants';
import { MimeType } from '../../constants';
import { AttachmentName } from '../../types';

export function MultimodalTemplateVariable(props: {
  onSubmit: (fileType: MimeType, variableName: string) => void;
  onUploadRequest: () => void;
  attachmentNames: AttachmentName[];
  templateFormat?: TemplateFormat;
}) {
  const onSubmit = (fileType: MimeType, variableName: string) => {
    const delimiters =
      props.templateFormat === 'mustache' ? ['{{', '}}'] : ['{', '}'];
    const name =
      variableName === INJECT_ALL_ATTACHMENTS_NAME
        ? INJECT_ALL_ATTACHMENTS_DELIMETER
        : variableName;
    props.onSubmit(fileType, `${delimiters[0]}${name}${delimiters[1]}`);
  };

  const hasAttachments = props.attachmentNames.length > 0;

  return (
    <div className="flex flex-col items-stretch gap-2">
      {SUPPORTED_MIME_TYPES.map((mimeType) => (
        <MimeTypeVariableInput
          key={mimeType}
          multimodalType={mimeType}
          onSubmit={onSubmit}
          attachmentNames={props.attachmentNames.filter((a) =>
            a.mimeType.startsWith(mimeType)
          )}
        />
      ))}
      {hasAttachments && (
        <button
          type="button"
          onClick={() => onSubmit(MIME_TYPES.PDF, INJECT_ALL_ATTACHMENTS_NAME)}
          className="flex flex-row items-center justify-center gap-2 rounded-md border border-secondary px-2 py-1.5 hover:bg-primary-hover"
        >
          <FolderIcon className="h-5 w-5 font-medium text-quaternary" />
          <span className="text-sm font-medium text-secondary">
            {INJECT_ALL_ATTACHMENTS_NAME}
          </span>
        </button>
      )}
    </div>
  );
}

function AttachmentSuggestions({
  attachmentNames,
  isOpen,
  setIsOpen,
  multimodalType,
  onSelect,
  children,
  inputValue,
}: {
  attachmentNames: AttachmentName[];
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  multimodalType: MimeType;
  onSelect: (name: string) => void;
  children: React.ReactNode;
  inputValue: string;
}) {
  const filteredAttachments = attachmentNames.filter(
    (a) =>
      a.mimeType.startsWith(multimodalType) &&
      a.name.toLowerCase().includes(inputValue.toLowerCase())
  );

  if (
    !attachmentNames ||
    !attachmentNames.length ||
    !filteredAttachments.length
  ) {
    return children;
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger className="w-full">
        <div className="w-full">{children}</div>
      </PopoverTrigger>
      <PopoverContent
        className="max-w-[300px] p-0"
        align="start"
        sideOffset={5}
      >
        <div className="flex flex-col gap-2">
          {filteredAttachments.map((attachment, idx) => (
            <Button
              key={idx}
              variant="plain"
              color="neutral"
              sx={{
                width: '100%',
              }}
              onClick={() => {
                onSelect(attachment.name);
                setIsOpen(false);
              }}
            >
              <div className="w-full truncate text-start">
                {attachment.name}
              </div>
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}

function MimeTypeVariableInput({
  multimodalType,
  onSubmit,
  attachmentNames,
}: {
  multimodalType: MimeType;
  onSubmit: (fileType: MimeType, variableName: string) => void;
  attachmentNames: AttachmentName[];
}) {
  const [variableName, setVariableName] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const isAbleToAddVariable = !!variableName;

  const handleSubmit = () => {
    if (isAbleToAddVariable) {
      onSubmit(multimodalType, variableName);
    }
  };

  const handleAdd = () => {
    if (isAbleToAddVariable) {
      handleSubmit();
    } else {
      // reset the variable name
      setVariableName('');
      if (attachmentNames.length > 0) {
        setIsOpen(true);
      }
    }
  };

  const addButton = (onClick?: () => void) => (
    <button
      type="button"
      className={cn(
        'rounded-md p-2 text-primary',
        !isAbleToAddVariable && 'text-disabled',
        isAbleToAddVariable &&
          'bg-brand-primary text-button-primary hover:bg-brand-primary-hover'
      )}
      onClick={onClick}
      disabled={!isAbleToAddVariable}
    >
      <PlusIcon className="h-4 w-4" />
    </button>
  );

  const renderAddButton = () => {
    if (multimodalType === MIME_TYPES.AUDIO && isAbleToAddVariable) {
      return (
        <Popover>
          <PopoverTrigger asChild>{addButton()}</PopoverTrigger>
          <PopoverContent
            className="m-0 w-[100px] p-0 text-xs"
            side="bottom"
            align="start"
          >
            <div className="flex flex-col gap-2">
              <button
                type="button"
                className="rounded-md p-2 text-primary hover:bg-primary-hover"
                onClick={() =>
                  onSubmit(MIME_TYPES.AUDIO, `${variableName}.wav`)
                }
              >
                .wav
              </button>
              <button
                type="button"
                className="rounded-md p-2 text-primary hover:bg-primary-hover"
                onClick={() =>
                  onSubmit(MIME_TYPES.AUDIO, `${variableName}.mp3`)
                }
              >
                .mp3
              </button>
            </div>
          </PopoverContent>
        </Popover>
      );
    }
    return addButton(handleAdd);
  };

  return (
    <div
      className={cn(
        'flex flex-row items-center justify-between gap-2 rounded-md border border-secondary px-2 py-1.5'
      )}
    >
      <div className="flex w-[66px] shrink-0 flex-row items-center gap-2">
        {ICON_FROM_MIME_TYPE[multimodalType]({
          className: 'h-5 w-5 text-quaternary',
        })}
        <span className="text-xs font-medium text-secondary">
          {LABEL_FROM_MIME_TYPE[multimodalType]}
        </span>
      </div>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
        className="w-full flex-grow"
      >
        <AttachmentSuggestions
          attachmentNames={attachmentNames}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          multimodalType={multimodalType}
          onSelect={(name) => {
            setVariableName(name);
            setIsOpen(false);
          }}
          inputValue={variableName}
        >
          <input
            name={`${multimodalType}-variable-name`}
            placeholder="Enter variable name..."
            value={variableName}
            onChange={(e) => {
              setVariableName(e.target.value);
              if (!isOpen) {
                setIsOpen(true);
              }
            }}
            className="w-full border-none bg-transparent px-0 text-xs focus:outline-none"
            onClick={() => setIsOpen(true)}
          />
        </AttachmentSuggestions>
      </form>
      {renderAddButton()}
    </div>
  );
}
