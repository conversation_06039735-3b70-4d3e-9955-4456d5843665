import { PaperclipIcon } from '@langchain/untitled-ui-icons';

import { forwardRef } from 'react';

import { Tooltip } from '@/components/Tooltip/Tooltip';
import { cn } from '@/utils/tailwind';

export const MultimodalButton = forwardRef<
  HTMLButtonElement,
  { icon?: React.ReactNode; label?: string; className?: string }
>((props, ref) => {
  const { icon, label, className, ...rest } = props;
  const Icon = icon ?? PaperclipIcon;

  return (
    <Tooltip title={label ?? 'Add multimodal content'} className="w-fit">
      <button
        {...rest}
        type="button"
        className={cn(
          'flex items-center justify-center rounded-full border border-primary p-1 transition duration-75 hover:bg-secondary-hover',
          className
        )}
        ref={ref}
      >
        {typeof Icon === 'function' ? <Icon className="h-4 w-4" /> : Icon}
      </button>
    </Tooltip>
  );
});
