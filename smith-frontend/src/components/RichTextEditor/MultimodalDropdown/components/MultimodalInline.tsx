import { UploadCloud02Icon } from '@langchain/untitled-ui-icons';

import { useId, useMemo, useState } from 'react';

import {
  MIME_TYPE_TO_FILE_EXTENSION,
  MimeType,
  SUPPORTED_MIME_TYPES,
  SUPPORTED_SUBTYPES,
} from '../../constants';
import { convertFileToBase64 } from '../../utils/RichTextEditor.utils';

export function MultimodalInline(props: {
  onUpload: (type: MimeType, src: string) => void;
  mimeType?: MimeType;
}) {
  const [error, setError] = useState<string | null>(null);
  const urlId = useId();

  const acceptedMimeTypes = useMemo(() => {
    return props.mimeType
      ? MIME_TYPE_TO_FILE_EXTENSION[props.mimeType]
      : SUPPORTED_MIME_TYPES.map(
          (mimeType) => MIME_TYPE_TO_FILE_EXTENSION[mimeType]
        ).join(',');
  }, [props.mimeType]);

  return (
    <>
      <div className="group relative grid">
        <button
          type="button"
          className="col-start-1 col-end-2 flex items-center gap-4 rounded-lg border border-secondary p-4 pr-8 text-left transition-colors group-hover:bg-secondary-hover group-active:bg-secondary"
        >
          <span className="flex h-10 w-10 items-center justify-center rounded-lg border border-secondary pt-1">
            <UploadCloud02Icon className="h-5 w-5 text-primary" />
          </span>
          <span className="text-xs">
            <span className="font-semibold">Upload a file</span>{' '}
            <span className="text-tertiary">or drag and drop</span>
          </span>
        </button>
        <input
          type="file"
          className="absolute inset-0 opacity-0"
          accept={acceptedMimeTypes}
          onChange={(e) => {
            const file = e.target.files?.[0];
            setError(null);
            if (file == null) return;
            convertFileToBase64(file).then((src) => {
              let supported = false;
              for (const mime of SUPPORTED_MIME_TYPES) {
                if (
                  file.type.startsWith(mime) &&
                  (!SUPPORTED_SUBTYPES[mime] ||
                    SUPPORTED_SUBTYPES[mime]?.includes(file.type.split('/')[1]))
                ) {
                  props.onUpload(mime, src);
                  supported = true;
                  break;
                }
              }
              if (!supported) {
                setError(`Do not support mime type: ${file.type}`);
              }
            });
          }}
        />
        {error && <div className="mt-2 text-xs text-error">{error}</div>}
      </div>

      <div className="grid grid-cols-[1fr,auto,1fr] items-center gap-2">
        <span className="h-px w-full bg-tertiary" />
        <span className="text-sm uppercase text-tertiary">or</span>
        <span className="h-px w-full bg-tertiary" />
      </div>

      <form
        className="flex flex-col items-stretch gap-2"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          const formData = new FormData(e.currentTarget);
          props.onUpload(
            'image',
            new URL(formData.get('url') as string).toString()
          );
        }}
      >
        <label htmlFor={urlId} className="text-sm font-medium">
          Embed link
        </label>
        <div className="grid grid-cols-[1fr,auto] gap-2">
          <input
            id={urlId}
            placeholder="Enter URL..."
            className="rounded-lg border border-secondary bg-transparent px-2 py-2 text-xs transition-colors focus:border-brand focus:outline-none"
            type="url"
            name="url"
          />
          <button
            type="submit"
            className="rounded-lg bg-brand-primary px-4 py-1 text-xs font-medium text-white transition-colors hover:bg-brand-primary-hover"
          >
            Embed
          </button>
        </div>
      </form>
    </>
  );
}
