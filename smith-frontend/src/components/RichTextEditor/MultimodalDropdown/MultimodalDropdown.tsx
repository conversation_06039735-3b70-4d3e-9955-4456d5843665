import { TemplateFormat } from '@langchain/core/prompts';

import { ReactNode, useState } from 'react';

import { InfoTooltip } from '@/components/InfoTooltip/InfoTooltip';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { TabGroup, TabLabel, TabList } from '@/components/Tabs';
import { cn } from '@/utils/tailwind';

import { MimeType } from '../constants';
import { AttachmentName } from '../types';
import { MultimodalInline } from './components/MultimodalInline';
import { MultimodalTemplateVariable } from './components/MultimodalTemplateVariable';
import { RichTextImageEditorVariant } from './types';

// if you specify a variant, the dropdown will only show the selected view
export function MultimodalDropdown(props: {
  variant?: RichTextImageEditorVariant;
  onUpload: (type: MimeType, src: string) => void;
  children?: ReactNode;
  templateFormat: TemplateFormat;
  mimeType?: MimeType;
  attachmentNames?: AttachmentName[];
}) {
  const [open, setOpen] = useState(false);

  const [view, setView] = useState<RichTextImageEditorVariant>(
    props.variant ?? 'template'
  );

  const renderContent = () => {
    if (view === 'template') {
      return (
        <MultimodalTemplateVariable
          onSubmit={(fileType, variableName) => {
            props.onUpload(fileType, variableName);
            setOpen(false);
          }}
          onUploadRequest={() => setView('inline')}
          templateFormat={props.templateFormat}
          attachmentNames={props.attachmentNames ?? []}
        />
      );
    }

    if (view === 'inline') {
      return (
        <MultimodalInline
          onUpload={(type, src) => {
            props.onUpload(type, src);
            setOpen(false);
          }}
          mimeType={props.mimeType}
        />
      );
    }

    return null;
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{props.children}</PopoverTrigger>
      <PopoverContent className="w-[350px]">
        <div className="flex flex-col gap-2">
          {!props.variant && (
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-semibold text-primary">
                  Add Multimodal Content
                </span>
                <InfoTooltip
                  title="Include images, PDFs, and audio files in your prompt"
                  description="Choose between adding a prompt variable for multimodal content or directly uploading files inline."
                />
              </div>
              <TabGroup
                defaultIndex={view === 'template' ? 0 : 1}
                setTabMethod="none"
                onChange={(index) => {
                  setView(index === 0 ? 'template' : 'inline');
                }}
              >
                <TabList className="m-0 flex gap-2 bg-background px-0">
                  <TabLabel
                    className={cn(
                      'flex items-center justify-center gap-2 whitespace-nowrap px-1 py-0.5 text-sm font-medium text-primary',
                      view !== 'template' && 'text-quaternary'
                    )}
                  >
                    Content Variable
                  </TabLabel>
                  <TabLabel
                    className={cn(
                      'flex items-center justify-center gap-2 whitespace-nowrap px-1 py-0.5 text-sm font-medium text-primary',
                      view !== 'inline' && 'text-quaternary'
                    )}
                  >
                    Upload Content
                  </TabLabel>
                </TabList>
              </TabGroup>
            </div>
          )}
          {renderContent()}
        </div>
      </PopoverContent>
    </Popover>
  );
}
