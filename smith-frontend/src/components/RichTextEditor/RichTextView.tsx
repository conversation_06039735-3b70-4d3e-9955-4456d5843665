import mime from 'mime/lite';
import { ReactNode } from 'react';

import {
  MessageContentPart,
  S3DataSchema,
  isMessageContentPartLangChainMultimodal,
  isMessageContentPartOpenAIAudio,
  isMessageContentPartOpenAIFile,
} from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { PlaygroundOutputCodeBlock } from '../../Pages/Playground/components/PlaygroundOutputCodeBlock';
import {
  OpenAIFunctionCallKwargsSchema,
  OpenAIToolCallsKwargsSchema,
  ToolCallsKwargsSchema,
} from '../Message.utils';
import { TextWithLinks } from '../TextWithLinks';
import { MultimodalContent } from './MultimodalContent';
import { DATA_PREFIX } from './constants';
import { getMimeTypeFromDataUrl } from './utils/RichTextEditor.utils';
import { splitMarkdownCodeBlocks } from './utils/markdown';

export function RichTextView(props: {
  content: MessageContentPart[] | string;
  toolCalls?: any;
  attachments?: Record<string, S3DataSchema>;
  fancyRendering?: boolean;
  empty?: ReactNode;
  className?: string;
  showToolCalls?: boolean;
  maxHeightForVirtualization?: string; // If provided, long text will be virtualized
}) {
  if (typeof props.content === 'string') {
    const toolCallsComponents = props.toolCalls &&
      props.showToolCalls &&
      (!Array.isArray(props.toolCalls) || props.toolCalls.length > 0) && (
        <RenderToolCalls
          toolCall={{
            tool_calls: Array.isArray(props.toolCalls)
              ? props.toolCalls
              : [props.toolCalls],
          }}
        />
      );

    if (props.content === '') {
      if (props.toolCalls) {
        return toolCallsComponents;
      }
      return props.empty ?? null;
    }

    const parts = splitMarkdownCodeBlocks(props.content.trim());

    // Only use fancy rendering if we found valid code blocks
    if (!props.fancyRendering || parts.length === 1) {
      return (
        <div className={cn('whitespace-pre-wrap text-sm', props.className)}>
          {
            <TextWithLinks
              text={props.content.trim()}
              containerHeight={props.maxHeightForVirtualization}
            />
          }
          {toolCallsComponents}
        </div>
      );
    }

    return (
      <div className={cn('flex flex-col gap-3.5 text-sm', props.className)}>
        {parts.map((part, idx) => {
          const match = part.match(/^```([A-Za-z]+)([\s\S]*?)```$/);
          if (!match) {
            return (
              part.trim() && (
                <div
                  key={idx}
                  className="dark:prose-invert prose prose-playground flex flex-col gap-3"
                >
                  {part.trim()}
                </div>
              )
            );
          }

          const [, language, code] = match;
          return (
            <div key={idx}>
              {props.fancyRendering ? (
                <PlaygroundOutputCodeBlock
                  code={code.trim()}
                  language={language}
                />
              ) : (
                code.trim()
              )}
            </div>
          );
        })}
        {toolCallsComponents}
      </div>
    );
  }

  if (Array.isArray(props.content)) {
    return props.content.map((part, idx) => {
      if (
        part.type === 'text' ||
        part.type === 'text_delta' ||
        part.type === 'output_text'
      ) {
        let content: string | ReactNode = '';
        if (part.text && typeof part.text === 'string') {
          content = <TextWithLinks key={idx} text={part.text.trim()} />;
        }

        if (
          part.text &&
          typeof part.text === 'object' &&
          'value' in part.text
        ) {
          content = part.text.value + '\n';
        }

        return (
          <div
            key={idx}
            className={cn('whitespace-pre-wrap text-sm', props.className)}
          >
            {content}
          </div>
        );
      }

      if (part.type === 'image_url') {
        if (part.image_url == null) {
          return null;
        }
        const url =
          typeof part.image_url === 'string'
            ? part.image_url
            : part.image_url.url;
        const urlByAttachment = props.attachments?.[url]?.presigned_url ?? url;
        const imageDataClasses =
          'max-h-[45vh] w-full rounded-lg border border-secondary bg-secondary-hover object-contain object-center';
        if (typeof urlByAttachment !== 'string') {
          return (
            <img
              key={idx}
              src={`data:image/png;base64,`}
              className={imageDataClasses}
            />
          );
        }
        // Check if the URL is a base64 string
        const isBase64 =
          urlByAttachment.startsWith('data:') ||
          /^[A-Za-z0-9+/=]+$/.test(urlByAttachment);
        const srcUrl =
          isBase64 && !urlByAttachment.startsWith('data:')
            ? `data:image/png;base64,${urlByAttachment}` // Adjust image type if needed
            : urlByAttachment;
        return <img key={idx} src={srcUrl} className={imageDataClasses} />;
      } else if (isMessageContentPartLangChainMultimodal(part)) {
        return (
          <MultimodalContent
            key={idx}
            contentType={part.mime_type}
            url={part.data ?? part.url}
            contentName={''}
            unstyled={true}
          />
        );
      } else if (isMessageContentPartOpenAIFile(part)) {
        const url = part.file.file_data;
        if (typeof url !== 'string') {
          return null;
        }
        let mimeType = mime.getType(part.file.filename);
        if (!mimeType && url?.startsWith(DATA_PREFIX)) {
          mimeType = getMimeTypeFromDataUrl(url);
        }
        if (!mimeType) {
          return null;
        }
        return (
          <MultimodalContent
            key={idx}
            contentType={mimeType}
            url={url}
            contentName={part.file.filename}
            unstyled={true}
          />
        );
      } else if (isMessageContentPartOpenAIAudio(part)) {
        const url = part.input_audio.data;
        if (typeof url !== 'string') {
          return null;
        }
        const mimeType = `audio/${part.input_audio.format}`;
        return (
          <MultimodalContent
            key={idx}
            contentType={mimeType}
            url={url}
            contentName={`audio.${part.input_audio.format}`}
            unstyled={true}
          />
        );
      } else if (part.type === 'tool_use' && props.showToolCalls) {
        const toolCallFound = props.toolCalls?.find((t) => t.id === part.id);
        const toolCall = toolCallFound ? { tool_calls: [toolCallFound] } : null;
        return <RenderToolCalls toolCall={toolCall} fallback={part} />;
      }
      return null;
    });
  }
  return null;
}

function RenderToolCalls({
  toolCall,
  fallback,
}: {
  toolCall?: any;
  fallback?: any;
}) {
  const fnCall = OpenAIFunctionCallKwargsSchema.safeParse(toolCall);

  if (fnCall.success) {
    return (
      <PlaygroundOutputCodeBlock
        code={JSON.stringify(fnCall.data, null, 2)}
        language={'json'}
      />
    );
  }

  const oaiToolCalls = OpenAIToolCallsKwargsSchema.safeParse(toolCall);
  if (oaiToolCalls.success) {
    return (
      <PlaygroundOutputCodeBlock
        code={JSON.stringify(oaiToolCalls.data, null, 2)}
        language={'json'}
      />
    );
  }

  const toolCalls = ToolCallsKwargsSchema.safeParse(toolCall);
  if (toolCalls.success && toolCalls.data.tool_calls.length > 0) {
    return (
      <PlaygroundOutputCodeBlock
        code={JSON.stringify(toolCalls.data, null, 2)}
        language={'json'}
      />
    );
  }

  return (
    <PlaygroundOutputCodeBlock
      code={JSON.stringify(toolCall ?? fallback, null, 2)}
      language={'json'}
    />
  );
}
