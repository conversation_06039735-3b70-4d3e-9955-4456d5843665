import { MutableRefObject, useEffect } from 'react';
import { useDebounce } from 'use-debounce';

import { getInputVarsFromTemplate } from '@/components/RichTextEditor/utils/RichTextEditor.utils';

import { PROMPT_EXCLUDED_VARIABLES } from '../constants';
import { useEvaluatorStore } from '../store/evaluatorStore';
import { deserializeSchemaToFeedback } from './serializeFeedbackUIToLC';
import { useParsedManifest } from './useParsedManifest';

export const useUpdateFewshotWithFeedbackName = (
  isDataset: boolean,
  refreshContentRef: MutableRefObject<(content: string) => void | null>
) => {
  const fewShotContent = useEvaluatorStore((state) => state.fewShotContent);
  const setFewShotContent = useEvaluatorStore(
    (state) => state.setFewShotContent
  );
  const fewShotExamplesMap = useEvaluatorStore(
    (state) => state.fewShotExamplesMap
  );

  const { manifestStructuredSchema } = useParsedManifest(isDataset);
  const deserializedSchema = deserializeSchemaToFeedback(
    manifestStructuredSchema
  );
  const feedbackName = deserializedSchema?.name ?? 'score';

  const [debouncedFeedbackName] = useDebounce(feedbackName, 500);

  useEffect(() => {
    if (!fewShotContent) return;

    // Extract all mustache variables from content
    const mustacheVars = getInputVarsFromTemplate(
      fewShotContent,
      'mustache',
      true
    );

    // Filter out variables that are in fewShotExampleMap and few_shot_explanation
    const remainingVars = mustacheVars.filter(
      (variable) =>
        !fewShotExamplesMap[variable] && variable !== 'few_shot_explanation'
    );

    // If we found exactly one remaining variable, it's our score variable to update
    if (
      remainingVars.length === 1 &&
      remainingVars[0] !== debouncedFeedbackName
    ) {
      const oldFeedbackName = remainingVars[0];
      const updatedContent = fewShotContent
        .replace(`{{${oldFeedbackName}}}`, `{{${debouncedFeedbackName}}`)
        .replace(`<${oldFeedbackName}>`, `<${debouncedFeedbackName}>`)
        .replace(`</${oldFeedbackName}>`, `</${debouncedFeedbackName}>`)
        .replace(`{{${oldFeedbackName}}}`, `{{${debouncedFeedbackName}}}`);

      setFewShotContent(updatedContent);
      refreshContentRef.current?.(updatedContent);
    }
  }, [debouncedFeedbackName]);

  return debouncedFeedbackName;
};

// Generate default few-shot content from variable mappings
export const generateDefaultContent = (
  variableMapping: Record<string, string> | null,
  feedbackName: string,
  hasReasoning?: boolean
) => {
  let mappingLines: string;
  if (!variableMapping || Object.keys(variableMapping).length === 0) {
    mappingLines = 'input: ...\noutput: ...';
  } else {
    mappingLines = Object.entries(variableMapping)
      .filter(([key]) => !PROMPT_EXCLUDED_VARIABLES.includes(key))
      .map(([key]) => `<${key}>\n{{${key}}}\n</${key}>`)
      .join('\n\n');
  }

  // Create the complete content template
  let content: string;
  if (hasReasoning) {
    content = `<example>\n${mappingLines}\n\n<extract>\n<comment>\n{{few_shot_explanation}}\n</comment>\n\n<${feedbackName}>\n{{${feedbackName}}}\n</${feedbackName}>\n</extract>\n</example>`;
  } else {
    content = `<example>\n${mappingLines}\n\n<reasoning>\n{{few_shot_explanation}}\n</reasoning>\n\n<extract>\n<${feedbackName}>\n{{${feedbackName}}}\n</${feedbackName}>\n</extract>\n</example>`;
  }

  return {
    content,
    mapping: variableMapping ?? {},
  };
};
1;
