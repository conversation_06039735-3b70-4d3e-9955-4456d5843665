import { describe, expect, it } from 'vitest';

import { MessageTuple } from '@/types/schema';

import {
  findFewShotMessageIndex,
  hasFewShotVariable,
  insertFewShotExamples,
  insertFewShotVariable,
  processFewShotExamples,
  removeFewShotVariable,
} from '../fewShotExamplesUtils';

describe('fewShotExamplesUtils', () => {
  describe('findFewShotMessageIndex', () => {
    it('should return -1 for null or empty messages', () => {
      expect(findFewShotMessageIndex(null)).toBe(-1);
      expect(findFewShotMessageIndex([])).toBe(-1);
    });

    it('should return the index of the last system message', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        ['system', 'System message 1'],
        ['assistant', 'Assistant message'],
        ['system', 'System message 2'],
        ['user', 'Final message'],
      ];
      expect(findFewShotMessageIndex(messages)).toBe(3); // Index of 'System message 2'
    });

    it('should return the second-to-last message index if no system message is found', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        ['assistant', 'Assistant message'],
        ['user', 'Final message'],
      ];
      expect(findFewShotMessageIndex(messages)).toBe(1); // Index of 'Assistant message'
    });

    it('should return the last message index if only one message and no system message', () => {
      const messages: MessageTuple[] = [['user', 'Single message']];
      expect(findFewShotMessageIndex(messages)).toBe(0);
    });
  });

  describe('insertFewShotVariable', () => {
    it('should return empty array for null or empty messages', () => {
      expect(insertFewShotVariable(null)).toEqual([]);
      expect(insertFewShotVariable([])).toEqual([]);
    });

    it('should insert the variable at the last system message', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        ['system', 'System message'],
        ['assistant', 'Assistant message'],
      ];
      const result = insertFewShotVariable(messages);
      expect(result).toEqual([
        ['user', 'Hello'],
        [
          'system',
          'System message\n\nUse the examples below for reference:\n{{few_shot_examples}}',
        ],
        ['assistant', 'Assistant message'],
      ]);
    });

    it('should not add the variable if it already exists', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        [
          'system',
          'System message\n\nUse the examples below for reference:\n{{few_shot_examples}}',
        ],
        ['assistant', 'Assistant message'],
      ];
      const result = insertFewShotVariable(messages);
      expect(result).toEqual(messages);
    });
  });

  describe('removeFewShotVariable', () => {
    it('should return empty array for null or empty messages', () => {
      expect(removeFewShotVariable(null)).toEqual([]);
      expect(removeFewShotVariable([])).toEqual([]);
    });

    it('should remove the variable from all messages', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        ['system', 'System message\n\n{{few_shot_examples}}'],
        ['assistant', 'Assistant message with {{few_shot_examples}}'],
      ];
      const result = removeFewShotVariable(messages);
      expect(result).toEqual([
        ['user', 'Hello'],
        ['system', 'System message'],
        ['assistant', 'Assistant message with '],
      ]);
    });

    it('should handle messages without the variable', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        ['system', 'System message'],
      ];
      const result = removeFewShotVariable(messages);
      expect(result).toEqual(messages);
    });
  });

  describe('hasFewShotVariable', () => {
    it('should return false for null or empty messages', () => {
      expect(hasFewShotVariable(null)).toBe(false);
      expect(hasFewShotVariable([])).toBe(false);
    });

    it('should return true if any message contains the variable', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        ['system', 'System message\n\n{{few_shot_examples}}'],
        ['assistant', 'Assistant message'],
      ];
      expect(hasFewShotVariable(messages)).toBe(true);
    });

    it('should return false if no message contains the variable', () => {
      const messages: MessageTuple[] = [
        ['user', 'Hello'],
        ['system', 'System message'],
      ];
      expect(hasFewShotVariable(messages)).toBe(false);
    });
  });

  describe('processFewShotExamples', () => {
    it('should extract few-shot content and replace with placeholder', () => {
      const messages: MessageTuple[] = [
        [
          'system',
          'System message\nUse the examples below for reference {{#examples_few_shot}}\nExample 1\nExample 2\n{{/examples_few_shot}}',
        ],
        ['user', 'User message'],
      ];
      const result = processFewShotExamples(messages);
      expect(result.fewShotContent).toBe('Example 1\nExample 2');
      expect(result.processedMessages).toEqual([
        [
          'system',
          'System message\nUse the examples below for reference {{few_shot_examples}}',
        ],
        ['user', 'User message'],
      ]);
    });

    it('should handle messages without few-shot examples', () => {
      const messages: MessageTuple[] = [
        ['system', 'System message'],
        ['user', 'User message'],
      ];
      const result = processFewShotExamples(messages);
      expect(result.fewShotContent).toBe('');
      expect(result.processedMessages).toEqual(messages);
    });
  });

  describe('insertFewShotExamples', () => {
    it('should replace placeholders with formatted content', () => {
      const messages: MessageTuple[] = [
        ['system', 'System message\n{{few_shot_examples}}'],
        ['user', 'User message'],
      ];
      const result = insertFewShotExamples(messages, 'Example 1\nExample 2');
      expect(result).toEqual([
        [
          'system',
          'System message\n{{#examples_few_shot}}\nExample 1\nExample 2\n{{/examples_few_shot}}',
        ],
        ['user', 'User message'],
      ]);
    });

    it('should not modify messages without the placeholder', () => {
      const messages: MessageTuple[] = [
        ['system', 'System message'],
        ['user', 'User message'],
      ];
      const result = insertFewShotExamples(messages, 'Example content');
      expect(result).toEqual(messages);
    });
  });
});
