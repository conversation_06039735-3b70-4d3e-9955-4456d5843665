import { useMemo } from 'react';

import { manifestModelAndProvider } from '@/Pages/Playground/components/manifest/serialized/ManifestConstructor';
import { isAnyOSeriesModel } from '@/Pages/Playground/components/manifest/serialized/chat_models/ManifestChatOpenAI.utils';
import { EMessageType } from '@/components/EditableMessage/types';

import { useEvaluatorStore } from '../store/evaluatorStore';
import { useHubTypeManifest } from './useHubTypeManifest';
import { usePromptTypeManifest } from './usePromptTypeManifest';

export function useParsedManifest(isDataset: boolean) {
  const evaluatorType = useEvaluatorStore((state) => state.evaluatorType);
  const promptManifest = usePromptTypeManifest(isDataset);
  const { manifest: hubManifest, isLoading: isHubManifestLoading } =
    useHubTypeManifest();

  const manifest = evaluatorType === 'prompt' ? promptManifest : hubManifest;
  const manifestLast = manifest.kwargs.last;
  const modelAndProvider = manifestModelAndProvider({
    variant: 'line',
    value: manifestLast,
    options: {},
  });
  const { isO1Generic } = isAnyOSeriesModel(modelAndProvider?.model ?? '');

  const manifestStructuredSchema =
    manifest.kwargs.first.kwargs.schema || manifest.kwargs.first.kwargs.schema_;
  const disabledMessageTypes = useMemo(
    () => getDisabledMessageTypes(modelAndProvider?.model ?? ''),
    [modelAndProvider?.model]
  );

  return {
    manifest,
    manifestLast,
    modelAndProvider,
    isO1Generic,
    manifestStructuredSchema,
    disabledMessageTypes,
    isHubManifestLoading,
  };
}

export const getDisabledMessageTypes = (model: string) => {
  const isO1Generic = isAnyOSeriesModel(model);
  const types = [
    EMessageType.FUNCTION,
    EMessageType.TOOL,
    EMessageType.CHAT,
    EMessageType.PLACEHOLDER,
  ];
  if (Object.values(isO1Generic).some((x) => x)) {
    types.push(EMessageType.SYSTEM);
  }
  return types;
};
