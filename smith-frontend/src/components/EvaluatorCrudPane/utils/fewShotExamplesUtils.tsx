import { MessageTuple } from '@/types/schema';

/**
 * Processes message tuples to extract few-shot examples content.
 * Looks for the pattern "Use the examples below for reference:\n{{#examples_few_shot}}...{{/examples_few_shot}}"
 * and replaces it with the placeholder "{{few_shot_examples}}".
 *
 * @param messages - The message tuples to process
 * @returns An object containing processed messages and extracted few-shot content
 */
export function processFewShotExamples(messages: MessageTuple[]): {
  processedMessages: MessageTuple[];
  fewShotContent: string;
} {
  const processedMessages: MessageTuple[] = [];
  let fewShotContent = '';
  const fewShotRegex =
    /{{#examples_few_shot}}([\s\S]*?){{\/examples_few_shot}}/;

  for (const [messageType, messageContent] of messages) {
    let processedContent = messageContent;
    const match = messageContent.match(fewShotRegex);
    if (match && match[1]) {
      // Extract few-shot content from the message
      fewShotContent = match[1].trim();

      // Replace the examples block with the placeholder
      processedContent = messageContent.replace(
        fewShotRegex,
        '{{few_shot_examples}}'
      );
    }

    processedMessages.push([messageType, processedContent]);
  }

  return { processedMessages, fewShotContent };
}

/**
 * Inserts few-shot examples content into message tuples.
 * Replaces the placeholder "{{few_shot_examples}}" with the formatted content:
 * "{{#examples_few_shot}}...{{/examples_few_shot}}".
 *
 * @param messages - The message tuples to process
 * @param fewShotContent - The few-shot content to insert
 * @returns The processed message tuples with few-shot examples inserted
 */
export function insertFewShotExamples(
  messages: MessageTuple[],
  fewShotContent: string
): MessageTuple[] {
  return messages.map(([messageType, messageContent]) => {
    // Replace {{few_shot_examples}} with the formatted block
    if (messageContent.includes('{{few_shot_examples}}')) {
      const replacementText = `{{#examples_few_shot}}
${fewShotContent}
{{/examples_few_shot}}`;

      const updatedContent = messageContent
        .replace(
          'Use the examples below for reference:\n{{few_shot_examples}}',
          replacementText
        )
        .replace('{{few_shot_examples}}', replacementText);

      return [messageType, updatedContent];
    }

    return [messageType, messageContent];
  });
}

/**
 * Find the appropriate message in which to insert/remove the few-shot examples variable.
 * Looks for the last "system" message, or falls back to second-to-last or last message.
 *
 * @param messages - The message tuples to search
 * @returns The index of the appropriate message, or -1 if no messages
 */
export function findFewShotMessageIndex(
  messages: MessageTuple[] | null
): number {
  if (!messages || messages.length === 0) {
    return -1;
  }

  // First, try to find the last system message
  for (let i = messages.length - 1; i >= 0; i--) {
    if (messages[i][0].toLowerCase() === 'system') {
      return i;
    }
  }

  // If no system message, use the second-to-last message if available
  if (messages.length > 1) {
    return messages.length - 2;
  }

  // Otherwise, use the last message
  return messages.length - 1;
}

/**
 * Insert the {{few_shot_examples}} variable into the appropriate message.
 *
 * @param messages - The message tuples to modify
 * @returns The modified message tuples with the variable inserted
 */
export function insertFewShotVariable(
  messages: MessageTuple[] | null
): MessageTuple[] {
  if (!messages || messages.length === 0) {
    return [];
  }

  const messageIndex = findFewShotMessageIndex(messages);
  if (messageIndex === -1) {
    return [...messages];
  }

  const result = [...messages];
  const [role, content] = result[messageIndex];

  // Only add the variable if it doesn't already exist
  if (!content.includes('{{few_shot_examples}}')) {
    result[messageIndex] = [
      role,
      `${content}\n\nUse the examples below for reference:\n{{few_shot_examples}}`,
    ];
  }

  return result;
}

/**
 * Remove the {{few_shot_examples}} variable from all messages.
 *
 * @param messages - The message tuples to modify
 * @returns The modified message tuples with the variable removed
 */
export function removeFewShotVariable(
  messages: MessageTuple[] | null
): MessageTuple[] {
  if (!messages || messages.length === 0) {
    return [];
  }

  return messages.map(([role, content]) => {
    // Remove the variable and any empty lines it might leave
    const newContent = content
      .replace(
        /\n\nUse the examples below for reference:\n{{few_shot_examples}}/,
        ''
      )
      .replace(
        /nUse the examples below for reference:\n{{few_shot_examples}}/,
        ''
      )
      .replace(/\n\n{{few_shot_examples}}/, '')
      .replace(/{{few_shot_examples}}/, '');

    return [role, newContent];
  });
}

/**
 * Checks if any messages contain the few-shot examples variable.
 *
 * @param messages - The message tuples to check
 * @returns True if any message contains the variable, false otherwise
 */
export function hasFewShotVariable(messages: MessageTuple[] | null): boolean {
  if (!messages || messages.length === 0) {
    return false;
  }

  return messages.some(([_, content]) =>
    content.includes('{{few_shot_examples}}')
  );
}
