import { useEffect } from 'react';

import { SessionEvaluatorCrudForm } from './EvaluatorCrudForm';
import { SessionsEvaluatorCrudPaneHeader } from './EvaluatorCrudPaneHeader';
import { EvaluatorCrudPaneLayout } from './EvaluatorCrudPaneLayout';
import { SessionPreviewPanel } from './PreviewPanel';
import { useEvaluatorStore } from './store/evaluatorStore';
import { SessionEvaluatorCrudPaneProps } from './types';

export function SessionsEvaluatorCrudPane(
  props: SessionEvaluatorCrudPaneProps
) {
  const importEvaluator = useEvaluatorStore((state) => state.importEvaluator);

  const { open, setOpen } = props;
  const fewShotEnabled = useEvaluatorStore((state) => state.fewShotEnabled);
  const setFewShotEnabled = useEvaluatorStore(
    (state) => state.setFewShotEnabled
  );
  const numFewShotExamples = useEvaluatorStore(
    (state) => state.numFewShotExamples
  );
  const setNumFewShotExamples = useEvaluatorStore(
    (state) => state.setNumFewShotExamples
  );

  const { onEvaluatorSaved, filters = { filter: 'eq(is_root, true)' } } = props;

  useEffect(() => {
    if (open) {
      importEvaluator(props.evaluator, 'session', props.backfill_from);
      if (props.fewShotEnabled !== undefined) {
        setFewShotEnabled(props.fewShotEnabled);
      }
      if (props.numFewShotExamples !== undefined) {
        setNumFewShotExamples(props.numFewShotExamples);
      }
    }
  }, [
    open,
    props.evaluator,
    props.backfill_from,
    importEvaluator,
    props.fewShotEnabled,
    props.numFewShotExamples,
    setFewShotEnabled,
    setNumFewShotExamples,
  ]);

  return (
    <EvaluatorCrudPaneLayout
      title={props.evaluator ? 'Edit evaluator' : 'Create evaluator'}
      open={open}
      setOpen={setOpen}
      left={
        <SessionEvaluatorCrudForm
          sessionId={props.sessionId}
          filters={filters}
          fewShotDatasetId={props.correctionsDatasetId}
          evaluatorVersion={props.evaluatorVersion}
          isCreating={Boolean(props.evaluator)}
        />
      }
      right={
        <SessionPreviewPanel sessionId={props.sessionId} filters={filters} />
      }
      header={
        <SessionsEvaluatorCrudPaneHeader
          selectedEvaluator={props.evaluator}
          onClose={() => {
            setOpen(false);
          }}
          onSave={(evaluator) => {
            onEvaluatorSaved({
              evaluator,
              fewShotEnabled,
              numFewShotExamples,
            });
            setOpen(false);
          }}
        />
      }
    />
  );
}
