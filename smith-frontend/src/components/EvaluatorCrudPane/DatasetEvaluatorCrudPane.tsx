import { useEffect } from 'react';

import { DatasetEvaluatorCrudForm } from './EvaluatorCrudForm';
import { DatasetEvaluatorCrudPaneHeader } from './EvaluatorCrudPaneHeader';
import { EvaluatorCrudPaneLayout } from './EvaluatorCrudPaneLayout';
import { DatasetPreviewPanel } from './PreviewPanel';
import { useEvaluatorStore } from './store/evaluatorStore';
import { DatasetEvaluatorCrudPaneProps } from './types';

export function DatasetEvaluatorCrudPane(props: DatasetEvaluatorCrudPaneProps) {
  const importEvaluator = useEvaluatorStore((state) => state.importEvaluator);
  const setName = useEvaluatorStore((state) => state.setName);
  const setFewShotEnabled = useEvaluatorStore(
    (state) => state.setFewShotEnabled
  );
  const setNumFewShotExamples = useEvaluatorStore(
    (state) => state.setNumFewShotExamples
  );

  const { setOpen, open, onRuleSaved } = props;

  useEffect(() => {
    if (open) {
      importEvaluator(
        props.evaluatorRule?.evaluators?.[0].structured,
        'dataset',
        props.evaluatorRule?.backfill_from
      );
      if (props.evaluatorRule) {
        setName(props.evaluatorRule?.display_name ?? '', true);
        setFewShotEnabled(
          props.evaluatorRule?.use_corrections_dataset ?? false
        );
        setNumFewShotExamples(props.evaluatorRule?.num_few_shot_examples ?? 5);
      }
    }
  }, [
    open,
    props.evaluatorRule,
    importEvaluator,
    setName,
    setFewShotEnabled,
    setNumFewShotExamples,
  ]);
  return (
    <EvaluatorCrudPaneLayout
      title={props.evaluatorRule?.display_name ?? 'Create Evaluator'}
      open={open}
      setOpen={setOpen}
      left={
        <DatasetEvaluatorCrudForm
          datasetId={props.datasetId}
          fewShotDatasetId={props.evaluatorRule?.corrections_dataset_id}
          evaluatorVersion={props.evaluatorRule?.evaluator_version}
          isCreating={!props.evaluatorRule?.display_name}
        />
      }
      right={<DatasetPreviewPanel datasetId={props.datasetId} />}
      header={
        <DatasetEvaluatorCrudPaneHeader
          datasetId={props.datasetId}
          selectedRule={props.evaluatorRule}
          onClose={() => {
            setOpen(false);
          }}
          onSuccess={(ruleId) => {
            onRuleSaved(ruleId);
            setOpen(false);
          }}
          onDiscard={() => {
            setOpen(false);
          }}
        />
      }
    />
  );
}
