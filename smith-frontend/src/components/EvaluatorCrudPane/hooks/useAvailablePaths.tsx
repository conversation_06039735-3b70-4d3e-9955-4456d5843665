import { useEffect } from 'react';
import { useMemo } from 'react';

import { useEvaluatorStore } from '../store/evaluatorStore';
import { traverseKeys } from '../utils/traverseKeys';

export const useAvailablePaths = (
  input,
  output,
  referenceOutput,
  isLoading
) => {
  const setAvailablePaths = useEvaluatorStore(
    (state) => state.setAvailablePaths
  );

  const paths = useMemo(() => {
    const mPaths: string[] = [];
    if (input) {
      mPaths.push(...traverseKeys(input, 'input'));
    }
    if (output) {
      mPaths.push(...traverseKeys(output, 'output'));
    }
    if (referenceOutput) {
      mPaths.push(...traverseKeys(referenceOutput, 'referenceOutput'));
    }
    return mPaths;
  }, [input, output, referenceOutput]);

  useEffect(() => {
    if (paths.length > 0 && !isLoading) {
      setAvailablePaths(paths);
    }
  }, [paths, setAvailablePaths, isLoading]);
};
