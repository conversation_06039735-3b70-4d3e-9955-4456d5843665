import { json } from '@codemirror/lang-json';
import { LinearProgress } from '@mui/joy';

import { useMemo } from 'react';
import { Link, useParams } from 'react-router-dom';

import { MinimalCodeContext } from '@/Pages/Run/utils/MinimalCodeContext';
import { useOrganizationId } from '@/hooks/useSwr';
import { appOrganizationPath } from '@/utils/constants';

import { Code } from '../Code/Code';
import { createJsonHighlighterExtension } from '../Code/JsonHighlighterExtension';
import { SearchModel } from '../RunsTable/types';
import { useAvailablePaths } from './hooks/useAvailablePaths';
import { useEvaluatorStore } from './store/evaluatorStore';
import { EvaluatorCrudFormProps } from './types';
import {
  useDatasetInputOutputData,
  useSessionInputOutputData,
} from './utils/useInputOutputData';

const ExamplePreviewPanel = ({
  variableMapping,
  input,
  referenceOutput,
  output,
  activeVariable,
}: {
  variableMapping: Record<string, string>;
  input: Record<string, unknown>;
  referenceOutput?: Record<string, unknown>;
  output?: Record<string, unknown>;
  activeVariable: string | null;
}) => {
  const organizationId = useOrganizationId();
  const { datasetId } = useParams();

  const codeBlockMaxHeight = datasetId ? '30vh' : '40vh';

  // Create extensions for each data type
  const inputExtension = useMemo(
    () =>
      createJsonHighlighterExtension(variableMapping, 'input', activeVariable),
    [variableMapping, activeVariable]
  );

  const referenceOutputExtension = useMemo(
    () =>
      referenceOutput
        ? createJsonHighlighterExtension(
            variableMapping,
            'referenceOutput',
            activeVariable
          )
        : undefined,
    [variableMapping, referenceOutput, activeVariable]
  );

  const outputExtension = useMemo(
    () =>
      output
        ? createJsonHighlighterExtension(
            variableMapping,
            'output',
            activeVariable
          )
        : undefined,
    [variableMapping, output, activeVariable]
  );

  return (
    <MinimalCodeContext.Provider value={{ minimalCode: false }}>
      <div className="space-y-4">
        <h3 className="text-sm font-medium uppercase">Input</h3>
        <div className="rounded-md border border-secondary bg-secondary p-2">
          <Code
            value={JSON.stringify(input, null, 2)}
            language={json()}
            readOnly
            extensions={[inputExtension]}
            variant="plain"
            maxHeight={codeBlockMaxHeight}
          />
        </div>

        {referenceOutput && (
          <>
            <h3 className="text-sm font-medium uppercase">Reference Output</h3>
            <div className="rounded-md border border-secondary bg-secondary p-2">
              <Code
                value={JSON.stringify(referenceOutput, null, 2)}
                language={json()}
                readOnly
                extensions={
                  referenceOutputExtension
                    ? [referenceOutputExtension]
                    : undefined
                }
                variant="plain"
                maxHeight={codeBlockMaxHeight}
              />
            </div>
          </>
        )}

        {output ? (
          <>
            <h3 className="text-sm font-medium uppercase">Output</h3>
            <div className="rounded-md border border-secondary bg-secondary p-2">
              <Code
                value={JSON.stringify(output, null, 2)}
                language={json()}
                readOnly
                extensions={outputExtension ? [outputExtension] : undefined}
                variant="plain"
                maxHeight={codeBlockMaxHeight}
              />
            </div>
          </>
        ) : (
          datasetId && (
            <>
              <h3 className="text-sm font-medium uppercase">Output</h3>
              <div className="rounded-md border border-secondary bg-secondary p-2">
                Run an experiment{' '}
                <Link
                  className="text-brand-green-400 underline"
                  target="_blank"
                  to={`https://docs.smith.langchain.com/evaluation/how_to_guides/evaluate_llm_application`}
                >
                  via the SDK
                </Link>{' '}
                or{' '}
                <Link
                  className="text-brand-green-400 underline"
                  target="_blank"
                  to={`/${appOrganizationPath}/${organizationId}/playground?datasetId=${datasetId}`}
                >
                  in the Playground
                </Link>{' '}
                to generate outputs. This is recommended before creating an
                evaluator to aid with mapping variables from your prompt to the
                output.
              </div>
            </>
          )
        )}
      </div>
    </MinimalCodeContext.Provider>
  );
};

export const PreviewPanel = (
  props: Omit<EvaluatorCrudFormProps, 'showEvaluatorVersionWarning'>
) => {
  const variableMapping = useEvaluatorStore((state) => state.variableMapping);
  const { input, output, referenceOutput, isLoading } = props;

  const activeVariable = useEvaluatorStore((state) => state.activeVariable);

  useAvailablePaths(input, output, referenceOutput, isLoading);
  const isDatasetEvaluator = referenceOutput !== undefined;
  return (
    <div className="flex flex-col">
      <label className="pb-1.5 font-semibold">
        {isDatasetEvaluator ? 'Example' : 'Sample Run'}
      </label>
      <p className="pb-4 text-sm text-secondary">
        Use this {isDatasetEvaluator ? 'Example' : 'Sample Run'} from your{' '}
        {isDatasetEvaluator ? 'dataset' : 'tracing project'} to help map the
        placeholder variables in the evaluation prompt to the inputs, outputs{' '}
        {isDatasetEvaluator && 'and references outputs'}.
      </p>
      {isLoading ? (
        <div className="py-4 text-center">
          <LinearProgress />
        </div>
      ) : input ? (
        <ExamplePreviewPanel
          variableMapping={variableMapping ?? {}}
          input={input}
          referenceOutput={referenceOutput}
          output={output}
          activeVariable={activeVariable}
        />
      ) : (
        <div className="py-4 text-center text-secondary">
          No examples found in this dataset
        </div>
      )}
    </div>
  );
};

export const DatasetPreviewPanel = (props: { datasetId: string }) => {
  const { input, output, referenceOutput, isLoading } =
    useDatasetInputOutputData(props.datasetId);

  return (
    <PreviewPanel
      input={input ?? {}}
      output={output}
      referenceOutput={referenceOutput ?? {}}
      isLoading={isLoading}
    />
  );
};

export const SessionPreviewPanel = (props: {
  sessionId: string;
  filters: SearchModel;
}) => {
  const { input, output, isLoading } = useSessionInputOutputData({
    sessionId: props.sessionId,
    filters: props.filters,
  });

  return (
    <PreviewPanel
      input={input ?? {}}
      output={output ?? {}}
      isLoading={isLoading}
    />
  );
};
