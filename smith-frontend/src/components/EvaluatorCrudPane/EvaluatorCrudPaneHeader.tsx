import { Trash04Icon } from '@langchain/untitled-ui-icons';
import { <PERSON><PERSON>, Tooltip } from '@mui/joy';

import { useRef, useState } from 'react';

import { gatherSecrets } from '@/Pages/Playground/components/PlaygroundSecrets.utils';
import { EvaluatorSecrets } from '@/Pages/Settings/components/EvaluatorSecrets';
import { CURRENT_RULE_EVALUATOR_VERSION } from '@/Pages/Settings/utils/constants';
import { useTenantSecretSet } from '@/Pages/Settings/utils/useTenantSecretSet';
import { useRunRulesMutation } from '@/hooks/useSwr';
import {
  DatasetRuleMutateBody,
  EvaluatorRuleSchema,
  StructuredEvaluatorSchema,
} from '@/types/schema';
import { apiRunsPath } from '@/utils/constants';

import { DeleteModal } from '../Delete';
import useToast from '../Toast';
import { useEvaluatorStore } from './store/evaluatorStore';
import { getUpdatedEvaluatorMappings } from './utils/getUpdatedEvaluatorMappings';

export function DatasetEvaluatorCrudPaneHeader({
  datasetId,

  onClose,
  onSuccess,
  selectedRule,
  onDiscard,
}: {
  datasetId: string;

  onClose: () => void;
  onSuccess: (ruleId: string) => void;
  selectedRule?: EvaluatorRuleSchema | DatasetRuleMutateBody;
  onDiscard: () => void;
}) {
  const ruleId =
    selectedRule && 'id' in selectedRule ? selectedRule.id : undefined;
  const mutate = useRunRulesMutation(ruleId);

  const [deleteRuleId, setDeleteRuleId] = useState<string | undefined>(
    undefined
  );

  const edited = useEvaluatorStore((state) => state.edited);
  const name = useEvaluatorStore((state) => state.name);
  const exportEvaluator = useEvaluatorStore((state) => state.exportEvaluator);
  const validateEvaluator = useEvaluatorStore(
    (state) => state.validateEvaluator
  );
  const model = useEvaluatorStore((state) => state.model);
  const backfillFrom = useEvaluatorStore((state) => state.backfillFrom);

  const numFewShotExamples = useEvaluatorStore(
    (state) => state.numFewShotExamples
  );
  const fewShotEnabled = useEvaluatorStore((state) => state.fewShotEnabled);

  const openSecretsRef = useRef<(() => void) | null>(null);
  const secretKeys = useTenantSecretSet();

  const { createToast } = useToast();

  const handleSave = async () => {
    if (gatherSecrets(model).some((i) => !secretKeys.has(i))) {
      openSecretsRef.current?.();
      return;
    }

    try {
      const isValidated = validateEvaluator();
      if (!isValidated) {
        return;
      }
      const validatedEvaluator = exportEvaluator();
      if (!validatedEvaluator) {
        return;
      }

      if (!name) {
        throw new Error('Rule name is required');
      }

      const evaluatorVersion = getUpdatedEvaluatorMappings({
        oldEvaluator: selectedRule?.evaluators?.[0].structured,
        newEvaluator: validatedEvaluator,
        oldRuleEvaluatorVersion:
          selectedRule?.evaluator_version ?? CURRENT_RULE_EVALUATOR_VERSION,
      });

      const newRule = await mutate.trigger({
        json: {
          ...(selectedRule ?? {}),
          dataset_id: datasetId,
          is_enabled: true,
          sampling_rate: 1,
          filter: 'eq(is_root, true)',
          display_name: name,
          use_corrections_dataset: false,
          backfill_from: backfillFrom ?? undefined,
          // Upgrade evaluator version if the variable mapping has changed
          evaluator_version: evaluatorVersion,
          evaluators: [
            {
              structured: validatedEvaluator,
            },
          ],
          ...(fewShotEnabled && {
            num_few_shot_examples: numFewShotExamples,
            use_corrections_dataset: true,
          }),
        },
      });
      if (newRule) {
        onSuccess(newRule.id);
      } else {
        createToast({
          title: 'Error',
          description: `Failed to save evaluator: ${mutate.error?.message}`,
          type: 'error',
        });
      }
    } catch (e) {
      if (e instanceof Error) {
        createToast({
          title: 'Error',
          description: `Failed to save evaluator: ${e.message}`,
          type: 'error',
        });
      }
    }
  };

  return (
    <>
      <EvaluatorCrudPanelHeader
        edited={edited}
        onSave={handleSave}
        isSaving={mutate.isMutating}
        onDiscard={onDiscard}
        onDelete={() => setDeleteRuleId(ruleId)}
        selectedEvaluator={selectedRule?.evaluators?.[0].structured}
        secretsRef={openSecretsRef}
      />

      {deleteRuleId && (
        <DeleteModal
          endpoint={`${apiRunsPath}/rules`}
          id={deleteRuleId}
          isOpen={!!deleteRuleId}
          doClose={() => {
            setDeleteRuleId(undefined);
            onClose();
          }}
          // this is needed to prevent weird interaction with the pane
          disablePortal
        />
      )}
    </>
  );
}

export function SessionsEvaluatorCrudPaneHeader({
  onClose,
  onSave,
  selectedEvaluator,
}: {
  onClose: () => void;
  onSave: (evaluator: StructuredEvaluatorSchema) => void;
  selectedEvaluator?: StructuredEvaluatorSchema;
}) {
  const reset = useEvaluatorStore((state) => state.reset);
  const exportEvaluator = useEvaluatorStore((state) => state.exportEvaluator);
  const edited = useEvaluatorStore((state) => state.edited);

  const { createToast } = useToast();

  const handleDiscard = () => {
    reset();
    onClose();
  };

  const model = useEvaluatorStore((state) => state.model);
  const openSecretsRef = useRef<(() => void) | null>(null);
  const secretKeys = useTenantSecretSet();

  const handleSave = () => {
    if (gatherSecrets(model).some((i) => !secretKeys.has(i))) {
      openSecretsRef.current?.();
      return;
    }
    try {
      const evaluator = exportEvaluator();
      if (!evaluator) {
        return;
      }
      onSave(evaluator);
    } catch (error) {
      createToast({
        title: 'Error',
        description: `Failed to save evaluator: ${error}`,
        type: 'error',
      });
    }
  };
  return (
    <EvaluatorCrudPanelHeader
      edited={edited}
      onSave={handleSave}
      onDiscard={handleDiscard}
      selectedEvaluator={selectedEvaluator}
      secretsRef={openSecretsRef}
    />
  );
}

function EvaluatorCrudPanelHeader({
  edited,
  onSave,
  isSaving,

  selectedEvaluator,
  onDiscard,
  onDelete,
  secretsRef,
}: {
  selectedEvaluator?: StructuredEvaluatorSchema;

  edited: boolean;
  onDiscard: () => void;

  onSave: () => void;
  isSaving?: boolean;

  onDelete?: () => void;

  secretsRef?: React.RefObject<(() => void) | null>;
}) {
  const model = useEvaluatorStore((state) => state.model);

  return (
    <div className="flex flex-row gap-2 px-4">
      {selectedEvaluator && onDelete && (
        <Button
          color="danger"
          variant="outlined"
          size="sm"
          startDecorator={<Trash04Icon className="h-4 w-4" />}
          onClick={() => {
            onDelete();
          }}
        >
          Delete Evaluator
        </Button>
      )}
      {edited && onDiscard && (
        <Button
          color="neutral"
          variant="outlined"
          size="sm"
          onClick={onDiscard}
        >
          Discard changes
        </Button>
      )}
      <Tooltip title={edited ? 'Save changes' : 'No changes'}>
        <div>
          <Button
            size="sm"
            onClick={onSave}
            loading={isSaving}
            disabled={!edited}
          >
            {selectedEvaluator ? 'Save' : 'Create'}
          </Button>
        </div>
      </Tooltip>
      {secretsRef && (
        <EvaluatorSecrets
          model={model ?? undefined}
          openSecretsRef={secretsRef}
          infoMessage={
            'Your API keys are stored encrypted on our servers in order to invoke evaluators without attendance.'
          }
          triggerComponent={<div className="h-8 w-1" />}
        />
      )}
    </div>
  );
}
