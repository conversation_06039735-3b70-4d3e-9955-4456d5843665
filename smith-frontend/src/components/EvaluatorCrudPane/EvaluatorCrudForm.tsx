import { XIcon } from '@langchain/untitled-ui-icons';
import { FormControl, Tooltip } from '@mui/joy';

import { useEffect, useRef, useState } from 'react';

import { PromptSettingsDisplay } from '@/Pages/Playground/components/PromptSettings';
import { Manifest } from '@/Pages/Playground/components/manifest/Manifest';
import { PromptSelector } from '@/Pages/Prompts/components/PromptSelector';
import { Skeleton } from '@/Pages/Run/components/InputOutputLoadingSkeleton';
import { convertSerializedMessagesToMessageTuple } from '@/Pages/Settings/RulesEvaluatorPane.utils';
import {
  BackfillDateInput,
  BackfillFromFormLabel,
} from '@/Pages/Settings/components/SessionRulesCrudForm';
import { CURRENT_RULE_EVALUATOR_VERSION } from '@/Pages/Settings/utils/constants';
import { MessageCapability } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { SearchModel } from '../RunsTable/types';
import { Switch } from '../Switch/Switch';
import { EvaluatorFeedbackSchema } from './EvaluatorFeedbackSchema';
import { EvaluatorFewShotConfig } from './EvaluatorFewShotConfig';
import { EvaluatorHubPromptPlaygroundLink } from './HubPromptPlaygroundLink';
import { PromptPreview } from './PromptPreview';
import { useEvaluatorStore } from './store/evaluatorStore';
import { EvaluatorCrudFormProps } from './types';
import {
  useDatasetInputOutputData,
  useSessionInputOutputData,
} from './utils/useInputOutputData';
import { useParsedManifest } from './utils/useParsedManifest';
import { useVariableMappingProps } from './utils/useVariableMappingProps';

function EvaluatorCrudForm(props: EvaluatorCrudFormProps) {
  const [preview, setPreview] = useState<boolean>(false);
  const reset = useEvaluatorStore((state) => state.reset);

  const setModel = useEvaluatorStore((state) => state.setModel);
  const setPromptMessages = useEvaluatorStore(
    (state) => state.setPromptMessages
  );
  const updateMappingWithPromptManifest = useEvaluatorStore(
    (state) => state.updateMappingWithPromptManifest
  );

  const fewShotEnabled = useEvaluatorStore((state) => state.fewShotEnabled);
  const promptTemplateFormat = useEvaluatorStore(
    (state) => state.promptTemplateFormat
  );
  const evaluatorType = useEvaluatorStore((state) => state.evaluatorType);

  // For hub prompt selector
  const hubRef = useEvaluatorStore((state) => state.hubRef);
  const setHubRef = useEvaluatorStore((state) => state.setHubRef);
  const setEvaluatorType = useEvaluatorStore((state) => state.setEvaluatorType);
  const setFeedbackEditorType = useEvaluatorStore(
    (state) => state.setFeedbackEditorType
  );

  const isDataset = !!props.referenceOutput;

  const { manifest, disabledMessageTypes, isHubManifestLoading } =
    useParsedManifest(isDataset);

  // For rich text editor variable mapping dropdown
  const variableMappingProps = useVariableMappingProps();

  useEffect(() => {
    return () => {
      reset();
    };
  }, [reset]);

  const renderManifest = () => {
    if (evaluatorType === 'hub' && isHubManifestLoading) {
      return (
        <div className="pt-4">
          <Skeleton lines={3} />
        </div>
      );
    }

    return (
      <Tooltip
        title={
          evaluatorType === 'hub' && (
            <div className="max-w-[350px]">
              <div className="text-sm font-semibold">
                Saved prompts cannot be edited in this view
              </div>
              <div className="text-sm font-normal text-gray-300 dark:text-gray-600">
                To make changes, go to the <EvaluatorHubPromptPlaygroundLink />.
                Your latest changes will appear here automatically.
              </div>
            </div>
          )
        }
        placement="top"
      >
        <div>
          <Manifest
            // Need to rerender manifeset when fewShotEnabled changes, it inserts or removes {{few_shot_examples}} variable
            key={`${evaluatorType}-${hubRef}-${fewShotEnabled}-${promptTemplateFormat}`}
            variant="evaluator"
            value={manifest.kwargs.first}
            onChange={async (promptManifest) => {
              if (evaluatorType === 'hub') {
                return;
              }
              try {
                const messagesTuple = convertSerializedMessagesToMessageTuple(
                  promptManifest.kwargs.messages
                );

                setPromptMessages(messagesTuple);
                updateMappingWithPromptManifest(promptManifest);
              } catch (error) {
                // quite often as users type, the prompt manifest is invalid
                // so we catch the error and do nothing
              }
            }}
            hideTemplateFormatSelector
            outputSchemaAsModal
            minimizedSchema={true}
            disabledMessageTypes={disabledMessageTypes}
            disabledMessageCapabilities={[
              MessageCapability.IMAGE,
              MessageCapability.CANVAS,
            ]}
            isAllCollapsed={false}
            readOnly={evaluatorType === 'hub'}
            options={{}}
            onOptionsChange={() => void 0}
            variableMappingProps={variableMappingProps}
          />
        </div>
      </Tooltip>
    );
  };

  return (
    <div>
      <div className="pb-2">{props.renderEvaluatorName?.()}</div>
      {props.backfillComponent && props.backfillComponent}
      <div className="flex flex-col gap-6">
        <div className="ml-5">
          <label className="block pb-1.5 font-semibold">Prompt & Model</label>

          <p className="pb-3 text-sm text-secondary">
            Create your evaluator by defining the criteria you want to evaluate
            in your prompt. Map parts of your{' '}
            {isDataset ? 'Example' : 'Sample Run'} dynamically to your prompt
            using <span className="text-brand-green-400">{`{{variable}}`}</span>
            . Create your prompt below or use a saved prompt from Prompt Hub.
          </p>

          {props.showEvaluatorVersionWarning && (
            <div className="mb-2 flex items-center gap-1 rounded-md bg-warning-primary p-2">
              <span className="text-sm text-warning">
                Please update variable mappings to ensure compatibility with the
                latest system format.
              </span>
            </div>
          )}

          <div className="flex justify-between gap-2 pb-3">
            <div className="flex items-center gap-1">
              <PromptSelector
                selectedPromptIdentifier={hubRef ? hubRef : 'Use hub prompt'}
                tags={['StructuredPrompt']}
                showNewPromptLink={false}
                onChange={(promptNameAndCommit) => {
                  setHubRef(promptNameAndCommit);
                  setEvaluatorType('hub');
                  setFeedbackEditorType('schema');
                }}
                showCommitSelector={true}
                selectByLatest={true}
                emptyStateText="No structured prompts found."
                className="min-w-[50px] max-w-[200px] flex-shrink text-sm"
              />
              {evaluatorType === 'hub' && (
                <Tooltip title="Deselect hub prompt" placement="right">
                  <button
                    type="button"
                    className="my-1 flex-shrink-0 rounded-md p-1 text-secondary file:text-sm hover:bg-tertiary"
                    onClick={() => {
                      setHubRef('');
                      setEvaluatorType('prompt');
                      setFeedbackEditorType('pretty');
                    }}
                  >
                    <XIcon className="h-4 w-4" />
                  </button>
                </Tooltip>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Switch
                checked={preview}
                onChange={setPreview}
                label="Preview"
                size="md"
                labelClassName="text-sm"
                labelPosition="left"
              />
              <PromptSettingsDisplay
                variant="evaluator"
                setManifest={setModel}
                manifest={manifest}
              />
            </div>
          </div>
        </div>
        <div
          className={cn(
            (evaluatorType === 'hub' || preview) && 'ml-5',
            'max-h-[50vh] overflow-y-auto'
          )}
        >
          {preview ? (
            <PromptPreview
              {...props}
              manifest={manifest}
              evaluatorType={evaluatorType}
            />
          ) : (
            renderManifest()
          )}
        </div>
      </div>
      <div className="ml-5">
        <EvaluatorFewShotConfig
          fewShotDatasetId={props.fewShotDatasetId}
          isDataset={isDataset}
        />
      </div>
      <div className="ml-5 pb-12 pt-4">
        <EvaluatorFeedbackSchema isDataset={isDataset} />
      </div>
    </div>
  );
}

export function DatasetEvaluatorCrudForm(props: {
  datasetId: string;
  fewShotDatasetId?: string;
  evaluatorVersion?: number;
  isCreating?: boolean;
}) {
  const { input, output, referenceOutput, isLoading } =
    useDatasetInputOutputData(props.datasetId);
  const name = useEvaluatorStore((state) => state.name);
  const setName = useEvaluatorStore((state) => state.setName);

  const isEdited = useEvaluatorStore((state) => state.edited);
  const inputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    // Focus the name input after component mounts
    if (inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    }
  }, []);

  const showNameError = !name && isEdited;
  const renderEvaluatorName = () => {
    return (
      <div className="ml-5 flex flex-col gap-2">
        <label className="mb-2 font-semibold">Evaluator Name</label>
        <input
          ref={inputRef}
          autoFocus
          placeholder="eg. Correctness"
          className={cn(
            'rounded-md border border-secondary bg-background px-3 py-2 text-sm ring-0 ',
            showNameError
              ? 'border-error focus:border-error'
              : 'border-secondary hover:border-primary focus:border-brand'
          )}
          name="value"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        {showNameError && (
          <span className="text-sm text-error">Rule name is required</span>
        )}
      </div>
    );
  };
  const [applyToPastRuns, setApplyToPastRuns] = useState(false);
  const setBackfillFrom = useEvaluatorStore((state) => state.setBackfillFrom);
  const backfillFrom = useEvaluatorStore((state) => state.backfillFrom);
  const errors = useEvaluatorStore((state) => state.errors);

  const backfillComponent = (
    <>
      {!props.fewShotDatasetId && (
        <div className="ml-5 pb-6">
          <div className="flex flex-row items-center gap-2 pb-2">
            <div className="text-sm font-semibold">Apply to past runs</div>
            <Switch
              size="md"
              label=""
              checked={applyToPastRuns}
              onChange={setApplyToPastRuns}
            />
          </div>
          {applyToPastRuns && (
            <FormControl
              error={!!errors.backfillFrom}
              sx={{ display: 'flex', flexDirection: 'row', gap: '8px' }}
            >
              <BackfillFromFormLabel disabled={false} />
              <BackfillDateInput
                value={backfillFrom ?? undefined}
                onChange={(value) => {
                  setBackfillFrom(value);
                }}
                disabled={false}
                error={
                  errors.backfillFrom
                    ? { type: 'validate', message: errors.backfillFrom }
                    : undefined
                }
              />
            </FormControl>
          )}
        </div>
      )}
      {backfillFrom && !props.isCreating && (
        <div className="ml-5 pb-6">
          <FormControl
            sx={{ display: 'flex', flexDirection: 'row', gap: '8px' }}
          >
            <BackfillFromFormLabel disabled={true} />
            <BackfillDateInput
              value={backfillFrom}
              onChange={(value) => {
                setBackfillFrom(value);
              }}
              disabled={true}
            />
          </FormControl>
        </div>
      )}
    </>
  );

  return (
    <EvaluatorCrudForm
      input={input ?? {}}
      output={output ?? {}}
      referenceOutput={referenceOutput ?? {}}
      isLoading={isLoading}
      fewShotDatasetId={props.fewShotDatasetId}
      showEvaluatorVersionWarning={
        Boolean(props.evaluatorVersion) &&
        props.evaluatorVersion !== CURRENT_RULE_EVALUATOR_VERSION
      }
      renderEvaluatorName={renderEvaluatorName}
      isCreating={props.isCreating}
      backfillComponent={backfillComponent}
    />
  );
}

export function SessionEvaluatorCrudForm(props: {
  sessionId: string;
  filters: SearchModel;
  fewShotDatasetId?: string;
  evaluatorVersion?: number;
  isCreating?: boolean;
}) {
  const { input, output, isLoading } = useSessionInputOutputData({
    sessionId: props.sessionId,
    filters: props.filters,
  });

  return (
    <EvaluatorCrudForm
      input={input ?? {}}
      output={output ?? {}}
      isLoading={isLoading}
      fewShotDatasetId={props.fewShotDatasetId}
      showEvaluatorVersionWarning={
        Boolean(props.evaluatorVersion) &&
        props.evaluatorVersion !== CURRENT_RULE_EVALUATOR_VERSION
      }
      isCreating={props.isCreating}
    />
  );
}
