import { Edit03Icon } from '@langchain/untitled-ui-icons';
import { Checkbox } from '@mui/joy';

import { useEffect, useMemo, useRef, useState } from 'react';
import { Link } from 'react-router-dom';

import { RichTextEditor } from '@/components/RichTextEditor/RichTextEditor';
import SliderWithInput from '@/components/SliderWithInput';
import { useOrganizationId } from '@/hooks/useSwr';
import { appOrganizationPath } from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { useEvaluatorStore } from './store/evaluatorStore';
import { deserializeSchemaToFeedback } from './utils/serializeFeedbackUIToLC';
import { useParsedManifest } from './utils/useParsedManifest';
import {
  generateDefaultContent,
  useUpdateFewshotWithFeedbackName,
} from './utils/useUpdateFewshotWithFeedbackName';

// Constants for slider
const MIN_FEW_SHOT_EXAMPLES = 1;
const MAX_FEW_SHOT_EXAMPLES = 10;

export function EvaluatorFewShotConfig(props: {
  fewShotDatasetId?: string;
  isDataset: boolean;
}) {
  // Access each state item separately to avoid unnecessary re-renders
  const setFewShotEnabled = useEvaluatorStore(
    (state) => state.setFewShotEnabled
  );
  const setFewShotContent = useEvaluatorStore(
    (state) => state.setFewShotContent
  );
  const setFewShotExamplesMap = useEvaluatorStore(
    (state) => state.setFewShotExamplesMap
  );
  const setNumFewShotExamples = useEvaluatorStore(
    (state) => state.setNumFewShotExamples
  );

  const fewShotEnabled = useEvaluatorStore((state) => state.fewShotEnabled);
  const fewShotContent = useEvaluatorStore((state) => state.fewShotContent);

  const fewShotExamplesMap = useEvaluatorStore(
    (state) => state.fewShotExamplesMap
  );
  const numFewShotExamples = useEvaluatorStore(
    (state) => state.numFewShotExamples
  );

  const variableMapping = useEvaluatorStore((state) => state.variableMapping);

  const templateFormat = useEvaluatorStore(
    (state) => state.promptTemplateFormat
  );
  const setTemplateFormat = useEvaluatorStore(
    (state) => state.setPromptTemplateFormat
  );

  const organizationId = useOrganizationId();

  const hasNoVariableMappings =
    !variableMapping || Object.keys(variableMapping).length === 0;

  const { manifestStructuredSchema } = useParsedManifest(props.isDataset);
  const deserializedSchema = deserializeSchemaToFeedback(
    manifestStructuredSchema
  );
  const hasReasoning = deserializedSchema?.includeReasoning;
  const hasMultipleFeedbackProperties =
    manifestStructuredSchema?.properties &&
    Object.keys(manifestStructuredSchema.properties).filter(
      (key) => key !== 'comment'
    ).length > 1;
  const feedbackName = deserializedSchema?.name ?? 'score';

  const refreshContentRef = useRef<(content: string) => void | null>(
    () => null
  );
  const debouncedFeedbackName = useUpdateFewshotWithFeedbackName(
    props.isDataset,
    refreshContentRef
  );
  const [lastFewShotDefaultContent, setLastFewShotDefaultContent] =
    useState<string>();

  const resetFewShotContent = () => {
    const { content, mapping } = generateDefaultContent(
      variableMapping,
      feedbackName,
      hasReasoning
    );

    setLastFewShotDefaultContent(content);
    setFewShotContent(content);
    setFewShotExamplesMap(mapping);
  };

  // Handler for enabling few-shot with default content
  const handleEnableFewShot = (checked: boolean) => {
    setFewShotEnabled(checked);
    if (checked) {
      resetFewShotContent();
    } else {
      setLastFewShotDefaultContent('');
      setFewShotContent('');
      setFewShotExamplesMap({});
    }
  };

  const hasNoUndefinedValues = (obj) => {
    return Object.entries(obj)
      .filter(([key]) => key !== 'few_shot_examples')
      .every(([, value]) => value !== undefined);
  };

  useEffect(() => {
    if (
      fewShotContent === lastFewShotDefaultContent &&
      hasNoUndefinedValues(variableMapping)
    ) {
      const { content, mapping } = generateDefaultContent(
        variableMapping,
        feedbackName,
        hasReasoning
      );

      // Create updated mapping that includes the feedback variable
      const updatedMapping = {
        ...mapping,
        [feedbackName]: feedbackName, // Add the feedback name as a valid variable
        few_shot_explanation: 'few_shot_explanation', // Ensure this is always mapped
      };

      // Update mapping first, then content
      setFewShotExamplesMap(updatedMapping);
      setLastFewShotDefaultContent(content);
      setFewShotContent(content);

      // Slight delay to ensure mapping is set before refreshing content
      setTimeout(() => {
        refreshContentRef.current?.(content);
      }, 0);
    }
  }, [variableMapping, feedbackName, hasReasoning]);

  const variableMappingProps = useMemo(() => {
    return {
      variableMappings: fewShotExamplesMap,
      availablePaths: Object.values(variableMapping ?? {}).filter(Boolean),
      setVariableMappings: setFewShotExamplesMap,
      // There's no active variable to set for few-shot mapping
      setActiveVariable: () => void 0,
      uneditableVariables: ['few_shot_explanation', debouncedFeedbackName],
    };
  }, [
    fewShotExamplesMap,
    variableMapping,
    setFewShotExamplesMap,
    debouncedFeedbackName,
  ]);

  const renderTemplateDescription = () => {
    return (
      <>
        {' '}
        Examples{' '}
        {props.fewShotDatasetId ? (
          <>
            from this{' '}
            <Link
              target="_blank"
              to={`/${appOrganizationPath}/${organizationId}/datasets/${props.fewShotDatasetId}`}
              className="font-semibold text-brand-green-400 underline"
            >
              dataset
            </Link>{' '}
          </>
        ) : (
          'will'
        )}{' '}
        be inserted at{' '}
        <span className="text-brand-green-400">{`{{few_shot_examples}}`}</span>{' '}
        with the template below
      </>
    );
  };

  const isDisabled =
    hasNoVariableMappings ||
    hasMultipleFeedbackProperties ||
    templateFormat === 'f-string';

  return (
    <div
      className={cn(
        'flex flex-col gap-3 rounded-md border border-secondary p-4',
        fewShotEnabled ? 'focus-within:bg-secondary' : 'hover:border-brand',
        isDisabled
          ? 'cursor-not-allowed opacity-80 hover:border-secondary'
          : 'cursor-pointer'
      )}
    >
      <div
        className="flex justify-between"
        onClick={() => {
          if (!isDisabled) {
            handleEnableFewShot(!fewShotEnabled);
          }
        }}
      >
        <div className="flex items-start">
          <div className="flex items-center justify-center rounded-full bg-brand-green-50 p-2">
            <Edit03Icon className="h-4 w-4 text-brand-green-600" />
          </div>
          <div className="flex flex-col gap-1">
            <label className="ml-2 text-sm font-semibold">
              Improve evaluator accuracy using few-shot examples
            </label>
            <p className="ml-2 text-sm text-secondary">
              <Link
                target="_blank"
                to="https://docs.smith.langchain.com/evaluation/how_to_guides/create_few_shot_evaluators#make-corrections"
                className="underline"
                rel="noopener noreferrer"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                Corrections
              </Link>{' '}
              made to this evaluator's results will serve as few-shot examples
              in subsequent evaluator runs.
              {renderTemplateDescription()}
            </p>
            <p
              className={cn(
                'ml-2 text-sm',
                hasNoVariableMappings ? 'text-error' : 'text-warning'
              )}
            >
              {templateFormat === 'f-string' ? (
                <>
                  Few-shot examples don't support f-string templates.
                  <button
                    type="button"
                    className="ml-2 text-sm text-warning underline"
                    onClick={() => setTemplateFormat('mustache')}
                  >
                    Use mustache
                  </button>
                </>
              ) : hasMultipleFeedbackProperties ? (
                'Few-shot examples are not supported for multiple feedback keys.'
              ) : hasNoVariableMappings ? (
                'At least 1 variable is required in the prompt.'
              ) : null}
            </p>
          </div>
        </div>
        <Checkbox
          sx={{
            justifySelf: 'end',
          }}
          checked={fewShotEnabled}
          onChange={(event) => handleEnableFewShot(event.target.checked)}
          component="button"
          disabled={isDisabled}
        />
      </div>

      {fewShotEnabled && !hasNoVariableMappings && (
        <div className="mt-2 flex flex-col gap-4">
          <div
            className={cn(
              'rounded-md border border-transparent bg-secondary p-3',
              'focus-within:border-brand-green-400 focus-within:border focus-within:bg-background'
            )}
          >
            <RichTextEditor
              initialValue={fewShotContent}
              onChange={(content) => {
                if (typeof content === 'string') {
                  setFewShotContent(content);
                }
              }}
              variant="template"
              smallText
              variableMappingProps={variableMappingProps}
              hideSyntaxHighlighting={false}
              templateFormat="mustache"
              refreshContentRef={refreshContentRef}
            />
          </div>

          <div className="px-1">
            <SliderWithInput
              title="Number of few-shot examples"
              value={numFewShotExamples}
              min={MIN_FEW_SHOT_EXAMPLES}
              max={MAX_FEW_SHOT_EXAMPLES}
              step={1}
              onChange={(value) => setNumFewShotExamples(value ?? 5)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
