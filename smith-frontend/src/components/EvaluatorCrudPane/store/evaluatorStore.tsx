import { SerializedConstructor } from '@langchain/core/load/serializable';
import { TemplateFormat } from '@langchain/core/prompts';

import { create } from 'zustand';

import {
  defaultEvaluatorOutputSchema,
  emptyStructuredPromptManifest,
} from '@/Pages/HubPlayground/HubPlayground.utils';
import { defaultChatModelManifest } from '@/Pages/Playground/utils/Playground.utils';
import {
  convertSerializedMessagesToMessageTuple,
  parseOwnerRepoCommit,
} from '@/Pages/Settings/RulesEvaluatorPane.utils';
import { validateBackfillDate } from '@/Pages/Settings/utils/helpers';
import {
  MessageTuple,
  StructuredEvaluatorSchema,
  StructuredEvaluatorSchemaHub,
  StructuredEvaluatorSchemaPrompt,
} from '@/types/schema';
import { toggleTemplateFormat } from '@/utils/template';

import { PROMPT_EXCLUDED_VARIABLES } from '../constants';
import {
  hasFewShotVariable,
  insertFewShotExamples,
  insertFewShotVariable,
  processFewShotExamples,
  removeFewShotVariable,
} from '../utils/fewShotExamplesUtils';
import { getUpdatedMappingFromManifest } from '../utils/getUpdatedMappingFromManifest';
import { SchemaDefinition } from '../utils/serializeFeedbackUIToLC';

export type EvaluatorType = 'hub' | 'prompt';
export type FeedbackEditorType = 'pretty' | 'schema' | 'json';

// Type guard to determine which type of evaluator we're dealing with
export const isHubEvaluator = (
  evaluator: StructuredEvaluatorSchema
): evaluator is StructuredEvaluatorSchemaHub => {
  return 'hub_ref' in evaluator && evaluator.hub_ref != null;
};

// State interface for the evaluator store
interface EvaluatorState {
  // Common discriminator
  evaluatorType: EvaluatorType;

  // Hub evaluator fields
  hubRef: string;

  // Prompt evaluator fields
  promptMessages: MessageTuple[] | null;
  feedbackSchema: SchemaDefinition | null;
  promptTemplateFormat: TemplateFormat;

  // common
  variableMapping: Record<string, string> | null;
  model: SerializedConstructor | null;

  // Backfill info
  backfillFrom: string | null;

  // Few-shot configuration
  fewShotEnabled: boolean;
  fewShotContent: string;
  fewShotExamplesMap: Record<string, any>;
  numFewShotExamples: number;

  // State management
  isValid: boolean;
  errors: Record<string, string>;
  edited: boolean;
  feedbackEditorType: FeedbackEditorType;
  setFeedbackEditorType: (type: FeedbackEditorType) => void;
  // Hub evaluator actions
  setHubRef: (ref: string) => void;

  // Prompt evaluator actions
  setPromptMessages: (messages: MessageTuple[]) => void;

  setFeedbackSchema: (schema: SchemaDefinition) => void;
  setPromptTemplateFormat: (format: TemplateFormat | undefined) => void;

  setBackfillFrom: (backfillFrom: string | null) => void;

  // Few-shot configuration actions
  setFewShotEnabled: (enabled: boolean) => void;
  setFewShotContent: (content: string) => void;
  setFewShotExamplesMap: (map: Record<string, any>) => void;
  setNumFewShotExamples: (num: number) => void;

  // common actions
  setVariableMapping: (mapping: Record<string, string>) => void;
  updateMappingWithPromptManifest: (manifest: SerializedConstructor) => void;
  setModel: (model: SerializedConstructor) => void;

  // Import/export methods
  importEvaluator: (
    evaluator?: StructuredEvaluatorSchema,
    ruleSource?: 'session' | 'dataset',
    backfill_from?: string | null
  ) => void;
  exportEvaluator: () => StructuredEvaluatorSchema | null;

  // Validation
  validateEvaluator: () => boolean;

  // Reset method
  reset: () => void;

  // Name
  name: string;
  setName: (name: string, isReset?: boolean) => void;

  // Active variable
  activeVariable: string | null;
  setActiveVariable: (variable: string) => void;
  availablePaths: string[] | null;
  setAvailablePaths: (paths: string[]) => void;

  // Evaluator type
  setEvaluatorType: (type: EvaluatorType) => void;
}
// Default state for the store
const defaultState = {
  evaluatorType: 'prompt' as EvaluatorType,

  // Hub evaluator defaults
  hubRef: '',

  // Prompt evaluator defaults
  promptMessages: null,
  feedbackSchema: null,
  promptTemplateFormat: 'mustache' as TemplateFormat,

  backfillFrom: null,

  // common
  variableMapping: null,
  model: null,

  // Few-shot configuration defaults
  fewShotEnabled: false,
  fewShotContent: '',
  fewShotExamplesMap: {},
  numFewShotExamples: 5,

  // State management defaults
  isValid: false,
  errors: {},
  feedbackEditorType: 'pretty' as FeedbackEditorType,
  edited: false,
  activeVariable: null,
  availablePaths: null,
  // Name
  name: '',
};

const updateFewShotExamplesMap = (
  set: (state: Partial<EvaluatorState>) => void,
  get: () => EvaluatorState,
  newMapping: Record<string, string>,
  originalMapping: Record<string, string>
) => {
  const { fewShotEnabled, fewShotExamplesMap } = get();

  if (fewShotEnabled && Object.keys(fewShotExamplesMap).length > 0) {
    const updatedFewShotExamplesMap = { ...fewShotExamplesMap };

    Object.entries(newMapping).forEach(([key, value]) => {
      // Only update if both the key exists AND the original values match
      // This preserves manually changed few-shot mappings
      if (
        key in updatedFewShotExamplesMap &&
        originalMapping[key] === fewShotExamplesMap[key]
      ) {
        updatedFewShotExamplesMap[key] = value;
      }
    });

    set({ fewShotExamplesMap: updatedFewShotExamplesMap });
  }
};

// Create the Zustand store
export const useEvaluatorStore = create<EvaluatorState>()((set, get) => ({
  ...defaultState,

  // Hub evaluator actions
  setHubRef: (ref) => {
    set({ hubRef: ref, edited: true });
    get().validateEvaluator();
  },
  setVariableMapping: (mapping) => {
    // Filter out excluded variables from the mapping
    const filteredMapping = Object.entries(mapping).reduce(
      (acc, [key, value]) => {
        if (!PROMPT_EXCLUDED_VARIABLES.includes(key)) {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, string>
    );
    const originalMapping = get().variableMapping ?? {};
    set({ variableMapping: filteredMapping, edited: true });
    updateFewShotExamplesMap(set, get, filteredMapping, originalMapping);
    get().validateEvaluator();
  },
  updateMappingWithPromptManifest: (manifest) => {
    const currentVariableMapping = get().variableMapping ?? {};
    const newMapping = getUpdatedMappingFromManifest(
      manifest,
      currentVariableMapping
    );
    if (newMapping !== currentVariableMapping) {
      set({ variableMapping: newMapping });
      updateFewShotExamplesMap(set, get, newMapping, currentVariableMapping);
      get().validateEvaluator();
    }
  },

  // Common model action
  setModel: (model) => {
    set({ model, edited: true });
    get().validateEvaluator();
  },

  setBackfillFrom: (backfillFrom) => {
    set({ backfillFrom, edited: true });
  },

  setPromptMessages: (messages) => {
    set({ promptMessages: messages, edited: true });
    get().validateEvaluator();
  },
  setFeedbackSchema: (schema) => {
    set({ feedbackSchema: schema, edited: true });
    get().validateEvaluator();
  },
  setPromptTemplateFormat: (format) => {
    // If switching from f-string to mustache, update prompt variables
    if (format !== get().promptTemplateFormat) {
      const messages = get().promptMessages?.map((message) => {
        if (typeof message[1] === 'string') {
          // toggle between mustache and f-string formats
          return [
            message[0],
            toggleTemplateFormat(message[1], get().promptTemplateFormat),
          ] as MessageTuple;
        }
        return message;
      });
      set({ promptMessages: messages });
      set({ promptTemplateFormat: format, edited: true });
      get().validateEvaluator();
    }
  },

  // Import method
  importEvaluator: (
    evaluator,
    ruleSource: 'session' | 'dataset' = 'session',
    backfill_from?: string | null
  ) => {
    // Reset state first
    get().reset();

    if (evaluator && isHubEvaluator(evaluator)) {
      // It's a hub evaluator
      set({
        evaluatorType: 'hub',
        model: evaluator.model ?? defaultChatModelManifest,
        hubRef: evaluator.hub_ref,
        variableMapping: evaluator.variable_mapping || {},
        backfillFrom: backfill_from ?? null,
        edited: false,
        feedbackEditorType: 'schema',
      });
    } else {
      const manifestType =
        ruleSource === 'dataset' ? 'dataset-eval' : 'session-eval';

      // Process prompt messages to extract few-shot content if present
      let promptMessages =
        evaluator?.prompt ??
        convertSerializedMessagesToMessageTuple(
          emptyStructuredPromptManifest(
            manifestType,
            null,
            evaluator?.template_format ?? 'mustache'
          ).kwargs.messages
        );

      let fewShotContent = '';
      let fewShotEnabled = false;

      // Check if messages contain {{few_shot_examples}} placeholder
      const hasFewShotPlaceholder = hasFewShotVariable(promptMessages);

      // Extract few-shot content from prompt messages if present
      if (promptMessages) {
        const { processedMessages, fewShotContent: extractedContent } =
          processFewShotExamples(promptMessages);
        promptMessages = processedMessages;
        if (extractedContent) {
          fewShotContent = extractedContent;
          fewShotEnabled = true;
        } else if (hasFewShotPlaceholder) {
          // If there's a placeholder but no content, still enable few-shot
          fewShotEnabled = true;
        }
      }

      // Determine variable mapping
      const variableMapping = evaluator?.variable_mapping || {
        input: 'input',
        output: 'output',
        ...(ruleSource === 'dataset' && { reference: 'referenceOutput' }),
      };

      // Create few-shot examples map based on variable mapping
      const fewShotExamplesMap = fewShotContent ? variableMapping : {};

      const hasMultipleFeedbackProperties =
        evaluator?.schema?.properties &&
        Object.keys(evaluator.schema.properties).filter(
          (key) => key !== 'comment'
        ).length > 1;
      // It's a prompt evaluator
      set({
        evaluatorType: 'prompt',
        model: evaluator?.model ?? defaultChatModelManifest,
        promptMessages,
        feedbackSchema:
          evaluator?.schema ?? defaultEvaluatorOutputSchema(manifestType),
        promptTemplateFormat: evaluator?.template_format ?? 'mustache',
        variableMapping,
        backfillFrom: backfill_from ?? null,
        edited: false,
        feedbackEditorType: hasMultipleFeedbackProperties ? 'schema' : 'pretty',
        // Set few-shot state based on extracted content
        fewShotEnabled,
        fewShotContent,
        fewShotExamplesMap,
      });
    }

    get().validateEvaluator();
  },

  // Export method
  exportEvaluator: () => {
    const state = get();

    if (!state.isValid) {
      throw new Error(Object.values(state.errors)[0]);
    }

    if (state.evaluatorType === 'hub') {
      if (!state.model) return null;

      const hubEvaluator: StructuredEvaluatorSchemaHub = {
        hub_ref: state.hubRef,
        model: state.model,
      };

      if (Object.keys(state?.variableMapping ?? {}).length > 0) {
        hubEvaluator.variable_mapping = state.variableMapping ?? {};
      }

      return hubEvaluator;
    } else {
      if (!state.model || !state.promptMessages || !state.feedbackSchema)
        return null;

      // Process messages to insert few-shot examples if enabled
      let processedPromptMessages = [...state.promptMessages];

      if (state.fewShotEnabled && state.fewShotContent) {
        // Insert few-shot examples into the messages
        processedPromptMessages = insertFewShotExamples(
          processedPromptMessages,
          state.fewShotContent
        );
      } else if (!state.fewShotEnabled) {
        // If few-shot is disabled, make sure to remove any {{few_shot_examples}} variables
        processedPromptMessages = removeFewShotVariable(
          processedPromptMessages
        );
      }

      const promptEvaluator: StructuredEvaluatorSchemaPrompt = {
        model: state.model,
        prompt: processedPromptMessages,
        schema: state.feedbackSchema,
      };

      if (state.promptTemplateFormat) {
        promptEvaluator.template_format = state.promptTemplateFormat;
      }

      if (Object.keys(state.variableMapping ?? {}).length > 0) {
        promptEvaluator.variable_mapping = state.variableMapping ?? {};
      }

      return promptEvaluator;
    }
  },

  // Validation method
  validateEvaluator: () => {
    const state = get();
    const errors: Record<string, string> = {};

    if (state.evaluatorType === 'hub') {
      // Validate hub evaluator
      if (!state.hubRef) {
        errors.hubRef = 'Hub reference is required';
      }

      if (!state.model) {
        errors.model = 'Model is required';
      }
    } else {
      // Validate prompt evaluator
      if (!state.model) {
        errors.model = 'Model is required';
      }

      if ((state.promptMessages ?? []).length === 0) {
        errors.promptMessages = 'At least one message is required';
      }

      if (
        !state.feedbackSchema ||
        Object.keys(state.feedbackSchema).length === 0
      ) {
        errors.promptSchema = 'Schema is required';
      }
    }

    if (Object.keys(state.variableMapping ?? {}).length === 0) {
      errors.variableMapping = 'At least one variable mapping is required';
    } else {
      // Check for undefined, null or empty string mapping values
      const unmappedVariables = Object.entries(state.variableMapping ?? {})
        .filter(
          ([key, value]) => !value && !PROMPT_EXCLUDED_VARIABLES.includes(key)
        )
        .map(([key]) => key);

      if (unmappedVariables.length > 0) {
        errors.variableMapping = `Some template variables are not mapped: ${unmappedVariables.join(
          ', '
        )}`;
      }
    }

    // Validate backfill date if present
    if (state.backfillFrom) {
      const validationResult = validateBackfillDate(state.backfillFrom);

      if (validationResult !== true) {
        errors.backfillFrom = validationResult as string;
      }
    }

    const isValid = Object.keys(errors).length === 0;

    set({ isValid, errors });

    return isValid;
  },

  // Reset method
  reset: () => {
    set(defaultState);
  },

  // Name
  setName: (name, isReset = false) => set({ name, edited: !isReset }),

  // Active variable
  setActiveVariable: (variable) => set({ activeVariable: variable }),

  // Available paths
  setAvailablePaths: (paths) => set({ availablePaths: paths }),

  // Feedback editor type
  setFeedbackEditorType: (type) => set({ feedbackEditorType: type }),

  // Evaluator type
  setEvaluatorType: (evaluatorType) => set({ evaluatorType }),

  // Few-shot configuration actions
  setFewShotEnabled: (enabled) => {
    const state = get();

    // Check if we need to modify the prompt messages
    if (state.promptMessages) {
      const updatedMessages = enabled
        ? insertFewShotVariable(state.promptMessages)
        : removeFewShotVariable(state.promptMessages);

      if (enabled && !state.fewShotContent) {
        // When enabling few-shot without content, initialize the examples map
        // based on variable mapping
        const variableMapping = state.variableMapping || {};
        const fewShotExamplesMap = {
          ...Object.entries(variableMapping).reduce((acc, [key, value]) => {
            acc[key] = value;
            return acc;
          }, {} as Record<string, string>),
        };

        set({
          fewShotEnabled: enabled,
          fewShotExamplesMap,
          promptMessages: updatedMessages,
          edited: true,
        });
      } else {
        set({
          fewShotEnabled: enabled,
          promptMessages: updatedMessages,
          edited: true,
        });
      }
    } else {
      set({ fewShotEnabled: enabled, edited: true });
    }

    get().validateEvaluator();
  },
  setFewShotContent: (content) => {
    set({ fewShotContent: content, edited: true });
    get().validateEvaluator();
  },
  setFewShotExamplesMap: (map) => {
    set({ fewShotExamplesMap: map, edited: true });
    get().validateEvaluator();
  },
  setNumFewShotExamples: (num) => {
    set({ numFewShotExamples: num, edited: true });
    get().validateEvaluator();
  },
}));

export const usePromptName = () => {
  const hubRef = useEvaluatorStore((state) => state.hubRef);
  const [, promptName] = hubRef ? parseOwnerRepoCommit(hubRef) : [];

  return promptName;
};
