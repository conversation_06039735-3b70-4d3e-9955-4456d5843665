import {
  ChevronDownIcon,
  InfoCircleIcon,
  Pencil01Icon,
  PlusIcon,
  Trash04Icon,
} from '@langchain/untitled-ui-icons';
import { Checkbox, Tooltip } from '@mui/joy';

import { dequal } from 'dequal';
import { produce } from 'immer';
import React, { useRef, useState } from 'react';

import { getColorByString } from '@/utils/get-color-by-string';
import { cn } from '@/utils/tailwind';

import { CodeDataEditor } from '../Code/Code';
import { EditInputFunctionParameters } from '../ExampleCrudPane/EditInputFunctions';
import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import { SchemaEditorVariant } from '../SchemaEditor/types';
import { EvaluatorHubPromptPlaygroundLink } from './HubPromptPlaygroundLink';
import {
  DEFAULT_BOOLEAN_FEEDBACK,
  DEFAULT_CATEGORICAL_FEEDBACK,
  DEFAULT_CONTINUOUS_FEEDBACK,
  FEEDBACK_TYPES,
} from './constants';
import { useEvaluatorStore } from './store/evaluatorStore';
import { EvaluatorFeedbackType } from './types';
import {
  ContinuousMetadata,
  FeedbackMetadata,
  SchemaDefinition,
  deserializeSchemaToFeedback,
  serializeFeedbackToSchema,
} from './utils/serializeFeedbackUIToLC';
import { useParsedManifest } from './utils/useParsedManifest';

const ScoreInput = ({
  value,
  onChange,
  placeholder,
  isContinuous = false,
  readOnly,
}: {
  value: string | number;
  onChange: (value: string | number) => void;
  placeholder: string;
  isContinuous?: boolean;
  readOnly?: boolean;
}) => (
  <div className="flex-none">
    <input
      type="text"
      className={cn(
        'w-full rounded-md border border-secondary bg-transparent p-1 text-sm',
        isContinuous ? 'max-w-[30px] text-center' : 'max-w-[100px] px-2',
        'focus:border-brand disabled:bg-secondary disabled:text-tertiary'
      )}
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={readOnly}
    />
  </div>
);

const DescriptionInput = ({
  value,
  onChange,
  placeholder,
  readOnly,
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  readOnly?: boolean;
}) => {
  // Auto-resize textarea based on content
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  React.useEffect(() => {
    if (textareaRef.current) {
      // Reset height to auto to get the correct scrollHeight
      textareaRef.current.style.height = 'auto';
      // Set the height to scrollHeight to fit the content
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [value]);

  return (
    <div className="flex-[2] pt-[1px]">
      <textarea
        ref={textareaRef}
        className={cn(
          'min-h-[18px] w-full resize-none rounded-md border-none bg-transparent',
          'px-2 py-1 text-xs leading-[18px] text-secondary outline-none',
          'h-fit disabled:bg-secondary disabled:text-tertiary'
        )}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        rows={2}
        disabled={readOnly}
      />
    </div>
  );
};

const SectionHeader = ({
  feedbackType,
  setFeedbackType,
  readOnly,
  rightContent,
}: {
  feedbackType: EvaluatorFeedbackType;
  setFeedbackType: (feedbackType: EvaluatorFeedbackType) => void;
  readOnly?: boolean;
  rightContent?: React.ReactNode;
}) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="-ml-2 flex items-center justify-between">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild disabled={readOnly}>
          <button
            type="button"
            className={cn(
              'flex items-center gap-2 rounded-md px-2 py-1 text-sm hover:bg-secondary',
              'disabled:bg-secondary disabled:text-tertiary'
            )}
          >
            <span>
              {feedbackType === 'categorical'
                ? 'Categories'
                : feedbackType === 'continuous'
                ? 'Score'
                : 'Boolean'}
            </span>
            <ChevronDownIcon className="h-4 w-4 text-secondary" />
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-40 p-0" align="start">
          <div className="flex flex-col rounded-md py-1">
            {FEEDBACK_TYPES.map(({ type, label }) => (
              <button
                key={type}
                type="button"
                className={cn(
                  'px-3 py-1.5 text-left text-sm hover:bg-secondary',
                  feedbackType === type && 'bg-secondary'
                )}
                onClick={() => {
                  setFeedbackType(type as EvaluatorFeedbackType);
                  setOpen(false);
                }}
              >
                {label}
              </button>
            ))}
          </div>
        </PopoverContent>
      </Popover>
      {rightContent}
    </div>
  );
};

const FeedbackNameTag = ({
  name,
  onChange,
  readOnly,
}: {
  name: string;
  onChange: (value: string) => void;
  readOnly?: boolean;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const toggleEditing = () => {
    if (!readOnly) {
      setIsEditing(!isEditing);
      // slightly confusing because we want to focus the
      // input when it's not editing and going into editing state
      // also need to do it on next frame because input is not enabled yet
      if (!isEditing) {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 0);
      }
    }
  };
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="flex items-center gap-2">
      <div
        className={cn(
          'flex items-center gap-2 whitespace-nowrap rounded-md border border-secondary bg-background px-2 py-[3px] text-xs',
          readOnly && 'bg-secondary text-tertiary'
        )}
      >
        <span
          className="h-2 w-2 min-w-2 shrink-0 rounded-sm"
          style={{ backgroundColor: getColorByString(name) }}
        />
        <input
          ref={inputRef}
          disabled={readOnly || !isEditing}
          type="text"
          className="w-[130px] border-none bg-transparent p-0 text-xs outline-none"
          placeholder="Unnamed Feedback"
          value={name}
          onChange={(e) => {
            onChange(e.target.value);
          }}
        />
      </div>
      {!readOnly && (
        <button
          type="button"
          className="rounded p-0.5 hover:bg-secondary"
          onClick={toggleEditing}
          title={isEditing ? 'Finish editing' : 'Edit name'}
        >
          <Pencil01Icon
            className={cn(
              'h-3.5 w-3.5',
              isEditing ? 'text-brand-green-400' : 'text-secondary'
            )}
          />
        </button>
      )}
    </div>
  );
};

export function EvaluatorFeedbackSchema(props: { isDataset: boolean }) {
  const feedbackEditorType = useEvaluatorStore(
    (state) => state.feedbackEditorType
  );
  const setFeedbackEditorType = useEvaluatorStore(
    (state) => state.setFeedbackEditorType
  );

  const { manifestStructuredSchema } = useParsedManifest(props.isDataset);
  const setFeedbackSchema = useEvaluatorStore(
    (state) => state.setFeedbackSchema
  );

  const evaluatorType = useEvaluatorStore((state) => state.evaluatorType);
  const readOnly = evaluatorType === 'hub';

  const renderFeedbackEditor = () => {
    if (!manifestStructuredSchema) {
      return null;
    }
    if (feedbackEditorType === 'pretty') {
      return (
        <FeedbackEditor
          readOnly={readOnly}
          feedbackSchema={manifestStructuredSchema}
          setFeedbackSchema={setFeedbackSchema}
        />
      );
    }
    if (feedbackEditorType === 'json') {
      return (
        <div className="relative rounded-md border border-secondary">
          <button
            type="button"
            className={cn(
              'absolute right-4 top-4 z-10 flex flex-none items-center gap-1 self-start whitespace-nowrap rounded-md border border-secondary bg-background px-2.5 py-[3px] text-sm font-medium hover:bg-secondary',
              'text-sm disabled:bg-secondary disabled:text-tertiary'
            )}
            onClick={() => setFeedbackEditorType('pretty')}
          >
            Editor
          </button>

          <CodeDataEditor
            value={manifestStructuredSchema}
            onChange={(value) => {
              if (dequal(value, manifestStructuredSchema)) {
                return;
              }
              setFeedbackSchema(value as SchemaDefinition);
            }}
            language="json"
            readOnly={readOnly}
          />
        </div>
      );
    }
    if (feedbackEditorType === 'schema') {
      return (
        <EditInputFunctionParameters
          variant={SchemaEditorVariant.EVALUATOR}
          value={manifestStructuredSchema}
          onChange={(value) => setFeedbackSchema(value as SchemaDefinition)}
          hideTitle={!manifestStructuredSchema?.title}
          readOnly={readOnly}
          showBorder={true}
        />
      );
    }
    return null;
  };

  return (
    <div className="flex flex-col gap-2">
      <label className="flex items-center gap-1 font-semibold">
        Feedback configuration{' '}
      </label>
      {evaluatorType === 'hub' ? (
        <div className="mb-3 text-sm text-secondary">
          To edit feedback configuration, go to the{' '}
          <EvaluatorHubPromptPlaygroundLink />
        </div>
      ) : (
        <div className="mb-3 text-sm text-secondary">
          Define your evaluation criteria. Describe what you're measuring, then
          select a response format. This configuration structures how your
          evaluation results are returned.
        </div>
      )}

      {renderFeedbackEditor()}
    </div>
  );
}

// Categorical Feedback Editor Component
const CategoricalFeedbackEditor = ({
  readOnly,
  metadata,
  setMetadata,
}: {
  metadata: Extract<FeedbackMetadata, { type: 'categorical' }>;
  setMetadata: (metadata: FeedbackMetadata) => void;
  readOnly?: boolean;
}) => {
  const removeCategory = (index: number) => {
    setMetadata(
      produce(metadata, (draft) => {
        draft.categoricalConfig.categories.splice(index, 1);
      })
    );
  };

  const updateCategory = (
    index: number,
    field: 'category' | 'description',
    value: string
  ) => {
    setMetadata(
      produce(metadata, (draft) => {
        draft.categoricalConfig.categories[index][field] = value;
      })
    );
  };

  return (
    <div className="flex flex-col items-stretch pt-2">
      {metadata.categoricalConfig.categories.length === 0 && (
        <div className="pb-4 text-sm text-secondary">
          No categories defined. Add a category to get started.
        </div>
      )}

      {metadata.categoricalConfig.categories.map((category, index) => (
        <div key={index} className="flex w-full gap-2 rounded-md">
          <ScoreInput
            readOnly={readOnly}
            value={category.category}
            onChange={(value) =>
              updateCategory(index, 'category', value as string)
            }
            placeholder="Name"
          />
          <DescriptionInput
            readOnly={readOnly}
            value={category.description}
            onChange={(value) => updateCategory(index, 'description', value)}
            placeholder="Describe what this category represents..."
          />
          <div className="flex-none">
            <button
              type="button"
              className="p-1 text-secondary hover:bg-secondary"
              onClick={() => removeCategory(index)}
            >
              <Trash04Icon className="h-4 w-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

// Continuous Feedback Editor Component
const ContinuousFeedbackEditor = ({
  readOnly,
  metadata,
  setMetadata,
}: {
  metadata: Extract<FeedbackMetadata, { type: 'continuous' }>;
  setMetadata: (metadata: FeedbackMetadata) => void;
  readOnly?: boolean;
}) => {
  // Update continuous feedback settings
  const updateContinuous = (
    field: keyof ContinuousMetadata,
    value: string | number
  ) => {
    // Convert string values to numbers for min/max fields
    const processedValue =
      field === 'min' || field === 'max'
        ? typeof value === 'string'
          ? Number(value)
          : value
        : value;

    setMetadata({
      ...metadata,
      continuousConfig: {
        ...metadata.continuousConfig,
        [field]: processedValue,
      },
    });
  };

  return (
    <div className="flex flex-col items-stretch pt-2">
      <div className="flex w-full gap-2 rounded-md">
        <ScoreInput
          readOnly={readOnly}
          value={metadata.continuousConfig.min}
          onChange={(value) => updateContinuous('min', value)}
          placeholder="Min"
          isContinuous={true}
        />
        <DescriptionInput
          readOnly={readOnly}
          value={metadata.continuousConfig.minDescription}
          onChange={(value) => updateContinuous('minDescription', value)}
          placeholder="Description for minimum value..."
        />
      </div>

      <div className="flex w-full gap-2 rounded-md">
        <ScoreInput
          readOnly={readOnly}
          value={metadata.continuousConfig.max}
          onChange={(value) => updateContinuous('max', value)}
          placeholder="Max"
          isContinuous={true}
        />
        <DescriptionInput
          readOnly={readOnly}
          value={metadata.continuousConfig.maxDescription}
          onChange={(value) => updateContinuous('maxDescription', value)}
          placeholder="Description for maximum value..."
        />
      </div>
    </div>
  );
};

const FeedbackEditor = ({
  readOnly,
  setFeedbackSchema,
  feedbackSchema,
}: {
  readOnly?: boolean;
  setFeedbackSchema: (schema: SchemaDefinition) => void;
  feedbackSchema: SchemaDefinition;
}) => {
  const feedbackEditorType = useEvaluatorStore(
    (state) => state.feedbackEditorType
  );
  const setFeedbackEditorType = useEvaluatorStore(
    (state) => state.setFeedbackEditorType
  );

  const defaultFeedback = deserializeSchemaToFeedback(feedbackSchema);
  const [name, setName] = useState<string>(defaultFeedback?.name ?? '');
  const [description, setDescription] = useState<string>(
    defaultFeedback?.description ?? ''
  );
  const [metadata, setMetadata] = useState<FeedbackMetadata>(
    defaultFeedback?.metadata ?? DEFAULT_BOOLEAN_FEEDBACK
  );
  const [includeReasoning, setIncludeReasoning] = useState<boolean>(
    defaultFeedback?.includeReasoning ?? false
  );

  const setSerializedSchema = (
    name: string,
    description: string,
    metadata: FeedbackMetadata,
    includeReasoning: boolean
  ) => {
    setFeedbackSchema(
      serializeFeedbackToSchema(name, description, metadata, includeReasoning)
    );
  };

  const handleNameChange = (name: string) => {
    setName(name);
    if (name !== 'comment') {
      setSerializedSchema(name, description, metadata, includeReasoning);
    }
  };

  const handleDescriptionChange = (description: string) => {
    setDescription(description);
    setSerializedSchema(name, description, metadata, includeReasoning);
  };

  const handleMetadataChange = (newMetadata: FeedbackMetadata) => {
    setMetadata(newMetadata);
    setSerializedSchema(name, description, newMetadata, includeReasoning);
  };

  const handleFeedbackTypeChange = (type: EvaluatorFeedbackType) => {
    if (type === 'categorical') {
      handleMetadataChange(DEFAULT_CATEGORICAL_FEEDBACK);
    } else if (type === 'continuous') {
      handleMetadataChange(DEFAULT_CONTINUOUS_FEEDBACK);
    } else if (type === 'boolean') {
      handleMetadataChange(DEFAULT_BOOLEAN_FEEDBACK);
    }
  };

  const handleIncludeReasoningChange = (includeReasoning: boolean) => {
    setIncludeReasoning(includeReasoning);
    setSerializedSchema(name, description, metadata, includeReasoning);
  };

  const addCategory = () => {
    if (metadata.type === 'categorical') {
      setMetadata(
        produce(metadata, (draft) => {
          draft.categoricalConfig.categories.push({
            category: `category`,
            description: '',
          });
        })
      );
    }
  };
  return (
    <div className="flex flex-col rounded-xl border border-secondary p-4">
      <div className="flex flex-col pb-3">
        <div className="flex w-full justify-between">
          <div className="flex">
            <FeedbackNameTag
              name={name}
              onChange={handleNameChange}
              readOnly={readOnly}
            />
          </div>

          <div className="flex items-center justify-end gap-4">
            <div className="flex items-center gap-1">
              <Checkbox
                disabled={readOnly || name === 'comment'}
                checked={includeReasoning}
                onChange={(e) => handleIncludeReasoningChange(e.target.checked)}
                label="Include reasoning"
                size="sm"
              />

              <Tooltip
                sx={{
                  maxWidth: '250px',
                }}
                title={
                  'Evaluator will provide a detailed explanation of how it arrived at this score'
                }
              >
                <div className="text-secondary">
                  <InfoCircleIcon className="h-4 w-4" />
                </div>
              </Tooltip>
            </div>
            {feedbackEditorType !== 'schema' && (
              <button
                type="button"
                className={cn(
                  'flex flex-none items-center gap-1 self-start whitespace-nowrap rounded-md border border-secondary bg-background px-2.5 py-[3px] text-sm font-medium hover:bg-secondary',
                  'text-sm disabled:bg-secondary disabled:text-tertiary'
                )}
                onClick={() => setFeedbackEditorType('json')}
              >
                {feedbackEditorType === 'json' ? 'Editor' : 'Advanced'}
              </button>
            )}
          </div>
        </div>
        {name === 'comment' && (
          <span className="mt-1 text-xs text-error">
            Feedback name cannot be reserved keyword "comment"
          </span>
        )}
      </div>

      {/* Basic Information */}

      <textarea
        disabled={readOnly}
        className={cn(
          'w-full resize-none overflow-hidden border-none bg-transparent p-0',
          'rounded-md text-xs font-normal leading-[18px] outline-none',
          'mb-2 disabled:bg-secondary disabled:text-tertiary'
        )}
        placeholder="Feedback description"
        value={description}
        onChange={(e) => handleDescriptionChange(e.target.value)}
      />

      <SectionHeader
        readOnly={readOnly}
        feedbackType={metadata.type}
        setFeedbackType={handleFeedbackTypeChange}
        rightContent={
          metadata.type === 'categorical' && (
            <button
              type="button"
              className={cn(
                'flex flex-none items-center gap-1 self-start whitespace-nowrap rounded-md border border-secondary bg-background px-2.5 py-[3px] text-sm font-medium hover:bg-secondary',
                'disabled:bg-secondary disabled:text-tertiary'
              )}
              disabled={readOnly}
              onClick={addCategory}
            >
              <PlusIcon className="h-4 w-4" />
              Category
            </button>
          )
        }
      />

      {/* Render appropriate editor based on feedback type */}
      {metadata.type === 'categorical' && (
        <CategoricalFeedbackEditor
          readOnly={readOnly}
          metadata={metadata}
          setMetadata={handleMetadataChange}
        />
      )}

      {metadata.type === 'continuous' && (
        <ContinuousFeedbackEditor
          readOnly={readOnly}
          metadata={metadata}
          setMetadata={handleMetadataChange}
        />
      )}

      {metadata.type === 'boolean' && (
        <p className="text-xs text-secondary">
          The evaluator will provide a true/false response based on the feedback
          criteria.
        </p>
      )}
    </div>
  );
};
