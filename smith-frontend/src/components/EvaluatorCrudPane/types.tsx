import {
  DatasetRuleMutateBody,
  EvaluatorRuleSchema,
  StructuredEvaluatorSchema,
} from '@/types/schema';

import { SearchModel } from '../RunsTable/types';

export type CommonProps = {
  evaluatorVersion: number;
};

export type DatasetEvaluatorCrudPaneProps = CommonProps & {
  evaluatorRule?: EvaluatorRuleSchema | DatasetRuleMutateBody;
  datasetId: string;
  onRuleSaved: (ruleId: string) => void;
  open: boolean;
  setOpen: (open: boolean) => void;
};

export type SessionEvaluatorCrudPaneProps = CommonProps & {
  sessionId: string;
  filters?: SearchModel;

  open: boolean;
  setOpen: (open: boolean) => void;
  onEvaluatorSaved: (params: {
    evaluator: StructuredEvaluatorSchema;
    fewShotEnabled: boolean;
    numFewShotExamples: number;
  }) => void;

  evaluator?: StructuredEvaluatorSchema;
  correctionsDatasetId?: string;
  fewShotEnabled?: boolean;
  numFewShotExamples?: number;
  evaluatorVersion?: number;
  backfill_from?: string | null;
};

export type EvaluatorCrudFormProps = {
  input: Record<string, unknown>;
  output?: Record<string, unknown>;
  referenceOutput?: Record<string, unknown>;
  activeVariable?: string;
  isLoading?: boolean;
  renderEvaluatorName?: () => React.ReactNode;
  // Used to generate a link to the few shot dataset
  fewShotDatasetId?: string;
  showEvaluatorVersionWarning: boolean;
  isCreating?: boolean;
  backfillComponent?: React.ReactNode;
};

export type EvaluatorFeedbackType = 'categorical' | 'continuous' | 'boolean';

export type RichTextEditorVariableMappingProps = {
  variableMappings: Record<string, string> | null;
  availablePaths: string[] | null;
  uneditableVariables: string[] | null;
  setVariableMappings: (variableMappings: Record<string, string>) => void;
  setActiveVariable: (activeVariable: string) => void;
};
