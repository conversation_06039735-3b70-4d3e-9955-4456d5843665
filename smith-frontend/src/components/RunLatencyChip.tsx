import AccessTimeIcon from '@mui/icons-material/AccessTime';

import { ReactNode } from 'react';

import { cn } from '@/utils/tailwind';

import { calculateLatency } from '../Pages/Run/components/RunStatsCard';
import { RunSchema } from '../types/schema';

export const getLatencyTheme = (latency: string | number) => {
  const latencySec =
    typeof latency === 'number' && !Number.isNaN(latency)
      ? latency
      : typeof latency === 'string' && latency.endsWith('s')
      ? parseFloat(latency.replace('s', ''))
      : null;

  if (latencySec === null) return undefined;
  if (latencySec > 7) {
    return cn('bg-error-primary text-error');
  }
  if (latencySec > 3) {
    return cn('bg-warning-primary text-warning');
  }

  return cn('bg-success-primary text-success');
};

export function LatencyChip(props: {
  latency: string | number | undefined;
  size?: 'xs' | 'sm';
  label?: ReactNode | undefined;
  className?: string;
  testId?: string;
  simple?: boolean;
  maximumFractionDigits?: number;
}) {
  if (props.latency == null) return null;
  const size = props.size ?? 'xs';
  const iconSize = size === 'sm' ? '14px' : '14px';

  const latencyLabel =
    typeof props.latency === 'number'
      ? `${props.latency.toFixed(props.maximumFractionDigits ?? 2)}s`
      : props.latency;

  return (
    <div
      className={cn(
        'flex items-center gap-1 rounded-md bg-background',
        getLatencyTheme(props.latency),
        {
          'py-0.5 text-xs': size === 'xs',
          'py-0 text-sm': size === 'sm',
        },
        props.label ? 'px-1.5' : 'pl-1 pr-1.5',
        props.simple && 'border-none',
        props.className
      )}
      data-testid={props.testId}
    >
      {props.label ? (
        <>{props.label}</>
      ) : props.simple ? null : (
        <AccessTimeIcon sx={{ width: iconSize, height: iconSize }} />
      )}
      {latencyLabel}
    </div>
  );
}

export function RunLatencyChip({
  run,
  size = 'xs',
  className,
}: {
  run: Pick<RunSchema, 'start_time' | 'end_time'>;
  size?: 'xs' | 'sm';
  className?: string;
}) {
  const { start_time, end_time } = run;
  const { latency } = end_time
    ? calculateLatency(start_time, end_time)
    : { latency: undefined };

  return <LatencyChip latency={latency} className={className} size={size} />;
}
