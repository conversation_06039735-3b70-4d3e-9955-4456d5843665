import {
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowsUpDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { LinearProgress, Option, Select } from '@mui/joy';
import { Cell, Row, Table, flexRender } from '@tanstack/react-table';

import { CSSProperties, MouseEvent, ReactNode, useMemo } from 'react';

import { TableSkeleton } from '@/Pages/Home/ResourceTable/TableSkeleton';
import DataGridEmptyStateIcon from '@/icons/DataGridEmptyStateIcon.svg?react';
import { cn } from '@/utils/tailwind';

import { useDelayedLoading } from './DataGrid.utils';
import { DataGridVirtual, DataGridVirtualProps } from './DataGridVirtual';
import {
  Table as BaseTable,
  TableBody,
  TableCell,
  TableCellTextOverflow,
  TableHead,
  TableHeader,
  TableRow,
} from './Table';

export interface DataGridTableHeaderProps<TData> {
  table: Table<TData>;
  cellSpacingVariant?: 'wide' | 'tight';
  showDividerByDefault?: boolean;
  resizeHandleHeight?: number;
  stickyRightColumn?: boolean;
}

export function DataGridEmptyState(props: { cellHeight?: number }) {
  return (
    <div className="flex h-full w-full flex-col">
      <div
        className="grow-0 bg-secondary"
        style={{
          height: props.cellHeight ?? 48,
        }}
      />
      <div className="my-[40px] flex flex-1 items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="flex h-[50px] w-[50px] items-center justify-center rounded-[10px] border border-secondary">
            <DataGridEmptyStateIcon className="h-5 w-5 " />
          </div>
          <div className="flex flex-col items-center gap-1.5">
            <div className="text-base font-semibold">No columns selected</div>
            <div className="text-sm font-normal text-tertiary">
              Select columns to see data
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function DataGridTableHeaderContent<TData>(
  props: DataGridTableHeaderProps<TData>
) {
  return (
    <>
      {props.table.getHeaderGroups().map((headerGroup) => (
        <TableRow key={headerGroup.id}>
          {headerGroup.headers.map((header, idx) => {
            const isLastColumn = idx === headerGroup.headers.length - 1;
            return (
              <TableHead
                spacingVariant={props.cellSpacingVariant}
                className={cn(
                  'relative select-none',
                  header.column.getCanSort() && 'cursor-pointer',
                  isLastColumn && props.stickyRightColumn && 'sticky right-0'
                )}
                key={header.id}
                style={{
                  width: header.getSize(),
                  ...(header.column.columnDef.id === 'select'
                    ? {
                        minWidth: header.getSize(),
                        maxWidth: header.getSize(),
                      }
                    : {}),
                }}
                onClick={
                  header.column.getCanSort()
                    ? header.column.getToggleSortingHandler()
                    : undefined
                }
              >
                <div className="flex items-center gap-2">
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}

                  {header.column.getCanSort() && (
                    <span>
                      {header.column.getIsSorted() ? (
                        <>
                          {{
                            asc: (
                              <ArrowUpIcon
                                data-testid="sort-arrow-up-icon"
                                className="h-4 w-4"
                              />
                            ),
                            desc: (
                              <ArrowDownIcon
                                data-testid="sort-arrow-down-icon"
                                className="h-4 w-4"
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? null}
                        </>
                      ) : (
                        <ArrowsUpDownIcon
                          data-testid="sort-arrows-up-down-icon"
                          className="h-4 w-4"
                        />
                      )}
                    </span>
                  )}

                  {header.column.getCanResize() && (
                    <div
                      className={cn(
                        'group absolute inset-y-0 right-[5px] z-[5] flex w-px cursor-ew-resize items-stretch justify-center transition-all',
                        props.resizeHandleHeight == null && 'min-h-[1000vh]'
                      )}
                      style={{ minHeight: props.resizeHandleHeight }}
                      onClick={(e) => e.stopPropagation()}
                      onMouseDown={header.getResizeHandler()}
                      onTouchStart={header.getResizeHandler()}
                    >
                      <div className="absolute inset-y-0 w-[9px]" />
                      <div
                        className={cn(
                          'absolute inset-y-0 w-[2px] bg-transparent transition-colors group-hover:bg-tertiary',
                          {
                            'bg-brand-green-400 group-hover:bg-brand-green-400':
                              header.column.getIsResizing(),
                            'bg-tertiary':
                              props.showDividerByDefault &&
                              idx !== headerGroup.headers.length - 1,
                          }
                        )}
                      />
                    </div>
                  )}
                </div>
              </TableHead>
            );
          })}
        </TableRow>
      ))}
    </>
  );
}

export interface DataGridProps<TData> {
  table: Table<TData>;
  isLoading: boolean;
  emptyState?: ReactNode | null;
  onClick?: (
    row: TData,
    event: MouseEvent<HTMLTableRowElement, globalThis.MouseEvent>
  ) => void;
  onMiddleClick?: (
    row: TData,
    event: MouseEvent<HTMLTableRowElement, globalThis.MouseEvent>
  ) => void;

  highlightedId?: string | null;
  highlightedIds?: string[];
  cellHeight?: number;
  cellSpacingVariant?: 'wide' | 'tight';
  showDividerByDefault?: boolean;
  withCustomTextOverflow?: boolean;
  maxWidthFull?: boolean;
  stickyRightColumn?: boolean;
  loadingRowCount?: number;
  hideHeader?: boolean;
}

export function DataGrid<TData>(props: DataGridProps<TData>) {
  const isEmpty = props.table.getRowModel().rows.length <= 0;
  const isLoading = useDelayedLoading(props.isLoading);
  const isImmediateLoading = props.isLoading && isEmpty;

  const allColumnsHidden =
    !props.table
      .getAllLeafColumns()
      .some((column) => column.getIsVisible() && column.getCanHide()) &&
    props.table.getAllLeafColumns().some((column) => column.getCanHide());

  return (
    <div className="relative">
      {isImmediateLoading ? (
        <TableSkeleton
          rows={props.loadingRowCount ?? 10}
          rowHeight={props.cellHeight}
          hideHeader={props.hideHeader}
          className="rounded-none border-none"
        />
      ) : (
        <>
          {isLoading && (
            <div
              className={cn(
                'absolute w-full -translate-y-[3px]',
                props.hideHeader ? 'top-[0px]' : 'top-[48px]'
              )}
            >
              <LinearProgress />
            </div>
          )}
          <BaseTable
            className={cn(
              'object min-w-full',
              props.maxWidthFull ? 'max-w-full' : ''
            )}
            style={{ width: props.table.getCenterTotalSize() }}
          >
            {allColumnsHidden ? (
              <DataGridEmptyState cellHeight={props.cellHeight} />
            ) : (
              <>
                {!props.hideHeader && (
                  <TableHeader>
                    <DataGridTableHeaderContent
                      table={props.table}
                      cellSpacingVariant={props.cellSpacingVariant}
                      showDividerByDefault={props.showDividerByDefault}
                      stickyRightColumn={props.stickyRightColumn}
                    />
                  </TableHeader>
                )}
                <DataGridTableBody
                  table={props.table}
                  isLoading={props.isLoading}
                  isEmpty={isEmpty}
                  emptyState={props.emptyState}
                  onClick={props.onClick}
                  onMiddleClick={props.onMiddleClick}
                  highlightedId={props.highlightedId}
                  highlightedIds={props.highlightedIds}
                  cellHeight={props.cellHeight}
                  cellSpacingVariant={props.cellSpacingVariant}
                  withCustomTextOverflow={props.withCustomTextOverflow}
                  stickyRightColumn={props.stickyRightColumn}
                />
              </>
            )}
          </BaseTable>
        </>
      )}
    </div>
  );
}

export function DataGridTableBody<TData>(props: {
  table: Table<TData>;
  isLoading: boolean;
  isEmpty: boolean;
  emptyState?: ReactNode;
  onClick?: (
    row: TData,
    event: MouseEvent<HTMLTableRowElement, globalThis.MouseEvent>
  ) => void;
  onMiddleClick?: (
    row: TData,
    event: MouseEvent<HTMLTableRowElement, globalThis.MouseEvent>
  ) => void;

  highlightedId?: string | null;
  highlightedIds?: string[];
  cellHeight?: number;
  cellSpacingVariant?: 'wide' | 'tight';
  withCustomTextOverflow?: boolean;
  stickyRightColumn?: boolean;
}) {
  const colSize = props.table.getAllColumns().length;

  return (
    <TableBody>
      {!props.isEmpty ? (
        props.table
          .getRowModel()
          .rows.map((row) => (
            <DataGridTableRow
              key={row.id}
              table={props.table}
              isLoading={props.isLoading}
              isEmpty={props.isEmpty}
              emptyState={props.emptyState}
              onClick={props.onClick}
              onMiddleClick={props.onMiddleClick}
              highlightedIds={props.highlightedIds}
              highlightedId={props.highlightedId}
              cellHeight={props.cellHeight}
              cellSpacingVariant={props.cellSpacingVariant}
              row={row}
              withCustomTextOverflow={props.withCustomTextOverflow}
              stickyRightColumn={props.stickyRightColumn}
            />
          ))
      ) : (
        <>
          {props.emptyState !== null && (
            <TableRow>
              {!props.isLoading && (
                <TableCell
                  spacingVariant={props.cellSpacingVariant}
                  colSpan={colSize}
                  className="h-24 w-full text-center"
                >
                  {props.emptyState ?? 'No data.'}
                </TableCell>
              )}
            </TableRow>
          )}
        </>
      )}
    </TableBody>
  );
}

const TableCellWithStableStyle = <TData,>(props: {
  cellSpacingVariant?: 'wide' | 'tight';
  cellHeight?: number;
  cell: Cell<TData, unknown>;
  withCustomTextOverflow?: boolean;
  className?: string;
}) => {
  const cellSize = props.cell.column.getSize();
  const cellColumnId = props.cell.column.columnDef.id;

  const style = useMemo(() => {
    return {
      height: props.cellHeight ?? 48,
      ...(cellColumnId === 'select'
        ? {
            width: cellSize,
            minWidth: cellSize,
            maxWidth: cellSize,
          }
        : {}),
    };
  }, [props.cellHeight, cellSize, cellColumnId]);

  return (
    <TableCell
      spacingVariant={props.cellSpacingVariant}
      key={props.cell.id}
      className={cn('relative', props.className)}
      style={style}
    >
      {props.withCustomTextOverflow ? (
        flexRender(props.cell.column.columnDef.cell, props.cell.getContext())
      ) : (
        <TableCellTextOverflow spacingVariant={props.cellSpacingVariant}>
          {flexRender(
            props.cell.column.columnDef.cell,
            props.cell.getContext()
          )}
        </TableCellTextOverflow>
      )}
    </TableCell>
  );
};

export function DataGridTableRow<TData>(props: {
  table: Table<TData>;
  isLoading: boolean;
  isEmpty: boolean;
  emptyState?: ReactNode;
  onClick?: (
    row: TData,
    event: MouseEvent<HTMLTableRowElement, globalThis.MouseEvent>
  ) => void;
  onMiddleClick?: (
    row: TData,
    event: MouseEvent<HTMLTableRowElement, globalThis.MouseEvent>
  ) => void;
  highlightedIds?: string[];
  highlightedId?: string | null;
  cellHeight?: number;
  cellSpacingVariant?: 'wide' | 'tight';
  withCustomTextOverflow?: boolean;
  row: Row<TData>;
  style?: CSSProperties;
  stickyRightColumn?: boolean;
  [data: `data-${string}`]: number;
}) {
  const dataKeys = Object.keys(props)
    .filter((i) => i.startsWith('data-'))
    .reduce<{ [data: `data-${string}`]: number }>((memo, key) => {
      memo[key] = props[key];
      return memo;
    }, {});

  let highlightClassNames = '';
  if (props.highlightedIds) {
    highlightClassNames = props.highlightedIds?.includes(props.row.id)
      ? 'border-l-brand bg-brand-secondary hover:bg-brand-green-200'
      : props.highlightedIds.length > 0
      ? 'border-l-transparent'
      : '';
  } else if (props.highlightedId) {
    highlightClassNames =
      props.highlightedId === props.row.id
        ? 'border-l-brand bg-brand-secondary hover:bg-brand-green-200'
        : 'border-l-transparent';
  }

  return (
    <TableRow
      {...dataKeys}
      key={props.row.id}
      data-state={props.row.getIsSelected() && 'selected'}
      className={cn(
        `data-grid-row-component group transition-all hover:bg-secondary data-grid-row-component-${props.row.id}`,
        props.onClick && 'cursor-pointer',
        highlightClassNames
      )}
      style={{
        ...props.style,
        // this needs to be here as there is a class with higher specificity
        // that overrides this for the last row
        ...(highlightClassNames && {
          borderLeftWidth: '2px',
        }),
        ...(props.row.getIsExpanded() && {
          backgroundColor: 'hsla(var(--ls-brand-green-400) / 0.1)',
        }),
      }}
      onClick={(e) => props.onClick?.(props.row.original, e)}
      onMouseUp={(e) => {
        if (e.button === 1) {
          props.onMiddleClick?.(props.row.original, e);
        }
      }}
    >
      {props.row.getVisibleCells().map((cell, idx) => {
        const isLastColumn = idx === props.row.getVisibleCells().length - 1;
        return (
          <TableCellWithStableStyle
            key={cell.id}
            cell={cell}
            cellSpacingVariant={props.cellSpacingVariant}
            cellHeight={props.cellHeight}
            withCustomTextOverflow={props.withCustomTextOverflow}
            className={cn(
              isLastColumn &&
                props.stickyRightColumn &&
                'sticky right-0 bg-background transition-colors group-hover:bg-secondary'
            )}
          />
        );
      })}
    </TableRow>
  );
}

export function DataGridPagination<TData>(props: {
  table: Table<TData>;
  className?: string;
  onNextTrigger?: ReactNode;
}) {
  return (
    <div
      className={cn(
        'mt-3 flex items-center justify-end gap-2',
        props.className
      )}
    >
      <div className="flex items-center gap-2">
        {props.table.getPageCount() > 0 && (
          <div>Page {props.table.getState().pagination.pageIndex + 1} </div>
        )}
        <button
          type="button"
          data-testid="previous-page-button"
          className="flex h-10 w-10 items-center justify-center rounded-lg border border-secondary hover:bg-secondary-hover active:bg-secondary disabled:bg-transparent disabled:opacity-50"
          onClick={() => props.table.previousPage()}
          disabled={!props.table.getCanPreviousPage()}
        >
          <ChevronLeftIcon className="h-5 w-5" />
        </button>

        {props.onNextTrigger && !props.table.getCanNextPage() ? (
          <>{props.onNextTrigger}</>
        ) : (
          <button
            type="button"
            data-testid="next-page-button"
            className="flex h-10 w-10 items-center justify-center rounded-lg border border-secondary hover:bg-secondary-hover active:bg-secondary disabled:bg-transparent disabled:opacity-50"
            onClick={() => props.table.nextPage()}
            disabled={!props.table.getCanNextPage()}
          >
            <ChevronRightIcon className="h-5 w-5" />
          </button>
        )}

        <Select
          value={props.table.getState().pagination.pageSize}
          onChange={(_, value) => props.table.setPageSize(value ?? 0)}
          className="hover:bg-secondary-hover active:bg-secondary"
        >
          {[10, 15, 25, 40, 50].map((pageSize) => (
            <Option key={pageSize} value={pageSize}>
              Show {pageSize}
            </Option>
          ))}
        </Select>
      </div>
    </div>
  );
}

export function DataGridWithFooter<TData>(
  props: { virtual: boolean } & DataGridVirtualProps<TData>
) {
  if (!props.virtual) {
    return (
      <>
        <DataGrid {...props} />
        {props.footer}
      </>
    );
  }
  return <DataGridVirtual {...props} />;
}
