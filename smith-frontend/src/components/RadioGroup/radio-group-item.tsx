'use client';

import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';

import * as React from 'react';

import { cn } from '@/utils/tailwind';

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        'flex items-start justify-end gap-3 self-stretch rounded-full border border-secondary bg-primary p-3 transition-colors data-[state=checked]:bg-brand-primary data-[state=checked]:p-2',
        className
      )}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
        <div className="h-2 w-2 rounded-full bg-white" />
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  );
});
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

export { RadioGroupItem };
