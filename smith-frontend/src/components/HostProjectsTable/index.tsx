import { EllipsisVerticalIcon, TrashIcon } from '@heroicons/react/24/outline';
import { PlayCircleIcon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';
import {
  ColumnDef,
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useEffect, useMemo, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { DEPLOYMENT_TYPES_TO_DISPLAY_NAME } from '@/Pages/HostProject/constants';
import { NoHostProjects } from '@/Pages/HostProjects/HostProjects';
import { CopyInlineLink } from '@/components/CopyInlineLink';
import { DataGridColumnVisibilityPopover } from '@/components/DataGrid/DataGridColumnVisibilityPopover';
import { DataGridSearchInput } from '@/components/DataGrid/DataGridSearchInput';
import { FetchError } from '@/data/fetcher';
import {
  GridPaginationModel,
  useDataGridState,
} from '@/hooks/useDataGridState';
import { usePermissions } from '@/hooks/usePermissions';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import { useHostProjects, useOrganizationId } from '@/hooks/useSwr';
import ProjectIcon from '@/icons/ProjectIcon';
import { HostProjectSchema } from '@/types/schema';
import {
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
  deploymentsAppIndexPath,
  hostApiProjectsPath,
} from '@/utils/constants';

import { DataGrid, DataGridPagination } from '../DataGrid';
import {
  emulateNativeClick,
  emulateNativeMiddleClick,
  useDataGridSizingLocalStorage,
} from '../DataGrid.utils';
import { DeleteModal } from '../Delete';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../Dropdown';
import { ErrorBanner } from '../ErrorBanner';
import { TableCellTextOverflow } from '../Table';
import { TextOverflowTooltip } from '../TextOverflowTooltip';
import { Tooltip } from '../Tooltip/Tooltip';

const columnHelper = createColumnHelper<HostProjectSchema>();

function HostProjectTracerSessionTag(props: {
  hostProject: HostProjectSchema;
}) {
  const organizationId = useOrganizationId();
  return (
    <Link
      to={`/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${props.hostProject.tracer_session_id}`}
      className="inline-flex"
      onClick={(e) => e.stopPropagation()}
    >
      <span className="inline-flex items-center gap-0.5 rounded-sm bg-primary p-1 py-0.5 pr-2 text-sm">
        <ProjectIcon className="mr-1 h-4 w-4" />
        {props.hostProject.name}
      </span>
    </Link>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const COLUMNS_DEF: ColumnDef<HostProjectSchema, any>[] = [
  columnHelper.accessor('name', {
    header: 'Deployment Name',
    enableSorting: false,
    size: 300,
    cell: ({ row }) => {
      return (
        <TextOverflowTooltip className="text-primary">
          {row.original.name}
        </TextOverflowTooltip>
      );
    },
  }),
  columnHelper.accessor('id', {
    header: 'Deployment ID',
    cell: ({ row }) => (
      <TextOverflowTooltip className="text-primary">
        {row.original.id}
      </TextOverflowTooltip>
    ),
    enableResizing: true,
    enableSorting: false,
  }),
  columnHelper.accessor(
    'resource.latest_active_revision.hosted_langserve_revision_id',
    {
      header: 'Active Revision ID',
      cell: ({ row }) => (
        <TextOverflowTooltip className="text-primary">
          {
            row.original.resource?.latest_active_revision
              ?.hosted_langserve_revision_id
          }
        </TextOverflowTooltip>
      ),
      enableResizing: true,
      enableSorting: false,
    }
  ),

  columnHelper.accessor('tracer_session_id', {
    header: 'Tracing Project',
    cell: ({ row }) => (
      <HostProjectTracerSessionTag hostProject={row.original} />
    ),
    enableResizing: true,
    enableSorting: false,
    size: 200,
  }),
  columnHelper.accessor('updated_at', {
    header: 'Updated At',
    cell: ({ row }) => new Date(row.original.updated_at).toLocaleString(),
    enableResizing: true,
    enableSorting: false,
    size: 150,
  }),
  columnHelper.accessor('metadata.deployment_type ', {
    header: 'Deployment Type',
    cell: ({ row }) => (
      <TextOverflowTooltip className="text-primary">
        {
          DEPLOYMENT_TYPES_TO_DISPLAY_NAME[
            row.original.metadata.deployment_type
          ]
        }
      </TextOverflowTooltip>
    ),
    enableResizing: true,
    enableSorting: false,
    size: 150,
  }),
  columnHelper.display({
    id: 'Resource URL',
    header: 'Resource URL',
    cell: ({ row }) => {
      if (!row.original.resource?.url) return null;
      return (
        <span
          className="flex min-w-0 items-center gap-1 text-ellipsis"
          onClick={(e) => e.stopPropagation()}
        >
          <CopyInlineLink
            value={row.original.resource.url}
            className="text-md"
            prefix="URL"
          >
            {row.original.resource.url}
          </CopyInlineLink>
        </span>
      );
    },
    enableResizing: true,
    enableSorting: false,
  }),
  columnHelper.display({
    id: 'actions',
    cell: ({ row, table }) => {
      const { actions } = (table.options.meta ?? {}) as {
        actions: {
          handleDelete: (id: string) => void;
          deleteEnabled: boolean;
        };
      };

      if (!actions.deleteEnabled) return null;

      return (
        <TableCellTextOverflow className="justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger
              onClick={(e) => e.stopPropagation()}
              className="flex h-6 w-6 items-center justify-center rounded-md transition-colors hover:bg-tertiary focus:outline-none"
            >
              <EllipsisVerticalIcon className="h-5 w-5" />
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="z-10"
              onClick={(e) => e.stopPropagation()}
            >
              <DropdownMenuItem
                onClick={() => actions.handleDelete(row.original.id)}
                className="flex items-center gap-2"
              >
                <TrashIcon className="h-5 w-5" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCellTextOverflow>
      );
    },
    enableSorting: false,
    enableHiding: false,
    enableResizing: false,
    size: 40,
  }),
];

const EMPTY_TABLE = [];

type SimpleTableProps =
  | {
      simple: true;
      defaultPaginationModel: GridPaginationModel;
    }
  | {
      simple?: false;
    };

export const HostProjectsTable = (props: SimpleTableProps) => {
  const organizationId = useOrganizationId();
  const { simple } = props;
  // navigation
  const navigate = useNavigate();
  const { authorize } = usePermissions();
  const { selectedTags } = useStoredResourceTags();
  // pagination
  const { paginationModel, setPaginationModel, resetPaginationModel } =
    useDataGridState({});
  const mPaginationModel = simple
    ? props.defaultPaginationModel
    : paginationModel;

  const [name, setName] = useState('');
  const [debounceName] = useDebounce(name, 500, { trailing: true });

  useEffect(() => {
    if (!simple) {
      resetPaginationModel();
    }
    // Reset pagination when search term or tags change
  }, [debounceName, selectedTags, simple, resetPaginationModel]);

  // data fetching
  const {
    data: rows,
    isLoading,
    isValidating,
    error,
  } = useHostProjects({
    name_contains: debounceName ?? undefined,
    offset: mPaginationModel.pageIndex * mPaginationModel.pageSize,
    limit: mPaginationModel.pageSize,
    tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
  });

  // columns and actions
  const [deleteOpen, setDeleteOpen] = useState<string | null>(null);

  const [columnSizing, setColumnSizing] =
    useDataGridSizingLocalStorage('hostProjects');

  const columns = useMemo(() => {
    return [
      ...COLUMNS_DEF.slice(0, -1),
      columnHelper.display({
        id: 'studio_url',
        header: 'Studio',
        cell: ({ row }) => {
          if (!row.original.resource?.url) return null;
          return (
            <Tooltip title="Open deployment in Studio">
              <Button
                component={Link}
                to={`/studio/?hostProjectId=${row.original.id}&organizationId=${organizationId}`}
                variant="plain"
                color="neutral"
                onClick={(e) => e.stopPropagation()}
                sx={{
                  p: 1,
                  py: 0,
                }}
              >
                <PlayCircleIcon className="size-4 text-tertiary" />
              </Button>
            </Tooltip>
          );
        },
        size: 40,
      }),
      ...COLUMNS_DEF.slice(-1),
    ];
  }, [organizationId]);

  const table = useReactTable({
    columns,
    data: rows ?? EMPTY_TABLE,

    state: { pagination: mPaginationModel, columnSizing },
    meta: {
      actions: {
        handleDelete: setDeleteOpen,
        deleteEnabled: authorize('deployments:delete'),
      },
    },
    onPaginationChange: setPaginationModel,
    onColumnSizingChange: setColumnSizing,

    getRowId: (row) => row.id,

    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),

    manualExpanding: true,
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,

    columnResizeMode: 'onChange',

    pageCount: Math.ceil(
      +(rows?.headers?.['x-pagination-total'] || 0) / mPaginationModel.pageSize
    ),
  });

  if (error instanceof FetchError) {
    return (
      <ErrorBanner>
        {error.status === 401
          ? 'Permission denied, you do not have the required permission deployments:read'
          : error.message}
      </ErrorBanner>
    );
  }

  return (
    <>
      {!simple && (
        <div className="mb-4 flex items-stretch justify-between">
          <div className="flex items-stretch gap-2">
            <DataGridSearchInput value={name} onChange={setName} />
          </div>
          <DataGridColumnVisibilityPopover
            table={table}
            localStorageKey="ls:deployment-columns"
          />
        </div>
      )}

      <div className="overflow-hidden rounded-lg border border-secondary">
        <DataGrid
          table={table}
          cellHeight={64}
          cellSpacingVariant="wide"
          isLoading={isLoading || isValidating}
          emptyState={<NoHostProjects hasTags={selectedTags.length > 0} />}
          onClick={(row, event) => {
            const path = `/${appOrganizationPath}/${organizationId}/${deploymentsAppIndexPath}/${row.id}`;
            if (!emulateNativeClick(path, event.nativeEvent)) {
              navigate(path);
            }
          }}
          onMiddleClick={(row) => {
            const path = `/${appOrganizationPath}/${organizationId}/${deploymentsAppIndexPath}/${row.id}`;
            if (!emulateNativeMiddleClick(path)) {
              navigate(path);
            }
          }}
          stickyRightColumn={true}
        />
      </div>

      {!simple && <DataGridPagination table={table} />}

      <DeleteModal
        endpoint={hostApiProjectsPath}
        invalidationPrefixes={['orgDeployments']}
        id={deleteOpen as string}
        isOpen={!!deleteOpen}
        doClose={() => setDeleteOpen(null)}
        modalTitle={`Delete deployment ${
          rows?.find((row) => row.id === deleteOpen)?.name
        }?`}
        modalText="Deleting a deployment will permanently delete the underlying database for the deployment. After deletion, data from the database (e.g. assistants, threads) cannot be restored."
      />
    </>
  );
};
