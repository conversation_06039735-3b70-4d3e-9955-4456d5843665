import { EllipsisVerticalIcon } from '@heroicons/react/24/outline';
import { Pencil01Icon, Trash01Icon } from '@langchain/untitled-ui-icons';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';

import Attachments from '@/Pages/Run/components/Attachments';
import { getAttachments } from '@/Pages/Run/utils/multimodalRunUtils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { TableCellTextOverflow } from '@/components/Table';
import { ExampleSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { filterInputs } from '../../RunsTable/utils/filterInputs';

const columnHelper = createColumnHelper<ExampleSchema>();

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const COLUMNS_DEF: ColumnDef<ExampleSchema, any>[] = [
  columnHelper.accessor(
    (row) => {
      const filteredInputs = filterInputs(row.inputs, {
        removeKeys: ['functions'],
      });
      return filteredInputs?.length ? filteredInputs : null;
    },
    {
      id: 'inputs',
      header: 'Inputs',
      enableResizing: true,
      enableSorting: false,
      cell: ({ getValue }) => {
        const value = getValue();
        return value ?? <i className="text-tertiary">No inputs</i>;
      },
    }
  ),
  columnHelper.accessor(
    (row) => {
      const filteredOutputs =
        filterInputs(row.outputs, {
          removeKeys: Object.keys(row.inputs).concat(['input', 'inputs']),
        }) || filterInputs(row.outputs);
      return filteredOutputs?.length ? filteredOutputs : null;
    },
    {
      id: 'outputs',
      header: 'Reference Outputs',
      enableResizing: true,
      enableSorting: false,
      cell: ({ getValue }) => {
        const value = getValue();
        return value ?? <i className="text-tertiary">No outputs</i>;
      },
    }
  ),
  columnHelper.display({
    id: 'attachment_urls',
    header: 'Attachments',
    cell: ({ row }) => {
      const attachments = getAttachments(row.original.attachment_urls);
      return (
        <Attachments
          attachments={attachments}
          className="p-0"
          gridClassName={cn(
            'flex flex-nowrap items-center gap-2 overflow-auto p-0 no-scrollbar'
          )}
          hideTitle
          size="small"
        />
      );
    },
  }),
  columnHelper.accessor('created_at', {
    header: 'Created At',
    cell: ({ row }) => new Date(row.original.created_at).toLocaleString(),
    enableResizing: true,
    enableSorting: false,
    size: 150,
  }),
  columnHelper.accessor('modified_at', {
    header: 'Modified At',
    cell: ({ row }) => new Date(row.original.modified_at).toLocaleString(),
    enableResizing: true,
    enableSorting: false,
    size: 150,
  }),
];

export const getActionColumn = (
  isReadOnly?: boolean
): ColumnDef<ExampleSchema, any> => {
  return columnHelper.display({
    id: 'actions',
    cell: ({ row, table }) => {
      const { actions } = (table.options.meta ?? {}) as {
        actions: {
          handleDelete: (id: string) => void;
          handleEdit: (example: ExampleSchema) => void;
        };
      };

      return (
        <TableCellTextOverflow className="justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger
              onClick={(e) => e.stopPropagation()}
              className="flex h-6 w-6 items-center justify-center rounded-md transition-colors hover:bg-secondary focus:outline-none"
            >
              <EllipsisVerticalIcon className="h-5 w-5" />
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="z-10"
              onClick={(e) => e.stopPropagation()}
            >
              <DropdownMenuItem
                onClick={() => actions.handleEdit(row.original)}
                className="flex items-center gap-2"
                disabled={isReadOnly}
              >
                <Pencil01Icon className="h-5 w-5" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => actions.handleDelete(row.original.id)}
                className="flex items-center gap-2"
                disabled={isReadOnly}
              >
                <Trash01Icon className="h-5 w-5" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCellTextOverflow>
      );
    },
    enableSorting: false,
    enableHiding: false,
    enableResizing: false,
    size: 40,
  });
};
