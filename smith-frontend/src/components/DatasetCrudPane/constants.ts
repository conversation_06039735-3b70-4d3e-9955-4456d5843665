import { DatasetTransformationType } from '@/types/schema';
import { getAPIUrlVercel } from '@/utils/get-api-url';

export const DEFAULT_SCHEMA = (inputOutput: 'input' | 'output') => ({
  type: 'object',
  title: `dataset_${inputOutput}_schema`,
  properties: {},
  required: [],
});

export const MESSAGE_REF_URL = `${getAPIUrlVercel()}/public/schemas/v1/message.json`;
export const TOOL_REF_URL = `${getAPIUrlVercel()}/public/schemas/v1/tooldef.json`;

export const DEFAULT_CHAT_INPUT_SCHEMA = () => {
  return {
    type: 'object',
    title: `dataset_input_schema`,
    required: ['messages'],
    properties: {
      messages: {
        type: 'array',
        items: {
          $ref: MESSAGE_REF_URL,
        },
      },
      tools: {
        type: 'array',
        items: {
          $ref: TOOL_REF_URL,
        },
      },
    },
  };
};
export const DEFAULT_CHAT_OUTPUT_SCHEMA = () => {
  return {
    type: 'object',
    title: `dataset_output_schema`,
    required: ['message'],
    properties: {
      message: {
        $ref: MESSAGE_REF_URL,
      },
    },
  };
};

export const TRANSFORMATION_TYPE_TO_LABEL = {
  [DatasetTransformationType.CONVERT_TO_OPENAI_MESSAGE]:
    'Convert to OpenAI message',
  [DatasetTransformationType.REMOVE_EXTRA_FIELDS]: 'Remove extra fields',
  [DatasetTransformationType.CONVERT_TO_OPENAI_TOOL]: 'Convert to OpenAI tool',
  [DatasetTransformationType.REMOVE_SYSTEM_MESSAGES]: 'Remove system messages',

  // Deprecated
  [DatasetTransformationType.EXTRACT_TOOLS_FROM_RUN]: 'Extract tools from run',
};

export const DEFAULT_CHAT_TRANSFORMATIONS = [
  {
    path: ['inputs', 'messages'],
    transformation_type: DatasetTransformationType.CONVERT_TO_OPENAI_MESSAGE,
  },
  {
    path: ['inputs'],
    transformation_type: DatasetTransformationType.REMOVE_EXTRA_FIELDS,
  },
  {
    path: ['inputs', 'tools'],
    transformation_type: DatasetTransformationType.CONVERT_TO_OPENAI_TOOL,
  },
  {
    path: ['outputs'],
    transformation_type: DatasetTransformationType.REMOVE_EXTRA_FIELDS,
  },
  {
    path: ['outputs', 'message'],
    transformation_type: DatasetTransformationType.CONVERT_TO_OPENAI_MESSAGE,
  },
];

export const CSV_PREVIEW_MAX_LENGTH = 750;

export const CHAT_MODEL_SCHEMA_ONBOARDING_TEXT = `Automatically convert tracing project LLM runs into industry standard
          OpenAI formats that can be used downstream with any model for testing.
          Compatible with LangChain ChatModel and OpenAI client traces.`;
