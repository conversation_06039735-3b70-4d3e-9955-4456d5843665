import { json } from '@codemirror/lang-json';
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, Button, LinearProgress, Tooltip } from '@mui/joy';

import { JSONSchema7 } from 'json-schema';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDebounce } from 'use-debounce';

import { useExample } from '@/hooks/useSwr';
import { RepoExampleCreateSchema } from '@/types/schema';
import { tryParseJSON } from '@/utils/try-parse-json';
import {
  jsonSchemaExtensions,
  useCodeMirrorTooltipFollowMouseManual,
} from '@/utils/use-codemirror-tooltip-follow-mouse';

import { Code, CodeCard } from '../Code/Code';
import { Pane } from '../Pane';
import { useSchemaConformance } from './utils';

type MinimalRunInfo = {
  run_id: string;
  inputs: Record<string, unknown>;
  outputs: Record<string, unknown>;
};

export function UpdateNonConformingExamplesQueuePane(props: {
  nonConformingExamples: string[] | MinimalRunInfo[];
  updatedExamples: Record<string, RepoExampleCreateSchema>;
  setUpdatedExamples: React.Dispatch<
    React.SetStateAction<Record<string, RepoExampleCreateSchema>>
  >;
  inputsSchemaDefinition?: JSONSchema7 | null;
  outputsSchemaDefinition?: JSONSchema7 | null;
  datasetId: string;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (
    updatedExamplesOverride?: Record<string, RepoExampleCreateSchema>
  ) => void;
}) {
  const [index, setIndex] = useState(0);

  const exampleIdToFetch = useMemo(() => {
    if (typeof props.nonConformingExamples[index] === 'string') {
      return props.nonConformingExamples[index] as string;
    }
    return null;
  }, [props.nonConformingExamples, index]);
  const exampleSwr = useExample(exampleIdToFetch);

  const getInitialValues = useCallback(
    (accessor: 'inputs' | 'outputs') => {
      if (typeof props.nonConformingExamples[index] === 'string') {
        if (
          props.nonConformingExamples[index] &&
          props.updatedExamples[props.nonConformingExamples[index] as string]
        ) {
          return JSON.stringify(
            props.updatedExamples[props.nonConformingExamples[index] as string][
              accessor
            ],
            null,
            2
          );
        }
        return exampleSwr.data && !exampleSwr.isLoading
          ? JSON.stringify(exampleSwr.data?.[accessor], null, 2)
          : undefined;
      }
      if (
        props.nonConformingExamples[index] &&
        props.updatedExamples[
          (props.nonConformingExamples[index] as MinimalRunInfo).run_id
        ]
      ) {
        return JSON.stringify(
          props.updatedExamples[
            (props.nonConformingExamples[index] as MinimalRunInfo).run_id
          ][accessor],
          null,
          2
        );
      }
      const currentExample = props.nonConformingExamples[index] as
        | MinimalRunInfo
        | undefined;
      return currentExample
        ? JSON.stringify(currentExample[accessor], null, 2)
        : null;
    },
    [
      props.nonConformingExamples,
      props.updatedExamples,
      index,
      exampleSwr.data,
      exampleSwr.isLoading,
    ]
  );

  const initialInputs = useMemo(() => {
    return getInitialValues('inputs');
  }, [getInitialValues]);

  const initialOutputs = useMemo(() => {
    return getInitialValues('outputs');
  }, [getInitialValues]);

  const { setUpdatedExamples: setNonConformingExamples } = props;

  const [inputs, setInputs] = useState(initialInputs);
  const [outputs, setOutputs] = useState(initialOutputs);

  useEffect(() => {
    setOutputs(initialOutputs);
  }, [initialOutputs]);

  useEffect(() => {
    setInputs(initialInputs);
  }, [initialInputs]);

  const inputConforms = useSchemaConformance(
    inputs,
    props.inputsSchemaDefinition
  );
  const outputConforms = useSchemaConformance(
    outputs,
    props.outputsSchemaDefinition
  );

  const updateExamplesOnNextOrSubmit = useCallback(() => {
    if (!inputConforms || !outputConforms) return;
    const parsedOutputs = tryParseJSON(outputs);
    const parsedInputs = tryParseJSON(inputs);
    if (typeof props.nonConformingExamples[index] === 'string') {
      setNonConformingExamples((prev) => ({
        ...prev,
        [props.nonConformingExamples[index] as string]: {
          ...prev[props.nonConformingExamples[index] as string],
          inputs: parsedInputs,
          outputs: parsedOutputs,
        },
      }));
      return {
        ...props.updatedExamples,
        [props.nonConformingExamples[index] as string]: {
          ...props.updatedExamples[
            props.nonConformingExamples[index] as string
          ],
          inputs: parsedInputs,
          outputs: parsedOutputs,
        },
      };
    } else {
      const currentExample = props.nonConformingExamples[index] as {
        run_id: string;
        inputs: Record<string, unknown>;
        outputs: Record<string, unknown>;
      };
      setNonConformingExamples((prev) => ({
        ...prev,
        [currentExample.run_id]: {
          ...prev[currentExample.run_id],
          inputs: JSON.parse(parsedInputs),
          outputs: JSON.parse(parsedOutputs),
        },
      }));
      return {
        ...props.updatedExamples,
        [currentExample.run_id]: {
          ...props.updatedExamples[currentExample.run_id],
          inputs: JSON.parse(parsedInputs),
          outputs: JSON.parse(parsedOutputs),
        },
      };
    }
  }, [
    index,
    inputConforms,
    inputs,
    outputConforms,
    outputs,
    props.nonConformingExamples,
    props.updatedExamples,
    setNonConformingExamples,
  ]);

  const onNext = useCallback(() => {
    updateExamplesOnNextOrSubmit();
    setIndex(index + 1);
  }, [index, updateExamplesOnNextOrSubmit]);

  const debouncedOpen = useDebounce(props.isOpen, 600);

  const { onSubmit: onSubmitProp, onClose } = props;
  const onSubmit = useCallback(() => {
    const newUpdatedExamples = updateExamplesOnNextOrSubmit();
    setIndex(0);
    onClose();
    onSubmitProp(newUpdatedExamples);
  }, [onClose, onSubmitProp, updateExamplesOnNextOrSubmit]);

  const [revealJsonSchemas, setRevealJsonSchemas] = useState(false);

  return (
    <Pane
      open={props.isOpen}
      onClose={props.onClose}
      title={`Update Non-Conforming Examples: ${index + 1}/${
        props.nonConformingExamples.length
      }`}
    >
      <div className="flex flex-col gap-3">
        <div className="flex w-full justify-between self-stretch">
          <div className="text-lg font-semibold">{exampleSwr.data?.name}</div>
          <button
            type="button"
            className="flex items-center gap-1 rounded border border-zinc-700 px-2 py-1 hover:bg-secondary active:bg-tertiary"
            onClick={() => setRevealJsonSchemas((prev) => !prev)}
          >
            {!revealJsonSchemas && (
              <ArrowLeftIcon className="h-3 w-3 shrink-0 fill-black dark:fill-white" />
            )}
            <span className="line-clamp-1 whitespace-normal break-words break-all">
              {revealJsonSchemas ? 'Hide JSON Schemas' : 'Show JSON Schemas'}
            </span>
            {revealJsonSchemas && (
              <ArrowRightIcon className="h-3 w-3 shrink-0 fill-black dark:fill-white" />
            )}
          </button>
        </div>
        <div className="divide-x-1 flex gap-5 divide-secondary">
          {exampleSwr.data && initialInputs && !exampleSwr.isLoading ? (
            (props.isOpen || debouncedOpen) && (
              <UpdateNonConformingSingleExample
                key={index}
                initialInputs={initialInputs}
                initialOutputs={initialOutputs ?? undefined}
                setInputs={setInputs}
                setOutputs={setOutputs}
                inputsSchemaConforms={inputConforms}
                outputsSchemaConforms={outputConforms}
                inputsSchemaDefinition={props.inputsSchemaDefinition}
                outputsSchemaDefinition={props.outputsSchemaDefinition}
                datasetId={props.datasetId}
                title={exampleSwr.data?.name ?? ''}
              />
            )
          ) : exampleSwr.data && !initialInputs && !exampleSwr.isLoading ? (
            <Alert variant="outlined" color="danger">
              Something went wrong, please reload and try again.
            </Alert>
          ) : (
            <div className="basis-full">
              <LinearProgress />
            </div>
          )}
          {revealJsonSchemas && (
            <NonConformingExampleSchemasJsonSchemasView
              inputsSchemaDefinition={props.inputsSchemaDefinition}
              outputsSchemaDefinition={props.outputsSchemaDefinition}
            />
          )}
        </div>

        <div className="sticky bottom-0 right-0 flex justify-end gap-3 p-4">
          <Button
            onClick={() => {
              updateExamplesOnNextOrSubmit();
              setIndex(index - 1);
            }}
            disabled={index === 0}
          >
            Previous
          </Button>
          <Tooltip
            title={
              !inputConforms && !outputConforms
                ? 'Inputs and outputs do not conform to the schema'
                : !inputConforms
                ? 'Inputs do not conform to the schema'
                : !outputConforms && 'Outputs do not conform to the schema'
            }
          >
            <span>
              <Button
                onClick={
                  index === props.nonConformingExamples.length - 1
                    ? onSubmit
                    : onNext
                }
                disabled={!inputConforms || !outputConforms}
              >
                {index === props.nonConformingExamples.length - 1
                  ? 'Submit'
                  : 'Next'}
              </Button>
            </span>
          </Tooltip>
        </div>
      </div>
    </Pane>
  );
}

function NonConformingExampleSchemasJsonSchemasView(props: {
  inputsSchemaDefinition?: JSONSchema7 | null;
  outputsSchemaDefinition?: JSONSchema7 | null;
}) {
  const datasetInputSchemaSection = (
    <div>
      <CodeCard>
        <Code
          language={json()}
          value={JSON.stringify(props.inputsSchemaDefinition, null, 2)}
          readOnly
        />
      </CodeCard>
    </div>
  );

  const datasetOutputSchemaSection = (
    <div>
      <CodeCard>
        <Code
          language={json()}
          value={JSON.stringify(props.outputsSchemaDefinition, null, 2)}
          readOnly
        />
      </CodeCard>
    </div>
  );

  return (
    <div className="flex basis-full flex-col gap-5">
      <div className="flex flex-col gap-2">
        <div className="text-sm font-semibold uppercase leading-tight tracking-wide">
          INPUT SCHEMA
        </div>
        {datasetInputSchemaSection}
      </div>
      <div className="flex flex-col gap-2">
        <div className="text-sm font-semibold uppercase leading-tight tracking-wide">
          OUTPUT SCHEMA
        </div>
        {datasetOutputSchemaSection}
      </div>
    </div>
  );
}

export function UpdateNonConformingSingleExample(props: {
  initialInputs: string;
  initialOutputs?: string;
  setInputs: (inputs: string) => void;
  setOutputs: (outputs: string) => void;
  inputsSchemaDefinition?: JSONSchema7 | null;
  outputsSchemaDefinition?: JSONSchema7 | null;
  inputsSchemaConforms?: boolean;
  outputsSchemaConforms?: boolean;
  datasetId: string;
  title: string;
}) {
  const inputElementRef = useRef<HTMLDivElement>(null);
  const outputElementRef = useRef<HTMLDivElement>(null);

  const [inputs] = useState(props.initialInputs);
  const [outputs] = useState(props.initialOutputs);

  const inputElement = (
    <CodeCard
      sx={{
        borderLeft: props.inputsSchemaConforms
          ? ''
          : '2px solid var(--border-error)',
        marginLeft: props.inputsSchemaConforms ? '2px' : '',
      }}
    >
      <div ref={inputElementRef}>
        <Code
          language={json()}
          value={inputs}
          onChange={props.setInputs}
          extensions={jsonSchemaExtensions(
            props.inputsSchemaDefinition ?? undefined
          )}
        />
      </div>
    </CodeCard>
  );

  const outputElement = (
    <CodeCard
      sx={{
        borderLeft: props.outputsSchemaConforms
          ? ''
          : '2px solid var(--border-error)',
        marginLeft: props.outputsSchemaConforms ? '2px' : '',
      }}
    >
      <div ref={outputElementRef}>
        <Code
          language={json()}
          value={outputs}
          onChange={props.setOutputs}
          extensions={jsonSchemaExtensions(
            props.outputsSchemaDefinition ?? undefined
          )}
        />
      </div>
    </CodeCard>
  );

  useCodeMirrorTooltipFollowMouseManual(
    inputElementRef,
    outputElementRef,
    !!props.inputsSchemaDefinition,
    !!props.outputsSchemaDefinition
  );

  return (
    <div className="flex basis-full flex-col gap-5">
      <div className="flex flex-col gap-2">
        <div className="text-sm font-semibold uppercase leading-tight tracking-wide">
          INPUT
        </div>
        {inputElement}
      </div>
      <div className="flex flex-col gap-2">
        <div className="text-sm font-semibold uppercase leading-tight tracking-wide">
          OUTPUT
        </div>
        {outputElement}
      </div>
    </div>
  );
}
