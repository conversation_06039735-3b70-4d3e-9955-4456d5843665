import { EllipsisVerticalIcon, TrashIcon } from '@heroicons/react/24/outline';
import { PlayCircleOutlineOutlined } from '@mui/icons-material';
import { Box, Chip, LinearProgress, Tooltip } from '@mui/joy';
import {
  ColumnDef,
  PaginationState,
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import BrowsePublicPromptsButton from '@/Pages/Prompts/components/BrowsePublicPromptsButton';
import { getPromptIcon } from '@/Pages/Prompts/utils/Prompts.utils';
import { DataGrid, DataGridPagination } from '@/components/DataGrid';
import { emulateNativeClick } from '@/components/DataGrid.utils';
import { DataGridColumnVisibilityPopover } from '@/components/DataGrid/DataGridColumnVisibilityPopover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { Error } from '@/components/Hub/Error';
import { TAGS_CATEGORY } from '@/components/Hub/TagFilterPane';
import { TableCellTextOverflow } from '@/components/Table';
import { DEFAULT_10_PAGINATION_MODEL } from '@/constants/dataGridConstants';
import {
  GridPaginationModel,
  tryJsonParseSearchParam,
} from '@/hooks/useDataGridState';
import { usePermissions } from '@/hooks/usePermissions';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import { useRepos, useSelectedTenant } from '@/hooks/useSwr';
import { RepoWithLookupsSchema } from '@/types/schema';
import { addQueryParams } from '@/utils/add-query-params';
import { appPromptsIndexPath } from '@/utils/constants';

import { DeletePromptModal } from './DeletePromptModal';
import { PromptsFilterDropdown } from './PromptsFilterDropdown';
import { NoData } from './PromptsNoData';
import { PromptsSearch } from './PromptsSearch';

const PROMPT_TEMPLATE_TAGS = [
  'ChatPromptTemplate',
  'StructuredPrompt',
  'StringPromptTemplate',
  'PromptPlayground',
];

function PromptsTable(props: {
  simple?: boolean;
  defaultPagination: PaginationState;
  className?: string;
}) {
  const selectedTenant = useSelectedTenant();
  const tenant = selectedTenant.data;
  const navigate = useNavigate();
  const organizationId = tenant?.id;
  const columnHelper = createColumnHelper<RepoWithLookupsSchema>();
  const { authorize } = usePermissions();

  const [deletePromptHandle, setDeletePromptHandle] = useState<{
    name: string;
    isPublic: boolean;
  } | null>(null);

  const playgroundLinkBase = (promptName: string) =>
    `/${appPromptsIndexPath}/${promptName}/playground`;
  const playgroundLink = (promptName: string) =>
    addQueryParams(playgroundLinkBase(promptName), {
      organizationId,
    });

  const commitLinkBase = (commitHash: string) =>
    `/${appPromptsIndexPath}/${commitHash}`;
  const commitLink = (commitHash: string) =>
    addQueryParams(commitLinkBase(commitHash), {
      organizationId,
    });

  // pagination
  const [searchParams, setSearchParams] = useSearchParams();
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>(
    tryJsonParseSearchParam(
      searchParams,
      'paginationModel',
      DEFAULT_10_PAGINATION_MODEL
    )
  );

  const DEFAULT_COLUMNS: ColumnDef<RepoWithLookupsSchema, any>[] = [
    // PLAYGROUND LINK
    columnHelper.accessor('full_name', {
      id: 'playground-link',
      enableHiding: false,
      enableResizing: false,
      size: 40,
      minSize: 40,
      maxSize: 40,
      header: () => (
        <div className="mx-4">
          <PlayCircleOutlineOutlined />
        </div>
      ),
      cell: ({ row }) => (
        <Tooltip title="Open in Playground">
          <div className="mx-3 rounded-lg p-1 hover:bg-gray-300 dark:hover:bg-gray-700">
            <PlayCircleOutlineOutlined
              onClick={(e) => {
                navigate(playgroundLink(row.original.repo_handle));
                e.stopPropagation();
              }}
            />
          </div>
        </Tooltip>
      ),
      enableSorting: false,
    }),
    // NAME
    columnHelper.accessor('repo_handle', {
      id: 'name',
      header: 'Name',
      enableSorting: false,
      size: 250,
      cell: ({ row }) =>
        `${
          row.original.is_public
            ? row.original.full_name
            : row.original.repo_handle
        }`,
    }),
    // PUBLIC / PRIVATE ICON
    columnHelper.accessor('is_public', {
      id: 'visibility',
      header: 'Visibility',
      size: 110,
      cell: ({ row }) => (
        <div className="rounded-lg p-1">
          <div className="flex flex-row gap-2">
            {getPromptIcon(row.original.is_public)}
            {row.original.is_archived
              ? 'Archived'
              : row.original.is_public
              ? 'Public'
              : 'Private'}
          </div>
        </div>
      ),
      enableSorting: false,
    }),
    // PROMPT TYPE
    columnHelper.accessor('tags', {
      id: 'prompt-type',
      header: 'Prompt Type',
      enableSorting: false,
      size: 200,
      cell: ({ row }) => (
        <div className="flex flex-row">
          {row.original.tags
            .filter((t) => PROMPT_TEMPLATE_TAGS.includes(t))
            .map((t, i) => (
              <Box key={i} marginX={0.2} marginY={0}>
                <Chip
                  variant={'outlined'}
                  color="neutral"
                  key={i}
                  size="sm"
                  sx={{ borderRadius: 4 }}
                >
                  {t}
                </Chip>
              </Box>
            ))}
        </div>
      ),
    }),
    // LAST UPDATED
    columnHelper.accessor('updated_at', {
      id: 'last-updated',
      header: 'Last Updated',
      cell: ({ row }) =>
        row.original.updated_at
          ? new Date(row.original.updated_at + 'Z').toLocaleString()
          : undefined,
      enableSorting: false,
    }),
    // NUM COMMITS
    columnHelper.accessor('num_commits', {
      id: 'num-commits',
      header: 'Commits',
      size: 45,
      enableSorting: false,
    }),
    // LAST COMMIT HASH
    columnHelper.accessor('last_commit_hash', {
      id: 'last-commit-hash',
      header: 'Last Commit',
      size: 70,
      enableSorting: false,
      cell: ({ row }) => (
        <button
          type="button"
          className="cursor-pointer underline hover:opacity-70"
          onClick={(e) => {
            navigate(
              commitLink(
                `${
                  row.original.repo_handle
                }/${row.original.last_commit_hash?.slice(0, 8)}`
              )
            );
            e.stopPropagation();
          }}
        >
          {row.original.last_commit_hash?.slice(0, 8)}
        </button>
      ),
    }),
    // CATEGORIES
    columnHelper.accessor('tags', {
      id: 'categories',
      header: TAGS_CATEGORY,
      size: 250,
      enableSorting: false,
      cell: ({ row }) => (
        <div className="flex flex-row">
          {row.original.tags
            .filter((t) => !PROMPT_TEMPLATE_TAGS.includes(t))
            .map((t, i) => (
              <Box key={i} marginX={0.2} marginY={0}>
                <Chip
                  variant={'outlined'}
                  color="neutral"
                  key={i}
                  size="sm"
                  sx={{ borderRadius: 4 }}
                >
                  {t}
                </Chip>
              </Box>
            ))}
        </div>
      ),
    }),
    // DELETE PROMPT
    columnHelper.display({
      id: 'delete-prompt',
      enableResizing: false,
      enableHiding: false,
      size: 40,
      minSize: 40,
      maxSize: 40,
      enableSorting: false,
      cell: ({ row }) => {
        const actions = {
          deleteEnabled: authorize('prompts:delete'),
        };

        if (!actions.deleteEnabled) return null;
        return (
          <TableCellTextOverflow className="justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger
                onClick={(e) => e.stopPropagation()}
                className="flex h-6 w-6 items-center justify-center rounded-md transition-colors hover:bg-secondary focus:outline-none"
              >
                <EllipsisVerticalIcon className="h-5 w-5" />
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="z-10"
                onClick={(e) => e.stopPropagation()}
              >
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setDeletePromptHandle({
                      name: row.original.repo_handle,
                      isPublic: row.original.is_public,
                    });
                  }}
                  className="flex items-center gap-2"
                >
                  <TrashIcon className="h-5 w-5" />
                  Delete prompt
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCellTextOverflow>
        );
      },
    }),
  ];

  const [currentPromptFilter, setCurrentPromptFilter] = useState<
    'all' | 'private' | 'public' | 'archived'
  >('all');

  const [searchTerm, setSearchTerm] = useState(searchParams.get('q') || '');

  const { selectedTags } = useStoredResourceTags();

  useEffect(() => {
    if (!props.simple) {
      setPaginationModel(() => props.defaultPagination);
    }
  }, [
    // Reset pagination when search term or tags change
    searchTerm,
    selectedTags,
    props.simple,
    setPaginationModel,
    props.defaultPagination,
  ]);

  const {
    data: repos,
    mutate: mutateRepos,
    isLoading,
    error,
  } = useRepos(
    tenant
      ? {
          tenant_handle: tenant?.tenant_handle ?? undefined,
          tenant_id: tenant?.id ?? undefined,
          query: searchTerm,
          offset: paginationModel.pageIndex * paginationModel.pageSize,
          limit: paginationModel.pageSize,
          is_public:
            currentPromptFilter === 'all' || currentPromptFilter === 'archived'
              ? undefined
              : currentPromptFilter === 'public',
          is_archived: currentPromptFilter === 'archived' ? 'true' : 'false',
          sort_field: 'updated_at',
          sort_direction: 'desc',
          tag_value_id: selectedTags.map((t) => t.tag_value_id),
        }
      : null
  );

  const onRowClick = (row: RepoWithLookupsSchema, e) => {
    const repoLink = `/${appPromptsIndexPath}/${row.repo_handle}${
      organizationId ? `?organizationId=${tenant?.id}` : ''
    }`;
    if (!emulateNativeClick(repoLink, e)) {
      navigate(repoLink);
    }
  };

  const table = useReactTable({
    columns: DEFAULT_COLUMNS,
    data: repos?.repos ?? [],
    state: {
      pagination: paginationModel,
    },

    onPaginationChange: !props.simple ? setPaginationModel : undefined,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),

    columnResizeMode: 'onChange',

    enableSorting: true,
    enableExpanding: true,
    enableColumnFilters: true,

    manualExpanding: true,
    manualFiltering: false,
    manualSorting: true,
    manualPagination: true,

    pageCount: Math.ceil((repos?.total || 1) / paginationModel.pageSize),
  });

  const showNoData =
    repos?.total === 0 &&
    !(isLoading || selectedTenant.isLoading) &&
    !searchParams.get('q') &&
    selectedTags.length === 0;

  return (
    <div className={props.className}>
      {error && <Error>{error.message}</Error>}
      {!props.simple && (
        <div className="flex flex-row justify-between">
          <div className="flex flex-row gap-2">
            <PromptsFilterDropdown
              currentPromptFilter={currentPromptFilter}
              setCurrentPromptFilter={(promptFilter) => {
                setCurrentPromptFilter(promptFilter);
                setPaginationModel(() => props.defaultPagination);
              }}
            />
            <PromptsSearch
              onSubmit={(q: string) => {
                setSearchTerm(q);
                setSearchParams(
                  (prev) => {
                    prev.set('q', q);
                    return prev;
                  },
                  { replace: true }
                );
                setPaginationModel(() => props.defaultPagination);
              }}
            />
          </div>
          <DataGridColumnVisibilityPopover
            table={table}
            localStorageKey="ls:prompts-table-columns"
          />
        </div>
      )}
      {(isLoading || selectedTenant.isLoading) && !searchParams.get('q') ? (
        <div className="flex flex-col">
          <LinearProgress />
        </div>
      ) : (
        <div>
          {showNoData ? (
            <div>
              <NoData hasTags={false} />
              <div className="absolute bottom-10 left-10 w-[500px]">
                <BrowsePublicPromptsButton organizationId={organizationId} />
              </div>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              <div className="w-full overflow-x-auto rounded-lg border border-secondary">
                <DataGrid
                  table={table}
                  isLoading={isLoading || selectedTenant.isLoading}
                  cellHeight={props.simple ? 56 : 44}
                  onClick={onRowClick}
                  emptyState={<NoData hasTags={selectedTags.length > 0} />}
                  maxWidthFull={true}
                  stickyRightColumn={true}
                />
              </div>
              {!props.simple && (
                <div className="flex justify-between">
                  <BrowsePublicPromptsButton organizationId={organizationId} />
                  <div className="ml-auto flex flex-row gap-8">
                    <div className="my-auto pt-3">
                      Page {paginationModel.pageIndex + 1} of{' '}
                      {Math.ceil(
                        (repos?.total || 1) / paginationModel.pageSize
                      )}
                    </div>
                    <DataGridPagination table={table} />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
      {!!deletePromptHandle && (
        <DeletePromptModal
          handle={deletePromptHandle?.name}
          isPublic={deletePromptHandle?.isPublic}
          open={!!deletePromptHandle}
          onClose={() => {
            setDeletePromptHandle(null);
          }}
          onSuccess={() => {
            mutateRepos();
          }}
        />
      )}
    </div>
  );
}

export default PromptsTable;
