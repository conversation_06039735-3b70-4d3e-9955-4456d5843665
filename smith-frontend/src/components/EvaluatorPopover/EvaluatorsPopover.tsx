import {
  ChevronRightIcon,
  File02Icon,
  File05Icon,
  Trash02Icon,
} from '@langchain/untitled-ui-icons';
import { Checkbox, Tooltip } from '@mui/joy';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@radix-ui/react-dropdown-menu';

import { ArrowRightIcon, EllipsisVerticalIcon } from 'lucide-react';
import React, { ReactNode, useState } from 'react';
import { useNavigate } from 'react-router';

import { isInPlayground } from '@/Pages/DatasetSessionCompare/utils/isInPlayground';
import { RuleLogsPane } from '@/Pages/Settings/RuleLogsPane';
import { DeleteModal } from '@/components/Delete';
import { DatasetEvaluatorCrudPane } from '@/components/EvaluatorCrudPane/DatasetEvaluatorCrudPane';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { Skeleton } from '@/components/Skeleton';
import {
  useOrganizationId,
  useRunRules,
  useRunRulesMutation,
} from '@/hooks/useSwr';
import DatasetIcon from '@/icons/DatasetIcon';
import SimpleEditIcon from '@/icons/SimpleEditIcon.svg?react';
import {
  DatasetRuleMutateBody,
  RuleSchema,
  StructuredEvaluatorSchema,
  StructuredEvaluatorSchemaPrompt,
} from '@/types/schema';
import { apiRunsPath, appOrganizationPath } from '@/utils/constants';

import { defaultChatModelManifest } from '../../Pages/Playground/utils/Playground.utils';
import { RuleCrudPane } from '../../Pages/Settings/OrganizationRules';
import { CURRENT_RULE_EVALUATOR_VERSION } from '../../Pages/Settings/utils/constants';
import { DATASET_PREBUILTS, Prebuilt, SESSION_PREBUILTS } from './constants';

// Common types for shared components
interface CommonRulesDropdownProps {
  popoverProps?: {
    side?: 'top' | 'right' | 'bottom' | 'left';
    sideOffset?: number;
    align?: 'center' | 'end' | 'start';
  };
}

// For session-based configuration
interface SessionRulesDropdownProps extends CommonRulesDropdownProps {
  trigger: ReactNode;
  setllmAsJudgePaneOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setCustomCodePaneOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onEvaluatorUpdate: (evaluator: StructuredEvaluatorSchema) => void;
}

// For dataset-based configuration
interface DatasetRulesDropdownProps extends CommonRulesDropdownProps {
  trigger: ReactNode;
  datasetId: string;
  isRuleSelected?: (ruleId: string) => boolean;
  handleSelectRule?: (ruleId: string, isSelected: boolean) => void;
  checkable?: boolean;
}

type ExistingEvaluatorProps = {
  rule: RuleSchema;
  setSelectedRule: (rule: RuleSchema) => void;
  setToBeDeletedRule: (rule: RuleSchema) => void;
  isRuleSelected?: (ruleId: string) => boolean;
  handleSelectRule?: (ruleId: string, isSelected: boolean) => void;
  checkable?: boolean;
  handleOpenRuleLogsPane: (rule: RuleSchema) => void;
};

const ExistingEvaluatorRow = ({
  rule,
  isRuleSelected,
  handleSelectRule,
  setSelectedRule,
  setToBeDeletedRule,
  handleOpenRuleLogsPane,
  checkable = true,
}: ExistingEvaluatorProps) => {
  const hasFewShotEnabled = (rule.num_few_shot_examples ?? 0) > 0;
  const organizationId = useOrganizationId();
  const navigate = useNavigate();
  const isSelected = isRuleSelected?.(rule.id);
  const isPlayground = isInPlayground();
  return (
    <div className="flex items-center justify-between px-4">
      <div className="flex items-center gap-2">
        {checkable && (
          <Checkbox
            checked={isSelected}
            onChange={() => handleSelectRule?.(rule.id, !isSelected)}
            size={'sm'}
          />
        )}
        <div className="flex min-h-[38px] flex-col justify-center">
          <div className="flex items-center">
            <p className="text-xs font-semibold">{rule.display_name}</p>
          </div>
          <div className="flex">
            {hasFewShotEnabled && rule.corrections_dataset_id && (
              <div className="flex items-center">
                <div className="relative mr-1">
                  <div className="h-3 w-3 rounded-full bg-green-500 bg-opacity-20"></div>
                  <div className="absolute left-1/2 top-1/2 h-1.5 w-1.5 -translate-x-1/2 -translate-y-1/2 transform rounded-full bg-green-500"></div>
                </div>
                <a
                  href="https://docs.smith.langchain.com/evaluation/how_to_guides/create_few_shot_evaluators"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-0.5 pr-1 text-secondary hover:underline"
                >
                  <span className="font-extrathin text-[12px]">
                    Few-shot enabled
                  </span>
                  <ArrowRightIcon className="h-4 w-4 flex-shrink-0 -rotate-45" />
                </a>

                <button
                  type="button"
                  className="flex items-center rounded bg-tertiary pl-2 pr-1"
                  onClick={() =>
                    navigate(
                      `/${appOrganizationPath}/${organizationId}/datasets/${rule.corrections_dataset_id}`
                    )
                  }
                >
                  <DatasetIcon className="h-3 w-3 shrink-0" />
                  <span className="font-extrathin ml-1 text-[12px]">
                    Dataset
                  </span>
                  <ArrowRightIcon className="ml-0.5 h-3 w-3 flex-shrink-0" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="min-w-0 overflow-hidden text-ellipsis whitespace-nowrap">
        <DropdownMenu>
          <DropdownMenuTrigger
            onClick={(e) => e.stopPropagation()}
            className="flex h-6 w-6 items-center justify-center rounded-md transition-colors hover:bg-secondary focus:outline-none"
          >
            <EllipsisVerticalIcon className="h-5 w-5" />
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="z-10 cursor-pointer rounded border border-primary bg-primary pb-0.5 pt-0.5"
            onClick={(e) => e.stopPropagation()}
            side="right"
            align="start"
          >
            {!isPlayground && (
              <DropdownMenuItem
                onClick={() => handleOpenRuleLogsPane(rule)}
                className="flex items-center gap-2 rounded pb-0.5 pl-2 pr-2 pt-0.5 text-xs hover:bg-tertiary"
              >
                <File02Icon className="size-4 text-tertiary" />
                Logs
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={() => setSelectedRule(rule)}
              className="flex items-center gap-2 rounded pb-0.5 pl-2 pr-2 pt-0.5 text-xs hover:bg-tertiary"
            >
              <SimpleEditIcon className="size-4 text-tertiary" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => setToBeDeletedRule(rule)}
              className="flex items-center gap-2 rounded pb-0.5 pl-2 pr-2 pt-0.5 text-xs hover:bg-tertiary"
            >
              <Trash02Icon className="size-4 text-tertiary" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

// Extracted components for cleaner UI pattern reuse
interface SectionHeadingProps {
  children: React.ReactNode;
}

function SectionHeading({ children }: SectionHeadingProps) {
  return (
    <h1 className="my-1.5 bg-tertiary px-4 py-0.5 text-sm font-medium uppercase text-quaternary">
      {children}
    </h1>
  );
}

interface EvaluatorOptionProps {
  title: string;
  description: string;
  onClick: () => void;
  badge?: {
    text: string;
  };
  rightContent?: ReactNode;
}

function EvaluatorOption({
  title,
  description,
  onClick,
  badge,
  rightContent,
}: EvaluatorOptionProps) {
  return (
    <div
      className="mx-2 flex cursor-pointer items-center justify-between rounded-md p-2 hover:bg-tertiary"
      onClick={() => {
        onClick();
      }}
    >
      <div>
        <div className="flex items-center gap-1">
          <h3 className="text-xs font-semibold text-primary">{title}</h3>
          {badge && (
            <span className="rounded bg-tertiary px-1 text-[10px] font-semibold text-tertiary">
              {badge.text}
            </span>
          )}
        </div>
        <p className="text-xxs text-secondary">{description}</p>
      </div>
      {rightContent}
    </div>
  );
}

type SelectedType = 'llm' | 'code';
// New shared component for dropdown content (extracted from lines 355-392)
interface RulesDropdownContentProps {
  prebuilts: Prebuilt[];
  onSelectType: (type: SelectedType) => void;
  onSelectPrebuilt: (prebuilt: Prebuilt) => void;
  existingEvaluatorsSection: ReactNode;
}

function RulesDropdownContent({
  prebuilts,
  onSelectType,
  onSelectPrebuilt,
  existingEvaluatorsSection,
}: RulesDropdownContentProps) {
  return (
    <div>
      <div className="flex p-4 pb-2">
        <div className="flex-1">
          <h1 className="text-sm font-bold">Evaluators</h1>
          <p className="text-xs text-tertiary">
            Measure the performance of your application
          </p>
        </div>
        <Tooltip title={'Documentation'}>
          <a
            href="https://docs.smith.langchain.com/evaluation/how_to_guides/llm_as_judge?mode=ui"
            target="_blank"
            rel="noopener noreferrer"
            className="self-center rounded-md border border-secondary p-1 hover:bg-secondary"
          >
            <File05Icon className="size-5 text-tertiary" />
          </a>
        </Tooltip>
      </div>

      {existingEvaluatorsSection}

      <SectionHeading>Create your own</SectionHeading>

      <EvaluatorOption
        title="LLM as judge"
        description="Evaluate using an LLM"
        onClick={() => onSelectType('llm')}
        rightContent={<ChevronRightIcon className="size-5 text-tertiary" />}
      />

      <EvaluatorOption
        title="Custom code"
        description="Create a custom Python evaluator"
        onClick={() => onSelectType('code')}
        rightContent={<ChevronRightIcon className="size-5 text-tertiary" />}
      />

      <SectionHeading>Prebuilt Evaluators</SectionHeading>

      {prebuilts.map((p, index) => (
        <EvaluatorOption
          key={index}
          title={p.name}
          description={p.description}
          onClick={() => onSelectPrebuilt(p)}
          badge={{ text: p.type === 'llm' ? 'LLM as judge' : 'Code' }}
        />
      ))}
    </div>
  );
}

export const DatasetRulesDropdown = ({
  trigger,
  datasetId,

  // Used for playground
  isRuleSelected,
  handleSelectRule,
  checkable,
  popoverProps,
}: DatasetRulesDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [evaluatorPaneOpen, setEvaluatorPaneOpen] = useState(false);
  const [customCodePaneOpen, setCustomCodePaneOpen] = useState(false);
  const [ruleLogsPaneOpen, setRuleLogsPaneOpen] = useState(false);

  const [showAllEvaluators, setShowAllEvaluators] = useState(false);
  const [selectedRule, setSelectedRule] = useState<
    RuleSchema | DatasetRuleMutateBody | undefined
  >(undefined);
  const [toBeDeletedRule, setToBeDeletedRule] = useState<
    RuleSchema | undefined
  >(undefined);
  const [ruleLogsRule, setRuleLogsRule] = useState<RuleSchema | undefined>(
    undefined
  );

  const { data: rules, mutate } = useRunRules({ dataset_id: datasetId });

  const createRuleMutation = useRunRulesMutation();
  const isPlayground = isInPlayground();

  const handleSelectType = (type: SelectedType) => {
    if (type === 'llm') {
      setEvaluatorPaneOpen(true);
    } else if (type === 'code') {
      setCustomCodePaneOpen(true);
    }
  };

  const handleSelectPrebuilt = (p: Prebuilt) => {
    // Create a predefined structured evaluator configuration
    const structuredEvaluator: StructuredEvaluatorSchemaPrompt = {
      model: defaultChatModelManifest,
      schema: p.schema,
      prompt: p.prompt,
      template_format: 'mustache',
      variable_mapping: p.variable_mapping,
    };

    // For datasets, create the rule first then add it
    const newRule: DatasetRuleMutateBody = {
      display_name: p.name,
      is_enabled: true,
      evaluators: [{ structured: structuredEvaluator }],
      sampling_rate: 1,
      filter: '',
      dataset_id: datasetId,
    };

    setSelectedRule(newRule);
    setEvaluatorPaneOpen(true);
  };

  const ruleId =
    selectedRule && 'id' in selectedRule ? selectedRule.id : undefined;
  const evaluatorVersion =
    selectedRule && 'evaluator_version' in selectedRule
      ? selectedRule.evaluator_version ?? CURRENT_RULE_EVALUATOR_VERSION
      : CURRENT_RULE_EVALUATOR_VERSION;

  return (
    <>
      <Popover
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          setShowAllEvaluators(false);
        }}
      >
        <PopoverTrigger asChild>
          <div>{trigger}</div>
        </PopoverTrigger>
        <PopoverContent
          className="max-h-[500px] w-[340px] overflow-auto rounded-lg border border-secondary bg-primary p-0 pb-2"
          sideOffset={4}
          align="end"
          {...popoverProps}
        >
          {/* Add Evaluators */}
          <RulesDropdownContent
            prebuilts={DATASET_PREBUILTS}
            onSelectType={handleSelectType}
            onSelectPrebuilt={handleSelectPrebuilt}
            existingEvaluatorsSection={
              rules &&
              rules.length > 0 && (
                <>
                  <SectionHeading>Current evaluators</SectionHeading>

                  {createRuleMutation.isMutating && (
                    <Skeleton className="mx-4 mb-1.5 mt-2.5 h-5 w-[calc(100%-32px)]" />
                  )}

                  {(showAllEvaluators ? rules : rules.slice(0, 3)).map((r) => (
                    <ExistingEvaluatorRow
                      key={r.id}
                      rule={r}
                      isRuleSelected={isRuleSelected}
                      handleSelectRule={handleSelectRule}
                      setSelectedRule={(rule) => {
                        setSelectedRule(rule);
                        if (rule?.evaluators && rule.evaluators.length > 0) {
                          setEvaluatorPaneOpen(true);
                        } else if (
                          rule?.code_evaluators &&
                          rule.code_evaluators.length > 0
                        ) {
                          setCustomCodePaneOpen(true);
                        }
                      }}
                      setToBeDeletedRule={setToBeDeletedRule}
                      handleOpenRuleLogsPane={(rule) => {
                        setRuleLogsRule(rule);
                        setRuleLogsPaneOpen(true);
                      }}
                      checkable={checkable}
                    />
                  ))}
                  {rules.length > 3 && (
                    <div
                      className="cursor-pointer px-4 py-2 text-xs text-tertiary"
                      onClick={() => setShowAllEvaluators(!showAllEvaluators)}
                    >
                      {showAllEvaluators ? 'Show less ...' : 'Show all ...'}
                    </div>
                  )}
                </>
              )
            }
          />
        </PopoverContent>
      </Popover>

      <DeleteModal
        modalTitle="Delete Evaluator"
        endpoint={`${apiRunsPath}/rules`}
        id={toBeDeletedRule?.id ?? ''}
        isOpen={!!toBeDeletedRule}
        doClose={() => {
          setToBeDeletedRule(undefined);
        }}
        name={toBeDeletedRule?.display_name}
        onSuccess={() => {
          mutate(rules?.filter((r) => r.id !== toBeDeletedRule?.id) ?? []);
          handleSelectRule?.(toBeDeletedRule?.id ?? '', false);
        }}
      />

      <DatasetEvaluatorCrudPane
        datasetId={datasetId}
        evaluatorRule={selectedRule ?? undefined}
        evaluatorVersion={evaluatorVersion}
        open={evaluatorPaneOpen}
        setOpen={(open) => {
          setEvaluatorPaneOpen(open);
          if (!open) {
            setSelectedRule(undefined);
          }
        }}
        onRuleSaved={(ruleId) => {
          handleSelectRule?.(ruleId, true);
          mutate();
        }}
      />
      {/* Custom Code Evaluator */}
      {/* TODO: clean up RuleCrudPane to remove llm as judge */}
      <RuleCrudPane
        datasetId={datasetId}
        ruleId={ruleId}
        evaluatorVersion={evaluatorVersion}
        onClose={() => {
          setCustomCodePaneOpen(false);
          setSelectedRule(undefined);
        }}
        variant="dataset"
        templateRule={undefined}
        rule={(selectedRule as DatasetRuleMutateBody) ?? undefined}
        open={customCodePaneOpen}
        ruleType="custom_code"
        // only call onAddRule if the rule doesn't exist
        onSuccess={() => {
          mutate();
        }}
      />
      {!isPlayground && (
        <RuleLogsPane
          rule={ruleLogsRule}
          open={ruleLogsPaneOpen}
          onClose={() => setRuleLogsPaneOpen(false)}
        />
      )}
    </>
  );
};

// New SessionRulesDropdown component
export const SessionRulesDropdown = ({
  trigger,
  setllmAsJudgePaneOpen,
  setCustomCodePaneOpen,
  onEvaluatorUpdate,
  popoverProps,
}: SessionRulesDropdownProps) => {
  const [open, setOpen] = useState(false);

  const handleSelectType = (type: SelectedType) => {
    if (type === 'llm') {
      setllmAsJudgePaneOpen?.(true);
    } else if (type === 'code') {
      setCustomCodePaneOpen?.(true);
    }
  };

  const handleSelectPrebuilt = (prebuilt: Prebuilt) => {
    if (prebuilt.type === 'llm') {
      const structuredEvaluator: StructuredEvaluatorSchemaPrompt = {
        model: defaultChatModelManifest,
        schema: prebuilt.schema,
        prompt: prebuilt.prompt,
        template_format: 'mustache',
        variable_mapping: prebuilt.variable_mapping,
      };
      onEvaluatorUpdate?.(structuredEvaluator);
      setllmAsJudgePaneOpen(true);
    }
    setOpen(false);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    >
      <PopoverTrigger asChild>
        <div>{trigger}</div>
      </PopoverTrigger>
      <PopoverContent
        className="max-h-[550px] w-[340px] overflow-auto rounded-lg border border-secondary bg-primary p-0 pb-2"
        sideOffset={4}
        align="end"
        {...popoverProps}
      >
        <RulesDropdownContent
          prebuilts={SESSION_PREBUILTS}
          onSelectType={handleSelectType}
          onSelectPrebuilt={handleSelectPrebuilt}
          existingEvaluatorsSection={<></>}
        />
      </PopoverContent>
    </Popover>
  );
};
