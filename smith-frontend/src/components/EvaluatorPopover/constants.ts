import {
  CODE_CORRECTNESS_PROMPT,
  CONCISENESS_PROMPT,
  CORRECTNESS_PROMPT,
  HALLUCINATION_PROMPT,
} from 'openevals/prompts';

import { MessageTuple } from '@/types/schema';
import { convertFStringToMustache } from '@/utils/template';

import { SchemaDefinition } from '../EvaluatorCrudPane/utils/serializeFeedbackUIToLC';

export interface Prebuilt {
  name: string;
  description: string;
  schema: SchemaDefinition;
  prompt: MessageTuple[];
  variable_mapping: Record<string, string>;
  type: 'llm' | 'code';
}

const DEFAULT_FEEDBACK_SCHEMA = {
  conciseness: {
    type: 'object',
    title: 'Conciseness Score',
    description:
      'An evaluation of how efficiently the output conveys the required information without any unnecessary elements.',
    properties: {
      comment: {
        type: 'string',
        description:
          'A detailed analysis that: 1) Identifies any unnecessary elements (hedging, pleasantries, meta-commentary, etc.), 2) Notes any redundant or extraneous information, 3) Evaluates if explanations were explicitly requested, 4) Analyzes word efficiency, and 5) Ends with "Thus, the score should be: X/10" where X reflects how close the response comes to perfect conciseness.',
      },
      conciseness: {
        type: 'number',
        description:
          'Evaluates how efficiently the output conveys the required information without any unnecessary elements. Min (0): Extremely verbose with many unnecessary elements. | Max (10): Contains only essential requested information with no extra words.',
        minimum: 0,
        maximum: 10,
      },
    },
    required: ['comment', 'conciseness'],
  },
  correctness: {
    type: 'object',
    title: 'Correctness Score',
    description:
      'An evaluation of whether the output is factually correct and complete when compared to the reference output.',
    properties: {
      comment: {
        type: 'string',
        description:
          'A detailed analysis that: 1) Evaluates factual accuracy of all claims, 2) Checks completeness against the input question, 3) Compares against reference output for accuracy, 4) Notes any errors, missing information, or inconsistencies, and 5) Ends with "Thus, the score should be: TRUE/FALSE" based on whether the response meets all correctness criteria.',
      },
      correctness: {
        type: 'boolean',
        description:
          'TRUE if the output is factually accurate, complete, logically consistent, and matches the reference output in key information. FALSE if there are any factual errors, missing information, logical inconsistencies, or significant deviations from the reference output.',
      },
    },
    required: ['comment', 'correctness'],
  },
  hallucination: {
    type: 'object',
    title: 'Hallucination Score',
    description:
      'An evaluation of whether the output contains any hallucinated information not supported by the input context.',
    properties: {
      comment: {
        type: 'string',
        description:
          'A detailed analysis that: 1) Lists any claims made in the output, 2) Identifies which claims are supported/unsupported by the input context, 3) Notes any contradictions or speculative additions, and 4) Ends with "Thus, the score should be: TRUE/FALSE" based on whether any hallucinations were found.',
      },
      hallucination: {
        type: 'boolean',
        description:
          'TRUE if the output contains any hallucinations (unsupported claims, contradictions, speculative details, or inaccurate facts). FALSE if all claims are directly verifiable from the input context.',
      },
    },
    required: ['comment', 'hallucination'],
  },
  code_checker: {
    type: 'object',
    title: 'Code Correctness Score',
    description:
      'An evaluation of whether the code solution correctly and completely solves the given problem.',
    properties: {
      comment: {
        type: 'string',
        description:
          'A detailed analysis that: 1) Verifies the code solves all requirements from the input, 2) Checks for bugs, logical errors, and edge cases, 3) Evaluates implementation efficiency and best practices, 4) Confirms syntax correctness and executability, 5) Notes any non-code content, and 6) Ends with "Thus, the score should be: TRUE/FALSE" based on whether the code meets all correctness criteria.',
      },
      code_checker: {
        type: 'boolean',
        description:
          'TRUE if the code is complete, correct, handles all edge cases, has no bugs, uses appropriate implementations, and would execute without errors. FALSE if there are any bugs, missing requirements, inefficiencies, syntax errors, or non-code content.',
      },
    },
    required: ['comment', 'code_checker'],
  },
};

const PREBUILTS = (rulesSource: 'session' | 'dataset') => [
  {
    name: 'Hallucination',
    description: 'Evaluate whether an answer hallucinates facts.',
    schema: DEFAULT_FEEDBACK_SCHEMA['hallucination'],
    prompt: [
      [
        'system',
        convertFStringToMustache(HALLUCINATION_PROMPT),
      ] as MessageTuple,
    ],
    variable_mapping: {
      inputs: 'input',
      outputs: 'output',
      ...(rulesSource === 'dataset' && {
        reference_outputs: 'referenceOutput',
      }),
    },
    type: 'llm' as const,
  },
  {
    name: 'Conciseness',
    description:
      'Evaluate whether an answer is a concise response to a question.',
    schema: DEFAULT_FEEDBACK_SCHEMA['conciseness'],
    prompt: [
      ['system', convertFStringToMustache(CONCISENESS_PROMPT)] as MessageTuple,
    ],
    variable_mapping: {
      inputs: 'input',
      outputs: 'output',
    },
    type: 'llm' as const,
  },
  {
    name: 'Code Checker',
    description: 'Evaluate whether code produced solves the question posed.',
    schema: DEFAULT_FEEDBACK_SCHEMA['code_checker'],
    prompt: [
      [
        'system',
        convertFStringToMustache(CODE_CORRECTNESS_PROMPT),
      ] as MessageTuple,
    ],
    variable_mapping: {
      inputs: 'input',
      outputs: 'output',
    },
    type: 'llm' as const,
  },
];

export const SESSION_PREBUILTS = PREBUILTS('session');
export const DATASET_PREBUILTS = [
  {
    name: 'Correctness',
    description:
      'Evaluate whether an answer semantically matches a reference answer.',
    schema: DEFAULT_FEEDBACK_SCHEMA['correctness'],
    prompt: [
      ['system', convertFStringToMustache(CORRECTNESS_PROMPT)] as MessageTuple,
    ],
    variable_mapping: {
      inputs: 'input',
      outputs: 'output',
      reference_outputs: 'referenceOutput',
    },
    type: 'llm' as const,
  },
  ...PREBUILTS('dataset'),
];
