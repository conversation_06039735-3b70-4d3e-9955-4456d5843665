import { TemplateFormat } from '@langchain/core/prompts';
import { Tooltip } from '@mui/joy';

import { CodeIcon } from 'lucide-react';
import { FC, ReactNode, RefObject } from 'react';
import { z } from 'zod';

import TrashIcon from '@/icons/TrashIcon.svg?react';
import { MessageCapability, MessageFields } from '@/types/schema';

import { CopyButton } from '../CopyButton/CopyButton';
import {
  OpenAIToolCallsKwargsSchema,
  ToolCallsKwargsSchema,
} from '../Message.utils';
import { RichTextImageEditorVariant } from '../RichTextEditor/MultimodalDropdown/types';
import { MimeType } from '../RichTextEditor/constants';
import { AttachmentName } from '../RichTextEditor/types';
import MessageType from './MessageType';
import { EMessageType } from './types';
import { generateCallID } from './utils';

type ToolCall =
  | MessageFields['additional_kwargs']
  | { tool_calls: MessageFields['tool_calls'] };

type TProps = {
  hideMessageTypeSwitch: boolean;
  readOnly: boolean;
  variant: RichTextImageEditorVariant | undefined;
  messageType: EMessageType | undefined;
  messageContentAsText: string | undefined;
  messageRole: string | undefined;
  onMessageTypeChange: (value: EMessageType | undefined) => void;
  onDelete?: () => void;
  toolCallsValue: ToolCall | undefined;
  onAddToolCall: (value: ToolCall) => void;
  actions?: ReactNode;
  capabilities?: MessageCapability[];
  templateFormat: TemplateFormat | undefined;
  uploadRef: RefObject<((type: MimeType, src: string) => void) | undefined>;
  expandToggle?: ReactNode;
  attachmentNames: AttachmentName[];
  disabledMessageTypes?: EMessageType[];
};

export const TopRowButtons: FC<TProps> = ({
  messageType,
  messageContentAsText,
  messageRole,
  hideMessageTypeSwitch,
  readOnly,
  variant,
  onDelete,
  toolCallsValue,
  onAddToolCall,
  onMessageTypeChange,
  actions,
  expandToggle,
  disabledMessageTypes,
}) => {
  const addToolCall = () => {
    const hasTools = !!toolCallsValue?.tool_calls;
    const toolCallsOpenAi =
      OpenAIToolCallsKwargsSchema.safeParse(toolCallsValue);
    // this will only pass if there are existing tool calls and they meet the OpenAIToolCallsKwargsSchema
    // todo: does there need to be a way to add a tool of this schema if there are no existing tool calls?
    if (toolCallsOpenAi.success) {
      return onAddToolCall({
        tool_calls: [
          ...(hasTools && toolCallsOpenAi.success
            ? toolCallsOpenAi.data.tool_calls
            : []),
          {
            function: {
              arguments: '{}',
              name: '',
            },
            id: generateCallID(),
            type: 'function',
          },
        ],
      });
    }
    const toolCalls = ToolCallsKwargsSchema.safeParse(toolCallsValue);
    if (!toolCalls.success && hasTools) return;
    onAddToolCall({
      tool_calls: [
        ...(hasTools && toolCalls.success ? toolCalls.data.tool_calls : []),
        {
          args: {},
          name: '',
          id: generateCallID(),
        },
      ],
    } satisfies z.infer<typeof ToolCallsKwargsSchema>);
  };

  return (
    <div className="flex items-center justify-between">
      <MessageType
        hideMessageTypeSwitch={!!hideMessageTypeSwitch}
        readOnly={!!readOnly}
        messageType={messageType ?? EMessageType.HUMAN}
        messageRole={messageRole}
        onChange={onMessageTypeChange}
        isTemplate={variant === 'template'}
        disabledMessageTypes={disabledMessageTypes}
      />
      <div className="flex items-center gap-1">
        <div className="flex items-start gap-1 opacity-0 transition-all focus-within:opacity-100 group-hover:opacity-100 [&:has([data-state='open'])]:opacity-100">
          {!readOnly && onDelete && (
            <>
              {messageType === EMessageType.AI && (
                <Tooltip title="Add tool call" placement="top">
                  <button
                    type="button"
                    onClick={addToolCall}
                    className="flex items-center justify-center rounded-md p-1 transition duration-75 hover:bg-secondary-hover"
                  >
                    <CodeIcon className="h-4 w-4" />
                  </button>
                </Tooltip>
              )}
              <Tooltip title="Delete message" placement="top">
                <button
                  type="button"
                  onClick={onDelete}
                  className="flex items-center justify-center rounded-md p-1 transition duration-75 hover:bg-secondary-hover"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </Tooltip>
            </>
          )}
          <CopyButton
            variant="icon"
            copy={messageContentAsText ?? ''}
            sx={{ userSelect: 'none' }}
          />
          {actions}
        </div>
        {expandToggle}
      </div>
    </div>
  );
};
