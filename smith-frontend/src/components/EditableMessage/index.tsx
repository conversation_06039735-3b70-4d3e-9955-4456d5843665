import { TemplateFormat } from '@langchain/core/prompts';
import {
  ChevronDownIcon,
  ChevronRightIcon,
} from '@langchain/untitled-ui-icons';
import { Input, Tooltip } from '@mui/joy';

import { MutableRefObject, ReactNode, useMemo, useRef, useState } from 'react';

import { convertAttachmentNameToTemplateVar } from '@/Pages/Playground/utils/Playground.utils';
import { CodeData } from '@/components/Code/Code';
import { useAttachmentMetadataBulk } from '@/hooks/useAttachmentMetadata';
import {
  MessageCapability,
  MessageContentPart,
  MessageFields,
  MessageUnionType,
  S3DataSchema,
  StoredMessage,
  ToolCall,
} from '@/types/schema';
import {
  getMessageContent,
  getMessageContentAsText,
  getMessageFields,
  getMessageType,
} from '@/utils/messages';
import { cn } from '@/utils/tailwind';
import { tryParseJSON } from '@/utils/try-parse-json';

import CanvasModal from '../CanvasModal';
import PromptCanvasButton from '../CanvasModal/PromptCanvasButton';
import CollapsedMessage from '../CollapsedMessage';
import { RichTextEditorVariableMappingProps } from '../EvaluatorCrudPane/types';
import { MultimodalDropdown } from '../RichTextEditor/MultimodalDropdown/MultimodalDropdown';
import { MultimodalButton } from '../RichTextEditor/MultimodalDropdown/components/MultimodalButton';
import { RichTextImageEditorVariant } from '../RichTextEditor/MultimodalDropdown/types';
import { RichTextEditor } from '../RichTextEditor/RichTextEditor';
import { MimeType, SUPPORTED_MIME_TYPES } from '../RichTextEditor/constants';
import { SuggestionsConfig } from '../RichTextEditor/suggestions/Suggestions';
import { AttachmentName } from '../RichTextEditor/types';
import CallIdInput from './CallIdInput';
import CodeFunctionCallEditor from './CodeFunctionCallEditor';
import FunctionNameInput from './FunctionNameInput';
import { TopRowButtons } from './TopRowButtons';
import { INPUT_SX } from './constants';
import { EMessageType } from './types';

/**
 * @deprecated Use the primary Message component instead
 */
export function EditableMessage({
  variant,
  templateFormat,
  messageValue,
  onChange,
  onDelete,
  readOnly,
  hideSyntaxHighlighting,
  className,
  actions,
  outerActions,
  attachments,
  capabilities,
  useSuggestions,
  suggestions,
  hideMessageTypeSwitch,
  isCollapsed,
  toggleCollapsed,
  refreshContentRef,
  disabledMessageTypes,
  variableMappingProps,
}: {
  variant?: RichTextImageEditorVariant;
  templateFormat?: TemplateFormat | undefined;
  messageValue: MessageUnionType;
  onChange: (value: MessageUnionType) => void;
  onDelete?: () => void;
  readOnly?: boolean;
  hideSyntaxHighlighting?: boolean;
  className?: string;
  // rendered in the top row
  actions?: ReactNode;
  // rendered outside of the message
  outerActions?: ReactNode;
  attachments?: Record<string, S3DataSchema>;
  capabilities?: MessageCapability[];
  useSuggestions?: boolean;
  suggestions?: SuggestionsConfig;
  hideMessageTypeSwitch?: boolean;
  refreshContentRef?: MutableRefObject<(content: string) => void>;
  isCollapsed?: boolean;
  toggleCollapsed?: () => void;
  disabledMessageTypes?: EMessageType[];
  variableMappingProps?: RichTextEditorVariableMappingProps;
}) {
  const { messageFields, messageType, messageContent } = useMemo(() => {
    const messageFields = getMessageFields(messageValue);
    return {
      messageFields,
      messageType: getMessageType(messageValue),
      messageContent: getMessageContent(messageValue),
    };
  }, [messageValue]);

  const { additionalKwargs, toolCalls } = useMemo(() => {
    const additionalKwargs = messageFields?.additional_kwargs;
    const toolCalls = messageFields?.tool_calls;
    return { additionalKwargs, toolCalls };
  }, [messageFields]);

  // Prefer the kwargs tool_calls if they exist and have a valid name
  // Fall back to additional_kwargs if:
  //   1. Primary tool_calls are empty or lack a name, AND
  //   2. additional_kwargs contains valid tool_calls with a function name (typically from OpenAI)
  const shouldUseAdditionalKwargs = useMemo(() => {
    const toolCallsEmpty = !toolCalls?.length || !toolCalls[0].name;
    const hasAdditionalKwargs = !!Object.keys(additionalKwargs ?? {}).length;
    const hasAdditionalKwargsToolCalls =
      !!additionalKwargs?.tool_calls?.length &&
      !!additionalKwargs.tool_calls[0].function.name;
    return (
      toolCallsEmpty && hasAdditionalKwargs && hasAdditionalKwargsToolCalls
    );
  }, [toolCalls, additionalKwargs]);

  const { toolCallsValue, getToolCallsData } = useMemo(() => {
    if (shouldUseAdditionalKwargs) {
      return {
        toolCallsValue: additionalKwargs,
        getToolCallsData: (value) => ({
          additional_kwargs: value as MessageFields['additional_kwargs'],
        }),
      };
    }
    return {
      toolCallsValue: { tool_calls: toolCalls },
      getToolCallsData: (value) => ({
        tool_calls: value?.tool_calls as ToolCall[],
      }),
    };
  }, [additionalKwargs, toolCalls, shouldUseAdditionalKwargs]);

  const handleToolCallsChange = useMemo(() => {
    return (
      value:
        | MessageFields['additional_kwargs']
        | { tool_calls: MessageFields['tool_calls'] }
    ) => {
      const message: StoredMessage = {
        type: messageType,
        data: {
          // message fields should override content
          content: '',
          ...messageFields,
          ...getToolCallsData(value),
        },
      };
      onChange(message);
    };
  }, [messageFields, getToolCallsData, onChange, messageType]);

  const isFunctionOrTool =
    additionalKwargs?.tool_calls ||
    additionalKwargs?.function_call ||
    (toolCalls || []).length > 0;
  const uploadRef = useRef<((type: MimeType, src: string) => void) | null>(
    null
  );

  const [canvasOpen, setCanvasOpen] = useState(false);

  // If changing to placeholder role, clear the content, but save it in the originalContent we change the role to something else
  const [originalContent, setOriginalContent] = useState<
    string | Array<MessageContentPart> | undefined
  >(messageContent);

  const handleMessageTypeChange = (value: string | undefined) => {
    if (value === 'messages list') {
      setOriginalContent(messageContent);
      onChange({
        type: value,
        data: { content: '' },
      });
    } else if (originalContent !== messageContent) {
      onChange({
        type: value,
        data: {
          ...getMessageFields(messageValue),
          content: originalContent ?? '',
        },
      });
    } else {
      onChange({
        data: getMessageFields(messageValue) ?? { content: '' },
        type: value ?? 'human',
      });
    }
  };

  const { data: attachmentsMetadata } = useAttachmentMetadataBulk(
    attachments
      ? Object.values(attachments).map((attachment) => attachment.presigned_url)
      : []
  );

  const attachmentNames: AttachmentName[] = useMemo(() => {
    if (!attachments || !attachmentsMetadata?.length) return [];

    return Object.entries(attachments)
      .map(([key, _], index) => {
        const metadata = attachmentsMetadata[index];
        if (!metadata?.contentType) return null;
        const contentType = metadata.contentType;
        if (
          !SUPPORTED_MIME_TYPES.some((mimeType) =>
            contentType.startsWith(mimeType)
          )
        ) {
          return null;
        }
        return {
          mimeType: metadata.contentType as MimeType,
          name: convertAttachmentNameToTemplateVar(key),
        };
      })
      .filter((item): item is AttachmentName => item !== null);
  }, [attachments, attachmentsMetadata]);

  const textEditor = useMemo(() => {
    return (
      <div className="flex flex-row items-end justify-between">
        <RichTextEditor
          variant={variant ?? 'inline'}
          templateFormat={templateFormat}
          autoFocus={false}
          uploadRef={uploadRef}
          readOnly={readOnly}
          hideSyntaxHighlighting={hideSyntaxHighlighting}
          initialValue={messageContent}
          attachments={attachments}
          onChange={(content) => {
            onChange({
              type: messageType,
              data: { ...getMessageFields(messageValue), content },
            });
          }}
          capabilities={capabilities}
          useSuggestions={useSuggestions}
          suggestions={suggestions}
          refreshContentRef={refreshContentRef}
          smallText
          variableMappingProps={variableMappingProps}
        />
        <div className="flex flex-row items-center gap-1">
          {typeof messageContent === 'string' &&
            messageContent &&
            capabilities?.includes(MessageCapability.CANVAS) &&
            !readOnly && (
              <PromptCanvasButton
                className={className}
                setCanvasOpen={setCanvasOpen}
              />
            )}
          {capabilities?.includes(MessageCapability.IMAGE) && (
            <MultimodalDropdown
              onUpload={(type, src) => {
                uploadRef.current?.(type, src);
              }}
              templateFormat={templateFormat ?? 'f-string'}
              attachmentNames={attachmentNames}
            >
              <MultimodalButton />
            </MultimodalDropdown>
          )}
        </div>
      </div>
    );
  }, [
    variant,
    templateFormat,
    readOnly,
    hideSyntaxHighlighting,
    messageContent,
    attachments,
    capabilities,
    useSuggestions,
    suggestions,
    refreshContentRef,
    variableMappingProps,
    attachmentNames,
    className,
    onChange,
    messageType,
    messageValue,
  ]);

  const expandToggle = useMemo(() => {
    if (!toggleCollapsed) return null;
    return (
      <div className="flex items-center justify-center rounded-md text-secondary hover:bg-primary-hover">
        <Tooltip title={isCollapsed ? 'Expand' : 'Collapse'} placement="top">
          <button
            onClick={(e) => {
              // clicking on collapsed message also triggers the collapse
              // so we need to stop the event from bubbling up
              e.stopPropagation();
              toggleCollapsed();
            }}
            type="button"
          >
            {isCollapsed ? (
              <ChevronRightIcon className="h-4 w-4 " />
            ) : (
              <ChevronDownIcon className="h-4 w-4" />
            )}
          </button>
        </Tooltip>
      </div>
    );
  }, [isCollapsed, toggleCollapsed]);

  const toCopy = useMemo(() => {
    return getMessageContentAsText(messageValue) ?? '';
  }, [messageValue]);

  const content = () => {
    if (isCollapsed) {
      return (
        <div className={cn('grow', className)}>
          <CollapsedMessage
            onCollapse={() => toggleCollapsed?.()}
            role={messageType ?? ''}
            toCopy={toCopy}
            actions={actions}
            expandToggle={expandToggle}
            isSchemaEditorVisible={false}
            readOnly={readOnly}
          />
        </div>
      );
    }
    return (
      <div
        className={cn(
          'group grow cursor-auto rounded-lg border border-secondary bg-background px-4 py-3 focus-within:border-brand hover:border-secondary hover:focus-within:border-brand',
          readOnly && 'bg-secondary text-tertiary',
          className
        )}
      >
        <TopRowButtons
          messageType={messageType}
          messageContentAsText={getMessageContentAsText(messageValue)}
          messageRole={getMessageFields(messageValue)?.role}
          hideMessageTypeSwitch={!!hideMessageTypeSwitch}
          readOnly={!!readOnly}
          variant={variant}
          onDelete={onDelete}
          toolCallsValue={toolCallsValue}
          onAddToolCall={handleToolCallsChange}
          onMessageTypeChange={handleMessageTypeChange}
          capabilities={capabilities}
          uploadRef={uploadRef}
          templateFormat={templateFormat}
          actions={actions}
          expandToggle={expandToggle}
          attachmentNames={attachmentNames}
          disabledMessageTypes={disabledMessageTypes}
        />

        {messageType === EMessageType.FUNCTION && (
          <div className="-mx-4 border-b border-secondary">
            <Input
              placeholder="Function Name"
              className="font-mono"
              sx={INPUT_SX}
              value={getMessageFields(messageValue)?.name ?? ''}
              onChange={(e) => {
                onChange({
                  type: messageType,
                  data: {
                    ...getMessageFields(messageValue),
                    content: messageContent ?? '{}',
                    name: e.target.value,
                  },
                });
              }}
            />
          </div>
        )}

        {messageType === EMessageType.TOOL && (
          <div className="-mx-4 border-b border-secondary">
            <div className="flex flex-wrap items-center">
              <FunctionNameInput
                value={getMessageFields(messageValue)?.name ?? ''}
                onChange={(name) =>
                  onChange({
                    type: messageType,
                    data: {
                      ...getMessageFields(messageValue),
                      content: messageContent ?? '{}',
                      name,
                    },
                  })
                }
              />

              <CallIdInput
                value={getMessageFields(messageValue)?.tool_call_id ?? ''}
                onChange={(value) => {
                  onChange({
                    type: messageType,
                    data: {
                      ...getMessageFields(messageValue),
                      content: messageContent ?? '{}',
                      tool_call_id: value,
                    },
                  });
                }}
              />
            </div>
          </div>
        )}

        {messageType === EMessageType.CHAT && !readOnly && (
          <div className="-mx-4 mb-3 border-b border-secondary">
            <Input
              placeholder="Role"
              className="font-mono"
              sx={INPUT_SX}
              value={getMessageFields(messageValue)?.role ?? ''}
              onChange={(e) => {
                onChange({
                  type: messageType,
                  data: {
                    ...getMessageFields(messageValue),
                    content: messageContent ?? '',
                    role: e.target.value,
                  },
                });
              }}
            />
          </div>
        )}

        <div className="p-0">
          {!isFunctionOrTool ? (
            messageType === EMessageType.FUNCTION ||
            messageType === EMessageType.TOOL ? (
              <div className="-mx-4 -mb-4">
                <CodeData
                  value={tryParseJSON(
                    getMessageContentAsText(messageValue) || '{}',
                    true
                  )}
                  readOnly={readOnly}
                  placeholder={'Enter your payload...'}
                  onChange={(content) => {
                    onChange({
                      type: messageType,
                      data: {
                        ...getMessageFields(messageValue),
                        content: JSON.stringify(content),
                      },
                    });
                  }}
                />
              </div>
            ) : messageType === EMessageType.PLACEHOLDER ? (
              <Input
                placeholder="variable_name"
                className="font-mono"
                sx={{ ...INPUT_SX, py: 0, px: 0 }}
                value={getMessageContentAsText(messageValue) ?? ''}
                readOnly={readOnly}
                onChange={(e) => {
                  onChange({
                    type: messageType,
                    data: {
                      ...getMessageFields(messageValue),
                      content: e.target.value,
                    },
                  });
                }}
              />
            ) : (
              textEditor
            )
          ) : (
            <>
              {textEditor}
              <div className="-mx-4 -mb-4 mt-2 border-t border-secondary">
                <CodeFunctionCallEditor
                  value={toolCallsValue}
                  onChange={handleToolCallsChange}
                  readOnly={readOnly}
                  placeholder={
                    messageType === EMessageType.FUNCTION
                      ? 'Enter your payload...'
                      : 'Enter your message...'
                  }
                />
              </div>
            </>
          )}
        </div>

        {typeof messageContent === 'string' &&
          capabilities?.includes(MessageCapability.CANVAS) && (
            <CanvasModal
              isOpen={canvasOpen}
              onClose={() => setCanvasOpen(false)}
              prompt={messageContent as string}
              setPrompt={(content) => {
                onChange({
                  type: messageType,
                  data: { ...getMessageFields(messageValue), content },
                });
                if (refreshContentRef?.current) {
                  refreshContentRef.current(content);
                }
              }}
              templateFormat={templateFormat ?? 'f-string'}
            />
          )}
      </div>
    );
  };

  return (
    <div className="group/actions flex flex-row items-start gap-1">
      {outerActions && (
        <div className="invisible group-hover/actions:visible">
          {outerActions}
        </div>
      )}
      {content()}
    </div>
  );
}
