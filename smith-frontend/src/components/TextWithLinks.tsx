import { useMemo, useState } from 'react';

import { MAX_TEXT_LENGTH } from '@/components/Code/constants';
import { parseTextWithLinks } from '@/utils/parseTextLinks';

import { TextVirtualizer } from './TextVirtualizer';

const TextWithLinksInner = ({ text }: { text: string }) => {
  const textParts = useMemo(() => parseTextWithLinks(text ?? ''), [text]);
  return (
    <>
      {textParts.map((part, index) => {
        if (part.type === 'link') {
          const href = part.url?.startsWith('http')
            ? part.url
            : `https://${part.url}`;
          return (
            <a
              key={index}
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="pointer-events-auto text-ls-blue underline"
              onClick={(e) => e.stopPropagation()}
            >
              {part.content}
            </a>
          );
        }
        return <span key={index}>{part.content}</span>;
      })}
    </>
  );
};

// Truncated version
export const TextWithLinks = (props: {
  text: string;
  containerHeight?: string;
  fullLength?: boolean;
}) => {
  const [isTruncated, setIsTruncated] = useState(true);
  const isTextLong = props.fullLength
    ? false
    : props.text.length > MAX_TEXT_LENGTH;
  const textToRender =
    isTextLong && isTruncated
      ? `${props.text.slice(0, MAX_TEXT_LENGTH)}...`
      : props.text;

  return isTextLong && props.containerHeight ? (
    <div style={{ height: props.containerHeight }}>
      <TextVirtualizer
        containerHeight={`calc(${props.containerHeight} - 20px)`}
        text={textToRender}
      />
      <button
        type="button"
        className="underline"
        onClick={() => setIsTruncated(!isTruncated)}
      >
        {isTruncated ? 'Show more' : 'Show less'}
      </button>
    </div>
  ) : (
    <TextWithLinksInner text={textToRender} />
  );
};
