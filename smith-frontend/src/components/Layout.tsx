import { LayoutLeftIcon } from '@langchain/untitled-ui-icons';

import { Dispatch, SetStateAction, forwardRef, useCallback } from 'react';
import { Link } from 'react-router-dom';

import { Logo } from '@/components/Logo/Logo';

import { TailwindAttributes, cn, utils } from '../utils/tailwind';
import { ToastProvider } from './Toast';

const SIDEBAR_EXPANDED_WIDTH = 233;
const SIDEBAR_COLLAPSED_WIDTH = 44;

const Root = ({ className, ...props }: TailwindAttributes) => {
  return (
    <ToastProvider>
      <div className={cn('', className)} {...props} />
    </ToastProvider>
  );
};

const Header = forwardRef<
  HTMLElement,
  TailwindAttributes & {
    expanded: boolean;
    setExpanded: Dispatch<SetStateAction<boolean>>;
  }
>(({ className, children, expanded, setExpanded, ...props }, ref) => {
  const toggleExpanded = useCallback(
    () => setExpanded((current) => !current),
    [setExpanded]
  );

  const logoComponent = (
    <span className="flex-shrink-0 whitespace-nowrap text-center leading-[28px]">
      <Logo size={expanded ? 'md' : 'xs'} />
    </span>
  );

  return (
    <header
      ref={ref}
      className={cn(
        'fixed bottom-0 left-0 top-0 flex flex-col items-stretch gap-3 overflow-hidden border-r border-r-secondary pb-3 pt-3 transition-all duration-200 ease-in-out',
        className
      )}
      style={{
        width: expanded
          ? `${SIDEBAR_EXPANDED_WIDTH}px`
          : `${SIDEBAR_COLLAPSED_WIDTH}px`,
      }}
      {...props}
    >
      <div
        className={cn(
          'flex min-h-[28px] flex-row items-center justify-between transition-all duration-200 ease-in-out',
          expanded ? 'pl-3 pr-1' : 'justify-center'
        )}
      >
        {expanded && <Link to="/">{logoComponent}</Link>}

        <button
          type="button"
          className={cn(
            utils.button,
            'flex h-8 w-8 flex-row items-center justify-center rounded-md p-2'
          )}
          title="Toggle Menu"
          onClick={toggleExpanded}
        >
          <LayoutLeftIcon />
        </button>
      </div>
      {children}
    </header>
  );
});

const Main = ({
  className,
  expanded,
  full,
  ...props
}: TailwindAttributes & { expanded: boolean; full?: boolean }) => {
  return (
    <main
      className={cn(
        'relative transition-all duration-200 ease-in-out',
        className
      )}
      style={{
        marginLeft: full
          ? 'auto'
          : expanded
          ? `${SIDEBAR_EXPANDED_WIDTH}px`
          : `${SIDEBAR_COLLAPSED_WIDTH}px`,
      }}
      {...props}
    />
  );
};

const Layout = {
  Root,
  Header,
  Main,
};

export default Layout;
