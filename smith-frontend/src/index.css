@import url('https://fonts.googleapis.com/css2?family=Fira+Code&family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* clash between MUI and Tailwind */
  input:focus,
  textarea:focus,
  select:focus {
    box-shadow: none !important;
  }

  /* Set default outline color to match our brand color */
  * {
    outline-color: hsl(var(--ls-brand-green-400));
  }

  /* Fix safari issues when overflow: hidden is set by popover content */
  body {
    min-height: 100vh;
  }

  /**
  * @radix-ui/react-dismissable-layer sets this property, which: 
  * 1. prevents non-radix components (@mui/joy) using Portal to accept pointer events
  * 2. when multiple versions of Radix UI are present, the property is not reset, thus
  *    freezing the whole page
  */
  body {
    pointer-events: unset !important;
  }
  :root {
    --popover: 0 0% 100%;
    --background: 0 0% 100%;

    /* colors */
    /* gray */
    --gray-25: #fcfcfc;
    --gray-50: #fafafa;
    --gray-100: #f4f4f5;
    --gray-200: #e4e4e7;
    --gray-300: #d1d1d6;
    --gray-400: #a0a0ab;
    --gray-500: #70707b;
    --gray-600: #51525c;
    --gray-700: #3f3f46;
    --gray-800: #26272b;
    --gray-900: #1a1a1e;
    --gray-950: #131316;

    --white: #ffffff;
    --black: #000000;

    /* Brand colors */
    --brand-25: #f8fafa;
    --brand-50: #f4f9f8;
    --brand-100: #d9eeec;
    --brand-200: #b3dcd8;
    --brand-300: #84c4c0;
    --brand-400: #5ba6a4;
    --brand-500: #418b8a;
    --brand-600: #2f6868;
    --brand-700: #2b5a5a;
    --brand-800: #264849;
    --brand-900: #233e3e;
    --brand-950: #102123;

    /* red colors */
    --red-25: #fffbfa;
    --red-50: #fef3f2;
    --red-100: #fee4e2;
    --red-200: #fecdca;
    --red-300: #fda29b;
    --red-400: #f97066;
    --red-500: #f04438;
    --red-600: #d92d20;
    --red-700: #b42318;
    --red-800: #912018;
    --red-900: #7a271a;
    --red-950: #55160c;

    /* orange colors */
    --orange-25: #fffcf5;
    --orange-50: #fffaeb;
    --orange-100: #fef0c7;
    --orange-200: #fedf89;
    --orange-300: #fec84b;
    --orange-400: #fdb022;
    --orange-500: #f79009;
    --orange-600: #cd6002;
    --orange-700: #ac5305;
    --orange-800: #894101;
    --orange-900: #6d3300;
    --orange-950: #4c2400;

    /* green colors */
    --green-25: #f5fef9;
    --green-50: #ecfdf3;
    --green-100: #dcfae6;
    --green-200: #acefc6;
    --green-300: #75e0a7;
    --green-400: #46cd89;
    --green-500: #19b26b;
    --green-600: #079455;
    --green-700: #047647;
    --green-800: #065d3a;
    --green-900: #084d31;
    --green-950: #053321;

    /* purple colors */
    --purple-25: #f4f3ff;
    --purple-50: #f4ebff;
    --purple-100: #e9d7fe;
    --purple-200: #d6bbfb;
    --purple-300: #9f8bfa;
    --purple-400: #7f5cf6;
    --purple-500: #6f3aed;
    --purple-600: #6028d9;
    --purple-700: #5021b6;
    --purple-800: #431d95;
    --purple-900: #42307d;
    --purple-950: #1f0f47;

    /* Supabase Auth */
    --colors-inputText: var(--ls-black) !important;

    /* BACKGROUND COLORS */
    --bg-primary: var(--white);
    --bg-primary_hover: var(--gray-50);
    --bg-secondary: var(--gray-50);
    --bg-secondary_hover: var(--gray-100);
    --bg-tertiary: var(--gray-100);
    --bg-quaternary: var(--gray-200);

    --bg-brand-primary: var(--brand-600);
    --bg-brand-primary_hover: var(--brand-700);
    --bg-brand-secondary: var(--brand-100);
    --bg-brand-tertiary: var(--brand-50);
    --bg-purple: var(--purple-50);

    --bg-success-primary: var(--green-50);
    --bg-success-secondary: var(--green-100);
    --bg-success-strong: var(--green-600);
    --bg-error-primary: var(--red-50);
    --bg-error-secondary: var(--red-100);
    --bg-error-strong: var(--red-600);
    --bg-warning-primary: var(--orange-50);
    --bg-warning-secondary: var(--orange-100);
    --bg-warning-strong: var(--orange-600);
    /* BORDER COLORS */
    --border-primary: var(--gray-300);
    --border-secondary: var(--gray-200);
    --border-tertiary: var(--gray-100);
    --border-error: var(--red-300);
    --border-error-strong: var(--red-600);
    --border-brand: var(--brand-400);
    --border-brand-strong: var(--brand-600);
    --border-brand-subtle: var(--brand-100);
    --border-strong: var(--gray-600);
    --border-warning: var(--orange-300);
    --border-success: var(--green-200);
    --border-purple: var(--purple-100);

    /* TEXT COLORS */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-700);
    --text-tertiary: var(--gray-600);
    --text-quaternary: var(--gray-500);
    --text-disabled: var(--gray-400);
    --text-error: var(--red-600);
    --text-warning: var(--orange-600);
    --text-success: var(--green-600);
    --text-placeholder: var(--gray-500);

    --text-button-primary: var(
      --white
    ); /* only used for primary button styles */

    --text-brand-primary: var(--brand-900);
    --text-brand-secondary: var(--brand-700);
    --text-brand-tertiary: var(--brand-600);
    --text-brand-disabled: var(--brand-300);
    --text-purple: var(--purple-600);

    /* Latency colors */
    --text-status-green: #02ae45;
    --text-status-yellow: #c98e06;
    --text-status-red: var(--red-400);
    --text-status-orange: #ee7207;

    --border-status-green: #0edc5e;
    --border-status-yellow: #ffd268;
    --border-status-red: #fda29b;
    --border-status-orange: #f9b072;
  }

  [data-joy-color-scheme='dark'] {
    --popover: 240 7.32% 8.04%; /* gray-iron-950 */
    --background: 240 7.32% 8.04%; /* gray-iron-950 */

    /* colors */
    /* gray */
    --gray-25: #131316;
    --gray-50: #1a1a1e;
    --gray-100: #26272b;
    --gray-200: #3f3f46;
    --gray-300: #51525c;
    --gray-400: #70707b;
    --gray-500: #a0a0ab;
    --gray-600: #d1d1d6;
    --gray-700: #e4e4e7;
    --gray-800: #f4f4f5;
    --gray-900: #fafafa;
    --gray-950: #fcfcfc;

    --white: #131316;
    --black: #ffffff;

    /* Brand colors */
    --brand-25: #102123;
    --brand-50: #233e3e;
    --brand-100: #264849;
    --brand-200: #2b5a5a;
    --brand-300: #2f6868;
    --brand-400: #418b8a;
    --brand-500: #5ba6a4;
    --brand-600: #84c4c0;
    --brand-700: #b3dcd8;
    --brand-800: #d9eeec;
    --brand-900: #f4f9f8;
    --brand-950: #f8fafa;

    /* red colors */
    --red-25: #55160c;
    --red-50: #7a271a;
    --red-100: #912018;
    --red-200: #b42318;
    --red-300: #d92d20;
    --red-400: #f04438;
    --red-500: #f97066;
    --red-600: #fda29b;
    --red-700: #fecdca;
    --red-800: #fee4e2;
    --red-900: #fef3f2;
    --red-950: #fffbfa;

    /* orange colors */
    --orange-25: #4c2400;
    --orange-50: #6d3300;
    --orange-100: #894101;
    --orange-200: #ac5305;
    --orange-300: #cd6002;
    --orange-400: #f79009;
    --orange-500: #fdb022;
    --orange-600: #fec84b;
    --orange-700: #fedf89;
    --orange-800: #fef0c7;
    --orange-900: #fffaeb;
    --orange-950: #fffcf5;

    /* green colors */
    --green-25: #053321;
    --green-50: #084d31;
    --green-100: #065d3a;
    --green-200: #047647;
    --green-300: #079455;
    --green-400: #19b26b;
    --green-500: #46cd89;
    --green-600: #75e0a7;
    --green-700: #acefc6;
    --green-800: #dcfae6;
    --green-900: #ecfdf3;
    --green-950: #f5fef9;

    /* purple colors */
    --purple-25: #1f0f47;
    --purple-50: #42307d;
    --purple-100: #431d95;
    --purple-200: #5021b6;
    --purple-300: #6028d9;
    --purple-400: #6f3aed;
    --purple-500: #7f5cf6;
    --purple-600: #9f8bfa;
    --purple-700: #d6bbfb;
    --purple-800: #e9d7fe;
    --purple-900: #f4ebff;
    --purple-950: #f4f3ff;

    /* BACKGROUND COLORS */
    --bg-primary: var(--white);
    --bg-primary_hover: var(--gray-50);
    --bg-secondary: var(--gray-50);
    --bg-secondary_hover: var(--gray-100);
    --bg-tertiary: var(--gray-100);
    --bg-quaternary: var(--gray-200);

    --bg-brand-primary: var(--brand-300);
    --bg-brand-primary_hover: var(--brand-50);
    --bg-brand-secondary: var(--brand-100);
    --bg-brand-tertiary: var(--brand-25);
    --bg-purple: var(--purple-50);

    --bg-success-primary: var(--green-25); /* success-50 */
    --bg-success-secondary: var(--green-100); /* success-100 */
    --bg-success-strong: var(--green-600); /* success-600 */
    --bg-error-primary: var(--red-25); /* error-50 */
    --bg-error-secondary: var(--red-100); /* error-100 */
    --bg-error-strong: var(--red-600); /* error-600 */
    --bg-warning-primary: var(--orange-50); /* warning-50 */
    --bg-warning-secondary: var(--orange-100); /* warning-100 */

    /* BORDER COLORS */
    --border-primary: var(--gray-300);
    --border-secondary: var(--gray-100);
    --border-tertiary: var(--gray-50);
    --border-error: var(--red-200);
    --border-error-strong: var(--red-600);
    --border-brand: var(--brand-400);
    --border-brand-strong: var(--brand-600);
    --border-brand-subtle: var(--brand-50);
    --border-strong: var(--gray-600);
    --border-warning: var(--orange-300);
    --border-success: var(--green-200);
    --border-purple: var(--purple-100);

    /* TEXT COLORS */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-700);
    --text-tertiary: var(--gray-600);
    --text-quaternary: var(--gray-500);
    --text-disabled: var(--gray-400);
    --text-error: var(--red-600);
    --text-warning: var(--orange-600);
    --text-success: var(--green-600);
    --text-placeholder: var(--gray-500);

    --text-button-primary: var(
      --black
    ); /* only used for primary button styles */

    --text-brand-primary: var(--brand-900);
    --text-brand-secondary: var(--brand-700);
    --text-brand-tertiary: var(--brand-600);
    --text-brand-disabled: var(--brand-300);
    --text-purple: var(--purple-800);

    /* Run status badge colors */
    --text-status-green: #0fc966;
    --text-status-yellow: #e6a81b;
    --text-status-red: var(--red-400);
    --text-status-orange: #ff5d1b;

    --border-status-green: #064c21;
    --border-status-yellow: #5b4300;
    --border-status-red: #500601;
    --border-status-orange: #7c2b09;
  }
}
@keyframes slideLeft {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes hold {
  from {
    opacity: 1;
  }
  to {
    opacity: 1;
  }
}

@layer utilities {
  .animate-slide-left {
    animation: slideLeft 0.4s ease-in-out;
  }

  .animate-slide-right {
    animation: slideRight 0.4s ease-in-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.4s ease-in-out;
  }

  .animate-fade-out {
    animation: fadeOut 0.4s ease-in-out;
  }

  .animate-hold {
    animation: hold 0.4s ease-in-out;
  }

  .shadow-glow {
    box-shadow: 0 0 8px rgba(67, 29, 149, 1); /* lilac-800 */
  }

  .shadow-glow-dark {
    box-shadow: 0 0 10px rgba(218, 214, 254, 1); /* lilac-200 */
  }
}
