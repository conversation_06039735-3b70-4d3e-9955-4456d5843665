import {
  EllipsisVerticalIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import {
  Copy01Icon,
  Download01Icon,
  Image01Icon,
  Link01Icon,
  Pencil01Icon,
  Trash04Icon,
} from '@langchain/untitled-ui-icons';
import { IconButton, LinearProgress, Tooltip } from '@mui/joy';
import Alert from '@mui/joy/Alert';
import Box from '@mui/joy/Box';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Link,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';
import { mutate } from 'swr';
import { useDebounce } from 'use-debounce';

import Breadcrumbs from '@/components/Breadcrumbs';
import { CommandItem } from '@/components/Command';
import { ComparativeExperimentsTable } from '@/components/ComparativeExperiments/ComparativeExperimentsTable';
import { CopyInlineLink } from '@/components/CopyInlineLink';
import { DatasetCrudPane } from '@/components/DatasetCrudPane/DatasetCrudPane';
import { DatasetExportPane } from '@/components/DatasetExportPane/DatasetExportPane';
import { DeleteModal } from '@/components/Delete';
import { ErrorBanner } from '@/components/ErrorBanner';
import { ExamplesTable } from '@/components/ExamplesTable';
import { IFrameTraceRenderingModal } from '@/components/IFrameTraceRenderingModal';
import { PageTitle } from '@/components/PageTitle';
import {
  PopoverDropdownMenu,
  PopoverDropdownMenuItem,
} from '@/components/PopoverDropdownMenu';
import { SessionsTable } from '@/components/SessionsTable';
import {
  TabGroup,
  TabLabel,
  TabList,
  TabPanel,
  TabPanels,
  useSetTab,
  useTab,
} from '@/components/Tabs';
import useToast from '@/components/Toast';
import { FetcherParams } from '@/data/fetcher';
import { useIsSelfHosted } from '@/hooks/useIsSelfHosted';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { usePermissions } from '@/hooks/usePermissions.tsx';
import {
  useCloneDatasetMutation,
  useDataset,
  useDatasetSingleVersion,
  useDatasetVersionsDiff,
  useDatasetVersionsInfinite,
  useOrganizationId,
} from '@/hooks/useSwr';
import DatasetIcon from '@/icons/DatasetIcon';
import { DatasetVersionsDiffSchema } from '@/types/schema';
import { getDatasetPath } from '@/utils';
import { addQueryParams } from '@/utils/add-query-params';
import {
  apiDatasetsPath,
  apiExamplesPath,
  appComparePath,
  appDatasetsPath,
  appOrganizationPath,
} from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { DatasetRulesDropdown } from '../../components/EvaluatorPopover/EvaluatorsPopover';
import { DatasetRuleButton, DatasetRulesPane } from './DatasetRulesPane';
import { CloneDatasetExamplesModal } from './components/CloneDatasetExamplesModal';
import { DatasetVersionDropdownMenu } from './components/DatasetVersionDropdownMenu';
import { FewShotSearchPanel } from './components/FewShotSearchPanel';
import { NewExperimentButton } from './components/NewExperimentButton';
import { ShareDatasetAction } from './components/ShareDatasetAction';
import { TITLE_ALL_VERSIONS } from './constants';
import { GroupedDatasetVersionSchemaWithOptionalAsOf } from './types';

const TABS_TO_SHOW_VERSIONING = [0, 2]; // examples, experiments
export function NoProjects() {
  return (
    <Box marginBottom={3}>
      <Alert variant="outlined">There are no experiments yet.</Alert>
    </Box>
  );
}

const VERSIONS_PAGE_SIZE = 20;

const Dataset = () => {
  const [rulesOpen, setRulesOpen] = useState(false);

  const { datasetId } = useParams();
  const dataset = useDataset(datasetId!);
  const [isExportOpen, setIsExportOpen] = useState(false);
  const [isDatasetCrudPaneOpen, setIsDatasetCrudPaneOpen] = useState(false);
  const [isCloneDatasetModalOpen, setIsCloneDatasetModalOpen] = useState(false);
  const [
    isCustomIFrameRenderingModalOpen,
    setIsCustomIFrameRenderingModalOpen,
  ] = useState(false);
  const [selectedExamples, setSelectedExamples] = useState({});
  const isSelfHosted = useIsSelfHosted();

  const { value: allowCustomIFrames } = useOrgConfig(
    OrgConfigs.allow_custom_iframes
  );

  const organizationId = useOrganizationId();
  const toPreserve = ['as_of'];
  const setTab = useSetTab({ preserve: toPreserve });
  const tab = useTab();
  const [defaultVersions, setDefaultVersions] = useState<
    GroupedDatasetVersionSchemaWithOptionalAsOf[]
  >([]);
  const [cloneTargetDatasetId, setCloneTargetDatasetId] = useState<
    string | undefined
  >();

  const [searchVersion, setSearchVersion] = useState<string>('');
  const [searchVersionDebounced] = useDebounce(searchVersion, 250);
  const {
    data: versionPages,
    size,
    setSize,
    mutate: mutateVersionsSWR,
    isLoading: versionPagesLoading,
  } = useDatasetVersionsInfinite(VERSIONS_PAGE_SIZE, datasetId ?? null, {
    search: searchVersionDebounced,
  });

  const [isDeleteDatasetModalOpen, setIsDeleteDatasetModalOpen] =
    useState(false);

  const [searchParams, setSearchParams] = useSearchParams();
  const searchParamsAsOf = searchParams.get('as_of');
  const decodedSearchParamsAsOf = searchParamsAsOf
    ? decodeURIComponent(searchParamsAsOf)
    : null;
  const versionsFlat = useMemo(
    () => versionPages?.flat() ?? [],
    [versionPages]
  );

  const mutateVersions = useCallback(() => {
    setSearchVersion('');
    mutateVersionsSWR();
    mutate((key: FetcherParams) => key?.url?.startsWith(apiDatasetsPath));
  }, [mutateVersionsSWR, setSearchVersion]);

  const selectedVersionSWR = useDatasetSingleVersion(
    datasetId ?? null,
    decodedSearchParamsAsOf ? { as_of: decodedSearchParamsAsOf } : null
  );

  const selectedVersion = decodedSearchParamsAsOf
    ? selectedVersionSWR.data
    : null;
  const setSelectedVersion = useCallback(
    (version: GroupedDatasetVersionSchemaWithOptionalAsOf | null) => {
      const isLatestVersion = (version?.tags ?? []).includes('latest');
      setSearchParams(
        (prev) => {
          if (version?.as_of && !(isLatestVersion && tab === 2)) {
            prev.set('as_of', version.as_of);
          } else {
            prev.delete('as_of');
          }
          return prev;
        },
        { replace: true }
      );
    },
    [setSearchParams, tab]
  );

  const examplesMutated = useCallback(() => {
    // if an example was changed, we need to reset the selectedVersion as well
    mutate((key: FetcherParams) => key?.url?.startsWith(apiExamplesPath));
    setSelectedVersion(null);
    mutateVersions();
  }, [mutateVersions, setSelectedVersion]);
  const { createToast } = useToast();
  const navigate = useNavigate();

  const cloneDataset = useCloneDatasetMutation({
    onSuccess: () => {
      setIsCloneDatasetModalOpen(false);
      if (cloneTargetDatasetId) {
        createToast({
          title: 'Examples Copied Successfully',
          description: (
            <Link
              to={`/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${cloneTargetDatasetId}`}
              className="underline"
            >
              View the dataset
            </Link>
          ),
        });
      }
    },
    onError: (e) => {
      createToast({
        title: 'Error copying examples',
        description: e.message,
        error: true,
      });
    },
  });

  useEffect(() => {
    if (versionsFlat.length > 0 && searchVersion === '') {
      setDefaultVersions([
        { tags: [TITLE_ALL_VERSIONS], as_of: undefined },
        versionsFlat[0],
      ]);
    }
  }, [searchVersion, versionsFlat]);

  const showAllVersionsOption = tab === 0 && searchVersion === '';
  const versionsToDisplay = showAllVersionsOption
    ? [{ tags: [TITLE_ALL_VERSIONS], as_of: undefined }, ...versionsFlat]
    : versionsFlat;
  const selectedVersionToUse = selectedVersion ?? defaultVersions[tab];

  const pageLoadEffectDone = useRef(false);
  useEffect(() => {
    if (pageLoadEffectDone.current) {
      return;
    }

    if (dataset.data) {
      if (!dataset.data.session_count) {
        setTab(2);
      }
      pageLoadEffectDone.current = true;
    }
  }, [dataset.data, setTab]);

  const currentVersionDiff = useDatasetVersionsDiff(
    datasetId ?? null,
    tab === 2 &&
      !selectedVersionToUse?.tags?.includes('latest') &&
      selectedVersionToUse?.as_of
      ? {
          from_version: selectedVersionToUse?.as_of,
          to_version: 'latest',
        }
      : null
  );

  const exampleCount = useMemo(() => {
    if (!decodedSearchParamsAsOf) {
      return dataset.data?.example_count;
    } else {
      return undefined;
    }
  }, [decodedSearchParamsAsOf, dataset.data?.example_count]);

  const { authorize, isLoading: isPermsLoading } = usePermissions();

  const datasetVersioningComponent = datasetId && (
    <div className="flex gap-2 self-end">
      <DatasetVersionDropdownMenu
        selectedVersion={selectedVersionToUse}
        datasetId={datasetId}
        setSelectedVersion={setSelectedVersion}
        versions={versionsToDisplay}
        size={size}
        setSize={setSize}
        isLoadingVersions={versionPagesLoading}
        totalVersions={
          Number(
            versionPages?.[versionPages.length - 1]?.headers?.[
              'x-pagination-total'
            ] ?? 0
          ) + (showAllVersionsOption ? 1 : 0)
        }
        setSearch={setSearchVersion}
        search={searchVersion}
        isLoadingCurrentVersion={selectedVersionSWR.isLoading}
        versionPageSize={VERSIONS_PAGE_SIZE}
        mutateVersions={mutateVersions}
      />
    </div>
  );

  const isReadOnly =
    decodedSearchParamsAsOf !== null &&
    decodedSearchParamsAsOf !== defaultVersions[1]?.as_of;

  const threeDotMenuOptions = [
    authorize('datasets:share') &&
      dataset.data && {
        label: 'Share Dataset',
        customCell: (
          <ShareDatasetAction
            variant="custom"
            dataset={dataset.data}
            bypassPortal
            customButton={(icon, isPublic) => (
              <CommandItem
                className={cn(
                  'mx-1.5 flex cursor-pointer flex-col items-center px-0 py-0'
                )}
                disabled={false}
              >
                <PopoverDropdownMenuItem
                  icon={<div className="flex items-center">{icon}</div>}
                  title={
                    <div className="text-md">
                      {isPublic ? 'Public Dataset' : 'Share Dataset'}
                    </div>
                  }
                  selected={false}
                  titleBold
                />
              </CommandItem>
            )}
          />
        ),
      },
    {
      label: 'Download Dataset',
      icon: <Download01Icon className="h-4 w-4" />,
      onClick: () => setIsExportOpen(true),
    },
    authorize('datasets:update') && {
      label: 'Edit Dataset',
      icon: <Pencil01Icon className="h-4 w-4" />,
      onClick: () => setIsDatasetCrudPaneOpen(true),
      dataTestId: 'edit-dataset-menu-item',
    },
    authorize('datasets:update') && {
      label: 'Clone Dataset',
      icon: <Copy01Icon className="h-4 w-4" />,
      onClick: () => setIsCloneDatasetModalOpen(true),
    },
    allowCustomIFrames && {
      label: 'Custom Trace Rendering',
      icon: <Image01Icon className="h-4 w-4" />,
      onClick: () => setIsCustomIFrameRenderingModalOpen(true),
    },
    authorize('datasets:delete') && {
      label: 'Delete Dataset',
      icon: <Trash04Icon className="h-4 w-4" />,
      onClick: () => setIsDeleteDatasetModalOpen(true),
    },
  ].filter(Boolean) as {
    label: string;
    icon?: JSX.Element;
    onClick?: () => void;
    customCell?: JSX.Element;
    dataTestId?: string;
  }[];

  if (isPermsLoading) {
    return <LinearProgress />;
  }

  if (!authorize('datasets:read')) {
    return <ErrorBanner className="m-4">Forbidden</ErrorBanner>;
  }

  return (
    <div className="px-4 pb-6 pt-3">
      <Breadcrumbs />
      <Box display="flex" alignItems={'center'} gap={2}>
        <div className="flex flex-grow items-center gap-2">
          <PageTitle
            icon={<DatasetIcon />}
            description={dataset.data?.description}
            className="grow-0"
          >
            {dataset.data?.name}
          </PageTitle>

          {dataset.data && (
            <div className="flex items-center gap-1.5 rounded-full border border-brand bg-brand-secondary px-2 py-1 text-brand-primary">
              <CopyInlineLink
                icon={<Link01Icon className="h-4 w-4" />}
                className="text-brand-primary"
                value={dataset.data.id}
              >
                ID
              </CopyInlineLink>
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          {dataset.data && (
            <div className="flex items-center gap-2 whitespace-nowrap">
              {TABS_TO_SHOW_VERSIONING.includes(tab) &&
                datasetVersioningComponent}

              {datasetId && authorize('rules:read') && (
                <DatasetRulesDropdown
                  trigger={
                    <DatasetRuleButton
                      datasetId={datasetId}
                      onClick={() => void 0}
                      variant="dataset"
                    />
                  }
                  datasetId={datasetId}
                  checkable={false}
                />
              )}

              {datasetId && <NewExperimentButton datasetId={datasetId} />}

              {threeDotMenuOptions.length > 0 && (
                <PopoverDropdownMenu
                  values={threeDotMenuOptions}
                  onSelect={(option) => option.onClick?.()}
                  getTitle={(option) => option.label}
                  getCustomCell={(option) => option.customCell}
                  getIsSelected={() => false}
                  getIcon={(option) => option.icon}
                  getDataTestId={(option) => option.dataTestId || ''}
                  asChild
                >
                  <Tooltip title="More" placement="top">
                    <IconButton
                      size="sm"
                      variant="outlined"
                      color="neutral"
                      data-testid="dataset-more-button"
                    >
                      <EllipsisVerticalIcon className="h-5 w-5" />
                    </IconButton>
                  </Tooltip>
                </PopoverDropdownMenu>
              )}
            </div>
          )}
        </div>
      </Box>

      <TabGroup preserve={toPreserve}>
        <TabList>
          <TabLabel>Experiments</TabLabel>
          <TabLabel>Pairwise Experiments</TabLabel>
          <TabLabel>Examples</TabLabel>
          {!isSelfHosted && <TabLabel>Few-shot search</TabLabel>}
        </TabList>
        <TabPanels>
          <TabPanel>
            <Box>
              {selectedVersion?.as_of && (
                <ViewingSingleVersionBanner
                  onClick={() => {
                    setSelectedVersion(null);
                  }}
                  bodyText="Viewing experiments from a single version."
                  linkText="Click here to view all experiments"
                />
              )}
              <SessionsTable
                filter={{
                  reference_dataset: [datasetId!],
                  ...(decodedSearchParamsAsOf
                    ? { dataset_version: decodedSearchParamsAsOf }
                    : {}),
                }}
                emptyState={<NoProjects />}
                columnVisibility={{
                  run_count: true,
                  total_tokens: false,
                  total_cost: false,
                }}
                isDatasetPage={true}
                getCompareClickUrl={
                  organizationId
                    ? (rowSelection) => {
                        const basePath = getDatasetPath(
                          datasetId!,
                          organizationId
                        );
                        return addQueryParams(`${basePath}/${appComparePath}`, {
                          selectedSessions: rowSelection.join(','),
                        });
                      }
                    : undefined
                }
                localStorageDisplayColumnsKey={
                  'ls:dataset-sessions-table-column-visibility'
                }
              />
            </Box>
          </TabPanel>
          <TabPanel>
            <ComparativeExperimentsTable datasetId={datasetId!} />
          </TabPanel>
          <TabPanel>
            {isReadOnly && (
              <>
                <ViewingSingleVersionBanner
                  onClick={() => {
                    setSelectedVersion(null);
                  }}
                  bodyText="Examples are read-only when viewing a past version of a dataset."
                  linkText="Click here to view the latest version."
                />
                {currentVersionDiff.data && (
                  <DatasetVersionsDiffComponent
                    currentVersionDiff={currentVersionDiff.data}
                  />
                )}
              </>
            )}
            <ExamplesTable
              rowSelection={selectedExamples}
              setRowSelection={setSelectedExamples}
              filter={{
                dataset: datasetId,
                ...(decodedSearchParamsAsOf
                  ? { as_of: decodedSearchParamsAsOf }
                  : {}),
              }}
              onMutate={examplesMutated}
              isReadOnly={isReadOnly}
              dataType={dataset.data?.data_type}
              dataset={dataset.data}
              totalExamplesCount={exampleCount}
            />
          </TabPanel>

          {!isSelfHosted && (
            <TabPanel>
              <FewShotSearchPanel
                datasetId={datasetId ?? ''}
                inputSchema={dataset.data?.inputs_schema_definition}
                onMutate={examplesMutated}
                setEditDatasetOpen={() => setIsDatasetCrudPaneOpen(true)}
              />
            </TabPanel>
          )}
        </TabPanels>
      </TabGroup>

      {isExportOpen && (
        <DatasetExportPane
          open={true}
          onCancel={() => setIsExportOpen(false)}
          onSubmit={() => setIsExportOpen(false)}
          defaultDatasetId={datasetId!}
          asOf={selectedVersionToUse?.as_of}
        />
      )}
      {rulesOpen && datasetId != null && (
        <DatasetRulesPane
          datasetId={datasetId}
          open={true}
          onClose={() => setRulesOpen(false)}
        />
      )}
      {isCloneDatasetModalOpen && datasetId != null && (
        <CloneDatasetExamplesModal
          open={true}
          onClose={() => setIsCloneDatasetModalOpen(false)}
          prefilledData={{
            inputs_schema_definition: dataset.data?.inputs_schema_definition,
            outputs_schema_definition: dataset.data?.outputs_schema_definition,
          }}
          sourceDatasetId={datasetId ?? null}
          sourceDataType={dataset.data?.data_type}
          targetDatasetId={cloneTargetDatasetId}
          isLoading={cloneDataset.isMutating}
          setTargetDatasetId={setCloneTargetDatasetId}
          onSubmit={() => {
            if (cloneTargetDatasetId) {
              cloneDataset.trigger({
                json: {
                  source_dataset_id: datasetId,
                  target_dataset_id: cloneTargetDatasetId,
                  as_of: selectedVersionToUse?.as_of,
                },
              });
            }
          }}
        />
      )}
      {isCustomIFrameRenderingModalOpen && datasetId != null && (
        <IFrameTraceRenderingModal
          datasetId={datasetId}
          open={true}
          onClose={() => setIsCustomIFrameRenderingModalOpen(false)}
        />
      )}
      {isDatasetCrudPaneOpen && (
        <DatasetCrudPane
          open={true}
          onCancel={() => setIsDatasetCrudPaneOpen(false)}
          dataset={dataset.data}
          onSuccess={() => dataset.mutate()}
          label="Edit"
        />
      )}
      {isDeleteDatasetModalOpen && (
        <DeleteModal
          endpoint={`${apiDatasetsPath}`}
          id={dataset.data?.id}
          name={dataset.data?.name}
          isOpen={true}
          doClose={() => setIsDeleteDatasetModalOpen(false)}
          invalidationPrefixes={['/datasets']}
          onSuccess={() => {
            navigate(
              `/${appOrganizationPath}/${organizationId}/${appDatasetsPath}`
            );
          }}
        />
      )}
    </div>
  );
};

function ViewingSingleVersionBanner(props: {
  onClick: () => void;
  bodyText: string;
  linkText: string;
}) {
  return (
    <div className="mb-4 rounded-xl bg-secondary px-5 py-4 pl-8">
      <div className="flex items-center gap-6">
        <div className="relative h-5 w-5 rounded-[20px]">
          <div className="absolute left-[-4px] top-[-4px] h-7 w-7 rounded-3xl border-2 border-brand opacity-30" />
          <div className="absolute left-[-9px] top-[-9px] h-[38px] w-[38px] rounded-3xl border-2 border-brand opacity-10" />
          <div className="absolute left-0 top-0 h-5 w-5" />
          <InformationCircleIcon className="absolute left-0 top-0 h-5 w-5 stroke-brand-green-400" />
        </div>
        <div className="flex items-start gap-2">
          <span>{props.bodyText}</span>
          <a
            onClick={props.onClick}
            className="cursor-pointer text-brand-green-600 hover:underline"
          >
            {props.linkText}
          </a>
        </div>
      </div>
    </div>
  );
}

function DatasetVersionsDiffComponent(props: {
  currentVersionDiff: DatasetVersionsDiffSchema;
}) {
  return (
    <div className="mb-3 ml-2 flex gap-1.5">
      <div className="text-success-primary">
        {props.currentVersionDiff.examples_added.length}{' '}
        {props.currentVersionDiff.examples_added.length !== 1
          ? 'additions'
          : 'addition'}
        ,{' '}
      </div>
      <div className="text-brand-green-400">
        {props.currentVersionDiff.examples_modified.length}{' '}
        {props.currentVersionDiff.examples_modified.length !== 1
          ? 'modifications'
          : 'modification'}
        ,{' '}
      </div>
      <div className="text-error">
        {props.currentVersionDiff.examples_removed.length}{' '}
        {props.currentVersionDiff.examples_removed.length !== 1
          ? 'deletions'
          : 'deletion'}
      </div>
      between this version and the latest version.
    </div>
  );
}

export default Dataset;
