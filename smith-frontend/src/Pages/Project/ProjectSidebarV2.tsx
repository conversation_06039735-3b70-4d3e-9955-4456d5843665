import { ChevronDownIcon, SearchLgIcon } from '@langchain/untitled-ui-icons';
import Checkbox from '@mui/joy/Checkbox';

import { memo, useCallback, useId, useMemo, useState } from 'react';

import {
  GroupedComparison,
  getAstFromGroupedComparisons,
  groupComparisonsByRunFields,
} from '@/components/FilterBar/FilterBar.utils';
import { getRunFields } from '@/components/FilterBar/fields/runs';
import {
  astToString,
  flattenAst,
  stringToAst,
} from '@/components/RunsTable/filter/ast';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';
import {
  InstanceFlags,
  useInstanceFlagOrDefault,
} from '@/hooks/useInstanceFlag';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { FeedbackChip } from '../../components/FeedbackChips';
import { SearchModel } from '../../components/RunsTable/types';
import { RunFacetField, RunStatsSchema } from '../../types/schema';
import { cn } from '../../utils/tailwind';
import { ProjectSidebarHeaderTag } from './ProjectSidebarComponents';
import {
  FEEDBACK_KEY_KEY,
  GROUP_VALUE_KEYS,
  KEEP_RAW_VALUE_KEYS,
  SIDEBAR_HEADER_STYLE,
} from './constants';
import { useFacetGroups } from './hooks/useFacetGroups';
import { filterFacets } from './utils/facetFiltering';
import { formatFacetValue } from './utils/facetValueFormatter';

type Query = RunFacetField['query'];

function SelectableFacet({
  facet,
  isSelected,
  isMatched,
  setSelected,
}: {
  facet: RunFacetField;
  isSelected: boolean;
  isMatched: boolean;
  setSelected: (query: Query) => void;
}) {
  const { query, n } = facet;
  const id = useId();
  const keepRawValue = KEEP_RAW_VALUE_KEYS.some((key) =>
    facet.key.startsWith(key)
  );

  return (
    <div
      className={cn(
        'flex cursor-pointer items-center gap-2 rounded-md p-2 py-1 text-xs text-secondary transition-colors hover:bg-secondary',
        GROUP_VALUE_KEYS.includes(facet.key) ? 'ml-6' : '',
        !isMatched && 'cursor-not-allowed text-disabled',
        isSelected && 'text-primary'
      )}
      onClick={() => {
        if (isMatched) {
          setSelected(query);
        }
      }}
    >
      <Checkbox
        size="sm"
        id={id}
        checked={isSelected}
        onChange={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setSelected(query);
        }}
        disabled={!isMatched}
        readOnly
      />
      <div className={cn('flex flex-1 items-center overflow-hidden')}>
        {facet.key === FEEDBACK_KEY_KEY ? (
          <FeedbackChip
            feedback_key={facet.value}
            className="flex min-w-0 flex-shrink items-center overflow-hidden text-xs"
          />
        ) : (
          <TextOverflowTooltip className="text-xs font-medium">
            {formatFacetValue(facet, keepRawValue)}
          </TextOverflowTooltip>
        )}
      </div>
      {n !== undefined && (
        <div className="text-xs opacity-70">{n.toLocaleString()}</div>
      )}
    </div>
  );
}

function FacetGroup({
  categoryName,
  group,
  groupedFilters,
  setGroupedFilters,
}: {
  categoryName: string;
  group: {
    facets: RunFacetField[];
    facetSelectMap: Map<
      string,
      {
        isSelected: boolean;
        queryGroups: Pick<GroupedComparison, 'comparisons'>[];
        selected: Pick<GroupedComparison, 'comparisons'>[];
        unselected: Pick<GroupedComparison, 'comparisons'>[];
        isMatched: boolean;
      }
    >;
    selectedCount: number;
  };
  groupedFilters: Pick<GroupedComparison, 'comparisons'>[];
  setGroupedFilters: (value: Pick<GroupedComparison, 'comparisons'>[]) => void;
}) {
  const [groupOpen, setGroupOpen] = useLocalStorageState(
    `ls:project:facet-group-${categoryName}-open`,
    true
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMore, setViewMore] = useState(false);

  const { facets, facetSelectMap, selectedCount } = group;
  const showSearch = facets.length > 10;

  const { filteredFacets, filteredSetHasMore } = useMemo(() => {
    const filtered = filterFacets(facets, searchQuery, categoryName);
    return {
      filteredFacets: viewMore ? filtered : filtered.slice(0, 10),
      filteredSetHasMore: filtered.length > 10,
    };
  }, [facets, searchQuery, categoryName, viewMore]);

  return (
    <div>
      <div
        className={cn(
          'flex cursor-pointer items-start justify-between px-4 py-2 text-secondary transition-colors hover:bg-secondary',
          groupOpen && 'bg-tertiary hover:bg-tertiary'
        )}
        onClick={() => {
          setGroupOpen(!groupOpen);
          setViewMore(false);
        }}
      >
        <h3
          className={cn(
            'text-xs font-medium capitalize tracking-wide text-tertiary',
            groupOpen && 'text-primary'
          )}
        >
          {categoryName.replace(/_/g, ' ')}
        </h3>

        <div className="flex items-center gap-2">
          <ProjectSidebarHeaderTag
            show={selectedCount > 0}
            text={`${selectedCount} active`}
          />

          <ChevronDownIcon
            className={`size-4 flex-none transition-transform ${
              groupOpen ? '' : '-rotate-90'
            }`}
          />
        </div>
      </div>
      {groupOpen && (
        <div className="flex flex-col">
          {showSearch && (
            <div className="mx-4 my-2">
              <div className="flex items-center gap-2 rounded-md border border-secondary bg-background px-2 py-1.5">
                <SearchLgIcon className="h-4 w-4 text-tertiary" />
                <input
                  type="text"
                  placeholder={`Search ${categoryName.replace(/_/g, ' ')}...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 border-none bg-transparent p-0 text-xs outline-none placeholder:text-quaternary"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          )}
          <div className="flex max-h-[400px] flex-col gap-1 overflow-y-auto px-2 py-1 pb-2">
            {filteredFacets.map((facet) => {
              const facetKey = facet.key + facet.value;
              const selectData = facetSelectMap.get(facetKey)!;

              return (
                <SelectableFacet
                  key={facetKey}
                  facet={facet}
                  isMatched={selectData.isMatched}
                  isSelected={selectData.isSelected}
                  setSelected={() => {
                    if (selectData.isSelected) {
                      setGroupedFilters(selectData.unselected);
                    } else {
                      setGroupedFilters([
                        ...groupedFilters,
                        ...selectData.queryGroups,
                      ]);
                    }
                  }}
                />
              );
            })}
            {!viewMore && filteredSetHasMore && (
              <button
                type="button"
                className="rounded-md px-2 py-1 text-left text-xs text-quaternary hover:bg-secondary hover:text-secondary"
                onClick={() => setViewMore(true)}
              >
                View more
              </button>
            )}
            {filteredFacets.length === 0 && searchQuery.trim() && (
              <div className="px-4 py-2 text-xs text-quaternary">
                No results found for "{searchQuery}"
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

function FiltersSection({
  currentFilterStats,
  allStats,
  groupedFilters,
  setGroupedFilters,
}: {
  currentFilterStats: RunStatsSchema | undefined;
  allStats: RunStatsSchema;
  groupedFilters: Pick<GroupedComparison, 'comparisons'>[];
  setGroupedFilters: (value: Pick<GroupedComparison, 'comparisons'>[]) => void;
}) {
  const [filtersOpen, setFiltersOpen] = useLocalStorageState(
    'ls:project:filters-open',
    true
  );

  const { facetGroups, totalSelectedCount } = useFacetGroups({
    currentFilterStats,
    allStats,
    groupedFilters,
  });

  return (
    <>
      <div
        className={cn(SIDEBAR_HEADER_STYLE, 'relative')}
        onClick={() => setFiltersOpen(!filtersOpen)}
      >
        <h2>Filter Shortcuts</h2>

        <div className="flex items-center gap-1">
          <ProjectSidebarHeaderTag
            show={!filtersOpen && totalSelectedCount > 0}
            text={`${totalSelectedCount} active`}
          />
          <ChevronDownIcon
            className={`h-4 w-4 transition-transform ${
              filtersOpen ? '' : '-rotate-90'
            }`}
          />
        </div>
      </div>
      {filtersOpen && (
        <div className="flex flex-col">
          {Object.entries(facetGroups).map(([categoryName, group]) => (
            <FacetGroup
              key={categoryName}
              categoryName={categoryName}
              group={group}
              groupedFilters={groupedFilters}
              setGroupedFilters={setGroupedFilters}
            />
          ))}
        </div>
      )}
    </>
  );
}

export const ProjectSidebarV2 = memo(function ProjectSidebar({
  stats,
  allStats,
  searchModel,
  setSearchModel,
}: {
  stats?: RunStatsSchema;
  allStats?: RunStatsSchema;
  searchModel: SearchModel;
  setSearchModel: (value: SearchModel) => void;
}) {
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;

  const runFields = useMemo(
    () =>
      getRunFields({
        searchEnabled: searchEnabled,
      }),
    [searchEnabled]
  );

  const groupedFilters = useMemo(
    () =>
      groupComparisonsByRunFields(
        flattenAst(stringToAst(searchModel.filter)),
        runFields
      ),
    [searchModel.filter, runFields]
  );

  const setGroupedFilters = useCallback(
    (groupedFilters: Pick<GroupedComparison, 'comparisons'>[]) => {
      setSearchModel({
        filter: astToString(getAstFromGroupedComparisons(groupedFilters)),
      });

      window.scrollTo({ behavior: 'smooth', top: 0 });
    },
    [setSearchModel]
  );

  return allStats?.run_facets ? (
    <FiltersSection
      currentFilterStats={stats}
      allStats={allStats}
      groupedFilters={groupedFilters}
      setGroupedFilters={setGroupedFilters}
    />
  ) : null;
});
