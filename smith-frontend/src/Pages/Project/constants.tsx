import { SessionSchema } from '@/types/schema';
import {
  appDashboardsIndexPath,
  appOrganizationPath,
  appProjectsPath,
} from '@/utils/constants';

export const COLUMN_VISIBILITY = {
  default: {
    feedback_stats: false,
    reference_example: false,
  },
  eval: {
    in_dataset: false,
    metadata: false,
    tags: false,
  },
};

export const getDashboardPath = (
  organizationId: string | null,
  project: SessionSchema
) => {
  const defaultDashboardUrl = project.default_dashboard_id
    ? getCustomDashboardPath(organizationId, project.default_dashboard_id)
    : getDefaultDashboardPath(organizationId, project.id);
  return defaultDashboardUrl;
};

const getDefaultDashboardPath = (
  organizationId: string | null,
  sessionId?: string
) =>
  `/${appOrganizationPath}/${organizationId}/${appDashboardsIndexPath}/${appProjectsPath}/${sessionId}`;
const getCustomDashboardPath = (
  organizationId: string | null,
  dashboardId?: string
) =>
  `/${appOrganizationPath}/${organizationId}/${appDashboardsIndexPath}/${dashboardId}`;

export const SIDEBAR_HEADER_STYLE =
  'flex cursor-pointer items-center justify-between p-4 py-2 font-semibold text-base flex-wrap transition-colors hover:bg-tertiary';

export const KEEP_RAW_VALUE_KEYS = ['tags', 'metadata', 'feedback'] as const;
export const GROUP_VALUE_KEYS = [
  'feedback_key_score',
  'metadata_key_value',
  'feedback_value',
  'input_key_value',
  'output_key_value',
];
export const FEEDBACK_KEY_KEY = 'feedback_key';
export const FEEDBACK_VALUE_KEY = 'feedback_value';
