import { ChevronDownIcon } from '@heroicons/react/24/outline';

import { debounce } from 'lodash-es';
import { useCallback, useState } from 'react';
import { Link } from 'react-router-dom';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import useToast from '@/components/Toast';
import { useCurrentTier } from '@/hooks/useCurrentTier';
import {
  InstanceFlags,
  useInstanceFlagOrDefault,
} from '@/hooks/useInstanceFlag';
import { useOrganizationId } from '@/hooks/useSwr';
import ExternalLinkIcon from '@/icons/ExternalLinkIcon.svg?react';
import { SessionSchema, TraceTTLTier } from '@/types/schema';
import { appOrganizationPath } from '@/utils/constants';
import { isSelfHosted } from '@/utils/is-self-hosted';

import { CondensedDataRetentionSetting } from '../Settings/components/CondensedDataRetentionSetting';
import { useConfigureProjectTTLSettings } from '../Settings/utils/ttlSettings.util';

type Props = {
  project?: SessionSchema;
};

const ProjectTTLSetting = (props: Props) => {
  const { createToast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const tenantId = useOrganizationId();
  const { isEnterprise } = useCurrentTier();

  const ttlDurations = useInstanceFlagOrDefault(
    InstanceFlags.trace_tier_duration_days
  );
  const retentionLabel =
    props.project?.trace_tier === TraceTTLTier.longlived
      ? ttlDurations[TraceTTLTier.longlived] + 'd'
      : ttlDurations[TraceTTLTier.shortlived] + 'd';

  const configureTtlSettingProps = useConfigureProjectTTLSettings({
    project: props.project,
  });
  const { handleProjectTTLChange, setTTL, ttl } = configureTtlSettingProps;

  const debouncedSave = useCallback(
    debounce(async (mTTL: TraceTTLTier | null) => {
      try {
        await handleProjectTTLChange(mTTL);
        createToast({
          title: 'Success',
          description: 'Trace data retention policy updated successfully',
        });
      } catch (e: any) {
        createToast({
          title: 'Error',
          description: e.message,
          error: true,
        });
      }
    }, 1000),
    []
  );

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger
        className="flex min-w-[190px] items-center gap-1 rounded-md border border-secondary p-1 pl-3 text-sm font-semibold hover:bg-secondary"
        onClick={() => setIsOpen(true)}
      >
        Data Retention
        <div className="flex items-center gap-1 rounded-md border border-secondary px-1 py-0">
          {retentionLabel}
          <ChevronDownIcon className="h-4 w-4" />
        </div>
      </PopoverTrigger>
      <PopoverContent
        className={'w-[450px]'}
        sideOffset={5}
        align={'start'}
        side={'bottom'}
      >
        <div className="flex flex-col gap-4">
          <div className="font-semibold">Configure Project Data Retention</div>
          <div className="text-sm">
            Set the default data retention period for new traces in this
            project.
          </div>
          <CondensedDataRetentionSetting
            ttl={ttl}
            setTTL={async (ttl) => {
              setTTL(ttl);
              await debouncedSave(ttl);
            }}
          />

          <div className="text-sm">
            This setting applies only to new traces. Base retention traces may
            be auto-upgraded to extended retention based on feature usage.{' '}
            <Link
              className="inline-flex items-center gap-1 text-sm underline"
              to="https://docs.smith.langchain.com/administration/concepts#usage-and-billing"
              target="_blank"
            >
              Learn More <ExternalLinkIcon className="h-4 w-4" />
            </Link>
          </div>
          <div className="flex justify-end gap-4">
            <Link
              className="flex flex-none items-center justify-center gap-1 text-sm underline underline-offset-4"
              to={
                isEnterprise
                  ? `/${appOrganizationPath}/${tenantId}/settings/usage${
                      isSelfHosted ? '' : '?tab=1'
                    }`
                  : `/${appOrganizationPath}/${tenantId}/settings/payments?tab=1`
              }
            >
              Configure organization defaults
            </Link>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ProjectTTLSetting;
