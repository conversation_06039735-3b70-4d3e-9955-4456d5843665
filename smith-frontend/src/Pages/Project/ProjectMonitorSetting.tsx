import {
  BarChart10Icon,
  Edit01Icon,
  InfoCircleIcon,
  SearchLgIcon,
} from '@langchain/untitled-ui-icons';

import { Command } from 'cmdk';
import { useMemo, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { PopoverDropdownMenu } from '@/components/PopoverDropdownMenu';
import useToast from '@/components/Toast';
import { Tooltip } from '@/components/Tooltip/Tooltip';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  useDashboards,
  useOrganizationId,
  useSessionUpdateMutation,
} from '@/hooks/useSwr';
import { SessionSchema } from '@/types/schema';

import { getDashboardPath } from './constants';

export function ProjectMonitorSetting({ project }: { project: SessionSchema }) {
  const organizationId = useOrganizationId();
  const { createToast } = useToast();
  const [search, setSearch] = useState<string>('');
  const [debouncedSearch] = useDebounce(search, 300);

  const { selectedTagIds, setSelectedTags } = useStoredResourceTags();
  const { data: dashboards, isLoading } = useDashboards(
    {
      title_contains: debouncedSearch,
      tag_value_id: selectedTagIds,
      limit: 20,
      offset: 0,
    },
    { keepPreviousData: true }
  );
  const onlyCustomDashboards = useMemo(() => {
    const onlyCustomDashboards =
      dashboards
        ?.filter((dashboard) => !dashboard.session_id)
        .sort((a, b) => {
          if (a.id === project.default_dashboard_id) {
            return -1;
          } else if (b.id === project.default_dashboard_id) {
            return 1;
          } else {
            return 0;
          }
        }) ?? [];
    if ('default'.includes(search.toLowerCase())) {
      onlyCustomDashboards.unshift({
        id: '',
        session_id: project.id,
        title: `Prebuilt for ${project.name}`,
        description: 'Prebuilt dashboard for this project',
        created_at: '',
        modified_at: '',
        chart_count: 0,
        index: 0,
      });
    }
    return onlyCustomDashboards;
  }, [dashboards, project.default_dashboard_id, project.id, search]);

  const updateMutation = useSessionUpdateMutation(project.id);

  const setDefaultDashboard = async (dashboardId: string | null) => {
    await updateMutation.trigger({
      json: {
        default_dashboard_id: dashboardId,
      },
    });
    createToast({
      title: 'Default dashboard set',
      description:
        'The default dashboard has been set to the selected dashboard',
    });
  };

  const defaultDashboardUrl = getDashboardPath(organizationId, project);

  const popoverRef = useRef<HTMLButtonElement>(null);
  return (
    <button
      type="button"
      className="flex items-center justify-center gap-1 rounded-md border border-secondary px-2.5 py-1 text-sm font-semibold hover:bg-secondary"
    >
      <Link to={defaultDashboardUrl} className="flex items-center gap-2">
        <BarChart10Icon className="h-4 w-4" />
        <span>Dashboard</span>
      </Link>
      <PopoverDropdownMenu
        contentWidth="220px"
        values={onlyCustomDashboards ?? []}
        getTitle={(dashboard) => {
          return dashboard.title;
        }}
        getSubtitle={(dashboard) => dashboard.description}
        onSelect={(value) => setDefaultDashboard(value.id || null)}
        getIsSelected={(value) => {
          if (!project.default_dashboard_id) {
            return value.session_id === project.id;
          }
          return value.id === project.default_dashboard_id;
        }}
        enableSearch={true}
        customSearchComponent={
          <div className="mx-1.5 mb-0.5 mt-1.5 flex flex-1 items-center gap-1.5 rounded-lg border border-secondary px-1.5 py-1">
            <SearchLgIcon className="h-4 w-4 flex-none text-secondary" />
            <Command.Input
              value={search}
              onValueChange={setSearch}
              className="w-full border-none bg-transparent p-0 text-xs"
              placeholder="Search dashboards"
            />
          </div>
        }
        searchPlaceholder="Search dashboards"
        controlledSearchTerm={search}
        setSearchTerm={setSearch}
        searchInputClassName="px-2 py-1 text-xs h-8"
        itemClassName="px-2 py-1.5 min-h-[30px] mx-1.5"
        asChild
        title={'Set default dashboard'}
        titleClassName="uppercase font-normal text-secondary text-xs px-2"
        headerRightDecorator={
          <Tooltip
            title="Set the dashboard to open when you click on this button"
            tooltipClassName="p-1"
          >
            <InfoCircleIcon className="h-4 w-4" />
          </Tooltip>
        }
        align="end"
        alignOffset={-14}
        sideOffset={10}
        isLoading={isLoading}
        emptyStateText={
          selectedTagIds.length > 0
            ? 'No dashboards found for selected tags'
            : 'No dashboards found'
        }
        topOfListComponent={
          selectedTagIds.length > 0 &&
          dashboards?.length === 0 && (
            <button
              type="button"
              className="ml-1"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedTags([]);
              }}
            >
              Clear selected tags
            </button>
          )
        }
      >
        <button
          type="button"
          className="ml-1 rounded-md border border-primary p-0.5 hover:bg-tertiary"
          onClick={(e) => {
            e.stopPropagation();
          }}
          ref={popoverRef}
        >
          <Tooltip
            title="Set project dashboard"
            tooltipClassName="p-1"
            className="flex items-center justify-center"
            placement="top"
          >
            <Edit01Icon className="h-3.5 w-3.5" />
          </Tooltip>
        </button>
      </PopoverDropdownMenu>
    </button>
  );
}
