import { BarChart10Icon } from '@langchain/untitled-ui-icons';
import { LocalOffer } from '@mui/icons-material';
import { Button } from '@mui/joy';
import LinearProgress from '@mui/joy/LinearProgress';
import { SxProps } from '@mui/joy/styles/types';

import dayjs, { ManipulateType } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useSearchParams } from 'react-router-dom';

import { ErrorBanner } from '@/components/ErrorBanner';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';

import { ButtonSwitchSimple } from '../../components/ButtonSwitch';
import { VegaPreview } from '../../components/VegaChart';
import {
  useOrganizationId,
  useRunStats,
  useRunsMonitor,
  useSessionMetadata,
} from '../../hooks/useSwr';
import {
  MonitorBlock,
  MonitorQueryParams,
  RunStatsSchema,
  SessionMetadataResponse,
  SessionSchema,
} from '../../types/schema';
import { cn } from '../../utils/tailwind';
import { ProjectMonitorGroupBySelectorPopoverContent } from './ProjectMonitorTagSelectorPopoverContent';
import { ProjectSidebarSlot } from './ProjectSidebar.utils';
import { ProjectStats } from './ProjectStats';
import { getDashboardPath } from './constants';

dayjs.extend(utc);

const INTERVALS: {
  label: string;
  value: string;
  params: Omit<MonitorQueryParams, 'groups'>;
}[] = [
  {
    value: '1h',
    label: '1 hour',
    params: {
      interval: { hours: 1 },
      stride: { minutes: 1 },
    },
  },
  {
    value: '9h',
    label: '9 hours',
    params: {
      interval: { hours: 9 },
      stride: { minutes: 5 },
    },
  },
  {
    value: '1d',
    label: '1 day',
    params: {
      interval: { days: 1 },
      stride: { minutes: 15 },
    },
  },
  {
    value: '3d',
    label: '3 days',
    params: {
      interval: { days: 3 },
      stride: { hours: 1 },
    },
  },
  {
    value: '7d',
    label: '7 days',
    params: {
      interval: { days: 7 },
      stride: { hours: 8 },
    },
  },
  {
    value: '30d',
    label: '30 days',
    params: {
      interval: { days: 30 },
      stride: { days: 1 },
    },
  },
];

function getTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (e) {
    return 'UTC';
  }
}

function useFallbackToPrevious(value: any) {
  const ref = useRef();
  useEffect(() => {
    ref.current = value ?? ref.current;
  }, [value]);
  return value ?? ref.current;
}

function MonitorConfigBar({
  stats,
  metadataValues,
  selectedTags,
  selectedMetadata,
  interval,
}: {
  metadataValues?: SessionMetadataResponse;
  stats?: RunStatsSchema;
  selectedTags: string[];
  selectedMetadata: string[];
  interval: string;
}) {
  const [isTagsPopoverOpen, setIsTagsPopoverOpen] = useState(false);
  const [isMetadataPopoverOpen, setIsMetadataPopoverOpen] = useState(false);
  const [, setSearchParams] = useSearchParams();

  const defaultSx: SxProps = (theme) => ({
    outline: `1px solid ${theme.vars.palette.divider}`,
    outlineOffset: '-1px',
  });
  const facets = useFallbackToPrevious(stats?.run_facets);
  const tagsToDisplay = facets
    ?.filter((facet) => facet.key === 'tag')
    .map((facet) => facet.value);
  const metadataToDisplay = metadataValues ? Object.keys(metadataValues) : [];

  const setSelectedTags = (next: string[]) => {
    setSearchParams(
      (prev) => {
        prev.set('tags', next.join('$|'));
        return prev;
      },
      { replace: true }
    );
  };
  const setSelectedMetadata = (next: string[]) => {
    setSearchParams(
      (prev) => {
        prev.set('metadata', next.join('$|'));
        return prev;
      },
      { replace: true }
    );
  };
  return (
    <div className="flex gap-5 self-end">
      {tagsToDisplay && (
        <Popover open={isTagsPopoverOpen} onOpenChange={setIsTagsPopoverOpen}>
          <PopoverTrigger asChild>
            <div>
              <Button
                color="neutral"
                variant={selectedTags.length > 0 ? 'solid' : 'outlined'}
                size="sm"
                startDecorator={<LocalOffer className="h-5 w-5" />}
                sx={[{ borderRadius: '6px' }, defaultSx]}
              >
                Trace tag
              </Button>
            </div>
          </PopoverTrigger>
          <PopoverContent
            sideOffset={5}
            alignOffset={5}
            align="start"
            className="w-[300px] p-0"
          >
            <ProjectMonitorGroupBySelectorPopoverContent
              selectedOptions={selectedTags}
              allOptions={tagsToDisplay.map((tag) => ({
                title: tag,
              }))}
              groupByTypeNamePlural="Tags"
              handleSelectOrUnselectOption={(
                option: string,
                shouldSelect: boolean
              ) => {
                setSelectedMetadata([]);
                if (shouldSelect) {
                  if (!selectedTags.includes(option)) {
                    setSelectedTags([...selectedTags, option]);
                  }
                } else {
                  setSelectedTags(
                    selectedTags.filter((selectedTag) => selectedTag !== option)
                  );
                }
              }}
              clearAllOptions={() => setSelectedTags([])}
            />
          </PopoverContent>
        </Popover>
      )}
      {metadataToDisplay && (
        <Popover
          open={isMetadataPopoverOpen}
          onOpenChange={setIsMetadataPopoverOpen}
        >
          <PopoverTrigger asChild>
            <div>
              <Button
                color="neutral"
                variant={selectedMetadata.length > 0 ? 'solid' : 'outlined'}
                size="sm"
                startDecorator={<LocalOffer className="h-5 w-5" />}
                sx={[{ borderRadius: '6px' }, defaultSx]}
              >
                Metadata
              </Button>
            </div>
          </PopoverTrigger>
          <PopoverContent
            sideOffset={5}
            alignOffset={5}
            align="start"
            className="w-[300px] p-0"
          >
            <ProjectMonitorGroupBySelectorPopoverContent
              selectedOptions={selectedMetadata}
              groupByTypeNamePlural="Metadata"
              allOptions={metadataToDisplay.map((metadata) => ({
                title: metadata,
                subtitle: metadataValues?.[metadata]?.join(', '),
              }))}
              handleSelectOrUnselectOption={(
                option: string,
                shouldSelect: boolean
              ) => {
                setSelectedTags([]);
                if (shouldSelect) {
                  if (!selectedMetadata.includes(option)) {
                    setSelectedMetadata([option]);
                  }
                } else {
                  setSelectedMetadata(
                    selectedMetadata.filter((s) => s !== option)
                  );
                }
              }}
              clearAllOptions={() => setSelectedMetadata([])}
            />
          </PopoverContent>
        </Popover>
      )}
      <ButtonSwitchSimple
        options={INTERVALS}
        value={interval}
        onChange={(nextInterval) =>
          setSearchParams(
            (prev) => {
              prev.set('interval', nextInterval);
              return prev;
            },
            { replace: true }
          )
        }
        sx={{ marginBottom: '16px', alignSelf: 'flex-end' }}
      />
    </div>
  );
}

export function ProjectMonitor({ sessionId }: { sessionId: string }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const interval = searchParams.get('interval') || '7d';
  const selectedInterval = INTERVALS.find((i) => i.value === interval);
  const intervalEntry = Object.entries(
    selectedInterval?.params.interval ?? {}
  )[0];
  const intervalUnit = intervalEntry[0];
  const intervalValue = intervalEntry[1];

  const start_time = useMemo(
    () =>
      dayjs()
        .subtract(intervalValue, intervalUnit as ManipulateType)
        .utc()
        .toISOString(),
    [intervalValue, intervalUnit]
  );

  const runStats = useRunStats({
    session: [sessionId],
    is_root: true,
    start_time,
    data_source_type: 'historical',
  });
  const commonMetadata = useSessionMetadata(
    {
      k: 5,
      start_time,
      root_runs_only: true,
    },
    sessionId
  );

  const selectedTagsParams = searchParams.get('tags') ?? '';
  const selectedTags = selectedTagsParams.split('$|').filter(Boolean);

  const selectedMetadataParams = searchParams.get('metadata') ?? '';
  const selectedMetadata = selectedMetadataParams.split('$|').filter(Boolean);

  const selectedMarks: { [key: string]: string[] } = {};

  for (const [key, value] of searchParams) {
    selectedMarks[key] = value.split(',').filter(Boolean);
  }
  const setSelectedMarks = useCallback(
    (newMarks: Record<string, string[]>) => {
      setSearchParams(
        (prev) => {
          Object.entries(newMarks).forEach(([key, value]) => {
            prev.set(key, value.join(','));
          });
          return prev;
        },
        { replace: true }
      );
    },
    [setSearchParams]
  );
  const selectedMetadataValues = selectedMetadata.flatMap((metadata) => {
    return commonMetadata.data?.[metadata]?.map((value) => ({
      key: metadata,
      value,
    }));
  }, []);

  const groups =
    selectedTags.length > 0
      ? selectedTags.map((tag) => ({ session: sessionId, tag }))
      : selectedMetadata.length > 0
      ? selectedMetadataValues.map((metadata) => ({
          session: sessionId,
          metadata,
        }))
      : [{ session: sessionId }];
  const monitor = useRunsMonitor(
    selectedInterval &&
      (selectedMetadata.length > 0 ? !!commonMetadata.data : true)
      ? {
          ...selectedInterval.params,
          groups,
          timezone: getTimezone(),
        }
      : null
  );
  const sections = useMemo(() => {
    return Object.entries(
      monitor.data?.blocks.reduce((acc, block) => {
        if (!acc[block.section]) {
          acc[block.section] = [];
        }
        acc[block.section].push(block);
        return acc;
      }, {} as Record<string, MonitorBlock[]>) || {}
    );
  }, [monitor.data]);

  const handleLLMOrTraceClick = (
    type: 'llm' | 'trace',
    datum: { ts: number[]; stride: string[]; group: string[] },
    section: string
  ) => {
    const start_time = new Date(datum.ts[0]).toISOString();
    const strideEntry = Object.entries(
      selectedInterval?.params.stride ?? {}
    )[0];
    const strideUnit = strideEntry[0];
    const strideValue = strideEntry[1];

    const end_time = dayjs(start_time)
      .add(strideValue, strideUnit as ManipulateType)
      .toISOString();
    const parsedTag = datum.group[0];
    const parsedMetadataValue = datum.group[0]?.split(' = ')[1];
    const parsedMetadataKey = datum.group[0]?.split(' = ')[0];

    const andQueries: string[] = [];
    andQueries.push(
      type === 'llm' ? 'eq(run_type, "llm")' : 'eq(is_root, true)'
    );

    if (selectedTags.length > 0 || parsedMetadataValue) {
      andQueries.push(
        selectedTags.length > 0
          ? `eq(tag, "${parsedTag}")`
          : `and(eq(metadata_key, '${parsedMetadataKey}'), eq(metadata_value, ${parsedMetadataValue}))`
      );

      const currentSectionSelectedMark = selectedMarks[section]?.[0]; // We only allow selecting one mark per section so just take the first
      if (
        ['Success', 'Pending', 'Error'].includes(currentSectionSelectedMark)
      ) {
        andQueries.push(
          `eq(status, "${currentSectionSelectedMark.toLocaleLowerCase()}")`
        );
      }
    }

    setSearchParams({
      tab: '0',
      timeModel: JSON.stringify({ start_time, end_time }),
      ...(andQueries.length > 0
        ? {
            searchModel: JSON.stringify({
              mode: 'raw',
              query:
                andQueries.length > 1
                  ? `and(${andQueries.join(', ')})`
                  : andQueries[0],
            }),
          }
        : {}),
    });
    window.scrollTo({ behavior: 'instant', top: 0 });
  };

  const CLICK_TARGETS = {
    llm: handleLLMOrTraceClick.bind(null, 'llm'),
    trace: handleLLMOrTraceClick.bind(null, 'trace'),
    feedback: (datum: {
      ts: number[];
      stride: string[];
      group: string[];
      feedback_key: string[];
    }) => {
      const start_time = new Date(datum.ts[0]).toISOString();
      const strideEntry = Object.entries(
        selectedInterval?.params.stride ?? {}
      )[0];
      const strideUnit = strideEntry[0];
      const strideValue = strideEntry[1];

      const end_time = dayjs(start_time)
        .add(strideValue, strideUnit as ManipulateType)
        .toISOString();
      let search_query = '';
      const parsedTag = datum.group[0];
      const parsedMetadataValue = datum.group[0]?.split(' = ')[1];
      const parsedMetadataKey = datum.group[0]?.split(' = ')[0];
      if (selectedTags.length > 0) {
        search_query = `and(eq(tag, "${parsedTag}"), eq(feedback_key, "${datum.feedback_key?.[0]}"))`;
      } else if (parsedMetadataValue) {
        search_query = `and(and(eq(metadata_key, '${parsedMetadataKey}'), eq(metadata_value, ${parsedMetadataValue})), eq(feedback_key, "${datum.feedback_key?.[0]}"))`;
      } else {
        search_query = `eq(feedback_key, "${datum.feedback_key?.[0]}")`;
      }
      setSearchParams({
        tab: '0',
        timeModel: JSON.stringify({ start_time, end_time }),
        searchModel: JSON.stringify({
          mode: 'raw',
          query: `and(eq(is_root, true), ${search_query})`,
        }),
      });
      window.scrollTo({ behavior: 'instant', top: 0 });
    },
  };

  useEffect(() => {
    const updatedSelectedMarks = selectedMarks;
    sections.forEach(([section, blocks]) => {
      if (!updatedSelectedMarks[section]) {
        const toggleableMarks = blocks.reduce((acc, block) => {
          if (block.toggleable_marks) {
            Object.keys(block.toggleable_marks).forEach((mark) => {
              acc.add(mark);
            });
          }
          return acc;
        }, new Set<string>());
        if (toggleableMarks.size > 0) {
          updatedSelectedMarks[section] = [
            toggleableMarks.values().next().value,
          ];
        }
      }
    });
    setSelectedMarks(updatedSelectedMarks);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sections]);

  const selectedFilters =
    selectedTags.length > 0 || selectedMetadata.length > 0;

  if (monitor.error) {
    return <ErrorBanner className="m-4">{monitor.error.message}</ErrorBanner>;
  }

  return (
    <div className="flex w-full flex-col items-start bg-secondary">
      {monitor.isLoading ? (
        <LinearProgress sx={{ alignSelf: 'stretch' }} />
      ) : (
        <div className="h-[6px] self-stretch" />
      )}
      <div className="flex w-full flex-col items-start px-4 pt-4">
        <MonitorConfigBar
          stats={runStats.data}
          metadataValues={commonMetadata.data}
          selectedTags={selectedTags}
          selectedMetadata={selectedMetadata}
          interval={interval}
        />

        {sections.map(([section, blocks]) => {
          const aggregateToggleableMarks = blocks.reduce((acc, block) => {
            if (block.toggleable_marks) {
              Object.keys(block.toggleable_marks).forEach((mark) =>
                acc.add(mark)
              );
            }
            return acc;
          }, new Set<string>());
          // Button group for selecting between the toggleable marks
          const toggleMarksButtonGroup = selectedFilters ? (
            <div className={cn('z-10 items-center font-medium')}>
              <ButtonSwitchSimple
                options={[...aggregateToggleableMarks].map((mark) => ({
                  label: mark,
                  value: mark,
                }))}
                value={selectedMarks[section]}
                onChange={(clickedMark) => {
                  const selectedMarksCurrentSection = selectedMarks[section];
                  if (selectedMarksCurrentSection.includes(clickedMark)) {
                    return;
                  } else {
                    setSelectedMarks({
                      ...selectedMarks,
                      [section]: [clickedMark],
                    });
                  }
                }}
              />
            </div>
          ) : null;
          return (
            <Fragment key={section}>
              <div className={cn('mb-4 flex items-center gap-5 pl-5')}>
                <h3 className={cn('z-10 font-medium')}>{section}</h3>
                {toggleMarksButtonGroup}
              </div>
              <div className="mb-8 grid auto-rows-[450px] grid-cols-2 gap-4 self-stretch">
                {blocks
                  .filter((block) => block.rows.length > 1)
                  .map((block, i) => (
                    <VegaPreview
                      key={i}
                      className="rounded-lg border border-tertiary bg-primary p-4"
                      title={
                        block.subtitle
                          ? `${block.title} – ${block.subtitle}`
                          : block.title
                      }
                      spec={block.chart_spec}
                      columns={block.columns}
                      selectedMarks={selectedMarks[section]}
                      toggleableMarks={
                        selectedFilters ? block.toggleable_marks : undefined
                      }
                      rows={block.rows}
                      alwaysEnableCursorPointer={groups.length < 2}
                      withZoom
                      numberOfGroups={groups.length}
                      signalListeners={
                        block.click_target && CLICK_TARGETS[block.click_target]
                          ? {
                              click: (_, datum) => {
                                CLICK_TARGETS[block.click_target!]?.(
                                  datum,
                                  section
                                );
                              },
                            }
                          : undefined
                      }
                    />
                  ))}
              </div>
            </Fragment>
          );
        })}
      </div>
      <ProjectSidebarSlot.Fill>
        <ProjectStats
          stats={runStats.data}
          isLoadingStats={runStats.isLoading}
          timeModel={{ duration: selectedInterval?.value }}
          isTraces={true}
        />
      </ProjectSidebarSlot.Fill>
    </div>
  );
}

export function ProjectMonitorMoved({ project }: { project?: SessionSchema }) {
  const organizationId = useOrganizationId();

  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-4">
      <div className="rounded-full bg-brand-secondary p-3">
        <BarChart10Icon className="size-6 text-brand-secondary" />
      </div>
      <div>
        <h1 className="mb-1 text-center text-lg font-medium">
          Monitoring has moved!
        </h1>
        <div className="max-w-[450px] text-center text-sm text-tertiary">
          Find all of your dashboards, both prebuilt and custom, in the global
          Monitoring section. Click the Dashboard button in the top right to see
          this project's dashboard, or use the link below.
        </div>
      </div>
      <Button color="primary">
        <a href={project ? getDashboardPath(organizationId, project) : ''}>
          Go to project dashboard
        </a>
      </Button>
    </div>
  );
}
