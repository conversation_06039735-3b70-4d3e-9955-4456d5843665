import { ChevronDownIcon, InfoCircleIcon } from '@langchain/untitled-ui-icons';
import { LinearProgress, Tooltip } from '@mui/joy';

import { ReactNode } from 'react';

import { Rate } from '@/components/Rate';
import { TooltipV2 } from '@/components/Tooltip/Tooltip';
import { RunGroupStatsSchema, RunStatsSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { FeedbackChips } from '../../components/FeedbackChips';
import { SessionLatencyCell } from '../../components/SessionsTable/components/Cell';
import { ProjectSidebarHeaderTag } from './ProjectSidebarComponents';
import { SIDEBAR_HEADER_STYLE } from './constants';

const formatter = new Intl.NumberFormat('en-US', {});
const usdFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

const STAT_TITLE_STYLE =
  'whitespace-nowrap text-xs font-medium tracking-tight text-quaternary';

export function ProjectStatsV2({
  stats,
  isLoadingStats,
  isTraces,
  useExperimentalSearch,
  source,
}: {
  stats?: RunStatsSchema | RunGroupStatsSchema;
  additionalStats?: Array<{ label: ReactNode; value: ReactNode }>;
  isLoadingStats?: boolean;
  isTraces?: boolean;
  useExperimentalSearch?: boolean;
  source: 'conversation' | 'trace';
}) {
  const [statsOpen, setStatsOpen] = useLocalStorageState(
    `ls:${source}:stats-open`,
    true
  );

  return (
    <div
      className={cn('flex flex-col gap-1 text-sm', {
        'pb-4': statsOpen,
      })}
    >
      <div
        className={cn(SIDEBAR_HEADER_STYLE, 'relative')}
        onClick={() => setStatsOpen(!statsOpen)}
      >
        <div className="flex items-center gap-1">
          <h2>Stats</h2>
          {useExperimentalSearch && (
            <div className="flex items-center gap-1 text-xs text-warning">
              <TooltipV2
                title={
                  <span className="truncate">
                    Stats don't update with experimental search
                  </span>
                }
              >
                <InfoCircleIcon className="size-4" />
              </TooltipV2>
            </div>
          )}
        </div>
        <div className="flex items-center justify-end gap-2">
          {stats && (
            <ProjectSidebarHeaderTag
              show={!statsOpen}
              text={
                source === 'conversation' && 'group_count' in stats
                  ? `${formatter.format(stats.group_count ?? 0)} threads`
                  : `${formatter.format(stats.run_count ?? 0)} runs`
              }
            />
          )}
          <ChevronDownIcon
            className={`h-4 w-4 transition-transform ${
              statsOpen ? '' : '-rotate-90'
            }`}
          />
        </div>

        {isLoadingStats && (
          <div className="absolute bottom-0 left-4 right-4 h-1 overflow-hidden">
            <LinearProgress />
          </div>
        )}
      </div>
      {stats?.run_count == null ? null : (
        <div className={cn(statsOpen ? 'flex flex-col gap-4 px-4' : 'hidden')}>
          {'group_count' in stats && stats.group_count != null && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>Thread Count</div>
              <div
                data-testid="project-stats-group-count"
                className="font-medium"
              >
                {formatter.format(stats.group_count)}
              </div>
            </div>
          )}

          {stats.run_count != null && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>
                {isTraces ? 'Trace Count' : 'Run Count'}
              </div>
              <div
                data-testid="project-stats-run-count"
                className="font-medium"
              >
                {formatter.format(stats.run_count)}
              </div>
            </div>
          )}

          {stats.total_tokens != null && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>Total Tokens</div>
              <div className="font-medium">
                {formatter.format(stats.total_tokens)}

                {stats.total_cost != null && (
                  <span className="text-tertiary">
                    {' / '}
                    {usdFormatter.format(+stats.total_cost)}{' '}
                    <Tooltip title="Only runs created after 5 Feb 2024 have cost info">
                      <span>
                        <InfoCircleIcon className="inline-block h-4 w-4 -translate-y-0.5" />
                      </span>
                    </Tooltip>
                  </span>
                )}
              </div>
            </div>
          )}

          {stats.median_tokens != null && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>Median Tokens</div>
              <div className="font-medium">
                {formatter.format(stats.median_tokens)}
              </div>
            </div>
          )}

          {stats.error_rate != null && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>Error Rate</div>
              <div
                data-testid="project-stats-error-rate"
                className="font-medium"
              >
                <Rate value={stats.error_rate} />
              </div>
            </div>
          )}

          {stats.streaming_rate != null && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>% Streaming</div>
              <div className="font-medium">
                {formatter.format(Math.round(stats.streaming_rate * 100))}%
              </div>
            </div>
          )}

          {(stats.first_token_p50 != null || stats.first_token_p99 != null) && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>First Token</div>

              <div className="flex flex-wrap gap-1.5">
                <SessionLatencyCell
                  row={stats}
                  metric="first_token_p50"
                  label="P50: "
                  labelPlacement="outside"
                />
                <SessionLatencyCell
                  row={stats}
                  metric="first_token_p99"
                  label="P99: "
                  labelPlacement="outside"
                />
              </div>
            </div>
          )}
          {(stats.latency_p50 != null || stats.latency_p99 != null) && (
            <div className="flex flex-col gap-1">
              <div className={STAT_TITLE_STYLE}>Latency</div>

              <div className="flex flex-wrap gap-1.5">
                <SessionLatencyCell
                  testId="project-stats-latency-p50"
                  row={stats}
                  label="P50: "
                  labelPlacement="outside"
                />
                <SessionLatencyCell
                  testId="project-stats-latency-p99"
                  row={stats}
                  metric="latency_p99"
                  label="P99: "
                  labelPlacement="outside"
                />
              </div>
            </div>
          )}

          {stats.feedback_stats != null &&
            Object.keys(stats.feedback_stats).length > 0 && (
              <div className="flex flex-col gap-1">
                <div className={STAT_TITLE_STYLE}>Feedback</div>

                <div className="flex flex-wrap items-center gap-2">
                  <FeedbackChips
                    className="text-xxs"
                    chipClassName="text-xxs"
                    testIdPrefix="project-stats"
                    feedbackStats={stats.feedback_stats}
                    disablePopover
                  />
                </div>
              </div>
            )}
        </div>
      )}
    </div>
  );
}
