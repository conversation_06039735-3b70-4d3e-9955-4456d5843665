import { partition, sortBy } from 'lodash-es';
import { useMemo } from 'react';

import {
  GroupedComparison,
  groupComparisonsByRunFields,
} from '@/components/FilterBar/FilterBar.utils';
import { getRunFields } from '@/components/FilterBar/fields/runs';
import {
  flattenAst,
  isSameComparisons,
  stringToAst,
} from '@/components/RunsTable/filter/ast';
import {
  InstanceFlags,
  useInstanceFlagOrDefault,
} from '@/hooks/useInstanceFlag';

import { RunFacetField, RunStatsSchema } from '../../../types/schema';
import { categoryKey } from '../utils/facetFiltering';

export interface FacetSelectData {
  isSelected: boolean;
  queryGroups: Pick<GroupedComparison, 'comparisons'>[];
  selected: Pick<GroupedComparison, 'comparisons'>[];
  unselected: Pick<GroupedComparison, 'comparisons'>[];
  isMatched: boolean;
}

export interface FacetGroup {
  facets: RunFacetField[];
  facetSelectMap: Map<string, FacetSelectData>;
  selectedCount: number;
}

export interface UseFacetGroupsParams {
  currentFilterStats: RunStatsSchema | undefined;
  allStats: RunStatsSchema;
  groupedFilters: Pick<GroupedComparison, 'comparisons'>[];
}

export interface ComputeFacetGroupsParams {
  allStatsFacets: RunFacetField[] | undefined;
  currentFilterStatsFacets: RunFacetField[] | undefined;
  runFields: ReturnType<typeof getRunFields>;
  groupedFilters: Pick<GroupedComparison, 'comparisons'>[];
}

export function createMatchedStatsSet(
  currentFilterStatsFacets: RunFacetField[] | undefined
): Set<string> {
  return (
    currentFilterStatsFacets?.reduce((acc, facet) => {
      const key = `${facet.key}${facet.value}`;
      return acc.add(key);
    }, new Set<string>()) ?? new Set<string>()
  );
}

export function sortFacetsByPriority(
  allStatsFacets: RunFacetField[] | undefined,
  runFields: ReturnType<typeof getRunFields>
): RunFacetField[] {
  return [
    ...sortBy(
      allStatsFacets ?? [],
      (f) => {
        const category = categoryKey(f).toLowerCase();
        return runFields.findIndex(
          (field) => field.label.toLowerCase().split(' ').join('_') === category
        );
      },
      (f) =>
        f.key.startsWith('feedback_key') || f.key === 'feedback_value'
          ? f.value.split(' == ')
          : '',
      (f) =>
        f.key.startsWith('metadata_key') ||
        f.key.startsWith('input_key') ||
        f.key.startsWith('output_key')
          ? f.value.split(' == ')
          : '',
      (f) => (f.key === 'feedback_score' ? -parseFloat(f.value) : 0),
      (f) => -(f.n ?? 0)
    ),
  ];
}

export function calculateFacetSelectionState(
  facet: RunFacetField,
  runFields: ReturnType<typeof getRunFields>,
  groupedFilters: Pick<GroupedComparison, 'comparisons'>[]
): Omit<FacetSelectData, 'isMatched'> {
  const queryGroups = groupComparisonsByRunFields(
    // wrap the query in `and(...)` to prevent flattening of feedback / metadata pairs
    flattenAst(stringToAst(facet.query ? `and(${facet.query})` : '')),
    runFields
  );

  const [selectedGroups, unselectedGroups] = partition(
    groupedFilters,
    (group) =>
      queryGroups.find((queryGroup) =>
        isSameComparisons(group.comparisons, queryGroup.comparisons)
      )
  );

  const isSelected = selectedGroups.length > 0;

  return {
    isSelected,
    queryGroups,
    selected: selectedGroups,
    unselected: unselectedGroups,
  };
}

export function processFacetIntoGroups(
  facet: RunFacetField,
  selectionState: Omit<FacetSelectData, 'isMatched'>,
  matchedStats: Set<string>,
  groups: Record<string, FacetGroup>
): number {
  const category = categoryKey(facet);
  if (!groups[category]) {
    groups[category] = {
      facets: [],
      facetSelectMap: new Map(),
      selectedCount: 0,
    };
  }

  const facetKey = `${facet.key}${facet.value}`;

  groups[category].facetSelectMap.set(facetKey, {
    isSelected: selectionState.isSelected,
    queryGroups: selectionState.queryGroups,
    selected: selectionState.selected,
    unselected: selectionState.unselected,
    isMatched: matchedStats.has(facetKey),
  });

  groups[category].facets.push(facet);
  groups[category].selectedCount += selectionState.isSelected ? 1 : 0;

  return selectionState.isSelected ? 1 : 0;
}

export function computeFacetGroups({
  allStatsFacets,
  currentFilterStatsFacets,
  runFields,
  groupedFilters,
}: ComputeFacetGroupsParams) {
  let totalSelectedCount = 0;
  const matchedStats = createMatchedStatsSet(currentFilterStatsFacets);
  const facets = sortFacetsByPriority(allStatsFacets, runFields);
  const groups: Record<string, FacetGroup> = {};

  facets.forEach((facet) => {
    const selectionState = calculateFacetSelectionState(
      facet,
      runFields,
      groupedFilters
    );

    const selectedCountIncrement = processFacetIntoGroups(
      facet,
      selectionState,
      matchedStats,
      groups
    );

    totalSelectedCount += selectedCountIncrement;
  });

  return { facetGroups: groups, totalSelectedCount };
}

export function useFacetGroups({
  currentFilterStats,
  allStats,
  groupedFilters,
}: UseFacetGroupsParams) {
  const searchEnabled = useInstanceFlagOrDefault(
    InstanceFlags.search_enabled
  ) as boolean;

  const runFields = useMemo(
    () =>
      getRunFields({
        searchEnabled,
      }),
    [searchEnabled]
  );

  const { facetGroups, totalSelectedCount } = useMemo(() => {
    return computeFacetGroups({
      allStatsFacets: allStats.run_facets,
      currentFilterStatsFacets: currentFilterStats?.run_facets,
      runFields,
      groupedFilters,
    });
  }, [allStats.run_facets, runFields, currentFilterStats, groupedFilters]);

  return {
    facetGroups,
    runFields,
    totalSelectedCount,
  };
}
