import { LinearProgress } from '@mui/joy';
import { PaginationState, Updater } from '@tanstack/react-table';

import dayjs from 'dayjs';
import { useCallback, useMemo, useRef, useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { useParams, useSearchParams } from 'react-router-dom';

import { ConversationList } from '@/Pages/Conversation/ConversationList';
import { AlertsCreateButton } from '@/components/Alerts/AlertButtons';
import { AlertsCreatePane } from '@/components/Alerts/AlertsPane';
import Breadcrumbs from '@/components/Breadcrumbs';
import { CopyInlineLink } from '@/components/CopyInlineLink';
import { ErrorBanner } from '@/components/ErrorBanner';
import { DEFAULT_FILTERS } from '@/components/FilterBar/constants';
import EnvQuickstart from '@/components/Onboarding/TracingQuickstart';
import {
  findComparsion,
  flattenAst,
  stringToAst,
} from '@/components/RunsTable/filter/ast';
import { SearchModel } from '@/components/RunsTable/types';
import { parseRelativeDate } from '@/components/RunsTable/utils/getDateTimeRangeLabel';
import { SessionCrudPaneWithButton } from '@/components/SessionCrudPane/SessionCrudPaneWithButton';
import { useDefaultDuration } from '@/hooks/useDefaultDuration';
import { InstanceFlags, useInstanceFlag } from '@/hooks/useInstanceFlag';
import { usePermissions } from '@/hooks/usePermissions.tsx';
import { useStateFromSearchParams } from '@/hooks/useStateFromSearchParams';
import {
  useGroupRuns,
  useGroupRunsStats,
  useRunStats,
  useSession,
} from '@/hooks/useSwr';
import { SessionSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { PageTitle } from '../../components/PageTitle';
import { RunsTable } from '../../components/RunsTable';
import { useDebouncedFilter } from '../../components/RunsTable/hooks/useDebouncedFilter';
import { useSearchModel } from '../../components/RunsTable/hooks/useSearchModel';
import {
  TabGroup,
  TabLabel,
  TabList,
  TabPanel,
  TabPanels,
} from '../../components/Tabs';
import { GridTimeModel, useDataGridState } from '../../hooks/useDataGridState';
import ProjectIcon from '../../icons/ProjectIcon';
import { appProjectsPath, appSessionPath } from '../../utils/constants';
import { ALERTS_BADGE_LOCAL_STORAGE_KEY } from '../Run/components/constants';
import { ProjectAlerts } from './ProjectAlerts';
import { ProjectMonitorSetting } from './ProjectMonitorSetting';
import {
  ProjectRuleButton,
  ProjectSessionRuleForm,
} from './ProjectSessionRuleForm';
import { ProjectSidebarSlot } from './ProjectSidebar.utils';
import { ProjectSidebarV2 } from './ProjectSidebarV2';
import { ProjectStatsV2 } from './ProjectStatsV2';
import ProjectTTLSetting from './ProjectTTLSetting';
import { COLUMN_VISIBILITY } from './constants';

function ProjectRunsTable({
  sessionId,
  isTestRun,

  isAutomate,
  onAutomateClose,
}: {
  sessionId: string;
  isTestRun: boolean;

  isAutomate: boolean;
  onAutomateClose?: () => void;
}) {
  const {
    searchModel,
    setSearchModel: _setSearchModel,
    updateSearchParams,
  } = useSearchModel();
  const [searchParams, setSearchParams] = useSearchParams();

  const beforePreviewSearchModelRef = useRef<SearchModel | undefined>();
  function handlePreviewSearchModel(model: SearchModel | null) {
    if (model) {
      beforePreviewSearchModelRef.current ??= searchModel;
      setSearchModel(model);
    } else {
      const newModel = beforePreviewSearchModelRef.current;
      if (newModel) setSearchModel(newModel);
      beforePreviewSearchModelRef.current = undefined;
    }
  }

  const view = useMemo(() => {
    const customFilterViewId = searchParams.get('custom_filter_view_id');
    if (customFilterViewId) return customFilterViewId;
    const operands = flattenAst(stringToAst(searchModel.filter))?.operands;
    if (operands?.find(findComparsion('eq', 'is_root', true))) return 'trace';
    if (operands?.find(findComparsion('eq', 'run_type', 'llm'))) return 'llm';
    return 'all';
  }, [searchModel.filter, searchParams]);

  const showTreeView = useMemo(() => {
    const operands = flattenAst(stringToAst(searchModel.filter))?.operands;
    return operands?.find(findComparsion('eq', 'is_root', true));
  }, [searchModel.filter]);

  const { defaultDurationTimeModel } = useDefaultDuration('runsTable');

  const {
    sortModel,
    columnVisibilityModel,
    paginationModel,
    filterModel,
    setSortModel,
    setColumnVisibilityModel,
    onColumnVisibilityResetClick,
    setPaginationModel,
    resetPaginationModel,
    setFilterModel,
    timeModel,
    setTimeModel,
    initialState,
  } = useDataGridState({
    defaultTimeModel: isTestRun ? undefined : defaultDurationTimeModel,
    defaultColumnVisibility: isTestRun
      ? COLUMN_VISIBILITY.eval
      : COLUMN_VISIBILITY.default,
    columnVisibilityStateKey: isTestRun
      ? 'ls:ui:runs:columnVisibilityModel:eval'
      : 'ls:ui:runs:columnVisibilityModel:default',
    updateSearchParams,
  });
  const setSearchModel: typeof _setSearchModel = useCallback(
    (arg) => {
      resetPaginationModel();
      return _setSearchModel(arg);
    },
    [resetPaginationModel, _setSearchModel]
  );
  const runsSessionFilter = useMemo(
    () => ({ session: [sessionId] }),
    [sessionId]
  );
  const debouncedRunsFilter = useDebouncedFilter({
    timeModel,
    runsFilter: runsSessionFilter,
    searchModel,
  });
  const { datasetShareToken } = useParams();
  const { data: stats, isLoading: isStatsLoading } = useRunStats(
    debouncedRunsFilter,
    {
      datasetShareToken,
    }
  );

  const { data: allStats } = useRunStats(
    {
      filter: '',
      session: [sessionId],
      start_time: debouncedRunsFilter?.start_time,
      end_time: debouncedRunsFilter?.end_time,
    },
    { datasetShareToken },
    { revalidateOnFocus: false }
  );

  const handleExperimentalSearchToggle = useCallback(
    (enabled: boolean) => {
      // update search model to include experimental search param when toggle is changed
      setSearchModel({
        ...searchModel,
        useExperimentalSearch: enabled,
      });
    },
    [searchModel, setSearchModel]
  );

  return (
    <>
      <RunsTable
        virtual
        view={view}
        tableDisplay={showTreeView ? 'tree' : 'list'}
        withTypeCol={!showTreeView}
        onViewChange={(
          newView,
          maybeFilter,
          maybeTraceFilter,
          maybeTreeFilter
        ) => {
          const isFilterView = !DEFAULT_FILTERS.includes(newView);
          if (isFilterView) {
            const filterViewId = newView;
            const isTogglingOff = filterViewId === view;
            setSearchModel({
              filter: isTogglingOff ? 'eq(is_root, true)' : maybeFilter,
              traceFilter: isTogglingOff ? '' : maybeTraceFilter,
              treeFilter: isTogglingOff ? '' : maybeTreeFilter,
            });
            setSearchParams((params) => {
              if (isTogglingOff) {
                params.delete('custom_filter_view_id');
              } else {
                params.set('custom_filter_view_id', filterViewId);
              }
              return params;
            });
          } else {
            setSearchParams((params) => {
              params.delete('custom_filter_view_id');
              return params;
            });

            const filterMap = {
              all: '',
              trace: 'eq(is_root, true)',
              llm: 'eq(run_type, "llm")',
            };
            setSearchModel({
              filter: filterMap[newView as keyof typeof filterMap] || '',
            });
          }
        }}
        debouncedRunsFilter={debouncedRunsFilter}
        runsFilter={runsSessionFilter}
        searchModel={searchModel}
        setSearchModel={setSearchModel}
        sortModel={sortModel}
        columnVisibilityModel={columnVisibilityModel}
        paginationModel={paginationModel}
        filterModel={filterModel}
        setSortModel={setSortModel}
        setColumnVisibilityModel={setColumnVisibilityModel}
        onColumnVisibilityResetClick={onColumnVisibilityResetClick}
        setPaginationModel={setPaginationModel}
        resetPaginationModel={resetPaginationModel}
        setFilterModel={setFilterModel}
        timeModel={timeModel}
        setTimeModel={setTimeModel}
        initialState={initialState}
        onExperimentalSearchToggle={handleExperimentalSearchToggle}
      />

      <ProjectSidebarSlot.Fill>
        {isAutomate && (
          <ProjectSessionRuleForm
            sessionId={sessionId}
            searchModel={searchModel}
            onClose={() => {
              handlePreviewSearchModel(null);
              onAutomateClose?.();
            }}
            onPreviewSearchModel={handlePreviewSearchModel}
          />
        )}

        {!isAutomate && (
          <>
            <ProjectStatsV2
              stats={stats}
              isLoadingStats={isStatsLoading}
              isTraces={debouncedRunsFilter?.is_root}
              useExperimentalSearch={searchModel.useExperimentalSearch}
              source="trace"
            />
            <div className="overflow-hidden border-t border-t-secondary empty:hidden">
              <ProjectSidebarV2
                stats={stats}
                searchModel={searchModel}
                setSearchModel={setSearchModel}
                allStats={allStats}
              />
            </div>
          </>
        )}
      </ProjectSidebarSlot.Fill>
    </>
  );
}

function ProjectConversations(props: { sessionId: string }) {
  // store filter in URL search bar
  const [searchParams, setSearchParams] = useSearchParams();

  const [pageIndex, setPageIndex] = useStateFromSearchParams(
    'pageIndex',
    '0',
    setSearchParams
  );
  const [pageSize, setPageSize] = useStateFromSearchParams(
    'pageSize',
    '10',
    setSearchParams
  );

  const pagination = useMemo(
    () => ({
      pageIndex: pageIndex ? Number(pageIndex) : 0,
      pageSize: pageSize ? Number(pageSize) : 10,
    }),
    [pageIndex, pageSize]
  );

  const setPagination = useCallback(
    (paginationUpdater: Updater<PaginationState>) => {
      if (typeof paginationUpdater === 'function') {
        const newPagination = paginationUpdater(pagination);
        setPageIndex(`${newPagination.pageIndex}`);
        setPageSize(`${newPagination.pageSize}`);
      } else {
        setPageIndex(`${paginationUpdater.pageIndex}`);
        setPageSize(`${paginationUpdater.pageSize}`);
      }
    },
    [setPageIndex, setPageSize, pagination]
  );

  const filter = searchParams.get('threadsFilter') || undefined;
  const setFilter = useCallback(
    (filter: string | undefined, setter?: typeof setSearchParams) => {
      const mSetSearchParams = setter ?? setSearchParams;
      mSetSearchParams((params) => {
        params.set('threadsFilter', filter || '');
        return params;
      });
    },
    [setSearchParams]
  );

  const { defaultDurationTimeModel } = useDefaultDuration('conversationsList');
  const [timeModel, setTimeModel] = useState<GridTimeModel>(
    defaultDurationTimeModel
  );

  const timeFilter = useMemo(() => {
    const startDuration = parseRelativeDate(timeModel.duration);
    let startTime = timeModel.start_time || undefined;
    let endTime = timeModel.end_time || undefined;

    if (timeModel.start_time == null && startDuration != null) {
      startTime = dayjs
        .utc()
        .subtract(startDuration.value, startDuration.unit)
        .toISOString();
      endTime = undefined;
    }

    return { start_time: startTime, end_time: endTime };
  }, [timeModel.duration, timeModel.end_time, timeModel.start_time]);

  const { data: stats, isLoading: isStatsLoading } = useGroupRunsStats({
    session_id: props.sessionId,
    group_by: 'conversation',
    start_time: timeFilter.start_time,
    end_time: timeFilter.end_time,
    filter,
  });
  const { data: allStats } = useGroupRunsStats(
    {
      session_id: props.sessionId,
      group_by: 'conversation',
      start_time: timeFilter.start_time,
      end_time: timeFilter.end_time,
    },
    { revalidateOnFocus: false }
  );

  const groups = useGroupRuns({
    session_id: props.sessionId,
    group_by: 'conversation',
    start_time: timeFilter.start_time,
    end_time: timeFilter.end_time,
    offset: pagination.pageIndex * pagination.pageSize,
    limit: pagination.pageSize,
    filter,
  });

  const resetPaginationModel = useCallback(() => {
    setPagination({ pageIndex: 0, pageSize: pagination.pageSize });
  }, [pagination.pageSize]);

  const setSearchModel: (value: SearchModel) => void = useCallback(
    (arg) => {
      resetPaginationModel();
      return setFilter(arg.filter);
    },
    [resetPaginationModel, setFilter]
  );

  if (groups.error) {
    return <ErrorBanner>{groups.error.message}</ErrorBanner>;
  }

  return (
    <>
      <ConversationList
        sessionId={props.sessionId}
        isLoading={groups.isLoading || groups.isValidating}
        groups={groups.data}
        pagination={pagination}
        onPaginationChange={setPagination}
        timeModel={timeModel}
        onTimeModelChange={setTimeModel}
        filter={filter}
        onFilterChange={setFilter}
        stats={stats}
      />
      <ProjectSidebarSlot.Fill>
        <>
          {stats && (
            <ProjectStatsV2
              stats={stats}
              isLoadingStats={isStatsLoading}
              isTraces={true}
              source="conversation"
            />
          )}
          <div className="overflow-hidden border-t border-t-secondary empty:hidden">
            <ProjectSidebarV2
              stats={stats}
              searchModel={{ filter: filter }}
              setSearchModel={setSearchModel}
              allStats={allStats}
            />
          </div>
        </>
      </ProjectSidebarSlot.Fill>
    </>
  );
}

const StatsSidebarLayout = (props: {
  children: React.ReactNode;
  automate: boolean;
}) => {
  return (
    <PanelGroup
      direction="horizontal"
      autoSaveId={appProjectsPath + appSessionPath}
    >
      <Panel className="flex flex-col !overflow-y-auto overflow-x-hidden">
        {props.children}
      </Panel>
      {!props.automate && (
        <>
          <PanelResizeHandle className="group relative flex items-stretch text-secondary data-[resize-handle-active]:text-brand-green-400">
            <div className="border-l border-secondary" />

            <div className="absolute inset-y-0 -left-[4px] z-10 w-[9px] opacity-0" />
            <div className="absolute inset-y-0 w-[2px] bg-current opacity-0 transition-all group-hover:opacity-100" />
          </PanelResizeHandle>

          <Panel
            defaultSize={20}
            minSize={15}
            className={cn('!overflow-y-auto')}
          >
            <ProjectSidebarSlot.Slot className="h-full" />
          </Panel>
        </>
      )}
    </PanelGroup>
  );
};

export const ProjectPage = ({
  sessionId,
  session,
  onSuccess,
  isPublic = false,
}: {
  sessionId: string;
  session?: SessionSchema;
  isPublic?: boolean;
  onSuccess?: (id: string, data: SessionSchema) => void;
}) => {
  const [searchParams] = useSearchParams();
  const isTestRun = searchParams.get('test_run') === 'true';
  const [automate, setAutomate] = useState(false);
  const [alertsPanelOpen, setAlertsPanelOpen] = useState(false);
  const { authorize, isLoading: isPermsLoading } = usePermissions();
  const tabIndex = searchParams.get('tab') ?? '0';
  const showTTLUI = useInstanceFlag(InstanceFlags.show_ttl_ui);

  const [alertsBadgeVisible, setAlertsBadgeVisible] = useLocalStorageState(
    ALERTS_BADGE_LOCAL_STORAGE_KEY,
    true
  );

  if (isPermsLoading) {
    return <LinearProgress />;
  }
  if (!authorize('projects:read')) {
    return <ErrorBanner className="m-4">Forbidden</ErrorBanner>;
  }

  const mainComponent = (
    <div className="flex h-screen flex-1 flex-col">
      <div className="px-4 pt-3">
        <Breadcrumbs />
      </div>
      <div className="flex flex-grow flex-col overflow-hidden">
        <div className="px-4">
          <div className="flex items-center justify-end">
            <PageTitle
              icon={<ProjectIcon />}
              description={session?.description}
            >
              {session?.name}
            </PageTitle>
            {!isPublic && session ? (
              <div className="flex flex-wrap items-center justify-end gap-2 py-2">
                <CopyInlineLink value={session.id}>ID</CopyInlineLink>
                {authorize('projects:update') && (
                  <SessionCrudPaneWithButton
                    session={session}
                    onSuccess={onSuccess}
                  />
                )}

                {showTTLUI && <ProjectTTLSetting project={session} />}

                <ProjectMonitorSetting project={session} />

                {tabIndex === '0' && authorize('rules:create') && (
                  <ProjectRuleButton
                    sessionId={sessionId}
                    onClick={() => setAutomate(true)}
                  />
                )}
                <AlertsCreateButton
                  onClick={() => {
                    if (alertsBadgeVisible) {
                      setAlertsBadgeVisible(false);
                    }
                    setAlertsPanelOpen(true);
                  }}
                />
              </div>
            ) : null}
          </div>
        </div>
        <AlertsCreatePane
          isOpen={alertsPanelOpen}
          onClose={() => setAlertsPanelOpen(false)}
          sessionId={sessionId}
          projectName={session?.name}
        />

        <TabGroup
          setTabMethod="merge"
          onChange={(index) => {
            if (index !== 0) {
              setAutomate(false);
            }
          }}
        >
          <TabList className="mx-0 mb-0">
            <TabLabel>Runs</TabLabel>
            {!isTestRun && <TabLabel>Threads</TabLabel>}
            {!isTestRun && (
              <TabLabel
                onBlur={() => {
                  if (alertsBadgeVisible) {
                    setAlertsBadgeVisible(false);
                  }
                }}
              >
                <div className="flex items-center gap-1.5">
                  <p>Alerts</p>
                  {alertsBadgeVisible && (
                    <div className="rounded-md bg-brand-green-400 px-1 py-0.5 text-sm text-button-primary">
                      New
                    </div>
                  )}
                </div>
              </TabLabel>
            )}
            {!isTestRun && <TabLabel>Setup</TabLabel>}
          </TabList>
          <TabPanels className="flex flex-grow flex-col overflow-hidden overflow-y-auto">
            <TabPanel className="h-full">
              <StatsSidebarLayout automate={automate}>
                <ProjectRunsTable
                  isAutomate={automate}
                  onAutomateClose={() => setAutomate(false)}
                  sessionId={sessionId}
                  isTestRun={isTestRun}
                />
              </StatsSidebarLayout>
            </TabPanel>

            {!isTestRun && (
              <TabPanel className="h-full">
                <StatsSidebarLayout automate={automate}>
                  <ProjectConversations sessionId={sessionId} />
                </StatsSidebarLayout>
              </TabPanel>
            )}

            {!isTestRun && (
              <TabPanel>
                <ProjectAlerts
                  sessionId={sessionId}
                  projectName={session?.name}
                  onCreateAlert={() => setAlertsPanelOpen(true)}
                />
              </TabPanel>
            )}
            {!isTestRun && (
              <TabPanel className="p-4">
                <EnvQuickstart sessionName={session?.name} />
              </TabPanel>
            )}
          </TabPanels>
        </TabGroup>
      </div>
    </div>
  );

  return (
    <ProjectSidebarSlot.Context>
      <div className={automate ? 'flex' : ''}>
        {mainComponent}
        {automate && tabIndex === '0' && (
          <div className="h-screen w-[600px] overflow-y-auto border-l border-secondary">
            <ProjectSidebarSlot.Slot />
          </div>
        )}
      </div>
    </ProjectSidebarSlot.Context>
  );
};

const Project = () => {
  const { sessionId } = useParams();
  const sessionSwr = useSession(
    sessionId ?? null,
    { include_stats: true },
    {
      revalidateOnFocus: false,
    }
  );
  const onSuccess = (_id: string, session: SessionSchema) => {
    sessionSwr?.mutate(session);
  };

  return sessionId ? (
    <ProjectPage
      sessionId={sessionId}
      session={sessionSwr?.data}
      onSuccess={onSuccess}
    />
  ) : null;
};

export default Project;
