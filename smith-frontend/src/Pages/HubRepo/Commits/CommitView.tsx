import {
  formatTools,
  getModelFromManifest,
  getToolsFromModelManifest,
} from '@/Pages/Playground/utils/Playground.utils';
import { HubCode } from '@/components/Hub/HubCode';
import { CommitWithLookupsSchema, GetRepoResponse } from '@/types/schema';

import { ProxyProviderWarning } from '../ProxyProviderWarning';
import { useStoredDiffs } from '../hooks/useStoredDiffs';
import { CommitManifestView } from './CommitManifestView/CommitManifestView';
import { CommitModelConfig } from './CommitModelConfig';
import { CommitTools } from './CommitTools';
import { CommitViewHeader } from './CommitViewHeader';
import {
  COMMIT_PROMPT_MESSAGES_ID,
  COMMIT_PROMPT_MODEL_CONFIGURATION_ID,
  COMMIT_PROMPT_TOOLS_ID,
} from './constants';

export const CommitView = ({
  repoObj,
  repo,
  currentCommit,
  previousCommit,
  showDiff,
  setShowDiff,
  showProxyProviderWarning,
}: {
  repoObj: GetRepoResponse;
  repo: string;
  currentCommit: CommitWithLookupsSchema;
  previousCommit?: CommitWithLookupsSchema;
  showDiff: boolean;
  setShowDiff: (showDiff: boolean) => void;
  showProxyProviderWarning: boolean;
}) => {
  const tools = formatTools(
    getToolsFromModelManifest(currentCommit?.manifest?.kwargs.last)
  );
  const previousTools = formatTools(
    getToolsFromModelManifest(previousCommit?.manifest?.kwargs.last)
  );
  const modelManifest = getModelFromManifest(currentCommit?.manifest);
  const hasModelConfig =
    !!modelManifest || (showDiff && !!previousCommit && !!modelManifest);

  const { getMessageDiffs, getToolDiffs, getModelConfigDiffs } =
    useStoredDiffs();

  return (
    <div className="my-auto h-full grow pt-2">
      <div className="flex h-full flex-col overflow-hidden">
        <CommitViewHeader
          currentCommitHash={currentCommit.commit_hash}
          showDiff={showDiff}
          setShowDiff={setShowDiff}
          hasTools={
            showDiff
              ? tools.length > 0 || previousTools.length > 0
              : tools.length > 0
          }
          hasModelConfig={hasModelConfig}
          hasPreviousCommit={!!previousCommit}
        />
        <div className="mx-auto flex h-full w-full max-w-[1024px] flex-col space-y-4 overflow-y-auto py-4">
          {showProxyProviderWarning && <ProxyProviderWarning />}
          <div
            id={COMMIT_PROMPT_MESSAGES_ID}
            className="flex flex-col gap-2 px-4"
          >
            <CommitManifestView
              showDiff={showDiff}
              currentCommit={currentCommit}
              previousCommit={previousCommit}
              getMessageDiffs={getMessageDiffs}
            />
          </div>
          {(tools.length > 0 || previousTools.length > 0) && (
            <div id={COMMIT_PROMPT_TOOLS_ID} className="px-4">
              <CommitTools
                commitId={currentCommit.id}
                showDiff={showDiff}
                tools={tools}
                previousTools={previousTools}
                getToolDiffs={getToolDiffs}
              />
            </div>
          )}
          <div id={COMMIT_PROMPT_MODEL_CONFIGURATION_ID} className="px-4">
            <CommitModelConfig
              commitId={currentCommit.id}
              showDiff={showDiff}
              currentCommitManifest={currentCommit.manifest}
              previousCommitManifest={previousCommit?.manifest}
              getModelConfigDiffs={getModelConfigDiffs}
            />
          </div>
          {!showDiff && (
            <div className="flex flex-col gap-2 px-4">
              <HubCode
                owner={repoObj?.repo?.owner}
                repo={repo}
                commit={currentCommit.commit_hash}
                isPublic={repoObj?.repo?.is_public}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
