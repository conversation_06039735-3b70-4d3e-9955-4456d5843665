import { Tab, <PERSON>b<PERSON>roup, <PERSON>b<PERSON>ist, Tab<PERSON>anel, TabPanels } from '@headlessui/react';
import {
  CheckIcon,
  ChevronDownIcon,
  SearchSmIcon,
  XIcon,
} from '@langchain/untitled-ui-icons';
import {
  IconButton,
  Input,
  LinearProgress,
  Option,
  Select,
  Tooltip,
} from '@mui/joy';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Virtuoso } from 'react-virtuoso';
import { useDebounce } from 'use-debounce';

import { REVISION_STATUS_DEPLOY_AVAILABLE } from '@/Pages/HostProject/constants';
import Breadcrumbs from '@/components/Breadcrumbs';
import { useCopyAction } from '@/components/CopyButton/useCopy';
import { DateTimeRangePicker } from '@/components/DateTimeRangePicker/DateTimeRangePicker';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import {
  HostRevisionStatusName,
  getRevisionSimpleStatus,
} from '@/components/HostRevisionStatus/HostRevisionStatus';
import { PageTitle } from '@/components/PageTitle';
import { LoadMoreButton } from '@/components/RunsTable/LoadMoreButton';
import {
  DateTimeRangePickerValue,
  parseRelativeDate,
} from '@/components/RunsTable/utils/getDateTimeRangeLabel';
import { useHostRevision, useHostRevisionLogs } from '@/hooks/useSwr';
import CopyIcon from '@/icons/CopyIcon.svg?react';
import RocketShipIcon from '@/icons/RocketShip.svg?react';
import {
  HostRevisionLogsRequest,
  HostRevisionLogsResponse,
} from '@/types/schema';
import { stringifyError } from '@/utils/stringify-error';
import { cn } from '@/utils/tailwind';

import { useDeploymentFormType } from '../HostProjects/hooks/useDeploymentFormType';
import { LogLevel } from './types';

dayjs.extend(utc);

const getLogsFilter = (
  timeModel: DateTimeRangePickerValue
): HostRevisionLogsRequest => {
  let startDuration;
  let startTime = timeModel?.start_time;
  const endTime = timeModel?.end_time;

  if (timeModel?.duration) {
    startDuration = parseRelativeDate(timeModel.duration);

    if (startDuration) {
      startTime = dayjs
        .utc()
        .subtract(startDuration.value, startDuration.unit)
        .toISOString();
    }
  }
  return { limit: 200, start_time: startTime, end_time: endTime };
};

interface RevisionLogsPageData {
  data?: HostRevisionLogsResponse | undefined;
  isLoading?: boolean;
  isValidating?: boolean;
  error?: unknown | undefined;
  mutate?: (
    data?: HostRevisionLogsResponse,
    options?: { revalidate?: boolean }
  ) => void;
}

// This is yet another hack to workaround SWR cache and cache key limitations...
// What we want is to have a more fine-grained control over invalidation
// where we only want to revalidate the last page, as previous pages are stable.
// SWR's useSWRInfinite will revalidate all pages in sequence when requesting a next page,
// causing an unnecessary waterfall of network requests. However, we still need the data in
// one place for virtualisation, thus we fetch each page separately and propagate the data
// in a callback.
const RevisionDataLoaderPage = (props: {
  type: 'deploy_logs' | 'build_logs';

  shouldRevalidate: 'slow' | 'fast' | false;
  hostProjectId: string;
  revisionId: string;
  searchQuery: string;
  filter: Omit<HostRevisionLogsRequest, 'offset'> | undefined;
  offset: string | undefined;
  logLevel?: LogLevel;

  pageIndex: number;
  onDelta: (index: number, delta: RevisionLogsPageData) => void;
}) => {
  const { mutate, isLoading, data, error, isValidating } = useHostRevisionLogs(
    props.type,
    props.hostProjectId,
    props.revisionId,
    {
      ...props.filter,
      offset: props.offset,
      query: props.searchQuery,
      level: props.logLevel,
    },
    {
      ...(props.shouldRevalidate
        ? {
            refreshInterval: props.shouldRevalidate === 'slow' ? 30_000 : 3_000,
          }
        : {
            revalidateOnFocus: false,
            revalidateIfStale: false,
            revalidateOnReconnect: false,
          }),
    }
  );

  const delta = useRef(props.onDelta);
  useEffect(() => void (delta.current = props.onDelta), [props.onDelta]);

  // Manually unload the data from cache on unmount
  // as the data lingers in the SWR cache forever
  // TODO: validate and remove after LRU cache
  useEffect(() => {
    return () => void mutate(undefined, { revalidate: false });
  }, [mutate]);

  // the swr object itself is not referentially stable, but each of the
  // tracked values should be stable.
  useEffect(() => {
    delta.current?.(props.pageIndex, { isLoading });
  }, [props.pageIndex, isLoading]);

  useEffect(() => {
    delta.current?.(props.pageIndex, { data });
  }, [props.pageIndex, data]);

  useEffect(() => {
    delta.current?.(props.pageIndex, { error });
  }, [props.pageIndex, error]);

  useEffect(() => {
    delta.current?.(props.pageIndex, { mutate });
  }, [props.pageIndex, mutate]);

  useEffect(() => {
    delta.current?.(props.pageIndex, { isValidating });
  }, [props.pageIndex, isValidating]);

  return null;
};

function findReverse<T>(arr: T[], predicate: (item: T) => boolean) {
  for (let i = arr.length - 1; i >= 0; i--) {
    if (predicate(arr[i])) return arr[i];
  }
  return undefined;
}

const LogLevelSelector = ({
  logLevel,
  setLogLevel,
}: {
  logLevel: LogLevel;
  setLogLevel: (level: LogLevel) => void;
}) => {
  const renderLogLevelButton = (level: LogLevel) => {
    return (
      <Option value={level} label={level} color="neutral">
        <div className="flex w-full items-center gap-2 capitalize">
          {level.toLowerCase()}
          {level === logLevel && <CheckIcon className="ml-auto size-4" />}
        </div>
      </Option>
    );
  };

  return (
    <Select
      value={logLevel}
      onChange={(_, value) => setLogLevel(value as LogLevel)}
      indicator={<ChevronDownIcon className="size-4" />}
      size="sm"
      renderValue={(value) => (
        <span className="text-sm capitalize">
          Log level{value?.value ? `: ${value?.value.toLowerCase()}` : ''}
        </span>
      )}
      color="neutral"
    >
      {Object.values(LogLevel).map(renderLogLevelButton)}
    </Select>
  );
};

function HostRevisionPaginatedLogs(props: {
  type: 'deploy_logs' | 'build_logs';
  shouldRevalidate: 'slow' | 'fast';
  hostProjectId: string;
  revisionId: string;
  isPending: boolean;
}) {
  const [timeModel, setTimeModel] = useState<DateTimeRangePickerValue>(() =>
    props.type === 'deploy_logs'
      ? { start_time: dayjs.utc().subtract(15, 'minutes').toISOString() }
      : undefined
  );

  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery] = useDebounce(searchQuery, 500);
  const [logLevel, setLogLevel] = useState<LogLevel>(LogLevel.INFO);
  const [filter, setFilter] = useState<HostRevisionLogsRequest>(() =>
    getLogsFilter(timeModel)
  );

  const [pageCount, setPageCount] = useState(1);
  const [pages, setPages] = useState<Array<RevisionLogsPageData | undefined>>(
    []
  );

  const data = pages.flatMap((page) => page?.data?.logs ?? []);
  const activePageCount = pages.filter((page) => page?.data != null).length;
  const lastPageWithData = findReverse(pages, (page) => page?.data != null);
  const isLoading =
    pages.some((page) => page?.isLoading ?? false) ||
    (lastPageWithData?.isValidating ?? false);

  const hasNextPage =
    activePageCount > 0 && !!lastPageWithData?.data?.next_offset;

  const lastError = findReverse(pages, (page) => page?.error != null)?.error;

  const onDelta = useCallback((index, delta) => {
    setPages((prev) => {
      const next: typeof prev = Array.from({
        length: Math.max(prev.length, index + 1),
      });

      for (let i = 0; i < prev.length; i++) next[i] = prev[i];
      next[index] = Object.assign({}, next[index], delta);
      return next;
    });
  }, []);

  useEffect(() => {
    if (
      lastError &&
      stringifyError(lastError).includes('Invalid or expired offset')
    ) {
      // Re-create the model to nudge SWR to re-fetch data properly
      // Failing to do so will result in empty log view
      const model = { ...timeModel };
      setTimeModel(model);
      setFilter(getLogsFilter(model));
      setPageCount(1);
      setPages([]);
    }
  }, [lastError, pages, timeModel]);

  const { onCopy, copied } = useCopyAction();

  const renderLogs = () => {
    if (!data.length)
      return (
        <div className="flex h-full flex-col items-center gap-2">
          <div className="text-center text-sm text-tertiary">
            No logs found for selected filters.
          </div>
        </div>
      );
    return (
      <Virtuoso
        data={data}
        components={{
          Footer: () => {
            if (!hasNextPage) return null;
            return (
              <LoadMoreButton
                isInitialLoad
                isLoading={isLoading}
                onClick={() => setPageCount(activePageCount + 1)}
              />
            );
          },
        }}
        itemContent={(_, item) => (
          <div key={item.id} className="grid grid-cols-[auto,1fr] gap-4 py-0.5">
            <div className="whitespace-nowrap font-mono text-sm text-tertiary">
              {new Date(item.timestamp).toLocaleString()}
            </div>
            <div
              className={cn(
                'relative whitespace-pre font-mono text-sm',
                (item.level === 'ERROR' || item.level === 'CRITICAL') &&
                  'text-error',
                item.level === 'WARNING' && 'text-warning'
              )}
            >
              <span>[{item.level}]</span>{' '}
              <span className="whitespace-pre-wrap">{item.message}</span>
            </div>
          </div>
        )}
      />
    );
  };

  return (
    <div className="flex h-full flex-grow flex-col gap-3">
      <div className="flex items-center justify-between gap-3">
        {props.type === 'deploy_logs' && (
          <div className="flex items-center gap-2">
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search"
              size="sm"
              startDecorator={
                <SearchSmIcon className="size-4 text-secondary" />
              }
              endDecorator={
                searchQuery && (
                  <IconButton
                    variant="plain"
                    color="neutral"
                    onClick={() => setSearchQuery('')}
                  >
                    <XIcon className="h-4 w-4 text-secondary" />
                  </IconButton>
                )
              }
              sx={{
                width: '200px',
              }}
            />
            <LogLevelSelector logLevel={logLevel} setLogLevel={setLogLevel} />

            <DateTimeRangePicker
              value={timeModel}
              onChange={(model) => {
                setTimeModel(model);
                setFilter(getLogsFilter(model));
                setPageCount(1);
                setPages([]);
              }}
            />
          </div>
        )}
        <div className="ml-auto flex items-center gap-2">
          <button
            type="button"
            className="flex flex-shrink-0 items-center gap-1.5 rounded-md border border-secondary px-2 py-1 text-sm font-medium transition-all hover:bg-secondary-hover active:bg-secondary disabled:pointer-events-none data-[state=open]:bg-secondary"
            disabled={!lastPageWithData || isLoading}
            onClick={() => {
              lastPageWithData?.mutate?.(undefined, { revalidate: true });
            }}
          >
            Refresh
          </button>
          <button
            type="button"
            className="flex flex-shrink-0 items-center gap-1.5 rounded-md border border-secondary px-2 py-1 text-sm font-medium transition-all hover:bg-secondary-hover active:bg-secondary disabled:pointer-events-none data-[state=open]:bg-secondary"
            disabled={isLoading}
            onClick={() =>
              onCopy(
                data
                  .map((log) =>
                    [
                      new Date(log.timestamp).toLocaleString(),
                      log.message,
                    ].join(' ')
                  )
                  .join('\n')
              )
            }
          >
            {copied ? (
              <>
                <CheckIcon className="size-4" />
                Copied
              </>
            ) : (
              <>
                <CopyIcon className="size-4" />
                Copy
              </>
            )}
          </button>
        </div>
      </div>

      {lastError != null && <ExpandableErrorAlert error={lastError} />}

      {Array.from({ length: pageCount }).map((_, index, lst) => {
        const offset =
          index > 0
            ? pages.at(index - 1)?.data?.next_offset ?? undefined
            : undefined;
        const isLastPage = index + 1 === lst.length;

        if (index > 0 && !offset) return null;
        return (
          <RevisionDataLoaderPage
            key={index}
            type={props.type}
            pageIndex={index}
            shouldRevalidate={isLastPage ? props.shouldRevalidate : false}
            hostProjectId={props.hostProjectId}
            revisionId={props.revisionId}
            filter={filter}
            offset={offset}
            onDelta={onDelta}
            searchQuery={debouncedSearchQuery}
            logLevel={logLevel}
          />
        );
      })}

      <div className="relative grid h-full flex-grow items-stretch justify-stretch rounded-lg border border-secondary bg-secondary p-3">
        {isLoading && (
          <div className="absolute inset-x-0 top-0">
            <LinearProgress />
          </div>
        )}
        {renderLogs()}
      </div>
    </div>
  );
}

export function HostRevision(props: {
  revisionId: string;
  hostProjectId: string;
}) {
  const { data: revision } = useHostRevision(
    { hostProjectId: props.hostProjectId, revisionId: props.revisionId },
    {
      refreshInterval: (data) =>
        getRevisionSimpleStatus(data?.status ?? '') === 'pending' ? 3000 : 0,
    }
  );
  const deploymentFormType = useDeploymentFormType();
  const revisionStatus = getRevisionSimpleStatus(revision?.status ?? 'UNKNOWN');
  const isDeployAvailable = REVISION_STATUS_DEPLOY_AVAILABLE.includes(
    revision?.status ?? ''
  );
  const isBuildAvailable = deploymentFormType === 'cloud';

  return (
    <div className="flex h-full flex-col gap-4">
      <PageTitle icon={<RocketShipIcon />} className="shrink-0 flex-grow-0">
        {revision?.id}
      </PageTitle>

      <div className="flex flex-wrap items-start gap-6">
        <div className="flex flex-col items-start gap-1.5">
          <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-slate-500">
            Status
          </div>

          <Tooltip
            title={revision?.status_message?.trim()}
            sx={{ maxWidth: 400, whiteSpace: 'pre-wrap' }}
          >
            <div className="font-medium">
              <HostRevisionStatusName status={revision?.status} />
            </div>
          </Tooltip>
        </div>
        {revision?.updated_at != null && (
          <div className="flex flex-col items-start gap-1.5">
            <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-slate-500">
              Last Updated
            </div>

            <div className="font-medium">
              {new Date(revision.updated_at).toLocaleString()}
            </div>
          </div>
        )}
      </div>

      <hr className="border-tertiary" />

      {(revisionStatus === 'error' || revisionStatus === 'unknown') &&
      revision?.status_message ? (
        <ExpandableErrorAlert error={revision?.status_message?.trim()} />
      ) : null}

      {revision && (
        <TabGroup
          defaultIndex={isDeployAvailable && isBuildAvailable ? 2 : 1}
          className="grid flex-grow grid-cols-[auto,1fr] gap-6"
        >
          <TabList className="flex flex-col gap-1">
            {isBuildAvailable && (
              <Tab className="rounded-lg py-2 pl-3.5 pr-12 text-left font-semibold outline-none transition-all hover:bg-secondary disabled:pointer-events-none disabled:opacity-50 ui-selected:bg-secondary-hover ui-selected:hover:bg-secondary ui-not-selected:text-tertiary">
                Build
              </Tab>
            )}
            <Tab
              className="rounded-lg py-2 pl-3.5 pr-12 text-left font-semibold outline-none transition-all hover:bg-secondary disabled:pointer-events-none disabled:opacity-50 ui-selected:bg-secondary-hover ui-selected:hover:bg-secondary ui-not-selected:text-tertiary"
              disabled={!isDeployAvailable}
            >
              Server
            </Tab>
          </TabList>
          <TabPanels className="min-w-0">
            {isBuildAvailable && (
              <TabPanel className="flex h-full flex-col gap-4">
                <h2 className="text-xl font-semibold">Build Logs</h2>

                <HostRevisionPaginatedLogs
                  type="build_logs"
                  shouldRevalidate={
                    revision.status === 'BUILDING' ||
                    revision.status === 'AWAITING_BUILD'
                      ? 'fast'
                      : 'slow'
                  }
                  hostProjectId={props.hostProjectId}
                  revisionId={revision.id}
                  isPending={
                    revision.status === 'BUILDING' ||
                    revision.status === 'AWAITING_BUILD'
                  }
                />
              </TabPanel>
            )}
            <TabPanel className="flex h-full flex-col gap-4">
              <h2 className="text-xl font-semibold">Server Logs</h2>

              <HostRevisionPaginatedLogs
                type="deploy_logs"
                shouldRevalidate="slow"
                hostProjectId={props.hostProjectId}
                revisionId={revision.id}
                isPending={
                  revision.status === 'DEPLOYING' ||
                  revision.status === 'AWAITING_DEPLOY'
                }
              />
            </TabPanel>
          </TabPanels>
        </TabGroup>
      )}
    </div>
  );
}

export default function Page() {
  const { hostProjectId, revisionId } = useParams();

  const revision = useHostRevision(
    { hostProjectId, revisionId },
    {
      refreshInterval: (data) =>
        getRevisionSimpleStatus(data?.status ?? '') === 'pending' ? 3000 : 0,
    }
  );

  if (!hostProjectId || !revisionId) return null;
  return (
    <div className="flex-grow px-4 pb-6 pt-3">
      <Breadcrumbs />

      {revision.isLoading ? (
        <LinearProgress />
      ) : revision.error ? (
        <ExpandableErrorAlert error={revision.error} />
      ) : (
        <HostRevision revisionId={revisionId} hostProjectId={hostProjectId} />
      )}
    </div>
  );
}
