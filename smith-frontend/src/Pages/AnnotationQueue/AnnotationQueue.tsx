import {
  ChevronDownIcon,
  EllipsisHorizontalIcon,
} from '@heroicons/react/24/outline';
import { ListIcon } from '@langchain/untitled-ui-icons';
import { Check } from '@mui/icons-material';
import ChevronLeftRoundedIcon from '@mui/icons-material/ChevronLeftRounded';
import {
  Alert,
  Box,
  Checkbox,
  IconButton,
  LinearProgress,
  Modal,
  ModalClose,
  ModalDialog,
  Tooltip,
} from '@mui/joy';
import Button from '@mui/joy/Button';

import { Trash2 } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { Link, useNavigate } from 'react-router-dom';
import { mutate } from 'swr';

import { emulateNativeClick } from '@/components/DataGrid.utils';
import { DatasetCrudPane } from '@/components/DatasetCrudPane/DatasetCrudPane';
import { DeleteModal } from '@/components/Delete';
import { useExampleCrud } from '@/components/ExampleCrudPane/hooks';
import { getDataType } from '@/components/ExampleCrudPane/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { DatasetSelectorPopoverContent } from '@/components/RunsTable/components/DatasetSelectorPopoverContent';
import useToast from '@/components/Toast';
import { useColorScheme } from '@/hooks/useColorScheme';
import { usePermissions } from '@/hooks/usePermissions';
import {
  matchKeyPartial,
  useOrganizationId,
  useSelectedTenant,
  useSession,
  useUpdateAnnotationQueueMutation,
} from '@/hooks/useSwr';
import {
  AnnotationQueueRunSchema,
  AnnotationQueueSchema,
  DatasetSchema,
  ExampleSchema,
  RunSchema,
} from '@/types/schema';
import { evaluateIfRunCanBeAddedToDataset } from '@/utils/compare-schemas';
import {
  apiAnnotationQueuesPath,
  appDatasetsPath,
  appOrganizationPath,
  appSessionPath,
} from '@/utils/constants';
import { cn } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import CommandKeyIcon from '../../icons/CommandKeyIcon.svg?react';
import CornerDownLeftIcon from '../../icons/CornerDownLeftIcon.svg?react';
import LeftArrow from '../../icons/LeftArrow.svg?react';
import RightArrow from '../../icons/RightArrow.svg?react';
import { useScrollParent } from '../../utils/VirtuosoCustomScrollParentContext/useScrollParent';
import { KeyboardShortcut } from '../KeyboardShortcutsSidePane';
import Attachments from '../Run/components/Attachments';
import { RunInputsAndOutputs } from '../Run/components/RunInputsAndOutputs';
import { AnnotationQueueRubricForm } from './components/AnnotationQueueRubricForm';
import { ATTACHMENT_PREFIX } from './constants';

interface AnnotationQueueProps {
  currentRun?: AnnotationQueueRunSchema;
  referenceExample?: ExampleSchema;
  annotationQueue: AnnotationQueueSchema;
  leftDisabled: boolean;
  rightDisabled: boolean;
  moveToEndDisabled: boolean;
  isDoneLoading: boolean;
  isMoveLoading: boolean;
  index?: number;
  totalRuns?: number;
  handleMarkDone: () => void;
  handleMoveToEnd: () => void;
  handleLeftClick: () => void;
  handleRightClick: () => void;
  onFeedbackGiven: () => void;
  setPeekedRun: (id: string | null) => void;
  dataset: DatasetSchema | null;
  setDatasetID: (id: string) => void;
  isDefaultDataset: boolean;
  loading: boolean;
  mutateSize?: () => void;
}

export const AnnotationQueue = ({
  currentRun,
  referenceExample,
  annotationQueue,
  handleMarkDone: handleMarkDoneParent,
  handleMoveToEnd: handleMoveToEndParent,
  leftDisabled,
  rightDisabled,
  handleLeftClick: handleLeftClickParent,
  handleRightClick: handleRightClickParent,
  moveToEndDisabled,
  isDoneLoading,
  isMoveLoading,
  onFeedbackGiven,
  index,
  totalRuns,
  setPeekedRun,
  dataset,
  setDatasetID,
  isDefaultDataset,
  loading,
  mutateSize,
}: AnnotationQueueProps) => {
  const { authorize } = usePermissions();
  const [isNewDatasetPaneOpen, setIsNewDatasetPaneOpen] = useState(false);
  const [isDatasetPickerOpen, setIsDatasetPickerOpen] = useState(false);
  const [pageHeight, setPageHeight] = useState('95vh');
  const [isSetDefaultDatasetPopoverOpen, setIsSetDefaultDatasetPopoverOpen] =
    useState(false);

  const [isDoneButtonHovered, setIsDoneButtonHovered] = useState(false);
  const handleMouseEnter = () => {
    setIsDoneButtonHovered(true);
  };
  const handleMouseLeave = () => {
    setIsDoneButtonHovered(false);
  };

  const datasetDataType = getDataType(undefined, currentRun);

  const { createToast } = useToast();
  const organizationId = useOrganizationId();
  const navigate = useNavigate();
  const { isDarkMode } = useColorScheme();

  const [inputsObj, setInputsObj] = useState<RunSchema['inputs'] | undefined>(
    currentRun?.inputs
  );
  const [outputsObj, setOutputsObj] = useState<
    RunSchema['outputs'] | undefined
  >(currentRun?.outputs);

  const [s3UrlsObj, setS3UrlsObj] = useState<RunSchema['s3_urls'] | undefined>(
    currentRun?.s3_urls
  );
  const attachments = useMemo(() => {
    if (!currentRun?.s3_urls || !s3UrlsObj) return [];
    return Object.entries(s3UrlsObj)
      .filter(([key]) => key.startsWith(ATTACHMENT_PREFIX))
      .map(([name, urls]) => ({
        name: name.slice(ATTACHMENT_PREFIX.length),
        url: urls.presigned_url,
      }));
  }, [currentRun?.s3_urls, s3UrlsObj]);

  const {
    isConfirmationModalOpen,
    setIsConfirmationModalOpen,
    handleMarkDone,
    handleMoveToEnd,
    handleLeftClick,
    handleRightClick,
    setDontShowConfirmationAgain,
    setHasEdited,
  } = useConfirmationModal(
    handleMarkDoneParent,
    handleMoveToEndParent,
    handleLeftClickParent,
    handleRightClickParent
  );

  const [rawIO, setRawIO] = useLocalStorageState(
    'ls:raw_io_annotation_queue',
    false
  );

  const [isDeleteRunModalOpen, setIsDeleteRunModalOpen] = useState(false);

  const { data: selectedTenant } = useSelectedTenant();
  const isPersonal = selectedTenant?.is_personal;

  const currentSession = useSession(currentRun?.session_id ?? null);
  const { loading: addToDatasetLoading, onSubmit } = useExampleCrud({
    datasetDataType,
    onSuccess: () => {
      createToast({
        title: 'Example added to dataset',
        description: (
          <Link
            to={`/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${dataset?.id}`}
            className="underline"
          >
            View the dataset
          </Link>
        ),
      });
      setHasEdited(false);
    },
    onError: (e) => {
      createToast({
        title: 'Error adding example to dataset',
        description: e,
        error: true,
      });
    },
  });

  const setContainerRef = (node: HTMLDivElement | null) => {
    if (node !== null) {
      setPageHeight(`calc(100vh - ${node.offsetTop}px)`);
    }
  };

  const handleAddToDataset = useCallback(() => {
    if (currentRun?.id && dataset?.id) {
      onSubmit({
        inputs: JSON.stringify(inputsObj),
        outputs: JSON.stringify(outputsObj),
        use_source_run_attachments: attachments.map(
          (attachment) => attachment.name
        ),
        source_run_id: currentRun.id,
        dataset_id: dataset?.id,
      });
    }
  }, [
    currentRun?.id,
    dataset?.id,
    inputsObj,
    onSubmit,
    outputsObj,
    attachments,
  ]);

  const currentRunWithUpdatedIO = useMemo(() => {
    try {
      return currentRun
        ? {
            id: currentRun?.id,
            inputs: inputsObj,
            outputs: outputsObj,
            extra: currentRun?.extra,
            run_type: currentRun?.run_type,
          }
        : null;
    } catch (e) {
      return null;
    }
  }, [currentRun, inputsObj, outputsObj]);

  const { trigger: updateTrigger } = useUpdateAnnotationQueueMutation({
    queue_id: annotationQueue.id,
  });

  const handleSetDefault = useCallback(
    (e) => {
      const defaultDatasetUpdatedDescriptionPersonalTenant = (
        <div>
          You've set <span className="font-bold">{dataset?.name}</span> as the
          new default dataset for this queue.
        </div>
      );

      const defaultDatasetUpdatedDescription = (
        <div>
          <span className="font-bold">{dataset?.name}</span> is the new default
          dataset for this queue. This updates for your team too. Happy
          collaborating!
        </div>
      );

      e.stopPropagation();
      if (dataset?.id) {
        setIsSetDefaultDatasetPopoverOpen(false);
        updateTrigger(
          {
            json: {
              default_dataset: dataset.id,
            },
          },
          {
            onSuccess: () => {
              createToast({
                title: 'Default dataset updated',
                description: isPersonal
                  ? defaultDatasetUpdatedDescriptionPersonalTenant
                  : defaultDatasetUpdatedDescription,
              });
              mutate(matchKeyPartial({ url: apiAnnotationQueuesPath }));
            },
            onError: (e) => {
              createToast({
                title: 'Error updating default dataset',
                description: e.message,
                error: true,
              });
            },
          }
        );
      }
    },
    [createToast, dataset?.id, dataset?.name, isPersonal, updateTrigger]
  );

  const handleNewDatasetCreated = useCallback(
    (id: string, newDataset: DatasetSchema) => {
      setHasEdited(false);
      setDatasetID(newDataset.id);
      const toastDescription = (
        <div>
          Successfully created new dataset{' '}
          <Link
            className="font-bold text-brand-green-400 underline"
            to={`/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${id}`}
          >
            {newDataset?.name}
          </Link>{' '}
          and added the current run.
        </div>
      );
      createToast({
        title: 'New Dataset Created',
        description: toastDescription,
      });
    },
    [createToast, organizationId, setDatasetID]
  );

  const onClickViewRun = useCallback(() => {
    setPeekedRun(currentRun?.id ?? null);
  }, [setPeekedRun, currentRun?.id]);

  const onClickDeleteRun = useCallback(() => {
    setIsDeleteRunModalOpen(true);
  }, [setIsDeleteRunModalOpen]);

  const onClickViewProject = useCallback(() => {
    navigate(`${appSessionPath}/${currentSession.data?.id}`);
  }, [navigate, currentSession.data?.id]);

  // ------------------------ HOTKEYS ---------------------------- //

  useHotkeys(
    'esc',
    (event) => {
      event.preventDefault();
      if (document.activeElement) {
        (document.activeElement as HTMLElement).blur();
      }
      if (isDatasetPickerOpen) {
        setIsDatasetPickerOpen(false);
      }
    },
    { enableOnContentEditable: true }
  );
  // keyboard shortcut for cmd(mac)/ctrl(win) + enter to click "done"
  useHotkeys('meta+return, ctrl+return', handleMarkDone, {
    enabled: !isNewDatasetPaneOpen,
    enableOnContentEditable: true,
  });
  // keyboard shortcut "v" opens up view run pane
  useHotkeys('v', onClickViewRun, { enabled: !isNewDatasetPaneOpen });
  // keyboard shortcut "p" clicks view project
  useHotkeys('p', onClickViewProject, { enabled: !isNewDatasetPaneOpen });
  // keyboard shortcut "d" adds to dataset if dataset is selected
  useHotkeys(
    'd',
    (event: KeyboardEvent) => {
      event.preventDefault();
      handleAddToDataset();
    },
    { enabled: !isNewDatasetPaneOpen && !addToDatasetLoading }
  );
  // j and k for next and previous
  useHotkeys('l', handleRightClick, { enabled: !isNewDatasetPaneOpen });
  useHotkeys('h', handleLeftClick, { enabled: !isNewDatasetPaneOpen });
  // move to end
  useHotkeys('e', handleMoveToEnd, {
    enabled: !isNewDatasetPaneOpen && !moveToEndDisabled && !isMoveLoading,
  });
  useHotkeys('r', () => setRawIO((prev) => !prev), {
    enabled: !isNewDatasetPaneOpen && !loading,
  });

  useEffect(() => {
    if (!loading && !currentRun) {
      mutateSize?.();
    }
  }, [currentRun, loading, mutateSize]);

  const ruleId = annotationQueue?.source_rule_id;

  // ------------------------ END HOTKEYS ---------------------------- //

  const scrollParentRef = useScrollParent();
  return (
    <div
      className="relative bottom-0 -mb-6 -mt-3 flex w-full items-stretch"
      style={{ height: pageHeight }}
      ref={setContainerRef}
    >
      <ConfirmationModal
        isConfirmationModalOpen={isConfirmationModalOpen}
        setIsConfirmationModalOpen={setIsConfirmationModalOpen}
        setDontShowConfirmationAgain={setDontShowConfirmationAgain}
      />
      <div className="relative flex grow flex-col items-stretch self-stretch pb-[90px] ">
        <AnnotationQueueHeader
          annotationQueueName={annotationQueue.name}
          annotationQueueId={annotationQueue.id}
          organizationId={organizationId}
          index={index}
          totalRuns={totalRuns}
          populatingInProgress={!!ruleId}
        />
        <div
          className="flex grow flex-col items-center self-stretch overflow-y-auto overflow-x-visible"
          ref={scrollParentRef}
        >
          <div className="z-0 mb-4 mr-4 flex w-[calc(100%-16px)] max-w-[900px] flex-col items-stretch rounded-xl px-2 py-6">
            {loading ? (
              <LinearProgress />
            ) : currentRun ? (
              <>
                <div className="flex flex-row items-center justify-between">
                  <div className="shrink font-medium display-base">
                    {currentRun.name}
                  </div>
                  <div className="flex items-center gap-3">
                    {currentRun != null && (
                      <>
                        <button
                          type="button"
                          className="inline-flex h-8 shrink-0 items-center justify-start rounded border border-secondary px-2 text-sm hover:bg-secondary"
                          onClick={() => setRawIO((prev) => !prev)}
                        >
                          <span className="my-auto">
                            {rawIO ? 'View Rendered' : 'View Raw'}
                          </span>
                          <KeyboardShortcut keys={['R']} />
                        </button>
                        {authorize('annotation-queues:delete') && (
                          <button
                            type="button"
                            className="inline-flex h-8 items-center justify-start rounded border border-secondary px-2 text-sm hover:bg-secondary"
                            onClick={onClickDeleteRun}
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          type="button"
                          className="inline-flex h-8 shrink-0 items-center justify-start rounded border border-secondary px-2 text-sm hover:bg-secondary"
                          onClick={onClickViewRun}
                        >
                          <span className="my-auto">View Run</span>
                          <KeyboardShortcut keys={['V']} />
                        </button>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-4">
                  <RunInputsAndOutputs
                    onEditInputs={(inputs: RunSchema['inputs']) => {
                      setInputsObj(inputs);
                      setHasEdited(true);
                    }}
                    onEditOutputs={(outputs: RunSchema['outputs']) => {
                      setOutputsObj(outputs);
                      setHasEdited(true);
                    }}
                    run={{
                      ...currentRun,
                      inputs: inputsObj,
                      outputs: outputsObj,
                    }}
                    example={referenceExample}
                    isRunLoading={loading}
                    forceRawRender={rawIO ? 'raw' : 'rendered'}
                    showExpandAll
                  />
                  <Attachments
                    attachments={attachments}
                    onDelete={(attachment) => {
                      setS3UrlsObj((s3Urls) => {
                        const newS3Urls = { ...s3Urls };
                        delete newS3Urls[ATTACHMENT_PREFIX + attachment.name];
                        return newS3Urls;
                      });
                    }}
                    className="px-4"
                    gridClassName="max-h-[250px] w-full"
                  />
                </div>
              </>
            ) : (
              <Box className="m-4">
                <Alert variant="outlined" color="danger">
                  404: This run does not exist
                </Alert>
              </Box>
            )}
          </div>
        </div>

        <div className="absolute bottom-0 left-0 right-0 -ml-4 flex items-center justify-between overflow-visible overflow-x-auto border-t border-secondary bg-[var(--joy-palette-background-body)] p-5 shadow-[0_-10px_15px_-3px_rgba(0,_0,_0,_0.07),_0_-4px_6px_-2px_rgba(0,_0,_0,_0.02)]">
          {currentRun ? (
            <div className="flex gap-3 self-stretch justify-self-center px-2">
              <div className="relative">
                <Popover
                  open={isDatasetPickerOpen}
                  onOpenChange={setIsDatasetPickerOpen}
                >
                  <PopoverTrigger className="self-stretch" asChild>
                    <Button
                      variant="outlined"
                      color="neutral"
                      endDecorator={<ChevronDownIcon className="h-5 w-5" />}
                      sx={{
                        height: '100%',
                        ':hover': {
                          backgroundColor: isDarkMode ? '#1b2428' : '#eceff1',
                        },
                        ':active': {
                          backgroundColor: isDarkMode ? '#1b2428' : '#eceff1',
                        },
                      }}
                    >
                      <div className="self-shrink mr-8 line-clamp-1 flex max-w-[125px] items-center overflow-hidden whitespace-nowrap">
                        {dataset ? dataset.name : 'Select Dataset'}
                      </div>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    align="start"
                    className="w-[508px] p-0"
                    avoidCollisions={false}
                    side={'top'}
                  >
                    <DatasetSelectorPopoverContent
                      runTypeAndInputs={{
                        run_type: currentRun.run_type,
                        inputs: currentRun.inputs,
                      }}
                      handleSelectDataset={(dataset) => {
                        setDatasetID(dataset.id);
                        setIsDatasetPickerOpen(false);
                      }}
                      openNewDatasetPane={() => setIsNewDatasetPaneOpen(true)}
                    />
                  </PopoverContent>
                </Popover>
                {dataset && (
                  <Popover
                    open={isSetDefaultDatasetPopoverOpen}
                    onOpenChange={setIsSetDefaultDatasetPopoverOpen}
                  >
                    <PopoverTrigger
                      asChild
                      style={{
                        position: 'absolute',
                        right: '40px',
                        top: '0px',
                        bottom: '0px',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <div>
                        <IconButton
                          variant="plain"
                          color="neutral"
                          size="sm"
                          sx={{
                            ':hover': {
                              backgroundColor: isDarkMode
                                ? '#263238'
                                : '#cfd8dc',
                            },
                            ':active': {
                              backgroundColor: isDarkMode
                                ? '#37474f'
                                : '#b0bec5',
                            },
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsSetDefaultDatasetPopoverOpen((prev) => !prev);
                          }}
                        >
                          <EllipsisHorizontalIcon className="h-5 w-5" />
                        </IconButton>
                      </div>
                    </PopoverTrigger>
                    <PopoverContent
                      align="start"
                      className="w-[220px] rounded-lg p-0"
                    >
                      <Tooltip
                        title={
                          isDefaultDataset && 'This is your default dataset'
                        }
                      >
                        <div className="flex flex-col">
                          <Button
                            variant="plain"
                            color="neutral"
                            onClick={handleSetDefault}
                            startDecorator={
                              isDefaultDataset && (
                                <Check className="stroke-current" />
                              )
                            }
                            disabled={isDefaultDataset}
                          >
                            Set as Default Dataset
                          </Button>
                        </div>
                      </Tooltip>
                    </PopoverContent>
                  </Popover>
                )}
              </div>
              <Tooltip
                title={
                  !dataset
                    ? 'Select a dataset first'
                    : !currentRunWithUpdatedIO
                    ? 'Invalid JSON'
                    : null
                }
              >
                <span className="flex flex-col">
                  <Button
                    variant="outlined"
                    color={'neutral'}
                    onClick={handleAddToDataset}
                    sx={{ whiteSpace: 'nowrap', height: '100%' }}
                    disabled={
                      addToDatasetLoading ||
                      !dataset ||
                      !currentRunWithUpdatedIO
                    }
                    loading={addToDatasetLoading}
                  >
                    Add to Dataset{' '}
                    <KeyboardShortcut
                      keys={['D']}
                      className={cn(
                        (addToDatasetLoading || !dataset) && 'opacity-10'
                      )}
                    />
                  </Button>
                </span>
              </Tooltip>
              <Button
                disabled={moveToEndDisabled || isMoveLoading}
                variant="outlined"
                color="neutral"
                onClick={handleMoveToEnd}
                loading={isMoveLoading}
                sx={{ whiteSpace: 'nowrap' }}
              >
                Requeue at end{' '}
                <KeyboardShortcut
                  keys={['E']}
                  className={cn(
                    (moveToEndDisabled || isMoveLoading) && 'invisible'
                  )}
                />
              </Button>
              <Button
                variant="solid"
                color="primary"
                onClick={handleMarkDone}
                disabled={isDoneLoading}
                loading={isDoneLoading}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                Done{' '}
                <KeyboardShortcut
                  keys={[
                    <CommandKeyIcon className="h-3 w-3" />,
                    <CornerDownLeftIcon className="h-3 w-3" />,
                  ]}
                  className={cn(
                    'gap-1 border-brand-subtle bg-brand-secondary py-1 text-brand-primary',
                    isDoneButtonHovered && 'opacity-50',
                    isDoneLoading && 'invisible'
                  )}
                  soloClassName="h-3 w-3"
                />
              </Button>
            </div>
          ) : (
            <div className="flex gap-3 self-stretch justify-self-center px-2" />
          )}
          <div className="col-start-3 flex min-w-[233px] shrink-0 gap-3 justify-self-end whitespace-nowrap">
            {index != undefined && totalRuns != undefined && (
              <div className="inline-flex items-center justify-start gap-[5px] rounded px-2 py-1">
                <div className="text-center text-sm font-normal leading-[15px]">
                  {`${index + 1} of ${totalRuns}`}
                </div>
              </div>
            )}
            <IconButton
              variant="outlined"
              size="lg"
              color="neutral"
              onClick={handleLeftClick}
              disabled={leftDisabled}
            >
              <LeftArrow
                className={cn(
                  leftDisabled && 'opacity-10',
                  'ml-2 fill-black dark:fill-white'
                )}
              />
              <KeyboardShortcut
                keys={['H']}
                className={cn(leftDisabled && 'opacity-10', 'ml-3')}
              />
            </IconButton>
            <IconButton
              variant="outlined"
              size="lg"
              color="neutral"
              onClick={handleRightClick}
              disabled={rightDisabled}
            >
              <KeyboardShortcut
                keys={['L']}
                className={cn(rightDisabled && 'opacity-10', 'ml-0 mr-3')}
              />
              <RightArrow
                className={cn(
                  rightDisabled && 'opacity-10',
                  'mr-2 fill-black dark:fill-white'
                )}
              />
            </IconButton>
          </div>
        </div>
      </div>

      <div className="-mr-4 w-[450px] min-w-[450px] max-w-[450px] overflow-auto border-l border-secondary bg-white dark:bg-black">
        {currentRun ? (
          <AnnotationQueueRubricForm
            annotationQueue={annotationQueue}
            runId={currentRun.id}
            sessionId={currentRun.session_id}
            onFeedbackGiven={onFeedbackGiven}
          />
        ) : (
          <LinearProgress />
        )}
      </div>
      {currentRun && (
        <DatasetCrudPane
          onSuccess={handleNewDatasetCreated}
          onCancel={() => setIsNewDatasetPaneOpen(false)}
          label={'New dataset'}
          open={isNewDatasetPaneOpen}
          isChatEnabled={evaluateIfRunCanBeAddedToDataset(currentRun, 'chat')}
          isKVEnabled={evaluateIfRunCanBeAddedToDataset(currentRun, 'kv')}
          isLLMEnabled={evaluateIfRunCanBeAddedToDataset(currentRun, 'llm')}
          preselectedRuns={
            currentRunWithUpdatedIO ? [currentRunWithUpdatedIO] : []
          }
          baseDatasetOnRunType={currentRun?.run_type}
          isManualIO
        />
      )}
      {isDeleteRunModalOpen && currentRun?.id && (
        <DeleteModal
          isOpen={isDeleteRunModalOpen}
          doClose={() => setIsDeleteRunModalOpen(false)}
          endpoint={`${apiAnnotationQueuesPath}/${annotationQueue.id}/runs`}
          id={currentRun?.id}
          modalText="This run will be deleted from the queue for all workspace members. Are you sure?"
          invalidationPrefixes={[apiAnnotationQueuesPath]}
        />
      )}
    </div>
  );
};

export const AnnotationQueueHeader = (props: {
  annotationQueueName: string;
  organizationId: string | null;
  index?: number;
  totalRuns?: number;
  annotationQueueId: string;
  populatingInProgress: boolean;
}) => {
  const navigate = useNavigate();

  const targetUrl = useMemo(
    () =>
      `/${appOrganizationPath}/${props.organizationId}/annotation-queues/${props.annotationQueueId}/list`,
    [props.annotationQueueId, props.organizationId]
  );
  return (
    <div
      role="heading"
      className="-ml-4 inline-flex h-[57px] shrink-0 items-center justify-between gap-4 self-stretch overflow-visible border-b border-secondary px-6"
    >
      <div className="flex w-full items-center justify-between">
        <div className="flex items-center gap-4">
          <IconButton
            variant="outlined"
            color="neutral"
            size="sm"
            onClick={() =>
              navigate(
                `/${appOrganizationPath}/${props.organizationId}/annotation-queues`
              )
            }
          >
            <ChevronLeftRoundedIcon />
          </IconButton>
          <div className="line-clamp-1 break-all text-xl font-bold leading-normal">
            {props.annotationQueueName}
          </div>
        </div>

        <div className="flex items-center gap-3">
          {props.populatingInProgress && (
            <div className="text-md shrink-0 text-disabled">
              Queue is being populated...
            </div>
          )}
          <Button
            variant="outlined"
            color="neutral"
            onClick={(e) => {
              const clickResult = emulateNativeClick(targetUrl, e.nativeEvent);
              if (!clickResult) {
                navigate(targetUrl);
              }
            }}
          >
            <div className="flex items-center gap-2">
              <ListIcon className="h-4 w-4" />
              <div className="text-[15px] font-medium">View all runs</div>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
};

const useConfirmationModal = (
  handleMarkDoneParent: () => void,
  handleMoveToEndParent: () => void,
  handleLeftClickParent: () => void,
  handleRightClickParent: () => void
) => {
  const [hasEdited, setHasEdited] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState<
    (() => void) | null
  >(null);
  const [dontShowConfirmationAgain, setDontShowConfirmationAgain] =
    useLocalStorageState('ls:annotation_queue_confirmation', false);

  const maybeOpenConfirmationModal = useCallback(
    (func: () => void) => {
      if (hasEdited && !dontShowConfirmationAgain) {
        setIsConfirmationModalOpen(() => func);
      } else {
        func();
      }
    },
    [dontShowConfirmationAgain, hasEdited]
  );

  const handleMarkDone = useCallback(() => {
    maybeOpenConfirmationModal(handleMarkDoneParent);
  }, [maybeOpenConfirmationModal, handleMarkDoneParent]);

  const handleMoveToEnd = useCallback(() => {
    maybeOpenConfirmationModal(handleMoveToEndParent);
  }, [maybeOpenConfirmationModal, handleMoveToEndParent]);

  const handleLeftClick = useCallback(() => {
    maybeOpenConfirmationModal(handleLeftClickParent);
  }, [maybeOpenConfirmationModal, handleLeftClickParent]);

  const handleRightClick = useCallback(() => {
    maybeOpenConfirmationModal(handleRightClickParent);
  }, [maybeOpenConfirmationModal, handleRightClickParent]);

  return {
    isConfirmationModalOpen,
    setIsConfirmationModalOpen,
    handleMarkDone,
    handleMoveToEnd,
    handleLeftClick,
    handleRightClick,
    setDontShowConfirmationAgain,
    setHasEdited,
  };
};

const ConfirmationModal = ({
  isConfirmationModalOpen,
  setIsConfirmationModalOpen,
  setDontShowConfirmationAgain,
}: {
  isConfirmationModalOpen: (() => void) | null;
  setIsConfirmationModalOpen: (value: (() => void) | null) => void;
  setDontShowConfirmationAgain: (value: boolean) => void;
}) => {
  const [isDontShowAgainChecked, setIsDontShowAgainChecked] = useState(false);
  return (
    <Modal
      open={isConfirmationModalOpen != null}
      onClose={() => setIsConfirmationModalOpen(null)}
    >
      <ModalDialog>
        <ModalClose />
        <div className="flex max-w-[500px] flex-col gap-5">
          <div className="text-lg font-semibold">Are you sure?</div>
          <div className="flex flex-col gap-3">
            <div className="text-md font-extrathin text-quaternary">
              You have made edits to the current run but they have not been
              added to a dataset. These changes won't be saved if you continue.
            </div>
            <div className="flex items-center gap-3">
              <Checkbox
                checked={isDontShowAgainChecked}
                onChange={() => setIsDontShowAgainChecked((prev) => !prev)}
              />
              <div className="text-disabled">Don't show this message again</div>
            </div>
          </div>
          <div className="flex gap-4 self-end">
            <Button
              variant="outlined"
              color="neutral"
              onClick={() => {
                isConfirmationModalOpen?.();
                setIsConfirmationModalOpen(null);
                if (isDontShowAgainChecked) {
                  setDontShowConfirmationAgain(true);
                }
              }}
            >
              Continue
            </Button>
            <Button
              variant="solid"
              color="primary"
              onClick={() => {
                setIsConfirmationModalOpen(null);
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      </ModalDialog>
    </Modal>
  );
};
