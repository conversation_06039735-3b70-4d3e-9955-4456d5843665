import { SerializedConstructor } from '@langchain/core/load/serializable';
import { Modal, ModalClose, ModalDialog } from '@mui/joy';

import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { RepoForm, useRepoState } from '@/components/Hub/RepoForm';
import useToast from '@/components/Toast';
import {
  useCreateCommitMutation,
  useCreateRepoMutation,
  useOrganizationId,
  useRepos,
  useSelectedTenant,
} from '@/hooks/useSwr';
import { useCreateTagsOnResource } from '@/hooks/useTagsOnResource';
import { ResourceType } from '@/types/schema';
import { addQueryParams } from '@/utils/add-query-params';
import { getRepoHandleValidationError } from '@/utils/hub';

import { appPromptsIndexPath, hubApiCommitsPath } from '../../utils/constants';
import { useFetchWorkspacePromptWebhook } from './components/Webhooks/hooks/useFetchWorkspacePromptWebhook';

export const PromptNewForm = ({
  manifest,
  datasetId,
  onClose,
}: {
  manifest?: SerializedConstructor;
  datasetId?: string | null;
  onClose: () => void;
}) => {
  const toast = useToast();
  const [repo, setRepo] = useRepoState();
  const navigate = useNavigate();
  const { data } = useSelectedTenant();
  const tenantHandle = data?.tenant_handle ?? undefined;

  const [debouncedRepoHandle] = useDebounce(repo.repo_handle, 500);

  const { data: prompts, isValidating: isValidatingPrompts } = useRepos(
    debouncedRepoHandle
      ? {
          query: debouncedRepoHandle,
          tenant_id: data?.id,
        }
      : null
  );
  const hasPromptWithSameName = prompts?.repos.find(
    (prompt) => prompt.repo_handle === debouncedRepoHandle
  );

  const organizationId = useOrganizationId();
  const { trigger: createCommit, isMutating: isCommitMutating } =
    useCreateCommitMutation(tenantHandle ?? '', repo.repo_handle ?? '', {
      onError: (error) => {
        toast.createToast({
          title: 'Failed to create commit',
          description: error.message,
        });
      },
    });

  const {
    trigger: createTagsOnResource,
    tagsOnResource,
    setTagsOnResource,
  } = useCreateTagsOnResource(ResourceType.Prompt);

  const { trigger: createRepo, isMutating: createRepoLoading } =
    useCreateRepoMutation({
      onError: (error) => {
        toast.createToast({
          title: 'Failed to create prompt',
          description: error.message,
        });
      },
    });

  const { data: webhooks } = useFetchWorkspacePromptWebhook({});

  const [excludedWebhookIds, setExcludedWebhookIds] = useState<string[]>([]);

  const onSubmit = async () => {
    try {
      const res = await createRepo({
        json: repo,
      });
      if (res?.repo !== undefined) {
        if (
          tagsOnResource &&
          tagsOnResource.length > 0 &&
          !res.repo.is_public
        ) {
          await createTagsOnResource(res.repo.id);
        }
        let commitRes;
        if (manifest) {
          commitRes = await createCommit({
            url: `${hubApiCommitsPath}/${
              tenantHandle ? res.repo.full_name : `-/${res.repo.repo_handle}`
            }`,
            json: {
              parent_commit: res.repo.last_commit_hash,
              manifest: manifest,
              ignore_webhook_ids: excludedWebhookIds,
            },
          });
        }
        navigate(
          addQueryParams(
            `/${appPromptsIndexPath}/${res.repo.repo_handle}/playground`,
            {
              organizationId,
              ...(datasetId ? { datasetId } : {}),
            }
          )
        );
        if (commitRes) {
          toast.createToast({
            title: 'Prompt created',
            description: (
              <Link
                to={`/${appPromptsIndexPath}/${res.repo.repo_handle}/${commitRes?.commit.commit_hash}?organizationId=${res.repo.tenant_id}`}
                className="underline"
              >
                View in the prompt hub
              </Link>
            ),
          });
        }
      }
    } catch (error: any) {
      toast.createToast({
        title: 'Failed to create prompt',
        description: error.message ?? 'Something went wrong',
        error: true,
      });
    }
  };

  const getError = () => {
    if (hasPromptWithSameName) {
      return new Error('A prompt with this name already exists');
    }
    return getRepoHandleValidationError(repo.repo_handle);
  };

  return (
    <RepoForm
      repo={repo}
      setRepo={setRepo}
      tenantHandle={tenantHandle}
      onSubmit={onSubmit}
      onCancel={onClose}
      isLoading={isCommitMutating || createRepoLoading}
      isValidatingPrompts={isValidatingPrompts}
      error={getError()}
      mode="create"
      hideTitle={true}
      minimal={true}
      tagsOnResource={tagsOnResource}
      setTagsOnResource={setTagsOnResource}
      webhooks={webhooks ?? []}
      excludedWebhookIds={excludedWebhookIds}
      setExcludedWebhookIds={setExcludedWebhookIds}
    />
  );
};

export const PlaygroundCreatePromptModal = ({
  open,
  manifest,
  datasetId,
  onClose,
}: {
  open: boolean;
  manifest?: SerializedConstructor;
  datasetId?: string | null;
  onClose: () => void;
}) => {
  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog variant="outlined" sx={{ maxWidth: '450px' }}>
        <ModalClose size="lg" />
        <PromptNewForm
          manifest={manifest}
          datasetId={datasetId}
          onClose={onClose}
        />
      </ModalDialog>
    </Modal>
  );
};
