import { ChevronRightIcon } from '@heroicons/react/24/outline';
import { AddOutlined } from '@mui/icons-material';
import { Button, ButtonGroup, IconButton } from '@mui/joy';

import { useNavigate } from 'react-router-dom';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { EPromptType } from '@/types/schema';
import { addQueryParams } from '@/utils/add-query-params';
import { appOrganizationPath, appPlaygroundPath } from '@/utils/constants';

function PromptTypeButton({
  text,
  subtitle,
  tag,
}: {
  text: string;
  subtitle: string;
  tag?: string;
}) {
  return (
    <span className="mr-4 flex flex-col text-left">
      <span className="flex flex-row gap-2">
        <span className="text-sm font-semibold">{text}</span>
        {tag && (
          <span className="py-0.25 my-auto rounded bg-tertiary px-1 text-xs font-semibold uppercase text-tertiary">
            {tag}
          </span>
        )}
      </span>
      <span className="text-sm text-tertiary">{subtitle}</span>
    </span>
  );
}

export function PlaygroundCreatePromptButton({
  organizationId,
}: {
  organizationId?: string;
}) {
  const navigate = useNavigate();
  const orgPrefix = organizationId
    ? `/${appOrganizationPath}/${organizationId}`
    : '';
  const playgroundPath = (type?: EPromptType) => {
    const basePath = `${orgPrefix}/${appPlaygroundPath}`;
    return addQueryParams(basePath, {
      type,
      reset: true,
    });
  };

  return (
    <DropdownMenu>
      <ButtonGroup variant="solid" color="primary" size="sm">
        <Button
          type="button"
          startDecorator={<AddOutlined />}
          onClick={() => navigate(playgroundPath())}
          style={{ whiteSpace: 'nowrap' }}
        >
          Prompt
        </Button>
        <DropdownMenuTrigger asChild>
          <IconButton sx={{ p: 0, minWidth: 32 }}>
            <ChevronRightIcon className="h-4 w-4 rotate-90" />
          </IconButton>
        </DropdownMenuTrigger>
      </ButtonGroup>
      <DropdownMenuContent align="end" sideOffset={5} className="relative z-10">
        <DropdownMenuItem
          onClick={() => navigate(playgroundPath(EPromptType.CHAT))}
        >
          <PromptTypeButton
            text={'Chat-style prompt'}
            subtitle={'List of messages'}
            tag={'default'}
          />
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => navigate(playgroundPath(EPromptType.INSTRUCT))}
        >
          <PromptTypeButton
            text={'Instruct-style prompt'}
            subtitle={'Provides instruction'}
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
