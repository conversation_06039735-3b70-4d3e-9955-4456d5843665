import { SWRConfiguration } from 'swr';

import { useSWR } from '@/hooks/useSwr';
import { apiPromptWebhooksPath } from '@/utils/constants';

import { PromptWebhook } from '../types';

export const useFetchWorkspacePromptWebhook = ({
  options,
}: {
  options?: SWRConfiguration;
}) => {
  const { data, ...rest } = useSWR<PromptWebhook[]>(
    {
      url: `${apiPromptWebhooksPath}`,
    },
    options
  );

  return {
    ...rest,
    data: data ? data.slice(0, 1) : undefined,
  };
};
