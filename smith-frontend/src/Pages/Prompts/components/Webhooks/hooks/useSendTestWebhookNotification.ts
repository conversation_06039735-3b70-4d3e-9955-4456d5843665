import { useSWRMutation } from '@/hooks/useSwr';
import { apiPromptWebhooksPath } from '@/utils/constants';

import { PromptWebhookTest } from '../types';

export const useSendTestWebhookNotification = (
  options: Parameters<
    typeof useSWRMutation<{ message: string }, PromptWebhookTest>
  >[1] = {}
) => {
  return useSWRMutation<{ message: string }, PromptWebhookTest>(
    { url: `${apiPromptWebhooksPath}/test`, method: 'POST' },
    options
  );
};
