import { mutate } from 'swr';

import { FetcherParams } from '@/data/fetcher';
import { useSWRMutation } from '@/hooks/useSwr';
import { apiPromptWebhooksPath } from '@/utils/constants';

import { PromptWebhookCreate, PromptWebhookUpdate } from '../types';

export const useUpsertPromptWebhook = (
  id?: string,
  options: Parameters<
    typeof useSWRMutation<unknown, PromptWebhookCreate | PromptWebhookUpdate>
  >[1] = {}
) => {
  const isUpdate = Boolean(id);
  const url = isUpdate
    ? `${apiPromptWebhooksPath}/${id}`
    : apiPromptWebhooksPath;
  const method = isUpdate ? 'PATCH' : 'POST';

  return useSWRMutation<unknown, PromptWebhookCreate | PromptWebhookUpdate>(
    { url, method },
    {
      ...options,
      onSuccess: (...args) => {
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiPromptWebhooksPath)
        );
        options?.onSuccess?.(...args);
      },
    }
  );
};
