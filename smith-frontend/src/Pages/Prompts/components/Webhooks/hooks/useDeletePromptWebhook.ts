import { mutate } from 'swr';

import { FetcherParams } from '@/data/fetcher';
import { UseSWRMutationOptions, useSWRMutation } from '@/hooks/useSwr';
import { apiPromptWebhooksPath } from '@/utils/constants';

export const useDeletePromptWebhook = (
  options?: UseSWRMutationOptions<void, void, { webhookId: string }>
) => {
  return useSWRMutation<void, void, { webhookId: string }>(
    {
      url: `${apiPromptWebhooksPath}/:webhookId`,
      method: 'DELETE',
    },
    {
      ...options,
      onSuccess: (...args) => {
        options?.onSuccess?.(...args);
        mutate((key: FetcherParams) =>
          key?.url?.startsWith(apiPromptWebhooksPath)
        );
      },
    }
  );
};
