import { v4 as uuidv4 } from 'uuid';

import { PromptWebhookPayload } from './types';

export const SAMPLE_PAYLOAD: PromptWebhookPayload = {
  prompt_id: uuidv4(),
  prompt_name: 'test-prompt',
  commit_hash: 'commit_hash_1234567890',
  created_at: '2021-01-01T00:00:00Z',
  created_by: '<PERSON>',
  manifest: {
    lc: 1,
    type: 'constructor',
    id: ['langchain', 'schema', 'runnable', 'RunnableSequence'],
    kwargs: {
      first: {
        lc: 1,
        type: 'constructor',
        id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
        kwargs: {
          messages: [
            {
              lc: 1,
              type: 'constructor',
              id: [
                'langchain_core',
                'prompts',
                'chat',
                'SystemMessagePromptTemplate',
              ],
              kwargs: {
                prompt: {
                  lc: 1,
                  type: 'constructor',
                  id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
                  kwargs: {
                    input_variables: [],
                    template_format: 'mustache',
                    template: 'You are a chatbot.',
                  },
                },
              },
            },
            {
              lc: 1,
              type: 'constructor',
              id: [
                'langchain_core',
                'prompts',
                'chat',
                'HumanMessagePromptTemplate',
              ],
              kwargs: {
                prompt: {
                  lc: 1,
                  type: 'constructor',
                  id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
                  kwargs: {
                    input_variables: ['question'],
                    template_format: 'mustache',
                    template: '{{question}}',
                  },
                },
              },
            },
          ],
          input_variables: ['question'],
        },
      },
      last: {
        lc: 1,
        type: 'constructor',
        id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
        kwargs: {
          bound: {
            lc: 1,
            type: 'constructor',
            id: ['langchain', 'chat_models', 'openai', 'ChatOpenAI'],
            kwargs: {
              temperature: 1,
              top_p: 1,
              presence_penalty: 0,
              frequency_penalty: 0,
              model: 'gpt-4.1-mini',
              extra_headers: {},
              openai_api_key: {
                id: ['OPENAI_API_KEY'],
                lc: 1,
                type: 'secret',
              },
            },
          },
          kwargs: {},
        },
      },
    },
  },
};
