import { json } from '@codemirror/lang-json';
import { Loading01Icon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import { useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';

import { Code } from '@/components/Code/Code';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import { PrettyJSONEditor } from '@/components/PrettyJSONEditor/PrettyJSONEditor';
import useToast from '@/components/Toast';
import { Tooltip } from '@/components/Tooltip/Tooltip';
import { cn } from '@/utils/tailwind';

import { SAMPLE_PAYLOAD } from '../constants';
import { useDeletePromptWebhook } from '../hooks/useDeletePromptWebhook';
import { useSendTestWebhookNotification } from '../hooks/useSendTestWebhookNotification';
import { useUpsertPromptWebhook } from '../hooks/useUpsertPromptWebhook';
import { PromptWebhook } from '../types';

type WebhookFormData = {
  url: string;
  headers: Record<string, string>;
};

export function WebhookForm({
  webhookConfig,
  onClose,
}: {
  webhookConfig?: PromptWebhook;
  onClose: () => void;
}) {
  const { createToast } = useToast();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    control,
  } = useForm<WebhookFormData>({
    defaultValues: {
      url: webhookConfig?.url ?? '',
      headers: webhookConfig?.headers ?? {},
    },
    mode: 'onChange',
  });

  const headers = useWatch({ control, name: 'headers' });
  const url = useWatch({ control, name: 'url' });
  const { trigger: triggerUpsert, isMutating: isMutatingUpsert } =
    useUpsertPromptWebhook(webhookConfig?.id, {
      onSuccess: () => {
        createToast({
          title: 'Webhook updated',
          description: 'Webhook updated successfully',
        });
        onClose();
      },
      onError: (error) => {
        createToast({
          title: webhookConfig
            ? 'Failed to update webhook'
            : 'Failed to create webhook',
          description: error.message,
        });
      },
    });

  const { trigger: triggerTest, isMutating: isMutatingTest } =
    useSendTestWebhookNotification({
      onSuccess: (response) => {
        createToast({
          title: 'Success',
          description: response?.message ?? 'Test webhook sent successfully',
        });
      },
      onError: (error) => {
        createToast({
          title: 'Failed to send test webhook',
          description:
            error instanceof Error
              ? error.message
              : 'An unknown error occurred',
        });
      },
    });

  const { trigger: deleteWebhook, isMutating: isDeleting } =
    useDeletePromptWebhook({
      onSuccess: () => {
        createToast({
          title: 'Webhook deleted',
          description: 'Webhook deleted successfully',
        });
        onClose();
      },
      onError: (error) => {
        createToast({
          title: 'Failed to delete webhook',
          description: error.message,
        });
      },
    });

  const onSubmit = (data: WebhookFormData) => {
    triggerUpsert({
      json: {
        url: data.url,
        headers: data.headers,
        triggers: ['commit'],
        include_prompts: [],
        exclude_prompts: [],
      },
    });
  };

  return (
    <>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex h-full flex-col gap-4"
      >
        <div className="flex flex-col gap-1">
          <label
            htmlFor="webhook-url"
            className="text-sm font-medium text-primary"
          >
            URL
          </label>
          <Controller
            name="url"
            control={control}
            rules={{
              required: 'URL is required',
              pattern: {
                value: /^http(s)?:\/\/.+/,
                message: 'Please enter a valid URL',
              },
            }}
            render={({ field }) => (
              <input
                {...field}
                id="webhook-url"
                type="text"
                placeholder="Enter webhook URL"
                className="rounded-md border border-secondary bg-transparent px-3 py-2 text-sm focus:border-brand"
              />
            )}
          />
          <p className="text-xs text-quaternary">Required</p>
          {errors.url && (
            <p className="text-xs text-error" role="alert">
              {errors.url.message}
            </p>
          )}
        </div>
        <div className="flex flex-col gap-1">
          <label
            htmlFor="webhook-headers"
            className="text-sm font-medium text-primary"
          >
            Headers
          </label>
          <div
            id="webhook-headers"
            className="rounded-md border border-secondary bg-transparent px-3 py-2 text-sm focus:border-brand"
          >
            <PrettyJSONEditor
              value={headers}
              setValue={(value) => setValue('headers', value)}
              keyPlaceholder="Content-Type"
              valuePlaceholder="application/json"
              readOnly={false}
            />
          </div>
          {errors.headers && (
            <p className="text-xs text-error" role="alert">
              {errors.headers.message?.toString()}
            </p>
          )}
        </div>
        <div className="flex flex-col justify-start gap-2">
          <label className="text-sm font-medium text-primary">
            Sample Payload
          </label>
          <Code
            language={json()}
            value={JSON.stringify(SAMPLE_PAYLOAD, null, 2)}
            readOnly
            fontSize="12px"
            maxHeight="200px"
          />
          <Tooltip
            description="Send a test notification to the webhook with the sample payload."
            placement="top"
            className="w-fit"
          >
            <button
              type="button"
              className={cn(
                'mt-2 flex w-fit flex-row gap-2 text-sm font-medium text-primary underline',
                !url || isMutatingTest ? 'opacity-50' : ''
              )}
              onClick={() => {
                triggerTest({
                  json: {
                    payload: SAMPLE_PAYLOAD,
                    webhook: {
                      url,
                      headers,
                      triggers: ['commit'],
                      include_prompts: [],
                      exclude_prompts: [],
                    },
                  },
                });
              }}
              disabled={!url || isMutatingTest}
            >
              Send Test Notification
              {isMutatingTest && (
                <Loading01Icon className="ml-2 h-4 w-4 animate-spin" />
              )}
            </button>
          </Tooltip>
        </div>
        <div className="mb-6 mt-auto flex flex-row justify-between gap-2">
          {webhookConfig && (
            <Button
              type="button"
              variant="outlined"
              color="danger"
              size="sm"
              onClick={() => setIsDeleteModalOpen(true)}
              disabled={isDeleting}
            >
              Delete Webhook
            </Button>
          )}
          <div className="ml-auto flex flex-row gap-2">
            <Button
              type="button"
              variant="outlined"
              color="neutral"
              size="sm"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isMutatingUpsert} size="sm">
              {webhookConfig ? 'Update Webhook' : 'Create Webhook'}
            </Button>
          </div>
        </div>
      </form>
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={() => {
          if (webhookConfig) {
            deleteWebhook({
              templateUrlParams: {
                webhookId: webhookConfig.id,
              },
            });
          }
        }}
        title="Delete Webhook"
        description="Are you sure you want to delete this webhook? This action cannot be undone."
      />
    </>
  );
}
