import { CheckIcon, PlusIcon, XIcon } from '@langchain/untitled-ui-icons';
import { IconButton } from '@mui/joy';

import { useState } from 'react';

import { DocsButton } from '@/Pages/SingleDashboard/components/DocsButton';
import { Pane } from '@/components/Pane';

import { WebhookForm } from './components/WebhookForm';
import { useFetchWorkspacePromptWebhook } from './hooks/useFetchWorkspacePromptWebhook';
import { PromptWebhook } from './types';

export function PromptWebhooks() {
  const [isWebhookModalOpen, setIsWebhookModalOpen] = useState(false);
  const { data: webhooks } = useFetchWorkspacePromptWebhook({});
  const existingWebhook = webhooks?.[0];

  return (
    <>
      <button
        type="button"
        className="h-fit rounded-md border border-brand-strong px-3 py-[5px] text-sm font-medium text-secondary hover:opacity-80"
        onClick={() => setIsWebhookModalOpen(true)}
      >
        <div className="flex items-center gap-2">
          {existingWebhook ? (
            <>
              <div>Webhook</div>
              <div className="rounded-[3px] bg-tertiary text-sm">
                <CheckIcon className="h-4 w-4" />
              </div>
            </>
          ) : (
            <>
              <PlusIcon className="h-4 w-4" />
              <div>Webhook</div>
            </>
          )}
        </div>
      </button>
      <WebhookPane
        isOpen={isWebhookModalOpen}
        onClose={() => setIsWebhookModalOpen(false)}
        webhookConfig={existingWebhook}
      />
    </>
  );
}

function WebhookPane({
  isOpen,
  onClose,
  webhookConfig,
}: {
  isOpen: boolean;
  onClose: () => void;
  webhookConfig?: PromptWebhook;
}) {
  return (
    <Pane
      title={
        <div className="flex w-full flex-col gap-4">
          <div className="flex w-full flex-col gap-2">
            <div className="flex w-full flex-row items-center justify-between">
              <div className="flex flex-row items-center gap-2">
                <div className="text-lg font-medium">
                  {webhookConfig ? 'Configure Webhook' : 'New Webhook'}
                </div>
                <DocsButton link="https://docs.smith.langchain.com/prompt_engineering/how_to_guides/trigger_webhook" />
              </div>
              <IconButton
                variant="plain"
                color="neutral"
                size="sm"
                onClick={onClose}
              >
                <XIcon className="h-5 w-5 text-tertiary" />
              </IconButton>
            </div>
            <div className="text-xs font-normal text-tertiary">
              Configure webhook to trigger on prompt commit
            </div>
          </div>
        </div>
      }
      open={isOpen}
      onClose={onClose}
      dialogStyle={{
        marginLeft: 'calc(max(4.5rem, 100vw - max(20vw, 900px)))',
      }}
      transparentBackdrop
      noBackArrow
      titleClassName="border-none pr-6"
      className="px-6 py-0"
    >
      <WebhookForm webhookConfig={webhookConfig} onClose={onClose} />
    </Pane>
  );
}
