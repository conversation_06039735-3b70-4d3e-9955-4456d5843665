export type PromptWebhookTrigger = 'commit';

export type BasePromptWebhook = {
  url: string;
  headers: Record<string, string>;
};

export type PromptWebhookCreate = BasePromptWebhook & {
  triggers: PromptWebhookTrigger[];
  include_prompts: string[];
  exclude_prompts: string[];
};

export type PromptWebhookUpdate = Partial<BasePromptWebhook> & {
  triggers?: PromptWebhookTrigger[];
  include_prompts?: string[];
  exclude_prompts?: string[];
};

export type PromptWebhook = BasePromptWebhook & {
  id: string;
  triggers: string[];
  created_at: string;
  updated_at: string;
};

export type PromptWebhookPayload = {
  prompt_id: string;
  prompt_name: string;
  manifest: Record<string, unknown>;
  commit_hash: string;
  created_at: string;
  created_by: string;
};

export type PromptWebhookTest = {
  payload: PromptWebhookPayload;
  webhook: PromptWebhookCreate;
};
