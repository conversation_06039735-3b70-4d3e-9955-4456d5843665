import { PageTitle } from '@/components/PageTitle';
import PromptsTable from '@/components/Prompts/PromptsTable';
import { DEFAULT_10_PAGINATION_MODEL } from '@/constants/dataGridConstants';
import {
  PROMPTS_PAGE_DESCRIPTION,
  PROMPTS_PAGE_TITLE,
} from '@/constants/pageTitlesAndDescriptionConstants';
import { usePermissions } from '@/hooks/usePermissions';
import { useOrganizationId } from '@/hooks/useSwr';

import { PlaygroundCreatePromptButton } from './PlaygroundButton';
import { PromptWebhooks } from './Webhooks/PromptWebhooks';

const PromptsPage = () => {
  const organizationId = useOrganizationId();
  const { authorize } = usePermissions();

  return (
    <div className="mx-4 pb-6">
      <div className="my-4 flex justify-between gap-2">
        <PageTitle className="py-0" description={PROMPTS_PAGE_DESCRIPTION}>
          {PROMPTS_PAGE_TITLE}
        </PageTitle>
        {authorize('prompts:create') && <PromptWebhooks />}
        {authorize('prompts:create') && (
          <div className="h-fit">
            <PlaygroundCreatePromptButton
              organizationId={organizationId ?? undefined}
            />
          </div>
        )}
      </div>
      <PromptsTable
        defaultPagination={DEFAULT_10_PAGINATION_MODEL}
        className="flex min-h-[calc(100vh-300px)] flex-col gap-4"
      />
    </div>
  );
};

export default PromptsPage;
