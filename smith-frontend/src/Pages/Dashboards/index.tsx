import { BarChart10Icon } from '@langchain/untitled-ui-icons';
import { Box } from '@mui/joy';

import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import Breadcrumbs from '@/components/Breadcrumbs';
import { DataGridSearchInput } from '@/components/DataGrid/DataGridSearchInput';
import EmptyState from '@/components/EmptyState';
import { SessionsTable } from '@/components/SessionsTable';
import {
  TabGroup,
  TabLabel,
  TabList,
  TabPanel,
  TabPanels,
} from '@/components/Tabs';
import useToast from '@/components/Toast';
import {
  DASHBOARDS_PAGE_DESCRIPTION,
  DASHBOARDS_PAGE_TITLE,
} from '@/constants/pageTitlesAndDescriptionConstants';
import { useDataGridState } from '@/hooks/useDataGridState';
import { usePermissions } from '@/hooks/usePermissions';
import { useSortingState } from '@/hooks/useSortingState';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  useCreateCustomChartsSectionMutation,
  useDashboards,
  useOrganizationId,
} from '@/hooks/useSwr';
import { useCreateTagsOnResource } from '@/hooks/useTagsOnResource';
import {
  CustomChartsSectionCreate,
  CustomChartsSectionUpdate,
  DashboardsSortBy,
  ResourceType,
} from '@/types/schema';
import { appDashboardsIndexPath, appOrganizationPath } from '@/utils/constants';

import { PageTitle } from '../../components/PageTitle';
import { AddButton } from '../SingleDashboard/components/CustomChartComponents';
import { DashboardsTable } from './DashboardsTable';
import { DashboardCrudModal } from './components/DashboardCrudModal';
import { DashboardsLayout } from './components/DashboardsLayout';
import {
  PREBUILT_DASHBOARDS_COLUMN_VISIBILITY,
  PREBUILT_DASHBOARDS_FILTER,
  PREBUILT_DASHBOARDS_SORTABLE_COLUMNS,
} from './constants';

export const NoDashboards = ({
  openSectionCreateModal,
}: {
  openSectionCreateModal: () => void;
}) => {
  return (
    <div className="mx-auto mt-36 flex w-[700px] flex-col gap-6 text-center">
      <EmptyState
        title="Create and organize custom charts"
        description="Start by creating a dashboard."
        icon={<BarChart10Icon />}
      />

      <div className="mx-auto">
        <AddButton
          onClick={openSectionCreateModal}
          text="Dashboard"
          variant="outlined"
        />
      </div>
    </div>
  );
};

const DashboardsHeader = ({
  showAddDashboardHeader,
  openSectionModal,
}: {
  showAddDashboardHeader: boolean;
  openSectionModal: () => void;
}) => (
  <Box display="flex" alignItems={'center'} paddingX={2}>
    <PageTitle description={DASHBOARDS_PAGE_DESCRIPTION}>
      {DASHBOARDS_PAGE_TITLE}
    </PageTitle>
    {showAddDashboardHeader ? (
      <AddButton
        onClick={openSectionModal}
        text="New dashboard"
        variant="solid"
      />
    ) : null}
  </Box>
);

const Dashboards = () => {
  const { authorize } = usePermissions();
  const { createToast } = useToast();
  const { selectedTags } = useStoredResourceTags();
  const [activeTabIndex, setActiveTabIndex] = useState(0); // Default to Prebuilt Dashboards

  const [sectionCreateModalOpen, setSectionCreateModalOpen] = useState(false);

  const {
    trigger: createTagsOnResource,
    tagsOnResource,
    setTagsOnResource,
  } = useCreateTagsOnResource(ResourceType.Dashboard);

  const organizationId = useOrganizationId();
  const navigate = useNavigate();

  const createDashboard = useCreateCustomChartsSectionMutation({
    onSuccess: async (resp) => {
      if (resp != null && tagsOnResource.length > 0) {
        await createTagsOnResource(resp.id);
      }
      mutateDashboards();
      createToast({
        title: 'Success',
        description: `Dashboard ${resp.title} created`,
      });

      const dashboardLink = `/${appOrganizationPath}/${organizationId}/${appDashboardsIndexPath}/${resp.id}`;
      navigate(dashboardLink);
    },
    onError: (error) => {
      createToast({
        title: 'Error creating dashboard',
        description: error.message,
        type: 'error',
      });
    },
  });

  const { paginationModel, setPaginationModel } = useDataGridState({
    defaultColumnVisibility: {},
  });

  const [sortModel, setSortModel] = useSortingState('created_at');

  const [customDashboardSearch, setCustomDashboardSearch] = useState('');
  const {
    data: sections,
    isLoading,
    mutate: mutateDashboards,
  } = useDashboards({
    title_contains: customDashboardSearch,
    limit: paginationModel.pageSize,
    offset: paginationModel.pageIndex * paginationModel.pageSize,
    sort_by: sortModel[0].id as DashboardsSortBy,
    sort_by_desc: sortModel[0].desc,
    tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
  });

  const handlePaginationChange = useCallback(
    (newPaginationModel) => {
      setPaginationModel(newPaginationModel);
      mutateDashboards();
    },
    [setPaginationModel, mutateDashboards]
  );

  if (!authorize('charts:read')) {
    return null;
  }

  // Only hide when there are no tags and no dashboards because
  // the empty state has a create dashboard button
  const showAddDashboardHeader =
    (!!selectedTags.length || !!sections?.length) && authorize('charts:create');

  return (
    <DashboardsLayout>
      <div className="px-4 pt-3">
        <Breadcrumbs />
      </div>
      <DashboardsHeader
        showAddDashboardHeader={showAddDashboardHeader}
        openSectionModal={() => setSectionCreateModalOpen(true)}
      />
      <div className="mb-3 px-4">
        <TabGroup defaultIndex={activeTabIndex} onChange={setActiveTabIndex}>
          <TabList>
            <TabLabel>Prebuilt Dashboards</TabLabel>
            <TabLabel>Custom Dashboards</TabLabel>
          </TabList>
          <TabPanels>
            <TabPanel>
              <div className="mb-8 flex min-h-[calc(100vh-300px)] flex-col gap-4">
                {/* 
                Custom implementation to only show specific columns 
                and disable the column visibility toggle
              */}
                <SessionsTable
                  filter={PREBUILT_DASHBOARDS_FILTER}
                  isDashboardPage={true}
                  onRowClickPrefix="/dashboards/projects/"
                  columnVisibility={PREBUILT_DASHBOARDS_COLUMN_VISIBILITY}
                  // Set custom key to prevent column selector preferences from affecting other tables
                  localStorageDisplayColumnsKey="ls:prebuilt-dashboards-fixed-columns"
                  // Sortable columns limited to shown columns
                  sortableColumns={PREBUILT_DASHBOARDS_SORTABLE_COLUMNS}
                  // Hide the column visibility toggle completely
                  hideColumnVisibilityToggle={true}
                />
              </div>
            </TabPanel>
            <TabPanel>
              <div className="mb-8 flex min-h-[calc(100vh-300px)] flex-col gap-4">
                {!sections?.length && !selectedTags.length && !isLoading ? (
                  <NoDashboards
                    openSectionCreateModal={() =>
                      setSectionCreateModalOpen(true)
                    }
                  />
                ) : (
                  <>
                    <div className="self-start">
                      <DataGridSearchInput
                        value={customDashboardSearch}
                        onChange={setCustomDashboardSearch}
                        size="md"
                      />
                    </div>
                    <DashboardsTable
                      sections={sections}
                      isLoading={isLoading}
                      paginationModel={paginationModel}
                      setPaginationModel={handlePaginationChange}
                      sortModel={sortModel}
                      setSortModel={setSortModel}
                      mutateDashboards={mutateDashboards}
                    />
                  </>
                )}
              </div>
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>
      <DashboardCrudModal
        isOpen={sectionCreateModalOpen}
        onSubmit={(
          data: CustomChartsSectionCreate | CustomChartsSectionUpdate
        ) =>
          createDashboard.trigger({ json: data as CustomChartsSectionCreate })
        }
        doClose={() => setSectionCreateModalOpen(false)}
        tagsOnResource={tagsOnResource}
        setTagsOnResource={setTagsOnResource}
        isCreating={createDashboard.isMutating}
      />
    </DashboardsLayout>
  );
};

export default Dashboards;
