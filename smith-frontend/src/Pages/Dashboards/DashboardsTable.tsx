import {
  ColumnDef,
  OnChangeFn,
  PaginationState,
  SortingState,
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { DataGrid, DataGridPagination } from '@/components/DataGrid';
import { DeleteModal } from '@/components/Delete';
import { TableCellTextOverflow } from '@/components/Table';
import useToast from '@/components/Toast';
import { DEFAULT_10_PAGINATION_MODEL } from '@/constants/dataGridConstants';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  useOrganizationId,
  useUpdateCustomChartsSectionMutation,
} from '@/hooks/useSwr';
import {
  CustomChartsSectionResponse,
  CustomChartsSectionUpdate,
} from '@/types/schema';
import {
  apiChartsSectionPath,
  appDashboardsIndexPath,
  appOrganizationPath,
} from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { TableSkeleton } from '../Home/ResourceTable/TableSkeleton';
import { MoreActionsDropdown } from '../SingleDashboard/components/CustomChartComponents';
import { DashboardCrudModal } from './components/DashboardCrudModal';

const MoreActionsItems = ({
  dashboard,
  mutateDashboards,
}: {
  dashboard: CustomChartsSectionResponse;
  mutateDashboards: () => void;
}) => {
  const { createToast } = useToast();
  const [isEditOpen, setEditOpen] = useState(false);
  const [isDeleteOpen, setDeleteOpen] = useState(false);

  const updateSection = useUpdateCustomChartsSectionMutation(dashboard.id, {
    onSuccess: () => {
      mutateDashboards();
      createToast({
        title: 'Success',
        description: 'Dashboard updated successfully',
      });
    },
    onError: (error) => {
      createToast({
        title: 'Error updating dashboard',
        description: error.message,
        type: 'error',
      });
    },
  });

  return (
    <>
      <MoreActionsDropdown
        onEdit={() => setEditOpen(true)}
        onDelete={() => setDeleteOpen(true)}
      />
      <DeleteModal
        endpoint={`${apiChartsSectionPath()}/${dashboard.id}`}
        isOpen={isDeleteOpen}
        onSuccess={mutateDashboards}
        doClose={() => setDeleteOpen(false)}
        onError={(error) => {
          createToast({
            title: 'Error deleting chart',
            description: error.message,
            type: 'error',
          });
        }}
        modalText={`Are you sure you want to delete ${dashboard.title}? This will delete all charts in this dashboard.`}
      />
      <DashboardCrudModal
        isOpen={isEditOpen}
        doClose={() => setEditOpen(false)}
        initialData={dashboard}
        onSubmit={(data) =>
          updateSection.trigger({ json: data as CustomChartsSectionUpdate })
        }
        isCreating={updateSection.isMutating}
      />
    </>
  );
};

const SUBTEXT_STYLE = 'text-sm text-tertiary';

type SimpleDashboardsTableProps =
  | {
      simple: true;
    }
  | {
      simple?: false;
      setPaginationModel: OnChangeFn<PaginationState>;
      setSortModel: OnChangeFn<SortingState>;
    };

export const DashboardsTable = (
  props: {
    sections?: CustomChartsSectionResponse[] & {
      headers?: {
        'x-pagination-total': number;
      };
    };
    isLoading: boolean;
    paginationModel: PaginationState;
    sortModel: SortingState;
    mutateDashboards: () => void;
    emptyState?: React.ReactNode;
  } & SimpleDashboardsTableProps
) => {
  const {
    simple,
    sections,
    isLoading,
    paginationModel,
    sortModel,
    mutateDashboards,
    emptyState,
  } = props;

  const organizationId = useOrganizationId();
  const navigate = useNavigate();
  const columnHelper = createColumnHelper<CustomChartsSectionResponse>();
  const { selectedTags } = useStoredResourceTags();

  useEffect(() => {
    if (!simple) {
      props.setPaginationModel(() => DEFAULT_10_PAGINATION_MODEL);
    }
    // Reset pagination when tags change
  }, [selectedTags, simple]);

  const DEFAULT_COLUMNS = useMemo<
    ColumnDef<CustomChartsSectionResponse, any>[]
  >(
    () => [
      columnHelper.accessor('title', {
        id: 'title',
        header: () => <div className="pl-4">Name</div>,
        cell: ({ row }) => (
          <div className="pl-4">
            <div className="text-sm font-medium">{row.original.title}</div>
            <div className={cn('truncate', SUBTEXT_STYLE)}>
              {row.original.description}
            </div>
          </div>
        ),
        enableSorting: false,
      }),
      columnHelper.accessor('created_at', {
        id: 'created_at',
        header: 'Created At',
        enableSorting: false,
        cell: ({ row }) => {
          if (!row.original.created_at) return '';

          const date = new Date(row.original.created_at);
          return (
            <p className={SUBTEXT_STYLE}>
              {date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true,
              })}
            </p>
          );
        },
      }),
      columnHelper.accessor('chart_count', {
        id: 'chart_count',
        header: 'Chart Count',
        enableSorting: false,
        size: 100,
        cell: ({ row }) => (
          <p className={SUBTEXT_STYLE}>{row.original.chart_count}</p>
        ),
      }),
      columnHelper.display({
        id: 'more_actions',
        enableResizing: false,
        size: 40,
        minSize: 40,
        maxSize: 40,
        enableSorting: false,
        cell: ({ row }) => (
          <TableCellTextOverflow className="justify-end">
            <div onClick={(e) => e.stopPropagation()}>
              <MoreActionsItems
                dashboard={row.original}
                mutateDashboards={mutateDashboards}
              />
            </div>
          </TableCellTextOverflow>
        ),
      }),
    ],
    [columnHelper, mutateDashboards]
  );

  const onRowClick = (row: CustomChartsSectionResponse) => {
    const dashboardLink = `/${appOrganizationPath}/${organizationId}/${appDashboardsIndexPath}/${row.id}`;
    navigate(dashboardLink);
  };

  const table = useReactTable({
    columns: DEFAULT_COLUMNS,
    data: sections ?? [],

    state: {
      pagination: paginationModel,
      sorting: sortModel,
    },

    onPaginationChange: !simple ? props.setPaginationModel : undefined,
    onSortingChange: !simple ? props.setSortModel : undefined,

    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),

    manualExpanding: true,
    manualFiltering: false,
    manualSorting: true,
    enableMultiSort: false,
    manualPagination: true,

    columnResizeMode: 'onChange',

    enableSorting: true,
    enableExpanding: true,
    enableColumnFilters: true,

    pageCount: Math.ceil(
      (sections?.headers?.['x-pagination-total'] || 1) /
        paginationModel.pageSize
    ),
  });

  if (isLoading && !sections?.length) {
    return (
      <TableSkeleton
        rows={paginationModel.pageSize}
        rowHeight={simple ? 56 : 72}
      />
    );
  }

  return (
    <>
      <div className="w-full overflow-x-auto rounded-lg border border-secondary">
        <DataGrid
          table={table}
          isLoading={isLoading}
          cellHeight={simple ? 56 : 72}
          onClick={onRowClick}
          emptyState={
            emptyState ?? selectedTags.length > 0
              ? 'No matching dashboards with current set of resource tags'
              : 'No matching dashboards'
          }
          maxWidthFull={true}
          stickyRightColumn={true}
        />
      </div>
      {!simple && (
        <div className="flex justify-between">
          <div className="ml-auto flex flex-row gap-8">
            <DataGridPagination table={table} />
          </div>
        </div>
      )}
    </>
  );
};
