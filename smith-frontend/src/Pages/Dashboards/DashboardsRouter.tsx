import { LinearProgress } from '@mui/joy';

import { Suspense, lazy, startTransition, useEffect, useState } from 'react';

import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';

// Lazy load dashboard components
const DashboardsLazy = lazy(() =>
  import('@/Pages/Dashboards').then((module) => ({ default: module.default }))
);
const DashboardsV2Lazy = lazy(() =>
  import('@/Pages/DashboardsV2').then((module) => ({ default: module.default }))
);

export const DashboardsRouter = () => {
  const [Component, setComponent] = useState<React.ComponentType | null>(null);
  const usePrebuiltDashboards = useOrgConfig(
    OrgConfigs.enable_prebuilt_dashboards
  );

  // Use startTransition to defer the state update that may cause suspension
  useEffect(() => {
    if (usePrebuiltDashboards.isLoading) return;

    startTransition(() => {
      setComponent(
        usePrebuiltDashboards.value ? DashboardsV2Lazy : DashboardsLazy
      );
    });
  }, [usePrebuiltDashboards.value, usePrebuiltDashboards.isLoading]);

  // Show a loader while the config is loading
  if (usePrebuiltDashboards.isLoading) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }

  return <Suspense>{Component && <Component />}</Suspense>;
};

export default DashboardsRouter;
