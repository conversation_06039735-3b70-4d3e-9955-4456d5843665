import { Button, Input, Modal, ModalDialog } from '@mui/joy';

import React, { Dispatch, SetStateAction } from 'react';
import { useForm } from 'react-hook-form';

import { ResourceTagCrudPaneEditor } from '@/components/ResourceTagFilter/ResourceTagCrudPaneEditor';
import useToast from '@/components/Toast';
import {
  CustomChartsSectionCreate,
  CustomChartsSectionSchema,
  CustomChartsSectionUpdate,
  ResourceTag,
} from '@/types/schema';

export const DashboardCrudModal: React.FC<{
  isOpen: boolean;
  onSubmit: (
    data: CustomChartsSectionCreate | CustomChartsSectionUpdate
  ) => Promise<CustomChartsSectionSchema | undefined>;
  doClose: () => void;
  initialData?: CustomChartsSectionCreate;
  tagsOnResource?: ResourceTag[];
  setTagsOnResource?: Dispatch<SetStateAction<ResourceTag[]>>;
  isCreating?: boolean;
}> = ({
  isOpen,
  onSubmit,
  doClose,
  initialData,
  tagsOnResource,
  setTagsOnResource,
  isCreating,
}) => {
  const { createToast } = useToast();
  const isEditMode = !!initialData;

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: initialData || {
      title: '',
      description: '',
    },
  });

  const onSubmitForm = async (
    data: CustomChartsSectionCreate | CustomChartsSectionUpdate
  ) => {
    try {
      const resp = await onSubmit(data);

      if (resp != null) {
        reset();
        doClose();
      }
    } catch (e) {
      createToast({
        title: 'Error creating dashboard',
        description: (e as Error).message,
      });
    }
  };

  const handleDialogClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <Modal open={isOpen} onClose={doClose}>
      <ModalDialog onClick={handleDialogClick}>
        <form
          onSubmit={handleSubmit(onSubmitForm)}
          className="flex w-[500px] flex-col gap-4 p-4"
        >
          <div className="flex flex-col gap-1">
            <h2 className="text-lg font-semibold">
              {isEditMode ? 'Edit' : 'New'} Dashboard
            </h2>
          </div>
          <div>
            <label
              htmlFor="title"
              className="block text-sm font-medium text-secondary"
            >
              Dashboard name
            </label>
            <Input
              id="title"
              type="text"
              {...register('title', { required: 'Title is required' })}
              className="mt-1 block w-full"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">
                {errors.title.message as string}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-secondary"
            >
              Description
            </label>
            <Input
              id="description"
              type="text"
              {...register('description')}
              className="mt-1 block w-full"
            />
          </div>
          {tagsOnResource && setTagsOnResource && !isEditMode && (
            <div>
              <ResourceTagCrudPaneEditor
                tagsOnResource={tagsOnResource}
                setTagsOnResource={setTagsOnResource}
              />
            </div>
          )}
          <Button type="submit" className="mt-4" loading={isCreating}>
            {isEditMode ? 'Save' : 'Create'}
          </Button>
        </form>
      </ModalDialog>
    </Modal>
  );
};
