import AddIcon from '@mui/icons-material/Add';
import { Button } from '@mui/joy';

import { useState } from 'react';

import Breadcrumbs from '@/components/Breadcrumbs';
import EmptyState from '@/components/EmptyState';
import {
  PROJECTS_PAGE_DESCRIPTION,
  PROJECTS_PAGE_TITLE,
} from '@/constants/pageTitlesAndDescriptionConstants';
import { usePermissions } from '@/hooks/usePermissions';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';

import { PageTitle } from '../../components/PageTitle';
import { SessionsTable } from '../../components/SessionsTable';
import { NewProjectPane } from './NewProjectPane';

export function NoSessions({ hasTags }: { hasTags: boolean }) {
  return (
    <div className="py-10">
      <EmptyState
        title={
          hasTags
            ? 'No projects matching current set of resource tags'
            : 'There are no projects.'
        }
      />
    </div>
  );
}

const FILTERS = [
  { value: 'none', filter: {}, label: 'All' },
  {
    value: 'exc-test-runs',
    filter: { reference_free: true },
    label: 'Exclude Experiments',
  },
];

export const ProjectsHome = () => {
  const { authorize } = usePermissions();
  const createEnabled = authorize('projects:create');
  const [filter] = useState(FILTERS[1].value);

  const [newProjectOpen, setNewProjectOpen] = useState(false);
  const { selectedTags } = useStoredResourceTags();
  return (
    <div className="px-4 pb-6 pt-3">
      <Breadcrumbs />
      <div className="flex items-center justify-between gap-2">
        <PageTitle description={PROJECTS_PAGE_DESCRIPTION}>
          {PROJECTS_PAGE_TITLE}
        </PageTitle>

        <div className="flex items-center gap-2">
          {createEnabled && (
            <Button
              type="button"
              color="primary"
              size="sm"
              startDecorator={<AddIcon />}
              onClick={() => setNewProjectOpen(true)}
            >
              New Project
            </Button>
          )}

          <NewProjectPane
            open={newProjectOpen}
            onClose={() => setNewProjectOpen(false)}
          />
        </div>
      </div>
      <SessionsTable
        filter={FILTERS.find((f) => f.value === filter)?.filter ?? {}}
        emptyState={<NoSessions hasTags={selectedTags.length > 0} />}
        localStorageDisplayColumnsKey="ls:projects-table-column-visibility"
        useApproxStats={true}
      />
    </div>
  );
};
// TODO filter
