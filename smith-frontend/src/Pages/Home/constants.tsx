import {
  ANNOTATION_QUEUES_PAGE_TITLE,
  DATASETS_PAGE_TITLE,
  PROJECTS_PAGE_TITLE,
  PROMPTS_PAGE_TITLE,
} from '@/constants/pageTitlesAndDescriptionConstants';
import {
  appAnnotationQueuesPath,
  appDashboardsIndexPath,
  appDatasetsPath,
  appProjectsPath,
  appPromptsIndexPath,
  hostAppIndexPath,
} from '@/utils/constants';

import { ResourceType } from '../../types/schema';

export const RESOURCE_DETAILS_MAPPING = {
  [ResourceType.Project]: {
    type: ResourceType.Project,
    resourceCountKey: 'tracer_session_count',
    label: PROJECTS_PAGE_TITLE,
    path: appProjectsPath,
  },
  [ResourceType.Dashboard]: {
    type: ResourceType.Dashboard,
    resourceCountKey: 'dashboards_count',
    label: 'Custom Dashboards',
    path: appDashboardsIndexPath,
  },
  [ResourceType.Prompt]: {
    type: ResourceType.Prompt,
    resourceCountKey: 'repo_count',
    label: PROMPTS_PAGE_TITLE,
    path: appPromptsIndexPath,
  },
  [ResourceType.Deployment]: {
    type: ResourceType.Deployment,
    resourceCountKey: 'deployment_count',
    label: 'LangGraph Platform',
    path: hostAppIndexPath,
  },
  [ResourceType.Dataset]: {
    type: ResourceType.Dataset,
    resourceCountKey: 'dataset_count',
    label: DATASETS_PAGE_TITLE,
    path: appDatasetsPath,
  },
  [ResourceType.Queue]: {
    type: ResourceType.Queue,
    resourceCountKey: 'annotation_queue_count',
    label: ANNOTATION_QUEUES_PAGE_TITLE,
    path: appAnnotationQueuesPath,
  },
};

export const SECTIONS_MAPING = {
  observability: {
    title: 'Observability',
    resources: [
      RESOURCE_DETAILS_MAPPING[ResourceType.Project],
      RESOURCE_DETAILS_MAPPING[ResourceType.Dashboard],
    ],
  },
  evaluation: {
    title: 'Evaluation',
    resources: [
      RESOURCE_DETAILS_MAPPING[ResourceType.Dataset],
      RESOURCE_DETAILS_MAPPING[ResourceType.Queue],
    ],
  },
  prompts: {
    title: 'Prompt engineering',
    resources: [RESOURCE_DETAILS_MAPPING[ResourceType.Prompt]],
  },
  deployments: {
    title: 'Deployments',
    resources: [RESOURCE_DETAILS_MAPPING[ResourceType.Deployment]],
  },
};

export const DEFAULT_PAGINATION_MODEL = { pageIndex: 0, pageSize: 5 };
