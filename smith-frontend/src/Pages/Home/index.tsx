import { TabGroup } from '@headlessui/react';
import { User02Icon } from '@langchain/untitled-ui-icons';

import { useMemo, useState } from 'react';

import Breadcrumbs from '@/components/Breadcrumbs';
import { CopyMultiButton } from '@/components/CopyButton/CopyMultiButton';
import { ProfileCircle } from '@/components/ProfileCircle';
import { Skeleton } from '@/components/Skeleton';
import { TabLabel, TabList, TabPanel, TabPanels } from '@/components/Tabs';
import { useAuth } from '@/hooks/useAuth';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  useCurrentOrganization,
  useSelectedWorkspace,
  useWorkspaceStats,
} from '@/hooks/useSwr';
import { TenantsStatsResponse } from '@/types/schema';
import { hostEnabled } from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { HomeGetStarted } from './HomeGetStarted';
import { ResourceSection } from './ResourceSection';
import { SECTIONS_MAPING } from './constants';

type HomeSection = 'observability' | 'evaluation' | 'prompts' | 'deployments';
function HomeSection(props: {
  section: HomeSection;
  stats?: TenantsStatsResponse;
}) {
  const { section, stats } = props;
  const [tab, setTab] = useState(0);
  return (
    <div className="flex flex-col gap-3">
      <span className="text-lg font-medium capitalize">
        {SECTIONS_MAPING[section].title}
      </span>
      <TabGroup tabIndex={tab} onChange={setTab}>
        <TabList className="m-0 gap-2 border-none pl-0">
          {SECTIONS_MAPING[section].resources.map(
            ({ type, resourceCountKey, label }) => (
              <TabLabel
                className={cn(
                  'border-none bg-transparent capitalize text-quaternary transition-colors',
                  'rounded-md px-3 py-2 text-sm font-medium',
                  'ui-selected:bg-tertiary ui-selected:text-primary dark:ui-selected:bg-tertiary dark:ui-selected:text-primary',
                  'ui-selected:font-medium',
                  'hover:bg-secondary dark:hover:bg-secondary'
                )}
                key={type}
              >
                {label}
                {stats && (
                  <div className="rounded-full border border-secondary bg-background px-2.5 py-0">
                    {stats[resourceCountKey]}
                  </div>
                )}
              </TabLabel>
            )
          )}
        </TabList>
        <TabPanels className="min-h-[380px]">
          {SECTIONS_MAPING[section].resources.map(
            ({ type, resourceCountKey }) => {
              const isEmpty = stats?.[resourceCountKey] === 0;
              return (
                <TabPanel key={type}>
                  <ResourceSection resourceType={type} isEmpty={isEmpty} />
                </TabPanel>
              );
            }
          )}
        </TabPanels>
      </TabGroup>
    </div>
  );
}

export function Home() {
  const { data: workspace, isLoading } = useSelectedWorkspace();
  const { data: organization } = useCurrentOrganization();

  const auth = useAuth();
  const { selectedTags } = useStoredResourceTags();
  const stats = useWorkspaceStats(
    {
      tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
    },
    {
      keepPreviousData: false,
      skip: !auth?.authed,
    }
  );

  const canUseLangGraphCloud = useOrgConfig(OrgConfigs.can_use_langgraph_cloud);

  // https://github.com/langchain-ai/langchainplus/blob/main/host-backend/host/api/endpoints/projects.py#L45
  const deploymentsEnabled = canUseLangGraphCloud.value;

  const sections = useMemo(() => {
    const result: HomeSection[] = ['observability', 'evaluation', 'prompts'];
    if (deploymentsEnabled && hostEnabled === '1') result.push('deployments');
    return result;
  }, [deploymentsEnabled]);

  return (
    <div>
      <div className="border-b border-secondary px-4 py-3">
        <Breadcrumbs />
      </div>
      <div className="flex flex-col gap-9 px-9 py-11 tracking-tight">
        <div className="justify-left flex items-center gap-2">
          {isLoading ? (
            <Skeleton className="m-0 min-h-[36px] w-48" />
          ) : (
            <>
              <ProfileCircle
                name={
                  workspace?.is_personal
                    ? ''
                    : organization?.display_name ?? 'Organization'
                }
                backupIcon={<User02Icon className="size-4" />}
                className="h-7 w-7"
              />

              <span className="text-3xl">
                {workspace?.is_personal
                  ? 'Personal'
                  : workspace?.display_name ?? 'Workspace'}
              </span>

              <CopyMultiButton
                copyItems={[
                  { prefix: 'Org ID', item: organization?.id ?? '' },
                  { prefix: 'Workspace ID', item: workspace?.id ?? '' },
                ]}
              />
            </>
          )}
        </div>

        <HomeGetStarted />
        {sections.map((section) => (
          <HomeSection section={section} stats={stats.data} key={section} />
        ))}
      </div>
    </div>
  );
}
