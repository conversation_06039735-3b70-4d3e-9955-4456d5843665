import { BarChart10Icon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { DashboardCrudModal } from '@/Pages/Dashboards/components/DashboardCrudModal';
import EmptyState from '@/components/EmptyState';
import useToast from '@/components/Toast';
import { useSortingState } from '@/hooks/useSortingState';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  useCreateCustomChartsSectionMutation,
  useDashboards,
} from '@/hooks/useSwr';
import { useCreateTagsOnResource } from '@/hooks/useTagsOnResource';
import {
  CustomChartsSectionCreate,
  CustomChartsSectionUpdate,
  DashboardsSortBy,
  ResourceType,
} from '@/types/schema';
import { appDashboardsIndexPath } from '@/utils/constants';

import { DashboardsTable } from '../../Dashboards/DashboardsTable';
import { DEFAULT_PAGINATION_MODEL } from '../constants';
import { TableSkeleton } from './TableSkeleton';

function DashboardsTableEmptyState({
  hasTags,
  mutateDashboards,
}: {
  hasTags: boolean;
  mutateDashboards: () => void;
}) {
  return (
    <EmptyState
      title="No dashboards found"
      description={
        hasTags
          ? 'No matching dashboards with current set of resource tags'
          : 'Start by creating a new dashboard and adding some charts'
      }
      action={<CreateDashboard mutateDashboards={mutateDashboards} />}
      fancyIcon={true}
      icon={<BarChart10Icon />}
    />
  );
}
function CreateDashboard(props: { mutateDashboards: () => void }) {
  const [sectionCreateModalOpen, setSectionCreateModalOpen] = useState(false);
  const { createToast } = useToast();
  const createDashboard = useCreateCustomChartsSectionMutation();
  const navigate = useNavigate();

  const {
    trigger: createTagsOnResource,
    tagsOnResource,
    setTagsOnResource,
  } = useCreateTagsOnResource(ResourceType.Dashboard);

  return (
    <>
      <Button
        color="primary"
        size="sm"
        onClick={() => {
          setSectionCreateModalOpen(true);
        }}
      >
        Create dashboard
      </Button>
      <DashboardCrudModal
        isOpen={sectionCreateModalOpen}
        tagsOnResource={tagsOnResource}
        setTagsOnResource={setTagsOnResource}
        onSubmit={async (
          data: CustomChartsSectionCreate | CustomChartsSectionUpdate
        ) => {
          try {
            const resp = await createDashboard.trigger({
              json: data as CustomChartsSectionCreate,
            });
            if (resp != null && tagsOnResource.length > 0) {
              await createTagsOnResource(resp.id);
            }
            props.mutateDashboards();
            createToast({
              title: 'Success',
              description: `Dashboard ${resp?.title} created`,
            });
            navigate(`${appDashboardsIndexPath}/${resp?.id}`);
            return resp;
          } catch (error) {
            createToast({
              title: 'Error creating dashboard',
              description: (error as Error).message,
              type: 'error',
            });
          }
        }}
        doClose={() => setSectionCreateModalOpen(false)}
        isCreating={createDashboard.isMutating}
      />
    </>
  );
}

export function HomeDashboardsTable() {
  const [sortModel] = useSortingState('created_at');
  const { selectedTags } = useStoredResourceTags();
  const {
    data: sections,
    isLoading,
    mutate: mutateDashboards,
  } = useDashboards({
    limit: DEFAULT_PAGINATION_MODEL.pageSize,
    offset:
      DEFAULT_PAGINATION_MODEL.pageIndex * DEFAULT_PAGINATION_MODEL.pageSize,
    sort_by: sortModel[0].id as DashboardsSortBy,
    sort_by_desc: sortModel[0].desc,
    tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
  });

  if (isLoading && !sections?.length) {
    return <TableSkeleton />;
  }
  if (sections?.length === 0 && selectedTags.length === 0) {
    return (
      <div className="p-4">
        <DashboardsTableEmptyState
          hasTags={selectedTags.length > 0}
          mutateDashboards={mutateDashboards}
        />
      </div>
    );
  }
  return (
    <DashboardsTable
      sections={sections}
      isLoading={isLoading}
      paginationModel={DEFAULT_PAGINATION_MODEL}
      simple={true}
      sortModel={sortModel}
      mutateDashboards={mutateDashboards}
      emptyState={
        <DashboardsTableEmptyState
          hasTags={selectedTags.length > 0}
          mutateDashboards={mutateDashboards}
        />
      }
    />
  );
}
