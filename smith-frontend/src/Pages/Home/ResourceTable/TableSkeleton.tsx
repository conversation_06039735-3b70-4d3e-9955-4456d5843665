import { Skeleton } from '@/components/Skeleton';
import { Table, TableBody, TableHeader, TableRow } from '@/components/Table';
import { cn } from '@/utils/tailwind';

export function TableSkeleton(props: {
  rows?: number;
  rowHeight?: number;
  hideHeader?: boolean;
  className?: string;
}) {
  return (
    <div
      className={cn(
        'overflow-hidden rounded-lg border border-secondary',
        props.className
      )}
    >
      <Table>
        {!props.hideHeader && (
          <TableHeader className="bg-secondary">
            <TableRow className="flex h-12 items-center px-6">
              <Skeleton className="h-5 w-full" />
            </TableRow>
          </TableHeader>
        )}
        <TableBody>
          {Array.from({ length: props.rows ?? 5 }).map((_, index) => (
            <TableRow
              key={index}
              className={cn('flex items-center px-6')}
              style={{ height: props.rowHeight }}
            >
              <Skeleton className="h-5 w-full" />
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
