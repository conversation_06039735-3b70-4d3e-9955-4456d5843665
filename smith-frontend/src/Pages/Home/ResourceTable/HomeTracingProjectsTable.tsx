import { GitBranch01Icon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import { useState } from 'react';

import EmptyState from '@/components/EmptyState';
import { ErrorBanner } from '@/components/ErrorBanner';
import { SetupObservability } from '@/components/Onboarding/SetupObservability';
import { Pane } from '@/components/Pane';
import { SessionsDataGrid } from '@/components/SessionsTable/components/SessionsDataGrid';
import { PROJECTS_PAGE_TITLE } from '@/constants/pageTitlesAndDescriptionConstants';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import { useSessions } from '@/hooks/useSwr';

import { DEFAULT_PAGINATION_MODEL } from '../constants';
import { TableSkeleton } from './TableSkeleton';

export function HomeTracingProjectsTable() {
  const { selectedTags } = useStoredResourceTags();
  const [showSetupObservability, setShowSetupObservability] = useState(false);

  const { data, isLoading, isValidating, mutate, error } = useSessions(
    {
      offset:
        DEFAULT_PAGINATION_MODEL.pageIndex * DEFAULT_PAGINATION_MODEL.pageSize,
      limit: DEFAULT_PAGINATION_MODEL.pageSize,
      sort_by: selectedTags.length > 0 ? undefined : 'last_run_start_time',
      sort_by_desc: true,
      tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
      reference_free: true,
      use_approx_stats: true,
    },
    {},
    {
      revalidateOnFocus: false,
    }
  );

  const emptyState = (
    <EmptyState
      title="Setup observability"
      description={
        selectedTags.length > 0
          ? 'No matching tracing projects with current set of resource tags'
          : 'Send a trace to LangSmith to create a new tracing project and start monitoring'
      }
      action={
        <Button
          color="primary"
          size="sm"
          onClick={() => {
            setShowSetupObservability(true);
          }}
        >
          Setup observability
        </Button>
      }
      fancyIcon={true}
      icon={<GitBranch01Icon />}
    />
  );

  if (isLoading && !data?.rows?.length) {
    return <TableSkeleton />;
  }
  if (error) {
    return <ErrorBanner>{error.message}</ErrorBanner>;
  }

  return (
    <>
      <SessionsDataGrid
        data={data || {}}
        isLoading={isLoading}
        isValidating={isValidating}
        simple={true}
        isDatasetPage={false}
        emptyState={emptyState}
        mutateSessions={mutate}
        localStorageDisplayColumnsKey={'ls:projects-table-column-visibility'}
        localStorageDataGridSizeKey={'home-tracing-projects'}
      />
      {data?.excluding_empty &&
        (data.rows?.length ?? 0) < 5 &&
        data.rows?.length !== 0 && (
          <div className="rounded-md bg-tertiary px-4 py-2 text-sm text-ls-black">
            Showing {PROJECTS_PAGE_TITLE} with runs in last 7 days. Some
            projects are hidden.
          </div>
        )}
      <Pane
        title="Setup observability"
        open={showSetupObservability}
        onClose={() => setShowSetupObservability(false)}
      >
        <SetupObservability />
      </Pane>
    </>
  );
}
