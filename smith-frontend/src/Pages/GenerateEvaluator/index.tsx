import { Box, LinearProgress } from '@mui/joy';

import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';

import Breadcrumbs from '@/components/Breadcrumbs';
import { ErrorBanner } from '@/components/ErrorBanner';
import { PageTitle } from '@/components/PageTitle';
import useToast from '@/components/Toast';
import { createDataset } from '@/data/mutations';
import { usePermissions } from '@/hooks/usePermissions';
import {
  useBulkExampleCreate,
  useExampleRunsInfinite,
  useOrganizationId,
  useSessions,
} from '@/hooks/useSwr';
import { ExampleSchemaWithRunsAndOptionalFields } from '@/types/schema';

import { usePlaygroundStore } from '../Playground/PlaygroundHome/hooks/usePlaygroundStore';
import { EvaluatorSessionCompareTable } from './components/EvaluatorSessionCompareTable';

const GenerateEvaluator = () => {
  const { experimentId } = useParams();
  const { authorize, isLoading: isPermsLoading } = usePermissions();
  const organizationId = useOrganizationId();
  const { createToast } = useToast();

  const [searchParams] = useSearchParams();
  const datasetName = searchParams.get('datasetName');

  // State for evaluator data
  const [evaluatorName, setEvaluatorName] = useState<string | undefined>(
    undefined
  );
  const [isCreatingDataset, setIsCreatingDataset] = useState(false);
  const [isAligningEvaluator, setIsAligningEvaluator] = useState(false);

  // New state for alignment mode
  const [isAlignmentMode, setIsAlignmentMode] = useState(false);
  const [alignmentDatasetId, setAlignmentDatasetId] = useState<string>('');

  const setIsAlignEvaluator = usePlaygroundStore(
    (state) => state.setIsAlignEvaluator
  );

  useEffect(() => {
    setIsAlignEvaluator(true);
  }, [setIsAlignEvaluator]);

  // Get experiment data
  const experimentData = useSessions(
    experimentId ? { id: [experimentId] } : null
  );
  const experiment = experimentData.data?.rows?.[0];

  // Setup bulk example create hook
  const { trigger: addExamples } = useBulkExampleCreate({
    datasetDataType: 'kv',
    onSuccess: () => {
      setIsCreatingDataset(false);
    },
    onError: (error) => {
      setIsCreatingDataset(false);
      createToast({
        title: 'Error creating examples',
        description: error,
      });
    },
  });

  // Get examples with runs for the experiment
  const {
    data: examplePages,
    isLoading: areExamplesLoading,
    isValidating: areExamplesValidating,
    size: examplesSize,
    setSize: setExamplesSize,
    mutate: mutateInfiniteExamplesSwr,
  } = useExampleRunsInfinite(
    100, // page size
    experiment?.reference_dataset_id ?? null,
    experimentId
      ? {
          session_ids: [experimentId],
          preview: false,
        }
      : null,
    undefined,
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      keepPreviousData: true,
    }
  );

  // Flatten example pages into a single array
  const examples = examplePages?.flatMap((page) => page) || [];

  // Take the last page which actually contains examples, to make sure we get the right total
  const total =
    (examplePages ?? [])
      .slice()
      .reverse()
      .find((page) => page.length > 0)?.headers?.['x-pagination-total'] ?? 0;

  const hasMoreToLoad = examples ? examples.length < total : false;

  const isNextPageValidating =
    areExamplesValidating &&
    examplesSize > 1 &&
    examplePages?.length != examplesSize;

  // Handle align evaluator - creates a dataset and preps the state for alignment mode
  const handleAlignEvaluator = async (
    evaluations: Map<string, number>,
    headerText: string,
    examples: ExampleSchemaWithRunsAndOptionalFields[]
  ) => {
    if (!organizationId) {
      createToast({
        title: 'Error',
        description: 'Organization ID not found',
      });
      return;
    }

    try {
      setIsAligningEvaluator(true);
      setIsCreatingDataset(true);

      // Transform examples for the dataset
      const datasetExamples: any[] = [];

      for (const example of examples) {
        const exampleId = example.id;
        const score = evaluations.get(exampleId);

        // Skip examples without an evaluation score
        if (score === undefined || score === null) continue;

        // Create the new dataset format with the required structure
        const datasetExample = {
          inputs: JSON.stringify({
            input: example.inputs,
            referenceOutput: example.outputs,
            output: example.runs[0].outputs,
          }),
          outputs: JSON.stringify({
            [headerText]: score,
          }),
          metadata: JSON.stringify({
            original_dataset_id: example.dataset_id,
            source_example_id: example.id,
          }),
        };

        datasetExamples.push(datasetExample);
      }

      if (datasetExamples.length === 0) {
        createToast({
          title: 'No valid examples',
          description: 'Please score at least one example before proceeding.',
        });
        setIsCreatingDataset(false);
        return;
      }

      // Create the dataset
      const dataset = await createDataset(organizationId, {
        name: `${headerText}-${datasetName}`,
        description: `Alignment dataset created from experiment "${experiment?.name}"`,
        data_type: 'kv',
      });

      // Add examples to the dataset
      await addExamples({
        dataset_id: dataset.id,
        examples: datasetExamples,
      });

      // Show success message
      createToast({
        title: 'Dataset created successfully',
        description: `Created dataset "${headerText}" with ${datasetExamples.length} examples.`,
      });

      // Instead of navigating, set the alignment mode states
      setAlignmentDatasetId(dataset.id);
      setIsAlignmentMode(true);
      setIsCreatingDataset(false);
    } catch (error) {
      console.error('Error creating aligned dataset:', error);
      createToast({
        title: 'Error creating dataset',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
      });
      setIsCreatingDataset(false);
    } finally {
      setIsAligningEvaluator(false);
    }
  };

  if (isPermsLoading) {
    return <LinearProgress />;
  }

  if (!authorize('datasets:read') || !experimentId) {
    return <ErrorBanner className="m-4">Forbidden</ErrorBanner>;
  }

  // Loading state
  if (experimentData.isLoading || !experimentData.data) {
    return (
      <div className="px-4 pb-6 pt-3">
        <Breadcrumbs />
        <LinearProgress />
      </div>
    );
  }

  return (
    <div className="px-4 pb-6 pt-3">
      <Breadcrumbs />
      <Box display="flex" alignItems="center" gap={2}>
        <PageTitle>Generate Evaluator</PageTitle>
      </Box>

      <div className="mb-4">
        <p className="text-lg">
          Create an evaluator based on experiment:{' '}
          <strong>{experiment?.name}</strong>
        </p>
        <p className="mb-4 text-sm text-gray-500">
          Mark each example with a 0 or 1 score to create a binary classifier
          evaluator.
        </p>
      </div>

      <div className="mb-4">
        <EvaluatorSessionCompareTable
          evaluatorName={evaluatorName}
          onChangeEvaluatorName={setEvaluatorName}
          onAlignEvaluator={handleAlignEvaluator}
          isAligningEvaluator={isAligningEvaluator}
          datasetId={experiment?.reference_dataset_id}
          columns={[{ sessionId: experimentId }]}
          sessions={experimentData}
          examples={examples}
          areExamplesLoading={areExamplesLoading || isCreatingDataset}
          hasMoreToLoad={hasMoreToLoad}
          isNextPageValidating={isNextPageValidating}
          setExamplesSize={setExamplesSize}
          mutateInfiniteExamplesSwr={mutateInfiniteExamplesSwr}
          isAlignmentMode={isAlignmentMode}
          alignmentDatasetId={alignmentDatasetId}
        />
      </div>
    </div>
  );
};

export default GenerateEvaluator;
