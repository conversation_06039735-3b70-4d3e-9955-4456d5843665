import { SerializedConstructor } from '@langchain/core/load/serializable';
import { TemplateFormat } from '@langchain/core/prompts';
import { Button, Tooltip } from '@mui/joy';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import {
  SecretsProvider,
  useSecrets,
} from '@/Pages/Playground/PlaygroundHome/PlaygroundSecretsContext';
import { useDefaultPromptConfig } from '@/Pages/Playground/PlaygroundHome/hooks/useDefaultPromptConfig';
import {
  useManifests,
  usePlaygroundStore,
} from '@/Pages/Playground/PlaygroundHome/hooks/usePlaygroundStore';
import { usePlaygroundSubmit } from '@/Pages/Playground/PlaygroundHome/hooks/usePlaygroundSubmit';
import { DEFAULT_PROPS } from '@/Pages/Playground/PlaygroundPage';
import { PlaygroundSecrets } from '@/Pages/Playground/components/PlaygroundSecrets.tsx';
import { PlaygroundSubmit } from '@/Pages/Playground/components/PlaygroundSubmit';
import { PromptSettingsDisplay } from '@/Pages/Playground/components/PromptSettings';
import { PlaygroundSecretsSource } from '@/Pages/Playground/components/constants';
import { Manifest } from '@/Pages/Playground/components/manifest/Manifest';
import { manifestModelAndProvider } from '@/Pages/Playground/components/manifest/serialized/ManifestConstructor';
import { PlaygroundParentContextProvider } from '@/Pages/Playground/context/PlaygroundParentContext';
import { convertManifestToTuple } from '@/Pages/Playground/utils/Playground.utils';
import { ETemplateFormat } from '@/Pages/Playground/utils/types';
import { useAvailablePaths } from '@/components/EvaluatorCrudPane/hooks/useAvailablePaths';
import { useEvaluatorStore } from '@/components/EvaluatorCrudPane/store/evaluatorStore';
import { getDisabledMessageTypes } from '@/components/EvaluatorCrudPane/utils/useParsedManifest';
import { useVariableMappingProps } from '@/components/EvaluatorCrudPane/utils/useVariableMappingProps';
import useToast from '@/components/Toast';
import {
  useExamples,
  useOrganizationId,
  useRunRulesMutation,
} from '@/hooks/useSwr';
import {
  EPromptType,
  ExampleSchemaWithRuns,
  MessageCapability,
} from '@/types/schema';
import { MessageCapabilitiesProvider } from '@/utils/MessageCapabilitiesContext';
import { appOrganizationPath } from '@/utils/constants';

interface EvaluatorPlaygroundPromptPanelProps {
  evaluatorName: string;
  datasetId: string;
  alignmentDatasetId: string;
  onSaveEvaluator?: () => void;
  onRunEvaluator?: () => void;
  originalDatasetExamples?: ExampleSchemaWithRuns[];
}

function PlaygroundPanelContent({
  evaluatorName,
  datasetId,
  alignmentDatasetId,
  onSaveEvaluator,
  onRunEvaluator,
  originalDatasetExamples,
}: EvaluatorPlaygroundPromptPanelProps) {
  const { defaultConfig } = useDefaultPromptConfig();

  const { data: examplesData } = useExamples(
    { dataset: alignmentDatasetId },
    undefined,
    {
      keepPreviousData: false,
    }
  );
  const sampleExample = examplesData?.[0];
  const { inputs: exampleInputs } = sampleExample ?? {};

  const [searchParams] = useSearchParams();
  const datasetName = searchParams.get('datasetName');

  const { promptManifest, modelManifest } = DEFAULT_PROPS(
    'structured' as EPromptType,
    exampleInputs ? Object.keys(exampleInputs)[0] : null,
    'dataset-eval',
    defaultConfig,
    ETemplateFormat.MUSTACHE,
    evaluatorName
  );

  const setVariableMapping = useEvaluatorStore(
    (state) => state.setVariableMapping
  );
  const variableMapping = useEvaluatorStore((state) => state.variableMapping);

  useEffect(() => {
    // Only set initial variable mapping if no mapping exists yet
    if (!variableMapping || Object.keys(variableMapping).length === 0) {
      setVariableMapping({
        input: 'input',
        output: 'output',
        referenceOutput: 'referenceOutput',
      });
    }

    // Clean up store on unmount
    return () => {
      useEvaluatorStore.getState().reset();
    };
  }, []);

  const firstExample = useMemo(
    () => originalDatasetExamples?.[0],
    [originalDatasetExamples]
  );

  const sampleRunOutputs = useMemo(() => {
    return firstExample?.runs?.[0]?.outputs;
  }, [firstExample]);
  useAvailablePaths(
    firstExample?.inputs,
    sampleRunOutputs,
    firstExample?.outputs,
    false
  );

  const initializeState = usePlaygroundStore((state) => state.initializeState);

  useEffect(() => {
    initializeState({
      promptManifest: promptManifest,
      modelManifest: modelManifest,
      type: EPromptType.STRUCTURED,
      datasetId: alignmentDatasetId,
    });

    // only want to run this on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { createToast } = useToast();
  const organizationId = useOrganizationId();
  const prompts = usePlaygroundStore((state) => state.prompts);
  const manifests = useManifests();
  const [isRunningAlignment, setIsRunningAlignment] = useState(false);
  const [isSavingEvaluator, setIsSavingEvaluator] = useState(false);

  const { secrets, setSecrets, openSecretsRef } = useSecrets();

  const onSubmit = usePlaygroundSubmit({
    selectedSecretsSource: PlaygroundSecretsSource.WORKSPACE,
    variableMapping,
  });

  // Handle running the evaluator
  const handleRunEvaluator = useCallback(async () => {
    if (onRunEvaluator) {
      await onRunEvaluator();
    }
    if (!alignmentDatasetId) {
      createToast({
        title: 'Error',
        description: 'No alignment dataset found',
      });
      return;
    }

    setIsRunningAlignment(true);

    try {
      await onSubmit(false);
    } catch (error) {
      console.error('Error running evaluator:', error);
      createToast({
        title: 'Error running evaluator',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
      });
    } finally {
      setIsRunningAlignment(false);
    }
  }, [alignmentDatasetId, createToast, onRunEvaluator, onSubmit]);

  const mutate = useRunRulesMutation(null, {
    onSuccess: () => {
      if (onSaveEvaluator) {
        onSaveEvaluator();
      }
    },
  });

  // Function to save an evaluator
  const saveEvaluator = useCallback(async () => {
    if (!datasetId || !evaluatorName || !organizationId || !variableMapping) {
      if (!datasetId) {
        createToast({
          title: 'Error saving evaluator',
          description: 'Missing dataset ID.',
        });
      }
      if (!evaluatorName) {
        createToast({
          title: 'Error saving evaluator',
          description: 'Missing evaluator name.',
        });
      }
      if (!organizationId) {
        createToast({
          title: 'Error saving evaluator',
          description: 'Missing organization ID.',
        });
      }
      if (!variableMapping) {
        createToast({
          title: 'Error saving evaluator',
          description: 'Missing variable mapping.',
        });
      }
      return;
    }

    try {
      setIsSavingEvaluator(true);

      // Create evaluator configuration
      const evaluatorConfig = {
        name: evaluatorName,
        model: manifests[0].kwargs.last.kwargs.bound,
        prompt: convertManifestToTuple(
          prompts[0].manifest.kwargs.first.kwargs.messages
        ),
        dataset_id: datasetId,
        schema: {
          title: 'extract',
          description: "Extract information from the user's response.",
          type: 'object',
          properties: {
            [evaluatorName]: {
              type: 'boolean',
            },
          },
          required: ['score'],
        },
        template_format: 'mustache' as TemplateFormat,
        variable_mapping: variableMapping,
      };

      await mutate.trigger({
        json: {
          filter: 'eq(is_root, true)',
          sampling_rate: 1,
          is_enabled: true,
          display_name: evaluatorName,
          dataset_id: datasetId,
          evaluator_version: 2,
          use_corrections_dataset: false,
          evaluators: [{ structured: evaluatorConfig }],
        },
      });

      // Navigate back to datasets page
      window.location.href = `/${appOrganizationPath}/${organizationId}/datasets/${datasetId}`;
    } catch (error) {
      console.error('Error saving evaluator:', error);
      createToast({
        title: 'Error saving evaluator',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
      });
    } finally {
      setIsSavingEvaluator(false);
    }
  }, [
    datasetId,
    evaluatorName,
    organizationId,
    createToast,
    manifests,
    prompts,
    variableMapping,
    mutate,
  ]);

  const variableMappingProps = useVariableMappingProps();

  const manifest: SerializedConstructor | undefined = prompts[0]?.manifest;
  const setManifest = usePlaygroundStore((state) => state.setManifest);

  const manifestLast = manifest?.kwargs?.last;
  const modelAndProvider = manifestLast
    ? manifestModelAndProvider({
        variant: 'line',
        value: manifestLast,
        options: {},
      })
    : null;
  const disabledMessageTypes = getDisabledMessageTypes(
    modelAndProvider?.model ?? ''
  );
  return prompts[0]?.manifest ? (
    <>
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold">Alignment Prompt</h3>
        <div className="flex gap-2">
          <PromptSettingsDisplay
            variant="evaluator"
            setManifest={(value) =>
              setManifest(0, (prev) => ({
                ...prev,
                kwargs: { ...prev.kwargs, last: value },
              }))
            }
            manifest={manifest}
          />
          <PlaygroundSecrets
            openSecretsRef={openSecretsRef}
            value={secrets}
            onChange={setSecrets}
            selectedSecretsSource={PlaygroundSecretsSource.WORKSPACE}
            forceWorkspaceSecrets={true}
          />
          <Tooltip
            title={
              datasetName
                ? `Save evaluator to dataset ${datasetName}`
                : 'Save evaluator to dataset'
            }
          >
            <Button
              onClick={saveEvaluator}
              size="sm"
              color="info"
              loading={isSavingEvaluator}
            >
              Save Evaluator
            </Button>
          </Tooltip>
          <PlaygroundSubmit
            onSubmit={handleRunEvaluator}
            disabled={isRunningAlignment}
          />
        </div>
      </div>
      <MessageCapabilitiesProvider
        disabledCapabilities={[
          MessageCapability.IMAGE,
          MessageCapability.CANVAS,
        ]}
      >
        <Manifest
          variant="evaluator"
          value={manifest.kwargs.first}
          onChange={(value) => {
            setManifest(0, (prev) => ({
              ...prev,
              kwargs: { ...prev.kwargs, first: value },
            }));
          }}
          hideTemplateFormatSelector
          outputSchemaAsModal
          minimizedSchema={true}
          disabledMessageTypes={disabledMessageTypes}
          disabledMessageCapabilities={[
            MessageCapability.IMAGE,
            MessageCapability.CANVAS,
          ]}
          isAllCollapsed={false}
          readOnly={false}
          options={{}}
          onOptionsChange={() => void 0}
          variableMappingProps={variableMappingProps}
        />
      </MessageCapabilitiesProvider>
    </>
  ) : null;
}

export function EvaluatorPlaygroundPromptPanel({
  evaluatorName,
  datasetId,
  alignmentDatasetId,
  onSaveEvaluator,
  onRunEvaluator,
  originalDatasetExamples,
}: EvaluatorPlaygroundPromptPanelProps) {
  return (
    <PlaygroundParentContextProvider>
      <SecretsProvider>
        <PlaygroundPanelContent
          evaluatorName={evaluatorName}
          datasetId={datasetId}
          alignmentDatasetId={alignmentDatasetId}
          onSaveEvaluator={onSaveEvaluator}
          onRunEvaluator={onRunEvaluator}
          originalDatasetExamples={originalDatasetExamples}
        />
      </SecretsProvider>
    </PlaygroundParentContextProvider>
  );
}
