import { XMarkIcon } from '@heroicons/react/24/outline';

import { useRef } from 'react';

import { cn } from '@/utils/tailwind';

interface EvaluatorColumnProps {
  exampleId: string;
  activeValue: number | null;
  onChange: (exampleId: string, value: number | null) => void;
  disabled?: boolean;
}

export const BinarySelector = ({
  exampleId,
  activeValue,
  onChange,
  disabled,
}: EvaluatorColumnProps) => {
  const categories = [{ value: 0 }, { value: 1 }];

  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div
      id={`select-evaluator-${exampleId}`}
      className={cn(
        'flex items-center divide-x divide-secondary overflow-hidden rounded-md border border-secondary focus-within:border-brand'
      )}
      tabIndex={-1}
      ref={containerRef}
    >
      {categories.map((category, idx) => (
        <button
          type="button"
          key={category.value}
          className={cn(
            'flex min-h-[30px] min-w-[30px] items-center justify-center px-2 py-1 text-sm outline-none',
            category.value === activeValue
              ? 'bg-brand-secondary text-brand-secondary'
              : 'bg-transparent'
          )}
          disabled={disabled}
          onClick={() =>
            activeValue === category.value
              ? onChange(exampleId, null)
              : onChange(exampleId, category.value)
          }
        >
          {idx}
        </button>
      ))}

      {activeValue != null ? (
        <div className="flex min-h-[30px] min-w-[30px] items-center justify-center">
          <button
            type="button"
            id={`clear-evaluator-${exampleId}`}
            className="rounded-sm p-1 outline-none transition-colors focus-within:bg-secondary hover:bg-secondary active:bg-secondary disabled:bg-transparent disabled:opacity-50"
            disabled={disabled}
            onClick={() => onChange(exampleId, null)}
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      ) : null}
    </div>
  );
};
