import { ArrowRightIcon } from '@heroicons/react/20/solid';
import { Button, CircularProgress, Input } from '@mui/joy';

import { useEffect, useMemo, useState } from 'react';

import { DatasetSessionCompareTable } from '@/Pages/DatasetSessionCompare/components/DatasetSessionCompareTable';
import { ExperimentRunDetails } from '@/Pages/DatasetSessionCompare/components/ExperimentRunDetails';
import { usePlaygroundStore } from '@/Pages/Playground/PlaygroundHome/hooks/usePlaygroundStore';
import { emulateNativeClick } from '@/components/DataGrid.utils';
import {
  useBulkExampleCreate,
  useBulkExampleUpdate,
  useDeleteExample,
  useExamples,
  useSessions,
} from '@/hooks/useSwr';
import TraceExpandIcon from '@/icons/TraceExpandIcon.svg?react';
import {
  BulkCreateExampleBody,
  ExampleSchemaWithRuns,
  ExampleSchemaWithRunsAndOptionalFields,
  ExampleUpdateSchema,
  RunSchema,
} from '@/types/schema';

import { evaluatorDisplaySettingsState } from '../constants';
import { BinarySelector } from './BinarySelector';
import { EvaluatorPlaygroundPromptPanel } from './EvaluatorPlaygroundPromptPanel';

// Type for our evaluation data
export interface EvaluationData {
  exampleId: string;
  score: number | null;
}

// Props for the custom table wrapper
interface EvaluatorSessionCompareTableProps {
  evaluatorName: string | undefined;
  onChangeEvaluatorName: (name: string) => void;
  onAlignEvaluator?: (
    evaluations: Map<string, number>,
    headerText: string,
    examples: any[]
  ) => void;

  // Props to pass through to DatasetSessionCompareTable
  datasetId?: string;
  columns: { sessionId: string }[];
  sessions?: ReturnType<typeof useSessions>;
  examples?: ExampleSchemaWithRuns[];
  areExamplesLoading: boolean;
  hasMoreToLoad: boolean;
  isNextPageValidating: boolean;
  setExamplesSize: (f: (size: number) => number) => void;
  mutateInfiniteExamplesSwr: () => void;

  // New props for alignment mode
  isAlignmentMode?: boolean;
  alignmentDatasetId?: string;
  isAligningEvaluator?: boolean;
}

export const EvaluatorSessionCompareTable = ({
  evaluatorName,
  onChangeEvaluatorName,
  onAlignEvaluator,
  datasetId,
  columns,
  sessions,
  examples,
  areExamplesLoading,
  hasMoreToLoad,
  isNextPageValidating,
  setExamplesSize,
  mutateInfiniteExamplesSwr,
  isAlignmentMode = false,
  alignmentDatasetId = '',
  isAligningEvaluator = false,
}: EvaluatorSessionCompareTableProps) => {
  // State for evaluator column header text
  const { data: alignmentDatasetExamples } = useExamples({
    dataset: alignmentDatasetId,
  });
  const alignmentExampleIdToExampleId = Object.fromEntries(
    alignmentDatasetExamples?.map((e) => {
      return [e.id, e.metadata?.source_example_id];
    }) ?? []
  );
  const exampleIdToAlignmentExampleId = Object.fromEntries(
    alignmentDatasetExamples?.map((e) => {
      return [e.metadata?.source_example_id, e.id];
    }) ?? []
  );
  const { trigger: updateExamples } = useBulkExampleUpdate({});
  const deleteExample = useDeleteExample({});
  const { trigger: addExamples } = useBulkExampleCreate({
    datasetDataType: 'kv',
  });

  // State for evaluation scores (0 or 1) for each example
  const [evaluations, setEvaluations] = useState<Map<string, number | null>>(
    new Map()
  );
  const prompts = usePlaygroundStore((state) => state.prompts);

  const [lastSavedEvaluations, setLastSavedEvaluations] = useState<
    Map<string, number | null>
  >(new Map());

  // We save examples when we switch to alignment mode, so we need to save the state there
  useEffect(() => {
    setLastSavedEvaluations(evaluations);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAlignmentMode]);

  const alignmentResults: Record<string, boolean> = useMemo(
    () =>
      evaluatorName
        ? Object.fromEntries(
            prompts[0]?.runs?.map((r) => {
              return r.reference_example_id
                ? [
                    alignmentExampleIdToExampleId[r.reference_example_id],
                    r.outputs?.output?.[evaluatorName] ===
                      evaluations.get(
                        alignmentExampleIdToExampleId[r.reference_example_id]
                      ),
                  ]
                : [];
            }) ?? []
          )
        : {},
    [evaluatorName, prompts, alignmentExampleIdToExampleId, evaluations]
  );

  const setLoadingState = usePlaygroundStore((state) => state.setLoadingState);

  // Any example that exists in the lastSavedEvaluations map but not in the current evaluations map
  // should be deleted
  const examplesToDelete = useMemo(() => {
    return Array.from(lastSavedEvaluations.keys())
      .filter((exampleId) => !evaluations.has(exampleId))
      .map((exampleId) => exampleIdToAlignmentExampleId[exampleId]);
  }, [lastSavedEvaluations, evaluations, exampleIdToAlignmentExampleId]);

  // Any example that exists in the current evaluations map but not in the lastSavedEvaluations map
  // should be added
  const examplesToAdd = useMemo<BulkCreateExampleBody['examples']>(() => {
    return Array.from(evaluations.keys())
      .filter((exampleId) => !lastSavedEvaluations.has(exampleId))
      .map((exampleId) => {
        const example = examples?.find((e) => e.id === exampleId);
        if (!example) return null;

        return {
          inputs: JSON.stringify({
            input: example.inputs,
            referenceOutput: example.outputs,
            output: example.runs[0]?.outputs,
          }),
          outputs: JSON.stringify({
            [evaluatorName ?? '']: evaluations.get(exampleId),
          }),
          metadata: JSON.stringify({
            original_dataset_id: datasetId,
            source_example_id: exampleId,
          }),
        };
      })
      .filter((item): item is BulkCreateExampleBody['examples'][number] =>
        Boolean(item)
      );
  }, [evaluations, lastSavedEvaluations, examples, evaluatorName, datasetId]);

  // Any example that exists in both the current evaluations map and the lastSavedEvaluations map AND has a different score
  // should be updated
  const examplesToUpdate = useMemo<
    ExampleUpdateSchema & { id?: string }[]
  >(() => {
    return Array.from(evaluations.keys())
      .filter(
        (exampleId) =>
          lastSavedEvaluations.has(exampleId) &&
          evaluations.get(exampleId) !== lastSavedEvaluations.get(exampleId)
      )
      .map((exampleId) => {
        const example = examples?.find((e) => e.id === exampleId);
        if (!example) return null;

        return {
          id: exampleIdToAlignmentExampleId[exampleId],
          inputs: {
            input: example.inputs,
            referenceOutput: example.outputs,
            output: example.runs[0]?.outputs,
          },
          outputs: {
            [evaluatorName ?? '']: evaluations.get(exampleId),
          },
        };
      })
      .filter((item): item is NonNullable<typeof item> => Boolean(item));
  }, [
    evaluations,
    lastSavedEvaluations,
    examples,
    exampleIdToAlignmentExampleId,
    evaluatorName,
  ]);

  const onRunEvaluator = async () => {
    setLoadingState('loading');
    for (const exampleId of examplesToDelete) {
      await deleteExample.trigger({
        templateUrlParams: { exampleId },
      });
    }
    if (examplesToUpdate.length > 0) {
      await updateExamples({
        json: examplesToUpdate,
      });
    }
    if (examplesToAdd.length > 0) {
      await addExamples({
        dataset_id: alignmentDatasetId,
        examples: examplesToAdd,
      });
    }
    setLastSavedEvaluations(evaluations);
  };

  /**
   * Handles changes to evaluation scores for examples in the comparison table.
   * In alignment mode, this function manages the state of examples to be added, updated, or deleted
   * based on the evaluation changes. It also maintains a map of evaluations (which are the human annotation scores)
   */
  const handleEvaluationChange = (exampleId: string, value: number | null) => {
    // Update the evaluations map and notify parent component
    setEvaluations((prev) => {
      const newMap = new Map(prev);
      if (value === null) {
        // Remove evaluation if value is null
        newMap.delete(exampleId);
      } else {
        // Set new evaluation value
        newMap.set(exampleId, value);
      }

      return newMap;
    });
  };

  // Generate a custom row with our binary selector component
  const renderEvaluatorColumn = (
    example: ExampleSchemaWithRunsAndOptionalFields
  ) => (
    <td className="border-b border-r border-secondary p-2 align-top">
      <div className="flex justify-center p-2">
        <BinarySelector
          exampleId={example.id}
          activeValue={evaluations.get(example.id) ?? null}
          onChange={handleEvaluationChange}
        />
      </div>
    </td>
  );

  const [openRunId, setOpenRunId] = useState<string | null>(null);

  const handleTraceButtonClick = (
    e: React.MouseEvent<HTMLButtonElement>,
    run: RunSchema
  ) => {
    const targetUrl = run.app_path;

    if (!emulateNativeClick(targetUrl, e.nativeEvent)) {
      setOpenRunId(run.id);
    }
  };

  const loadingState = usePlaygroundStore((state) => state.loadingState);

  // Render the alignment result column
  const renderAlignmentColumn = (example: any) => {
    return (
      <td className="border-b border-r border-secondary p-2 align-top">
        <div className="flex justify-center p-2">
          {alignmentResults[example.id] != null &&
          evaluations.get(example.id) !== undefined ? (
            <>
              <div
                className={`rounded-full p-1 px-3 text-sm ${
                  alignmentResults[example.id]
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100'
                }`}
              >
                {alignmentResults[example.id] ? 'Aligned' : 'Not Aligned'}
              </div>
              <button
                type="button"
                className={
                  'spacing-md ml-1 flex items-center gap-0.5 rounded border border-zinc-700 hover:bg-secondary active:bg-tertiary'
                }
                onClick={(e) => {
                  const runToOpen = prompts[0].runs?.find(
                    (r) =>
                      r.reference_example_id ===
                      exampleIdToAlignmentExampleId[example.id]
                  );
                  if (runToOpen) {
                    handleTraceButtonClick(e, runToOpen);
                  }
                }}
              >
                <span
                  className={
                    'line-clamp-1 whitespace-normal break-words break-all'
                  }
                >
                  <TraceExpandIcon className="h-6 w-6" />
                </span>
              </button>
            </>
          ) : evaluations.get(example.id) !== undefined &&
            isAlignmentMode &&
            loadingState !== 'loading' ? (
            <div className="rounded-full bg-yellow-100 p-1 px-3 text-sm text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100">
              Run alignment test to see score
            </div>
          ) : loadingState === 'loading' ? (
            <CircularProgress
              sx={{
                '--CircularProgress-size': '12px',
                '--CircularProgress-progressThickness': '2px',
                '--CircularProgress-trackThickness': '2px',
                transform: 'scaleX(-1)',
              }}
            />
          ) : (
            <div className="text-sm text-quaternary">-</div>
          )}
        </div>
      </td>
    );
  };

  const examplesWithAlignmentResults = useMemo(
    () =>
      (examples ?? []).filter((example) => {
        return (
          alignmentResults[example.id] != null &&
          evaluations.get(example.id) !== undefined
        );
      }),
    [examples, alignmentResults, evaluations]
  );

  const averageAlignmentScore =
    examplesWithAlignmentResults.length > 0
      ? Object.values(alignmentResults).reduce(
          (prev, curr) => prev + (curr ? 1 : 0),
          0
        ) / examplesWithAlignmentResults.length
      : 'None';

  const renderCustomExtraHeaderColumn = useMemo(() => {
    return (
      <>
        <th className="sticky top-0 whitespace-nowrap border-y border-secondary bg-secondary text-left text-sm font-semibold capitalize">
          {evaluatorName ? (
            <div className="px-4 py-3">{evaluatorName}</div>
          ) : (
            <div className="px-4 py-3 text-tertiary">Evaluator Name</div>
          )}
        </th>
        {isAlignmentMode && (
          <th className="sticky top-0 flex flex-col items-start gap-1 whitespace-nowrap border-y border-secondary bg-secondary px-4 py-3 text-left text-sm font-semibold capitalize">
            <div className="">Alignment</div>
            {averageAlignmentScore !== 'None' && (
              <div>{(averageAlignmentScore * 100).toFixed(0)}%</div>
            )}
          </th>
        )}
      </>
    );
  }, [evaluatorName, isAlignmentMode, averageAlignmentScore]);

  return (
    <div className="flex flex-col">
      {/* Show input and Align Evaluator button when not in alignment mode */}
      {!isAlignmentMode ? (
        <div className="mb-4 flex items-center gap-2">
          <Input
            placeholder="Evaluator Name"
            value={evaluatorName}
            onChange={(e) => onChangeEvaluatorName(e.target.value)}
            className="max-w-xs"
            size="sm"
            variant="outlined"
          />
          <Button
            size="sm"
            color="primary"
            loading={isAligningEvaluator}
            startDecorator={<ArrowRightIcon className="h-5 w-5" />}
            onClick={() => {
              if (!evaluatorName) {
                return;
              }
              // Only include evaluations with a score (0 or 1)
              const filteredEvaluations = new Map(
                Array.from(evaluations.entries()).filter(
                  ([_, value]) => value !== null
                )
              ) as Map<string, number>;

              if (filteredEvaluations.size === 0) {
                alert('Please score at least one example before aligning.');
                return;
              }

              onAlignEvaluator?.(
                filteredEvaluations,
                evaluatorName,
                examples ?? []
              );
            }}
            disabled={
              !examples?.length ||
              Array.from(evaluations.entries()).filter(([_, v]) => v !== null)
                .length === 0 ||
              !evaluatorName ||
              evaluatorName === ''
            }
          >
            Align Evaluator
          </Button>
        </div>
      ) : null}

      {/* Show the Playground panel when in alignment mode */}
      {isAlignmentMode && alignmentDatasetId && evaluatorName && (
        <div className="mb-4">
          {/* Import and use the EvaluatorPlaygroundPromptPanel component */}
          <EvaluatorPlaygroundPromptPanel
            evaluatorName={evaluatorName}
            datasetId={datasetId || ''}
            alignmentDatasetId={alignmentDatasetId}
            originalDatasetExamples={examples}
            onRunEvaluator={onRunEvaluator}
          />
        </div>
      )}

      <DatasetSessionCompareTable
        datasetId={datasetId}
        columns={columns}
        sessions={sessions}
        examples={examples}
        areExamplesLoading={areExamplesLoading}
        hasMoreToLoad={hasMoreToLoad}
        isNextPageValidating={isNextPageValidating}
        setExamplesSize={setExamplesSize}
        mutateInfiniteExamplesSwr={mutateInfiniteExamplesSwr}
        displaySettingsState={evaluatorDisplaySettingsState}
        datadogPageName="generate_evaluator"
        filteringRegressionsOnSession={null}
        regressionsFilteringToImprovements={false}
        hoveredSession={null}
        setHoveredSession={() => {}}
        // Custom props to inject our evaluator column(s)
        customExtraHeaderColumn={renderCustomExtraHeaderColumn}
        customExtraTableCell={(example) => (
          <>
            {renderEvaluatorColumn(example)}
            {isAlignmentMode && renderAlignmentColumn(example)}
          </>
        )}
        useWindowScroll={true}
      />
      <ExperimentRunDetails runId={openRunId} setOpenRunId={setOpenRunId} />
    </div>
  );
};
