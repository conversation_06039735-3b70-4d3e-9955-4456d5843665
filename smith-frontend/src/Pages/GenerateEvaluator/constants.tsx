import {
  DatasetSessionCompareSettings,
  TextDisplayMode,
} from '../DatasetSessionCompare/constants';

export const evaluatorDisplaySettingsState: DatasetSessionCompareSettings = {
  // Force Full mode
  textDisplayMode: TextDisplayMode.FULL,
  setTextDisplayMode: () => {}, // No-op since we don't want this to change

  hideMetrics: true,
  setHideMetrics: () => {},
  hideFeedback: false,
  setHideFeedback: () => {},

  // Show only the columns we want
  isReferenceInputHidden: false,
  setIsReferenceInputHidden: () => {}, // No-op

  isReferenceOutputHidden: false,
  setIsReferenceOutputHidden: () => {},

  isHeatmapVisible: false,
  setIsHeatmapVisible: () => {},

  barChartSpecs: null,
  canResetDefaults: false,
  onResetDefaults: () => {},

  isAttachmentsHidden: true, // Hide attachments
  setIsAttachmentsHidden: () => {},

  language: 'json' as any,
  setLanguage: () => {},

  selectedCharts: [],
  setSelectedCharts: () => {},

  hiddenFeedbackColumns: [],
  setHiddenFeedbackColumns: () => {},

  hiddenMetricsColumns: ['latency', 'status', 'tokens', 'cost'],
  setHiddenMetricsColumns: () => {},
};
