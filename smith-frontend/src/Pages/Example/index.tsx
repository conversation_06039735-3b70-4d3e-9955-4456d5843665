import { Button } from '@mui/joy';
import Box from '@mui/joy/Box';
import LinearProgress from '@mui/joy/LinearProgress';

import { useMemo, useRef } from 'react';
import {
  Link,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';

import Breadcrumbs from '@/components/Breadcrumbs';
import { CopyInlineLink } from '@/components/CopyInlineLink';
import { ScrollSpy } from '@/components/ScrollSpy/ScrollSpy';
import { useScrollSpyState } from '@/components/ScrollSpy/hooks/useScrollSpyState';
import {
  TabGroup,
  TabLabel,
  TabList,
  TabPanel,
  TabPanels,
  useTab,
} from '@/components/Tabs';
import { usePermissions } from '@/hooks/usePermissions';
import { useSegmentEventWrapper } from '@/hooks/useSegmentEventWrapper';
import {
  appPublicDatasetsPath,
  appPublicPath,
  appRunPath,
  appSessionPath,
} from '@/utils/constants';

import { ExampleCrudPaneWithButton } from '../../components/ExampleCrudPane/ExampleCrudPaneWithButton';
import { PageTitle } from '../../components/PageTitle';
import { UncontrolledRunsTable } from '../../components/RunsTable/UncontrolledRunsTable';
import {
  useDataset,
  useDatasetSingleVersion,
  useRun,
} from '../../hooks/useSwr';
import { COLUMN_VISIBILITY } from '../Project/constants';
import Attachments from '../Run/components/Attachments';
import { RunInputsAndOutputs } from '../Run/components/RunInputsAndOutputs';
import { getAttachments } from '../Run/utils/multimodalRunUtils';
import { ExampleMetadataDisplay } from './components/ExampleMetadataDisplay';
import { useExampleData } from './hooks/useExampleData';
import { transformRuleGeneratedSchema } from './utils';

const defaultColumnVisibility = {
  ...COLUMN_VISIBILITY.eval,
  reference_example_id: false,
  outputs: true,
  name: false,
};

const formatter = new Intl.DateTimeFormat('en-US', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  timeZoneName: 'short',
});

function ExampleSourceRunButton(props: { runId: string }) {
  const run = useRun({ id: props.runId });

  if (!run.data) return null;
  return (
    <Link to={run.data.app_path}>
      <Button type="button" size="sm" color="neutral" variant="outlined">
        Open source run
      </Button>
    </Link>
  );
}
export const Example = (props: {
  exampleId?: string | null;
  datasetId?: string;
  onMutate?: () => void;
}) => {
  const params = useParams();
  const navigate = useNavigate();

  const exampleId = props.exampleId ?? params.exampleId;
  const datasetId = props.datasetId ?? params.datasetId;
  const datasetShareToken = params.datasetShareToken;

  const [searchParams] = useSearchParams();
  const asOfRaw = searchParams.get('as_of');
  const asOf = asOfRaw ? decodeURIComponent(asOfRaw) : null;
  const isReadOnly = asOf != null;
  const { authorize } = usePermissions();

  const {
    data: mExample,
    isLoading: exampleIsLoading,
    mutate: exampleMutate,
  } = useExampleData({
    exampleId,
  });

  const { data: currentVersionSwr } = useDatasetSingleVersion(
    datasetId ?? null,
    !datasetShareToken && asOf
      ? {
          as_of: asOf,
        }
      : null
  );
  const currentVersion = asOf ? currentVersionSwr : null;
  const currentVersionTags = currentVersion?.tags ?? [];

  const dataset = useDataset(datasetId, { datasetShareToken });
  const example =
    dataset.data?.data_type !== 'kv'
      ? transformRuleGeneratedSchema(mExample)
      : mExample;

  const runsFilter = useMemo(
    () =>
      exampleId
        ? {
            reference_example: [exampleId],
            is_root: true,
          }
        : null,
    [exampleId]
  );

  const formattedDate =
    currentVersion &&
    `As of ${formatter.format(new Date(currentVersion.as_of))}`;

  const attachments = getAttachments(example?.attachment_urls);

  const tabPanelRef = useRef<HTMLDivElement>(null);
  const selectedTab = useTab({ prefix: 'pane-' });
  const isExampleVisible = selectedTab === 0;
  const attachmentsRef = useRef<HTMLDivElement>(null);
  const inputsRef = useRef<HTMLDivElement>(null);
  const outputsRef = useRef<HTMLDivElement>(null);

  const scrollSpyElements = [
    ...(attachments.length > 0
      ? [
          {
            id: 'attachments',
            elementRef: attachmentsRef,
            title: 'Attachments',
          },
        ]
      : []),
    { id: 'input', elementRef: inputsRef, title: 'Inputs' },
    { id: 'output', elementRef: outputsRef, title: 'Outputs' },
  ];

  const scrollSpyState = useScrollSpyState(
    tabPanelRef,
    isExampleVisible,
    scrollSpyElements
  );

  const track = useSegmentEventWrapper('example_linked_runs_tab_click');
  const handleLinkedRunsTabClick = track(() => void 0);

  return (
    <div className="-mx-4 flex h-full flex-col overflow-x-hidden">
      <div className="flex flex-col px-4">
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <div className="flex flex-col">
            <PageTitle>
              {datasetShareToken ? 'Public Example:' : 'Example:'}{' '}
              {example?.name}
            </PageTitle>
            {example?.metadata?.dataset_split &&
              (!Array.isArray(example?.metadata?.dataset_split) ||
                example.metadata.dataset_split.length > 0) && (
                <div className="flex gap-3 text-lg font-normal">
                  <div className="font-semibold">Split:</div>
                  {!Array.isArray(example?.metadata?.dataset_split)
                    ? example?.metadata?.dataset_split
                    : example?.metadata?.dataset_split.join(', ')}
                </div>
              )}
          </div>

          {example && (
            <div className="flex items-center gap-2 whitespace-nowrap">
              <CopyInlineLink value={example.id}>Example ID</CopyInlineLink>

              <span />

              {example.source_run_id && (
                <ExampleSourceRunButton runId={example.source_run_id} />
              )}
              {authorize('datasets:update') && (
                <ExampleCrudPaneWithButton
                  key={exampleId}
                  example={example}
                  dataset={dataset.data}
                  onSuccess={() => {
                    props.onMutate?.();
                    exampleMutate();
                  }}
                  disabled={isReadOnly}
                />
              )}
            </div>
          )}
        </Box>
        {exampleIsLoading && (
          <div className="min-h-[6px]">
            <LinearProgress />
          </div>
        )}
        {currentVersion && (
          <div>
            <div className="text-lg">
              {currentVersionTags.length > 0
                ? `Version with tags: ${currentVersion.tags?.join(', ')}`
                : formattedDate}
            </div>
            <div className="flex gap-3">
              {currentVersionTags.length > 0 && (
                <div className="text-md text-tertiary">{formattedDate}</div>
              )}
            </div>
          </div>
        )}
      </div>
      <TabGroup setTabMethod="merge" prefix="pane-">
        <TabList className="mb-0 px-8">
          <TabLabel>Example</TabLabel>
          <TabLabel onClick={handleLinkedRunsTabClick}>Linked Runs</TabLabel>
          <TabLabel>Metadata</TabLabel>
        </TabList>
        <TabPanels className="relative m-0 flex h-full flex-col overflow-hidden p-0">
          <TabPanel
            className="overflow-y-auto overflow-x-visible p-2"
            onScroll={scrollSpyState.handleScroll}
            panelRef={tabPanelRef}
          >
            <div className="flex gap-2">
              <div className="flex flex-1 flex-col">
                {attachments.length > 0 && (
                  <Attachments
                    attachments={attachments}
                    className="pt-0"
                    gridClassName="grid max-h-[250px] grid-cols-2 overflow-y-auto"
                    attachmentsRef={attachmentsRef}
                  />
                )}
                {example && dataset.data && (
                  <RunInputsAndOutputs
                    run={{
                      id: example.id,
                      run_type: 'chain',
                      inputs: example.inputs,
                      outputs: example.outputs,
                    }}
                    inputsRef={inputsRef}
                    outputsRef={outputsRef}
                    variant="example"
                  />
                )}
              </div>
              <ScrollSpy {...scrollSpyState} elements={scrollSpyElements} />
            </div>
          </TabPanel>
          <TabPanel className="overflow-y-auto overflow-x-hidden p-4">
            <Box>
              <UncontrolledRunsTable
                tableDisplay="list"
                runsFilter={runsFilter}
                navigateToRun={
                  datasetShareToken
                    ? (runId, sessionId, trace_id, root_start_time) => {
                        navigate(
                          `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${sessionId}/${appSessionPath}/${appRunPath}/${runId}?trace_id=${trace_id}${
                            root_start_time && `&start_time=${root_start_time}`
                          }`
                        );
                      }
                    : undefined
                }
                defaultColumnVisibility={defaultColumnVisibility}
              />
            </Box>
          </TabPanel>
          <TabPanel className="overflow-y-auto overflow-x-hidden p-4">
            <Box>
              {example && dataset.data?.data_type && (
                <ExampleMetadataDisplay
                  metadata={example?.metadata}
                  dataType={dataset.data?.data_type}
                  readOnly={true}
                />
              )}
            </Box>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  );
};

export default function ExamplePage() {
  return (
    <div className="h-screen max-h-screen overflow-hidden px-4 pb-6 pt-3">
      <Breadcrumbs />
      <Example />
    </div>
  );
}
