import { SearchLgIcon } from '@langchain/untitled-ui-icons';

import { SearchIcon } from 'lucide-react';
import React, { ReactNode, forwardRef } from 'react';

import useToastOn429Error from '@/hooks/useToastOn429Error';
import {
  CustomChartExistingInfo,
  CustomChartSchema,
  CustomChartsSubSectionSchema,
  RunStatsGroupBy,
} from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { Chart } from './CustomChart';
import { ChartTimeFilter } from './hooks/useChartTimeFilter';
import { shouldShowEmptyState } from './utils/CustomChartsUtils.utils';
import { GRID_CHART_HEIGHT } from './utils/constants';

export const NoCharts = () => {
  return (
    <div className="mx-auto mt-36 flex w-[700px] flex-col gap-6 text-center">
      <div className="mx-auto rounded-lg border border-secondary">
        <SearchIcon size={48} className="p-3" />
      </div>
      <div className="flex flex-col gap-1.5">
        <span className="text-base font-bold">
          No charts in this dashboard yet
        </span>
        <p className="font-small mx-auto max-w-[400px] text-base text-[#70707B]">
          Start by creating a new chart
        </p>
      </div>
    </div>
  );
};

const getColumnSpan = (index: number, numCharts) => {
  if (numCharts % 2 === 1 && index === 0) {
    return 'col-span-2';
  }
  return 'col-span-1';
};

const ChartSection = forwardRef<
  HTMLDivElement,
  {
    sectionName: string;
    children: ReactNode;
  }
>(({ sectionName, children }, ref) => {
  return (
    <div ref={ref} className="flex flex-col">
      <h2 className="mx-5 mb-2 mt-4 text-lg font-semibold">{sectionName}</h2>
      {children}
    </div>
  );
});

type ChartsGridProps = {
  charts: CustomChartSchema[];
  sub_sections?: CustomChartsSubSectionSchema[];
  isLoading: boolean;
  mutateChartData: () => void;
  openExistingChart: (data: CustomChartExistingInfo) => void;
  timeFilter: ChartTimeFilter;
  hideProjectNames?: boolean;
  hideExpandedView?: boolean;
  forceV1Tooltip?: boolean;
  readOnly?: boolean;
  error?: Error;
  className?: string;
  emptyState?: JSX.Element;
  isPrebuilt?: boolean;
  sectionRefs?: Record<string, React.RefObject<HTMLDivElement>>;
  groupBySelection?: RunStatsGroupBy;
  seriesColorsMap: Record<string, string>;
};

export const ChartsGrid = (
  props: ChartsGridProps & { dashboardId?: string }
) => {
  const { charts, dashboardId, isLoading, error, emptyState } = props;
  const showEmptyState = shouldShowEmptyState(
    charts,
    props.sub_sections,
    dashboardId,
    isLoading
  );
  const { is429Error } = useToastOn429Error();
  return (
    <div className="relative flex flex-col gap-4">
      {error ? (
        <div className="pl-6 text-error">
          Error loading charts:{' '}
          {is429Error(error)
            ? 'Too many requests. Please try again later.'
            : error.message}
        </div>
      ) : showEmptyState || !dashboardId ? (
        emptyState ?? <NoCharts />
      ) : (
        <ChartsGridLayout {...props} dashboardId={dashboardId} />
      )}
    </div>
  );
};

const SectionNoData = ({ isFeedback = false }: { isFeedback?: boolean }) => {
  return (
    <div
      className="col-span-2 flex flex-col items-center justify-center gap-4 rounded-lg bg-secondary p-4 py-8"
      style={{ minHeight: GRID_CHART_HEIGHT }}
    >
      <div className="rounded-full bg-tertiary p-2">
        <SearchLgIcon className="size-6 text-disabled" />
      </div>
      <span className="max-w-[600px] text-center text-sm text-quaternary">
        {isFeedback ? (
          <>
            No feedback scores to show. <br></br> Measure application
            performance by configuring{' '}
            <a
              href="https://docs.smith.langchain.com/observability/how_to_guides/online_evaluations"
              target="_blank"
              rel="noopener noreferrer"
              className="text-brand-secondary underline"
            >
              online evaluators
            </a>
            .
          </>
        ) : (
          'No charts in this section'
        )}
      </span>
    </div>
  );
};

const ChartsGridLayout = ({
  charts,
  sub_sections,
  dashboardId,
  mutateChartData,
  openExistingChart,
  timeFilter,
  hideProjectNames = false,
  forceV1Tooltip = false,
  hideExpandedView = false,
  readOnly = false,
  className,
  sectionRefs,
  isPrebuilt = false,
  groupBySelection,
  isLoading,
  seriesColorsMap,
}: ChartsGridProps & { dashboardId: string }) => {
  const numCharts = charts.length;

  if (shouldShowEmptyState(charts, sub_sections, dashboardId)) {
    return null;
  }

  if (isPrebuilt && sub_sections) {
    return (
      <div className="flex flex-col gap-0 pb-6">
        {sub_sections.map((section) => {
          const charts = section.charts.map((chart, idx) => {
            return (
              <Chart
                key={chart.id}
                chart={chart}
                dashboardId={dashboardId}
                customClass={getColumnSpan(idx, section.charts.length)}
                openExistingChart={openExistingChart}
                mutateChartData={mutateChartData}
                timeFilter={timeFilter}
                hideProjectNames={hideProjectNames}
                hideExpandedView={hideExpandedView}
                readOnly={readOnly || isPrebuilt}
                isLastColumn={(section.charts.length - 1) % 2 === idx % 2}
                forceV1Tooltip={forceV1Tooltip}
                groupBySelection={groupBySelection}
                isLoading={isLoading}
                seriesColorsMap={seriesColorsMap}
              />
            );
          });
          return (
            <ChartSection
              key={section.title}
              sectionName={section.title}
              ref={sectionRefs?.[section.id]}
            >
              <div
                className={cn('grid grid-cols-2 gap-4 px-4 pb-4', className)}
              >
                {charts.length > 0 ? (
                  charts
                ) : (
                  <SectionNoData
                    isFeedback={section.title === 'Feedback Scores'}
                  />
                )}
              </div>
            </ChartSection>
          );
        })}
      </div>
    );
  }
  return (
    <div className={cn('mb-6 grid grid-cols-2 gap-4 px-4 pb-4', className)}>
      {charts
        .sort((a, b) => a.index - b.index)
        .map((chart, idx) => (
          <Chart
            key={chart.id}
            chart={chart}
            dashboardId={dashboardId}
            customClass={getColumnSpan(idx, numCharts)}
            openExistingChart={openExistingChart}
            mutateChartData={mutateChartData}
            timeFilter={timeFilter}
            hideProjectNames={hideProjectNames}
            hideExpandedView={hideExpandedView}
            readOnly={readOnly || isPrebuilt}
            isLastColumn={(numCharts - 1) % 2 === idx % 2}
            forceV1Tooltip={forceV1Tooltip}
            isLoading={isLoading}
            seriesColorsMap={seriesColorsMap}
          />
        ))}
    </div>
  );
};
