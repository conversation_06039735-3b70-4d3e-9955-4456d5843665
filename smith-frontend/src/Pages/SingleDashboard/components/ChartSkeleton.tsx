import { BarChart10Icon } from '@langchain/untitled-ui-icons';

import { cn } from '@/utils/tailwind';

interface ChartSkeletonProps {
  className?: string;
  height?: string;
}

export function ChartSkeleton({ className }: ChartSkeletonProps) {
  return (
    <div
      className={cn(
        'w-full animate-pulse rounded-lg bg-tertiary p-4',
        className
      )}
    >
      <div className={cn('relative h-full w-full')}>
        <div className="absolute inset-0 flex items-center justify-center gap-2 text-quaternary">
          <BarChart10Icon className="size-8" /> Loading chart
        </div>
      </div>
    </div>
  );
}
