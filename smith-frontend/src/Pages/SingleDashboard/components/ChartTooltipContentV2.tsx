import { GitBranch01Icon } from '@langchain/untitled-ui-icons';
import { Tooltip } from '@mui/joy';
import { timeFormat } from '@visx/vendor/d3-time-format';

import { Duration } from 'dayjs/plugin/duration';
import { useMemo } from 'react';

import { FeedbackChip } from '@/components/FeedbackChips';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useOrganizationId } from '@/hooks/useSwr';
import {
  CustomChartDataPoint,
  CustomChartDataSchema,
  CustomChartMetric,
  CustomChartPreviewSchema,
  CustomChartSchema,
} from '@/types/schema';
import { cn } from '@/utils/tailwind';

import {
  canNavigateToRunsTable,
  createTimeModel,
  ensureUTCFormatTimestamp,
  generateChartPointRunsUrl,
  getCategories,
  getValueFromDataPoint,
} from '../utils/CustomChartsUtils.utils';
import {
  COST_METRICS,
  GRID_CHART_MARGIN,
  TOOLTIP_WIDTH,
  currencyFormatter,
  numberFormatter,
} from '../utils/constants';
import { TooltipDataPoint } from '../utils/types';
import { TruncatedText } from './TruncatedText';

export const TOOLTIP_ITEM_MAX_LENGTH = 24;
const MIN_PREVIEW_TOOLTIP_TOP = -100;

export const ChartTooltipV2 = ({
  tooltipFrozen,
  setTooltipFrozen,
  chart,
  totalChartDuration,
  colorScale,
  tooltipLeft,
  tooltipTop,
  isLastColumn,
  dimensions,
  tooltipData,
  tooltipRef,
}: {
  tooltipFrozen: boolean;
  setTooltipFrozen: (value: boolean) => void;
  chart: CustomChartSchema | CustomChartPreviewSchema;
  totalChartDuration: Duration;
  colorScale: (category: string) => string;
  tooltipLeft?: number;
  tooltipTop?: number;
  isLastColumn?: boolean;
  dimensions: {
    width: number;
    height: number;
  };
  tooltipData:
    | { nearestDatum: CustomChartDataSchema; datumByKey: string }
    | Record<string, never>;
  tooltipRef: React.RefObject<HTMLDivElement>;
}) => {
  const { isDarkMode } = useColorScheme();
  const showTooltipLeft =
    isLastColumn &&
    tooltipLeft &&
    tooltipLeft > dimensions.width - TOOLTIP_WIDTH;
  const isPreview = !('id' in chart);

  return (
    <>
      {/* Create a dotted line on the x of the tooltip */}
      <div
        style={{
          position: 'absolute',
          left: `${tooltipLeft}px`,
          top: GRID_CHART_MARGIN.top,
          width: 0,
          zIndex: 5,
          height: dimensions.height,
          borderLeft: `1px dashed ${
            isDarkMode ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.5)'
          }`,
          pointerEvents: 'none',
        }}
      >
        {tooltipFrozen && (
          <>
            {/* Top triangle pointing down */}
            <div style={{ position: 'absolute', top: '-7px', left: '-5px' }}>
              <svg
                width="9"
                height="7"
                viewBox="0 0 9 7"
                style={{ display: 'block' }}
              >
                <polygon
                  points="0,0 9,0 4.5,7"
                  fill={
                    isDarkMode ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)'
                  }
                />
              </svg>
            </div>

            {/* bottom triangle pointing up */}
            <div style={{ position: 'absolute', bottom: '-8px', left: '-5px' }}>
              <svg
                width="9"
                height="8"
                viewBox="0 0 9 8"
                style={{ display: 'block' }}
              >
                <polygon
                  points="0,8 9,8 4.5,0"
                  fill={
                    isDarkMode ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)'
                  }
                />
              </svg>
            </div>
          </>
        )}
      </div>
      <div
        className="shrink-0 bg-primary"
        style={{
          position: 'absolute',
          ...(showTooltipLeft
            ? { right: `${dimensions.width - (tooltipLeft ?? 0) + 16}px` }
            : { left: `${(tooltipLeft ?? 0) + 16}px` }),
          top: isPreview
            ? `${Math.max(
                (tooltipTop ?? 0) - (tooltipRef.current?.clientHeight ?? 0) / 2,
                MIN_PREVIEW_TOOLTIP_TOP
              )}px`
            : `${
                (tooltipTop ?? 0) - (tooltipRef.current?.clientHeight ?? 0) / 2
              }px`,
          zIndex: 10,
          pointerEvents: tooltipFrozen ? 'all' : 'none',
          padding: '6px 6px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          color: isDarkMode ? 'white' : 'black',
          flexShrink: 0,
          width: 'max-content',
        }}
        ref={tooltipRef}
      >
        {renderCustomTooltipContent({
          tooltipData,
          chart,
          colorScale,
          tooltipFrozen,
          setTooltipFrozen,
          totalChartDuration,
        })}
      </div>
    </>
  );
};

// Helper function to render tooltip content based on your existing logic
const renderCustomTooltipContent = ({
  tooltipData,
  chart,
  colorScale,
  tooltipFrozen,
  setTooltipFrozen,
  totalChartDuration,
}: {
  tooltipData:
    | { nearestDatum: CustomChartDataSchema; datumByKey: string }
    | Record<string, never>;
  chart: CustomChartSchema | CustomChartPreviewSchema;
  colorScale: (category: string) => string;
  tooltipFrozen: boolean;
  setTooltipFrozen: (value: boolean) => void;
  totalChartDuration: Duration;
}) => {
  // Extract datum from tooltip data
  if (!tooltipData || !('nearestDatum' in tooltipData))
    return <div>No data</div>;
  const datum = tooltipData.nearestDatum;

  const timeModel = createTimeModel(totalChartDuration, datum.timestamp, chart);

  // Regular metrics rendering logic
  const seriesData = chart.series.map((series) => {
    const dataPoint = chart.data.find(
      (d) => d.series_id === series.id && d.timestamp === datum.timestamp
    );
    return {
      id: series.id,
      name: series.name,
      datum: dataPoint ?? null,
      color: colorScale(series.id),
      series: series,
    };
  });

  return (
    <div>
      <ChartTooltipContentV2
        timeModel={timeModel}
        seriesData={seriesData}
        metric={chart.series[0].metric}
        valueAccessor={getValueFromDataPoint}
        tooltipFrozen={tooltipFrozen}
        setTooltipFrozen={setTooltipFrozen}
        chart={chart}
        totalChartDuration={totalChartDuration}
        colorScale={colorScale}
      />
    </div>
  );
};

const ChartTooltipContentV2 = ({
  timeModel,
  seriesData,
  metric,
  valueAccessor,
  tooltipFrozen,
  setTooltipFrozen,
  chart,
  totalChartDuration,
  colorScale,
}: {
  timeModel: {
    start_time: string;
    end_time: string;
  };
  seriesData: TooltipDataPoint[];
  metric: CustomChartMetric;
  valueAccessor: (
    d: CustomChartDataPoint,
    metric: CustomChartMetric,
    feedbackValue?: string
  ) => number | null;
  tooltipFrozen: boolean;
  setTooltipFrozen: (value: boolean) => void;
  chart: CustomChartSchema | CustomChartPreviewSchema;
  totalChartDuration: Duration;
  colorScale: (category: string) => string;
}) => {
  const organizationId = useOrganizationId();
  const feedbackKey = chart.series[0].feedback_key;
  const isCategorical =
    chart.series[0].metric === 'feedback_values' && feedbackKey;
  const isPreview = !('id' in chart);

  const sortedSeriesData = useMemo(() => {
    if (isCategorical) {
      const allCategories = getCategories(chart.data, feedbackKey!);
      return allCategories
        .map((category) => {
          const dataPointValue = seriesData[0].datum?.value;
          if (!dataPointValue) {
            return null;
          }
          return {
            id: category,
            category: category,
            series: chart.series[0],
            datum: seriesData[0].datum,
            name: chart.series[0].name,
            color: colorScale(category),
          };
        })
        .filter((series) => series != null);
    } else {
      return seriesData;
    }
  }, [
    chart.data,
    chart.series,
    colorScale,
    feedbackKey,
    isCategorical,
    seriesData,
  ]);

  const startDate = new Date(ensureUTCFormatTimestamp(timeModel.start_time));
  const endDate = new Date(ensureUTCFormatTimestamp(timeModel.end_time));
  const formattedStartDateDay = timeFormat('%b %d, %Y')(startDate);
  const formattedEndDateDay = timeFormat('%b %d, %Y')(endDate);
  const formattedStartDateTime = timeFormat('%-I:%M %p')(startDate);
  const formattedEndDateTime = timeFormat('%-I:%M %p')(endDate);

  return (
    <div
      className="flex flex-col gap-[7px]"
      style={{ width: 'auto', maxWidth: TOOLTIP_WIDTH, flexShrink: 0 }}
    >
      <div className="z-50 mb-0.5 flex shrink-0 flex-col gap-2 px-2 pt-3 text-sm font-medium leading-none">
        <div className="flex gap-1">
          <span className="font-medium">{formattedStartDateDay}</span>{' '}
          <span className="text-quaternary">{formattedStartDateTime} -</span>
        </div>
        <div className="flex gap-1">
          <span className="font-medium">{formattedEndDateDay}</span>{' '}
          <span className="text-quaternary">{formattedEndDateTime}</span>
        </div>
      </div>
      {isCategorical && (
        <div className="mt-1 flex px-2">
          <FeedbackChip feedback_key={feedbackKey} />
        </div>
      )}
      <div
        className={cn(
          'flex flex-col overflow-y-auto',
          isPreview ? 'max-h-[600px]' : 'max-h-[400px]'
        )}
      >
        {sortedSeriesData.map((series, idx) => {
          const value = series.datum?.value;
          const accessedValue =
            value != null
              ? valueAccessor(value, metric, series.category)
              : null;
          const seriesLabel =
            series.datum?.group?.replaceAll('"', '') ??
            series.category ??
            series.name ??
            series.id.slice(0, 8);

          const runsTableUrl =
            canNavigateToRunsTable(chart) && series.datum
              ? generateChartPointRunsUrl(
                  series.series,
                  totalChartDuration,
                  series.datum.timestamp,
                  chart,
                  organizationId,
                  series.category
                )
              : '';

          return (
            <Tooltip
              key={idx}
              title={
                seriesLabel.length > TOOLTIP_ITEM_MAX_LENGTH && isCategorical
                  ? `View runs in new tab for category '${seriesLabel}'`
                  : seriesLabel.length > TOOLTIP_ITEM_MAX_LENGTH
                  ? `View runs in new tab for series '${seriesLabel}'`
                  : 'View runs in new tab'
              }
              sx={{ maxWidth: '300px', pointerEvents: 'none' }}
            >
              <a
                href={runsTableUrl}
                target="_blank"
                className={cn(
                  'flex items-center justify-between gap-2 rounded-md p-1.5',
                  tooltipFrozen && 'cursor-pointer hover:bg-secondary'
                )}
                onClick={() => {
                  if (canNavigateToRunsTable(chart) && series.datum) {
                    setTooltipFrozen(false);
                  }
                }}
              >
                <div className="flex gap-2">
                  <div
                    className="h-[42px] w-1 rounded-sm"
                    style={{ backgroundColor: series.color }}
                  />
                  <div className="flex flex-col justify-between font-normal">
                    <TruncatedText
                      className="line-clamp-1 whitespace-normal break-all text-xs"
                      text={seriesLabel}
                      maxLength={TOOLTIP_ITEM_MAX_LENGTH}
                    />
                    <span className="text-xxs font-normal text-tertiary">
                      {accessedValue == null
                        ? COST_METRICS.includes(metric)
                          ? '$0'
                          : 'No runs'
                        : COST_METRICS.includes(metric)
                        ? currencyFormatter.format(accessedValue)
                        : /.*_rate/.test(metric)
                        ? numberFormatter.format(accessedValue) + '%'
                        : numberFormatter.format(accessedValue)}
                    </span>
                  </div>
                </div>
                {tooltipFrozen ? (
                  <div className="rounded-md bg-secondary p-2">
                    <GitBranch01Icon className="h-4 w-4" />
                  </div>
                ) : (
                  <div className="w-8" />
                )}
              </a>
            </Tooltip>
          );
        })}
      </div>
    </div>
  );
};
