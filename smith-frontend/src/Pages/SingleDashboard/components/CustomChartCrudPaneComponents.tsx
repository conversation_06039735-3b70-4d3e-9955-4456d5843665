import {
  ChevronDownIcon,
  InfoCircleIcon,
  LineChartUp01Icon,
  PlusIcon,
  Rows01Icon,
  SlashCircle01Icon,
  Sliders02Icon,
  Trash04Icon,
} from '@langchain/untitled-ui-icons';
import { Autocomplete, Button, Input, Tooltip } from '@mui/joy';

import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  useController,
  useFieldArray,
} from 'react-hook-form';
import { Link } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { FilterDialogContent } from '@/components/FilterBar/FilterBar';
import { RadioGroup } from '@/components/RadioGroup/radio-group';
import { RadioGroupItem } from '@/components/RadioGroup/radio-group-item';
import { Skeleton } from '@/components/Skeleton';
import { useColorScheme } from '@/hooks/useColorScheme';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  useDashboards,
  useRunStats,
  useSessionMetadata,
  useSessions,
} from '@/hooks/useSwr';
import { CustomChartMetric } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { ChartFormInput } from '../CustomChartsCrudPane';
import { CUSTOM_CHART_GROUP_BY_VALUES } from '../constants';
import { splitMetadataKeys } from '../utils/CustomChartsUtils.utils';
import {
  CustomChartTypeOption,
  MAX_CHARTS_PER_SECTION,
  MAX_DATA_SERIES_PER_CHART,
} from '../utils/constants';
import { getSeriesColor } from '../utils/getSeriesColor';
import { GroupByButton } from './GroupByButton';

const MAX_GROUP_BY = 20;

export const ChartFormSection = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn('flex flex-col gap-2 px-6', className)}>{children}</div>
  );
};

export const ChartFormLabel = ({
  text,
  tooltipText,
  className,
  link,
}: {
  text: string;
  tooltipText?: React.ReactNode;
  className?: string;
  link?: string;
}) => {
  return (
    <div className="flex flex-row items-center gap-2">
      <div className={cn('font-semibold', className)}>{text}</div>
      {tooltipText && (
        <Tooltip
          title={
            <div className="max-w-[400px]">
              {tooltipText}{' '}
              {link && (
                <Link to={link} target="_blank" className="inline underline">
                  View documentation
                </Link>
              )}
            </div>
          }
          placement="top"
        >
          <div>
            <InfoCircleIcon className="my-auto h-4 w-4 text-tertiary" />
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export const ChartSectionBand = ({
  text,
  link,
}: {
  text?: string;
  link?: string;
}) => {
  const { isDarkMode } = useColorScheme();

  return (
    <div
      className={cn(
        'flex items-center gap-2 bg-secondary px-6 uppercase',
        isDarkMode ? 'text-[rgb(160,160,171)]' : 'text-gray-500',
        text ? 'py-1' : 'py-[1px]'
      )}
    >
      {text}
      {link && (
        <Link to={link} target="_blank" className="inline">
          <InfoCircleIcon className="my-auto h-4 w-4 text-tertiary" />
        </Link>
      )}
    </div>
  );
};

export const ChartSubtext = ({ text }: { text: string }) => {
  return <span className="text-xs text-tertiary">{text}</span>;
};

export const ChartErrorMsg = ({ text }: { text?: string }) => {
  return <span className="text-xs text-error">{text}</span>;
};

export function renderErrorMessages(errors: FieldErrors<ChartFormInput>) {
  if (Object.entries(errors).length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col gap-1 whitespace-pre-wrap">
      <span>Errors:</span>
      {Object.entries(errors)
        .map(([, value], idx) => `${idx + 1}: ${value.message}`)
        .join('\n')}
    </div>
  );
}

interface ProjectsSelectorProps {
  selectedProjectIds: string[];
  onSelectedProjectIdsChange: (ids: string[]) => void;
}

export const ProjectsSelector: React.FC<ProjectsSelectorProps> = ({
  selectedProjectIds,
  onSelectedProjectIdsChange,
}) => {
  const [nameContains, setNameContains] = useState<string | null>(null);
  const [debouncedNameContains] = useDebounce(nameContains, 300);

  const {
    data: currentlySelectedProjects,
    isLoading: isLoadingCurrentlySelectedProjects,
  } = useSessions(
    selectedProjectIds.length === 0
      ? null
      : {
          id: selectedProjectIds,
          facets: false,
        }
  );

  // currently selected projects with name and id
  const selectedProjects = useMemo(
    () =>
      currentlySelectedProjects?.rows
        ?.filter((p) => selectedProjectIds.includes(p.id))
        .map((project) => {
          return { name: project.name, id: project.id };
        }) ?? [],
    [currentlySelectedProjects, selectedProjectIds]
  );

  const { selectedTags } = useStoredResourceTags();

  // fetch workspace projects
  const { data: workspaceProjects, isLoading } = useSessions(
    {
      tag_value_id: selectedTags.map((tag) => tag.tag_value_id),
      limit: 100,
      offset: 0,
      reference_free: true,
      sort_by: 'last_run_start_time',
      sort_by_desc: true,
      ...(debouncedNameContains && debouncedNameContains.trim() !== ''
        ? { name_contains: debouncedNameContains }
        : {}),
    },
    {},
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      revalidateIfStale: false,
    }
  );

  // formatted project list
  const projectList = useMemo(
    () =>
      workspaceProjects?.rows
        ?.filter((project) => !selectedProjectIds.includes(project.id))
        .map((project) => ({
          id: project.id,
          name: project.name,
        })) || [],
    [workspaceProjects, selectedProjectIds]
  );

  return isLoadingCurrentlySelectedProjects ||
    !workspaceProjects?.rows?.length ? (
    <Skeleton className="m-0 h-10" />
  ) : (
    <Autocomplete
      value={selectedProjects}
      onChange={(_, v: { id: string; name: string }[] | null) =>
        onSelectedProjectIdsChange(v ? v.map((v) => v.id) : [])
      }
      options={projectList}
      getOptionLabel={(option) => option?.name || ''}
      multiple
      clearOnBlur
      placeholder="Select projects..."
      onInputChange={(_, v) => setNameContains(v || null)}
      loading={isLoading || isLoadingCurrentlySelectedProjects}
      loadingText="Loading projects..."
    />
  );
};

interface MetricSelectorProps {
  value?: CustomChartMetric;
  onChange: (value: CustomChartMetric | undefined) => void;
  options: {
    value: CustomChartMetric;
    label: string;
    disabled?: boolean;
  }[];
  placeholder?: string;
  disabled: boolean;
}

export const MetricSelector: React.FC<MetricSelectorProps> = ({
  value,
  onChange,
  options,
  placeholder = 'Select metric...',
  disabled = false,
}) => {
  return (
    <Autocomplete
      value={value ? options.find((opt) => opt.value === value) || null : null}
      onChange={(_, v) => onChange(v ? v.value : undefined)}
      options={options}
      getOptionLabel={(option) => option.label}
      isOptionEqualToValue={(option, value) => option.value === value.value}
      getOptionDisabled={(option) => option.disabled || false}
      clearOnBlur
      placeholder={placeholder}
      disabled={disabled}
      onKeyDown={(e) => {
        if (e.key === 'Enter') e.preventDefault();
      }}
    />
  );
};

export interface Section {
  id: string;
  title: string;
  atCapacity: boolean;
}

interface DashboardSelectorProps {
  value?: string | null;
  onChange: (value: string | undefined) => void;
}

export const DashboardSelector: React.FC<DashboardSelectorProps> = ({
  value,
  onChange,
}) => {
  const [titleContains, setTitleContains] = useState<string | null>(null);
  const [debouncedTitleContains] = useDebounce(titleContains, 300);

  const { data: sections } = useDashboards({
    limit: 100,
    offset: 0,
    ...(debouncedTitleContains && debouncedTitleContains.trim() !== ''
      ? { title_contains: debouncedTitleContains }
      : {}),
    sort_by: 'modified_at',
    sort_by_desc: true,
  });

  const options =
    sections?.map((section) => ({
      value: section.id,
      label: section.title,
      disabled: section.chart_count >= MAX_CHARTS_PER_SECTION,
    })) || [];

  return (
    <Autocomplete
      value={options.find((opt) => opt.value === value) || null}
      onChange={(_, v) => onChange(v ? v.value : undefined)}
      options={options}
      getOptionLabel={(option) => option.label}
      isOptionEqualToValue={(option, value) => option.value === value.value}
      getOptionDisabled={(option) => option.disabled}
      clearOnBlur
      placeholder="Select dashboard..."
      onInputChange={(_, v) => setTitleContains(v || null)}
      onKeyDown={(e) => {
        if (e.key === 'Enter') e.preventDefault();
      }}
    />
  );
};

export const FeedbackKeySelector = ({
  value,
  onChange,
  options,
}: {
  value?: string;
  onChange: (value: string | undefined) => void;
  options: string[];
}) => {
  return (
    <Autocomplete
      freeSolo
      value={value}
      onChange={(_, v) => onChange(v || undefined)}
      options={options}
      placeholder="Select feedback key..."
      clearOnBlur
      onKeyDown={(e) => {
        if (e.key === 'Enter') e.preventDefault();
      }}
    />
  );
};

export const CustomChartTypeButton = ({
  option,
  isSelected,
  onChange,
}: {
  option: CustomChartTypeOption;
  isSelected: boolean;
  onChange: (value: string) => void;
}) => {
  return (
    <Button
      key={option.value}
      type="button"
      variant="outlined"
      color="neutral"
      size="sm"
      sx={{
        padding: '1rem',
        backgroundColor: isSelected
          ? 'var(--bg-secondary)'
          : 'var(--bg-primary)',
        borderColor: 'var(--border-secondary)',
        '&:hover': {
          borderColor: 'var(--border-primary)',
          backgroundColor: 'var(--bg-secondary)',
        },
      }}
      onClick={() => onChange(option.value)}
    >
      <div className="flex flex-row gap-2">
        {option.icon}
        <span className="mx-auto my-auto text-base">{option.label}</span>
      </div>
    </Button>
  );
};

export const DataSeriesEditor = ({
  disableDataSeries,
  control,
  stats,
  seriesColorsMap,
}: {
  disableDataSeries: boolean;
  control: Control<ChartFormInput>;
  stats: ReturnType<typeof useRunStats>;
  seriesColorsMap: Record<string, string>;
}) => {
  const {
    fields: dataSeriesArray,
    append: appendDataSeries,
    remove: removeDataSeries,
  } = useFieldArray({
    control,
    name: 'dataSeries',
  });
  return (
    <>
      {/* DATA SERIES */}
      {disableDataSeries
        ? null
        : dataSeriesArray.map((f, index) => (
            <Controller
              key={f.id}
              name={`dataSeries.${index}`}
              control={control}
              render={({ field }) => (
                <div
                  className={'flex flex-col gap-4 rounded-lg bg-secondary p-4'}
                >
                  <div
                    key={f.id}
                    className={cn('flex flex-row gap-2', index > 0 && 'mt-2')}
                  >
                    <div className="grow">
                      <Input
                        type="text"
                        size="sm"
                        placeholder="Series name..."
                        startDecorator={
                          <LineChartUp01Icon
                            className={'my-auto h-6 w-6'}
                            style={{
                              color: `${
                                seriesColorsMap[field.value?.name] ??
                                getSeriesColor(
                                  field.value?.name,
                                  field.value?.name
                                )
                              }`,
                            }}
                          />
                        }
                        value={field.value?.name}
                        onChange={(e) => {
                          field.onChange({
                            ...field.value,
                            name: e.target.value,
                          });
                        }}
                      />
                    </div>
                    <Button
                      variant="outlined"
                      color="neutral"
                      size="sm"
                      className="h-fit text-tertiary"
                      onClick={() => removeDataSeries(index)}
                    >
                      <Trash04Icon className="h-4 w-4" />
                    </Button>
                  </div>
                  <FilterDialogContent
                    stats={stats.data}
                    value={field.value?.filters}
                    onChange={(model) => {
                      field.onChange({
                        ...field.value,
                        filters: model,
                      });
                    }}
                    simpleFiltersOnly={false}
                    showSaveFilters={false}
                    setFilterBarOpen={() => null}
                    hideAIQuery={true}
                    hideFilterTitle={true}
                    updateEveryChange={true}
                  />
                </div>
              )}
            />
          ))}

      {/* ADD DATA SERIES */}
      <Tooltip
        title={
          dataSeriesArray.length >= MAX_DATA_SERIES_PER_CHART
            ? `Maximum of ${MAX_DATA_SERIES_PER_CHART} data series allowed`
            : null
        }
      >
        <button
          type="button"
          className={cn(
            'flex cursor-pointer flex-row items-center gap-2 self-start rounded-md border border-secondary p-1 px-2 text-sm transition-colors',
            disableDataSeries ||
              dataSeriesArray.length >= MAX_DATA_SERIES_PER_CHART
              ? 'cursor-auto bg-secondary text-ls-gray-200'
              : 'focus-within:bg-secondary hover:bg-secondary-hover active:bg-secondary'
          )}
          onClick={() => {
            const numDataSeries = dataSeriesArray.length;
            appendDataSeries(
              numDataSeries
                ? {
                    name: `Series ${numDataSeries + 1}`,
                    filters: dataSeriesArray[numDataSeries - 1].filters,
                  }
                : {
                    name: `Series 1`,
                    filters: { filter: '' },
                  }
            );
          }}
          disabled={
            disableDataSeries ||
            dataSeriesArray.length >= MAX_DATA_SERIES_PER_CHART
          }
        >
          <PlusIcon className="h-4 w-4" />
          Data series
        </button>
      </Tooltip>
    </>
  );
};

export const DataSplitOption = ({
  children,
  icon,
  title,
  description,
  radio,
  selected,
  htmlFor,
  disabled,
  disabledMessage,
}: {
  children?: React.ReactNode;
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
  radio: React.ReactNode;
  selected?: boolean;
  htmlFor: string;
  disabled?: boolean;
  disabledMessage?: string;
}) => {
  return (
    <label
      htmlFor={htmlFor}
      className={cn(
        'block cursor-pointer rounded-md border border-primary p-4 transition-colors hover:border-brand',
        disabled &&
          'cursor-not-allowed bg-secondary text-secondary hover:border-primary'
      )}
    >
      <div className="flex gap-3">
        <div className="self-start rounded-md border border-primary p-2">
          {icon}
        </div>
        <div className="flex flex-1 flex-col gap-2 text-left">
          <span className="text-sm font-semibold">{title}</span>
          <span className="text-sm text-tertiary">{description}</span>
        </div>
        <div>{radio}</div>
      </div>
      {selected && children && (
        <div className="flex flex-col items-start gap-3 pl-12 pt-3">
          {children}
        </div>
      )}
      {disabled && disabledMessage && (
        <div className="mt-2 text-sm text-warning">{disabledMessage}</div>
      )}
    </label>
  );
};

export const DataSplitSelector = ({
  control,
  stats,
  disableDataSplits,
  disableDataSeries,
  metadata,
  seriesColorsMap,
}: {
  control: Control<ChartFormInput>;
  stats: ReturnType<typeof useRunStats>;
  disableDataSplits: boolean;

  disableDataSeries: boolean;
  metadata: ReturnType<typeof useSessionMetadata>;
  seriesColorsMap: Record<string, string>;
}) => {
  const { value: isPrebuiltEnabled } = useOrgConfig(
    OrgConfigs.enable_prebuilt_dashboards
  );

  const { metadataToDisplay, ignoredMetadata } = splitMetadataKeys(
    metadata.data ?? {}
  );

  const { field: groupBySelection } = useController({
    name: 'groupBySelection',
    control,
  });

  const { remove: removeDataSeries, fields: dataSeriesArray } = useFieldArray({
    control,
    name: 'dataSeries',
  });

  const [selected, setSelected] = useState<string>(() => {
    if (groupBySelection.value) {
      return 'group-by-metadata';
    } else if (dataSeriesArray.length > 0) {
      return 'custom-data-series';
    }
    return 'none';
  });

  const handleValueChange = useCallback(
    (value: string) => {
      if (value !== 'group-by-metadata') {
        groupBySelection.onChange(undefined);
      }
      if (value !== 'custom-data-series') {
        removeDataSeries();
      }
      setSelected(value);
    },
    [groupBySelection, removeDataSeries]
  );

  useEffect(() => {
    if (disableDataSplits) {
      handleValueChange('none');
    }
    if (disableDataSeries && selected === 'custom-data-series') {
      handleValueChange('none');
    }
  }, [disableDataSplits, disableDataSeries, selected, handleValueChange]);

  return (
    <div>
      <RadioGroup
        value={selected}
        onValueChange={handleValueChange}
        disabled={disableDataSplits}
      >
        {isPrebuiltEnabled && (
          <DataSplitOption
            disabled={disableDataSplits}
            icon={<Rows01Icon className="size-5" />}
            title="Group by"
            description={
              <>
                Use <b>Group by</b> to automatically split your data into
                multiple series based on a field in runs.
              </>
            }
            radio={
              <RadioGroupItem
                value="group-by-metadata"
                id="group-by-metadata"
              />
            }
            selected={selected === 'group-by-metadata'}
            htmlFor="group-by-metadata"
            disabledMessage={"Remove 'compare to' field to create data splits"}
          >
            <div className="flex gap-2">
              <GroupByButton
                groupBySelection={groupBySelection.value}
                setGroupBySelection={groupBySelection.onChange}
                metadataToDisplay={metadataToDisplay}
                additionalMetadataKeys={ignoredMetadata}
                variant="chart"
                disabled={disableDataSplits}
                values={CUSTOM_CHART_GROUP_BY_VALUES}
              />
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <button
                    type="button"
                    className="flex items-center gap-1 rounded-md border border-secondary px-1.5 py-[7px] text-sm text-quaternary"
                  >
                    <span className="text-xs font-semibold text-primary">
                      Max Groups:
                    </span>{' '}
                    {groupBySelection.value?.max_groups ?? 5}
                    <ChevronDownIcon className="h-4 w-4" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="max-h-[200px] overflow-y-auto">
                  {Array.from({ length: MAX_GROUP_BY }, (_, i) => (
                    <DropdownMenuItem
                      className="text-sm"
                      key={i}
                      onClick={() => {
                        groupBySelection.onChange({
                          ...groupBySelection.value,
                          max_groups: i + 1,
                        });
                      }}
                    >
                      <span>{i + 1}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </DataSplitOption>
        )}

        <DataSplitOption
          disabled={disableDataSplits || disableDataSeries}
          icon={<Sliders02Icon className="size-5" />}
          title="Custom data splits"
          description="Use this for more manual control over your filters and queries"
          radio={
            <RadioGroupItem
              value="custom-data-series"
              id="custom-data-series"
            />
          }
          selected={selected === 'custom-data-series'}
          htmlFor="custom-data-series"
          disabledMessage={
            disableDataSeries
              ? 'Remove feedback values metric to create data splits'
              : "Remove 'compare to' field to create data splits"
          }
        >
          <DataSeriesEditor
            disableDataSeries={disableDataSeries}
            control={control}
            stats={stats}
            seriesColorsMap={seriesColorsMap}
          />
        </DataSplitOption>
        <DataSplitOption
          icon={<SlashCircle01Icon className="size-5" />}
          title="No data splits"
          description="No data splits will be applied to the chart"
          radio={<RadioGroupItem value="none" id="none" />}
          selected={selected === 'none'}
          htmlFor="none"
        />
      </RadioGroup>
    </div>
  );
};
