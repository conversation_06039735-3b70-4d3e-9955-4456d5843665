import {
  AlertCircleIcon,
  ChevronDownIcon,
  Copy01Icon,
  DotsVerticalIcon,
  Edit01Icon,
  GitBranch01Icon,
  PlusIcon,
  Trash04Icon,
} from '@langchain/untitled-ui-icons';
import { Button, Tooltip } from '@mui/joy';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { TruncatedTextWithTooltip } from '@/components/TruncatedTextWithTooltip.tsx';
import { usePermissions } from '@/hooks/usePermissions';
import { useOrganizationId, useSessions } from '@/hooks/useSwr';
import {
  CustomChartPreviewSchema,
  CustomChartSchema,
  CustomChartType,
} from '@/types/schema';
import {
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
} from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { ACTION_ICON_STYLE } from '../constants';
import { getUniqueSessionIdsFromChart } from '../utils/CustomChartsUtils.utils';
import { GRID_CHART_HEIGHT } from '../utils/constants';

export const MoreActionsDropdown = ({
  onEdit,
  onDelete,
  onClone,
}: {
  onEdit: () => void;
  onDelete: () => void;
  onClone?: () => void;
}) => {
  const { authorize } = usePermissions();

  const updateEnabled = authorize('charts:update');
  const deleteEnabled = authorize('charts:delete');
  const createEnabled = authorize('charts:create');

  if (!updateEnabled && !deleteEnabled && !createEnabled) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger onClick={(e) => e.stopPropagation()}>
        <DotsVerticalIcon className="size-4" />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="z-10 text-sm"
        onClick={(e) => e.stopPropagation()}
      >
        {updateEnabled && (
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            className="flex items-center gap-2"
          >
            <Edit01Icon className="size-4" />
            Edit
          </DropdownMenuItem>
        )}
        {deleteEnabled && (
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="flex items-center gap-2"
          >
            <Trash04Icon className="size-4" />
            Delete
          </DropdownMenuItem>
        )}
        {onClone && createEnabled && (
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              onClone();
            }}
            className="flex items-center gap-2"
          >
            <Copy01Icon className="size-4" />
            Clone
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const ChartTypeDropdown = ({
  type,
  setType,
}: {
  type: CustomChartType;
  setType: (type: CustomChartType) => void;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex h-fit w-[60px] flex-row justify-between rounded-md border border-secondary p-1">
        <span className="pl-1 text-xs capitalize">{type}</span>
        {<ChevronDownIcon className="my-auto h-4 w-4" />}
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-16">
        <DropdownMenuItem onClick={() => setType('line')} className="text-xs">
          Line
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setType('bar')} className="text-xs">
          Bar
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const ChartWrapper = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <ErrorBoundary
      fallback={(error) => (
        <CustomChartFallback
          icon={<AlertCircleIcon className="size-6 text-disabled" />}
          message={`Error showing chart: ${error.message}`}
        />
      )}
    >
      <div
        className={cn('flex flex-col rounded-md bg-secondary p-4', className)}
      >
        {children}
      </div>
    </ErrorBoundary>
  );
};

export const CustomChartHeader = ({
  chart,
  moreActions,
  hideProjectNames,
}: {
  chart: CustomChartSchema | CustomChartPreviewSchema;
  moreActions?: React.ReactNode;
  hideProjectNames?: boolean;
}) => {
  return (
    <div className="flex flex-wrap justify-between">
      <div className="flex max-w-[calc(100%-150px)] flex-col gap-1">
        <h3 className="truncate text-sm font-semibold tracking-tight">
          {chart.title}
        </h3>
        {chart.description && (
          <TruncatedTextWithTooltip
            text={chart.description}
            className="h-5 text-xs font-normal text-quaternary"
            tooltipMaxWidth={400}
          />
        )}
      </div>
      <div className="mb-auto flex flex-row gap-2">
        <SessionLinks
          sessionIds={getUniqueSessionIdsFromChart(chart)}
          hideProjectNames={hideProjectNames}
        />
        <div className="flex flex-row gap-1">{moreActions}</div>
      </div>
    </div>
  );
};

export const AddButton = ({
  onClick,
  text,
  variant = 'plain',
  disabled = false,
}: {
  onClick: () => void;
  text?: string;
  variant?: 'plain' | 'outlined' | 'solid' | 'soft';
  disabled?: boolean;
}) => {
  const { authorize } = usePermissions();
  const canUpdate = authorize('charts:update');
  return (
    <div className="w-fit">
      {canUpdate && (
        <Button
          type="button"
          variant={variant}
          size="sm"
          className="h-fit w-full rounded-md border border-tertiary p-2"
          disabled={disabled}
          onClick={onClick}
        >
          <PlusIcon className="h-4 w-4" />
          {text && <span className="pl-3">{text}</span>}
        </Button>
      )}
    </div>
  );
};

export const SessionLinks = ({
  sessionIds,
  hideProjectNames,
  buttonClassName,
  variant = 'chart',
}: {
  sessionIds: string[];
  hideProjectNames?: boolean;
  buttonClassName?: string;
  variant?: 'chart' | 'dashboard';
}) => {
  const { data: sessions } = useSessions(
    sessionIds.length === 0
      ? null
      : {
          id: sessionIds,
          facets: false,
        }
  );
  const organizationId = useOrganizationId();

  if (hideProjectNames || !sessions?.rows?.length) return null;

  if (sessions?.rows?.length === 1) {
    return (
      <Tooltip title={sessions.rows[0].name} placement="bottom">
        <a
          href={`/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${sessions.rows[0].id}`}
          target="_blank"
          rel="noopener noreferrer"
          className={cn(ACTION_ICON_STYLE, buttonClassName)}
          onClick={(e) => e.stopPropagation()}
        >
          <GitBranch01Icon className="size-4" />
        </a>
      </Tooltip>
    );
  }

  return (
    !hideProjectNames && (
      <Tooltip
        title={
          <div className="flex flex-col gap-1">
            <div className="text-xxs uppercase">Tracing projects</div>
            {sessions.rows.map((session) => (
              <a
                key={session.id}
                href={`/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${session.id}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex max-w-[200px] items-center gap-1 truncate rounded-md bg-[var(--gray-800)] px-1 py-0.5 text-xxs text-[var(--gray-200)] hover:text-brand-green-400"
                onClick={(e) => e.stopPropagation()}
              >
                <GitBranch01Icon className="size-3" /> {session.name}
              </a>
            ))}
          </div>
        }
        placement="bottom"
      >
        <div className={cn(ACTION_ICON_STYLE, 'px-1.5', buttonClassName)}>
          <GitBranch01Icon
            className={variant === 'chart' ? 'size-3' : 'size-4'}
          />
          {variant === 'chart' && (
            <span className="text-xxs">{sessions.rows.length}</span>
          )}
        </div>
      </Tooltip>
    )
  );
};

export const CustomChartFallback = ({
  icon,
  message,
}: {
  icon?: React.ReactNode;
  message: string;
}) => {
  return (
    <div
      className="flex flex-1 flex-col items-center justify-center gap-4"
      style={{ minHeight: GRID_CHART_HEIGHT }}
    >
      {icon && <div className="rounded-full bg-tertiary p-2">{icon}</div>}
      <div className="flex max-w-[50%] flex-col items-center gap-2">
        <div className="text-center text-sm text-quaternary">{message}</div>
      </div>
    </div>
  );
};
