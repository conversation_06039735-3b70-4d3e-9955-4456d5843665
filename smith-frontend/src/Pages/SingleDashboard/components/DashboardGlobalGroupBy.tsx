import { Skeleton } from '@/components/Skeleton';
import { useSessionMetadata } from '@/hooks/useSwr';
import { RunStatsGroupBy, TimedeltaInput } from '@/types/schema';

import { splitMetadataKeys } from '../utils/CustomChartsUtils.utils';
import { GroupByButton } from './GroupByButton';

export const DashboardGlobalGroupBy = ({
  sessionId,
  customChartsParams,
  groupBySelection,
  setGroupBySelection,
}: {
  sessionId: string;
  customChartsParams: {
    start_time: string;
    end_time: string;
    stride: TimedeltaInput;
  };
  groupBySelection?: RunStatsGroupBy;
  setGroupBySelection: (groupBySelection?: RunStatsGroupBy) => void;
}) => {
  const { start_time } = customChartsParams;

  const commonMetadata = useSessionMetadata(
    {
      k: 5,
      start_time,
      root_runs_only: true,
    },
    sessionId
  );

  const { metadataToDisplay, ignoredMetadata } = splitMetadataKeys(
    commonMetadata.data ?? {}
  );

  if (commonMetadata.isLoading) {
    return <Skeleton className="my-0 h-9 w-24" />;
  }
  return (
    metadataToDisplay && (
      <GroupByButton
        variant="global"
        groupBySelection={groupBySelection}
        setGroupBySelection={setGroupBySelection}
        metadataToDisplay={metadataToDisplay}
        additionalMetadataKeys={ignoredMetadata}
        showTooltip={true}
      />
    )
  );
};
