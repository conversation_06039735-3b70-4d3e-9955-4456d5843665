import { TabPanels } from '@headlessui/react';

import { useCallback, useMemo } from 'react';
import { useDebounce } from 'use-debounce';

import { COLUMN_VISIBILITY } from '@/Pages/Project/constants';
import { RunsTable } from '@/components/RunsTable';
import { DateTimeRangePickerValue } from '@/components/RunsTable/utils/getDateTimeRangeLabel';
import { TabGroup, TabLabel, TabList, TabPanel } from '@/components/Tabs';
import { TruncatedTextWithTooltip } from '@/components/TruncatedTextWithTooltip.tsx';
import { GridTimeModel, useDataGridState } from '@/hooks/useDataGridState';
import { CustomChartSeriesFilters, CustomChartsRequest } from '@/types/schema';

import { getFakeIdForSeries } from '../hooks/useCustomChartsPreviewData';
import { combineFilters } from '../utils/CustomChartsUtils.utils';
import { DataSeriesFilter, METRIC_TO_RUN_COLUMN_ID } from '../utils/constants';
import { getSeriesColor } from '../utils/getSeriesColor';

export const CustomChartRunsTabs = ({
  filters,
  dataSeries,
  customChartsParams,
  timeModel,
  setTimeModel,
  metrics,
  seriesColorsMap,
}: {
  filters: CustomChartSeriesFilters;
  dataSeries: DataSeriesFilter[];
  customChartsParams: CustomChartsRequest;
  timeModel: GridTimeModel;
  setTimeModel: (model: GridTimeModel) => void;
  metrics: string[];
  seriesColorsMap: Record<string, string>;
}) => {
  return (
    <TabGroup>
      <TabList className="max-w-full overflow-x-auto">
        {dataSeries.map((ds, idx) => {
          const fakeId = getFakeIdForSeries(ds.name, idx);
          return (
            <TabLabel key={fakeId}>
              <div
                className="h-3 w-3 rounded-full"
                style={{
                  backgroundColor:
                    seriesColorsMap[ds.name] ??
                    getSeriesColor(ds.name ?? fakeId),
                }}
              />
              <TruncatedTextWithTooltip
                text={ds.name || fakeId.slice(0, 8)}
                className="max-w-[200px] text-xs"
              />
            </TabLabel>
          );
        })}
      </TabList>
      <TabPanels>
        {dataSeries.map((ds, idx) => {
          const fakeId = getFakeIdForSeries(ds.name, idx);
          return (
            <TabPanel key={fakeId}>
              <CustomChartRunsTable
                filters={filters}
                dataSeries={ds}
                customChartsParams={customChartsParams}
                timeModel={timeModel as GridTimeModel}
                setTimeModel={setTimeModel as (model: GridTimeModel) => void}
                metrics={metrics}
              />
            </TabPanel>
          );
        })}
      </TabPanels>
    </TabGroup>
  );
};

export const CustomChartRunsTable = ({
  filters,
  dataSeries,
  customChartsParams,
  timeModel,
  setTimeModel,
  metrics,
}: {
  filters: CustomChartSeriesFilters;
  dataSeries?: DataSeriesFilter;
  customChartsParams: CustomChartsRequest;
  timeModel: GridTimeModel;
  setTimeModel: (model: GridTimeModel) => void;
  metrics: string[];
}) => {
  const {
    paginationModel,
    setPaginationModel,
    resetPaginationModel,
    sortModel,
    setSortModel,
    filterModel,
    setFilterModel,
    initialState,
  } = useDataGridState({
    defaultTimeModel: timeModel,
    defaultColumnVisibility: COLUMN_VISIBILITY.default,
  });

  const and = useCallback(combineFilters, []);

  // Add special handling if the metric is error rate:
  const metricFilter =
    metrics.length == 1 && metrics[0] == 'error_rate'
      ? "eq(status, 'error')"
      : undefined;
  const baseFilter = combineFilters(filters.filter, metricFilter);

  const runsFilterInput = useMemo(
    () => ({
      filter: and(baseFilter, dataSeries?.filters.filter),
      tree_filter: and(filters.tree_filter, dataSeries?.filters.treeFilter),
      trace_filter: and(filters.trace_filter, dataSeries?.filters.traceFilter),
      session: filters.session,
      start_time: customChartsParams.start_time,
      end_time: customChartsParams.end_time,
    }),
    [
      baseFilter,
      filters.tree_filter,
      filters.trace_filter,
      filters.session,
      dataSeries?.filters.filter,
      dataSeries?.filters.treeFilter,
      dataSeries?.filters.traceFilter,
      customChartsParams.start_time,
      customChartsParams.end_time,
      and,
    ]
  );

  const [debouncedRunsFilter] = useDebounce(runsFilterInput, 1000);

  const prioritizedColumns = useMemo(() => {
    return metrics
      .map((metric) => METRIC_TO_RUN_COLUMN_ID[metric])
      .filter(Boolean);
  }, [metrics]);

  return (
    <div className="bg-primary">
      <RunsTable
        view="all"
        onViewChange={() => null}
        tableDisplay="list"
        runsFilter={{ session: filters.session }}
        debouncedRunsFilter={debouncedRunsFilter}
        searchModel={{
          filter: filters.filter || '',
          treeFilter: filters.tree_filter || '',
          traceFilter: filters.trace_filter || '',
        }}
        setSearchModel={() => null}
        columnVisibilityModel={COLUMN_VISIBILITY.default}
        setColumnVisibilityModel={() => null}
        onColumnVisibilityResetClick={() => null}
        timeModel={timeModel as GridTimeModel}
        setTimeModel={
          setTimeModel as React.Dispatch<React.SetStateAction<GridTimeModel>>
        }
        navigateToRun={() => null}
        paginationModel={paginationModel}
        setPaginationModel={setPaginationModel}
        resetPaginationModel={resetPaginationModel}
        sortModel={sortModel}
        setSortModel={setSortModel}
        filterModel={filterModel}
        setFilterModel={setFilterModel}
        initialState={initialState}
        withToolbar={false}
        prioritizedColumns={prioritizedColumns}
      />
    </div>
  );
};

export const CustomChartRunsPreview = ({
  filters,
  dataSeries,
  customChartsParams,
  timeModel,
  setTimeModel,
  metrics,
  seriesColorsMap,
}: {
  filters: CustomChartSeriesFilters;
  dataSeries: DataSeriesFilter[];
  customChartsParams: CustomChartsRequest;
  timeModel: DateTimeRangePickerValue;
  setTimeModel: (model: DateTimeRangePickerValue) => void;
  metrics: string[];
  seriesColorsMap: Record<string, string>;
}) => {
  return dataSeries.length > 0 ? (
    <CustomChartRunsTabs
      filters={filters}
      dataSeries={dataSeries}
      customChartsParams={customChartsParams}
      timeModel={timeModel as GridTimeModel}
      setTimeModel={setTimeModel as (model: GridTimeModel) => void}
      metrics={metrics}
      seriesColorsMap={seriesColorsMap}
    />
  ) : (
    <CustomChartRunsTable
      filters={filters}
      customChartsParams={customChartsParams}
      timeModel={timeModel as GridTimeModel}
      setTimeModel={setTimeModel as (model: GridTimeModel) => void}
      metrics={metrics}
    />
  );
};
