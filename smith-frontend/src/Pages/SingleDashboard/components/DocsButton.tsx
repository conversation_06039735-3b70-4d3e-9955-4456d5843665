import { File02Icon } from '@langchain/untitled-ui-icons';

import { Link } from 'react-router-dom';

import { Tooltip } from '@/components/Tooltip/Tooltip';
import { cn } from '@/utils/tailwind';

export function DocsButton({
  link,
  className,
}: {
  link: string;
  className?: string;
}) {
  return (
    <Tooltip title="Documentation" placement="bottom" tooltipClassName="p-0">
      <button
        type="button"
        className={cn(
          'rounded-md border border-secondary bg-primary p-1.5',
          className
        )}
      >
        <Link to={link} target="_blank">
          <File02Icon className="size-4" />
        </Link>
      </button>
    </Tooltip>
  );
}
