import {
  ChevronDownIcon,
  ChevronRightIcon,
  XIcon,
} from '@langchain/untitled-ui-icons';

import { CommandItem } from '@/components/Command';
import {
  PopoverDropdownMenu,
  PopoverDropdownMenuItem,
} from '@/components/PopoverDropdownMenu';
import { Tooltip, TooltipV2 } from '@/components/Tooltip/Tooltip';
import { TruncatedTextWithTooltip } from '@/components/TruncatedTextWithTooltip.tsx';
import { RunStatsGroupBy, SessionMetadataResponse } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { DEFAULT_GROUP_BY_VALUES, METADATA_GROUP_BY } from '../constants';

interface GroupByButtonProps {
  groupBySelection?: RunStatsGroupBy | undefined;
  setGroupBySelection: (selection: RunStatsGroupBy | undefined) => void;
  showTooltip?: boolean;
  metadataToDisplay: SessionMetadataResponse;
  additionalMetadataKeys?: SessionMetadataResponse;
  variant: 'global' | 'chart';
  disabled?: boolean;
  values?: { display: string; value: RunStatsGroupBy['attribute'] }[];
}

export const GroupByButton = ({
  values = DEFAULT_GROUP_BY_VALUES,
  groupBySelection,
  setGroupBySelection,
  showTooltip = false,
  metadataToDisplay,
  additionalMetadataKeys,
  variant,
  disabled,
}: GroupByButtonProps) => {
  const prefix =
    variant === 'global' || groupBySelection ? (
      <span className="text-xs font-semibold">Group by</span>
    ) : (
      <span className="text-xs font-semibold">Select metadata or tag</span>
    );

  const trigger = (
    <div className="flex cursor-pointer items-center gap-1.5 rounded-md border border-secondary py-2 pl-3 hover:bg-secondary">
      {prefix}
      {groupBySelection && (
        <div className="flex items-center gap-0.5 rounded-sm bg-quaternary px-1.5 py-0 text-xxs text-tertiary">
          <span className="line-clamp-1 max-w-[150px] overflow-hidden truncate break-words break-all">
            <TruncatedTextWithTooltip
              className="line-clamp-1 truncate text-ellipsis break-words break-all"
              text={groupBySelection.path || groupBySelection.attribute}
            />
          </span>
          {variant === 'global' && (
            <button
              className="ml-1"
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                setGroupBySelection(undefined);
              }}
            >
              <XIcon className="h-3 w-3" />
            </button>
          )}
        </div>
      )}
      <ChevronDownIcon className="mr-2 h-3 w-3" />
    </div>
  );

  const withTooltip = (trigger: React.ReactNode) => {
    return showTooltip ? (
      <Tooltip
        title="Group data by run tags or metadata"
        tooltipClassName="p-1"
        placement="left"
      >
        {trigger}
      </Tooltip>
    ) : (
      trigger
    );
  };

  return (
    <PopoverDropdownMenu
      contentWidth="150px"
      values={values}
      getTitle={(value) => value.display}
      getIsSelected={(value) => {
        return (
          groupBySelection?.attribute.toLowerCase() ===
          value.value.toLowerCase()
        );
      }}
      alignOffset={0}
      onSelect={(value) => {
        if (value.value !== METADATA_GROUP_BY) {
          setGroupBySelection({
            attribute: value.value,
            path: '',
          });
        }
      }}
      getCustomCell={(value, onSelect) => {
        if (value.value !== METADATA_GROUP_BY) {
          // This will fallback to default PopoverDropdownMenuItem
          return null;
        }
        return (
          <MetadataPopoverDropdownMenu
            key={value.value}
            disabled={disabled}
            setGroupBySelection={(value) => {
              setGroupBySelection(value);
              onSelect();
            }}
            metadataToDisplay={metadataToDisplay}
            additionalMetadataKeys={additionalMetadataKeys}
            groupBySelection={groupBySelection}
          />
        );
      }}
      disableTrigger={disabled}
      itemClassName="min-h-[32px] flex-1 px-2 py-1"
    >
      {withTooltip(trigger)}
    </PopoverDropdownMenu>
  );
};

const MetadataCellWithTooltip = ({
  metadataKey,
  values,
  disabled = false,
  onSelect,
  selected,
}: {
  metadataKey: string;
  values: string[];
  disabled?: boolean;
  onSelect: () => void;
  selected: boolean;
}) => {
  return (
    <TooltipV2
      key={metadataKey}
      title={
        <div className="flex max-h-[300px] flex-col gap-1 overflow-y-auto">
          <div>
            <span className="text-sm font-semibold text-secondary">
              {metadataKey}
            </span>
            <span className="ml-2 font-normal text-tertiary">
              {values.length} values
            </span>
          </div>
          {values.map((v) => (
            <span className="line-clamp-1 max-w-[280px] truncate text-tertiary">
              {v}
            </span>
          ))}
        </div>
      }
      placement="left"
      tooltipClassName=""
    >
      <CommandItem
        className={cn(
          'mx-1.5 flex cursor-pointer justify-between px-0 py-0',
          disabled && 'cursor-default'
        )}
        onSelect={onSelect}
        disabled={disabled}
      >
        <PopoverDropdownMenuItem
          title={metadataKey}
          disabled={disabled}
          selected={selected}
          titleBold={true}
        />
      </CommandItem>
    </TooltipV2>
  );
};

// this is very jank and would be nice to make a generic component
export const MetadataPopoverDropdownMenu = ({
  metadataToDisplay,
  disabled,
  groupBySelection,
  setGroupBySelection,
  additionalMetadataKeys,
}: {
  metadataToDisplay: SessionMetadataResponse;
  additionalMetadataKeys?: SessionMetadataResponse;
  disabled?: boolean;
  groupBySelection: RunStatsGroupBy | undefined;
  setGroupBySelection: (selection: RunStatsGroupBy | undefined) => void;
}) => {
  const metadataToDisplayArray = Object.entries(metadataToDisplay);
  const additionalMetadataKeysArray = Object.entries(
    additionalMetadataKeys ?? {}
  );
  return (
    <PopoverDropdownMenu
      key={'metadataPopover'}
      enableSearch
      searchInputClassName="py-1 h-auto"
      contentWidth="250px"
      getSection={([key]) =>
        additionalMetadataKeys?.[key] ? 'Default metadata' : 'Custom metadata'
      }
      sectionsOrder={['Custom metadata', 'Default metadata']}
      values={[...metadataToDisplayArray, ...additionalMetadataKeysArray]}
      getTitle={([key]) => key}
      getIsSelected={([key]) =>
        groupBySelection?.attribute === 'metadata' &&
        groupBySelection?.path === key
      }
      alignOffset={0}
      side="right"
      onSelect={([key]) => {
        setGroupBySelection({
          attribute: 'metadata',
          path: key,
        });
      }}
      asChild
      getCustomCell={([key, values], onSelect) => {
        const selected =
          groupBySelection?.attribute === 'metadata' &&
          groupBySelection?.path === key;
        return (
          <MetadataCellWithTooltip
            metadataKey={key}
            values={values}
            disabled={disabled}
            onSelect={onSelect}
            selected={selected}
          />
        );
      }}
    >
      {/* Trigger for metadata sub menu*/}
      <div>
        <CommandItem
          key={'metadata'}
          className={cn(
            'mx-1.5 flex cursor-pointer justify-between px-0 py-0',
            disabled && 'cursor-default'
          )}
          onSelect={() => void 0}
          disabled={disabled}
        >
          <PopoverDropdownMenuItem
            title="Metadata"
            disabled={disabled}
            selected={false}
            titleBold={true}
            endDecorator={<ChevronRightIcon className="size-4" />}
            className="min-h-[32px] flex-1 px-2 py-1"
          />
        </CommandItem>
      </div>
    </PopoverDropdownMenu>
  );
};
