import { RunStatsGroupBy } from '@/types/schema';

import { ChartFormInput } from './CustomChartsCrudPane';
import { DEFAULT_CHART_TYPE } from './utils/constants';

export const PREBUILT_SECTIONS = [
  {
    section_name: 'Overview',
    chart_names: [
      'Trace Count',
      'Trace Error Rate',
      'Error Rate',
      'Trace Latency',
      'Total Cost',
      'Tokens per Trace',
      'Cost per Trace',
    ],
  },
  {
    section_name: 'LLM Stats',
    chart_names: [
      'LLM Call Count',
      'Total Tokens',
      'LLM Cost per Model',
      'LLM Calls per Trace',
      'Tokens per Model',
      'Latency per Model',
      'LLM Latency',
    ],
  },
  {
    section_name: 'Errors',
    chart_names: [
      'LLM Error Rate',
      'Error rates per run',
      'Errors broken down by type',
    ],
  },
];

export const ACTION_ICON_STYLE =
  'my-auto flex p-1 gap-1 items-center justify-center rounded-md transition-colors bg-tertiary hover:bg-quaternary focus:outline-none cursor-pointer';

export const defaultValues: ChartFormInput = {
  title: '',
  description: '',
  metric: null,
  filters: {
    filter: 'eq(is_root, true)',
    traceFilter: undefined,
    treeFilter: undefined,
  },
  dataSeries: [],
  chartType: DEFAULT_CHART_TYPE,
  sessionIds: [],
  sectionId: null,
};

export const LEGEND_INDICATOR_WIDTH = 30;

export const METADATA_GROUP_BY = 'metadata';
export const TAG_GROUP_BY = 'tag';
export const DEFAULT_GROUP_BY_VALUES: {
  display: string;
  value: RunStatsGroupBy['attribute'];
}[] = [
  { display: 'Tag', value: TAG_GROUP_BY },
  { display: 'Metadata', value: METADATA_GROUP_BY },
];
export const CUSTOM_CHART_GROUP_BY_VALUES: {
  display: string;
  value: RunStatsGroupBy['attribute'];
}[] = [
  { display: 'Tag', value: TAG_GROUP_BY },
  { display: 'Run Name', value: 'name' },
  { display: 'Run Type', value: 'run_type' },
  { display: 'Metadata', value: METADATA_GROUP_BY },
];
