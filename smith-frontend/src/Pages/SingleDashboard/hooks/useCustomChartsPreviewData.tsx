import { useMemo } from 'react';
import { useDebounce } from 'use-debounce';
import { v5 as uuidv5 } from 'uuid';

import { useCustomChartsPreview } from '@/hooks/useSwr';
import {
  CustomChartCreatePreview,
  CustomChartsPreviewRequest,
} from '@/types/schema';

import { ChartFormInput } from '../CustomChartsCrudPane';
import {
  convertFilterFormat,
  createSeriesFromFilters,
} from '../utils/CustomChartsUtils.utils';
import { FEEDBACK_METRICS } from '../utils/constants';
import { useChartTimeFilter } from './useChartTimeFilter';

// random UUID namespace for generating series IDs
const CUSTOM_NAMESPACE = '6ba7b812-9dad-11d1-80b4-00c04fd430c8';

export const getFakeIdForSeries = (name: string, idx: number) =>
  uuidv5(`${name}-${idx}`, CUSTOM_NAMESPACE);

export const useCustomChartsPreviewData = (formValues: ChartFormInput) => {
  const { timeModel, setTimeModel, customChartsParams } = useChartTimeFilter({
    max_num_buckets: 60,
    customStride: formValues.customStride,
    timeRange: formValues.timeRange,
  });

  const dataSeriesStringified = JSON.stringify(formValues.dataSeries);
  const comparisonMetricsStringified = JSON.stringify(
    formValues.comparisonMetrics
  );
  const sessionIdsStringified = JSON.stringify(formValues.sessionIds);
  const groupBySelectionStringified = JSON.stringify(
    formValues.groupBySelection
  );

  const seriesPayload = useMemo(() => {
    if (!formValues.sessionIds?.length || !formValues.metric) {
      return {
        series: [],
        common_filters: {
          session: [],
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
        },
      } as CustomChartCreatePreview;
    }
    return {
      series: createSeriesFromFilters({
        sessionIds: formValues.sessionIds,
        metric: formValues.metric,
        comparisonMetrics: formValues.comparisonMetrics,
        dataSeriesFilters: formValues.dataSeries,
        feedbackKey: FEEDBACK_METRICS.includes(formValues.metric)
          ? formValues.feedbackKey
          : undefined,
        groupBySelection: formValues.groupBySelection,
      }).map((s, idx) => ({
        ...s,
        id: getFakeIdForSeries(s.name, idx),
      })),
      common_filters: {
        session: formValues.sessionIds,
        ...convertFilterFormat(formValues.filters, 'seriesFilters'),
      },
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    formValues.filters,
    sessionIdsStringified,
    formValues.metric,
    comparisonMetricsStringified,
    dataSeriesStringified,
    formValues.feedbackKey,
    groupBySelectionStringified,
  ]);

  const payload: CustomChartsPreviewRequest = useMemo(() => {
    return {
      chart: seriesPayload,
      bucket_info: customChartsParams,
    };
  }, [seriesPayload, customChartsParams]);

  const [debouncedPayload] = useDebounce(payload, 500);
  const { data, error, isLoading } = useCustomChartsPreview(
    debouncedPayload.chart.common_filters.session?.length
      ? debouncedPayload
      : null
  );

  const previewData = {
    previewData: data?.data ?? [],
    seriesPayload,
    previewDataIsLoading: isLoading,
    previewDataError: error,
    previewTimeModel: timeModel,
    setPreviewTimeModel: setTimeModel,
    previewChartsParams: customChartsParams,
  };

  // With group by we need to transform data and series into
  // the right format to be rendered by the chart component
  if (formValues.groupBySelection && data?.data) {
    // Get unique groups and create series for each
    const uniqueGroups = [
      ...new Set(data.data.map((item) => item.group).filter(Boolean)),
    ];

    const transformedSeries = uniqueGroups.map((group) => ({
      ...seriesPayload.series[0],
      id: `${seriesPayload.series[0].id}:${group}`,
      group_by: formValues.groupBySelection,
    }));

    // Transform data points to match series IDs
    const transformedData = data.data.map((point) =>
      point.group
        ? { ...point, series_id: `${point.series_id}:${point.group}` }
        : point
    );

    if (transformedSeries.length === 0) {
      return previewData;
    }

    return {
      ...previewData,
      previewData: transformedData,
      seriesPayload: {
        ...seriesPayload,
        series: transformedSeries,
      },
    };
  }

  return previewData;
};
