import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';
import { useMemo, useState } from 'react';

import {
  DateTimeRangePickerValue,
  parseRelativeDate,
} from '@/components/RunsTable/utils/getDateTimeRangeLabel';
import { TimedeltaInput } from '@/types/schema';
import { utcTime } from '@/utils/utc-time';

dayjs.extend(duration);
dayjs.extend(utc);
const MAX_NUM_BUCKETS_PER_CHART = 30;
const MIN_NUM_BUCKETS_PER_CHART = 7;

function calculateSmartBucketsPreferFullDays(
  start_time: string,
  end_time: string,
  max_num_buckets: number,
  min_num_buckets: number
): TimedeltaInput {
  const start = utcTime(start_time);
  const end = utcTime(end_time);
  const duration = dayjs.duration(end.diff(start));
  const totalMinutes = duration.asMinutes();

  let numBuckets: number;
  const hours = duration.asHours();
  const days = duration.asDays();
  const weeks = duration.asWeeks();

  // figure out the proper stride based on the duration
  if (hours <= max_num_buckets) {
    numBuckets = Math.max(Math.ceil(hours), min_num_buckets);
  } else if (days <= max_num_buckets) {
    numBuckets = Math.max(Math.ceil(days), min_num_buckets);
  } else if (weeks <= max_num_buckets) {
    numBuckets = Math.max(Math.ceil(weeks), min_num_buckets);
  } else {
    // For longer durations, cap at MAX_BUCKETS
    numBuckets = max_num_buckets;
  }

  const stride = Math.ceil(totalMinutes / numBuckets);

  return { minutes: stride };
}

function calculateSmartBuckets(
  start_time: string,
  end_time: string,
  max_num_buckets: number
): TimedeltaInput {
  const start = utcTime(start_time);
  const end = utcTime(end_time);
  const durationObj = dayjs.duration(end.diff(start));
  const totalHours = durationObj.asHours();
  const totalDays = durationObj.asDays();

  // For intervals not exactly matching our predefined ones, extrapolate
  if (totalHours <= 1) {
    // Less than 1 hour: use 1-minute stride
    return { minutes: 1 };
  } else if (totalHours <= 9) {
    // Between 1-9 hours: scale between 1-5 minute strides
    const stride = Math.max(1, Math.round(totalHours * 0.5));
    return { minutes: stride };
  } else if (totalDays <= 1) {
    // Between 9 hours and 1 day: scale between 5-15 minute strides
    const scale = (totalHours - 9) / (24 - 9);
    const stride = Math.round(5 + scale * (15 - 5));
    return { minutes: stride };
  } else if (totalDays <= 3) {
    // Between 1-3 days: scale between 15-60 minute strides
    const scale = (totalDays - 1) / 2;
    const stride = Math.round(15 + scale * (60 - 15));
    return { minutes: stride };
  } else if (totalDays <= 7) {
    // Between 3-7 days: scale between 1 hour and 8 hour strides
    const scale = (totalDays - 3) / 4;
    const hourStride = Math.round(1 + scale * (8 - 1));
    return { minutes: hourStride * 60 };
  } else if (totalDays <= 14) {
    // Between 7-14 days: scale between 8 hour and 12 hour strides
    const scale = (totalDays - 7) / 7;
    const hourStride = Math.round(8 + scale * (12 - 8));
    return { minutes: hourStride * 60 };
  } else if (totalDays <= 30) {
    // Between 14-30 days: scale between 12 hour and 24 hour strides
    const scale = (totalDays - 14) / (30 - 14);
    const hourStride = Math.round(12 + scale * (24 - 12));
    return { minutes: hourStride * 60 };
  } else {
    // More than 30 days: use larger strides based on duration
    const daysStride = Math.ceil(totalDays / max_num_buckets);
    return { minutes: daysStride * 1440 };
  }
}

const sevenDaysAgo = dayjs.utc().subtract(7, 'days').toISOString();

export const calculateStartAndEndTimeFromTimeModel = (
  timeModel: DateTimeRangePickerValue,
  defaultStartTime = sevenDaysAgo
) => {
  let startDuration;
  let startTime = timeModel?.start_time;
  let endTime = timeModel?.end_time;

  if (timeModel?.duration) {
    startDuration = parseRelativeDate(timeModel.duration);
    if (startDuration) {
      startTime = dayjs
        .utc()
        .subtract(startDuration.value, startDuration.unit)
        .toISOString();
      endTime = dayjs.utc().toISOString();
    }
  }

  return {
    start_time: startTime ?? defaultStartTime,
    end_time: endTime ?? dayjs.utc().toISOString(),
  };
};

export const calculateDurationFromTimeModel = (
  timeModel: DateTimeRangePickerValue
) => {
  const startAndEndTime = calculateStartAndEndTimeFromTimeModel(timeModel);
  const duration = dayjs.duration(
    utcTime(startAndEndTime.end_time).diff(utcTime(startAndEndTime.start_time))
  );
  return duration;
};

export type ChartTimeFilter = {
  timeModel: DateTimeRangePickerValue;
  setTimeModel: (timeModel: DateTimeRangePickerValue) => void;
  customChartsParams: {
    start_time: string;
    end_time: string;
    stride: TimedeltaInput;
  };
};

export const useChartTimeFilter = ({
  max_num_buckets = MAX_NUM_BUCKETS_PER_CHART,
  min_num_buckets = MIN_NUM_BUCKETS_PER_CHART,
  prefer_full_days = false,
  customStride,
  timeRange = { duration: '7d' },
}: {
  max_num_buckets?: number;
  min_num_buckets?: number;
  prefer_full_days?: boolean;
  customStride?: TimedeltaInput;
  timeRange?: DateTimeRangePickerValue;
}): ChartTimeFilter => {
  const [timeModel, setTimeModel] =
    useState<DateTimeRangePickerValue>(timeRange);

  const timeFilter = useMemo(() => {
    return calculateStartAndEndTimeFromTimeModel(timeModel);
  }, [timeModel]);

  const customChartsParams = useMemo(() => {
    const stride =
      customStride ??
      (prefer_full_days
        ? calculateSmartBucketsPreferFullDays(
            timeFilter.start_time,
            timeFilter.end_time,
            max_num_buckets,
            min_num_buckets
          )
        : calculateSmartBuckets(
            timeFilter.start_time,
            timeFilter.end_time,
            max_num_buckets
          ));

    return {
      start_time: timeFilter.start_time,
      end_time: timeFilter.end_time,
      stride,
    };
  }, [
    customStride,
    prefer_full_days,
    timeFilter.start_time,
    timeFilter.end_time,
    max_num_buckets,
    min_num_buckets,
  ]);

  return {
    timeModel,
    setTimeModel,
    customChartsParams,
  };
};
