import { ArrowsPointingOutIcon } from '@heroicons/react/24/outline';
import {
  AlertCircleIcon,
  InfoCircleIcon,
  SearchLgIcon,
} from '@langchain/untitled-ui-icons';
import { curveBasis, curveLinear } from '@visx/curve';
import { scaleBand } from '@visx/scale';
import { Circle } from '@visx/shape';
import { useTooltip } from '@visx/tooltip';
import { timeFormat } from '@visx/vendor/d3-time-format';
import {
  AnimatedBarSeries,
  AnimatedLineSeries,
  Axis,
  Tooltip as ChartTooltip,
  DataContext,
  Grid,
  XYChart,
  darkTheme,
  lightTheme,
} from '@visx/xychart';

import dayjs from 'dayjs';
import { dequal } from 'dequal';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDebounce } from 'use-debounce';

import { DateTimeRangePicker } from '@/components/DateTimeRangePicker/DateTimeRangePicker';
import { DeleteModal } from '@/components/Delete';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { GroupedTabOption, GroupedTabs } from '@/components/GroupedTabs';
import { SplitViewPane } from '@/components/SplitViewPane';
import useToast from '@/components/Toast';
import { TruncatedTextWithTooltip } from '@/components/TruncatedTextWithTooltip.tsx';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useCustomChartsPreview } from '@/hooks/useSwr';
import {
  CustomChartDataPoint,
  CustomChartDataSchema,
  CustomChartExistingInfo,
  CustomChartMetric,
  CustomChartPreviewSchema,
  CustomChartPreviewThresholdConfig,
  CustomChartSchema,
  CustomChartSeriesSchema,
  CustomChartType,
  RunStatsGroupBy,
} from '@/types/schema';
import { apiChartsPath } from '@/utils/constants';
import { getAdjustedYDomain } from '@/utils/get-adjusted-y-domain';
import { cn } from '@/utils/tailwind';

import { ChartSkeleton } from './components/ChartSkeleton';
import { ChartTooltipV2 } from './components/ChartTooltipContentV2';
import { CloneChartDialog } from './components/CloneChartDialog';
import {
  ChartWrapper,
  CustomChartFallback,
  CustomChartHeader,
  MoreActionsDropdown,
} from './components/CustomChartComponents';
import { CustomChartRunsPreview } from './components/CustomChartRunsPreview';
import { TruncatedText } from './components/TruncatedText';
import {
  ACTION_ICON_STYLE,
  LEGEND_INDICATOR_WIDTH,
  defaultValues,
} from './constants';
import {
  ChartTimeFilter,
  calculateDurationFromTimeModel,
  useChartTimeFilter,
} from './hooks/useChartTimeFilter';
import {
  canNavigateToRunsTable,
  convertChartToFormInput,
  ensureUTCFormatTimestamp,
  formatTickLabel,
  getSeriesDisplayName,
  getValueFromDataPoint,
  noDataChart,
} from './utils/CustomChartsUtils.utils';
import {
  CHART_TICKS,
  COST_METRICS,
  GRID_CHART_HEIGHT,
  GRID_CHART_MARGIN,
  METRICS_TO_UNIT_LABEL,
  PERCENTAGE_METRICS,
  currencyFormatter,
  numberFormatter,
} from './utils/constants';
import { getSeriesColor } from './utils/getSeriesColor';
import { ChartDataProps } from './utils/types';
import { useChartData } from './utils/useChartData';

const Series = ({
  type,
  series,
  seriesData,
  color,
  valueAccessor,
  smoothCurve,
}: {
  type: CustomChartType;
  series: CustomChartSeriesSchema;
  seriesData: CustomChartDataSchema[];
  color: string;
  valueAccessor: (
    d: CustomChartDataPoint,
    metric: CustomChartMetric
  ) => number | null;

  smoothCurve?: boolean;
}) => {
  if (seriesData.length === 0) {
    return null;
  }

  const seriesProps = {
    dataKey: series.id,
    data: seriesData,
    xAccessor: (d) => {
      return d.timestamp;
    },
    yAccessor: (d) => {
      return valueAccessor(d.value, series.metric);
    },
    stroke: color,
    colorAccessor: () => color,
    ...(type === 'line' && { curve: smoothCurve ? curveBasis : curveLinear }),
  };

  return type === 'line' ? (
    <AnimatedLineSeries {...seriesProps} />
  ) : (
    <AnimatedBarSeries {...seriesProps} />
  );
};

const getCoordsFromDatum = (datum: CustomChartDataSchema, dateScale) => {
  const xCoord = dateScale?.(datum.timestamp);
  return { xCoord: xCoord + dateScale.bandwidth() / 2, yCoord: 75 }; // Keep yCoord fixed - better to just keep it in the center of the chart rather than having it jump around
};

const ThresholdAnnotation = ({
  thresholdConfig,
}: {
  thresholdConfig?: CustomChartPreviewThresholdConfig | null;
}) => {
  const { margin, xScale, yScale, innerHeight, innerWidth } =
    useContext(DataContext);
  if (
    !thresholdConfig ||
    !xScale ||
    !yScale ||
    !innerHeight ||
    !margin ||
    !(typeof thresholdConfig.data.value === 'number')
  )
    return null;
  const { timestamp, value: thresholdValue } = thresholdConfig.data;
  return thresholdConfig.type === 'lte' ? (
    <rect
      x={Number(xScale(timestamp as any))}
      y={Number(yScale(thresholdValue))}
      width={innerWidth}
      height={Math.abs(Number(yScale(0)) - Number(yScale(thresholdValue)))}
      fill={thresholdConfig.color}
    />
  ) : (
    <rect
      x={Number(xScale(timestamp as any))}
      y={0}
      width={innerWidth}
      height={Number(yScale(thresholdValue))}
      fill={thresholdConfig.color}
    />
  );
};

const ChartDataTabHeaderOrWarning = ({
  overrideGlobalGroupBy,
  needsGroupTabs,
  value,
  onChange,
  options,
}: {
  overrideGlobalGroupBy?: boolean;
  needsGroupTabs: boolean;
  value: string;
  onChange: (value: string) => void;
  options: GroupedTabOption<string>[];
}) => {
  return (
    <>
      {/* Show override warning if this chart has it configured */}
      {overrideGlobalGroupBy === true && (
        <div className="my-1 flex items-center gap-3 rounded-sm bg-tertiary p-1.5 pl-3">
          <InfoCircleIcon className="h-4 w-4 text-tertiary" />
          <h3 className="text-xxs font-normal leading-4 text-secondary">
            This chart has grouping built in, so global 'Group by' settings
            won't apply
          </h3>
        </div>
      )}

      {/* Show tabs if this chart needs them */}
      {needsGroupTabs && (
        <div className="mb-1 mt-2 flex">
          <GroupedTabs
            value={value}
            onChange={onChange}
            options={options}
            className="text-xs"
          />
        </div>
      )}
    </>
  );
};

export const ErrorBoundedChartData = (props: ChartDataProps) => {
  const chartHasNoData = useMemo(() => noDataChart(props.chart), [props.chart]);
  if (props.isLoading) {
    return <ChartSkeleton className="h-[284px] w-full" />;
  }
  if (chartHasNoData) {
    return (
      <CustomChartFallback
        icon={<SearchLgIcon className="size-6 text-disabled" />}
        message="No data available for the selected time range."
      />
    );
  }
  return (
    <ErrorBoundary
      fallback={(error) => (
        <CustomChartFallback
          icon={<AlertCircleIcon className="size-6 text-disabled" />}
          message={`Error showing chart: ${error.message}`}
        />
      )}
    >
      <ChartData {...props} />
    </ErrorBoundary>
  );
};

const ChartData = ({
  chart,
  type,
  timeFilter,
  thresholdConfig,
  hideLegend = false,
  isLastColumn,
  forceV1Tooltip,
  smoothCurve,
  groupBySelection,
  activeGroup,
  setActiveGroup,
  seriesColorsMap,
}: ChartDataProps) => {
  const [tooltipFrozen, setTooltipFrozen] = useState(false);

  const useNewTooltip = !forceV1Tooltip;

  const {
    tooltipData,
    tooltipLeft,
    tooltipTop,
    tooltipOpen,
    showTooltip,
    hideTooltip,
  } = useTooltip({});

  const containerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const { needsGroupTabs, uniqueSeriesNames, chartDataToRender, legendItems } =
    useChartData({
      chart,
      activeGroup,
      setActiveGroup,
      seriesColorsMap,
    });

  // Handle ESC key press to dismiss the tooltip
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && tooltipFrozen) {
        setTooltipFrozen(false);
        hideTooltip();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node) &&
        tooltipFrozen
      ) {
        setTooltipFrozen(false);
        hideTooltip();
      }
    };

    if (tooltipFrozen) {
      window.addEventListener('keydown', handleKeyDown);
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [tooltipFrozen, hideTooltip]);

  const { isDarkMode } = useColorScheme();

  const colorScale = useCallback(
    (key: string) => {
      // First check if the series/category is named "Success" or "Error"
      const seriesName =
        chartDataToRender.series.find((s) => s.id === key)?.name || key;

      return (
        seriesColorsMap[getSeriesDisplayName(seriesName, key)] ??
        getSeriesColor(seriesName, key)
      );
    },
    [chartDataToRender.series, seriesColorsMap]
  );

  const renderSeriesData = useCallback(() => {
    if (!chartDataToRender.series.length || !chartDataToRender.data.length)
      return null;

    // Create a map of series data by series id
    const seriesDataMap = chartDataToRender.data.reduce((acc, data) => {
      const seriesId = data.series_id;
      if (!acc[seriesId]) {
        acc[seriesId] = [];
      }
      acc[seriesId].push(data);
      return acc;
    }, {} as Record<string, CustomChartDataSchema[]>);

    return chartDataToRender.series.map((series) => {
      const seriesData = seriesDataMap[series.id] || [];
      if (seriesData.length === 0) {
        return null;
      }

      return (
        <Series
          key={`${series.id}-${type}`}
          type={type}
          series={series}
          seriesData={seriesData}
          color={colorScale(series.id)}
          valueAccessor={getValueFromDataPoint}
          smoothCurve={smoothCurve}
        />
      );
    });
  }, [chartDataToRender, type, colorScale, smoothCurve]);

  const { min: minY, max: maxY } = useMemo(() => {
    return chartDataToRender.data.reduce(
      (acc, d) => {
        let value: number | null = null;

        value = getValueFromDataPoint(
          d.value,
          chartDataToRender.series[0].metric
        );

        if (value != null) {
          acc.min = Math.min(acc.min, value);
          acc.max = Math.max(acc.max, value);
        }
        return acc;
      },
      { min: Infinity, max: 0 }
    );
  }, [chartDataToRender.data, chartDataToRender.series]);

  const { domain, numTicks } = getAdjustedYDomain(minY, maxY);

  const yScale = useMemo(
    () => ({
      type: 'linear' as const,
      domain,
      nice: true,
    }),
    [domain]
  );

  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  const innerHeight =
    GRID_CHART_HEIGHT - GRID_CHART_MARGIN.bottom - GRID_CHART_MARGIN.top;

  // Use ResizeObserver to track container width for tooltip positioning
  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      if (entries.length > 0) {
        const { width } = entries[0].contentRect;
        setDimensions({
          width: width,
          height: innerHeight,
        });
      }
    });

    resizeObserver.observe(containerRef.current);
    return () => resizeObserver.disconnect();
  }, [innerHeight]);

  const dateScale = useMemo(() => {
    const timestamps = Array.from(
      new Set(chartDataToRender.data.map((d) => d.timestamp))
    );

    timestamps.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

    return scaleBand({
      range: [
        GRID_CHART_MARGIN.left,
        dimensions.width - GRID_CHART_MARGIN.right,
      ],
      domain: timestamps, // Use the string timestamps as discrete categories
      align: 0.5,
    });
  }, [dimensions.width, chartDataToRender.data]);

  // Calculate min and max timestamps outside the tickFormat callback
  const { minTimestamp, maxTimestamp } = useMemo(() => {
    const timestamps = chart.data.map((d) => d.timestamp);
    if (timestamps.length === 0) {
      return { minTimestamp: 0, maxTimestamp: 0 };
    }

    const timestampsInLocalTime = timestamps.map((t) =>
      dayjs(ensureUTCFormatTimestamp(t)).local().valueOf()
    );
    // Convert UTC timestamps to local time using dayjs
    const minTimestamp = Math.min(...timestampsInLocalTime);
    const maxTimestamp = Math.max(...timestampsInLocalTime);

    return { minTimestamp, maxTimestamp };
  }, [chart.data]);

  const handlePointerMove = useNewTooltip
    ? (event) => {
        // Only update tooltip if not frozen
        if (!tooltipFrozen) {
          const datum = event.datum as CustomChartDataSchema;

          const { xCoord, yCoord } = getCoordsFromDatum(datum, dateScale);

          if (xCoord && yCoord) {
            showTooltip({
              tooltipLeft: xCoord,
              tooltipTop: yCoord,
              tooltipData: { nearestDatum: datum, datumByKey: event.key },
            });
          }
        }
      }
    : undefined;

  const handlePointerLeave = useNewTooltip
    ? () => {
        // Only hide tooltip if not frozen
        if (!tooltipFrozen) {
          hideTooltip();
        }
      }
    : undefined;

  const isChartPreview = chart['id'] === undefined;

  const handleClick = useNewTooltip
    ? (event: { datum: CustomChartDataSchema; key: string }) => {
        if (isChartPreview) {
          return;
        }
        const datum = event.datum;

        if (tooltipFrozen) {
          setTooltipFrozen(false);
          hideTooltip();
        } else if (canNavigateToRunsTable(chart)) {
          const { xCoord, yCoord } = getCoordsFromDatum(datum, dateScale);
          if (xCoord && yCoord) {
            showTooltip({
              tooltipLeft: xCoord,
              tooltipTop: yCoord,
              tooltipData: { nearestDatum: datum, datumByKey: event.key },
            });
          }
          setTooltipFrozen(true);
        }
      }
    : undefined;

  return (
    <>
      {/* Top section with tabs/warnings - this will flex to fill available space */}
      <div className="flex-grow">
        <ChartDataTabHeaderOrWarning
          overrideGlobalGroupBy={
            chart.series[0].group_by?.set_by == 'series' && !!groupBySelection
          }
          needsGroupTabs={needsGroupTabs && uniqueSeriesNames.length > 0}
          value={activeGroup || uniqueSeriesNames[0]}
          onChange={(value) => setActiveGroup?.(value)}
          options={uniqueSeriesNames.map((v) => ({
            value: v,
            display: v,
          }))}
        />
      </div>
      <div className="flex flex-col">
        {/* Bottom section with chart - this will maintain its height */}
        <div className="relative">
          <div
            ref={containerRef}
            className={cn(canNavigateToRunsTable(chart) && 'cursor-pointer')}
            style={{ height: GRID_CHART_HEIGHT }}
          >
            <XYChart
              width={dimensions.width}
              height={GRID_CHART_HEIGHT}
              xScale={{ type: 'band' }}
              yScale={yScale}
              margin={GRID_CHART_MARGIN}
              theme={isDarkMode ? darkTheme : lightTheme}
              onPointerMove={handlePointerMove}
              onPointerOut={handlePointerLeave}
              onPointerUp={handleClick}
            >
              <Grid
                numTicks={numTicks}
                lineStyle={{
                  stroke: isDarkMode ? 'white' : 'black',
                  strokeOpacity: 0.05,
                }}
              />
              <Axis
                numTicks={CHART_TICKS}
                orientation="bottom"
                tickFormat={(timestamp: string) => {
                  // Use the pre-calculated min and max timestamps
                  return formatTickLabel(timestamp, minTimestamp, maxTimestamp);
                }}
                tickLineProps={{
                  strokeWidth: 1,
                }}
                labelOffset={
                  COST_METRICS.includes(chart.series[0]?.metric) ? 100 : 30
                }
                tickLabelProps={() => ({
                  textAnchor: 'middle',
                  dominantBaseline: 'text-before-edge',
                  fontSize: 12,
                  dy: '0.1em',
                  width: 100,
                })}
                axisLineClassName="stroke-gray-300 stroke-1"
                axisClassName="stroke-gray-300 stroke-1"
              />
              <Axis
                labelClassName="font-medium text-quaternary"
                orientation={'left'}
                tickFormat={(value: number) => {
                  return COST_METRICS.includes(chart.series[0]?.metric)
                    ? currencyFormatter.format(value)
                    : PERCENTAGE_METRICS.includes(chart.series[0]?.metric)
                    ? `${numberFormatter.format(value)}%`
                    : numberFormatter.format(value);
                }}
                label={METRICS_TO_UNIT_LABEL[chart.series[0]?.metric]}
                labelOffset={20}
                numTicks={numTicks}
                axisClassName="stroke-gray-300 stroke-1"
                tickLabelProps={() => ({
                  dx: '-0.25em',
                  dy: '0.25em',
                  fill: isDarkMode ? 'white' : 'black',
                  fontFamily: 'Arial',
                  fontSize: 10,
                  textAnchor: 'end',
                })}
              />

              <ThresholdAnnotation thresholdConfig={thresholdConfig} />
              {renderSeriesData()}
              {!useNewTooltip && (
                <ChartTooltip
                  snapTooltipToDatumX
                  snapTooltipToDatumY
                  showSeriesGlyphs
                  className="z-50"
                  renderGlyph={({ x, y, key }) => {
                    return type == 'line' ? (
                      <Circle
                        cx={x}
                        cy={y}
                        r={4}
                        fill={colorScale(key)}
                        strokeWidth={1}
                        key={key}
                      />
                    ) : null;
                  }}
                  renderTooltip={({ tooltipData }) => {
                    if (!tooltipData?.nearestDatum) {
                      return <div>No data</div>;
                    }
                    const tooltipDataObj = tooltipData?.nearestDatum
                      ?.datum as CustomChartDataSchema;

                    const date = new Date(tooltipDataObj.timestamp);
                    const formattedDate = timeFormat('%b %d, %Y')(date);

                    const seriesData = chartDataToRender.series.map(
                      (series) => {
                        const dataPoint = chartDataToRender.data.find(
                          (d) =>
                            d.series_id === series.id &&
                            d.timestamp === tooltipDataObj.timestamp
                        );
                        return {
                          id: series.id,
                          name: series.name,
                          value: dataPoint ? dataPoint.value : null,
                          color: colorScale(series.id),
                        };
                      }
                    );
                    return (
                      <ChartTooltipContent
                        formattedDate={formattedDate}
                        seriesData={seriesData}
                        metric={
                          chartDataToRender.series[0]?.metric ?? 'run_count'
                        }
                        valueAccessor={getValueFromDataPoint}
                      />
                    );
                  }}
                />
              )}
            </XYChart>
          </div>

          {tooltipOpen && tooltipData && useNewTooltip && (
            <ChartTooltipV2
              tooltipRef={tooltipRef}
              tooltipData={tooltipData}
              chart={chartDataToRender}
              colorScale={colorScale}
              tooltipLeft={tooltipLeft}
              tooltipTop={tooltipTop}
              isLastColumn={isLastColumn}
              dimensions={dimensions}
              tooltipFrozen={tooltipFrozen}
              setTooltipFrozen={setTooltipFrozen}
              totalChartDuration={calculateDurationFromTimeModel(
                timeFilter.timeModel
              )}
            />
          )}
        </div>
      </div>

      <ChartLegend legendItems={legendItems} hideLegend={hideLegend} />
    </>
  );
};

export const ChartLegend = ({
  hideLegend,
  legendItems,
}: {
  hideLegend: boolean;
  legendItems: { label: string; color: string }[];
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [overflowCount, setOverflowCount] = useState(0);
  const [isIndicatorHovered, setIsIndicatorHovered] = useState(false);
  useEffect(() => {
    if (!wrapperRef.current || hideLegend || legendItems.length <= 1) return;

    const calculateOverflow = () => {
      const container = wrapperRef.current;
      if (!container) return;

      const computedStyle = window.getComputedStyle(container);
      const paddingLeft = parseFloat(computedStyle.paddingLeft);
      const paddingRight = parseFloat(computedStyle.paddingRight);
      const totalPadding = paddingLeft + paddingRight;

      // Reserve space for the indicator by adding padding-right to account for indicator width
      const containerWidth = container.offsetWidth - totalPadding;
      const items = Array.from(container.children) as HTMLElement[];

      let totalWidth = 0;
      let visibleCount = 0;
      const gap = 8; // 2rem gap between items (from gap-2 class)

      // Account for the indicator width in our calculations
      const reservedSpace = overflowCount > 0 ? LEGEND_INDICATOR_WIDTH : 0;

      for (const item of items) {
        const itemWidth = item.offsetWidth;
        if (totalWidth + itemWidth + gap < containerWidth - reservedSpace) {
          totalWidth += itemWidth + gap;
          visibleCount++;
        } else {
          break;
        }
      }

      setOverflowCount(Math.max(0, legendItems.length - visibleCount));
    };

    // Calculate on mount and resize
    calculateOverflow();
    const resizeObserver = new ResizeObserver(calculateOverflow);
    resizeObserver.observe(wrapperRef.current);

    return () => resizeObserver.disconnect();
  }, [legendItems, hideLegend, overflowCount]);

  return (
    <div
      className={cn(
        'relative -m-1 mt-2 flex-none',
        (isIndicatorHovered || legendItems.length === 1) && 'h-9'
      )}
    >
      {!hideLegend && (
        <>
          <div
            onMouseLeave={() => setIsIndicatorHovered(false)}
            className={cn(
              'flex h-9 w-full flex-wrap gap-2 overflow-hidden rounded-md border border-transparent p-2 pr-9',
              isIndicatorHovered &&
                'absolute top-0 h-auto border-primary bg-background pr-2'
            )}
            ref={wrapperRef}
          >
            {legendItems.map(({ label, color }, idx) => (
              <div key={idx} className="flex items-center">
                <div
                  className="mr-2 h-3 w-3 rounded-full"
                  style={{ backgroundColor: color }}
                />
                <TruncatedTextWithTooltip
                  text={label}
                  className="max-w-[150px] text-xs"
                />
              </div>
            ))}
          </div>
          {overflowCount > 0 && !isIndicatorHovered && (
            <div
              onMouseEnter={() => setIsIndicatorHovered(true)}
              className="overflow-indicator absolute right-2 top-2 flex h-5 flex-none items-center rounded-md border border-primary p-1 text-xs"
            >
              <div className="text-xs">+{overflowCount}</div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

const ChartTooltipContent = ({
  formattedDate,
  seriesData,
  metric,
  valueAccessor,
}: {
  formattedDate: string;
  seriesData: {
    id: string;
    name: string;
    value: CustomChartDataPoint | null;
    color: string;
  }[];
  metric: CustomChartMetric;
  valueAccessor: (
    d: CustomChartDataPoint,
    metric: CustomChartMetric
  ) => number | null;
}) => {
  return (
    <div className="max-w-xs">
      <div className="z-50 mb-2 font-bold">{formattedDate}</div>
      {seriesData.map((series, idx) => {
        const value = series.value;
        const accessedValue =
          value || value === 0 ? valueAccessor(value, metric) : null;
        return (
          <div key={idx} className="mb-1 flex items-start">
            <div
              className="mr-2 mt-1 h-3 w-3 flex-shrink-0 rounded-full"
              style={{ backgroundColor: series.color }}
            ></div>
            <div className="flex flex-col font-normal">
              <TruncatedText
                text={series.name || series.id.slice(0, 8)}
                maxLength={25}
              />
              <span>
                {accessedValue == null
                  ? COST_METRICS.includes(metric)
                    ? '0'
                    : 'null'
                  : COST_METRICS.includes(metric)
                  ? currencyFormatter.format(accessedValue)
                  : numberFormatter.format(accessedValue)}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

const ExpandedChartModal = ({
  chart,
  dashboardId,
  isOpen,
  doClose,
  openExistingChart,
  setDeleteChartModalOpen,
  readOnly,
  hideProjectNames,
  chartGridTimeFilter,
  seriesColorsMap,
}: {
  chart: CustomChartSchema;
  dashboardId: string;
  isOpen: boolean;
  doClose: () => void;
  openExistingChart: (data: CustomChartExistingInfo) => void;
  setDeleteChartModalOpen: (value: boolean) => void;
  readOnly?: boolean;
  hideProjectNames?: boolean;
  chartGridTimeFilter?: ChartTimeFilter;
  seriesColorsMap: Record<string, string>;
}) => {
  return (
    <SplitViewPane
      title={
        <div className="flex w-full flex-row justify-between">
          {chart.title}
          {!readOnly && (
            <MoreActionsDropdown
              onEdit={() => {
                doClose();
                openExistingChart({ ...chart, section_id: dashboardId });
              }}
              onDelete={() => {
                setDeleteChartModalOpen(true);
              }}
              onClone={() => {
                doClose();
                openExistingChart({
                  ...chart,
                  section_id: dashboardId,
                  id: undefined,
                });
              }}
            />
          )}
        </div>
      }
      sidePaneId="expanded-chart-modal"
      open={isOpen}
      onClose={doClose}
      className="relative"
      hideArrows={true}
    >
      <ExpandedChartModalContent
        chart={chart}
        hideProjectNames={hideProjectNames}
        chartGridTimeFilter={chartGridTimeFilter}
        seriesColorsMap={seriesColorsMap}
      />
    </SplitViewPane>
  );
};

const ExpandedChartModalContent = ({
  chart,
  hideProjectNames,
  chartGridTimeFilter,
  seriesColorsMap,
}: {
  chart: CustomChartSchema;
  hideProjectNames?: boolean;
  chartGridTimeFilter?: ChartTimeFilter;
  seriesColorsMap: Record<string, string>;
}) => {
  const timeFilter = useChartTimeFilter({
    max_num_buckets: 101,
    min_num_buckets: 7,
  });

  const previewPayload = {
    chart: {
      series: chart.series,
      common_filters: chart.common_filters,
    },
    bucket_info: timeFilter.customChartsParams,
  };

  // only fetch preview data if the time model has changed
  const usePreviewData = useMemo(() => {
    return !dequal(timeFilter.timeModel, chartGridTimeFilter?.timeModel);
  }, [timeFilter.timeModel, chartGridTimeFilter?.timeModel]);

  const [debouncedPayload] = useDebounce(previewPayload, 500);
  const {
    data: chartPreviewData,
    error: previewError,
    isLoading: isPreviewLoading,
  } = useCustomChartsPreview(usePreviewData ? debouncedPayload : null);

  const chartWithMaybePreviewData = useMemo(() => {
    if (chartPreviewData?.data.length && !previewError && usePreviewData) {
      return {
        ...chart,
        data: chartPreviewData.data,
      };
    }
    return chart;
  }, [chart, chartPreviewData, previewError, usePreviewData]);

  const [activeGroup, setActiveGroup] = useState<string>('');

  const { chartDataToRender } = useChartData({
    chart: chartWithMaybePreviewData,
    activeGroup: '',
    setActiveGroup,
    seriesColorsMap,
  });

  const chartFormInput = useMemo(
    () => convertChartToFormInput(chartDataToRender, defaultValues),
    [chartDataToRender]
  );

  const metrics = chart.series.map((series) => series.metric);

  return (
    <div className="absolute inset-0 flex flex-col overflow-auto px-4">
      <div className="ml-8 mt-4">
        <DateTimeRangePicker
          value={timeFilter.timeModel}
          onChange={timeFilter.setTimeModel}
          hideAllTime
          context="dashboard"
        />
      </div>
      <div className="mx-8 mb-8 mt-4 flex flex-col gap-4">
        <ChartWrapper>
          <CustomChartHeader
            chart={chartDataToRender}
            hideProjectNames={hideProjectNames}
          />
          <ErrorBoundedChartData
            chart={chartDataToRender}
            type={chartDataToRender.chart_type}
            timeFilter={timeFilter}
            isLastColumn
            activeGroup={activeGroup}
            setActiveGroup={setActiveGroup}
            isLoading={usePreviewData ? isPreviewLoading : false}
            seriesColorsMap={seriesColorsMap}
          />
        </ChartWrapper>
        <CustomChartRunsPreview
          filters={{
            session: chartFormInput.sessionIds,
            filter: chartFormInput.filters.filter || '',
            tree_filter: chartFormInput.filters.treeFilter || '',
            trace_filter: chartFormInput.filters.traceFilter || '',
          }}
          dataSeries={chartFormInput.dataSeries}
          timeModel={timeFilter.timeModel}
          setTimeModel={timeFilter.setTimeModel}
          customChartsParams={timeFilter.customChartsParams}
          metrics={metrics}
          seriesColorsMap={seriesColorsMap}
        />
      </div>
    </div>
  );
};

export const CustomChartPreview = ({
  chart,
  timeFilter,
  error,
  thresholdConfig,
  isLoading,
  seriesColorsMap,
}: {
  chart: CustomChartPreviewSchema;
  timeFilter: ChartTimeFilter;
  error?: Error | null;
  thresholdConfig?: CustomChartPreviewThresholdConfig | null;
  isLoading?: boolean;
  seriesColorsMap: Record<string, string>;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <DateTimeRangePicker
        value={timeFilter.timeModel}
        onChange={timeFilter.setTimeModel}
        hideAllTime
        context="dashboard"
      />
      <ChartWrapper className="bg-tertiary">
        <CustomChartHeader chart={chart} hideProjectNames={true} />
        {error ? (
          <div className="text-error">{error.message}</div>
        ) : (
          <ErrorBoundedChartData
            chart={chart}
            type={chart.chart_type}
            timeFilter={timeFilter}
            thresholdConfig={thresholdConfig}
            isLastColumn
            isLoading={isLoading}
            seriesColorsMap={seriesColorsMap}
          />
        )}
      </ChartWrapper>
    </div>
  );
};

export const Chart = ({
  chart,
  dashboardId,
  customClass,
  openExistingChart,
  mutateChartData,
  timeFilter,
  hideExpandedView,
  readOnly,
  isLastColumn,
  forceV1Tooltip = false,
  hideProjectNames,
  groupBySelection,
  isLoading,
  hideChartHeader = false,
  seriesColorsMap,
}: {
  chart: CustomChartSchema;
  dashboardId: string;
  customClass?: string;
  openExistingChart: (data: CustomChartExistingInfo) => void;
  mutateChartData: () => void;
  timeFilter: ChartTimeFilter;
  hideExpandedView?: boolean;
  readOnly?: boolean;
  hideProjectNames?: boolean;
  forceV1Tooltip?: boolean;
  groupBySelection?: RunStatsGroupBy;
  isLoading?: boolean;
  isLastColumn?: boolean;
  hideChartHeader?: boolean;
  seriesColorsMap: Record<string, string>;
}) => {
  const { createToast } = useToast();
  const [deleteChartModalOpen, setDeleteChartModalOpen] = useState(false);
  const [expandChartModalOpen, setExpandChartModalOpen] = useState(false);
  const [chartToCloneId, setChartToCloneId] = useState<string | null>(null);

  const [activeGroup, setActiveGroup] = useState<string>('');
  useEffect(() => {
    if (!groupBySelection) {
      setActiveGroup('');
    }
  }, [groupBySelection]);

  return (
    <ChartWrapper className={customClass}>
      {!hideChartHeader && (
        <CustomChartHeader
          hideProjectNames={hideProjectNames}
          chart={chart}
          moreActions={
            <>
              {!hideExpandedView && (
                <ArrowsPointingOutIcon
                  className={cn(ACTION_ICON_STYLE, 'h-6 w-6')}
                  onClick={() => setExpandChartModalOpen(true)}
                />
              )}
              {!readOnly && (
                <MoreActionsDropdown
                  onEdit={() =>
                    openExistingChart({ ...chart, section_id: dashboardId })
                  }
                  onDelete={() => {
                    setDeleteChartModalOpen(true);
                  }}
                  onClone={() => setChartToCloneId(chart.id)}
                />
              )}
            </>
          }
        />
      )}
      <ErrorBoundedChartData
        chart={chart}
        type={chart.chart_type}
        timeFilter={timeFilter}
        isLastColumn={isLastColumn}
        forceV1Tooltip={forceV1Tooltip}
        groupBySelection={groupBySelection}
        activeGroup={activeGroup}
        setActiveGroup={setActiveGroup}
        isLoading={isLoading}
        seriesColorsMap={seriesColorsMap}
      />
      <DeleteModal
        endpoint={`${apiChartsPath()}/${chart.id}`}
        isOpen={deleteChartModalOpen}
        onSuccess={mutateChartData}
        doClose={() => setDeleteChartModalOpen(false)}
        onError={(error) => {
          createToast({
            title: 'Error deleting chart',
            description: error.message,
            type: 'error',
          });
        }}
        modalText="Are you sure you want to delete this chart?"
      />
      {!hideExpandedView && (
        <ExpandedChartModal
          chart={chart}
          dashboardId={dashboardId}
          isOpen={expandChartModalOpen}
          doClose={() => setExpandChartModalOpen(false)}
          openExistingChart={openExistingChart}
          setDeleteChartModalOpen={setDeleteChartModalOpen}
          readOnly={readOnly}
          hideProjectNames={hideProjectNames}
          chartGridTimeFilter={timeFilter}
          seriesColorsMap={seriesColorsMap}
        />
      )}
      <CloneChartDialog
        chartToCloneId={chartToCloneId}
        setChartToCloneId={setChartToCloneId}
      />
    </ChartWrapper>
  );
};
