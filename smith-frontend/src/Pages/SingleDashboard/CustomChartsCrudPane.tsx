import { LineChartUp01Icon } from '@langchain/untitled-ui-icons';
import { Button, FormLabel, Input, LinearProgress, Tooltip } from '@mui/joy';

import { useCallback, useEffect } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

import { FilterDialogContent } from '@/components/FilterBar/FilterBar';
import { Pane } from '@/components/Pane';
import { SearchModel } from '@/components/RunsTable/types';
import { DateTimeRangePickerValue } from '@/components/RunsTable/utils/getDateTimeRangeLabel';
import useToast from '@/components/Toast';
import {
  useCreateCustomChartMutation,
  useRunStats,
  useSessionMetadata,
  useSingleChartData,
  useUpdateCustomChartMutation,
} from '@/hooks/useSwr';
import {
  CustomChartExistingInfo,
  CustomChartMetric,
  CustomChartSeriesSchema,
  CustomChartType,
  RunStatsGroupBy,
  TimedeltaInput,
} from '@/types/schema';

import { CustomChartPreview } from './CustomChart';
import {
  ChartErrorMsg,
  ChartFormLabel,
  ChartFormSection,
  ChartSectionBand,
  ChartSubtext,
  CustomChartTypeButton,
  DataSplitSelector,
  FeedbackKeySelector,
  MetricSelector,
  ProjectsSelector,
  renderErrorMessages,
} from './components/CustomChartCrudPaneComponents';
import { CustomChartRunsPreview } from './components/CustomChartRunsPreview';
import { DocsButton } from './components/DocsButton';
import { defaultValues } from './constants';
import { useCustomChartsPreviewData } from './hooks/useCustomChartsPreviewData';
import {
  convertChartToFormInput,
  convertFilterFormat,
  createSeriesFromFilters,
} from './utils/CustomChartsUtils.utils';
import {
  CHART_TYPES,
  DataSeriesFilter,
  FEEDBACK_METRICS,
  METRICS,
  RELATED_METRICS_MAP,
} from './utils/constants';

export interface ChartFormInput {
  title: string;
  description: string;
  filters: SearchModel;
  dataSeries: DataSeriesFilter[];
  metric: CustomChartMetric | null;
  comparisonMetrics?: CustomChartMetric[];
  chartType: CustomChartType;
  sessionIds?: string[];
  feedbackKey?: string;
  sectionId?: string | null;
  customStride?: TimedeltaInput;
  timeRange?: DateTimeRangePickerValue;
  groupBySelection?: RunStatsGroupBy;
}

export const ChartsCrudPane = ({
  open,
  onSuccess,
  onClose,
  existingChart,
  seriesColorsMap,
}: {
  open: boolean;
  onSuccess: () => void;
  onClose: () => void;
  existingChart?: CustomChartExistingInfo;
  seriesColorsMap: Record<string, string>;
}) => {
  const { createToast } = useToast();

  const {
    control,
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
    reset,
  } = useForm<ChartFormInput>({
    mode: 'onChange',
    defaultValues,
  });

  const doClose = useCallback(() => {
    reset(defaultValues);
    onClose();
  }, [onClose, reset]);

  const { data: chartDataWithSeries, isLoading: isLoadingSeries } =
    useSingleChartData(
      {
        omit_data: true,
      },
      existingChart?.id ? existingChart.id : undefined,
      {
        revalidateOnFocus: false,
      }
    );

  // existing chart can either have an id or not
  // if it has an id it means we're editing an existing chart
  // if it doesn't have an id, it's a new chart but it has default values
  useEffect(() => {
    if (existingChart) {
      const combinedChart = {
        ...existingChart,
      };

      if (
        // use the fetched series for non group by charts
        chartDataWithSeries?.series?.length &&
        !chartDataWithSeries?.series?.[0].group_by
      ) {
        combinedChart.series = chartDataWithSeries.series;
      }

      // TODO(eric): Honestly should just refactor this to use chartDataWithSeries data,
      // instead of the one that's passed in from the charts pane.
      const existingData = convertChartToFormInput(
        combinedChart,
        defaultValues
      );

      reset(existingData);
    } else {
      reset(defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    existingChart?.title,
    existingChart?.description,
    existingChart?.chart_type,
    existingChart?.series,
    chartDataWithSeries?.series,
    open,
  ]);

  const { trigger: createTrigger, isMutating: isMutatingCreate } =
    useCreateCustomChartMutation({
      onError: (error) => {
        createToast({
          title: 'Error creating chart',
          description: error.message,
          error: true,
        });
      },
      onSuccess: () => {
        createToast({
          title: 'Chart created',
          description: 'Your chart has been created successfully',
          error: false,
        });
        onSuccess();
        doClose();
      },
    });
  const { trigger: updateTrigger, isMutating: isMutatingUpdate } =
    useUpdateCustomChartMutation(existingChart?.id ?? '', {
      onError: (error) => {
        createToast({
          title: 'Error updating chart',
          description: error.message,
          error: true,
        });
      },
      onSuccess: () => {
        createToast({
          title: 'Chart updated',
          description: 'Your chart has been updated successfully',
          error: false,
        });
        onSuccess();
        doClose();
      },
    });

  const formValues = watch();

  const {
    previewData,
    seriesPayload,
    previewDataError,
    previewTimeModel,
    setPreviewTimeModel,
    previewChartsParams,
    previewDataIsLoading,
  } = useCustomChartsPreviewData(formValues);

  const stats = useRunStats(
    formValues.sessionIds && formValues.sessionIds.length > 0
      ? {
          session: formValues.sessionIds,
          start_time: previewTimeModel?.start_time,
        }
      : null,
    undefined,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      revalidateIfStale: false,
    }
  );

  const metadata = useSessionMetadata(
    {
      k: 5,
      start_time: previewTimeModel?.start_time,
      root_runs_only: true,
    },
    formValues.sessionIds?.[0]
  );

  const dontShowPreview =
    previewData.length === 0 ||
    errors.comparisonMetrics ||
    errors.metric ||
    errors.sessionIds ||
    errors.filters ||
    errors.dataSeries ||
    errors.chartType ||
    errors.feedbackKey ||
    !formValues.sessionIds?.length ||
    !formValues.metric ||
    !formValues.chartType ||
    previewDataError;

  const onSubmit: SubmitHandler<ChartFormInput> = (data) => {
    if (!data.metric) {
      return;
    }
    const chartData = {
      title: data.title,
      description: data.description,
      series: createSeriesFromFilters({
        sessionIds: data.sessionIds,
        metric: data.metric,
        comparisonMetrics: data.comparisonMetrics,
        dataSeriesFilters: data.dataSeries,
        feedbackKey: FEEDBACK_METRICS.includes(data.metric)
          ? data.feedbackKey
          : undefined,
        groupBySelection: data.groupBySelection,
      }),
      common_filters: {
        ...convertFilterFormat(data.filters, 'seriesFilters'),
        session: data.sessionIds,
      },
      chart_type: data.chartType,
      section_id: data.sectionId ?? null,
      index: null,
    };

    try {
      if (existingChart?.id) {
        updateTrigger({
          json: {
            ...chartData,
            index: existingChart.index,
          },
        });
      } else {
        createTrigger({
          json: chartData,
        });
      }
    } catch (e) {
      createToast({
        title: `Error ${existingChart?.id ? 'updating' : 'creating'} chart`,
        description: (e as Error).message || 'Try again',
        error: true,
      });
    }
  };

  return (
    <Pane
      open={open}
      onClose={onClose}
      title={<div>{existingChart?.id ? 'Edit Chart' : 'New Chart'}</div>}
      className="p-0"
      dialogStyle={{
        overflowY: 'hidden',
      }}
    >
      <div className="inset-0 flex h-full flex-grow overflow-auto">
        <PanelGroup direction="horizontal" className="flex-1">
          <Panel
            defaultSize={50}
            minSize={30}
            className="flex h-[calc(100vh-77px)] flex-col overflow-y-auto"
          >
            <form
              onSubmit={handleSubmit(onSubmit)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              className="flex h-full flex-col overflow-hidden"
            >
              <div className="flex-grow overflow-y-auto py-4">
                <div className="flex flex-col gap-6">
                  <ChartFormSection>
                    <FormLabel>Name</FormLabel>
                    <Input
                      type="text"
                      {...register('title', { required: 'Name is required' })}
                    />
                    {errors.title && (
                      <ChartErrorMsg text={errors.title.message} />
                    )}
                  </ChartFormSection>
                  <ChartFormSection>
                    <FormLabel>Description</FormLabel>
                    <Input type="text" {...register('description')} />
                  </ChartFormSection>
                  <ChartSectionBand text="Select tracing projects and filter runs" />

                  {/* SESSION IDS */}
                  <Controller
                    name="sessionIds"
                    control={control}
                    rules={{
                      required: 'At least one project must be selected',
                      validate: (value) =>
                        (value?.length ?? 0) > 0 ||
                        'At least one project must be selected',
                    }}
                    render={({ field }) => (
                      <ChartFormSection>
                        <ChartFormLabel
                          text="Tracing project(s)"
                          tooltipText={
                            'Select the projects that contain the runs you want to chart.'
                          }
                          link="https://docs.smith.langchain.com/observability/how_to_guides/dashboards#select-tracing-projects-and-filter-runs"
                        />
                        <ProjectsSelector
                          selectedProjectIds={field.value ?? []}
                          onSelectedProjectIdsChange={field.onChange}
                        />
                        {errors?.sessionIds ? (
                          <ChartErrorMsg text={errors.sessionIds?.message} />
                        ) : (
                          <ChartSubtext text={'Required'} />
                        )}
                      </ChartFormSection>
                    )}
                  />
                  {formValues.sessionIds?.length != null &&
                    formValues.sessionIds.length > 0 && (
                      <>
                        <Controller
                          name="filters"
                          control={control}
                          render={({ field }) => (
                            <ChartFormSection>
                              <ChartFormLabel
                                text="Filtered on"
                                tooltipText={
                                  'Filters that apply to the entire chart.'
                                }
                                link="https://docs.smith.langchain.com/observability/how_to_guides/dashboards#filter-runs"
                              />
                              <div className="rounded-lg bg-secondary px-3 py-2">
                                <FilterDialogContent
                                  value={field.value}
                                  onChange={(value) =>
                                    field.onChange({
                                      filter: value.filter || undefined,
                                      traceFilter:
                                        value.traceFilter || undefined,
                                      treeFilter: value.treeFilter || undefined,
                                    })
                                  }
                                  stats={stats.data}
                                  setFilterBarOpen={() => {}}
                                  showSaveFilters={false}
                                  hideAIQuery={true}
                                  hideFilterTitle={true}
                                  updateEveryChange={true}
                                />
                              </div>
                            </ChartFormSection>
                          )}
                        />
                        <ChartSectionBand text="Pick a metric" />

                        {/* METRIC */}
                        <Controller
                          name="metric"
                          control={control}
                          rules={{ required: 'Please select a metric' }}
                          render={({ field }) => (
                            <ChartFormSection>
                              <ChartFormLabel
                                text="Choose metric"
                                tooltipText="The main metric to measure, this determines y-axis unit"
                                link="https://docs.smith.langchain.com/observability/how_to_guides/dashboards#pick-a-metric"
                              />
                              <MetricSelector
                                value={field.value || undefined}
                                onChange={(value) => {
                                  // clear feedback key if metric is changed from feedback to non-feedback
                                  if (
                                    field.value &&
                                    FEEDBACK_METRICS.includes(field.value) &&
                                    value &&
                                    !FEEDBACK_METRICS.includes(value)
                                  ) {
                                    setValue('feedbackKey', undefined);
                                  }
                                  // clear comparison metrics if metric is changed
                                  field.onChange(value);
                                  setValue('comparisonMetrics', undefined);
                                }}
                                options={Object.entries(METRICS).map(
                                  ([value, label]) => ({
                                    value: value as CustomChartMetric,
                                    label: label,
                                    disabled:
                                      value === 'feedback_values'
                                        ? formValues.dataSeries.length > 0
                                        : false,
                                  })
                                )}
                                disabled={false}
                              />
                              {errors.metric ? (
                                <ChartErrorMsg text={errors.metric.message} />
                              ) : (
                                <ChartSubtext text={'Required'} />
                              )}
                            </ChartFormSection>
                          )}
                        />

                        {/* COMPARISON METRICS */}
                        {formValues.metric &&
                          RELATED_METRICS_MAP[formValues.metric].length > 0 && (
                            <Controller
                              name="comparisonMetrics"
                              control={control}
                              render={({ field }) => {
                                const disabled =
                                  formValues.dataSeries.length > 0 ||
                                  Boolean(formValues.groupBySelection);
                                return (
                                  <div className="flex">
                                    <div className="ml-10 h-12 w-5 rounded-bl-lg border-b border-l border-primary"></div>
                                    <ChartFormSection className="pl-3">
                                      <ChartFormLabel
                                        text="Compare to"
                                        tooltipText="Select a metric to compare to the main metric. This will create a new data series on the chart."
                                        className="text-sm font-normal"
                                      />

                                      <MetricSelector
                                        value={
                                          field.value
                                            ? field.value[0]
                                            : undefined
                                        }
                                        onChange={(value) =>
                                          value
                                            ? field.onChange([value])
                                            : field.onChange(undefined)
                                        }
                                        options={
                                          RELATED_METRICS_MAP[
                                            formValues.metric
                                              ? formValues.metric
                                              : ''
                                          ]
                                        }
                                        placeholder="Select related metric..."
                                        disabled={disabled}
                                      />

                                      <ChartSubtext
                                        text={
                                          disabled
                                            ? 'Cannot add comparison metric when splitting data'
                                            : 'Optional'
                                        }
                                      />
                                    </ChartFormSection>
                                  </div>
                                );
                              }}
                            />
                          )}

                        {/* FEEDBACK KEY */}
                        {formValues.metric &&
                          FEEDBACK_METRICS.includes(formValues.metric) && (
                            <Controller
                              name="feedbackKey"
                              control={control}
                              render={({ field }) => (
                                <ChartFormSection>
                                  <FormLabel>Feedback key</FormLabel>
                                  <FeedbackKeySelector
                                    value={field.value}
                                    onChange={(value) => field.onChange(value)}
                                    options={
                                      stats.data?.run_facets
                                        ?.filter(
                                          (f) => f.key === 'feedback_key'
                                        )
                                        .map((f) => f.value) ?? []
                                    }
                                  />
                                  <ChartSubtext text={'Required'} />
                                </ChartFormSection>
                              )}
                            />
                          )}
                        {formValues.dataSeries.length > 0 &&
                          formValues.metric &&
                          RELATED_METRICS_MAP[formValues.metric].length > 0 && (
                            <ChartFormSection>
                              <ChartSubtext
                                text={
                                  'Remove data series to compare metrics or to use "Feedback Values" metric'
                                }
                              />
                            </ChartFormSection>
                          )}
                        <ChartSectionBand
                          text="Choose how to split the data"
                          link="https://docs.smith.langchain.com/observability/how_to_guides/dashboards#split-the-data"
                        />
                        {isLoadingSeries ? (
                          <div className="flex flex-col gap-3 px-6">
                            <LinearProgress />
                          </div>
                        ) : (
                          <div className="flex flex-col gap-3 px-6">
                            <DataSplitSelector
                              control={control}
                              stats={stats}
                              disableDataSplits={Boolean(
                                formValues.comparisonMetrics
                              )}
                              disableDataSeries={Boolean(
                                formValues.feedbackKey
                              )}
                              metadata={metadata}
                              seriesColorsMap={seriesColorsMap}
                            />
                          </div>
                        )}
                        <ChartSectionBand text="Pick a chart type" />

                        {/* CHART TYPE */}
                        <Controller
                          name="chartType"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <ChartFormSection>
                              <div className={`mb-2 flex w-full gap-4`}>
                                {CHART_TYPES.map((option) => (
                                  <CustomChartTypeButton
                                    key={option.label}
                                    option={option}
                                    isSelected={option.value === field.value}
                                    onChange={field.onChange}
                                  />
                                ))}
                              </div>
                            </ChartFormSection>
                          )}
                        />
                      </>
                    )}
                </div>
              </div>

              <div className="sticky bottom-0 flex justify-between border-t border-secondary bg-[var(--joy-palette-background-body)] px-6 py-4">
                <DocsButton
                  className="p-3"
                  link="https://docs.smith.langchain.com/observability/how_to_guides/dashboards#chart-configuration"
                />
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    color={'primary'}
                    variant="outlined"
                    onClick={doClose}
                  >
                    Cancel
                  </Button>
                  <Tooltip
                    title={!isValid ? renderErrorMessages(errors) : ''}
                    placement="top"
                  >
                    <div>
                      <Button
                        type="submit"
                        color={'primary'}
                        loading={isMutatingCreate || isMutatingUpdate}
                      >
                        {existingChart?.id ? 'Save' : 'Create'}
                      </Button>
                    </div>
                  </Tooltip>
                </div>
              </div>
            </form>
          </Panel>
          <PanelResizeHandle className="group relative flex items-stretch text-secondary data-[resize-handle-active]:text-ls-blue">
            <div className="w-[1px] bg-tertiary" />
            <div className="absolute inset-y-0 -left-[4px] z-10 w-[9px] opacity-0" />
            <div className="absolute inset-y-0 w-[2px] bg-current opacity-0 transition-all group-hover:opacity-100" />
          </PanelResizeHandle>
          <Panel className="h-[calc(100vh-77px)] overflow-hidden bg-secondary">
            <div className="h-full overflow-y-auto p-8">
              {dontShowPreview ? (
                <div className="flex h-full flex-col items-center justify-center">
                  <LineChartUp01Icon className="size-10 text-tertiary" />
                  <p className="text-lg font-medium text-tertiary">
                    Add a project and a metric to preview chart
                  </p>
                </div>
              ) : (
                <div className="flex flex-col gap-4">
                  <CustomChartPreview
                    chart={{
                      title: formValues.title,
                      description: formValues.description,
                      chart_type: formValues.chartType,
                      series:
                        seriesPayload?.series as CustomChartSeriesSchema[],
                      data: previewData,
                      group_by: formValues.groupBySelection,
                    }}
                    timeFilter={{
                      timeModel: previewTimeModel,
                      setTimeModel: setPreviewTimeModel,
                      customChartsParams: previewChartsParams,
                    }}
                    error={previewDataError}
                    isLoading={previewDataIsLoading}
                    seriesColorsMap={seriesColorsMap}
                  />
                  <CustomChartRunsPreview
                    filters={{
                      filter: formValues.filters.filter ?? '',
                      tree_filter: formValues.filters.treeFilter ?? '',
                      trace_filter: formValues.filters.traceFilter ?? '',
                      session: formValues.sessionIds,
                    }}
                    dataSeries={formValues.dataSeries}
                    customChartsParams={previewChartsParams}
                    timeModel={previewTimeModel}
                    setTimeModel={setPreviewTimeModel}
                    metrics={formValues.metric ? [formValues.metric] : []}
                    seriesColorsMap={seriesColorsMap}
                  />
                </div>
              )}
            </div>
          </Panel>
        </PanelGroup>
      </div>
    </Pane>
  );
};
