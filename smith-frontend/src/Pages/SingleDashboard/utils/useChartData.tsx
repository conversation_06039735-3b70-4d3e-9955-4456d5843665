import { Dispatch, SetStateAction, useEffect, useMemo } from 'react';

import {
  CustomChartDataSchema,
  CustomChartPreviewSchema,
  CustomChartSchema,
  CustomChartSeriesSchema,
} from '@/types/schema';

import { getSeriesDisplayName } from './CustomChartsUtils.utils';
import { getSeriesColor } from './getSeriesColor';

/**
 * Extracts unique series names from chart series and determines if group tabs are needed
 */
export const getSeriesInfo = (
  data: CustomChartDataSchema[],
  series: CustomChartSeriesSchema[]
): {
  hasGroups: boolean;
  uniqueSeriesNames: string[];
} => {
  // Check if data points have groups
  const hasGroups = data.some((d) => d.group);

  // Extract unique series names
  const uniqueSeriesNamesSet = new Set<string>();
  series.forEach((s) => {
    if (s.name) uniqueSeriesNamesSet.add(s.name);
  });

  // Remove empty strings and convert to array
  uniqueSeriesNamesSet.delete('');
  const uniqueSeriesNames = Array.from(uniqueSeriesNamesSet);

  return {
    hasGroups,
    uniqueSeriesNames,
  };
};

/**
 * Filters series and data based on active group
 */
export const filterChartData = (
  series: CustomChartSeriesSchema[],
  data: CustomChartDataSchema[],
  activeGroup: string | undefined
): {
  filteredSeries: CustomChartSeriesSchema[];
  filteredData: CustomChartDataSchema[];
} => {
  const filteredSeries = series.filter(
    (s) => !activeGroup || s.name === activeGroup
  );

  const seriesIds = filteredSeries.map((s) => s.id);
  const { filteredData, seriesIdToName } = data.reduce(
    (acc, d) => {
      if (seriesIds.includes(d.series_id)) {
        acc.filteredData.push(d);
        acc.seriesIdToName.set(d.series_id, d.group || '');
      }
      return acc;
    },
    {
      filteredData: [] as CustomChartDataSchema[],
      seriesIdToName: new Map<string, string>(),
    }
  );

  // we were renaming series always which led to a double renaming when expanded chart data
  // was passed to actual chart data.
  const maybeRenamedSeries = activeGroup
    ? filteredSeries.map((s) => ({
        ...s,
        name: seriesIdToName.get(s.id) || s.name,
      }))
    : filteredSeries;

  return { filteredSeries: maybeRenamedSeries, filteredData };
};

export const useChartData = ({
  chart,
  activeGroup,
  setActiveGroup,
  seriesColorsMap,
}: {
  chart: CustomChartSchema | CustomChartPreviewSchema;
  activeGroup?: string;
  setActiveGroup?: Dispatch<SetStateAction<string>>;
  seriesColorsMap: Record<string, string>;
}) => {
  // Get series information
  const { hasGroups, uniqueSeriesNames } = useMemo(
    () => getSeriesInfo(chart.data, chart.series),
    [chart.data, chart.series]
  );

  // Determine if we need group tabs
  const needsGroupTabs = useMemo(
    () =>
      hasGroups &&
      uniqueSeriesNames.length > 1 &&
      chart.series.some((s) => s.group_by?.set_by),
    [hasGroups, uniqueSeriesNames, chart.series]
  );

  // Set initial active group to first series name
  useEffect(() => {
    if (
      needsGroupTabs &&
      uniqueSeriesNames.length > 0 &&
      !activeGroup &&
      setActiveGroup
    ) {
      setActiveGroup(uniqueSeriesNames[0]);
    }
  }, [needsGroupTabs, uniqueSeriesNames, activeGroup, setActiveGroup]);

  const legendItems = useMemo(() => {
    if (!needsGroupTabs || !activeGroup) {
      // If no grouping needed, just map the filtered series
      return chart.series.map((s) => ({
        label: getSeriesDisplayName(s.name, s.id),
        color:
          seriesColorsMap[getSeriesDisplayName(s.name, s.id)] ??
          getSeriesColor(s.name, s.id),
      }));
    }

    const seriesGroupedByName = chart.series.reduce((acc, s) => {
      if (acc[s.name]) {
        acc[s.name].push(s);
      } else {
        acc[s.name] = [s];
      }
      return acc;
    }, {} as Record<string, CustomChartSeriesSchema[]>);

    const longestGroupSeries = Object.values(seriesGroupedByName).reduce(
      (acc, s) => {
        return s.length > acc.length ? s : acc;
      },
      seriesGroupedByName[Object.keys(seriesGroupedByName)[0]]
    );

    return longestGroupSeries.map((s) => ({
      label: getSeriesDisplayName(s.name, s.id),
      color:
        seriesColorsMap[getSeriesDisplayName(s.name, s.id)] ??
        getSeriesColor(s.name, s.id),
    }));
  }, [needsGroupTabs, chart.series, activeGroup, seriesColorsMap]);

  // Filter series and data based on active group
  const { filteredSeries, filteredData } = useMemo(
    () => filterChartData(chart.series, chart.data, activeGroup),
    [chart.series, chart.data, activeGroup]
  );

  // The data to use for rendering
  const chartDataToRender = useMemo(
    () => ({
      ...chart,
      series: filteredSeries,
      data: filteredData,
    }),
    [chart, filteredSeries, filteredData]
  );

  return {
    hasGroups,
    needsGroupTabs,
    uniqueSeriesNames,
    chartDataToRender,
    legendItems,
  };
};
