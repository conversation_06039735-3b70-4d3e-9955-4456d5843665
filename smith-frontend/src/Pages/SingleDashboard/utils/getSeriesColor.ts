import { useEffect, useState } from 'react';

import {
  CustomChartPreviewSchema,
  CustomChartSchema,
  CustomChartSeriesSchema,
  CustomChartsSubSectionSchema,
} from '@/types/schema';
import { getContrastingColorByString } from '@/utils/get-contrasting-colors';

import { getSeriesDisplayName } from './CustomChartsUtils.utils';

export const getSeriesColor = (seriesName: string, key?: string) => {
  if ([key?.toLowerCase(), seriesName.toLowerCase()].includes('success'))
    return 'rgba(46, 204, 113, 0.7)'; // Green color for Success
  if ([key?.toLowerCase(), seriesName.toLowerCase()].includes('error'))
    return 'rgba(228, 26, 28, 0.7)'; // Red color for Error

  const keyToUse = getSeriesDisplayName(seriesName, key);

  return getContrastingColorByString(keyToUse, 0.8);
};

const REORDERED_COLORS = [
  // Alternating between blues, purples, and oranges for maximum contrast
  'rgba(0, 122, 255, 0.7)', // Bright Blue
  'rgba(148, 0, 211, 0.7)', // Dark Violet
  'rgba(255, 165, 0, 0.7)', // Orange
  'rgba(0, 199, 190, 0.7)', // Teal
  'rgba(153, 50, 204, 0.7)', // Dark Orchid
  'rgba(255, 140, 0, 0.7)', // Dark Orange
  'rgba(64, 156, 255, 0.7)', // Sky Blue
  'rgba(128, 0, 128, 0.7)', // Purple
  'rgba(255, 69, 0, 0.7)', // Red Orange
  'rgba(0, 168, 132, 0.7)', // Sea Green
  'rgba(147, 51, 234, 0.7)', // Indigo
  'rgba(255, 160, 122, 0.7)', // Light Salmon
  'rgba(0, 180, 216, 0.7)', // Ocean Blue
  'rgba(139, 0, 139, 0.7)', // Dark Magenta
  'rgba(255, 127, 80, 0.7)', // Coral
  'rgba(0, 119, 182, 0.7)', // Deep Blue
  'rgba(111, 0, 255, 0.7)', // Electric Indigo
  'rgba(255, 140, 105, 0.7)', // Salmon
  'rgba(0, 150, 199, 0.7)', // Steel Blue
  'rgba(147, 112, 219, 0.7)', // Medium Purple
  'rgba(0, 191, 255, 0.7)', // Deep Sky Blue
];

const containsSuccessOrError = (name: string) => {
  return ['success', 'error'].some((keyword) =>
    name.toLowerCase().includes(keyword)
  );
};

export const mapSeriesToColors = (
  series: CustomChartSeriesSchema[],
  existingColors: Record<string, string>
) => {
  const uniqueNames = [
    ...new Set(
      series
        .map((s) => getSeriesDisplayName(s.name, s.id))
        .filter((name) => {
          return !existingColors[name];
        })
    ),
  ];

  return uniqueNames
    .sort((a, b) => {
      if (containsSuccessOrError(a)) {
        return 1;
      }
      if (containsSuccessOrError(b)) {
        return -1;
      }
      return 0;
    })
    .reduce((acc, name, index) => {
      if (name.toLowerCase().includes('success')) {
        acc[name] = 'rgba(46, 204, 113, 0.7)'; // Green color for Success
      } else if (name.toLowerCase().includes('error')) {
        acc[name] = 'rgba(228, 26, 28, 0.7)'; // Red color for Error
      } else {
        const indexPlusExisting = index + Object.keys(existingColors).length;
        acc[name] =
          REORDERED_COLORS[indexPlusExisting % REORDERED_COLORS.length];
      }
      return acc;
    }, {} as Record<string, string>);
};

export const useSeriesColorsMap = ({
  dashboard,
  isPrebuilt,
}: {
  dashboard?: {
    sub_sections?: CustomChartsSubSectionSchema[];
    charts: CustomChartSchema[] | CustomChartPreviewSchema[];
  };
  isPrebuilt: boolean;
}) => {
  const [seriesColorsMap, setSeriesColorsMap] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    let allSeries: CustomChartSeriesSchema[] = [];
    if (isPrebuilt && dashboard?.sub_sections) {
      allSeries = dashboard.sub_sections.flatMap((section) =>
        section.charts.flatMap((chart) => chart.series)
      );
    } else {
      allSeries = dashboard?.charts?.flatMap((c) => c.series) || [];
    }
    const newSeriesColorsMap = mapSeriesToColors(allSeries, seriesColorsMap);

    if (Object.keys(newSeriesColorsMap).length > 0) {
      setSeriesColorsMap({ ...seriesColorsMap, ...newSeriesColorsMap });
    }
  }, [dashboard, isPrebuilt, seriesColorsMap]);

  return seriesColorsMap;
};
