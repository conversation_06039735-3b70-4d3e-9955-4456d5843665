import dayjs from 'dayjs';
import { Duration } from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';

import {
  Comparison,
  astToString,
  flattenAst,
  isSameComparison,
  stringToAst,
} from '@/components/RunsTable/filter/ast';
import { SearchModel } from '@/components/RunsTable/types';
import {
  CustomChartDataPoint,
  CustomChartDataSchema,
  CustomChartMetric,
  CustomChartPreviewSchema,
  CustomChartSchema,
  CustomChartSeriesCreate,
  CustomChartSeriesFilters,
  CustomChartSeriesSchema,
  CustomChartUpdateBase,
  CustomChartsSectionSchema,
  CustomChartsSubSectionSchema,
  RunStatsGroupBy,
  SessionMetadataResponse,
} from '@/types/schema';
import {
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
} from '@/utils/constants';

import { ChartFormInput } from '../CustomChartsCrudPane';
import {
  CHART_TICKS,
  DEFAULT_CHART_TYPE,
  DEFAULT_METADATA_TO_IGNORE,
  DataSeriesFilter,
  METRICS,
} from './constants';
import { TooltipDataPoint } from './types';

dayjs.extend(utc);

/**
 * Type guard to check if an object is a SearchModel
 */
function isSearchModel(
  filters: CustomChartSeriesFilters | SearchModel
): filters is SearchModel {
  return 'traceFilter' in filters;
}
/**
 * Converts a CustomChartSeriesFilters to a SearchModel
 */
function toSearchModel(filters: CustomChartSeriesFilters): SearchModel {
  return {
    filter: filters.filter || undefined,
    traceFilter: filters.trace_filter || undefined,
    treeFilter: filters.tree_filter || undefined,
  };
}

/**
 * Converts a SearchModel to a CustomChartSeriesFilters
 */
function toCustomChartSeriesFilters(
  filters: SearchModel
): CustomChartSeriesFilters {
  return {
    filter: filters.filter || undefined,
    trace_filter: filters.traceFilter || undefined,
    tree_filter: filters.treeFilter || undefined,
  };
}

/**
 * Converts between CustomChartSeriesFilters and SearchModel filters
 * The main difference is in the naming convention:
 * - CustomChartSeriesFilters uses snake_case (tree_filter, trace_filter)
 * - SearchModel uses camelCase (treeFilter, traceFilter)
 */
export function convertFilterFormat<T extends 'searchModel' | 'seriesFilters'>(
  filters: CustomChartSeriesFilters | SearchModel | undefined,
  targetFormat: T
): T extends 'searchModel' ? SearchModel : CustomChartSeriesFilters {
  // If the input is already in the target format, return it as is
  if (!filters) {
    return targetFormat === 'searchModel'
      ? ({
          filter: undefined,
          traceFilter: undefined,
          treeFilter: undefined,
        } as SearchModel)
      : ({
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
        } as CustomChartSeriesFilters);
  }

  if (
    (targetFormat === 'searchModel' && isSearchModel(filters)) ||
    (targetFormat === 'seriesFilters' && !isSearchModel(filters))
  ) {
    return filters as T extends 'searchModel'
      ? SearchModel
      : CustomChartSeriesFilters;
  }

  // Otherwise, convert to the target format
  return (
    targetFormat === 'searchModel'
      ? toSearchModel(filters as CustomChartSeriesFilters)
      : toCustomChartSeriesFilters(filters as SearchModel)
  ) as T extends 'searchModel' ? SearchModel : CustomChartSeriesFilters;
}

export function createSeriesFromFilters({
  sessionIds,
  metric,
  comparisonMetrics,
  dataSeriesFilters,
  feedbackKey,
  groupBySelection,
}: {
  sessionIds: string[] | undefined;
  metric: CustomChartMetric;
  comparisonMetrics?: CustomChartMetric[];
  dataSeriesFilters?: DataSeriesFilter[];
  feedbackKey?: string;
  groupBySelection?: RunStatsGroupBy;
}): CustomChartSeriesCreate[] {
  if (
    comparisonMetrics &&
    comparisonMetrics.length > 0 &&
    Object.keys(comparisonMetrics[0]).length > 0
  ) {
    return comparisonMetrics.concat(metric).map((comparisonMetric) => ({
      name: METRICS[comparisonMetric],
      metric: comparisonMetric,
      feedback_key: feedbackKey || null,
    }));
  } else if (
    dataSeriesFilters &&
    dataSeriesFilters.length > 0 &&
    Object.keys(dataSeriesFilters[0]).length > 0
  ) {
    return dataSeriesFilters.map((dataSeriesFilter) => ({
      name: dataSeriesFilter.name,
      filters: {
        ...convertFilterFormat(dataSeriesFilter.filters, 'seriesFilters'),
        session: sessionIds,
      },
      metric: metric,
      feedback_key: feedbackKey || null,
    }));
  } else {
    return [
      {
        name: METRICS[metric],
        metric: metric,
        feedback_key: feedbackKey || null,
        group_by: groupBySelection,
      },
    ];
  }
}

export function convertChartToFormInput(
  existingChart: CustomChartUpdateBase,
  defaultValues: ChartFormInput
): ChartFormInput {
  return {
    title: existingChart.title || '',
    description: existingChart.description || '',
    chartType: existingChart.chart_type || DEFAULT_CHART_TYPE,
    sectionId: existingChart.section_id,
    ...(existingChart.series
      ? existingChart.common_filters
        ? createChartDataFromSeries(
            existingChart.series,
            existingChart.common_filters
          )
        : createChartDataFromSeries(existingChart.series, {
            session: defaultValues.sessionIds,
          })
      : {
          metric: defaultValues.metric,
          filters: existingChart.common_filters
            ? convertFilterFormat(existingChart.common_filters, 'searchModel')
            : defaultValues.filters,
          dataSeries: defaultValues.dataSeries,
          sessionIds:
            existingChart.common_filters?.session || defaultValues.sessionIds,
        }),
  };
}

export function getSeriesDisplayName(seriesName: string, id?: string) {
  // series id is in format of either
  // ${uuid}
  // series-${seriesName}
  // series-${seriesName}:${groupByName}
  // series-${feedbackKey}:${feedbackValue}
  // series-${feedbackKey}:${feedbackValue}:${groupByName}
  // we always want to return the last part of the id because that's the most interesting
  // to display as legend and tooltip series name.
  // if there's no colon, return undefined and outer function should just use series name.
  const splitKey = id?.split(':').filter(Boolean) ?? [];

  const keyToUse =
    splitKey.length > 1 ? splitKey[splitKey.length - 1] : seriesName;

  return keyToUse;
}

/**
 * Extracts series-specific filters by removing common filters from each series filter
 * @param series The series data with filters
 * @param commonFilters The common filters to remove from each series
 * @returns Array of data series filters with only series-specific filters
 */
export function extractSeriesSpecificFilters(
  series: (CustomChartSeriesCreate | CustomChartSeriesSchema)[],
  commonFilters: CustomChartSeriesFilters
): DataSeriesFilter[] {
  return series.map((s) => {
    // Parse the series filter and common filter into ASTs
    const seriesFilterAst = stringToAst(s.filters?.filter || '');
    const commonFilterAst = stringToAst(commonFilters.filter || '');

    const name =
      s.group_by && 'id' in s ? getSeriesDisplayName(s.name, s.id) : s.name;

    // If there's no series filter, return empty filter
    if (!seriesFilterAst) {
      return {
        name,
        filters: {
          filter: undefined,
          treeFilter: undefined,
          traceFilter: undefined,
        },
      };
    }

    // If there's no common filter, use the series filter as is
    if (!commonFilterAst) {
      return {
        name,
        filters: convertFilterFormat(s.filters, 'searchModel'),
      };
    }

    // Flatten both ASTs to get individual comparisons
    const seriesFlatAst = flattenAst(seriesFilterAst);
    const commonFlatAst = flattenAst(commonFilterAst);

    if (!seriesFlatAst || !commonFlatAst) {
      return {
        name,
        filters: convertFilterFormat(s.filters, 'searchModel'),
      };
    }

    // Filter out comparisons that are in the common filter using isSameComparison helper
    const seriesOnlyComparisons = seriesFlatAst.operands.filter(
      (seriesComp) =>
        !commonFlatAst.operands.some((commonComp) =>
          isSameComparison(seriesComp, commonComp)
        )
    );

    // Create a new AST with only the series-specific comparisons
    const seriesOnlyAst =
      seriesOnlyComparisons.length > 0
        ? { operator: 'and', operands: seriesOnlyComparisons }
        : undefined;

    // Convert back to string
    const seriesOnlyFilter = astToString(seriesOnlyAst) || undefined;

    return {
      name,
      filters: {
        filter: seriesOnlyFilter,
        treeFilter: s.filters?.tree_filter,
        traceFilter: s.filters?.trace_filter,
      },
    };
  });
}

export function createChartDataFromSeries(
  series: CustomChartSeriesCreate[],
  commonFilters: CustomChartSeriesFilters
): {
  filters: SearchModel;
  sessionIds?: string[];
  metric: CustomChartMetric;
  dataSeries: DataSeriesFilter[];
  comparisonMetrics?: CustomChartMetric[];
  feedbackKey?: string;
  groupBySelection?: RunStatsGroupBy;
} {
  if (series.length === 0) {
    return {
      filters: convertFilterFormat(commonFilters, 'searchModel'),
      sessionIds: commonFilters.session,
      metric: 'run_count',
      dataSeries: [],
    };
  }

  const firstSeries = series[0];
  const metric = firstSeries.metric;
  const feedbackKey = firstSeries.feedback_key ?? undefined;
  const sessionIds = commonFilters.session;

  // Extract out series specific filters
  const dataSeriesFilters = extractSeriesSpecificFilters(series, commonFilters);
  const firstSeriesSpecificFilters = dataSeriesFilters[0];

  // If there's only one series and it does not have any series specific filters, we're done, return the base filters
  if (
    series.length === 1 &&
    !firstSeriesSpecificFilters?.filters?.filter &&
    !firstSeriesSpecificFilters?.filters?.treeFilter &&
    !firstSeriesSpecificFilters?.filters?.traceFilter
  ) {
    return {
      filters: convertFilterFormat(commonFilters, 'searchModel'),
      sessionIds,
      metric,
      feedbackKey,
      dataSeries: [],
      groupBySelection: undefined,
    };
  }

  // Check if we're dealing with comparison metrics by checking
  // if any series has a different metric and
  // if all series have the same filters
  const hasDifferentMetrics = series.some(
    (s) => s.metric !== firstSeries.metric
  );
  const hasSameFilters = series.every((s) =>
    series.every(
      (s2) =>
        s.filters?.filter === s2.filters?.filter &&
        s.filters?.tree_filter === s2.filters?.tree_filter &&
        s.filters?.trace_filter === s2.filters?.trace_filter
    )
  );

  if (hasDifferentMetrics && hasSameFilters) {
    // Get rid of the first one since it's the base metric
    const comparisonMetrics = series.slice(1).map((s) => s.metric);
    return {
      filters: convertFilterFormat(commonFilters, 'searchModel'),
      sessionIds,
      metric,
      comparisonMetrics,
      feedbackKey,
      dataSeries: [],
    };
  }

  // If not comparison metrics, we're dealing with data series or group by.
  // If the first series has a group by selection, we have a group by metadata chart
  if (firstSeries.group_by) {
    return {
      filters: convertFilterFormat(commonFilters, 'searchModel'),
      sessionIds,
      metric,
      feedbackKey,
      dataSeries: dataSeriesFilters,
      groupBySelection: firstSeries.group_by,
    };
  }

  return {
    filters: convertFilterFormat(commonFilters, 'searchModel'),
    sessionIds,
    metric,
    dataSeries: dataSeriesFilters,
    feedbackKey,
  };
}

export function combineFilters(...filters: (string | undefined)[]) {
  // Convert each filter string to an AST
  const asts = filters.map((filter) => stringToAst(filter));
  // Filter out any undefined ASTs and ensure we have valid AST objects
  const validAsts = asts.filter(
    (ast): ast is NonNullable<typeof ast> => ast !== undefined
  );

  if (validAsts.length === 0) {
    return undefined;
  }

  if (validAsts.length === 1) {
    return astToString(validAsts[0]);
  }

  const flattenedAst = flattenAst({
    operator: 'and',
    operands: validAsts,
  });
  let dedupedOperands: Partial<Comparison>[] = [];
  dedupedOperands =
    flattenedAst?.operands.reduce(
      (unique: Partial<Comparison>[], operand: Partial<Comparison>) => {
        if (!unique.some((existing) => isSameComparison(existing, operand))) {
          unique.push(operand);
        }
        return unique;
      },
      []
    ) ?? [];

  // Convert the combined AST back to a string
  return astToString({ operator: 'and', operands: dedupedOperands });
}

// Anything passed here should already be in UTC timezone, but we
// want to add a Z if it's not already there to ensure it's treated as such
export function ensureUTCFormatTimestamp(timestamp: string) {
  return timestamp.endsWith('Z') ? timestamp : timestamp + 'Z';
}

export function createTimeModel(
  totalChartDuration: Duration,
  timestamp: string,
  chart: CustomChartSchema | CustomChartPreviewSchema
) {
  const bucketSize =
    totalChartDuration.asMilliseconds() /
    (chart.data.length / chart.series.length);

  // Parse the timestamp while preserving UTC
  const parsedDate = new Date(ensureUTCFormatTimestamp(timestamp)); // Adding Z ensures it's treated as UTC
  const endTimeMs = parsedDate.getTime() + bucketSize;

  // Create new UTC date string without timezone conversion
  const endDate = new Date(endTimeMs);
  const endTimeStr = endDate.toISOString().slice(0, 19);

  const timeModel = {
    start_time: timestamp,
    end_time: endTimeStr,
  };
  return timeModel;
}

export function createSearchModel(
  chart: CustomChartSchema,
  relevantSeries: CustomChartSeriesSchema,
  feedbackValue?: string
) {
  let metricFilter: string | undefined = undefined;
  if (
    (relevantSeries.metric === 'feedback' ||
      relevantSeries.metric === 'feedback_score_avg') &&
    relevantSeries.feedback_key
  ) {
    metricFilter = `eq(feedback_key, "${relevantSeries.feedback_key}")`;
  } else if (
    relevantSeries.metric === 'feedback_values' &&
    feedbackValue &&
    relevantSeries.feedback_key
  ) {
    metricFilter = `and(eq(feedback_key, "${
      relevantSeries.feedback_key
    }"), eq(feedback_value, "${feedbackValue.replace(/"/g, '\\"')}"))`;
  } else if (relevantSeries.metric === 'error_rate') {
    metricFilter = "eq(status, 'error')";
  }

  const commonFilters = chart.common_filters;
  const seriesFilters = relevantSeries.filters;
  const searchModel = {
    filter: combineFilters(
      metricFilter,
      commonFilters.filter,
      seriesFilters?.filter
    ),
    tree_filter: combineFilters(
      commonFilters.tree_filter,
      seriesFilters?.tree_filter
    ),
    trace_filter: combineFilters(
      commonFilters.trace_filter,
      seriesFilters?.trace_filter
    ),
  };
  return searchModel;
}

export function generateChartPointRunsUrl(
  relevantSeries: CustomChartSeriesSchema,
  totalChartDuration: Duration,
  timestamp: string,
  chart: CustomChartSchema,
  organizationId: string | null,
  feedbackValue?: string
) {
  const sessionId =
    relevantSeries.filters?.session?.[0] ?? chart.common_filters.session?.[0];
  const timeModel = createTimeModel(totalChartDuration, timestamp, chart);
  const searchModel = createSearchModel(chart, relevantSeries, feedbackValue);
  const targetUrl = `/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${sessionId}?timeModel=${encodeURIComponent(
    JSON.stringify(timeModel)
  )}&searchModel=${encodeURIComponent(JSON.stringify(searchModel))}`;
  return targetUrl;
}

export function isDashboardReadOnly(dashboard?: CustomChartsSectionSchema) {
  return !!dashboard?.session_id;
}

export const getUniqueSessionIdsFromChart = (
  chart: CustomChartSchema | CustomChartPreviewSchema
) => {
  const allSeriesIds = chart.series.flatMap((s) => s.filters?.session ?? []);
  const allCommonSessionIds = chart['common_filters']?.session ?? [];

  return Array.from(new Set([...allSeriesIds, ...allCommonSessionIds]));
};

export function shouldShowEmptyState(
  charts: CustomChartSchema[],
  sub_sections?: CustomChartsSubSectionSchema[],
  dashboardId?: string,
  isLoading?: boolean
) {
  const hasNotLoaded = charts == null;
  const hasCharts = charts.length > 0;
  const hasSubSections = (sub_sections?.length ?? 0) > 0;
  const showEmptyState =
    ((!hasCharts && !hasSubSections) || !dashboardId) &&
    !hasNotLoaded &&
    !isLoading;
  return showEmptyState;
}

export const getCategories = (
  seriesData: CustomChartDataSchema[],
  categoryKey: string
) => {
  return Array.from(
    new Set(
      seriesData.flatMap((d) =>
        typeof d.value === 'object'
          ? Object.keys(d.value[categoryKey]?.values || {})
          : []
      )
    )
  );
};

export const canNavigateToRunsTable = (
  chart: CustomChartSchema | CustomChartPreviewSchema
): chart is CustomChartSchema => {
  return (
    'common_filters' in chart &&
    (chart.series[0].filters?.session?.length === 1 ||
      chart.common_filters?.session?.length === 1)
  );
};

export const getValueFromDataPoint = (
  value: CustomChartDataPoint,
  metric: CustomChartMetric,
  feedbackValue?: string
): number | null => {
  if (typeof value === 'number') {
    return /.*_rate/.test(metric) ? value * 100 : value;
  }

  if (value && typeof value === 'object') {
    const key = Object.keys(value)[0];
    if (key == undefined) {
      // if the key doesn't exist, return null for feedback_score_avg and 0 for count metrics
      return metric === 'feedback_score_avg' ? null : 0;
    }
    if (metric === 'feedback_score_avg') {
      return value[key].avg;
    } else if (metric === 'feedback') {
      return value[key].n;
    } else if (metric === 'feedback_values' && feedbackValue) {
      return value[key]['values'][feedbackValue] ?? 0;
    } else {
      // this should not happen unless we add a new feedback metric
      return 0;
    }
  }

  return 0;
};

// Compare two tooltip data points for sorting purposes
export const compareDataPoints = (
  a: TooltipDataPoint,
  b: TooltipDataPoint,
  feedbackKey: string | undefined
) => {
  const aValue = a.datum?.value;
  const bValue = b.datum?.value;
  if (typeof aValue === 'number' && typeof bValue === 'number') {
    return bValue - aValue;
  } else if (
    typeof aValue === 'object' &&
    typeof bValue === 'object' &&
    aValue &&
    bValue &&
    feedbackKey
  ) {
    const aAvg = aValue[feedbackKey]?.avg;
    const bAvg = bValue[feedbackKey]?.avg;
    if (aAvg != null && bAvg != null) {
      return bAvg - aAvg;
    }
  }
  return 0;
};

/**
 * Formats a tick label based on its position in the chart timeline
 * @param timestamp The timestamp to format
 * @param minTimestamp The minimum timestamp in the chart range (as a number)
 * @param maxTimestamp The maximum timestamp in the chart range (as a number)
 * @returns A formatted string for the tick label
 */
export function formatTickLabel(
  timestamp: string,
  minTimestamp: number,
  maxTimestamp: number
): string {
  // Ensure timestamp is in UTC format
  const utcTimestamp = ensureUTCFormatTimestamp(timestamp);

  // Convert to dayjs objects
  const date = dayjs(utcTimestamp);
  const minDate = dayjs(minTimestamp);
  const maxDate = dayjs(maxTimestamp);

  // Calculate the total duration in milliseconds
  const totalDuration = maxDate.diff(minDate);

  // Calculate the interval between ticks in milliseconds
  const tickInterval = totalDuration / (CHART_TICKS - 1);

  // Calculate the position of this tick (0 to CHART_TICKS-1)
  const tickPosition = Math.round(date.diff(minDate) / tickInterval);

  // Format the day of week and day of month (e.g., "Wed 15")
  const dayFormat = date.format('ddd D');

  // If this is the first tick, always show the day format
  if (tickPosition === 0) {
    return dayFormat;
  }

  // Calculate the time difference between ticks in hours
  const hoursBetweenTicks = tickInterval / (1000 * 60 * 60);
  const minutesBetweenTicks = tickInterval / (1000 * 60);

  // If ticks are less than a day apart, show hours for subsequent ticks
  // until we hit the next day
  if (hoursBetweenTicks < 24) {
    // Check if this tick is on a different day than the previous tick
    const prevTickDate = minDate.add(
      (tickPosition - 1) * tickInterval,
      'millisecond'
    );
    const isNewDay = !date.isSame(prevTickDate, 'day');

    // If it's a new day, show the day format, otherwise show the hour
    if (isNewDay) {
      return dayFormat;
    } else if (minutesBetweenTicks < 60) {
      return date.format('h:mm A'); // e.g., "12:00 PM"
    } else {
      return date.format('h A'); // e.g., "12 PM"
    }
  }

  // For larger intervals, just show the day format
  return dayFormat;
}

export function noDataChart(
  chart: CustomChartSchema | CustomChartPreviewSchema
) {
  // Start with data points that have a value
  let nonEmptyData = chart.data.filter((d) => d.value != null);

  // Process each series to filter out data points with no values
  for (const series of chart.series) {
    if (series.metric === 'feedback_values' && series.feedback_key) {
      // For feedback_values metric, check if any subcategory has values
      const feedbackKey = series.feedback_key;
      const subcategories = getCategories(nonEmptyData, feedbackKey);

      nonEmptyData = nonEmptyData.filter((dataPoint) => {
        // Skip if not an object or doesn't have the feedback key
        if (
          typeof dataPoint.value !== 'object' ||
          !dataPoint.value[feedbackKey]
        ) {
          return true;
        }

        // Check if any subcategory has a non-zero value
        return subcategories.some(
          (subcategory) => dataPoint.value[feedbackKey]?.values[subcategory] > 0
        );
      });
    } else {
      // For other metrics, use getValueFromDataPoint to check for non-zero values
      nonEmptyData = nonEmptyData.filter(
        (dataPoint) =>
          getValueFromDataPoint(dataPoint.value, series.metric) != null
      );
    }
  }

  // Return true if there's no data left after filtering
  return nonEmptyData.length === 0;
}

export const splitMetadataKeys = (commonMetadata: SessionMetadataResponse) => {
  if (!commonMetadata) {
    return {
      ignoredMetadata: {},
      metadataToDisplay: {},
    };
  }

  // Store ignored keys for potential future use
  const ignoredMetadata = Object.entries(commonMetadata).reduce(
    (acc, [key, value]) => {
      if (DEFAULT_METADATA_TO_IGNORE.includes(key)) {
        acc[key] = value;
      }
      return acc;
    },
    {} as SessionMetadataResponse
  );

  const metadataToDisplay = Object.entries(commonMetadata).reduce(
    (acc, [key, value]) => {
      if (!DEFAULT_METADATA_TO_IGNORE.includes(key)) {
        acc[key] = value;
      }
      return acc;
    },
    {} as SessionMetadataResponse
  );

  return {
    ignoredMetadata,
    metadataToDisplay,
  };
};
