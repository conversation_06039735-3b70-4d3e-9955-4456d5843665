import { BarChartI<PERSON>, LineChartIcon } from 'lucide-react';

import { SearchModel } from '@/components/RunsTable/types';
import { CustomChartMetric, CustomChartType } from '@/types/schema';
import { cn } from '@/utils/tailwind';

export const GRID_CHART_HEIGHT = 240;
export const GRID_CHART_MARGIN = { top: 16, right: 10, bottom: 32, left: 50 };
export const TOOLTIP_WIDTH = 254;

export const currencyFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});

// formatter for normal numbers to max 2 decimal places
export const numberFormatter = new Intl.NumberFormat('en-US', {
  notation: 'compact',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});

export const MAX_CHARTS_PER_SECTION = 10;
export const MAX_DATA_SERIES_PER_CHART = 10;
export const DEFAULT_CHART_TYPE: CustomChartType = 'line';

export interface DataSeriesFilter {
  filters: SearchModel;
  name: string;
}

export interface CustomChartMetricLabel {
  label: string;
  value: CustomChartMetric;
}

// Refactored Metric Data
export interface CustomChartMetricLabel {
  label: string;
  value: CustomChartMetric;
}

export const METRICS: Record<CustomChartMetric, string> = {
  run_count: 'Run count',
  latency_avg: 'Average Latency',
  latency_p50: 'P50 Latency',
  latency_p99: 'P99 Latency',
  first_token_p50: 'P50 First Token',
  first_token_p99: 'P99 First Token',
  total_tokens: 'Total Tokens',
  prompt_tokens: 'Prompt Tokens',
  completion_tokens: 'Output Tokens',
  median_tokens: 'Median Tokens',
  feedback: 'Feedback Count',
  feedback_score_avg: 'Average Feedback Score',
  feedback_values: 'Feedback Values',
  total_cost: 'Total Cost',
  cost_p50: 'P50 Cost per Trace',
  cost_p99: 'P99 Cost per Trace',
  prompt_cost: 'Prompt Cost',
  completion_cost: 'Completion Cost',
  error_rate: 'Error Rate',
  streaming_rate: 'Streaming Rate',
};

export const METRICS_TO_UNIT_LABEL: Record<CustomChartMetric, string> = {
  run_count: 'Number of runs',
  latency_avg: 'Seconds',
  latency_p50: 'Seconds',
  latency_p99: 'Seconds',
  first_token_p50: 'Seconds',
  first_token_p99: 'Seconds',
  total_tokens: 'Token count',
  prompt_tokens: 'Token count',
  completion_tokens: 'Token count',
  median_tokens: 'Token count',
  feedback: 'Feedback count',
  feedback_score_avg: 'Average feedback score',
  feedback_values: 'Feedback count',
  total_cost: 'Cost ($)',
  prompt_cost: 'Cost ($)',
  completion_cost: 'Cost ($)',
  cost_p50: 'Cost ($)',
  cost_p99: 'Cost ($)',
  error_rate: 'Rate %',
  streaming_rate: 'Rate %',
};

export const METRIC_TO_RUN_COLUMN_ID: Record<CustomChartMetric, string | null> =
  {
    run_count: null,
    latency_avg: 'latency',
    latency_p50: 'latency',
    latency_p99: 'latency',
    first_token_p50: 'first_token_time',
    first_token_p99: 'first_token_time',
    total_tokens: 'total_tokens',
    prompt_tokens: null,
    completion_tokens: null,
    median_tokens: 'total_tokens',
    feedback: null,
    feedback_score_avg: null,
    feedback_values: null,
    total_cost: 'total_cost',
    cost_p50: 'total_cost',
    cost_p99: 'total_cost',
    prompt_cost: null,
    completion_cost: null,
    error_rate: 'error',
    streaming_rate: null,
  };

export const RELATED_METRICS_MAP: Record<
  CustomChartMetric,
  CustomChartMetricLabel[]
> = {
  run_count: [],
  latency_avg: [
    { label: METRICS.latency_p50, value: 'latency_p50' },
    { label: METRICS.first_token_p50, value: 'first_token_p50' },
    { label: METRICS.first_token_p99, value: 'first_token_p99' },
  ],
  latency_p50: [
    { label: METRICS.latency_p99, value: 'latency_p99' },
    { label: METRICS.first_token_p50, value: 'first_token_p50' },
    { label: METRICS.first_token_p99, value: 'first_token_p99' },
  ],
  latency_p99: [
    { label: METRICS.latency_p50, value: 'latency_p50' },
    { label: METRICS.first_token_p50, value: 'first_token_p50' },
    { label: METRICS.first_token_p99, value: 'first_token_p99' },
  ],
  first_token_p50: [
    { label: METRICS.first_token_p99, value: 'first_token_p99' },
    { label: METRICS.latency_p99, value: 'latency_p99' },
    { label: METRICS.latency_p50, value: 'latency_p50' },
  ],
  first_token_p99: [
    { label: METRICS.first_token_p50, value: 'first_token_p50' },
    { label: METRICS.latency_p99, value: 'latency_p99' },
    { label: METRICS.latency_p50, value: 'latency_p50' },
  ],
  total_tokens: [
    { label: METRICS.prompt_tokens, value: 'prompt_tokens' },
    { label: METRICS.completion_tokens, value: 'completion_tokens' },
    { label: METRICS.median_tokens, value: 'median_tokens' },
  ],
  prompt_tokens: [
    { label: METRICS.total_tokens, value: 'total_tokens' },
    { label: METRICS.completion_tokens, value: 'completion_tokens' },
    { label: METRICS.median_tokens, value: 'median_tokens' },
  ],
  completion_tokens: [
    { label: METRICS.total_tokens, value: 'total_tokens' },
    { label: METRICS.prompt_tokens, value: 'prompt_tokens' },
    { label: METRICS.median_tokens, value: 'median_tokens' },
  ],
  median_tokens: [
    { label: METRICS.total_tokens, value: 'total_tokens' },
    { label: METRICS.prompt_tokens, value: 'prompt_tokens' },
    { label: METRICS.completion_tokens, value: 'completion_tokens' },
  ],
  feedback: [],
  feedback_score_avg: [],
  feedback_values: [],
  total_cost: [
    { label: METRICS.prompt_cost, value: 'prompt_cost' },
    { label: METRICS.completion_cost, value: 'completion_cost' },
  ],
  prompt_cost: [
    { label: METRICS.total_cost, value: 'total_cost' },
    { label: METRICS.completion_cost, value: 'completion_cost' },
  ],
  completion_cost: [
    { label: METRICS.total_cost, value: 'total_cost' },
    { label: METRICS.prompt_cost, value: 'prompt_cost' },
  ],
  error_rate: [{ label: METRICS.streaming_rate, value: 'streaming_rate' }],
  streaming_rate: [{ label: METRICS.error_rate, value: 'error_rate' }],
  cost_p50: [{ label: METRICS.cost_p50, value: 'cost_p50' }],
  cost_p99: [{ label: METRICS.cost_p99, value: 'cost_p99' }],
};

export const FEEDBACK_METRICS = [
  'feedback',
  'feedback_score_avg',
  'feedback_values',
];

export const COST_METRICS = [
  'total_cost',
  'prompt_cost',
  'completion_cost',
  'cost_p50',
  'cost_p99',
];

export const PERCENTAGE_METRICS = ['error_rate', 'streaming_rate'];

const chartIconStyle = 'w-5 h-5 text-[#2F6868]';
const chartIconWrapperStyle =
  'p-2.5 border border-secondary dark:border-secondary rounded-lg';

export interface CustomChartTypeOption {
  label: string;
  value: CustomChartType;
  icon: JSX.Element;
}

export const CHART_TYPES: CustomChartTypeOption[] = [
  {
    label: 'Line',
    value: 'line',
    icon: (
      <div className={chartIconWrapperStyle}>
        <LineChartIcon className={cn(chartIconStyle)} />
      </div>
    ),
  },
  {
    label: 'Bar',
    value: 'bar',
    icon: (
      <div className={chartIconWrapperStyle}>
        <BarChartIcon className={cn(chartIconStyle)} />
      </div>
    ),
  },
];

export const CHART_TICKS = 6;
export const DEFAULT_METADATA_TO_IGNORE = [
  'created_by',
  'LANGCHAIN_CALLBACKS_BACKGROUND',
  'langgraph_api_url',
  'langgraph_auth_user_id',
  'langgraph_host',
  'langgraph_plan',
  'langgraph_version',
  'LANGSMITH_AUTH_ENDPOINT',
  'LANGSMITH_AUTH_VERIFY_TENANT_ID',
  'LANGSMITH_HOST_PROJECT_ID',
  'LANGSMITH_HOST_PROJECT_NAME',
  'LANGSMITH_HOST_REVISION_ID',
  'LANGSMITH_LANGGRAPH_API_REVISION',
  'LANGSMITH_LANGGRAPH_API_VARIANT',
  'LANGSMITH_TENANT_ID',
  'LANGSMITH_TRACING',
  'run_attempt',
  'run_id',
  'thread_id',
  'user-agent',
  'x-forwarded-for',
  'x-forwarded-host',
  'x-forwarded-port',
  'x-forwarded-proto',
  'x-forwarded-scheme',
  'x-middleware-subrequest',
  'x-real-ip',
  'x-request-id',
  'x-scheme',
  'x-vercel-id',
  'x-original-forwarded-for',
  'thread_ts',
  'LANGGRAPH_API_URL',
  'x-auth-scheme',
  'from_studio',
];
