import { describe, expect, it } from 'vitest';

import { CustomChartSeriesSchema } from '@/types/schema';
import { getContrastingColorByString } from '@/utils/get-contrasting-colors';

import { getSeriesColor, mapSeriesToColors } from '../getSeriesColor';

describe('getSeriesColor', () => {
  it('should return green color for success series', () => {
    expect(getSeriesColor('success')).toBe('rgba(46, 204, 113, 0.7)');
    expect(getSeriesColor('Success')).toBe('rgba(46, 204, 113, 0.7)');
    expect(getSeriesColor('any', 'success')).toBe('rgba(46, 204, 113, 0.7)');
  });

  it('should return red color for error series', () => {
    expect(getSeriesColor('error')).toBe('rgba(228, 26, 28, 0.7)');
    expect(getSeriesColor('Error')).toBe('rgba(228, 26, 28, 0.7)');
    expect(getSeriesColor('any', 'error')).toBe('rgba(228, 26, 28, 0.7)');
  });

  it('should use key if provided and extract part after colon', () => {
    const color = getSeriesColor('seriesName', 'prefix:actualKey');
    expect(color).toBe(getContrastingColorByString('actualKey', 0.8));
  });

  it('should fallback to seriesName if key is not provided', () => {
    const color = getSeriesColor('seriesName');
    expect(color).toBe(getContrastingColorByString('seriesName', 0.8));
  });

  it('should fallback to key if seriesName is not provided', () => {
    const color = getSeriesColor('', 'key');
    expect(color).toBe(getContrastingColorByString('', 0.8));
  });

  it('should handle empty inputs', () => {
    const color = getSeriesColor('', '');
    expect(color).toBe(getContrastingColorByString('', 0.8));
  });

  it('should handle multiple colons in key', () => {
    const color = getSeriesColor('seriesName', 'prefix:middle:actualKey');
    expect(color).toBe(getContrastingColorByString('actualKey', 0.8));
  });

  it('should handle undefined key', () => {
    const color = getSeriesColor('seriesName', undefined);
    expect(color).toBe(getContrastingColorByString('seriesName', 0.8));
  });
});

describe('mapSeriesToColors', () => {
  it('should assign colors to new series not in existingColors', () => {
    const series: CustomChartSeriesSchema[] = [
      {
        id: 'series1',
        name: 'Series 1',
        metric: 'run_count',
        feedback_key: null,
      },
      {
        id: 'series2',
        name: 'Series 2',
        metric: 'run_count',
        feedback_key: null,
      },
    ];
    const existingColors = {
      'Series 3': 'rgba(255, 0, 0, 0.7)',
    };

    const result = mapSeriesToColors(series, existingColors);

    expect(Object.keys(result)).toHaveLength(2);
    expect(result['Series 1']).toBeDefined();
    expect(result['Series 2']).toBeDefined();
    expect(result['Series 3']).toBeUndefined(); // Should not modify existing colors
  });

  it('should assign success and error colors correctly', () => {
    const series: CustomChartSeriesSchema[] = [
      {
        id: 'series1',
        name: 'Success Rate',
        metric: 'run_count',
        feedback_key: null,
      },
      {
        id: 'series2',
        name: 'Error Rate',
        metric: 'run_count',
        feedback_key: null,
      },
      {
        id: 'series3',
        name: 'Normal Series',
        metric: 'run_count',
        feedback_key: null,
      },
    ];
    const existingColors = {};

    const result = mapSeriesToColors(series, existingColors);

    expect(result['Success Rate']).toBe('rgba(46, 204, 113, 0.7)');
    expect(result['Error Rate']).toBe('rgba(228, 26, 28, 0.7)');
    expect(result['Normal Series']).toBeDefined();
    expect(result['Normal Series']).not.toBe('rgba(46, 204, 113, 0.7)');
    expect(result['Normal Series']).not.toBe('rgba(228, 26, 28, 0.7)');
  });

  it('should handle series with complex IDs correctly', () => {
    const series: CustomChartSeriesSchema[] = [
      {
        id: 'series-feedback:positive:group1',
        name: 'Feedback',
        metric: 'run_count',
        feedback_key: null,
      },
      {
        id: 'series-feedback:negative:group2',
        name: 'Feedback',
        metric: 'run_count',
        feedback_key: null,
      },
    ];
    const existingColors = {};

    const result = mapSeriesToColors(series, existingColors);

    expect(Object.keys(result)).toHaveLength(2);
    expect(result['group1']).toBeDefined();
    expect(result['group2']).toBeDefined();
    expect(result['group1']).not.toBe(result['group2']); // Should have different colors
  });

  it('should maintain color consistency with existing colors', () => {
    const series: CustomChartSeriesSchema[] = [
      {
        id: 'series1',
        name: 'Series 1',
        metric: 'run_count',
        feedback_key: null,
      },
      {
        id: 'series2',
        name: 'Series 2',
        metric: 'run_count',
        feedback_key: null,
      },
    ];
    const existingColors = {
      'Series 3': 'rgba(255, 0, 0, 0.7)',
      'Series 4': 'rgba(0, 255, 0, 0.7)',
    };

    const result = mapSeriesToColors(series, existingColors);

    // New colors should start after existing colors in the REORDERED_COLORS array
    const firstNewColor = result['Series 1'];
    const secondNewColor = result['Series 2'];
    expect(firstNewColor).not.toBe(secondNewColor);
    expect(firstNewColor).not.toBe(existingColors['Series 3']);
    expect(firstNewColor).not.toBe(existingColors['Series 4']);
  });

  it('should handle empty series array', () => {
    const series: CustomChartSeriesSchema[] = [];
    const existingColors = {
      'Series 1': 'rgba(255, 0, 0, 0.7)',
    };

    const result = mapSeriesToColors(series, existingColors);

    expect(Object.keys(result)).toHaveLength(0);
  });
});
