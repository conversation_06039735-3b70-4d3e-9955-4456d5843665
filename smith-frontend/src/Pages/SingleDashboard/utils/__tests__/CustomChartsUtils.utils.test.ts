import dayjs from 'dayjs';
import { Duration } from 'dayjs/plugin/duration';
import { describe, expect, it } from 'vitest';

import { SearchModel } from '@/components/RunsTable/types';
import {
  CustomChartDataSchema,
  CustomChartMetric,
  CustomChartPreviewSchema,
  CustomChartSchema,
  CustomChartSeriesCreate,
  CustomChartSeriesFilters,
  CustomChartSeriesSchema,
  SessionMetadataResponse,
} from '@/types/schema';

import {
  canNavigateToRunsTable,
  compareDataPoints,
  convertFilterFormat,
  createSearchModel,
  createTimeModel,
  extractSeriesSpecificFilters,
  formatTickLabel,
  getCategories,
  getUniqueSessionIdsFromChart,
  getValueFromDataPoint,
  noDataChart,
  splitMetadataKeys,
} from '../CustomChartsUtils.utils';
import { DEFAULT_METADATA_TO_IGNORE } from '../constants';
import { TooltipDataPoint } from '../types';

describe('createTimeModel', () => {
  it('should create correct time model with a 7-day duration', () => {
    const totalChartDuration = {
      asMilliseconds: () => 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    } as Duration;

    const datum: CustomChartDataSchema = {
      series_id: 'series1',
      timestamp: '2024-01-01T00:00:00',
      value: 1,
    };

    const chart: CustomChartSchema = {
      id: 'test',
      description: 'test',
      index: 0,
      data: [
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
        datum,
      ],
      series: [
        {
          id: 'series1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
          metric: 'run_count',
          feedback_key: null,
          name: 'series1',
        },
        {
          id: 'series2',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
          metric: 'latency_p50',
          feedback_key: null,
          name: 'series2',
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      chart_type: 'line',
      title: 'Test Chart',
    };

    const result = createTimeModel(totalChartDuration, datum.timestamp, chart);

    expect(result).toEqual({
      start_time: '2024-01-01T00:00:00',
      end_time: '2024-01-02T00:00:00',
    });
  });
});

describe('createSearchModel', () => {
  it('should combine common filters and series filters correctly', () => {
    const chart: CustomChartSchema = {
      id: 'test',
      description: 'test',
      index: 0,
      data: [],
      series: [],
      common_filters: {
        filter: 'eq(is_root, true)',
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      chart_type: 'line',
      title: 'Test Chart',
    };

    const relevantSeries: CustomChartSeriesSchema = {
      id: 'series1',
      feedback_key: null,
      name: 'series1',
      filters: {
        filter: 'eq(name, "ChatOpenAI")',
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      metric: 'feedback_values',
    };

    const result = createSearchModel(chart, relevantSeries);

    expect(result).toEqual({
      filter: 'and(eq(is_root, true), eq(name, "ChatOpenAI"))',
      tree_filter: undefined,
      trace_filter: undefined,
    });
  });

  it('should handle null filters correctly', () => {
    const chart: CustomChartSchema = {
      id: 'test',
      description: 'test',
      index: 0,
      data: [],
      series: [],
      common_filters: {
        filter: undefined,
        tree_filter: 'eq(is_root, true)',
        trace_filter: undefined,
        session: [],
      },
      chart_type: 'line',
      title: 'Test Chart',
    };

    const relevantSeries: CustomChartSeriesSchema = {
      id: 'series1',
      feedback_key: null,
      name: 'series1',
      filters: {
        filter: 'eq(name, "ChatOpenAI")',
        tree_filter: undefined,
        trace_filter: 'eq(name, "ChatOpenAI")',
        session: [],
      },
      metric: 'feedback_values',
    };

    const result = createSearchModel(chart, relevantSeries);

    expect(result).toEqual({
      filter: 'eq(name, "ChatOpenAI")',
      tree_filter: 'eq(is_root, true)',
      trace_filter: 'eq(name, "ChatOpenAI")',
    });
  });

  it('should apply feedback filter when metric is feedback-related and feedback key is provided', () => {
    const chart: CustomChartSchema = {
      id: 'test',
      description: 'test',
      index: 0,
      data: [],
      series: [],
      common_filters: {
        filter: 'eq(is_root, true)',
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      chart_type: 'line',
      title: 'Test Chart',
    };

    const relevantSeries: CustomChartSeriesSchema = {
      id: 'series1',
      feedback_key: 'key123',
      name: 'series1',
      filters: {
        filter: 'eq(name, "ChatOpenAI")',
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      metric: 'feedback_score_avg',
    };

    const result = createSearchModel(chart, relevantSeries);

    expect(result.filter).toBe(
      'and(eq(feedback_key, "key123"), eq(is_root, true), eq(name, "ChatOpenAI"))'
    );
    expect(result.tree_filter).toBeUndefined();
    expect(result.trace_filter).toBeUndefined();
  });

  it('should apply feedback filter when metric is feedback-related and feedback key is provided', () => {
    const chart: CustomChartSchema = {
      id: 'test',
      description: 'test',
      index: 0,
      data: [],
      series: [],
      common_filters: {
        filter: 'eq(is_root, true)',
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      chart_type: 'line',
      title: 'Test Chart',
    };

    const relevantSeries: CustomChartSeriesSchema = {
      id: 'series1',
      feedback_key: 'key123',
      name: 'series1',
      filters: {
        filter: 'eq(name, "ChatOpenAI")',
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      metric: 'feedback_values',
    };

    const result = createSearchModel(chart, relevantSeries, 'val');

    expect(result.filter).toBe(
      'and(eq(feedback_key, "key123"), eq(feedback_value, "val"), eq(is_root, true), eq(name, "ChatOpenAI"))'
    );
    expect(result.tree_filter).toBeUndefined();
    expect(result.trace_filter).toBeUndefined();
  });
});

describe('getUniqueSessionIds', () => {
  it('should handle CustomChartSchema with common_filters and series sessions', () => {
    const chart: CustomChartSchema = {
      id: '1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: ['session1', 'session2'],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: ['session2', 'session3'],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: ['session4'],
      },
      data: [],
    };

    const result = getUniqueSessionIdsFromChart(chart);
    expect(result).toEqual(['session1', 'session2', 'session3', 'session4']);
  });

  it('should handle CustomChartPreviewSchema without common_filters', () => {
    const chart: CustomChartPreviewSchema = {
      title: 'Test Chart',
      description: 'Test Description',
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: ['session1', 'session2'],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      data: [],
    };

    const result = getUniqueSessionIdsFromChart(chart);
    expect(result).toEqual(['session1', 'session2']);
  });

  it('should handle undefined series sessions', () => {
    const chart: CustomChartSchema = {
      id: '1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: ['session1'],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: ['session2'],
      },
      data: [],
    };

    const result = getUniqueSessionIdsFromChart(chart);
    expect(result).toEqual(['session1', 'session2']);
  });

  it('should handle empty arrays and undefined values', () => {
    const chart: CustomChartSchema = {
      id: '1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      data: [],
    };

    const result = getUniqueSessionIdsFromChart(chart);
    expect(result).toEqual([]);
  });
});

describe('getCategories', () => {
  it('should return empty array when series data values are numbers', () => {
    const seriesData: CustomChartDataSchema[] = [
      {
        series_id: '1',
        timestamp: '2023-01-01T00:00:00Z',
        value: 42,
      },
      {
        series_id: '2',
        timestamp: '2023-01-02T00:00:00Z',
        value: 100,
      },
    ];

    const categories = getCategories(seriesData, 'feedback_key');
    expect(categories).toEqual([]);
  });

  it('should extract categories from object values', () => {
    const seriesData: CustomChartDataSchema[] = [
      {
        series_id: '1',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          feedback_key: {
            n: 10,
            avg: 4.5,
            values: {
              good: 7,
              bad: 3,
            },
          },
        },
      },
      {
        series_id: '2',
        timestamp: '2023-01-02T00:00:00Z',
        value: {
          feedback_key: {
            n: 8,
            avg: 3.9,
            values: {
              good: 5,
              bad: 2,
              neutral: 1,
            },
          },
        },
      },
    ];

    const categories = getCategories(seriesData, 'feedback_key');
    expect(categories).toEqual(
      expect.arrayContaining(['good', 'bad', 'neutral'])
    );
    expect(categories.length).toBe(3);
  });

  it('should handle mixed data types correctly', () => {
    const seriesData: CustomChartDataSchema[] = [
      {
        series_id: '1',
        timestamp: '2023-01-01T00:00:00Z',
        value: 42,
      },
      {
        series_id: '2',
        timestamp: '2023-01-02T00:00:00Z',
        value: {
          feedback_key: {
            n: 10,
            avg: 4.5,
            values: {
              positive: 8,
              negative: 2,
            },
          },
        },
      },
    ];

    const categories = getCategories(seriesData, 'feedback_key');
    expect(categories).toEqual(
      expect.arrayContaining(['positive', 'negative'])
    );
    expect(categories.length).toBe(2);
  });

  it('should handle objects without values property', () => {
    const seriesData: CustomChartDataSchema[] = [
      {
        series_id: '1',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          feedback_key: {
            n: 10,
            avg: 4.5,
            values: {},
          },
        },
      },
      {
        series_id: '2',
        timestamp: '2023-01-02T00:00:00Z',
        value: {
          feedback_key: {
            n: 8,
            avg: 3.9,
            values: {
              good: 7,
              bad: 1,
            },
          },
        },
      },
    ];

    const categories = getCategories(seriesData, 'feedback_key');
    expect(categories).toEqual(expect.arrayContaining(['good', 'bad']));
    expect(categories.length).toBe(2);
  });

  it('should handle empty series data', () => {
    const seriesData: CustomChartDataSchema[] = [];
    const categories = getCategories(seriesData, 'feedback_key');
    expect(categories).toEqual([]);
  });

  it('should handle non-existent category key', () => {
    const seriesData: CustomChartDataSchema[] = [
      {
        series_id: '1',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          feedback_key: {
            n: 10,
            avg: 4.5,
            values: {
              good: 7,
              bad: 3,
            },
          },
        },
      },
    ];

    const categories = getCategories(seriesData, 'non_existent_key');
    expect(categories).toEqual([]);
  });
});

describe('canNavigateToRunsTable', () => {
  it('should return true for a CustomChartSchema with session in common_filters', () => {
    const chart: CustomChartSchema = {
      id: '1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: ['session1'],
      },
      data: [],
    };

    const result = canNavigateToRunsTable(chart);
    expect(result).toBe(true);
  });

  it('should return true for a CustomChartSchema with session in series filters', () => {
    const chart: CustomChartSchema = {
      id: '1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: ['session1'],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      data: [],
    };

    const result = canNavigateToRunsTable(chart);
    expect(result).toBe(true);
  });

  it('should return false for a CustomChartSchema with multiple sessions', () => {
    const chart: CustomChartSchema = {
      id: '1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: ['session1', 'session2'],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      data: [],
    };

    const result = canNavigateToRunsTable(chart);
    expect(result).toBe(false);
  });

  it('should return false for a CustomChartPreviewSchema', () => {
    const chart: CustomChartPreviewSchema = {
      title: 'Test Chart',
      description: 'Test Description',
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: ['session1'],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      data: [],
    };

    const result = canNavigateToRunsTable(chart);
    expect(result).toBe(false);
  });

  it('should return false for a CustomChartSchema with no sessions', () => {
    const chart: CustomChartSchema = {
      id: '1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
          metric: 'feedback_values',
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
      data: [],
    };

    const result = canNavigateToRunsTable(chart);
    expect(result).toBe(false);
  });
});

describe('getValueFromDataPoint', () => {
  it('should return the value directly when it is a number', () => {
    const value = 42;
    const result = getValueFromDataPoint(value, 'run_count');
    expect(result).toBe(42);
  });

  it('should handle various non-feedback numeric metrics', () => {
    const numericValue = 42;

    // Test various non-feedback metrics with numeric values
    const metrics: CustomChartMetric[] = [
      'run_count',
      'latency_avg',
      'latency_p50',
      'total_tokens',
      'error_rate',
    ];

    metrics.forEach((metric) => {
      const result = getValueFromDataPoint(numericValue, metric);
      if (metric === 'error_rate') {
        expect(result).toBe(4200);
      } else {
        expect(result).toBe(42);
      }
    });
  });

  it('should return avg value for feedback_score_avg metric', () => {
    const value = {
      feedback_key: {
        n: 10,
        avg: 4.5,
        values: {
          positive: 8,
          negative: 2,
        },
      },
    };

    const result = getValueFromDataPoint(value, 'feedback_score_avg');
    expect(result).toBe(4.5);
  });

  it('should handle null avg value for feedback_score_avg metric', () => {
    const value = {
      feedback_key: {
        n: 10,
        avg: null,
        values: {
          positive: 8,
          negative: 2,
        },
      },
    };

    const result = getValueFromDataPoint(value, 'feedback_score_avg');
    expect(result).toBeNull();
  });

  it('should return count (n) value for feedback metric', () => {
    const value = {
      feedback_key: {
        n: 15,
        avg: 3.8,
        values: {
          positive: 10,
          negative: 5,
        },
      },
    };

    const result = getValueFromDataPoint(value, 'feedback');
    expect(result).toBe(15);
  });

  it('should return specific value for feedback_values with valid feedbackValue', () => {
    const value = {
      feedback_key: {
        n: 20,
        avg: 4.2,
        values: {
          good: 15,
          bad: 3,
          neutral: 2,
        },
      },
    };

    const result = getValueFromDataPoint(value, 'feedback_values', 'good');
    expect(result).toBe(15);
  });

  it('should return 0 for feedback_values with non-existent feedbackValue', () => {
    const value = {
      feedback_key: {
        n: 20,
        avg: 4.2,
        values: {
          good: 15,
          bad: 3,
          neutral: 2,
        },
      },
    };

    const result = getValueFromDataPoint(value, 'feedback_values', 'excellent');
    expect(result).toBe(0);
  });

  it('should return 0 for feedback_values without feedbackValue parameter', () => {
    const value = {
      feedback_key: {
        n: 20,
        avg: 4.2,
        values: {
          good: 15,
          bad: 3,
          neutral: 2,
        },
      },
    };

    const result = getValueFromDataPoint(value, 'feedback_values');
    expect(result).toBe(0);
  });

  it('should return 0 for non-feedback metrics with object values', () => {
    const value = {
      feedback_key: {
        n: 20,
        avg: 4.2,
        values: {
          good: 15,
          bad: 3,
          neutral: 2,
        },
      },
    };

    const result = getValueFromDataPoint(value, 'run_count');
    expect(result).toBe(0);
  });

  it('should handle empty object values', () => {
    const value = {};

    const result = getValueFromDataPoint(value, 'feedback_score_avg');
    expect(result).toBeNull(); // Should return null as the avg doesn't exist
  });

  it('should handle empty object values', () => {
    const value = {};

    const result = getValueFromDataPoint(value, 'feedback_values');
    expect(result).toBe(0); // Should return 0 since this is a count metric
  });

  it('should handle empty object values', () => {
    const value = {};

    const result = getValueFromDataPoint(value, 'feedback');
    expect(result).toBe(0);
  });

  it('should return 0 for null or undefined values', () => {
    const result1 = getValueFromDataPoint(null as any, 'run_count');
    expect(result1).toBe(0);

    const result2 = getValueFromDataPoint(undefined as any, 'feedback');
    expect(result2).toBe(0);
  });
});

describe('compareDataPoints', () => {
  it('should compare two numeric values in descending order', () => {
    const a: TooltipDataPoint = {
      series: {
        id: 'series1',
        name: 'Series 1',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'run_count',
        feedback_key: null,
      },
      id: 'point1',
      name: 'Point 1',
      datum: {
        series_id: 'series1',
        timestamp: '2023-01-01T00:00:00Z',
        value: 42,
      },
      color: 'blue',
    };

    const b: TooltipDataPoint = {
      series: {
        id: 'series2',
        name: 'Series 2',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'run_count',
        feedback_key: null,
      },
      id: 'point2',
      name: 'Point 2',
      datum: {
        series_id: 'series2',
        timestamp: '2023-01-01T00:00:00Z',
        value: 100,
      },
      color: 'red',
    };

    // Should sort in descending order (b - a)
    expect(compareDataPoints(a, b, undefined)).toBe(58); // 100 - 42
    expect(compareDataPoints(b, a, undefined)).toBe(-58); // 42 - 100
  });

  it('should compare two object values with avg fields using feedbackKey', () => {
    const feedbackKey = 'feedback_key';

    const a: TooltipDataPoint = {
      series: {
        id: 'series1',
        name: 'Series 1',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: feedbackKey,
      },
      id: 'point1',
      name: 'Point 1',
      datum: {
        series_id: 'series1',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          [feedbackKey]: {
            n: 10,
            avg: 3.5,
            values: { good: 7, bad: 3 },
          },
        },
      },
      color: 'blue',
    };

    const b: TooltipDataPoint = {
      series: {
        id: 'series2',
        name: 'Series 2',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: feedbackKey,
      },
      id: 'point2',
      name: 'Point 2',
      datum: {
        series_id: 'series2',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          [feedbackKey]: {
            n: 15,
            avg: 4.2,
            values: { good: 10, bad: 5 },
          },
        },
      },
      color: 'red',
    };

    // Should compare avg values in descending order
    expect(compareDataPoints(a, b, feedbackKey)).toBeCloseTo(0.7); // 4.2 - 3.5
    expect(compareDataPoints(b, a, feedbackKey)).toBeCloseTo(-0.7); // 3.5 - 4.2
  });

  it('should return 0 when comparing numeric and object values', () => {
    const feedbackKey = 'feedback_key';

    const a: TooltipDataPoint = {
      series: {
        id: 'series1',
        name: 'Series 1',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'run_count',
        feedback_key: null,
      },
      id: 'point1',
      name: 'Point 1',
      datum: {
        series_id: 'series1',
        timestamp: '2023-01-01T00:00:00Z',
        value: 42,
      },
      color: 'blue',
    };

    const b: TooltipDataPoint = {
      series: {
        id: 'series2',
        name: 'Series 2',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: feedbackKey,
      },
      id: 'point2',
      name: 'Point 2',
      datum: {
        series_id: 'series2',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          [feedbackKey]: {
            n: 15,
            avg: 4.2,
            values: { good: 10, bad: 5 },
          },
        },
      },
      color: 'red',
    };

    // Should return 0 when types don't match
    expect(compareDataPoints(a, b, feedbackKey)).toBe(0);
    expect(compareDataPoints(b, a, feedbackKey)).toBe(0);
  });

  it('should return 0 when comparing object values without a feedbackKey', () => {
    const a: TooltipDataPoint = {
      series: {
        id: 'series1',
        name: 'Series 1',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: 'feedback_key1',
      },
      id: 'point1',
      name: 'Point 1',
      datum: {
        series_id: 'series1',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          feedback_key1: {
            n: 10,
            avg: 3.5,
            values: { good: 7, bad: 3 },
          },
        },
      },
      color: 'blue',
    };

    const b: TooltipDataPoint = {
      series: {
        id: 'series2',
        name: 'Series 2',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: 'feedback_key2',
      },
      id: 'point2',
      name: 'Point 2',
      datum: {
        series_id: 'series2',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          feedback_key2: {
            n: 15,
            avg: 4.2,
            values: { good: 10, bad: 5 },
          },
        },
      },
      color: 'red',
    };

    // Should return 0 when no matching feedbackKey
    expect(compareDataPoints(a, b, undefined)).toBe(0);
  });

  it('should return 0 when datum is null or undefined', () => {
    const a: TooltipDataPoint = {
      series: {
        id: 'series1',
        name: 'Series 1',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'run_count',
        feedback_key: null,
      },
      id: 'point1',
      name: 'Point 1',
      datum: null,
      color: 'blue',
    };

    const b: TooltipDataPoint = {
      series: {
        id: 'series2',
        name: 'Series 2',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'run_count',
        feedback_key: null,
      },
      id: 'point2',
      name: 'Point 2',
      datum: {
        series_id: 'series2',
        timestamp: '2023-01-01T00:00:00Z',
        value: 42,
      },
      color: 'red',
    };

    // Should return 0 when one datum is null
    expect(compareDataPoints(a, b, undefined)).toBe(0);
    expect(compareDataPoints(b, a, undefined)).toBe(0);

    // Both null datums
    const c: TooltipDataPoint = {
      ...a,
      id: 'point3',
      name: 'Point 3',
    };

    expect(compareDataPoints(a, c, undefined)).toBe(0);
  });

  it('should return 0 when comparing objects with null or missing avg values', () => {
    const feedbackKey = 'feedback_key';

    const a: TooltipDataPoint = {
      series: {
        id: 'series1',
        name: 'Series 1',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: feedbackKey,
      },
      id: 'point1',
      name: 'Point 1',
      datum: {
        series_id: 'series1',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          [feedbackKey]: {
            n: 10,
            avg: null,
            values: { good: 7, bad: 3 },
          },
        },
      },
      color: 'blue',
    };

    const b: TooltipDataPoint = {
      series: {
        id: 'series2',
        name: 'Series 2',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: feedbackKey,
      },
      id: 'point2',
      name: 'Point 2',
      datum: {
        series_id: 'series2',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          [feedbackKey]: {
            n: 15,
            avg: 4.2,
            values: { good: 10, bad: 5 },
          },
        },
      },
      color: 'red',
    };

    // Should return 0 when one avg is null
    expect(compareDataPoints(a, b, feedbackKey)).toBe(0);
    expect(compareDataPoints(b, a, feedbackKey)).toBe(0);

    // With missing feedback key in object
    const c: TooltipDataPoint = {
      series: {
        id: 'series3',
        name: 'Series 3',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_score_avg',
        feedback_key: feedbackKey,
      },
      id: 'point3',
      name: 'Point 3',
      datum: {
        series_id: 'series3',
        timestamp: '2023-01-01T00:00:00Z',
        value: {
          different_key: {
            n: 8,
            avg: 3.8,
            values: { good: 6, bad: 2 },
          },
        },
      },
      color: 'green',
    };

    // Should return 0 when feedbackKey doesn't exist in object
    expect(compareDataPoints(b, c, feedbackKey)).toBe(0);
  });
});

describe('formatTickLabel', () => {
  it('should format the first tick with day format', () => {
    // May 15, 2023 was a Monday
    const minDate = dayjs('2023-05-15T10:00:00').local();
    const maxDate = dayjs('2023-05-20T10:00:00').local();

    // Convert to number timestamps
    const minTimestampNum = minDate.valueOf();
    const maxTimestampNum = maxDate.valueOf();

    const result = formatTickLabel(
      minDate.utc().format(),
      minTimestampNum,
      maxTimestampNum
    );

    expect(result).toBe('Mon 15');
  });

  it('should show hour format for ticks within the same day', () => {
    const minDate = dayjs('2023-05-15T10:00:00').local();
    const maxDate = dayjs('2023-05-15T22:00:00').local();

    // Convert to number timestamps
    const minTimestampNum = minDate.valueOf();
    const maxTimestampNum = maxDate.valueOf();

    // This should be the second tick (around 12:00)
    const tickDate = dayjs('2023-05-15T12:00:00').local();
    const result = formatTickLabel(
      tickDate.utc().format(),
      minTimestampNum,
      maxTimestampNum
    );

    expect(result).toBe('12 PM');
  });

  it('should show day format when crossing to a new day', () => {
    // Create timestamps in local timezone
    const minDate = dayjs('2023-05-15T10:00:00').local();
    const maxDate = dayjs('2023-05-16T10:00:00').local();
    const tickDate = dayjs('2023-05-16T01:00:00').local();

    // Convert to number timestamps
    const minTimestampNum = minDate.valueOf();
    const maxTimestampNum = maxDate.valueOf();

    const result = formatTickLabel(
      tickDate.utc().format(),
      minTimestampNum,
      maxTimestampNum
    );

    // Get the expected day format in local timezone
    const expectedDayFormat = tickDate.format('ddd D');
    expect(result).toBe(expectedDayFormat);
  });

  it('should handle multi-day ranges with day format', () => {
    const minDate = dayjs('2023-05-15T10:00:00').local();
    const maxDate = dayjs('2023-05-20T10:00:00').local();

    // Convert to number timestamps
    const minTimestampNum = minDate.valueOf();
    const maxTimestampNum = maxDate.valueOf();

    // This should be a middle tick (around May 17)
    const tickDate = dayjs('2023-05-17T10:00:00').local();
    const result = formatTickLabel(
      tickDate.utc().format(),
      minTimestampNum,
      maxTimestampNum
    );

    // May 17, 2023 was a Wednesday
    expect(result).toBe('Wed 17');
  });

  it('should handle edge case with same timestamp for min and max', () => {
    const date = dayjs('2023-05-15T10:00:00').local();

    // Convert to number timestamp
    const timestampNum = date.valueOf();

    const result = formatTickLabel(
      date.utc().format(),
      timestampNum,
      timestampNum
    );

    expect(result).toBe('Mon 15');
  });

  it('should show minutes format when ticks are less than an hour apart', () => {
    // Create timestamps in local timezone with 30-minute intervals
    const minDate = dayjs('2023-05-15T10:00:00').local();
    const maxDate = dayjs('2023-05-15T11:30:00').local();
    const tickDate = dayjs('2023-05-15T10:30:00').local();

    // Convert to number timestamps
    const minTimestampNum = minDate.valueOf();
    const maxTimestampNum = maxDate.valueOf();

    const result = formatTickLabel(
      tickDate.utc().format(),
      minTimestampNum,
      maxTimestampNum
    );

    // Get the expected time format in local timezone
    const expectedTimeFormat = tickDate.format('h:mm A');
    expect(result).toBe(expectedTimeFormat);
  });
});

describe('extractSeriesSpecificFilters', () => {
  it('should return empty filters when series has no filters', () => {
    const series: CustomChartSeriesCreate[] = [
      {
        name: 'Series 1',
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].filters.filter).toBeUndefined();
    expect(result[0].filters.treeFilter).toBeUndefined();
    expect(result[0].filters.traceFilter).toBeUndefined();
  });

  it('should return series filter as is when common filter is empty', () => {
    const series: CustomChartSeriesCreate[] = [
      {
        name: 'Series 1',
        filters: {
          filter: 'eq(status, "completed")',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: undefined,
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].filters.filter).toBe('eq(status, "completed")');
  });

  it('should remove common filter from series filter', () => {
    const series: CustomChartSeriesCreate[] = [
      {
        name: 'Series 1',
        filters: {
          filter: 'and(eq(status, "completed"), eq(run_type, "test"))',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].filters.filter).toBe('eq(run_type, "test")');
  });

  it('should handle complex nested filters', () => {
    const series: CustomChartSeriesCreate[] = [
      {
        name: 'Series 1',
        filters: {
          filter:
            'and(and(eq(status, "completed"), eq(run_type, "test")), eq(name, "high"))',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'and(eq(status, "completed"), eq(run_type, "test"))',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].filters.filter).toBe('eq(name, "high")');
  });

  it('should handle multiple series', () => {
    const series: CustomChartSeriesCreate[] = [
      {
        name: 'Series 1',
        filters: {
          filter: 'and(eq(status, "completed"), eq(run_type, "test"))',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
      {
        name: 'Series 2',
        filters: {
          filter: 'and(eq(status, "completed"), eq(name, "high"))',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(2);
    expect(result[0].filters.filter).toBe('eq(run_type, "test")');
    expect(result[1].filters.filter).toBe('eq(name, "high")');
  });

  it('should return empty string when all filters are common', () => {
    const series: CustomChartSeriesCreate[] = [
      {
        name: 'Series 1',
        filters: {
          filter: 'eq(status, "completed")',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].filters.filter).toBeUndefined();
  });

  it('should use id-based name when group_by is present and id exists', () => {
    const series: (CustomChartSeriesCreate & { id?: string })[] = [
      {
        name: 'Series 1',
        id: 'group:by:test-value',
        group_by: {
          attribute: 'tag',
          path: 'test_field',
        },
        filters: {
          filter: 'eq(status, "completed")',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('test-value');
  });

  it('should use original name when group_by is present but no id exists', () => {
    const series: CustomChartSeriesCreate[] = [
      {
        name: 'Series 1',
        group_by: {
          attribute: 'tag',
          path: 'test_field',
        },
        filters: {
          filter: 'eq(status, "completed")',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('Series 1');
  });

  it('should use original name when no group_by is present', () => {
    const series: (CustomChartSeriesCreate & { id?: string })[] = [
      {
        name: 'Series 1',
        id: 'group:by:test-value',
        filters: {
          filter: 'eq(status, "completed")',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('Series 1');
  });

  it('should handle multiple series with mixed group_by and regular naming', () => {
    const series: (CustomChartSeriesCreate & { id?: string })[] = [
      {
        name: 'Series 1',
        id: 'group:by:value1',
        group_by: {
          attribute: 'tag',
          path: 'test_field',
        },
        filters: {
          filter: 'eq(status, "completed")',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
      {
        name: 'Series 2',
        filters: {
          filter: 'eq(status, "completed")',
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        metric: 'feedback_values',
        feedback_key: null,
      },
    ];
    const commonFilters: CustomChartSeriesFilters = {
      filter: 'eq(status, "completed")',
      tree_filter: undefined,
      trace_filter: undefined,
      session: [],
    };

    const result = extractSeriesSpecificFilters(series, commonFilters);

    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('value1');
    expect(result[1].name).toBe('Series 2');
  });
});

describe('noDataChart', () => {
  it('should return true when chart has no data', () => {
    const chart: CustomChartSchema = {
      id: 'chart1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'count' as CustomChartMetric,
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
      },
      data: [],
    };

    expect(noDataChart(chart)).toBe(true);
  });

  it('should return false when chart has all 0s data', () => {
    const chart: CustomChartSchema = {
      id: 'chart1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'count' as CustomChartMetric,
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
      },
      data: [
        { series_id: 'series1', timestamp: '2023-01-01T00:00:00Z', value: 0 },
        { series_id: 'series1', timestamp: '2023-01-02T00:00:00Z', value: 0 },
      ],
    };

    expect(noDataChart(chart)).toBe(false);
  });

  it('should return false when chart has data with non-empty values', () => {
    const chart: CustomChartSchema = {
      id: 'chart1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'count' as CustomChartMetric,
          feedback_key: null,
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
      },
      data: [
        { series_id: 'series1', timestamp: '2023-01-01T00:00:00Z', value: 10 },
        { series_id: 'series1', timestamp: '2023-01-02T00:00:00Z', value: 20 },
      ],
    };

    expect(noDataChart(chart)).toBe(false);
  });

  it('should handle feedback_values metric correctly', () => {
    const chart: CustomChartSchema = {
      id: 'chart1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'feedback_values' as CustomChartMetric,
          feedback_key: 'feedback_key',
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
      },
      data: [
        {
          series_id: 'series1',
          timestamp: '2023-01-01T00:00:00Z',
          value: {
            feedback_key: {
              n: 8,
              avg: 4,
              values: {
                positive: 5,
                negative: 3,
              },
            },
          },
        },
        {
          series_id: 'series1',
          timestamp: '2023-01-02T00:00:00Z',
          value: {
            feedback_key: {
              n: 0,
              avg: 0,
              values: {
                positive: 0,
                negative: 0,
              },
            },
          },
        },
      ],
    };

    expect(noDataChart(chart)).toBe(false);
  });

  it('should return true for feedback_values metric when all values are empty', () => {
    const chart: CustomChartSchema = {
      id: 'chart1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'feedback_values' as CustomChartMetric,
          feedback_key: 'feedback_key',
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
      },
      data: [
        {
          series_id: 'series1',
          timestamp: '2023-01-01T00:00:00Z',
          value: {
            feedback_key: {
              n: 0,
              avg: null,
              values: {},
            },
          },
        },
        {
          series_id: 'series1',
          timestamp: '2023-01-02T00:00:00Z',
          value: {
            feedback_key: {
              n: 0,
              avg: null,
              values: {},
            },
          },
        },
      ],
    };

    expect(noDataChart(chart)).toBe(true);
  });

  it('should handle multiple series correctly', () => {
    const chart: CustomChartSchema = {
      id: 'chart1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'count' as CustomChartMetric,
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'feedback_values' as CustomChartMetric,
          feedback_key: 'feedback_key',
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
      },
      data: [
        { series_id: 'series1', timestamp: '2023-01-01T00:00:00Z', value: 10 },
        { series_id: 'series1', timestamp: '2023-01-02T00:00:00Z', value: 0 },
        {
          series_id: 'series2',
          timestamp: '2023-01-01T00:00:00Z',
          value: {
            feedback_key: {
              n: 0,
              avg: null,
              values: {},
            },
          },
        },
        {
          series_id: 'series2',
          timestamp: '2023-01-02T00:00:00Z',
          value: {
            feedback_key: {
              n: 0,
              avg: null,
              values: {},
            },
          },
        },
      ],
    };

    expect(noDataChart(chart)).toBe(false);
  });

  it('should return false when all series have all 0s data', () => {
    const chart: CustomChartSchema = {
      id: 'chart1',
      title: 'Test Chart',
      description: 'Test Description',
      index: 0,
      chart_type: 'line',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'count' as CustomChartMetric,
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'feedback_values' as CustomChartMetric,
          feedback_key: 'feedback_key',
        },
      ],
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
      },
      data: [
        { series_id: 'series1', timestamp: '2023-01-01T00:00:00Z', value: 0 },
        { series_id: 'series1', timestamp: '2023-01-02T00:00:00Z', value: 0 },
        {
          series_id: 'series2',
          timestamp: '2023-01-01T00:00:00Z',
          value: {
            feedback_key: {
              n: 0,
              avg: null,
              values: {},
            },
          },
        },
        {
          series_id: 'series2',
          timestamp: '2023-01-02T00:00:00Z',
          value: {
            feedback_key: {
              n: 0,
              avg: null,
              values: {},
            },
          },
        },
      ],
    };

    expect(noDataChart(chart)).toBe(false);
  });
});

describe('convertFilterFormat', () => {
  it('should return empty filters when input is undefined', () => {
    // Test converting to SearchModel
    const searchModelResult = convertFilterFormat(undefined, 'searchModel');
    expect(searchModelResult).toEqual({
      filter: undefined,
      traceFilter: undefined,
      treeFilter: undefined,
    });

    // Test converting to CustomChartSeriesFilters
    const seriesFiltersResult = convertFilterFormat(undefined, 'seriesFilters');
    expect(seriesFiltersResult).toEqual({
      filter: undefined,
      tree_filter: undefined,
      trace_filter: undefined,
    });
  });

  it('should return the input as is if it is already in the target format', () => {
    // Test with SearchModel input and target
    const searchModelInput: SearchModel = {
      filter: 'test filter',
      traceFilter: 'test trace filter',
      treeFilter: 'test tree filter',
    };
    const searchModelResult = convertFilterFormat(
      searchModelInput,
      'searchModel'
    );
    expect(searchModelResult).toBe(searchModelInput);

    // Test with CustomChartSeriesFilters input and target
    const seriesFiltersInput: CustomChartSeriesFilters = {
      filter: 'test filter',
      trace_filter: 'test trace filter',
      tree_filter: 'test tree filter',
    };
    const seriesFiltersResult = convertFilterFormat(
      seriesFiltersInput,
      'seriesFilters'
    );
    expect(seriesFiltersResult).toBe(seriesFiltersInput);
  });

  it('should convert from CustomChartSeriesFilters to SearchModel', () => {
    const seriesFiltersInput: CustomChartSeriesFilters = {
      filter: 'test filter',
      trace_filter: 'test trace filter',
      tree_filter: 'test tree filter',
      session: ['session1', 'session2'],
    };

    const expectedSearchModel: SearchModel = {
      filter: 'test filter',
      traceFilter: 'test trace filter',
      treeFilter: 'test tree filter',
    };

    const result = convertFilterFormat(seriesFiltersInput, 'searchModel');
    expect(result).toEqual(expectedSearchModel);
  });

  it('should convert from SearchModel to CustomChartSeriesFilters', () => {
    const searchModelInput: SearchModel = {
      filter: 'test filter',
      traceFilter: 'test trace filter',
      treeFilter: 'test tree filter',
    };

    const expectedSeriesFilters: CustomChartSeriesFilters = {
      filter: 'test filter',
      trace_filter: 'test trace filter',
      tree_filter: 'test tree filter',
    };

    const result = convertFilterFormat(searchModelInput, 'seriesFilters');
    expect(result).toEqual(expectedSeriesFilters);
  });

  it('should handle empty strings in filters', () => {
    // Test with empty strings in SearchModel
    const searchModelInput: SearchModel = {
      filter: undefined,
      traceFilter: undefined,
      treeFilter: undefined,
    };

    const expectedSeriesFilters: CustomChartSeriesFilters = {
      filter: undefined,
      trace_filter: undefined,
      tree_filter: undefined,
    };

    const result = convertFilterFormat(searchModelInput, 'seriesFilters');
    expect(result).toEqual(expectedSeriesFilters);

    // Test with empty strings in CustomChartSeriesFilters
    const seriesFiltersInput: CustomChartSeriesFilters = {
      filter: undefined,
      trace_filter: undefined,
      tree_filter: undefined,
    };

    const expectedSearchModel: SearchModel = {
      filter: undefined,
      traceFilter: undefined,
      treeFilter: undefined,
    };

    const result2 = convertFilterFormat(seriesFiltersInput, 'searchModel');
    expect(result2).toEqual(expectedSearchModel);
  });
});

describe('splitMetadataKeys', () => {
  it('should return empty objects when commonMetadata is null', () => {
    const result = splitMetadataKeys(
      null as unknown as SessionMetadataResponse
    );
    expect(result).toEqual({
      ignoredMetadata: {},
      metadataToDisplay: {},
    });
  });

  it('should split metadata into ignored and display objects', () => {
    const commonMetadata: SessionMetadataResponse = {
      [DEFAULT_METADATA_TO_IGNORE[0]]: ['1.0'],
      [DEFAULT_METADATA_TO_IGNORE[1]]: ['train'],
      custom_field: ['value'],
      another_field: ['123'],
    };

    const result = splitMetadataKeys(commonMetadata);

    expect(result.ignoredMetadata).toEqual({
      [DEFAULT_METADATA_TO_IGNORE[0]]: ['1.0'],
      [DEFAULT_METADATA_TO_IGNORE[1]]: ['train'],
    });

    expect(result.metadataToDisplay).toEqual({
      custom_field: ['value'],
      another_field: ['123'],
    });
  });

  it('should handle empty metadata object', () => {
    const result = splitMetadataKeys({});
    expect(result).toEqual({
      ignoredMetadata: {},
      metadataToDisplay: {},
    });
  });

  it('should handle metadata with only ignored keys', () => {
    const commonMetadata: SessionMetadataResponse = {
      [DEFAULT_METADATA_TO_IGNORE[0]]: ['1.0'],
      [DEFAULT_METADATA_TO_IGNORE[1]]: ['train'],
      [DEFAULT_METADATA_TO_IGNORE[2]]: ['test'],
    };

    const result = splitMetadataKeys(commonMetadata);

    expect(result.ignoredMetadata).toEqual(commonMetadata);
    expect(result.metadataToDisplay).toEqual({});
  });

  it('should handle metadata with only display keys', () => {
    const commonMetadata: SessionMetadataResponse = {
      custom_field: ['value'],
      another_field: ['123'],
    };

    const result = splitMetadataKeys(commonMetadata);

    expect(result.ignoredMetadata).toEqual({});
    expect(result.metadataToDisplay).toEqual(commonMetadata);
  });
});
