import { renderHook } from '@testing-library/react';

import { describe, expect, it, vi } from 'vitest';

import {
  CustomChartDataSchema,
  CustomChartMetric,
  CustomChartSchema,
  CustomChartSeriesSchema,
} from '@/types/schema';

import { filterChartData, getSeriesInfo, useChartData } from '../useChartData';

describe('useChartData utils', () => {
  describe('getSeriesInfo', () => {
    it('should detect groups and extract unique series names', () => {
      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Group 1',
        },
        {
          series_id: 'series2',
          timestamp: '2024-01-01T00:00:00',
          value: 2,
          group: 'Group 2',
        },
      ];

      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series3',
          name: 'Series 1', // Duplicate name to test uniqueness
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const result = getSeriesInfo(data, series);

      expect(result.hasGroups).toBe(true);
      expect(result.uniqueSeriesNames).toEqual(['Series 1', 'Series 2']);
    });

    it('should handle data without groups', () => {
      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
        },
        {
          series_id: 'series2',
          timestamp: '2024-01-01T00:00:00',
          value: 2,
        },
      ];

      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const result = getSeriesInfo(data, series);

      expect(result.hasGroups).toBe(false);
      expect(result.uniqueSeriesNames).toEqual(['Series 1', 'Series 2']);
    });

    it('should filter out empty series names', () => {
      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
        },
      ];

      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: '', // Empty name
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const result = getSeriesInfo(data, series);

      expect(result.uniqueSeriesNames).toEqual(['Series 2']);
    });
  });

  describe('filterChartData', () => {
    it('should filter series and data based on active group', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Group A',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Group B',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Series 1 Group',
        },
        {
          series_id: 'series2',
          timestamp: '2024-01-01T00:00:00',
          value: 2,
          group: 'Series 2 Group',
        },
      ];

      const result = filterChartData(series, data, 'Group A');

      expect(result.filteredSeries.length).toBe(1);
      expect(result.filteredSeries[0].id).toBe('series1');
      expect(result.filteredSeries[0].name).toBe('Series 1 Group');
      expect(result.filteredData.length).toBe(1);
      expect(result.filteredData[0].series_id).toBe('series1');
    });

    it('should include all series when no active group is provided', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Group A',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Group B',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Group A Data',
        },
        {
          series_id: 'series2',
          timestamp: '2024-01-01T00:00:00',
          value: 2,
          group: 'Group B Data',
        },
      ];

      const result = filterChartData(series, data, undefined);

      expect(result.filteredSeries.length).toBe(2);
      expect(result.filteredSeries[0].name).toBe('Group A');
      expect(result.filteredSeries[1].name).toBe('Group B');
      expect(result.filteredData.length).toBe(2);
    });

    it('should handle data points without group', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Group A',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          // No group field
        },
      ];

      const result = filterChartData(series, data, 'Group A');

      expect(result.filteredSeries.length).toBe(1);
      expect(result.filteredSeries[0].name).toBe('Group A'); // Series name is preserved when no group is available
      expect(result.filteredData.length).toBe(1);
    });

    // New comprehensive tests for double renaming prevention and edge cases
    it('should prevent double renaming when no activeGroup is provided', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Original Series Name',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Data Group Name',
        },
      ];

      const result = filterChartData(series, data, undefined);

      // When no activeGroup, series names should NOT be renamed to prevent double renaming
      expect(result.filteredSeries[0].name).toBe('Original Series Name');
      expect(result.filteredSeries[0].id).toBe('series1');
    });

    it('should only rename series when activeGroup is provided', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Target Group',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Other Group',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Renamed Group Name',
        },
        {
          series_id: 'series2',
          timestamp: '2024-01-01T00:00:00',
          value: 2,
          group: 'Other Renamed Group',
        },
      ];

      const result = filterChartData(series, data, 'Target Group');

      // Only the filtered series should be renamed when activeGroup is provided
      expect(result.filteredSeries.length).toBe(1);
      expect(result.filteredSeries[0].name).toBe('Renamed Group Name');
      expect(result.filteredSeries[0].id).toBe('series1');
    });

    it('should handle empty data array', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Group A',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [];

      const result = filterChartData(series, data, 'Group A');

      expect(result.filteredSeries.length).toBe(1);
      expect(result.filteredSeries[0].name).toBe('Group A'); // Original name preserved
      expect(result.filteredData.length).toBe(0);
    });

    it('should handle empty series array', () => {
      const series: CustomChartSeriesSchema[] = [];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Some Group',
        },
      ];

      const result = filterChartData(series, data, 'Group A');

      expect(result.filteredSeries.length).toBe(0);
      expect(result.filteredData.length).toBe(0);
    });

    it('should handle data with missing series_id references', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Group A',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Valid Group',
        },
        {
          series_id: 'nonexistent_series',
          timestamp: '2024-01-01T00:00:00',
          value: 2,
          group: 'Invalid Group',
        },
      ];

      const result = filterChartData(series, data, 'Group A');

      expect(result.filteredSeries.length).toBe(1);
      expect(result.filteredData.length).toBe(1);
      expect(result.filteredData[0].series_id).toBe('series1');
    });

    it('should handle multiple data points for same series with different groups', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Target Group',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'First Group Name',
        },
        {
          series_id: 'series1',
          timestamp: '2024-01-02T00:00:00',
          value: 2,
          group: 'Second Group Name',
        },
      ];

      const result = filterChartData(series, data, 'Target Group');

      expect(result.filteredSeries.length).toBe(1);
      expect(result.filteredData.length).toBe(2);
      // Series should be renamed to the last group name found (due to Map.set() overwriting)
      expect(result.filteredSeries[0].name).toBe('Second Group Name');
    });

    it('should consistently use the last group name when multiple groups exist for same series', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Target Group',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Group A',
        },
        {
          series_id: 'series1',
          timestamp: '2024-01-02T00:00:00',
          value: 2,
          group: 'Group B',
        },
        {
          series_id: 'series1',
          timestamp: '2024-01-03T00:00:00',
          value: 3,
          group: 'Group C',
        },
      ];

      const result = filterChartData(series, data, 'Target Group');

      expect(result.filteredSeries.length).toBe(1);
      expect(result.filteredData.length).toBe(3);
      // Should use the last group name encountered
      expect(result.filteredSeries[0].name).toBe('Group C');
    });

    it('should preserve original series properties except name when renaming', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Original Name',
          metric: 'latency_p99',
          feedback_key: 'test_feedback',
          group_by: {
            attribute: 'tag',
            path: 'test_path',
            set_by: 'section',
          },
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'New Group Name',
        },
      ];

      const result = filterChartData(series, data, 'Original Name');

      expect(result.filteredSeries[0]).toEqual({
        id: 'series1',
        name: 'New Group Name', // Only name should change
        metric: 'latency_p99',
        feedback_key: 'test_feedback',
        group_by: {
          attribute: 'tag',
          path: 'test_path',
          set_by: 'section',
        },
      });
    });

    it('should fallback to original series name when group is empty string', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Target Group',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: '', // Empty group
        },
      ];

      const result = filterChartData(series, data, 'Target Group');

      expect(result.filteredSeries[0].name).toBe('Target Group'); // Should fallback to original name
    });

    it('should handle null activeGroup same as undefined', () => {
      const series: CustomChartSeriesSchema[] = [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'run_count',
          feedback_key: null,
        },
      ];

      const data: CustomChartDataSchema[] = [
        {
          series_id: 'series1',
          timestamp: '2024-01-01T00:00:00',
          value: 1,
          group: 'Group 1',
        },
        {
          series_id: 'series2',
          timestamp: '2024-01-01T00:00:00',
          value: 2,
          group: 'Group 2',
        },
      ];

      const resultWithNull = filterChartData(series, data, null as any);
      const resultWithUndefined = filterChartData(series, data, undefined);

      expect(resultWithNull.filteredSeries).toEqual(
        resultWithUndefined.filteredSeries
      );
      expect(resultWithNull.filteredData).toEqual(
        resultWithUndefined.filteredData
      );
      // Both should preserve original series names
      expect(resultWithNull.filteredSeries[0].name).toBe('Series 1');
      expect(resultWithNull.filteredSeries[1].name).toBe('Series 2');
    });
  });
});

describe('useChartData', () => {
  // Mock data for testing
  const createMockChart = (
    hasGroups = false,
    hasSectionGroupBy = false
  ): CustomChartSchema => {
    const series: CustomChartSeriesSchema[] = [
      {
        id: 'series1',
        name: 'Series 1',
        metric: 'run_count',
        feedback_key: null,
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        ...(hasSectionGroupBy && {
          group_by: {
            attribute: 'tag',
            path: 'test',
            set_by: 'section',
          },
        }),
      },
    ];

    if (hasSectionGroupBy) {
      series.push({
        id: 'series2',
        name: 'Series 2',
        metric: 'run_count',
        feedback_key: null,
        filters: {
          filter: undefined,
          tree_filter: undefined,
          trace_filter: undefined,
          session: [],
        },
        group_by: {
          attribute: 'tag',
          path: 'test',
          set_by: 'section',
        },
      });
    }

    const data: CustomChartDataSchema[] = [
      {
        series_id: 'series1',
        timestamp: '2024-01-01T00:00:00',
        value: 1,
        ...(hasGroups && { group: 'Group 1' }),
      },
    ];

    if (hasSectionGroupBy) {
      data.push({
        series_id: 'series2',
        timestamp: '2024-01-01T00:00:00',
        value: 2,
        ...(hasGroups && { group: 'Group 2' }),
      });
    }

    return {
      id: 'test-chart',
      title: 'Test Chart',
      description: 'Test Chart Description',
      chart_type: 'line',
      index: 0,
      series,
      data,
      common_filters: {
        filter: undefined,
        tree_filter: undefined,
        trace_filter: undefined,
        session: [],
      },
    };
  };

  it('should return correct values when there are no groups', () => {
    const mockChart = createMockChart(false, false);
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        seriesColorsMap: {},
      })
    );

    expect(result.current.hasGroups).toBe(false);
    expect(result.current.needsGroupTabs).toBe(false);
    expect(result.current.uniqueSeriesNames).toEqual(['Series 1']);
    expect(result.current.chartDataToRender).toEqual({
      ...mockChart,
      series: mockChart.series,
      data: mockChart.data,
    });
  });

  it('should return correct values when there are groups but no section group_by', () => {
    const mockChart = createMockChart(true, false);
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        seriesColorsMap: {},
      })
    );

    expect(result.current.hasGroups).toBe(true);
    expect(result.current.needsGroupTabs).toBe(false);
    expect(result.current.uniqueSeriesNames).toEqual(['Series 1']);
    // When needsGroupTabs is false, activeGroup is undefined, so series names are NOT renamed
    // This prevents double renaming when expanded chart data is passed to actual chart data
    expect(result.current.chartDataToRender).toEqual({
      ...mockChart,
      series: mockChart.series, // Series should remain unchanged
      data: mockChart.data,
    });
  });

  it('should return correct values when there are groups and section group_by', () => {
    const mockChart = createMockChart(true, true);
    const setActiveGroup = vi.fn();
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        activeGroup: undefined,
        setActiveGroup,
        seriesColorsMap: {},
      })
    );

    expect(result.current.hasGroups).toBe(true);
    expect(result.current.needsGroupTabs).toBe(true);
    expect(result.current.uniqueSeriesNames).toEqual(['Series 1', 'Series 2']);

    // Should filter based on activeGroup (undefined in this case)
    expect(result.current.chartDataToRender.series.length).toBe(2);
    expect(result.current.chartDataToRender.data.length).toBe(2);

    // Check if setActiveGroup was called with the first series name
    expect(setActiveGroup).toHaveBeenCalledWith('Series 1');
  });

  it('should filter data based on activeGroup', () => {
    const mockChart = createMockChart(true, true);
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        activeGroup: 'Series 1',
        seriesColorsMap: {},
      })
    );

    expect(result.current.hasGroups).toBe(true);
    expect(result.current.needsGroupTabs).toBe(true);
    expect(result.current.uniqueSeriesNames).toEqual(['Series 1', 'Series 2']);

    // Should filter series and data based on activeGroup
    expect(result.current.chartDataToRender.series.length).toBe(1);
    expect(result.current.chartDataToRender.series[0].name).toBe('Group 1');
    expect(result.current.chartDataToRender.data.length).toBe(1);
    expect(result.current.chartDataToRender.data[0].series_id).toBe('series1');
  });

  it('should not set activeGroup if not needed', () => {
    const mockChart = createMockChart(false, false);
    const setActiveGroup = vi.fn();
    renderHook(() =>
      useChartData({
        chart: mockChart,
        activeGroup: undefined,
        setActiveGroup,
        seriesColorsMap: {},
      })
    );

    expect(setActiveGroup).not.toHaveBeenCalled();
  });

  it('should not set activeGroup if already set', () => {
    const mockChart = createMockChart(true, true);
    const setActiveGroup = vi.fn();
    renderHook(() =>
      useChartData({
        chart: mockChart,
        activeGroup: 'Series 1',
        setActiveGroup,
        seriesColorsMap: {},
      })
    );

    expect(setActiveGroup).not.toHaveBeenCalled();
  });

  it('should return correct legend items with colors when no groups are present', () => {
    const mockChart = createMockChart(false, false);
    const seriesColorsMap = {
      'Series 1': 'rgba(255, 0, 0, 0.7)', // Custom color for Series 1
    };
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        seriesColorsMap,
      })
    );

    expect(result.current.legendItems).toEqual([
      {
        label: 'Series 1',
        color: 'rgba(255, 0, 0, 0.7)', // Should use color from seriesColorsMap
      },
    ]);
  });

  it('should fall back to getSeriesColor when color not in seriesColorsMap', () => {
    const mockChart = createMockChart(false, false);
    const seriesColorsMap = {}; // Empty color map
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        seriesColorsMap,
      })
    );

    expect(result.current.legendItems).toEqual([
      {
        label: 'Series 1',
        color: expect.stringMatching(/^rgba\(\d+,\s*\d+,\s*\d+,\s*0\.8\)$/), // Should use getSeriesColor
      },
    ]);
  });

  it('should return correct legend items when groups are active', () => {
    const mockChart = createMockChart(true, true);
    const seriesColorsMap = {
      'Series 1': 'rgba(255, 0, 0, 0.7)',
      series2: 'rgba(0, 255, 0, 0.7)',
    };
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        activeGroup: 'Series 1',
        seriesColorsMap,
      })
    );

    // Should only show legend items for the active group
    expect(result.current.legendItems).toEqual([
      {
        label: 'Series 1',
        color: 'rgba(255, 0, 0, 0.7)',
      },
    ]);
  });

  it('should handle special series names (success/error) correctly', () => {
    const mockChart = {
      ...createMockChart(false, false),
      series: [
        {
          id: 'series1',
          name: 'Success Rate',
          metric: 'run_count' as CustomChartMetric,
          feedback_key: null,
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
        },
        {
          id: 'series2',
          name: 'Error Rate',
          metric: 'run_count' as CustomChartMetric,
          feedback_key: null,
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
        },
      ],
    };
    const seriesColorsMap = {
      'Success Rate': 'rgba(46, 204, 113, 0.7)',
      'Error Rate': 'rgba(228, 26, 28, 0.7)',
    };
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        seriesColorsMap,
      })
    );

    expect(result.current.legendItems).toEqual([
      {
        label: 'Success Rate',
        color: 'rgba(46, 204, 113, 0.7)', // Green for success
      },
      {
        label: 'Error Rate',
        color: 'rgba(228, 26, 28, 0.7)', // Red for error
      },
    ]);
  });

  it('should handle series with complex IDs correctly', () => {
    const mockChart = {
      ...createMockChart(false, false),
      series: [
        {
          id: 'series-feedback:positive:group1',
          name: 'Feedback',
          metric: 'run_count' as CustomChartMetric,
          feedback_key: null,
          filters: {
            filter: undefined,
            tree_filter: undefined,
            trace_filter: undefined,
            session: [],
          },
        },
      ],
    };
    const seriesColorsMap = {
      group1: 'rgba(255, 0, 0, 0.7)',
    };
    const { result } = renderHook(() =>
      useChartData({
        chart: mockChart,
        seriesColorsMap,
      })
    );

    expect(result.current.legendItems).toEqual([
      {
        label: 'group1',
        color: 'rgba(255, 0, 0, 0.7)',
      },
    ]);
  });
});
