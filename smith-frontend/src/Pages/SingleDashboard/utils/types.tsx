import { Dispatch, SetStateAction } from 'react';

import {
  CustomChartDataSchema,
  CustomChartPreviewSchema,
  CustomChartPreviewThresholdConfig,
  CustomChartSchema,
  CustomChartSeriesSchema,
  CustomChartType,
  RunStatsGroupBy,
} from '@/types/schema';

import { ChartTimeFilter } from '../hooks/useChartTimeFilter';

export type TooltipDataPoint = {
  series: CustomChartSeriesSchema;
  id: string;
  name: string;
  datum: CustomChartDataSchema | null;
  color: string;
  category?: string;
};

export type ChartDataProps = {
  chart: CustomChartSchema | CustomChartPreviewSchema;
  type: CustomChartType;
  timeFilter: ChartTimeFilter;
  thresholdConfig?: CustomChartPreviewThresholdConfig | null;
  hideLegend?: boolean;
  isLastColumn?: boolean;
  forceV1Tooltip?: boolean;
  smoothCurve?: boolean;
  groupBySelection?: RunStatsGroupBy;
  activeGroup?: string;
  setActiveGroup?: Dispatch<SetStateAction<string>>;
  isLoading?: boolean;
  seriesColorsMap: Record<string, string>;
};
