import { ThreadStatus } from '@langchain/langgraph-sdk';
import {
  AlertCircleIcon,
  ClockSnoozeIcon,
  Loading02Icon,
  ZapOffIcon,
} from '@langchain/untitled-ui-icons';

import React from 'react';

export const GRAPH_SELECTOR_TITLE = 'Graph';
export const GRAPH_SELECTOR_DESCRIPTION =
  'Select a graph from this deployment for your assistant.';

export const GRAPH_PREVIEW_TITLE = 'Graph Preview';
export const GRAPH_PREVIEW_DESCRIPTION =
  'This shows the structure and sequence of the assistant.';

export const EDIT_AGENT_TITLE = 'Edit assistant';
export const EDIT_AGENT_DESCRIPTION =
  'Customize your assistant and personalize it to your needs.';

export const EDIT_AGENT_NAME_TITLE = 'Assistant name';
export const EDIT_AGENT_NAME_DESCRIPTION =
  'Give your assistant a name that will help you identify it.';

export const EDIT_AGENT_DESCRIPTION_TITLE = 'Description';
export const EDIT_AGENT_DESCRIPTION_DESCRIPTION =
  'Briefly explain how you will use this assistant.';

export const THREAD_STATUS_MAP: Record<
  ThreadStatus,
  {
    label: string;
    icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    bgColor: string;
    textColor: string;
  }
> = {
  idle: {
    label: 'Idle',
    icon: ClockSnoozeIcon,
    bgColor: 'bg-secondary',
    textColor: 'text-tertiary',
  },
  busy: {
    label: 'Busy',
    icon: Loading02Icon,
    bgColor: 'bg-brand-secondary',
    textColor: 'text-brand-tertiary',
  },
  interrupted: {
    label: 'Interrupted',
    icon: ZapOffIcon,
    bgColor: 'bg-purple',
    textColor: 'text-purple',
  },
  error: {
    label: 'Error',
    icon: AlertCircleIcon,
    bgColor: 'bg-error-primary',
    textColor: 'text-error',
  },
};

export const DEPLOYMENT_TYPES_TO_DISPLAY_NAME = {
  prod: 'Production',
  dev: 'Development',
  dev_free: 'Development (free)',
  undefined: 'Unknown',
};

export const REVISION_STATUS_TO_DISPLAY_NAME = {
  AWAITING_BUILD: 'Waiting for build',
  BUILDING: 'Building',
  AWAITING_DEPLOY: 'Waiting for deploy',
  DEPLOYING: 'Deploying',
  DEPLOYED: 'Deployed',
  BUILD_FAILED: 'Build failed',
  CREATE_FAILED: 'Create failed',
  DEPLOY_FAILED: 'Deploy failed',
  INTERRUPTED: 'Interrupted',
  UNKNOWN: 'Unknown',
  CREATING: 'Creating revision',
};

export const REVISION_STATUS_DEPLOY_AVAILABLE = [
  'AWAITING_DEPLOY',
  'DEPLOYING',
  'DEPLOY_FAILED',
  'DEPLOYED',
  'INTERRUPTED',
  'UNKNOWN',
];
