import { JSONSchema7Definition } from 'json-schema';
import { useContext, useEffect, useMemo } from 'react';

import { useGraphAssistants } from '@/Pages/Graph/src/api';
import { useGraphAssistantsDebugSchema } from '@/Pages/Graph/src/api/useGraphSwr';
import { StudioModeContext } from '@/Pages/Graph/src/hooks/useStudioMode';
import { isSchemaMessageArray } from '@/Pages/Graph/src/utils';

export const useProjectSupportsChat = () => {
  const { studioMode } = useContext(StudioModeContext);
  const {
    assistants: graphs,
    isLoading: isLoadingAssistants,
    isValidating: isAssistantsValidating,
    hasMore,
    size,
    setSize,
  } = useGraphAssistants({
    metadata: { created_by: 'system' },
    options: {
      revalidateOnFocus: false,
      keepPreviousData: true,
    },
  });
  const { data: schemas, isLoading: isLoadingSchemas } =
    useGraphAssistantsDebugSchema(
      graphs?.map((a) => a.assistant_id),
      {
        revalidateOnFocus: false,
        keepPreviousData: true,
      }
    );
  const supportedGraphs = useMemo(() => {
    return graphs?.filter((_, index) => {
      const schema = schemas?.[index];
      const inputSchema = schema?.input ?? schema?.state;
      return getMessagesKey(inputSchema);
    });
  }, [graphs, schemas]);

  // if trying to load chat and haven't fetched any supported graphs, fetch next page
  useEffect(() => {
    if (
      hasMore &&
      !supportedGraphs?.length &&
      !isAssistantsValidating &&
      !isLoadingSchemas
    ) {
      setSize(size + 1);
    }
  }, [
    supportedGraphs,
    hasMore,
    size,
    setSize,
    schemas,
    isAssistantsValidating,
    studioMode,
    isLoadingSchemas,
  ]);

  return {
    supportedGraphs,
    isLoading:
      (isLoadingAssistants || isLoadingSchemas) && !supportedGraphs?.length,
    hasMore,
    size,
    setSize,
    isValidating: isAssistantsValidating || isLoadingSchemas,
  };
};

export const useAssistantSupportsChat = (assistantId?: string) => {
  const { data: schemas, isLoading: isLoadingSchema } =
    useGraphAssistantsDebugSchema(assistantId ? [assistantId] : undefined);
  const schema = schemas?.[0];
  const inputSchema = schema?.input ?? schema?.state;
  const messagesKey = useMemo(() => getMessagesKey(inputSchema), [inputSchema]);

  return {
    supportsChat: messagesKey !== undefined,
    messagesKey,
    isLoading: isLoadingSchema,
  };
};

const getMessagesKey = (
  inputSchema: JSONSchema7Definition | null | undefined
) => {
  // todo: this may not be a complete implementation.
  // take a look at smith-frontend/src/Pages/Graph/src/components/debug/common/schema-editor.tsx renderSchemaBase
  if (typeof inputSchema === 'object' && inputSchema?.properties) {
    const [key] =
      Object.entries(inputSchema.properties).find(
        ([_, value]) => typeof value === 'object' && isSchemaMessageArray(value)
      ) ?? [];
    return key;
  }
  return undefined;
};
