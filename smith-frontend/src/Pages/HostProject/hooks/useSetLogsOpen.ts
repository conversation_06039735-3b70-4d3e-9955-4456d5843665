import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export const useSetLogsOpen = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [revisionPeekId, setRevisionPeekId] = useState<string | null>(
    searchParams.get('revisionPeek')
  );

  const onRevisionPeekChange = (runId: string | null) => {
    if (runId) {
      setRevisionPeekId(runId);
      setSearchParams(
        (prev) => {
          prev.set('revisionPeek', runId);
          return prev;
        },
        { replace: true }
      );
    } else {
      setRevisionPeekId(null);
      setSearchParams(
        (prev) => {
          prev.delete('revisionPeek');
          return prev;
        },
        { replace: true }
      );
    }
  };

  return {
    onRevisionPeekChange,
    revisionPeekId,
  };
};
