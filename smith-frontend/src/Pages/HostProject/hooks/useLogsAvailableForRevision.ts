import { useDeploymentFormType } from '@/Pages/HostProjects/hooks/useDeploymentFormType';
import { getRevisionSimpleStatus } from '@/components/HostRevisionStatus/HostRevisionStatus';
import { useHostRevision } from '@/hooks/useSwr';

import { REVISION_STATUS_DEPLOY_AVAILABLE } from '../constants';

export const useLogsAvailableForRevision = ({
  hostProjectId,
  revisionId,
}: {
  hostProjectId: string | undefined;
  revisionId: string | undefined;
}) => {
  const { data: revision } = useHostRevision(
    { hostProjectId, revisionId },
    {
      refreshInterval: (data) =>
        getRevisionSimpleStatus(data?.status ?? '') === 'pending' ? 3000 : 0,
    }
  );
  const deploymentFormType = useDeploymentFormType();
  const getLogsAvailable = (status?: string) => {
    if (deploymentFormType === 'self_hosted') return true;
    if (deploymentFormType === 'cloud') return true;
    if (!status) return undefined;
    return REVISION_STATUS_DEPLOY_AVAILABLE.includes(status);
  };
  return {
    logsAvailable: getLogsAvailable(revision?.status),
    getLogsAvailable,
  };
};
