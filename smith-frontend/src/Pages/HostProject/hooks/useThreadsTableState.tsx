import { DefaultVal<PERSON>, Thread, ThreadStatus } from '@langchain/langgraph-sdk';
import {
  CheckCircleIcon,
  PlayCircleIcon,
  Trash02Icon,
  XCircleIcon,
} from '@langchain/untitled-ui-icons';
import { Button, CircularProgress, Tooltip } from '@mui/joy';
import {
  ColumnDef,
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useCallback, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import {
  ThreadSortBy,
  useGraphCancelRunningRunsForThreads,
  useGraphThreadDelete,
  useGraphThreadsBypassSdk,
} from '@/Pages/Graph/src/api/useGraphSwr';
import {
  getTableSelectColumnDef,
  useDataGridSizingLocalStorage,
} from '@/components/DataGrid.utils';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';
import { useDataGridState } from '@/hooks/useDataGridState';
import { useSortingState } from '@/hooks/useSortingState';
import { useOrganizationId } from '@/hooks/useSwr';
import { cn } from '@/utils/tailwind';

import { THREAD_STATUS_MAP } from '../constants';

const columnHelper = createColumnHelper<Thread<DefaultValues>>();

const COLUMNS_DEF: ColumnDef<Thread<DefaultValues>, any>[] = [
  columnHelper.accessor('thread_id', {
    id: 'thread_id',
    header: 'Thread ID',
    size: 350,
    minSize: 200,
    maxSize: 400,
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {row.original.thread_id}
      </TextOverflowTooltip>
    ),
  }),

  columnHelper.accessor('updated_at', {
    id: 'updated_at',
    header: 'Updated At',
    size: 150,
    minSize: 100,
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {new Date(row.original.updated_at).toLocaleString()}
      </TextOverflowTooltip>
    ),
  }),
  columnHelper.accessor('created_at', {
    id: 'created_at',
    header: 'Created At',
    size: 150,
    minSize: 100,
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {new Date(row.original.created_at).toLocaleString()}
      </TextOverflowTooltip>
    ),
  }),
  columnHelper.display({
    id: 'delete',
    cell: ({ row, table }) => {
      const { actions } = (table.options.meta ?? {}) as {
        actions: {
          handleDelete: (threadId: string) => void;
        };
      };

      return (
        <div className="flex items-center justify-end">
          <Button
            color="neutral"
            variant="plain"
            onClick={(e) => {
              e.stopPropagation();
              actions.handleDelete(row.original.thread_id);
            }}
          >
            <Trash02Icon className="h-4 w-4" />
          </Button>
        </div>
      );
    },
    size: 80,
  }),
];

export const useThreadsTableState = ({ hostProjectId }) => {
  const { trigger: deleteThread, isMutating: isDeletingThread } =
    useGraphThreadDelete();
  const {
    trigger: cancelRunningRunsForThreads,
    isMutating: isCancellingRunningRuns,
  } = useGraphCancelRunningRunsForThreads();
  const organizationId = useOrganizationId();
  const { paginationModel, setPaginationModel, resetPaginationModel } =
    useDataGridState({
      defaultPaginationModel: {
        pageIndex: 0,
        pageSize: 10,
      },
    });
  const [sortingState, setSortingState] = useSortingState('updated_at');
  const [selectedStatusFilter, _setSelectedStatusFilter] = useState<
    ThreadStatus | 'all'
  >('all');
  const hasFilter = selectedStatusFilter !== 'all';

  const [tableSelectedThreadIds, setTableSelectedThreadIds] = useState<
    Record<string, boolean>
  >({});

  const setSelectedStatusFilter = useCallback(
    (status: ThreadStatus | 'all') => {
      resetPaginationModel();
      setTableSelectedThreadIds({});
      _setSelectedStatusFilter(status);
    },
    [resetPaginationModel, _setSelectedStatusFilter]
  );

  const [columnSizing, setColumnSizing] =
    useDataGridSizingLocalStorage('threads');

  const [selectedThreadId, setSelectedThreadId] = useState<
    string | undefined
  >();
  const [deletingThreadId, setDeletingThreadId] = useState<string>();
  const [stoppingThreadId, setStoppingThreadId] = useState<string>();

  const paginationOffset = paginationModel.pageIndex * paginationModel.pageSize;
  const {
    data: threadsResponse,
    isLoading,
    isValidating,
  } = useGraphThreadsBypassSdk({
    offset: paginationOffset,
    limit: paginationModel.pageSize,
    sort_by: sortingState[0].id as ThreadSortBy,
    sort_order: sortingState[0].desc ? 'desc' : 'asc',
    status: hasFilter ? selectedStatusFilter : undefined,
  });
  const { threads, total } = threadsResponse ?? {};

  const threadIdToThreadMap = useMemo(() => {
    return threads?.reduce((acc, thread) => {
      acc[thread.thread_id] = thread;
      return acc;
    }, {} as Record<string, Thread>);
  }, [threads]);

  const [lastSelectedId, setLastSelectedId] = useState<string | null>(null);
  const columns = useMemo(() => {
    return [
      getTableSelectColumnDef<Thread>({
        checkboxesHighlighted: false,
        lastSelectedId,
        setLastSelectedId,
      }),
      columnHelper.accessor('status', {
        id: 'status',
        header: () => <CheckCircleIcon className="m-1 h-5 w-5" />,
        size: 40,
        minSize: 20,
        enableResizing: false,
        cell: ({ row }) => {
          const status = row.original.status;
          const { icon: Icon, bgColor, textColor } = THREAD_STATUS_MAP[status];
          return (
            <div className={cn(bgColor, textColor, 'rounded-full p-1')}>
              <Icon className="size-4" />
            </div>
          );
        },
        enableSorting: !hasFilter,
      }),
      ...COLUMNS_DEF.slice(0, -1),
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <Tooltip title="Open thread in Studio">
              <Button
                component={Link}
                to={`/studio/?threadId=${row.original.thread_id}&hostProjectId=${hostProjectId}&organizationId=${organizationId}`}
                variant="plain"
                color="neutral"
                onClick={(e) => e.stopPropagation()}
                sx={{
                  p: 1,
                  py: 0,
                }}
              >
                <PlayCircleIcon className="size-4 text-tertiary" />
              </Button>
            </Tooltip>
            {row.original.status === 'busy' ? (
              isCancellingRunningRuns ? (
                <CircularProgress size="sm" />
              ) : (
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    setStoppingThreadId(row.original.thread_id);
                  }}
                  className="rounded-md bg-tertiary p-1 hover:bg-quaternary"
                  disabled={isCancellingRunningRuns}
                >
                  <div className="flex items-center gap-2">
                    <XCircleIcon className="size-4 text-tertiary" />
                    <span className="text-xs font-semibold text-tertiary">
                      Stop
                    </span>
                  </div>
                </button>
              )
            ) : null}
          </div>
        ),
      },
      ...COLUMNS_DEF.slice(-1),
    ];
  }, [
    lastSelectedId,
    setLastSelectedId,
    hostProjectId,
    organizationId,
    hasFilter,
    isCancellingRunningRuns,
    setStoppingThreadId,
  ]);
  const table = useReactTable({
    data: threads ?? [],
    columns,
    state: {
      pagination: paginationModel,
      columnSizing,
      sorting: sortingState,
      rowSelection: tableSelectedThreadIds,
    },
    meta: {
      actions: {
        handleDelete: setDeletingThreadId,
        handleStop: setStoppingThreadId,
      },
    },
    getRowId: (row) => row.thread_id,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onPaginationChange: setPaginationModel,
    onSortingChange: setSortingState,
    onColumnSizingChange: setColumnSizing,
    onRowSelectionChange: setTableSelectedThreadIds,
    columnResizeMode: 'onChange',
    manualFiltering: true,
    manualPagination: true,
    enableSorting: true,
    enableColumnResizing: true,
    pageCount: Math.ceil((total ?? 0) / paginationModel.pageSize),
  });

  return {
    table,
    threads,
    threadIdToThreadMap,
    total,
    isLoading,
    isValidating,
    selectedStatusFilter,
    setSelectedStatusFilter,
    paginationModel,
    selectedThreadId,
    setSelectedThreadId,
    deletingThreadId,
    setDeletingThreadId,
    stoppingThreadId,
    setStoppingThreadId,
    tableSelectedThreadIds,
    setTableSelectedThreadIds,
    deleteThread,
    isDeletingThread,
    cancelRunningRunsForThreads,
    isCancellingRunningRuns,
  };
};
