import PlayCircleOutline from '@mui/icons-material/PlayCircleOutline';
import { Button } from '@mui/joy';
import Alert from '@mui/joy/Alert';
import LinearProgress from '@mui/joy/LinearProgress';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';

import Breadcrumbs from '@/components/Breadcrumbs.tsx';
import { CopyMultiButton } from '@/components/CopyButton/CopyMultiButton.tsx';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert.tsx';
import { HostProjectSettingsModalWithButton } from '@/components/HostProjectCrudPane/HostProjectSettingsModalWithButton.tsx';
import { HostRevisionsTable } from '@/components/HostRevisionsTable/HostRevisionsTable.tsx';
import { UncontrolledRunsTable } from '@/components/RunsTable/UncontrolledRunsTable.tsx';
import {
  TabGroup,
  TabLabel,
  TabList,
  TabPanel,
  TabPanels,
} from '@/components/Tabs.tsx';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip.tsx';
import { Tooltip } from '@/components/Tooltip/Tooltip.tsx';
import { VegaPreview } from '@/components/VegaChart.tsx';
import { usePermissions } from '@/hooks/usePermissions.tsx';
import {
  useHostProject,
  useOrganizationId,
  useRunsMonitor,
} from '@/hooks/useSwr.tsx';
import ArrowRightIcon from '@/icons/ArrowRightIcon.svg?react';
import ProjectIcon from '@/icons/ProjectIcon';
import RocketShipIcon from '@/icons/RocketShip.svg?react';
import { HostProjectSchema } from '@/types/schema.ts';
import {
  appGraphIndexPath,
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
} from '@/utils/constants.tsx';
import { useResizeObserver } from '@/utils/use-resize-observer.tsx';

import { PageTitle } from '../../components/PageTitle.tsx';
import { RevisionCrudModalWithButton } from '../../components/RevisionsCrudModal/RevisionCrudModalWithButton.tsx';
import { LangGraphContext } from '../Graph/src/api/index.ts';
import { useCreateStudioClient } from '../Graph/src/hooks/useCreateStudioClient.ts';
import PlatformBetaInfoBanner from '../HostProjects/components/PlatformBetaInfoBanner.tsx';
import { AgentsPanel } from './components/AgentsPanel/index.tsx';
import { ProjectStats } from './components/ProjectStats.tsx';
import { ThreadsPanel } from './components/ThreadsPanel/index.tsx';

dayjs.extend(utc);

function NoRevisions() {
  return (
    <div className="mx-5">
      <Alert variant="outlined">There are no revisions.</Alert>
    </div>
  );
}

const HostProjectRunsTable = (props: { hostProject: HostProjectSchema }) => {
  const runsFilter = useMemo(
    () => ({
      session: [props.hostProject.tracer_session_id],
      start_time: dayjs.utc().subtract(7, 'days').toISOString(),
      is_root: true,
    }),
    [props.hostProject.tracer_session_id]
  );
  if (!props.hostProject.tracer_session_id) return null;
  return (
    <UncontrolledRunsTable
      tableDisplay="list"
      runsFilter={runsFilter}
      withToolbar={false}
      withPagination={false}
    />
  );
};

function HostProjectRevisionsCard(props: {
  hostProject: HostProjectSchema;
  limit?: number;
  createRevisionEnabled?: boolean;
}) {
  const copyPrefix =
    props.limit != null ? `The most recent revisions` : 'All revisions';
  return (
    <div className="overflow-hidden rounded-lg border border-secondary">
      <div className="flex items-center justify-between border-b border-secondary bg-secondary p-5">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold">Revisions</h2>
          <p className="text-sm text-tertiary">
            {copyPrefix} associated with this deployment
          </p>
        </div>
        {props.createRevisionEnabled && (
          <RevisionCrudModalWithButton hostProject={props.hostProject} />
        )}
      </div>

      <HostRevisionsTable
        hostProject={props.hostProject}
        emptyState={<NoRevisions />}
        limit={props.limit}
      />
    </div>
  );
}

function HostProjectTraceCount(props: { hostProject: HostProjectSchema }) {
  function getTimezone(): string {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (e) {
      return 'UTC';
    }
  }

  const selectedInterval = {
    value: '7d',
    label: '7 days',
    params: {
      interval: { days: 7 },
      stride: { hours: 8 },
    },
  };

  const monitor = useRunsMonitor(
    selectedInterval
      ? {
          ...selectedInterval.params,
          groups: [{ session: props.hostProject.tracer_session_id }],
          timezone: getTimezone(),
        }
      : null
  );

  const block = monitor.data?.blocks.find(
    (i) => i.title.toLocaleLowerCase() === 'trace count'
  );
  if (block == null) return null;
  return (
    <VegaPreview
      className="absolute inset-0 rounded-lg"
      spec={block.chart_spec}
      columns={block.columns}
      rows={block.rows}
    />
  );
}

function HostProjectTraceCountCard(props: { hostProject: HostProjectSchema }) {
  const organizationId = useOrganizationId();
  return (
    <div className="flex flex-col rounded-lg border border-secondary bg-secondary">
      <div className="flex items-center justify-between p-5">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold">Trace Count</h2>
          <p className="text-sm text-tertiary">
            A summary chart showing successful, pending and erroring traces in
            the last 7 days.
          </p>
        </div>
        <Link
          to={`/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${props.hostProject.tracer_session_id}?tab=2`}
        >
          <Button
            variant="outlined"
            color="neutral"
            endDecorator={<ArrowRightIcon />}
            sx={{ pr: '12px', whiteSpace: 'nowrap' }}
          >
            All charts
          </Button>
        </Link>
      </div>
      <div className="relative mx-5 mb-5 min-h-[420px] flex-grow">
        <HostProjectTraceCount hostProject={props.hostProject} />
      </div>
    </div>
  );
}

function HostProjectRunsCard(props: { hostProject: HostProjectSchema }) {
  const organizationId = useOrganizationId();
  return (
    <div className="rounded-lg border border-secondary bg-secondary">
      <div className="flex items-center justify-between border-b border-secondary p-5">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold">Recent Traces</h2>
          <p className="text-sm text-tertiary">
            Recent traces (7D) for this deployment appear here. Go to the
            tracing project to see all traces.
          </p>
        </div>
        <Link
          to={`/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${props.hostProject.tracer_session_id}`}
        >
          <Button
            variant="outlined"
            color="neutral"
            startDecorator={<ProjectIcon />}
            endDecorator={<ArrowRightIcon />}
            sx={{ pr: '12px', pl: '12px', whiteSpace: 'nowrap' }}
          >
            See tracing project
          </Button>
        </Link>
      </div>

      <HostProjectRunsTable hostProject={props.hostProject} />
    </div>
  );
}

const HostProjectPage = ({
  hostProjectId,
  createRevisionEnabled,
}: {
  hostProjectId: string | undefined;
  createRevisionEnabled?: boolean;
}) => {
  const organizationId = useOrganizationId();
  const {
    data: hostProject,
    isLoading,
    error,
  } = useHostProject(hostProjectId ?? null);

  const [actionsCompact, setActionsCompact] = useState(false);
  const actionsContainerRef = useResizeObserver<HTMLDivElement>((entry) => {
    const width = entry[0]?.contentRect.width;
    if (!width) return;
    setActionsCompact(width < 400);
  });

  const getDeploymentPlatformId = (): string => {
    const metadata = hostProject?.metadata || {};
    if ('platform' in metadata) {
      return metadata.platform;
    } else {
      return 'cloud_run';
    }
  };

  const copyItems = useMemo(() => {
    if (!hostProject) return [];
    return [
      { prefix: 'Deployment ID', item: hostProject.id },
      ...(hostProject.resource?.latest_revision?.hosted_langserve_revision_id
        ? [
            {
              prefix: 'Latest Revision ID',
              item: hostProject.resource?.latest_revision
                ?.hosted_langserve_revision_id,
            },
          ]
        : []),
    ];
  }, [hostProject]);

  if (isLoading)
    return (
      <div className="flex w-full items-start justify-start">
        <LinearProgress />
      </div>
    );
  if (error)
    return (
      <div className="flex w-full items-start justify-start p-4">
        <ExpandableErrorAlert error={error} />
      </div>
    );

  if (!hostProject) return <div>No host project</div>;

  const { name } = hostProject;
  return (
    <div className="flex w-full grow overflow-x-hidden border-t border-secondary">
      <div className="flex flex-1 flex-col overflow-hidden px-4 py-3">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <PageTitle icon={<RocketShipIcon />}>
              <TextOverflowTooltip>{name}</TextOverflowTooltip>
            </PageTitle>

            <CopyMultiButton copyItems={copyItems} />
          </div>
          <div
            className="flex grow items-center justify-end gap-2"
            ref={actionsContainerRef}
          >
            {createRevisionEnabled && (
              <>
                <HostProjectSettingsModalWithButton
                  hostProject={hostProject}
                  deploymentPlatformId={getDeploymentPlatformId()}
                />
                <RevisionCrudModalWithButton
                  hostProject={hostProject}
                  compact={actionsCompact}
                />
              </>
            )}

            {hostProject.resource?.url && (
              <Tooltip title={actionsCompact ? 'LangGraph Studio' : ''}>
                <Button
                  component={Link}
                  to={`/${appGraphIndexPath}/thread?organizationId=${organizationId}&hostProjectId=${encodeURIComponent(
                    hostProject.id
                  )}`}
                  size="sm"
                >
                  <div className="flex items-center gap-2">
                    <PlayCircleOutline />
                    {!actionsCompact && 'LangGraph Studio'}
                  </div>
                </Button>
              </Tooltip>
            )}
          </div>
        </div>
        <div className="w-full">
          <PlatformBetaInfoBanner />
        </div>

        <TabGroup>
          <TabList className="min-h-[40px] overflow-x-auto">
            <TabLabel>Overview</TabLabel>
            <TabLabel>Revisions</TabLabel>
            {hostProject.resource?.url && <TabLabel>Agents</TabLabel>}
            {hostProject.resource?.url && <TabLabel>Threads</TabLabel>}
          </TabList>
          <TabPanels className="overflow-y-auto">
            <TabPanel>
              <div className="flex flex-col gap-4">
                <div className="grid gap-4 lg:grid-cols-2">
                  <HostProjectRevisionsCard
                    hostProject={hostProject}
                    limit={5}
                  />

                  <HostProjectTraceCountCard hostProject={hostProject} />
                </div>

                <HostProjectRunsCard hostProject={hostProject} />
              </div>
            </TabPanel>
            <TabPanel>
              <HostProjectRevisionsCard
                hostProject={hostProject}
                createRevisionEnabled={createRevisionEnabled}
              />
            </TabPanel>
            <TabPanel>
              <AgentsPanel hostProjectId={hostProject.id} />
            </TabPanel>
            <TabPanel>
              <ThreadsPanel hostProjectId={hostProject.id} />
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>
      <ProjectStats hostProject={hostProject} />
    </div>
  );
};

export default function Page() {
  const { hostProjectId } = useParams();
  const { authorize } = usePermissions();

  const client = useCreateStudioClient({
    projectId: hostProjectId ?? undefined,
    standalone: false,
  });

  return (
    <LangGraphContext.Provider value={client}>
      <div className="flex h-[100vh] flex-col">
        <div className="px-4 py-3">
          <Breadcrumbs />
        </div>
        <HostProjectPage
          hostProjectId={hostProjectId}
          createRevisionEnabled={authorize('deployments:update')}
        />
      </div>
    </LangGraphContext.Provider>
  );
}
