import PlayCircleOutline from '@mui/icons-material/PlayCircleOutline';
import { Button } from '@mui/joy';
import Alert from '@mui/joy/Alert';
import LinearProgress from '@mui/joy/LinearProgress';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';

import Breadcrumbs from '@/components/Breadcrumbs.tsx';
import { CopyMultiButton } from '@/components/CopyButton/CopyMultiButton.tsx';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert.tsx';
import { HostProjectSettingsModalWithButton } from '@/components/HostProjectCrudPane/HostProjectSettingsModalWithButton.tsx';
import { PricingBanner } from '@/components/HostProjectCrudPane/PricingBanner.tsx';
import { getRevisionSimpleStatus } from '@/components/HostRevisionStatus/HostRevisionStatus.tsx';
import { HostRevisionsTable } from '@/components/HostRevisionsTable/HostRevisionsTable.tsx';
import { LatestRevisionWarningButton } from '@/components/HostRevisionsTable/LatestRevisionWarningButton.tsx';
import { UncontrolledRunsTable } from '@/components/RunsTable/UncontrolledRunsTable.tsx';
import {
  TabGroup,
  TabLabel,
  TabList,
  TabPanel,
  TabPanels,
} from '@/components/Tabs.tsx';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip.tsx';
import { Tooltip } from '@/components/Tooltip/Tooltip.tsx';
import { VegaPreview } from '@/components/VegaChart.tsx';
import { useDataGridState } from '@/hooks/useDataGridState.tsx';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig.tsx';
import { usePermissions } from '@/hooks/usePermissions.tsx';
import {
  useHostProject,
  useHostRevisions,
  useOrganizationId,
  usePrebuiltDashboardForSession,
  useRunsMonitor,
} from '@/hooks/useSwr.tsx';
import ArrowRightIcon from '@/icons/ArrowRightIcon.svg?react';
import ProjectIcon from '@/icons/ProjectIcon';
import RocketShipIcon from '@/icons/RocketShip.svg?react';
import { HostProjectSchema, HostRevisionSchema } from '@/types/schema.ts';
import {
  appDashboardsIndexPath,
  appGraphIndexPath,
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
} from '@/utils/constants.tsx';
import { useLocalStorageState } from '@/utils/use-local-storage-state.tsx';
import { useResizeObserver } from '@/utils/use-resize-observer.tsx';

import { PageTitle } from '../../components/PageTitle.tsx';
import { RevisionCrudModalWithButton } from '../../components/RevisionsCrudModal/RevisionCrudModalWithButton.tsx';
import { LangGraphContext } from '../Graph/src/api/index.ts';
import { useCreateStudioClient } from '../Graph/src/hooks/useCreateStudioClient.ts';
import PlatformBetaInfoBanner from '../HostProjects/components/PlatformBetaInfoBanner.tsx';
import { useDeploymentFormType } from '../HostProjects/hooks/useDeploymentFormType.ts';
import { Chart } from '../SingleDashboard/CustomChart.tsx';
import { useChartTimeFilter } from '../SingleDashboard/hooks/useChartTimeFilter.tsx';
import { useSeriesColorsMap } from '../SingleDashboard/utils/getSeriesColor.ts';
import { AgentsPanel } from './components/AgentsPanel/index.tsx';
import { ProjectStats } from './components/ProjectStats.tsx';
import { ThreadsPanel } from './components/ThreadsPanel/index.tsx';
import { useSetLogsOpen } from './hooks/useSetLogsOpen.ts';

dayjs.extend(utc);

function NoRevisions() {
  return (
    <div>
      <Alert variant="outlined" color="danger">
        There are no revisions.
      </Alert>
    </div>
  );
}

const HostProjectRunsTable = (props: { hostProject: HostProjectSchema }) => {
  const runsFilter = useMemo(
    () => ({
      session: [props.hostProject.tracer_session_id],
      start_time: dayjs.utc().subtract(7, 'days').toISOString(),
      is_root: true,
    }),
    [props.hostProject.tracer_session_id]
  );
  if (!props.hostProject.tracer_session_id) return null;
  return (
    <UncontrolledRunsTable
      tableDisplay="list"
      runsFilter={runsFilter}
      withToolbar={false}
      withPagination={false}
    />
  );
};

function HostProjectRevisionsCard(props: {
  hostProject: HostProjectSchema;
  createRevisionEnabled?: boolean;
  hideHeader?: boolean;
  showActiveRevision?: boolean;
}) {
  const { hostProject, showActiveRevision } = props;
  const { paginationModel, setPaginationModel } = useDataGridState({});
  const { mutate: refetchHostProject } = useHostProject(
    hostProject?.id ?? null
  );
  const {
    data: rows,
    isLoading,
    isValidating,
    mutate: refetchRevisions,
  } = useHostRevisions(
    hostProject?.id ?? null,
    showActiveRevision
      ? { limit: 1, offset: 0, status: 'DEPLOYED' }
      : {
          offset: paginationModel.pageIndex * paginationModel.pageSize,
          limit: paginationModel.pageSize,
        },
    {
      refreshInterval: (data) =>
        data?.some((item) => getRevisionSimpleStatus(item.status) === 'pending')
          ? 3000
          : 0,
      onSuccess: (data) => {
        const deployedRevision = data?.find((r) => r.status === 'DEPLOYED');
        if (
          deployedRevision?.id !==
          hostProject?.resource?.latest_active_revision
            ?.hosted_langserve_revision_id
        ) {
          refetchHostProject();
        }
      },
    }
  );
  const { data: latestRevision } = useHostRevisions(
    hostProject?.id ?? null,
    {
      limit: 1,
      offset: 0,
    },
    {
      refreshInterval: (data) =>
        data?.some((item) => getRevisionSimpleStatus(item.status) === 'pending')
          ? 3000
          : 0,
    }
  );

  const rowsToDisplay = useMemo(
    () =>
      showActiveRevision
        ? // if there is no deployed revision, show the latest revision
          ([rows?.[0] ?? latestRevision?.[0]].filter(
            Boolean
          ) as HostRevisionSchema[])
        : rows,
    [showActiveRevision, latestRevision, rows]
  );

  const { onRevisionPeekChange, revisionPeekId } = useSetLogsOpen();

  return (
    <div className="flex flex-col gap-2">
      {props.showActiveRevision && !!rows?.length && (
        <LatestRevisionWarningButton
          hostProjectId={props.hostProject?.id}
          onRevisionPeekChange={onRevisionPeekChange}
          onRevisionSuccess={refetchRevisions}
        />
      )}
      <div className="w-full overflow-hidden rounded-lg border border-secondary bg-primary">
        {!props.showActiveRevision && (
          <div className="flex items-center justify-between border-b border-secondary bg-secondary p-5">
            <div className="flex flex-col">
              <h2 className="text-lg font-semibold">Revisions</h2>
              <p className="text-sm text-tertiary">
                All revisions associated with this deployment
              </p>
            </div>
            {props.createRevisionEnabled && (
              <RevisionCrudModalWithButton hostProject={props.hostProject} />
            )}
          </div>
        )}

        <HostRevisionsTable
          rows={rowsToDisplay}
          isLoading={isLoading}
          isValidating={isValidating}
          paginationModel={paginationModel}
          setPaginationModel={setPaginationModel}
          onRevisionPeekChange={onRevisionPeekChange}
          revisionPeekId={revisionPeekId}
          hostProject={props.hostProject}
          emptyState={<NoRevisions />}
          showActiveRevision={props.showActiveRevision}
          hideHeader={props.hideHeader}
          paginationTotal={rows?.headers?.['x-pagination-total']}
        />
      </div>
    </div>
  );
}

function HostProjectTraceCount(props: { hostProject: HostProjectSchema }) {
  function getTimezone(): string {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (e) {
      return 'UTC';
    }
  }
  const {
    value: prebuiltDashboardsEnabled,
    isLoading: isPrebuiltDashboardsLoading,
  } = useOrgConfig(OrgConfigs.enable_prebuilt_dashboards);

  const timeFilter = useChartTimeFilter({
    timeRange: { duration: '7d' },
  });

  const {
    data: dashboard,
    mutate: mutateChartData,
    isLoading,
    error,
  } = usePrebuiltDashboardForSession(props.hostProject.tracer_session_id, {
    ...timeFilter.customChartsParams,
  });

  const chart = dashboard?.sub_sections
    ?.find((section) => section.id === 'sub-section-traces')
    ?.charts?.find((chart) => chart.id === 'chart-trace-count');

  const selectedInterval = {
    value: '7d',
    label: '7 days',
    params: {
      interval: { days: 7 },
      stride: { hours: 8 },
    },
  };

  const monitor = useRunsMonitor(
    selectedInterval
      ? {
          ...selectedInterval.params,
          groups: [{ session: props.hostProject.tracer_session_id }],
          timezone: getTimezone(),
        }
      : null
  );

  const block = monitor.data?.blocks.find(
    (i) => i.title.toLocaleLowerCase() === 'trace count'
  );

  const seriesColorsMap = useSeriesColorsMap({
    dashboard,
    isPrebuilt: true,
  });

  if (isPrebuiltDashboardsLoading)
    return (
      <div className="min-h-[420px]">
        <LinearProgress />
      </div>
    );

  if (prebuiltDashboardsEnabled) {
    if (isLoading)
      return (
        <div className="min-h-[420px]">
          <LinearProgress />
        </div>
      );
    if (error) return <ExpandableErrorAlert error={error} />;
    if (!chart) return null;
    return (
      <Chart
        key={chart.id}
        chart={chart}
        dashboardId={props.hostProject.tracer_session_id}
        openExistingChart={() => {}}
        mutateChartData={mutateChartData}
        timeFilter={timeFilter}
        hideProjectNames={true}
        hideExpandedView={true}
        hideChartHeader={true}
        readOnly={true}
        seriesColorsMap={seriesColorsMap}
      />
    );
  }
  if (block == null) return null;
  return (
    <div className="min-h-[420px]">
      <VegaPreview
        className="absolute inset-0 rounded-lg"
        spec={block.chart_spec}
        columns={block.columns}
        rows={block.rows}
      />
    </div>
  );
}

function HostProjectTraceCountCard(props: { hostProject: HostProjectSchema }) {
  const organizationId = useOrganizationId();
  const { value: enablePrebuiltDashboards } = useOrgConfig(
    OrgConfigs.enable_prebuilt_dashboards
  );
  const dashboardLink = enablePrebuiltDashboards
    ? `/${appOrganizationPath}/${organizationId}/${appDashboardsIndexPath}/${appProjectsPath}/${props.hostProject.tracer_session_id}`
    : `/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${props.hostProject.tracer_session_id}?tab=2`;
  return (
    <div className="flex flex-col rounded-lg border border-secondary bg-secondary">
      <div className="flex items-center justify-between p-5">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold">Trace Count</h2>
          <p className="text-sm text-tertiary">
            A summary chart showing successful, pending and erroring traces in
            the last 7 days.
          </p>
        </div>
        <Link to={dashboardLink}>
          <Button
            variant="outlined"
            color="neutral"
            endDecorator={<ArrowRightIcon />}
            sx={{ pr: '12px', whiteSpace: 'nowrap' }}
          >
            All charts
          </Button>
        </Link>
      </div>
      <div className="relative mx-5 mb-5 flex-grow">
        <HostProjectTraceCount hostProject={props.hostProject} />
      </div>
    </div>
  );
}

function HostProjectRunsCard(props: { hostProject: HostProjectSchema }) {
  const organizationId = useOrganizationId();
  return (
    <div className="rounded-lg border border-secondary bg-secondary">
      <div className="flex items-center justify-between border-b border-secondary p-5">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold">Recent Traces</h2>
          <p className="text-sm text-tertiary">
            Recent traces (7D) for this deployment appear here. Go to the
            tracing project to see all traces.
          </p>
        </div>
        <Link
          to={`/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${props.hostProject.tracer_session_id}`}
        >
          <Button
            variant="outlined"
            color="neutral"
            startDecorator={<ProjectIcon />}
            endDecorator={<ArrowRightIcon />}
            sx={{ pr: '12px', pl: '12px', whiteSpace: 'nowrap' }}
          >
            See tracing project
          </Button>
        </Link>
      </div>

      <HostProjectRunsTable hostProject={props.hostProject} />
    </div>
  );
}

const HostProjectPage = ({
  hostProjectId,
  createRevisionEnabled,
}: {
  hostProjectId: string | undefined;
  createRevisionEnabled?: boolean;
}) => {
  const deploymentFormType = useDeploymentFormType();
  const {
    value: lgPlatformGAEnabled,
    isLoading: isLangGraphPlatformGALoading,
  } = useOrgConfig(OrgConfigs.langgraph_platform_ga_enabled);
  const organizationId = useOrganizationId();
  const {
    data: hostProject,
    isLoading,
    error,
  } = useHostProject(hostProjectId ?? null);

  const { created_at } = hostProject ?? {};

  const createdBeforePricingUpdate = dayjs(created_at).isBefore(
    dayjs.utc('2025-05-14T00:00:00Z')
  );
  const [hasSeenPricingBanner, setHasSeenPricingBanner] = useLocalStorageState(
    `ls:host:${hostProjectId}:hasSeenPricingBanner`,
    false
  );

  const [projectStatsOpen, setProjectStatsOpen] = useState(true);

  const [actionsCompact, setActionsCompact] = useState(false);
  const actionsContainerRef = useResizeObserver<HTMLDivElement>((entry) => {
    const width = entry[0]?.contentRect.width;
    if (!width) return;
    setActionsCompact(width < 400);
  });

  const getDeploymentPlatformId = (): string => {
    const metadata = hostProject?.metadata || {};
    if ('platform' in metadata) {
      return metadata.platform;
    } else {
      return 'cloud_run';
    }
  };

  const copyItems = useMemo(() => {
    if (!hostProject) return [];
    return [
      { prefix: 'Deployment ID', item: hostProject.id },
      ...(hostProject.resource?.latest_revision?.hosted_langserve_revision_id
        ? [
            {
              prefix: 'Latest Revision ID',
              item: hostProject.resource?.latest_revision
                ?.hosted_langserve_revision_id,
            },
          ]
        : []),
    ];
  }, [hostProject]);

  if (isLoading || isLangGraphPlatformGALoading)
    return (
      <div className="flex w-full items-start justify-start">
        <LinearProgress />
      </div>
    );
  if (error)
    return (
      <div className="flex w-full items-start justify-start p-4">
        <ExpandableErrorAlert error={error} />
      </div>
    );

  if (!hostProject) return <div>No host project</div>;

  const { name } = hostProject;
  return (
    <div className="flex w-full grow overflow-x-hidden border-t border-secondary">
      <div className="flex flex-1 flex-col overflow-hidden px-4 py-3">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <PageTitle icon={<RocketShipIcon />}>
              <TextOverflowTooltip>{name}</TextOverflowTooltip>
            </PageTitle>

            <CopyMultiButton copyItems={copyItems} className="text-xs" />
          </div>
          <div
            className="flex grow items-center justify-end gap-2"
            ref={actionsContainerRef}
          >
            {!projectStatsOpen && (
              <button
                type="button"
                onClick={() => setProjectStatsOpen(!projectStatsOpen)}
                className="rounded-md px-1 hover:bg-primary-hover"
              >
                <span className="text-xs text-secondary">Details</span>
              </button>
            )}
            {createRevisionEnabled && (
              <>
                <HostProjectSettingsModalWithButton
                  hostProject={hostProject}
                  deploymentPlatformId={getDeploymentPlatformId()}
                />
                <RevisionCrudModalWithButton
                  hostProject={hostProject}
                  compact={actionsCompact}
                />
              </>
            )}

            {hostProject.resource?.url && (
              <Tooltip title={actionsCompact ? 'LangGraph Studio' : ''}>
                <Button
                  component={Link}
                  to={`/${appGraphIndexPath}/thread?organizationId=${organizationId}&hostProjectId=${encodeURIComponent(
                    hostProject.id
                  )}`}
                  size="sm"
                >
                  <div className="flex items-center gap-2">
                    <PlayCircleOutline />
                    {!actionsCompact && 'Studio'}
                  </div>
                </Button>
              </Tooltip>
            )}
          </div>
        </div>
        {!lgPlatformGAEnabled && (
          <div className="w-full">
            <PlatformBetaInfoBanner />
          </div>
        )}
        {createdBeforePricingUpdate &&
          !hasSeenPricingBanner &&
          deploymentFormType === 'cloud' && (
            <div className="w-full">
              <PricingBanner
                closeable={true}
                isCreatingProject={false}
                freeDevDeployments={[]}
                onClose={() => setHasSeenPricingBanner(true)}
              />
            </div>
          )}

        <TabGroup>
          <TabList className="min-h-[40px] overflow-x-auto">
            <TabLabel>Overview</TabLabel>
            <TabLabel>Revisions</TabLabel>
            {hostProject.resource?.url && <TabLabel>Assistants</TabLabel>}
            {hostProject.resource?.url && <TabLabel>Threads</TabLabel>}
          </TabList>
          <TabPanels className="overflow-y-auto">
            <TabPanel>
              <div className="flex flex-col gap-4">
                <HostProjectRevisionsCard
                  hostProject={hostProject}
                  showActiveRevision={true}
                  hideHeader={true}
                />

                <HostProjectTraceCountCard hostProject={hostProject} />

                <HostProjectRunsCard hostProject={hostProject} />
              </div>
            </TabPanel>
            <TabPanel>
              <HostProjectRevisionsCard
                hostProject={hostProject}
                createRevisionEnabled={createRevisionEnabled}
              />
            </TabPanel>
            <TabPanel>
              <AgentsPanel hostProjectId={hostProject.id} />
            </TabPanel>
            <TabPanel>
              <ThreadsPanel hostProjectId={hostProject.id} />
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>
      {projectStatsOpen && (
        <ProjectStats
          hostProject={hostProject}
          onClose={() => setProjectStatsOpen(false)}
        />
      )}
    </div>
  );
};

export default function Page() {
  const { hostProjectId } = useParams();
  const { authorize } = usePermissions();

  const client = useCreateStudioClient({
    projectId: hostProjectId ?? undefined,
    standalone: false,
  });

  return (
    <LangGraphContext.Provider value={client}>
      <div className="flex h-[100vh] flex-col">
        <div className="px-4 py-3">
          <Breadcrumbs />
        </div>
        <HostProjectPage
          hostProjectId={hostProjectId}
          createRevisionEnabled={authorize('deployments:update')}
        />
      </div>
    </LangGraphContext.Provider>
  );
}
