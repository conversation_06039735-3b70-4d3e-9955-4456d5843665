import { Thread } from '@langchain/langgraph-sdk';
import { XCircleIcon, XIcon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import { useState } from 'react';

import { DataGrid, DataGridPagination } from '@/components/DataGrid';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import { xCount } from '@/utils/stringUtils';

import { useThreadsTableState } from '../../hooks/useThreadsTableState';
import { SelectedThreadPane } from './SelectedThreadPane';
import { ThreadsFilterBar } from './ThreadsFilterBar';

export const ThreadsPanel = ({ hostProjectId }: { hostProjectId: string }) => {
  const {
    table,
    threads,
    threadIdToThreadMap,
    isLoading,
    isValidating,
    selectedStatusFilter,
    setSelectedStatusFilter,
    paginationModel,
    selectedThreadId,
    setSelectedThreadId,
    deletingThreadId,
    setDeletingThreadId,
    stoppingThreadId,
    setStoppingThreadId,
    tableSelectedThreadIds,
    setTableSelectedThreadIds,
    deleteThread,
    cancelRunningRunsForThreads,
    isCancellingRunningRuns,
  } = useThreadsTableState({
    hostProjectId,
  });

  return (
    <>
      <div className="w-full overflow-hidden">
        <div className="flex flex-col gap-3 overflow-y-auto">
          <div className="flex items-center gap-2">
            <ThreadsFilterBar
              selectedStatusFilter={selectedStatusFilter}
              setSelectedStatusFilter={setSelectedStatusFilter}
            />
          </div>
          <div className="rounded-lg border border-secondary">
            <DataGrid
              onClick={(row) => setSelectedThreadId(row.thread_id)}
              table={table}
              isLoading={isLoading || isValidating}
              stickyRightColumn
              emptyState={<div>No threads found</div>}
              highlightedId={selectedThreadId}
              loadingRowCount={paginationModel.pageSize}
              cellHeight={48}
            />
          </div>
          <DataGridPagination table={table} />
          <ThreadsMultiSelectOptions
            rowSelection={tableSelectedThreadIds}
            setRowSelection={setTableSelectedThreadIds}
            threadIdToThreadMap={threadIdToThreadMap}
            deleteThread={deleteThread}
            cancelRunningRunsForThreads={cancelRunningRunsForThreads}
          />
        </div>
      </div>
      {selectedThreadId && (
        <SelectedThreadPane
          threads={threads}
          threadId={selectedThreadId}
          hostProjectId={hostProjectId}
          setSelectedThreadId={setSelectedThreadId}
          setDeletingThreadId={setDeletingThreadId}
          setStoppingThreadId={setStoppingThreadId}
          onClose={() => setSelectedThreadId(undefined)}
          isCancellingRunningRuns={isCancellingRunningRuns}
        />
      )}
      {deletingThreadId && (
        <DeleteConfirmationModal
          isOpen={true}
          title={`Delete Thread: ${deletingThreadId}`}
          description={`Are you sure you want to delete this thread? This action cannot be undone.`}
          onClose={() => setDeletingThreadId(undefined)}
          onConfirm={() => {
            deleteThread({ threadId: deletingThreadId });
            setDeletingThreadId(undefined);
            setSelectedThreadId(undefined);
          }}
        />
      )}
      {stoppingThreadId && (
        <DeleteConfirmationModal
          isOpen={true}
          title={`Stop Thread: ${stoppingThreadId}`}
          description={`Are you sure you want to stop this thread? This will stop all active runs for this thread.`}
          onClose={() => setStoppingThreadId(undefined)}
          onConfirm={() => {
            cancelRunningRunsForThreads({ threadIds: [stoppingThreadId] });
            setStoppingThreadId(undefined);
          }}
          confirmText="Stop"
          icon={<XCircleIcon className="h-6 w-6 text-warning" />}
          intent="warning"
        />
      )}
    </>
  );
};

const ThreadsMultiSelectOptions = ({
  threadIdToThreadMap,
  rowSelection,
  setRowSelection,
  deleteThread,
  cancelRunningRunsForThreads,
}: {
  threadIdToThreadMap: Record<string, Thread> | undefined;
  rowSelection: Record<string, boolean>;
  setRowSelection: (rowSelection: Record<string, boolean>) => void;
  deleteThread: ({ threadId }: { threadId: string }) => void;
  cancelRunningRunsForThreads: ({ threadIds }: { threadIds: string[] }) => void;
}) => {
  const busyThreadIds = Object.keys(rowSelection).filter(
    (threadId) => threadIdToThreadMap?.[threadId]?.status === 'busy'
  );
  const [deleteBulkModalOpen, setDeleteBulkModalOpen] = useState(false);
  const [stopBulkModalOpen, setStopBulkModalOpen] = useState(false);
  return (
    <>
      {Object.keys(rowSelection).length > 0 && (
        <div className="fixed bottom-3 left-1/2 -translate-x-1/2">
          <div className="flex items-center gap-4 rounded-md border border-secondary bg-popover p-2 pl-4 pr-2 shadow-md">
            <span className="text-md">
              {xCount('thread', Object.keys(rowSelection).length)} selected
            </span>

            <div className="flex items-center gap-2">
              {busyThreadIds.length > 0 && (
                <button
                  type="button"
                  onClick={() => {
                    setStopBulkModalOpen(true);
                  }}
                  className="flex items-center gap-2 rounded-md bg-tertiary px-3 py-2 hover:bg-quaternary"
                >
                  <XCircleIcon className="h-5 w-5" />
                  <span className="text-sm font-medium">
                    Stop ({busyThreadIds.length})
                  </span>
                </button>
              )}
              <div>
                <Button
                  size="md"
                  color="danger"
                  onClick={() => {
                    setDeleteBulkModalOpen(true);
                  }}
                >
                  Delete
                </Button>
              </div>
              <Button
                size="sm"
                variant="plain"
                color="neutral"
                onClick={() => setRowSelection({})}
              >
                <XIcon className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      )}
      {deleteBulkModalOpen && (
        <DeleteConfirmationModal
          isOpen={deleteBulkModalOpen}
          title={`Delete ${xCount('thread', Object.keys(rowSelection).length)}`}
          description={`Are you sure you want to delete these threads? This action cannot be undone.`}
          onClose={() => setDeleteBulkModalOpen(false)}
          onConfirm={() => {
            // todo: enable bulk delete in api
            Object.keys(rowSelection).forEach((threadId) => {
              deleteThread({ threadId });
            });
            setRowSelection({});
          }}
        />
      )}
      {stopBulkModalOpen && (
        <DeleteConfirmationModal
          isOpen={stopBulkModalOpen}
          title={`Stop ${xCount('thread', busyThreadIds.length)}`}
          description={`Are you sure you want to stop these threads? This will stop all active runs for these threads.`}
          onClose={() => setStopBulkModalOpen(false)}
          onConfirm={() => {
            cancelRunningRunsForThreads({ threadIds: busyThreadIds });
            setRowSelection({});
          }}
          confirmText="Stop"
          icon={<XCircleIcon className="h-6 w-6 text-warning" />}
          intent="warning"
        />
      )}
    </>
  );
};
