import { ThreadStatus } from '@langchain/langgraph-sdk';
import { Trash02Icon, XCircleIcon } from '@langchain/untitled-ui-icons';
import { PlayCircleOutline } from '@mui/icons-material';
import { Button, CircularProgress, LinearProgress } from '@mui/joy';

import { useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import { Thread } from '@/Pages/Graph/src/api';
import {
  useGraphThread,
  useGraphThreadState,
} from '@/Pages/Graph/src/api/useGraphSwr';
import { ForeignDataTreeView } from '@/Pages/Graph/src/components/debug/common/foreign-data-tree';
import {
  RenderModeContext,
  useRenderModeCtx,
} from '@/Pages/Graph/src/control/state';
import {
  foreignDataToTree,
  parseForeignData,
} from '@/Pages/Graph/src/data/foreign-data';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { SplitViewPane } from '@/components/SplitViewPane';
import { useHostProject, useOrganizationId } from '@/hooks/useSwr';
import {
  appGraphIndexPath,
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
  appThreadPath,
} from '@/utils/constants';
import { pluralize } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import { THREAD_STATUS_MAP } from '../../constants';

export const SelectedThreadPane = ({
  threadId,
  hostProjectId,
  threads,
  onClose,
  setSelectedThreadId,
  setDeletingThreadId,
  setStoppingThreadId,
  isCancellingRunningRuns,
}: {
  threads: Thread[] | undefined;
  threadId: string;
  hostProjectId: string;
  onClose: () => void;
  setSelectedThreadId: (threadId: string) => void;
  setDeletingThreadId: (threadId: string) => void;
  setStoppingThreadId: (threadId: string) => void;
  isCancellingRunningRuns: boolean;
}) => {
  const navigate = useNavigate();
  const organizationId = useOrganizationId();
  const { data: thread, isLoading, error } = useGraphThread(threadId);

  const indexOfThread = threads?.findIndex((t) => t.thread_id === threadId);
  const nextThreadId =
    indexOfThread !== undefined && threads?.[indexOfThread + 1]?.thread_id;
  const previousThreadId =
    indexOfThread !== undefined && threads?.[indexOfThread - 1]?.thread_id;

  const { data: hostProject } = useHostProject(hostProjectId);
  const { tracer_session_id: tracingProjectId } = hostProject ?? {};

  return (
    <SplitViewPane
      open={true}
      onClose={onClose}
      title={<span className="text-sm font-semibold">Thread</span>}
      sidePaneId="threads-selected-thread-pane"
      className="relative"
      // todo: if we're at the end of the list we need to fetch the next page
      onNext={() => {
        if (nextThreadId) {
          setSelectedThreadId(nextThreadId);
        }
      }}
      onPrevious={() => {
        if (previousThreadId) {
          setSelectedThreadId(previousThreadId);
        }
      }}
      {...(tracingProjectId && {
        onExpand: () => {
          navigate(
            `/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${tracingProjectId}/${appThreadPath}/${threadId}`
          );
        },
      })}
    >
      <ThreadPaneBody
        thread={thread}
        isLoading={isLoading}
        error={error}
        hostProjectId={hostProjectId}
        setDeletingThreadId={setDeletingThreadId}
        setStoppingThreadId={setStoppingThreadId}
        isCancellingRunningRuns={isCancellingRunningRuns}
      />
    </SplitViewPane>
  );
};

const ThreadPaneBody = ({
  hostProjectId,
  thread,
  isLoading,
  error,
  setDeletingThreadId,
  setStoppingThreadId,
  isCancellingRunningRuns,
}: {
  hostProjectId: string;
  thread: Thread | undefined | null;
  isLoading: boolean;
  error: Error | undefined;
  setDeletingThreadId: (threadId: string) => void;
  setStoppingThreadId: (threadId: string) => void;
  isCancellingRunningRuns: boolean;
}) => {
  if (isLoading) {
    return <LinearProgress />;
  }
  if (error) {
    return <ExpandableErrorAlert error={error} />;
  }
  if (!thread) {
    return <div>No thread found</div>;
  }
  return (
    <div className="flex flex-col gap-9 p-8">
      <TopBar
        thread={thread}
        hostProjectId={hostProjectId}
        setDeletingThreadId={setDeletingThreadId}
        setStoppingThreadId={setStoppingThreadId}
        isCancellingRunningRuns={isCancellingRunningRuns}
      />
      <ThreadStateValues threadId={thread.thread_id} />
    </div>
  );
};

const ThreadStateValues = ({ threadId }: { threadId: string }) => {
  const { data: threadState, isLoading, error } = useGraphThreadState(threadId);
  const threadErrors = useMemo(() => {
    return threadState?.tasks.filter((t) => !!t.error).map((t) => t.error);
  }, [threadState]);
  const threadStateValues = useMemo(() => {
    return Object.keys(threadState?.values ?? {}).length > 0
      ? foreignDataToTree(parseForeignData(threadState?.values))
      : null;
  }, [threadState]);
  const renderModeState = useRenderModeCtx({ renderMode: 'pretty' });

  if (isLoading) {
    return <LinearProgress />;
  }
  if (error) {
    return (
      <ExpandableErrorAlert error={`Error fetching thread state: ${error}`} />
    );
  }

  return (
    <div className="flex flex-col gap-3 overflow-y-auto">
      {!!threadErrors?.length && (
        <div className="flex flex-col gap-3 overflow-y-auto">
          <span className="text-md font-medium">
            {`${pluralize('Error', threadErrors.length)} (${
              threadErrors.length
            }):`}
          </span>
          {threadErrors.map((e) => (
            <ExpandableErrorAlert error={e} />
          ))}
        </div>
      )}
      {threadStateValues ? (
        <RenderModeContext.Provider value={renderModeState}>
          <span className="text-md font-medium">State</span>
          <ForeignDataTreeView data={threadStateValues} />
        </RenderModeContext.Provider>
      ) : (
        <div className="flex flex-col gap-2">
          <span className="text-sm text-secondary">No values for thread.</span>
        </div>
      )}
    </div>
  );
};

const TopBar = ({
  thread,
  hostProjectId,
  setDeletingThreadId,
  setStoppingThreadId,
  isCancellingRunningRuns,
}: {
  thread: Thread;
  hostProjectId: string;
  setDeletingThreadId: (threadId: string) => void;
  setStoppingThreadId: (threadId: string) => void;
  isCancellingRunningRuns: boolean;
}) => {
  const organizationId = useOrganizationId();
  const isRunning = thread.status === 'busy';
  return (
    <div className="flex items-start gap-2">
      <div className="flex flex-col gap-4">
        <div className="text-md flex items-center gap-2 pb-2 font-medium">
          <span>Thread:</span>
          <span>{thread?.thread_id}</span>
          <StatusBadge status={thread.status} />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-quaternary">CREATED</span>
          <span className="text-sm font-medium">
            {new Date(thread?.created_at).toLocaleString()}
          </span>
          <span className="pl-6 text-sm font-medium text-quaternary">
            UPDATED
          </span>
          <span className="text-sm font-medium">
            {new Date(thread?.updated_at).toLocaleString()}
          </span>
        </div>
      </div>
      <div className="ml-auto flex items-center gap-2">
        {isRunning ? (
          isCancellingRunningRuns ? (
            <CircularProgress size="sm" />
          ) : (
            <button
              type="button"
              onClick={() => setStoppingThreadId(thread.thread_id)}
              className="rounded-md bg-tertiary px-3 py-2 hover:bg-quaternary"
            >
              <div className="flex items-center gap-2">
                <XCircleIcon className="size-4 text-tertiary" />
                <span className="text-xs font-semibold text-tertiary">
                  Stop
                </span>
              </div>
            </button>
          )
        ) : null}
        <Button
          component={Link}
          to={`/${appGraphIndexPath}/thread?organizationId=${organizationId}&hostProjectId=${encodeURIComponent(
            hostProjectId
          )}&threadId=${thread.thread_id}`}
          startDecorator={<PlayCircleOutline />}
          size="sm"
        >
          Studio
        </Button>
        <Button
          onClick={() => {
            setDeletingThreadId(thread.thread_id);
          }}
          size="sm"
          variant="plain"
          color="danger"
        >
          <Trash02Icon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const StatusBadge = ({ status }: { status: ThreadStatus }) => {
  const { icon: Icon, bgColor, textColor, label } = THREAD_STATUS_MAP[status];

  return (
    <div
      className={cn(
        `flex items-center gap-1 rounded-md px-1.5 py-0.5`,
        bgColor,
        textColor
      )}
    >
      <Icon className="size-4" />
      <span className="text-xxs font-medium">{label}</span>
    </div>
  );
};
