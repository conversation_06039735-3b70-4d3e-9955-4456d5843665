import { XIcon } from '@langchain/untitled-ui-icons';

import { Link } from 'react-router-dom';

import { CopyInlineLink } from '@/components/CopyInlineLink';
import { useHostRevisions, useOrganizationId } from '@/hooks/useSwr.tsx';
import AWSIcon from '@/icons/AWSIcon.svg?react';
import DockerIcon from '@/icons/DockerIcon.svg?react';
import GithubIcon from '@/icons/GithubIcon.svg?react';
import LinkIcon from '@/icons/LinkIcon.svg?react';
import ProjectIcon from '@/icons/ProjectIcon';
import { HostProjectImageSource, HostProjectSchema } from '@/types/schema.ts';
import {
  appOrganizationPath,
  appProjectsPath,
  appSessionPath,
} from '@/utils/constants.tsx';

import { DEPLOYMENT_TYPES_TO_DISPLAY_NAME } from '../constants';

function getRepoUrlLabel(repoUrl: string) {
  try {
    const url = new URL(repoUrl);

    if (url.hostname === 'github.com') {
      let pathname = url.pathname;
      if (pathname.startsWith('/')) pathname = pathname.slice(1);

      return pathname.split('/').slice(0, 2).join('/');
    }
  } catch {
    // pass
  }
  return repoUrl;
}

export const ProjectStats = ({
  hostProject,
  onClose,
}: {
  hostProject: HostProjectSchema;
  onClose: () => void;
}) => {
  const organizationId = useOrganizationId();

  const {
    id,
    metadata,
    repo_url,
    resource,
    lc_hosted,
    updated_at,
    name,
    tracer_session_id,
  } = hostProject;

  // retrieve latest revision
  const { data: rows, isLoading } = useHostRevisions(id, {
    limit: 1,
    offset: 0,
  });
  const revisionPending = !rows && isLoading;
  const latestRevision = rows?.[0];

  const imageSource: HostProjectImageSource = metadata.image_source || 'github';

  return (
    <div className="flex w-[300px] shrink-0 flex-col items-start gap-6 overflow-x-hidden border-l border-secondary px-4 py-3">
      <div className="flex w-full items-center justify-between">
        <span className="text-md font-medium">Details</span>
        <button
          type="button"
          onClick={onClose}
          className="rounded-md p-1 hover:bg-primary-hover"
        >
          <XIcon className="size-4" />
        </button>
      </div>
      <div className="flex flex-col items-start gap-1.5">
        <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
          Tracing Project
        </div>

        <Link
          to={`/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${tracer_session_id}`}
          className="inline-flex hover:underline"
        >
          <span className="inline-flex items-center gap-0.5 rounded-sm bg-primary p-1 py-0.5 pr-2 text-sm">
            <ProjectIcon className="mr-1 h-4 w-4 shrink-0" />
            {name}
          </span>
        </Link>
      </div>
      {/* Revision deployed from Docker Image */}
      {['internal_docker', 'external_docker'].includes(imageSource) &&
        !revisionPending && (
          <div className="flex flex-col items-start gap-1.5">
            <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
              Image
            </div>

            <span className="flex w-full items-center gap-1.5 overflow-hidden text-ellipsis font-medium">
              <DockerIcon className="h-4 w-4 shrink-0" />
              {latestRevision
                ? latestRevision?.image_path?.split('/').pop()
                : 'No Image Deployed'}
            </span>
          </div>
        )}
      {/* Revision deployed from GitHub */}
      {imageSource === 'github' && (
        <div className="flex flex-col items-start gap-1.5">
          <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
            Repository
          </div>

          <a
            href={repo_url}
            target="_blank"
            rel="noreferrer noopener"
            className="flex items-center gap-1.5 font-medium"
          >
            <GithubIcon className="h-4 w-4 shrink-0" />
            {getRepoUrlLabel(repo_url ?? '')}
          </a>
        </div>
      )}
      {resource?.url && (
        <div className="flex w-full flex-col items-start gap-1.5 overflow-x-hidden">
          <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
            API URL
          </div>
          <CopyInlineLink
            value={resource.url}
            className="text-md w-full"
            prefix="URL"
          >
            <span className="text-md w-full overflow-hidden text-ellipsis">
              {resource.url}
            </span>
          </CopyInlineLink>
        </div>
      )}
      {resource?.url && (
        <div className="flex flex-col items-start gap-1.5">
          <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
            API Docs
          </div>

          <a
            href={`${resource.url}/docs`}
            target="_blank"
            rel="noreferrer noopener"
            className="flex items-center gap-1.5 font-medium"
          >
            <LinkIcon className="h-3 w-3" />
            /docs
          </a>
        </div>
      )}
      {!lc_hosted && 'aws_external_id' in metadata && (
        <div className="flex flex-col items-center gap-1.5">
          <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
            Platform
          </div>

          <span className="flex flex-col items-center">
            <AWSIcon />
          </span>
        </div>
      )}
      {!lc_hosted && 'region' in metadata && (
        <div className="flex flex-col items-start gap-1.5">
          <div className="whitespace-nowrnpap text-xs font-medium uppercase tracking-wide text-quaternary">
            Region
          </div>

          <span className="flex items-center gap-1.5 font-medium">
            {metadata.region}
          </span>
        </div>
      )}
      {!lc_hosted && 'aws_account_id' in metadata && (
        <div className="flex flex-col items-start gap-1.5">
          <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
            Account ID
          </div>

          <span className="flex items-center gap-1.5 font-medium">
            {metadata.aws_account_id}
          </span>
        </div>
      )}
      <div className="flex flex-col items-start gap-1.5">
        <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
          Last Updated
        </div>
        <div className="font-medium">
          {new Date(updated_at).toLocaleString()}
        </div>
      </div>
      <div className="flex flex-col items-start gap-1.5">
        <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
          Deployment Type
        </div>
        <div className="font-medium">
          {DEPLOYMENT_TYPES_TO_DISPLAY_NAME[metadata.deployment_type]}
        </div>
      </div>
      <div className="flex flex-col items-start gap-1.5">
        <div className="whitespace-nowrap text-xs font-medium uppercase tracking-wide text-quaternary">
          Shareable
        </div>
        <div className="font-medium">
          {metadata.shareable ? (
            <CopyInlineLink
              value={
                new URL('/studio/thread', window.location.origin).toString() +
                `?baseUrl=${encodeURIComponent(resource?.url || '')}`
              }
              className="text-md"
              prefix="URL"
            >
              Yes
            </CopyInlineLink>
          ) : (
            'No'
          )}
        </div>
      </div>
    </div>
  );
};
