import { Config } from '@langchain/langgraph-sdk';
import { ChevronDownIcon } from '@langchain/untitled-ui-icons';
import { Button, Input, Option, Select, Textarea, Tooltip } from '@mui/joy';

import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import {
  useGraphAssistant,
  useGraphAssistants,
  useGraphAssistantsCreate,
} from '@/Pages/Graph/src/api/assistants';
import {
  useGraphAssistantDebugGraph,
  useGraphAssistantsDebugSchema,
} from '@/Pages/Graph/src/api/useGraphSwr';
import { SchemaEditor } from '@/Pages/Graph/src/components/debug/common/schema-editor';
import { templateSchemaRenderer } from '@/Pages/Graph/src/components/debug/common/template-renderer';
import { checkIsSystemAssistant } from '@/Pages/Graph/src/components/site/Assistants/utils';
import { getDefaultConfigSchema } from '@/Pages/Graph/src/hooks/assistants/useAssistantDefaultConfigSchema';
import { useAgentCreateUpdateError } from '@/Pages/HostAgents/hooks/useAgentCreateUpdateError';
import { Pane } from '@/components/Pane';
import { useGetGraphStudioPath } from '@/utils/useGetGraphStudioPath';

import {
  EDIT_AGENT_DESCRIPTION_DESCRIPTION,
  EDIT_AGENT_DESCRIPTION_TITLE,
  EDIT_AGENT_NAME_DESCRIPTION,
  EDIT_AGENT_NAME_TITLE,
  GRAPH_PREVIEW_DESCRIPTION,
  GRAPH_PREVIEW_TITLE,
  GRAPH_SELECTOR_DESCRIPTION,
  GRAPH_SELECTOR_TITLE,
} from '../../constants';
import { GraphPreview } from './GraphPreview';

export const CreateAgentPane = ({
  isOpen,
  hostProjectId,
  setShowCreateAgent,
}: {
  hostProjectId: string;
  isOpen: boolean;
  setShowCreateAgent: (show: boolean) => void;
}) => {
  const navigate = useNavigate();
  const { generatePath } = useGetGraphStudioPath();
  const [selectedGraphAssistantId, setSelectedGraphAssistantId] = useState<
    string | undefined
  >();
  const {
    data: graph,
    isLoading: graphLoading,
    error: graphError,
  } = useGraphAssistantDebugGraph(selectedGraphAssistantId ?? undefined);

  const { data: selectedGraphAssistant } = useGraphAssistant(
    selectedGraphAssistantId
  );

  const [agentName, setAgentName] = useState<string | undefined>();
  const trimmedAgentName = agentName?.trim();
  const [agentDescription, setAgentDescription] = useState<
    string | undefined
  >();
  const trimmedAgentDescription = agentDescription?.trim();
  const [agentConfigurable, setAgentConfigurable] = useState<
    Config['configurable'] | undefined
  >();

  const resetState = () => {
    setAgentName(undefined);
    setAgentDescription(undefined);
    setAgentConfigurable(undefined);
  };

  // reset the agent values when the pane is closed.
  useEffect(() => {
    resetState();
  }, [isOpen]);

  const { data: schemas } = useGraphAssistantsDebugSchema(
    selectedGraphAssistantId ? [selectedGraphAssistantId] : null
  );
  const schema = schemas?.[0];
  const { config: configSchema, configRoot: configRootSchema } = schema ?? {};

  const defaultConfigSchema = getDefaultConfigSchema(configSchema);

  useEffect(() => {
    if (Object.keys(agentConfigurable ?? {}).length === 0) {
      setAgentConfigurable(defaultConfigSchema);
    }
  }, [defaultConfigSchema, agentConfigurable, setAgentConfigurable]);

  const { trigger: createAssistant, isMutating: isCreatingAssistant } =
    useGraphAssistantsCreate(
      selectedGraphAssistant?.graph_id
        ? {
            graphId: selectedGraphAssistant.graph_id,
          }
        : null
    );
  const { agentNameError } = useAgentCreateUpdateError({
    agentName: trimmedAgentName,
    agentDescription: trimmedAgentDescription,
    isSaving: isCreatingAssistant,
  });

  const graphView = useMemo(() => {
    if (!graph || !selectedGraphAssistantId)
      return (
        <div className="flex h-full flex-col items-center justify-center gap-2">
          <span className="text-sm text-tertiary">
            Select a graph to preview its structure
          </span>
        </div>
      );
    return (
      <GraphPreview
        key={selectedGraphAssistantId}
        className="h-full w-full"
        graphData={graph}
        isLoading={graphLoading}
        error={graphError}
      />
    );
  }, [graph, graphLoading, graphError, selectedGraphAssistantId]);

  const renderAgentForm = () => {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-1">
          <span className="text-md font-medium text-primary">
            {EDIT_AGENT_NAME_TITLE}
          </span>
          <span className="text-sm text-secondary">
            {EDIT_AGENT_NAME_DESCRIPTION}
          </span>
          <Input
            placeholder="Enter assistant name"
            value={agentName}
            onChange={(e) => setAgentName(e.target.value)}
            autoFocus
            error={!!agentNameError}
          />
          <span className="text-sm text-error">{agentNameError}</span>
        </div>
        <div className="flex flex-col gap-1">
          <span className="text-md font-medium text-primary">
            {EDIT_AGENT_DESCRIPTION_TITLE}
          </span>
          <span className="text-sm text-secondary">
            {EDIT_AGENT_DESCRIPTION_DESCRIPTION}
          </span>
          <Textarea
            placeholder="Enter assistant description"
            value={agentDescription}
            onChange={(e) => setAgentDescription(e.target.value)}
            minRows={3}
          />
        </div>
        {agentConfigurable && !!schema && (
          <SchemaEditor
            input={agentConfigurable}
            onChange={(v) => setAgentConfigurable(v)}
            schema={configSchema}
            rootSchema={configRootSchema}
            renderer={templateSchemaRenderer}
            options={{ showNode: true }}
          />
        )}
      </div>
    );
  };

  const submitButtonDisabledMessage = (() => {
    if (!selectedGraphAssistantId) return 'Select a graph for your agent.';
    if (!trimmedAgentName) return 'Enter a name for your agent.';
    if (!trimmedAgentDescription) return 'Enter a description for your agent.';
    return null;
  })();

  return (
    <Pane
      title="Create Assistant"
      open={isOpen}
      onClose={() => setShowCreateAgent(false)}
      className="h-full w-full overflow-y-auto p-0"
      topBarRightElement={
        <div className="flex items-center gap-2 px-4">
          <Button
            variant="outlined"
            color="neutral"
            onClick={() => setShowCreateAgent(false)}
            size="sm"
          >
            Cancel
          </Button>
          <div>
            <Tooltip title={submitButtonDisabledMessage}>
              <div>
                <Button
                  size="sm"
                  onClick={async () => {
                    if (!trimmedAgentName) return;
                    const assistant = await createAssistant({
                      name: trimmedAgentName,
                      config: { configurable: agentConfigurable },
                      description: trimmedAgentDescription,
                    });
                    if (!assistant) return;
                    resetState();
                    navigate(
                      generatePath({
                        hostProjectId: hostProjectId,
                        assistantId: assistant.assistant_id,
                      })
                    );
                  }}
                  disabled={!!submitButtonDisabledMessage}
                  loading={isCreatingAssistant}
                >
                  Create assistant
                </Button>
              </div>
            </Tooltip>
          </div>
        </div>
      }
    >
      <div className="grid h-full grid-cols-2 gap-4 p-6">
        <div className="flex h-full gap-5 pr-6">
          <div className="flex grow flex-col gap-6">
            <div className="flex flex-col gap-1">
              <span className="text-md font-medium text-primary">
                Personalize your Assistant
              </span>
              <span className="text-sm text-secondary">
                Define your assistant's configuration to fit your needs.
              </span>
            </div>
            <GraphSelector
              selectedGraphAssistantId={selectedGraphAssistantId}
              setSelectedGraphAssistantId={setSelectedGraphAssistantId}
            />
            {renderAgentForm()}
          </div>
        </div>
        <div className="flex h-full flex-col gap-1 pl-6">
          <span className="text-md font-medium text-primary">
            {GRAPH_PREVIEW_TITLE}
          </span>
          <span className="text-sm text-tertiary">
            {GRAPH_PREVIEW_DESCRIPTION}
          </span>
          <div
            className="relative z-0 mt-3 rounded-md border border-secondary bg-primary p-4"
            style={{
              width: 'min(100%, calc(100vh - 200px))',
              height: 'min(100%, calc(100vh - 200px))',
            }}
          >
            <div className="h-full w-full bg-secondary">{graphView}</div>
          </div>
        </div>
      </div>
    </Pane>
  );
};

const GraphSelector = ({
  selectedGraphAssistantId,
  setSelectedGraphAssistantId,
}: {
  selectedGraphAssistantId: string | undefined;
  setSelectedGraphAssistantId: (id: string | undefined) => void;
}) => {
  const { data: assistants } = useGraphAssistants();
  const systemAssistants = useMemo(() => {
    return assistants?.filter(checkIsSystemAssistant);
  }, [assistants]);

  useEffect(() => {
    if (!selectedGraphAssistantId && systemAssistants?.length) {
      setSelectedGraphAssistantId(systemAssistants[0].assistant_id);
    }
  }, [selectedGraphAssistantId, systemAssistants, setSelectedGraphAssistantId]);

  return (
    <div className="flex flex-col gap-1">
      <span className="text-md font-medium text-primary">
        {GRAPH_SELECTOR_TITLE}
      </span>
      <span className="text-sm text-tertiary">
        {GRAPH_SELECTOR_DESCRIPTION}
      </span>
      <Select
        value={selectedGraphAssistantId}
        onChange={(_, value) => setSelectedGraphAssistantId(value ?? undefined)}
        indicator={<ChevronDownIcon className="h-4 w-4" />}
        sx={{
          mt: 2,
        }}
        placeholder="Select a graph..."
        renderValue={(value) => {
          const assistant = assistants?.find(
            (a) => a.assistant_id === value?.value
          );
          return assistant?.name ?? assistant?.graph_id;
        }}
      >
        {systemAssistants?.map((assistant) => (
          <Option key={assistant.assistant_id} value={assistant.assistant_id}>
            <div className="flex flex-col gap-1">
              {assistant.name ?? assistant.graph_id}
            </div>
          </Option>
        ))}
      </Select>
    </div>
  );
};
