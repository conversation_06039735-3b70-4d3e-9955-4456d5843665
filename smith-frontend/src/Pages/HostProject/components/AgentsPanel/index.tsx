import { Assistant } from '@langchain/langgraph-sdk';
import {
  Dataflow03Icon,
  DotsVerticalIcon,
  Pencil01Icon,
  PlayCircleIcon,
  PlusIcon,
  Trash02Icon,
} from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';
import {
  ColumnDef,
  ColumnSizingState,
  OnChangeFn,
  PaginationState,
  SortingState,
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import {
  AssistantSortBy,
  useGraphAssistantDelete,
  useGraphAssistantsBypassSdk,
} from '@/Pages/Graph/src/api';
import { checkIsSystemAssistant } from '@/Pages/Graph/src/components/site/Assistants/utils';
import { DataGrid, DataGridPagination } from '@/components/DataGrid';
import { useDataGridSizingLocalStorage } from '@/components/DataGrid.utils';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import EmptyState from '@/components/EmptyState';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';
import { Tooltip } from '@/components/Tooltip/Tooltip';
import { useDataGridState } from '@/hooks/useDataGridState';
import { useSortingState } from '@/hooks/useSortingState';
import { useOrganizationId } from '@/hooks/useSwr';

import { CreateAgentPane } from './CreateAgentPane';
import { EditAgentPane } from './EditAgentPane';

const columnHelper = createColumnHelper<Assistant>();

const COLUMNS_DEF: ColumnDef<Assistant, any>[] = [
  columnHelper.accessor('name', {
    id: 'name',
    header: () => <span className="pl-4 text-sm text-tertiary">Name</span>,
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300} className="pl-4 font-medium">
        {checkIsSystemAssistant(row.original)
          ? `Default (${row.original.name})`
          : row.original.name}
      </TextOverflowTooltip>
    ),
  }),

  columnHelper.accessor('description', {
    id: 'description',
    header: 'Description',
    enableSorting: false,
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {row.original.description}
      </TextOverflowTooltip>
    ),
  }),
  columnHelper.accessor('assistant_id', {
    id: 'assistant_id',
    header: 'Assistant ID',
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {row.original.assistant_id}
      </TextOverflowTooltip>
    ),
  }),
  columnHelper.accessor('graph_id', {
    id: 'graph_id',
    header: 'Graph',
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {row.original.graph_id}
      </TextOverflowTooltip>
    ),
  }),

  columnHelper.accessor('updated_at', {
    id: 'updated_at',
    header: 'Updated At',
    size: 150,
    minSize: 100,
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {new Date(row.original.updated_at).toLocaleString()}
      </TextOverflowTooltip>
    ),
  }),
  columnHelper.accessor('created_at', {
    id: 'created_at',
    header: 'Created At',
    size: 150,
    minSize: 100,
    cell: ({ row }) => (
      <TextOverflowTooltip tooltipMaxWidth={300}>
        {new Date(row.original.created_at).toLocaleString()}
      </TextOverflowTooltip>
    ),
  }),
  columnHelper.display({
    id: 'actions',
    size: 50,
    cell: ({ row, table }) => {
      const { actions } = (table.options.meta ?? {}) as {
        actions: {
          handleEdit: (assistant: Assistant) => void;
          handleDelete: (assistant: Assistant) => void;
        };
      };
      const isSystemAssistant = checkIsSystemAssistant(row.original);
      return (
        <DropdownMenu>
          <Tooltip
            title={
              isSystemAssistant ? 'Default assistants cannot be modifed' : ''
            }
          >
            <DropdownMenuTrigger
              onClick={(e) => e.stopPropagation()}
              disabled={isSystemAssistant}
              className="flex h-6 w-6 items-center justify-center rounded-md hover:bg-tertiary disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-transparent"
            >
              <DotsVerticalIcon className="h-5 w-5 text-tertiary" />
            </DropdownMenuTrigger>
          </Tooltip>
          <DropdownMenuContent
            className="z-10"
            onClick={(e) => e.stopPropagation()}
          >
            <DropdownMenuItem
              onClick={() => actions.handleEdit(row.original)}
              className="flex items-center gap-2"
            >
              <Pencil01Icon className="h-5 w-5" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => actions.handleDelete(row.original)}
              className="flex items-center gap-2"
            >
              <Trash02Icon className="h-5 w-5" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  }),
];

export const AgentsPanel = ({ hostProjectId }: { hostProjectId: string }) => {
  const [showCreateAgent, setShowCreateAgent] = useState(false);
  const [agentToEdit, setAgentToEdit] = useState<Assistant | undefined>();
  const [agentToDelete, setAgentToDelete] = useState<Assistant | undefined>();

  const { paginationModel, setPaginationModel } = useDataGridState({
    defaultPaginationModel: {
      pageIndex: 0,
      pageSize: 10,
    },
  });
  const [columnSizing, setColumnSizing] =
    useDataGridSizingLocalStorage('agents');

  const [sortingState, setSortingState] = useSortingState('updated_at');

  const {
    data: assistantsResponse,
    isLoading,
    error,
    isValidating,
    mutate: mutateAgents,
  } = useGraphAssistantsBypassSdk({
    limit: paginationModel.pageSize,
    offset: paginationModel.pageIndex * paginationModel.pageSize,
    sort_by: sortingState[0].id as AssistantSortBy,
    sort_order: sortingState[0].desc ? 'desc' : 'asc',
  });
  const { assistants, total } = assistantsResponse ?? {};

  const { trigger: deleteAgent, isMutating: isDeletingAgent } =
    useGraphAssistantDelete(agentToDelete?.assistant_id);

  const handleDeleteAgent = async () => {
    await deleteAgent();
    mutateAgents();
    setAgentToDelete(undefined);
    setAgentToEdit(undefined);
  };

  return (
    <>
      <AgentsPanelBody
        hostProjectId={hostProjectId}
        setAgentToEdit={setAgentToEdit}
        setShowCreateAgent={setShowCreateAgent}
        setAgentToDelete={setAgentToDelete}
        assistants={assistants}
        total={total}
        isLoading={isLoading || isValidating}
        error={error}
        paginationModel={paginationModel}
        setPaginationModel={setPaginationModel}
        columnSizing={columnSizing}
        setColumnSizing={setColumnSizing}
        sortingState={sortingState}
        setSortingState={setSortingState}
      />

      <CreateAgentPane
        isOpen={showCreateAgent}
        hostProjectId={hostProjectId}
        setShowCreateAgent={setShowCreateAgent}
      />
      <EditAgentPane
        agent={agentToEdit}
        onClose={() => setAgentToEdit(undefined)}
        setAgentToDelete={setAgentToDelete}
        isDeleteModalOpen={!!agentToDelete}
        isDeletingAgent={isDeletingAgent}
      />
      {agentToDelete && (
        <DeleteConfirmationModal
          isOpen={!!agentToDelete}
          onClose={() => setAgentToDelete(undefined)}
          onConfirm={handleDeleteAgent}
          title={`Delete ${agentToDelete?.name}`}
          description="Are you sure you want to delete this assistant? This action cannot be undone."
        />
      )}
    </>
  );
};

const AgentsPanelBody = ({
  hostProjectId,
  setAgentToEdit,
  setShowCreateAgent,
  setAgentToDelete,
  assistants,
  total,
  isLoading,
  error,
  paginationModel,
  setPaginationModel,
  columnSizing,
  setColumnSizing,
  sortingState,
  setSortingState,
}: {
  hostProjectId: string;
  setAgentToEdit: (agent: Assistant) => void;
  setShowCreateAgent: (show: boolean) => void;
  setAgentToDelete: (agent: Assistant) => void;
  assistants: Assistant[] | undefined;
  total: number | undefined;
  isLoading: boolean;
  error: Error | undefined;
  paginationModel: PaginationState;
  setPaginationModel: OnChangeFn<PaginationState>;
  columnSizing: ColumnSizingState;
  setColumnSizing: OnChangeFn<ColumnSizingState>;
  sortingState: SortingState;
  setSortingState: OnChangeFn<SortingState>;
}) => {
  const organizationId = useOrganizationId();

  const columns = useMemo(() => {
    return [
      ...COLUMNS_DEF.slice(0, -1),
      columnHelper.display({
        id: 'studio_url',
        header: 'Studio',
        cell: ({ row }) => {
          return (
            <Tooltip title="Open assistant in Studio">
              <Button
                component={Link}
                to={`/studio/?assistantId=${row.original.assistant_id}&hostProjectId=${hostProjectId}&organizationId=${organizationId}`}
                variant="plain"
                color="neutral"
                onClick={(e) => e.stopPropagation()}
                sx={{
                  p: 1,
                  py: 0,
                }}
              >
                <PlayCircleIcon className="size-4 text-tertiary" />
              </Button>
            </Tooltip>
          );
        },
      }),
      ...COLUMNS_DEF.slice(-1),
    ];
  }, [hostProjectId, organizationId]);

  const table = useReactTable({
    data: assistants ?? [],
    columns,
    state: {
      pagination: paginationModel,
      columnSizing,
      sorting: sortingState,
    },
    meta: {
      actions: {
        handleDelete: setAgentToDelete,
        handleEdit: setAgentToEdit,
      },
    },
    getRowId: (row) => row.assistant_id,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onPaginationChange: setPaginationModel,
    onSortingChange: setSortingState,
    onColumnSizingChange: setColumnSizing,
    columnResizeMode: 'onChange',
    manualFiltering: true,
    manualPagination: true,
    enableSorting: true,
    enableColumnResizing: true,
    pageCount: Math.ceil((total ?? 0) / paginationModel.pageSize),
  });

  if (error) {
    return (
      <div className="w-full">
        <ExpandableErrorAlert error={error} />
      </div>
    );
  }
  return (
    <div className="w-full overflow-hidden rounded-lg border border-secondary">
      <div className="flex items-center justify-between border-b border-secondary bg-secondary p-5">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold">Assistants</h2>
          <p className="text-sm text-tertiary">
            Assistants associated with this deployment
          </p>
        </div>
        <Button
          onClick={() => setShowCreateAgent(true)}
          variant="outlined"
          color="neutral"
          size="sm"
          startDecorator={<PlusIcon className="size-4" />}
        >
          New assistant
        </Button>
      </div>

      <div className="flex w-full flex-col gap-4 overflow-y-auto">
        <DataGrid
          table={table}
          isLoading={isLoading}
          stickyRightColumn
          emptyState={
            <div className="flex w-full flex-col items-center justify-center py-10">
              <EmptyState
                title="Create your first assistant"
                description="Customize an assistant from a graph in this deployment."
                icon={<Dataflow03Icon />}
              />
            </div>
          }
          loadingRowCount={paginationModel.pageSize}
          cellHeight={48}
        />
      </div>
      <div className="p-4">
        <DataGridPagination table={table} />
      </div>
    </div>
  );
};
