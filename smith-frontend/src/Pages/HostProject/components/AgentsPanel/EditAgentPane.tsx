import { Assistant } from '@langchain/langgraph-sdk';
import { Trash04Icon } from '@langchain/untitled-ui-icons';
import { Button, Input, Textarea, Tooltip } from '@mui/joy';

import { useEffect, useState } from 'react';

import {
  useGraphAssistants,
  useGraphAssistantsUpdate,
} from '@/Pages/Graph/src/api/assistants';
import {
  useGraphAssistantDebugGraph,
  useGraphAssistantsDebugSchema,
} from '@/Pages/Graph/src/api/useGraphSwr';
import { SchemaEditor } from '@/Pages/Graph/src/components/debug/common/schema-editor';
import { templateSchemaRenderer } from '@/Pages/Graph/src/components/debug/common/template-renderer';
import { useAgentCreateUpdateError } from '@/Pages/HostAgents/hooks/useAgentCreateUpdateError';
import { Pane } from '@/components/Pane';

import {
  EDIT_AGENT_DESCRIPTION,
  EDIT_AGENT_DESCRIPTION_DESCRIPTION,
  EDIT_AGENT_DESCRIPTION_TITLE,
  EDIT_AGENT_NAME_DESCRIPTION,
  EDIT_AGENT_NAME_TITLE,
  EDIT_AGENT_TITLE,
  GRAPH_PREVIEW_DESCRIPTION,
  GRAPH_PREVIEW_TITLE,
} from '../../constants';
import { GraphPreview } from './GraphPreview';

export const EditAgentPane = ({
  agent,
  onClose,
  setAgentToDelete,
  isDeleteModalOpen,
  isDeletingAgent,
}: {
  agent: Assistant | undefined;
  onClose: () => void;
  setAgentToDelete: (agent: Assistant | undefined) => void;
  isDeleteModalOpen: boolean;
  isDeletingAgent: boolean;
}) => {
  const {
    name,
    description,
    graph_id: graphId,
    assistant_id: assistantId,
    config,
  } = agent ?? {};

  const [stagedAgentName, setStagedAgentName] = useState(name);
  const [stagedAgentDescription, setStagedAgentDescription] =
    useState(description);
  const [stagedAgentConfigurable, setStagedAgentConfigurable] = useState(
    config?.configurable ?? {}
  );

  // reset the staged agent values when the agent changes (by being selected in the table).
  useEffect(() => {
    setStagedAgentName(name);
    setStagedAgentDescription(description);
    setStagedAgentConfigurable(config?.configurable ?? {});
  }, [name, description, config]);

  const trimmedStagedAgentName = stagedAgentName?.trim();
  const trimmedStagedAgentDescription = stagedAgentDescription?.trim();

  const { data: schemas, isLoading: schemaLoading } =
    useGraphAssistantsDebugSchema(assistantId ? [assistantId] : null);
  const schema = schemas?.[0];
  const {
    data: graph,
    isLoading: graphLoading,
    error: graphError,
  } = useGraphAssistantDebugGraph(assistantId);

  const { config: configSchema, configRoot: configRootSchema } = schema ?? {};

  const renderAgentForm = () => {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-1">
          <span className="text-md font-medium text-primary">
            {EDIT_AGENT_NAME_TITLE}
          </span>
          <span className="text-sm text-secondary">
            {EDIT_AGENT_NAME_DESCRIPTION}
          </span>
          <Input
            placeholder="Enter agent name"
            value={stagedAgentName}
            onChange={(e) => setStagedAgentName(e.target.value)}
            autoFocus
            error={!!agentNameError}
          />
          <span className="text-sm text-error">{agentNameError}</span>
        </div>
        <div className="flex flex-col gap-1">
          <span className="text-md font-medium text-primary">
            {EDIT_AGENT_DESCRIPTION_TITLE}
          </span>
          <span className="text-sm text-secondary">
            {EDIT_AGENT_DESCRIPTION_DESCRIPTION}
          </span>
          <Textarea
            placeholder="Enter agent description"
            value={stagedAgentDescription}
            onChange={(e) => setStagedAgentDescription(e.target.value)}
            minRows={3}
          />
        </div>
        {stagedAgentConfigurable && !schemaLoading && (
          <SchemaEditor
            input={stagedAgentConfigurable}
            onChange={(v) => setStagedAgentConfigurable(v)}
            schema={configSchema}
            rootSchema={configRootSchema}
            renderer={templateSchemaRenderer}
            options={{ showNode: true }}
          />
        )}
      </div>
    );
  };

  const { trigger: updateAgent, isMutating: isUpdatingAgent } =
    useGraphAssistantsUpdate(
      agent && graphId ? { assistantId: agent.assistant_id, graphId } : null
    );
  const { mutate: mutateAgents } = useGraphAssistants({});

  const handleSaveAgent = async () => {
    await updateAgent({
      name: trimmedStagedAgentName,
      description: trimmedStagedAgentDescription,
      config: { configurable: stagedAgentConfigurable },
    });
    mutateAgents();
    onClose();
  };

  const { agentNameError, submitButtonDisabledMessage } =
    useAgentCreateUpdateError({
      agentId: assistantId,
      agentName: trimmedStagedAgentName,
      agentDescription: trimmedStagedAgentDescription,
      isSaving: isUpdatingAgent,
    });

  return (
    <Pane
      open={!!agent}
      onClose={onClose}
      title={`${EDIT_AGENT_TITLE}: ${name}`}
      className="h-full w-full overflow-y-auto p-0"
      preventInteractOutside={isDeleteModalOpen}
      topBarRightElement={
        <div className="flex items-center gap-2 px-4">
          <Button
            variant="outlined"
            color="danger"
            onClick={() => setAgentToDelete(agent)}
            loading={isDeletingAgent}
            startDecorator={<Trash04Icon className="h-4 w-4" />}
            size="sm"
          >
            Delete agent
          </Button>
          <Button
            variant="outlined"
            color="neutral"
            onClick={onClose}
            size="sm"
          >
            Cancel
          </Button>
          <Tooltip title={submitButtonDisabledMessage}>
            <div>
              <Button
                onClick={handleSaveAgent}
                disabled={!!submitButtonDisabledMessage}
                loading={isUpdatingAgent}
                size="sm"
              >
                Save
              </Button>
            </div>
          </Tooltip>
        </div>
      }
    >
      <div className="grid h-full grid-cols-2 gap-4 p-6">
        <div className="flex h-full gap-5 pr-6">
          <div className="flex grow flex-col gap-6">
            <span className="text-sm text-secondary">
              {EDIT_AGENT_DESCRIPTION}
            </span>
            {renderAgentForm()}
          </div>
        </div>
        <div className="flex flex-shrink-0 grow flex-col gap-1 pl-6">
          <span className="text-md font-medium text-primary">
            {GRAPH_PREVIEW_TITLE}
          </span>
          <span className="text-sm text-tertiary">
            {GRAPH_PREVIEW_DESCRIPTION}
          </span>
          <div
            className="relative z-0 mt-3 rounded-md border border-secondary bg-primary p-4"
            style={{
              width: 'min(100%, calc(100vh - 200px))',
              height: 'min(100%, calc(100vh - 200px))',
            }}
          >
            <div className="h-full w-full bg-secondary">
              <GraphPreview
                key={assistantId}
                className="h-full w-full"
                graphData={graph}
                isLoading={graphLoading}
                error={graphError}
              />
            </div>
          </div>
        </div>
      </div>
    </Pane>
  );
};
