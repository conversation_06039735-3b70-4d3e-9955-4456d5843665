import type { SerializedConstructor } from '@langchain/core/load/serializable';
import { TemplateFormat } from '@langchain/core/prompts';

import {
  SchemaDefinition,
  SchemaProperty,
} from '@/components/EvaluatorCrudPane/utils/serializeFeedbackUIToLC';

import { StructuredPromptSchema } from '../Playground/components/manifest/serialized/prompts/ManifestStructuredPrompt';

const defaultEvaluatorPromptMessages = (templateFormat) => {
  const value = 'constructor' as const;
  return [
    {
      id: ['langchain', 'prompts', 'chat', 'SystemMessagePromptTemplate'],
      lc: 1,
      type: value,
      kwargs: {
        prompt: {
          id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
          lc: 1,
          type: value,
          kwargs: {
            template: `First, describe the persona of your evaluator (i.e. "You are an expert at comparing two answers")

<Rubric>
Provide bullet points for how the LLM should score both correct and incorrect examples, i.e.:
A correct answer:
- Matches the reference answer exactly
- etc.

An incorrect answer:
- Differs from the reference answer
- etc.
</Rubric>

<Instructions>
Provide instructions for how the LLM should process the example, i.e.:
- Find all facts in the reference answer and provided answer
- etc.
</Instructions>

<Reminder>
Provide any remaining important reminders for the LLM to keep in mind while grading, i.e.:
- Different words with the same semantic meaning are the same
</Reminder>`,
            input_variables: ['input', 'output', 'reference'],
            template_format: templateFormat ?? 'mustache',
          },
        },
      },
    },
    {
      id: ['langchain', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
      lc: 1,
      type: value,
      kwargs: {
        prompt: {
          id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
          lc: 1,
          type: value,
          kwargs: {
            template: `Please grade the following example according to the above instructions:

<example>
<input>
${templateFormat === 'mustache' ? '{{input}}' : '{input}'}
</input>

<output>
${templateFormat === 'mustache' ? '{{output}}' : '{output}'}
</output>

<reference_outputs>
${templateFormat === 'mustache' ? '{{reference}}' : '{reference}'}
</reference_outputs>
</example>`,
            input_variables: ['input', 'output', 'reference'],
            template_format: templateFormat ?? 'mustache',
          },
        },
      },
    },
  ];
};

export function emptyStructuredPromptManifest(
  variant: 'dataset-eval' | 'session-eval' | undefined,
  inputVar?: string | null,
  templateFormat?: TemplateFormat
): SerializedConstructor {
  const kwargs: {
    messages: SerializedConstructor[];
    input_variables: string[];
  } = {
    messages: [
      {
        id: ['langchain', 'prompts', 'chat', 'SystemMessagePromptTemplate'],
        lc: 1,
        type: 'constructor',
        kwargs: {
          prompt: {
            id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
            lc: 1,
            type: 'constructor',
            kwargs: {
              template: 'You are a chatbot.',
              input_variables: [],
              template_format: templateFormat ?? 'f-string',
            },
          },
        },
      },
      {
        id: ['langchain', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
        lc: 1,
        type: 'constructor',
        kwargs: {
          prompt: {
            id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
            lc: 1,
            type: 'constructor',
            kwargs: {
              template:
                templateFormat === 'mustache'
                  ? `{{${inputVar ?? 'question'}}}`
                  : `{${inputVar ?? 'question'}}`,
              input_variables: [`${inputVar ? inputVar : 'question'}`],
              template_format: templateFormat ?? 'f-string',
            },
          },
        },
      },
    ],
    input_variables: [`${inputVar ? inputVar : 'question'}`],
  };

  if (variant === 'session-eval') {
    kwargs.input_variables = ['input', 'output'];
    kwargs.messages = defaultEvaluatorPromptMessages(templateFormat);
  }

  if (variant === 'dataset-eval') {
    kwargs.input_variables = ['input', 'output', 'reference_output'];
    kwargs.messages = defaultEvaluatorPromptMessages(templateFormat);
  }

  return {
    lc: 1,
    type: 'constructor',
    id: ['langchain_core', 'prompts', 'structured', 'StructuredPrompt'],
    kwargs: {
      ...kwargs,
      ...emptyOutputSchema(),
    },
  };
}
export function emptyChatPromptManifest(
  inputVar?: string | null,
  templateFormat?: TemplateFormat
): SerializedConstructor {
  return {
    lc: 1,
    type: 'constructor',
    id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
    kwargs: {
      messages: [
        {
          id: ['langchain', 'prompts', 'chat', 'SystemMessagePromptTemplate'],
          lc: 1,
          type: 'constructor',
          kwargs: {
            prompt: {
              id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
              lc: 1,
              type: 'constructor',
              kwargs: {
                template: 'You are a chatbot.',
                input_variables: [],
                template_format: templateFormat ?? 'f-string',
              },
            },
          },
        },
        {
          id: ['langchain', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
          lc: 1,
          type: 'constructor',
          kwargs: {
            prompt: {
              id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
              lc: 1,
              type: 'constructor',
              kwargs: {
                template:
                  templateFormat === 'mustache'
                    ? `{{${inputVar ?? 'question'}}}`
                    : `{${inputVar ?? 'question'}}`,
                input_variables: [`${inputVar ? inputVar : 'question'}`],
                template_format: templateFormat ?? 'f-string',
              },
            },
          },
        },
      ],
      input_variables: [`${inputVar ? inputVar : 'question'}`],
    },
  };
}
export function defaultEvaluatorPromptManifest(
  evaluatorName: string
): SerializedConstructor {
  return {
    lc: 1,
    type: 'constructor',
    id: ['langchain_core', 'prompts', 'structured', 'StructuredPrompt'],
    kwargs: {
      messages: [
        {
          lc: 1,
          type: 'constructor',
          id: [
            'langchain_core',
            'prompts',
            'chat',
            'SystemMessagePromptTemplate',
          ],
          kwargs: {
            prompt: {
              lc: 1,
              type: 'constructor',
              id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
              kwargs: {
                input_variables: [],
                template_format: 'mustache',
                template: 'Write your evaluator prompt here.',
              },
            },
          },
        },
        {
          lc: 1,
          type: 'constructor',
          id: [
            'langchain_core',
            'prompts',
            'chat',
            'HumanMessagePromptTemplate',
          ],
          kwargs: {
            prompt: {
              lc: 1,
              type: 'constructor',
              id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
              kwargs: {
                input_variables: ['inputs', 'outputs', 'reference_outputs'],
                template_format: 'mustache',
                template:
                  '<input>\n{{input}}\n</input>\n\n<output>\n{{output}}\n</output>\n\n<referenceOutput>\n{{referenceOutput}}\n</referenceOutput>',
              },
            },
          },
        },
      ],
      template_format: 'mustache',
      input_variables: ['question'],
      schema_: {
        title: 'extract',
        description: "Extract information from the user's response.",
        type: 'object',
        properties: {
          [evaluatorName]: {
            type: 'integer',
            enum: [0, 1],
          },
        },
        required: [evaluatorName],
        strict: true,
        additionalProperties: false,
      },
    },
  };
}
export function emptyPromptManifest(
  templateFormat?: TemplateFormat
): SerializedConstructor {
  return {
    lc: 1,
    type: 'constructor',
    id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
    kwargs: {
      template: templateFormat === 'mustache' ? '{{question}}' : '{question}',
      input_variables: ['question'],
      template_format: templateFormat ?? 'f-string',
    },
  };
}

export function emptyOutputSchema(): Record<string, StructuredPromptSchema> {
  return {
    schema_: {
      title: 'extract',
      description: "Extract information from the user's response.",
      type: 'object',
      properties: {
        correctness: {
          type: 'boolean',
          description: 'Is the submission correct, accurate, and factual?',
        },
      },
      required: ['correctness'],
    },
  };
}

export function defaultEvaluatorOutputSchema(
  type: 'dataset-eval' | 'session-eval'
): SchemaDefinition {
  const properties: Record<string, SchemaProperty> = {};

  if (type === 'session-eval') {
    properties.conciseness = {
      type: 'number',
      description:
        'How concise is the submission? Min (1): least concise | Max (10): most concise',
    };
  } else {
    properties.correctness = {
      type: 'boolean',
      description: 'Is the submission correct, accurate, and factual?',
    };
  }

  const propertyKey = Object.keys(properties)[0];

  return {
    title: 'extract',
    description: "Extract information from the user's response.",
    type: 'object',
    properties,
    required: [propertyKey],
  };
}

export const emptyO1ChatPromptManifest = (
  inputVar?: string | null,
  templateFormat?: TemplateFormat
): SerializedConstructor => {
  return {
    lc: 1,
    type: 'constructor',
    id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
    kwargs: {
      messages: [
        {
          id: ['langchain', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
          lc: 1,
          type: 'constructor',
          kwargs: {
            prompt: {
              id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
              lc: 1,
              type: 'constructor',
              kwargs: {
                template:
                  templateFormat === 'mustache'
                    ? `{{${inputVar ?? 'question'}}}`
                    : `{${inputVar ?? 'question'}}`,
                input_variables: [`${inputVar ? inputVar : 'question'}`],
                template_format: templateFormat ?? 'f-string',
              },
            },
          },
        },
      ],
      input_variables: [`${inputVar ? inputVar : 'question'}`],
    },
  };
};
