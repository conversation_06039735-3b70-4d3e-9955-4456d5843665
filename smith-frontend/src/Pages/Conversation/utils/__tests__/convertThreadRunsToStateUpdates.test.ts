// @ts-nocheck
import { describe, expect, test } from 'vitest';

import { FlatRun } from '@/Pages/Run/utils/utils';

import {
  INPUT_NODE_NAME,
  convertThreadRunsToStateUpdates,
} from '../convertThreadRunsToStateUpdates';

describe('convertThreadRunsToStateUpdates before langgraph breaking version', () => {
  const langGraphVersion = '0.3.28';
  test('handles empty flattenedRuns', () => {
    const result = convertThreadRunsToStateUpdates({
      flattenedRuns: [],
      langGraphVersion,
    });

    expect(result).toHaveLength(0);
  });

  test('handles missing langgraph step', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1',
          inputs: {},
          outputs: { value: 'output1' },
          extra: { metadata: {} }, // No langgraph_step
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should just return initial state since no valid steps were found
    expect(result).toHaveLength(0);
  });

  test('handles one node per step without writes', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1',
          inputs: {},
          outputs: { value: 'output1' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'run2',
        depth: 1,
        run: {
          id: 'run2',
          name: 'node2',
          inputs: {},
          outputs: { value: 'output2' },
          extra: { metadata: { langgraph_step: 1 } },
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should have initial state and final state
    expect(result).toHaveLength(2);
    expect(result[0]).toEqual({
      values: {},
      asNode: INPUT_NODE_NAME,
    });
    expect(result[1]).toEqual({
      values: { value: 'output2' },
      asNode: 'node2',
    });
  });

  test('handles one node per step with writes', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1',
          inputs: {},
          outputs: { value: 'output1' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'write1',
        depth: 2,
        run: {
          id: 'write1',
          name: '_write',
          inputs: {},
          outputs: {},
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'run2',
        depth: 1,
        run: {
          id: 'run2',
          name: 'node2',
          inputs: {},
          outputs: { value: 'output2' },
          extra: { metadata: { langgraph_step: 1 } },
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should have initial state, node1 state, and final state
    expect(result).toHaveLength(3);
    expect(result[0]).toEqual({
      values: {},
      asNode: INPUT_NODE_NAME,
    });
    expect(result[1]).toEqual({
      values: { value: 'output1' },
      asNode: 'node1',
    });
    expect(result[2]).toEqual({
      values: { value: 'output2' },
      asNode: 'node2',
    });
  });

  test('handles multiple nodes per step with writes', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1a',
          inputs: {},
          outputs: { value: 'output1a' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'run2',
        depth: 1,
        run: {
          id: 'run2',
          name: 'node1b',
          inputs: {},
          outputs: { value: 'output1b' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'write1',
        depth: 2,
        run: {
          id: 'write1',
          name: '_write',
          inputs: {},
          outputs: {},
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'run3',
        depth: 1,
        run: {
          id: 'run3',
          name: 'node2',
          inputs: {},
          outputs: { value: 'output2' },
          extra: { metadata: { langgraph_step: 1 } },
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should have initial state, both step 0 nodes as state updates, and final state
    expect(result).toHaveLength(4);
    expect(result[0]).toEqual({
      values: {},
      asNode: INPUT_NODE_NAME,
    });
    expect(result[1]).toEqual({
      values: { value: 'output1a' },
      asNode: 'node1a',
    });
    expect(result[2]).toEqual({
      values: { value: 'output1b' },
      asNode: 'node1b',
    });
    expect(result[3]).toEqual({
      values: { value: 'output2' },
      asNode: 'node2',
    });
  });

  test('handles ChannelWrite nodes', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1',
          inputs: {},
          outputs: { value: 'output1' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'channel_write',
        depth: 3, // Different depth but should still be recognized
        run: {
          id: 'channel_write',
          name: 'ChannelWrite_something',
          inputs: {},
          outputs: {},
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'run2',
        depth: 1,
        run: {
          id: 'run2',
          name: 'node2',
          inputs: {},
          outputs: { value: 'output2' },
          extra: { metadata: { langgraph_step: 1 } },
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should handle ChannelWrite nodes the same as _write nodes
    expect(result).toHaveLength(3);
    expect(result[0]).toEqual({
      values: {},
      asNode: INPUT_NODE_NAME,
    });
    expect(result[1]).toEqual({
      values: { value: 'output1' },
      asNode: 'node1',
    });
    expect(result[2]).toEqual({
      values: { value: 'output2' },
      asNode: 'node2',
    });
  });

  test('properly processes steps in order', () => {
    const flattenedRuns: FlatRun[] = [
      // Intentionally not in order to test sorting
      {
        id: 'run2',
        depth: 1,
        run: {
          id: 'run2',
          name: 'node2',
          inputs: {},
          outputs: { value: 'output2' },
          extra: { metadata: { langgraph_step: 1 } },
        },
      },
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1',
          inputs: {},
          outputs: { value: 'output1' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'write1',
        depth: 2,
        run: {
          id: 'write1',
          name: '_write',
          inputs: {},
          outputs: {},
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should process steps in order regardless of input order
    expect(result).toHaveLength(3);
    expect(result[0]).toEqual({
      values: {},
      asNode: INPUT_NODE_NAME,
    });
    expect(result[1]).toEqual({
      values: { value: 'output1' },
      asNode: 'node1',
    });
    expect(result[2]).toEqual({
      values: { value: 'output2' },
      asNode: 'node2',
    });
  });
});

describe('convertThreadRunsToStateUpdates after langgraph breaking version', () => {
  const langGraphVersion = '0.3.29';
  test('handles empty flattenedRuns', () => {
    const result = convertThreadRunsToStateUpdates({
      flattenedRuns: [],
      langGraphVersion,
    });

    expect(result).toHaveLength(0);
  });

  test('handles missing langgraph step', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1',
          inputs: {},
          outputs: { value: 'output1' },
          extra: { metadata: {} }, // No langgraph_step
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    expect(result).toHaveLength(0);
  });

  test('handles one node per step', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run0',
        depth: 0,
        run: {
          id: 'run0',
          name: 'node0',
          inputs: { value: 'input0' },
          outputs: {},
          extra: { metadata: {} },
        },
      },
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1',
          inputs: {},
          outputs: { value: 'output1' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should have initial state and final state
    expect(result).toHaveLength(3);
    expect(result[0]).toEqual({
      values: {},
      asNode: INPUT_NODE_NAME,
    });
    expect(result[1]).toEqual({
      values: { value: 'input0' },
      asNode: '__start__',
    });
    expect(result[2]).toEqual({
      values: { value: 'output1' },
      asNode: 'node1',
    });
  });

  test('handles multiple nodes per step', () => {
    const flattenedRuns: FlatRun[] = [
      {
        id: 'run0',
        depth: 0,
        run: {
          id: 'run0',
          name: 'node0',
          inputs: { value: 'input0' },
          outputs: {},
          extra: { metadata: {} },
        },
      },
      {
        id: 'run1',
        depth: 1,
        run: {
          id: 'run1',
          name: 'node1a',
          inputs: {},
          outputs: { value: 'output1a' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'run2',
        depth: 1,
        run: {
          id: 'run2',
          name: 'node1b',
          inputs: {},
          outputs: { value: 'output1b' },
          extra: { metadata: { langgraph_step: 0 } },
        },
      },
      {
        id: 'run3',
        depth: 1,
        run: {
          id: 'run3',
          name: 'node2',
          inputs: {},
          outputs: { value: 'output2' },
          extra: { metadata: { langgraph_step: 1 } },
        },
      },
    ];

    const result = convertThreadRunsToStateUpdates({
      flattenedRuns,
      langGraphVersion,
    });

    // Should have initial state, both step 0 nodes as state updates, and final state
    expect(result).toHaveLength(5);
    expect(result[0]).toEqual({
      values: {},
      asNode: INPUT_NODE_NAME,
    });
    expect(result[1]).toEqual({
      values: { value: 'input0' },
      asNode: '__start__',
    });
    expect(result[2]).toEqual({
      values: { value: 'output1a' },
      asNode: 'node1a',
    });
    expect(result[3]).toEqual({
      values: { value: 'output1b' },
      asNode: 'node1b',
    });
    expect(result[4]).toEqual({
      values: { value: 'output2' },
      asNode: 'node2',
    });
  });
});
