import semver from 'semver';
import { z } from 'zod';

import { FlatRun } from '@/Pages/Run/utils/utils';

type StateUpdate = {
  values: Record<string, unknown>;
  asNode: string;
};

export const INPUT_NODE_NAME = '__input__';
export const START_NODE_NAME = '__start__';
const WRITE_NODE_NAME = '_write';
const CHANNEL_WRITE_NODE_NAME = 'ChannelWrite';

// this version of langgraph has a breaking change where tracing happens diferently.
const LANGGRAPH_BREAKING_VERSION = '0.3.29';

const InputSchema = z
  .union([
    z.object({ input: z.record(z.unknown()) }).transform((val) => val.input),
    z.record(z.unknown()),
  ])
  .optional()
  .default({});

const OutputSchema = z
  .union([
    z.object({ output: z.record(z.unknown()) }).transform((val) => val.output),
    z.record(z.unknown()),
  ])
  .optional()
  .default({});

export const convertThreadRunsToStateUpdates = ({
  flattenedRuns,
  langGraphVersion,
}: {
  flattenedRuns: FlatRun[];
  langGraphVersion: string | undefined;
}): StateUpdate[] => {
  const isAfterLangGraphBreakingVersion = langGraphVersion
    ? semver.gte(langGraphVersion, LANGGRAPH_BREAKING_VERSION)
    : false;

  const stateUpdates: StateUpdate[] = [];
  const stepMap = getGroupedLanggraphSteps(flattenedRuns);
  if (stepMap.size === 0) return stateUpdates;

  // Sort steps to process them in order
  const sortedSteps = Array.from(stepMap.keys()).sort((a, b) => a - b);

  const initialState: StateUpdate = {
    values: {},
    asNode: INPUT_NODE_NAME,
  };
  stateUpdates.push(initialState);

  // after this version, __start__ is hidden from the trace so we infer it here
  if (isAfterLangGraphBreakingVersion) {
    const firstRun = flattenedRuns[0];

    const firstRunInput = InputSchema.parse(firstRun.run.inputs);

    stateUpdates.push({
      values: firstRunInput,
      asNode: START_NODE_NAME,
    });
  }

  // Process each step
  for (const step of sortedSteps.slice(0, -1)) {
    const { nodes, writes } = stepMap.get(step) || { nodes: [], writes: [] };

    if (isAfterLangGraphBreakingVersion || writes.length > 0) {
      // new values will be the next step's node's input. we must do it this way because we don't know how the writes are combined in
      // the original thread run.
      nodes.forEach((node) => {
        const input = InputSchema.parse(node.run.inputs);
        const output = OutputSchema.parse(node.run.outputs);
        const newValues = { ...input, ...output };
        const stateUpdate: StateUpdate = {
          values: newValues as Record<string, unknown>,
          asNode: node.run.name,
        };
        stateUpdates.push(stateUpdate);
      });
    }
  }
  // before returning, we need to create the final state update.
  const lastStep = sortedSteps[sortedSteps.length - 1];
  const lastNode = stepMap.get(lastStep)?.nodes[0];
  const finalState: StateUpdate = {
    // this isn't technically the right value of the final state update. We don't have a next node to look at though.
    values: lastNode?.run.outputs || {},
    asNode: lastNode?.run.name || '',
  };
  stateUpdates.push(finalState);
  return stateUpdates;
};

/**
 * Groups runs by langgraph_step.
 * @param runs - The runs to group.
 * @returns A map of langgraph_step to an object containing the nodes and writes for that step.
 */
const getGroupedLanggraphSteps = (
  runs: FlatRun[]
): Map<number, { nodes: FlatRun[]; writes: FlatRun[] }> => {
  const stepMap = new Map<number, { nodes: FlatRun[]; writes: FlatRun[] }>();

  for (const flatRun of runs) {
    const step = flatRun.run.extra?.metadata?.langgraph_step;
    if (step === undefined || step === null) continue;

    if (!stepMap.has(step)) {
      stepMap.set(step, { nodes: [], writes: [] });
    }
    const stepData = stepMap.get(step)!;

    // _write runs are at depth 2 and update state
    // todo: maybe this is actually mod 2 = 0, isntead of === 2
    if (
      (flatRun.depth === 2 && flatRun.run.name === WRITE_NODE_NAME) ||
      flatRun.run.name.includes(CHANNEL_WRITE_NODE_NAME)
    ) {
      stepData.writes.push(flatRun);
      // todo maybe this is actually mod 2 = 1, instead of === 1
    } else if (flatRun.depth === 1) {
      // Normal nodes are at depth 1
      stepData.nodes.push(flatRun);
    }
  }
  return stepMap;
};
