import { useMemo } from 'react';

import { useGraphAssistants } from '@/Pages/Graph/src/api/assistants';

export const useAgentCreateUpdateError = ({
  agentId,
  agentName,
  agentDescription,
  isSaving,
}: {
  agentId?: string;
  agentName?: string;
  agentDescription?: string;
  isSaving?: boolean;
}) => {
  const { assistants } = useGraphAssistants({});
  const otherAssistants = useMemo(() => {
    return assistants?.filter((a) => a.assistant_id !== agentId);
  }, [assistants, agentId]);

  const agentNameError = (() => {
    if (otherAssistants?.some((a) => a.name === agentName))
      return 'An agent with this name already exists';
    return undefined;
  })();

  const submitButtonDisabledMessage = (() => {
    if (!agentName) return 'Please enter an agent name';
    if (agentNameError) return agentNameError;
    if (!agentDescription) return 'Please enter an agent description';
    if (isSaving) return 'Saving agent...';
    return undefined;
  })();

  return { agentNameError, submitButtonDisabledMessage };
};
