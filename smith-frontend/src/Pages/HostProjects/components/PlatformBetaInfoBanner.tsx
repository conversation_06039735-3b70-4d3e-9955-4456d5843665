import { ExternalLinkIcon } from 'lucide-react';
import { Link } from 'react-router-dom';

import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { useOrganizationId } from '@/hooks/useSwr';
import { appOrganizationPath, appSettingsPath } from '@/utils/constants';
import { xCount } from '@/utils/stringUtils';

const PlatformBetaInfoBanner = () => {
  const organizationId = useOrganizationId();
  const { value: maxLangGraphCloudDeployments } = useOrgConfig(
    OrgConfigs.max_langgraph_cloud_deployments
  );
  return (
    <div className="rounded-md bg-brand-tertiary p-4 text-sm text-primary">
      <span>
        Read the LangGraph Platform docs{' '}
        <a
          href="https://langchain-ai.github.io/langgraph/cloud/"
          className="inline-flex items-center gap-0.5 underline"
          target="_blank"
          rel="noreferrer"
        >
          here
          <ExternalLinkIcon className="h-4 w-4" />
        </a>
        . During open beta, each org can make up to{' '}
        {xCount('deployment', maxLangGraphCloudDeployments as number)} and node
        execution is free. Track usage{' '}
        <Link
          to={`/${appOrganizationPath}/${organizationId}/${appSettingsPath}/usage`}
          className="inline-flex items-center gap-0.5 underline"
          target="_blank"
          rel="noreferrer"
        >
          here
          <ExternalLinkIcon className="h-4 w-4" />
        </Link>
        .
      </span>
    </div>
  );
};

export default PlatformBetaInfoBanner;
