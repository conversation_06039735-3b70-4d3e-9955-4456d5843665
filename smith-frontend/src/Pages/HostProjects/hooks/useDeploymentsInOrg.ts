import useSWR from 'swr';

import { fetcher } from '@/data/fetcher';
import { useWorkspaceList } from '@/hooks/useSwr';
import { HostProjectSchema } from '@/types/schema';
import { hostApiPath } from '@/utils/constants';

export const useDeploymentsInOrg = () => {
  const { data: workspaces } = useWorkspaceList();
  const workspaceIds = workspaces?.map((w) => w.id);
  const {
    data,
    isLoading: isDeploymentsInOrgLoading,
    error: deploymentsInOrgError,
    mutate: mutateDeploymentsInOrg,
  } = useSWR<HostProjectSchema[][] | undefined>(
    ['orgDeployments', workspaceIds],
    () => {
      if (!workspaceIds) {
        return undefined;
      }
      return Promise.all(
        workspaceIds.map((id) =>
          fetcher<HostProjectSchema[]>({
            url: `${hostApiPath}/projects`,
            headers: {
              'X-Tenant-Id': id,
            },
          })
        )
      );
    }
  );
  const flattenedData = data?.flat();

  return {
    deploymentsInOrg: flattenedData,
    isDeploymentsInOrgLoading,
    deploymentsInOrgError,
    mutateDeploymentsInOrg,
  };
};
