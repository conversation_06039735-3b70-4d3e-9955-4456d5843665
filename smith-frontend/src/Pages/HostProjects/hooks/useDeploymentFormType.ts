import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { singleOriginEnabled } from '@/utils/constants';

export const useDeploymentFormType = () => {
  const { value: langGraphRemoteReconcilerEnabled } = useOrgConfig(
    OrgConfigs.langgraph_remote_reconciler_enabled
  );
  const { value: langGraphDeployOwnCloudEnabled } = useOrgConfig(
    OrgConfigs.langgraph_deploy_own_cloud_enabled
  );
  if (
    singleOriginEnabled === '1' ||
    langGraphRemoteReconcilerEnabled === true
  ) {
    return 'self_hosted';
  } else {
    if (langGraphDeployOwnCloudEnabled) {
      return 'byoc_aws';
    } else {
      return 'cloud';
    }
  }
};
