import { ArrowUpRightIcon, Rocket02Icon } from '@langchain/untitled-ui-icons';
import { Button, LinearProgress } from '@mui/joy';

import { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';

import GraphFillIcon from '@/Pages/Graph/icons/GraphFillIcon.svg?react';
import Breadcrumbs from '@/components/Breadcrumbs';
import EmptyState from '@/components/EmptyState';
import { ErrorBanner } from '@/components/ErrorBanner';
import {
  HOST_PROJECTS_PAGE_DESCRIPTION,
  HOST_PROJECTS_PAGE_TITLE,
} from '@/constants/pageTitlesAndDescriptionConstants';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { usePermissions } from '@/hooks/usePermissions';
import { useHostProjects, useOrganizationId } from '@/hooks/useSwr';
import { appOrganizationPath } from '@/utils/constants';

import { HostProjectCrudPaneWithButton } from '../../components/HostProjectCrudPane/HostProjectCrudPaneWithButton';
import { HostProjectsTable } from '../../components/HostProjectsTable';
import { PageTitle } from '../../components/PageTitle';
import { GraphConnectPopover } from '../Graph/GraphConnectPopover';

export function NoHostProjects({ hasTags }: { hasTags: boolean }) {
  const [newDeploymentOpen, setNewDeploymentOpen] = useState(false);
  return (
    <div className="py-10">
      <EmptyState
        title="No deployments found"
        description={
          hasTags
            ? 'No deployments matching current set of resource tags'
            : 'Start by creating a new application'
        }
        icon={<Rocket02Icon />}
        fancyIcon
        action={
          <HostProjectCrudPaneWithButton
            open={newDeploymentOpen}
            setIsOpen={setNewDeploymentOpen}
          />
        }
      />
    </div>
  );
}

const HostOnboarding = ({
  newDeploymentOpen,
  setNewDeploymentOpen,
}: {
  newDeploymentOpen: boolean;
  setNewDeploymentOpen: (open: boolean) => void;
}) => {
  const { authorize } = usePermissions();

  return (
    <div className="flex max-w-[600px] flex-col gap-4 empty:hidden">
      <div className="flex flex-col gap-3 rounded-lg border border-primary p-6">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-tertiary">
          <GraphFillIcon className="size-5 text-brand-secondary" />
        </div>
        <p className="mb-1 font-semibold">Build</p>

        <p className="text-sm text-tertiary">
          Build LLM apps faster with LangGraph Studio -- a dedicated visual IDE
          for agent development.
        </p>

        <div className="flex-grow" />

        <div className="flex items-center gap-2">
          <GraphConnectPopover />
          <a
            href="https://langchain-ai.github.io/langgraph/concepts/langgraph_studio"
            className="rounded-md px-3 py-1 text-sm text-secondary hover:bg-primary-hover"
            target="_blank"
            rel="noreferrer"
          >
            <span>Docs</span>
          </a>
        </div>
      </div>
      <div className="flex flex-col gap-3 rounded-lg border border-primary p-6">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-tertiary">
          <Rocket02Icon className="size-5 text-brand-secondary" />
        </div>
        <p className="mb-1 font-semibold">Deploy</p>

        <p className="text-sm text-tertiary">
          Deploy your LangGraph API instantly with LangGraph Platform — a
          managed service that handles all dependencies and infrastructure,
          letting you focus on building.
        </p>

        <div className="flex-grow" />

        <div className="flex items-center gap-2">
          {authorize('deployments:create') && (
            <HostProjectCrudPaneWithButton
              open={newDeploymentOpen}
              setIsOpen={setNewDeploymentOpen}
            />
          )}
          <a
            href="https://langchain-ai.github.io/langgraph/concepts/langgraph_platform"
            className="rounded-md px-3 py-1 text-sm text-secondary hover:bg-primary-hover"
            target="_blank"
            rel="noreferrer"
          >
            <span>Docs</span>
          </a>
        </div>
      </div>
    </div>
  );
};

const HostProjectsBody = () => {
  const { authorize } = usePermissions();
  // just to check if there are any host projects
  const { data: hostProjects, isLoading } = useHostProjects({
    offset: 0,
    limit: 1,
  });
  const [newDeploymentOpen, setNewDeploymentOpen] = useState(false);

  if (isLoading) {
    return <LinearProgress />;
  }

  const showOnboarding = hostProjects?.length === 0;

  if (showOnboarding)
    return (
      <div className="flex h-[70vh] flex-col items-center justify-center gap-6">
        <div className="flex flex-col items-center gap-2">
          <span className="text-3xl font-medium">LangGraph Platform</span>
          <span className="text-md text-secondary">
            LangGraph Platform is a solution for deploying agentic applications
            to production.{' '}
            <a
              href="https://langchain-ai.github.io/langgraph/concepts/langgraph_platform/"
              target="_blank"
              rel="noreferrer"
              className="underline"
            >
              Learn more
            </a>
          </span>
        </div>
        <HostOnboarding
          newDeploymentOpen={newDeploymentOpen}
          setNewDeploymentOpen={setNewDeploymentOpen}
        />
      </div>
    );
  return (
    <>
      <div className="my-4 flex items-center justify-between gap-4">
        <div className="flex flex-col gap-1">
          <PageTitle
            className="py-0"
            description={HOST_PROJECTS_PAGE_DESCRIPTION}
          >
            {HOST_PROJECTS_PAGE_TITLE}
          </PageTitle>
        </div>
        <div className="flex gap-2">
          <GraphConnectPopover />
          {authorize('deployments:create') && (
            <HostProjectCrudPaneWithButton
              open={newDeploymentOpen}
              setIsOpen={setNewDeploymentOpen}
            />
          )}
        </div>
      </div>

      <div className="flex flex-col gap-3">
        <HostProjectsTable />
      </div>
    </>
  );
};

const HostProjects = () => {
  const { value: canUseLangGraphCloud, isLoading: isLangGraphCloudLoading } =
    useOrgConfig(OrgConfigs.can_use_langgraph_cloud);
  const organizationId = useOrganizationId();

  const { authorize, isLoading: isPermsLoading } = usePermissions();

  if (isLangGraphCloudLoading) {
    return <LinearProgress />;
  }

  if (!canUseLangGraphCloud) {
    return (
      <div className="h-[100vh] px-4 pb-6 pt-3">
        <Breadcrumbs />

        <div className="my-4 flex items-center justify-between gap-4">
          <div className="flex flex-col gap-1">
            <PageTitle
              className="py-0"
              description={HOST_PROJECTS_PAGE_DESCRIPTION}
            >
              {HOST_PROJECTS_PAGE_TITLE}
            </PageTitle>
          </div>

          <div className="flex gap-2">
            <GraphConnectPopover connectionTypes={['deployed']} />
          </div>
        </div>

        <div className="flex h-[70vh] flex-col items-center justify-center gap-2">
          <EmptyState
            title="LangGraph Platform is available to users on a paid plan."
            description={'Upgrade to access'}
            icon={<Rocket02Icon />}
            fancyIcon
            action={
              <Button
                component={RouterLink}
                variant="outlined"
                to={`/${appOrganizationPath}/${organizationId}/settings/payments`}
                endDecorator={<ArrowUpRightIcon />}
              >
                Upgrade
              </Button>
            }
          />
        </div>
      </div>
    );
  }

  if (isPermsLoading) {
    return <LinearProgress />;
  }

  if (!authorize('deployments:read')) {
    return <ErrorBanner className="m-4">Forbidden</ErrorBanner>;
  }

  return (
    <div className="px-4 pb-6 pt-3">
      <Breadcrumbs />

      <HostProjectsBody />
    </div>
  );
};

export default HostProjects;
