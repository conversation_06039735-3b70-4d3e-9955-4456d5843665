import { LinearProgress } from '@mui/joy';

import { Outlet, useSearchParams } from 'react-router-dom';

import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { useHostProject } from '@/hooks/useSwr';

import { LangGraphContext } from './src/api';
import { useGraphThread } from './src/api/useGraphSwr';
import GraphPlaygroundPane from './src/components/debug/trace/GraphPlaygroundPane';
import { GraphTraceLogRowSmith } from './src/components/debug/trace/GraphTraceLogRow.smith';
import { useInputHistory } from './src/components/debug/trace/hooks/useInputHistory';
import { InputHistoryContext } from './src/components/debug/trace/hooks/useInputHistory';
import { ExperimentModalAndButton } from './src/components/site/ExperimentModal';
import { GraphTopBar } from './src/components/site/GraphTopBar';
import { GraphInitializeError } from './src/components/site/error/GraphInitializeError';
import { useAssistantToInitialize } from './src/hooks/assistants/useAssistantToInitialize';
import { useInitializeAssistantState } from './src/hooks/assistants/useInitializeAssistantState';
import { useIntializeStudioClient } from './src/hooks/useIntializeStudioClient';
import { useStudioAnalytics } from './src/hooks/useStudioAnalytics';
import { StudioModeContext, useStudioMode } from './src/hooks/useStudioMode';
import { LangSmithAwareContext } from './src/langsmith';

function HostedProvider(props: { hostProjectId: string }) {
  const project = useHostProject(props.hostProjectId);
  if (project.isLoading) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }
  if (project.error) {
    return <ExpandableErrorAlert error={project.error} />;
  }

  return <Outlet />;
}

function StandaloneProvider() {
  return <Outlet />;
}

export function GraphLayout() {
  const [searchParams] = useSearchParams();

  const hostProjectId = searchParams.get('hostProjectId');
  const rawOrganizationId = searchParams.get('organizationId');

  const { client, isLoadingClient } = useIntializeStudioClient({
    hostProjectId,
  });

  useStudioAnalytics();
  const { studioMode, setStudioMode } = useStudioMode();

  const inputHistory = useInputHistory();

  if (isLoadingClient) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LinearProgress />
      </div>
    );
  }
  if (!client)
    return (
      <ExpandableErrorAlert
        error={new Error('Failed to connect to LangGraph Server')}
      />
    );

  return (
    <LangGraphContext.Provider value={client}>
      <StudioModeContext.Provider value={{ studioMode, setStudioMode }}>
        {/* this context is used to supply langsmith aware components/information to the graph */}
        <LangSmithAwareContext.Provider
          value={
            rawOrganizationId
              ? {
                  TraceLogRow: GraphTraceLogRowSmith,
                  PlaygroundPane: GraphPlaygroundPane,
                  ExperimentModalAndButton: ExperimentModalAndButton,
                }
              : {}
          }
        >
          <InputHistoryContext.Provider value={inputHistory}>
            <div className="grid h-[100svh] grid-rows-[auto,1fr]">
              <div className="px-4 py-3">
                <GraphTopBar hostProjectId={hostProjectId} />
              </div>
              <GraphLayoutInner hostProjectId={hostProjectId} />
            </div>
          </InputHistoryContext.Provider>
        </LangSmithAwareContext.Provider>
      </StudioModeContext.Provider>
    </LangGraphContext.Provider>
  );
}

function GraphLayoutInner({ hostProjectId }: { hostProjectId: string | null }) {
  const [searchParams] = useSearchParams();

  const threadId = searchParams.get('threadId') ?? undefined;
  const thread = useGraphThread(threadId);
  const {
    assistant,
    intitializeError,
    initializeLoading,
    fetchedAssistant,
    onAssistantIdChange,
  } = useAssistantToInitialize({
    graphId: thread.data?.metadata?.graph_id,
  });

  useInitializeAssistantState({
    initAssistantId: assistant?.assistant_id,
    onAssistantIdChange,
  });

  if (intitializeError) {
    return (
      <div className="flex flex-col gap-4 p-4">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-medium tracking-tighter">
            Failed to load assistants
          </h2>
          <p className="text-sm text-secondary">
            Please verify if the API server is running or accessible from the
            browser.
          </p>
        </div>
        <GraphInitializeError error={intitializeError} />
      </div>
    );
  }

  if (initializeLoading && !fetchedAssistant) {
    return (
      <div className="flex w-full items-start justify-center">
        <LinearProgress />
      </div>
    );
  }

  return (
    <div className="relative w-full">
      <div className="absolute inset-0 overflow-y-auto overflow-x-hidden">
        {hostProjectId ? (
          <HostedProvider hostProjectId={hostProjectId} />
        ) : (
          <StandaloneProvider />
        )}
      </div>
    </div>
  );
}
