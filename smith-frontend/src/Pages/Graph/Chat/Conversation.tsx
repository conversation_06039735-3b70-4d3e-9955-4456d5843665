import { ArrowUpIcon, XIcon } from '@langchain/untitled-ui-icons';
import { CircularProgress, Tooltip } from '@mui/joy';

import { useContext, useEffect, useMemo, useRef, useState } from 'react';

import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import FloatingOverflowScrollButton from '@/components/FloatingOverflowScrollButton';
import { Switch } from '@/components/Switch/Switch';
import GraphIcon from '@/icons/GraphIcon.svg?react';
import { cn } from '@/utils/tailwind';

import { Assistant } from '../src/api';
import { useGraphAssistant } from '../src/api/assistants';
import { checkIsSystemAssistant } from '../src/components/site/Assistants/utils';
import { StudioModeContext } from '../src/hooks/useStudioMode';
import { useAssistantStore } from '../src/store/assistants/assistantsStore';
import { InterruptForm } from './InterruptForm';
import { Message } from './Message';
import { useChatContext } from './hooks/useChatContext';

export const Conversation = () => {
  const thread = useChatContext();
  const { messages, error, interrupt } = thread;
  const [showingToolCalls, setShowingToolCalls] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const { studioMode } = useContext(StudioModeContext);

  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);

  const filteredMessages = useMemo(() => {
    return messages.filter((message) => {
      const messageHasToolCalls =
        'tool_calls' in message && !!message.tool_calls?.length;
      if (messageHasToolCalls || message.type === 'tool') {
        return showingToolCalls;
      }
      return true;
    });
  }, [messages, showingToolCalls]);

  useEffect(() => {
    scrollRef.current?.scrollTo({
      top: scrollRef.current?.scrollHeight,
      behavior: 'smooth',
    });
  }, [filteredMessages.length, studioMode]);

  const getAssistantDisplayName = (a: Assistant | undefined) => {
    if (!a) return;
    if (checkIsSystemAssistant(a)) {
      return `Default (${a.graph_id})`;
    }
    return a.name;
  };

  if (!messages.length) {
    return (
      <div className="mx-auto flex h-full max-w-[800px] flex-col items-center gap-3">
        <div className="flex grow flex-col items-center justify-center gap-2">
          <div className="flex flex-row items-center gap-2 text-primary">
            <GraphIcon className="size-6" />
            <span className="text-xl">LangGraph Studio</span>
          </div>
          <span className="text-xl font-semibold text-disabled">
            {`Start chatting with ${getAssistantDisplayName(assistant)}`}
          </span>
        </div>
        {!!error && <ExpandableErrorAlert error={error} />}
        <InputBar
          showingToolCalls={showingToolCalls}
          setShowingToolCalls={setShowingToolCalls}
        />
      </div>
    );
  }

  return (
    <div className="mx-auto flex h-full max-w-[1000px] flex-col gap-3">
      <div className="flex flex-col gap-8 overflow-y-auto px-8" ref={scrollRef}>
        {filteredMessages.map((message) => (
          <Message key={message.id} message={message} />
        ))}
        {interrupt && <InterruptForm interrupt={interrupt} />}
      </div>
      <FloatingOverflowScrollButton
        containerRef={scrollRef}
        className="relative w-fit"
      />
      {!!error && <ExpandableErrorAlert error={error} />}
      <InputBar
        showingToolCalls={showingToolCalls}
        setShowingToolCalls={setShowingToolCalls}
      />
    </div>
  );
};

const InputBar = ({
  showingToolCalls,
  setShowingToolCalls,
}: {
  showingToolCalls: boolean;
  setShowingToolCalls: (showingToolCalls: boolean) => void;
}) => {
  const { isLoading, submit, stop, interrupt } = useChatContext();
  const [message, setMessage] = useState('');
  const onSubmit = () => {
    if (isLoading) {
      stop();
      return;
    }
    submit({ messages: [{ type: 'human', content: message }] });
    setMessage('');
  };
  const disabled = isLoading || !!interrupt;
  return (
    <div
      className={cn(
        'mt-auto flex w-full flex-col gap-4 rounded-xl border border-secondary bg-primary p-4'
      )}
    >
      <textarea
        placeholder="Enter message"
        className="w-full resize-none rounded-md border-none bg-transparent p-2 text-sm outline-none"
        autoFocus={true}
        value={message}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && e.metaKey && !e.shiftKey && !disabled) {
            e.preventDefault();
            onSubmit();
          }
        }}
        onChange={(e) => setMessage(e.target.value)}
        disabled={disabled}
      />
      <div className="flex w-full flex-row items-center gap-2">
        <Switch
          checked={showingToolCalls}
          onChange={() => setShowingToolCalls(!showingToolCalls)}
          label="Show tool calls"
          labelClassName="text-sm font-medium text-tertiary"
        />
        <div className="ml-auto flex items-center justify-end">
          <SubmitButton
            onSubmit={onSubmit}
            isLoading={isLoading}
            disabled={!message || !!interrupt}
          />
        </div>
      </div>
    </div>
  );
};

const SubmitButton = ({
  onSubmit,
  isLoading,
  disabled,
}: {
  onSubmit: () => void;
  isLoading: boolean;
  disabled: boolean;
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <CircularProgress size="sm" />
        <Tooltip title="Cancel" placement="top">
          <button
            className="flex items-center justify-center rounded-full bg-brand-primary p-2.5 hover:bg-brand-primary-hover disabled:bg-brand-secondary"
            onClick={onSubmit}
            type="button"
            aria-label="Send instruction"
          >
            <XIcon size="5" className="text-button-primary" />
          </button>
        </Tooltip>
      </div>
    );
  }
  return (
    <Tooltip title="Send message" placement="top">
      <button
        className="flex items-center justify-center rounded-full bg-brand-primary p-2.5 hover:bg-brand-primary-hover disabled:bg-brand-secondary"
        onClick={onSubmit}
        disabled={disabled}
        type="button"
        aria-label="Send instruction"
      >
        <ArrowUpIcon size="5" className="text-button-primary" />
      </button>
    </Tooltip>
  );
};
