import type { Message } from '@langchain/langgraph-sdk';
import { useStream } from '@langchain/langgraph-sdk/react';
import { LinearProgress } from '@mui/joy';

import { useContext, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

import { useLangGraphClient } from '@/Pages/Graph/src/api';
import { useGraphThreads } from '@/Pages/Graph/src/api/useGraphSwr';
import { useAssistantStore } from '@/Pages/Graph/src/store/assistants/assistantsStore';
import {
  useAssistantSupportsChat,
  useProjectSupportsChat,
} from '@/Pages/HostProject/hooks/useProjectSupportsChat';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { useOrganizationId } from '@/hooks/useSwr';

import { useActiveThreadId } from '../src/hooks/useActiveThreadId';
import { getAuthFetch } from '../src/hooks/useCreateStudioClient';
import { StudioMode, StudioModeContext } from '../src/hooks/useStudioMode';
import { Conversation } from './Conversation';
import { ThreadsList } from './ThreadsList';
import { ChatContext } from './hooks/useChatContext';

const Chat = ({ assistantId }: { assistantId: string }) => {
  const [searchParams] = useSearchParams();
  const hostProjectId = searchParams.get('hostProjectId');
  const tenantId = useOrganizationId();
  const [_, apiUrl, customFetch] = useLangGraphClient();
  const { mutate } = useGraphThreads({});

  const { threadId, setThreadId } = useActiveThreadId();

  const { messagesKey } = useAssistantSupportsChat(assistantId);

  const thread = useStream<{
    messages: Message[];
  }>({
    apiUrl,
    assistantId,
    threadId,
    onThreadId: (threadId) => {
      setThreadId(threadId);
      // todo: this is a hack to ensure the thread is created before the mutate is called
      setTimeout(() => {
        mutate();
      }, 500);
    },
    onFinish: () => {
      mutate();
    },
    messagesKey,
    ...(hostProjectId
      ? {
          callerOptions: { fetch: customFetch ?? getAuthFetch(tenantId) },
          defaultHeaders: {
            'x-auth-scheme': 'langsmith',
          },
        }
      : {}),
  });

  return (
    <ChatContext.Provider value={thread}>
      <div className="flex grow flex-row gap-2 overflow-hidden border border-secondary bg-[#f9fafc] dark:bg-[#1a1c24]">
        <div className="flex-1 p-4 ">
          <Conversation messagesKey={messagesKey} />
        </div>
        <div className="flex w-fit flex-col items-end gap-2">
          <ThreadsList threadId={threadId} setThreadId={setThreadId} />
        </div>
      </div>
    </ChatContext.Provider>
  );
};

export const AgentChat = () => {
  const { supportedGraphs, isLoading } = useProjectSupportsChat();
  const _assistantId = useAssistantStore((state) => state.activeAssistantId);
  const assistantId = _assistantId ?? supportedGraphs?.[0]?.assistant_id;

  const { supportsChat, isLoading: isLoadingAssistantSupportsChat } =
    useAssistantSupportsChat(assistantId);

  const { setStudioMode } = useContext(StudioModeContext);

  useEffect(() => {
    if (!supportsChat && !isLoadingAssistantSupportsChat && assistantId) {
      setStudioMode(StudioMode.GRAPH);
    }
  }, [
    supportsChat,
    isLoadingAssistantSupportsChat,
    setStudioMode,
    assistantId,
  ]);

  if (isLoading || !assistantId)
    return (
      <div>
        <LinearProgress />
      </div>
    );

  return (
    <div className="flex h-full w-full flex-col">
      <ErrorBoundary
        fallback={(error, onRetry) => (
          <div className="flex flex-1 flex-col gap-4 px-8 text-left">
            <span className="flex items-center justify-between gap-2">
              <span className="text-lg font-medium tracking-tighter">
                Failed to render conversation
              </span>
              <button
                type="button"
                onClick={() => {
                  onRetry?.();
                }}
                className="rounded-md border border-primary px-2 py-1 text-sm"
              >
                Retry
              </button>
            </span>
            <ExpandableErrorAlert error={error} />
          </div>
        )}
      >
        <Chat assistantId={assistantId} />
      </ErrorBoundary>
    </div>
  );
};

export default AgentChat;
