import { Interrupt } from '@langchain/langgraph-sdk';
import { PlayCircleIcon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import { useState } from 'react';

import { CodeData } from '@/components/Code/Code';

import { ForeignDataTreeView } from '../src/components/debug/common/foreign-data-tree';
import { foreignDataToTree, parseForeignData } from '../src/data/foreign-data';
import { useChatContext } from './hooks/useChatContext';

export const InterruptForm = ({
  interrupt,
}: {
  interrupt: Interrupt<unknown>;
}) => {
  const thread = useChatContext();

  const [value, setValue] = useState('');

  const checkpoint = thread.history.at(-1)?.checkpoint;

  const onSubmit = () => {
    if (!checkpoint) {
      return;
    }
    thread.submit(undefined, {
      checkpoint,
      command: { resume: value },
    });
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="rounded-md border border-brand-subtle bg-brand-tertiary p-3 text-sm">
        <span className="font-medium uppercase text-brand-primary">
          Interrupt
        </span>

        <div className="mt-1 text-primary">
          <ForeignDataTreeView
            data={foreignDataToTree(parseForeignData(interrupt.value))}
          />
        </div>
      </div>
      <form>
        <div className="rounded-md border border-secondary bg-secondary">
          <span className="text-md flex px-3 py-2 font-medium tracking-tighter">
            Provide a value to resume execution
          </span>
          <div className="h-px w-full border-t border-secondary" />
          <CodeData
            value={value}
            onChange={setValue}
            language="json"
            actions={
              <div className="m-3 flex items-center gap-2">
                <Button
                  type="button"
                  onClick={onSubmit}
                  size="sm"
                  startDecorator={<PlayCircleIcon className="size-5" />}
                >
                  Resume
                </Button>
              </div>
            }
          />
        </div>
      </form>
    </div>
  );
};
