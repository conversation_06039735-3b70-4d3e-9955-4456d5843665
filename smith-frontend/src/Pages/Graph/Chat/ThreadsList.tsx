import { DefaultValues, Thread } from '@langchain/langgraph-sdk';
import { InfoCircleIcon, PlusIcon, XIcon } from '@langchain/untitled-ui-icons';
import { LinearProgress } from '@mui/joy';

import { useEffect, useState } from 'react';

import { useGraphThreads } from '@/Pages/Graph/src/api/useGraphSwr';
import { LoadMoreButton } from '@/components/RunsTable/LoadMoreButton';
import { Tooltip } from '@/components/Tooltip/Tooltip';
import { cn } from '@/utils/tailwind';

import { useGraphAssistants } from '../src/api';
import { checkIsSystemAssistant } from '../src/components/site/Assistants/utils';

export const ThreadsList = ({
  threadId,
  setThreadId,
}: {
  threadId: string | undefined;
  setThreadId: (threadId: string | undefined) => void;
}) => {
  const { threads, isLoading, isValidating, hasMore, size, setSize } =
    useGraphThreads({});

  const [isListOpen, setIsListOpen] = useState(false);
  const { assistants } = useGraphAssistants({});

  useEffect(() => {
    if (threads?.length) {
      setIsListOpen(true);
    }
  }, [threads, setIsListOpen]);

  const getThreadName = (thread: Thread<DefaultValues>) => {
    const { values } = thread;
    const hasMessages =
      !!values &&
      typeof values === 'object' &&
      'messages' in values &&
      Array.isArray(values.messages) &&
      values.messages.length > 0;
    if (!hasMessages) return 'New Thread';
    const firstMessage = Array.isArray(values.messages) && values.messages[0];
    const { content } = firstMessage;
    if (content && typeof content === 'string') {
      return content.trim();
    }
    return thread.thread_id;
  };

  const getThreadAgentName = (thread: Thread<DefaultValues>) => {
    const { metadata } = thread;
    const assistantId = metadata?.assistant_id as string;
    const assistant = assistants?.find((a) => a.assistant_id === assistantId);

    if (checkIsSystemAssistant(assistant)) {
      return `Default${metadata?.graph_id ? ` (${metadata?.graph_id})` : ''}`;
    }

    return assistant?.name ?? metadata?.graph_id?.toString();
  };

  if (isLoading)
    return (
      <div className="min-w-[200px]">
        <LinearProgress />
      </div>
    );

  if (!isListOpen) {
    return (
      <div className="m-4 flex flex-row gap-2">
        <button
          type="button"
          onClick={() => setIsListOpen(true)}
          className="flex flex-row items-center gap-2 rounded-md border border-primary bg-primary px-3 py-1.5 hover:bg-primary-hover"
        >
          <span className="text-xs font-medium text-primary">Threads</span>
        </button>
        {threadId !== undefined && (
          <button
            onClick={() => setThreadId(undefined)}
            type="button"
            className={cn(
              'flex flex-row items-center gap-1 rounded-md border border-primary bg-primary px-2 py-0.5',
              threadId === undefined && 'bg-primary-hover',
              'hover:bg-primary-hover'
            )}
          >
            <Tooltip title="New Thread">
              <PlusIcon className=" size-4" />
            </Tooltip>
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="flex h-full w-[250px] flex-col gap-3 bg-primary p-4">
      <div className="flex flex-row items-center gap-2">
        <span className="text-sm font-semibold text-secondary">Threads</span>
        <div className="flex grow flex-row items-center justify-end gap-2">
          <button
            onClick={() => setThreadId(undefined)}
            type="button"
            className={cn(
              'flex flex-row items-center gap-1 rounded-md border border-primary px-2 py-0.5',
              threadId === undefined && 'bg-primary-hover',
              'hover:bg-primary-hover',
              'disabled:cursor-not-allowed disabled:opacity-50'
            )}
            disabled={threadId === undefined}
          >
            <PlusIcon className=" size-4" />

            <span className="line-clamp-1 text-xxs font-medium text-secondary">
              New
            </span>
          </button>
          <button
            onClick={() => setIsListOpen(false)}
            type="button"
            className="rounded-md p-1 text-sm font-semibold text-secondary hover:bg-secondary"
          >
            <XIcon className="size-4" />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {threads?.length ? (
          <>
            {threads?.map((thread) => (
              <button
                key={thread.thread_id}
                onClick={() => setThreadId(thread.thread_id)}
                type="button"
                className={cn(
                  'group flex w-full items-center justify-between gap-1 overflow-hidden rounded-md p-2 py-3 font-medium',
                  thread.thread_id === threadId && 'bg-tertiary',
                  'hover:bg-tertiary',
                  'h-fit'
                )}
              >
                <span className="line-clamp-1 text-left text-xs font-medium text-secondary ">
                  {getThreadName(thread)}
                </span>
                <Tooltip
                  title={
                    <div className="flex flex-col gap-1 text-xxs">
                      <div className="flex flex-row gap-1">
                        <span className="font-semibold">Agent:</span>
                        <span>{getThreadAgentName(thread)}</span>
                      </div>
                      <div className="flex flex-row gap-1">
                        <span className="font-semibold">Thread ID:</span>
                        <span>{thread.thread_id}</span>
                      </div>
                    </div>
                  }
                  placement="left"
                >
                  <InfoCircleIcon className="size-4 text-tertiary" />
                </Tooltip>
              </button>
            ))}
            {hasMore && (
              <LoadMoreButton
                isInitialLoad
                isLoading={isValidating}
                onClick={() => setSize(size + 1)}
                className="text-sm text-secondary"
              />
            )}
          </>
        ) : (
          <div className="text-center text-sm text-secondary">
            No threads found.
          </div>
        )}
      </div>
    </div>
  );
};
