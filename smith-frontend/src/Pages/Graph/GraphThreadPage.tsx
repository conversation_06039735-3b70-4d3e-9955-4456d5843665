import { LinearProgress } from '@mui/joy';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import Banner from '@/components/Banner';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';

import { useGraphAssistant, useLangGraphClient } from './src/api';
import {
  useGraphAssistantDebugGraph,
  useGraphServerInfo,
} from './src/api/useGraphSwr';
import { GraphDebugger } from './src/components/debug/GraphDebugger';
import { GraphDatasetProvider } from './src/components/debug/dataset/GraphDatasetProvider';
import { GraphDataContext } from './src/control/graph-data';
import {
  RenderModeContext,
  StateContext,
  getStoredBreakpoints,
  useRenderModeCtx,
  useStateCtx,
} from './src/control/state';
import { useAssistantStore } from './src/store/assistants/assistantsStore';
import { isHostedUrl } from './src/utils';

function StatefulThreadPage(props: { threadId?: string; assistantId: string }) {
  const breakpoints = getStoredBreakpoints(props.assistantId);
  const state = useStateCtx({
    initRunState: props.threadId
      ? { type: 'completed', threadId: props.threadId }
      : { type: 'empty' },
    initBreakpoints: breakpoints,
  });
  const renderMode = useRenderModeCtx({});

  const assistantId = useAssistantStore((state) => state.activeAssistantId);

  const graph = useGraphAssistantDebugGraph(assistantId);

  return (
    <StateContext.Provider value={state}>
      <RenderModeContext.Provider value={renderMode}>
        <GraphDataContext.Provider value={graph.data}>
          <GraphDatasetProvider>
            <GraphDebugger
              assistantId={assistantId ?? props.assistantId}
              graph={graph}
            />
          </GraphDatasetProvider>
        </GraphDataContext.Provider>
      </RenderModeContext.Provider>
    </StateContext.Provider>
  );
}

export const GraphThreadPage = () => {
  const [_, apiUrl] = useLangGraphClient();
  // threadId will be only reflected when loading the page
  // the actual URL might change esp. when the thread is newly created
  const [searchParams] = useSearchParams();
  const threadId = searchParams.get('threadId') ?? undefined;

  const apiInfo = useGraphServerInfo();
  const missingLangSmithKey = apiInfo.data?.flags?.langsmith === false;

  const [showLangSmithKeyWarning, setShowLangSmithKeyWarning] = useState(
    !isHostedUrl(apiUrl) && missingLangSmithKey
  );
  useEffect(() => {
    setShowLangSmithKeyWarning(!isHostedUrl(apiUrl) && missingLangSmithKey);
  }, [apiUrl, missingLangSmithKey]);

  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const {
    data: assistant,
    error: assistantError,
    isLoading: assistantLoading,
  } = useGraphAssistant(assistantId, {
    keepPreviousData: true,
  });

  if (assistantError) {
    return (
      <ExpandableErrorAlert
        error={
          'Failed to fetch assistant. Please try selecting a different assistant and make sure the API server is running.'
        }
      />
    );
  }

  if (assistantLoading && !assistant) {
    return (
      <div className="flex w-full items-start justify-center">
        <LinearProgress />
      </div>
    );
  }

  if (!assistant) return null;

  return (
    <div className="flex h-full w-full flex-col">
      <Banner
        title="Not seeing LangSmith runs?"
        description={
          <span className="text-sm text-primary">
            It looks like your LangSmith API key is missing. Please make sure to
            add{' '}
            <span className="font-mono text-xs font-bold">
              LANGSMITH_API_KEY
            </span>{' '}
            to your local server's{' '}
            <span className="font-mono text-xs font-bold">.env</span> file.
          </span>
        }
        closeable
        open={showLangSmithKeyWarning}
        className="m-4"
      />
      <StatefulThreadPage
        threadId={threadId}
        assistantId={assistant.assistant_id}
      />
    </div>
  );
};
