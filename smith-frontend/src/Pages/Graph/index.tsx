import { useContext, useMemo } from 'react';

import { cn } from '@/utils/tailwind';

import { AgentChat } from './Chat';
import { GraphThreadPage } from './GraphThreadPage';
import {
  InputHistoryContext,
  useInputHistory,
} from './src/components/debug/trace/hooks/useInputHistory';
import { StudioMode, StudioModeContext } from './src/hooks/useStudioMode';

export const StudioPage = () => {
  const { studioMode: mode } = useContext(StudioModeContext);

  const body = useMemo(() => {
    return (
      <>
        <div
          className={cn(
            'duration-50 absolute h-full w-full transition-opacity ease-in-out',
            mode === StudioMode.CHAT
              ? 'opacity-100'
              : 'pointer-events-none opacity-0'
          )}
        >
          {mode === StudioMode.CHAT && <AgentChat />}
        </div>
        <div
          className={cn(
            'duration-50 absolute h-full w-full transition-opacity ease-in-out',
            mode === StudioMode.GRAPH
              ? 'opacity-100'
              : 'pointer-events-none z-[-1] opacity-0'
          )}
        >
          {mode === StudioMode.GRAPH && <GraphThreadPage />}
        </div>
      </>
    );
  }, [mode]);
  const inputHistory = useInputHistory();

  return (
    <InputHistoryContext.Provider value={inputHistory}>
      {body}
    </InputHistoryContext.Provider>
  );
};
