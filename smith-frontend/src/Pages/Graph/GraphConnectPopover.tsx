import {
  ChevronRightIcon,
  PlusIcon,
  Trash01Icon,
} from '@langchain/untitled-ui-icons';
import { Button, ButtonGroup, IconButton, Input, Tooltip } from '@mui/joy';

import { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDebouncedCallback } from 'use-debounce';
import { z } from 'zod';

import GraphFillIcon from '@/Pages/Graph/icons/GraphFillIcon.svg?react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { SecretPlainInput } from '@/components/SecretInput';
import ExternalLinkIcon from '@/icons/ExternalLinkIcon.svg?react';
import { RunSchema } from '@/types/schema';
import { cn, utils } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';
import { useGetGraphStudioPath } from '@/utils/useGetGraphStudioPath';

import { useGraphConnection } from './src/api/useGraphSwr';
import {
  useConvertTraceToThread,
  useGetGraphs,
} from './src/hooks/useConvertTraceToThread';
import { useCreateStudioClient } from './src/hooks/useCreateStudioClient';

const INCOMPATIBLE_SCHEMA_ERROR =
  'Cloning is disabled because your local graph has different nodes than the remote graph.';

export function GraphConnectHeadersInput(props: {
  value: Record<string, string>;
  onChange: (value: Exclude<Record<string, string>, undefined>) => void;
}) {
  const [innerValue, setInnerValue] = useState(
    Object.entries(props.value ?? {})
  );

  const onChange = (
    value:
      | [key: string, value: string]
      | Array<[key: string, value: string]>
      | null,
    index?: number
  ) => {
    const isList = (x: unknown): x is Array<[string, string]> => {
      return Array.isArray(x) && x.every((x) => Array.isArray(x));
    };

    setInnerValue((prev) => {
      let newValue = prev.slice(0);
      if (index == null) {
        if (value != null) {
          if (isList(value)) {
            newValue = value;
          } else {
            newValue.push(value);
          }
        }
      } else {
        if (value != null) {
          if (isList(value)) {
            newValue.splice(index, 1, ...value);
          } else {
            newValue[index] = value;
          }
        } else {
          newValue.splice(index, 1);
        }
      }

      props.onChange(Object.fromEntries(newValue.filter((i) => i[0] !== '')));
      return newValue;
    });
  };

  const firstMount = useRef(false);
  if (!firstMount.current) {
    if (innerValue.length === 0) onChange(['', '']);
    firstMount.current = true;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          type="button"
          className="w-fit text-left text-sm text-tertiary hover:underline"
        >
          {Object.keys(props.value ?? {}).length > 0
            ? 'Edit custom headers'
            : 'Add custom headers'}
        </button>
      </PopoverTrigger>
      <PopoverContent className="min-w-[420px] pt-3">
        <div className="flex flex-col gap-3">
          <div className="flex flex-col gap-1">
            <h4 className="text-md font-medium">Custom headers</h4>
            <p className="text-sm text-tertiary">
              Values will be stored locally in your browser and applied to all
              local connections regardless of the endpoint.
            </p>
          </div>
          <div className="grid grid-cols-[1fr,1fr,auto] items-end gap-2 empty:hidden">
            {innerValue.map(([key, value], idx) => (
              <Fragment key={idx}>
                <input
                  type="text"
                  className="min-w-0 rounded-md border border-secondary bg-transparent px-2.5 py-2 text-sm focus:border-brand"
                  value={key}
                  onChange={(e) => onChange([e.target.value, value], idx)}
                  placeholder="Type name..."
                />

                <SecretPlainInput
                  type="text"
                  value={value}
                  onChange={(e) => onChange([key, e.target.value], idx)}
                  placeholder="Type value..."
                  required
                />

                <button
                  type="button"
                  aria-label="Delete"
                  onClick={() => onChange(null, idx)}
                  className="flex h-[38px] w-[38px] items-center justify-center rounded-md border border-secondary hover:bg-secondary-hover active:bg-secondary"
                >
                  <Trash01Icon className="h-5 w-5" />
                </button>
              </Fragment>
            ))}
          </div>
          <div className="flex items-center justify-end gap-4">
            <button
              type="button"
              className="inline-flex items-center gap-2 self-end rounded-md border border-secondary bg-transparent px-2.5 py-1.5 text-sm hover:bg-secondary-hover active:bg-secondary"
              onClick={() => onChange(['', ''])}
            >
              <PlusIcon className="h-5 w-5" />
              <span>Add</span>
            </button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

const GraphCloneThreadButton = ({
  connectionError,
  baseUrl,
  lastStudioUrl,
  localServerDisabled,
  headers,
  rootRun,
}: {
  connectionError?: boolean;
  baseUrl: string | undefined;
  lastStudioUrl: string | undefined;
  localServerDisabled?: boolean;
  headers: Record<string, string>;
  rootRun?: RunSchema;
}) => {
  const navigate = useNavigate();
  const { generatePath } = useGetGraphStudioPath();
  const client = useCreateStudioClient({
    projectId: undefined,
    standalone: true,
    baseUrl,
    headers,
  });

  const { createThreadWithStateMutation, isSchemaCompatible, stateUpdates } =
    useConvertTraceToThread({
      studioClient: client?.[0],
      rootRun,
      lastStudioUrl,
      diffUrl: lastStudioUrl !== baseUrl,
    });
  const { trigger: createThreadWithState, isMutating: isCreatingThread } =
    createThreadWithStateMutation;

  const onOpenStudioWithNewThread = async (graphId: string) => {
    const thread = await createThreadWithState({ graphId });
    if (!thread) return;
    navigate(generatePath({ threadId: thread.thread_id }));
  };

  const { data: graphs } = useGetGraphs({
    client: client?.[0],
    apiUrl: lastStudioUrl,
  });
  const compatibleGraphs = useMemo(() => {
    if (!graphs) return [];
    return graphs.filter((graph) => isSchemaCompatible(graph.nodes));
  }, [graphs, isSchemaCompatible]);

  const renderButton = () => {
    if (compatibleGraphs.length > 1) {
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="solid" color="primary">
              <span className="text-sm">Clone thread locally</span>
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            side="right"
            align="start"
            sideOffset={16}
            className="w-full flex-col px-4 py-2"
          >
            <span className="text-xs font-medium text-tertiary">
              SELECT A GRAPH
            </span>
            <div className="flex flex-col items-start gap-2">
              {compatibleGraphs.map((graph) => (
                <button
                  key={graph.graph_id}
                  type="button"
                  onClick={() => onOpenStudioWithNewThread(graph.graph_id)}
                  className="w-full cursor-pointer rounded-md py-1 text-left hover:bg-secondary"
                >
                  <span className="text-sm">{graph.graph_id}</span>
                </button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      );
    }
    const graph = compatibleGraphs.length ? compatibleGraphs[0] : undefined;
    const isCompatible = graph ? isSchemaCompatible(graph.nodes) : false;
    return (
      <Button
        variant="solid"
        color="primary"
        disabled={
          localServerDisabled || !isCompatible || stateUpdates.length === 0
        }
        loading={isCreatingThread}
        {...(graph && {
          onClick: () => onOpenStudioWithNewThread(graph.graph_id),
        })}
      >
        <span className="text-sm">Clone thread locally</span>
      </Button>
    );
  };

  return (
    <Tooltip
      title={
        compatibleGraphs.length === 0 && !connectionError
          ? INCOMPATIBLE_SCHEMA_ERROR
          : ''
      }
    >
      <div>{renderButton()}</div>
    </Tooltip>
  );
};

const GraphConnectForm = ({
  defaultHeaders,
  baseUrl,
  setBaseUrl,
  lastStudioUrl,
  setLastStudioUrl,
  onSubmit,
  openExistingGraphDisabled,
  showAdvancedFooter,
  connectionError,
  connected,
  rootRun,
}: {
  defaultHeaders: Record<string, string>;
  baseUrl: string | undefined;
  setBaseUrl: (baseUrl: string) => void;
  lastStudioUrl: string | undefined;
  setLastStudioUrl: (lastStudioUrl: string) => void;
  onSubmit: (headers?: Record<string, string>) => void;
  openExistingGraphDisabled?: boolean;
  showAdvancedFooter?: boolean;
  connectionError?: boolean;
  connected?: boolean;
  rootRun?: RunSchema;
}) => {
  const [headers, setHeaders] = useState(defaultHeaders ?? {});
  const debouncedSetLastStudioUrl = useDebouncedCallback(setLastStudioUrl, 500);

  useEffect(() => {
    // on dismount, set the last studio url to the base url
    return () => {
      setLastStudioUrl(baseUrl ?? '');
    };
  }, [baseUrl, setLastStudioUrl]);

  const localServerDisabled = !connected || !baseUrl;

  return (
    <>
      <div className="flex flex-col gap-1.5 border-b border-secondary p-4">
        <p>
          <strong className="text-md font-medium">LangGraph Studio</strong>
        </p>
        <p className="pr-10 text-sm text-tertiary">
          Agent IDE for prototyping and debugging LangGraph applications.{' '}
          <a
            href="https://langchain-ai.github.io/langgraph/concepts/langgraph_studio"
            target="_blank"
            className="inline-flex items-center gap-1 text-brand-green-400 focus-within:underline hover:underline"
            rel="noreferrer"
          >
            Learn more
            <ExternalLinkIcon className="h-4 w-4" />
          </a>
        </p>
      </div>

      <form
        className="flex flex-col items-stretch gap-2 p-4"
        onSubmit={(e) => {
          e.preventDefault();
          onSubmit(headers);
        }}
      >
        <p className="text-sm text-secondary">
          Enter your local endpoint below to connect:
        </p>
        <div>
          <Input
            type="text"
            placeholder="http://localhost:8123"
            value={baseUrl}
            onChange={(e) => {
              setBaseUrl(e.target.value);
              debouncedSetLastStudioUrl(e.target.value);
            }}
            name="baseUrl"
            error={connectionError}
          />
          {connectionError && (
            <span className="text-xs text-error">
              Connection failed. Ensure your server is running at this endpoint.
            </span>
          )}
        </div>
        {!showAdvancedFooter && (
          <Button type="submit" variant="solid" disabled={localServerDisabled}>
            Connect
          </Button>
        )}
        <GraphConnectHeadersInput
          value={headers}
          onChange={(headers) => setHeaders(headers)}
        />
        {showAdvancedFooter && (
          <div className="flex w-full flex-row justify-between gap-2 pt-2">
            <Tooltip
              title={
                openExistingGraphDisabled &&
                "Can't find original thread's host project."
              }
            >
              <div>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => onSubmit()}
                  disabled={openExistingGraphDisabled}
                >
                  <span className="text-sm">View original thread</span>
                </Button>
              </div>
            </Tooltip>

            <GraphCloneThreadButton
              baseUrl={baseUrl}
              lastStudioUrl={lastStudioUrl}
              headers={defaultHeaders}
              connectionError={connectionError}
              localServerDisabled={localServerDisabled}
              rootRun={rootRun}
            />
          </div>
        )}
      </form>
    </>
  );
};

const GraphConnectButton = ({
  size,
  buttonWidth,
  buttonLabel,
  onClick,
  connected,
  supportsCloneAndOpenExisting,
}: {
  size?: 'sm' | 'md';
  buttonWidth?: 'sm' | 'md';
  buttonLabel?: string;
  onClick: () => void;
  connected: boolean;
  supportsCloneAndOpenExisting?: boolean;
}) => {
  const connectionDot = useMemo(() => {
    if (connected) {
      return (
        <Tooltip title="Connected to server">
          <span className="size-2 rounded-full bg-success-strong" />
        </Tooltip>
      );
    }
    return (
      <Tooltip title="Disconnected from server">
        <span className="size-2 rounded-full bg-quaternary" />
      </Tooltip>
    );
  }, [connected]);

  if (buttonWidth === 'sm') {
    const button = (
      <Button variant="outlined" color="primary" size={size}>
        {connectionDot}
      </Button>
    );
    if (supportsCloneAndOpenExisting) {
      return <PopoverTrigger asChild>{button}</PopoverTrigger>;
    }
    return { ...button, onClick };
  }

  if (connected) {
    return (
      <ButtonGroup variant="outlined" color="primary" size={size}>
        <Button
          type="button"
          variant="outlined"
          className="flex items-center gap-2.5"
          onClick={onClick}
        >
          {connectionDot}
          <span>{buttonLabel ?? 'LangGraph Studio'}</span>
        </Button>
        <PopoverTrigger asChild>
          <IconButton className="min-w-8">
            <ChevronRightIcon className="h-4 w-4 rotate-90" />
          </IconButton>
        </PopoverTrigger>
      </ButtonGroup>
    );
  }

  return (
    <PopoverTrigger asChild>
      <Button
        type="button"
        variant="outlined"
        className="flex items-center gap-2.5"
        size={size}
        color={connected ? 'primary' : 'neutral'}
      >
        {connectionDot}
        <span>{buttonLabel ?? 'LangGraph Studio'}</span>
      </Button>
    </PopoverTrigger>
  );
};

export const GraphConnectPopover = ({
  size,
  buttonWidth,
  rootRun,
  buttonLabel,
  connectionTypes,
  graphUrl,
}: {
  size?: 'sm' | 'md';
  buttonWidth?: 'sm' | 'md';
  rootRun?: RunSchema;
  buttonLabel?: string;
  connectionTypes?: ('local' | 'deployed')[];
  graphUrl?: string | null;
}) => {
  const supportsCloneAndOpenExisting = (connectionTypes?.length ?? 0) > 1;
  const navigate = useNavigate();
  const hostProjectId =
    rootRun?.extra?.metadata?.['LANGSMITH_HOST_PROJECT_ID'] ??
    rootRun?.extra?.metadata?.['langsmith_host_project_id'];

  const { generatePath, lastStudioUrl, setLastStudioUrl } =
    useGetGraphStudioPath();

  const { error: lastActiveError, data: lastActive } = useGraphConnection({
    studioUrl: lastStudioUrl,
  });

  const [localHeaders, setLocalHeaders] = useLocalStorageState(
    'ls:studio:connectLocalHeaders',
    {},
    z.record(z.string(), z.string())
  );
  const [baseUrl, setBaseUrl] = useState(lastStudioUrl);

  const openExistingThread = () => {
    navigate(
      generatePath({
        threadId: rootRun?.extra?.metadata?.thread_id,
        hostProjectId,
      })
    );
  };

  return (
    <Popover>
      <GraphConnectButton
        size={size}
        buttonWidth={buttonWidth}
        buttonLabel={buttonLabel}
        onClick={openExistingThread}
        connected={hostProjectId || lastActive}
        supportsCloneAndOpenExisting={supportsCloneAndOpenExisting}
      />
      <PopoverContent
        align="end"
        className="flex w-full max-w-[435px] flex-col p-0"
      >
        <GraphConnectForm
          defaultHeaders={localHeaders}
          baseUrl={baseUrl}
          setBaseUrl={setBaseUrl}
          lastStudioUrl={lastStudioUrl}
          setLastStudioUrl={setLastStudioUrl}
          onSubmit={(headers) => {
            setLastStudioUrl(baseUrl);
            if (headers) {
              setLocalHeaders(headers);
            }
            openExistingThread();
          }}
          openExistingGraphDisabled={!graphUrl}
          showAdvancedFooter={supportsCloneAndOpenExisting}
          connected={!!lastActive}
          connectionError={!!lastActiveError}
          rootRun={rootRun}
        />
      </PopoverContent>
    </Popover>
  );
};

export const NavLinksGraphConnectPopover = (props: { expanded: boolean }) => {
  const navigate = useNavigate();
  const { lastStudioUrl, setLastStudioUrl, generatePath } =
    useGetGraphStudioPath();
  const [baseUrl, setBaseUrl] = useState(lastStudioUrl);

  const { error: lastActiveError, data: lastActive } = useGraphConnection({
    studioUrl: lastStudioUrl,
  });

  const [localHeaders, setLocalHeaders] = useLocalStorageState(
    'ls:studio:connectLocalHeaders',
    {},
    z.record(z.string(), z.string())
  );

  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      {props.expanded ? (
        <PopoverTrigger
          className={cn(
            utils.button,
            'flex w-full select-none items-center gap-1.5 rounded-none px-3 py-1.5 text-xs text-secondary outline-none'
          )}
        >
          <GraphFillIcon className="h-4 w-4" />
          <div className="line-clamp-1 font-semibold">LangGraph Studio</div>
        </PopoverTrigger>
      ) : (
        <Tooltip title="LangGraph Studio" placement="right">
          <div className="flex w-full items-center justify-center gap-0">
            <PopoverTrigger
              className={cn(
                utils.button,
                'flex flex-col items-center rounded-md p-2'
              )}
            >
              <GraphFillIcon className="h-4 w-4" />
            </PopoverTrigger>
          </div>
        </Tooltip>
      )}
      <PopoverContent
        side="right"
        align="start"
        sideOffset={!props.expanded ? 12 : undefined}
        className="flex w-full max-w-[360px] flex-col p-0"
      >
        <GraphConnectForm
          baseUrl={baseUrl}
          setBaseUrl={setBaseUrl}
          lastStudioUrl={lastStudioUrl}
          setLastStudioUrl={setLastStudioUrl}
          defaultHeaders={localHeaders}
          onSubmit={(headers) => {
            setIsOpen(false);
            setLastStudioUrl(baseUrl);
            if (headers) {
              setLocalHeaders(headers);
            }
            navigate(generatePath({}));
          }}
          connected={!!lastActive}
          connectionError={!!lastActiveError}
        />
      </PopoverContent>
    </Popover>
  );
};
