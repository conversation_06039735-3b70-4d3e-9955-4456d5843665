import { LinearProgress, Link } from '@mui/joy';

import { useMemo } from 'react';
import { Outlet, useSearchParams } from 'react-router-dom';
import { z } from 'zod';

import Banner from '@/components/Banner';
import { MultiToastProvider } from '@/components/Toast';
import { LanguagePreferenceProvider } from '@/hooks/useLanguagePreferenceContext';

import { LangGraphContext, StudioClient } from './src/api';
import { useGraphThread } from './src/api/useGraphSwr';
import {
  InputHistoryContext,
  useInputHistory,
} from './src/components/debug/trace/hooks/useInputHistory';
import { GraphTopBar } from './src/components/site/GraphTopBar';
import { GraphInitializeError } from './src/components/site/error/GraphInitializeError';
import { GraphDesktop } from './src/desktop/GraphDesktop';
import { useAssistantToInitialize } from './src/hooks/assistants/useAssistantToInitialize';
import { useInitializeAssistantState } from './src/hooks/assistants/useInitializeAssistantState';
import { StudioModeContext, useStudioMode } from './src/hooks/useStudioMode';

const defaultGraphUrl =
  window.studio_local_graph_url ??
  import.meta.env.VITE_STUDIO_LOCAL_GRAPH_URL ??
  'http://127.0.0.1:8123';

const defaultGraphHeadersStr =
  window.studio_custom_headers ||
  import.meta.env.VITE_STUDIO_CUSTOM_HEADERS ||
  '{}';

function GraphStandalone() {
  const [searchParams] = useSearchParams();
  const baseUrl = searchParams.get('baseUrl');

  const apiUrl = baseUrl ?? defaultGraphUrl;
  const client = useMemo(() => {
    let parsedHeaders: Record<
      string,
      string | number | boolean | null | undefined
    > = {};
    try {
      parsedHeaders = z
        .record(
          z.union([
            z.string(),
            z.number(),
            z.boolean(),
            z.null(),
            z.undefined(),
          ])
        )
        .parse(JSON.parse(defaultGraphHeadersStr));
    } catch (error) {
      console.error('Failed to parse custom headers', error);
    }
    const defaultHeaders: Record<string, string | null | undefined> = {};
    for (const [key, value] of Object.entries(parsedHeaders)) {
      defaultHeaders[key] = value != null ? String(value) : value;
    }

    return [new StudioClient({ apiUrl, defaultHeaders }), apiUrl] as [
      StudioClient,
      string
    ];
  }, [apiUrl]);

  const { studioMode, setStudioMode } = useStudioMode();

  const inputHistory = useInputHistory();

  return (
    <LanguagePreferenceProvider>
      <LangGraphContext.Provider value={client}>
        <StudioModeContext.Provider value={{ studioMode, setStudioMode }}>
          <InputHistoryContext.Provider value={inputHistory}>
            <div className="grid h-[100svh] grid-rows-[auto,1fr]">
              <div className="px-4 py-3">
                <GraphTopBar standalone={true} />
              </div>
              <GraphLayoutInner />
            </div>
          </InputHistoryContext.Provider>
        </StudioModeContext.Provider>
      </LangGraphContext.Provider>
    </LanguagePreferenceProvider>
  );
}

function GraphLayoutInner() {
  const [searchParams] = useSearchParams();
  const threadId = searchParams.get('threadId') ?? undefined;
  const thread = useGraphThread(threadId);
  const {
    assistant,
    intitializeError,
    initializeLoading,
    fetchedAssistant,
    onAssistantIdChange,
  } = useAssistantToInitialize({
    graphId: thread.data?.metadata?.graph_id,
  });

  useInitializeAssistantState({
    initAssistantId: assistant?.assistant_id,
    onAssistantIdChange,
  });

  if (intitializeError) {
    return (
      <div className="flex flex-col gap-4 p-4">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-medium tracking-tighter">
            Failed to load assistants
          </h2>
          <p className="text-sm text-secondary">
            Please verify if the API server is running or accessible from the
            browser.
          </p>
        </div>
        <GraphInitializeError error={intitializeError} />
      </div>
    );
  }

  if (initializeLoading && !fetchedAssistant) {
    return (
      <div className="flex w-full items-start justify-center">
        <LinearProgress />
      </div>
    );
  }

  return (
    <div className="relative">
      <Outlet />
    </div>
  );
}

export function GraphLayout() {
  const renderBody = () => {
    if (window.__electron_preload__is_langgraph_desktop) {
      return (
        <>
          <Banner
            open={true}
            closeable={true}
            title="Studio desktop app is moving to the browser!"
            description={
              <div className="flex flex-col gap-2">
                <span className="text-xs text-primary">
                  LangGraph Studio nows run directly in your browser for
                  cross-platform support. The MacOS desktop app will no longer
                  receive updates. Use the 'langgraph dev' command to start the
                  server locally and access Studio within LangSmith.
                </span>
                <Link
                  href="https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/#getting-started"
                  target="_blank"
                  color="neutral"
                  rel="noreferrer"
                >
                  <span className="text-xs font-medium text-primary underline">
                    View setup guide
                  </span>
                </Link>
              </div>
            }
            className="m-4 mb-0"
          />
          <LanguagePreferenceProvider>
            <MultiToastProvider>
              <GraphDesktop />
            </MultiToastProvider>
          </LanguagePreferenceProvider>
        </>
      );
    }

    return <GraphStandalone />;
  };

  return <div className="flex h-full w-full flex-col">{renderBody()}</div>;
}
