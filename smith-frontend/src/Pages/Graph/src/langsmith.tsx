import { ReactNode, createContext } from 'react';

import { GraphPlaygroundPaneProps } from './components/debug/trace/GraphPlaygroundPane';
import { TraceLog } from './data/misc';

export const LangSmithAwareContext = createContext<{
  TraceLogRow?: (props: { traceLog: TraceLog }) => ReactNode;
  PlaygroundPane?: (props: GraphPlaygroundPaneProps) => ReactNode;
  ExperimentModalAndButton?: () => ReactNode;
}>({});
