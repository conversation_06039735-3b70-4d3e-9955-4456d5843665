import { renderHook } from '@testing-library/react';

import { useContext } from 'react';
import { useSearchParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  useAssistantSupportsChat,
  useProjectSupportsChat,
} from '@/Pages/HostProject/hooks/useProjectSupportsChat';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { useGraphAssistant, useGraphAssistants } from '../../api/assistants';
import { useAssistantToInitialize } from '../assistants/useAssistantToInitialize';

vi.mock('react-router-dom', () => ({
  useSearchParams: vi.fn(),
  useNavigate: vi.fn(),
}));

vi.mock('@/Pages/HostProject/hooks/useProjectSupportsChat', () => ({
  useProjectSupportsChat: vi.fn(),
  useAssistantSupportsChat: vi.fn(),
}));

vi.mock('@/utils/use-local-storage-state', () => ({
  useLocalStorageState: vi.fn(),
}));

vi.mock('../../api/assistants', () => ({
  useGraphAssistant: vi.fn(),
  useGraphAssistants: vi.fn(),
}));

vi.mock('../useStudioMode', () => ({
  StudioModeContext: {
    Provider: vi.fn(),
  },
  StudioMode: {
    GRAPH: 'graph',
    CHAT: 'chat',
  },
}));

vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useContext: vi.fn(),
  };
});

describe('useAssistantToInitialize', () => {
  const mockGraphId = 'mock-graph-id';

  const mockSystemAssistant = {
    assistant_id: 'system-assistant-id',
    graph_id: mockGraphId,
    metadata: {
      created_by: 'system',
    },
  };

  const mockCustomAssistant = {
    assistant_id: 'custom-assistant-id',
    graph_id: mockGraphId,
    metadata: {},
  };

  const mockSupportedAssistant = {
    assistant_id: 'supported-assistant-id',
  };

  beforeEach(() => {
    vi.resetAllMocks();

    (useGraphAssistants as any).mockReturnValue({
      assistants: [mockSystemAssistant, mockCustomAssistant],
      error: null,
      isLoading: false,
    });

    (useProjectSupportsChat as any).mockReturnValue({
      supportedGraphs: [mockSupportedAssistant],
      isLoading: false,
    });

    (useAssistantSupportsChat as any).mockImplementation((id) => ({
      supportsChat: id === 'supported-assistant-id',
    }));

    (useGraphAssistant as any).mockImplementation((id) => ({
      data: id ? { assistant_id: id } : undefined,
      error: null,
      isLoading: false,
    }));
    (useSearchParams as any).mockReturnValue([{ get: vi.fn() }]);

    const setLocalStorageAssistantId = vi.fn();
    (useLocalStorageState as any).mockReturnValue([
      null,
      setLocalStorageAssistantId,
    ]);
  });

  describe('studioMode = graph', () => {
    beforeEach(() => {
      (useContext as any).mockImplementation(() => ({
        studioMode: 'graph',
      }));
    });

    it('should use assistantId from search params if available', () => {
      const searchParamsGet = vi
        .fn()
        .mockReturnValue('search-param-assistant-id');
      (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);

      const { result } = renderHook(() =>
        useAssistantToInitialize({ graphId: mockGraphId })
      );

      expect(useGraphAssistant).toHaveBeenCalledWith(
        'search-param-assistant-id',
        expect.anything()
      );
      expect(result.current.assistant?.assistant_id).toBe(
        'search-param-assistant-id'
      );
    });

    it('should use localStorageAssistantId if search params assistantId is not available', () => {
      const searchParamsGet = vi.fn().mockReturnValue(null);
      (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);
      (useLocalStorageState as any).mockReturnValue([
        'local-storage-assistant-id',
        vi.fn(),
      ]);

      const { result } = renderHook(() =>
        useAssistantToInitialize({ graphId: mockGraphId })
      );

      expect(useGraphAssistant).toHaveBeenCalledWith(
        'local-storage-assistant-id',
        expect.anything()
      );
      expect(result.current.assistant?.assistant_id).toBe(
        'local-storage-assistant-id'
      );
    });

    it('should use system assistant when neither search params nor localStorage has assistantId', () => {
      const searchParamsGet = vi.fn().mockReturnValue(null);
      (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);
      (useLocalStorageState as any).mockReturnValue([null, vi.fn()]);

      const { result } = renderHook(() =>
        useAssistantToInitialize({ graphId: mockGraphId })
      );

      expect(result.current.assistant).toBe(mockSystemAssistant);
    });

    describe('with undefined graphId', () => {
      it('should use assistantId from search params if available', () => {
        const searchParamsGet = vi
          .fn()
          .mockReturnValue('search-param-assistant-id');
        (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);

        const { result } = renderHook(() =>
          useAssistantToInitialize({ graphId: undefined })
        );

        expect(useGraphAssistant).toHaveBeenCalledWith(
          'search-param-assistant-id',
          expect.anything()
        );
        expect(result.current.assistant?.assistant_id).toBe(
          'search-param-assistant-id'
        );
      });

      it('should use localStorageAssistantId if search params assistantId is not available', () => {
        const searchParamsGet = vi.fn().mockReturnValue(null);
        (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);
        (useLocalStorageState as any).mockReturnValue([
          'local-storage-assistant-id',
          vi.fn(),
        ]);

        const { result } = renderHook(() =>
          useAssistantToInitialize({ graphId: undefined })
        );

        expect(useGraphAssistant).toHaveBeenCalledWith(
          'local-storage-assistant-id',
          expect.anything()
        );
        expect(result.current.assistant?.assistant_id).toBe(
          'local-storage-assistant-id'
        );
      });

      it('should fall back to the last assistant when neither search params nor localStorage has assistantId', () => {
        const searchParamsGet = vi.fn().mockReturnValue(null);
        (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);
        (useLocalStorageState as any).mockReturnValue([null, vi.fn()]);

        const { result } = renderHook(() =>
          useAssistantToInitialize({ graphId: undefined })
        );

        // Since no graphId to match, it should fall back to the last assistant
        expect(result.current.assistant).toBe(mockCustomAssistant);
      });
    });
  });

  describe('studioMode = chat', () => {
    beforeEach(() => {
      (useContext as any).mockImplementation(() => ({
        studioMode: 'chat',
      }));
    });

    it('should check if default assistant is supported for chat mode', () => {
      const searchParamsGet = vi.fn().mockReturnValue('supported-assistant-id');
      (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);

      const { result } = renderHook(() =>
        useAssistantToInitialize({ graphId: mockGraphId })
      );

      expect(result.current.assistant?.assistant_id).toBe(
        'supported-assistant-id'
      );
    });

    it('should fallback to first supported assistant if default is not supported', () => {
      const searchParamsGet = vi
        .fn()
        .mockReturnValue('unsupported-assistant-id');
      (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);

      // Mock that no assistant matches the ID in supported assistants
      (useProjectSupportsChat as any).mockReturnValue({
        supportedGraphs: [{ assistant_id: 'other-supported-id' }],
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useAssistantToInitialize({ graphId: mockGraphId })
      );

      expect(result.current.assistant?.assistant_id).toBe('other-supported-id');
    });

    it('should handle loading state while checking supported assistants', () => {
      (useProjectSupportsChat as any).mockReturnValue({
        supportedGraphs: undefined,
        isLoading: true,
      });

      const { result } = renderHook(() =>
        useAssistantToInitialize({ graphId: mockGraphId })
      );

      expect(result.current.assistant).toBeUndefined();
      expect(result.current.initializeLoading).toBe(true);
    });

    describe('with undefined graphId', () => {
      it('should check if default assistant is supported for chat mode', () => {
        const searchParamsGet = vi
          .fn()
          .mockReturnValue('supported-assistant-id');
        (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);

        const { result } = renderHook(() =>
          useAssistantToInitialize({ graphId: undefined })
        );

        expect(result.current.assistant?.assistant_id).toBe(
          'supported-assistant-id'
        );
      });

      it('should fallback to first supported assistant if default is not supported', () => {
        const searchParamsGet = vi
          .fn()
          .mockReturnValue('unsupported-assistant-id');
        (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);

        // Mock that no assistant matches the ID in supported assistants
        (useProjectSupportsChat as any).mockReturnValue({
          supportedGraphs: [{ assistant_id: 'other-supported-id' }],
          isLoading: false,
        });

        const { result } = renderHook(() =>
          useAssistantToInitialize({ graphId: undefined })
        );

        expect(result.current.assistant?.assistant_id).toBe(
          'other-supported-id'
        );
      });

      it('should handle loading state while checking supported assistants', () => {
        (useProjectSupportsChat as any).mockReturnValue({
          supportedGraphs: undefined,
          isLoading: true,
        });

        const { result } = renderHook(() =>
          useAssistantToInitialize({ graphId: undefined })
        );

        expect(result.current.assistant).toBeUndefined();
        expect(result.current.initializeLoading).toBe(true);
      });

      it('should fall back to the last assistant when no supported assistants are available', () => {
        const searchParamsGet = vi.fn().mockReturnValue(null);
        (useSearchParams as any).mockReturnValue([{ get: searchParamsGet }]);
        (useLocalStorageState as any).mockReturnValue([null, vi.fn()]);
        (useProjectSupportsChat as any).mockReturnValue({
          supportedGraphs: [],
          isLoading: false,
        });

        const { result } = renderHook(() =>
          useAssistantToInitialize({ graphId: undefined })
        );

        // With no graphId to match for system assistant, it should fall back to last assistant
        expect(result.current.assistant).toBe(mockCustomAssistant);
      });
    });
  });
});
