import { AssistantGraph } from '@langchain/langgraph-sdk';

import { useCallback, useMemo } from 'react';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

import {
  INPUT_NODE_NAME,
  convertThreadRunsToStateUpdates,
} from '@/Pages/Conversation/utils/convertThreadRunsToStateUpdates';
import { useThreadChildRuns } from '@/Pages/Conversation/utils/useThreadChildRuns';
import { useRunsInTreeStructure } from '@/Pages/Run/utils/useRunsInTreeStructure';
import useToast from '@/components/Toast';
import { RunSchema } from '@/types/schema';

import { StudioClient } from '../api';
import { checkIsSystemAssistant } from '../components/site/Assistants/utils';

export const useConvertTraceToThread = ({
  studioClient,
  rootRun,
  lastStudioUrl,
  diffUrl,
}: {
  studioClient: StudioClient | undefined;
  rootRun: RunSchema | undefined;
  lastStudioUrl: string | undefined;
  diffUrl: boolean;
}) => {
  const { data: threadChildRuns } = useThreadChildRuns({
    selectedRootRun: rootRun,
    getChildIO: true,
  });
  const unfilteredTree = useRunsInTreeStructure({
    rootRuns: rootRun ? [rootRun] : [],
    run: rootRun,
    childRunLookup: threadChildRuns,
    filteredRuns: [],
    filteredOnly: false,
    userAllRuns: true,
    foldedRuns: [],
  });
  const langGraphVersion = rootRun?.extra?.metadata?.langgraph_version;

  const { tree } = unfilteredTree;
  const { flattenedRuns } = tree;

  const stateUpdates = useMemo(
    () => convertThreadRunsToStateUpdates({ flattenedRuns, langGraphVersion }),
    [flattenedRuns, langGraphVersion]
  );

  const isSchemaCompatible = useCallback(
    (nodes: AssistantGraph['nodes']) => {
      if (diffUrl) return true;
      if (!nodes) return false;
      // check that every node in the state updates is in the graph
      const graphNodeNames = nodes.map((n) => n.id.toString());
      const nodeNames = stateUpdates
        .map((stateUpdate) => stateUpdate.asNode)
        .filter((n) => n !== INPUT_NODE_NAME);
      return nodeNames.every(
        (nodeName) =>
          // Check for direct match
          graphNodeNames.includes(nodeName) ||
          // Check if this is a parent node with children in the graph
          graphNodeNames.some((graphNode) =>
            graphNode.startsWith(`${nodeName}:`)
          )
      );
    },
    [stateUpdates, diffUrl]
  );

  const { createToast } = useToast();

  const createThreadWithStateMutation = useSWRMutation(
    [lastStudioUrl, 'createThreadWithState'],
    async (_, { arg }: { arg: { graphId: string } }) => {
      if (!studioClient || !arg.graphId || diffUrl) return;
      const thread = await studioClient.threads.create({
        graphId: arg.graphId,
        supersteps: stateUpdates.map((stateUpdate) => ({
          updates: [stateUpdate],
        })),
      });
      return thread;
    },
    {
      onError: (error) => {
        createToast({
          title: 'Failed to clone trace to local graph',
          description: error instanceof Error ? error.message : 'Unknown error',
          type: 'error',
        });
      },
    }
  );

  return {
    createThreadWithStateMutation,
    isSchemaCompatible,
    stateUpdates,
  };
};

export const useGetGraphs = ({
  client,
  apiUrl,
}: {
  client: StudioClient | undefined;
  apiUrl: string | undefined;
}) => {
  return useSWR([apiUrl, 'assistantsAndGraphNodes'], async () => {
    if (!client || !apiUrl) return undefined;
    const assistants = await client.assistants.search({
      limit: 10,
    });
    const systemAssistants = assistants.filter(checkIsSystemAssistant);
    const assistantIdToGraphId = systemAssistants.reduce((acc, a) => {
      if (a.graph_id) {
        acc[a.assistant_id] = a.graph_id;
      }
      return acc;
    }, {} as Record<string, string>);
    const graphs = await Promise.all(
      Object.entries(assistantIdToGraphId).map(
        async ([assistantId, graphId]) => {
          try {
            const graph = await client.assistants.getGraph(assistantId, {
              xray: true,
            });
            return {
              ...graph,
              graph_id: graphId,
            };
          } catch (err) {
            console.error(
              `Error fetching graph for assistant ${assistantId}`,
              err
            );
            return null;
          }
        }
      )
    );
    const filteredGraphs = graphs.filter(Boolean) as (AssistantGraph & {
      graph_id: string;
    })[];
    return filteredGraphs;
  });
};
