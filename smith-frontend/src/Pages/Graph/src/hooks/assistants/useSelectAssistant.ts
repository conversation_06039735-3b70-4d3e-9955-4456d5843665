import { Assistant } from '@langchain/langgraph-sdk';

import { useGraphAssistants } from '../../api/assistants';
import { useAssistantStore } from '../../store/assistants/assistantsStore';

export const useSelectAssistant = ({
  graphId,
}: {
  graphId: string | undefined;
}) => {
  const setConfig = useAssistantStore((state) => state.setConfig);
  const { assistants: systemAssistants } = useGraphAssistants({
    graphId,
    metadata: { created_by: 'system' },
  });
  const systemAssistant = systemAssistants?.[0];

  const setSelectedAssistantId = useAssistantStore(
    (state) => state.setSelectedAssistantId
  );
  const setActiveAssistantId = useAssistantStore(
    (state) => state.setActiveAssistantId
  );
  const setCurrentSelectedVersion = useAssistantStore(
    (state) => state.setCurrentSelectedVersion
  );
  const setStagedAssistant = useAssistantStore(
    (state) => state.setStagedAssistant
  );

  const activateSystemAssistant = () => {
    if (!systemAssistant?.assistant_id) return;
    setSelectedAssistantId(systemAssistant.assistant_id);
    setActiveAssistantId(systemAssistant.assistant_id);
    setConfig(systemAssistant.config ?? { configurable: {} });
    setCurrentSelectedVersion(1);
  };

  const onSelectAssistant = ({
    assistant,
    version,
    activate,
  }: {
    assistant?: Assistant;
    version?: number;
    activate?: boolean;
  }) => {
    setStagedAssistant(undefined);
    // if no assistant is selected, select and activate the system assistant
    if (!assistant) {
      activateSystemAssistant();
    } else {
      setSelectedAssistantId(assistant.assistant_id);
      setCurrentSelectedVersion(version ?? assistant.version);
      if (activate) {
        setActiveAssistantId(assistant.assistant_id);
        setConfig(assistant.config ?? { configurable: {} });
      }
    }
  };
  return {
    onSelectAssistant,
    activateSystemAssistant,
  };
};
