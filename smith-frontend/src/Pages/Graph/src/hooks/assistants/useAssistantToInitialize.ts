import { useContext, useMemo, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import {
  useAssistantSupportsChat,
  useProjectSupportsChat,
} from '@/Pages/HostProject/hooks/useProjectSupportsChat';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { useGraphAssistant, useGraphAssistants } from '../../api/assistants';
import { StudioMode, StudioModeContext } from '../useStudioMode';

export const useAssistantToInitialize = ({ graphId }: { graphId: unknown }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { studioMode } = useContext(StudioModeContext);
  const [localStorageAssistantId, setLocalStorageAssistantId] =
    useLocalStorageState<string | null>('ls:studio:defaultAssistant', null);
  const [assistantId] = useState(searchParams.get('assistantId'));
  const defaultAssistantId = assistantId ?? localStorageAssistantId;

  const { supportedGraphs, isLoading: isLoadingSupportedAssistants } =
    useProjectSupportsChat();

  const {
    data: defaultAssistant,
    error: defaultAssistantError,
    isLoading: isLoadingDefaultAssistant,
  } = useGraphAssistant(defaultAssistantId ?? undefined, {
    keepPreviousData: true,
  });

  const { supportsChat } = useAssistantSupportsChat(
    defaultAssistantId ?? undefined
  );

  const defaultAssistantIsSupportedForMode =
    studioMode === StudioMode.GRAPH ? true : supportsChat;

  const defaultAssistantToFetch = defaultAssistantIsSupportedForMode
    ? defaultAssistant
    : supportedGraphs?.[0];

  const {
    assistants,
    error: assistantsError,
    isLoading: assistantsLoading,
  } = useGraphAssistants({
    metadata: {
      created_by: 'system',
    },
  });

  const assistant = useMemo(() => {
    if (
      (studioMode === StudioMode.CHAT && isLoadingSupportedAssistants) ||
      isLoadingDefaultAssistant
    ) {
      return undefined;
    }
    return (
      defaultAssistantToFetch ??
      assistants?.find((item) => item.graph_id === graphId) ??
      assistants?.at(-1)
    );
  }, [
    assistants,
    graphId,
    defaultAssistantToFetch,
    studioMode,
    isLoadingSupportedAssistants,
    isLoadingDefaultAssistant,
  ]);

  // pre-fetch assistant data
  const {
    data: fetchedAssistant,
    isLoading: fetchedAssistantLoading,
    error: fetchedAssistantError,
  } = useGraphAssistant(assistant?.assistant_id, {
    keepPreviousData: true,
  });

  const onAssistantIdChange = (assistantId: string | null) => {
    setLocalStorageAssistantId(assistantId);
    searchParams.set('assistantId', assistantId ?? '');
    navigate({ search: searchParams.toString() });
  };

  return {
    assistant,
    assistants,
    intitializeError: fetchedAssistantError ?? assistantsError,
    initializeLoading:
      isLoadingDefaultAssistant ||
      fetchedAssistantLoading ||
      (defaultAssistantError && assistantsLoading) ||
      (studioMode === StudioMode.CHAT && isLoadingSupportedAssistants),
    onAssistantIdChange,
    fetchedAssistant,
  };
};
