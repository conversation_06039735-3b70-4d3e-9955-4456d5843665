import { Lang<PERSON>hainTracer } from '@langchain/core/tracers/tracer_langchain';
import { RunsInvokePayload } from '@langchain/langgraph-sdk';

import { useState } from 'react';

import { ExampleSchema, SessionSchema } from '@/types/schema';

import { useLangGraphClient } from '../api';
import { useCreateStudioExperiment } from '../api/useGraphSwr';
import { STUDIO_METADATA } from '../constants';

const NUMBER_OF_EXAMPLES_TO_WAIT = 10;

export const useRunStudioExperiment = ({
  setIsRunningInputs,
  setExperimentModalOpen,
}: {
  setIsRunningInputs: (isRunning: boolean) => void;
  setExperimentModalOpen: (isOpen: boolean) => void;
}) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { trigger: createStudioExperiment, isMutating: creatingExperiment } =
    useCreateStudioExperiment();

  const [progress, setProgress] = useState({ completed: 0, total: 0 });

  const createThreadAndRun = async ({
    example,
    index,
    experiment,
    assistantId,
  }: {
    example: ExampleSchema;
    index: number;
    experiment: SessionSchema;
    assistantId: string;
  }) => {
    try {
      const thread = await langgraph.threads.create({
        metadata: {
          ls_experiment_id: experiment.id,
        },
      });
      const payload: RunsInvokePayload = {
        input: example.inputs,
        metadata: {
          ...STUDIO_METADATA(apiUrl),
        },
        _langsmithTracer: {
          projectName: experiment.name,
          exampleId: example.id,
        } as LangChainTracer,
      };

      // create run then update progress
      if (index < NUMBER_OF_EXAMPLES_TO_WAIT) {
        await langgraph.runs.wait(thread.thread_id, assistantId, payload);
      } else {
        await langgraph.runs.create(thread.thread_id, assistantId, payload);
      }
      setProgress((prev) => ({ ...prev, completed: prev.completed + 1 }));
    } catch (error) {
      console.error(error);
      throw new Error(`Failed to create run for example ${example.id}`);
    }
  };

  const runInputs = async ({
    assistantId,
    examples,
    selectedDatasetId,
    experimentName,
  }: {
    assistantId: string;
    examples: ExampleSchema[];
    selectedDatasetId: string;
    experimentName: string;
  }) => {
    const experiment = await createStudioExperiment({
      json: {
        project_name: experimentName,
        dataset_id: selectedDatasetId,
      },
    });
    if (!experiment) {
      throw new Error('Failed to create experiment');
    }
    setIsRunningInputs(true);
    setProgress({ completed: 0, total: examples.length });
    setExperimentModalOpen(false);

    // Create thread and run for each example
    const promises = examples.map((example, index) =>
      createThreadAndRun({
        example,
        index,
        experiment,
        assistantId,
      })
    );

    await Promise.allSettled(promises);
    setProgress({ total: 0, completed: 0 });
    return experiment;
  };

  return {
    progress,
    creatingExperiment,
    runInputs,
  };
};
