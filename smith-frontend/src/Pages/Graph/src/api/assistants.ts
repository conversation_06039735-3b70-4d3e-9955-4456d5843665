import {
  Assistant,
  AssistantBase,
  Config,
  <PERSON>ada<PERSON>,
  OnConflictBehavior,
} from '@langchain/langgraph-sdk';

import { useMemo } from 'react';
import useSWR, { SWRConfiguration } from 'swr';
import useSWRInfinite from 'swr/infinite';
import useSWRMutation, { SWRMutationConfiguration } from 'swr/mutation';

import useToast from '@/components/Toast';
import { DEFAULT_INFINITE_OPTIONS } from '@/constants/swrDefaultOptions';
import { getCustomHeaders } from '@/data/fetcher';
import { useOrganizationId } from '@/hooks/useSwr';

import { useLangGraphClient } from '.';

export type AssistantSortBy =
  | 'created_at'
  | 'assistant_id'
  | 'updated_at'
  | 'name';
export type AssistantSortOrder = 'desc' | 'asc';
const ASSISTANTS_PAGE_SIZE = 10;

export const useGraphAssistant = (
  assistantId: string | undefined,
  options?: SWRConfiguration
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    assistantId ? [apiUrl, 'assistants', assistantId] : null,
    ([, , id]) => langgraph.assistants.get(id),
    options
  );
};

export const useGraphAssistantsInfinite = (
  params: {
    limit?: number;
    sortBy?: AssistantSortBy;
    sortOrder?: AssistantSortOrder;
    metadata?: Record<string, string | number | boolean | null>;
    graphId?: string;
    options?: SWRConfiguration;
  } | null
) => {
  const [_, apiUrl, customFetch] = useLangGraphClient();
  const organizationId = useOrganizationId();

  const getKey = (pageIndex) => {
    if (!params) return null;
    const { sortBy, sortOrder, metadata, graphId } = params;
    const limit = params?.limit ?? ASSISTANTS_PAGE_SIZE;
    return {
      url: `${apiUrl}/assistants/search`,
      params: {
        limit,
        offset: pageIndex * limit,
        sortBy: sortBy,
        sortOrder: sortOrder,
        metadata: metadata,
        graph_id: graphId,
      },
      headers: { 'X-Tenant-Id': organizationId },
    };
  };

  const fetcher = async ({ url, params }: { url: string; params: any }) => {
    const res = await (customFetch ?? fetch)(url, {
      method: 'POST',
      headers: {
        'x-auth-scheme': 'langsmith',
      },
      body: JSON.stringify(params),
    });
    if (!res.ok) {
      throw new Error(`Failed to fetch assistants: ${res.statusText}`);
    }
    const assistants = await res.json();
    const headers = getCustomHeaders(res.headers);
    const total = headers?.['x-pagination-total']
      ? parseInt(headers['x-pagination-total'], 10)
      : undefined;
    return {
      assistants: assistants as Assistant[],
      total,
    };
  };

  return useSWRInfinite<
    { assistants: Assistant[]; total: number | undefined },
    Error,
    typeof getKey
  >(getKey, fetcher, {
    ...DEFAULT_INFINITE_OPTIONS,
    ...params?.options,
  });
};

export const useGraphAssistants = (
  params: {
    limit?: number;
    sortBy?: AssistantSortBy;
    sortOrder?: AssistantSortOrder;
    metadata?: Record<string, string | number | boolean | null>;
    graphId?: string;
    options?: SWRConfiguration;
  } | null
) => {
  const {
    data: assistantPages,
    isLoading: isAssistantsLoading,
    isValidating: isAssistantsValidating,
    error: assistantsError,
    mutate: mutateAssistants,
    size,
    setSize,
  } = useGraphAssistantsInfinite(params);
  const assistants = useMemo(
    () => assistantPages?.flatMap((i) => i.assistants),
    [assistantPages]
  );
  const limit = params?.limit ?? ASSISTANTS_PAGE_SIZE;
  const hasMore = useMemo(() => {
    const totalAssistants = assistantPages?.[0]?.total;
    return totalAssistants ? size * limit < totalAssistants : false;
  }, [size, assistantPages, limit]);

  return {
    assistants,
    hasMore,
    isLoading: isAssistantsLoading,
    isValidating: isAssistantsValidating,
    error: assistantsError,
    mutate: mutateAssistants,
    size,
    setSize,
  };
};

// the sdk doesn't return the total number of threads, so we need to bypass it
export const useGraphAssistantsBypassSdk = (params: {
  offset?: number;
  limit?: number;
  sort_by?: AssistantSortBy;
  sort_order?: AssistantSortOrder;
}) => {
  const [_, apiUrl, customFetch] = useLangGraphClient();
  return useSWR([apiUrl, 'assistants', params], async ([, , params]) => {
    const fetcher = customFetch ?? fetch;
    const res = await fetcher(`${apiUrl}/assistants/search`, {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        'x-auth-scheme': 'langsmith',
      },
    });
    if (!res.ok) {
      throw new Error(`Failed to fetch assistants: ${res.statusText}`);
    }
    const assistants = await res.json();
    const headers = getCustomHeaders(res.headers);
    const total = headers?.['x-pagination-total']
      ? parseInt(headers['x-pagination-total'], 10)
      : undefined;
    return {
      assistants,
      total,
    };
  });
};

// -------- assistants crud --------
export const useGraphAssistantsCreate = (
  params?: { graphId: string } | null,
  options?: SWRMutationConfiguration<
    Assistant,
    unknown,
    any,
    [string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { mutate } = useGraphAssistants({});
  const { mutate: mutateAssistantsForGraph } = useGraphAssistants({
    graphId: params?.graphId,
  });
  const { createToast } = useToast();
  return useSWRMutation(
    params ? [apiUrl, 'assistantsCreate', params.graphId] : null,
    async (
      [, , graphId],
      {
        arg,
      }: {
        arg: {
          name?: string;
          description?: string;
          config?: Config;
          metadata?: Metadata;
          assistantId?: string;
          ifExists?: OnConflictBehavior;
        };
      }
    ) =>
      langgraph.assistants.create({
        graphId,
        name: arg.name,
        description: arg.description,
        config: arg.config,
        metadata: arg.metadata,
        assistantId: arg.assistantId,
        ifExists: arg.ifExists,
      }),
    {
      onSuccess: () => {
        mutate();
        mutateAssistantsForGraph();
      },
      onError: () => {
        createToast({
          title: 'Error creating assistant',
          error: true,
        });
      },
      ...options,
    }
  );
};

export const useGraphAssistantsUpdate = (
  params?: { assistantId: string; graphId: string } | null,
  options?: SWRMutationConfiguration<
    Assistant,
    unknown,
    any,
    [string, string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { createToast } = useToast();
  const { mutate: mutateAssistantVersions } = useGraphAssistantVersions(
    params?.assistantId
  );
  const { mutate: mutateAssistant } = useGraphAssistant(params?.assistantId);
  const { mutate: mutateAssistants } = useGraphAssistants({});
  const { mutate: mutateAssistantsForGraph } = useGraphAssistants({
    graphId: params?.graphId,
  });
  return useSWRMutation(
    params
      ? [apiUrl, 'assistantsUpdate', params.assistantId, params.graphId]
      : null,
    async (
      [, , id, gId],
      { arg }: { arg: { config?: Config; name?: string; description?: string } }
    ) =>
      langgraph.assistants.update(id, {
        graphId: gId,
        config: arg.config,
        name: arg.name,
        // for some reason api fails if description is null, so only include if it's not null
        ...(arg.description && { description: arg.description }),
      }),
    {
      ...options,
      onError: () => {
        createToast({
          title: 'Error saving assistant',
          error: true,
        });
      },
      onSuccess: async (...args) => {
        await Promise.all([
          options?.onSuccess?.(...args),
          mutateAssistantVersions(),
          mutateAssistant(),
          mutateAssistants(),
          mutateAssistantsForGraph(),
        ]);
      },
    }
  );
};

export const useGraphAssistantDelete = (
  assistant?: AssistantBase,
  options?: SWRMutationConfiguration<
    void,
    unknown,
    void,
    [string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { mutate } = useGraphAssistants({});
  const { mutate: mutateAssistantsForGraph } = useGraphAssistants({
    graphId: assistant?.graph_id,
  });
  const { createToast } = useToast();
  return useSWRMutation(
    assistant ? [apiUrl, 'assistantDelete', assistant.assistant_id] : null,
    async ([, , id]) => langgraph.assistants.delete(id),
    {
      onError: () => {
        createToast({
          title: 'Error deleting assistant',
          error: true,
        });
      },
      onSuccess: () => {
        // todo: we can actually set the size to the min of the pages the user has fetched and the total number of assistants
        // cuz otherwise this will refetch all pages even if they become empty
        mutate();
        mutateAssistantsForGraph();
      },
      ...options,
    }
  );
};

// -------- versions --------
export const useGraphAssistantVersions = (assistantId?: string) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    assistantId ? [apiUrl, 'assistantVersions', assistantId] : null,
    ([, , id]) => langgraph.assistants.getVersions(id, { limit: 20 })
  );
};

export const useGraphAssistantSetLatestVersion = (
  assistant?: Assistant,
  options?: SWRMutationConfiguration<
    Assistant,
    unknown,
    any,
    [string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { mutate } = useGraphAssistant(assistant?.assistant_id);
  const { mutate: mutateAssistants } = useGraphAssistants({
    graphId: assistant?.graph_id,
  });
  return useSWRMutation(
    assistant
      ? [apiUrl, 'assistantSetLatestVersion', assistant.assistant_id]
      : null,
    async ([, , id], { arg: version }: { arg: number }) =>
      langgraph.assistants.setLatest(id, version),
    {
      onSuccess: (newItem) => {
        mutate((assistant) => {
          if (!assistant) return undefined;
          return newItem;
        });
        mutateAssistants();
      },
      ...options,
    }
  );
};
