import {
  Assistant,
  Config,
  Metadata,
  OnConflictBehavior,
} from '@langchain/langgraph-sdk';

import useSWR, { mutate } from 'swr';
import useSWRMutation, { SWRMutationConfiguration } from 'swr/mutation';

import useToast from '@/components/Toast';
import { getCustomHeaders } from '@/data/fetcher';

import { useLangGraphClient } from '.';

export type AssistantSortBy =
  | 'created_at'
  | 'assistant_id'
  | 'updated_at'
  | 'name';
export type AssistantSortOrder = 'desc' | 'asc';

export const useGraphAssistants = (limit = 1000, offset = 0) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    [apiUrl, 'assistants', limit, offset],
    (): Promise<Assistant[]> => langgraph.assistants.search({ limit, offset })
  );
};

export const useGraphAssistant = (assistantId: string | undefined) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    assistantId ? [apiUrl, 'assistants', assistantId] : null,
    ([, , id]) => langgraph.assistants.get(id)
  );
};

// the sdk doesn't return the total number of threads, so we need to bypass it
export const useGraphAssistantsBypassSdk = (params: {
  offset?: number;
  limit?: number;
  sort_by?: AssistantSortBy;
  sort_order?: AssistantSortOrder;
}) => {
  const [_, apiUrl, customFetch] = useLangGraphClient();
  return useSWR([apiUrl, 'assistants', params], async ([, , params]) => {
    const fetcher = customFetch ?? fetch;
    const res = await fetcher(`${apiUrl}/assistants/search`, {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        'x-auth-scheme': 'langsmith',
      },
    });
    if (!res.ok) {
      throw new Error(`Failed to fetch assistants: ${res.statusText}`);
    }
    const assistants = await res.json();
    const headers = getCustomHeaders(res.headers);
    const total = headers?.['x-pagination-total']
      ? parseInt(headers['x-pagination-total'], 10)
      : undefined;
    return {
      assistants,
      total,
    };
  });
};

// -------- assistants crud --------
export const useGraphAssistantsCreate = (
  params?: { graphId: string } | null,
  options?: SWRMutationConfiguration<
    Assistant,
    unknown,
    any,
    [string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { mutate } = useGraphAssistants();
  const { createToast } = useToast();
  return useSWRMutation(
    params ? [apiUrl, 'assistantsCreate', params.graphId] : null,
    async (
      [, , graphId],
      {
        arg,
      }: {
        arg: {
          name?: string;
          description?: string;
          config?: Config;
          metadata?: Metadata;
          assistantId?: string;
          ifExists?: OnConflictBehavior;
        };
      }
    ) =>
      langgraph.assistants.create({
        graphId,
        name: arg.name,
        description: arg.description,
        config: arg.config,
        metadata: arg.metadata,
        assistantId: arg.assistantId,
        ifExists: arg.ifExists,
      }),
    {
      onSuccess: (newItem) => {
        mutate((assistants) => {
          if (!assistants) return [];
          // update cache w new item
          return [newItem, ...assistants];
        });
      },
      onError: () => {
        createToast({
          title: 'Error creating assistant',
          error: true,
        });
      },
      ...options,
    }
  );
};

export const useGraphAssistantsUpdate = (
  params?: { assistantId: string; graphId: string } | null,
  options?: SWRMutationConfiguration<
    Assistant,
    unknown,
    any,
    [string, string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { createToast } = useToast();
  const { mutate: mutateAssistantVersions } = useGraphAssistantVersions(
    params?.assistantId
  );
  return useSWRMutation(
    params
      ? [apiUrl, 'assistantsUpdate', params.assistantId, params.graphId]
      : null,
    async (
      [, , id, gId],
      { arg }: { arg: { config?: Config; name?: string; description?: string } }
    ) =>
      langgraph.assistants.update(id, {
        graphId: gId,
        config: arg.config,
        name: arg.name,
        // for some reason api fails if description is null, so only include if it's not null
        ...(arg.description && { description: arg.description }),
      }),
    {
      ...options,
      onError: () => {
        createToast({
          title: 'Error saving assistant',
          error: true,
        });
      },
      onSuccess: async (...args) => {
        await Promise.all([
          options?.onSuccess?.(...args),
          mutateAssistantVersions(),
          mutate((key) => {
            if (!Array.isArray(key)) return false;
            if (key[0] !== apiUrl) return false;
            if (key[1] === 'assistants') return true;
          }),
        ]);
      },
    }
  );
};

export const useGraphAssistantDelete = (
  assistantId?: string,
  options?: SWRMutationConfiguration<
    void,
    unknown,
    void,
    [string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { mutate } = useGraphAssistants();
  const { createToast } = useToast();
  return useSWRMutation(
    assistantId ? [apiUrl, 'assistantDelete', assistantId] : null,
    async ([, , id]) => langgraph.assistants.delete(id),
    {
      onError: () => {
        createToast({
          title: 'Error deleting assistant',
          error: true,
        });
      },
      onSuccess: () => {
        mutate((assistants) => {
          if (!assistants) return [];
          // remove from cache if deleted
          return assistants.filter(
            (oldItem) => oldItem.assistant_id !== assistantId
          );
        });
      },
      ...options,
    }
  );
};

// -------- versions --------
export const useGraphAssistantVersions = (assistantId?: string) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    assistantId ? [apiUrl, 'assistantVersions', assistantId] : null,
    ([, , id]) => langgraph.assistants.getVersions(id, { limit: 20 })
  );
};

export const useGraphAssistantSetLatestVersion = (
  assistantId?: string,
  options?: SWRMutationConfiguration<
    Assistant,
    unknown,
    any,
    [string, string, string] | null
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { mutate } = useGraphAssistants();
  return useSWRMutation(
    assistantId ? [apiUrl, 'assistantSetLatestVersion', assistantId] : null,
    async ([, , id], { arg: version }: { arg: number }) =>
      langgraph.assistants.setLatest(id, version),
    {
      onSuccess: (newItem) => {
        mutate((assistants) => {
          if (!assistants) return [];
          return assistants.map((a) =>
            a.assistant_id === assistantId ? newItem : a
          );
        });
      },
      ...options,
    }
  );
};
