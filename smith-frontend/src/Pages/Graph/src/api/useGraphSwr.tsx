import {
  Checkpoint,
  GraphSchema,
  ThreadStatus,
} from '@langchain/langgraph-sdk';

import { useCallback } from 'react';
import useSWR, { SWRConfiguration, mutate } from 'swr';
import useSWRMutation, { SWRMutationConfiguration } from 'swr/mutation';

import useToast from '@/components/Toast';
import { getCustomHeaders } from '@/data/fetcher';

import { useLangGraphClient } from '.';
import { SchemaBundle } from '../control/state';
import { Edge, Graph, Node } from '../data/misc';
import { isJSONSchema7, resolveRef } from '../json-schema-utils';

export type ThreadSortBy = 'created_at' | 'thread_id' | 'status' | 'updated_at';
export type ThreadSortOrder = 'desc' | 'asc';

export const useGraphThreads = ({
  graphId,
  offset,
  limit,
  sortBy,
  sortOrder,
}: {
  graphId?: string;
  offset?: number;
  limit?: number;
  sortBy?: 'created_at' | 'thread_id' | 'status' | 'updated_at';
  sortOrder?: 'desc' | 'asc';
}) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    [apiUrl, 'threads', graphId, offset, limit],
    ([, , graphId, offset, limit]) =>
      langgraph.threads.search({
        metadata: { graph_id: graphId },
        limit: limit ?? 1000,
        offset: offset ?? 0,
        sortBy: sortBy ?? 'created_at',
        sortOrder: sortOrder ?? 'desc',
      })
  );
};

// the sdk doesn't return the total number of threads, so we need to bypass it
export const useGraphThreadsBypassSdk = (params: {
  graphId?: string;
  offset?: number;
  limit?: number;
  sort_by?: ThreadSortBy;
  sort_order?: ThreadSortOrder;
  status?: ThreadStatus;
}) => {
  const [_, apiUrl, customFetch] = useLangGraphClient();
  return useSWR([apiUrl, 'threads', params], async ([, , params]) => {
    const res = await (customFetch ?? fetch)(`${apiUrl}/threads/search`, {
      method: 'POST',
      headers: {
        'x-auth-scheme': 'langsmith',
      },
      body: JSON.stringify(params),
    });
    if (!res.ok) {
      throw new Error(`Failed to fetch threads: ${res.statusText}`);
    }
    const threads = await res.json();
    const headers = getCustomHeaders(res.headers);
    const total = headers?.['x-pagination-total']
      ? parseInt(headers['x-pagination-total'], 10)
      : undefined;
    return {
      threads,
      total,
    };
  });
};

export const useGraphThreadDelete = () => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWRMutation(
    [apiUrl, 'thread'],
    (_, { arg }: { arg: { threadId: string } }) =>
      langgraph.threads.delete(arg.threadId),
    {
      onSuccess: () => {
        mutate(
          (key) =>
            Array.isArray(key) && key[0] === apiUrl && key[1] === 'threads'
        );
      },
    }
  );
};

export const useGraphCancelAllPendingRuns = (
  options?: SWRMutationConfiguration<
    number,
    unknown,
    void,
    [string, string, string]
  >
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWRMutation(
    [apiUrl, 'runs', 'cancelAll'],
    async () => {
      let cancelled = 0;
      for (const thread of await langgraph.threads.search({
        status: 'busy',
        limit: 1000,
      })) {
        if (thread.status !== 'busy') continue;
        const runs = await langgraph.runs.list(thread.thread_id, {
          limit: 1000,
          status: 'pending',
        });

        for (const run of runs) {
          if (run.status !== 'pending') continue;
          await langgraph.runs.cancel(thread.thread_id, run.run_id);
          cancelled++;
        }
      }
      return cancelled;
    },
    options
  );
};

// todo: this feels brittle. Hopefully we can write a better api handler for this
export const useGraphCancelRunningRunsForThreads = () => {
  const [langgraph, apiUrl] = useLangGraphClient();
  const { createToast } = useToast();
  return useSWRMutation(
    [apiUrl, 'runs', 'cancelAll'],
    async (_, { arg }: { arg: { threadIds: string[] } }) => {
      const cancelled: string[] = [];
      for (const threadId of arg.threadIds) {
        const runs = await langgraph.runs.list(threadId, {
          limit: 1000,
          status: 'running',
        });
        await Promise.all(
          runs
            .filter((run) => run.status === 'running')
            .map(async (run) => {
              await langgraph.runs.cancel(threadId, run.run_id);
              cancelled.push(threadId);
            })
        );
      }
      return cancelled;
    },
    {
      onSuccess: (data) => {
        mutate(
          (key) =>
            Array.isArray(key) &&
            key[0] === apiUrl &&
            ((key[1] === 'thread' && data.includes(key[2])) ||
              key[1] === 'threads')
        );
      },
      onError: (error) => {
        createToast({
          title: 'Error cancelling runs',
          description: error.message,
          type: 'error',
        });
      },
    }
  );
};

export const useGraphServerInfo = () => {
  const [_, apiUrl, customFetch] = useLangGraphClient();
  return useSWR(
    [apiUrl, 'info'],
    async () => {
      const response = await (customFetch ?? fetch)(`${apiUrl}/info`);
      if (!response.ok) {
        // Older versions of LangGraph don't support this endpoint
        if (response.status === 404) return null;
        throw new Error(`Failed to fetch API info: ${response.statusText}`);
      }
      return (await response.json()) as {
        flags: {
          assistants?: boolean;
          crons?: boolean;
          langsmith?: boolean;
        };
      };
    },
    { revalidateOnFocus: false, shouldRetryOnError: false }
  );
};

export const useGraphThread = (threadId?: string) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    threadId ? [apiUrl, 'thread', threadId] : null,
    ([, , threadId]) => {
      if (!threadId) return null;
      return langgraph.threads.get(threadId);
    }
  );
};

export const useGraphThreadPendingRuns = (threadId: string | undefined) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    threadId ? [apiUrl, 'runs', threadId] : null,
    ([, , threadId]) => {
      if (!threadId) return null;
      // TODO: implement proper pagination
      return langgraph.runs.list(threadId, { status: 'pending', limit: 1000 });
    }
  );
};

export const useGraphThreadHistory = (
  args: {
    threadId: string | null | undefined;
    checkpoint?: Partial<Checkpoint>;
  },
  options?: SWRConfiguration
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    args.threadId
      ? args.checkpoint
        ? [apiUrl, 'thread', args.threadId, 'history', args.checkpoint]
        : [apiUrl, 'thread', args.threadId, 'history']
      : null,
    (key) => {
      const [, , threadId, , checkpoint] = key;
      if (!threadId) return null;
      return langgraph.threads.getHistory(threadId, {
        checkpoint,
        limit: 1000,
      });
    },
    options
  );
};

export const useGraphThreadHistoryMutate = (
  threadId: string | null | undefined
) => {
  const [langgraph, apiUrl] = useLangGraphClient();

  return useCallback(() => {
    if (!threadId || !langgraph || !threadId) return;
    return mutate(
      (key) =>
        Array.isArray(key) &&
        key[0] === apiUrl &&
        key[1] === 'thread' &&
        key[2] === threadId &&
        key[3] === 'history'
    );
  }, [apiUrl, langgraph, threadId]);
};

export const useGraphThreadState = (
  threadId: string | null | undefined,
  checkpoint?: Checkpoint | string
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    threadId ? [apiUrl, 'thread', threadId, 'state', checkpoint] : null,
    ([, , threadId, , checkpoint]) => {
      return langgraph.threads.getState(threadId, checkpoint);
    }
  );
};

export const getSchemaBundle = (x: GraphSchema): SchemaBundle => {
  // there's actually some mismatch in the api/type definition of GraphSchema
  // where it's possible for one of these schemas to be returned as null. So here we convert them to undefined
  // to be consistent downstream.
  const stateRoot = x.state_schema ?? undefined;
  const state = resolveRef(stateRoot, stateRoot?.$ref) ?? stateRoot;

  const inputRoot = x.input_schema ?? undefined;
  const input = resolveRef(inputRoot, inputRoot?.$ref) ?? inputRoot;

  const outputRoot = x.output_schema ?? undefined;
  const output = resolveRef(outputRoot, outputRoot?.$ref) ?? outputRoot;

  const configRoot = x.config_schema ?? undefined;
  const config =
    resolveRef(
      configRoot,
      isJSONSchema7(configRoot?.properties?.['configurable'])
        ? configRoot.properties?.['configurable']?.$ref
        : null
    ) ?? configRoot;

  return {
    state,
    stateRoot,
    config,
    configRoot,
    input,
    inputRoot,
    output,
    outputRoot,
  };
};

export const useGraphAssistantDebugGraph = (assistantId?: string) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    assistantId ? [apiUrl, 'graph', assistantId] : null,
    async ([, , assistantId]): Promise<Graph | undefined> => {
      const [graph, subgraphs] = await Promise.all([
        langgraph.assistants.getGraph(assistantId, { xray: true }),
        (async () => {
          try {
            const res = await langgraph.assistants.getSubgraphs(assistantId, {
              recurse: true,
            });

            return Object.fromEntries(
              Object.entries(res).map(([k, v]) => [k, getSchemaBundle(v)])
            );
          } catch (error) {
            console.error(error);
            return {};
          }
        })(),
      ]);

      const nodes = graph.nodes.map((x, idx) => {
        const id = x.id ?? `node-${idx}`;
        if (typeof x.id !== 'string') {
          console.error('Node id is not a string', x);
        }
        return { id: String(id), data: x.data, metadata: x.metadata } as Node;
      });

      const edges = graph.edges.map(
        (x): Edge => ({
          source: String(x.source),
          target: String(x.target),
          conditional: !!x.conditional,
          data: x.data,
        })
      );

      return { nodes, edges, subgraphs };
    },
    { keepPreviousData: true, shouldRetryOnError: false }
  );
};

export const useGraphAssistantsDebugSchema = (
  assistantIds: string[] | null | undefined
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR(
    assistantIds ? [apiUrl, 'schema', assistantIds] : null,
    async ([, , assistantIds]) => {
      const schemas = await Promise.allSettled(
        assistantIds.map((id) => langgraph.assistants.getSchemas(id))
      );
      return schemas.map((x) => {
        if (x.status === 'fulfilled') return getSchemaBundle(x.value);
        return undefined;
      });
    }
  );
};
type MemoryUpdatePayload = {
  key: string;
  value: Record<string, unknown>;
  namespace: string[];
};

export const useGraphMemoryPut = (
  initialValues: Partial<MemoryUpdatePayload> | undefined,
  options?: {
    onSuccess?: (arg: MemoryUpdatePayload) => void;
  }
) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWRMutation(
    [apiUrl, 'store', 'docs', 'put'],
    async (_, { arg }: { arg: MemoryUpdatePayload }) => {
      if (
        initialValues &&
        initialValues.namespace &&
        initialValues.key &&
        initialValues.namespace?.join('/') !== arg.namespace.join('/')
      ) {
        await langgraph.store.deleteItem(
          initialValues.namespace,
          initialValues.key
        );
      }
      await langgraph.store.putItem(arg.namespace, arg.key, arg.value);
      return arg;
    },
    {
      onSuccess: (args) => {
        mutate(
          (key) => Array.isArray(key) && key[0] === apiUrl && key[1] === 'store'
        );
        options?.onSuccess?.(args);
      },
    }
  );
};

type MemoryDeletePayload = {
  key: string;
  namespace: string[];
};

export const useGraphMemoryDelete = (options?: { onSuccess?: () => void }) => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWRMutation(
    [apiUrl, 'store', 'docs', 'delete'],
    async (_, { arg }: { arg: MemoryDeletePayload }) => {
      await langgraph.store.deleteItem(arg.namespace, arg.key);
      return arg;
    },
    {
      onSuccess: () => {
        mutate(
          (key) => Array.isArray(key) && key[0] === apiUrl && key[1] === 'store'
        );
        options?.onSuccess?.();
      },
    }
  );
};

export const useGraphMemorySearch = (name: string | null) => {
  const [langgraph, apiUrl] = useLangGraphClient();

  return useSWR(
    name ? [apiUrl, 'store', 'docs', name] : null,
    async (args) => {
      const nsPrefix = args[3];
      const pageSize = 100;
      let result: Awaited<
        ReturnType<typeof langgraph.store.searchItems>
      >['items'] = [];
      let offset = 0;

      // TODO: figure out how to paginate / stream values
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const search = await langgraph.store.searchItems([nsPrefix], {
          limit: pageSize,
          offset,
        });

        result = result.concat(...search.items);
        if (search.items.length < pageSize) break;
        offset += pageSize;
      }
      return result;
    },
    { keepPreviousData: true }
  );
};

export const useGraphMemoryNamespaces = () => {
  const [langgraph, apiUrl] = useLangGraphClient();
  return useSWR([apiUrl, 'store', 'list'], async () => {
    const result = await langgraph.store.listNamespaces({ maxDepth: 1 });
    return result.namespaces.filter((x) => x.filter((y) => !!y).length > 0);
  });
};

export const useGraphConnection = ({ studioUrl }: { studioUrl?: string }) => {
  return useSWR(['studio', 'connectLocalUrl', studioUrl], async () => {
    if (!studioUrl) return false;
    const response = await fetch(new URL('/info', studioUrl));
    if (!response.ok) throw new Error('Failed to fetch');
    return response.ok;
  });
};
