import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { Assistant } from '@langchain/langgraph-sdk';
import { CheckIcon, ChevronRightIcon } from '@langchain/untitled-ui-icons';

import { partition } from 'lodash';
import { useContext, useState } from 'react';

import { useProjectSupportsChat } from '@/Pages/HostProject/hooks/useProjectSupportsChat';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { LoadMoreButton } from '@/components/RunsTable/LoadMoreButton';
import { Skeleton } from '@/components/Skeleton';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';

import { useGraphAssistant, useGraphAssistants } from '../../api/assistants';
import { useActiveThreadId } from '../../hooks/useActiveThreadId';
import { StudioMode, StudioModeContext } from '../../hooks/useStudioMode';
import { useAssistantStore } from '../../store/assistants/assistantsStore';
import { checkIsSystemAssistant } from './Assistants/utils';

export function GraphSystemAssistantSelect() {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const setAssistantId = useAssistantStore(
    (state) => state.setActiveAssistantId
  );
  const setSelectedAssistantId = useAssistantStore(
    (state) => state.setSelectedAssistantId
  );
  const config = useAssistantStore((state) => state.config);
  const setConfig = useAssistantStore((state) => state.setConfig);
  const { isLoading: assistantLoading, error: assistantError } =
    useGraphAssistant(assistantId);

  const { studioMode } = useContext(StudioModeContext);

  const { setThreadId } = useActiveThreadId();

  if ((assistantLoading || !assistantId) && !assistantError) {
    return <Skeleton className="h-6 w-[150px]" />;
  }

  const onSelectAssistant = (assistant: Assistant) => {
    setAssistantId(assistant.assistant_id);
    setSelectedAssistantId(assistant.assistant_id);
    setConfig(assistant.config ?? config);
    setThreadId(undefined);
  };

  return (
    <Popover>
      {studioMode === StudioMode.GRAPH ? (
        <GraphSelect onSelectAssistant={onSelectAssistant} />
      ) : (
        <ChatSelect onSelectAssistant={onSelectAssistant} />
      )}
    </Popover>
  );
}

const GraphSelect = ({
  onSelectAssistant,
}: {
  onSelectAssistant: (assistant: Assistant) => void;
}) => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);
  const {
    assistants: systemAssistants,
    hasMore,
    size,
    setSize,
    isValidating,
  } = useGraphAssistants({ metadata: { created_by: 'system' } });

  return (
    <>
      <PopoverTrigger className="flex min-h-[38px] max-w-[30vw] items-center justify-between gap-2 rounded-md  bg-transparent p-1.5 px-2.5 text-sm focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 data-[placeholder]:text-secondary [&>span]:line-clamp-1">
        <TextOverflowTooltip className="text-sm font-medium">
          {assistant?.graph_id ?? 'Select a graph'}
        </TextOverflowTooltip>

        <ChevronDownIcon className="h-4 w-4 flex-shrink-0" />
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="max-h-[75vh] w-[215px] overflow-y-auto p-0"
      >
        <div className="flex flex-col gap-2 p-2">
          <span className="px-2 text-sm font-medium text-quaternary">
            Select a graph
          </span>
          <div className="flex flex-col items-start gap-2 ">
            {systemAssistants?.map((a) => (
              <button
                key={a.assistant_id}
                type="button"
                className="w-full rounded-md p-2 text-start text-sm hover:bg-secondary"
                onClick={() => {
                  onSelectAssistant(a);
                }}
              >
                <div className="flex flex-row items-center justify-between">
                  <span className="truncate text-sm font-medium">
                    {a.graph_id}
                  </span>
                  {assistant?.graph_id === a.graph_id && (
                    <CheckIcon className="h-4 w-4 flex-shrink-0 text-brand-tertiary" />
                  )}
                </div>
              </button>
            ))}
            {hasMore && (
              <LoadMoreButton
                isInitialLoad
                isLoading={isValidating}
                onClick={() => setSize(size + 1)}
                className="text-sm"
              />
            )}
          </div>
        </div>
      </PopoverContent>
    </>
  );
};

const ChatSelect = ({
  onSelectAssistant,
}: {
  onSelectAssistant: (assistant: Assistant) => void;
}) => {
  const [hoveredGraphId, setHoveredGraphId] = useState<string | undefined>();
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);

  const { supportedGraphs, isValidating, hasMore, size, setSize } =
    useProjectSupportsChat();

  const getAssistantDisplayName = (a: Assistant | undefined) => {
    if (!a) return;
    if (checkIsSystemAssistant(a)) {
      return `Default`;
    }
    return a.name;
  };
  return (
    <>
      <PopoverTrigger className="flex min-h-[38px] max-w-[30vw] items-center justify-between gap-2 rounded-md  bg-transparent p-1.5 px-2.5 text-sm focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 data-[placeholder]:text-secondary [&>span]:line-clamp-1">
        <TextOverflowTooltip
          className="text-sm font-medium"
          tooltipMaxWidth={500}
          placement="right"
          customTooltip={
            <div className="flex flex-row items-center gap-1">
              <span className="text-sm font-medium">
                {getAssistantDisplayName(assistant)}
              </span>
              {checkIsSystemAssistant(assistant) && (
                <span className="text-xxs">({assistant?.graph_id})</span>
              )}
            </div>
          }
        >
          <span className="text-sm font-medium">
            {getAssistantDisplayName(assistant)}
          </span>{' '}
          <span className="text-xxs text-tertiary">
            ({assistant?.graph_id})
          </span>
        </TextOverflowTooltip>

        <ChevronDownIcon className="h-4 w-4 flex-shrink-0" />
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="max-h-[75vh] w-[215px] overflow-y-auto p-0"
      >
        <div className="flex flex-col gap-2 p-2">
          <span className="px-2 text-sm font-medium text-quaternary">
            Select a graph
          </span>
          <div className="flex flex-col gap-2">
            {supportedGraphs?.map((g) => (
              <ChatSelectItem
                key={g.graph_id}
                graph={g}
                isOpen={hoveredGraphId === g.graph_id}
                onSelectAssistant={onSelectAssistant}
                onMouseEnter={() => setHoveredGraphId(g.graph_id)}
                onMouseLeave={() => setHoveredGraphId(undefined)}
              />
            ))}
            {hasMore && (
              <LoadMoreButton
                isInitialLoad
                isLoading={isValidating}
                onClick={() => setSize(size + 1)}
                className="text-sm"
              />
            )}
          </div>
        </div>
      </PopoverContent>
    </>
  );
};

const ChatSelectItem = ({
  graph,
  isOpen,
  onSelectAssistant,
  onMouseEnter,
  onMouseLeave,
}: {
  graph: Assistant;
  isOpen: boolean;
  onSelectAssistant: (assistant: Assistant) => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}) => {
  const { graph_id: graphId } = graph;
  const { assistants, hasMore, size, setSize, isValidating } =
    useGraphAssistants({ graphId });
  const [_, userAssistants] = partition(assistants, checkIsSystemAssistant);

  const { assistants: systemAssistants } = useGraphAssistants({
    graphId,
    metadata: { created_by: 'system' },
  });

  const activeAssistantId = useAssistantStore(
    (state) => state.activeAssistantId
  );
  const { data: activeAssistant } = useGraphAssistant(activeAssistantId);

  const activeAssistantInList = activeAssistantId
    ? new Set(
        [...userAssistants, ...(systemAssistants ?? [])].map(
          (a) => a.assistant_id
        )
      ).has(activeAssistantId)
    : undefined;

  const getAssistantDisplayName = (a: Assistant | undefined) => {
    if (!a) return;
    if (checkIsSystemAssistant(a)) {
      return `Default (${a.graph_id})`;
    }
    return a.name;
  };

  const renderAssistantButton = (assistant: Assistant) => {
    return (
      <button
        key={assistant.assistant_id}
        type="button"
        className="flex w-full flex-row items-center justify-between rounded-md p-2 text-start text-sm hover:bg-secondary"
        onClick={() => onSelectAssistant(assistant)}
      >
        <span className="truncate text-sm font-medium">
          {getAssistantDisplayName(assistant)}
        </span>
        {activeAssistantId === assistant.assistant_id && (
          <CheckIcon className="h-4 w-4 flex-shrink-0 text-brand-tertiary" />
        )}
      </button>
    );
  };

  return (
    <Popover open={isOpen}>
      <PopoverTrigger>
        <div
          className="flex w-full flex-row items-center justify-between rounded-md p-2 hover:bg-secondary"
          onClick={() => onSelectAssistant(graph)}
          onMouseEnter={onMouseEnter}
        >
          <span className="truncate text-sm font-medium">{graphId}</span>
          <ChevronRightIcon className="h-4 w-4 flex-shrink-0" />
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        side="right"
        sideOffset={8}
        className="max-h-[75vh] w-[215px] overflow-y-auto p-0"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div className="flex flex-col gap-2 p-2">
          <span className="px-2 text-sm font-medium text-quaternary">
            Select an assistant
          </span>
          <div className="flex flex-col items-start gap-2">
            {systemAssistants?.map((a) => renderAssistantButton(a))}
            {!activeAssistantInList &&
              activeAssistant &&
              renderAssistantButton(activeAssistant)}
            {userAssistants.map((a) => renderAssistantButton(a))}
          </div>
          {hasMore && (
            <LoadMoreButton
              isInitialLoad
              isLoading={isValidating}
              onClick={() => setSize(size + 1)}
            />
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};
