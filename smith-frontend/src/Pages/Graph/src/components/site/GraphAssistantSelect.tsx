import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { Assistant } from '@langchain/langgraph-sdk';

import { partition } from 'lodash-es';
import { useContext } from 'react';

import { useProjectSupportsChat } from '@/Pages/HostProject/hooks/useProjectSupportsChat';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { Skeleton } from '@/components/Skeleton';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';

import { useGraphAssistant, useGraphAssistants } from '../../api/assistants';
import { useActiveThreadId } from '../../hooks/useActiveThreadId';
import { StudioMode, StudioModeContext } from '../../hooks/useStudioMode';
import { useAssistantStore } from '../../store/assistants/assistantsStore';
import { checkIsSystemAssistant } from './Assistants/utils';

export function GraphSystemAssistantSelect() {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const setAssistantId = useAssistantStore(
    (state) => state.setActiveAssistantId
  );
  const setSelectedAssistantId = useAssistantStore(
    (state) => state.setSelectedAssistantId
  );
  const config = useAssistantStore((state) => state.config);
  const setConfig = useAssistantStore((state) => state.setConfig);
  const { isLoading: assistantLoading, error: assistantError } =
    useGraphAssistant(assistantId);

  const { studioMode } = useContext(StudioModeContext);

  const { setThreadId } = useActiveThreadId();

  if ((assistantLoading || !assistantId) && !assistantError) {
    return <Skeleton className="h-6 w-[150px]" />;
  }

  const onSelectAssistant = (assistant: Assistant) => {
    setAssistantId(assistant.assistant_id);
    setSelectedAssistantId(assistant.assistant_id);
    setConfig(assistant.config ?? config);
    setThreadId(undefined);
  };

  return (
    <DropdownMenu>
      {studioMode === StudioMode.GRAPH ? (
        <GraphSelect onSelectAssistant={onSelectAssistant} />
      ) : (
        <ChatSelect onSelectAssistant={onSelectAssistant} />
      )}
    </DropdownMenu>
  );
}

const GraphSelect = ({
  onSelectAssistant,
}: {
  onSelectAssistant: (assistant: Assistant) => void;
}) => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);
  const { data: assistants } = useGraphAssistants();
  const [systemAssistants] = partition(assistants, checkIsSystemAssistant);

  return (
    <>
      <DropdownMenuTrigger className="flex min-h-[38px] max-w-[30vw] items-center justify-between gap-2 rounded-md  bg-transparent p-1.5 px-2.5 text-sm focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 data-[placeholder]:text-secondary [&>span]:line-clamp-1">
        <TextOverflowTooltip className="text-sm font-medium">
          {assistant?.graph_id ?? 'Select a graph'}
        </TextOverflowTooltip>

        <ChevronDownIcon className="h-4 w-4 flex-shrink-0" />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="max-h-[75vh] overflow-y-auto"
      >
        <div className="flex flex-col gap-2 py-1">
          <span className="px-2 text-sm font-medium text-quaternary">
            Select a graph
          </span>
          <div className="flex flex-col gap-2">
            {systemAssistants?.map((a) => (
              <DropdownMenuItem
                key={a.assistant_id}
                className="text-sm"
                onClick={() => {
                  onSelectAssistant(a);
                }}
              >
                {a.graph_id}
              </DropdownMenuItem>
            ))}
          </div>
        </div>
      </DropdownMenuContent>
    </>
  );
};

const ChatSelect = ({
  onSelectAssistant,
}: {
  onSelectAssistant: (assistant: Assistant) => void;
}) => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);

  const { supportedAssistants } = useProjectSupportsChat();

  const getAssistantDisplayName = (a: Assistant | undefined) => {
    if (!a) return;
    if (checkIsSystemAssistant(a)) {
      return `Default`;
    }
    return a.name;
  };
  return (
    <>
      <DropdownMenuTrigger className="flex min-h-[38px] max-w-[30vw] items-center justify-between gap-2 rounded-md  bg-transparent p-1.5 px-2.5 text-sm focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 data-[placeholder]:text-secondary [&>span]:line-clamp-1">
        <TextOverflowTooltip
          className="text-sm font-medium"
          tooltipMaxWidth={500}
          placement="right"
          customTooltip={
            <div className="flex flex-row items-center gap-1">
              <span className="text-sm font-medium">
                {getAssistantDisplayName(assistant)}
              </span>
              {checkIsSystemAssistant(assistant) && (
                <span className="text-xxs">({assistant?.graph_id})</span>
              )}
            </div>
          }
        >
          <span className="text-sm font-medium">
            {getAssistantDisplayName(assistant)}
          </span>{' '}
          <span className="text-xxs text-tertiary">
            ({assistant?.graph_id})
          </span>
        </TextOverflowTooltip>

        <ChevronDownIcon className="h-4 w-4 flex-shrink-0" />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="max-h-[75vh] overflow-y-auto"
      >
        <div className="flex flex-col gap-2 py-1">
          <span className="px-2 text-sm font-medium text-quaternary">
            Select an assistant
          </span>
          <div className="flex flex-col gap-2">
            {supportedAssistants?.map((a) => (
              <DropdownMenuItem
                key={a.assistant_id}
                className="text-sm"
                onClick={() => {
                  onSelectAssistant(a);
                }}
              >
                <div className="flex flex-col gap-1">
                  <span className="text-sm font-medium">
                    {getAssistantDisplayName(a)}
                  </span>
                  <span className="text-xs text-secondary">{a.graph_id}</span>
                </div>
              </DropdownMenuItem>
            ))}
          </div>
        </div>
      </DropdownMenuContent>
    </>
  );
};
