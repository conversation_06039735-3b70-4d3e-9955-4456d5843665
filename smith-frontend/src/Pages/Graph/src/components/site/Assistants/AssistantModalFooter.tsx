import { Assistant, AssistantBase } from '@langchain/langgraph-sdk';
import { But<PERSON>, Tooltip } from '@mui/joy';

import { isEqual } from 'lodash-es';
import { useMemo } from 'react';

import { ButtonWithTracking } from '@/components/ButtonWithTracking';
import { DeleteConfirmationButton } from '@/components/Delete';

import { useGraphAssistantDelete } from '../../../api/assistants';
import { useGraphServerInfo } from '../../../api/useGraphSwr';
import { useSaveOrCreateAssistant } from '../../../hooks/assistants/useSaveOrCreateAssistant';
import { checkIsSystemAssistant } from './utils';

type AssistantModalFooterProps = {
  assistant?: AssistantBase;
  value: Partial<AssistantBase>;
  hasNameError: boolean;
  saveButtonTooltipText?: string;
  onSelectAssistant: ({
    assistant,
    version,
    activate,
  }: {
    assistant?: Assistant;
    version?: number;
    activate?: boolean;
  }) => void;
  onClose: () => void;
};

const AssistantModalFooter = ({
  assistant,
  value,
  hasNameError,
  saveButtonTooltipText,
  onSelectAssistant,
  onClose,
}: AssistantModalFooterProps) => {
  const { data: apiInfo } = useGraphServerInfo();
  const supportsAssistants = !!apiInfo?.flags.assistants;

  const { assistant_id: assistantId, config } = assistant ?? {};
  const isSystemAssistant = checkIsSystemAssistant(assistant);

  const hasChanges = useMemo(() => {
    if (isSystemAssistant) return true;
    return !isEqual(config, value.config);
  }, [config, value, isSystemAssistant]);

  const { saveOrCreateAssistant, isSavingAssistant, isCreatingAssistant } =
    useSaveOrCreateAssistant({
      assistantId,
      value,
      save: !isSystemAssistant && !!assistantId,
    });

  const { trigger: deleteAssistant, isMutating: isDeletingAssistant } =
    useGraphAssistantDelete(assistant);

  const handleDelete = async () => {
    await deleteAssistant();
    onSelectAssistant({});
  };

  const getSaveButtonTooltipText = () => {
    if (!hasChanges) {
      return 'No changes to save';
    }
    if (hasNameError) {
      return saveButtonTooltipText;
    }
    return undefined;
  };

  if (!supportsAssistants) {
    return null;
  }

  return (
    <div className="sticky bottom-0 flex w-full items-center justify-between gap-4 px-4">
      {!isSystemAssistant && assistantId && (
        <DeleteConfirmationButton
          title={`Delete Assistant: ${assistant?.name}?`}
          description="This will delete the assistant and all associated versions. This action cannot be undone."
          onDelete={handleDelete}
          deleteButtonText="Delete"
          isDeleting={isDeletingAssistant}
          trigger={
            <Button
              color="danger"
              variant="outlined"
              type="button"
              loading={isDeletingAssistant}
            >
              Delete Assistant
            </Button>
          }
        />
      )}
      <div className="flex grow items-center justify-end gap-4">
        <Button variant="outlined" color="neutral" onClick={onClose}>
          Cancel
        </Button>
        <Tooltip title={getSaveButtonTooltipText()} placement="top">
          <div>
            <ButtonWithTracking
              as={Button}
              eventName={
                isSystemAssistant ? 'create_assistant' : 'save_assistant'
              }
              loading={isSavingAssistant || isCreatingAssistant}
              onClick={saveOrCreateAssistant}
              disabled={!hasChanges || isSavingAssistant || hasNameError}
              variant={isSystemAssistant ? 'outlined' : 'solid'}
            >
              {isSystemAssistant ? 'Create New Assistant' : 'Save'}
            </ButtonWithTracking>
          </div>
        </Tooltip>
      </div>
    </div>
  );
};

export default AssistantModalFooter;
