import { Assistant } from '@langchain/langgraph-sdk';
import {
  ArrowLeftIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  PlusIcon,
} from '@langchain/untitled-ui-icons';
import { Button, IconButton } from '@mui/joy';

import { partition } from 'lodash-es';
import { Dispatch, SetStateAction, useState } from 'react';

import { LoadMoreButton } from '@/components/RunsTable/LoadMoreButton';
import { TruncatedTextWithTooltip } from '@/components/TruncatedTextWithTooltip.tsx';
import { cn } from '@/utils/tailwind';
import { toRelativeTime } from '@/utils/to-relative-time';

import {
  useGraphAssistant,
  useGraphAssistantVersions,
  useGraphAssistants,
} from '../../../api/assistants';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import ActiveBadge from './ActiveBadge';
import { checkIsSystemAssistant, getAssistantDisplayName } from './utils';

const AssistantsListItem = ({
  assistant,
  isSelected,
  onSelect,
  currentSelectedVersion,
  isExpanded,
  setIsExpanded,
}: {
  assistant: Assistant;
  isSelected: boolean;
  onSelect: (assistant: Assistant, version?: number) => void;
  currentSelectedVersion: number | undefined;
  isExpanded: boolean;
  setIsExpanded: Dispatch<SetStateAction<string | undefined>>;
}) => {
  const activeAssistantId = useAssistantStore(
    (state) => state.activeAssistantId
  );
  const isSystemAssistant = checkIsSystemAssistant(assistant);
  const isActiveAssistant = assistant.assistant_id === activeAssistantId;

  const activeVersion = assistant.version;

  const { data: assistantsVersions, isLoading: isLoadingVersions } =
    useGraphAssistantVersions(isExpanded ? assistant.assistant_id : undefined);

  const orderedVersions = assistantsVersions?.sort(
    (a, b) => b.version - a.version
  );

  const renderVersionsList = () => {
    if (isLoadingVersions) {
      return <span className="text-xs text-secondary">Loading...</span>;
    }

    return (
      <div className="flex flex-col">
        {orderedVersions?.map((version) => (
          <div
            key={version.version}
            className={cn(
              'flex cursor-pointer items-center justify-between rounded-l-none border-l-[2px] px-4 py-3',
              isSelected ? ' border-brand-solid' : 'border-secondary',
              version.version === currentSelectedVersion && 'bg-brand-tertiary'
            )}
            onClick={() => onSelect(assistant, version.version)}
          >
            <div className="flex items-center gap-2 py-1">
              <span className="text-xs font-medium text-brand-primary">
                v{version.version}
              </span>
              <span className="text-xs text-quaternary">
                {toRelativeTime(version.created_at)}
              </span>
            </div>
            {version.version === activeVersion && isActiveAssistant && (
              <ActiveBadge />
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div
      className="flex flex-col pr-2 hover:bg-secondary"
      key={assistant.assistant_id}
    >
      <div
        className={cn(
          'flex cursor-pointer items-center justify-between p-2',
          isSelected
            ? 'border-brand-solid border-l-[4px] bg-brand-secondary'
            : 'border-l-[2px] border-secondary'
        )}
        onClick={() => {
          onSelect(assistant);
        }}
      >
        <div className="flex items-center gap-1 overflow-x-hidden">
          {!isSystemAssistant && (
            <IconButton
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded((p) => {
                  const isCollapsing = p === assistant.assistant_id;
                  if (!isCollapsing) onSelect(assistant);
                  return isCollapsing ? undefined : assistant.assistant_id;
                });
              }}
              size="sm"
              color="neutral"
              variant="plain"
              sx={{
                padding: '0px',
                minWidth: '16px',
                minHeight: '16px',
                borderRadius: '4px',
              }}
            >
              {!isExpanded ? (
                <ChevronRightIcon className="h-4 w-4" />
              ) : (
                <ChevronDownIcon className="h-4 w-4" />
              )}
            </IconButton>
          )}
          <TruncatedTextWithTooltip
            text={getAssistantDisplayName(assistant)}
            className={cn(
              'py-1 text-sm font-medium ',
              isSystemAssistant ? 'text-tertiary' : 'text-primary'
            )}
            placement="right"
          />
        </div>
        {isActiveAssistant && (isSystemAssistant || !isExpanded) && (
          <ActiveBadge />
        )}
      </div>
      {isExpanded && !isSystemAssistant && renderVersionsList()}
    </div>
  );
};

const AssistantsList = ({
  graphId,
  assistants,
  selectedAssistantId,
  currentSelectedVersion,
  onSelectAssistant,
  setSelectedAssistantId,
  setCurrentSelectedVersion,
  onClose,
  hasMore,
  isLoading,
  onLoadNextPage,
}: {
  graphId: string | undefined;
  assistants: Assistant[] | undefined;
  selectedAssistantId: string | undefined;
  currentSelectedVersion: number | undefined;
  onSelectAssistant: ({
    assistant,
    version,
    activate,
  }: {
    assistant: Assistant;
    version?: number;
    activate?: boolean;
  }) => void;
  setSelectedAssistantId: (assistantId?: string) => void;
  setCurrentSelectedVersion: (version?: number) => void;
  onClose: () => void;
  isLoading: boolean;
  hasMore: boolean;
  onLoadNextPage: () => void;
}) => {
  const activeAssistantId = useAssistantStore(
    (state) => state.activeAssistantId
  );
  const { data: activeAssistant } = useGraphAssistant(activeAssistantId);
  const [_, userAssistants] = partition(assistants, checkIsSystemAssistant);
  const { assistants: systemAssistants } = useGraphAssistants({
    graphId,
    metadata: { created_by: 'system' },
  });

  const activeAssistantInList = activeAssistantId
    ? new Set(
        [...userAssistants, ...(systemAssistants ?? [])].map(
          (a) => a.assistant_id
        )
      ).has(activeAssistantId)
    : undefined;

  const openingAssistantFromNodeConfiguration = useAssistantStore(
    (state) => state.openingAssistantFromNodeConfiguration
  );

  const setNodeConfigurationModalOpen = useAssistantStore(
    (state) => state.setNodeConfigurationModalOpen
  );

  const [expandedAssistantId, setExpandedAssistantId] =
    useState(selectedAssistantId);

  const handleSelectAssistant = (assistant: Assistant, version?: number) => {
    setExpandedAssistantId(assistant.assistant_id);
    onSelectAssistant({ assistant, version });
  };
  const createNewButtonDisabled =
    selectedAssistantId === systemAssistants?.[0]?.assistant_id;
  const renderSystemAssistants = () => {
    return systemAssistants?.map((assistant) => {
      return (
        <AssistantsListItem
          key={assistant.assistant_id}
          assistant={assistant}
          isSelected={assistant.assistant_id === selectedAssistantId}
          onSelect={handleSelectAssistant}
          currentSelectedVersion={currentSelectedVersion}
          isExpanded={expandedAssistantId === assistant.assistant_id}
          setIsExpanded={setExpandedAssistantId}
        />
      );
    });
  };

  const renderBody = () => {
    if (userAssistants.length === 0) return null;

    return userAssistants.map((assistant) => {
      return (
        <AssistantsListItem
          key={assistant.assistant_id}
          assistant={assistant}
          isSelected={assistant.assistant_id === selectedAssistantId}
          onSelect={handleSelectAssistant}
          currentSelectedVersion={currentSelectedVersion}
          isExpanded={expandedAssistantId === assistant.assistant_id}
          setIsExpanded={setExpandedAssistantId}
        />
      );
    });
  };

  return (
    <div className="overflow-hidden">
      <div className="flex h-full flex-col rounded-l-xl border-r border-secondary">
        <div className="flex items-center justify-between px-2">
          <span className="pt-2 text-sm font-medium text-secondary">
            ASSISTANTS
          </span>
          <Button
            variant="plain"
            color="neutral"
            size="sm"
            onClick={() => {
              setSelectedAssistantId(undefined);
              setCurrentSelectedVersion(undefined);
              setExpandedAssistantId(undefined);
            }}
          >
            <PlusIcon className="h-4 w-4" />
            <span
              className={cn(
                'text-xs text-secondary',
                createNewButtonDisabled && 'text-disabled'
              )}
            >
              New
            </span>
          </Button>
        </div>

        <div className="flex h-full flex-col items-stretch gap-2 overflow-x-hidden py-2">
          <div className="overflow-y-auto">
            {renderSystemAssistants()}
            {/* if the loaded assistant is not in the list, render it at the top */}
            {!activeAssistantInList && activeAssistant && (
              <AssistantsListItem
                key={activeAssistantId}
                assistant={activeAssistant}
                isSelected={activeAssistantId === selectedAssistantId}
                onSelect={handleSelectAssistant}
                currentSelectedVersion={currentSelectedVersion}
                isExpanded={expandedAssistantId === activeAssistantId}
                setIsExpanded={setExpandedAssistantId}
              />
            )}
            {renderBody()}
            {hasMore && (
              <LoadMoreButton
                isInitialLoad
                isLoading={isLoading}
                onClick={onLoadNextPage}
                className="text-sm"
              />
            )}
          </div>
          {openingAssistantFromNodeConfiguration && (
            <div className="mt-auto">
              <button
                type="button"
                onClick={() => {
                  onClose();
                  setNodeConfigurationModalOpen(
                    openingAssistantFromNodeConfiguration
                  );
                }}
                className="flex flex-row items-center gap-2 rounded-md p-2 text-secondary hover:bg-secondary"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span className="text-sm">Back</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssistantsList;
