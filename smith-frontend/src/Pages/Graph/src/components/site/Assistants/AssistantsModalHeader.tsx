import { XMarkIcon } from '@heroicons/react/24/outline';
import { Assistant, AssistantVersion } from '@langchain/langgraph-sdk';
import { Switch } from '@mui/joy';

import { CopyInlineLink } from '@/components/CopyInlineLink';

import {
  useGraphAssistantSetLatestVersion,
  useGraphAssistantVersions,
} from '../../../api/assistants';
import { useAssistantDefaultConfigSchema } from '../../../hooks/assistants/useAssistantDefaultConfigSchema';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import { checkIsSystemAssistant, getAssistantDisplayName } from './utils';

const AssistantsModalHeader = ({
  selectedAssistant,
  currentSelectedVersion,
  assistants,
  onClose,
}: {
  selectedAssistant?: Assistant;
  currentSelectedVersion?: number;
  assistants: Assistant[];
  onClose: () => void;
}) => {
  const { assistant_id: selectedAssistantId, version: activeVersion } =
    selectedAssistant ?? {};
  const setConfig = useAssistantStore((state) => state.setConfig);
  const activeAssistantId = useAssistantStore(
    (state) => state.activeAssistantId
  );
  const setAssistantId = useAssistantStore(
    (state) => state.setActiveAssistantId
  );

  const systemAssistant = assistants?.find(checkIsSystemAssistant);
  const isSystemAssistant = checkIsSystemAssistant(selectedAssistant);

  const isAssistantVersionActive =
    activeAssistantId === selectedAssistantId &&
    currentSelectedVersion === activeVersion;

  const { data: assistantsVersions } =
    useGraphAssistantVersions(selectedAssistantId);

  const selectedAssistantVersion = assistantsVersions
    ?.map((v) => ({
      ...v,
      // for old deployed assistants, the name is not included in the version
      // so we use the name from the selected assistant
      name: v.name ?? selectedAssistant?.name,
    }))
    ?.find((a) => a.version === currentSelectedVersion);

  const { trigger: setLatest } =
    useGraphAssistantSetLatestVersion(selectedAssistant);

  const { defaultConfigSchema } =
    useAssistantDefaultConfigSchema(selectedAssistantId);
  const activateAssistant = (assistant: AssistantVersion) => {
    setAssistantId(assistant.assistant_id);
    setConfig(assistant.config ?? { configurable: defaultConfigSchema ?? {} });
  };

  const handleToggleActiveAssistant = (
    assistant: AssistantVersion,
    version?: number
  ) => {
    // if the assistant and version is already active, find the system assistant and activate it
    if (
      assistant.assistant_id === activeAssistantId &&
      version === activeVersion
    ) {
      // find system assistant
      if (systemAssistant?.assistant_id) {
        activateAssistant(systemAssistant);
      }
    }
    // otherwise activate the assistant (and the specifed version if provided)
    else {
      activateAssistant(assistant);
      version && setLatest(version);
    }
  };

  const renderAssistantName = () => {
    return (
      <div className="flex items-center gap-1">
        <span className="max-w-[250px] truncate rounded-sm text-xl font-semibold">
          {selectedAssistant
            ? getAssistantDisplayName(selectedAssistant)
            : 'Create New Assistant'}
        </span>
        {!isSystemAssistant && currentSelectedVersion && (
          <span className="text-xl">v{currentSelectedVersion}</span>
        )}
      </div>
    );
  };

  return (
    <div className="flex items-start justify-between gap-1 border-b border-secondary px-4 pb-2">
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-6">
          {renderAssistantName()}
          {selectedAssistantVersion && (
            <div className="flex items-center gap-2">
              <Switch
                checked={isAssistantVersionActive}
                onChange={() =>
                  handleToggleActiveAssistant(
                    selectedAssistantVersion,
                    currentSelectedVersion
                  )
                }
                // can't disable if system assistant is active (since we need something to be active)
                disabled={isSystemAssistant && isAssistantVersionActive}
                size="sm"
              />

              <span className="text-sm">Active</span>
            </div>
          )}
        </div>
        {isSystemAssistant && (
          <div className="text-sm text-secondary">
            <span>
              Edit settings to run this graph with. Optionally, save the
              configuration as an assistant.
            </span>{' '}
            <a
              className="underline"
              href="https://langchain-ai.github.io/langgraph/concepts/assistants/#assistants"
            >
              Learn more
            </a>
          </div>
        )}
        {selectedAssistantId && (
          <div className="w-fit py-2">
            <CopyInlineLink value={selectedAssistantId}>
              Assistant ID
            </CopyInlineLink>
          </div>
        )}
      </div>

      <button
        type="button"
        onClick={onClose}
        className="hover:bg-secondary_hover rounded-md p-1 text-ls-black outline-none transition-all"
      >
        <XMarkIcon className="h-6 w-6" />
      </button>
    </div>
  );
};

export default AssistantsModalHeader;
