import { LinearProgress, Modal, ModalDialog } from '@mui/joy';

import { useGraphAssistant, useGraphAssistants } from '../../../api/assistants';
import { useSelectAssistant } from '../../../hooks/assistants/useSelectAssistant';
import {
  AssistantModalState,
  useAssistantStore,
} from '../../../store/assistants/assistantsStore';
import AssistantsModalBody from './AssistantsModalBody';
import AssistantsList from './AssistantsModalList';
import { checkIsSystemAssistant } from './utils';

const AssistantsModal = () => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId, {
    keepPreviousData: true,
  });
  const graphId = assistant?.graph_id;
  const setOpeningAssistantFromNodeConfiguration = useAssistantStore(
    (state) => state.setOpeningAssistantFromNodeConfiguration
  );
  const selectedAssistantId = useAssistantStore(
    (state) => state.selectedAssistantId
  );
  const setSelectedAssistantId = useAssistantStore(
    (state) => state.setSelectedAssistantId
  );
  const currentSelectedVersion = useAssistantStore(
    (state) => state.currentSelectedVersion
  );
  const setCurrentSelectedVersion = useAssistantStore(
    (state) => state.setCurrentSelectedVersion
  );
  const config = useAssistantStore((state) => state.config);
  const setConfig = useAssistantStore((state) => state.setConfig);
  const stagedAssistant = useAssistantStore((state) => state.stagedAssistant);
  const assistantModalState = useAssistantStore(
    (state) => state.assistantModalState
  );
  const setAssistantModalState = useAssistantStore(
    (state) => state.setAssistantModalState
  );
  const isOpen = assistantModalState !== AssistantModalState.CLOSED;

  const {
    assistants,
    isLoading: isLoadingAssistants,
    error: assistantsError,
    hasMore,
    size,
    setSize,
    isValidating: isAssistantsValidating,
  } = useGraphAssistants({ graphId, options: { keepPreviousData: true } });

  const { data: selectedAssistant, isLoading: isLoadingSelectedAssistant } =
    useGraphAssistant(selectedAssistantId);

  const onClose = () => {
    setAssistantModalState(AssistantModalState.CLOSED);
    setOpeningAssistantFromNodeConfiguration(undefined);
    if (checkIsSystemAssistant(selectedAssistant)) {
      setConfig({ ...config, ...stagedAssistant?.config });
    }
  };

  const { onSelectAssistant } = useSelectAssistant({
    graphId,
  });

  const renderModalContent = () => {
    if (isLoadingAssistants) {
      return (
        <div className="w-full">
          <LinearProgress />
        </div>
      );
    }
    if (assistantsError) {
      return <div>Error loading assistants</div>;
    }
    if (!assistants) {
      return <div>No assistants found for this graph</div>;
    }
    return (
      <div className="grid h-full grid-cols-[2fr,5fr]">
        <AssistantsList
          graphId={graphId}
          assistants={assistants}
          selectedAssistantId={selectedAssistantId}
          currentSelectedVersion={currentSelectedVersion}
          onSelectAssistant={onSelectAssistant}
          setSelectedAssistantId={setSelectedAssistantId}
          setCurrentSelectedVersion={setCurrentSelectedVersion}
          onClose={onClose}
          isLoading={isAssistantsValidating}
          hasMore={hasMore}
          onLoadNextPage={() => setSize(size + 1)}
        />
        <AssistantsModalBody
          selectedAssistant={selectedAssistant}
          isLoadingSelectedAssistant={isLoadingSelectedAssistant}
          assistants={assistants}
          onClose={onClose}
          onSelectAssistant={onSelectAssistant}
          currentSelectedVersion={currentSelectedVersion}
        />
      </div>
    );
  };

  return (
    <Modal open={isOpen} onClose={onClose}>
      <ModalDialog
        size="lg"
        sx={{
          padding: '8px',
          paddingY: '16px',
          width: '900px',
          height: '600px',
        }}
      >
        <div className="h-full">{renderModalContent()}</div>
      </ModalDialog>
    </Modal>
  );
};

export default AssistantsModal;
