import { Assistant, AssistantBase, Config } from '@langchain/langgraph-sdk';
import { Alert, Input, LinearProgress } from '@mui/joy';

import { useCallback, useEffect, useMemo, useState } from 'react';

import Banner from '@/components/Banner';
import { CodeData } from '@/components/Code/Code';
import ExternalLinkIcon from '@/icons/ExternalLinkIcon.svg?react';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { useGraphAssistantVersions } from '../../../api/assistants';
import { useGraphAssistantsDebugSchema } from '../../../api/useGraphSwr';
import { SCHEMA_ERROR_MESSAGE } from '../../../constants';
import { useAssistantDefaultConfigSchema } from '../../../hooks/assistants/useAssistantDefaultConfigSchema';
import {
  getConfigFieldNodes,
  getSchemaProperties,
} from '../../../json-schema-utils';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import { isConfigSchemaEmpty } from '../../../utils';
import { MultiStringArray } from '../../debug/common/MultiStringArray';
import { SchemaEditor } from '../../debug/common/schema-editor';
import { templateSchemaRenderer } from '../../debug/common/template-renderer';
import AssistantModalFooter from './AssistantModalFooter';
import AssistantsModalHeader from './AssistantsModalHeader';
import { DEFAULT_ASSISTANT_NAME } from './constants';
import {
  checkIsSystemAssistant,
  getAssistantDisplayName,
  getAssistantNameError,
  getNewAssistantName,
} from './utils';

const AssistantVersionConfig = ({
  assistantId,
  value,
  onChange,
  nameError,
  isNewAssistant,
  systemAssistantId,
}: {
  assistantId?: string;
  value: { config: Config; name: string };
  onChange: (config: Partial<Config>, name: string) => void;
  nameError?: string;
  isNewAssistant: boolean;
  systemAssistantId?: string;
}) => {
  const { config, name } = value;
  const { recursion_limit, configurable, tags } = config;
  const idToFetch = assistantId ?? systemAssistantId;
  const {
    data: schemas,
    isLoading: isLoadingSchema,
    error: schemaError,
  } = useGraphAssistantsDebugSchema(idToFetch ? [idToFetch] : undefined);
  const schema = schemas?.[0];

  const renderConfigurable = () => {
    const codeEditor = () => {
      return (
        <div className="overflow-hidden rounded-md border border-secondary">
          <CodeData
            value={configurable}
            onChange={(v) => onChange({ configurable: v }, name)}
          />
        </div>
      );
    };
    if (!configurable) return null;
    if (schemaError) {
      return (
        <>
          <Alert color="warning">
            <span>{`${SCHEMA_ERROR_MESSAGE} You can still manually edit your configuration.`}</span>
          </Alert>
          {codeEditor()}
        </>
      );
    }

    if (schema && isConfigSchemaEmpty(schema)) {
      return (
        <>
          <Alert color="warning">
            <p>
              No configuration schema or an empty schema found for assistant.{' '}
              <br />
              <a
                className="inline underline"
                href="https://langchain-ai.github.io/langgraph/how-tos/configuration/"
              >
                Learn more how to add schema{' '}
                <ExternalLinkIcon className="inline-block h-4 w-4" />
              </a>
            </p>
          </Alert>
          {codeEditor()}
        </>
      );
    }

    return (
      <SchemaEditor
        input={configurable}
        onChange={(v) => onChange({ configurable: v }, name)}
        schema={schema?.config}
        rootSchema={schema?.configRoot}
        renderer={templateSchemaRenderer}
        options={{ showNode: true }}
      />
    );
  };

  if (isLoadingSchema) {
    return <LinearProgress />;
  }

  return (
    <div className="flex flex-col gap-4">
      {isNewAssistant && (
        <div className="flex w-full flex-col items-start gap-1">
          <span className="text-sm font-medium">Assistant Name</span>
          <Input
            placeholder="Assistant Name"
            value={name}
            onChange={(e) => onChange(config, e.target.value)}
            className="w-full"
            size="sm"
            autoFocus
            error={!!nameError}
          />
          {nameError && <span className="text-xs text-error">{nameError}</span>}
        </div>
      )}
      {renderConfigurable()}
      <div className="flex flex-col gap-2">
        <div className="flex flex-col gap-1">
          <span className="text-sm">Recursion limit</span>
          <span className="text-xs text-tertiary">
            The maximum number of times the assistant can call itself
            recursively. This is to prevent infinite loops.
          </span>
        </div>
        <input
          disabled={false}
          className="w-full rounded-lg border border-secondary bg-background p-2 px-2.5 text-sm transition-colors focus-within:border-brand"
          placeholder="Recursion limit"
          type="number"
          // 25 is the default value of the sdk
          value={recursion_limit ?? 25}
          onChange={(e) => {
            const value = parseInt(e.target.value);
            if (value < 0) return;
            onChange({ recursion_limit: value }, name);
          }}
        />
      </div>
      <div className="flex flex-col gap-2">
        <div className="flex flex-col gap-1">
          <span className="text-sm">Tags</span>
          <span className="text-xs text-tertiary">
            Add tags to categorize runs for easier filtering and organization.
          </span>
        </div>
        <MultiStringArray
          value={tags}
          onChange={(v) => onChange({ tags: v }, name)}
          enum={[]}
          placeholder="Add tags"
        />
      </div>
    </div>
  );
};

const EditAssistantForm = ({
  assistant,
  assistants,
  onSelectAssistant,
  onClose,
}: {
  assistant?: AssistantBase;
  assistants: Assistant[];
  onSelectAssistant: ({
    assistant,
    version,
    activate,
  }: {
    assistant?: Assistant;
    version?: number;
    activate?: boolean;
  }) => void;
  onClose: () => void;
}) => {
  const activeAssistantId = useAssistantStore(
    (state) => state.activeAssistantId
  );
  const { assistant_id: assistantId } = assistant ?? {};
  const isSystemAssistant = checkIsSystemAssistant(assistant);
  const systemAssistants = assistants.filter(checkIsSystemAssistant);
  const { defaultConfigSchema } = useAssistantDefaultConfigSchema(assistantId);

  const openingAssistantFromNodeConfiguration = useAssistantStore(
    (state) => state.openingAssistantFromNodeConfiguration
  );
  const config = useAssistantStore((state) => state.config);
  const stagedAssistant = useAssistantStore((state) => state.stagedAssistant);
  const setStagedAssistant = useAssistantStore(
    (state) => state.setStagedAssistant
  );
  const initializeLocaValue = useCallback(() => {
    const getInitialConfigurable = () => {
      if (
        openingAssistantFromNodeConfiguration &&
        stagedAssistant?.config?.configurable &&
        activeAssistantId === assistantId
      ) {
        return stagedAssistant.config.configurable;
      }
      if (!assistant) return {};
      if (isSystemAssistant && activeAssistantId === assistantId) {
        return config?.configurable ?? defaultConfigSchema ?? {};
      }
      if (!Object.keys(assistant.config.configurable ?? {}).length) {
        return defaultConfigSchema ?? {};
      }
      return assistant.config.configurable ?? {};
    };

    const name = getAssistantDisplayName(assistant);
    const generateName = !name || name === DEFAULT_ASSISTANT_NAME;
    const displayName = generateName ? getNewAssistantName(assistants) : name;

    const configurable = getInitialConfigurable();

    const newConfig = {
      ...assistant?.config,
      configurable,
    };
    return {
      ...assistant,
      name: displayName,
      config: newConfig,
    };
  }, [
    assistant,
    assistants,
    defaultConfigSchema,
    activeAssistantId,
    config,
    isSystemAssistant,
    assistantId,
    openingAssistantFromNodeConfiguration,
    stagedAssistant,
  ]);

  const [value, setValue] = useState<Partial<AssistantBase>>(
    initializeLocaValue()
  );

  // keep global state in sync with local state
  useEffect(() => {
    setStagedAssistant(value);
  }, [value, setStagedAssistant]);

  useEffect(() => {
    setValue(initializeLocaValue());
  }, [defaultConfigSchema]);

  const nameError = useMemo(() => {
    const existingAssistantNames = assistants
      .filter((a) => a.assistant_id !== assistantId)
      .map((a) => a.name);
    return getAssistantNameError({
      assistantName: value.name,
      existingAssistantNames,
    });
  }, [value.name, assistants, assistantId]);

  const handleChange = (newConfig: Partial<Config>, name: string) => {
    setValue((prev) => ({
      ...prev,
      name,
      config: { ...prev.config, ...newConfig },
    }));
  };

  return (
    <>
      <div className="grow overflow-auto px-4">
        <div className="flex flex-col gap-4 text-sm">
          <AssistantVersionConfig
            assistantId={assistantId}
            value={{
              config: value.config ?? {},
              name: value.name ?? '',
            }}
            onChange={handleChange}
            nameError={nameError}
            isNewAssistant={isSystemAssistant || !assistantId}
            systemAssistantId={systemAssistants[0]?.assistant_id}
          />
        </div>
      </div>

      <AssistantModalFooter
        assistant={assistant}
        value={value}
        hasNameError={!!nameError || value.name === DEFAULT_ASSISTANT_NAME}
        saveButtonTooltipText={
          value.name === DEFAULT_ASSISTANT_NAME
            ? 'Change the name of the assistant to save'
            : undefined
        }
        onSelectAssistant={onSelectAssistant}
        onClose={onClose}
      />
    </>
  );
};

const AssistantsModalBody = ({
  selectedAssistant,
  isLoadingSelectedAssistant,
  currentSelectedVersion,
  assistants,
  onClose,
  onSelectAssistant,
}: {
  selectedAssistant?: Assistant;
  isLoadingSelectedAssistant: boolean;
  currentSelectedVersion?: number;
  assistants: Assistant[];
  onClose: () => void;
  onSelectAssistant: ({
    assistant,
    version,
    activate,
  }: {
    assistant?: Assistant;
    version?: number;
    activate?: boolean;
  }) => void;
}) => {
  const { assistant_id: selectedAssistantId } = selectedAssistant ?? {};

  const {
    data: assistantsVersions,
    isLoading: isLoadingVersions,
    isValidating: isValidatingVersions,
  } = useGraphAssistantVersions(selectedAssistantId);

  const selectedAssistantVersion = assistantsVersions
    ?.map((v) => ({
      ...v,
      // for old deployed assistants, the name is not included in the version
      // so we use the name from the selected assistant
      name: v.name ?? selectedAssistant?.name,
    }))
    .find((a) => a.version === currentSelectedVersion);

  const { data: schemas, isLoading: isLoadingSchema } =
    useGraphAssistantsDebugSchema(
      selectedAssistantId ? [selectedAssistantId] : undefined
    );
  const schema = schemas?.[0];
  const [hasSeenNodeConfigBanner, setHasSeenNodeConfigBanner] =
    useLocalStorageState('ls:hasSeenNodeConfigBanner', false);
  const hasNodeConfig = Object.values(
    getSchemaProperties(schema?.config) ?? {}
  ).some((a) => getConfigFieldNodes(a));

  if (
    isLoadingVersions ||
    isLoadingSelectedAssistant ||
    (isValidatingVersions && !selectedAssistantVersion) ||
    isLoadingSchema
  ) {
    return (
      <div className="h-full">
        <LinearProgress />
      </div>
    );
  }

  const renderConfig = () => {
    return (
      <EditAssistantForm
        key={`${selectedAssistantId}:${currentSelectedVersion}`}
        assistant={selectedAssistantVersion}
        assistants={assistants}
        onSelectAssistant={onSelectAssistant}
        onClose={onClose}
      />
    );
  };

  return (
    <div className="flex flex-grow flex-col gap-4 overflow-hidden">
      <div>
        <AssistantsModalHeader
          selectedAssistant={selectedAssistant}
          currentSelectedVersion={currentSelectedVersion}
          assistants={assistants}
          onClose={onClose}
        />

        <Banner
          title="Have assistant settings that are specific to a node in your graph?"
          description={
            <span>
              <a
                href="https://langchain-ai.github.io/langgraph/cloud/how-tos/iterate_graph_studio/"
                className="inline-block underline"
              >
                Learn more
              </a>{' '}
              about how to configure fields for nodes.
            </span>
          }
          open={!hasSeenNodeConfigBanner && !hasNodeConfig}
          intent="info"
          closeable={true}
          className="mx-4 mt-2"
          onClose={() => setHasSeenNodeConfigBanner(true)}
        />
      </div>
      {renderConfig()}
    </div>
  );
};

export default AssistantsModalBody;
