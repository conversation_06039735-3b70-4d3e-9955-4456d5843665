import { useContext } from 'react';

import {
  useAssistantSupportsChat,
  useProjectSupportsChat,
} from '@/Pages/HostProject/hooks/useProjectSupportsChat';
import { GroupedTabs } from '@/components/GroupedTabs';

import { useGraphAssistant } from '../../api/assistants';
import { StudioMode, StudioModeContext } from '../../hooks/useStudioMode';
import { useAssistantStore } from '../../store/assistants/assistantsStore';

export const GraphChatModeToggle = () => {
  const { studioMode, setStudioMode } = useContext(StudioModeContext);
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const setAssistantId = useAssistantStore(
    (state) => state.setActiveAssistantId
  );
  const setSelectedAssistantId = useAssistantStore(
    (state) => state.setSelectedAssistantId
  );

  const { data: assistant } = useGraphAssistant(assistantId);

  const { supportsChat, isLoading: isLoadingAssistantSupport } =
    useAssistantSupportsChat(assistantId);

  const { supportedGraphs, isLoading: isLoadingProjectSupport } =
    useProjectSupportsChat();

  const disabledMessage = (() => {
    if (isLoadingAssistantSupport || isLoadingProjectSupport) {
      return 'Loading...';
    }
    if (!supportedGraphs?.length) {
      return 'Create a graph with a messages key to chat with.';
    }
    if (!supportsChat) {
      return 'Chat is not supported for this graph as it does not have a messages key';
    }
    return undefined;
  })();

  return (
    <GroupedTabs
      options={[
        {
          display: 'Graph',
          value: StudioMode.GRAPH,
        },
        {
          display: 'Chat',
          value: StudioMode.CHAT,
          disabled: !!disabledMessage,
          tooltip: disabledMessage,
        },
      ]}
      value={studioMode}
      onChange={(value) => {
        setStudioMode(value as StudioMode);
        if (value === StudioMode.CHAT) {
          // if current assistant is not supported, switch to the first supported assistant
          const assistantToSwitchTo = supportsChat
            ? assistant
            : supportedGraphs?.[0];
          if (assistantToSwitchTo) {
            setAssistantId(assistantToSwitchTo.assistant_id);
            setSelectedAssistantId(assistantToSwitchTo.assistant_id);
          }
        }
        if (value === StudioMode.GRAPH) {
          setAssistantId(assistantId);
          setSelectedAssistantId(assistantId);
        }
      }}
    />
  );
};
