import { ArrowLeftIcon } from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import { useContext } from 'react';
import { useNavigate } from 'react-router-dom';

import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { useHostProject, useOrganizationId } from '@/hooks/useSwr';
import { appOrganizationPath, hostAppIndexPath } from '@/utils/constants';

import { LangSmithAwareContext } from '../../langsmith';
import { GraphSystemAssistantSelect } from './GraphAssistantSelect';
import { GraphChatModeToggle } from './GraphChatModeToggle';

export const GraphTopBar = ({
  hostProjectId,
  standalone = false,
}: {
  hostProjectId?: string | null;
  standalone?: boolean;
}) => {
  const { value: enableStudioExperiment } = useOrgConfig(
    OrgConfigs.enable_studio_experiments
  );
  const { ExperimentModalAndButton } = useContext(LangSmithAwareContext);
  return (
    <div className="flex w-full items-center gap-2">
      {!standalone && <BackButton hostProjectId={hostProjectId} />}

      <GraphSystemAssistantSelect />
      <GraphChatModeToggle />
      {enableStudioExperiment && ExperimentModalAndButton && (
        <div className="ml-auto">
          <ExperimentModalAndButton />
        </div>
      )}
    </div>
  );
};

const BackButton = ({ hostProjectId }: { hostProjectId?: string | null }) => {
  const { data: project } = useHostProject(hostProjectId ?? null);
  const navigate = useNavigate();
  const organizationId = useOrganizationId();
  return (
    <>
      <Button
        variant="plain"
        color="neutral"
        size="sm"
        onClick={() => {
          navigate(
            `/${appOrganizationPath}/${organizationId}/${hostAppIndexPath}/deployments${
              hostProjectId ? `/${hostProjectId}` : ''
            }`
          );
        }}
        startDecorator={<ArrowLeftIcon className="h-5 w-5" />}
      >
        <span className="text-primary">
          {hostProjectId ? project?.name : 'LangGraph Studio'}
        </span>
      </Button>
      <span>/</span>
    </>
  );
};
