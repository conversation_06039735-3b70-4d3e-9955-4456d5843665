import {
  ArrowNarrowRightIcon,
  CheckCircleIcon,
  InfoCircleIcon,
} from '@langchain/untitled-ui-icons';
import {
  Button,
  CircularProgress,
  Modal,
  ModalClose,
  ModalDialog,
} from '@mui/joy';

import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { TextDisplayMode } from '@/Pages/DatasetSessionCompare/constants';
import { DatasetAndSplitPicker } from '@/Pages/Playground/components/DatasetAndSplitPicker';
import useToast from '@/components/Toast';
import { useExamples, useOrganizationId } from '@/hooks/useSwr';
import { ExampleSchema } from '@/types/schema';
import {
  appComparePath,
  appDatasetsPath,
  appOrganizationPath,
} from '@/utils/constants';
import { xCount } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import { useGraphAssistant } from '../../api';
import { useRunStudioExperiment } from '../../hooks/useRunStudioExperiment';
import { useAssistantStore } from '../../store/assistants/assistantsStore';
import { checkIsSystemAssistant } from './Assistants/utils';

const EXPERIMENT_PREFIX = 'studio';

export const ExperimentModalAndButton = () => {
  const { createToast } = useToast();
  const organizationId = useOrganizationId();

  const [experimentModalOpen, setExperimentModalOpen] = useState(false);

  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);

  const [selectedDatasetId, setSelectedDatasetId] = useState<
    string | undefined
  >();
  const [selectedDatasetSplits, setSelectedDatasetSplits] = useState<string[]>(
    []
  );

  const [isRunningInputs, setIsRunningInputs] = useState(false);

  const [experimentName, setExperimentName] = useState(EXPERIMENT_PREFIX);
  useEffect(() => {
    const getAssistantDisplayName = () => {
      if (!assistant) return;
      if (checkIsSystemAssistant(assistant)) {
        return assistant.graph_id;
      }
      return assistant.name;
    };
    if (assistant) {
      setExperimentName(`${EXPERIMENT_PREFIX}::${getAssistantDisplayName()}`);
    }
  }, [assistant, setExperimentName]);

  const { data: examples } = useExamples(
    selectedDatasetId
      ? {
          dataset: selectedDatasetId,
          splits:
            selectedDatasetSplits.length > 0
              ? selectedDatasetSplits
              : undefined,
        }
      : null,
    undefined,
    { keepPreviousData: false }
  );

  const resetExperimentState = () => {
    setIsRunningInputs(false);
    setExperimentModalOpen(false);
    setSelectedDatasetId(undefined);
    setSelectedDatasetSplits([]);
  };

  const { runInputs, creatingExperiment, progress } = useRunStudioExperiment({
    setIsRunningInputs,
    setExperimentModalOpen,
  });

  const handleConfirm = async () => {
    if (
      !selectedDatasetId ||
      !examples?.length ||
      !experimentName ||
      !assistantId
    )
      return;
    try {
      const experiment = await runInputs({
        assistantId,
        examples,
        selectedDatasetId,
        experimentName,
      });
      resetExperimentState();
      createToast({
        title: (
          <div className="flex flex-row items-center gap-2">
            <CheckCircleIcon className="size-4 shrink-0 text-success" />
            <span className="text-md font-semibold">Experiment completed!</span>
          </div>
        ),
        description: (
          <div className="flex flex-col gap-3">
            <span className="text-sm">
              Evaluators attached to the dataset will get scheduled and run in
              the background. Check your experiment to see the results.
            </span>
            <Link
              className="flex flex-row items-center gap-1 text-sm underline"
              to={`/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${selectedDatasetId}/${appComparePath}?selectedSessions=${experiment.id}&textDisplayMode=${TextDisplayMode.FULL}`}
            >
              View experiment
              <ArrowNarrowRightIcon className="size-4" />
            </Link>
          </div>
        ),
        type: 'success',
        duration: 10000,
      });
    } catch (error) {
      console.error(error);
      createToast({
        title: 'Error',
        description: 'Failed to run experiment. Please try again.',
        type: 'error',
      });
    }
  };

  return (
    <>
      {!isRunningInputs ? (
        <Button
          variant="outlined"
          color="primary"
          onClick={() => {
            setExperimentModalOpen(true);
          }}
          size="sm"
        >
          Run experiment
        </Button>
      ) : (
        <div className="flex flex-row items-center gap-2">
          <div className="flex flex-row items-center gap-1 rounded-md border border-secondary bg-tertiary px-1.5 py-0.5 text-secondary">
            <InfoCircleIcon className="size-3" />
            <span className="text-xxs font-medium">Keep tab open</span>
          </div>
          <div className="flex items-center gap-2 rounded-md border border-brand-subtle bg-brand-tertiary px-3 py-1 text-brand-tertiary">
            <CircularProgress
              size="sm"
              variant="outlined"
              determinate
              value={Math.floor((progress.completed / progress.total) * 100)}
              sx={{
                '--CircularProgress-size': '1.25rem',
              }}
            />
            <span className="text-xs font-medium">
              Running Experiment {progress.completed}/{progress.total}
            </span>
          </div>
        </div>
      )}
      {experimentModalOpen && (
        <ExperimentModal
          onClose={() => setExperimentModalOpen(false)}
          isRunningInputs={isRunningInputs}
          selectedDatasetId={selectedDatasetId}
          selectedDatasetSplits={selectedDatasetSplits}
          setSelectedDatasetId={setSelectedDatasetId}
          setSelectedDatasetSplits={setSelectedDatasetSplits}
          experimentName={experimentName}
          handleConfirm={handleConfirm}
          creatingExperiment={creatingExperiment}
          examples={examples}
        />
      )}
    </>
  );
};

const ExperimentModal = ({
  onClose,
  isRunningInputs,
  selectedDatasetId,
  selectedDatasetSplits,
  setSelectedDatasetId,
  setSelectedDatasetSplits,
  experimentName,
  handleConfirm,
  creatingExperiment,
  examples,
}: {
  onClose: () => void;
  isRunningInputs: boolean;
  selectedDatasetId: string | undefined;
  selectedDatasetSplits: string[];
  setSelectedDatasetId: (datasetId?: string) => void;
  setSelectedDatasetSplits: (splits: string[]) => void;
  experimentName: string;
  handleConfirm: () => void;
  creatingExperiment: boolean;
  examples: ExampleSchema[] | undefined;
}) => {
  const handleClose = () => {
    if (isRunningInputs) return;
    onClose();
  };

  return (
    <Modal open={true} onClose={handleClose}>
      <ModalDialog
        sx={{
          width: '550px',
          maxHeight: '5000px',
        }}
      >
        <div className="flex h-full flex-col gap-5">
          <div className="flex flex-row items-center justify-between">
            <div className="flex flex-col gap-1">
              <span className="text-md font-semibold">Run experiment</span>
              <span className="text-sm text-secondary">
                Test your assistant over a dataset and run configured evaluators
                to measure your application's performance.
              </span>
            </div>
            <ModalClose />
          </div>
          <div className="flex flex-col gap-3">
            <div className="flex flex-col gap-1">
              <span className="text-sm text-secondary">Dataset</span>
              <DatasetAndSplitPicker
                // this prop is also used when clicking 'x' on the selected dataset
                onCreateNew={() => {
                  setSelectedDatasetId(undefined);
                }}
                hideCreateNew={true}
                datasetId={selectedDatasetId}
                isCreatingNewDataset={false}
                setDatasetIdAndUpdateUrl={setSelectedDatasetId}
                splits={selectedDatasetSplits}
                setSplits={setSelectedDatasetSplits}
              />
              <span
                className={cn(
                  'text-xs text-tertiary',
                  !examples?.length && 'invisible'
                )}
              >
                {xCount('example', examples?.length ?? 0)} selected
              </span>
            </div>
            {selectedDatasetId && (
              <div className="flex flex-col gap-1 rounded-md bg-secondary p-4 text-sm">
                <div className="flex flex-row items-center gap-2 text-primary">
                  <InfoCircleIcon className="size-4 text-brand-secondary" />
                  <span className="font-medium">
                    Keep this tab open until all runs have been initiated.
                  </span>
                </div>
                <span className="text-secondary">
                  Navigating away will result in an incomplete experiment.
                </span>
              </div>
            )}
          </div>
          <div className="mt-auto flex flex-row justify-end gap-2">
            <Button onClick={onClose} variant="outlined" color="neutral">
              Cancel
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={
                !selectedDatasetId || !examples?.length || !experimentName
              }
              loading={creatingExperiment}
            >
              Start
            </Button>
          </div>
        </div>
      </ModalDialog>
    </Modal>
  );
};
