import { <PERSON>ton, Modal, ModalClose, <PERSON>dal<PERSON><PERSON><PERSON>, Too<PERSON><PERSON> } from '@mui/joy';

import { JSONSchema7 } from 'json-schema';
import { isEqual } from 'lodash-es';
import { useMemo, useState } from 'react';
import 'reactflow/dist/style.css';

import { useGraphAssistants } from '@/Pages/Graph/src/api/assistants';
import { SchemaBundle } from '@/Pages/Graph/src/control/state';
import { useAssistantDefaultConfigSchema } from '@/Pages/Graph/src/hooks/assistants/useAssistantDefaultConfigSchema';
import { useSaveOrCreateAssistant } from '@/Pages/Graph/src/hooks/assistants/useSaveOrCreateAssistant';
import {
  AssistantModalState,
  useAssistantStore,
} from '@/Pages/Graph/src/store/assistants/assistantsStore';
import { getConfigFieldsForNode } from '@/Pages/Graph/src/utils';
import { CodeDataEditor } from '@/components/Code/Code';

import {
  checkIsSystemAssistant,
  getNewAssistantName,
} from '../../../site/Assistants/utils';
import { TraceLogAvatar } from '../../common/TraceLogAvatar';
import { SchemaEditor } from '../../common/schema-editor';
import { templateSchemaRenderer } from '../../common/template-renderer';

type NodeConfigurationProps = {
  onClose: () => void;
  nodeName: string;
  schema?: SchemaBundle;
  configFields: Record<string, unknown>;
  configFieldsSchema: JSONSchema7 | undefined;
};

export const NodeConfiguration = ({
  onClose,
  schema,
  nodeName,
  configFields,
  configFieldsSchema,
}: NodeConfigurationProps) => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const config = useAssistantStore((state) => state.config);
  const { config: configSchema, configRoot } = schema ?? {};

  const setAssistantModalState = useAssistantStore(
    (state) => state.setAssistantModalState
  );
  const setOpeningAssistantFromNodeConfiguration = useAssistantStore(
    (state) => state.setOpeningAssistantFromNodeConfiguration
  );
  const stagedAssistant = useAssistantStore((state) => state.stagedAssistant);
  const setStagedAssistant = useAssistantStore(
    (state) => state.setStagedAssistant
  );

  const { data: assistants } = useGraphAssistants();
  const assistant = assistants?.find((a) => a.assistant_id === assistantId);

  const { defaultConfigSchema } = useAssistantDefaultConfigSchema(assistantId);
  const newAssistantName = assistants
    ? getNewAssistantName(assistants)
    : 'New Assistant';
  const createNewAssistant = !assistantId || checkIsSystemAssistant(assistant);

  const getConfigInput = () => {
    // if coming from the assistant modal, use those values
    if (stagedAssistant?.config?.configurable) {
      return getConfigFieldsForNode({
        configurable: stagedAssistant.config.configurable,
        configSchema,
        nodeId: nodeName,
      });
    }
    return configFields;
  };

  const [configInput, setConfigInput] = useState<Record<string, unknown>>(
    getConfigInput()
  );
  const assistantValue = useMemo(() => {
    if (createNewAssistant) {
      return {
        name: newAssistantName,
        config: {
          configurable: {
            ...defaultConfigSchema,
            ...configInput,
          },
        },
      };
    }
    return {
      config: {
        ...config,
        configurable: {
          ...config?.configurable,
          ...configInput,
        },
      },
    };
  }, [
    config,
    configInput,
    createNewAssistant,
    defaultConfigSchema,
    newAssistantName,
  ]);

  const hasChanges = useMemo(() => {
    return !isEqual(configFields, configInput);
  }, [configFields, configInput]);

  const { saveOrCreateAssistant, isSavingAssistant, isCreatingAssistant } =
    useSaveOrCreateAssistant({
      assistantId,
      value: assistantValue,
      save: !createNewAssistant,
    });
  return (
    <Modal open={true} onClose={onClose}>
      <ModalDialog
        sx={{
          width: '900px',
          maxHeight: '90vh',
        }}
      >
        <div className="flex h-full flex-col gap-8 overflow-y-hidden">
          <div className="flex flex-col gap-1">
            <div className="flex flex-row items-center gap-2">
              <TraceLogAvatar nodeName={nodeName} isSubgraph={false} />
              <div className="text-lg font-medium">{`${nodeName} Configuration`}</div>
              <ModalClose />
            </div>
            <span className="text-xs text-secondary">
              Edit assistant settings specific to this node and save to create a
              new assistant version.
            </span>
          </div>
          <div className="flex-1 overflow-y-auto">
            {configFieldsSchema ? (
              <SchemaEditor
                schema={configFieldsSchema}
                onChange={setConfigInput}
                input={configInput}
                renderer={templateSchemaRenderer}
                rootSchema={configRoot}
              />
            ) : (
              <div>
                <CodeDataEditor
                  value={JSON.stringify(configInput, null, 2)}
                  language={'json'}
                />
              </div>
            )}
          </div>
          <div className="sticky bottom-0 flex flex-row items-center gap-2 bg-background">
            <button
              type="button"
              className="flex flex-row items-center gap-2 rounded-md p-2 text-sm text-secondary hover:bg-secondary"
              onClick={() => {
                setAssistantModalState(AssistantModalState.OPEN);
                setOpeningAssistantFromNodeConfiguration(nodeName);
                setStagedAssistant(assistantValue);
                onClose();
              }}
            >
              <span>View full assistant settings</span>
            </button>
            <div className="ml-auto flex flex-row items-center gap-2">
              <Button variant="outlined" onClick={onClose} color="neutral">
                Cancel
              </Button>
              <Tooltip
                title={!hasChanges ? 'No changes to save' : ''}
                placement="top"
              >
                <Button
                  variant="solid"
                  onClick={async () => {
                    await saveOrCreateAssistant();
                    onClose();
                  }}
                  loading={isSavingAssistant || isCreatingAssistant}
                  disabled={!hasChanges}
                >
                  Save
                </Button>
              </Tooltip>
            </div>
          </div>
        </div>
      </ModalDialog>
    </Modal>
  );
};
