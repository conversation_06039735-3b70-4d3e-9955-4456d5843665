import { SchemaBundle } from '../../../control/state';
import type { Edge, Graph, Node } from '../../../data/misc';

export const getSubgraphIds = (subgraphs: Record<string, SchemaBundle>) =>
  Object.keys(subgraphs)
    .map((i) => i.split('|').join(':'))
    .sort((a, b) => {
      const aLength = a.split(':').length;
      const bLength = b.split(':').length;
      return aLength - bLength;
    });

export const getCollapsedGraph = (
  graphData: Graph,
  subgraphSelection: string[]
) => {
  const subgraphs = getSubgraphIds(graphData.subgraphs);
  type SubgraphNode = {
    type: 'subgraph';
    id: string;
    children: (Node | SubgraphNode)[];
  };

  // Initialize with the root subgraph
  const nsMap: Record<string, SubgraphNode> = {
    ['']: { type: 'subgraph', id: '', children: [] },
  };

  // Create a set of potential subgraphs that have child nodes
  const potentialParents = new Set<string>();

  // Keep track of all actual subgraphs that have been validated
  const actualSubgraphs: string[] = [];

  // Scan nodes to identify which subgraphs actually have children
  for (const node of graphData.nodes) {
    if (node.id?.includes(':')) {
      const segments = node.id.split(':');
      const parentSubgraph = segments.slice(0, -1).join(':');
      potentialParents.add(parentSubgraph);
    }
  }

  // Create subgraph entries only for those that have children
  for (const id of subgraphs) {
    // Skip if this subgraph doesn't have child nodes
    const hasChildNodes = Array.from(potentialParents).some(
      (parent) => parent.startsWith(id + ':') || parent === id
    );
    const hasDirectChildren = graphData.nodes.some(
      (node) => node.id?.split(':').slice(0, -1).join(':') === id
    );

    if (!hasChildNodes && !hasDirectChildren) continue;

    const segments = id.split(':');
    const parentSubgraph = segments.slice(0, -1).join(':');

    // Create the subgraph entry only if it doesn't exist yet
    if (!nsMap[id]) {
      nsMap[id] = { type: 'subgraph', id, children: [] };
      // Add to our list of actual validated subgraphs
      actualSubgraphs.push(id);
    }

    // Add to parent if parent exists
    if (nsMap[parentSubgraph]) {
      nsMap[parentSubgraph].children.push(nsMap[id]);
    }
  }
  // Add nodes to their parent subgraphs
  for (const node of graphData.nodes) {
    const segments = node.id?.split(':') ?? [];
    const parentSubgraph = segments.slice(0, -1).join(':');
    if (nsMap[parentSubgraph]) {
      nsMap[parentSubgraph].children.push(node);
    }
  }

  const isSubgraphExpanded = (subgraph: string) =>
    subgraphSelection.includes(subgraph);

  const isAnySubgraph = (id: string) => {
    if (!id.includes(':')) return false;
    const [subgraph] = id.split(':');
    return Object.keys(graphData.subgraphs).includes(subgraph);
  };

  const getMergedSubgraph = (subgraph: string) => {
    const isSubgraph = (id: string) => id.startsWith(subgraph + ':');

    type SourceEdge = Omit<Edge, 'target'>;
    type TargetEdge = Omit<Edge, 'source'>;

    const sourceGather: SourceEdge[] = [];
    const targetGather: TargetEdge[] = [];

    // extract edges coming in/out
    for (const edge of graphData.edges) {
      if (isSubgraph(edge.target) && !isSubgraph(edge.source)) {
        sourceGather.push(edge);
      } else if (isSubgraph(edge.source) && !isSubgraph(edge.target)) {
        targetGather.push(edge);
      }
    }

    const toAdd: Edge[] = [];
    const node: Node = { id: subgraph, data: subgraph, metadata: {} };

    const toRemove: Edge[] = [
      ...(sourceGather as Edge[]),
      ...(targetGather as Edge[]),
    ];

    // edges are conditional if any of the edge from source is conditional
    const sourceNodes = new Set(sourceGather.map((i) => i.source));

    for (const sourceNode of sourceNodes) {
      let finalSourceNode = sourceNode;
      if (isAnySubgraph(sourceNode)) {
        const [sourceSubgraph] = sourceNode.split(':');
        if (!isSubgraphExpanded(sourceSubgraph)) {
          finalSourceNode = sourceSubgraph;
        }
      }

      toAdd.push({
        source: finalSourceNode,
        target: subgraph,
        conditional: sourceGather
          .filter((i) => i.source === sourceNode)
          .some((i) => i.conditional),
      });
    }

    // if multiple targets, all edges are conditional
    const targetNodes = new Set(targetGather.map((i) => i.target));
    for (const targetNode of targetNodes) {
      let finalTargetNode = targetNode;
      if (isAnySubgraph(targetNode)) {
        const [sourceSubgraph] = targetNode.split(':');
        if (!isSubgraphExpanded(sourceSubgraph)) {
          finalTargetNode = sourceSubgraph;
        }
      }

      toAdd.push({
        source: subgraph,
        target: finalTargetNode,
        conditional: targetNodes.size > 1,
      });
    }

    return { node, toAdd, toRemove };
  };

  const queue: (Node | SubgraphNode)[] = [...nsMap[''].children];
  const graphNodes: Node[] = [];

  const edgeHashMap: Record<string, Edge> = {};
  const toRemove: string[] = [];

  const addEdge = (edge: Edge) => {
    const hashKey = `${edge.source}-${edge.target}`;
    if (edgeHashMap[hashKey]) return;
    edgeHashMap[hashKey] = edge;
  };

  const scheduleRemove = (edge: Edge) =>
    toRemove.push(`${edge.source}-${edge.target}`);

  const isSubgraphNode = (item: Node | SubgraphNode): item is SubgraphNode =>
    'type' in item && item.type === 'subgraph' && item.children.length > 0;

  const seen = new Set<string>();
  while (queue.length > 0) {
    const item = queue.shift()!;
    if (seen.has(item.id)) continue;
    seen.add(item.id);

    if (isSubgraphNode(item)) {
      if (isSubgraphExpanded(item.id)) {
        queue.push(...item.children);
      } else {
        const merged = getMergedSubgraph(item.id);
        graphNodes.push(merged.node);
        for (const edge of merged.toAdd) addEdge(edge);
        for (const edge of merged.toRemove) scheduleRemove(edge);
      }
    } else {
      graphNodes.push(item);
      const nodeEdges = graphData.edges.filter(
        (edge) => edge.source === item.id || edge.target === item.id
      );
      for (const edge of nodeEdges) addEdge(edge);
    }
  }

  for (const hashKey of toRemove) delete edgeHashMap[hashKey];
  const graphEdges = Object.values(edgeHashMap);
  return { nodes: graphNodes, edges: graphEdges, subgraphs: actualSubgraphs };
};
