import { Expand01Icon, Minimize01Icon } from '@langchain/untitled-ui-icons';
import { Tooltip } from '@mui/joy';
import * as HoverCard from '@radix-ui/react-hover-card';

import * as React from 'react';

import GraphFillIcon from '@/Pages/Graph/icons/GraphFillIcon.svg?react';
import { useColorScheme } from '@/hooks/useColorScheme';
import CheckIcon from '@/icons/CheckIcon.svg?react';
import { cn } from '@/utils/tailwind';

import { useGraphAssistantDebugGraph } from '../../../api/useGraphSwr';
import { StateContext } from '../../../control/state';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import {
  hashString,
  hslaToStr,
  hslaWithAlpha,
  hslaWithLightness,
  hslaWithMinLightness,
  numberToColor,
} from '../../../utils';
import { overrideReservedLabelsColor } from './graph.utils';
import { getCollapsedGraph } from './subgraph.utils';

const NodeCell = (props: {
  label: string;
  className?: string;
  isSubgraph?: boolean;
}) => {
  const { isDarkMode } = useColorScheme();
  const label = props.label.split(':').at(-1) || props.label;
  const hsla = overrideReservedLabelsColor(
    { label, isDarkMode },
    numberToColor(hashString(label))
  );

  return (
    <span
      className={cn(
        'border px-2 py-1 text-xs font-medium',
        label === '__start__' || label === '__end__'
          ? 'rounded-full'
          : 'rounded-md',
        props.isSubgraph && '-my-1 inline-flex items-center gap-1 leading-none',
        props.className
      )}
      style={{
        color: hslaToStr(
          overrideReservedLabelsColor(
            { label, isDarkMode },
            hslaWithLightness(hsla, isDarkMode ? 80 : 40)
          )
        ),
        backgroundColor: hslaToStr(hslaWithAlpha(hsla, 0.15)),
        borderColor: hslaToStr(hslaWithMinLightness(hsla, 60)),
      }}
    >
      {props.isSubgraph && <GraphFillIcon className="inline-flex h-3 w-3" />}
      {label}
    </span>
  );
};

export const NodeMetadataCard = (props: {
  node: string;
  disabled: boolean;
  children: React.ReactNode;
}) => {
  const state = React.useContext(StateContext);
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const graph = useGraphAssistantDebugGraph(assistantId);

  if (!graph.data || props.disabled) return <>{props.children}</>;
  const collapsed = getCollapsedGraph(graph.data, state.subgraphSelection);

  const nodes = collapsed.nodes ?? [];
  const node = nodes.find((node) => node.id === props.node);

  const isSubgraph = collapsed.subgraphs.includes(props.node);
  const isInSubgraph = props.node.includes(':');

  const edges = collapsed.edges ?? [];
  const nodeFrom = edges
    .filter((edge) => edge.target === props.node)
    .map((edge) => edge.source);
  const nodeTo = edges
    .filter((edge) => edge.source === props.node)
    .map((edge) => edge.target);

  const metadata = (node?.metadata ?? {}) as Record<string, any>;

  const configInterrupts = metadata.__interrupt?.split(',') ?? [];

  const beforeConfigured = configInterrupts.includes('before');
  const afterConfigured = configInterrupts.includes('after');

  const beforeSelected = state.beforeBreakpoints.includes(props.node);
  const afterSelected = state.afterBreakpoints.includes(props.node);

  const userMetadata = Object.fromEntries(
    Object.entries(metadata).filter(([key]) => !key.startsWith('__'))
  );

  const notStartOrEndNode =
    props.node !== '__start__' && props.node !== '__end__';

  return (
    <HoverCard.Root openDelay={300} closeDelay={100}>
      <HoverCard.Trigger asChild>{props.children}</HoverCard.Trigger>
      <HoverCard.Portal>
        <HoverCard.Content
          className="relative z-10 flex max-w-[300px] flex-col rounded-lg border border-secondary bg-background pb-3 text-sm shadow-lg"
          sideOffset={6}
          side="right"
        >
          <div className="mb-4 ml-3 mt-4">
            <NodeCell
              label={props.node}
              className="text-sm"
              isSubgraph={isSubgraph}
            />
          </div>
          <div className="grid grid-cols-[auto,1fr] items-center gap-x-3 gap-y-2">
            {nodeFrom.length > 0 && (
              <React.Fragment>
                <div className="pl-3 text-tertiary">Source</div>
                <div className="flex items-center gap-2 overflow-y-auto no-scrollbar">
                  {nodeFrom.map((node) => (
                    <NodeCell label={node} key={node} className="text-xs" />
                  ))}
                  <div className="w-2 flex-shrink-0" />
                </div>
              </React.Fragment>
            )}
            {nodeTo.length > 0 && (
              <React.Fragment>
                <div className="pl-3 text-tertiary">Target</div>
                <div className="flex items-center gap-2 overflow-y-auto no-scrollbar">
                  {nodeTo.map((node) => (
                    <NodeCell label={node} key={node} className="text-xs" />
                  ))}
                  <div className="w-2 flex-shrink-0" />
                </div>
              </React.Fragment>
            )}

            {Object.keys(userMetadata).length > 0 && (
              <React.Fragment>
                <div className="self-start pl-3 pt-1 text-tertiary">
                  Metadata
                </div>
                <div className="flex flex-wrap items-center gap-1 overflow-y-auto pr-3 no-scrollbar">
                  {Object.entries(userMetadata).map(([key, value]) => (
                    <span className="whitespace-nowrap rounded-md border border-secondary px-2 py-1">
                      <span className="font-semibold">{key}</span>
                      {': '}
                      <span>{value}</span>
                    </span>
                  ))}
                </div>
              </React.Fragment>
            )}
          </div>

          {isSubgraph && (
            <div className="mt-3 border-t border-secondary px-3 pt-3">
              <button
                type="button"
                className="inline-flex shrink-0 items-center gap-2 rounded-md border border-primary px-2 py-1 text-xs text-tertiary hover:bg-secondary-hover"
                onClick={() => state.toggleSubgraphSelection(props.node)}
              >
                <Expand01Icon className="size-4" />
                <span>Show subgraph nodes</span>
              </button>
            </div>
          )}

          {notStartOrEndNode && !isInSubgraph && (
            <div className="-mb-2 mt-3 flex items-center gap-2 border-t border-secondary px-3 pt-1">
              <Tooltip title={beforeConfigured && 'Interrupt set via code'}>
                <span
                  className={cn(
                    'inline-flex shrink-0 cursor-pointer items-center gap-1.5 py-2 text-xs text-tertiary',
                    beforeConfigured && 'opacity-50'
                  )}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (beforeConfigured) return; // do nothing
                    if (beforeSelected) {
                      state.removeBeforeBreakpoint(props.node);
                    } else {
                      state.addBeforeBreakpoint(props.node);
                    }
                  }}
                >
                  <span
                    className={cn(
                      'flex h-4 w-4 items-center justify-center rounded-[4px] border border-secondary',
                      beforeSelected &&
                        'border-brand bg-brand-tertiary text-brand-green-400'
                    )}
                  >
                    {(beforeConfigured || beforeSelected) && (
                      <CheckIcon className="h-4 w-4" aria-hidden="true" />
                    )}
                  </span>
                  <span>Interrupt Before</span>
                </span>
              </Tooltip>

              <Tooltip title={afterConfigured && 'Interrupt set via code'}>
                <span
                  className={cn(
                    'inline-flex shrink-0 cursor-pointer items-center gap-1.5 py-2 text-xs text-tertiary',
                    afterConfigured && 'opacity-50'
                  )}
                  onClickCapture={(e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (afterConfigured) return; // do nothing
                    if (afterSelected) {
                      state.removeAfterBreakpoint(props.node);
                    } else {
                      state.addAfterBreakpoint(props.node);
                    }
                  }}
                >
                  <span
                    className={cn(
                      'flex h-4 w-4 items-center justify-center rounded-[4px] border border-secondary',
                      afterSelected &&
                        'border-brand bg-brand-tertiary text-brand-green-400'
                    )}
                  >
                    {(afterConfigured || afterSelected) && (
                      <CheckIcon className="h-4 w-4" aria-hidden="true" />
                    )}
                  </span>
                  <span>Interrupt After</span>
                </span>
              </Tooltip>
            </div>
          )}
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
};

export const SubgraphMetadataCard = (props: {
  id: string;
  disabled: boolean;
  children: React.ReactNode;
}) => {
  const state = React.useContext(StateContext);
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const graph = useGraphAssistantDebugGraph(assistantId);

  if (!graph.data || props.disabled) return <>{props.children}</>;
  const isSubgraph = !!Object.keys(graph.data.subgraphs).find(
    (i) => i.split('|').join(':') === props.id
  );
  const isInSubgraph = props.id.includes(':');

  // TODO: figure out how to get metadata for subgraphs
  const beforeConfigured = false;
  const afterConfigured = false;

  const beforeSelected = state.beforeBreakpoints.includes(props.id);
  const afterSelected = state.afterBreakpoints.includes(props.id);

  return (
    <HoverCard.Root openDelay={300} closeDelay={100}>
      <HoverCard.Trigger asChild>{props.children}</HoverCard.Trigger>
      <HoverCard.Portal>
        <HoverCard.Content
          className="relative flex max-w-[300px] flex-col rounded-lg border border-secondary bg-background pb-3 text-sm shadow-lg"
          sideOffset={6}
          side="right"
          align="start"
        >
          <div className="mb-4 ml-3 mt-4">
            <NodeCell
              label={props.id}
              className="text-sm"
              isSubgraph={isSubgraph}
            />
          </div>

          {isSubgraph && (
            <div className="border-t border-secondary px-3 pt-3">
              <button
                type="button"
                className="inline-flex shrink-0 items-center gap-2 rounded-md border border-primary px-2 py-1 text-xs text-tertiary hover:bg-secondary-hover"
                onClick={() => state.toggleSubgraphSelection(props.id)}
              >
                <Minimize01Icon className="size-4" />
                <span>Hide subgraph nodes</span>
              </button>
            </div>
          )}

          {!isInSubgraph && (
            <div className="-mb-2 mt-3 flex items-center gap-2 border-t border-secondary px-3 pt-1">
              <Tooltip title={beforeConfigured && 'Interrupt set via code'}>
                <span
                  className={cn(
                    'inline-flex shrink-0 cursor-pointer items-center gap-1.5 py-2 text-xs text-tertiary',
                    beforeConfigured && 'opacity-50'
                  )}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (beforeConfigured) return; // do nothing
                    if (beforeSelected) {
                      state.removeBeforeBreakpoint(props.id);
                    } else {
                      state.addBeforeBreakpoint(props.id);
                    }
                  }}
                >
                  <span
                    className={cn(
                      'flex h-4 w-4 items-center justify-center rounded-[4px] border border-secondary',
                      beforeSelected &&
                        'border-brand bg-brand-tertiary text-brand-green-400'
                    )}
                  >
                    {(beforeConfigured || beforeSelected) && (
                      <CheckIcon className="h-4 w-4" aria-hidden="true" />
                    )}
                  </span>
                  <span>Interrupt Before</span>
                </span>
              </Tooltip>

              <Tooltip title={afterConfigured && 'Interrupt set via code'}>
                <span
                  className={cn(
                    'inline-flex shrink-0 cursor-pointer items-center gap-1.5 py-2 text-xs text-tertiary',
                    afterConfigured && 'opacity-50'
                  )}
                  onClickCapture={(e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (afterConfigured) return; // do nothing
                    if (afterSelected) {
                      state.removeAfterBreakpoint(props.id);
                    } else {
                      state.addAfterBreakpoint(props.id);
                    }
                  }}
                >
                  <span
                    className={cn(
                      'flex h-4 w-4 items-center justify-center rounded-[4px] border border-secondary',
                      afterSelected &&
                        'border-brand bg-brand-tertiary text-brand-green-400'
                    )}
                  >
                    {(afterConfigured || afterSelected) && (
                      <CheckIcon className="h-4 w-4" aria-hidden="true" />
                    )}
                  </span>
                  <span>Interrupt After</span>
                </span>
              </Tooltip>
            </div>
          )}
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
};
