import { describe, expect, it } from 'vitest';

import type { Graph } from '../../../../data/misc';
import { getCollapsedGraph } from '../subgraph.utils';

describe('getCollapsedGraph', () => {
  it('should handle a graph with no subgraphs', () => {
    const graph: Graph = {
      nodes: [
        { id: 'node1', data: 'Node 1', metadata: {} },
        { id: 'node2', data: 'Node 2', metadata: {} },
        { id: 'node3', data: 'Node 3', metadata: {} },
      ],
      edges: [
        { source: 'node1', target: 'node2', conditional: false },
        { source: 'node2', target: 'node3', conditional: false },
      ],
      subgraphs: {},
    };

    const result = getCollapsedGraph(graph, []);

    expect(result.nodes).toHaveLength(3);
    expect(result.edges).toHaveLength(2);
    expect(result.subgraphs).toEqual([]);
  });

  it('should filter out subgraphs that have no child nodes', () => {
    const graph: Graph = {
      nodes: [
        {
          id: '__start__',
          data: {
            id: ['langchain', 'schema', 'runnable', 'RunnablePassthrough'],
            name: '__start__',
          },
        },
        {
          id: 'supervisor',
          data: {
            id: ['langgraph', 'utils', 'runnable', 'RunnableCallable'],
            name: 'supervisor',
          },
        },
        {
          id: 'code_research',
          data: {
            id: ['langgraph', 'utils', 'runnable', 'RunnableCallable'],
            name: 'code_research',
          },
        },
        { id: '__end__', data: '__end__', metadata: {} },
        { id: 'code_write:__start__', data: 'code_write:__start__' },
        {
          id: 'code_write:code_writing_assistant',
          data: 'code_write:code_writing_assistant',
        },
        {
          id: 'code_write:code_writing_assistant_tools',
          data: 'code_write:code_writing_assistant_tools',
        },
        { id: 'code_write:__end__', data: 'code_write:__end__' },
      ],
      edges: [
        { source: '__start__', target: 'supervisor', conditional: false },
        { source: 'supervisor', target: 'code_research', conditional: false },
        {
          source: 'code_research',
          target: 'code_write:__start__',
          conditional: false,
        },
        { source: 'code_write:__end__', target: '__end__', conditional: false },
      ],
      subgraphs: {
        code_write: {} as any,
        code_research: {} as any,
      },
    };

    const result = getCollapsedGraph(graph, []);

    expect(result.subgraphs).toContain('code_write');
    expect(result.subgraphs).not.toContain('code_research');

    const subgraphNodeExists = result.nodes.some(
      (node) => node.id === 'code_write'
    );
    expect(subgraphNodeExists).toBe(true);

    // The original node count minus the 4 code_write nodes plus 1 for the code_write collapsed node
    expect(result.nodes).toHaveLength(5);
  });

  it('should handle normal subgraphs with one level of nesting', () => {
    const graph: Graph = {
      nodes: [
        { id: 'start', data: 'Start', metadata: {} },
        { id: 'end', data: 'End', metadata: {} },
        { id: 'subgraph1:node1', data: 'Subgraph1 Node1', metadata: {} },
        { id: 'subgraph1:node2', data: 'Subgraph1 Node2', metadata: {} },
        { id: 'subgraph2:node1', data: 'Subgraph2 Node1', metadata: {} },
        { id: 'subgraph2:node2', data: 'Subgraph2 Node2', metadata: {} },
      ],
      edges: [
        { source: 'start', target: 'subgraph1:node1', conditional: false },
        {
          source: 'subgraph1:node1',
          target: 'subgraph1:node2',
          conditional: false,
        },
        {
          source: 'subgraph1:node2',
          target: 'subgraph2:node1',
          conditional: false,
        },
        {
          source: 'subgraph2:node1',
          target: 'subgraph2:node2',
          conditional: false,
        },
        { source: 'subgraph2:node2', target: 'end', conditional: false },
      ],
      subgraphs: {
        subgraph1: {} as any,
        subgraph2: {} as any,
      },
    };

    // Case 1: No subgraphs expanded
    const resultCollapsed = getCollapsedGraph(graph, []);

    // Both subgraphs should be included in the actual subgraphs list
    expect(resultCollapsed.subgraphs).toContain('subgraph1');
    expect(resultCollapsed.subgraphs).toContain('subgraph2');

    // Should have 4 nodes: start, subgraph1 (collapsed), subgraph2 (collapsed), end
    expect(resultCollapsed.nodes).toHaveLength(4);
    expect(resultCollapsed.nodes.some((n) => n.id === 'subgraph1')).toBe(true);
    expect(resultCollapsed.nodes.some((n) => n.id === 'subgraph2')).toBe(true);

    // Should have edges connecting the collapsed subgraphs
    const edgeSubgraph1ToSubgraph2 = resultCollapsed.edges.some(
      (e) => e.source === 'subgraph1' && e.target === 'subgraph2'
    );
    expect(edgeSubgraph1ToSubgraph2).toBe(true);

    // Case 2: All subgraphs expanded
    const resultExpanded = getCollapsedGraph(graph, ['subgraph1', 'subgraph2']);

    // Should have all original nodes
    expect(resultExpanded.nodes).toHaveLength(6);
    expect(resultExpanded.nodes.some((n) => n.id === 'subgraph1:node1')).toBe(
      true
    );
    expect(resultExpanded.nodes.some((n) => n.id === 'subgraph2:node2')).toBe(
      true
    );

    // Original edges should be preserved
    expect(resultExpanded.edges).toHaveLength(5);

    // Case 3: Only one subgraph expanded
    const resultPartialExpanded = getCollapsedGraph(graph, ['subgraph1']);

    // Should have 5 nodes: start, subgraph1:node1, subgraph1:node2, subgraph2 (collapsed), end
    expect(resultPartialExpanded.nodes).toHaveLength(5);
    expect(
      resultPartialExpanded.nodes.some((n) => n.id === 'subgraph1:node1')
    ).toBe(true);
    expect(
      resultPartialExpanded.nodes.some((n) => n.id === 'subgraph1:node2')
    ).toBe(true);
    expect(resultPartialExpanded.nodes.some((n) => n.id === 'subgraph2')).toBe(
      true
    );

    // Should have edge from subgraph1:node2 to collapsed subgraph2
    const edgeFromNode2ToSubgraph2 = resultPartialExpanded.edges.some(
      (e) => e.source === 'subgraph1:node2' && e.target === 'subgraph2'
    );
    expect(edgeFromNode2ToSubgraph2).toBe(true);
  });

  it('should handle nested subgraphs correctly', () => {
    const graph: Graph = {
      nodes: [
        { id: 'root', data: 'Root', metadata: {} },
        { id: 'parent:child1', data: 'parent:child1', metadata: {} },
        { id: 'parent:child2', data: 'parent:child2', metadata: {} },
        {
          id: 'parent:nested:grandchild1',
          data: 'parent:nested:grandchild1',
          metadata: {},
        },
        {
          id: 'parent:nested:grandchild2',
          data: 'parent:nested:grandchild2',
          metadata: {},
        },
        { id: 'sibling', data: 'Sibling', metadata: {} },
      ],
      edges: [
        { source: 'root', target: 'parent:child1', conditional: false },
        {
          source: 'parent:child1',
          target: 'parent:nested:grandchild1',
          conditional: false,
        },
        {
          source: 'parent:nested:grandchild2',
          target: 'parent:child2',
          conditional: false,
        },
        { source: 'parent:child2', target: 'sibling', conditional: false },
      ],
      subgraphs: {
        parent: {} as any,
        'parent|nested': {} as any,
      },
    };

    // Test Case 1: No subgraphs expanded
    const resultNoneExpanded = getCollapsedGraph(graph, []);

    // Should have identified both actual subgraphs
    expect(resultNoneExpanded.subgraphs).toContain('parent');
    expect(resultNoneExpanded.subgraphs).toContain('parent:nested');

    // Should have 3 nodes in the collapsed graph: root, parent (collapsed), sibling
    expect(resultNoneExpanded.nodes).toHaveLength(3);
    expect(resultNoneExpanded.nodes.some((n) => n.id === 'parent')).toBe(true);

    // Test Case 2: Only parent expanded, nested still collapsed
    const resultParentExpanded = getCollapsedGraph(graph, ['parent']);

    // In this case we should have: root, parent:child1, parent:child2, parent:nested (collapsed), sibling
    expect(resultParentExpanded.nodes).toHaveLength(5);
    expect(
      resultParentExpanded.nodes.some((n) => n.id === 'parent:nested')
    ).toBe(true);
    expect(
      resultParentExpanded.nodes.some((n) => n.id === 'parent:child1')
    ).toBe(true);
    expect(
      resultParentExpanded.nodes.some((n) => n.id === 'parent:child2')
    ).toBe(true);

    // Test Case 3: All subgraphs expanded
    const resultAllExpanded = getCollapsedGraph(graph, [
      'parent',
      'parent:nested',
    ]);

    // Should have all original nodes
    expect(resultAllExpanded.nodes).toHaveLength(6);
    expect(
      resultAllExpanded.nodes.some((n) => n.id === 'parent:nested:grandchild1')
    ).toBe(true);
    expect(
      resultAllExpanded.nodes.some((n) => n.id === 'parent:nested:grandchild2')
    ).toBe(true);
  });
  it('should handle nested subgraphs without direct children', () => {
    const graph: Graph = {
      nodes: [
        { id: 'root', data: 'Root', metadata: {} },
        {
          id: 'parent:child1:grandchild1',
          data: 'parent:child1:grandchild1',
          metadata: {},
        },
        {
          id: 'parent:child1:grandchild2',
          data: 'parent:child1:grandchild2',
          metadata: {},
        },
      ],
      edges: [
        {
          source: 'root',
          target: 'parent:child1:grandchild1',
          conditional: false,
        },
        {
          source: 'parent:child1:grandchild1',
          target: 'parent:child1:grandchild2',
          conditional: false,
        },
      ],
      subgraphs: {
        parent: {} as any,
        'parent|child1': {} as any,
      },
    };

    const result = getCollapsedGraph(graph, []);

    expect(result.subgraphs).toContain('parent');
    expect(result.subgraphs).toContain('parent:child1');
    expect(result.subgraphs).not.toContain('parent:child1:grandchild1');
    expect(result.subgraphs).not.toContain('parent:child1:grandchild2');
  });
});
