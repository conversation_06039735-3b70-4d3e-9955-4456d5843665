import { PlusIcon, XMarkIcon } from '@heroicons/react/24/solid';
import { Checkpoint, Command, Config } from '@langchain/langgraph-sdk';
import { ChevronDownIcon } from '@langchain/untitled-ui-icons';
import { LinearProgress, Option, Select, Tooltip } from '@mui/joy';

import * as React from 'react';
import { useCallback, useEffect, useMemo } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { KeyboardShortcut } from '@/Pages/KeyboardShortcutsSidePane';
import DatasetIcon from '@/icons/DatasetIcon';
import ShiftIcon from '@/icons/ShiftIcon.svg?react';
import { cn } from '@/utils/tailwind';
import { useGetGraphStudioPath } from '@/utils/useGetGraphStudioPath';

import { useGraphAssistant } from '../../api/assistants';
import {
  useGraphAssistantsDebugSchema,
  useGraphServerInfo,
} from '../../api/useGraphSwr';
import { RenderModeContext, StateContext } from '../../control/state';
import { TRACE_LOG_INFO_LEVEL } from '../../control/types';
import { Graph } from '../../data/misc';
import { GraphDesktopTrigger } from '../../desktop/GraphDesktopSidebar';
import {
  AssistantModalState,
  useAssistantStore,
} from '../../store/assistants/assistantsStore';
import AssistantsModal from '../site/Assistants/AssistantsModal';
import { GraphBreakpointDropdown } from '../site/GraphBreakpointDropdown';
import { GraphMemorySelect } from '../site/GraphMemoryEditor';
import { GraphThreadSelect } from '../site/GraphThreadSelect';
import { GraphDatasetContext } from './dataset/GraphDatasetProvider';
import { GraphDatasetToolbar } from './dataset/GraphDatasetToolbar';
import { GraphView } from './graph/graph-view';
import { GraphInputEditor } from './trace/GraphInputEditor';
import { ContinueRefContext } from './trace/GraphInputEditor.utils';
import { GraphOutput } from './trace/GraphOutput';
import { GraphTrace } from './trace/GraphTrace';
import ThreadInfoLevelSlider from './trace/ThreadInfoLevelSlider';
import { useTraceLogStore } from './trace/store/traceLogStore';

const GraphDebuggerNewThreadButton = () => {
  const state = React.useContext(StateContext);

  const navigate = useNavigate();

  const [searchParams] = useSearchParams();
  const hostProjectId = searchParams.get('hostProjectId') ?? undefined;
  const urlThreadId = searchParams.get('threadId');

  const { generatePath } = useGetGraphStudioPath();

  const newThreadPath = generatePath({ hostProjectId });
  const { setRunState, clearUpdates } = state;

  const onNewThread = useCallback(() => {
    navigate(newThreadPath);
    setRunState({ type: 'empty' });
    clearUpdates();
  }, [navigate, newThreadPath, setRunState, clearUpdates]);

  const isMac = useMemo(
    () => /(Mac|iPhone|iPod|iPad)/i.test(navigator.userAgent),
    []
  );

  useEffect(() => {
    function callback(event: KeyboardEvent) {
      // Cmd/Ctrl + Shift + O
      // (Same shortcut as in ChatGPT)
      if (
        event.key.toLocaleLowerCase() === 'o' &&
        (event.metaKey || event.ctrlKey) &&
        event.shiftKey
      ) {
        onNewThread();
      }
    }
    document.addEventListener('keydown', callback);
    return () => document.removeEventListener('keydown', callback);
  }, [onNewThread]);

  const threadId = state.threadId ?? urlThreadId;
  if (!threadId) return null;

  return (
    <Tooltip
      title={
        <div className="flex items-center gap-1">
          <span>New Thread</span>
          <KeyboardShortcut
            keys={[
              isMac ? '⌘' : '⌃',
              '+',
              <ShiftIcon className="h-4 w-2" />,
              '+',
              'O',
            ]}
          />
        </div>
      }
    >
      <button
        type="button"
        aria-label="New Thread"
        className="flex items-center gap-2 whitespace-nowrap px-1 py-1 text-sm transition-colors hover:bg-secondary-hover active:bg-secondary"
        onClick={onNewThread}
      >
        <PlusIcon className="h-4 w-4" />
      </button>
    </Tooltip>
  );
};

export const GraphDebugger = (props: {
  assistantId: string;
  graph: {
    data: Graph | undefined;
    isLoading: boolean;
    error: unknown | undefined;
  };
}) => {
  const state = React.useContext(StateContext);
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);
  const graphId = assistant?.graph_id;
  const renderModeState = React.useContext(RenderModeContext);
  const datasetState = React.useContext(GraphDatasetContext);

  const setTraceLogInfoLevel = useTraceLogStore(
    (state) => state.setTraceLogInfoLevel
  );

  const continueRef = React.useRef<
    ({
      checkpoint,
      command,
      config,
    }: {
      checkpoint: Checkpoint | string | undefined;
      command?: Command;
      config?: Config;
    }) => void
  >(() => void 0);

  const graphView = useMemo(() => {
    return (
      <GraphView
        // todo: in theory it is possible to have multiple different graphs with the same id
        // note that this may be problematic for how this graph is memoized
        key={graphId}
        className="left-body"
        graphData={props.graph.data}
        isLoading={props.graph.isLoading}
        error={props.graph.error}
      />
    );
  }, [props.graph.data, props.graph.isLoading, props.graph.error, graphId]);

  return (
    <ContinueRefContext.Provider value={continueRef}>
      <DebuggerLayout
        graph={graphView}
        trace={({ className }) => (
          <>
            <GraphTrace className={className} selectedNodeIds={[]} />
            <GraphOutput />
            {datasetState.isAddingToDataset && <GraphDatasetToolbar />}
          </>
        )}
        navRight={
          <div className="flex items-center gap-2">
            <ThreadInfoLevelSlider />
            <Select
              value={renderModeState.renderMode}
              onChange={(_, value) =>
                renderModeState.setRenderMode(value as 'pretty' | 'data')
              }
              size="sm"
              sx={{
                backgroundColor: 'transparent',
              }}
              indicator={<ChevronDownIcon className="h-4 w-4" />}
            >
              <Option value="pretty">Pretty</Option>
              <Option value="data">JSON</Option>
            </Select>
            <Tooltip title="Add to Dataset">
              <button
                type="button"
                disabled={
                  datasetState.isAddingToDataset || state.traceLog.length === 0
                }
                className={cn(
                  'flex items-center gap-1.5 whitespace-nowrap rounded-md border px-2 py-2 text-sm transition-all',
                  datasetState.isAddingToDataset || state.traceLog.length === 0
                    ? 'cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-500'
                    : 'border-secondary text-tertiary hover:bg-secondary-hover active:bg-secondary'
                )}
                onClick={() => {
                  datasetState.setIsAddingToDataset(true);
                  setTraceLogInfoLevel(TRACE_LOG_INFO_LEVEL.NODE_STATE);
                }}
              >
                <DatasetIcon className="h-4 w-4 text-tertiary" />
              </button>
            </Tooltip>
          </div>
        }
        input={<DebuggerInput />}
      />
    </ContinueRefContext.Provider>
  );
};

const DebuggerInput = ({ className }: { className?: string }) => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);

  const {
    data: schemas,
    isLoading,
    error,
  } = useGraphAssistantsDebugSchema(assistantId ? [assistantId] : undefined);
  const schema = schemas?.[0];

  const assistantModalState = useAssistantStore(
    (state) => state.assistantModalState
  );
  const assistantModalOpen = assistantModalState !== AssistantModalState.CLOSED;

  return (
    <>
      <div
        className={cn(
          className,
          'border-1 m-4 mb-5 overflow-y-auto rounded-xl border border-secondary bg-background shadow-lg no-scrollbar empty:hidden'
        )}
      >
        {isLoading ? (
          <LinearProgress />
        ) : (
          <GraphInputEditor schema={schema} schemaError={error} />
        )}
      </div>
      {assistantModalOpen && <AssistantsModal />}
    </>
  );
};

function PanelAccessibleHandle() {
  return (
    <PanelResizeHandle className="text-secondary data-[resize-handle-active]:text-brand-green-400">
      <div className="group absolute inset-y-0 -mx-1 flex w-2 justify-center">
        <div className="w-[2px] transition-colors group-hover:bg-current" />
      </div>
    </PanelResizeHandle>
  );
}

const DebuggerLayout = (props: {
  graph: React.ReactNode;
  trace: (props: { className?: string }) => React.ReactNode;
  input: React.ReactNode;
  navRight: React.ReactNode;
}) => {
  const apiInfo = useGraphServerInfo();
  const datasetState = React.useContext(GraphDatasetContext);

  return (
    <PanelGroup
      autoSaveId="graph-layout"
      direction="horizontal"
      className="relative grow overflow-hidden border-t border-secondary"
    >
      <Panel
        id="left"
        order={1}
        className="flex flex-col !overflow-y-auto !overflow-x-hidden bg-[#f9fafc] dark:bg-[#1a1c24]"
        defaultSize={50}
        minSize={33}
      >
        {apiInfo.data === null && (
          <div className="flex flex-col bg-brand-primary p-4 py-3.5 text-sm">
            <span className="font-medium text-white">
              You're running an outdated version of LangGraph API
            </span>
            <span className="text-white/75">
              Studio might not work properly. Please create a new revision to
              ensure proper functionality.
            </span>
          </div>
        )}
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-2">
            <GraphDesktopTrigger />
          </div>
          <div className="flex items-center gap-2">
            <GraphMemorySelect />
            <GraphBreakpointDropdown />
          </div>
        </div>

        {props.graph}
        {props.input}
      </Panel>

      <PanelAccessibleHandle />

      <Panel
        id="right"
        order={2}
        className="relative flex flex-col !overflow-y-auto !overflow-x-hidden border-l border-secondary"
        defaultSize={50}
        minSize={33}
      >
        <div className="right-head z-10 flex flex-col border-b border-secondary bg-background">
          <div className="flex w-full items-center justify-between gap-4 overflow-x-auto px-4 py-2">
            <div className="flex min-w-0 shrink items-center gap-1">
              <GraphThreadSelect />
              <GraphDebuggerNewThreadButton />
            </div>
            <div className="flex shrink-0 items-center gap-4">
              {props.navRight}
            </div>
          </div>
          {datasetState.isAddingToDataset && (
            <div className="flex items-center justify-between border-t border-secondary bg-brand-primary px-4 py-2">
              <div className="flex flex-1 items-center justify-center gap-2">
                <DatasetIcon className="h-4 w-4 text-white" />
                <span className="text-sm font-medium text-white">
                  Adding to Dataset
                </span>
              </div>
              <button
                type="button"
                onClick={() => datasetState.setIsAddingToDataset(false)}
                className="text-white/70 hover:text-white"
                disabled={datasetState.isAddToDatasetMutating}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>

        {props.trace({ className: 'border-secondary' })}
      </Panel>
    </PanelGroup>
  );
};
