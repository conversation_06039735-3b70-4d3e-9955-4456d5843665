import { ArrowPathIcon } from '@heroicons/react/24/solid';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/joy';

import { SettingsIcon } from 'lucide-react';
import * as React from 'react';

import DatasetIcon from '@/icons/DatasetIcon';
import { xCount } from '@/utils/stringUtils';

import { useGraphAssistants } from '../../../api/assistants';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import { GraphDatasetContext } from './GraphDatasetProvider';
import {
  GraphDatasetTableSettings,
  GraphNodeTableItem,
} from './GraphDatasetProvider.types';
import { GraphDatasetSettingsDialog } from './GraphDatasetSettingsDialog';

export const GraphDatasetToolbar = () => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const props = React.useContext(GraphDatasetContext);

  const [open, _setOpen] = React.useState(false);

  const [tempSettings, setTempSettings] =
    React.useState<GraphDatasetTableSettings>({
      graphs: [],
    });

  const setOpen = (open: boolean) => {
    _setOpen(open);
    if (open) setTempSettings(props.settings);
  };

  const handleSettingsConfirm = () => {
    props.setSettings(tempSettings);
    setOpen(false);
  };

  const handleDatasetChange = (graphNodeItem: GraphNodeTableItem) => {
    setTempSettings((prev) => ({
      graphs: prev.graphs.map((graph) => ({
        ...graph,
        nodes: graph.nodes.map((node) => {
          if (node.nodeId === graphNodeItem.nodeId) {
            return { ...graphNodeItem };
          }
          return node;
        }),
      })),
    }));
  };

  const { assistants } = useGraphAssistants({});
  const assistant = assistants?.find((a) => a.assistant_id === assistantId);

  const selectedNodes = props.nodes.filter((node) => node.visible === true);

  return (
    <>
      <div className="pointer-events-none absolute inset-x-0 bottom-16 flex justify-center">
        <div className="pointer-events-auto flex items-center gap-4 rounded-md border border-secondary bg-popover p-2 pl-4 pr-2 shadow-md">
          <div className="flex flex-shrink-0 items-center gap-3.5 text-sm">
            <span className="font-medium">
              {xCount('example', selectedNodes.length)} selected
            </span>
            <button
              type="button"
              className="text-left text-tertiary hover:underline"
              onClick={props.toggleSelectingAll}
            >
              {props.isSelectingAll ? 'Unselect all' : 'Select all'}
            </button>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outlined"
              onClick={() => setOpen(true)}
              className="grow"
              startDecorator={<SettingsIcon className="-ml-1 w-6" />}
              disabled={props.isAddToDatasetMutating}
            >
              Settings
            </Button>

            <Button
              size="sm"
              color="primary"
              startDecorator={<DatasetIcon className="h-5 w-5" />}
              className="grow"
              onClick={props.addToDataset}
              loading={props.isAddToDatasetMutating}
            >
              Add to Dataset
            </Button>

            <Tooltip title="Will reset all edits as well">
              <Button
                size="sm"
                variant="outlined"
                onClick={props.resetNodes}
                className="grow"
                startDecorator={<ArrowPathIcon className="h-5 w-5" />}
                disabled={props.isAddToDatasetMutating}
              >
                Reset
              </Button>
            </Tooltip>
          </div>
        </div>
      </div>

      {open && (
        <GraphDatasetSettingsDialog
          value={tempSettings}
          selectedAssistant={assistant}
          onClose={() => setOpen(false)}
          onItemChange={handleDatasetChange}
          onConfirm={handleSettingsConfirm}
        />
      )}
    </>
  );
};
