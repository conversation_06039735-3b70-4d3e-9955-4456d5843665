import * as React from 'react';
import { z } from 'zod';

import { mutateExample } from '@/components/ExampleCrudPane/hooks';
import useToast from '@/components/Toast';
import { fetcher } from '@/data/fetcher';
import { createDataset } from '@/data/mutations';
import { useOrganizationId } from '@/hooks/useSwr';
import { DatasetSchema, GetDatasetsQueryParams } from '@/types/schema';
import { apiDatasetsPath } from '@/utils/constants';
import { useLocalStorageState } from '@/utils/use-local-storage-state';
import { isUUID } from '@/utils/uuid';

import { useGraphAssistants } from '../../../api/assistants';
import { GraphDataContext } from '../../../control/graph-data';
import { TraceLog } from '../../../data/misc';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import { GraphDatasetTableSettings } from './GraphDatasetProvider.types';
import {
  constructDatasetString,
  getGraphDebugTableData,
} from './GraphDatasetProvider.utils';
import { GraphDatasetTraceLogStore } from './GraphDatasetTraceLogStore';

export interface GraphDatasetNode {
  id: string;
  name: string;
  visible: boolean;
  datasetNameOrId: string;
  stateInput: unknown;
  stateUpdate: unknown;

  status?: { type: 'success' } | { type: 'error'; error: unknown };
}

export const GraphDatasetContext = React.createContext<{
  // ----- state -----
  isAddingToDataset: boolean;
  setIsAddingToDataset: (value: boolean) => void;

  nodes: GraphDatasetNode[];
  updateNode: (newNode: GraphDatasetNode) => void;
  resetNodes: () => void;
  getDefaultNode: (traceLog: TraceLog) => GraphDatasetNode | null;

  // ----- user settings -----
  settings: GraphDatasetTableSettings;
  setSettings: (newData: GraphDatasetTableSettings) => void;

  // ----- actions -----
  addToDataset: () => void;
  isAddToDatasetMutating: boolean;

  // ----- selection -----
  isSelectingAll: boolean;
  toggleSelectingAll: () => void;

  // ----- expanded ids -----
  // dataset mode treats all nodes as collapsed by default
  expandedIds: string[];
  toggleExpandedId: (id: string) => void;
}>({
  nodes: [],
  updateNode: () => {},
  resetNodes: () => {},
  getDefaultNode: () => null,

  settings: { graphs: [] },
  setSettings: () => {},

  addToDataset: () => {},
  isAddToDatasetMutating: false,

  isAddingToDataset: false,
  setIsAddingToDataset: () => {},

  isSelectingAll: false,
  toggleSelectingAll: () => {},

  expandedIds: [],
  toggleExpandedId: () => {},
});

export const GraphDatasetProvider = (props: { children: React.ReactNode }) => {
  const { createToast } = useToast();
  const organizationId = useOrganizationId();
  const graphData = React.useContext(GraphDataContext);
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const [isAddingToDataset, _setAddingToDataset] =
    React.useState<boolean>(false);

  const [settings, setSettings] = useLocalStorageState<
    Record<string, Record<string, { enabled: boolean; dataset: string }>>
  >(
    `ls:graph:dataset:settings`,
    {},
    z.record(
      z.string(),
      z.record(
        z.string(),
        z.object({ enabled: z.boolean(), dataset: z.string() })
      )
    )
  );

  const [nodesToAddToDataset, setNodesToAddToDataset] = React.useState<
    GraphDatasetNode[]
  >([]);

  const [expandedIds, setExpandedIds] = React.useState<string[]>([]);

  const { assistants } = useGraphAssistants({});
  const assistant = assistants?.find((a) => a.assistant_id === assistantId);
  const assistantName = assistant?.name ?? assistant?.graph_id;

  const [isAddToDatasetMutating, setIsAddToDatasetMutating] =
    React.useState(false);

  const handleAddToDataset = async () => {
    const targetNodes = nodesToAddToDataset.filter((node) => node.visible);
    if (targetNodes.length === 0) {
      return void createToast({
        type: 'error',
        title: 'No nodes selected',
        description: 'Please select at least one node to add to a dataset.',
      });
    }

    const hasEmptyDataset = targetNodes.some(
      (node) => node.datasetNameOrId === ''
    );

    if (hasEmptyDataset) {
      return void createToast({
        type: 'error',
        title: 'Dataset name must be nonempty',
        description: 'Please select a dataset for each node.',
      });
    }

    // Check for null inputs
    const nodesWithNullInputs = targetNodes.filter(
      (node) => node.stateInput === null
    );

    if (nodesWithNullInputs.length > 0) {
      const nodeNames = nodesWithNullInputs.map((node) => node.name).join(', ');
      return void createToast({
        type: 'error',
        title: 'Inputs are null',
        description: `The following nodes have null inputs: ${nodeNames}. Please ensure all inputs are loaded before confirming.`,
      });
    }

    const resolveDatasetTask = async (query): Promise<{ id: string }> => {
      // skip a network request if the dataset is known by ID
      if (isUUID(query)) return { id: query };

      // try finding the dataset by name first
      try {
        const byName = (
          await fetcher<DatasetSchema[], GetDatasetsQueryParams>({
            url: apiDatasetsPath,
            params: { name: query, limit: 1 },
            headers: organizationId
              ? { 'X-Tenant-Id': organizationId }
              : undefined,
          })
        ).at(0);
        if (byName) return byName;
      } catch {
        // ignore
      }

      const nodeNames = targetNodes
        .filter((node) => node.datasetNameOrId === query)
        .map((node) => node.name);

      // TODO: invalidate useDatasets queries
      return await createDataset(organizationId ?? '', {
        name: query,
        description: `Auto-generated dataset for ${
          nodeNames.join(', ') || 'Studio'
        }`,
        data_type: 'kv',
      });
    };

    const tasksByQuery: Record<string, Promise<{ id: string }>> = {};
    setIsAddToDatasetMutating(true);

    const resolveExampleTask = async (node: GraphDatasetNode) => {
      if (!node.visible) return;

      const datasetQuery = node.datasetNameOrId;
      tasksByQuery[datasetQuery] ??= resolveDatasetTask(datasetQuery);

      const dataset = await tasksByQuery[datasetQuery];
      return await mutateExample(
        { organizationId, dataType: 'kv' },
        {
          dataset_id: dataset.id,
          inputs: JSON.stringify(node.stateInput),
          outputs: JSON.stringify(node.stateUpdate),
        }
      );
    };

    const exampleTasks: Record<
      string,
      { type: 'success' } | { type: 'error'; error: unknown }
    > = {};

    for (const node of targetNodes) {
      try {
        await resolveExampleTask(node);
        exampleTasks[node.id] = { type: 'success' };
      } catch (error) {
        exampleTasks[node.id] = { type: 'error', error };
      }
    }

    const newNodes = nodesToAddToDataset.map((node) => {
      const status = exampleTasks[node.id];
      if (!node.visible || !status) return node;

      if (status.type === 'error') return { ...node, status };
      return { ...node, status, visible: false };
    });

    const isAllHidden = newNodes.every((node) => !node.visible);
    const isSomeSuccess = newNodes.some(
      (node) => node.status?.type === 'success'
    );

    setIsAddToDatasetMutating(false);
    if (isAllHidden) {
      createToast({
        type: 'success',
        title: 'Added to datasets',
        description: 'All selected nodes have been added to datasets.',
      });

      _setAddingToDataset(false);
    } else {
      setNodesToAddToDataset(newNodes);

      if (isSomeSuccess) {
        createToast({
          type: 'warning',
          title: 'Partially added to datasets',
          description:
            'Some nodes were not added to datasets. Please check the error messages for more details.',
        });
      } else {
        createToast({
          type: 'error',
          title: 'Failed to add to datasets',
          description: 'Please check the error messages for more details.',
        });
      }
    }
  };

  const graphSettings = React.useMemo((): GraphDatasetTableSettings => {
    if (graphData?.nodes && assistantId) {
      const mutate = getGraphDebugTableData(graphData, assistantName);

      for (let i = 0; i < mutate.graphs.length; i++) {
        const graph = mutate.graphs[i];
        for (let n = 0; n < graph.nodes.length; n++) {
          const node = graph.nodes[n];
          const item = settings[assistantId]?.[node.nodeId];

          node.datasetNameOrId = item?.dataset || node.datasetNameOrId;
          node.enabled = item?.enabled ?? node.enabled;
        }
      }

      return mutate;
    }

    return { graphs: [] };
  }, [graphData, assistantId, assistantName, settings]);

  const setGraphSettings = React.useCallback(
    (newDefaults: GraphDatasetTableSettings) => {
      if (!assistantId) return;

      const settings: Record<string, { enabled: boolean; dataset: string }> =
        {};
      for (const graph of newDefaults.graphs) {
        for (const node of graph.nodes) {
          settings[node.nodeId] = {
            enabled: node.enabled,
            dataset: node.datasetNameOrId,
          };
        }
      }

      setSettings((prev) => ({ ...prev, [assistantId]: settings }));
    },
    [assistantId, setSettings]
  );

  const getDatasetNode = React.useCallback(
    (
      traceLog: TraceLog,
      settings: Record<string, { enabled: boolean; dataset: string }>
    ): GraphDatasetNode | null => {
      if (
        (traceLog.entry.type !== 'node' &&
          traceLog.entry.type !== 'input' &&
          traceLog.entry.type !== 'pending') ||
        traceLog.entry.name === '__start__'
      ) {
        return null;
      }

      const subgraphIds =
        traceLog.state?.checkpoint.checkpoint_ns
          .split('|')
          .map((item) => {
            const [subgraph] = item.split(':');
            return subgraph;
          })
          .filter(Boolean) ?? [];

      const name = [...subgraphIds, traceLog.entry.name].join(':');

      const defaultDataset = constructDatasetString(
        assistantName,
        subgraphIds.at(-1) ?? 'main',
        name
      );

      return {
        id: traceLog.meta.id,
        name: name,
        visible: settings[name]?.enabled ?? true,
        datasetNameOrId: settings[name]?.dataset ?? defaultDataset,
        stateInput: traceLog.state?.values,
        stateUpdate: traceLog.entry.data.raw,
      };
    },
    [assistantName]
  );

  const resetNodes = React.useCallback(() => {
    if (!assistantId || !assistantName) return;

    const store = GraphDatasetTraceLogStore.getAll();
    const newNodes: GraphDatasetNode[] = [];
    const newSettings: Record<string, { enabled: boolean; dataset: string }> = {
      ...settings[assistantId],
    };

    for (const logs of Object.values(store)) {
      // add all of these nodes to the dataset
      for (const traceLog of logs) {
        const newNode = getDatasetNode(traceLog, newSettings);
        if (!newNode) continue;

        newNodes.push(newNode);

        const defaultSettings = {
          enabled: newNode.visible,
          dataset: newNode.datasetNameOrId,
        };

        newSettings[newNode.name] ??= defaultSettings;
        newSettings[newNode.name].enabled ??= defaultSettings.enabled;
        newSettings[newNode.name].dataset ??= defaultSettings.dataset;
      }
    }

    setNodesToAddToDataset(newNodes);
    setSettings((prev) => ({ ...prev, [assistantId]: newSettings }));
  }, [assistantId, assistantName, getDatasetNode, setSettings, settings]);

  const getDefaultNode = React.useCallback(
    (traceLog: TraceLog) => {
      if (!assistantId) return null;
      return getDatasetNode(traceLog, settings[assistantId]);
    },
    [assistantId, getDatasetNode, settings]
  );

  const setAddingToDataset = React.useCallback(
    (mode: boolean) => {
      if (mode) resetNodes();
      _setAddingToDataset(mode);
    },
    [resetNodes]
  );

  const isSelectingAll = React.useMemo(() => {
    return nodesToAddToDataset.every((i) => i.visible);
  }, [nodesToAddToDataset]);

  const toggleSelectingAll = React.useCallback(() => {
    setNodesToAddToDataset((currentNodes) => {
      return currentNodes.map((node) => ({
        ...node,
        visible: !isSelectingAll,
      }));
    });
  }, [isSelectingAll, setNodesToAddToDataset]);

  const toggleExpandedId = React.useCallback((id: string) => {
    setExpandedIds((currentIds) => {
      if (currentIds.includes(id)) {
        return currentIds.filter((i) => i !== id);
      }
      return [...currentIds, id];
    });
  }, []);

  return (
    <GraphDatasetContext.Provider
      value={{
        nodes: nodesToAddToDataset,
        updateNode: (payload: GraphDatasetNode) => {
          const newNode = { ...payload, status: undefined };
          setNodesToAddToDataset((currentNodes) => {
            const nodeExists = currentNodes.some(
              (node) => node.id === newNode.id
            );
            if (nodeExists) {
              return currentNodes.map((node) =>
                node.id === newNode.id ? newNode : node
              );
            } else {
              return [...currentNodes, newNode];
            }
          });
        },

        settings: graphSettings,
        setSettings: setGraphSettings,

        addToDataset: handleAddToDataset,
        isAddToDatasetMutating: isAddToDatasetMutating,

        isAddingToDataset: isAddingToDataset,
        setIsAddingToDataset: setAddingToDataset,

        resetNodes,
        getDefaultNode,

        isSelectingAll,
        toggleSelectingAll,

        expandedIds,
        toggleExpandedId,
      }}
    >
      {props.children}
    </GraphDatasetContext.Provider>
  );
};
