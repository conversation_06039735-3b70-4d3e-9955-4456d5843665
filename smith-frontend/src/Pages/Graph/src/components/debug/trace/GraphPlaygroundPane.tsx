import {
  Maximize02Icon,
  PlayCircleIcon,
  XIcon,
} from '@langchain/untitled-ui-icons';
import { LinearProgress, Tooltip } from '@mui/joy';

import { useContext, useMemo, useState } from 'react';

import { RunsPlayground } from '@/Pages/Playground/RunsPlayground';
import { useNavigateToPlaygroundFromRun } from '@/Pages/Playground/hooks/useNavigateToPlaygroundFromRun';
import { calculateLatency } from '@/Pages/Run/components/RunStatsCard';
import { RunType } from '@/Pages/Run/components/RunType';
import { TokensChip } from '@/Pages/Run/components/TokensChip';
import FloatingOverflowScrollButton from '@/components/FloatingOverflowScrollButton';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { Skeleton } from '@/components/Skeleton';
import { SplitViewPane } from '@/components/SplitViewPane';
import { useRun } from '@/hooks/useSwr';
import { RunSchema } from '@/types/schema';
import { getModelNameFromRun } from '@/utils/get-model-name-from-run';
import { pluralize } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import { StateContext } from '../../../control/state';
import { useLLMRunsForNode } from '../../../hooks/useLLMRunsForNode';
import { TraceLogAvatar } from '../common/TraceLogAvatar';

const PlaygroundPane = ({
  playgroundRun: playgroundRunProps,
  nodeName,
  onClose,
}: {
  playgroundRun: RunSchema | undefined;
  nodeName: string;
  onClose: () => void;
}) => {
  // get full run from playgroundRunProps.id
  const {
    data: playgroundRunFull,
    isLoading: playgroundRunLoading,
    error: playgroundRunError,
    contents,
  } = useRun({
    id: playgroundRunProps?.id,
    withContents: true,
    shareToken: undefined,
  });
  const playgroundRun = playgroundRunFull ?? playgroundRunProps;
  const { inputs, outputs } = contents ?? {};

  const isLoadingPlayground =
    playgroundRunLoading || inputs?.loading || outputs?.loading;

  const navigateToPlaygroundFromRun = useNavigateToPlaygroundFromRun();

  const onExpand = () => {
    if (playgroundRun) {
      navigateToPlaygroundFromRun(playgroundRun);
    }
  };

  const renderNodeName = () => {
    return (
      <div className="flex flex-row items-center gap-2">
        <div className="flex items-center gap-2">
          <TraceLogAvatar nodeName={nodeName} />
          <span className="text-sm font-semibold text-secondary">
            {nodeName}
          </span>
        </div>
        <span className="size-1 rounded-full bg-black first:hidden [&:not(:has(+*))]:hidden" />

        {playgroundRun ? (
          <div className="flex flex-row items-center gap-2">
            <RunType run={playgroundRun} className="h-5 w-5" />
            <span className="text-sm text-primary">{playgroundRun.name}</span>
          </div>
        ) : (
          <Skeleton className="h-6 w-16" />
        )}
      </div>
    );
  };

  // using state for this because need to wait for the playground to fully load before we can get the ref
  const [playgroundPaneRef, setPlaygroundPaneRef] =
    useState<HTMLDivElement | null>(null);

  const renderPaneBody = () => {
    if (isLoadingPlayground) {
      return (
        <div>
          <LinearProgress />
        </div>
      );
    }
    if (!playgroundRun || playgroundRunError) {
      return <div>Run not found</div>;
    }
    return (
      <div className="z-auto">
        <div className="absolute inset-0 flex h-full w-full flex-col gap-4 overflow-y-auto p-0">
          <div className="h-full">
            <RunsPlayground
              run={playgroundRun}
              isSimplified={true}
              scrollRef={setPlaygroundPaneRef}
            />
          </div>
        </div>
        {playgroundPaneRef && (
          <FloatingOverflowScrollButton
            containerRef={{ current: playgroundPaneRef }}
          />
        )}
      </div>
    );
  };
  return (
    <SplitViewPane
      open={!!playgroundRunProps}
      sidePaneId="playgroundPane"
      hideArrows={true}
      customHeader={
        <div className="sticky top-0 z-10 flex w-full flex-row items-start justify-between gap-1 bg-background px-6 py-6 text-lg font-medium ">
          <div className="flex flex-col gap-2">
            <span className="text-[24px] font-medium text-primary">
              Playground
            </span>
            {renderNodeName()}
          </div>
          <div className="flex flex-row items-center gap-1">
            <Tooltip title="Open full playground">
              <button
                type="button"
                className="rounded-md p-1 hover:bg-quaternary"
                onClick={onExpand}
              >
                <Maximize02Icon className="h-5 w-5 text-primary" />
              </button>
            </Tooltip>
            <button
              type="button"
              className="rounded-md p-1 hover:bg-quaternary"
              onClick={onClose}
            >
              <XIcon className="h-6 w-6 text-primary" />
            </button>
          </div>
        </div>
      }
      className="relative h-full overflow-hidden"
      withOverlay={true}
    >
      {renderPaneBody()}
    </SplitViewPane>
  );
};

const LLMRunPopoverItem = ({
  run,
  setPlaygroundRun,
  setIsLLMPopoverOpen,
}: {
  run: RunSchema;
  setPlaygroundRun: (run: RunSchema) => void;
  setIsLLMPopoverOpen: (open: boolean) => void;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const renderIcon = () => {
    if (isHovered) {
      return <PlayCircleIcon className="h-5 w-5 rounded-sm text-primary" />;
    }
    return <RunType run={run} hideTooltip={true} className="h-5 w-5" />;
  };

  return (
    <button
      type="button"
      key={run.id}
      onClick={() => {
        setPlaygroundRun(run);
        setIsLLMPopoverOpen(false);
      }}
      className="-mx-2 flex h-5 items-center gap-3 whitespace-nowrap rounded-md bg-background px-2 py-4 text-xs hover:bg-tertiary"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {renderIcon()}
      <span
        className={cn(
          'overflow-hidden text-ellipsis whitespace-nowrap px-px py-0.5 text-sm font-medium leading-none'
        )}
      >
        {run.name}
      </span>

      <span className="mt-[-2px] overflow-hidden text-ellipsis whitespace-nowrap rounded-md border border-secondary px-1 py-px text-xs">
        {getModelNameFromRun(run)}
      </span>
      <span className="text-xs text-tertiary">
        {run.start_time && run.end_time
          ? calculateLatency(run.start_time, run.end_time).latency
          : undefined}
      </span>
      {run.total_tokens && <TokensChip run={run} />}
    </button>
  );
};

export type GraphPlaygroundPaneProps = {
  runId: string;
  nodeName: string;
  nodeId: string;
  showChildLLMRuns: boolean;
};

const GraphPlaygroundPane = ({
  runId,
  nodeName,
  nodeId,
  showChildLLMRuns,
}: GraphPlaygroundPaneProps) => {
  const [isLLMPopoverOpen, setIsLLMPopoverOpen] = useState(false);
  const { selectedPlaygroundRun, setSelectedPlaygroundRun } =
    useContext(StateContext);

  const llmRunsForNode = useLLMRunsForNode({
    runId,
    nodeName,
    nodeId,
    getChildLLMRuns: showChildLLMRuns,
  });

  const llmRunsButton = useMemo(() => {
    if (!llmRunsForNode?.length) return null;
    return (
      <button
        type="button"
        onClick={() => setIsLLMPopoverOpen(true)}
        className="flex justify-end whitespace-nowrap rounded-md px-1.5 text-xs font-medium text-secondary transition-colors hover:bg-tertiary"
      >
        View {pluralize('LLM run', llmRunsForNode.length)}
      </button>
    );
  }, [llmRunsForNode?.length]);

  if (!llmRunsForNode?.length) return null;

  const renderLLMListPopover = () => {
    return (
      <Popover open={isLLMPopoverOpen} onOpenChange={setIsLLMPopoverOpen}>
        <PopoverTrigger>{llmRunsButton}</PopoverTrigger>
        <PopoverContent className="w-84" align="start">
          <div className="flex flex-col gap-1">
            <span className="text-sm font-medium text-primary">LLM runs</span>
            <span className="text-xs text-tertiary">
              Select a run to open it in the Playground
            </span>
            <div className="flex flex-col gap-2 pt-2">
              {llmRunsForNode?.map((run) => (
                <LLMRunPopoverItem
                  key={run.id}
                  run={run}
                  setPlaygroundRun={setSelectedPlaygroundRun}
                  setIsLLMPopoverOpen={setIsLLMPopoverOpen}
                />
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  return (
    <>
      {renderLLMListPopover()}
      <PlaygroundPane
        playgroundRun={selectedPlaygroundRun}
        nodeName={nodeName}
        onClose={() => setSelectedPlaygroundRun(undefined)}
      />
    </>
  );
};

export default GraphPlaygroundPane;
