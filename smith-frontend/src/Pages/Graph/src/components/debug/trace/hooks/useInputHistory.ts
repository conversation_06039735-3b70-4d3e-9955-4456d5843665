import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import { UntypedInput } from '@/Pages/Graph/src/control/state';

export const useInputHistory = () => {
  const resetInput = useCallback(() => {
    return {};
  }, []);

  const [inputHistory, setInputHistory] = useState<UntypedInput[]>([]);
  const [inputIndex, setInputIndex] = useState(inputHistory.length);
  const [input, setInput] = useState<UntypedInput>(resetInput());

  const onSelectPreviousInput = useCallback(() => {
    setInputIndex((i) => {
      const prevIndex = i - 1;
      setInput(inputHistory[prevIndex]);
      return prevIndex;
    });
  }, [inputHistory, setInput]);
  const onSelectNextInput = useCallback(() => {
    setInputIndex((i) => {
      const nextIndex = i + 1;
      setInput(inputHistory[nextIndex] ?? resetInput());
      return nextIndex;
    });
  }, [inputHistory, setInput, resetInput]);

  const previousInputDisabled = inputIndex <= 0;
  const nextInputDisabled = inputIndex === inputHistory.length;

  // Check for Command/Meta + Up/Down arrows
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!event.metaKey) return;
      if (event.key === 'ArrowUp' && !previousInputDisabled) {
        event.preventDefault();
        onSelectPreviousInput();
      } else if (event.key === 'ArrowDown' && !nextInputDisabled) {
        event.preventDefault();
        onSelectNextInput();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    inputIndex,
    inputHistory.length,
    onSelectPreviousInput,
    onSelectNextInput,
    previousInputDisabled,
    nextInputDisabled,
  ]);

  const addToInputHistory = useCallback(
    (input: UntypedInput) => {
      setInputHistory((history) => {
        setInputIndex(history.length + 1);
        return [...history, input];
      });
    },
    [setInputHistory, setInputIndex]
  );

  return {
    inputHistory,
    setInputHistory,
    addToInputHistory,
    inputIndex,
    setInputIndex,
    input,
    setInput,
    onSelectPreviousInput,
    onSelectNextInput,
    previousInputDisabled,
    nextInputDisabled,
  };
};

export const InputHistoryContext = createContext<
  ReturnType<typeof useInputHistory>
>({
  inputHistory: [],
  setInputHistory: () => {},
  addToInputHistory: () => {},
  inputIndex: 0,
  setInputIndex: () => {},
  input: {},
  setInput: () => {},
  onSelectPreviousInput: () => {},
  onSelectNextInput: () => {},
  previousInputDisabled: false,
  nextInputDisabled: false,
});

export const useInputHistoryContext = () => {
  const inputHistory = useContext(InputHistoryContext);
  if (!inputHistory) {
    throw new Error('InputHistoryContext not found');
  }
  return inputHistory;
};
