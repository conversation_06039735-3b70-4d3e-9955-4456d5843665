import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { Checkpoint, Command, Config } from '@langchain/langgraph-sdk';
import {
  AlertCircleIcon,
  ArrowDownIcon,
  ArrowUpIcon,
} from '@langchain/untitled-ui-icons';
import PlayCircleOutline from '@mui/icons-material/PlayCircleOutline';
import {
  Button,
  ButtonGroup,
  CircularProgress,
  FormControl,
  FormLabel,
  IconButton,
  Switch,
  Tooltip,
} from '@mui/joy';
import * as Popover from '@radix-ui/react-popover';

import { produce } from 'immer';
import React, { useCallback, useContext, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import { z } from 'zod';

import { KeyboardShortcut } from '@/Pages/KeyboardShortcutsSidePane';
import { ButtonWithTracking } from '@/components/ButtonWithTracking';
import { CodeData } from '@/components/Code/Code';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/Dropdown';
import { ElapsedTime } from '@/components/ElapsedTime';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/Select/Select';
import ProgressStarIcon from '@/icons/ProgressStarIcon.svg?react';
import SettingsIcon from '@/icons/SettingsIcon.svg?react';
import { cn } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { useLangGraphClient } from '../../../api';
import { useGraphAssistant, useGraphAssistants } from '../../../api/assistants';
import { SCHEMA_ERROR_MESSAGE } from '../../../constants';
import { GraphDataContext } from '../../../control/graph-data';
import {
  SchemaBundle,
  StateContext,
  UntypedInput,
} from '../../../control/state';
import {
  abortRun,
  canAbort,
  isRunExecuting,
} from '../../../control/state.utils';
import { useGraphThreadSubmit } from '../../../control/thread-runner';
import { getCheckpointId } from '../../../data/misc';
import { useInterruptShown } from '../../../hooks/useInterruptShown';
import {
  AssistantModalState,
  useAssistantStore,
} from '../../../store/assistants/assistantsStore';
import { getEditableNodes, pick, useEnterSubmit } from '../../../utils';
import {
  checkIsSystemAssistant,
  getAssistantDisplayName,
} from '../../site/Assistants/utils';
import { RawRenderedToggle } from '../common/RawRenderedToggle';
import { SchemaEditor } from '../common/schema-editor';
import { defaultSchemaRenderer } from '../common/schema-renderer';
import { ContinueRefContext } from './GraphInputEditor.utils';
import { useInputHistoryContext } from './hooks/useInputHistory';

const ManageAssistantsButton = ({ disabled }: { disabled: boolean }) => {
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const { data: assistant } = useGraphAssistant(assistantId);
  const graphId = assistant?.graph_id;
  const { assistants: assistantsForGraph } = useGraphAssistants({ graphId });

  const onlyHasSystemAssistant =
    (assistantsForGraph?.length ?? 0) <= 2 && checkIsSystemAssistant(assistant);

  const showAssistantName = assistant && !onlyHasSystemAssistant;

  const setAssistantModalState = useAssistantStore(
    (state) => state.setAssistantModalState
  );

  return (
    <Tooltip title={showAssistantName ? 'Manage Configuration' : undefined}>
      <ButtonWithTracking
        as={Button}
        eventName="manage_assistants"
        variant="outlined"
        color="neutral"
        startDecorator={<SettingsIcon className="-ml-1 w-6" />}
        onClick={() => setAssistantModalState(AssistantModalState.OPEN)}
        disabled={disabled}
      >
        {showAssistantName ? (
          <span>{getAssistantDisplayName(assistant)}</span>
        ) : (
          <span>Manage Assistants</span>
        )}
      </ButtonWithTracking>
    </Tooltip>
  );
};

const EditAndContinueButton = (props: {
  input: UntypedInput;
  namespace: string | undefined;
  onDone: (config: Config['configurable']) => void;
}) => {
  const state = useContext(StateContext);
  const [langgraph, apiUrl] = useLangGraphClient();

  const graph = React.useContext(GraphDataContext);
  const nodes = getEditableNodes(graph, props.namespace);

  const threadHead = state.threadHead;
  const [asNode, setAsNode] = useState<string | undefined>(
    threadHead?.next?.[0] ?? nodes.at(-1)
  );

  const continueAs = useSWRMutation(
    [apiUrl, 'threads', state.threadId],
    async () => {
      const threadHead = state.threadHead;
      if (!threadHead || !state.threadId) {
        throw new Error('Missing threadHead or threadId');
      }

      const { configurable } = await langgraph.threads.updateState(
        state.threadId,
        {
          values: props.input,
          asNode,
          checkpointId: getCheckpointId(threadHead),
        }
      );

      return configurable;
    },
    { onSuccess: props.onDone }
  );

  return (
    <div className="flex items-center gap-2">
      <div className="flex place-items-center gap-2">
        <span className="text-sm text-tertiary">As Node</span>
        <Select value={asNode} onValueChange={setAsNode}>
          <SelectTrigger>
            <SelectValue placeholder="Select node" />
          </SelectTrigger>
          <SelectContent>
            {graph?.nodes.map((n) => (
              <SelectItem key={n.id} value={n.id}>
                {n.id}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <ButtonWithTracking
        as={Button}
        eventName="graph_input_submit"
        startDecorator={<PlayCircleOutline />}
        loading={continueAs.isMutating}
        onClick={() => continueAs.trigger()}
      >
        Submit
      </ButtonWithTracking>
    </div>
  );
};

const GraphInputSubmit = (props: {
  isLoading: boolean;
  onClick: () => void;
  messagesEnabled: boolean;
  setMessagesEnabled: (enabled: boolean) => void;
}) => {
  if (props.isLoading) {
    const decorator = (
      <CircularProgress
        size="sm"
        sx={{
          transform: 'scale(0.65)',
          marginLeft: '-5.5px',
          marginRight: '-2px',
        }}
      />
    );

    return (
      <Button startDecorator={decorator} onClick={props.onClick}>
        Cancel
      </Button>
    );
  }

  return (
    <Popover.Root>
      <ButtonGroup variant="solid" color="primary">
        <ButtonWithTracking
          as={Button}
          eventName="graph_input_submit"
          startDecorator={<PlayCircleOutline />}
          onClick={props.onClick}
        >
          Submit
        </ButtonWithTracking>
        <Popover.Trigger asChild>
          <IconButton
            sx={{
              '&.MuiIconButton-root': {
                width: '28px',
                minWidth: '28px',
              },
            }}
          >
            <ChevronDownIcon className="w-4" />
          </IconButton>
        </Popover.Trigger>
      </ButtonGroup>
      <Popover.Portal>
        <Popover.Content align="end" sideOffset={5} className="relative z-10">
          <div className="flex max-w-[400px] flex-col space-y-4 rounded-lg border border-secondary bg-popover p-3 shadow-lg">
            <FormControl orientation="horizontal">
              <div className="mr-4 flex flex-col gap-1">
                <FormLabel>Enable messages stream mode</FormLabel>
                <p className="text-sm text-tertiary">
                  Stream message chunks even if the LLM is called via{' '}
                  <span className="font-mono">invoke()</span>.
                </p>
                <p className="text-sm text-tertiary">
                  Disable if your model does not support streaming message
                  chunks.
                </p>
              </div>
              <div className="my-auto">
                <Switch
                  title="Enable streaming"
                  checked={props.messagesEnabled}
                  onChange={() =>
                    props.setMessagesEnabled(!props.messagesEnabled)
                  }
                />
              </div>
            </FormControl>
          </div>
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

export const GraphInputEditor = (props: {
  schema?: SchemaBundle;
  schemaError?: unknown;
  namespace?: string;
}) => {
  const state = useContext(StateContext);
  const assistantId = useAssistantStore((state) => state.activeAssistantId);
  const continueRef = useContext(ContinueRefContext);
  const [open, setOpen] = useState(true);
  const [messagesEnabled, setMessagesEnabled] = useLocalStorageState(
    'ls:studio:messagesEnabled',
    true,
    z.boolean()
  );

  const resetInput = useCallback(() => {
    return {}; // ASK DAVID: why do we need to populate the input here? causes all of the X's to show up initially, but as soon as you click one of them, they disappear. Can we just make resetInput set to an empty object?
  }, []);

  const {
    inputHistory,
    addToInputHistory,
    inputIndex,
    input,
    setInput,
    onSelectPreviousInput,
    previousInputDisabled,
    onSelectNextInput,
    nextInputDisabled,
  } = useInputHistoryContext();
  const onChange = useCallback(
    (inp, touched: string[] | undefined) => setInput(pick(inp, touched)),
    [setInput]
  );

  const config = useAssistantStore((state) => state.config);
  const submit = useGraphThreadSubmit({ messagesEnabled });
  const onInputSubmit = useCallback(() => {
    if (canAbort(state.runState)) {
      abortRun(state.runState);
    } else {
      submit({
        input,
        state,
        assistantId,
        config: produce(config ?? {}, (draft) => {
          if (!draft.configurable) draft.configurable = {};
          draft.configurable.thread_ts = getCheckpointId(state.threadHead);
        }),
      });
      addToInputHistory(input);
    }

    // clear the input immediately after submission
    setInput(resetInput());
  }, [
    input,
    state,
    config,
    submit,
    resetInput,
    assistantId,
    addToInputHistory,
  ]);

  const onContinue = ({
    checkpoint,
    command,
    config,
  }: {
    checkpoint: Checkpoint | string | undefined;
    command?: Command;
    config?: Config;
  }) => {
    submit({
      input: undefined,
      command,
      state,
      checkpoint,
      config,
      assistantId,
    });
    // clear the input immediately after submission
    setInput(resetInput());
  };

  continueRef.current = onContinue;
  const interruptShown = useInterruptShown();
  useEnterSubmit(
    (state.editingCheckpointId != null &&
      state.threadHead?.next.length === 0) ||
      !!state.selectedPlaygroundRun ||
      interruptShown,
    onInputSubmit
  );

  const tryJoinPendingRun = (runIdToJoin: string) => {
    if (!isRunExecuting(state.runState))
      submit({ runIdToJoin, state, assistantId });
  };

  // TODO: remove this once the server is properly updated everywhere (cloud, in-mem dev)
  const pendingRuns = state.pendingRuns?.filter(
    (run) => run.status === 'pending'
  );

  const renderInputHistoryNav = () => {
    if (!inputHistory.length) return null;
    return (
      <>
        <Tooltip
          title={
            <div className="flex items-center gap-1">
              <span>Previous input</span>
              <KeyboardShortcut keys={['⌘', '+', '↑']} />
            </div>
          }
        >
          <button
            type="button"
            onClick={onSelectPreviousInput}
            disabled={previousInputDisabled}
            className="disabled:opacity-50"
          >
            <ArrowUpIcon className="h-5 w-5" />
          </button>
        </Tooltip>
        <Tooltip
          title={
            <div className="flex items-center gap-1">
              <span>Next input</span>
              <KeyboardShortcut keys={['⌘', '+', '↓']} />
            </div>
          }
        >
          <button
            type="button"
            onClick={onSelectNextInput}
            disabled={nextInputDisabled}
            className="disabled:opacity-50"
          >
            <ArrowDownIcon className="h-5 w-5" />
          </button>
        </Tooltip>
      </>
    );
  };

  return (
    <div className="relative">
      {!isRunExecuting(state.runState) && (
        <div className="flex flex-col gap-2 p-3.5">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-xl font-semibold tracking-tighter">
                Input
              </span>
              {renderInputHistoryNav()}
              {!!props.schemaError && (
                <Tooltip
                  title={`${SCHEMA_ERROR_MESSAGE} You can still manually edit your input.`}
                >
                  <span className="text-sm text-warning">
                    <AlertCircleIcon className="h-4 w-4" />
                  </span>
                </Tooltip>
              )}
            </div>

            <div className="flex items-center gap-1">
              {open && !props.schemaError && <RawRenderedToggle />}
              <button
                type="button"
                className="-mr-1 rounded p-1 transition-colors hover:bg-secondary-hover active:bg-secondary"
                onClick={() => setOpen((o) => !o)}
              >
                <ChevronDownIcon
                  className={cn(
                    'h-5 w-5 flex-shrink-0 text-tertiary transition-transform',
                    !open && '-rotate-90'
                  )}
                />
              </button>
            </div>
          </div>

          <div className={cn(!open && 'hidden')}>
            {state.viewRaw ? (
              <div className="rounded-md border border-secondary bg-background">
                <CodeData
                  value={input}
                  language="json"
                  onChange={(value) => setInput(value)}
                />
              </div>
            ) : (
              <SchemaEditor
                key={inputIndex}
                input={input}
                onChange={onChange}
                schema={props.schema?.input ?? props.schema?.state}
                rootSchema={props.schema?.inputRoot ?? props.schema?.stateRoot}
                renderer={defaultSchemaRenderer}
              />
            )}
          </div>
        </div>
      )}

      {pendingRuns?.length && !isRunExecuting(state.runState) ? (
        <div className="flex items-center justify-between gap-4 border-t border-secondary p-3 px-4 text-sm">
          <div className="flex items-center gap-2">
            <ProgressStarIcon className="h-4 w-4 animate-spin text-brand-green-400" />
            <span className="text-brand-green-500">
              Thread is in a pending state
            </span>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="sm"
                variant="outlined"
                onClick={() => {
                  if (pendingRuns.length === 1) {
                    tryJoinPendingRun(pendingRuns[0].run_id);
                  }
                }}
              >
                Join run
              </Button>
            </DropdownMenuTrigger>
            {pendingRuns.length > 1 && (
              <DropdownMenuContent align="end">
                {pendingRuns.map((run) => (
                  <DropdownMenuItem
                    key={run.run_id}
                    className="flex items-center justify-between gap-8"
                    onClick={() => tryJoinPendingRun(run.run_id)}
                  >
                    <Tooltip title={run.run_id}>
                      <span className="text-sm">
                        Run ...{run.run_id.slice(-4)}
                      </span>
                    </Tooltip>
                    <span className="text-sm text-tertiary">
                      <ElapsedTime date={new Date(run.created_at)} />
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            )}
          </DropdownMenu>
        </div>
      ) : null}

      <div className="sticky bottom-0 flex justify-between gap-2 border-t border-secondary bg-background p-3.5 first-of-type:border-none">
        <div>
          <ManageAssistantsButton disabled={isRunExecuting(state.runState)} />
        </div>

        <div className="flex items-center gap-2">
          {!isRunExecuting(state.runState) && state.threadHead?.next.length ? (
            <EditAndContinueButton
              input={input}
              namespace={props.namespace}
              onDone={(config) =>
                onContinue({
                  // @ts-expect-error LangGraph <0.2, starting checkpoint
                  checkpoint: config?.checkpoint_id ?? config?.thread_ts,
                })
              }
            />
          ) : (
            <GraphInputSubmit
              isLoading={isRunExecuting(state.runState)}
              onClick={onInputSubmit}
              messagesEnabled={messagesEnabled}
              setMessagesEnabled={setMessagesEnabled}
            />
          )}
        </div>
      </div>
    </div>
  );
};
