import { useCallback } from 'react';
import { create } from 'zustand';

import {
  TRACE_LOG_INFO_LEVEL,
  TraceLogInfoLevel,
} from '@/Pages/Graph/src/control/types';

interface TraceLogState {
  expansionStates: Record<string, boolean>;
  traceLogInfoLevel: TraceLogInfoLevel;
  supportedInfoLevels: Set<TraceLogInfoLevel>;
}

interface TraceLogStateActions {
  togglePath: (path: string[], open: boolean) => void;
  setTraceLogInfoLevel: (level: TraceLogInfoLevel) => void;
  toggleSupportedInfoLevel: (level: TraceLogInfoLevel, toggle: boolean) => void;
  resetExpansionStatesForDepth: (depth: number) => void;
}

export const getNodeKey = (path: string[]) => path.join('.');

export const useTraceLogStore = create<TraceLogState & TraceLogStateActions>(
  (set) => ({
    expansionStates: {},
    togglePath: (path: string[], open: boolean) => {
      const pathKey = getNodeKey(path);
      set((state) => ({
        expansionStates: {
          ...state.expansionStates,
          [pathKey]: state.expansionStates[pathKey]
            ? !state.expansionStates[pathKey]
            : open,
        },
      }));
    },
    resetExpansionStatesForDepth: (depth: number) => {
      set((state) => ({
        expansionStates: Object.fromEntries(
          Object.entries(state.expansionStates).filter(
            ([path]) => path.split('.').length < depth
          )
        ),
      }));
    },
    traceLogInfoLevel: TRACE_LOG_INFO_LEVEL.NODE,
    setTraceLogInfoLevel: (level: TraceLogInfoLevel) => {
      set({ traceLogInfoLevel: level });
    },
    supportedInfoLevels: new Set([
      TRACE_LOG_INFO_LEVEL.TURN,
      TRACE_LOG_INFO_LEVEL.NODE,
      TRACE_LOG_INFO_LEVEL.NODE_STATE,
      TRACE_LOG_INFO_LEVEL.NODE_STATE_EXPANDED,
    ]),
    toggleSupportedInfoLevel: (level: TraceLogInfoLevel, toggle: boolean) => {
      set((state) => {
        const newLevels = new Set(state.supportedInfoLevels);
        if (!toggle) {
          newLevels.delete(level);
        } else {
          newLevels.add(level);
        }
        return {
          supportedInfoLevels: newLevels,
        };
      });
    },
  })
);

export const useTraceLogExpansionStateForPath = (
  traceLogLevel: TraceLogInfoLevel
) => {
  const traceLogInfoLevel = useTraceLogStore(
    (state) => state.traceLogInfoLevel
  );
  const expansionStates = useTraceLogStore((state) => state.expansionStates);
  const togglePath = useTraceLogStore((state) => state.togglePath);
  const getIsExpanded = useCallback(
    (path: string[], forceExpanded?: boolean) => {
      const pathKey = getNodeKey(path);
      const userExpanded = expansionStates[pathKey];
      const defaultExpanded =
        traceLogInfoLevel > traceLogLevel || !!forceExpanded;
      return userExpanded ?? defaultExpanded;
    },
    [expansionStates, traceLogLevel, traceLogInfoLevel]
  );
  const setIsExpanded = useCallback(
    (path: string[], open: boolean) => {
      togglePath(path, open);
    },
    [togglePath]
  );
  return { getIsExpanded, setIsExpanded };
};
