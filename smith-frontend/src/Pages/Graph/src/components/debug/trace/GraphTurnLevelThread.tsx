import {
  AlertCircleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@langchain/untitled-ui-icons';
import { Button } from '@mui/joy';

import * as React from 'react';
import {
  RefObject,
  forwardRef,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { GroupedVirtuoso, VirtuosoHandle } from 'react-virtuoso';

import { cn } from '@/utils/tailwind';

import { StateContext } from '../../../control/state';
import { TRACE_LOG_INFO_LEVEL } from '../../../control/types';
import { foreignDataToTree } from '../../../data/foreign-data';
import {
  ErrorEntry,
  NodeEntry,
  PendingEntry,
  TraceLog,
} from '../../../data/misc';
import { useInterruptShown } from '../../../hooks/useInterruptShown';
import { useTurnsForThread } from '../../../hooks/useTurnsForThread';
import { LangSmithAwareContext } from '../../../langsmith';
import { ForeignDataTreeView } from '../common/foreign-data-tree';
import { TraceSelectFork } from './CheckpointEntry';
import { GraphTraceLogRow } from './GraphTraceLogRow';
import { useAutoScrollThreadLog } from './hooks/useAutoScrollThreadLog';
import { useHandleNodeOrTurnExpansion } from './hooks/useHandleNodeOrTurnExpansion';
import { useSetSupportedThreadLogInfoLevels } from './hooks/useSetSupportedThreadLogInfoLevels';
import {
  useTraceLogExpansionStateForPath,
  useTraceLogStore,
} from './store/traceLogStore';

const TurnHeader = ({
  virtuosoRef,
  turnId,
  index,
  highlighted,
}: {
  virtuosoRef: RefObject<VirtuosoHandle>;
  turnId: string;
  index: number;
  highlighted: boolean;
}) => {
  const { isExpanded, setExpanded } = useHandleNodeOrTurnExpansion({
    virtuosoRef,
    expansionPath: [turnId],
    expansionLevel: TRACE_LOG_INFO_LEVEL.TURN,
  });

  const renderTurnDivider = () => {
    return (
      <div className="flex w-full items-center gap-3 pb-4 pl-0.5">
        <button
          onClick={() => setExpanded([turnId], !isExpanded)}
          type="button"
          className="text-primary"
        >
          {isExpanded ? (
            <ChevronDownIcon className="h-4 w-4 shrink-0" />
          ) : (
            <ChevronRightIcon className="h-4 w-4 shrink-0" />
          )}
        </button>
        <span className="whitespace-nowrap text-sm font-medium text-primary">
          TURN {index + 1}
        </span>
        <hr className="border-border flex-grow border-t border-secondary" />
      </div>
    );
  };

  return (
    <div
      key={turnId}
      className={cn(
        'flex w-full flex-col gap-2 bg-primary',
        !isExpanded && highlighted && 'bg-brand-secondary'
      )}
    >
      <div className="px-6 pt-3">{renderTurnDivider()}</div>
    </div>
  );
};

const TurnThread = ({
  virtuosoRef,
  threadEntries,
  index,
  groupIndex,
  isTurnExpanded,
  isLast,
  isLastForTurn,
  turnId,
  setShouldScrollToLast,
  setIndexToHighlight,
}: {
  virtuosoRef: React.RefObject<VirtuosoHandle>;
  threadEntries: TraceLog[];
  index: number;
  groupIndex: number;
  isTurnExpanded: boolean;
  isLast: boolean;
  isLastForTurn: boolean;
  turnId: string;
  setShouldScrollToLast: (open: boolean) => void;
  setIndexToHighlight: (index: number | undefined) => void;
}) => {
  const setTraceLogInfoLevel = useTraceLogStore(
    (state) => state.setTraceLogInfoLevel
  );
  const { TraceLogRow } = useContext(LangSmithAwareContext);
  const { setForestPath } = useContext(StateContext);

  const inputEntry = threadEntries.find(
    ({ entry }) => entry.type === 'node' || entry.type === 'error'
  )?.entry as NodeEntry | ErrorEntry | undefined;

  // i think this technically not correct "output" bc you could have multiple nodes in parallel
  // so should collect the set of entries between the last two checkpoints?
  const outputIndex = threadEntries.findLastIndex(
    ({ entry }) =>
      (entry.type === 'node' ||
        entry.type === 'error' ||
        entry.type === 'pending') &&
      entry.id !== inputEntry?.id
  );
  const output = outputIndex !== -1 ? threadEntries[outputIndex] : undefined;
  const outputEntry = output?.entry as
    | NodeEntry
    | ErrorEntry
    | PendingEntry
    | undefined;

  const lastCheckpointIndex = threadEntries.findLastIndex(
    ({ entry }) => entry.type === 'checkpoint'
  );
  const lastTrace =
    lastCheckpointIndex > outputIndex
      ? threadEntries[lastCheckpointIndex]
      : output;

  const forks = threadEntries.filter(
    ({ entry, meta }) => entry.type === 'checkpoint' && meta.forks.length > 1
  );

  const interruptShown = useInterruptShown();
  const continueShown = isLast && interruptShown;

  useEffect(() => {
    if (continueShown) {
      setIndexToHighlight(groupIndex);
    } else {
      setIndexToHighlight(undefined);
    }
  }, [continueShown, groupIndex, setIndexToHighlight]);

  const entryToShow = threadEntries[index];
  const forceExpanded =
    entryToShow.entry.id === inputEntry?.id ||
    entryToShow.entry.id === outputEntry?.id;
  const { isExpanded, setExpanded } = useHandleNodeOrTurnExpansion({
    virtuosoRef,
    expansionPath: [turnId, entryToShow.entry.id],
    forceExpanded,
    expansionLevel: TRACE_LOG_INFO_LEVEL.NODE,
  });

  const renderInputOrOutput = ({
    entry,
  }: {
    entry?: NodeEntry | ErrorEntry | PendingEntry;
  }) => {
    if (!entry) {
      return <span className="text-sm text-secondary">None</span>;
    }
    if (entry.type === 'error') {
      const errorMsg =
        typeof entry.error === 'string'
          ? entry.error
          : JSON.stringify(entry.error);
      return (
        <div className="line-clamp-1 flex flex-row items-center gap-2">
          <span className="text-sm font-medium text-secondary">Error: </span>
          <span className="text-sm text-error">{errorMsg}</span>
          <AlertCircleIcon className="h-6 w-6 shrink-0 rounded-full bg-error-primary p-0.5 text-error" />
        </div>
      );
    }

    if (entry.type === 'pending') {
      return (
        <div className="line-clamp-1 flex flex-row items-center gap-2">
          <span className="text-sm text-secondary">Pending</span>
        </div>
      );
    }

    return (
      <div
        className={cn(
          'line-clamp-1 flex flex-row justify-start gap-2 text-sm text-tertiary'
        )}
      >
        <ForeignDataTreeView
          data={foreignDataToTree(entry.data)}
          expandable={false}
          expandDepth={0}
        />
      </div>
    );
  };

  const renderTurnIOPreview = (
    inputEntry?: NodeEntry | ErrorEntry,
    outputEntry?: NodeEntry | ErrorEntry | PendingEntry
  ) => {
    return (
      <div
        className={cn(
          'w-full bg-primary px-7 pb-3',
          continueShown && 'bg-brand-secondary'
        )}
      >
        <div className="flex flex-col gap-3 px-7">
          <div className="flex flex-col gap-2">
            <span className="text-sm font-semibold text-secondary">Input</span>
            {renderInputOrOutput({ entry: inputEntry })}
          </div>
          {forks.map((fork) => (
            <TraceSelectFork
              value={fork.meta.path}
              onChange={setForestPath}
              name={fork.entry.name}
              paths={fork.meta.forks}
            />
          ))}
          <div className="flex flex-col gap-2">
            <span className="text-sm font-semibold text-secondary">Output</span>
            {renderInputOrOutput({ entry: outputEntry })}
          </div>

          {continueShown && (
            <div className="flex w-full items-center gap-1.5">
              <div className="rounded-md border border-secondary p-1">
                <AlertCircleIcon className="h-4 w-4 shrink-0 text-secondary" />
              </div>
              <span className="text-xs font-semibold text-primary">
                Execution paused.
              </span>
              <span className="text-xs text-tertiary">Review to continue.</span>
              <Button
                color="primary"
                onClick={() => {
                  setTraceLogInfoLevel(TRACE_LOG_INFO_LEVEL.NODE);
                  setShouldScrollToLast(true);
                  setExpanded([turnId], true);
                }}
                size="sm"
                sx={{
                  marginLeft: 'auto',
                }}
              >
                Review
              </Button>
            </div>
          )}
          <div className="flex items-center gap-2 text-sm empty:hidden">
            {lastTrace && TraceLogRow && <TraceLogRow traceLog={lastTrace} />}
          </div>
        </div>
      </div>
    );
  };

  if (!isTurnExpanded) {
    return renderTurnIOPreview(inputEntry, outputEntry);
  }

  return (
    <div className="bg-primary px-6 py-1.5">
      <GraphTraceLogRow
        key={entryToShow.entry.id}
        traceLog={entryToShow}
        isLast={isLast}
        isExpanded={isExpanded}
        setIsExpanded={setExpanded}
        parentId={turnId}
        interruptsShown={continueShown}
        continueShown={continueShown}
      />
      {isLastForTurn && (
        <div className="flex items-center px-8 py-2 text-sm empty:hidden">
          {output && TraceLogRow && <TraceLogRow traceLog={output} />}
        </div>
      )}
    </div>
  );
};

const GroupedZIndexTopItemsList = forwardRef<
  HTMLDivElement,
  React.HTMLProps<HTMLDivElement>
>((props, ref) => {
  const { children, style, ...rest } = props;
  return (
    <div ref={ref} {...rest} style={{ ...style, zIndex: 9 }}>
      {children}
    </div>
  );
});

const COMPONENTS = { TopItemList: GroupedZIndexTopItemsList };

const GraphTurnLevelThread = () => {
  const virtuosoRef = React.useRef<VirtuosoHandle>(null);

  const { threadGroupedByTurns } = useTurnsForThread();

  const { getIsExpanded } = useTraceLogExpansionStateForPath(
    TRACE_LOG_INFO_LEVEL.TURN
  );
  const { groupCounts, lastIndex } = useMemo(() => {
    const groupCounts = Object.entries(threadGroupedByTurns).map(
      ([runId, thread]) => (getIsExpanded([runId]) ? thread.length : 1)
    );
    const lastIndex = groupCounts.reduce((sum, count) => sum + count, 0) - 1;
    return { groupCounts, lastIndex };
  }, [threadGroupedByTurns, getIsExpanded]);

  const [isAtBottom, setIsAtBottom] = useState(true);
  const { setShouldScrollToLast } = useAutoScrollThreadLog({
    ref: virtuosoRef,
    lastIndex,
    isAtBottom,
  });
  useSetSupportedThreadLogInfoLevels({
    threadGroupedByTurns,
  });

  const [indexToHighlight, setIndexToHighlight] = useState<
    number | undefined
  >();

  if (!groupCounts.length) {
    return null;
  }

  return (
    <GroupedVirtuoso
      ref={virtuosoRef}
      groupCounts={groupCounts}
      groupContent={(index) => (
        <TurnHeader
          virtuosoRef={virtuosoRef}
          turnId={Object.keys(threadGroupedByTurns)[index]}
          index={index}
          highlighted={indexToHighlight === index}
        />
      )}
      components={COMPONENTS}
      itemContent={(index, groupIndex) => {
        const turnId = Object.keys(threadGroupedByTurns)[groupIndex];
        const isTurnExpanded = getIsExpanded([turnId]);
        const previousGroupsTotal = groupCounts
          .slice(0, groupIndex)
          .reduce((sum, count) => sum + count, 0);
        const groupRelativeIndex = index - previousGroupsTotal;

        const threadEntries = threadGroupedByTurns[turnId];

        const isLastGroup =
          groupIndex === Object.keys(threadGroupedByTurns).length - 1;
        const isLastItem = groupRelativeIndex === threadEntries.length - 1;

        const isLast = isTurnExpanded ? isLastGroup && isLastItem : isLastGroup;

        return (
          <TurnThread
            virtuosoRef={virtuosoRef}
            threadEntries={threadEntries}
            index={groupRelativeIndex}
            groupIndex={groupIndex}
            isTurnExpanded={isTurnExpanded}
            isLast={isLast}
            isLastForTurn={groupRelativeIndex === threadEntries.length - 1}
            turnId={turnId}
            setShouldScrollToLast={setShouldScrollToLast}
            setIndexToHighlight={setIndexToHighlight}
          />
        );
      }}
      initialTopMostItemIndex={lastIndex}
      overscan={10}
      atBottomStateChange={(isAtBottom) => {
        setIsAtBottom(isAtBottom);
      }}
      // 50px threshold to determine if we're at the bottom
      atBottomThreshold={50}
    />
  );
};

export default GraphTurnLevelThread;
