import { TabGroup } from '@headlessui/react';
import { Checkpoint } from '@langchain/langgraph-sdk';
import { LinearProgress, Tooltip } from '@mui/joy';

import { LucideArrowLeft, LucideArrowRight } from 'lucide-react';
import * as React from 'react';

import LoadingSlimIcon from '@/Pages/Graph/icons/LoadingSlimIcon.svg?react';
import { ButtonWithTracking } from '@/components/ButtonWithTracking';
import { CodeData } from '@/components/Code/Code';
import { CopyInlineLink } from '@/components/CopyInlineLink';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import { TabLabel, TabList, TabPanel, TabPanels } from '@/components/Tabs';
import { cn } from '@/utils/tailwind';

import { useGraphThreadState } from '../../../api/useGraphSwr';
import { StateContext } from '../../../control/state';
import { isRunExecuting } from '../../../control/state.utils';
import {
  foreignDataToTree,
  parseForeignData,
} from '../../../data/foreign-data';
import { TraceLog } from '../../../data/misc';
import * as Tree from '../../../data/tree';
import { useReRunNode } from '../../../hooks/useReRunNode';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import { ForeignDataTreeView } from '../common/foreign-data-tree';
import { RtfAuto } from '../common/relative-time';

const TraceStateViewTabs = (props: {
  checkpoint?: Checkpoint | string;
  threadId: string;
}) => {
  const state = useGraphThreadState(props.threadId, props.checkpoint);
  const foreignData = React.useMemo(
    () => parseForeignData(state.data?.values, [], 'simple'),
    [state.data]
  );

  if (state.isLoading && !state.data) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }

  return (
    <>
      <TabPanel className="flex flex-col gap-2 p-4">
        <ForeignDataTreeView data={foreignDataToTree(foreignData)} />
      </TabPanel>
      <TabPanel>
        <CodeData maxHeight="30vh" value={state.data} readOnly />
      </TabPanel>
    </>
  );
};

const TraceStateView = (props: { traceLog: TraceLog }) => {
  const state = React.useContext(StateContext);
  const [open, setOpen] = React.useState(false);
  if (!state.threadId) return null;
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        className={cn(
          'flex justify-end whitespace-nowrap rounded-md px-1.5 text-xs font-medium text-secondary transition-colors hover:bg-tertiary '
        )}
      >
        View state
      </PopoverTrigger>
      <PopoverContent
        className="max-h-[50vh] min-w-[600px] overflow-y-auto p-0"
        align="start"
      >
        <div className="sticky top-0 z-10 flex items-center gap-2 border-b border-secondary bg-popover p-4 text-sm text-tertiary">
          Viewing checkpoint:{' '}
          <CopyInlineLink
            value={props.traceLog.meta.id}
            prefix="ID"
            className="font-mono"
          >
            {props.traceLog.meta.id}
          </CopyInlineLink>
        </div>
        <TabGroup>
          <TabList className="mx-0 mb-0">
            <TabLabel>Values</TabLabel>
            <TabLabel>JSON</TabLabel>
          </TabList>
          <TabPanels>
            {open && (
              <TraceStateViewTabs
                threadId={state.threadId}
                checkpoint={
                  props.traceLog.state?.checkpoint ?? props.traceLog.meta.id
                }
              />
            )}
          </TabPanels>
        </TabGroup>
      </PopoverContent>
    </Popover>
  );
};

const TraceRetryButton = (props: {
  traceLog: TraceLog;
  onForkSuccess: () => void;
}) => {
  const state = React.useContext(StateContext);
  const config = useAssistantStore((state) => state.config);
  const updateTarget =
    props.traceLog.meta.source === 'input'
      ? null
      : props.traceLog.state?.checkpoint ?? props.traceLog.meta.id;

  const forkInputNull = useReRunNode({
    onSuccess: props.onForkSuccess,
    updateTarget,
  });

  if (!updateTarget || isRunExecuting(state.runState)) return null;
  return (
    <ButtonWithTracking
      eventName="trace_retry"
      disabled={forkInputNull.isMutating}
      onClick={() => forkInputNull.trigger({ config })}
      className="inline-flex items-center gap-1.5 whitespace-nowrap rounded-md px-1.5 text-xs text-secondary transition-colors hover:bg-tertiary"
    >
      <span className="font-medium">Re-run from here</span>
      {forkInputNull.isMutating && (
        <LoadingSlimIcon className="size-4 animate-spin" />
      )}
    </ButtonWithTracking>
  );
};

const isSamePath = (a: Tree.ForestPath, b: Tree.ForestPath) => {
  return a.length === b.length && a.every((x, i) => b[i] === x);
};

export const TraceSelectFork = (props: {
  name: string;
  paths: Array<Tree.ForestPath>;
  value: Tree.ForestPath;
  onChange: (value: Tree.ForestPath) => void;
  className?: string;
}) => {
  if (props.paths.length < 2) return null;
  const selectedIndex = props.paths.findIndex((path) =>
    isSamePath(props.value, path)
  );

  return (
    <div className={cn('flex items-center gap-2', props.className)}>
      <span className="flex items-center gap-1">
        <button
          type="button"
          disabled={selectedIndex === 0}
          onClick={() => {
            props.onChange(props.paths[Math.max(0, selectedIndex - 1)]);
          }}
          className="text-primary disabled:cursor-not-allowed disabled:text-tertiary"
        >
          <LucideArrowLeft className="size-4 transition-colors" />
        </button>
        <button
          type="button"
          disabled={selectedIndex === props.paths.length - 1}
          onClick={() => {
            props.onChange(
              props.paths[Math.min(props.paths.length - 1, selectedIndex + 1)]
            );
          }}
          className="text-primary disabled:cursor-not-allowed disabled:text-tertiary"
        >
          <LucideArrowRight className="size-4 transition-colors" />
        </button>
      </span>

      <span className="text-xs text-tertiary">
        Fork <span className="tabular-nums">{selectedIndex + 1}</span>
        {' of '}
        <span className="tabular-nums">{props.paths.length}</span>
      </span>
    </div>
  );
};

const CheckpointEntry = (props: {
  traceLog: TraceLog;
  children?: React.ReactNode;
  runIdsMap: Record<string, string>;
  isLast: boolean;
  className?: string;
  onForestPathChange: (value: Tree.ForestPath) => void;
  onForkSuccess: () => void;
}) => {
  const { threadId } = React.useContext(StateContext);

  const renderDiamond = () => {
    return (
      <Tooltip title="Checkpoint" placement="left">
        <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center bg-background">
          <div className="h-[7px] w-[7px] rotate-45 rounded-[1px] bg-current text-primary" />
        </div>
      </Tooltip>
    );
  };

  return (
    <div
      className={cn(
        'group',
        'relative grid grid-cols-[auto,1fr] gap-x-3 py-1.5',
        props.className
      )}
    >
      <div className="flex flex-col items-center ">
        {renderDiamond()}
        <div
          className={cn('relative flex flex-grow', props.isLast && 'opacity-0')}
        >
          <span className="h-full w-px bg-tertiary" />
        </div>
      </div>

      <div className="flex flex-col items-start gap-2">
        <div className="mr-4 flex min-w-0 flex-row items-center gap-3 ">
          <TraceSelectFork
            value={props.traceLog.meta.path}
            onChange={(value) => props.onForestPathChange(value)}
            name={props.traceLog.entry.name}
            paths={props.traceLog.meta.forks}
          />

          <span className="size-1 rounded-full bg-[--black] first:hidden [&:not(:has(+*))]:hidden" />

          <span className="whitespace-nowrap text-xs text-tertiary">
            <RtfAuto time={new Date(props.traceLog.meta.timestamp)} />
          </span>

          <div className="invisible flex items-center gap-3 group-hover:visible">
            <span className="size-1 rounded-full bg-[--black] first:hidden [&:not(:has(+*))]:hidden" />

            {threadId && <TraceStateView traceLog={props.traceLog} />}

            {threadId && (
              <span className="size-1 rounded-full bg-[--black] first:hidden [&:not(:has(+*))]:hidden" />
            )}

            <TraceRetryButton
              traceLog={props.traceLog}
              onForkSuccess={props.onForkSuccess}
            />
          </div>
        </div>

        {props.children}
      </div>
    </div>
  );
};

export default CheckpointEntry;
