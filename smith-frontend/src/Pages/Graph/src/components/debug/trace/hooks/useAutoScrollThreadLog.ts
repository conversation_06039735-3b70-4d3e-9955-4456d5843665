import { RefObject, useContext, useEffect, useState } from 'react';
import { VirtuosoHandle } from 'react-virtuoso';

import { StateContext } from '../../../../control/state';

export const useAutoScrollThreadLog = ({
  ref,
  lastIndex,
  isAtBottom,
}: {
  ref: RefObject<VirtuosoHandle>;
  lastIndex: number;
  isAtBottom: boolean;
}) => {
  const { traceLog } = useContext(StateContext);

  const [shouldScrollToLast, setShouldScrollToLast] = useState(false);
  useEffect(() => {
    if (!isAtBottom) return;
    ref.current?.scrollToIndex({
      index: 'LAST',
      align: 'end',
      behavior: 'auto',
    });
  }, [traceLog, ref, isAtBottom]);

  useEffect(() => {
    if (shouldScrollToLast) {
      ref.current?.scrollToIndex({
        index: lastIndex,
      });
      setShouldScrollToLast(false);
    }
  }, [shouldScrollToLast, ref, lastIndex]);

  return {
    shouldScrollToLast,
    setShouldScrollToLast,
  };
};
