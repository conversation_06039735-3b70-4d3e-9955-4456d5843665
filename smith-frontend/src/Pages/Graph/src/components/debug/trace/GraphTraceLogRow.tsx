import {
  AlertCircleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  Expand01Icon,
  Minimize01Icon,
  PlayCircleIcon,
} from '@langchain/untitled-ui-icons';
import { <PERSON><PERSON>, Tooltip } from '@mui/joy';

import * as React from 'react';
import { useState } from 'react';

import LoadingSlimIcon from '@/Pages/Graph/icons/LoadingSlimIcon.svg?react';
import { CodeData } from '@/components/Code/Code';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { createPortalSlot } from '@/components/PortalSlot';
import { filterInputs } from '@/components/RunsTable/utils/filterInputs';
import { useColorScheme } from '@/hooks/useColorScheme';
import ArrowRightIcon from '@/icons/ArrowRightIcon.svg?react';
import { cn } from '@/utils/tailwind';

import { useGraphThreadHistory } from '../../../api/useGraphSwr';
import { StateContext } from '../../../control/state';
import { getThreadId, isRunExecuting } from '../../../control/state.utils';
import { useTraceLog } from '../../../control/trace';
import { TRACE_LOG_INFO_LEVEL } from '../../../control/types';
import {
  foreignDataToTree,
  parseForeignData,
} from '../../../data/foreign-data';
import {
  TraceLog,
  Updates,
  getCheckpointId,
  traceLogKey,
} from '../../../data/misc';
import * as Tree from '../../../data/tree';
import {
  hashString,
  hslaToStr,
  hslaWithAlpha,
  hslaWithLightness,
  hslaWithMinLightness,
  numberToColor,
  useEnterSubmit,
} from '../../../utils';
import { TraceLogAvatar } from '../common/TraceLogAvatar';
import {
  ForeginDataTreeContext,
  ForeignDataTreeView,
} from '../common/foreign-data-tree';
import { GraphDatasetInlineControls } from '../dataset/GraphDatasetInlineControls';
import { GraphDatasetContext } from '../dataset/GraphDatasetProvider';
import { useRegisterTraceLogToDatasetStore } from '../dataset/GraphDatasetTraceLogStore';
import { overrideReservedLabelsColor } from '../graph/graph.utils';
import CheckpointEntry from './CheckpointEntry';
import { ContinueRefContext } from './GraphInputEditor.utils';
import GraphTraceInputOrNode from './GraphTraceInputOrNode';
import { useTraceLogExpansionStateForPath } from './store/traceLogStore';

const TraceLogActions = createPortalSlot();

const TraceLogPill = (props: {
  label: string;
  className?: string;
  before?: React.ReactNode;
  after?: React.ReactNode;
  onClick?: () => void;
}) => {
  const { isDarkMode } = useColorScheme();
  const hsla = overrideReservedLabelsColor(
    { label: props.label, isDarkMode },
    numberToColor(hashString(props.label))
  );

  return (
    <span
      className={cn(
        'rounded-md border px-2 text-sm font-medium leading-relaxed',
        props.className
      )}
      onClick={props.onClick}
      style={{
        color: hslaToStr(
          overrideReservedLabelsColor(
            { label: props.label, isDarkMode },
            hslaWithLightness(hsla, isDarkMode ? 80 : 40)
          )
        ),
        backgroundColor: hslaToStr(hslaWithAlpha(hsla, 0.2)),
        borderColor: hslaToStr(hslaWithMinLightness(hsla, 60)),
      }}
    >
      {props.before}
      {props.label}
      {props.after}
    </span>
  );
};

const TraceLogContent = (props: {
  traceLog: TraceLog;
  onForkSuccess: () => void;
  retryDisabled: boolean;
  isExpandedSubgraph?: boolean;
  parentId: string;
}) => {
  switch (props.traceLog.entry.type) {
    case 'error': {
      const rawError = props.traceLog.entry.error;
      const error =
        typeof rawError === 'object' && rawError != null
          ? 'message' in rawError
            ? rawError.message
            : JSON.stringify(rawError)
          : rawError;
      return <ExpandableErrorAlert error={error} />;
    }
    case 'pending':
      return <PendingMessages traceLog={props.traceLog} />;
    case 'node':
    case 'input':
      return (
        <GraphTraceInputOrNode
          traceLog={props.traceLog}
          isExpandedSubgraph={props.isExpandedSubgraph}
          onForkSuccess={props.onForkSuccess}
          TraceLogActionsFill={TraceLogActions.Fill}
          parentId={props.parentId}
        />
      );
  }
};

const PendingMessages = React.memo((props: { traceLog: TraceLog }) => {
  const state = React.useContext(StateContext);
  const msgs = state.pendingMessages[props.traceLog.entry.name];

  if (!msgs) return null;
  return (
    <ForeginDataTreeContext.Provider
      value={{ variant: 'default', streaming: true, location: 'studio' }}
    >
      <ForeignDataTreeView data={foreignDataToTree(parseForeignData(msgs))} />
    </ForeginDataTreeContext.Provider>
  );
});

const TracePreviewInput = (props: {
  traceLog: TraceLog;
  onClick: () => void;
}) => {
  if (
    props.traceLog.entry.type === 'error' ||
    props.traceLog.entry.type === 'checkpoint'
  )
    return null;

  const input = props.traceLog.entry.data.raw;
  const preview =
    typeof input === 'object' && input != null
      ? filterInputs(input) ?? ''
      : JSON.stringify(input);

  const isTruncated = preview.length > 50;

  return (
    <span
      onClick={props.onClick}
      className="group/preview cursor-pointer truncate text-sm text-tertiary"
    >
      {preview?.slice(0, 50)}
      {isTruncated && '... '}
      {isTruncated && (
        <span className="text-xs text-tertiary transition-colors group-hover/preview:text-primary">
          (click to expand)
        </span>
      )}
    </span>
  );
};

const TRACE_LOG_TOP_PADDING = 48;
const STICKY_HEADER_HEIGHT = 28;
const STICKY_HEADER_Z_INDEX_MAX = 8;

const GraphTraceContent = (props: {
  traceLog: TraceLog;
  runIdsMap: Record<string, string>;
  hoverId: string;
  children?: React.ReactNode;
  className?: string;
  onForestPathChange: (value: Tree.ForestPath) => void;
  onForkSuccess: () => void;
  isLast: boolean;
  expanded?: boolean;
  setIsExpanded: (open: boolean) => void;
  parentId: string;
  depth: number;
}) => {
  const state = React.useContext(StateContext);
  const datasetState = React.useContext(GraphDatasetContext);
  const threadId = getThreadId(state.runState);
  const focusRef = React.useRef<HTMLDivElement>(null);

  const [subgraphOpen, setSubgraphOpen] = useState(false);

  if (props.traceLog.entry.type === 'checkpoint') {
    return (
      <CheckpointEntry
        traceLog={props.traceLog}
        isLast={props.isLast}
        className={props.className}
        onForestPathChange={props.onForestPathChange}
        onForkSuccess={props.onForkSuccess}
        runIdsMap={props.runIdsMap}
      />
    );
  }

  const subgraphNamespace =
    state.editingCheckpointId !== props.traceLog.meta.id
      ? props.traceLog.task?.checkpoint?.checkpoint_ns
      : undefined;

  const datasetNode = datasetState.nodes.find(
    (node) => node.id === props.traceLog.meta.id
  );

  const isCollapsed =
    datasetState.isAddingToDataset &&
    !datasetState.expandedIds.includes(props.traceLog.meta.id);

  const onToggleCollapsed = () => {
    props.setIsExpanded(!props.expanded);
    if (datasetState.isAddingToDataset) {
      datasetState.toggleExpandedId(props.traceLog.meta.id);
    }
  };

  const showContent = !isCollapsed && props.expanded;

  const errorTooltipMessage: string | undefined =
    !showContent && props.traceLog.entry.type === 'error'
      ? (props.traceLog.entry.error as string)
      : undefined;

  const stickyStyle = {
    top: TRACE_LOG_TOP_PADDING + props.depth * STICKY_HEADER_HEIGHT,
    // decrement z-index by depth, as the top-most sticky headers should be always on top
    // max z-index is 8 to ensure that the headers are not on top of other crucial popovers
    zIndex: Math.max(0, STICKY_HEADER_Z_INDEX_MAX - props.depth),
  };

  return (
    <TraceLogActions.Context>
      <div
        className={cn(
          'relative grid grid-cols-[auto,1fr] gap-x-3',
          props.className
        )}
        onMouseEnter={() => {
          state.setHoverSelection(props.hoverId);
          if (focusRef.current) {
            focusRef.current.dataset['active'] = 'true';
          }
        }}
        onMouseLeave={() => {
          state.setHoverSelection((prev) =>
            prev === props.hoverId ? null : prev
          );

          if (focusRef.current) {
            delete focusRef.current.dataset['active'];
          }
        }}
      >
        <div className="flex flex-col items-center pt-0.5">
          <TraceLogAvatar
            nodeName={props.traceLog.entry.name}
            isSubgraph={subgraphNamespace != null}
            isAddingToDataset={datasetState.isAddingToDataset}
            isDatasetChecked={datasetNode?.visible ?? false}
            onDatasetCheckedChange={(checked) => {
              const node =
                datasetNode ?? datasetState.getDefaultNode(props.traceLog);

              if (!node) return;
              datasetState.updateNode({ ...node, visible: checked });
            }}
            className="sticky"
            style={stickyStyle}
          />
        </div>

        <div className="flex min-w-0 flex-col gap-2">
          <SubgraphSlot.Context>
            <div
              className={cn(
                'group',
                'sticky flex h-6 items-center gap-1.5 bg-primary'
              )}
              style={stickyStyle}
              ref={focusRef}
            >
              <button
                onClick={onToggleCollapsed}
                type="button"
                className="flex h-5 w-5 shrink-0 items-center justify-center text-secondary"
              >
                {props.expanded ? (
                  <ChevronDownIcon className="h-4 w-4 shrink-0" />
                ) : (
                  <ChevronRightIcon className="h-4 w-4 shrink-0" />
                )}
              </button>
              <NodeLabelWithSubgraph
                threadId={threadId}
                isCollapsed={isCollapsed}
                namespace={subgraphNamespace}
                onForkSuccess={props.onForkSuccess}
                subgraphOpen={subgraphOpen}
                setSubgraphOpen={setSubgraphOpen}
                parentId={props.parentId}
                depth={props.depth}
              >
                <span className="text-sm font-semibold">
                  {props.traceLog.entry.name}
                </span>
              </NodeLabelWithSubgraph>

              <TraceLogActions.Slot
                className={cn(
                  'flex items-center gap-2 empty:hidden',
                  'opacity-0 transition-opacity focus-within:opacity-100',
                  'opacity-100',
                  'text-secondary'
                )}
              />
              {errorTooltipMessage && (
                <Tooltip title={errorTooltipMessage as string}>
                  <div className="flex flex-row items-center gap-1 rounded-md border border-error bg-error-secondary px-1 py-0.5 text-error">
                    <AlertCircleIcon className="h-3.5 w-3.5" />
                    <div className="flex flex-row items-center gap-1">
                      <span className="text-xs font-medium leading-none">
                        Error
                      </span>
                    </div>
                  </div>
                </Tooltip>
              )}
            </div>
            <SubgraphSlot.Slot className="flex flex-col empty:hidden" />
          </SubgraphSlot.Context>

          {showContent && (
            <TraceLogContent
              key={props.traceLog.entry.name}
              traceLog={props.traceLog}
              onForkSuccess={props.onForkSuccess}
              retryDisabled={false}
              isExpandedSubgraph={subgraphOpen}
              parentId={props.parentId}
            />
          )}

          {datasetState.isAddingToDataset &&
            (props.traceLog.entry.type === 'node' ||
              props.traceLog.entry.type === 'input') &&
            props.traceLog.entry.name !== '__start__' && (
              <>
                {isCollapsed && (
                  <TracePreviewInput
                    traceLog={props.traceLog}
                    onClick={onToggleCollapsed}
                  />
                )}
                <GraphDatasetInlineControls traceLog={props.traceLog} />
              </>
            )}

          {props.children}
        </div>
      </div>
    </TraceLogActions.Context>
  );
};

const SubgraphSlot = createPortalSlot();
const GraphTraceSubgraph = (props: {
  threadId: string;
  namespace: string;
  children: React.ReactNode;
  updates: Updates[];
  onForkSuccess: () => void;
  open: boolean;
  setOpen: (value: boolean) => void;
  parentId: string;
  depth: number;
}) => {
  const { open, setOpen } = props;
  const state = React.useContext(StateContext);
  const loading = isRunExecuting(state.runState);

  const shouldFetchData = open && !loading;
  const history = useGraphThreadHistory(
    {
      // only fetch if we're not actively streaming and if open
      threadId: shouldFetchData ? props.threadId : null,
      checkpoint: { checkpoint_ns: props.namespace },
    },
    { keepPreviousData: true }
  );

  const [forestPath, setForestPath] = React.useState<Tree.ForestPath>([]);
  const log = useTraceLog(history.data, { updates: props.updates, forestPath });
  useRegisterTraceLogToDatasetStore(
    props.namespace,
    shouldFetchData ? log.traceLog : null
  );

  const subgraphs = props.namespace.split('|').map((i) => {
    const [graphId, taskId] = i.split(':');
    return { graphId, taskId };
  });

  const { getIsExpanded, setIsExpanded } = useTraceLogExpansionStateForPath(
    TRACE_LOG_INFO_LEVEL.NODE
  );

  return (
    <>
      <Tooltip title="See subgraph steps">
        <button
          type="button"
          className="group inline-flex cursor-pointer items-center gap-1.5 self-start rounded"
          onClick={() => {
            if (!open) {
              const selectionId = subgraphs.map((i) => i.graphId).join(':');
              if (!state.subgraphSelection.includes(selectionId)) {
                state.toggleSubgraphSelection(selectionId);
              }
              setOpen(true);
            } else {
              setOpen(false);
            }
          }}
        >
          {props.children}

          {history.isLoading ? (
            <LoadingSlimIcon className="size-6 animate-spin p-1" />
          ) : open ? (
            <Minimize01Icon className="invisible size-6 rounded-md p-1 text-secondary hover:bg-tertiary group-hover:visible" />
          ) : (
            <Expand01Icon className="invisible size-6 rounded-md p-1 text-secondary hover:bg-tertiary group-hover:visible" />
          )}
        </button>
      </Tooltip>

      {open && !history.isLoading && (
        <SubgraphSlot.Fill>
          {log.traceLog.map((traceLog, idx, lst) => {
            const subgraphId = [
              ...subgraphs.map((s) => s.graphId),
              traceLog.entry.name,
            ].join(':');

            return (
              <div key={traceLogKey(traceLog)} className="flex flex-col py-1.5">
                <GraphTraceContent
                  traceLog={traceLog}
                  isLast={lst.length - 1 === idx}
                  hoverId={subgraphId}
                  onForestPathChange={setForestPath}
                  runIdsMap={log.runIdsMap}
                  onForkSuccess={() => {
                    setForestPath(traceLog.meta.path.slice(0, -1));
                    props.onForkSuccess();
                    setOpen(false);
                  }}
                  depth={props.depth + 1}
                  expanded={getIsExpanded([
                    props.parentId,
                    props.namespace,
                    traceLog.entry.id,
                  ])}
                  setIsExpanded={(open) =>
                    setIsExpanded(
                      [props.parentId, props.namespace, traceLog.entry.id],
                      open
                    )
                  }
                  parentId={props.parentId}
                />
              </div>
            );
          })}
        </SubgraphSlot.Fill>
      )}
    </>
  );
};

const NodeLabelWithSubgraph = (props: {
  threadId: string | undefined;
  namespace: string | undefined;
  isCollapsed: boolean;
  children: React.ReactNode;
  onForkSuccess: () => void;
  subgraphOpen: boolean;
  setSubgraphOpen: (value: boolean) => void;
  parentId: string;
  depth: number;
}) => {
  const state = React.useContext(StateContext);
  if (!props.namespace || !props.threadId || props.isCollapsed)
    return props.children;

  return (
    <GraphTraceSubgraph
      threadId={props.threadId}
      namespace={props.namespace}
      updates={state.updates.filter(
        (update) => update.namespace === props.namespace
      )}
      onForkSuccess={props.onForkSuccess}
      open={props.subgraphOpen}
      setOpen={props.setSubgraphOpen}
      parentId={props.parentId}
      depth={props.depth}
    >
      {props.children}
    </GraphTraceSubgraph>
  );
};

const ResumeContinue = () => {
  const state = React.useContext(StateContext);
  const continueRef = React.useContext(ContinueRefContext);

  const checkpoint =
    state.threadHead?.checkpoint ?? getCheckpointId(state.threadHead);
  const [value, setValue] = React.useState('');

  const onSubmit = React.useCallback(() => {
    continueRef.current?.({ checkpoint, command: { resume: value } });
  }, [continueRef, checkpoint, value]);

  useEnterSubmit(state.editingCheckpointId != null, onSubmit);

  return (
    <form>
      <div className="rounded-md border border-secondary bg-secondary">
        <span className="text-md flex px-3 py-2 font-medium tracking-tighter">
          Provide a value to resume execution
        </span>
        <div className="h-px w-full border-t border-secondary" />
        <CodeData
          value={value}
          onChange={setValue}
          language="json"
          actions={
            <div className="m-3 flex items-center gap-2">
              <Button
                type="button"
                onClick={onSubmit}
                size="sm"
                startDecorator={<PlayCircleIcon className="size-5" />}
              >
                Resume
              </Button>
            </div>
          }
        />
      </div>
    </form>
  );
};

const SimpleContinue = () => {
  const state = React.useContext(StateContext);
  const continueRef = React.useContext(ContinueRefContext);

  const checkpoint =
    state.threadHead?.checkpoint ?? getCheckpointId(state.threadHead);

  const onContinue = React.useCallback(
    () => continueRef.current?.({ checkpoint }),
    [continueRef, checkpoint]
  );

  const disabled = state.editingCheckpointId != null;

  useEnterSubmit(disabled, onContinue);

  return (
    <div className="flex items-center gap-2">
      <Button type="button" onClick={onContinue} disabled={disabled} size="sm">
        Continue
      </Button>
      <ArrowRightIcon className="h-4 w-4 text-tertiary" />

      <span className="inline-flex items-center gap-2">
        {state.threadHead?.next.map((name) => (
          <TraceLogPill key={name} label={name} />
        ))}
      </span>
    </div>
  );
};

export const GraphTraceLogRow = ({
  traceLog,
  isLast,
  isExpanded,
  setIsExpanded,
  parentId,
  interruptsShown,
  continueShown,
}: {
  traceLog: TraceLog;
  isLast: boolean;
  isExpanded?: boolean;
  setIsExpanded: (path: string[], open: boolean) => void;
  parentId: string;
  interruptsShown: boolean;
  continueShown: boolean;
}) => {
  const state = React.useContext(StateContext);
  const threadId = getThreadId(state.runState);

  const hasResumable = traceLog.state?.tasks.some((task) =>
    task?.interrupts?.some((i) => i.resumable)
  );

  if (!threadId) return null;

  return (
    <GraphTraceContent
      hoverId={traceLog.entry.name}
      runIdsMap={state.runIdsMap}
      traceLog={traceLog}
      isLast={isLast}
      className={cn('mr-4')}
      onForestPathChange={state.setForestPath}
      onForkSuccess={() => state.setForestPath(traceLog.meta.path.slice(0, -1))}
      expanded={isExpanded}
      setIsExpanded={(open) =>
        setIsExpanded([parentId, traceLog.entry.id], open)
      }
      depth={0}
      parentId={parentId}
    >
      {interruptsShown &&
        traceLog.task?.interrupts?.map((interrupt, idx) => (
          <div
            key={idx}
            className="rounded-md border border-brand-subtle bg-brand-tertiary p-3 text-sm"
          >
            <span className="font-medium uppercase text-brand-primary">
              Interrupt
            </span>

            <div className="mt-1 text-primary">
              <ForeignDataTreeView
                data={foreignDataToTree(parseForeignData(interrupt.value))}
              />
            </div>
          </div>
        ))}
      {continueShown &&
        (hasResumable ? <ResumeContinue /> : <SimpleContinue />)}
    </GraphTraceContent>
  );
};
