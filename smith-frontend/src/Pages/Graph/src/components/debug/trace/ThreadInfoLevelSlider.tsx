import { Slider } from '@mui/joy';

import { useContext, useEffect } from 'react';

import { Tooltip } from '@/components/Tooltip/Tooltip';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import {
  TRACE_LOG_INFO_LEVEL,
  TraceLogInfoLevel,
} from '../../../control/types';
import { GraphDatasetContext } from '../dataset/GraphDatasetProvider';
import { useTraceLogStore } from './store/traceLogStore';

const ThreadInfoLevelSlider = () => {
  const { isAddingToDataset } = useContext(GraphDatasetContext);

  const traceLogInfoLevel = useTraceLogStore(
    (state) => state.traceLogInfoLevel
  );
  const setTraceLogInfoLevel = useTraceLogStore(
    (state) => state.setTraceLogInfoLevel
  );
  const supportedLevels = useTraceLogStore(
    (state) => state.supportedInfoLevels
  );
  const supportedLevelsArray = Array.from(supportedLevels).sort();
  const resetExpansionStatesForDepth = useTraceLogStore(
    (state) => state.resetExpansionStatesForDepth
  );

  const [storedTraceLogInfoLevel, setStoredTraceLogInfoLevel] =
    useLocalStorageState<TraceLogInfoLevel>(
      'ls:studio:traceLogInfoLevel',
      TRACE_LOG_INFO_LEVEL.NODE
    );

  // on mount, set the trace log info level to the stored value
  useEffect(() => {
    setTraceLogInfoLevel(storedTraceLogInfoLevel);
  }, []);

  const getSliderValue = (level: TraceLogInfoLevel) => {
    return supportedLevelsArray.indexOf(level);
  };

  const setSliderValue = (value: number) => {
    setTraceLogInfoLevel(supportedLevelsArray[value]);
    setStoredTraceLogInfoLevel(supportedLevelsArray[value]);
    // when this changes, re-set the expansion states for the correct depth level
    resetExpansionStatesForDepth(value);
  };

  const { isDarkMode } = useColorScheme();

  const maxSliderValue = supportedLevelsArray.length - 1;

  return (
    <Tooltip title="Set the level of detail for the thread log.">
      <div className="flex w-[100px] items-center gap-2 px-1">
        <Slider
          value={getSliderValue(traceLogInfoLevel)}
          onChange={(_, value) => setSliderValue(value as number)}
          min={0}
          max={maxSliderValue}
          step={1}
          color="primary"
          marks={true}
          disabled={isAddingToDataset}
          slotProps={{
            rail: {
              sx: {
                backgroundColor: 'var(--bg-quaternary)',
                mx: '2px',
                height: '4px',
              },
            },
            track: {
              sx: {
                height: '4px',
                backgroundColor: 'var(--bg-quaternary)',
              },
            },
            thumb: {
              sx: {
                backgroundColor: isDarkMode
                  ? 'rgba(209, 209, 214, 1)'
                  : 'white',
                width: '16px',
                height: '16px',
                borderRadius: '100%',
                '&::before': {
                  borderColor: isDarkMode ? 'rgba(47, 104, 104, 1)' : undefined,
                },
              },
            },
            mark: {
              sx: {
                height: '12px',
                backgroundColor: 'var(--bg-quaternary)',
              },
            },
          }}
        />
      </div>
    </Tooltip>
  );
};

export default ThreadInfoLevelSlider;
