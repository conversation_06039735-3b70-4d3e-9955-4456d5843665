import { InfoCircleIcon } from '@langchain/untitled-ui-icons';
import { Tooltip } from '@mui/joy';

import * as React from 'react';
import { ReactNode, ReactPortal, useCallback, useMemo } from 'react';

import SimpleEditIcon from '@/icons/SimpleEditIcon.svg?react';

import {
  useGraphAssistantDebugGraph,
  useGraphAssistantsDebugSchema,
} from '../../../api/useGraphSwr';
import { StateContext } from '../../../control/state';
import { TRACE_LOG_INFO_LEVEL } from '../../../control/types';
import { foreignDataToTree } from '../../../data/foreign-data';
import { InputEntry, NodeEntry, TraceLog } from '../../../data/misc';
import { LangSmithAwareContext } from '../../../langsmith';
import { useAssistantStore } from '../../../store/assistants/assistantsStore';
import { ForeignDataTreeView } from '../common/foreign-data-tree';
import { GraphDatasetContext } from '../dataset/GraphDatasetProvider';
import { EditNodeState } from './EditNodeState';
import { ContinueRefContext } from './GraphInputEditor.utils';
import { useTraceLogStore } from './store/traceLogStore';

const GraphTraceOrNode = ({
  traceLog,
  isExpandedSubgraph,
  onForkSuccess,
  TraceLogActionsFill,
  parentId,
}: {
  traceLog: TraceLog;
  isExpandedSubgraph?: boolean;
  onForkSuccess?: () => void;
  TraceLogActionsFill: (props: { children?: ReactNode }) => ReactPortal | null;
  parentId: string;
}) => {
  const { id: traceLogId } = traceLog.meta;
  const { entry } = traceLog as { entry: InputEntry | NodeEntry };
  const assistantId = useAssistantStore((state) => state.selectedAssistantId);
  const { threadId, editingCheckpointId, setEditingCheckpointId } =
    React.useContext(StateContext);
  const datasetState = React.useContext(GraphDatasetContext);
  const continueRef = React.useContext(ContinueRefContext);

  const traceLogInfoLevel = useTraceLogStore(
    (state) => state.traceLogInfoLevel
  );

  const { PlaygroundPane } = React.useContext(LangSmithAwareContext);
  const expansionStates = useTraceLogStore((state) => state.expansionStates);
  const togglePath = useTraceLogStore((state) => state.togglePath);
  const togglePathForTraceEntry = useCallback(
    (path: string[], open: boolean) => {
      togglePath([parentId, traceLog.entry.id, ...path], open);
    },
    [parentId, traceLog.entry.id, togglePath]
  );
  const expansionStatesForTraceEntry = useMemo(
    () =>
      Object.fromEntries(
        Object.entries(expansionStates)
          // remove entries that are for other trace logs and strip the traceLog id from keys
          .filter(([path]) =>
            path.startsWith(`${parentId}.${traceLog.entry.id}`)
          )
          .map(([path, value]) => [
            path.slice(`${parentId}.${traceLog.entry.id}.`.length),
            value,
          ])
      ),
    [expansionStates, traceLogId, parentId]
  );
  const editing = editingCheckpointId === traceLogId;
  const setEditing = React.useCallback(
    (e: boolean | ((editingState: boolean) => boolean)) => {
      if (typeof e === 'function') {
        const current = editingCheckpointId === traceLogId;
        const toSet = e(current);
        if (toSet || current) {
          setEditingCheckpointId(toSet ? traceLogId : null);
        }
      } else {
        if (e) {
          setEditingCheckpointId(traceLogId);
        } else if (editingCheckpointId === traceLogId) {
          setEditingCheckpointId(null);
        }
      }
    },
    [traceLogId, editingCheckpointId, setEditingCheckpointId]
  );

  const { data: assistantSchemas } = useGraphAssistantsDebugSchema(
    assistantId ? [assistantId] : undefined
  );
  const assistantSchema = assistantSchemas?.[0];
  const subgraphNamespace = traceLog.state?.checkpoint?.checkpoint_ns
    ?.split('|')
    .map((x) => x.split(':').at(0))
    .join('|');

  const graphData = useGraphAssistantDebugGraph(assistantId);
  const subgraphSchema = subgraphNamespace
    ? graphData.data?.subgraphs?.[subgraphNamespace]
    : undefined;

  const schema = subgraphSchema ?? assistantSchema;
  const checkpoint = traceLog.state?.checkpoint ?? traceLogId;
  const foreignDataTree = foreignDataToTree(entry.data);

  return (
    <>
      {!editing && !datasetState.isAddingToDataset && (
        <TraceLogActionsFill>
          <div className="invisible flex items-center gap-2 group-hover:visible">
            {!entry.readonly && checkpoint != null && threadId != null && (
              <Tooltip title="Edit node state">
                <button
                  type="button"
                  onClick={() => setEditing((e) => !e)}
                  className="flex size-6 items-center justify-center rounded-md p-1 text-secondary transition-colors hover:bg-tertiary"
                >
                  <SimpleEditIcon className="size-4 stroke-current" />
                </button>
              </Tooltip>
            )}
            {traceLog.meta.runId && PlaygroundPane && (
              <PlaygroundPane
                runId={traceLog.meta.runId}
                nodeName={entry.name}
                nodeId={entry.id}
                // if the subgraph is expanded, don't show the child LLM runs
                showChildLLMRuns={!isExpandedSubgraph}
              />
            )}

            {entry.readonly && graphData.data != null && (
              <Tooltip title="Node has been removed from the graph.">
                <span>
                  <InfoCircleIcon className="size-4" />
                </span>
              </Tooltip>
            )}
          </div>
        </TraceLogActionsFill>
      )}
      {editing && checkpoint != null && threadId != null ? (
        <EditNodeState
          traceLog={traceLog}
          schema={schema}
          threadId={threadId}
          checkpoint={checkpoint}
          namespace={subgraphNamespace}
          onCancel={() => setEditing(false)}
          onDone={({ checkpoint }) => {
            setEditing(false);
            if (checkpoint) {
              onForkSuccess?.();
              continueRef.current?.({ checkpoint });
            }
          }}
        />
      ) : (
        <div className="flex flex-col gap-2">
          <ForeignDataTreeView
            data={foreignDataTree}
            onToggle={togglePathForTraceEntry}
            dataExpansionStates={expansionStatesForTraceEntry}
            expandAll={
              traceLogInfoLevel === TRACE_LOG_INFO_LEVEL.NODE_STATE_EXPANDED
            }
            expandDepth={
              traceLogInfoLevel === TRACE_LOG_INFO_LEVEL.NODE
                ? 0
                : traceLogInfoLevel === TRACE_LOG_INFO_LEVEL.NODE_STATE
                ? 2
                : undefined
            }
          />
        </div>
      )}
    </>
  );
};

export default GraphTraceOrNode;
