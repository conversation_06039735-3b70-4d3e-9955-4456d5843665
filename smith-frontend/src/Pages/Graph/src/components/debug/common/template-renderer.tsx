import { Tooltip } from '@mui/joy';

import { JSONSchema7Definition } from 'json-schema';
import * as React from 'react';

import { CodeData } from '@/components/Code/Code';
import { PlainTextEditor } from '@/components/PlainTextEditor';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/Select/Select';
import { pluralize } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import {
  getConfigFieldNodes,
  isConfigFieldPrompt,
} from '../../../json-schema-utils';
import { MultiNumberArray } from './MultiNumberArray';
import { MultiStringArray } from './MultiStringArray';
import { TraceLogAvatar } from './TraceLogAvatar';
import { SchemaRenderer } from './schema-editor';

export const TemplateSecretKeyContext = React.createContext<
  (key: string) => () => void
>(() => () => void 0);

type SchemaEnvironment =
  | Array<{ value: unknown; variables: string[] | string }>
  | Array<string>;

const TemplateSecretRegister = (props: { envName: string }) => {
  const register = React.useContext(TemplateSecretKeyContext);
  React.useEffect(() => register(props.envName), [props.envName, register]);
  return null;
};

export const TemplateSecretsEntry = (props: {
  value: unknown;
  schema: JSONSchema7Definition & {
    environment?: SchemaEnvironment;
  };
}) => {
  const isPlainEnvironment = (
    x: SchemaEnvironment | undefined
  ): x is Array<string> => x != null && x.every((x) => typeof x === 'string');

  const rawEnvVars = isPlainEnvironment(props.schema.environment)
    ? props.schema.environment
    : props.schema.environment?.find(
        (x) =>
          x.value === props.value ||
          JSON.stringify(x.value) === JSON.stringify(props.value)
      )?.variables;

  const envVars =
    rawEnvVars != null
      ? !Array.isArray(rawEnvVars)
        ? [rawEnvVars]
        : rawEnvVars
      : [];

  if (!envVars.length) return null;

  return (
    <>
      {envVars.map((envVar) => (
        <TemplateSecretRegister key={envVar} envName={envVar} />
      ))}
    </>
  );
};

const TemplateEntry = (props: {
  name: string | null;
  children: React.ReactNode;
  value: unknown;
  schema: JSONSchema7Definition & {
    environment?: Array<{ value: unknown; variables: string[] }>;
    description?: string;
  };
  showNode?: boolean;
}) => {
  let title = props.name?.replaceAll('_', ' ').replaceAll('-', ' ');
  if (typeof props.schema !== 'boolean') {
    title = props.schema.title || title;
  }

  if (!title)
    return (
      <>
        {props.children}
        <TemplateSecretsEntry schema={props.schema} value={props.value} />
      </>
    );

  const nodeNames = getConfigFieldNodes(props.schema);
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-1">
        <div className="flex w-full items-center gap-1">
          <div className="flex items-center capitalize">{title}</div>
          {props.showNode && !!nodeNames?.length && (
            <div className="ml-auto flex items-center gap-1">
              <span className="text-xs text-secondary">{`Used in ${pluralize(
                'node',
                nodeNames.length
              )}:`}</span>
              {nodeNames.map((nodeName) => (
                <Tooltip title={nodeName} placement="top">
                  <div>
                    <TraceLogAvatar nodeName={nodeName} isSubgraph={false} />
                  </div>
                </Tooltip>
              ))}
            </div>
          )}
        </div>
        {typeof props.schema.description === 'string' &&
          !!props.schema.description && (
            <div className="text-xs text-tertiary">
              {props.schema.description}
            </div>
          )}
      </div>
      <div className="flex flex-col gap-2">
        {props.children}
        <TemplateSecretsEntry schema={props.schema} value={props.value} />
      </div>
    </div>
  );
};

export const templateSchemaRenderer: SchemaRenderer = {
  StringArray: (props) => {
    const options =
      typeof props.schema === 'object' &&
      props.schema.items !== undefined &&
      typeof props.schema.items === 'object' &&
      'enum' in props.schema.items &&
      Array.isArray(props.schema.items.enum)
        ? props.schema.items.enum.filter(
            (x): x is string => typeof x === 'string'
          )
        : [];

    return (
      <TemplateEntry
        showNode={props.options?.showNode}
        value={props.value}
        schema={props.schema}
        name={props.name}
      >
        <MultiStringArray
          value={props.value}
          onChange={props.onChange}
          enum={options}
        />
      </TemplateEntry>
    );
  },
  NumberArray: (props) => {
    const options =
      typeof props.schema === 'object' &&
      props.schema.items !== undefined &&
      typeof props.schema.items === 'object' &&
      'enum' in props.schema.items &&
      Array.isArray(props.schema.items.enum)
        ? props.schema.items.enum.filter(
            (x): x is number => typeof x === 'number'
          )
        : [];

    return (
      <TemplateEntry
        value={props.value}
        schema={props.schema}
        name={props.name}
        showNode={props.options?.showNode}
      >
        <MultiNumberArray
          disableAutoFocus={props.options?.disableAutoFocus}
          value={props.value}
          onChange={props.onChange}
          enum={options}
        />
      </TemplateEntry>
    );
  },
  String: (props) => {
    let content = (
      <PlainTextEditor
        readOnly={props.readOnly}
        className={cn(
          'w-full rounded-lg border border-secondary p-2 px-2.5 transition-colors focus-within:border-brand'
        )}
        textareaClassName="text-sm placeholder:text-quaternary"
        placeholder={props.name}
        value={props.value}
        onChange={(value) => props.onChange(value)}
        resize={isConfigFieldPrompt(props.schema)}
        defaultRows={isConfigFieldPrompt(props.schema) ? 3 : undefined}
        fullLength={true}
      />
    );

    // https://react.dev/errors/31?invariant=31&args%5B%5D=object%2520with%2520keys%2520%257Bcontent%252C%2520additional_kwargs%252C%2520response_metadata%252C%2520type%252C%2520name%252C%2520id%252C%2520example%252C%2520tool_calls%252C%2520invalid_tool_calls%252C%2520usage_metadata%257D
    if (typeof props.value !== 'string' && props.value != null) {
      content = (
        <CodeData
          value={props.value}
          onChange={props.onChange}
          language="json"
        />
      );
    }

    if (typeof props.schema === 'object') {
      let schemaEnums =
        'enum' in props.schema &&
        Array.isArray(props.schema.enum) &&
        props.schema.enum.length > 0
          ? props.schema.enum
          : null;

      if (
        'environment' in props.schema &&
        Array.isArray(props.schema.environment)
      ) {
        schemaEnums = props.schema.environment.map((i) => i.value);
      }
      if (schemaEnums) {
        content = (
          <Select value={props.value} onValueChange={props.onChange}>
            <SelectTrigger className="min-h-[38px] px-2.5 py-2 text-left">
              <SelectValue placeholder={props.name} />
            </SelectTrigger>
            <SelectContent>
              {schemaEnums
                ?.filter((x): x is string => typeof x === 'string')
                .map((value: string, i) => (
                  <SelectItem key={i} value={value}>
                    {value}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        );
      }
    }

    return (
      <TemplateEntry
        value={props.value}
        schema={props.schema}
        name={props.name}
        showNode={props.options?.showNode}
      >
        {content}
      </TemplateEntry>
    );
  },
  Number: (props) => (
    <TemplateEntry
      value={props.value}
      schema={props.schema}
      name={props.name}
      showNode={props.options?.showNode}
    >
      <input
        disabled={props.readOnly}
        className="w-full rounded-lg border border-secondary bg-background p-2 px-2.5 text-sm transition-colors focus-within:border-brand"
        placeholder="Input"
        type="number"
        value={
          typeof props.value !== 'number' || Number.isNaN(props.value)
            ? ''
            : props.value
        }
        onChange={(e) => props.onChange(e.target.valueAsNumber)}
      />
    </TemplateEntry>
  ),
  Boolean: (props) => (
    <TemplateEntry
      value={props.value}
      schema={props.schema}
      name={props.name}
      showNode={props.options?.showNode}
    >
      <input
        disabled={props.readOnly}
        type="checkbox"
        checked={props.value}
        onChange={(e) => props.onChange(e.target.checked)}
      />
    </TemplateEntry>
  ),

  Default: (props) => {
    const isValidJsonSchema =
      typeof props.schema === 'object' && !!props.schema.type;

    return (
      <TemplateEntry
        value={props.value}
        schema={props.schema}
        name={isValidJsonSchema ? props.name : null}
        showNode={props.options?.showNode}
      >
        {isValidJsonSchema && (
          <div className="rounded-md border border-secondary">
            <CodeData value={props.value} onChange={props.onChange} />
          </div>
        )}
      </TemplateEntry>
    );
  },
  Wrapper: (props) => (
    <>
      <TemplateSecretsEntry schema={props.schema} value={props.value} />
      {props.children}
    </>
  ),
  Message: () => null,
  MessageArray: () => null,
  MessageRecord: () => null,
  Object: (props) => (
    <div className="relative">
      <TemplateEntry
        value={props.value}
        schema={props.schema}
        name={props.name}
        showNode={props.options?.showNode}
      >
        <div className="relative ml-[11px] border-l border-primary pl-4">
          {React.Children.map(props.children, (child, index) => (
            <div key={index} className="relative">
              <div className="absolute -left-4 top-4 h-px w-4 bg-secondary" />
              {child}
            </div>
          ))}
        </div>
      </TemplateEntry>
    </div>
  ),
};
