import { ChevronDownIcon } from '@langchain/untitled-ui-icons';

import * as React from 'react';
import { useEffect } from 'react';

import { RunPageDocumentList } from '@/Pages/Run/components/RunPageDocument';
import { isBase64Image, isValidUrl } from '@/components/Code/utils';
import { CopyInlineLink } from '@/components/CopyInlineLink';
import { Skeleton } from '@/components/Skeleton';
import { TextWithLinks } from '@/components/TextWithLinks';
import { TruncateLargeTextContent } from '@/components/TruncateLargeTextContent';
import { useForeignDataParser } from '@/hooks/useForeignDataParser';
import CopyIcon from '@/icons/CopyIcon.svg?react';
import { cn } from '@/utils/tailwind';

import { RenderModeContext } from '../../../control/state';
import { Document } from '../../../data/document';
import {
  FlatRecord,
  ForeignData,
  ForeignDataTree,
  SimplePrimitive,
  _Unknown,
  foreignDataPreview,
} from '../../../data/foreign-data';
import {
  JSONArray,
  JSONObject,
  JSONValue,
  jsonValueIsSmall,
  jsonValuePreview,
} from '../../../data/json';
import { Message } from '../../../data/message';
import { isEmptyObj, isPlainObj } from '../../../utils';
import { MessageViewInner } from '../trace/message';
import { getMessagePreview, getMessageTitle } from '../trace/preview-line';
import { getNodeKey } from '../trace/store/traceLogStore';
import { JsonView } from './json-view';

const DepthContext = React.createContext({
  depth: 0,
  path: [] as string[],
});

const CollapsableDataRow = ({
  children,
  head,
  initOpen,
  expandable = true,
  testId,
  onToggle,
  path: _path,
  getIsOpen,
}: {
  children: React.ReactNode;
  head: (props: { open: boolean; isNested: boolean }) => {
    title?: React.ReactNode;
    actions?: React.ReactNode;
    value?: React.ReactNode;
  } | null;
  initOpen?: boolean;
  expandable?: boolean;
  testId?: string;
  onToggle?: (path: string[], open: boolean) => void;
  path?: string;
  getIsOpen?: (depth: number, key: string) => boolean | undefined;
}) => {
  const { depth, path: fullPath } = React.useContext(DepthContext);
  const path = _path ? [...fullPath, _path] : fullPath;
  const isNested = depth > 0;
  const [_open, setOpen] = React.useState(() => {
    if (initOpen != undefined) return initOpen;
    return depth < 1;
  });

  const open = getIsOpen?.(depth, getNodeKey(path)) ?? _open;
  const headContent = head({ open, isNested });

  const showExpandedContent = headContent?.value === false || open;

  return (
    <DepthContext.Provider value={{ depth: depth + 1, path }}>
      <div
        className={cn(
          'group/collapsible relative flex w-full flex-col items-stretch justify-center gap-y-1',
          isNested && 'pl-6'
        )}
        data-testid={testId}
      >
        {headContent && (
          <button
            type="button"
            disabled={!expandable}
            className="grid w-full min-w-0 select-none grid-cols-[auto,1fr] items-center justify-center gap-2 text-left outline-none"
            onClick={() => {
              setOpen((o) => !o);
              onToggle?.(path, !open);
            }}
          >
            <div
              className={cn(
                'flex min-w-0 items-center gap-1.5',
                isNested && 'gap-2'
              )}
            >
              {expandable && (
                <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center">
                  <ChevronDownIcon
                    className={cn(
                      'h-4 w-4 flex-shrink-0',
                      !open && '-rotate-90',
                      isNested && '-ml-1'
                    )}
                  />
                </div>
              )}

              <span className="min-w-[30px] truncate whitespace-nowrap text-sm font-medium">
                {headContent?.title}
              </span>
            </div>
            <span className="flex min-w-[30px] items-center gap-2">
              {headContent?.value && (
                <span className="min-w-0 truncate text-sm text-tertiary">
                  {headContent?.value}
                </span>
              )}

              {headContent?.actions && (
                <span className="opacity-0 transition-opacity group-hover/collapsible:opacity-100">
                  {headContent?.actions}
                </span>
              )}
            </span>
          </button>
        )}
        {showExpandedContent && (
          <span className="flex min-w-0 flex-col gap-2 pl-0 text-sm empty:hidden">
            {children}
          </span>
        )}
      </div>
    </DepthContext.Provider>
  );
};

export const ForeginDataTreeContext = React.createContext<{
  variant: 'json-like' | 'default';
  streaming: boolean;
  location: 'studio' | 'langsmith';
} | null>(null);

const SmallPill = (props: { children: string }) => {
  return (
    <div
      className={cn(
        'flex w-fit place-items-center rounded bg-gray-100 px-1 text-xs'
      )}
    >
      <div className="flex leading-relaxed">
        <span className={cn('font-medium', 'text-gray-700')}>
          {props.children}
        </span>
      </div>
    </div>
  );
};

const DocumentView = ({ document }: { document: Document }) => {
  const [mode, setMode] = React.useState<'content' | 'metadata'>('content');

  return (
    <div className="max-h-[60ch] overflow-y-auto whitespace-pre-line py-2 text-tertiary [scrollbar-width:thin]">
      {document.metadata && !isEmptyObj(document.metadata) && (
        <div
          className="mb-4 select-none"
          onClick={() =>
            setMode((m) => (m === 'metadata' ? 'content' : 'metadata'))
          }
        >
          <SmallPill>{'metadata {}'}</SmallPill>
        </div>
      )}
      {mode === 'metadata' && document.metadata && (
        <div className="mb-4">
          <JsonView json={document.metadata} />
        </div>
      )}
      <TruncateLargeTextContent
        text={document.page_content}
        noSpanWrapper={true}
      >
        {document.page_content}
      </TruncateLargeTextContent>
    </div>
  );
};

const DocumentsView = ({ documents }: { documents: Array<Document> }) => {
  return (
    <div className="mt-2">
      <RunPageDocumentList documents={documents} />
    </div>
  );
};

const MessageTitle = ({ message }: { message: Message }) => {
  return (
    <div className="inline-flex gap-2">
      <span className="truncate font-semibold">{getMessageTitle(message)}</span>
    </div>
  );
};

const MessageActions = ({
  message,
  open,
}: {
  message: Message;
  open: boolean;
}) => {
  if (!message.id || !open) return null;
  return (
    <CopyInlineLink
      className="rounded-full border border-secondary px-1 font-sans text-xs font-normal"
      value={message.id}
      icon={<CopyIcon className="size-3" />}
    >
      ID
    </CopyInlineLink>
  );
};

const MessagesView = ({
  messages,
  ...props
}: { messages: Array<Message> } & ForeignDataRecursiveProps) => {
  if (messages.length === 0) return null;
  return (
    <div className="flex flex-col gap-1">
      {messages.map((msg, i) => {
        return (
          <div key={i} className="flex items-start gap-2">
            <CollapsableDataRow
              key={i}
              initOpen={messages.length === 1}
              head={({ open, isNested }) => {
                if (open && !isNested && messages.length === 1) return null;

                return {
                  title: <MessageTitle message={msg} />,
                  actions: <MessageActions message={msg} open={open} />,
                  value: !open && getMessagePreview(msg),
                };
              }}
              {...props}
              path={getNodeKey([props.path ?? '', msg.id ?? i.toString()])}
              getIsOpen={(depth, key) =>
                messages.length === 1 || props.getIsOpen?.(depth, key)
              }
            >
              <MessageViewInner message={msg} {...props} />
            </CollapsableDataRow>
          </div>
        );
      })}
    </div>
  );
};

const SimplePrimitiveView = ({ data }: { data: SimplePrimitive }) => {
  const treeContext = React.useContext(ForeginDataTreeContext);
  if (data == null || data === '') {
    return (
      <div className="flex flex-col gap-2">
        <span className="whitespace-pre-wrap text-tertiary">
          {data === '' ? '""' : 'null'}
        </span>
      </div>
    );
  }

  const stringData = data.toString();
  const shouldShowImage = isBase64Image(stringData) || isValidUrl(stringData);

  return (
    <div className="flex flex-col gap-2">
      {shouldShowImage ? (
        <div className="relative">
          <img
            src={stringData}
            alt="Rendered Image"
            className="h-auto max-w-full rounded"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                const fallback = document.createElement('span');
                fallback.className = 'whitespace-pre-wrap text-tertiary';
                fallback.textContent = stringData;
                parent.appendChild(fallback);
              }
            }}
          />
        </div>
      ) : (
        <span className="w-full whitespace-pre-wrap text-tertiary">
          <TextWithLinks
            text={stringData}
            containerHeight={
              treeContext?.location === 'langsmith' ? '50vh' : undefined
            }
          />
        </span>
      )}
    </div>
  );
};

const FlatRecordView = ({ data }: { data: FlatRecord }) => {
  const entries = Object.entries(data);
  if (entries.length === 0) return null;
  return (
    <div className="flex flex-col gap-2">
      {entries.map(([k, v], i) => (
        <CollapsableDataRow key={i} head={() => ({ title: k })}>
          <SimplePrimitiveView data={v} />
        </CollapsableDataRow>
      ))}
    </div>
  );
};

const JSONObjectView = ({ data }: { data: JSONObject }) => {
  const entries = Object.entries(data);
  if (entries.length === 0) return null;
  return (
    <div className="flex flex-col gap-2">
      {entries.map(([k, v], i) => (
        <CollapsableDataRow
          key={i}
          head={({ open }) => ({
            title: k,
            value: (!open || jsonValueIsSmall(v)) && jsonValuePreview(v),
          })}
        >
          <JSONValueView data={v} />
        </CollapsableDataRow>
      ))}
    </div>
  );
};

const JSONArrayView = ({ data }: { data: JSONArray }) => {
  if (data.length === 0) return null;
  return (
    <div className="flex flex-col gap-2">
      {data.map((v, i) => (
        <CollapsableDataRow
          key={i}
          head={({ open }) => ({
            title: i,
            value: (!open || jsonValueIsSmall(v)) && jsonValuePreview(v),
          })}
        >
          <JSONValueView data={v} />
        </CollapsableDataRow>
      ))}
    </div>
  );
};

const JSONValueView = ({ data }: { data: JSONValue }) => {
  switch (typeof data) {
    case 'string':
      return <SimplePrimitiveView data={data} />;
    case 'number':
      return <SimplePrimitiveView data={data} />;
    case 'boolean':
      return <SimplePrimitiveView data={data} />;
    case 'object':
      if (data === null) return <SimplePrimitiveView data={data} />;
      if (Array.isArray(data)) return <JSONArrayView data={data} />;
      return <JSONObjectView data={data} />;
    default:
      return <SimplePrimitiveView data={data} />;
  }
};

const UnknownView = ({ data }: { data: _Unknown }) => {
  return (
    <JsonView
      json={
        isPlainObj(data.value)
          ? data.value
          : { [data.path.join('.')]: data.value }
      }
    />
  );
};

const PlainRegex = /^[a-zA-Z0-9_-]*$/;

const PrettyHeading = ({ children }: { children: string }) => {
  const config = React.useContext(ForeginDataTreeContext);
  if (config?.variant === 'json-like') {
    return <span className="font-mono">{children}</span>;
  }

  if (children.match(PlainRegex)) {
    return (
      <span className="capitalize">
        {children.replaceAll('_', ' ').replaceAll('-', ' ')}
      </span>
    );
  }

  return <span>{children}</span>;
};

type ForeignDataRenderer = {
  [K in ForeignData['type']]: React.FC<{
    data: Extract<ForeignData, { type: K }>;
  }>;
};

const foreignDataRenderer: ForeignDataRenderer = {
  Document: ({ data }) => <DocumentView document={data.value} />,
  Documents: ({ data }) => <DocumentsView documents={data.value} />,
  FlatRecord: ({ data }) => <FlatRecordView data={data.value} />,
  JSON: ({ data }) => <JSONValueView data={data.value} />,
  JSONArray: ({ data }) => <JSONArrayView data={data.value} />,
  JSONObject: ({ data }) => <JSONObjectView data={data.value} />,
  Message: ({ data, ...props }) => (
    <CollapsableDataRow
      head={({ open, isNested }) => {
        if (open && !isNested) return null;
        return {
          title: <MessageTitle message={data.value} />,
          actions: <MessageActions message={data.value} open={open} />,
          value: !open && getMessagePreview(data.value),
        };
      }}
      {...props}
    >
      <MessageViewInner message={data.value} />
    </CollapsableDataRow>
  ),
  Messages: ({ data, ...props }) => (
    <MessagesView messages={data.value} {...props} />
  ),
  Unknown: ({ data }) => (
    <CollapsableDataRow head={() => ({ title: 'Unknown Value' })}>
      <UnknownView data={data} />
    </CollapsableDataRow>
  ),
};

const renderForeignData = (
  data: ForeignData,
  props: ForeignDataRecursiveProps
) =>
  // @ts-expect-error - this is safe, but would require needless type refinement otherwise
  foreignDataRenderer[data.type]({ data, ...props });

export const AsyncForeignDataTreeView = ({
  data,
  expandAll,
}: {
  data: unknown;
  expandAll?: boolean;
}) => {
  const { parseData, data: parsedData } = useForeignDataParser();

  useEffect(() => {
    if (data) {
      parseData(data);
    }
  }, [data]);

  if (!parsedData)
    return (
      <div>
        <Skeleton />
      </div>
    );

  return <ForeignDataTreeView data={parsedData} expandAll={expandAll} />;
};

const CHUNK_SIZE = 100;

const DataChunk = ({
  data,
  index,
  total,
}: {
  data: ForeignDataTree[];
  index: number;
  total: number;
}) => {
  const start = index * CHUNK_SIZE;
  const end = Math.min(start + CHUNK_SIZE, total);

  return (
    <CollapsableDataRow
      expandable={true}
      initOpen={index === 0}
      head={() => ({
        title: `${start + 1}-${end}`,
      })}
      testId={`data-chunk-${index}`}
    >
      {data.map((x, i) => (
        <ForeignDataTreeView key={i} data={x} />
      ))}
    </CollapsableDataRow>
  );
};

export type ForeignDataRecursiveProps = {
  path?: string;
  getIsOpen?: (depth: number, key: string) => boolean | undefined;
  // represents user controlled expansion states. If defined, overrides the default behavior of the component
  dataExpansionStates?: Record<string, boolean>;
  onToggle?: (path: string[], open: boolean) => void;
  expandAll?: boolean;
  expandDepth?: number;
  expandable?: boolean;
};

export const ForeignDataChildrenView = (
  props: ForeignDataRecursiveProps & {
    data: ForeignDataTree[];
  }
) => {
  const { data, ...rest } = props;
  if (data.length <= CHUNK_SIZE) {
    return (
      <>
        {data.map((x, i) => (
          <ForeignDataTreeView key={i} data={x} {...rest} />
        ))}
      </>
    );
  }

  const chunks = Array.from(
    { length: Math.ceil(data.length / CHUNK_SIZE) },
    (_, index) => {
      const start = index * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, data.length);
      return data.slice(start, end);
    }
  );

  return (
    <div className="flex flex-col gap-2">
      {chunks.map((chunk, index) => (
        <DataChunk key={index} data={chunk} index={index} total={data.length} />
      ))}
    </div>
  );
};

export const ForeignDataTreeView = (
  props: ForeignDataRecursiveProps & {
    data: ForeignDataTree;
  }
) => {
  const { data, ...rest } = props;
  const { dataExpansionStates, onToggle, expandAll, expandable, expandDepth } =
    rest;
  const { renderMode } = React.useContext(RenderModeContext);

  if (renderMode === 'data') {
    return (
      <div className="flex flex-col gap-2">
        <JsonView json={data.raw as object} />
      </div>
    );
  }

  const isInitialOpen = () => {
    if (data.type === 'Node') {
      return data.children.length < 20;
    }
    return;
  };

  const getIsOpen = (depth: number, key: string) => {
    if (dataExpansionStates?.[key] !== undefined)
      return dataExpansionStates[key];
    if (expandAll) return true;
    if (expandDepth !== undefined) return depth < expandDepth;
    return;
  };

  // if the key is empty, just render the content
  if (data.key === '') {
    if (data.type === 'Node') {
      return <ForeignDataChildrenView data={data.children} {...rest} />;
    }
    if (data.type === 'Leaf') {
      return (
        <>
          {renderForeignData(data.value, {
            ...rest,
            onToggle,
            getIsOpen,
          })}
        </>
      );
    }
  }

  // otherwise render the key and collapsible children
  return (
    <CollapsableDataRow
      onToggle={onToggle}
      head={({ open }) => ({
        title: <PrettyHeading>{data.key}</PrettyHeading>,
        ...(data.type === 'Leaf' && {
          value: !open && foreignDataPreview(data.value),
        }),
      })}
      testId={`foreign-data-tree-${data.type.toLowerCase()}-${data.key}`}
      path={data.key}
      expandable={expandable}
      getIsOpen={getIsOpen}
      initOpen={isInitialOpen()}
    >
      {data.type === 'Leaf' ? (
        renderForeignData(data.value, {
          ...rest,
          getIsOpen,
          path: data.key,
        })
      ) : (
        <ForeignDataChildrenView data={data.children} {...rest} />
      )}
    </CollapsableDataRow>
  );
};
