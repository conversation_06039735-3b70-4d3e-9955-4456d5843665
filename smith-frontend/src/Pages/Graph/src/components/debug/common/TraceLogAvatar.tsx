import { User03Icon } from '@langchain/untitled-ui-icons';
import { Checkbox } from '@mui/joy';

import GraphFillIcon from '@/Pages/Graph/icons/GraphFillIcon.svg?react';
import { useColorScheme } from '@/hooks/useColorScheme';
import { cn } from '@/utils/tailwind';

import {
  HSLA,
  hashString,
  hslaToStr,
  hslaWithAlpha,
  hslaWithLightness,
  hslaWithMinLightness,
  numberToColor,
} from '../../../utils';
import { overrideReservedLabelsColor } from '../graph/graph.utils';

const CHECKPOINT_COLOR: HSLA = [240, 4.8, 95.9, 1];

export const TraceLogAvatar = (props: {
  nodeName: string;
  className?: string;
  style?: React.CSSProperties;
  isSubgraph?: boolean;

  isAddingToDataset?: boolean;
  isDatasetChecked?: boolean;
  onDatasetCheckedChange?: (checked: boolean) => void;
}) => {
  const { isDarkMode } = useColorScheme();
  const hsla = props.nodeName
    ? overrideReservedLabelsColor(
        { label: props.nodeName, isDarkMode },
        numberToColor(hashString(props.nodeName))
      )
    : CHECKPOINT_COLOR;

  if (props.isAddingToDataset && props.nodeName !== '__start__') {
    return (
      <Checkbox
        checked={props.isDatasetChecked ?? false}
        onChange={(e) => props.onDatasetCheckedChange?.(e.target.checked)}
        size="sm"
        className={cn('size-5 items-center justify-center', props.className)}
      />
    );
  }

  return (
    <span
      className={cn(
        'flex size-5 items-center justify-center rounded-full border border-secondary text-center text-[10px] font-semibold uppercase',
        props.isSubgraph && 'rounded',
        props.className
      )}
      style={{
        ...props.style,
        color: hslaToStr(
          overrideReservedLabelsColor(
            { label: props.nodeName, isDarkMode },
            hslaWithLightness(hsla, isDarkMode ? 80 : 40)
          )
        ),
        backgroundColor: hslaToStr(hslaWithAlpha(hsla, 0.2)),
        borderColor: hslaToStr(
          props.nodeName
            ? hslaWithMinLightness(hsla, props.nodeName ? 60 : 80)
            : hsla
        ),
      }}
    >
      {props.isSubgraph ? (
        <GraphFillIcon className="size-3" />
      ) : props.nodeName === '__start__' ? (
        <User03Icon className="size-3" />
      ) : (
        props.nodeName.charAt(0)
      )}
    </span>
  );
};
