import { Link } from 'react-router-dom';

import { InformationalBanner } from '@/components/InformationalBanner';
import { useIsSelfHosted } from '@/hooks/useIsSelfHosted';
import { OrgConfigs, useOrgConfig } from '@/hooks/useOrgConfig';
import { useOrganizationId } from '@/hooks/useSwr';
import { appOrganizationPath } from '@/utils/constants';

import { OrganizationDashboard } from './components/OrganizationDashboard';
import { OrganizationUsageCharts } from './components/OrganizationUsageCharts';

export function OrganizationUsage() {
  const tenantId = useOrganizationId();

  const { value: ffEnableOrgUsageCharts } = useOrgConfig(
    OrgConfigs.enable_org_usage_charts
  );

  const { value: ffEnableMonthlyUsageCharts } = useOrgConfig(
    OrgConfigs.enable_monthly_usage_charts
  );

  const selfHosted = useIsSelfHosted();

  return (
    <div className="flex h-full flex-col overflow-auto">
      <InformationalBanner>
        <div>
          <div className="text-sm font-semibold">
            To reduce your monthly spend,{' '}
            <Link
              className="underline"
              to={`/${appOrganizationPath}/${tenantId}/settings/payments?tab=1`}
            >
              configure usage
            </Link>{' '}
            for each workspace.
          </div>
          <div className="text-sm font-semibold">
            Usage may take a couple hours to show up.
          </div>
        </div>
      </InformationalBanner>
      <div className="flex-auto">
        {selfHosted ? (
          ffEnableMonthlyUsageCharts ? (
            <OrganizationDashboard type="usage" />
          ) : ffEnableOrgUsageCharts ? (
            <OrganizationUsageCharts />
          ) : null
        ) : (
          <OrganizationDashboard type="usage" />
        )}
      </div>
    </div>
  );
}
