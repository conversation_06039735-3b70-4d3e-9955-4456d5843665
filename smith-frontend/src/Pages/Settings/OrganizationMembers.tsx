import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mui/joy';
import LinearProgress from '@mui/joy/LinearProgress';

import { ReactNode, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import { InformationalBanner } from '@/components/InformationalBanner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/Table';
import {
  TabGroup,
  TabLabel,
  TabList,
  TabPanel,
  TabPanels,
} from '@/components/Tabs';
import { usePermissions } from '@/hooks/usePermissions.tsx';
import {
  useCurrentOrganization,
  useOrganizationId,
  usePermissionsList,
  useRoles,
  useTenantList,
} from '@/hooks/useSwr';
import EditIcon from '@/icons/EditIcon.svg?react';
import {
  appOrganizationPath,
  appSettingsPath,
  mixedLoginMethods,
} from '@/utils/constants';
import { isSelfHosted } from '@/utils/is-self-hosted';

import { OrganizationSSOConfiguration } from './OrganizationSSOConfiguration';
import { EditRoleDrawer } from './components/EditRoleDrawer';
import { MembersTableWithTabs } from './components/MembersTable';
import { UpsellBadge } from './components/UpsellBadge';
import { MemberGroupType } from './types/organizationMembers.type';
import {
  extractGroups,
  plural,
  sortRoles,
} from './utils/organizationMembers.utils';

function OrganizationRoleList() {
  const roles = useRoles(undefined);

  const nRoles = roles.data?.length ?? 0;

  const sortedRoles = sortRoles(roles.data);

  const permissions = usePermissionsList();
  const workspacePermissionsOnly = permissions.data?.filter(
    (p) => !p.name.startsWith('organization')
  );
  const permissionGroups = extractGroups(workspacePermissionsOnly);

  const { authorize } = usePermissions();

  if (roles.isLoading || permissions.isLoading) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-secondary">
      <div className="flex justify-between border-b border-secondary px-5 py-4">
        <div>
          <span>{plural(nRoles, 'role', 's')}</span>
        </div>
        <div>
          {authorize('workspaces:manage') && (
            <EditRoleDrawer
              trigger={(onClick) => (
                <Button size="sm" onClick={onClick} color="primary">
                  Create Role
                </Button>
              )}
              permissionGroups={permissionGroups}
            ></EditRoleDrawer>
          )}
        </div>
      </div>

      <Table className="table-fixed">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[55%] pl-5">Role</TableHead>
            <TableHead className="w-[15%] text-center">
              <span className="capitalize">Permissions</span>
            </TableHead>
            <TableHead className="w-[15%] text-center">
              <span className="capitalize">Edit</span>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedRoles?.map((role) => {
            return (
              <TableRow key={role.id}>
                <TableCell className="w-[55%] pl-5">
                  <div>{role.display_name}</div>
                  <div className="text-tertiary">{role.description}</div>
                </TableCell>

                <TableCell className="w-[15%]  text-center">
                  <span className="rounded bg-secondary-hover px-4 py-2">
                    {role.permissions.length}
                  </span>
                </TableCell>
                {authorize('workspaces:manage') && (
                  <TableCell className="w-[15%]  text-center">
                    <EditRoleDrawer
                      role={role}
                      permissionGroups={permissionGroups}
                      trigger={(onClick) => {
                        return (
                          <Tooltip title="Edit">
                            <Button
                              aria-label="edit"
                              disabled={role.name !== 'CUSTOM'}
                              onClick={onClick}
                              variant="outlined"
                              color="neutral"
                              size="sm"
                              className="flex flex-none gap-2 self-center"
                            >
                              <EditIcon></EditIcon>
                            </Button>
                          </Tooltip>
                        );
                      }}
                    ></EditRoleDrawer>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}

function OrganizationMembersPage() {
  const organizationQuery = useCurrentOrganization();
  const organizationId = useOrganizationId();
  const isRbacEnabled = organizationQuery.data?.config.can_use_rbac;
  const isSsoEnabled = organizationQuery.data?.config.can_use_saml_sso;
  const canAddSeats = organizationQuery.data?.config?.can_add_seats;
  const { authorize } = usePermissions('organization');

  if (organizationQuery.isLoading) {
    return <LinearProgress />;
  }

  let banner: ReactNode = null;
  if (!canAddSeats) {
    banner = (
      <InformationalBanner>
        <div className="text-sm font-semibold">
          To add more members to your organization please{' '}
          <Link
            to={`/${appOrganizationPath}/${organizationId}/settings/payments`}
            className="underline"
          >
            subscribe to the Plus plan
          </Link>
        </div>
      </InformationalBanner>
    );
  } else if (!isRbacEnabled) {
    banner = (
      <InformationalBanner>
        <div className="text-sm font-semibold">
          Role based access control is only available to enterprise customers.
          To edit the roles of your team, please{' '}
          <Link
            className="underline"
            to={'https://www.langchain.com/contact-sales'}
            target="_blank"
          >
            contact our sales team{' '}
          </Link>
          to upgrade to enterprise.
        </div>
      </InformationalBanner>
    );
  }

  return (
    <TabGroup setTabMethod="merge">
      <TabList className="relative mx-0 mb-0 pl-0">
        <TabLabel>Members</TabLabel>
        <TabLabel disabled={!isRbacEnabled}>
          Roles
          {!isRbacEnabled && (
            <UpsellBadge
              tooltipMsg={
                'Role based access control is only available for enterprise customers. Contact our sales team for more'
              }
              tier="ENTERPRISE"
              linkProps={{
                to: 'https://www.langchain.com/contact-sales',
                target: '_blank',
              }}
            />
          )}
        </TabLabel>
        {authorize('organization:manage') && !isSelfHosted && (
          <TabLabel disabled={!isSsoEnabled}>
            SSO Configuration
            {!isSsoEnabled && (
              <UpsellBadge
                tooltipMsg={
                  'SSO login is only available for enterprise customers. Contact our sales team for more'
                }
                tier="ENTERPRISE"
                linkProps={{
                  to: 'https://www.langchain.com/contact-sales',
                  target: '_blank',
                }}
              />
            )}
          </TabLabel>
        )}
      </TabList>
      <TabPanels className="flex flex-grow flex-col overflow-hidden overflow-y-auto">
        <TabPanel className="py-4">
          {banner}

          <MembersTableWithTabs type={MemberGroupType.ORGANIZATION} />
        </TabPanel>

        <TabPanel className="self-start py-4">
          {isRbacEnabled && <OrganizationRoleList />}
        </TabPanel>

        {authorize('organization:manage') && isSsoEnabled && (
          <TabPanel className="self-start py-4">
            <OrganizationSSOConfiguration />
          </TabPanel>
        )}
      </TabPanels>
    </TabGroup>
  );
}

function RedirectToMainSettings() {
  const organizationId = useOrganizationId();
  const navigate = useNavigate();
  useEffect(() => {
    navigate(`/${appOrganizationPath}/${organizationId}/${appSettingsPath}`, {
      replace: true,
    });
  }, [navigate, organizationId]);
  return null;
}

export function OrganizationMembers() {
  const tenants = useTenantList();
  const organizationId = useOrganizationId();
  const selectedTenant = tenants.data?.find(
    (tenant) => tenant.id === organizationId
  );

  if (!mixedLoginMethods.basic && selectedTenant?.is_personal)
    return <RedirectToMainSettings />;
  return <OrganizationMembersPage />;
}
