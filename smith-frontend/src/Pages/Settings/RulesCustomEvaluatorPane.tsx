import { python } from '@codemirror/lang-python';
import { Button } from '@mui/joy';

import { useState } from 'react';

import { Code, CodeCard, CodeDataEditor } from '@/components/Code/Code';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { Pane } from '@/components/Pane';
import { TopBarPaneSlot } from '@/components/Pane.utils';
import { useInvokeCustomCode } from '@/hooks/useSwr';
import PythonIcon from '@/icons/PythonIcon.svg?react';
import { ExampleSchema, RunSchema } from '@/types/schema';

import {
  DATASET_EVALUATOR_STARTER_CODE,
  SESSION_EVALUATOR_STARTER_CODE,
} from './utils/constants';

const SAMPLE_RUN = {
  name: 'Sample Run',
  inputs: {
    input: 'value',
  },
  outputs: {
    output: 'value',
  },
  is_root: true,
  status: 'success',
  extra: {
    metadata: {
      key: 'value',
    },
  },
};

const SAMPLE_EXAMPLE = {
  name: 'Sample Example',
  inputs: {
    input: 'value',
  },
  outputs: {
    output: 'value',
  },
  metadata: {
    key: 'value',
  },
};

export const RulesCustomEvaluatorPane = (props: {
  value?: {
    code?: string;
  };
  onChange: (value: { code: string }) => void;
  variant: 'session' | 'dataset';
  open: boolean;
  onClose: (value?: { code: string }) => void;
  saveMutating?: boolean;
  sampleExample?: ExampleSchema;
  sampleRun?: RunSchema;
}) => {
  return (
    <Pane
      title="New Custom Evaluator"
      open={props.open}
      onClose={() => props.onClose()}
      dialogStyle={{
        marginLeft: '80px',
      }}
    >
      <RulesCustomEvaluatorForm
        value={props.value}
        onChange={props.onChange}
        variant={props.variant}
        onSave={props.onClose}
        saveMutating={props.saveMutating}
        sampleExample={props.sampleExample}
        sampleRun={props.sampleRun}
      />
    </Pane>
  );
};

export const RulesCustomEvaluatorForm = (props: {
  value?: {
    code?: string;
  };
  onChange: (value: { code: string }) => void;
  variant: 'session' | 'dataset';
  onSave: (value: { code: string }) => void;
  saveMutating?: boolean;
  sampleRun?: RunSchema;
  sampleExample?: ExampleSchema;
  configureRuleComponent?: React.ReactNode;
}) => {
  const sampleRunFieldsToDisplay = [
    'name',
    'inputs',
    'outputs',
    'extra',
    'is_root',
    'status',
  ];
  const sampleExampleFieldsToDisplay = ['inputs', 'outputs', 'metadata'];
  const [sampleRun, setSampleRun] = useState(
    props.sampleRun
      ? sampleRunFieldsToDisplay.reduce((acc, key) => {
          acc[key] = props.sampleRun?.[key];
          return acc;
        }, {})
      : SAMPLE_RUN
  );

  const [sampleExample, setSampleExample] = useState(
    props.sampleExample
      ? sampleExampleFieldsToDisplay.reduce((acc, key) => {
          acc[key] = props.sampleExample?.[key];
          return acc;
        }, {})
      : SAMPLE_EXAMPLE
  );

  const aceTrigger = useInvokeCustomCode();
  const cleanedTrace = aceTrigger.data?.stacktrace?.replace(
    /File "<exec>", line\b(?=\s)/g,
    'Line'
  );

  return (
    <div className="flex gap-16 pt-3">
      <TopBarPaneSlot.Fill>
        <div className="mr-10 flex items-center gap-2">
          <Button
            loading={props.saveMutating}
            variant="solid"
            size="sm"
            color="primary"
            sx={{ paddingRight: '1rem', paddingLeft: '1rem' }}
            onClick={() =>
              props.onSave(
                props.value?.code
                  ? { code: props.value.code }
                  : {
                      code:
                        props.variant === 'dataset'
                          ? DATASET_EVALUATOR_STARTER_CODE
                          : SESSION_EVALUATOR_STARTER_CODE,
                    }
              )
            }
          >
            Save
          </Button>
        </div>
      </TopBarPaneSlot.Fill>
      <div className="flex w-[50%] flex-col">
        <div className="mb-3 flex h-[40px] items-center text-lg font-semibold">
          Write the code
        </div>
        {props.configureRuleComponent && (
          <>
            <div className="mb-5 flex flex-col gap-2">
              {props.configureRuleComponent}
            </div>
            <div className="pb-2 text-sm font-semibold">Function</div>
          </>
        )}
        <div className="flex gap-4 rounded-lg border border-primary p-4">
          <div className="flex items-center">
            <div className="rounded-lg border border-secondary p-2">
              <PythonIcon className="h-6 w-6 text-quaternary" />
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex text-sm font-semibold">
              Write the code in Python
            </div>
            <div className="text-sm text-disabled">
              All standard Python libraries are supported, along with a few
              external libraries like numpy and jsonschema.{' '}
              <a
                href="https://docs.smith.langchain.com/how_to_guides/evaluation/bind_evaluator_to_dataset#custom-code-evaluators"
                target="_blank"
                rel="noreferrer"
                className="text-tertiary hover:underline focus:underline"
              >
                See docs for full list.
              </a>
            </div>
          </div>
        </div>
        <div className="pt-2">
          <CodeCard>
            <Code
              language={python()}
              value={
                props.value?.code ??
                (props.variant === 'dataset'
                  ? DATASET_EVALUATOR_STARTER_CODE
                  : SESSION_EVALUATOR_STARTER_CODE)
              }
              onChange={(code) => props.onChange({ code })}
            />
          </CodeCard>
        </div>
      </div>

      <div className="flex w-[50%] flex-col">
        <div className="flex items-center justify-between">
          <div className="text-lg font-semibold">Test the code</div>
          <Button
            loading={aceTrigger.isMutating}
            variant="outlined"
            color="neutral"
            onClick={() =>
              aceTrigger.trigger({
                json: {
                  code:
                    props.value?.code ??
                    (props.variant === 'dataset'
                      ? DATASET_EVALUATOR_STARTER_CODE
                      : SESSION_EVALUATOR_STARTER_CODE),
                  args:
                    props.variant === 'dataset'
                      ? [sampleRun, sampleExample]
                      : [sampleRun],
                },
              })
            }
          >
            Test Code
          </Button>
        </div>
        <div className="mt-3 flex flex-col justify-stretch gap-3">
          <div className="flex flex-1 flex-col gap-3">
            <div className="text-sm font-semibold">Input Run</div>
            <CodeCard>
              <CodeDataEditor
                language={'json'}
                maxHeight={props.variant === 'dataset' ? '30vh' : '50vh'}
                value={sampleRun}
                onChange={setSampleRun}
              />
            </CodeCard>
          </div>
          {props.variant === 'dataset' && (
            <div className="flex flex-1 flex-col gap-3">
              <div className="text-sm font-semibold">Input Example</div>
              <CodeCard>
                <CodeDataEditor
                  language={'json'}
                  maxHeight="30vh"
                  value={sampleExample}
                  onChange={setSampleExample}
                />
              </CodeCard>
            </div>
          )}
        </div>

        {aceTrigger.data && !aceTrigger.isMutating && (
          <div className="flex flex-col gap-3">
            <div className="font-semibold">Result</div>
            {aceTrigger.data?.status === 'success' ? (
              <CodeCard>
                <CodeDataEditor
                  readOnly
                  language={'json'}
                  value={aceTrigger.data.result}
                />
              </CodeCard>
            ) : aceTrigger.data?.status === 'failed' ? (
              <ExpandableErrorAlert
                error={cleanedTrace ?? JSON.stringify(aceTrigger.data)}
              />
            ) : aceTrigger.data?.status === 'timeout' ? (
              <ExpandableErrorAlert error="Timed out executing your code" />
            ) : (
              <ExpandableErrorAlert error="Unknown error" />
            )}
          </div>
        )}
      </div>
    </div>
  );
};
