import { DotsHorizontalIcon, PlusIcon } from '@langchain/untitled-ui-icons';
import {
  Button,
  FormControl,
  FormHelperText,
  FormLabel,
  Input,
} from '@mui/joy';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { escapeRegExp } from 'lodash-es';
import {
  ComponentProps,
  forwardRef,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Controller, useForm } from 'react-hook-form';

import { DeleteModal } from '@/components/Delete';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { MultipleArrayInput } from '@/components/MultipleArrayInput';
import { Pane } from '@/components/Pane';
import { TopBarPaneSlot } from '@/components/Pane.utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/Table';
import { Tooltip } from '@/components/Tooltip/Tooltip';
import { usePermissions } from '@/hooks/usePermissions.tsx';
import {
  useModelPriceMap,
  useUpsertModelPriceMapMutation,
} from '@/hooks/useSwr';
import ExternalLinkIcon from '@/icons/ExternalLinkIcon.svg?react';
import { ModelPriceMapUpdateBody } from '@/types/schema';
import { apiModelPriceMapPath } from '@/utils/constants';
import { cn } from '@/utils/tailwind';
import { localTime } from '@/utils/utc-time';

import { CostBreakdownEditor } from './components/CostBreakdownEditor';
import { InputDollar } from './components/InputDollar';
import {
  convertTokensForDefaultValues,
  convertTokensForUpdateBody,
  convertUnitCostToString,
} from './utils/convertTokens';
import { ModelPriceMapFormData } from './utils/types';
import { validatePrecision } from './utils/validatePrecision';

dayjs.extend(utc);

const InputDate = forwardRef<
  HTMLInputElement,
  Omit<ComponentProps<typeof Input>, 'onChange'> & {
    onChange: (value: string) => void;
  }
>((props, ref) => {
  const value =
    typeof props.value === 'string'
      ? dayjs(props.value).format('YYYY-MM-DD')
      : '';

  return (
    <Input
      ref={ref}
      type="date"
      {...props}
      onChange={(e) =>
        props.onChange(dayjs(e.target.value).utc().toISOString())
      }
      value={value}
    />
  );
});

const DEFAULT_MATCH_PATH = [
  'model',
  'model_name',
  'model_id',
  'model_path',
  'endpoint_name',
];

function ModelPriceListCrudForm(props: {
  id?: string;
  defaultValues?: ModelPriceMapUpdateBody;
  onSuccess?: () => void;
  onCancel?: () => void;
}) {
  const mutate = useUpsertModelPriceMapMutation(props.id, {
    onSuccess: (data) => {
      form.reset(data);
      props.onSuccess?.();
    },
  });

  const formRef = useRef<HTMLFormElement>(null);
  const form = useForm<ModelPriceMapFormData>({
    shouldUnregister: true,
    defaultValues: {
      ...(convertTokensForDefaultValues(props.defaultValues) ?? {}),
      match_path: props.defaultValues?.match_path ?? DEFAULT_MATCH_PATH,
    },
  });

  // to simplify onboarding to writing patterns, we should
  // prefill the match pattern with the model name
  const [currName, currPattern, currMatchPath] = form.watch([
    'name',
    'match_pattern',
    'match_path',
  ]);
  const prevValues = useRef<[string, string]>([currName, currPattern]);

  useEffect(() => {
    const [prevName] = prevValues.current;
    const currSuggestedRegex = currName ? `^${escapeRegExp(currName)}$` : '';
    const prevSuggestedRegex = prevName ? `^${escapeRegExp(prevName)}$` : '';
    if (
      prevName !== currName &&
      (currPattern === prevSuggestedRegex || !currPattern)
    ) {
      form.setValue('match_pattern', currSuggestedRegex);
    }

    prevValues.current = [currName, currPattern];
  }, [currName, currPattern, form]);

  const [hideMatchPath] = useState(() => {
    if (!currMatchPath) return true;
    if (currMatchPath.length !== DEFAULT_MATCH_PATH.length) return false;
    return DEFAULT_MATCH_PATH.every((v) => currMatchPath.includes(v));
  });

  return (
    <>
      <form
        ref={formRef}
        onSubmit={form.handleSubmit((json) => {
          // Remove temporary array fields before submission
          const {
            prompt_cost_details_temp_array: _prompt_cost_details_temp_array,
            completion_cost_details_temp_array:
              _completion_cost_details_temp_array,
            ...cleanJson
          } = json;
          mutate.trigger({ json: convertTokensForUpdateBody(cleanJson) });
        })}
        className="flex flex-col gap-6"
      >
        <div className="flex flex-col gap-6">
          <ExpandableErrorAlert error={mutate.error} />

          <FormControl error={!!form.formState.errors.name}>
            <FormLabel>Model Name</FormLabel>
            <Input
              {...form.register('name', {
                required: {
                  value: true,
                  message: 'Model name is required',
                },
              })}
            />
            <FormHelperText className="empty:hidden">
              {form.formState.errors.name?.message}
            </FormHelperText>
          </FormControl>

          <div className="flex flex-col">
            <FormControl error={!!form.formState.errors.prompt_cost}>
              <FormLabel>Prompt Price / 1M Tokens</FormLabel>

              <Controller
                name="prompt_cost"
                control={form.control}
                rules={{
                  required: {
                    value: true,
                    message: 'Prompt price is required',
                  },
                  validate: {
                    validatePrecision,
                  },
                }}
                render={({ field }) => <InputDollar {...field} />}
              />

              <FormHelperText className="empty:hidden">
                {form.formState.errors.prompt_cost?.message}
              </FormHelperText>
            </FormControl>

            <CostBreakdownEditor
              type="Prompt"
              name="prompt_cost_details"
              control={form.control}
              errors={form.formState.errors['prompt_cost_details_temp_array']}
              setValue={form.setValue}
              getValues={form.getValues}
            />
          </div>

          <div className="flex flex-col">
            <FormControl error={!!form.formState.errors.completion_cost}>
              <FormLabel>Completion Price / 1M Tokens</FormLabel>

              <Controller
                name="completion_cost"
                control={form.control}
                rules={{
                  required: {
                    value: true,
                    message: 'Completion price is required',
                  },
                  validate: {
                    validatePrecision,
                  },
                }}
                render={({ field }) => <InputDollar {...field} />}
              />

              <FormHelperText className="empty:hidden">
                {form.formState.errors.completion_cost?.message}
              </FormHelperText>
            </FormControl>

            <CostBreakdownEditor
              type="Completion"
              name="completion_cost_details"
              control={form.control}
              errors={
                form.formState.errors['completion_cost_details_temp_array']
              }
              setValue={form.setValue}
              getValues={form.getValues}
            />
          </div>

          <FormControl>
            <FormLabel>Model Activation Date</FormLabel>
            <Controller
              name="start_time"
              control={form.control}
              render={({ field }) => <InputDate {...field} />}
            />

            <span className="mt-1 text-sm text-tertiary">
              If set, the pricing will be used for traces created from this
              date.
            </span>
          </FormControl>

          <FormControl error={!!form.formState.errors.match_pattern}>
            <FormLabel>Match Pattern</FormLabel>
            <Input
              className="!font-mono"
              {...form.register('match_pattern', {
                required: { value: true, message: 'Match pattern is required' },
                validate: {
                  validRegex: (value) => {
                    try {
                      return !!new RegExp(value);
                    } catch (e) {
                      return 'Invalid regular expression';
                    }
                  },
                },
              })}
            />
            <FormHelperText className="empty:hidden">
              {form.formState.errors.match_pattern?.message}
            </FormHelperText>
            <span className="mt-1 text-sm text-tertiary">
              Regular expression used to match model name found in invocation
              parameters of a trace.
            </span>
          </FormControl>

          <FormControl>
            <FormLabel>Provider</FormLabel>

            <Input {...form.register('provider')} />

            <span className="mt-1 text-sm text-tertiary">
              If provider is present in traced run, apply this pricing map only
              if the provider matches the specified value.
            </span>
          </FormControl>

          <div className={cn(hideMatchPath && 'hidden')}>
            <FormControl>
              <FormLabel>Match Paths</FormLabel>

              <Controller
                name="match_path"
                control={form.control}
                render={({ field }) => (
                  <MultipleArrayInput className="bg-background" {...field} />
                )}
              />

              <span className="mt-1 text-sm text-tertiary">
                Used in older versions of LangChain to find the model name from{' '}
                <span className="rounded border border-secondary bg-secondary px-0.5 py-px font-mono text-xs">
                  extra.invocation_params
                </span>
                . This has been replaced with LangSmith specific metadata keys.{' '}
                <a
                  href="https://docs.smith.langchain.com/how_to_guides/tracing/log_llm_trace"
                  target="_blank"
                  className="text-brand-green-400 hover:underline"
                >
                  Learn more{' '}
                  <ExternalLinkIcon className="-mt-1 inline-flex h-4 w-4" />
                </a>
                .
              </span>
            </FormControl>
          </div>

          {hideMatchPath && (
            <>
              <hr className="border-secondary" />
              <span className="text-sm text-tertiary">
                Model name and provider values are automaticaly added to
                metadata of each run created by LangChain and LangSmith SDK
                wrappers.{' '}
                <a
                  href="https://docs.smith.langchain.com/how_to_guides/tracing/log_llm_trace"
                  target="_blank"
                  className="text-brand-green-400 hover:underline"
                >
                  Learn more how to log custom model values{' '}
                  <ExternalLinkIcon className="-mt-1 inline-flex h-4 w-4" />
                </a>
                .
              </span>
            </>
          )}
        </div>
        <button type="submit" className="hidden">
          Submit
        </button>
      </form>
      <TopBarPaneSlot.Fill>
        <div className="mr-5 flex items-center gap-2">
          <Button
            type="button"
            onClick={props.onCancel}
            variant="outlined"
            color="neutral"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={form.formState.isSubmitting}
            onClick={() => {
              formRef.current?.dispatchEvent(
                new Event('submit', { cancelable: true, bubbles: true })
              );
            }}
          >
            {props.id ? 'Update' : 'Create'}
          </Button>
        </div>
      </TopBarPaneSlot.Fill>
    </>
  );
}

function ModelPriceListCrudPane(props: {
  open: boolean;
  onOpenChange: (open: boolean) => void;

  id?: string;
  defaultValues?: ModelPriceMapUpdateBody;
}) {
  const title = props.id
    ? 'Edit model'
    : props.defaultValues
    ? 'Clone model'
    : 'Add model';

  return (
    <Pane
      title={title}
      open={props.open}
      onClose={() => props.onOpenChange(false)}
      dialogStyle={{
        marginLeft: 'calc(max(4.5rem, 100vw - max(20vw, 600px)))',
      }}
    >
      <ModelPriceListCrudForm
        defaultValues={props.defaultValues}
        id={props.id}
        onSuccess={() => props.onOpenChange(false)}
        onCancel={() => props.onOpenChange(false)}
      />
    </Pane>
  );
}

function ModelPriceListCrud(props: {
  id?: string;
  defaultValues?: ModelPriceMapUpdateBody;
}) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button
        variant={props.id || props.defaultValues ? 'outlined' : 'solid'}
        color={props.id || props.defaultValues ? 'neutral' : 'primary'}
        size="sm"
        onClick={() => setOpen(true)}
      >
        {props.id ? (
          'Edit'
        ) : props.defaultValues ? (
          'Clone'
        ) : (
          <div className="flex items-center gap-2">
            <PlusIcon className="h-4 w-4" />
            Model
          </div>
        )}
      </Button>

      <ModelPriceListCrudPane
        open={open}
        onOpenChange={setOpen}
        id={props.id}
        defaultValues={props.defaultValues}
      />
    </>
  );
}

function ModelPriceListDeleteButton(props: { id: string }) {
  const [deleteOpen, setDeleteOpen] = useState<boolean>(false);
  return (
    <>
      <Button
        variant="outlined"
        color="danger"
        size="sm"
        onClick={() => setDeleteOpen(true)}
      >
        Delete
      </Button>
      <DeleteModal
        endpoint={apiModelPriceMapPath}
        id={props.id}
        isOpen={!!deleteOpen}
        doClose={() => setDeleteOpen(false)}
      />
    </>
  );
}

function ModelPriceList() {
  const priceMap = useModelPriceMap();
  const { authorize } = usePermissions();
  return (
    <div className="rounded-lg border border-secondary">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="pl-5">Model name</TableHead>
            <TableHead>Match rule</TableHead>
            <TableHead>Start date</TableHead>
            <TableHead>Prompt price</TableHead>
            <TableHead>Completion price</TableHead>
            {authorize('runs:create') && (
              <TableHead className="pr-5">Actions</TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {priceMap.data?.map((item) => {
            return (
              <TableRow key={item.id}>
                <TableCell className="pl-5">
                  {item.name}
                  {!item.tenant_id && (
                    <span className="ml-1.5 rounded-md border border-secondary bg-secondary-hover px-1 py-0.5 text-xs font-semibold uppercase text-tertiary">
                      Built-in
                    </span>
                  )}
                </TableCell>

                <TableCell>
                  <span className="rounded-md border border-secondary bg-secondary-hover px-1 py-0.5 font-mono text-sm">
                    {item.match_pattern}
                  </span>
                  {item.provider && (
                    <span className="ml-1.5 rounded-md border border-secondary bg-secondary-hover px-1 py-0.5 text-sm text-tertiary">
                      <span className="uppercase text-tertiary">
                        Provider:{' '}
                      </span>
                      <span className="font-mono text-ls-black">
                        {item.provider}
                      </span>
                    </span>
                  )}
                </TableCell>

                <TableCell>
                  {item.start_time
                    ? localTime(item.start_time).format('YYYY-MM-DD')
                    : null}
                </TableCell>

                <TableCell>
                  <div className="flex items-center gap-2">
                    <div>{convertUnitCostToString(item.prompt_cost)}</div>
                    <CostBreakdownTooltip
                      costBreakdown={item.prompt_cost_details}
                      defaultCost={item.prompt_cost}
                      title="Price Breakdown"
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div>{convertUnitCostToString(item.completion_cost)}</div>
                    <CostBreakdownTooltip
                      costBreakdown={item.completion_cost_details}
                      defaultCost={item.completion_cost}
                      title="Price Breakdown"
                    />
                  </div>
                </TableCell>
                {authorize('runs:create') && (
                  <TableCell className="pr-5">
                    <div className="flex flex-shrink-0 items-center gap-2 whitespace-nowrap">
                      <ModelPriceListCrud
                        id={item.tenant_id != null ? item.id : undefined}
                        defaultValues={item}
                      />

                      {item.tenant_id != null && (
                        <ModelPriceListDeleteButton id={item.id} />
                      )}
                    </div>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
export function OrganizationModels() {
  const { authorize } = usePermissions();

  return (
    <div className="flex flex-col gap-4 overflow-auto">
      <div className="grid grid-cols-[1fr,auto] items-center gap-2">
        <div className="flex flex-grow flex-col gap-1">
          <h2 className="text-2xl font-medium">Models</h2>
          <p className="text-sm text-tertiary">
            Track usage and cost for LLM generations
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="text-xs font-medium text-tertiary">
            Prices shown are per 1M tokens
          </div>
          {authorize('runs:create') && <ModelPriceListCrud />}
        </div>
      </div>
      <ModelPriceList />
    </div>
  );
}

function CostBreakdownTooltip(props: {
  costBreakdown?: Record<string, number>;
  defaultCost: number;
  title: string;
}) {
  const costEntries: [string, number][] = useMemo(() => {
    return [
      ['default', props.defaultCost],
      ...Object.entries(props.costBreakdown ?? {}),
    ];
  }, [props.costBreakdown, props.defaultCost]);
  const content = useMemo(() => {
    return (
      <div className="flex flex-col gap-2">
        <div className="text-xs font-semibold">{props.title}</div>
        <div className="overflow-hidden rounded-md border border-[--gray-700] text-xs">
          <table className="w-full border-collapse divide-y divide-[--gray-600]">
            <thead className="rounded-md bg-[--gray-700] text-xs font-medium text-[--gray-300]">
              <tr>
                <th className="px-4 py-2 text-left">Token Type</th>
                <th className="px-4 py-2 text-left">Price / 1M Tokens</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-[--gray-600]">
              {costEntries.map(([key, value]) => (
                <tr className="text-[--gray-300]" key={key}>
                  <td className="px-4 py-2">{key}</td>
                  <td className="px-4 py-2 text-left">
                    {convertUnitCostToString(value)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }, [costEntries, props.title]);
  return (
    <Tooltip title={content} sxProps={{ borderRadius: '8px' }}>
      <DotsHorizontalIcon className="h-5 w-5 rounded-md bg-tertiary p-1" />
    </Tooltip>
  );
}
