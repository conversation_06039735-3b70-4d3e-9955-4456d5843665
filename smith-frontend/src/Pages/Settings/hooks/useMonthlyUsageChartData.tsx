import { useEffect, useMemo } from 'react';

import {
  billableMetricNameToChartSchema,
  generateUsageCsvData,
} from '@/Pages/Settings/utils/usage';
import { calculateStartAndEndTimeFromTimeModel } from '@/Pages/SingleDashboard/hooks/useChartTimeFilter';
import {
  useOrgUsage,
  useOrganizationId,
  useWorkspaceList,
} from '@/hooks/useSwr';
import { CustomChartPreviewSchema } from '@/types/schema';

export function useMonthlyUsageChartData() {
  const workspaceList = useWorkspaceList();
  const organizationId = useOrganizationId();

  const tenantIdToName = useMemo<Map<string, string>>(() => {
    const result = new Map<string, string>();
    return workspaceList.data
      ? workspaceList.data?.reduce((acc, workspace) => {
          acc.set(workspace.id, workspace.display_name);
          return acc;
        }, result)
      : result;
  }, [workspaceList]);

  const { start_time: startTimestamp, end_time: endTimestamp } = useMemo(
    () => calculateStartAndEndTimeFromTimeModel({ duration: '1y' }),
    []
  );

  const {
    data: orgUsage,
    isLoading,
    error,
  } = useOrgUsage(
    {
      starting_on: startTimestamp,
      ending_before: endTimestamp,
    },
    { revalidateOnFocus: false }
  );

  const billableMetricNameToChart = useMemo<
    Map<string, CustomChartPreviewSchema> | undefined
  >(() => {
    if (orgUsage && organizationId) {
      return billableMetricNameToChartSchema(
        orgUsage,
        organizationId,
        tenantIdToName
      );
    }
  }, [orgUsage, organizationId, tenantIdToName]);

  const sortedBillableMetricNames = useMemo<string[]>(() => {
    if (billableMetricNameToChart) {
      // sort here to make sure charts keeps order on refresh
      const names = Array.from(billableMetricNameToChart.keys());
      names.sort((a, b) => b.localeCompare(a));
      return names;
    }
    return [];
  }, [billableMetricNameToChart]);

  const csvData = useMemo(() => {
    if (organizationId) {
      return generateUsageCsvData(orgUsage, organizationId, tenantIdToName);
    }
    return null;
  }, [orgUsage, organizationId, tenantIdToName]);

  // Clean up the object URL when component unmounts
  useEffect(() => {
    return () => {
      if (csvData?.url) {
        URL.revokeObjectURL(csvData.url);
      }
    };
  }, [csvData?.url]);

  return {
    sortedBillableMetricNames,
    billableMetricNameToChart,
    startTimestamp,
    endTimestamp,
    isLoading,
    error,
    csvData,
  };
}
