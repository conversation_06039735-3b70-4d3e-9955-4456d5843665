import { ChevronLeftIcon } from '@heroicons/react/24/outline';

import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { DisabledTooltip } from '@/components/DisabledTooltip';
import { SecondaryContent } from '@/components/NavSidebar/NavLinks';
import { WorkspacePicker } from '@/components/NavSidebar/WorkspacePicker';
import { useAuth } from '@/hooks/useAuth';
import { useCurrentTier, useOrgRequiresPayment } from '@/hooks/useCurrentTier';
import {
  InstanceFlags,
  useInstanceFlagOrDefault,
} from '@/hooks/useInstanceFlag';
import { useCurrentOrganization, useOrganizationId } from '@/hooks/useSwr';
import { appOrganizationPath, mixedLoginMethods } from '@/utils/constants';
import { isSelfHosted } from '@/utils/is-self-hosted';
import { cn } from '@/utils/tailwind';

import { OrgPicker } from './components/OrgPicker';
import { OrgSettingsTabLink } from './components/OrgSettingsTabLink';
import { UpsellBadge } from './components/UpsellBadge';
import { WorkspacesNavItem } from './components/WorkspacesNavItem';

type Props = {
  className?: string;
};

type SettingsTab = 'workspace' | 'members' | 'usage' | 'payments';

const OrganizationSettingsSideNav = (props: Props) => {
  const auth = useAuth();
  const navigate = useNavigate();
  const organizationId = useOrganizationId();

  const org = useCurrentOrganization();

  const { isEnterprise, isNoPlan } = useCurrentTier();
  const isPersonal = org.data?.is_personal;

  const { requiresPayment } = useOrgRequiresPayment();
  const paymentEnabledFF = useInstanceFlagOrDefault(
    InstanceFlags.payment_enabled
  ) as boolean;
  const disableBackButton = isNoPlan && !isSelfHosted && paymentEnabledFF;

  const canInviteMembers =
    mixedLoginMethods.basic || (!isNoPlan && !isPersonal);

  const tabEligibility: Record<SettingsTab, boolean> = useMemo(
    () => ({
      workspace:
        auth.needsAuth && !requiresPayment && (!isNoPlan || isSelfHosted),
      members: auth.needsAuth,
      apikeys: auth.needsAuth && isPersonal,
      models: !requiresPayment && isPersonal,
      shared: auth.needsAuth && isPersonal,
      secrets: !requiresPayment && isPersonal,
      feedbacks: !requiresPayment && isPersonal,
      rules: !requiresPayment && isPersonal,
      usage: !requiresPayment && isEnterprise,
      payments: paymentEnabledFF && !isSelfHosted && !isEnterprise,
    }),
    [
      auth.needsAuth,
      requiresPayment,
      paymentEnabledFF,
      isEnterprise,
      isPersonal,
      isNoPlan,
      isSelfHosted,
    ]
  );
  return (
    <header
      className={cn(
        'fixed bottom-0 left-0 top-0 flex flex-col items-stretch gap-6 border-r border-r-secondary pb-4 pt-6 transition-all duration-200 ease-in-out',
        'text-xs'
      )}
      style={{
        width: '235px',
        paddingLeft: '0',
        paddingRight: '0',
      }}
      {...props}
    >
      <div
        className={
          'flex flex-1 flex-col justify-between px-[6px] transition-all duration-200 ease-in-out'
        }
      >
        <div className="flex flex-col gap-1">
          <DisabledTooltip
            disabled={disableBackButton}
            disabledMsg={
              <>
                <div>Subscribe to access all features</div>
                <div>or select another organization</div>
              </>
            }
          >
            <button
              disabled={disableBackButton}
              type="button"
              className={cn(
                'flex items-center gap-2 font-semibold',
                'disabled:text-ls-gray-200/75'
              )}
              onClick={() =>
                navigate(`/${appOrganizationPath}/${organizationId}/`)
              }
            >
              <ChevronLeftIcon className="h-4 w-4"></ChevronLeftIcon> Back
            </button>
          </DisabledTooltip>

          <div className="mt-8 flex flex-col whitespace-nowrap">
            {auth.needsAuth && <OrgPicker className="mb-4" />}
            {tabEligibility['workspace'] && <WorkspacesNavItem />}
            {tabEligibility['members'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/members`}
                disabled={!canInviteMembers && paymentEnabledFF}
              >
                {!canInviteMembers && paymentEnabledFF ? (
                  <div className="flex flex-1 items-center justify-between">
                    <div>Members and roles</div>

                    <UpsellBadge
                      tooltipMsg={
                        'Subscribe to Plus to invite members to your team'
                      }
                      onClick={() => {
                        navigate(
                          `/${appOrganizationPath}/${organizationId}/settings/payments`
                        );
                      }}
                      tier="PLUS"
                    />
                  </div>
                ) : (
                  'Members and roles'
                )}
              </OrgSettingsTabLink>
            )}
            {tabEligibility['apikeys'] && (
              <OrgSettingsTabLink
                isIndex
                to={`/${appOrganizationPath}/${organizationId}/settings`}
              >
                API Keys
              </OrgSettingsTabLink>
            )}
            {tabEligibility['models'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/models`}
              >
                Models
              </OrgSettingsTabLink>
            )}
            {tabEligibility['shared'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/shared`}
              >
                Shared
              </OrgSettingsTabLink>
            )}
            {tabEligibility['secrets'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/secrets`}
              >
                Secrets
              </OrgSettingsTabLink>
            )}
            {tabEligibility['feedbacks'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/feedbacks`}
              >
                Feedback tags
              </OrgSettingsTabLink>
            )}
            {tabEligibility['rules'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/rules`}
              >
                Rules
              </OrgSettingsTabLink>
            )}
            {tabEligibility['usage'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/usage`}
              >
                Usage configuration
              </OrgSettingsTabLink>
            )}
            {tabEligibility['payments'] && (
              <OrgSettingsTabLink
                to={`/${appOrganizationPath}/${organizationId}/settings/payments`}
              >
                Usage and billing
              </OrgSettingsTabLink>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-1">
          <SecondaryContent expanded={true} />
          <WorkspacePicker expanded={true} />
        </div>
      </div>
    </header>
  );
};

export default OrganizationSettingsSideNav;
