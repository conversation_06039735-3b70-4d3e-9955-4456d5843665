import { Trash04Icon } from '@langchain/untitled-ui-icons';
import {
  Button,
  FormControl,
  FormHelperText,
  IconButton,
  Input,
} from '@mui/joy';

import { useEffect } from 'react';
import {
  Control,
  Controller,
  FieldError,
  FieldErrorsImpl,
  Merge,
  UseFormGetValues,
  UseFormSetValue,
  useFieldArray,
} from 'react-hook-form';

import { ModelPriceMapFormData } from '../utils/types';
import { validatePrecision } from '../utils/validatePrecision';
import { InputDollar } from './InputDollar';

// Component to handle model price breakdown editing with individual form controls
export function CostBreakdownEditor(props: {
  type: 'Prompt' | 'Completion';
  name: 'prompt_cost_details' | 'completion_cost_details';
  control: Control<ModelPriceMapFormData, any, ModelPriceMapFormData>;
  errors?:
    | Merge<
        FieldError,
        (
          | Merge<FieldError, FieldErrorsImpl<{ key: string; value: number }>>
          | undefined
        )[]
      >
    | undefined;
  setValue: UseFormSetValue<ModelPriceMapFormData>;
  getValues: UseFormGetValues<ModelPriceMapFormData>;
}) {
  // Use a different field name for the array to avoid conflicts
  const arrayFieldName = `${props.name}_temp_array` as const;

  const { fields, append, remove } = useFieldArray({
    control: props.control,
    name: arrayFieldName,
  });

  // On mount, initialize array from object value
  const objectValue = props.getValues(props.name);
  useEffect(() => {
    if (objectValue && fields.length === 0) {
      append(
        Object.entries(objectValue).map(([key, value]) => ({ key, value }))
      );

      setTimeout(syncToObject, 0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Convert array back to object and update the main field
  const syncToObject = () => {
    const currentArrayValues = props.getValues(arrayFieldName) || [];
    const obj: Record<string, number> = {};

    currentArrayValues.forEach((item: any) => {
      if (item?.key && item?.value != null) {
        obj[item.key] = Number(item.value) || 0;
      }
    });

    const hasValidEntries = Object.keys(obj).length > 0;
    props.setValue(props.name, hasValidEntries ? obj : undefined);
  };

  if (fields.length === 0) {
    return (
      <div className="mt-2 self-start">
        <Button
          variant="plain"
          color="neutral"
          size="sm"
          onClick={() => append({ key: '', value: 0 })}
          className="mt-2"
        >
          + Add Breakdown
        </Button>
      </div>
    );
  }

  return (
    <div className="mt-4 flex flex-col gap-2 rounded-md">
      <div className="text-sm font-medium">{props.type} Price Breakdown</div>
      <div className="max-w-full overflow-hidden rounded-md border border-secondary text-xs">
        <table className="w-full table-fixed border-collapse divide-y divide-secondary">
          <thead className="rounded-md bg-secondary-hover text-xs font-medium text-tertiary">
            <tr>
              <th className="py-2 pl-3 text-left">Token Type</th>
              <th className="py-2 pl-3 text-left">Price / 1M Tokens</th>
              <th className="w-[60px] px-4 py-2"></th>
            </tr>
          </thead>
          <tbody className="divide-y divide-secondary">
            {fields.map((field, index) => (
              <CostBreakdownRow
                key={field.id}
                index={index}
                errors={props.errors?.[index]}
                arrayFieldName={arrayFieldName}
                control={props.control}
                onRemove={() => {
                  remove(index);
                  // Sync after removal
                  setTimeout(syncToObject, 0);
                }}
                onValueChange={syncToObject}
              />
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between gap-2">
        <Button
          variant="outlined"
          color="neutral"
          size="sm"
          onClick={() => append({ key: '', value: 0 })}
        >
          + Token Type
        </Button>
        <Button
          variant="plain"
          size="sm"
          onClick={() => {
            // Remove all fields
            for (let i = fields.length - 1; i >= 0; i--) {
              remove(i);
            }
            // Clear the main field
            props.setValue(props.name, undefined);
          }}
        >
          <div className="flex items-center gap-2">
            <Trash04Icon className="h-4 w-4" />
            <span className="text-xs font-medium text-tertiary">
              Delete breakdown
            </span>
          </div>
        </Button>
      </div>
    </div>
  );
}

// Row component with individual form controls
function CostBreakdownRow(props: {
  index: number;
  errors?:
    | Merge<
        FieldError,
        (
          | Merge<FieldError, FieldErrorsImpl<{ key: string; value: number }>>
          | undefined
        )[]
      >
    | undefined;
  arrayFieldName:
    | `completion_cost_details_temp_array`
    | `prompt_cost_details_temp_array`;
  control: Control<ModelPriceMapFormData, any, ModelPriceMapFormData>;
  onRemove: () => void;
  onValueChange: () => void;
}) {
  const keyFieldName = `${props.arrayFieldName}.${props.index}.key` as const;
  const valueFieldName =
    `${props.arrayFieldName}.${props.index}.value` as const;

  return (
    <tr className="bg-primary">
      <td className="min-w-0 py-2 pl-3 align-top">
        <FormControl size="sm" error={!!props.errors?.['key']}>
          <Controller
            name={keyFieldName}
            control={props.control}
            rules={{
              required: 'Token type is required',
            }}
            render={({ field }) => (
              <Input
                {...field}
                size="sm"
                placeholder="text"
                sx={{ minWidth: '0px' }}
                className="w-full min-w-0 text-xs"
                onChange={(e) => {
                  field.onChange(e);
                  props.onValueChange();
                }}
              />
            )}
          />
          <FormHelperText className="empty:hidden">
            {props.errors?.['key']?.message}
          </FormHelperText>
        </FormControl>
      </td>
      <td className="min-w-0 py-2 pl-3 align-top">
        <FormControl size="sm" error={!!props.errors?.['value']}>
          <Controller
            name={valueFieldName}
            control={props.control}
            rules={{
              required: 'Price is required',
              validate: validatePrecision,
            }}
            render={({ field }) => (
              <InputDollar
                {...field}
                size="sm"
                sx={{ minWidth: '0px' }}
                className="w-full min-w-0 text-xs"
                onChange={(e) => {
                  field.onChange(e);
                  props.onValueChange();
                }}
              />
            )}
          />
          <FormHelperText className="empty:hidden">
            {props.errors?.['value']?.message}
          </FormHelperText>
        </FormControl>
      </td>
      <td className="w-[60px] px-3 py-2 text-center">
        <IconButton
          variant="plain"
          size="sm"
          onClick={props.onRemove}
          className="p-1"
        >
          <Trash04Icon className="h-4 w-4" />
        </IconButton>
      </td>
    </tr>
  );
}
