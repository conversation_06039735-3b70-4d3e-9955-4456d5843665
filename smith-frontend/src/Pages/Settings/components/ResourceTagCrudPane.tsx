import {
  Combobox,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from '@headlessui/react';
import {
  PlusIcon,
  SearchLgIcon,
  Trash04Icon,
  XIcon,
} from '@langchain/untitled-ui-icons';
import {
  Button,
  Checkbox,
  CircularProgress,
  FormControl,
  FormHelperText,
  FormLabel,
  IconButton,
  Input,
  LinearProgress,
  Tooltip,
} from '@mui/joy';

import { forwardRef, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';

import { DeleteConfirmationButton } from '@/components/Delete';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { Pane } from '@/components/Pane';
import { SingleResourceTag } from '@/components/ResourceTagFilter/SingleResourceTag';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';
import useToast from '@/components/Toast';
import { usePermissions } from '@/hooks/usePermissions';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import {
  useDeleteTagKey,
  useDeleteTagValue,
  useListTagKeys,
  useWorkspaceId,
} from '@/hooks/useSwr';
import CheckIcon from '@/icons/CheckIcon.svg?react';
import {
  ResourceTag,
  ResourceTagKey,
  ResourceType,
  Tagging,
  TaggingsByResourceType,
} from '@/types/schema';
import { getScrollParent } from '@/utils/get-closest-scroll-parent';
import { lowerCaseIncludes } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import { ICON_MAP } from '../utils/ResourceIconMap';
import { DEFAULT_RESOURCES } from '../utils/constants';
import {
  getResourceLink,
  isResourceTag,
  useCreateTagMutation,
  useListGroupedTags,
  useUpdateTagMutation,
} from '../utils/resourceTag.utils';
import { useGetResources } from '../utils/useGetResource';

type AssignResourcesProps = {
  resources?: Tagging[];
  setResources: (resources: Tagging[]) => void;
  resourceType: ResourceType;
};

const ResourceTypeList = Object.values(ResourceType).filter(
  (resourceType) => resourceType !== ResourceType.Experiment
);

export function TaggedResource({
  resourceType,
  name,
  resourceId,
  onRemove,
  isLink,
}: {
  resourceType: ResourceType;
  name: string;
  resourceId: string;
  onRemove?: () => void;
  isLink?: boolean;
}) {
  const workspaceId = useWorkspaceId();
  const tag = (
    <div className="flex min-w-[120px] flex-shrink items-center justify-between gap-1 rounded-md border border-secondary px-2 py-1">
      <div className="flex min-w-0 flex-1 items-center gap-2">
        {ICON_MAP('h-4 w-4 flex-none')[resourceType]}
        <TextOverflowTooltip>{name}</TextOverflowTooltip>
      </div>
      {onRemove && (
        <button type="button" className="flex-shrink-0" onClick={onRemove}>
          <XIcon className="h-4 w-4" />
        </button>
      )}
    </div>
  );
  return isLink ? (
    <Link
      to={getResourceLink(
        resourceType,
        resourceType === ResourceType.Prompt ? name : resourceId,
        workspaceId
      )}
      target="_blank"
      className="cursor-pointer"
    >
      {tag}
    </Link>
  ) : (
    tag
  );
}

function AssignResources({
  resources: selectedResources,
  resourceType,
  setResources,
}: AssignResourcesProps) {
  const {
    resources: foundResources,
    name,
    setName,
    isLoading,
  } = useGetResources(resourceType);

  const componentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        componentRef.current &&
        !componentRef.current.contains(event.target as Node)
      ) {
        setName('');
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setName]);

  return (
    <div className="relative w-full pb-2" ref={componentRef}>
      <div className="flex max-h-[300px] w-full items-center gap-2 overflow-y-auto rounded-lg border border-secondary px-3 py-1.5 focus-within:outline focus-within:-outline-offset-2 focus-within:outline-brand-green-500">
        <SearchLgIcon className="text-text-tertiary size-5 flex-none" />
        <input
          data-testid={`${resourceType}-search-input`}
          placeholder={`Search...`}
          value={name}
          className="flex-1 border-none bg-transparent text-sm outline-none"
          onChange={(e) => setName(e.target.value)}
        />
      </div>
      <div className="mt-2 flex gap-2">
        {selectedResources && selectedResources.length > 0 && (
          <div className="flex min-w-0 flex-1 gap-1 overflow-hidden">
            {selectedResources.slice(0, 2).map(
              (resource, idx) =>
                resource && (
                  <TaggedResource
                    key={resource.resource_id}
                    resourceId={resource.resource_id}
                    resourceType={resourceType}
                    name={resource.resource_name}
                    onRemove={() => {
                      setResources(
                        (selectedResources ?? []).filter((_, i) => i !== idx)
                      );
                    }}
                  />
                )
            )}
            {selectedResources.length > 2 && (
              <Tooltip
                title={selectedResources.slice(2).map((r) => (
                  <div className="py-1">{r.resource_name}</div>
                ))}
              >
                <div className="flex-none rounded-md border border-secondary px-2 py-1 text-sm">
                  +{selectedResources.length - 2}
                </div>
              </Tooltip>
            )}
          </div>
        )}
      </div>
      {name && (
        <div className="absolute left-0 right-0 z-10 mt-2 rounded-md border border-primary bg-background">
          {isLoading || !foundResources ? (
            <LinearProgress />
          ) : (
            foundResources?.map((resource) => {
              return (
                <button
                  type="button"
                  className="flex h-10 w-full cursor-pointer select-none items-center gap-3 rounded-sm px-2 py-1.5 outline-none hover:bg-secondary focus:bg-secondary data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  key={resource.id}
                  onClick={() => {
                    if (
                      selectedResources?.find(
                        (r) => r.resource_id === resource.id
                      )
                    ) {
                      setResources(
                        selectedResources.filter(
                          (r) => r.resource_id !== resource.id
                        )
                      );
                    } else {
                      setResources([
                        ...(selectedResources ?? []),
                        {
                          resource_id: resource.id,
                          resource_name:
                            resource.name ??
                            resource.repo_handle ??
                            resource.title,
                        },
                      ]);
                    }
                  }}
                >
                  {ICON_MAP('h-6 w-6')[resourceType]}
                  <div className="truncate">
                    {resource.name ?? resource.repo_handle ?? resource.title}
                  </div>
                  {selectedResources?.find(
                    (r) => r.resource_id === resource.id
                  ) && (
                    <div className="flex-1 text-right text-sm text-tertiary">
                      <CheckIcon className="inline h-4 w-4" />
                    </div>
                  )}
                </button>
              );
            })
          )}

          {foundResources?.length === 0 && (
            <div className="ml-3 text-sm text-tertiary">
              No {resourceType}s found
            </div>
          )}
        </div>
      )}
    </div>
  );
}

type EditKeyValueFormProps = {
  error?: Error | null;
  onSubmit: (data: {
    key?: string;
    keyId?: string;
    value: string;
    description: string;
    resources: TaggingsByResourceType;
  }) => void;
  onCancel: () => void;
  onDelete: () => void;
  resourceTag?:
    | (ResourceTag & { description?: string })
    | { tag_key: string; tag_key_id: string };
  isMutating?: boolean;
  isDeleting?: boolean;
};

function KeySelect({
  selectedKey,
  setSelectedKey,
  newKey,
  setNewKey,
  disabled,
  keyError,
  setKeyError,
}: {
  selectedKey?: { key: string; id: string } | null;
  setSelectedKey: (key: { key: string; id: string } | null) => void;
  newKey: string;
  setNewKey: (key: string) => void;
  disabled?: boolean;
  keyError?: string;
  setKeyError: (keyError: string) => void;
}) {
  const { data: tagKeys } = useListTagKeys();

  const filtered =
    tagKeys?.filter((tagKey) => lowerCaseIncludes(tagKey.key, newKey)) || [];

  return (
    <>
      <Combobox
        value={selectedKey}
        onChange={(v: ResourceTagKey | null) => {
          if (v === null) {
            setSelectedKey(null);
          } else {
            setSelectedKey(v);
            setNewKey('');
          }
        }}
        disabled={disabled}
      >
        <div className="relative">
          <ComboboxInput
            className={cn(
              'flex w-full gap-2 rounded-lg border border-secondary px-3 py-[5px] focus-within:outline focus-within:-outline-offset-2 focus-within:outline-brand-green-400 disabled:cursor-not-allowed disabled:bg-secondary disabled:opacity-50',
              keyError ? 'border-error focus:border-error-strong' : '',
              'bg-[var(--joy-palette-background-surface)] text-sm'
            )}
            onChange={(e) => {
              setNewKey(e.target.value);
              setKeyError('');
            }}
            placeholder="Search or create a new key..."
            displayValue={(v: ResourceTagKey | null) => {
              if (v) {
                return v.key;
              }
              return newKey;
            }}
          />

          <ComboboxOptions className="absolute z-[var(--joy-zIndex-modal)] mt-2 w-[220px] min-w-[8rem] overflow-hidden rounded-md border border-secondary bg-popover shadow-lg">
            {filtered.map((tagKey) => (
              <ComboboxOption
                className="relative flex w-full cursor-pointer select-none items-center rounded-sm px-4 py-2.5 text-sm outline-none hover:bg-secondary focus:bg-secondary data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                key={tagKey.id}
                value={tagKey}
              >
                <TextOverflowTooltip>{tagKey.key}</TextOverflowTooltip>
              </ComboboxOption>
            ))}
            {newKey && (
              <ComboboxOption
                className="relative flex w-full cursor-pointer select-none items-center rounded-sm px-4 py-2.5 text-sm outline-none hover:bg-secondary focus:bg-secondary"
                value={null}
              >
                <PlusIcon className="mr-2 h-4 w-4" /> Create "{newKey}"
              </ComboboxOption>
            )}
          </ComboboxOptions>
        </div>
      </Combobox>
      {keyError && (
        <div className="ml-1 mt-1 text-sm text-red-600">{keyError}</div>
      )}
    </>
  );
}

const EditResourceTagForm = forwardRef<HTMLFormElement, EditKeyValueFormProps>(
  (
    {
      onSubmit,
      onCancel,
      onDelete,
      resourceTag,
      isMutating,
      isDeleting,
      error,
    },
    ref
  ) => {
    const { register, handleSubmit, formState, setValue } = useForm<{
      value: string;
      description: string;
      key_description: string;
    }>();

    // null and undefined states here are used to determine if the user has
    // selected to create a new key or not. When this is null, the user has
    // selected to create a new key. When this is undefined, the user has not picked
    // between creating a new key or selecting an existing one.
    const [selectedKey, setSelectedKey] = useState<
      | {
          key: string;
          id: string;
        }
      | null
      | undefined
    >(undefined);
    const [newKey, setNewKey] = useState<string>('');
    const [keyError, setKeyError] = useState<string>('');
    const [selectedResources, setSelectedResources] =
      useState<TaggingsByResourceType>(DEFAULT_RESOURCES);

    const handleSetResourceTaggings = (
      resourceType: ResourceType,
      resources: Tagging[]
    ) => {
      setSelectedResources((prevSelectedResources) => ({
        ...prevSelectedResources,
        [`${resourceType.toLowerCase()}s`]: resources,
      }));
    };

    useEffect(() => {
      if (isResourceTag(resourceTag)) {
        if (resourceTag?.tag_value !== undefined) {
          setValue('value', resourceTag.tag_value);
        }
        if (resourceTag?.description !== undefined) {
          setValue('description', resourceTag.description ?? '');
        }
        if (resourceTag?.resources) {
          setSelectedResources(resourceTag.resources);
        }
      }
      if (resourceTag?.tag_key) {
        setSelectedKey({
          key: resourceTag.tag_key,
          id: resourceTag.tag_key_id,
        });
      }
    }, [resourceTag, setValue]);

    const isNewTag = resourceTag?.tag_key_id === '';

    const disableSave = !selectedKey?.id && !newKey;
    return (
      <form
        ref={ref}
        className="flex w-full flex-col items-stretch gap-4"
        onSubmit={handleSubmit((data) => {
          if (!selectedKey?.id && !newKey) {
            setKeyError('Key is required');
            return;
          }
          const keyObj = selectedKey?.id
            ? {
                keyId: selectedKey.id,
              }
            : {
                key: newKey,
                key_description: data.key_description,
              };

          onSubmit({ ...data, resources: selectedResources, ...keyObj });
        })}
      >
        <div className="flex gap-2">
          <FormControl style={{ flex: 1 }}>
            <FormLabel>Resource tag</FormLabel>

            <KeySelect
              selectedKey={selectedKey}
              setSelectedKey={setSelectedKey}
              newKey={newKey}
              setNewKey={setNewKey}
              disabled={!!resourceTag?.tag_key}
              keyError={keyError}
              setKeyError={setKeyError}
            />
          </FormControl>

          <FormControl
            error={!!formState.errors.value?.message}
            style={{ flex: 1 }}
          >
            <FormLabel>&nbsp;</FormLabel>
            <Input
              size="sm"
              placeholder="Value..."
              {...register('value', { required: 'Value is required' })}
            />
            {formState.errors.value?.message && (
              <FormHelperText>{formState.errors.value?.message}</FormHelperText>
            )}
          </FormControl>
        </div>
        <FormControl error={!!formState.errors.description?.message}>
          <FormLabel>Description</FormLabel>
          <Input
            size="sm"
            placeholder="Description..."
            {...register('description')}
          />
          {formState.errors.description?.message && (
            <FormHelperText>
              {formState.errors.description?.message}
            </FormHelperText>
          )}
        </FormControl>
        <div className="flex w-full flex-col gap-4">
          <h4 className="mb-2 text-base">Assign resources</h4>
          {ResourceTypeList.map((resourceType) => {
            const resourceTypeTaggings: Tagging[] =
              selectedResources[`${resourceType.toLowerCase()}s`] ?? [];

            return (
              <div className="flex flex-col gap-2">
                <div className="flex-1 text-left text-sm capitalize">{`${resourceType
                  .split('_')
                  .join(' ')}s`}</div>

                <AssignResources
                  resources={resourceTypeTaggings}
                  resourceType={resourceType}
                  setResources={(resources) =>
                    handleSetResourceTaggings(resourceType, resources)
                  }
                />
              </div>
            );
          })}
        </div>
        {error && <ExpandableErrorAlert error={error} />}
        <div
          className={cn('flex justify-between', isNewTag ? 'justify-end' : '')}
        >
          {!isNewTag && (
            <DeleteConfirmationButton
              onDelete={onDelete}
              title="Delete resource tag"
              description="Are you sure you want to delete this resource tag?"
              deleteButtonText="Delete"
              isDeleting={false}
              disabled={isMutating}
              trigger={
                <IconButton size="sm" color="danger" variant="outlined">
                  {isDeleting ? (
                    <CircularProgress size="sm" />
                  ) : (
                    <Trash04Icon className="size-5" />
                  )}
                </IconButton>
              }
            />
          )}
          <div className="flex gap-2">
            <Button
              type="button"
              disabled={isMutating}
              size="sm"
              color="neutral"
              variant="outlined"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={isMutating}
              size="sm"
              color="primary"
              variant="outlined"
              disabled={disableSave}
            >
              Save
            </Button>
          </div>
        </div>
      </form>
    );
  }
);

function ResourcesCountSetup(props: {
  onClick?: () => void;
  resourceCount: number;
  isTagValueSetup: boolean;
  canManageTags: boolean;
}) {
  return (
    <Tooltip
      title={props.canManageTags ? '' : 'Workspace manage permission required'}
    >
      <button
        type="button"
        className={cn(
          'rounded-md border border-secondary px-2 py-1 text-sm text-secondary hover:bg-secondary',
          !props.isTagValueSetup || props.resourceCount === 0
            ? 'rounded-full'
            : '',
          props.canManageTags
            ? 'cursor-pointer'
            : 'cursor-not-allowed border-tertiary text-tertiary'
        )}
        disabled={!props.canManageTags}
        onClick={props.onClick}
      >
        {!props.isTagValueSetup ? (
          <>Set up</>
        ) : props.resourceCount === 0 ? (
          'Assign resources'
        ) : (
          `${props.resourceCount} resource${props.resourceCount > 1 ? 's' : ''}`
        )}
      </button>
    </Tooltip>
  );
}

function ResourceTagList(props: {
  headerOffset?: number;
  resourceTag?:
    | (ResourceTag & { description?: string })
    | { tag_key: string; tag_key_id: string };
  onSelect: (
    tag: ResourceTag | { tag_key: string; tag_key_id: string } | undefined
  ) => void;
}) {
  const { selectedTagIds, setSelectedTags } = useStoredResourceTags();

  const { data, isLoading } = useListGroupedTags();
  const { resourceTag, onSelect } = props;

  const { createToast } = useToast();

  const { authorize } = usePermissions('workspace');
  const canManageTags = authorize('workspaces:manage');

  const createTagMutation = useCreateTagMutation();
  const updateTagMutation = useUpdateTagMutation(
    isResourceTag(resourceTag) ? resourceTag : undefined
  );

  const deleteTagMutation = useDeleteTagValue();
  const deleteTagKeyMutation = useDeleteTagKey();

  const editFormRef = useRef<HTMLFormElement>(null);

  const itemClassNames =
    'flex items-center justify-between text-sm px-3 py-4 rounded-md w-full border border-secondary';

  // scroll current editing into view.
  useEffect(() => {
    if (editFormRef.current && props.headerOffset) {
      const scrollParent = getScrollParent(editFormRef.current);
      if (scrollParent) {
        const headerHeight = props.headerOffset;
        const yOffset = -headerHeight - 20; // Account for both header and own padding top

        const y =
          editFormRef.current.getBoundingClientRect().top +
          window.scrollY +
          yOffset;

        scrollParent.scrollTo({
          behavior: 'smooth',
          top: y,
        });
      }
    }
  }, [resourceTag, props.headerOffset]);

  const editForm = (
    <EditResourceTagForm
      key={resourceTag?.tag_key_id}
      ref={editFormRef}
      error={createTagMutation.error || updateTagMutation.error}
      resourceTag={resourceTag}
      onSubmit={async (data) => {
        if (isResourceTag(resourceTag)) {
          await updateTagMutation.trigger(data);
        } else {
          await createTagMutation.trigger(data);
        }
        createToast({
          title: `Resource tag ${resourceTag ? 'updated' : 'created'}`,
        });
        onSelect(undefined);
      }}
      onCancel={() => onSelect(undefined)}
      onDelete={async () => {
        if (isResourceTag(resourceTag)) {
          await deleteTagMutation.trigger({
            templateUrlParams: {
              tagKeyId: resourceTag.tag_key_id,
              tagValueId: resourceTag.tag_value_id,
            },
          });
          createToast({
            title: `Deleted resource tag ${resourceTag?.tag_value}`,
          });
        } else if (resourceTag?.tag_key_id) {
          await deleteTagKeyMutation.trigger({
            templateUrlParams: {
              tagKeyId: resourceTag.tag_key_id,
            },
          });
          createToast({
            title: `Deleted resource tag ${resourceTag?.tag_key}`,
          });
        }

        onSelect(undefined);
      }}
      isMutating={createTagMutation.isMutating || updateTagMutation.isMutating}
      isDeleting={deleteTagMutation.isMutating}
    />
  );

  // for when creating a new key with the + create tag button
  const isNewKey =
    resourceTag?.tag_key === '' && resourceTag?.tag_key_id === '';

  // for when creating a new value on an existing key
  // not editing an existing tag.
  const isNewValueOnExistingKey = Boolean(
    Object.values(data)?.find(
      (k) =>
        k.id === resourceTag?.tag_key_id &&
        !isResourceTag(resourceTag) &&
        k.taggings.length > 0
    )
  );

  if (isLoading) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }
  return (
    <>
      <div className="flex justify-between">
        <h3 className="font-semibold">Resource tags</h3>
        <Tooltip
          title={canManageTags ? '' : 'Workspace manage permission required'}
        >
          <button
            type="button"
            disabled={!canManageTags}
            className="flex cursor-pointer items-center gap-2 border-b border-primary disabled:cursor-not-allowed disabled:text-tertiary"
            onClick={() => onSelect({ tag_key: '', tag_key_id: '' })}
          >
            <PlusIcon className="h-4 w-4" />
            <span>Create tag</span>
          </button>
        </Tooltip>
      </div>

      {/* creating new tag */}
      {(isNewKey || isNewValueOnExistingKey) && canManageTags ? (
        <div className={itemClassNames}>{editForm}</div>
      ) : null}
      {data && (
        <div className="flex flex-col gap-3">
          {Object.values(data)?.map(({ taggings, key, id }) => {
            if (taggings.length === 0) {
              return (
                <div className={itemClassNames} key={id}>
                  {resourceTag && resourceTag.tag_key_id === id ? (
                    editForm
                  ) : (
                    <>
                      <div className="flex items-center gap-3">
                        <Checkbox disabled={true} />
                        <SingleResourceTag
                          tag_key={key}
                          tag_value={'0 values'}
                          gap={2}
                          maxWidth={250}
                        />
                      </div>
                      <ResourcesCountSetup
                        resourceCount={0}
                        isTagValueSetup={taggings.length > 0}
                        onClick={() =>
                          onSelect({ tag_key: key, tag_key_id: id })
                        }
                        canManageTags={canManageTags}
                      />
                    </>
                  )}
                </div>
              );
            }
            return taggings.map((tag) => {
              const resourceCount = tag.resources
                ? Object.entries(tag.resources).reduce(
                    (total, [resourceType, resources]) => {
                      if (resourceType === `${ResourceType.Experiment}s`)
                        return total;
                      return total + resources.length;
                    },
                    0
                  )
                : 0;

              return (
                <div key={tag.tag_value_id} className={itemClassNames}>
                  {isResourceTag(resourceTag) &&
                  resourceTag.tag_key_id === id &&
                  resourceTag.tag_value_id === tag.tag_value_id ? (
                    editForm
                  ) : (
                    <>
                      <div className="flex items-center gap-3">
                        <Checkbox
                          data-testid={`${tag.tag_key}-${tag.tag_value}-select`}
                          disabled={resourceCount === 0}
                          checked={selectedTagIds.includes(tag.tag_value_id)}
                          onChange={(e) => {
                            setSelectedTags((prev) => {
                              if (e.target.checked) {
                                return [...prev, tag];
                              }
                              return prev.filter(
                                (t) => t.tag_value_id !== tag.tag_value_id
                              );
                            });
                          }}
                        />
                        <SingleResourceTag
                          tag_key={key}
                          tag_value={tag.tag_value}
                          gap={2}
                          maxWidth={250}
                        />
                      </div>
                      <ResourcesCountSetup
                        resourceCount={resourceCount}
                        isTagValueSetup={true}
                        onClick={() => onSelect(tag)}
                        canManageTags={canManageTags}
                      />
                    </>
                  )}
                </div>
              );
            });
          })}
        </div>
      )}
    </>
  );
}

type EditKeyValueDrawerProps = {
  resourceTag?:
    | (ResourceTag & { description?: string })
    | { tag_key: string; tag_key_id: string };
  isOpen: boolean;
  setOpen: (open: boolean) => void;
};

export function ResourceTagCrudPane({
  resourceTag: focusedTagKey,
  setOpen,
  isOpen,
}: EditKeyValueDrawerProps) {
  const [resourceTag, setResourceTag] = useState<
    | (ResourceTag & { description?: string })
    | { tag_key: string; tag_key_id: string }
    | undefined
  >(focusedTagKey);

  useEffect(() => {
    setResourceTag(focusedTagKey);
  }, [focusedTagKey]);

  const doClose = () => {
    setResourceTag(undefined);
    setOpen(false);
  };

  const headerRef = useRef<HTMLDivElement>(null);

  return (
    <Pane
      open={isOpen}
      noBackArrow={true}
      onClose={doClose}
      className="p-0"
      dialogStyle={{
        marginLeft: 'calc(max(4.5rem, 100vw - max(20vw, 520px)))',
      }}
      titleClassName="min-h-[120px]"
      title={
        <div className="flex flex-col gap-0.5" ref={headerRef}>
          <h3 className="text-xl font-medium text-secondary">Tag resources</h3>
          <p className="text-sm font-normal text-quaternary">
            Organize your resources with Application, Environment, or custom
            tags to easily find and manage your projects, datasets, prompts and
            deployments.
          </p>
        </div>
      }
      topBarRightElement={
        <button
          type="button"
          className="mr-6 self-start rounded-md p-1 hover:bg-secondary-hover active:bg-secondary"
          aria-label="Close"
          onClick={doClose}
        >
          <XIcon className="h-6 w-6 text-ls-black" />
        </button>
      }
    >
      <div className="px-6 py-4">
        <div className="mx-auto flex w-full flex-col gap-3">
          <ResourceTagList
            onSelect={setResourceTag}
            resourceTag={resourceTag}
            headerOffset={headerRef.current?.parentElement?.offsetHeight}
          />
        </div>
      </div>
    </Pane>
  );
}
