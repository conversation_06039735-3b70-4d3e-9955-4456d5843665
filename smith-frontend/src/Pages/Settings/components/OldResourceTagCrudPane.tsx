import {
  Combobox,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from '@headlessui/react';
import {
  ChevronRightIcon,
  PlusIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import {
  Button,
  FormControl,
  FormHelperText,
  FormLabel,
  Input,
  LinearProgress,
} from '@mui/joy';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@radix-ui/react-accordion';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';

import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { Pane } from '@/components/Pane';
import useToast from '@/components/Toast';
import { useListTagKeys, useWorkspaceId } from '@/hooks/useSwr';
import CheckIcon from '@/icons/CheckIcon.svg?react';
import SearchIcon from '@/icons/SearchIcon.svg?react';
import {
  ResourceTag,
  ResourceTagKey,
  ResourceType,
  Tagging,
  TaggingsByResourceType,
} from '@/types/schema';
import { lowerCaseIncludes } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import { ICON_MAP } from '../utils/ResourceIconMap';
import { DEFAULT_RESOURCES } from '../utils/constants';
import {
  getResourceLink,
  isResourceTag,
  useCreateTagMutation,
  useUpdateTagMutation,
} from '../utils/resourceTag.utils';
import { useGetResources } from '../utils/useGetResource';

type AssignResourcesProps = {
  resources?: Tagging[];
  setResources: (resources: Tagging[]) => void;
  resourceType: ResourceType;
};

const ResourceTypeList = Object.values(ResourceType).filter(
  (resourceType) => resourceType !== ResourceType.Experiment
);

export function TaggedResource({
  resourceType,
  name,
  resourceId,
  onRemove,
  isLink,
}: {
  resourceType: ResourceType;
  name: string;
  resourceId: string;
  onRemove?: () => void;
  isLink?: boolean;
}) {
  const workspaceId = useWorkspaceId();
  const tag = (
    <div className="flex items-center gap-1 rounded-md border border-secondary px-2 py-1">
      {ICON_MAP('h-4 w-4')[resourceType]}
      {name}
      {onRemove && (
        <button type="button" className="flex-shrink-0" onClick={onRemove}>
          <XMarkIcon className="h-4 w-4" />
        </button>
      )}
    </div>
  );
  return isLink ? (
    <Link
      to={getResourceLink(
        resourceType,
        resourceType === ResourceType.Prompt ? name : resourceId,
        workspaceId
      )}
      target="_blank"
      className="cursor-pointer"
    >
      {tag}
    </Link>
  ) : (
    tag
  );
}

function AssignResources({
  resources: selectedResources,
  resourceType,
  setResources,
}: AssignResourcesProps) {
  const {
    resources: foundResources,
    name,
    setName,
    isLoading,
  } = useGetResources(resourceType);

  return (
    <div className="pb-2">
      <div className="flex max-h-[300px] gap-2 overflow-y-auto rounded-lg border border-secondary px-3 py-2 focus-within:outline focus-within:-outline-offset-2 focus-within:outline-brand-green-400">
        <SearchIcon className="text-text-tertiary mt-2 flex-shrink-0 self-start" />
        <div className="flex flex-wrap gap-1">
          {selectedResources && selectedResources.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {selectedResources.map(
                (resource, idx) =>
                  resource && (
                    <TaggedResource
                      key={resource.resource_id}
                      resourceId={resource.resource_id}
                      resourceType={resourceType}
                      name={resource.resource_name}
                      onRemove={() => {
                        setResources(
                          (selectedResources ?? []).filter((_, i) => i !== idx)
                        );
                      }}
                    />
                  )
              )}
            </div>
          )}
          <input
            placeholder={`Search by ${resourceType.split('_').join(' ')}...`}
            value={name}
            className="ml-2 flex-grow border-none bg-transparent outline-none "
            onChange={(e) => setName(e.target.value)}
          />
        </div>
      </div>
      <div className="py-2">
        {isLoading || !foundResources ? (
          <LinearProgress />
        ) : (
          foundResources?.map((resource) => {
            return (
              <button
                type="button"
                className="flex h-10 w-full cursor-pointer select-none items-center gap-3 rounded-sm px-2 py-1.5 outline-none hover:bg-secondary focus:bg-secondary data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                key={resource.id}
                onClick={() => {
                  if (
                    selectedResources?.find(
                      (r) => r.resource_id === resource.id
                    )
                  ) {
                    setResources(
                      selectedResources.filter(
                        (r) => r.resource_id !== resource.id
                      )
                    );
                  } else {
                    setResources([
                      ...(selectedResources ?? []),
                      {
                        resource_id: resource.id,
                        resource_name:
                          resource.name ??
                          resource.repo_handle ??
                          resource.title,
                      },
                    ]);
                  }
                }}
              >
                {ICON_MAP('h-6 w-6')[resourceType]}
                {resource.name ?? resource.repo_handle ?? resource.title}
                {selectedResources?.find(
                  (r) => r.resource_id === resource.id
                ) && (
                  <div className="flex-1 text-right text-sm text-tertiary">
                    <CheckIcon className="inline h-4 w-4" />
                  </div>
                )}
              </button>
            );
          })
        )}

        {foundResources?.length === 0 && (
          <div className="ml-3 text-sm text-tertiary">
            No {resourceType}s found
          </div>
        )}
      </div>
    </div>
  );
}

type EditKeyValueFormProps = {
  error?: Error | null;
  onSubmit: (data: {
    key?: string;
    keyId?: string;
    value: string;
    description: string;
    resources: TaggingsByResourceType;
  }) => void;
  resourceTag?:
    | (ResourceTag & { description?: string })
    | { tag_key: string; tag_key_id: string };
  isMutating?: boolean;
};

function KeySelect({
  selectedKey,
  setSelectedKey,
  newKey,
  setNewKey,
  disabled,
  keyError,
  setKeyError,
}: {
  selectedKey?: { key: string; id: string } | null;
  setSelectedKey: (key: { key: string; id: string } | null) => void;
  newKey: string;
  setNewKey: (key: string) => void;
  disabled?: boolean;
  keyError?: string;
  setKeyError: (keyError: string) => void;
}) {
  const { data: tagKeys } = useListTagKeys();

  const filtered =
    tagKeys?.filter((tagKey) => lowerCaseIncludes(tagKey.key, newKey)) || [];

  return (
    <>
      <Combobox
        value={selectedKey}
        onChange={(v: ResourceTagKey | null) => {
          if (v === null) {
            setSelectedKey(null);
          } else {
            setSelectedKey(v);
            setNewKey('');
          }
        }}
        disabled={disabled}
      >
        <ComboboxInput
          className={cn(
            'flex gap-2 rounded-lg border border-secondary bg-[var(--joy-palette-background-surface)] px-3 py-2 focus-within:outline focus-within:-outline-offset-2 focus-within:outline-brand-green-400 disabled:cursor-not-allowed disabled:bg-secondary disabled:opacity-50',
            keyError ? 'border-error focus:border-error-strong' : ''
          )}
          onChange={(e) => {
            setNewKey(e.target.value);
            setKeyError('');
          }}
          placeholder="Search or create a new key..."
          displayValue={(v: ResourceTagKey | null) => {
            if (v) {
              return v.key;
            }
            return newKey;
          }}
        />

        <ComboboxOptions className="relative z-[var(--joy-zIndex-modal)] min-w-[8rem] overflow-hidden rounded-md border border-secondary bg-popover shadow-lg">
          {filtered.map((tagKey) => (
            <ComboboxOption
              className="relative flex w-full cursor-pointer select-none items-center rounded-sm px-4 py-2.5 text-sm outline-none hover:bg-secondary focus:bg-secondary data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              key={tagKey.id}
              value={tagKey}
            >
              {tagKey.key}
            </ComboboxOption>
          ))}
          {newKey && (
            <ComboboxOption
              className="relative flex w-full cursor-pointer select-none items-center rounded-sm px-4 py-2.5 text-sm outline-none hover:bg-secondary focus:bg-secondary"
              value={null}
            >
              <PlusIcon className="mr-2 h-4 w-4" /> Create "{newKey}"
            </ComboboxOption>
          )}
        </ComboboxOptions>
      </Combobox>
      {keyError && (
        <div className="ml-1 mt-1 text-sm text-error">{keyError}</div>
      )}
    </>
  );
}

function EditResourceTagForm({
  onSubmit,
  resourceTag,
  isMutating,
  error,
}: EditKeyValueFormProps) {
  const { register, handleSubmit, formState, setValue } = useForm<{
    value: string;
    description: string;
    key_description: string;
  }>();

  // null and undefined states here are used to determine if the user has
  // selected to create a new key or not. When this is null, the user has
  // selected to create a new key. When this is undefined, the user has not picked
  // between creating a new key or selecting an existing one.
  const [selectedKey, setSelectedKey] = useState<
    | {
        key: string;
        id: string;
      }
    | null
    | undefined
  >(undefined);
  const [newKey, setNewKey] = useState<string>('');
  const [keyError, setKeyError] = useState<string>('');
  const [selectedResources, setSelectedResources] =
    useState<TaggingsByResourceType>(DEFAULT_RESOURCES);

  const handleSetResourceTaggings = (
    resourceType: ResourceType,
    resources: Tagging[]
  ) => {
    setSelectedResources((prevSelectedResources) => ({
      ...prevSelectedResources,
      [`${resourceType.toLowerCase()}s`]: resources,
    }));
  };

  useEffect(() => {
    if (isResourceTag(resourceTag)) {
      if (resourceTag?.tag_value !== undefined) {
        setValue('value', resourceTag.tag_value);
      }
      if (resourceTag?.description !== undefined) {
        setValue('description', resourceTag.description ?? '');
      }
      if (resourceTag?.resources) {
        setSelectedResources(resourceTag.resources);
      }
    }
    if (resourceTag?.tag_key) {
      setSelectedKey({ key: resourceTag.tag_key, id: resourceTag.tag_key_id });
    }
  }, [resourceTag, setValue]);

  return (
    <form
      className="flex flex-col items-stretch gap-6"
      onSubmit={handleSubmit((data) => {
        if (!selectedKey?.id && !newKey) {
          setKeyError('Key is required');
          return;
        }
        const keyObj = selectedKey?.id
          ? {
              keyId: selectedKey.id,
            }
          : {
              key: newKey,
              key_description: data.key_description,
            };

        onSubmit({ ...data, resources: selectedResources, ...keyObj });
      })}
    >
      <FormControl>
        <FormLabel>Key</FormLabel>

        <KeySelect
          selectedKey={selectedKey}
          setSelectedKey={setSelectedKey}
          newKey={newKey}
          setNewKey={setNewKey}
          disabled={!!resourceTag?.tag_key}
          keyError={keyError}
          setKeyError={setKeyError}
        />
      </FormControl>
      {selectedKey === null && (
        <FormControl error={!!formState.errors.key_description?.message}>
          <FormLabel>Key description</FormLabel>
          <Input
            placeholder="Description..."
            {...register('key_description')}
          />
          {formState.errors.key_description?.message && (
            <FormHelperText>
              {formState.errors.key_description?.message}
            </FormHelperText>
          )}
        </FormControl>
      )}
      <FormControl error={!!formState.errors.value?.message}>
        <FormLabel>Value</FormLabel>
        <Input
          placeholder="Value..."
          {...register('value', { required: 'Value is required' })}
        />
        {formState.errors.value?.message && (
          <FormHelperText>{formState.errors.value?.message}</FormHelperText>
        )}
      </FormControl>
      <FormControl error={!!formState.errors.description?.message}>
        <FormLabel>Value description</FormLabel>
        <Input placeholder="Description..." {...register('description')} />
        {formState.errors.description?.message && (
          <FormHelperText>
            {formState.errors.description?.message}
          </FormHelperText>
        )}
      </FormControl>
      <div>
        <FormLabel>Assign resources</FormLabel>
        <Accordion type="multiple" defaultValue={ResourceTypeList}>
          {ResourceTypeList.map((resourceType) => {
            const resourceTypeTaggings: Tagging[] =
              selectedResources[`${resourceType.toLowerCase()}s`] ?? [];

            if (resourceType === ResourceType.Dashboard) {
              return null;
            }

            return (
              <AccordionItem
                key={resourceType}
                value={resourceType}
                className="border-b border-secondary "
              >
                <AccordionTrigger className="group my-2 flex w-full items-center gap-2 px-2 py-2 hover:bg-secondary focus-visible:outline-none">
                  {ICON_MAP('h-6 w-6')[resourceType]}
                  <div className="flex-1 text-left capitalize">{`${resourceType
                    .split('_')
                    .join(' ')}s`}</div>
                  <div>
                    <div className="text-sm text-tertiary">
                      {resourceTypeTaggings.length} assigned
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-tertiary transition-transform group-data-[state=closed]:rotate-90">
                    <ChevronRightIcon className={'h-6 w-6 '}></ChevronRightIcon>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <AssignResources
                    resources={resourceTypeTaggings}
                    resourceType={resourceType}
                    setResources={(resources) =>
                      handleSetResourceTaggings(resourceType, resources)
                    }
                  />
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>

      <div className="sticky bottom-0 right-4 bg-background py-4 text-right text-tertiary">
        {error && <ExpandableErrorAlert error={error} />}
        <div>
          <Button type="submit" loading={isMutating}>
            Submit
          </Button>
        </div>
      </div>
    </form>
  );
}

type EditKeyValueDrawerProps = {
  resourceTag?:
    | (ResourceTag & { description?: string })
    | { tag_key: string; tag_key_id: string };
  isOpen: boolean;
  setOpen: (open: boolean) => void;
};

export function ResourceTagCrudPane({
  resourceTag,
  setOpen,
  isOpen,
}: EditKeyValueDrawerProps) {
  const { createToast } = useToast();

  const createTagMutation = useCreateTagMutation();
  const updateTagMutation = useUpdateTagMutation(
    isResourceTag(resourceTag) ? resourceTag : undefined
  );

  const doClose = () => {
    setOpen(false);
  };

  return (
    <Pane
      open={isOpen}
      noBackArrow={true}
      onClose={doClose}
      className="p-0"
      dialogStyle={{
        marginLeft: 'calc(max(4.5rem, 100vw - max(20vw, 800px)))',
      }}
      title={
        <div className="flex flex-col gap-0.5">
          <h3 className="text-xl font-medium">
            {isResourceTag(resourceTag)
              ? 'Assign resources to tag'
              : 'Create resource tag'}
          </h3>
          <p className="text-sm font-normal text-tertiary">
            Resource tags are key-value pairs that categorize workspace
            resources. Keys define categories (e.g., 'Environment'), while
            values specify attributes within those categories (e.g.,
            'Production').
          </p>
        </div>
      }
      topBarRightElement={
        <button
          type="button"
          className="mr-6 rounded-md p-1 hover:bg-secondary active:bg-secondary"
          onClick={doClose}
        >
          <XMarkIcon className="h-6 w-6 text-ls-black" />
        </button>
      }
    >
      <div className="px-6 pt-4">
        <div className="mx-auto flex w-full flex-col gap-8">
          <EditResourceTagForm
            error={createTagMutation.error || updateTagMutation.error}
            resourceTag={resourceTag}
            onSubmit={async (data) => {
              if (isResourceTag(resourceTag)) {
                await updateTagMutation.trigger(data);
              } else {
                await createTagMutation.trigger(data);
              }
              setOpen(false);
              createToast({
                title: `Resource tag ${resourceTag ? 'updated' : 'created'}`,
              });
            }}
            isMutating={
              createTagMutation.isMutating || updateTagMutation.isMutating
            }
          />
        </div>
      </div>
    </Pane>
  );
}
