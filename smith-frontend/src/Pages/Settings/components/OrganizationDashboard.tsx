import { MetronomeEmbeddedDashboard } from './MetronomeEmbeddedDashboard';
import { MonthlyUsageDashboard } from './MonthlyUsageDashboard';

export function OrganizationDashboard(props: {
  type: 'invoices' | 'usage' | 'credits';
}) {
  // metronome embedded usage dashboards do not let us
  // aggregate usage by month, so we have a custom component here
  if (props.type === 'usage') {
    return <MonthlyUsageDashboard />;
  }
  return <MetronomeEmbeddedDashboard type={props.type} />;
}
