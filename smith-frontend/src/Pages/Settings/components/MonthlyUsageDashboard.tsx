import {
  AlertCircleIcon,
  Download01Icon,
  SearchLgIcon,
} from '@langchain/untitled-ui-icons';
import { LinearProgress } from '@mui/joy';

import { useMonthlyUsageChartData } from '@/Pages/Settings/hooks/useMonthlyUsageChartData';
import {
  ChartWrapper,
  CustomChartFallback,
  CustomChartHeader,
} from '@/Pages/SingleDashboard/components/CustomChartComponents';
import { useSeriesColorsMap } from '@/Pages/SingleDashboard/utils/getSeriesColor';
import { cn } from '@/utils/tailwind';

import { MonthlyUsageChart } from './MonthlyUsageChart';

export function MonthlyUsageDashboard() {
  const {
    sortedBillableMetricNames,
    billableMetricNameToChart,
    startTimestamp,
    endTimestamp,
    isLoading,
    error,
    csvData,
  } = useMonthlyUsageChartData();

  const seriesColorsMap = useSeriesColorsMap({
    dashboard: {
      charts: Array.from(billableMetricNameToChart?.values() ?? []),
    },
    isPrebuilt: false,
  });

  return isLoading ? (
    <div>
      <LinearProgress />
    </div>
  ) : error ? (
    <CustomChartFallback
      icon={<AlertCircleIcon className="size-6 text-disabled" />}
      message={`Error showing usage dashboard: ${error.message}`}
    />
  ) : (
    <>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-lg font-semibold">Usage Dashboard</h2>
        <a
          href={csvData?.url || '#'}
          download={csvData?.filename}
          className={cn(
            'flex items-center gap-1 whitespace-nowrap rounded-md border border-secondary px-3 py-1 text-sm font-semibold hover:bg-secondary',
            !csvData && 'pointer-events-none opacity-50'
          )}
        >
          <Download01Icon className="h-4 w-4" />
          Export CSV
        </a>
      </div>
      <div className="flex flex-col gap-8">
        {sortedBillableMetricNames.map((billableMetricName) => {
          const chart = billableMetricNameToChart?.get(billableMetricName);
          const chartHasNoData = chart?.series.length === 0;
          if (chart) {
            return (
              <ChartWrapper key={billableMetricName}>
                <CustomChartHeader hideProjectNames chart={chart} />

                {chartHasNoData ? (
                  <CustomChartFallback
                    icon={<SearchLgIcon className="size-6 text-disabled" />}
                    message="No usage data available"
                  />
                ) : (
                  <MonthlyUsageChart
                    startTimestamp={startTimestamp}
                    endTimestamp={endTimestamp}
                    chart={chart}
                    seriesColorsMap={seriesColorsMap}
                  />
                )}
              </ChartWrapper>
            );
          }
          return null;
        })}
      </div>
    </>
  );
}
