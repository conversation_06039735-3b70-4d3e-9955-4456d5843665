import { defaultStyles, useTooltip, useTooltipInPortal } from '@visx/tooltip';
import {
  AnimatedBarSeries,
  AnimatedBarStack,
  Axis,
  EventHandlerParams,
  Grid,
  XYChart,
  darkTheme,
  lightTheme,
} from '@visx/xychart';

import dayjs from 'dayjs';
import { useCallback, useMemo } from 'react';

import { ChartLegend } from '@/Pages/SingleDashboard/CustomChart';
import { TOOLTIP_ITEM_MAX_LENGTH } from '@/Pages/SingleDashboard/components/ChartTooltipContentV2';
import { TruncatedText } from '@/Pages/SingleDashboard/components/TruncatedText';
import { getSeriesDisplayName } from '@/Pages/SingleDashboard/utils/CustomChartsUtils.utils';
import {
  GRID_CHART_HEIGHT,
  TOOLTIP_WIDTH,
  numberFormatter,
} from '@/Pages/SingleDashboard/utils/constants';
import { getSeriesColor } from '@/Pages/SingleDashboard/utils/getSeriesColor';
import { useColorScheme } from '@/hooks/useColorScheme';
import {
  CustomChartDataSchema,
  CustomChartPreviewSchema,
} from '@/types/schema';
import { getAdjustedYDomain } from '@/utils/get-adjusted-y-domain';

import {
  fillMissingMonths,
  getAllSeriesForMonth,
  getUsageXDomain,
} from '../utils/usage';

export function MonthlyUsageChart({
  chart,
  startTimestamp,
  endTimestamp,
  seriesColorsMap,
}: {
  chart: CustomChartPreviewSchema;
  startTimestamp: string;
  endTimestamp: string;
  seriesColorsMap: Record<string, string>;
}) {
  const { isDarkMode } = useColorScheme();

  const xDomain = useMemo(() => {
    return getUsageXDomain(startTimestamp, endTimestamp);
  }, [startTimestamp, endTimestamp]);

  const colorScale = useCallback(
    (key: string) => {
      const seriesName = chart.series.find((s) => s.id === key)?.name || key;

      return (
        seriesColorsMap[getSeriesDisplayName(seriesName, key)] ??
        getSeriesColor(seriesName, key)
      );
    },
    [chart.series]
  );

  const legendItems = useMemo(() => {
    return chart.series
      .filter((s) =>
        chart.data.find(
          (d) =>
            d.series_id === s.id && typeof d.value === 'number' && d.value > 0
        )
      )
      .map((s) => ({
        label: s.name || s.id,
        color: colorScale(s.id),
      }));
  }, [chart.series, chart.data]);

  const { min: minY, max: maxY } = useMemo(() => {
    return chart.data.reduce(
      (acc, d) => {
        if (typeof d.value === 'number') {
          acc.min = Math.min(acc.min, d.value);
          acc.max = Math.max(acc.max, d.value);
        }
        return acc;
      },
      { min: Infinity, max: 0 }
    );
  }, [chart.data, chart.series]);

  const { domain: yDomain, numTicks } = getAdjustedYDomain(minY, maxY);

  const handlePointerMove = (
    event: EventHandlerParams<CustomChartDataSchema>
  ) => {
    showTooltip({
      tooltipLeft: event.svgPoint?.x,
      tooltipTop: event.svgPoint?.y,
      tooltipData: event.datum,
    });
  };

  const handlePointerLeave = () => {
    hideTooltip();
  };

  const { containerRef, TooltipInPortal } = useTooltipInPortal({
    scroll: true,
  });

  const {
    tooltipOpen,
    tooltipLeft,
    tooltipTop,
    tooltipData,
    hideTooltip,
    showTooltip,
  } = useTooltip<CustomChartDataSchema>();

  return (
    <div className="relative" ref={containerRef}>
      <XYChart
        xScale={{ type: 'band', domain: xDomain, padding: 0.5 }}
        yScale={{
          type: 'linear',
          domain: yDomain,
          nice: true,
        }}
        height={GRID_CHART_HEIGHT}
        theme={isDarkMode ? darkTheme : lightTheme}
        captureEvents={false}
      >
        <Axis
          orientation="bottom"
          tickLineProps={{
            strokeWidth: 1,
          }}
          tickLabelProps={() => ({
            textAnchor: 'middle',
            dominantBaseline: 'text-before-edge',
            fontSize: 12,
            dy: '0.1em',
            width: 100,
          })}
          tickFormat={(value: string) => {
            return dayjs(value).format('MMM YYYY');
          }}
          axisLineClassName="stroke-gray-300"
          axisClassName="stroke-gray-300"
        />
        <Grid
          numTicks={numTicks}
          lineStyle={{
            stroke: isDarkMode ? 'white' : 'black',
            strokeOpacity: 0.05,
          }}
        />
        <Axis
          labelClassName="font-medium text-quaternary"
          orientation={'left'}
          tickFormat={(value: number) => {
            return numberFormatter.format(value);
          }}
          labelOffset={20}
          numTicks={numTicks}
          axisClassName="stroke-gray-300 stroke-1"
          tickLabelProps={() => ({
            dx: '-0.25em',
            dy: '0.25em',
            fill: isDarkMode ? 'white' : 'black',
            fontFamily: 'Arial',
            fontSize: 10,
            textAnchor: 'end',
          })}
        />
        <AnimatedBarStack
          onPointerMove={handlePointerMove}
          onPointerOut={handlePointerLeave}
        >
          {chart.series.map((seriesSchema) => {
            const seriesData = fillMissingMonths(
              chart.data.filter((d) => d.series_id === seriesSchema.id),
              xDomain
            );
            return (
              <AnimatedBarSeries
                key={seriesSchema.id}
                dataKey={seriesSchema.id}
                data={seriesData}
                xAccessor={(d: CustomChartDataSchema) => d.timestamp}
                yAccessor={(d: CustomChartDataSchema) => d.value}
                colorAccessor={(d: CustomChartDataSchema) =>
                  colorScale(d.series_id)
                }
              />
            );
          })}
        </AnimatedBarStack>
      </XYChart>
      {tooltipOpen && tooltipData && (
        <TooltipInPortal
          left={tooltipLeft}
          top={tooltipTop}
          style={{
            ...defaultStyles,
            backgroundColor: 'var(--bg-secondary)',
          }}
        >
          <UsageTooltipContent
            tooltipData={tooltipData}
            chart={chart}
            colorScale={colorScale}
          />
        </TooltipInPortal>
      )}
      <ChartLegend legendItems={legendItems} hideLegend={false} />
    </div>
  );
}

function UsageTooltipContent({
  chart,
  colorScale,
  tooltipData,
}: {
  chart: CustomChartPreviewSchema;
  colorScale: (series_id: string) => string;
  tooltipData: CustomChartDataSchema | undefined;
}) {
  const seriesData = getAllSeriesForMonth(chart, tooltipData?.timestamp);

  return (
    <div
      className="flex flex-col gap-[7px]"
      style={{
        width: 'auto',
        maxWidth: TOOLTIP_WIDTH,
        flexShrink: 0,
      }}
    >
      {seriesData.map((d, idx) => {
        const value = typeof d.value === 'number' ? d.value : 0;

        return (
          <div key={idx} className="flex gap-2">
            <div
              className="h-[42px] w-1 rounded-sm"
              style={{ backgroundColor: colorScale(d.series_id) }}
            />
            <div className="flex flex-col justify-between font-normal">
              <TruncatedText
                className="text-xs"
                text={d.name}
                maxLength={TOOLTIP_ITEM_MAX_LENGTH}
              />
              <span className="text-xxs font-normal text-tertiary">
                {numberFormatter.format(value)}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
}
