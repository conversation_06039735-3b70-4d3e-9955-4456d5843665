import { LinearProgress } from '@mui/joy';

import { useColorScheme } from '@/hooks/useColorScheme';
import { useEmbeddableDashboard } from '@/hooks/useSwr';

export function MetronomeEmbeddedDashboard(props: {
  type: 'invoices' | 'credits';
}) {
  const { isDarkMode } = useColorScheme();
  const organizationInvoices = useEmbeddableDashboard(
    props.type,
    isDarkMode ? 'dark' : 'light',
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  return organizationInvoices.isLoading ? (
    <div>
      <LinearProgress />
    </div>
  ) : !organizationInvoices.data ? (
    <div>Failed to load {props.type}</div>
  ) : (
    <iframe
      src={organizationInvoices.data?.embeddable_url}
      width="100%"
      height="100%"
      style={{ border: 'none', colorScheme: 'none' }}
      title="Dashboard"
      allowFullScreen
    ></iframe>
  );
}
