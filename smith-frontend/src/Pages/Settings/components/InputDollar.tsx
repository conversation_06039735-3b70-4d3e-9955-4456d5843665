import { Input } from '@mui/joy';

import { ComponentProps } from 'react';
import { forwardRef } from 'react';

export const InputDollar = forwardRef<
  HTMLInputElement,
  ComponentProps<typeof Input>
>((props, ref) => {
  let value = props.value != null ? String(props.value) : '';
  if (value.includes('e')) {
    // 20 is the maximum number of decimal places that we can show
    value = Number(value).toFixed(20);
    while (value.endsWith('0')) {
      value = value.slice(0, -1);
    }
  }

  return (
    <Input
      ref={ref}
      startDecorator="$"
      placeholder="0.00"
      {...props}
      value={value}
    />
  );
});
