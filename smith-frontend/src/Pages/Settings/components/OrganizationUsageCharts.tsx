import { SearchIcon } from 'lucide-react';

import { ChartsGrid } from '@/Pages/SingleDashboard/ChartsGrid';
import { useChartTimeFilter } from '@/Pages/SingleDashboard/hooks/useChartTimeFilter';
import { useSeriesColorsMap } from '@/Pages/SingleDashboard/utils/getSeriesColor';
import { DateTimeRangePicker } from '@/components/DateTimeRangePicker/DateTimeRangePicker';
import { useDashboards, useSingleDashboard } from '@/hooks/useSwr';

export function OrganizationUsageCharts() {
  const dashboards = useDashboards(
    {
      title_contains: 'Organization Usage Graphs',
      limit: 1,
      offset: 0,
      sort_by: 'created_at',
      sort_by_desc: false,
    },
    {},
    true
  );

  const timeFilter = useChartTimeFilter({
    prefer_full_days: true,
  });

  const charts = useSingleDashboard(
    dashboards.data?.[0]?.id,
    {
      ...timeFilter.customChartsParams,
    },
    {},
    true
  );

  const chartsExist = !!charts.data?.charts?.length;

  const seriesColorsMap = useSeriesColorsMap({
    dashboard: {
      sub_sections: [],
      charts: charts.data?.charts ?? [],
    },
    isPrebuilt: false,
  });
  return (
    <div>
      <div className="flex flex-col gap-2 px-4 pb-4">
        {chartsExist && (
          <DateTimeRangePicker
            value={timeFilter.timeModel}
            onChange={timeFilter.setTimeModel}
            hideAllTime
          />
        )}
      </div>
      <ChartsGrid
        charts={charts.data?.charts ?? []}
        dashboardId={dashboards.data?.[0]?.id}
        isLoading={charts.isLoading}
        mutateChartData={charts.mutate}
        openExistingChart={() => {}}
        timeFilter={timeFilter}
        hideProjectNames
        hideExpandedView
        readOnly
        forceV1Tooltip
        className="flex flex-col gap-2"
        seriesColorsMap={seriesColorsMap}
        emptyState={<EmptyChartsState />}
      />
    </div>
  );
}

const EmptyChartsState = () => {
  return (
    <div className="mx-auto mt-36 flex w-[700px] flex-col gap-6 text-center">
      <div className="mx-auto rounded-lg border border-secondary">
        <SearchIcon size={48} className="p-3" />
      </div>
      <div className="flex flex-col gap-1.5">
        <span className="text-base font-bold">No usage charts available</span>
      </div>
    </div>
  );
};
