export function validatePrecision(value: number) {
  // we're currently storing price as numeric(18,12)
  // check, if the value has more than 12 significant digits

  if (value < 0) {
    return 'Price cannot be negative';
  }

  let strValue = String(value);
  if (strValue.includes('e')) {
    // 20 is the maximum number of decimal places that we can show
    strValue = Number(value).toFixed(20);
    while (strValue.endsWith('0') && !strValue.endsWith('.0')) {
      strValue = strValue.slice(0, -1);
    }
  }

  if (!strValue.includes('.')) {
    strValue = `${strValue}.0`;
  }
  // eslint-disable-next-line prefer-const
  let [integerPart, decimalPart] = strValue.split('.');

  while (integerPart.startsWith('0')) {
    integerPart = integerPart.slice(1);
  }

  const totalDigitsAfterDivision = decimalPart.length + 6;

  if (totalDigitsAfterDivision > 12) {
    return 'Precision is limited to 6 decimal places';
  }

  if (integerPart.length > 12) {
    return 'Value is too large';
  }

  return undefined;
}
