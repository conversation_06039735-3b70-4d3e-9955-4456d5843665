// Tests for convertTokens.ts
import { describe, expect, it } from 'vitest';

import { ModelPriceMapUpdateBody } from '@/types/schema';

import {
  convertPerMTokenToUnitCost,
  convertTokensForDefaultValues,
  convertTokensForUpdateBody,
  convertUnitCostToPerMToken,
  convertUnitCostToString,
} from '../convertTokens';

describe('convertTokens', () => {
  describe('convertUnitCostToPerMToken', () => {
    it('should convert unit cost to price per million tokens', () => {
      // $1 per token = $1,000,000 per million tokens
      expect(convertUnitCostToPerMToken(1)).toBe(1000000);

      // $0.000001 per token = $1 per million tokens
      expect(convertUnitCostToPerMToken(0.000001)).toBe(1);

      // $0.000002 per token = $2 per million tokens
      expect(convertUnitCostToPerMToken(0.000002)).toBe(2);

      // Common model pricing: $0.0000005 per token = $0.5 per million tokens
      expect(convertUnitCostToPerMToken(0.0000005)).toBe(0.5);
    });

    it('should handle zero', () => {
      expect(convertUnitCostToPerMToken(0)).toBe(0);
    });

    it('should handle very small numbers', () => {
      // $0.000000001 per token = $0.001 per million tokens
      expect(convertUnitCostToPerMToken(0.000000001)).toBe(0.001);
    });

    it('should handle large numbers', () => {
      // $100 per token = $100,000,000 per million tokens
      expect(convertUnitCostToPerMToken(100)).toBe(100000000);
    });
  });

  describe('convertPerMTokenToUnitCost', () => {
    it('should convert price per million tokens to unit cost', () => {
      // $1,000,000 per million tokens = $1 per token
      expect(convertPerMTokenToUnitCost(1000000)).toBe(1);

      // $1 per million tokens = $0.000001 per token
      expect(convertPerMTokenToUnitCost(1)).toBe(0.000001);

      // $2 per million tokens = $0.000002 per token
      expect(convertPerMTokenToUnitCost(2)).toBe(0.000002);

      // $0.5 per million tokens = $0.0000005 per token
      expect(convertPerMTokenToUnitCost(0.5)).toBe(0.0000005);
    });

    it('should handle zero', () => {
      expect(convertPerMTokenToUnitCost(0)).toBe(0);
    });

    it('should handle very small numbers', () => {
      // $0.001 per million tokens = $0.000000001 per token
      expect(convertPerMTokenToUnitCost(0.001)).toBe(0.000000001);
    });

    it('should handle large numbers', () => {
      // $100,000,000 per million tokens = $100 per token
      expect(convertPerMTokenToUnitCost(100000000)).toBe(100);
    });
  });

  describe('convertUnitCostToString', () => {
    it('should format unit cost as USD per million tokens', () => {
      // $0.000001 per token = $1.00 per million tokens
      expect(convertUnitCostToString(0.000001)).toBe('$1.00');

      // $0.000002 per token = $2.00 per million tokens
      expect(convertUnitCostToString(0.000002)).toBe('$2.00');

      // $0.0000005 per token = $0.50 per million tokens
      expect(convertUnitCostToString(0.0000005)).toBe('$0.50');
    });

    it('should handle zero', () => {
      expect(convertUnitCostToString(0)).toBe('$0.00');
    });

    it('should format very small numbers with appropriate precision', () => {
      // $0.000000001 per token = $0.001 per million tokens
      expect(convertUnitCostToString(0.000000001)).toBe('$0.001');

      // $0.0000000001 per token = $0.0001 per million tokens
      expect(convertUnitCostToString(0.0000000001)).toBe('$0.0001');
    });

    it('should format large numbers', () => {
      // $1 per token = $1,000,000.00 per million tokens
      expect(convertUnitCostToString(1)).toBe('$1,000,000.00');

      // $0.01 per token = $10,000.00 per million tokens
      expect(convertUnitCostToString(0.01)).toBe('$10,000.00');
    });

    it('should handle decimal precision correctly', () => {
      // $0.0000012345 per token = $1.2345 per million tokens
      expect(convertUnitCostToString(0.0000012345)).toBe('$1.2345');

      // $0.0**********789012345 per token should show up to 14 decimal places
      expect(convertUnitCostToString(0.0**********789012345)).toBe(
        '$1.23456789012345'
      );
    });
  });

  describe('convertTokensForUpdateBody', () => {
    it('should convert form data to backend format', () => {
      const formData: ModelPriceMapUpdateBody = {
        name: 'gpt-4',
        match_path: ['model'],
        match_pattern: '^gpt-4$',
        prompt_cost: 30, // $30 per million tokens
        completion_cost: 60, // $60 per million tokens
        provider: 'openai',
      };

      const result = convertTokensForUpdateBody(formData);

      expect(result).toEqual({
        name: 'gpt-4',
        match_path: ['model'],
        match_pattern: '^gpt-4$',
        prompt_cost: 0.00003, // $0.00003 per token
        completion_cost: 0.00006, // $0.00006 per token
        provider: 'openai',
        prompt_cost_details: undefined,
        completion_cost_details: undefined,
      });
    });

    it('should convert cost details when present', () => {
      const formData: ModelPriceMapUpdateBody = {
        name: 'gpt-4-vision',
        match_path: ['model'],
        match_pattern: '^gpt-4-vision$',
        prompt_cost: 10, // $10 per million tokens
        completion_cost: 30, // $30 per million tokens
        prompt_cost_details: {
          text: 10, // $10 per million tokens
          image: 765, // $765 per million tokens
        },
        completion_cost_details: {
          text: 30, // $30 per million tokens
          audio: 100, // $100 per million tokens
          video: 2000, // $2000 per million tokens
        },
      };

      const result = convertTokensForUpdateBody(formData);

      expect(result).toEqual({
        name: 'gpt-4-vision',
        match_path: ['model'],
        match_pattern: '^gpt-4-vision$',
        prompt_cost: 0.00001, // $0.00001 per token
        completion_cost: 0.00003, // $0.00003 per token
        prompt_cost_details: {
          text: 0.00001, // $0.00001 per token
          image: 0.000765, // $0.000765 per token
        },
        completion_cost_details: {
          text: 0.00003, // $0.00003 per token
          audio: 0.0001, // $0.0001 per token
          video: 0.002, // $0.002 per token
        },
      });
    });

    it('should handle empty cost details', () => {
      const formData: ModelPriceMapUpdateBody = {
        name: 'claude-3',
        match_path: ['model'],
        match_pattern: '^claude-3$',
        prompt_cost: 15, // $15 per million tokens
        completion_cost: 75, // $75 per million tokens
        prompt_cost_details: {},
        completion_cost_details: {},
      };

      const result = convertTokensForUpdateBody(formData);

      expect(result.prompt_cost_details).toEqual({});
      expect(result.completion_cost_details).toEqual({});
    });

    it('should handle zero costs', () => {
      const formData: ModelPriceMapUpdateBody = {
        name: 'free-model',
        match_path: ['model'],
        match_pattern: '^free-model$',
        prompt_cost: 0,
        completion_cost: 0,
        prompt_cost_details: {
          text: 0,
          image: 0,
        },
      };

      const result = convertTokensForUpdateBody(formData);

      expect(result.prompt_cost).toBe(0);
      expect(result.completion_cost).toBe(0);
      expect(result.prompt_cost_details).toEqual({
        text: 0,
        image: 0,
      });
    });
  });

  describe('convertTokensForDefaultValues', () => {
    it('should return undefined when no default values provided', () => {
      expect(convertTokensForDefaultValues()).toBeUndefined();
      expect(convertTokensForDefaultValues(undefined)).toBeUndefined();
    });

    it('should convert backend data to form format', () => {
      const backendData: ModelPriceMapUpdateBody = {
        name: 'gpt-4',
        match_path: ['model'],
        match_pattern: '^gpt-4$',
        prompt_cost: 0.00003, // $0.00003 per token
        completion_cost: 0.00006, // $0.00006 per token
        provider: 'openai',
      };

      const result = convertTokensForDefaultValues(backendData);

      expect(result).toEqual({
        name: 'gpt-4',
        match_path: ['model'],
        match_pattern: '^gpt-4$',
        prompt_cost: 30, // $30 per million tokens
        completion_cost: 60, // $60 per million tokens
        provider: 'openai',
        prompt_cost_details: undefined,
        completion_cost_details: undefined,
      });
    });

    it('should convert cost details when present', () => {
      const backendData: ModelPriceMapUpdateBody = {
        name: 'gpt-4-vision',
        match_path: ['model'],
        match_pattern: '^gpt-4-vision$',
        prompt_cost: 0.00001, // $0.00001 per token
        completion_cost: 0.00003, // $0.00003 per token
        prompt_cost_details: {
          text: 0.00001, // $0.00001 per token
          image: 0.000765, // $0.000765 per token
        },
        completion_cost_details: {
          text: 0.00003, // $0.00003 per token
          audio: 0.0001, // $0.0001 per token
          video: 0.002, // $0.002 per token
        },
      };

      const result = convertTokensForDefaultValues(backendData);

      expect(result).toEqual({
        name: 'gpt-4-vision',
        match_path: ['model'],
        match_pattern: '^gpt-4-vision$',
        prompt_cost: 10, // $10 per million tokens
        completion_cost: 30, // $30 per million tokens
        prompt_cost_details: {
          text: 10, // $10 per million tokens
          image: 765, // $765 per million tokens
        },
        completion_cost_details: {
          text: 30, // $30 per million tokens
          audio: 100, // $100 per million tokens
          video: 2000, // $2000 per million tokens
        },
      });
    });

    it('should handle empty cost details', () => {
      const backendData: ModelPriceMapUpdateBody = {
        name: 'claude-3',
        match_path: ['model'],
        match_pattern: '^claude-3$',
        prompt_cost: 0.000015, // $0.000015 per token
        completion_cost: 0.000075, // $0.000075 per token
        prompt_cost_details: {},
        completion_cost_details: {},
      };

      const result = convertTokensForDefaultValues(backendData);

      expect(result?.prompt_cost_details).toEqual({});
      expect(result?.completion_cost_details).toEqual({});
    });

    it('should handle zero costs', () => {
      const backendData: ModelPriceMapUpdateBody = {
        name: 'free-model',
        match_path: ['model'],
        match_pattern: '^free-model$',
        prompt_cost: 0,
        completion_cost: 0,
        prompt_cost_details: {
          text: 0,
          image: 0,
        },
      };

      const result = convertTokensForDefaultValues(backendData);

      expect(result?.prompt_cost).toBe(0);
      expect(result?.completion_cost).toBe(0);
      expect(result?.prompt_cost_details).toEqual({
        text: 0,
        image: 0,
      });
    });

    it('should handle missing optional fields', () => {
      const backendData: ModelPriceMapUpdateBody = {
        name: 'basic-model',
        match_path: ['model'],
        match_pattern: '^basic-model$',
        prompt_cost: 0.00001,
        completion_cost: 0.00002,
        // No prompt_cost_details, completion_cost_details, provider, start_time
      };

      const result = convertTokensForDefaultValues(backendData);

      expect(result).toEqual({
        name: 'basic-model',
        match_path: ['model'],
        match_pattern: '^basic-model$',
        prompt_cost: 10,
        completion_cost: 20,
        prompt_cost_details: undefined,
        completion_cost_details: undefined,
      });
    });
  });

  describe('conversion symmetry', () => {
    it('should be symmetric between unit cost and per million token conversions', () => {
      const originalUnitCost = 0.**********;
      const perMToken = convertUnitCostToPerMToken(originalUnitCost);
      const backToUnitCost = convertPerMTokenToUnitCost(perMToken);

      // Should be approximately equal due to floating point precision
      expect(backToUnitCost).toBeCloseTo(originalUnitCost, 10);
    });

    it('should be symmetric between form and backend data conversions', () => {
      const originalFormData: ModelPriceMapUpdateBody = {
        name: 'test-model',
        match_path: ['model'],
        match_pattern: '^test-model$',
        prompt_cost: 15.5, // $15.50 per million tokens
        completion_cost: 31.25, // $31.25 per million tokens
        prompt_cost_details: {
          text: 10.75,
          image: 500.25,
        },
        completion_cost_details: {
          audio: 25.5,
          video: 1000.123,
        },
      };

      // Convert to backend format and back
      const backendData = convertTokensForUpdateBody(originalFormData);
      const backToFormData = convertTokensForDefaultValues(backendData);

      expect(backToFormData?.prompt_cost).toBeCloseTo(
        originalFormData.prompt_cost,
        6
      );
      expect(backToFormData?.completion_cost).toBeCloseTo(
        originalFormData.completion_cost,
        6
      );
      expect(backToFormData?.prompt_cost_details?.text).toBeCloseTo(
        originalFormData.prompt_cost_details!.text,
        6
      );
      expect(backToFormData?.prompt_cost_details?.image).toBeCloseTo(
        originalFormData.prompt_cost_details!.image,
        6
      );
      expect(backToFormData?.completion_cost_details?.audio).toBeCloseTo(
        originalFormData.completion_cost_details!.audio,
        6
      );
      expect(backToFormData?.completion_cost_details?.video).toBeCloseTo(
        originalFormData.completion_cost_details!.video,
        6
      );
    });
  });
});
