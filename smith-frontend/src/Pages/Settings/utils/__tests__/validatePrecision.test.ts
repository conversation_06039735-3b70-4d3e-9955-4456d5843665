import { describe, expect, it } from 'vitest';

import { validatePrecision } from '../validatePrecision';

// Import the function from the parent file

describe('validatePrecision', () => {
  it('should return undefined for valid numbers within precision limits', () => {
    // Numbers with acceptable precision
    expect(validatePrecision(0.123456)).toBeUndefined();
    expect(validatePrecision(123.456789)).toBeUndefined();
    expect(validatePrecision(0.000001)).toBeUndefined();
    expect(validatePrecision(123456)).toBeUndefined();
  });

  it('should handle numbers with leading zeros correctly', () => {
    expect(validatePrecision(0.123)).toBeUndefined();
    expect(validatePrecision(0.000123)).toBeUndefined();
    expect(validatePrecision(0.0000000123)).toBe(
      'Precision is limited to 6 decimal places'
    );
  });

  it('should return error for numbers with too many decimal places', () => {
    // After division by 1,000,000 (adding 6 to decimal places), these exceed 12 decimal places
    expect(validatePrecision(0.0000000000123456789)).toBe(
      'Precision is limited to 6 decimal places'
    );
    expect(validatePrecision(0.1234567890123)).toBe(
      'Precision is limited to 6 decimal places'
    );
  });

  it('should return error for numbers that are too large', () => {
    expect(validatePrecision(1234567890123456)).toBe('Value is too large');
    expect(validatePrecision(10000000000000)).toBe('Value is too large');
  });

  it('should handle scientific notation correctly', () => {
    // Valid scientific notation
    expect(validatePrecision(1e-6)).toBeUndefined();
    expect(validatePrecision(1.23e-5)).toBe(
      'Precision is limited to 6 decimal places'
    );

    // Invalid due to precision after conversion
    expect(validatePrecision(1.23456789e-10)).toBe(
      'Precision is limited to 6 decimal places'
    );

    // Invalid due to large value after conversion
    expect(validatePrecision(1.23e15)).toBe('Value is too large');
  });

  it('should handle edge cases correctly', () => {
    // Zero
    expect(validatePrecision(0)).toBeUndefined();

    // Negative numbers
    expect(validatePrecision(-123.456)).toBe('Price cannot be negative');
    expect(validatePrecision(-0.0000001)).toBe('Price cannot be negative');

    // Large negative number
    expect(validatePrecision(-1234567890123456)).toBe(
      'Price cannot be negative'
    );

    // Very small negative number with too much precision
    expect(validatePrecision(-0.0000000000123456789)).toBe(
      'Price cannot be negative'
    );
  });

  it('should handle whole numbers correctly', () => {
    // Whole numbers without decimal point
    expect(validatePrecision(123)).toBeUndefined();
    expect(validatePrecision(123456789012)).toBeUndefined();
    expect(validatePrecision(1234567890123)).toBe('Value is too large');
  });

  it('should handle floating point precision issues', () => {
    // JavaScript floating point issues should be rejected if they result in too much precision
    expect(validatePrecision(0.1 + 0.2)).toBe(
      'Precision is limited to 6 decimal places'
    ); // 0.30000000000000004
    expect(validatePrecision(1.005 * 100)).toBe(
      'Precision is limited to 6 decimal places'
    ); // 100.49999999999999

    // But clean numbers should pass
    expect(validatePrecision(0.3)).toBeUndefined();
    expect(validatePrecision(100.5)).toBeUndefined();
  });
});
