import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { describe, expect, it } from 'vitest';

import {
  CustomChartDataSchema,
  CustomChartPreviewSchema,
  OrgUsage,
} from '@/types/schema';

import {
  billableMetricNameToChartSchema,
  fillMissingMonths,
  generateUsageCsvRows,
  getAllSeriesForMonth,
  getChartSchemaFromUsage,
  getMonthKey,
  getUsageXDomain,
} from '../usage';

dayjs.extend(utc);

describe('getUsageXDomain', () => {
  it('returns correct month range for same month', () => {
    const startDate = '2025-01-01';
    const endDate = '2025-01-31';
    const result = getUsageXDomain(startDate, endDate);
    expect(result).toEqual(['2025-01']);
  });

  it('returns correct month range for multiple months', () => {
    const startDate = '2025-01-15';
    const endDate = '2025-03-15';
    const result = getUsageXDomain(startDate, endDate);
    expect(result).toEqual(['2025-01', '2025-02', '2025-03']);
  });

  it('returns correct month range across years', () => {
    const startDate = '2024-11-01';
    const endDate = '2025-02-28';
    const result = getUsageXDomain(startDate, endDate);
    expect(result).toEqual(['2024-11', '2024-12', '2025-01', '2025-02']);
  });

  it('handles empty range', () => {
    const startDate = '2025-02-01';
    const endDate = '2025-01-31'; // End date before start date
    const result = getUsageXDomain(startDate, endDate);
    expect(result).toEqual([]);
  });
});

describe('fillMissingMonths', () => {
  it('fills in missing months with zero values', () => {
    const data: CustomChartDataSchema[] = [
      { series_id: 'series1', timestamp: '2025-01', value: 10 },
      { series_id: 'series1', timestamp: '2025-03', value: 30 },
    ];
    const xDomain = ['2025-01', '2025-02', '2025-03', '2025-04'];

    const result = fillMissingMonths(data, xDomain);

    expect(result).toHaveLength(4);
    expect(result[0]).toEqual({
      series_id: 'series1',
      timestamp: '2025-01',
      value: 10,
    });
    expect(result[1]).toEqual({
      series_id: 'series1',
      timestamp: '2025-02',
      value: 0,
    });
    expect(result[2]).toEqual({
      series_id: 'series1',
      timestamp: '2025-03',
      value: 30,
    });
    expect(result[3]).toEqual({
      series_id: 'series1',
      timestamp: '2025-04',
      value: 0,
    });
  });

  it('returns empty array for empty input data', () => {
    const data: CustomChartDataSchema[] = [];
    const xDomain = ['2025-01', '2025-02'];

    const result = fillMissingMonths(data, xDomain);

    expect(result).toEqual([]);
  });

  it('preserves existing data points', () => {
    const data: CustomChartDataSchema[] = [
      { series_id: 'series1', timestamp: '2025-01', value: 10 },
      { series_id: 'series1', timestamp: '2025-02', value: 20 },
    ];
    const xDomain = ['2025-01', '2025-02'];

    const result = fillMissingMonths(data, xDomain);

    expect(result).toEqual(data);
  });
});

describe('getAllSeriesForMonth', () => {
  it('returns all series data for a specific month', () => {
    const chart: CustomChartPreviewSchema = {
      title: 'Test Chart',
      description: '',
      chart_type: 'bar',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series3',
          name: 'Series 3',
          metric: 'run_count',
          feedback_key: null,
        },
      ],
      data: [
        { series_id: 'series1', timestamp: '2025-01', value: 10 },
        { series_id: 'series2', timestamp: '2025-01', value: 20 },
        { series_id: 'series3', timestamp: '2025-01', value: 0 },
        { series_id: 'series1', timestamp: '2025-02', value: 15 },
        { series_id: 'series2', timestamp: '2025-02', value: 25 },
      ],
    };

    const result = getAllSeriesForMonth(chart, '2025-01');

    expect(result).toHaveLength(2); // Only series with non-zero values
    expect(result).toContainEqual({
      series_id: 'series1',
      name: 'Series 1',
      timestamp: '2025-01',
      value: 10,
    });
    expect(result).toContainEqual({
      series_id: 'series2',
      name: 'Series 2',
      timestamp: '2025-01',
      value: 20,
    });
  });

  it('returns empty array for month with no data', () => {
    const chart: CustomChartPreviewSchema = {
      title: 'Test Chart',
      description: '',
      chart_type: 'bar',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'run_count',
          feedback_key: null,
        },
      ],
      data: [{ series_id: 'series1', timestamp: '2025-01', value: 10 }],
    };

    const result = getAllSeriesForMonth(chart, '2025-02');

    expect(result).toEqual([]);
  });

  it('filters out series with zero values', () => {
    const chart: CustomChartPreviewSchema = {
      title: 'Test Chart',
      description: '',
      chart_type: 'bar',
      series: [
        {
          id: 'series1',
          name: 'Series 1',
          metric: 'run_count',
          feedback_key: null,
        },
        {
          id: 'series2',
          name: 'Series 2',
          metric: 'run_count',
          feedback_key: null,
        },
      ],
      data: [
        { series_id: 'series1', timestamp: '2025-01', value: 0 },
        { series_id: 'series2', timestamp: '2025-01', value: 0 },
      ],
    };

    const result = getAllSeriesForMonth(chart, '2025-01');

    expect(result).toEqual([]);
  });
});

describe('getMonthKey', () => {
  it('formats timestamp to YYYY-MM format', () => {
    expect(getMonthKey('2025-01-15T12:30:45Z')).toBe('2025-01');
    expect(getMonthKey('2024-12-31')).toBe('2024-12');
    expect(getMonthKey('2025-02-01T00:00:00.000Z')).toBe('2025-02');
  });

  it('handles different timestamp formats', () => {
    expect(getMonthKey('2025-03-15T12:30:45Z')).toBe('2025-03');
    expect(getMonthKey('2025-04-15')).toBe('2025-04');
    expect(getMonthKey('2025-05-15T12:30:45.123Z')).toBe('2025-05');
  });
});

describe('getChartSchemaFromUsage', () => {
  it('creates a chart schema from usage data with groups', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-01-01',
        end_timestamp: '2025-01-31',
        value: null,
        groups: {
          tenant1: 100,
          tenant2: 200,
        },
      },
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-02-01',
        end_timestamp: '2025-02-28',
        value: null,
        groups: {
          tenant1: 150,
          tenant2: 250,
        },
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([
      ['tenant1', 'Development Team'],
      ['tenant2', 'Production Team'],
    ]);

    const result = getChartSchemaFromUsage(
      orgUsage,
      organizationId,
      tenantIdToName
    );

    expect(result.title).toBe('API Calls');
    expect(result.chart_type).toBe('bar');

    expect(result.series).toHaveLength(2);
    expect(result.series[0].id).toBe('tenant1');
    expect(result.series[0].name).toBe('Development Team');
    expect(result.series[1].id).toBe('tenant2');
    expect(result.series[1].name).toBe('Production Team');

    expect(result.data).toHaveLength(4); // 2 tenants × 2 months

    const janData = result.data.filter((d) => d.timestamp === '2025-01');
    expect(janData).toHaveLength(2);
    expect(janData.find((d) => d.series_id === 'tenant1')?.value).toBe(100);
    expect(janData.find((d) => d.series_id === 'tenant2')?.value).toBe(200);

    const febData = result.data.filter((d) => d.timestamp === '2025-02');
    expect(febData).toHaveLength(2);
    expect(febData.find((d) => d.series_id === 'tenant1')?.value).toBe(150);
    expect(febData.find((d) => d.series_id === 'tenant2')?.value).toBe(250);
  });

  it('creates a chart schema from usage data with direct values', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'Storage',
        start_timestamp: '2025-01-01',
        end_timestamp: '2025-01-31',
        value: 500,
        groups: null,
      },
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'Storage',
        start_timestamp: '2025-02-01',
        end_timestamp: '2025-02-28',
        value: 750,
        groups: null,
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([['org1', 'My Organization']]);

    const result = getChartSchemaFromUsage(
      orgUsage,
      organizationId,
      tenantIdToName
    );

    expect(result.title).toBe('Storage');
    expect(result.chart_type).toBe('bar');

    expect(result.series).toHaveLength(1);
    expect(result.series[0].id).toBe('org1');
    expect(result.series[0].name).toBe('My Organization');

    expect(result.data).toHaveLength(2); // 1 org × 2 months

    const janData = result.data.find((d) => d.timestamp === '2025-01');
    expect(janData?.series_id).toBe('org1');
    expect(janData?.value).toBe(500);

    const febData = result.data.find((d) => d.timestamp === '2025-02');
    expect(febData?.series_id).toBe('org1');
    expect(febData?.value).toBe(750);
  });

  it('returns empty chart schema for empty input', () => {
    const result = getChartSchemaFromUsage([], 'org1', new Map());

    expect(result.title).toBe('');
    expect(result.series).toEqual([]);
    expect(result.data).toEqual([]);
  });
});

describe('billableMetricNameToChartSchema', () => {
  it('groups usage data by billable metric name', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'metric1',
        start_timestamp: '2025-01-01',
        end_timestamp: '2025-01-31',
        value: 10,
        groups: null,
      },
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm2',
        billable_metric_name: 'metric2',
        start_timestamp: '2025-01-01',
        end_timestamp: '2025-01-31',
        value: 20,
        groups: null,
      },
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'metric1',
        start_timestamp: '2025-02-01',
        end_timestamp: '2025-02-28',
        value: 15,
        groups: null,
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([['org1', 'Organization 1']]);

    const result = billableMetricNameToChartSchema(
      orgUsage,
      organizationId,
      tenantIdToName
    );

    expect(result.size).toBe(2);
    expect(result.has('metric1')).toBe(true);
    expect(result.has('metric2')).toBe(true);

    const metric1Chart = result.get('metric1');
    expect(metric1Chart?.title).toBe('metric1');
    expect(metric1Chart?.data).toHaveLength(2);

    expect(metric1Chart?.series).toHaveLength(1);
    expect(metric1Chart?.series[0].id).toBe('org1');
    expect(metric1Chart?.series[0].name).toBe('Organization 1');

    const metric2Chart = result.get('metric2');
    expect(metric2Chart?.title).toBe('metric2');
    expect(metric2Chart?.data).toHaveLength(1);

    expect(metric2Chart?.series).toHaveLength(1);
    expect(metric2Chart?.series[0].id).toBe('org1');
    expect(metric2Chart?.series[0].name).toBe('Organization 1');
  });

  it('handles tenant groups in usage data', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'metric1',
        start_timestamp: '2025-01-01',
        end_timestamp: '2025-01-31',
        value: null,
        groups: {
          tenant1: 10,
          tenant2: 20,
        },
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([
      ['tenant1', 'Development Team'],
      ['tenant2', 'Production Team'],
      ['unknown-tenant', 'Unknown Team'], // This one shouldn't be used
    ]);

    const result = billableMetricNameToChartSchema(
      orgUsage,
      organizationId,
      tenantIdToName
    );

    const metric1Chart = result.get('metric1');
    expect(metric1Chart?.series).toHaveLength(2);
    expect(metric1Chart?.data).toHaveLength(2);

    const seriesIds = metric1Chart?.series.map((s) => s.id);
    expect(seriesIds).toContain('tenant1');
    expect(seriesIds).toContain('tenant2');
    expect(seriesIds).not.toContain('unknown-tenant');

    const tenant1Series = metric1Chart?.series.find((s) => s.id === 'tenant1');
    expect(tenant1Series?.name).toBe('Development Team');

    const tenant2Series = metric1Chart?.series.find((s) => s.id === 'tenant2');
    expect(tenant2Series?.name).toBe('Production Team');

    const tenant1Data = metric1Chart?.data.find(
      (d) => d.series_id === 'tenant1'
    );
    expect(tenant1Data?.value).toBe(10);

    const tenant2Data = metric1Chart?.data.find(
      (d) => d.series_id === 'tenant2'
    );
    expect(tenant2Data?.value).toBe(20);
  });

  it('returns empty map for empty input', () => {
    const result = billableMetricNameToChartSchema([], 'org1', new Map());
    expect(result.size).toBe(0);
  });

  it('falls back to tenant ID when name is not in the map', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'metric1',
        start_timestamp: '2025-01-01',
        end_timestamp: '2025-01-31',
        value: null,
        groups: {
          'unmapped-tenant': 30,
        },
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([['tenant1', 'Development Team']]);

    const result = billableMetricNameToChartSchema(
      orgUsage,
      organizationId,
      tenantIdToName
    );

    const metric1Chart = result.get('metric1');

    const unmappedSeries = metric1Chart?.series.find(
      (s) => s.id === 'unmapped-tenant'
    );
    expect(unmappedSeries?.name).toBe('unmapped-tenant');
  });
});

describe('generateUsageCsvRows', () => {
  it('returns null when inputs are missing', () => {
    expect(generateUsageCsvRows(undefined, 'org1', new Map())).toBeNull();
    expect(generateUsageCsvRows([], undefined, new Map())).toBeNull();
    expect(generateUsageCsvRows([], 'org1', undefined)).toBeNull();
  });

  it('generates CSV rows with organization usage (no groups)', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-01-15T12:30:45Z',
        end_timestamp: '2025-01-31T23:59:59Z',
        value: 100,
        groups: null,
      },
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm2',
        billable_metric_name: 'Storage',
        start_timestamp: '2025-02-01T00:00:00Z',
        end_timestamp: '2025-02-28T23:59:59Z',
        value: 250,
        groups: null,
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([['org1', 'Main Organization']]);

    const rows = generateUsageCsvRows(orgUsage, organizationId, tenantIdToName);

    expect(rows).not.toBeNull();
    expect(rows?.length).toBe(3); // Header + 2 data rows

    expect(rows?.[0]).toBe('Billable Metric Name,Workspace,Amount,Month');

    // Check data rows (order might vary due to sorting)
    const dataRows = rows?.slice(1) || [];
    expect(
      dataRows.some(
        (row) =>
          row.includes('API Calls') &&
          row.includes('Main Organization') &&
          row.includes('100')
      )
    ).toBe(true);
    expect(
      dataRows.some(
        (row) =>
          row.includes('Storage') &&
          row.includes('Main Organization') &&
          row.includes('250')
      )
    ).toBe(true);
  });

  it('generates CSV rows with workspace groups', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-01-15T12:30:45Z',
        end_timestamp: '2025-01-31T23:59:59Z',
        value: null,
        groups: {
          tenant1: 100,
          tenant2: 200,
        },
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([
      ['org1', 'Main Organization'],
      ['tenant1', 'Development Team'],
      ['tenant2', 'Production Team'],
    ]);

    const rows = generateUsageCsvRows(orgUsage, organizationId, tenantIdToName);

    expect(rows).not.toBeNull();
    expect(rows?.length).toBe(3); // Header + 2 data rows (one for each tenant)

    const dataRows = rows?.slice(1) || [];
    expect(
      dataRows.some(
        (row) =>
          row.includes('API Calls') &&
          row.includes('Development Team') &&
          row.includes('100')
      )
    ).toBe(true);
    expect(
      dataRows.some(
        (row) =>
          row.includes('API Calls') &&
          row.includes('Production Team') &&
          row.includes('200')
      )
    ).toBe(true);
  });

  it('sorts usage data by timestamp (newest first)', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-01-15T12:30:45Z',
        end_timestamp: '2025-01-31T23:59:59Z',
        value: 100,
        groups: null,
      },
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-03-01T00:00:00Z',
        end_timestamp: '2025-03-31T23:59:59Z',
        value: 300,
        groups: null,
      },
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-02-01T00:00:00Z',
        end_timestamp: '2025-02-28T23:59:59Z',
        value: 200,
        groups: null,
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([['org1', 'Main Organization']]);

    const rows = generateUsageCsvRows(orgUsage, organizationId, tenantIdToName);

    expect(rows).not.toBeNull();
    expect(rows?.length).toBe(4); // Header + 3 data rows

    // Check that the rows are sorted by timestamp (newest first)
    // The first data row (after header) should contain March data
    expect(rows?.[1]).toContain('2025-03');
    // The second data row should contain February data
    expect(rows?.[2]).toContain('2025-02');
    // The third data row should contain January data
    expect(rows?.[3]).toContain('2025-01');
  });

  it('uses organization name when there are no groups', () => {
    const orgUsage: OrgUsage[] = [
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-01-15T12:30:45Z',
        end_timestamp: '2025-01-31T23:59:59Z',
        value: 100,
        groups: null,
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([['org1', 'Main Organization']]);

    const rows = generateUsageCsvRows(orgUsage, organizationId, tenantIdToName);

    expect(rows).not.toBeNull();
    expect(rows?.length).toBe(2); // Header + 1 data row

    // Check that the organization name is used
    expect(rows?.[1]).toContain('"Main Organization"');
  });

  it('filters out rows with zero usage', () => {
    const orgUsage: OrgUsage[] = [
      // Organization-level usage with zero value (should be filtered out)
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm1',
        billable_metric_name: 'API Calls',
        start_timestamp: '2025-01-01T00:00:00Z',
        end_timestamp: '2025-01-31T23:59:59Z',
        value: 0,
        groups: null,
      },
      // Organization-level usage with positive value (should be included)
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm2',
        billable_metric_name: 'Storage',
        start_timestamp: '2025-02-01T00:00:00Z',
        end_timestamp: '2025-02-28T23:59:59Z',
        value: 250,
        groups: null,
      },
      // Group-level usage with mixed values (zero values should be filtered out)
      {
        customer_id: 'customer1',
        billable_metric_id: 'bm3',
        billable_metric_name: 'Compute',
        start_timestamp: '2025-03-01T00:00:00Z',
        end_timestamp: '2025-03-31T23:59:59Z',
        value: null,
        groups: {
          tenant1: 100, // Should be included
          tenant2: 0, // Should be filtered out
          tenant3: 50, // Should be included
        },
      },
    ];

    const organizationId = 'org1';
    const tenantIdToName = new Map([
      ['org1', 'Main Organization'],
      ['tenant1', 'Development Team'],
      ['tenant2', 'Test Team'],
      ['tenant3', 'Production Team'],
    ]);

    const rows = generateUsageCsvRows(orgUsage, organizationId, tenantIdToName);

    expect(rows).not.toBeNull();
    expect(rows?.length).toBe(4); // Header + 3 data rows (1 org row + 2 tenant rows)

    const dataRows = rows?.slice(1) || [];
    expect(dataRows.some((row) => row.includes('Storage'))).toBe(true);
    expect(dataRows.some((row) => row.includes('Development Team'))).toBe(true);
    expect(dataRows.some((row) => row.includes('Production Team'))).toBe(true);

    expect(dataRows.every((row) => !row.includes('API Calls'))).toBe(true); // Zero usage metric should be filtered out
    expect(dataRows.every((row) => !row.includes('Test Team'))).toBe(true); // Zero usage tenant should be filtered out
  });
});
