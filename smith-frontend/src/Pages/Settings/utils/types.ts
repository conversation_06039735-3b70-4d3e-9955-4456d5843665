import { ModelPriceMapUpdateBody } from '@/types/schema';

export type PricingPlanInfo = {
  planPriceHead: string;
  planPriceDesc: string;
  planDesc: string;
  planFeatures: string[];
};

// Extended form type to include temporary array fields for model price breakdown editing
export type ModelPriceMapFormData = ModelPriceMapUpdateBody & {
  // Temporary array fields that don't get submitted
  prompt_cost_details_temp_array?: { key: string; value: number }[];
  completion_cost_details_temp_array?: { key: string; value: number }[];
};
