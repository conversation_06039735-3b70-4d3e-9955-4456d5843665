import dayjs from 'dayjs';

import {
  CustomChartDataSchema,
  CustomChartPreviewSchema,
  OrgUsage,
} from '@/types/schema';

// generate a list of months between startDate and endDate for monthly usage charts
export function getUsageXDomain(startDate: string, endDate: string): string[] {
  const months: string[] = [];
  let current = dayjs(startDate).utc().startOf('month');

  const end = dayjs(endDate).utc().endOf('month');

  while (current.isBefore(end) || current.isSame(end, 'month')) {
    months.push(current.utc().format('YYYY-MM'));
    current = current.add(1, 'month');
  }
  return months;
}

// map a list of metronome usage to CustomChartPreviewSchema
// this will allow us to use existing charting hooks with data from metronome
export function getChartSchemaFromUsage(
  orgUsage: OrgUsage[],
  organizationId: string,
  tenantIdToName: Map<string, string>
): CustomChartPreviewSchema {
  let hasAddedOrgSeries = false;
  return orgUsage.reduce(
    (acc: CustomChartPreviewSchema, current: OrgUsage) => {
      if (!acc.title) {
        acc.title = current.billable_metric_name;
      }
      // we expect usage to be returned from the metronome api either grouped by tenant ids,
      // or not grouped at all in which case we assume the usage is for the organization
      if (current.groups) {
        Object.keys(current.groups).forEach((tenantId) => {
          if (!acc.series.map((schema) => schema.id).includes(tenantId)) {
            acc.series.push({
              id: tenantId,
              name: tenantIdToName.get(tenantId) ?? tenantId,
              metric: 'run_count',
              feedback_key: null,
            });
          }
          acc.data.push({
            series_id: tenantId,
            timestamp: getMonthKey(current.start_timestamp),
            value: current.groups?.[tenantId] ?? 0,
          });
        });
      } else if (current.value) {
        if (!hasAddedOrgSeries) {
          acc.series.push({
            id: organizationId,
            name: tenantIdToName.get(organizationId) ?? organizationId,
            metric: 'run_count',
            feedback_key: null,
          });
          hasAddedOrgSeries = true;
        }
        acc.data.push({
          series_id: organizationId,
          timestamp: getMonthKey(current.start_timestamp),
          value: current.value,
        });
      }
      return acc;
    },
    {
      title: '',
      description: '',
      chart_type: 'bar',
      series: [],
      data: [],
    } as CustomChartPreviewSchema
  );
}

// fill in all empty months with 0 values for a given series
// need to do this because of https://github.com/airbnb/visx/issues/1876
export function fillMissingMonths(
  data: CustomChartDataSchema[],
  xDomain: string[]
): CustomChartDataSchema[] {
  const dataMap = new Map<string, CustomChartDataSchema>();

  const seriesId = data[0]?.series_id;
  if (!seriesId) {
    return [];
  }

  data.forEach((d) => {
    dataMap.set(d.timestamp, d);
  });

  return xDomain.map((timestamp) => {
    if (dataMap.has(timestamp)) {
      return dataMap.get(timestamp)!;
    }
    return {
      timestamp: timestamp,
      value: 0,
      series_id: seriesId,
    } as CustomChartDataSchema;
  });
}

// return all series values for a given month - useful function to generate tooltips
export function getAllSeriesForMonth(
  chart: CustomChartPreviewSchema,
  monthKey?: string | undefined
): (CustomChartDataSchema & { name: string })[] {
  if (!monthKey) {
    return [];
  }
  return chart.series
    .map((series) => {
      const seriesDataPoint = chart.data.find(
        (d) => d.series_id === series.id && d.timestamp === monthKey
      );
      return {
        series_id: series.id,
        name: series.name,
        timestamp: monthKey,
        value: seriesDataPoint?.value ?? 0,
      };
    })
    .filter((d) => {
      return !!(d.value ?? 0);
    });
}

// get a map from metronome billing metric name to CustomChartPreviewSchema
// main function used to power monthly usage charts for all billing metrics in metronome
export function billableMetricNameToChartSchema(
  orgUsage: OrgUsage[],
  organizationId: string,
  tenantIdToName: Map<string, string>
): Map<string, CustomChartPreviewSchema> {
  const result = new Map<string, CustomChartPreviewSchema>();

  const billableMetricNameToOrgUsage = orgUsage.reduce((acc, current) => {
    const billableMetric = current.billable_metric_name;
    if (!acc.get(billableMetric)) {
      acc.set(billableMetric, []);
    }
    acc.get(billableMetric)?.push(current);
    return acc;
  }, new Map<string, OrgUsage[]>());

  for (const [billableMetricName, orgUsage] of billableMetricNameToOrgUsage) {
    const schema = getChartSchemaFromUsage(
      orgUsage,
      organizationId,
      tenantIdToName
    );
    result.set(billableMetricName, schema);
  }

  return result;
}

export function getMonthKey(timestamp: string) {
  return dayjs(timestamp).utc().format('YYYY-MM');
}

export function generateUsageCsvRows(
  orgUsage: OrgUsage[] | undefined,
  organizationId: string | undefined,
  tenantIdToName: Map<string, string> | undefined
): string[] | null {
  if (!orgUsage || !tenantIdToName || !organizationId) return null;

  const headers = ['Billable Metric Name', 'Workspace', 'Amount', 'Month'];
  const csvRows = [headers.join(',')];

  const sortedUsage = [...orgUsage].sort((a, b) => {
    return b.start_timestamp.localeCompare(a.start_timestamp);
  });

  sortedUsage.forEach((usage) => {
    const metricName = usage.billable_metric_name;
    const month = getMonthKey(usage.start_timestamp);

    if (!usage.groups) {
      if (usage.value && usage.value > 0) {
        const orgName = tenantIdToName.get(organizationId) || organizationId;
        csvRows.push(
          [`"${metricName}"`, `"${orgName}"`, usage.value, month].join(',')
        );
      }
    } else {
      Object.entries(usage.groups).forEach(([tenantId, value]) => {
        if (value > 0) {
          const groupName = tenantIdToName.get(tenantId) || tenantId;
          csvRows.push(
            [`"${metricName}"`, `"${groupName}"`, value, month].join(',')
          );
        }
      });
    }
  });

  return csvRows;
}

export function generateUsageCsvData(
  orgUsage: OrgUsage[] | undefined,
  organizationId: string | undefined,
  tenantIdToName: Map<string, string> | undefined
): { url: string; filename: string } | null {
  const csvRows = generateUsageCsvRows(
    orgUsage,
    organizationId,
    tenantIdToName
  );
  if (!csvRows) return null;

  const csvContent = csvRows.join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const filename = `usage-data-${dayjs().utc().format('YYYY-MM-DD')}.csv`;

  return { url, filename };
}
