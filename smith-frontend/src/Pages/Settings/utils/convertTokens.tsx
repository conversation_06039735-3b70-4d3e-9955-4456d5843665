import { Decimal } from 'decimal.js';

import { ModelPriceMapUpdateBody } from '@/types/schema';

const usdFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
  maximumFractionDigits: 14,
});

export const convertUnitCostToPerMToken = (unitCost: number) => {
  return new Decimal(unitCost).times(1000000).toNumber();
};

export const convertPerMTokenToUnitCost = (perMTokenCost: number) => {
  return new Decimal(perMTokenCost).div(1000000).toNumber();
};

export const convertUnitCostToString = (unitCost: number) => {
  return usdFormatter.format(convertUnitCostToPerMToken(unitCost));
};

// user will enter the price per 1M tokens, we need to convert it to the price per token
export const convertTokensForUpdateBody = (
  updateBody: ModelPriceMapUpdateBody
): ModelPriceMapUpdateBody => {
  const convertedPromptCostDetails = updateBody.prompt_cost_details
    ? Object.fromEntries(
        Object.entries(updateBody.prompt_cost_details ?? {}).map(
          ([key, value]) => [key, convertPerMTokenToUnitCost(value)]
        )
      )
    : undefined;
  const convertedCompletionCostDetails = updateBody.completion_cost_details
    ? Object.fromEntries(
        Object.entries(updateBody.completion_cost_details ?? {}).map(
          ([key, value]) => [key, convertPerMTokenToUnitCost(value)]
        )
      )
    : undefined;

  return {
    ...updateBody,
    prompt_cost: convertPerMTokenToUnitCost(updateBody.prompt_cost),
    completion_cost: convertPerMTokenToUnitCost(updateBody.completion_cost),
    prompt_cost_details: convertedPromptCostDetails,
    completion_cost_details: convertedCompletionCostDetails,
  };
};

// loaded schema from backend will be in price per token, we need to convert it to price per 1M tokens
export const convertTokensForDefaultValues = (
  defaultValues?: ModelPriceMapUpdateBody
): ModelPriceMapUpdateBody | undefined => {
  if (!defaultValues) return undefined;
  const convertedPromptCostDetails = defaultValues.prompt_cost_details
    ? Object.fromEntries(
        Object.entries(defaultValues.prompt_cost_details ?? {}).map(
          ([key, value]) => [key, convertUnitCostToPerMToken(value)]
        )
      )
    : undefined;
  const convertedCompletionCostDetails = defaultValues.completion_cost_details
    ? Object.fromEntries(
        Object.entries(defaultValues.completion_cost_details ?? {}).map(
          ([key, value]) => [key, convertUnitCostToPerMToken(value)]
        )
      )
    : undefined;

  return {
    ...defaultValues,
    prompt_cost: convertUnitCostToPerMToken(defaultValues.prompt_cost),
    completion_cost: convertUnitCostToPerMToken(defaultValues.completion_cost),
    prompt_cost_details: convertedPromptCostDetails,
    completion_cost_details: convertedCompletionCostDetails,
  };
};
