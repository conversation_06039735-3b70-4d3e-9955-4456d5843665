import type { SerializedConstructor } from '@langchain/core/load/serializable';
import { TemplateFormat } from '@langchain/core/prompts';

import { union } from 'lodash';
import { useMemo } from 'react';

import {
  MESSAGE_TYPE_TO_MIME_TYPE,
  MIME_TYPES,
  MimeType,
  SUPPORTED_SUBTYPES,
} from '@/components/RichTextEditor/constants';
import {
  isMessageContentPartLangChainMultimodal,
  isMessageContentPartOpenAIAudio,
} from '@/types/schema';

import { isSerializedConstructor } from '../../../../utils/serialized';
import { extractTemplateFormatFromKwargs } from '../../components/manifest/serialized/prompts/ManifestChatPromptTemplate.utils';

function findUnion<T>(arrays: T[][]): T[] {
  return union(arrays.flat());
}

function getMessagePlaceholderVariables(manifest: SerializedConstructor) {
  const names: string[] = [];
  JSON.stringify(manifest.kwargs.first, (_, value) => {
    if (!isSerializedConstructor(value)) return value;
    const id = value.id.join('/');
    if (
      id === 'langchain/prompts/chat/MessagesPlaceholder' ||
      id === 'langchain_core/prompts/chat/MessagesPlaceholder'
    ) {
      names.push(value.kwargs.variable_name);
    }

    return value;
  });

  return names;
}

function getImageVariables(manifest: SerializedConstructor) {
  const names: PromptVariable[] = [];
  JSON.stringify(manifest.kwargs.first, (_, value) => {
    if (!isSerializedConstructor(value)) return value;
    const id = value.id.join('/');
    if (
      id === 'langchain/prompts/image/ImagePromptTemplate' ||
      id === 'langchain_core/prompts/image/ImagePromptTemplate'
    ) {
      names.push(
        ...value.kwargs.input_variables.map((variable) => ({
          name: variable,
          type: MIME_TYPES.IMAGE,
        }))
      );
    }
    return value;
  });

  return names;
}

export function getMultimodalVariables(manifest: SerializedConstructor) {
  const names: PromptVariable[] = [];
  JSON.stringify(manifest.kwargs.first, (_, value) => {
    if (!isSerializedConstructor(value)) return value;
    const id = value.id.join('/');
    if (id === 'langchain_core/prompts/dict/DictPromptTemplate') {
      names.push(
        ...(value.kwargs.input_variables?.map((variable) => {
          const templateType = value.kwargs.template?.type;
          const type = templateType
            ? MESSAGE_TYPE_TO_MIME_TYPE[templateType] ?? MIME_TYPES.IMAGE
            : MIME_TYPES.IMAGE;

          const subtype = Object.keys(SUPPORTED_SUBTYPES).includes(type)
            ? (() => {
                switch (type) {
                  case MIME_TYPES.AUDIO:
                    if (
                      isMessageContentPartOpenAIAudio(value.kwargs.template)
                    ) {
                      const mimeSubtype =
                        value.kwargs.template.input_audio.format;
                      return SUPPORTED_SUBTYPES[type]?.includes(mimeSubtype)
                        ? mimeSubtype
                        : undefined;
                    } else if (
                      isMessageContentPartLangChainMultimodal(
                        value.kwargs.template
                      )
                    ) {
                      const mimeSubtype =
                        value.kwargs.template.mime_type.split('/')[1];
                      return SUPPORTED_SUBTYPES[type]?.includes(mimeSubtype)
                        ? mimeSubtype
                        : undefined;
                    }
                    return undefined;
                  default:
                    return undefined;
                }
              })()
            : undefined;

          return {
            name: variable,
            type,
            subtype,
          };
        }) ?? [])
      );
    }
    return value;
  });

  return names;
}

export type PromptVariable = {
  name: string;
  type: MimeType;
  subtype?: string;
};

export const usePlaygroundPromptTemplateVariables = (
  manifests: SerializedConstructor[]
): {
  manifestInputVariables: {
    names: string[];
    templateFormat: TemplateFormat;
  }[];
  multimodalVariables: PromptVariable[];
  imageVariables: PromptVariable[];
  messagePlaceholders: string[];
} => {
  const imageVariables = findUnion(manifests.map(getImageVariables));

  const multimodalVariables = findUnion(manifests.map(getMultimodalVariables));

  const manifestInputVariables = useMemo(() => {
    return manifests.map((m) => ({
      names: m.kwargs.first?.kwargs?.input_variables as string[],
      templateFormat: extractTemplateFormatFromKwargs(
        m.kwargs.first.kwargs
      ) as TemplateFormat,
    }));
  }, [manifests]);

  const messagePlaceholders = findUnion(
    manifests.map(getMessagePlaceholderVariables)
  );

  return {
    manifestInputVariables,
    multimodalVariables,
    imageVariables,
    messagePlaceholders,
  };
};
