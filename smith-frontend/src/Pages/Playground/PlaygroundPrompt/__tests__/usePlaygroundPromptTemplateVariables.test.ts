import type { SerializedConstructor } from '@langchain/core/load/serializable';

import { describe, expect, it } from 'vitest';

import { MIME_TYPES } from '@/components/RichTextEditor/constants';

import { getMultimodalVariables } from '../hooks/usePlaygroundPromptTemplateVariables';

const manifestWrapper = (
  template: SerializedConstructor
): SerializedConstructor => ({
  lc: 1,
  type: 'constructor',
  id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
  kwargs: { first: template },
});

describe('getMultimodalVariables', () => {
  it('should extract variables from DictPromptTemplate with image type', () => {
    const manifest = manifestWrapper({
      lc: 1,
      type: 'constructor',
      id: ['langchain_core', 'prompts', 'dict', 'DictPromptTemplate'],
      kwargs: {
        input_variables: ['image1', 'image2'],
        template: {
          type: 'image_url',
          image_url: 'http://example.com/image.png',
        },
      },
    });

    const result = getMultimodalVariables(manifest);
    expect(result).toEqual([
      { name: 'image1', type: MIME_TYPES.IMAGE },
      { name: 'image2', type: MIME_TYPES.IMAGE },
    ]);
  });

  it('should extract variables from DictPromptTemplate with pdf type', () => {
    const manifest = manifestWrapper({
      lc: 1,
      type: 'constructor',
      id: ['langchain_core', 'prompts', 'dict', 'DictPromptTemplate'],
      kwargs: {
        input_variables: ['pdf1'],
        template: {
          type: 'file',
          file: {
            file_name: 'datahereencoded',
            filedata: 'datahereencoded',
          },
        },
      },
    });

    const result = getMultimodalVariables(manifest);
    expect(result).toEqual([{ name: 'pdf1', type: MIME_TYPES.PDF }]);
  });

  it('should extract variables with audio subtype', () => {
    const manifest = manifestWrapper({
      lc: 1,
      type: 'constructor',
      id: ['langchain_core', 'prompts', 'dict', 'DictPromptTemplate'],
      kwargs: {
        input_variables: ['audio1'],
        template: {
          type: 'input_audio',
          input_audio: {
            data: 'datahereencoded',
            format: 'mp3',
          },
        },
      },
    });

    const result = getMultimodalVariables(manifest);
    expect(result).toEqual([
      { name: 'audio1', type: MIME_TYPES.AUDIO, subtype: 'mp3' },
    ]);
  });

  it('should properly handle audio langchain format', () => {
    const manifest = manifestWrapper({
      lc: 1,
      type: 'constructor',
      id: ['langchain_core', 'prompts', 'dict', 'DictPromptTemplate'],
      kwargs: {
        input_variables: ['audio1'],
        template: {
          type: 'audio',
          source_type: 'base64',
          data: 'datahereencoded',
          mime_type: 'audio/mp3',
        },
      },
    });

    const result = getMultimodalVariables(manifest);
    expect(result).toEqual([
      { name: 'audio1', type: MIME_TYPES.AUDIO, subtype: 'mp3' },
    ]);
  });

  it('should return empty array when no DictPromptTemplate is found', () => {
    const manifest = manifestWrapper({
      lc: 1,
      type: 'constructor',
      id: ['langchain_core', 'prompts', 'chat', 'ChatPromptTemplate'],
      kwargs: {
        input_variables: ['text1'],
      },
    });

    const result = getMultimodalVariables(manifest);
    expect(result).toEqual([]);
  });

  it('should handle missing input_variables', () => {
    const manifest = manifestWrapper({
      lc: 1,
      type: 'constructor',
      id: ['langchain_core', 'prompts', 'dict', 'DictPromptTemplate'],
      kwargs: {
        template: {
          template: {
            type: 'image',
          },
        },
      },
    });

    const result = getMultimodalVariables(manifest);
    expect(result).toEqual([]);
  });
});
