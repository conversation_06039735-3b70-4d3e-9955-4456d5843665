import type { SerializedConstructor } from '@langchain/core/load/serializable';
import { TemplateFormat } from '@langchain/core/prompts';

import { ToolDefinition } from 'node_modules/@langchain/core/dist/language_models/base';
import { ReactNode, useMemo } from 'react';

import { useOrganizationId } from '@/hooks/useSwr';
import { RepoWithLookupsSchema, RunSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { PlaygroundCommit } from '../components/PlaygroundCommit';
import { getVariablesRequiringJSONEditor } from '../utils/parsingPromptTemplates.utils';
import { PlaygroundPromptInputVars } from './PlaygroundPromptInputVars';
import { usePlaygroundPromptTemplateVariables } from './hooks/usePlaygroundPromptTemplateVariables';

function dedupeInputVars(
  inputVars: { names: string[]; templateFormat: TemplateFormat }[]
) {
  return inputVars.reduce<{ name: string; templateFormat: TemplateFormat }[]>(
    (acc, curr) => {
      curr.names.forEach((name) => {
        const existingVar = acc.find((item) => item.name === name);

        if (!existingVar) {
          // If the variable doesn't exist, add it to the accumulator
          acc.push({ name, templateFormat: curr.templateFormat });
        } else {
          // If the variable already exists, apply deduplication logic
          if (existingVar.templateFormat !== curr.templateFormat) {
            // If formats are different, prioritize keeping 'mustache'
            if (curr.templateFormat === 'mustache') {
              existingVar.templateFormat = curr.templateFormat;
            }
          }
          // If the formats are the same, do nothing (only keep one)
        }
      });
      return acc;
    },
    []
  );
}

export const PlaygroundPromptInputs = ({
  manifests,
  input,
}: {
  manifests: SerializedConstructor[];
  input: RunSchema['inputs'];
}) => {
  const {
    imageVariables,
    manifestInputVariables,
    multimodalVariables,
    messagePlaceholders,
  } = usePlaygroundPromptTemplateVariables(manifests);
  const templateFormats = useMemo(() => {
    return new Set(manifestInputVariables.map((m) => m.templateFormat));
  }, [manifestInputVariables]);
  const isAllMustache =
    templateFormats.size === 1 && templateFormats.has('mustache');
  const isMixed = templateFormats.size > 1;

  const jsonStringManifestInputVariables = JSON.stringify(
    manifestInputVariables
  );

  const inputVars = useMemo(() => {
    return dedupeInputVars(manifestInputVariables);
    // need to do json stringify because react doesn't do deep equality checks
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jsonStringManifestInputVariables]);

  const variablesRequiringJSONEditor = useMemo(
    () => getVariablesRequiringJSONEditor(manifests),
    [manifests]
  );

  const isCompact = useMemo(
    () =>
      inputVars.every((inputVar) => {
        let value = input?.[inputVar.name];

        if (value != null && Array.isArray(value)) {
          return value.length === 0;
        }

        if (value != null && typeof value !== 'string') {
          value = null;
        }
        return (
          (value?.length ?? 0) <= 50 && (value?.split('\n').length ?? 0) <= 3
        );
      }),
    [inputVars, input]
  );

  const renderInputVarsHelpText = () => {
    const mustacheHelpText = (
      <>
        <span className="text-brand-green-400">{'{{'}</span> and{' '}
        <span className="text-brand-green-400">{'}}'}</span> brackets for
        mustache formatting
      </>
    );
    const fStringHelpText = (
      <>
        <span className="text-brand-green-400">{'{'}</span> and{' '}
        <span className="text-brand-green-400">{'}'}</span> brackets for
        f-string formatting
      </>
    );
    const mixedHelpText = (
      <>
        {fStringHelpText} and {mustacheHelpText}
      </>
    );

    return (
      <div className="rounded-md border border-dashed border-secondary p-3 text-sm text-tertiary">
        Add a new variable by wrapping variable name with{' '}
        {isMixed
          ? mixedHelpText
          : isAllMustache
          ? mustacheHelpText
          : fStringHelpText}
        .
      </div>
    );
  };

  if (inputVars.length === 0) {
    return renderInputVarsHelpText();
  }

  return (
    <div className="@container">
      <div
        className={cn(
          'flex flex-col',
          isCompact && '@xl:grid @xl:grid-cols-[auto,1fr] @xl:gap-y-2'
        )}
      >
        <PlaygroundPromptInputVars
          inputVars={inputVars}
          isCompact={isCompact}
          input={input}
          imageVariables={imageVariables}
          multimodalVariables={multimodalVariables}
          messagePlaceholders={messagePlaceholders}
          variablesRequiringJSONEditor={variablesRequiringJSONEditor}
        />
      </div>
    </div>
  );
};

export function PlaygroundPromptCommit(props: {
  manifest: SerializedConstructor | null;
  disabled: boolean;
  repo?: RepoWithLookupsSchema;
  onSuccess?: (newCommitHash: string) => void;
  className?: string;
  replaceTools?: ToolDefinition[];
  customButton?: (props) => ReactNode;
}) {
  const organizationId = useOrganizationId();
  const currentRepo = props.repo;

  const manifestWithTools = props.replaceTools && {
    ...props.manifest,
    kwargs: {
      ...props.manifest?.kwargs,
      last: {
        ...props.manifest?.kwargs?.last,
        kwargs: {
          ...props.manifest?.kwargs?.last?.kwargs,
          kwargs: {
            ...props.manifest?.kwargs?.last?.kwargs?.kwargs,
            tools: props.replaceTools,
          },
        },
      },
    },
  };

  if (!organizationId) return null;
  return (
    <PlaygroundCommit
      currentRepo={currentRepo}
      manifest={(manifestWithTools as SerializedConstructor) ?? props.manifest}
      disabled={props.disabled}
      onSuccess={props.onSuccess}
      className={props.className}
      customButton={props.customButton}
    />
  );
}
