import { TemplateFormat } from '@langchain/core/prompts';
import { PlusIcon, XIcon } from '@langchain/untitled-ui-icons';
import { Tooltip } from '@mui/joy';

import { Fragment, useId, useMemo, useState } from 'react';

import { CodeData } from '@/components/Code/Code';
import { EditableChatInput } from '@/components/EditableChatInput';
import { Pane } from '@/components/Pane';
import { PlainTextEditor } from '@/components/PlainTextEditor';
import { MultimodalContent } from '@/components/RichTextEditor/MultimodalContent';
import { MultimodalDropdown } from '@/components/RichTextEditor/MultimodalDropdown/MultimodalDropdown';
import { MultimodalButton } from '@/components/RichTextEditor/MultimodalDropdown/components/MultimodalButton';
import { RichTextView } from '@/components/RichTextEditor/RichTextView';
import {
  ICON_FROM_MIME_TYPE,
  LABEL_FROM_MIME_TYPE,
  MimeType,
} from '@/components/RichTextEditor/constants';
import InfoCircleIcon from '@/icons/InfoCircleIcon.svg?react';
import { MessageUnionType } from '@/types/schema';
import {
  getMessageContent,
  getMessageFields,
  getMessageType,
  maybeGetArrayOfMessages,
} from '@/utils/messages';
import { cn } from '@/utils/tailwind';

import { PLAYGROUND_RESERVED_NAMES } from '../constants';

export function PlaygroundPromptInputMultimodalItem(props: {
  name: string;
  value: unknown | undefined;
  onChange?: (value: string | undefined) => void;
  isCompact: boolean;
  canEdit: boolean;
  cursorPointer: boolean;
  mimeType: MimeType;
  mimeSubtype?: string;
  templateFormat?: TemplateFormat;
}) {
  const id = useId();

  const Icon = props.mimeType ? ICON_FROM_MIME_TYPE[props.mimeType] : PlusIcon;
  const label = `Add ${
    props.mimeType ? LABEL_FROM_MIME_TYPE[props.mimeType] : 'multimodal'
  } content`;

  return (
    <Fragment>
      <label
        htmlFor={id}
        className={cn(
          'flex flex-shrink-0 items-start justify-start break-all rounded-t-md border border-b-0 border-secondary bg-secondary p-3 pr-5 font-mono text-sm font-medium tracking-wide',
          props.isCompact &&
            '@xl:max-w-[256px] @xl:rounded-s-md @xl:rounded-tr-none @xl:border-b @xl:border-r-0',
          props.cursorPointer && 'cursor-pointer'
        )}
      >
        <span className="mt-[2px] flex flex-row gap-2">
          <Icon className="h-4 w-4" />
          {PLAYGROUND_RESERVED_NAMES[props.name] ?? props.name}
        </span>
      </label>

      <div
        className={cn(
          'mb-2 max-h-[256px] overflow-auto rounded-md rounded-t-none border border-secondary bg-background/50 p-3 outline-none last-of-type:mb-0',
          props.isCompact && '@xl:mb-0 @xl:rounded-l-none @xl:rounded-tr-md',
          props.cursorPointer && 'cursor-pointer'
        )}
      >
        <MultimodalDropdown
          variant="inline"
          onUpload={(_, src) => {
            props.onChange?.(src);
          }}
          templateFormat={props.templateFormat ?? 'f-string'}
          mimeType={props.mimeType}
        >
          {props.value != null && typeof props.value === 'string' ? (
            <div className="flex max-w-full flex-row gap-2 overflow-hidden">
              <MultimodalContent
                contentType={props.mimeType}
                mimeSubtype={props.mimeSubtype}
                url={props.value}
                contentName={props.name}
                unstyled={true}
              />
              <Tooltip title="Remove content">
                <button
                  type="button"
                  className="h-fit rounded-md border border-primary p-1 transition duration-75 hover:bg-secondary-hover"
                  onClick={(e) => {
                    e.stopPropagation();
                    props.onChange?.(undefined);
                  }}
                >
                  <XIcon className="h-4 w-4" />
                </button>
              </Tooltip>
            </div>
          ) : (
            <MultimodalButton
              icon={PlusIcon}
              label={label}
              className="rounded-md"
            />
          )}
        </MultimodalDropdown>
      </div>
    </Fragment>
  );
}

export function PlaygroundPromptInputChatItem(props: {
  name: string;
  value: unknown | undefined;
  onChange?: (value: MessageUnionType[] | undefined) => void;
  isCompact: boolean;
  canEdit: boolean;
  cursorPointer: boolean;
}) {
  const id = useId();

  const [open, setOpen] = useState(false);

  const value = maybeGetArrayOfMessages(props.value) ?? [];

  return (
    <Fragment key={props.name}>
      <label
        htmlFor={id}
        className={cn(
          'flex flex-shrink-0 items-start justify-start break-all rounded-t-md border border-b-0 border-secondary bg-secondary p-3 pr-5 font-mono text-sm font-medium tracking-wide',
          props.isCompact &&
            '@xl:max-w-[256px] @xl:rounded-s-md @xl:rounded-tr-none @xl:border-b @xl:border-r-0',
          props.cursorPointer && 'cursor-pointer'
        )}
      >
        <span className="mt-[2px]">{`{${props.name}}`}</span>
      </label>

      <div
        className={cn(
          'mb-4 max-h-[256px] overflow-auto rounded-md rounded-t-none border border-secondary bg-background/50 p-3 outline-none last-of-type:mb-0',
          props.isCompact && '@xl:mb-0 @xl:rounded-l-none @xl:rounded-tr-md',
          props.cursorPointer && 'cursor-pointer'
        )}
        onClick={() => props.canEdit && setOpen(true)}
      >
        <div
          className={cn(
            'flex flex-col gap-3',
            props.canEdit && 'cursor-pointer'
          )}
        >
          {value && value.length ? (
            <>
              {value?.map((message, idx, lst) => {
                const content = getMessageContent(message);
                const kwargs = getMessageFields(message)?.additional_kwargs;

                return (
                  <Fragment key={idx}>
                    <div>
                      <strong className="text-sm font-semibold uppercase text-tertiary">
                        {getMessageType(message)}
                      </strong>
                      {content ? (
                        <p className="whitespace-pre-wrap break-words">
                          <RichTextView content={content} />
                        </p>
                      ) : kwargs ? (
                        <pre className="max-w-full whitespace-pre-wrap break-words">
                          {JSON.stringify(kwargs, null, 2)}
                        </pre>
                      ) : (
                        <p className="whitespace-pre-wrap break-words text-ls-gray-200">
                          Empty message
                        </p>
                      )}
                    </div>

                    {idx + 1 !== lst.length && (
                      <div className="h-px w-full bg-tertiary" />
                    )}
                  </Fragment>
                );
              })}
            </>
          ) : (
            <span className="text-ls-gray-200">
              No messages. Add a message...
            </span>
          )}
        </div>
      </div>
      {props.canEdit && (
        <Pane
          open={open}
          onClose={() => setOpen(false)}
          title={`{${props.name}}`}
        >
          <EditableChatInput
            messages={value}
            setMessages={(msgs) => props.onChange?.(msgs)}
            createOnEmpty
          />
        </Pane>
      )}
    </Fragment>
  );
}

export function PlaygroundPromptInputItem(props: {
  name: string;
  isCompact: boolean;
  canEdit: boolean;
  cursorPointer: boolean;
  templateFormat: TemplateFormat;
  value: string | null | undefined;
  onChange?: (value: string) => void;
}) {
  const id = useId();
  const [focus, setFocus] = useState(false);

  return (
    <Fragment key={props.name}>
      <label
        htmlFor={id}
        className={cn(
          'flex flex-shrink-0 items-start justify-start break-all rounded-t-md border border-b-0 border-secondary bg-background px-3 py-2 pr-1 font-mono text-xs font-medium tracking-wide',
          props.isCompact &&
            '@xl:max-w-[256px] @xl:rounded-s-md @xl:rounded-tr-none @xl:border-b @xl:border-r-0',
          focus && 'border-brand',
          props.cursorPointer && 'cursor-pointer'
        )}
      >
        <span className="mt-[2px]">{props.name}</span>
      </label>

      <div
        className={cn(
          'mb-4 max-h-[256px] overflow-auto rounded-md rounded-t-none border border-t-0 border-secondary bg-background px-3 pb-2 outline-none last-of-type:mb-0',
          props.isCompact &&
            '@xl:mb-0 @xl:rounded-l-none @xl:rounded-tr-md @xl:border-l-0 @xl:border-t @xl:pt-2',
          focus && 'border-brand',
          props.cursorPointer && 'cursor-pointer'
        )}
      >
        <PlainTextEditor
          id={id}
          value={props.value}
          onChange={props.onChange}
          onFocus={() => setFocus(true)}
          onBlur={() => setFocus(false)}
          placeholder="Enter variable value..."
          readOnly={!props.canEdit}
          cursorPointer={props.cursorPointer}
          textareaClassName="text-sm"
        />
      </div>
    </Fragment>
  );
}

export function PlaygroundPromptJSONInputItem({
  name,
  isCompact,
  canEdit,
  cursorPointer,
  value,
  onChange,
}: {
  name: string;
  isCompact: boolean;
  canEdit: boolean;
  cursorPointer: boolean;
  value: Record<string, unknown> | boolean | string;
  onChange?: (value: Record<string, unknown> | boolean | string) => void;
}) {
  const id = useId();

  const sectionTooltipTitle = (
    <div>
      <span>Data values for sections can take various forms:</span>
      <span>
        <ul>
          <li>
            <b>Boolean</b>: <code>false</code>, <code>true</code>
          </li>
          <li>
            <b>List</b>: <code>["a", "b", "c"]</code>
          </li>
          <li>
            <b>List of Objects</b>:{' '}
            <span>
              {'['}
              {'{'} "key": "value1" {'}'}
              {'{'} "key": "value2" {'}'}
              {']'}
            </span>
          </li>
        </ul>
        <br></br>
        <p>
          For more details, refer to{' '}
          <a
            href="https://mustache.github.io/"
            target="_blank"
            className="underline"
          >
            Mustache documentation
          </a>
          .
        </p>
      </span>
    </div>
  );

  return (
    <Fragment key={name}>
      <label
        htmlFor={id}
        className={cn(
          'flex flex-shrink-0 items-start justify-start break-all rounded-t-md border border-b-0 border-secondary bg-secondary p-3 pr-5 font-mono text-sm font-medium tracking-wide',
          isCompact &&
            '@xl:max-w-[256px] @xl:rounded-s-md @xl:rounded-tr-none @xl:border-b @xl:border-r-0',
          cursorPointer && 'cursor-pointer'
        )}
      >
        <div className="mt-[2px] flex flex-row gap-1">
          {name}
          <Tooltip title={sectionTooltipTitle}>
            <div className="my-auto cursor-pointer">
              <InfoCircleIcon className="h-4 w-4" />
            </div>
          </Tooltip>
        </div>
      </label>

      <div
        className={cn(
          'mb-4 max-h-[256px] overflow-auto rounded-md rounded-t-none border border-secondary bg-background/50 p-3 outline-none last-of-type:mb-0',
          isCompact && '@xl:mb-0 @xl:rounded-l-none @xl:rounded-tr-md',
          cursorPointer && 'cursor-pointer p-0'
        )}
      >
        <CodeWrapper value={value} onChange={onChange} canEdit={canEdit} />
      </div>
    </Fragment>
  );
}

const CodeWrapper = ({
  value,
  onChange,
  canEdit,
}: {
  value: Record<string, unknown> | boolean | string;
  onChange?: (value: Record<string, unknown> | boolean | string) => void;
  canEdit: boolean;
}) => {
  const memoizedCode = useMemo(
    () => (
      <CodeData
        value={value}
        onChange={onChange}
        language={'json'}
        placeholder="Enter variable value..."
        readOnly={!canEdit}
        variant="plain"
      />
    ),
    [value, canEdit, onChange]
  );

  return <div className="flex flex-col">{memoizedCode}</div>;
};
