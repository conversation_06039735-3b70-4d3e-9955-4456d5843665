import { TemplateFormat } from '@langchain/core/prompts';

import { FC, useCallback, useMemo } from 'react';

import { RunInputSchema } from '@/types/schema';

import { usePlaygroundStore } from '../PlaygroundHome/hooks/usePlaygroundStore';
import { objectToString, stringToJSON } from '../utils/Playground.utils';
import { VariableNameAndNestedKey } from '../utils/parsingPromptTemplates.utils';
import {
  PlaygroundPromptInputChatItem,
  PlaygroundPromptInputItem,
  PlaygroundPromptInputMultimodalItem,
  PlaygroundPromptJSONInputItem,
} from './PlaygroundPromptInput';
import { PromptVariable } from './hooks/usePlaygroundPromptTemplateVariables';

type TProps = {
  imageVariables: PromptVariable[];
  multimodalVariables: PromptVariable[];
  messagePlaceholders: string[];
  variablesRequiringJSONEditor: VariableNameAndNestedKey[];
  inputVars: { name: string; templateFormat: TemplateFormat }[];
  isCompact: boolean;
  input: RunInputSchema | undefined;
};

export const PlaygroundPromptInputVars: FC<TProps> = ({
  imageVariables,
  multimodalVariables,
  messagePlaceholders,
  variablesRequiringJSONEditor,
  inputVars,
  isCompact,
  input,
}) => {
  const { setSingleInput } = usePlaygroundStore();

  // Memoize the handlers to prevent unnecessary re-renders
  const handlersMap = useMemo(() => {
    const handlers = new Map<
      string,
      (value: string | boolean | Record<string, unknown>) => void
    >();

    inputVars.forEach(({ name }) => {
      handlers.set(
        name,
        (value: string | boolean | Record<string, unknown>) => {
          setSingleInput(name, value);
        }
      );
    });

    return handlers;
  }, [setSingleInput, inputVars]);

  const renderJsonInputItem = useCallback(
    (name: string) => {
      const varType = variablesRequiringJSONEditor.find((v) => v.name === name);
      const defaultValue = varType?.nestedKey
        ? { [varType.nestedKey]: '' }
        : { key: '' };
      const value = stringToJSON(input?.[name]) ?? defaultValue;

      return (
        <PlaygroundPromptJSONInputItem
          key={name}
          name={name}
          isCompact={isCompact}
          value={value}
          onChange={handlersMap.get(name)}
          canEdit={true}
          cursorPointer={false}
        />
      );
    },
    [isCompact, input, handlersMap, variablesRequiringJSONEditor]
  );

  return inputVars.map(({ name, templateFormat }) => {
    const variable =
      multimodalVariables.find((v) => v.name === name) ||
      imageVariables.find((v) => v.name === name);
    if (variable) {
      return (
        <PlaygroundPromptInputMultimodalItem
          key={name}
          name={name}
          isCompact={isCompact}
          value={input?.[name]}
          onChange={(value) => {
            setSingleInput(name, value);
          }}
          canEdit={true}
          cursorPointer={false}
          templateFormat={templateFormat}
          mimeType={variable.type}
          mimeSubtype={variable.subtype}
        />
      );
    }

    if (messagePlaceholders.includes(name)) {
      return (
        <PlaygroundPromptInputChatItem
          key={name}
          name={name}
          isCompact={isCompact}
          value={input?.[name]}
          onChange={(value) => {
            setSingleInput(name, value);
          }}
          canEdit={true}
          cursorPointer={false}
        />
      );
    }

    if (variablesRequiringJSONEditor.find((v) => v.name === name)) {
      return renderJsonInputItem(name);
    }

    return (
      <PlaygroundPromptInputItem
        key={name}
        name={name}
        isCompact={isCompact}
        value={objectToString(input?.[name])}
        onChange={(value) => {
          setSingleInput(name, value);
        }}
        canEdit={true}
        cursorPointer={false}
        templateFormat={templateFormat}
      />
    );
  });
};
