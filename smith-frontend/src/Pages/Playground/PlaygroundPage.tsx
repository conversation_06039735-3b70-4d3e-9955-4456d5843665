import { TemplateFormat } from '@langchain/core/prompts';
import { LinearProgress } from '@mui/joy';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import Breadcrumbs from '@/components/Breadcrumbs';
import { useExamples } from '@/hooks/useSwr';
import { EPromptType, PlaygroundSettingsSchema } from '@/types/schema';
import { useLocalStorageState } from '@/utils/use-local-storage-state.tsx';

import { DEFAULT_CHAT_MODEL_CONSTRUCTOR } from './Playground.constants';
import { PlaygroundMain } from './PlaygroundHome/PlaygroundMain';
import { useDefaultPromptConfig } from './PlaygroundHome/hooks/useDefaultPromptConfig';
import { PREFERRED_PROMPT_TEMPLATE_FORMAT_STORAGE_KEY } from './components/PromptSettings/PromptFormatSection';
import {
  getDefaultModelManifest,
  getDefaultPromptManifest,
} from './utils/Playground.utils';

export const DEFAULT_PROPS = (
  type: EPromptType,
  inputVar: string | null,
  variant: 'dataset-eval' | 'session-eval',
  defaultConfig: PlaygroundSettingsSchema | undefined,
  templateFormat: TemplateFormat,
  evaluatorName?: string
) => {
  const modelManifest = getDefaultModelManifest(
    defaultConfig?.settings ?? DEFAULT_CHAT_MODEL_CONSTRUCTOR
  );
  return {
    promptManifest: getDefaultPromptManifest({
      modelManifest,
      type,
      templateFormat,
      defaultInputVar: inputVar,
      structuredOutputVariant: variant,
      evaluatorName,
    }),
    modelManifest,
  };
};

function Create() {
  const [searchParams, setSearchParams] = useSearchParams();
  const type =
    (searchParams.get('type') as EPromptType | null) ?? EPromptType.CHAT;
  const datasetId = searchParams.get('datasetId') ?? undefined;

  const [shouldReset] = useState(searchParams.get('reset') ?? undefined);
  const [preferredPromptFormat] = useLocalStorageState<TemplateFormat>(
    PREFERRED_PROMPT_TEMPLATE_FORMAT_STORAGE_KEY,
    'f-string'
  );

  useEffect(() => {
    if (shouldReset) {
      setSearchParams((prev) => {
        prev.delete('reset');
        return prev;
      });
    }
  }, []);

  // use an example from the dataset to prepopulate the prompt with the correct variable name
  const { data: examplesData, isLoading: isExamplesLoading } = useExamples(
    datasetId ? { dataset: datasetId } : null,
    undefined,
    {
      keepPreviousData: false,
    }
  );
  const sampleExample = examplesData?.[0];
  const { inputs: exampleInputs } = sampleExample ?? {};

  const { defaultConfig, isLoading: isConfigsLoading } =
    useDefaultPromptConfig();

  const { promptManifest, modelManifest } = DEFAULT_PROPS(
    type,
    exampleInputs ? Object.keys(exampleInputs)[0] : null,
    datasetId ? 'dataset-eval' : 'session-eval',
    defaultConfig,
    preferredPromptFormat
  );

  return isExamplesLoading || isConfigsLoading ? (
    <div className="mx-2 mt-4">
      <LinearProgress />
    </div>
  ) : (
    <PlaygroundMain
      type={type}
      datasetId={datasetId}
      forceReset={!!shouldReset}
      promptManifest={promptManifest}
      modelManifest={modelManifest}
      {...(defaultConfig ? { options: defaultConfig.options } : {})}
      showChatCompletionToggle
    />
  );
}

export function PlaygroundPage() {
  return (
    <div className="flex h-screen flex-col">
      <div className="px-4 pt-3">
        <Breadcrumbs />
      </div>
      <Create />
    </div>
  );
}
