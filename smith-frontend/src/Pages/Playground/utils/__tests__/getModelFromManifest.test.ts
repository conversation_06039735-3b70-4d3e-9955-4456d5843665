import { SerializedConstructor } from '@langchain/core/load/serializable';

import { describe, expect, it } from 'vitest';

import { getModelFromManifest } from '../Playground.utils';

const manifestWithModelKwargs: SerializedConstructor = {
  lc: 1,
  type: 'constructor',
  id: ['langchain', 'schema', 'runnable', 'RunnableSequence'],
  kwargs: {
    first: {
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
      kwargs: {
        messages: [
          {
            lc: 1,
            type: 'constructor',
            id: ['langchain', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
            kwargs: {
              prompt: {
                lc: 1,
                type: 'constructor',
                id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
                kwargs: {
                  input_variables: [],
                  template_format: 'f-string',
                  template: 'You are a chatbot.',
                },
              },
            },
          },
        ],
        template_format: 'f-string',
        input_variables: [],
      },
    },
    last: {
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
      kwargs: {
        bound: {
          lc: 1,
          type: 'constructor',
          id: ['langchain', 'chat_models', 'openai', 'ChatOpenAI'],
          name: 'ChatOpenAI',
          kwargs: {
            model_kwargs: {
              extra_headers: {},
              reasoning_effort: 'medium',
            },
            openai_api_key: {
              id: ['OPENAI_API_KEY'],
              lc: 1,
              type: 'secret',
            },
            presence_penalty: 0,
            frequency_penalty: 0,
            top_p: 1,
            extra_headers: {},
            model: 'o1',
            stream_usage: true,
            temperature: 1,
          },
        },
        kwargs: {},
      },
    },
  },
};

const manifestWithoutModelKwargs: SerializedConstructor = {
  lc: 1,
  type: 'constructor',
  id: ['langchain', 'schema', 'runnable', 'RunnableSequence'],
  kwargs: {
    first: {
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
      kwargs: {
        messages: [
          {
            lc: 1,
            type: 'constructor',
            id: ['langchain', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
            kwargs: {
              prompt: {
                lc: 1,
                type: 'constructor',
                id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
                kwargs: {
                  input_variables: [],
                  template_format: 'f-string',
                  template: 'You are a chatbot.',
                },
              },
            },
          },
        ],
        template_format: 'f-string',
        input_variables: [],
      },
    },
    last: {
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
      kwargs: {
        bound: {
          lc: 1,
          type: 'constructor',
          id: ['langchain', 'chat_models', 'openai', 'ChatOpenAI'],
          name: 'ChatOpenAI',
          kwargs: {
            openai_api_key: {
              id: ['OPENAI_API_KEY'],
              lc: 1,
              type: 'secret',
            },
            presence_penalty: 0,
            frequency_penalty: 0,
            top_p: 1,
            extra_headers: {},
            model: 'o1',
            stream_usage: true,
            temperature: 1,
          },
        },
        kwargs: {},
      },
    },
  },
};

describe('getModelFromManifest', () => {
  it('should properly combine model_kwargs into kwargs and delete model_kwargs field', () => {
    const model = getModelFromManifest(manifestWithModelKwargs);
    expect(model).toEqual({
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
      kwargs: {
        bound: {
          lc: 1,
          type: 'constructor',
          id: ['langchain', 'chat_models', 'openai', 'ChatOpenAI'],
          name: 'ChatOpenAI',
          kwargs: {
            openai_api_key: {
              id: ['OPENAI_API_KEY'],
              lc: 1,
              type: 'secret',
            },
            presence_penalty: 0,
            frequency_penalty: 0,
            top_p: 1,
            reasoning_effort: 'medium',
            extra_headers: {},
            model: 'o1',
            stream_usage: true,
            temperature: 1,
          },
        },
        kwargs: {},
      },
    });
  });

  it('should handle a manifest with no model_kwargs', () => {
    const model = getModelFromManifest(manifestWithoutModelKwargs);
    expect(model).toEqual(manifestWithoutModelKwargs.kwargs.last);
  });

  it("should return null if it doesn't have last", () => {
    const model = getModelFromManifest({
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
      kwargs: {
        messages: [
          {
            lc: 1,
            type: 'constructor',
            id: ['langchain', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
            kwargs: {
              prompt: {
                lc: 1,
                type: 'constructor',
                id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
                kwargs: {
                  input_variables: [],
                  template_format: 'f-string',
                  template: 'You are a chatbot.',
                },
              },
            },
          },
        ],
        template_format: 'f-string',
        input_variables: [],
      },
    });
    expect(model).toBeNull();
  });
});
