import { describe, expect, it } from 'vitest';

import { PlaygroundInputSourceType } from '../../PlaygroundContext';
import { modifyInputForSubmit } from '../Playground.utils';

describe('modifyInputForSubmit', () => {
  it('should extract base64 from audio data URL', () => {
    const input = {
      audio: 'data:audio/wav;base64,ABC123',
    };
    const result = modifyInputForSubmit(
      input,
      PlaygroundInputSourceType.MANUAL
    );
    expect(result).toEqual({
      audio: 'ABC123',
    });
  });

  it('should return original input for dataset source type', () => {
    const input = {
      audio: 'data:audio/wav;base64,ABC123',
    };
    const result = modifyInputForSubmit(
      input,
      PlaygroundInputSourceType.DATASET
    );
    expect(result).toEqual(input);
  });

  it('should not modify non-audio data URLs', () => {
    const input = {
      image: 'data:image/png;base64,XYZ789',
      text: 'Hello world',
    };
    const result = modifyInputForSubmit(
      input,
      PlaygroundInputSourceType.MANUAL
    );
    expect(result).toEqual(input);
  });
});
