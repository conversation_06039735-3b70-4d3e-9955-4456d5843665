import { SerializedConstructor } from '@langchain/core/load/serializable';

import { ToolDefinition } from 'node_modules/@langchain/core/dist/language_models/base';
import { describe, expect, it } from 'vitest';

import { RunSchema } from '@/types/schema';

import { SerializedChatPromptTemplateMessagesKwarg } from '../../components/manifest/serialized/prompts/ManifestChatPromptTemplate.utils';
import {
  convertManifestToTuple,
  replaceTemplateVariables,
} from '../Playground.utils';
import { getStructuredOutputFromRun } from '../Playground.utils';
import {
  AnthropicToolDefinition,
  GeminiToolListDefinition,
  anthropicToOpenAIFormat,
  escapeFString,
  geminiToOpenAIFormat,
  optionsToOpenAIToolFormat,
  toOpenAIToolFormat,
} from '../Playground.utils';

describe('escapeFString', () => {
  it('returns the same string when no braces are present', () => {
    const input = 'Hello, world!';
    const result = escapeFString(input);
    expect(result).toEqual('Hello, world!');
  });

  it('escapes a string with only braces', () => {
    const input = '{}{}';
    const result = escapeFString(input);
    expect(result).toEqual('{{}}{{}}');
  });

  it('does not over-escape already escaped braces', () => {
    const input = '{{already escaped}}';
    const result = escapeFString(input);
    expect(result).toEqual('{{{{already escaped}}}}');
  });

  it('handles unicode characters correctly', () => {
    const input = '你好{world}!';
    const result = escapeFString(input);
    expect(result).toEqual('你好{{world}}!');
  });

  it('returns a new string instance', () => {
    const input = 'Hello {world}!';
    const result = escapeFString(input);
    expect(result).not.toBe(input);
  });

  it('handles very short strings correctly', () => {
    expect(escapeFString('{')).toEqual('{{');
    expect(escapeFString('}')).toEqual('}}');
    expect(escapeFString('a')).toEqual('a');
  });
});

describe('geminiToOpenAIFormat', () => {
  it('converts Gemini tool format to OpenAI format', () => {
    const geminiTools: GeminiToolListDefinition = [
      {
        functionDeclarations: [
          {
            name: 'testTool',
            description: 'A test tool',
            parameters: { type: 'object', properties: {} },
          },
          {
            name: 'anotherTool',
            description: 'Another test tool',
            parameters: { type: 'object', properties: {} },
          },
        ],
      },
    ];
    const result = geminiToOpenAIFormat(geminiTools);
    expect(result).toHaveLength(2);
    expect(result[0]).toHaveProperty('type', 'function');
    expect(result[0].function).toHaveProperty('name', 'testTool');
    expect(result[0]).toMatchObject({
      type: 'function',
      function: {
        name: 'testTool',
        description: 'A test tool',
        parameters: { type: 'object', properties: {} },
      },
    });

    expect(result[1]).toHaveProperty('type', 'function');
    expect(result[1].function).toHaveProperty('name', 'anotherTool');
    expect(result[1]).toMatchObject({
      type: 'function',
      function: {
        name: 'anotherTool',
        description: 'Another test tool',
        parameters: { type: 'object', properties: {} },
      },
    });
  });

  it('handles empty Gemini tool list', () => {
    const emptyGeminiTools: GeminiToolListDefinition = [
      { functionDeclarations: [] },
    ];
    const result = geminiToOpenAIFormat(emptyGeminiTools);
    expect(result).toHaveLength(0);
  });
});

describe('anthropicToOpenAIFormat', () => {
  it('converts Anthropic tool format to OpenAI format', () => {
    const anthropicTools: AnthropicToolDefinition[] = [
      {
        name: 'testTool',
        description: 'A test tool',
        input_schema: { type: 'object', properties: {} },
      },
      {
        name: 'anotherTool',
        description: 'Another test tool',
        input_schema: { type: 'object', properties: {} },
      },
    ];
    const result = anthropicToOpenAIFormat(anthropicTools);
    expect(result).toHaveLength(2);
    expect(result[0]).toHaveProperty('type', 'function');
    expect(result[0].function).toHaveProperty('name', 'testTool');
    expect(result[0]).toMatchObject({
      type: 'function',
      function: {
        name: 'testTool',
        description: 'A test tool',
        parameters: { type: 'object', properties: {} },
      },
    });

    expect(result[1]).toHaveProperty('type', 'function');
    expect(result[1].function).toHaveProperty('name', 'anotherTool');
    expect(result[1]).toMatchObject({
      type: 'function',
      function: {
        name: 'anotherTool',
        description: 'Another test tool',
        parameters: { type: 'object', properties: {} },
      },
    });
  });

  it('handles empty Anthropic tool list', () => {
    const result = anthropicToOpenAIFormat([]);
    expect(result).toHaveLength(0);
  });
});

describe('toOpenAIToolFormat', () => {
  it('returns OpenAI format for OpenAI formatted models', () => {
    const openAITool: ToolDefinition[] = [
      {
        type: 'function',
        function: {
          name: 'openAITool',
          description: 'An OpenAI tool',
          parameters: { type: 'object', properties: {} },
        },
      },
    ];

    const result = toOpenAIToolFormat(openAITool);
    expect(result).toEqual(openAITool);
  });
});

it('converts Anthropic format to OpenAI format for Bedrock Anthropic models', () => {
  const anthropicTools: AnthropicToolDefinition[] = [
    {
      name: 'anthropicTool',
      description: 'An Anthropic tool',
      input_schema: { type: 'object', properties: {} },
    },
  ];
  const result = toOpenAIToolFormat(anthropicTools);
  expect(result).toHaveLength(1);
  expect(result?.[0]).toHaveProperty('type', 'function');
  expect(result?.[0].function).toHaveProperty('name', 'anthropicTool');
});

it('converts Gemini format to OpenAI format for Gemini models', () => {
  const geminiTools: GeminiToolListDefinition = [
    {
      functionDeclarations: [
        {
          name: 'geminiTool',
          description: 'A Gemini tool',
          parameters: { type: 'object', properties: {} },
        },
      ],
    },
  ];
  const result = toOpenAIToolFormat(geminiTools);
  expect(result).toHaveLength(1);
  expect(result?.[0]).toHaveProperty('type', 'function');
  expect(result?.[0].function).toHaveProperty('name', 'geminiTool');
});

it('returns undefined for unsupported models in ToolDefinition format', () => {
  const result = toOpenAIToolFormat([]);
  expect(result).toBeUndefined();
});

describe('optionsToOpenAIToolFormat', () => {
  it('converts tools in options to OpenAI format', () => {
    const options = {
      tools: [
        {
          name: 'testTool',
          description: 'A test tool',
          input_schema: { type: 'object', properties: {} },
        },
      ],
      otherOption: 'value',
    };
    const result = optionsToOpenAIToolFormat(options);
    expect(result).toBeDefined();
    expect(result).toHaveProperty('tools');

    if (result === undefined) {
      throw new Error('Result is undefined');
    }

    expect(result.tools).toHaveLength(1);
    expect(result.tools[0]).toHaveProperty('type', 'function');
    expect(result.tools[0].function).toHaveProperty('name', 'testTool');
    expect(result).toHaveProperty('otherOption', 'value');
  });

  it('returns original options if no tools are present', () => {
    const options = { otherOption: 'value' };
    const result = optionsToOpenAIToolFormat(options);
    expect(result).toEqual(options);
  });

  it('handles empty tools array', () => {
    const options = { tools: [], otherOption: 'value' };
    const result = optionsToOpenAIToolFormat(options);
    expect(result).toHaveProperty('tools');
    if (result === undefined) {
      throw new Error('Result is undefined');
    }
    expect(result).toHaveProperty('otherOption', 'value');
  });
});

describe('getStructuredOutputFromRun', () => {
  it('returns null when no structured output format is present', () => {
    const run = {} as Partial<RunSchema>;
    const result = getStructuredOutputFromRun(run as RunSchema);
    expect(result).toBeNull();
  });

  it('extracts standard OSS format structured output', () => {
    const run: Partial<RunSchema> = {
      extra: {
        options: {
          ls_structured_output_format: {
            schema: { type: 'object', properties: { foo: { type: 'string' } } },
            kwargs: { method: 'json_schema', strict: true },
          },
        },
      },
    };

    const result = getStructuredOutputFromRun(run as RunSchema);

    expect(result).toEqual({
      schema_: { type: 'object', properties: { foo: { type: 'string' } } },
      structured_output_kwargs: { method: 'json_schema', strict: true },
    });
  });

  it('extracts OpenAI client format structured output', () => {
    const run: Partial<RunSchema> = {
      inputs: {
        response_format: {
          json_schema: {
            name: 'UserProfile',
            schema: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                age: { type: 'number' },
              },
            },
          },
        },
      },
    };

    const result = getStructuredOutputFromRun(run as RunSchema);

    expect(result).toEqual({
      schema_: {
        title: 'UserProfile',
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'number' },
        },
      },
    });
  });

  it('handles missing schema in OpenAI format gracefully', () => {
    const run: Partial<RunSchema> = {
      inputs: {
        response_format: {
          json_schema: {},
        },
      },
    };

    const result = getStructuredOutputFromRun(run as RunSchema);

    expect(result).toEqual({
      schema_: {
        title: undefined,
      },
    });
  });

  it('prioritizes OSS format over OpenAI format when both are present', () => {
    const run: Partial<RunSchema> = {
      extra: {
        options: {
          ls_structured_output_format: {
            schema: { type: 'object', properties: { foo: { type: 'string' } } },
            kwargs: { method: 'json_schema' },
          },
        },
      },
      inputs: {
        response_format: {
          json_schema: {
            name: 'UserProfile',
            schema: { type: 'object' },
          },
        },
      },
    };

    const result = getStructuredOutputFromRun(run as RunSchema);

    expect(result).toEqual({
      schema_: { type: 'object', properties: { foo: { type: 'string' } } },
      structured_output_kwargs: { method: 'json_schema' },
    });
  });
});

describe('replaceTemplateVariables', () => {
  const baseManifest: SerializedConstructor = {
    lc: 1,
    type: 'constructor',
    id: ['langsmith', 'playground', 'PromptPlayground'],
    kwargs: {
      first: {
        lc: 1,
        type: 'constructor',
        id: ['langchain_core', 'prompts', 'structured', 'StructuredPrompt'],
        kwargs: {
          messages: [
            {
              lc: 1,
              type: 'constructor',
              id: [
                'langchain_core',
                'prompts',
                'chat',
                'SystemMessagePromptTemplate',
              ],
              kwargs: {
                prompt: {
                  lc: 1,
                  type: 'constructor',
                  id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
                  kwargs: {
                    input_variables: [],
                    template_format: 'mustache',
                    template: 'Write your evaluator prompt here.',
                  },
                },
              },
            },
            {
              lc: 1,
              type: 'constructor',
              id: [
                'langchain_core',
                'prompts',
                'chat',
                'HumanMessagePromptTemplate',
              ],
              kwargs: {
                prompt: {
                  lc: 1,
                  type: 'constructor',
                  id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
                  kwargs: {
                    input_variables: ['inputs', 'outputs', 'reference_outputs'],
                    template_format: 'mustache',
                    template:
                      '<input>\n{{input}}\n</input>\n\n<output>\n{{output}}\n</output>\n\n<referenceOutput>\n{{referenceOutput}}\n</referenceOutput>',
                  },
                },
              },
            },
          ],
          template_format: 'mustache',
          input_variables: ['question'],
          schema_: {
            title: 'extract',
            description: "Extract information from the user's response.",
            type: 'object',
            properties: {
              dsfgsfdgsdfg: {
                type: 'integer',
                enum: [0, 1],
              },
            },
            required: ['dsfgsfdgsdfg'],
            strict: true,
            additionalProperties: false,
          },
        },
      },
      last: {
        lc: 1,
        type: 'constructor',
        id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
        kwargs: {
          bound: {
            lc: 1,
            type: 'constructor',
            id: ['langchain', 'chat_models', 'openai', 'ChatOpenAI'],
            kwargs: {
              temperature: 1,
              top_p: 1,
              presence_penalty: 0,
              frequency_penalty: 0,
              model: 'gpt-4.1-mini',
              extra_headers: {},
              openai_api_key: {
                id: ['OPENAI_API_KEY'],
                lc: 1,
                type: 'secret',
              },
            },
          },
          kwargs: {},
        },
      },
    },
  };

  it('should replace mustache template variables with mapped values', () => {
    const variableMapping = {
      input: 'foo.bar',
      output: 'baz.qux',
      referenceOutput: 'ref.output',
    };

    const result = replaceTemplateVariables(baseManifest, variableMapping);

    // Check that the template variables were replaced
    const template =
      result.kwargs.first.kwargs.messages[1].kwargs.prompt.kwargs.template;
    expect(template).toContain('{{foo.bar}}');
    expect(template).toContain('{{baz.qux}}');
    expect(template).toContain('{{ref.output}}');
    expect(template).not.toContain('{{input}}');
    expect(template).not.toContain('{{output}}');
    expect(template).not.toContain('{{referenceOutput}}');
  });

  it('should handle f-string format variables', () => {
    const manifestWithFString: SerializedConstructor = {
      ...baseManifest,
      kwargs: {
        ...baseManifest.kwargs,
        first: {
          ...baseManifest.kwargs.first,
          kwargs: {
            ...baseManifest.kwargs.first.kwargs,
            messages: [
              {
                ...baseManifest.kwargs.first.kwargs.messages[0],
                kwargs: {
                  prompt: {
                    ...baseManifest.kwargs.first.kwargs.messages[0].kwargs
                      .prompt,
                    kwargs: {
                      template_format: 'f-string',
                      template:
                        'Input: {input}\nOutput: {output}\nReference: {referenceOutput}',
                    },
                  },
                },
              },
            ],
          },
        },
      },
    };

    const variableMapping = {
      input: 'foo.bar',
      output: 'baz.qux',
      referenceOutput: 'ref.output',
    };

    const result = replaceTemplateVariables(
      manifestWithFString,
      variableMapping
    );

    // Check that the f-string variables were replaced
    const template =
      result.kwargs.first.kwargs.messages[0].kwargs.prompt.kwargs.template;
    expect(template).toContain('{foo.bar}');
    expect(template).toContain('{baz.qux}');
    expect(template).toContain('{ref.output}');
    expect(template).not.toContain('{input}');
    expect(template).not.toContain('{output}');
    expect(template).not.toContain('{referenceOutput}');
  });

  it('should not replace variables that are not in the mapping', () => {
    const variableMapping = {
      input: 'foo.bar',
    };

    const result = replaceTemplateVariables(baseManifest, variableMapping);

    // Check that only mapped variables were replaced
    const template =
      result.kwargs.first.kwargs.messages[1].kwargs.prompt.kwargs.template;
    expect(template).toContain('{{foo.bar}}');
    expect(template).toContain('{{output}}');
    expect(template).toContain('{{referenceOutput}}');
  });

  it('should handle complex f-string expressions', () => {
    const manifestWithComplexFString: SerializedConstructor = {
      ...baseManifest,
      kwargs: {
        ...baseManifest.kwargs,
        first: {
          ...baseManifest.kwargs.first,
          kwargs: {
            ...baseManifest.kwargs.first.kwargs,
            messages: [
              {
                ...baseManifest.kwargs.first.kwargs.messages[0],
                kwargs: {
                  prompt: {
                    ...baseManifest.kwargs.first.kwargs.messages[0].kwargs
                      .prompt,
                    kwargs: {
                      template_format: 'f-string',
                      template:
                        'Input: {input.upper()}\nOutput: {output if output else "No output"}\nReference: {referenceOutput}',
                    },
                  },
                },
              },
            ],
          },
        },
      },
    };

    const variableMapping = {
      input: 'foo.bar',
      output: 'baz.qux',
      referenceOutput: 'ref.output',
    };

    const result = replaceTemplateVariables(
      manifestWithComplexFString,
      variableMapping
    );

    // Check that complex expressions were preserved
    const template =
      result.kwargs.first.kwargs.messages[0].kwargs.prompt.kwargs.template;
    expect(template).toContain('{input.upper()}');
    expect(template).toContain('{output if output else "No output"}');
    expect(template).toContain('{ref.output}');
  });

  it('should handle empty variable mapping', () => {
    const result = replaceTemplateVariables(baseManifest, {});

    // Check that no variables were replaced
    const template =
      result.kwargs.first.kwargs.messages[1].kwargs.prompt.kwargs.template;
    expect(template).toContain('{{input}}');
    expect(template).toContain('{{output}}');
    expect(template).toContain('{{referenceOutput}}');
  });

  it('should handle null or undefined values in the manifest', () => {
    const manifestWithNulls: SerializedConstructor = {
      ...baseManifest,
      kwargs: {
        ...baseManifest.kwargs,
        first: {
          ...baseManifest.kwargs.first,
          kwargs: {
            ...baseManifest.kwargs.first.kwargs,
            messages: [
              {
                ...baseManifest.kwargs.first.kwargs.messages[0],
                kwargs: {
                  prompt: {
                    ...baseManifest.kwargs.first.kwargs.messages[0].kwargs
                      .prompt,
                    kwargs: {
                      template_format: 'f-string',
                      template: null,
                    },
                  },
                },
              },
            ],
          },
        },
      },
    };

    const variableMapping = {
      input: 'foo.bar',
    };

    const result = replaceTemplateVariables(manifestWithNulls, variableMapping);
    expect(result).toBeDefined();
  });
});

describe('convertManifestToTuple', () => {
  it('should convert a manifest with system and human messages to tuples', () => {
    const manifest: SerializedChatPromptTemplateMessagesKwarg = [
      {
        lc: 1,
        type: 'constructor',
        id: [
          'langchain_core',
          'prompts',
          'chat',
          'SystemMessagePromptTemplate',
        ],
        kwargs: {
          prompt: {
            lc: 1,
            type: 'constructor',
            id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
            kwargs: {
              template: 'Write your evaluator prompt here.',
              template_format: 'mustache',
              input_variables: [],
            },
          },
        },
      },
      {
        lc: 1,
        type: 'constructor',
        id: ['langchain_core', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
        kwargs: {
          prompt: {
            lc: 1,
            type: 'constructor',
            id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
            kwargs: {
              template:
                '<input>\n{{input}}\n</input>\n\n<output>\n{{output}}\n</output>\n\n<referenceOutput>\n{{referenceOutput}}\n</referenceOutput>',
              template_format: 'mustache',
              input_variables: ['input', 'output', 'referenceOutput'],
            },
          },
        },
      },
    ];

    const result = convertManifestToTuple(manifest);

    expect(result).toEqual([
      ['system', 'Write your evaluator prompt here.'],
      [
        'human',
        '<input>\n{{input}}\n</input>\n\n<output>\n{{output}}\n</output>\n\n<referenceOutput>\n{{referenceOutput}}\n</referenceOutput>',
      ],
    ]);
  });

  it('should handle a manifest with a single message', () => {
    const manifest: SerializedChatPromptTemplateMessagesKwarg = [
      {
        lc: 1,
        type: 'constructor',
        id: ['langchain_core', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
        kwargs: {
          prompt: {
            lc: 1,
            type: 'constructor',
            id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
            kwargs: {
              template:
                'Evaluate the following response:\n\nInput: {{input}}\nOutput: {{output}}',
              template_format: 'mustache',
              input_variables: ['input', 'output'],
            },
          },
        },
      },
    ];

    const result = convertManifestToTuple(manifest);

    expect(result).toEqual([
      [
        'human',
        'Evaluate the following response:\n\nInput: {{input}}\nOutput: {{output}}',
      ],
    ]);
  });

  it('should handle a manifest with empty messages array', () => {
    const manifest: SerializedChatPromptTemplateMessagesKwarg = [];

    const result = convertManifestToTuple(manifest);

    expect(result).toEqual([]);
  });

  it('should handle a manifest with messages containing template variables', () => {
    const manifest: SerializedChatPromptTemplateMessagesKwarg = [
      {
        lc: 1,
        type: 'constructor',
        id: ['langchain_core', 'prompts', 'chat', 'HumanMessagePromptTemplate'],
        kwargs: {
          prompt: {
            lc: 1,
            type: 'constructor',
            id: ['langchain_core', 'prompts', 'prompt', 'PromptTemplate'],
            kwargs: {
              template:
                'Compare the following responses:\n\nOriginal: {{referenceOutput}}\nGenerated: {{output}}\n\nContext: {{input}}',
              template_format: 'mustache',
              input_variables: ['referenceOutput', 'output', 'input'],
            },
          },
        },
      },
    ];

    const result = convertManifestToTuple(manifest);

    expect(result).toEqual([
      [
        'human',
        'Compare the following responses:\n\nOriginal: {{referenceOutput}}\nGenerated: {{output}}\n\nContext: {{input}}',
      ],
    ]);
  });
});
