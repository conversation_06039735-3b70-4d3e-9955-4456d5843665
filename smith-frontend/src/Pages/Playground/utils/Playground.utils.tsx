import { SerializedConstructor } from '@langchain/core/load/serializable';
import { TemplateFormat } from '@langchain/core/prompts';

import cloneDeep from 'lodash/cloneDeep';
import omit from 'lodash/omit';
import {
  FunctionDefinition,
  ToolDefinition,
} from 'node_modules/@langchain/core/dist/language_models/base';
import { useCallback, useMemo, useState } from 'react';

import {
  defaultEvaluatorPromptManifest,
  emptyChatPromptManifest,
  emptyO1ChatPromptManifest,
  emptyPromptManifest,
  emptyStructuredPromptManifest,
} from '@/Pages/HubPlayground/HubPlayground.utils';
import { DATA_PREFIX } from '@/components/RichTextEditor/constants';
import {
  EXTRACT_MIME_TYPE_FROM_DATA_URL,
  MIME_TYPES,
} from '@/components/RichTextEditor/constants';
import { EXTRACT_BASE64_FROM_DATA_URL } from '@/components/RichTextEditor/constants';
import { getInputVarsFromTemplate } from '@/components/RichTextEditor/utils/RichTextEditor.utils';
import { useS3Serialized } from '@/hooks/useSwr';
import { OpenAITool, simplifyToolChoice } from '@/utils/ai-tools';
import {
  getInvocationParams,
  isMessageLike,
  isStoredMessage,
  maybeGetArrayOfMessages,
} from '@/utils/messages';
import { isSerialized } from '@/utils/serialized';
import { xCount } from '@/utils/stringUtils';

import {
  EPromptType,
  ExampleSchemaWithRunsAndOptionalFields,
  MessageTuple,
  type RunSchema,
  StoredMessage,
} from '../../../types/schema';
import {
  CHAT_MODELS_FOR_PLAYGROUND,
  CHAT_MODEL_MAPPING,
  DEFAULT_CHAT_MODEL,
  DEFAULT_TEXT_MODEL,
  MODEL_DEFAULT_KWARGS,
  MODEL_SECRETS,
} from '../Playground.constants';
import {
  ExampleSchemaOrDraftWithEdited,
  PlaygroundInputSourceType,
  PlaygroundPromptType,
} from '../PlaygroundContext';
import { PlaygroundInputValueType } from '../PlaygroundHome/types';
import { applySecret } from '../components/manifest/ManifestSelectProvider';
import {
  manifestModelAndProvider,
  useInferManifestFromRun,
} from '../components/manifest/serialized/ManifestConstructor';
import { isAnyOSeriesModel } from '../components/manifest/serialized/chat_models/ManifestChatOpenAI.utils';
import {
  SerializedChatPromptTemplateMessagesKwarg,
  convertManifestToMessages,
} from '../components/manifest/serialized/prompts/ManifestChatPromptTemplate.utils';
import { PLAYGROUND_RESERVED_NAMES } from '../constants';
import { validateManifest } from '../manifest-validation';
import { ManifestOptions, PlaygroundManifestParts } from './types';

export type PlaygroundSubmitValues<TInput = unknown> = {
  input: TInput;
  manifest: RunSchema['serialized'];
  secrets: Record<string, string>;
  options: unknown;
  streaming: boolean;
  reference_example_id?: string;
  session_id?: string;
  hubOwner?: string;
  hubRepo?: string;
  hubCommit?: string;
};

const ALLOWED_RUN_TYPES = new Set(['llm', 'prompt']);

export function isPlaygroundAvailableForRun(
  run: RunSchema | null | undefined,
  manifest: SerializedConstructor | null
): boolean {
  if (run == null) return false;
  if (!ALLOWED_RUN_TYPES.has(run.run_type)) return false;
  if (manifest == null) return false;

  let hasNonImplemented = false;
  JSON.stringify(run.serialized, (_, value) => {
    hasNonImplemented = hasNonImplemented || value === 'not_implemented';
    return value;
  });
  if (hasNonImplemented) return false;

  return !validateManifest(manifest);
}

export function usePlaygroundAvailableForRun(
  run: RunSchema | null | undefined
) {
  const shouldUseS3Serialized =
    !run?.serialized && run?.s3_urls?.serialized?.presigned_url;
  const { data: statefulSerializedData, isLoading: isSerializedLoading } =
    useS3Serialized(
      shouldUseS3Serialized ? run?.s3_urls?.serialized.presigned_url : null
    );

  const serializedData = shouldUseS3Serialized
    ? statefulSerializedData
    : undefined;

  const manifest = useInferManifestFromRun(
    serializedData ?? run?.serialized,
    run?.extra,
    run?.name
  );

  return useMemo(
    () => ({
      isAvailable: isPlaygroundAvailableForRun(run, manifest),
      isLoading: isSerializedLoading,
    }),
    [run, manifest, isSerializedLoading]
  );
}

export function getPlaygroundSerializedWithFallback(
  run: RunSchema,
  available: boolean,
  manifest: SerializedConstructor | null
): SerializedConstructor | undefined {
  if (available) return manifest ?? undefined;
  if (run.run_type === 'llm') {
    const modelId =
      run.inputs?.messages != null
        ? 'langchain/chat_models/openai/ChatOpenAI'
        : 'langchain/llms/openai/OpenAI';

    const kwargs =
      modelId in MODEL_DEFAULT_KWARGS ? MODEL_DEFAULT_KWARGS[modelId] : {};

    return {
      lc: 1,
      type: 'constructor',
      id: modelId.split('/'),
      kwargs: kwargs,
    };
  }

  return undefined;
}

export function usePlaygroundSerializedWithFallback(run: RunSchema) {
  const { isAvailable: available } = usePlaygroundAvailableForRun(run);
  const manifest = useInferManifestFromRun(
    run?.serialized,
    run?.extra,
    run?.name
  );
  return useMemo(
    () => getPlaygroundSerializedWithFallback(run, available, manifest),
    [run, available, manifest]
  );
}

export function normalizeKwargsName(value: string) {
  let newValue = value
    .replace('llm', 'LLM')
    .replace('openai', 'OpenAI')
    .replace('api', 'API');

  newValue = newValue
    .split('_')
    .map((i) => i[0].toUpperCase() + i.slice(1))
    .join(' ');
  return newValue;
}

// TODO: inline this function everywhere
export function usePlaygroundManifest<
  TManifest = SerializedConstructor
>(initialState: { manifest: TManifest }) {
  const [manifest, setManifest] = useState<TManifest>(initialState.manifest);
  return { manifest, setManifest };
}

export function isRunnablePrompt(manifestId: string[]) {
  const promptPlaygroundManifestId = [
    'langchain',
    'schema',
    'runnable',
    'RunnableSequence',
  ];
  return (
    manifestId.length === promptPlaygroundManifestId.length &&
    manifestId.every(
      (value, index) => value === promptPlaygroundManifestId[index]
    )
  );
}

export function isPromptPlaygroundManifest(manifestId: string[]) {
  const promptPlaygroundManifestId = [
    'langsmith',
    'playground',
    'PromptPlayground',
  ];
  return (
    manifestId.length === promptPlaygroundManifestId.length &&
    manifestId.every(
      (value, index) => value === promptPlaygroundManifestId[index]
    )
  );
}

export function getModelFromManifest(
  manifest: SerializedConstructor
): SerializedConstructor | null {
  if (!manifest.id || !manifest.kwargs) {
    return null;
  }

  if (
    isRunnablePrompt(manifest.id) ||
    isPromptPlaygroundManifest(manifest.id)
  ) {
    const last = manifest.kwargs.last;
    if (!last) {
      return null;
    }

    // model_kwargs contains all of the invocation params that were unrecognized
    // so spread them here with the kwargs to ensure they're recognized in the playground
    // and then remove the model_kwargs field itself
    if (last.kwargs?.bound?.kwargs) {
      const { model_kwargs, ...rest } = last.kwargs.bound.kwargs;
      return {
        ...last,
        kwargs: {
          ...last.kwargs,
          bound: {
            ...last.kwargs.bound,
            kwargs: {
              ...rest,
              ...(model_kwargs || {}),
            },
          },
        },
      };
    }
    return last;
  }

  return null;
}

export function getPromptManifest(
  manifest: SerializedConstructor
): SerializedConstructor {
  if (
    isRunnablePrompt(manifest.id) ||
    isPromptPlaygroundManifest(manifest.id)
  ) {
    return manifest.kwargs.first;
  }

  return manifest;
}

export function getTemplateNameFromPromptManifest(
  manifest: SerializedConstructor
): string {
  return manifest.id.at(-1) ?? '';
}

export function getToolNameList(options: Record<string, unknown>): string[] {
  if (!options.tools || !Array.isArray(options.tools)) {
    return [];
  }
  return options.tools.map((tool) => tool?.function?.name ?? '');
}

export function isFunctionDefinition(obj: unknown): obj is FunctionDefinition {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  const candidate = obj as FunctionDefinition;

  if (typeof candidate.name !== 'string') {
    return false;
  }

  if (
    typeof candidate.parameters !== 'object' ||
    candidate.parameters === null
  ) {
    return false;
  }

  // Check description (optional)
  if (
    'description' in candidate &&
    candidate.description !== undefined &&
    candidate.description !== null &&
    typeof candidate.description !== 'string'
  ) {
    return false;
  }

  return true;
}

export function isToolDefinition(tool: unknown): tool is ToolDefinition {
  if (typeof tool !== 'object' || tool === null) {
    return false;
  }

  const candidate = tool as ToolDefinition;

  if (candidate.type !== 'function') {
    return false;
  }

  if (!isFunctionDefinition(candidate.function)) {
    return false;
  }

  return true;
}

export function isAnthropicToolDefinition(
  tool: unknown
): tool is AnthropicToolDefinition {
  if (!tool || typeof tool !== 'object') {
    return false;
  }
  return 'name' in tool && 'description' in tool && 'input_schema' in tool;
}

export function isGeminiToolListDefinition(
  tool: unknown
): tool is GeminiToolListDefinition {
  if (!Array.isArray(tool) || tool.length !== 1) {
    return false;
  }

  const item = tool[0] as unknown;

  if (typeof item !== 'object' || !item) {
    return false;
  }

  if (
    'functionDeclarations' in item == false ||
    !Array.isArray(item?.functionDeclarations)
  ) {
    return false;
  }

  return item.functionDeclarations.every(
    (func) =>
      typeof func === 'object' &&
      func !== null &&
      'name' in func &&
      typeof func.name === 'string' &&
      'description' in func &&
      typeof func.description === 'string' &&
      'parameters' in func &&
      typeof func.parameters === 'object' &&
      func.parameters !== null
  );
}

export function stringToJSON(value: unknown) {
  if (typeof value == 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  } else {
    return value;
  }
}

export function objectToString(value: object | string): string {
  if (typeof value == 'object') {
    return JSON.stringify(value);
  } else {
    return value;
  }
}

export function escapeFString(input: string): string {
  let result = '';

  for (let i = 0; i < input.length; i++) {
    if (input[i] === '{' || input[i] === '}') {
      // Escape all braces by doubling them
      result += input[i] + input[i];
    } else {
      // Any other character, add as is
      result += input[i];
    }
  }

  return result;
}

export function escapedRun(run: RunSchema): RunSchema {
  return {
    ...run,
    inputs: {
      ...run.inputs,
      messages: maybeGetArrayOfMessages(run.inputs?.messages)?.map(
        (message) => {
          if (isMessageLike(message) && 'kwargs' in message) {
            const content = message.kwargs.content;
            return {
              ...message,
              kwargs: {
                ...message?.kwargs,
                content:
                  typeof content === 'string'
                    ? escapeFString(content)
                    : content,
              },
            };
          } else if (isStoredMessage(message)) {
            return {
              ...message,
              data: {
                ...message.data,
                content:
                  typeof message.data.content === 'string'
                    ? escapeFString(message.data.content)
                    : message.data.content,
              },
            };
          }
          return message;
        }
      ),
    },
  };
}

export type GeminiToolListDefinition = [
  {
    functionDeclarations: {
      name: string;
      description: string;
      parameters: Record<string, unknown>;
    }[];
  }
];

export function geminiToOpenAIFormat(
  tools: GeminiToolListDefinition
): ToolDefinition[] {
  return tools[0].functionDeclarations.map((declaration) => ({
    type: 'function',
    function: {
      name: declaration.name,
      description: declaration.description,
      parameters: declaration.parameters,
    },
  }));
}

export type AnthropicToolDefinition = {
  name: string;
  description: string;
  input_schema: Record<string, unknown>;
};

export function anthropicToOpenAIFormat(
  tools: AnthropicToolDefinition[]
): ToolDefinition[] {
  return tools.map((tool) => ({
    type: 'function',
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.input_schema,
    },
  }));
}

export function toOpenAIToolFormat(
  tools: AnthropicToolDefinition[] | GeminiToolListDefinition | ToolDefinition[]
): ToolDefinition[] | undefined {
  if (tools.length === 0) {
    return undefined;
  }

  if (tools.every(isToolDefinition)) {
    return tools as ToolDefinition[];
  } else if (tools.every(isAnthropicToolDefinition)) {
    return anthropicToOpenAIFormat(tools as AnthropicToolDefinition[]);
  } else if (isGeminiToolListDefinition(tools)) {
    return geminiToOpenAIFormat(tools as GeminiToolListDefinition);
  } else {
    console.error('Model not supported');
    return undefined;
  }
}

export function optionsToOpenAIToolFormat(
  options: Record<string, any> | undefined
): Record<string, any> | undefined {
  if (options && 'tools' in options && Array.isArray(options.tools)) {
    const convertedTools = toOpenAIToolFormat(options.tools);
    return {
      ...options,
      tools: convertedTools,
    };
  }
  return options;
}

export const defaultChatModelManifest = applySecret(
  {
    lc: 1,
    type: 'constructor',
    id: DEFAULT_CHAT_MODEL.split('/'),
    kwargs: MODEL_DEFAULT_KWARGS[DEFAULT_CHAT_MODEL],
  },
  MODEL_SECRETS[DEFAULT_CHAT_MODEL]
);

export const defaultCompletionModelManifest = applySecret(
  {
    lc: 1,
    type: 'constructor',
    id: DEFAULT_TEXT_MODEL.split('/'),
    kwargs: {},
  },
  MODEL_SECRETS[DEFAULT_TEXT_MODEL]
);

const PROVIDERS_WITHOUT_TOOL_SUPPORT = [
  'Fireworks',
  'VertexAI',
  'Bedrock',
  'ChatDeepSeek',
];
const MODELS_WITHOUT_TOOL_SUPPORT = ['o1-preview', 'o1-mini'];

export function supportsTools(
  modelAndProvider: ReturnType<typeof manifestModelAndProvider>
): boolean {
  if (modelAndProvider === null || modelAndProvider.provider === null) {
    return false;
  }

  const { provider, model } = modelAndProvider;

  // only allow tools with chat models
  if (!Object.keys(CHAT_MODEL_MAPPING).includes(provider)) {
    return false;
  }

  // Check for Anthropic models below Claude 3
  if (provider === 'ChatAnthropic') {
    const version = Number(model?.split('-')?.at(1));
    if (version < 3) {
      return false;
    }
  }

  // Check for non-Anthropic Bedrock models
  if (provider === 'ChatBedrock' && model?.split('.')?.at(0) !== 'anthropic') {
    return false;
  }

  if (model && MODELS_WITHOUT_TOOL_SUPPORT.includes(model)) {
    return false;
  }

  return !PROVIDERS_WITHOUT_TOOL_SUPPORT.includes(provider);
}

// Currently all providers with tool support have support for specific tool choice, leaving this here in case there's a new provider that doesn't support tool choice
const PROVIDERS_WITHOUT_SPECIFIC_TOOL_CHOICE_SUPPORT: string[] = [];

// specifying a SPECIFIC tool as the tool choice (i.e. forcing the LLM to call a specific tool)
// does not give an error
export function supportsEnforcedToolSelection(
  modelAndProvider: ReturnType<typeof manifestModelAndProvider>
) {
  if (modelAndProvider === null || modelAndProvider.provider === null) {
    return false;
  }
  return (
    supportsTools(modelAndProvider) &&
    !PROVIDERS_WITHOUT_SPECIFIC_TOOL_CHOICE_SUPPORT.includes(
      modelAndProvider.provider
    )
  );
}

const PROVIDERS_WITHOUT_TOOL_CHOICE_PARAM_SUPPORT = ['BedrockChat'];

// specifying "tool_choice" does not give error
export function supportsToolChoiceParameter(
  modelAndProvider: ReturnType<typeof manifestModelAndProvider>
) {
  if (modelAndProvider === null || modelAndProvider.provider === null) {
    return false;
  }
  return (
    supportsTools(modelAndProvider) &&
    !PROVIDERS_WITHOUT_TOOL_CHOICE_PARAM_SUPPORT.includes(
      modelAndProvider.provider
    )
  );
}

export function constructInitialOptions(
  run: Pick<RunSchema, 'extra' | 'inputs'>
) {
  const extra = run.extra;
  if (extra == null) return {};
  const params = getInvocationParams(run);
  // Spread to avoid mutating run.extra.options
  let baseOptions = { ...(extra.options ?? {}) };

  // Python version does not send functions and function_call in extra.options
  // instead, they send them in extra.invocation_params
  if (params != null && typeof params === 'object') {
    if (params.functions != null) {
      baseOptions = {
        ...baseOptions,
        functions: params.functions,
      };
    }

    if (params.function_call != null) {
      baseOptions = {
        ...baseOptions,
        function_call: params.function_call,
      };
    }

    if (params.tools != null) {
      baseOptions = {
        ...baseOptions,
        tools: params.tools,
      };
    }

    if (params.tool_choice != null) {
      baseOptions = {
        ...baseOptions,
        tool_choice: simplifyToolChoice(params.tool_choice),
      };
    }

    if (params.parallel_tool_calls != null) {
      baseOptions = {
        ...baseOptions,
        parallel_tool_calls: params.parallel_tool_calls,
      };
    }
  }

  // LangchainJS does support passing StructuredTool in extra.options,
  // which we don't support in LangSmith Playground
  if (
    Array.isArray(baseOptions.tools) &&
    baseOptions.tools.every(isSerialized)
  ) {
    if (params.tools != null) {
      baseOptions['tools'] = params.tools;
    } else {
      delete baseOptions['tools'];
    }
  }

  // Standard OSS format for sending structured output schema and kwargs.
  // If present should be extracted and added to the prompt, not passed in as options.
  if ('ls_structured_output_format' in baseOptions) {
    // Let StructuredPrompt handle setting the tools if tool calling is being used
    // under the hood for output structuring.
    if (
      baseOptions.ls_structured_output_format.kwargs?.method ==
      'function_calling'
    ) {
      delete baseOptions.tools;
      delete baseOptions.tool_choice;
      delete baseOptions.parallel_tool_calls;
    }
    delete baseOptions.ls_structured_output_format;
  }

  return baseOptions;
}

export function createPlaygroundManifest(
  promptManifest: SerializedConstructor,
  manifestForLast: SerializedConstructor
): SerializedConstructor {
  return {
    lc: 1,
    type: 'constructor',
    id: ['langsmith', 'playground', 'PromptPlayground'],
    kwargs: {
      first: promptManifest,
      last: manifestForLast,
    },
  };
}

export function createDefaultPromptManifest({
  type,
  templateFormat,
  isO1,
  defaultInputVar,
  structuredOutputVariant,
  evaluatorName,
}: {
  type?: EPromptType;
  templateFormat?: TemplateFormat;
  isO1?: boolean;
  defaultInputVar: string | null;
  structuredOutputVariant?: 'dataset-eval' | 'session-eval';
  evaluatorName?: string;
}): SerializedConstructor {
  if (evaluatorName) {
    return defaultEvaluatorPromptManifest(evaluatorName);
  }
  if (isO1 && type !== EPromptType.INSTRUCT) {
    return emptyO1ChatPromptManifest(defaultInputVar, templateFormat);
  }
  switch (type) {
    case EPromptType.INSTRUCT:
      return emptyPromptManifest(templateFormat);
    case EPromptType.STRUCTURED:
      return emptyStructuredPromptManifest(
        structuredOutputVariant,
        defaultInputVar,
        templateFormat
      );
    case EPromptType.CHAT:
    default:
      return emptyChatPromptManifest(defaultInputVar, templateFormat);
  }
}

export const getDefaultModelManifest = (model: SerializedConstructor) =>
  ({
    lc: 1,
    type: 'constructor',
    id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
    kwargs: {
      bound: applySecret(model, MODEL_SECRETS[model.id.join('/')]),
      kwargs: {},
    },
  } as SerializedConstructor);

export function getStructuredOutputFromRun(run: RunSchema) {
  // Standard OSS format
  if (run.extra?.options?.ls_structured_output_format) {
    return {
      schema_: run.extra.options.ls_structured_output_format.schema,
      structured_output_kwargs:
        run.extra.options.ls_structured_output_format.kwargs,
    };
  }
  // OpenAI Structured Output via client
  if (
    run.inputs?.response_format?.json_schema &&
    typeof run.inputs?.response_format?.json_schema === 'object'
  ) {
    const { name, schema } = run.inputs.response_format.json_schema;
    return {
      schema_: {
        title: name,
        ...schema,
      },
    };
  }
  return null;
}

export function createPromptManifestFromRun(
  run: RunSchema,
  type: EPromptType
): SerializedConstructor {
  if (type === EPromptType.CHAT) {
    // OpenAI Structured Output via client
    const structuredOutput = getStructuredOutputFromRun(run);
    if (structuredOutput) {
      return {
        lc: 1,
        type: 'constructor',
        id: ['langchain_core', 'prompts', 'structured', 'StructuredPrompt'],
        kwargs: {
          messages: maybeGetArrayOfMessages(run.inputs?.messages) ?? [],
          template_format: 'f-string',
          input_variables: [],
          ...structuredOutput,
        },
      };
    }
    return {
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
      kwargs: {
        messages: maybeGetArrayOfMessages(run.inputs?.messages) ?? [],
        template_format: 'f-string',
        input_variables: [],
      },
    };
  }

  return {
    lc: 1,
    type: 'constructor',
    id: ['langchain', 'prompts', 'prompt', 'PromptTemplate'],
    kwargs: {
      template: run.inputs?.prompt ?? run.inputs?.prompts?.[0] ?? '',
      template_format: 'f-string',
      input_variables: [],
    },
  };
}

export const changeTemplateFormat = (
  manifest: SerializedConstructor,
  newFormat: TemplateFormat
) => {
  let inputVars: string[] = [];
  if (manifest.kwargs.first.kwargs.messages) {
    const updatedMessages = manifest.kwargs.first.kwargs.messages.map(
      (message) => {
        // this could be an array of text and images ex. [text, image, text] if the message is hi <image> there
        if (Array.isArray(message.kwargs.prompt)) {
          return {
            ...message,
            kwargs: {
              ...message.kwargs,
              prompt: message.kwargs.prompt.map((prompt) => {
                inputVars =
                  typeof prompt.kwargs.template === 'string'
                    ? getInputVarsFromTemplate(
                        prompt.kwargs.template,
                        newFormat
                      )
                    : prompt.kwargs.input_variables;
                return {
                  ...prompt,
                  kwargs: {
                    ...prompt.kwargs,
                    template_format: newFormat,
                    input_variables: inputVars,
                  },
                };
              }),
            },
          };
        } else {
          if (message.kwargs.prompt) {
            inputVars =
              typeof message.kwargs.prompt.kwargs.template === 'string'
                ? getInputVarsFromTemplate(
                    message.kwargs.prompt.kwargs.template,
                    newFormat
                  )
                : message.kwargs.prompt.kwargs.input_variables;
            return {
              ...message,
              kwargs: {
                ...message.kwargs,
                prompt: {
                  ...message.kwargs.prompt,
                  kwargs: {
                    ...message.kwargs.prompt.kwargs,
                    template_format: newFormat,
                    input_variables: inputVars,
                  },
                },
              },
            };
          } else {
            // This message has an incomplete variable and is therefore a Message instead of a prompt template
            // Which means it doesn't have a template format to change
            // ex. this is an AIMessage instead of an AIMessagePromptTemplate (see ManifestChatPromptTemplate.utils.tsx)
            return message;
          }
        }
      }
    );
    return {
      ...manifest,
      kwargs: {
        ...manifest.kwargs,
        first: {
          ...manifest.kwargs.first,
          kwargs: {
            ...manifest.kwargs.first.kwargs,
            messages: updatedMessages,
            input_variables: inputVars,
            template_format: newFormat,
          },
        },
      },
    };
  } else {
    const inputVars = getInputVarsFromTemplate(
      manifest.kwargs.first.kwargs.template,
      newFormat
    );
    return {
      ...manifest,
      kwargs: {
        ...manifest.kwargs,
        first: {
          ...manifest.kwargs.first,
          kwargs: {
            ...manifest.kwargs.first.kwargs,
            template_format: newFormat,
            input_variables: inputVars,
          },
        },
      },
    };
  }
};

export function getPlaygroundStateWithReplacedFetchedRuns(
  prompts?: PlaygroundPromptType[],
  fetchedRuns?: RunSchema[]
) {
  const replacedRunsPlaygroundState = prompts
    ? prompts.map((p) => ({
        ...p,
        runs:
          p.runs?.map((r) => {
            const fetchedRun = fetchedRuns?.find((fr) => fr.id === r.id);
            return fetchedRun ?? r;
          }) ?? [],
      }))
    : undefined;
  return replacedRunsPlaygroundState;
}

export function convertAttachmentNameToTemplateVar(name: string) {
  return name.replace('attachment.', '').replaceAll('.', '_');
}

export const getExampleRunsFromPlaygroundState = ({
  isCreatingNewDataset,
  prompts,
  examples,
  datasetId,
  fetchedRuns,
  input,
}: {
  isCreatingNewDataset: boolean;
  prompts?: PlaygroundPromptType[];
  examples?: ExampleSchemaOrDraftWithEdited[];
  datasetId?: string | null;
  fetchedRuns?: RunSchema[];
  input?: Record<string, PlaygroundInputValueType>;
}) => {
  const replacedRunsPlaygroundState = getPlaygroundStateWithReplacedFetchedRuns(
    prompts,
    fetchedRuns
  );

  // This is for the case where we are running over a dataset
  let exampleRuns: Array<ExampleSchemaWithRunsAndOptionalFields> | undefined =
    examples?.map((e) => {
      const runs =
        replacedRunsPlaygroundState?.flatMap((p) => {
          const inputVariables = getInputVariablesFromPrompt(p);
          const missingInputVars = inputVariables.filter(
            (inputVar) =>
              !(
                inputVar in PLAYGROUND_RESERVED_NAMES ||
                inputVar in e.inputs ||
                Object.keys(e.attachment_urls ?? {})
                  .map((k) => convertAttachmentNameToTemplateVar(k))
                  .includes(inputVar)
              )
          );

          return missingInputVars.length > 0 && p.runs?.length === 0
            ? [
                {
                  id: '',
                  status: null,
                  run_type: '',
                  name: '',
                  start_time: '',
                  dotted_order: '',
                  trace_id: '',
                  app_path: '',
                  session_id: p.sessionId || p.columnId,
                  error: `Missing ${xCount(
                    'input variable',
                    missingInputVars.length
                  )} from prompt:\n${missingInputVars.map((m) => ` ${m}`)}`,
                },
              ]
            : p.runs && p.runs.length > 0
            ? p.runs.filter((r) => r.reference_example_id === e.id)
            : [];
        }) ?? [];

      return {
        ...e,
        runs,
      };
    });

  if ((!datasetId || !exampleRuns) && !isCreatingNewDataset) {
    exampleRuns = replacedRunsPlaygroundState
      ? [
          {
            id: 'E6B84553-9F8E-4E32-93F1-D8D70E075D73', // In the case of a manual prompt, there will only be one example and we need the id to stay consistent, so we use an arbitrary id.
            // In the future if we ever allow adding multiple manual inputs, we will need to assign them ids.
            inputs: input ?? {},
            outputs: {},
            runs:
              replacedRunsPlaygroundState?.flatMap((p) =>
                p.runs.map((r) => {
                  const foundRun = fetchedRuns?.find((fr) => fr.id === r.id);
                  if (foundRun) {
                    return {
                      ...foundRun,
                      session_id: p.columnId,
                    };
                  }
                  return r;
                })
              ) ?? [],
          },
        ]
      : [];
  }

  // add in check for if the example has the right input variables

  return exampleRuns;
};

export function needsToFetchFeedbackKeys(
  runs?: RunSchema[],
  evaluatorFeedbackKeys?: string[]
): boolean {
  if (!runs || !evaluatorFeedbackKeys) {
    return false;
  }

  return runs.some((r) =>
    evaluatorFeedbackKeys.some((key) => {
      return !r.feedback_stats?.[key];
    })
  );
}

// for structured output runs, we need to format the output to match the expected format (for frontend component rendering)
// chat outputs give us { outputs: { output: { ... } } } and structured output gives us { outputs: { ... } }
// this is because langchain requires outputs to be a dict, so if the output is a base message type or other pydantic object
// it will be wrapped with an output key in the response
export function formatRunOutput(run: RunSchema) {
  return {
    ...run,
    outputs: (run.outputs?.output
      ? run.outputs
      : { output: run.outputs }) as RunSchema['outputs'],
  };
}

export function isChatModel(model: SerializedConstructor) {
  // doesn't have kwargs if not_implemented
  if (model.kwargs) {
    if (model.kwargs?.bound?.id) {
      return CHAT_MODELS_FOR_PLAYGROUND.includes(
        model.kwargs.bound.id.join('/')
      );
    }
  } else if (model.id?.at(-1) === 'JsonOutputKeyToolsParser') {
    // structured prompt
    return true;
  }

  // unsure what structure this would be
  return false;
}

export function getModelForPrompt(
  runnableOrPromptManifest: SerializedConstructor
): [boolean, SerializedConstructor] {
  const promptManifest = getPromptManifest(runnableOrPromptManifest);
  const promptTemplate = getTemplateNameFromPromptManifest(promptManifest);
  const isChat = ['ChatPromptTemplate', 'StructuredPrompt'].includes(
    promptTemplate
  );

  const model = getModelFromManifest(runnableOrPromptManifest);
  if (model) {
    return [isChat, model];
  }

  const modelId = isChat ? DEFAULT_CHAT_MODEL : DEFAULT_TEXT_MODEL;
  const modelKwargs =
    modelId in MODEL_DEFAULT_KWARGS ? MODEL_DEFAULT_KWARGS[modelId] : {};
  return [
    isChat,
    {
      lc: 1,
      type: 'constructor',
      id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
      kwargs: {
        bound: applySecret(
          {
            lc: 1,
            type: 'constructor',
            id: modelId?.split('/'),
            kwargs: modelKwargs,
          },
          MODEL_SECRETS[modelId]
        ),
        kwargs: {},
      },
    },
  ];
}

export function getInputVariables(manifest: SerializedConstructor) {
  const inputVars: string[] = [
    ...new Set<string>(manifest.kwargs.first?.kwargs?.input_variables ?? []),
  ];
  return inputVars;
}

// This class is used to signal that the evaluator pane is open in playground - if so, we should block cmd+enter from running the playground.
export const useBlockRunOnEvaluatorPaneOpen = () => {
  const setOpen = useCallback((open: boolean) => {
    if (open) {
      document.body.classList.add('evaluators-pane');
    } else {
      document.body.classList.remove('evaluators-pane');
    }
  }, []);

  const isOpen = useMemo(
    () => document.body.classList.contains('evaluators-pane'),
    []
  );

  return {
    setOpen,
    isOpen,
  };
};

export const getDatasetSplits = (datasetSplits?: string[]) => {
  return datasetSplits?.length === 0 ? undefined : datasetSplits;
};

export const hasProxyProvider = (
  manifest: SerializedConstructor | undefined
) => {
  return !!manifest?.kwargs.last?.kwargs?.bound?.kwargs.base_url;
};

export const DEFAULT_EXPERIMENT_NAME = 'pg';
export const createExperimentName = ({
  repoHandle,
  modelManifest,
  customExperimentName = DEFAULT_EXPERIMENT_NAME,
}: {
  repoHandle?: string;
  modelManifest?: SerializedConstructor;
  customExperimentName?: string;
}) => {
  const separator = '::';
  const parts = [customExperimentName];

  if (repoHandle) {
    parts.push(repoHandle);
  }

  if (modelManifest) {
    const model = getModelNameFromRunnableBinding(modelManifest);
    if (model) {
      parts.push(model);
    }
  }

  return parts.join(separator);
};

// requires a manifest with first in kwargs
export function promptTypeFromManifest(manifest: SerializedConstructor) {
  return manifest.kwargs.first.id.at(-1) === 'PromptTemplate'
    ? EPromptType.INSTRUCT
    : EPromptType.CHAT;
}

export const getSelectedEvaluatorIds = (
  evaluatorIdSelectionStates: Record<string, boolean> | undefined
) => {
  if (!evaluatorIdSelectionStates) return undefined;
  return Object.keys(evaluatorIdSelectionStates).filter(
    (key) => evaluatorIdSelectionStates[key]
  );
};

export function getToolsFromModelManifest(
  modelManifest: SerializedConstructor
) {
  return modelManifest?.kwargs?.kwargs?.tools;
}

export function formatTools(tools?: any[]): OpenAITool[] {
  if (!tools) return [];

  return tools.map((tool: any) =>
    isAnthropicToolDefinition(tool)
      ? {
          type: 'function',
          function: {
            name: tool.name,
            parameters: tool.input_schema,
            description: tool.description,
          },
        }
      : tool
  );
}

export function storedManifestToPlaygroundManifestAndOptions(
  manifest: SerializedConstructor
): PlaygroundManifestParts {
  const [isChat, modelManifest] = getModelForPrompt(manifest);
  const promptManifest = getPromptManifest(manifest);

  // Handle tools if present
  const tools = formatTools(getToolsFromModelManifest(modelManifest));
  const hasTools = tools.length > 0;

  // We store tools in the model manifest, but we need to put them in `options` for manipulation in the playground
  const options = hasTools ? { tools } : {};

  const finalModelManifest = hasTools
    ? omit(cloneDeep(modelManifest), 'kwargs.kwargs.tools')
    : modelManifest;

  return {
    promptManifest,
    modelManifest: finalModelManifest,
    options,
    isChat,
  };
}

export function getIdFromRunnableBinding(
  manifest: SerializedConstructor
): string[] {
  return manifest.kwargs.bound?.id;
}

export function getProviderFromRunnableBinding(
  manifest: SerializedConstructor
): string {
  return getIdFromRunnableBinding(manifest).at(-1) ?? '';
}

export function getModelNameFromRunnableBinding(
  manifest: SerializedConstructor
) {
  const kwargs = manifest.kwargs.bound?.kwargs;
  return kwargs?.model ?? kwargs?.model_name ?? kwargs?.model_id;
}

export function handleOptionsForSubmit(
  manifest: SerializedConstructor,
  options: ManifestOptions
): ManifestOptions {
  const modelAndProvider = manifestModelAndProvider({
    variant: 'line',
    value: manifest.kwargs.last?.kwargs?.bound ?? manifest.kwargs.last,
    options: {},
  });

  const parsedOptions = JSON.parse(JSON.stringify(options));

  // Remove tool-related fields if no tools specified
  if (!parsedOptions.tools?.length) {
    delete parsedOptions.tools;
    delete parsedOptions.tool_choice;
    delete parsedOptions.parallel_tool_calls;

    return parsedOptions;
  }

  // Remove parallel_tool_calls if true (default), only keep if false
  if (parsedOptions.parallel_tool_calls) {
    delete parsedOptions.parallel_tool_calls;
  }

  // Handle tool-related options based on model capabilities
  if (!supportsTools(modelAndProvider)) {
    // Remove all tool options if model doesn't support tools
    delete parsedOptions.tools;
    delete parsedOptions.tool_choice;
    delete parsedOptions.parallel_tool_calls;
  } else {
    // Handle tool choice options for models that support tools
    if (
      !supportsEnforcedToolSelection(modelAndProvider) &&
      typeof parsedOptions.tool_choice !== 'string'
    ) {
      delete parsedOptions.tool_choice;
    }

    if (!supportsToolChoiceParameter(modelAndProvider)) {
      delete parsedOptions.tool_choice;
    }
  }

  return parsedOptions;
}
export function removeOptionalModelSecrets(
  manifest: SerializedConstructor,
  secrets: Record<string, string>,
  optionalModelSecrets: string[]
): SerializedConstructor {
  const optionalModelSecretsSet = new Set(optionalModelSecrets);
  const newManifest = structuredClone(manifest);
  const last = newManifest.kwargs.last;

  if (last?.kwargs?.bound?.kwargs) {
    const newKwargs = { ...last.kwargs.bound.kwargs };
    Object.keys(newKwargs).forEach((key) => {
      const upperCaseKey = key.toUpperCase();
      if (optionalModelSecretsSet.has(upperCaseKey) && !secrets[upperCaseKey]) {
        delete newKwargs[key];
      }
    });
    last.kwargs.bound.kwargs = newKwargs;
  }

  return newManifest;
}

export const getDefaultPromptManifest = ({
  modelManifest,
  type,
  templateFormat = 'f-string',
  defaultInputVar = null,
  structuredOutputVariant = 'session-eval',
  evaluatorName,
}: {
  modelManifest: SerializedConstructor;
  type?: EPromptType;
  templateFormat?: TemplateFormat;
  defaultInputVar?: string | null;
  structuredOutputVariant?: 'dataset-eval' | 'session-eval';
  evaluatorName?: string;
}) => {
  const { isO1Generic } = isAnyOSeriesModel(
    getModelNameFromRunnableBinding(modelManifest)
  );
  return createDefaultPromptManifest({
    type,
    templateFormat,
    isO1: isO1Generic,
    defaultInputVar,
    structuredOutputVariant,
    evaluatorName,
  });
};

export const getInputVariablesFromPrompt = (prompt: PlaygroundPromptType) => {
  return prompt.manifest.kwargs.first.kwargs.input_variables;
};

// Modify input for submission to the backend
export const modifyInputForSubmit = (
  input: Record<string, PlaygroundInputValueType>,
  inputSourceType: PlaygroundInputSourceType
) => {
  if (inputSourceType === PlaygroundInputSourceType.DATASET) {
    return input;
  }

  // OpenAI format expects the audio to be in base64 format without the data:audio/wav;base64 prefix
  // But in the frontend we store the audio with the data:audio/wav;base64 prefix so we can render it properly
  // This function removes the prefix for submission to the model
  return Object.fromEntries(
    Object.entries(input).map(([key, value]) => {
      if (typeof value === 'string' && value.startsWith(DATA_PREFIX)) {
        const mimeType = EXTRACT_MIME_TYPE_FROM_DATA_URL(value);
        if (mimeType?.startsWith(MIME_TYPES.AUDIO)) {
          return [key, EXTRACT_BASE64_FROM_DATA_URL(value)];
        }
      }
      return [key, value];
    })
  );
};

export function convertManifestToTuple(
  manifest: SerializedChatPromptTemplateMessagesKwarg
) {
  const messages = convertManifestToMessages(manifest);
  return messages.map((m) => {
    const message = m as StoredMessage;
    return [message.type, message.data.content];
  }) as MessageTuple[];
}

/**
 * Replaces template variables in a prompt manifest with the corresponding
 * variable mapping values. This is used in AlignEvals.
 *
 * For example, if variable mapping is {'input': 'foo.bar'}, replaces {{input}}
 * with {{foo.bar}} throughout the manifest.
 */
export const replaceTemplateVariables = (
  manifest: SerializedConstructor,
  variableMapping: Record<string, string>
) => {
  // Helper function to recursively process all strings in the manifest
  const processObject = (obj: any): any => {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map((item) => processObject(item));
    }

    // Create a new object to avoid mutating the original
    const result: any = { ...obj };

    // Process each property of the object
    for (const [key, value] of Object.entries(obj)) {
      // Handle string values - these might contain template variables
      if (typeof value === 'string') {
        // Replace mustache template variables {{variable}} with {{mapped_value}}
        result[key] = value.replace(/\{\{([^}]+)\}\}/g, (match, varName) => {
          const trimmedVarName = varName.trim();
          // Only replace if the variable exists in our mapping
          return variableMapping[trimmedVarName]
            ? `{{${variableMapping[trimmedVarName]}}}`
            : match;
        });

        // Also handle f-string format {variable} for completeness
        result[key] = result[key].replace(/\{([^}]+)\}/g, (match, varName) => {
          // Only replace if it's not a complex expression and the variable exists in our mapping
          // This is a simple approach and might need refinement for complex f-strings
          const trimmedVarName = varName.trim();
          return variableMapping[trimmedVarName] && !varName.includes(' ')
            ? `{${variableMapping[trimmedVarName]}}`
            : match;
        });
      }
      // Recursively process nested objects and arrays
      else if (typeof value === 'object' && value !== null) {
        result[key] = processObject(value);
      }
    }

    return result;
  };

  // Start processing from the top-level manifest
  return processObject(manifest);
};
