import { ChevronRightIcon } from 'lucide-react';
import { Dispatch, ReactNode, SetStateAction, forwardRef } from 'react';

import { InfoTooltip } from '@/components/InfoTooltip/InfoTooltip';
import { cn } from '@/utils/tailwind';

import { usePlaygroundParentContext } from '../context/PlaygroundParentContext';

export const PlaygroundSectionHeader = forwardRef<
  HTMLDivElement,
  {
    title: string | ReactNode;
    tooltipDescription?: string;
    rightActions?: ReactNode;
    className?: string;
    collapsed?: boolean;
    setCollapsed?: Dispatch<SetStateAction<boolean>>;
  }
>(
  (
    {
      title,
      tooltipDescription,
      rightActions,
      className,
      collapsed,
      setCollapsed,
    },
    ref
  ) => {
    const { isSimplified } = usePlaygroundParentContext();
    return (
      <div
        className={cn(
          'group flex h-10 items-center justify-between px-6',
          isSimplified ? 'bg-primary' : 'bg-tertiary',
          className
        )}
        ref={ref}
      >
        {typeof title === 'string' && tooltipDescription ? (
          <div className="flex shrink-0 flex-row items-center gap-2">
            <div className="text-sm font-medium">{title}</div>
            <InfoTooltip
              description={tooltipDescription}
              size="sm"
              icon="info"
            />
            {collapsed !== undefined && (
              <div
                className="opacity-0 transition-opacity hover:cursor-pointer group-hover:opacity-100"
                onClick={() => setCollapsed?.(!collapsed)}
              >
                <ChevronRightIcon
                  className={cn(
                    !collapsed && 'rotate-90',
                    'size-4 transition-transform'
                  )}
                />
              </div>
            )}
          </div>
        ) : (
          <h3 className="text-sm font-semibold text-secondary">{title}</h3>
        )}
        {rightActions && <div className="flex gap-2">{rightActions}</div>}
      </div>
    );
  }
);
