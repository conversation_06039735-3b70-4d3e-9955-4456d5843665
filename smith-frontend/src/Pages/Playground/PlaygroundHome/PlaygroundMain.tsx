import { File05Icon, RefreshCw01Icon } from '@langchain/untitled-ui-icons';
import { LinearProgress } from '@mui/joy';

import { isEqual } from 'lodash-es';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';

import { useOrganizationId } from '@/hooks/useSwr.tsx';
import { useLocalStorageState } from '@/utils/use-local-storage-state.tsx';

import { PlaygroundInputSourceType } from '../PlaygroundContext.tsx';
import { PlaygroundHeader } from '../components/PlaygroundHeader.tsx';
import { PlaygroundLayout } from '../components/PlaygroundLayout.tsx';
import { PlaygroundSecrets } from '../components/PlaygroundSecrets.tsx';
import { PlaygroundSubmit } from '../components/PlaygroundSubmit.tsx';
import {
  PLAYGROUND_SECRETS_SOURCE_LOCAL_STORAGE_KEY,
  PlaygroundSecretsSource,
} from '../components/constants.ts';
import {
  PlaygroundParentContextProvider,
  usePlaygroundParentContext,
} from '../context/PlaygroundParentContext.tsx';
import {
  getInputVariables,
  promptTypeFromManifest,
} from '../utils/Playground.utils.tsx';
import { useEvaluatorFeedbackKeys } from '../utils/use-evaluator-feedback-keys.tsx';
import { EvaluateModeButton } from './EvaluateModeButton.tsx';
import { PlaygroundIcon } from './PlaygroundButtons.tsx';
import { SecretsProvider, useSecrets } from './PlaygroundSecretsContext.tsx';
import {
  useInput,
  useManifests,
  usePlaygroundStore,
} from './hooks/usePlaygroundStore.tsx';
import { usePlaygroundSubmit } from './hooks/usePlaygroundSubmit.ts';
import { useSelectedEvaluatorIds } from './hooks/useSelectedEvaluatorIds.ts';
import { PlaygroundMainProps } from './types.tsx';

export function PlaygroundMain(props: PlaygroundMainProps) {
  const datasetId = usePlaygroundStore((state) => state.datasetId);
  const prompts = usePlaygroundStore((state) => state.prompts);
  const originalRunId = usePlaygroundStore((state) => state.originalRunId);
  const initializeState = usePlaygroundStore((state) => state.initializeState);
  const isAlignEvaluator = usePlaygroundStore(
    (state) => state.isAlignEvaluator
  );

  const [hasInitializedState, setHasInitializedState] = useState(false);

  useEffect(() => {
    // if these are true, we're likely clicking into the playground from the sidebar
    const isHubPromptUnset = !props.repo && !!prompts[0]?.hubPrompt;
    const isDatasetUnset = !props.datasetId && !!datasetId;

    const openingNewDataset = props.datasetId !== datasetId && !isDatasetUnset;
    const openingNewPrompt =
      props.repo?.last_commit_hash !==
        prompts[0]?.hubPrompt?.prompt.last_commit_hash && !isHubPromptUnset;
    const openingNewCommit =
      props.commitHash?.slice(0, 8) !==
        prompts[0]?.hubPrompt?.commitHash?.slice(0, 8) && !isHubPromptUnset;

    const openingNewRun = props.run?.id !== originalRunId;
    const openingEmptyPlaygroundButHaveExistingRun =
      !props.run?.id && originalRunId;

    const existingPromptType = prompts[0]?.manifest
      ? promptTypeFromManifest(prompts[0]?.manifest)
      : undefined;
    const openingPromptWithDifferentType = props.type !== existingPromptType;

    if (
      props.forceReset ||
      openingPromptWithDifferentType ||
      openingNewDataset ||
      openingNewPrompt ||
      openingNewCommit ||
      (openingNewRun && !openingEmptyPlaygroundButHaveExistingRun) ||
      isAlignEvaluator
    ) {
      initializeState(props);
    }

    setHasInitializedState(true);
    // only want to run this on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!hasInitializedState)
    return (
      <div className="mx-2 mt-4">
        <LinearProgress />
      </div>
    );

  return (
    <PlaygroundParentContextProvider isSimplified={props.isSimplified}>
      <SecretsProvider>
        <PlaygroundMainContent scrollRef={props.scrollRef} />
      </SecretsProvider>
    </PlaygroundParentContextProvider>
  );
}

function PlaygroundMainContent({
  scrollRef,
}: {
  scrollRef?: Dispatch<SetStateAction<HTMLDivElement | null>>;
}) {
  const { isSimplified } = usePlaygroundParentContext();

  const organizationId = useOrganizationId();

  const manifests = useManifests();

  const setLoadingState = usePlaygroundStore((state) => state.setLoadingState);
  const datasetId = usePlaygroundStore((state) => state.datasetId);
  const isCreatingNewDataset = usePlaygroundStore(
    (state) => state.isCreatingNewDataset
  );
  const input = useInput();
  const setInput = usePlaygroundStore((state) => state.setInput);
  const inputSourceType = usePlaygroundStore((state) => state.inputSourceType);
  const setEvaluatorFeedbackKeys = usePlaygroundStore(
    (state) => state.setEvaluatorFeedbackKeys
  );

  const { selectedEvaluatorIdsSet } = useSelectedEvaluatorIds();

  const { evaluatorFeedbackKeys } = useEvaluatorFeedbackKeys({
    organizationId,
    datasetId,
    selectedRuleIds: selectedEvaluatorIdsSet,
  });

  useEffect(() => {
    setEvaluatorFeedbackKeys(evaluatorFeedbackKeys);
  }, [evaluatorFeedbackKeys, setEvaluatorFeedbackKeys]);

  const { secrets, setSecrets, openSecretsRef } = useSecrets();
  const [selectedSecretsSourceLS, setSelectedSecretsSource] =
    useLocalStorageState<PlaygroundSecretsSource>(
      PLAYGROUND_SECRETS_SOURCE_LOCAL_STORAGE_KEY,
      PlaygroundSecretsSource.BROWSER
    );

  const selectedSecretsSource = organizationId
    ? selectedSecretsSourceLS
    : PlaygroundSecretsSource.BROWSER;

  const onSubmit = usePlaygroundSubmit({
    selectedSecretsSource,
  });

  const resetState = usePlaygroundStore((state) => state.resetState);

  // Set loading state to done if the user changes the dataset
  useEffect(() => {
    setLoadingState('done');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [datasetId]);

  // if there are new input variables in the manifest, add them to the input object
  // if there are unused input variables, we still keep them in the input object so that if they're deleted and re-added the input values are still there
  useEffect(() => {
    // both of these are arrays of unique strings
    const currentInputVars = input ? Object.keys(input) : [];
    const manifestVars = manifests.flatMap((m) => getInputVariables(m));

    // Check if we need to update the input
    const needsUpdate = isEqual(manifestVars, currentInputVars);

    if (needsUpdate) {
      setInput({
        // keep the existing input variables
        ...input,
        // Add new variables with empty strings
        ...Object.fromEntries(
          manifestVars
            .filter((key) => !currentInputVars.includes(key))
            .map((key) => [key, ''])
        ),
      });
    }
  }, [manifests, input, setInput]);

  return (
    <div className="flex h-full flex-col overflow-hidden">
      {!isSimplified && (
        <PlaygroundHeader
          rightActions={
            <>
              <PlaygroundIcon
                tooltipTitle={
                  inputSourceType === PlaygroundInputSourceType.DATASET
                    ? 'Learn more about running evaluations'
                    : 'Learn more about prompt engineering'
                }
              >
                <a
                  href={
                    inputSourceType === PlaygroundInputSourceType.DATASET
                      ? 'https://docs.smith.langchain.com/evaluation?mode=ui'
                      : 'https://docs.smith.langchain.com/prompt_engineering/quickstarts/quickstart_ui'
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <File05Icon className="mx-1 my-1.5 h-4 w-4" />
                </a>
              </PlaygroundIcon>
              <EvaluateModeButton />
              <PlaygroundIcon tooltipTitle="Reset playground">
                <button onClick={resetState} type="button">
                  <RefreshCw01Icon className="mx-1 my-1.5 h-4 w-4" />
                </button>
              </PlaygroundIcon>
              <PlaygroundSecrets
                openSecretsRef={openSecretsRef}
                value={secrets}
                onChange={setSecrets}
                selectedSecretsSource={selectedSecretsSource}
                setSelectedSecretsSource={setSelectedSecretsSource}
              />
              <PlaygroundSubmit
                disabled={
                  inputSourceType === 'dataset' &&
                  !datasetId &&
                  !isCreatingNewDataset
                }
                onSubmit={onSubmit}
              />
            </>
          }
        />
      )}

      <PlaygroundLayout scrollRef={scrollRef} />
      {isSimplified && (
        <div className="ml-auto flex flex-row items-center gap-2 px-6 py-4">
          <PlaygroundSecrets
            openSecretsRef={openSecretsRef}
            value={secrets}
            onChange={setSecrets}
            selectedSecretsSource={selectedSecretsSource}
            setSelectedSecretsSource={setSelectedSecretsSource}
          />
          <PlaygroundSubmit onSubmit={onSubmit} size="lg" />
        </div>
      )}
    </div>
  );
}
