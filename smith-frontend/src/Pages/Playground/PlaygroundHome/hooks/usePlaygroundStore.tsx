import type { SerializedConstructor } from '@langchain/core/load/serializable';

import { useCallback, useMemo, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { create } from 'zustand';

import { hasNestedObjects } from '@/components/PrettyJSONEditor/utils.ts';
import { simplifyToolChoice } from '@/utils/ai-tools.tsx';

import { DEFAULT_CHAT_MODEL_CONSTRUCTOR } from '../../Playground.constants.tsx';
import {
  ExampleSchemaOrDraftWithEdited,
  PlaygroundContextType,
  PlaygroundInputSourceType,
  PlaygroundPromptType,
} from '../../PlaygroundContext';
import { getOpenAIModelCapabilities } from '../../components/manifest/serialized/chat_models/ManifestChatOpenAI.utils.ts';
import {
  createExperimentName,
  createPlaygroundManifest,
  getDefaultModelManifest,
  getDefaultPromptManifest,
  getInputVariablesFromPrompt,
  getModelFromManifest,
  getModelNameFromRunnableBinding,
} from '../../utils/Playground.utils.tsx';
import { ManifestOptions } from '../../utils/types.ts';
import {
  PlaygroundActions,
  PlaygroundInputValueType,
  PlaygroundMainProps,
} from '../types.tsx';
import { createNewExample } from '../utils.ts';

const DEFAULT_INPUT_TYPE = PlaygroundInputSourceType.MANUAL;
const DEFAULT_DATASET_SPLITS = [];

export const usePlaygroundStore = create<
  PlaygroundContextType & PlaygroundActions
>((set) => ({
  defaultModelManifest: undefined,
  prompts: [],
  input: {},
  datasetId: undefined,
  datasetSplits: DEFAULT_DATASET_SPLITS,
  evaluatorIdSelectionStates: undefined,
  examples: [],
  exampleCount: 0,
  inputSourceType: DEFAULT_INPUT_TYPE,
  repetitions: 1,
  loadingState: 'done',
  evaluatorFeedbackKeys: [],
  abortController: new AbortController(),
  originalRunId: null,
  isStreaming: true,
  isCreatingNewDataset: false,
  isAlignEvaluator: false,
  expectedNumberOfRuns: 0,
  setIsAlignEvaluator: (isAlignEvaluator: boolean) => set({ isAlignEvaluator }),
  setExpectedNumberOfRuns: (expectedNumberOfRuns: number) =>
    set({ expectedNumberOfRuns }),

  initializeState: (props: PlaygroundMainProps) =>
    set(() => {
      const initialPlaygroundManifest = createPlaygroundManifest(
        props.promptManifest,
        props.modelManifest
      );

      return {
        defaultModelManifest: props.modelManifest,
        prompts: [
          {
            columnId: uuidv4(),
            manifest: initialPlaygroundManifest,
            options: {
              ...props.options,
              // https://langchain.slack.com/archives/C05RL6DPH4M/p1736973510658829?thread_ts=**********.125499&cid=C05RL6DPH4M
              // all providers that support tool_choice should support this simplified format (which is just the tool name as a string)
              tool_choice: simplifyToolChoice(props.options?.tool_choice),
            },
            hubPrompt: props.repo
              ? {
                  prompt: props.repo,
                  edited: false,
                  commitHash: props.commitHash,
                }
              : undefined,
            runs: props.run ? [props.run] : [],
            customExperimentName: createExperimentName({
              modelManifest: props.modelManifest,
            }),
          },
        ],
        input: props.run?.run_type === 'llm' ? {} : props.run?.inputs ?? {},
        datasetId:
          props.inputSourceType === PlaygroundInputSourceType.MANUAL
            ? undefined
            : props.run
            ? undefined
            : props.datasetId,
        datasetSplits: DEFAULT_DATASET_SPLITS,
        selectedEvaluatorIds: undefined,
        inputSourceType:
          props.inputSourceType ?? props.run
            ? PlaygroundInputSourceType.MANUAL
            : props.datasetId
            ? PlaygroundInputSourceType.DATASET
            : DEFAULT_INPUT_TYPE,
        originalRunId: props.run?.id,
        isCreatingNewDataset: false,
      };
    }),
  resetState: () =>
    set((state) => {
      const modelManifest =
        state.defaultModelManifest ??
        getDefaultModelManifest(DEFAULT_CHAT_MODEL_CONSTRUCTOR);
      const defaultPlaygroundManifest = createPlaygroundManifest(
        getDefaultPromptManifest({ modelManifest }),
        modelManifest
      );

      return {
        prompts: [
          {
            columnId: uuidv4(),
            manifest: defaultPlaygroundManifest,
            options: {},
            hubPrompt: undefined,
            runs: [],
          },
        ],
        input: {},
        datasetId: undefined,
        datasetSplits: DEFAULT_DATASET_SPLITS,
        selectedEvaluatorIds: undefined,
        inputSourceType: DEFAULT_INPUT_TYPE,
        originalRunId: null,
      };
    }),
  resetPrompt: (index: number, modelManifest?: SerializedConstructor) =>
    set((state) => {
      const modelManifestToUse =
        modelManifest ??
        state.defaultModelManifest ??
        getDefaultModelManifest(DEFAULT_CHAT_MODEL_CONSTRUCTOR);
      const defaultPlaygroundManifest = createPlaygroundManifest(
        getDefaultPromptManifest({ modelManifest: modelManifestToUse }),
        modelManifestToUse
      );
      const prompts = [...state.prompts];
      prompts[index] = {
        columnId: uuidv4(),
        manifest: defaultPlaygroundManifest,
        options: {},
        hubPrompt: undefined,
        runs: [],
        customExperimentName: createExperimentName({
          modelManifest: modelManifestToUse,
        }),
      };
      return { prompts };
    }),
  resetAbortController: () =>
    set(() => {
      return { abortController: new AbortController() };
    }),
  setManifests: (manifests) =>
    set((state) => {
      const prompts = [...state.prompts];
      const newManifests =
        typeof manifests === 'function'
          ? manifests(prompts.map((p) => p.manifest))
          : manifests;
      for (let i = 0; i < newManifests.length; i++) {
        prompts[i].manifest = newManifests[i];
      }
      return { prompts };
    }),
  setManifest: (
    index: number,
    manifest:
      | SerializedConstructor
      | ((prev: SerializedConstructor) => SerializedConstructor)
  ) =>
    set((state) => {
      const prompts = [...state.prompts];
      const newManifest =
        typeof manifest === 'function'
          ? manifest(state.prompts[index].manifest)
          : manifest;

      prompts[index] = {
        ...prompts[index],
        manifest: newManifest,
        customExperimentName: createExperimentName({
          repoHandle: state.prompts[index].hubPrompt?.prompt.repo_handle,
          modelManifest: getModelFromManifest(newManifest) ?? undefined,
        }),
      };
      return { prompts };
    }),
  addPrompt: () =>
    set((state) => ({
      prompts: [
        ...state.prompts,
        {
          ...state.prompts[0],
          columnId: uuidv4(),
          runs: [],
          sessionId: undefined,
          hubPrompt: state.prompts[0].hubPrompt,
          customExperimentName: createExperimentName({
            repoHandle: state.prompts[0].hubPrompt?.prompt.repo_handle,
            modelManifest:
              getModelFromManifest(state.prompts[0].manifest) ?? undefined,
          }),
        },
      ],
    })),
  removePrompt: (columnId: string) =>
    set((state) => ({
      prompts: state.prompts.filter((p) => p.columnId !== columnId),
    })),
  setLoadingState: (loading: 'loading' | 'done') =>
    set({ loadingState: loading }),
  setInput: (input: Record<string, PlaygroundInputValueType>) => set({ input }),
  setSingleInput: (name: string, value: PlaygroundInputValueType) =>
    set((state) => ({ input: { ...state.input, [name]: value } })),
  setInputSourceTypeAndUpdateUrl: (
    inputSourceType: PlaygroundInputSourceType
  ) => {
    set({ inputSourceType });
    if (inputSourceType === PlaygroundInputSourceType.MANUAL) {
      set({ datasetId: undefined });
      setDatasetIdInUrl();
    }
  },
  setDatasetIdAndUpdateUrl: (datasetId?: string) => {
    set({ datasetId });
    set({ isCreatingNewDataset: false });
    if (!datasetId) {
      set({
        inputSourceType: PlaygroundInputSourceType.MANUAL,
        datasetSplits: DEFAULT_DATASET_SPLITS,
        evaluatorIdSelectionStates: undefined,
        examples: [],
        exampleCount: 0,
      });
      setDatasetIdInUrl();
    } else {
      set({ inputSourceType: PlaygroundInputSourceType.DATASET });
      set((state) =>
        state.datasetId === datasetId
          ? state
          : {
              ...state,
              datasetSplits: DEFAULT_DATASET_SPLITS,
              evaluatorIdSelectionStates: undefined,
            }
      );
      setDatasetIdInUrl(datasetId);
    }
  },
  setDatasetSplits: (splits: string[]) => set({ datasetSplits: splits }),
  setEvaluatorIdSelectionStates: (
    evaluatorIds: Record<string, boolean> | undefined
  ) => set({ evaluatorIdSelectionStates: evaluatorIds }),
  setSingleEvaluatorIdSelectionState: (
    evaluatorId: string,
    isSelected: boolean
  ) =>
    set((state) => ({
      evaluatorIdSelectionStates: {
        ...state.evaluatorIdSelectionStates,
        [evaluatorId]: isSelected,
      },
    })),
  setExamples: (examples: ExampleSchemaOrDraftWithEdited[]) =>
    set({ examples: examples.map((e) => ({ ...e, edited: false })) }),
  removeReferenceOutputFromExamples: () =>
    set((state) => {
      if (!state.isCreatingNewDataset) {
        return state;
      }
      return {
        examples: state.examples.map((e) => ({
          ...e,
          outputs: {},
          edited: true,
        })),
      };
    }),
  setExample: (
    exampleId: string,
    example: (
      prevExample: ExampleSchemaOrDraftWithEdited
    ) => ExampleSchemaOrDraftWithEdited
  ) => {
    set((state) => ({
      examples: state.examples.map((e) =>
        e.id === exampleId ? example(e) : e
      ),
    }));
  },
  clearRuns: (preserveSessionId?: boolean) =>
    set((state) => ({
      prompts: state.prompts.map((p) => ({
        ...p,
        runs: [],
        ...(!preserveSessionId ? { sessionId: undefined } : {}),
      })),
    })),
  setPrompt: (
    index: number,
    prompt:
      | PlaygroundPromptType
      | ((prev: PlaygroundPromptType) => PlaygroundPromptType)
  ) => {
    set((state) => {
      const newPrompts = { prompts: [...state.prompts] };

      const newPrompt =
        typeof prompt === 'function' ? prompt(state.prompts[index]) : prompt;

      newPrompts.prompts[index] = newPrompt;
      return newPrompts;
    });
  },
  setEvaluatorFeedbackKeys: (keys: string[]) =>
    set({ evaluatorFeedbackKeys: keys }),
  setRepetitions: (num: number) => set({ repetitions: num }),
  setEdited: (index: number, edited: boolean) =>
    set((state) => {
      const prompts = [...state.prompts];
      if (prompts[index].hubPrompt) {
        prompts[index].hubPrompt!.edited = edited;
      }
      return { prompts };
    }),
  setOptions: (index: number, options: ManifestOptions) =>
    set((state) => {
      const prompts = [...state.prompts];
      prompts[index].options = options;
      return { prompts };
    }),
  setCompareToOptimizedManifest: (optimizedManifest: SerializedConstructor) =>
    set((state) => ({
      prompts: [
        state.prompts[0],
        {
          ...state.prompts[0],
          manifest: optimizedManifest,
          columnId: uuidv4(),
          runs: [],
          sessionId: undefined,
          hubPrompt: undefined,
        },
      ],
    })),
  setIsStreaming: (isStreaming: boolean) => set({ isStreaming }),
  setIsCreatingNewDataset: (isCreatingNewDataset: boolean) =>
    set({ isCreatingNewDataset }),
  onCreateNewDataset: (useInputValues: boolean = false) => {
    set((state) => {
      // Only use the input if it matches our criteria for creating datasets inline:
      // 1. useInputValues is true
      // 2. the input is not nested
      // 3. all values are strings
      const values = Object.values(state.input);
      const inputValues =
        useInputValues &&
        !hasNestedObjects(state.input) &&
        values.length &&
        values.every((v) => typeof v === 'string')
          ? (state.input as Record<string, string>)
          : undefined;

      const inputVariables = getInputVariablesFromPrompt(state.prompts[0]);
      const outputVariables = ['output'];
      return {
        datasetSplits: DEFAULT_DATASET_SPLITS,
        inputSourceType: PlaygroundInputSourceType.DATASET,
        datasetId: undefined,
        isCreatingNewDataset: true,
        evaluatorIdSelectionStates: undefined,
        examples: [
          createNewExample({ inputVariables, outputVariables, inputValues }),
        ],
        exampleCount: 1,
      };
    });
    setDatasetIdInUrl();
  },
  addNewExample: () => {
    set((state) => {
      const inputVariables = getInputVariablesFromPrompt(state.prompts[0]);
      const outputVariables = state.examples[0]
        ? Object.keys(state.examples[0].outputs)
        : ['output'];
      return {
        examples: [
          createNewExample({ inputVariables, outputVariables }),
          ...state.examples,
        ],
        exampleCount: state.exampleCount + 1,
      };
    });
  },
  decreaseExampleCount: () =>
    set((state) => ({ exampleCount: state.exampleCount - 1 })),
  increaseExampleCount: () =>
    set((state) => ({ exampleCount: state.exampleCount + 1 })),
  setExampleCount: (exampleCount: number) => set({ exampleCount }),
  markExamplesAsSaved: () => {
    set((state) => {
      const examples = state.examples.map((e) => ({
        ...e,
        edited: false,
      }));
      return { examples };
    });
  },
  saveDataset: () => {
    set((state) => {
      const examples = state.examples.map((e) => ({
        ...e,
        modified_at: new Date().toISOString(),
      }));
      return { examples };
    });
  },
  removeExampleFromDataset: (exampleId: string) => {
    set((state) => ({
      examples: state.examples.filter((e) => e.id !== exampleId),
      exampleCount: state.exampleCount - 1,
    }));
  },
}));

const setDatasetIdInUrl = (datasetId?: string) => {
  const url = new URL(window.location.href);
  if (datasetId) {
    url.searchParams.set('datasetId', datasetId);
  } else {
    url.searchParams.delete('datasetId');
  }
  window.history.replaceState({}, '', url);
};

export const useManifests = () =>
  usePlaygroundStore(useDeep((state) => state.prompts.map((p) => p.manifest)));

export const useDisabledStreamingText = ({
  prompts,
  manifests,
}: {
  prompts: PlaygroundPromptType[] | undefined;
  manifests: SerializedConstructor[] | undefined;
}) => {
  const promptCount = prompts?.length ?? 0;
  const hasStructuredPrompt = prompts?.some(
    (p) => p.manifest.kwargs?.first?.id?.at(-1) === 'StructuredPrompt'
  );
  const hasModelThatDoesNotSupportStreaming = useMemo(() => {
    return manifests?.some((manifest) => {
      const model = getModelFromManifest(manifest);
      if (model) {
        const modelName = getModelNameFromRunnableBinding(model);
        const { isStreamingSupported } = getOpenAIModelCapabilities(modelName);
        return !isStreamingSupported;
      }
      return false;
    });
  }, [manifests]);

  if (promptCount > 3) {
    return 'Streaming allowed with 3 or fewer prompts';
  }
  if (hasModelThatDoesNotSupportStreaming) {
    return 'This model does not support streaming';
  }

  return hasStructuredPrompt
    ? 'Streaming disabled with structured output'
    : undefined;
};

export const useInput = () =>
  usePlaygroundStore(useDeep((state) => state.input));

function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false;
    }
    return true;
  }
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;
    for (const key of keysA) {
      if (!deepEqual(a[key], b[key])) return false;
    }
    return true;
  }
  return false;
}

export function useDeep<T, U>(selector: (state: T) => U) {
  const ref = useRef<U>();
  return useCallback(
    (state: T) => {
      const selected = selector(state);
      if (ref.current === undefined || !deepEqual(ref.current, selected)) {
        ref.current = selected;
      }
      return ref.current;
    },
    [selector]
  );
}

export function isDatasetExamplesEdited(
  examples?: ExampleSchemaOrDraftWithEdited[]
) {
  return examples?.some((e) => e.edited);
}
