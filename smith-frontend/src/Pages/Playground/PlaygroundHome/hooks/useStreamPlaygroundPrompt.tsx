import { AIMessageChunk } from '@langchain/core/messages';

import { useCallback, useState } from 'react';

import useToast from '@/components/Toast';
import { Op, subscriber } from '@/data/fetcher';
import { useOrganizationId } from '@/hooks/useSwr';
import { ObservableValue } from '@/hooks/useSwrObservable';
import {
  PlaygroundStreamDatasetRequestSchema,
  PlaygroundStreamRequestSchema,
  RunSchema,
} from '@/types/schema';
import { getPlaygroundPath } from '@/utils/constants';
import { isSerializedConstructor } from '@/utils/serialized';

import { usePlaygroundStore } from './usePlaygroundStore';

const initialData = {
  done: true,
  value: { outputs: [] } as any,
};

const initialDatasetData = {
  done: true,
  value: { runs: {} },
};

export const useStreamPlaygroundPrompt = (
  isDataset: boolean,
  abortController: AbortController
) => {
  const { createToast } = useToast();
  const organizationId = useOrganizationId();

  const setLoadingState = usePlaygroundStore((state) => state.setLoadingState);
  const setPrompt = usePlaygroundStore((state) => state.setPrompt);

  const [dataState, setData] = useState<Array<ObservableValue<any>>>([]);
  const resetData = useCallback(() => {
    setData([]);
  }, [setData]);

  const generate = useCallback(
    (
      params:
        | PlaygroundStreamRequestSchema
        | PlaygroundStreamDatasetRequestSchema,
      index: number
    ) => {
      subscriber<
        any,
        never,
        PlaygroundStreamRequestSchema | PlaygroundStreamDatasetRequestSchema
      >(
        {
          url: `${getPlaygroundPath(isDataset)}/${
            isDataset ? 'playground_experiment/stream' : 'stream'
          }`,
          subscriberParser: 'passthrough',
          method: 'POST',
          json: params,
          headers: { 'X-Tenant-Id': organizationId },
        },
        {
          next: function (error, data) {
            if (error) {
              let errorMessage = 'Failed to stream';
              try {
                const parsedError = JSON.parse(error.message);
                errorMessage = parsedError?.detail ?? errorMessage;
              } catch (e) {
                errorMessage = error.message || errorMessage;
              }
              createToast({
                title: 'Failed to stream',
                description: errorMessage,
                error: true,
              });
              setLoadingState('done');
              return;
            }
            if (data) {
              if (typeof data === 'function') {
                setData((prev) => {
                  const chunk = data();
                  if (!chunk || !chunk.value) {
                    setLoadingState(chunk?.done ? 'done' : 'loading');
                    return prev;
                  }
                  const { patch } = JSON.parse(chunk.value);
                  const defaultData = deepCopy(
                    isDataset ? initialDatasetData : initialData
                  );
                  const newData = {
                    done: chunk.done,
                    value: applyMessageChunkPatch(
                      prev[index]?.value
                        ? deepCopy(prev[index]?.value)
                        : defaultData.value,
                      patch
                    ),
                  };
                  const newPrev = [...prev];
                  newPrev[index] = newData;
                  if (isDataset) {
                    if (newData.done) {
                      setLoadingState('done');
                    }
                    setPrompt(index, (prev) => ({
                      ...prev,
                      sessionId: Object.values(newData.value.runs ?? {})[0]?.[
                        'session_id'
                      ],
                      runs: Object.values(newData.value.runs ?? {}).map((r) => {
                        const run = r as RunSchema;
                        return {
                          ...(run as RunSchema),
                          ...('output' in (run.outputs ?? {})
                            ? { outputs: run.outputs }
                            : {
                                outputs: {
                                  output: run.outputs,
                                },
                              }),
                        };
                      }),
                    }));
                  } else {
                    if (newData.done) {
                      setLoadingState('done');
                    }
                    setPrompt(index, (prev) => ({
                      ...prev,
                      runs: [
                        {
                          ...(newData.value as RunSchema),
                          session_id: prev.columnId,
                          ...('output' in newData.value.outputs
                            ? { outputs: newData.value.outputs }
                            : {
                                outputs: {
                                  output: newData.value.outputs,
                                },
                              }),
                        },
                      ],
                    }));
                  }

                  return newPrev;
                });
              } else {
                console.error(
                  'Not implemented - should never not be a function'
                );
              }
            }
          },
        },
        abortController
      );
    },
    [
      abortController,
      createToast,
      isDataset,
      organizationId,
      setLoadingState,
      setPrompt,
    ]
  );

  return {
    data: dataState,
    isStreaming: !dataState?.some((ds) => !ds || !ds.done),
    generate,
    resetData,
  };
};

function applyMessageChunkPatch<R>(current: R | undefined, patch: Op[]): R {
  let newDocument = current;
  for (const p of patch) {
    const { op, path, value } = p;
    if (op === 'add' && path.endsWith('outputs/-')) {
      const parts = path.split('/').slice(1);
      if (parts.length === 0) {
        newDocument = value as R;
      } else if (newDocument) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        parts.pop();
        const last = parts.pop()!;
        let target = newDocument;
        parts.forEach((part) => {
          target = target[part];
        });
        if (isSerializedConstructor(value)) {
          try {
            const loaded: AIMessageChunk = loadAIMessage(value);
            if (target[last] == null) {
              target[last] = JSON.parse(JSON.stringify(loaded));
            } else {
              if (target[last] instanceof AIMessageChunk) {
                target[last] = JSON.parse(
                  JSON.stringify(target[last].concat(loaded))
                );
              } else {
                const loadedTarget = loadAIMessage(target[last]);
                target[last] = JSON.parse(
                  JSON.stringify(
                    (loadedTarget as unknown as AIMessageChunk).concat(loaded)
                  )
                );
              }
            }
          } catch (e) {
            target[last] = value;
          }
        } else {
          // instruct models return a string
          if (target[last] == null) {
            target[last] = value;
          } else {
            target[last] = target[last].concat(value);
          }
        }
      }
    } else if (op === 'add') {
      const parts = path.split('/').slice(1);
      if (parts.length === 0) {
        newDocument = value as R;
      } else if (newDocument) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const last = parts.pop()!;
        let target = newDocument;
        parts.forEach((part) => {
          target = target[part];
        });
        if (last === '-' && Array.isArray(target)) {
          target.push(value);
        } else {
          target[last] = value;
        }
      }
    } else if (op === 'replace') {
      const parts = path.split('/').slice(1);
      if (parts.length === 0) {
        newDocument = value as R;
        if (
          Array.isArray(newDocument?.['outputs']) &&
          newDocument['outputs'].length === 0
        ) {
          newDocument['outputs'] = null;
        } else if (Array.isArray(newDocument?.['outputs'])) {
          newDocument['outputs'] = newDocument['outputs'][0];
        }
      } else if (newDocument) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const last = parts.pop()!;
        let target = newDocument;
        parts.forEach((part) => {
          target = target[part];
        });

        target[last] = value;
        if (
          Array.isArray(target[last]?.['outputs']) &&
          target[last]['outputs'].length === 0
        ) {
          target[last]['outputs'] = null;
        } else if (Array.isArray(target[last]?.['outputs'])) {
          target[last]['outputs'] = target[last]['outputs'][0];
        }
      }
    }
  }
  return newDocument as R;
}

function loadAIMessage(value: any): AIMessageChunk {
  return new AIMessageChunk(value['kwargs']);
}

// Need to use a custom deep copy function to avoid modifying AIMessageChunk objects
function deepCopy(obj) {
  if (
    obj === null ||
    typeof obj !== 'object' ||
    obj instanceof AIMessageChunk
  ) {
    return obj;
  }

  const copy = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      copy[key] = deepCopy(obj[key]);
    }
  }

  return copy;
}
