import type { SerializedConstructor } from '@langchain/core/load/serializable';

import { omit } from 'lodash-es';
import { useCallback, useMemo } from 'react';

import { useTenantSecretSet } from '@/Pages/Settings/utils/useTenantSecretSet.tsx';
import { useSegmentAnalyticsContext } from '@/SegmentAnalyticsProvider.tsx';
import useToast from '@/components/Toast/index.tsx';
import {
  useInvokePlaygroundDataset,
  useInvokePlaygroundPrompt,
  useSelectedTenant,
} from '@/hooks/useSwr.tsx';
import { PlaygroundRequestSchema } from '@/types/schema';

import { OPTIONAL_MODEL_SECRETS } from '../../Playground.constants.tsx';
import { PlaygroundPromptType } from '../../PlaygroundContext.tsx';
import { PlaygroundSecretsSource } from '../../components/constants.ts';
import {
  createExperimentName,
  formatRunOutput,
  getDatasetSplits,
  getModelFromManifest,
  handleOptionsForSubmit,
  modifyInputForSubmit,
  removeOptionalModelSecrets,
  replaceTemplateVariables,
} from '../../utils/Playground.utils.tsx';
import { useSecrets } from '../PlaygroundSecretsContext.tsx';
import {
  checkMissingSecrets,
  splitNewExamplesFromExistingEditedExamples,
} from '../utils.ts';
import { useCreateDatasetWithExamples } from './useCreateDatasetWithExamples.tsx';
import { usePlaygroundStore } from './usePlaygroundStore.tsx';
import { useSelectedEvaluatorIds } from './useSelectedEvaluatorIds.ts';
import { useStreamPlaygroundPrompt } from './useStreamPlaygroundPrompt.tsx';
import { useUpdateAndCreateDatasetExamples } from './useUpdateAndCreateDatasetExamples.tsx';

export const usePlaygroundSubmit = ({
  selectedSecretsSource,
  variableMapping,
}: {
  selectedSecretsSource: PlaygroundSecretsSource;
  variableMapping?: Record<string, string> | null;
}) => {
  const { secrets: browserSecrets, openSecretsRef } = useSecrets();
  const workspaceSecrets = useTenantSecretSet();

  const { createToast } = useToast();
  const { data: selectedTenant } = useSelectedTenant();

  const {
    prompts,
    input,
    repetitions,
    setPrompt,
    inputSourceType,
    datasetId,
    setDatasetIdAndUpdateUrl: setDatasetId,
    datasetSplits,
    abortController,
    resetAbortController,
    clearRuns,
    loadingState,
    setLoadingState,
    isCreatingNewDataset,
    examples,
    markExamplesAsSaved,
  } = usePlaygroundStore();

  const { selectedEvaluatorIds } = useSelectedEvaluatorIds();

  // if there is a new dataset name, need to create dataset first before running the prompt
  const { saveDataset } = useCreateDatasetWithExamples({
    examples: examples,
    onSuccess: () => {
      markExamplesAsSaved();
    },
  });

  const { updateDataset } = useUpdateAndCreateDatasetExamples({
    datasetId: datasetId ?? '',
    onSuccess: () => {
      markExamplesAsSaved();
    },
  });

  const hasBeenEdited = useMemo(() => {
    return examples.some((example) => example.edited);
  }, [examples]);

  const requests: PlaygroundRequestSchema[] = useMemo(() => {
    const getSecrets = (selectedSecretsSource: PlaygroundSecretsSource) => {
      // Determine if we should  or fallback to workspace secrets
      const useOrFallbackToWorkspaceSecrets =
        selectedSecretsSource === PlaygroundSecretsSource.WORKSPACE ||
        // If we're using browser secrets, but we should fall back to workspace
        // when browser secrets are not available
        (selectedSecretsSource === PlaygroundSecretsSource.BROWSER &&
          Object.entries(browserSecrets).some(
            ([key, value]) =>
              !value &&
              workspaceSecrets.has(key) &&
              !OPTIONAL_MODEL_SECRETS.includes(key)
          ));

      // Submit secrets based on the source
      // We submit browserSecrets if they contain all secrets needed
      // or if the user had selected browser secrets and
      // there are some secrets missing in the browser, so we fallback to workspace
      const submitSecrets = useOrFallbackToWorkspaceSecrets
        ? selectedSecretsSource === PlaygroundSecretsSource.BROWSER
          ? browserSecrets
          : {}
        : browserSecrets;

      // Use browser secrets or workspace secrets for manifest
      const manifestSecrets = useOrFallbackToWorkspaceSecrets
        ? Object.fromEntries(workspaceSecrets.entries())
        : browserSecrets;

      return {
        submitSecrets,
        manifestSecrets,
        useOrFallbackToWorkspaceSecrets,
      };
    };

    return prompts.map((prompt) => {
      const options = handleOptionsForSubmit(prompt.manifest, prompt.options);
      const {
        submitSecrets,
        manifestSecrets,
        useOrFallbackToWorkspaceSecrets,
      } = getSecrets(selectedSecretsSource);

      let manifestToUse = prompt.manifest;

      if (variableMapping && Object.keys(variableMapping).length > 0) {
        manifestToUse = replaceTemplateVariables(
          prompt.manifest,
          variableMapping
        );
      }

      return {
        manifest: removeOptionalModelSecrets(
          manifestToUse,
          manifestSecrets,
          OPTIONAL_MODEL_SECRETS
        ),
        secrets: submitSecrets,
        tools: options.tools as SerializedConstructor[] | undefined,
        tool_choice: options.tool_choice as string | undefined,
        parallel_tool_calls: options.parallel_tool_calls,
        requests_per_second: options.requests_per_second,
        options: omit(options, [
          'tools',
          'tool_choice',
          'parallel_tool_calls',
          'requests_per_second',
        ]),
        repo_handle: prompt.hubPrompt?.prompt.repo_handle,
        commit: prompt.hubPrompt?.prompt.last_commit_hash,
        ...(selectedTenant?.tenant_handle !== prompt.hubPrompt?.prompt.owner
          ? { owner: prompt.hubPrompt?.prompt.owner }
          : {}),
        use_or_fallback_to_workspace_secrets: useOrFallbackToWorkspaceSecrets,
      };
    });
  }, [
    selectedSecretsSource,
    prompts,
    browserSecrets,
    workspaceSecrets,
    selectedTenant?.tenant_handle,
    variableMapping,
  ]);
  const invokeSinglePlaygroundRun = useInvokePlaygroundPrompt(
    {
      onError: (err) => {
        createToast({
          title: 'Error running prompt',
          description: err.message,
          error: true,
        });
        setLoadingState('done');
      },
    },
    abortController
  );
  const invokeDatasetExperiment = useInvokePlaygroundDataset(
    {
      onError: (err) => {
        createToast({
          title: 'Error invoking experiment',
          description: err.message,
          error: true,
        });
        setLoadingState('done');
      },
    },
    abortController
  );

  function getRepoHandleFromPrompt(prompt: PlaygroundPromptType) {
    return prompt.hubPrompt?.prompt.repo_handle ?? '';
  }

  const { generate, resetData } = useStreamPlaygroundPrompt(
    false,
    abortController
  );

  const { generate: generateDataset, resetData: resetDatasetData } =
    useStreamPlaygroundPrompt(true, abortController);

  const segmentContext = useSegmentAnalyticsContext();

  const onSubmit = useCallback(
    async (streaming: boolean) => {
      const hasMissingSecrets = checkMissingSecrets(
        browserSecrets,
        selectedSecretsSource,
        workspaceSecrets
      );
      if (hasMissingSecrets) {
        openSecretsRef.current?.();
        return;
      }

      if (loadingState === 'loading') {
        abortController.abort();
        resetAbortController();
        setLoadingState('done');
        return;
      }

      setLoadingState('loading');
      clearRuns(true);
      resetData();

      if (segmentContext) {
        segmentContext.analytics.track('playground_submit', {
          ...segmentContext.data,
          input_source: inputSourceType,
          streaming,
        });
      }

      // manual inputs
      if (inputSourceType === 'manual') {
        if (streaming) {
          const modifiedInput = modifyInputForSubmit(input, inputSourceType);
          prompts.forEach((_, index) => {
            generate({ ...requests[index], input: modifiedInput }, index);
          });
        } else {
          const runPrompts = async () => {
            const allRuns = prompts.flatMap(async (prompt, promptIndex) => {
              const runs = await invokeSinglePlaygroundRun.trigger({
                json: {
                  ...requests[promptIndex],
                  input: modifyInputForSubmit(input, inputSourceType),
                  repetitions: repetitions,
                },
              });

              if (runs) {
                setPrompt(promptIndex, (prev) => ({
                  ...prev,
                  runs: runs
                    .map((r) => ({
                      ...r,
                      session_id: prompt.columnId,
                    }))
                    .map(formatRunOutput),
                }));
              }
            });

            await Promise.all(allRuns);
          };

          try {
            await runPrompts();
          } finally {
            setLoadingState('done');
          }
        }
      } else if (inputSourceType === 'dataset') {
        clearRuns();
        setLoadingState('loading');
        resetDatasetData();

        let recentlyGeneratedDatasetId: string | undefined;
        if (isCreatingNewDataset) {
          const dataset = await saveDataset();
          if (dataset) {
            recentlyGeneratedDatasetId = dataset.id;
            setDatasetId(dataset.id);
          }
        } else if (datasetId && hasBeenEdited) {
          const { newExamples, editedExistingExamples } =
            splitNewExamplesFromExistingEditedExamples(examples);
          await updateDataset(newExamples, editedExistingExamples);
        }
        if (!datasetId && !recentlyGeneratedDatasetId) {
          alert('Please select a dataset before running the prompt.');
          setLoadingState('done');
          return;
        }
        const datasetIdToUse: string = (recentlyGeneratedDatasetId ??
          datasetId)!;

        if (streaming) {
          prompts.forEach((prompt, index) => {
            generateDataset(
              {
                ...requests[index],
                dataset_id: datasetIdToUse,
                dataset_splits: getDatasetSplits(datasetSplits),
                project_name:
                  prompt.customExperimentName ??
                  createExperimentName({
                    repoHandle: getRepoHandleFromPrompt(prompt),
                    modelManifest:
                      getModelFromManifest(prompt.manifest) ?? undefined,
                  }),
                ...(selectedEvaluatorIds
                  ? {
                      evaluator_rules: selectedEvaluatorIds,
                    }
                  : {}),
              },
              index
            );
          });
        } else {
          await Promise.all(
            prompts.map(async (prompt, index) => {
              const runs = await invokeDatasetExperiment.trigger({
                json: {
                  ...requests[index],
                  dataset_id: datasetIdToUse,
                  dataset_splits: getDatasetSplits(datasetSplits),
                  repetitions: repetitions,
                  project_name:
                    prompt.customExperimentName ??
                    createExperimentName({
                      repoHandle: getRepoHandleFromPrompt(prompt),
                      modelManifest:
                        getModelFromManifest(prompt.manifest) ?? undefined,
                    }),
                  ...(selectedEvaluatorIds
                    ? {
                        evaluator_rules: selectedEvaluatorIds,
                      }
                    : {}),
                },
              });

              setPrompt(index, (prev) => ({
                ...prev,
                sessionId: runs?.[0].session_id,
                runs: runs?.map(formatRunOutput) ?? [],
              }));
            })
          );

          setLoadingState('done');
        }
      }
    },
    [
      browserSecrets,
      selectedSecretsSource,
      workspaceSecrets,
      loadingState,
      setLoadingState,
      clearRuns,
      resetData,
      inputSourceType,
      openSecretsRef,
      abortController,
      resetAbortController,
      prompts,
      generate,
      requests,
      input,
      invokeSinglePlaygroundRun,
      repetitions,
      setPrompt,
      resetDatasetData,
      isCreatingNewDataset,
      datasetId,
      hasBeenEdited,
      saveDataset,
      setDatasetId,
      examples,
      updateDataset,
      generateDataset,
      datasetSplits,
      selectedEvaluatorIds,
      invokeDatasetExperiment,
    ]
  );

  return onSubmit;
};
