import { PlusIcon } from '@langchain/untitled-ui-icons';

import { useCallback, useState } from 'react';

import { emptyOutputSchema } from '@/Pages/HubPlayground/HubPlayground.utils';
import { EMessageType } from '@/components/EditableMessage/types';
import { SchemaEditorModal } from '@/components/SchemaEditor/SchemaEditorModal';

import { PlaygroundPromptTopBar } from '../components/PlaygroundPromptTopBar';
import { Manifest } from '../components/manifest/Manifest';
import { manifestModelAndProvider } from '../components/manifest/serialized/ManifestConstructor';
import { getOpenAIModelCapabilities } from '../components/manifest/serialized/chat_models/ManifestChatOpenAI.utils';
import { usePlaygroundParentContext } from '../context/PlaygroundParentContext';
import {
  supportsEnforcedToolSelection,
  supportsTools,
} from '../utils/Playground.utils';
import { PlaygroundButton } from './PlaygroundButtons';
import { PlaygroundToolsButton } from './PlaygroundToolsButton';
import { useDeep, usePlaygroundStore } from './hooks/usePlaygroundStore';

export default function PromptTemplateSection({
  index,
  promptCollapseState,
  setPromptCollapseState,
}: {
  index: number;
  promptCollapseState: boolean;
  setPromptCollapseState: (index: number, isAllCollapsed?: boolean) => void;
}) {
  const { isSimplified } = usePlaygroundParentContext();
  const manifest = usePlaygroundStore(
    useDeep((state) => state.prompts[index].manifest)
  );

  const options = usePlaygroundStore(
    useDeep((state) => state.prompts[index].options)
  );
  const hubPrompt = usePlaygroundStore(
    useDeep((state) => state.prompts[index].hubPrompt)
  );

  const setManifest = usePlaygroundStore((state) => state.setManifest);
  const setEdited = usePlaygroundStore((state) => state.setEdited);
  const setPrompt = usePlaygroundStore((state) => state.setPrompt);

  const currentOutputSchema = manifest.kwargs.first.kwargs.schema_;
  const hasOutputSchema = !!currentOutputSchema;

  const manifestLast = manifest.kwargs.last;

  const modelAndProvider = manifestModelAndProvider({
    variant: 'line',
    value: manifestLast,
    options: {},
  });
  const toolsSupportedForModel = supportsTools(modelAndProvider);

  const isChatStyle = manifest.kwargs.first.id.at(-1) === 'ChatPromptTemplate';
  const [isSchemaModalOpen, setIsSchemaModalOpen] = useState(false);

  function onDeleteSchema() {
    const manifestWithoutSchema = {
      ...manifest,
      kwargs: {
        ...manifest.kwargs,
        first: {
          ...manifest.kwargs.first,
          id: ['langchain', 'prompts', 'chat', 'ChatPromptTemplate'],
          kwargs: {
            ...manifest.kwargs.first.kwargs,
          },
        },
      },
    };
    delete manifestWithoutSchema.kwargs.first.kwargs.schema_;
    setManifest(index, manifestWithoutSchema);
  }

  const onChangeManifest = useCallback(
    (value: Record<string, unknown>) => {
      setManifest(index, (prev) => ({
        ...prev,
        kwargs: { ...prev.kwargs, first: value },
      }));
      setPrompt(index, (prev) => ({
        ...prev,
        hubPrompt: prev.hubPrompt
          ? {
              ...prev.hubPrompt,
              edited: true,
            }
          : undefined,
      }));
    },
    [index, setManifest, setPrompt]
  );

  const { isSystemMessagesSupported } = getOpenAIModelCapabilities(
    modelAndProvider?.model ?? ''
  );

  return (
    <div className="my-3 flex h-full flex-col gap-1 overflow-y-hidden px-2">
      {!isSimplified &&
        hubPrompt?.commitHash?.slice(0, 8) !==
          hubPrompt?.prompt.last_commit_hash?.slice(0, 8) && (
          <div className="mb-2 rounded-md border border-warning bg-warning-primary p-1 text-xs">
            You're viewing an older version of this prompt. Committing changes
            will overwrite the latest version.
          </div>
        )}
      {!isSimplified && (
        <PlaygroundPromptTopBar
          index={index}
          isAllCollapsed={promptCollapseState}
          setIsAllCollapsed={() => setPromptCollapseState(index)}
        />
      )}

      <div className={'mt-0 flex h-full flex-col gap-2 overflow-y-hidden pr-4'}>
        <div className="-ml-1 h-full overflow-x-hidden">
          <Manifest
            key={`${hubPrompt?.prompt.full_name}-${hubPrompt?.commitHash}`}
            variant={'pane'}
            value={manifest.kwargs.first}
            onChange={onChangeManifest}
            options={options}
            onOptionsChange={() => void 0}
            onDeleteSchema={onDeleteSchema}
            hideTemplateFormatSelector
            outputSchemaAsModal
            minimizedSchema={true}
            disabledMessageTypes={
              !isSystemMessagesSupported ? [EMessageType.SYSTEM] : []
            }
            isAllCollapsed={promptCollapseState}
            setIsAllCollapsed={(isAllCollapsed) =>
              setPromptCollapseState(index, isAllCollapsed)
            }
            actions={
              <>
                {!hasOutputSchema && isChatStyle && (
                  <PlaygroundButton
                    text={'Output Schema'}
                    onClick={() => setIsSchemaModalOpen(true)}
                    tooltipTitle={
                      supportsEnforcedToolSelection(modelAndProvider)
                        ? 'Force output to follow a specific schema'
                        : "Model doesn't support output schema"
                    }
                    disabled={!supportsEnforcedToolSelection(modelAndProvider)}
                    startDecorator={<PlusIcon className="my-auto h-4 w-4" />}
                  />
                )}
                <PlaygroundToolsButton
                  index={index}
                  hasOutputSchema={hasOutputSchema}
                  toolsSupportedForModel={toolsSupportedForModel}
                />
              </>
            }
          />
        </div>
      </div>

      {/*
       * Only used with chat prompts when adding a schema from scratch
       * otherwise we use the modal in the ManifestStructuredPrompt component
       */}
      {isSchemaModalOpen && (
        <SchemaEditorModal
          isOpen={isSchemaModalOpen}
          onClose={() => setIsSchemaModalOpen(false)}
          value={emptyOutputSchema().schema_}
          onSubmit={(schema) => {
            setEdited(index, true);
            setManifest(index, {
              ...manifest,
              kwargs: {
                ...manifest.kwargs,
                first: {
                  ...manifest.kwargs.first,
                  id: [
                    'langchain_core',
                    'prompts',
                    'structured',
                    'StructuredPrompt',
                  ],
                  kwargs: {
                    ...manifest.kwargs.first.kwargs,
                    schema_: schema as Record<string, unknown>,
                  },
                },
              },
            });
            setIsSchemaModalOpen(false);
          }}
          title="Add Output Schema"
        />
      )}
    </div>
  );
}
