import { PlusIcon } from '@langchain/untitled-ui-icons';
import {
  CircularProgress,
  FormControl,
  FormLabel,
  Switch,
  Tooltip,
} from '@mui/joy';

import { useEffect, useMemo, useRef, useState } from 'react';

import {
  ColumnCategoryId,
  ColumnVisibilityPopover,
} from '@/Pages/DatasetSessionCompare/components/ColumnVisibilityPopover';
import { DatasetSessionControlHeader } from '@/Pages/DatasetSessionCompare/components/DatasetSessionControlHeader';
import {
  DatasetSessionCompareSettings,
  TextDisplayMode,
} from '@/Pages/DatasetSessionCompare/constants';
import { anyExampleHasAttachments } from '@/Pages/DatasetSessionCompare/utils/anyExampleHasAttachments';
import { useFancyRendering } from '@/components/RichTextEditor/utils/fancyRenderContext/useFancyRender';
import { useAuth } from '@/hooks/useAuth';
import {
  useCountExamples,
  useDataset,
  useOrganizationId,
  useSession,
} from '@/hooks/useSwr';
import {
  ExampleSchemaWithRunsAndOptionalFields,
  SessionSchema,
} from '@/types/schema';
import {
  appComparePath,
  appDatasetsPath,
  appIndexPath,
  appOrganizationPath,
} from '@/utils/constants';
import { xCount } from '@/utils/stringUtils';

import { DatasetAndSplitPicker } from '../components/DatasetAndSplitPicker';
import { PlaygroundDatasetRules } from '../components/PlaygroundDatasetRules';
import { usePlaygroundParentContext } from '../context/PlaygroundParentContext';
import { getDatasetSplits } from '../utils/Playground.utils';
import { EditableExperimentName } from './EditableExperimentName';
import { PlaygroundButton } from './PlaygroundButtons';
import { PlaygroundSectionHeader } from './PlaygroundSectionHeader';
import { SaveDatasetButton } from './SaveDatasetButton';
import { useDeep, usePlaygroundStore } from './hooks/usePlaygroundStore';
import { focusOnFirstExampleRowValue } from './utils';

export const PromptOutputSectionHeader = ({
  displaySettingsState,
  exampleRuns,
  isEditable,
}: {
  displaySettingsState: DatasetSessionCompareSettings;
  exampleRuns: ExampleSchemaWithRunsAndOptionalFields[];
  isEditable: boolean;
}) => {
  const { authed } = useAuth();
  const organizationId = useOrganizationId();
  const prompts = usePlaygroundStore(useDeep((state) => state.prompts));
  const datasetId = usePlaygroundStore((state) => state.datasetId);
  const inputSourceType = usePlaygroundStore((state) => state.inputSourceType);
  const evaluatorFeedbackKeys = usePlaygroundStore(
    (state) => state.evaluatorFeedbackKeys
  );
  const {
    setDatasetIdAndUpdateUrl,
    datasetSplits: splits,
    setDatasetSplits: setSplits,
    examples,
  } = usePlaygroundStore();

  const sessionIdsString = usePlaygroundStore((state) => {
    const sessionIds = new Set<string>();
    state.prompts.forEach((p) => p.sessionId && sessionIds.add(p.sessionId));

    return Array.from(sessionIds).join(',');
  });
  const firstSessionId = sessionIdsString.split(',')[0];
  const session = useSession(firstSessionId);
  const datasetSplits = usePlaygroundStore((state) => state.datasetSplits);

  // creating a new dataset
  const isCreatingNewDataset = usePlaygroundStore(
    (state) => state.isCreatingNewDataset
  );
  const onCreateNewDataset = usePlaygroundStore(
    (state) => state.onCreateNewDataset
  );

  const numPrompts = prompts.length;
  const firstPrompt = prompts[0];

  const { data: dataset } = useDataset(datasetId);
  const anyAttachments = useMemo(
    () => anyExampleHasAttachments(exampleRuns),
    [exampleRuns]
  );

  const setExampleCount = usePlaygroundStore((state) => state.setExampleCount);
  const examplesCount = usePlaygroundStore((state) => state.exampleCount);
  const { data: exampleCount, isLoading: isLoadingExamplesCount } =
    useCountExamples(
      datasetId
        ? {
            splits: getDatasetSplits(datasetSplits),
            dataset: datasetId ?? undefined,
          }
        : null,
      {}
    );

  useEffect(() => {
    if (exampleCount !== undefined) {
      setExampleCount(exampleCount);
    }
  }, [exampleCount, setExampleCount]);

  const headerRef = useRef<HTMLDivElement>(null);
  const [useSmallIcons, setUseSmallIcons] = useState(false);

  // use small icons when the output section is less than 700px wide
  useEffect(() => {
    const shouldUseSmallIcons = () => {
      if (
        headerRef.current?.clientWidth &&
        headerRef.current?.clientWidth < 480
      ) {
        setUseSmallIcons(true);
      } else {
        setUseSmallIcons(false);
      }
    };

    window.addEventListener('resize', shouldUseSmallIcons);

    return () => {
      window.removeEventListener('resize', shouldUseSmallIcons);
    };
  }, []);

  const isDataset = (datasetId && dataset) || isCreatingNewDataset;
  const { isSimplified } = usePlaygroundParentContext();
  const { fancyRendering, setFancyRendering } = useFancyRendering();

  const renderLeftActions = () => {
    if (!isDataset) return 'Output';
    const renderExamplesCount = () => {
      if (isLoadingExamplesCount)
        return (
          <CircularProgress
            size="sm"
            sx={{
              '--CircularProgress-size': '12px',
            }}
          />
        );
      if (examplesCount === undefined) return null;
      return (
        <Tooltip
          title={
            examplesCount > 20
              ? `Showing first 20 examples. View full experiment to see all results.`
              : undefined
          }
        >
          <span>
            {`${examplesCount > 20 ? '20/' : ''}${xCount(
              'Example',
              examplesCount
            )}`}
          </span>
        </Tooltip>
      );
    };

    const hasCurrentSession =
      datasetId &&
      firstPrompt.runs?.[0]?.session_id === session.data?.id &&
      session.data?.reference_dataset_id === datasetId;

    const sessionName =
      hasCurrentSession && numPrompts === 1 ? session.data?.name : undefined;

    const hasFullExperiment = numPrompts > 0 && hasCurrentSession;
    const openFullExperiment = () => {
      const target = `${appIndexPath}${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${datasetId}/${appComparePath}?selectedSessions=${sessionIdsString}&textDisplayMode=${TextDisplayMode.FULL}`;
      window.open(target, '_blank');
    };

    return (
      <div className="mr-2 flex flex-row items-center gap-3">
        {hasCurrentSession ? (
          <div className="flex flex-row gap-2">
            {sessionName && <span className="my-auto">Experiment</span>}
            {hasCurrentSession ? (
              <EditableExperimentName
                sessionId={session.data?.id}
                sessionName={sessionName || 'View Full Experiment'}
                textClassProp={hasFullExperiment ? 'border-0 ml-2 mr-1' : ''}
                inputTextClassProp="w-[300px]"
                onValueClick={
                  hasFullExperiment ? openFullExperiment : undefined
                }
                valueClickTooltipTitle={
                  sessionName ? 'View Full Experiment' : undefined
                }
                readOnly={numPrompts > 1}
              />
            ) : (
              <span>View Full Experiment</span>
            )}
          </div>
        ) : (
          <span className="whitespace-nowrap">New Experiment</span>
        )}
        <div className="my-auto whitespace-nowrap text-xs font-medium text-quaternary">
          {renderExamplesCount()}
        </div>
      </div>
    );
  };

  const { setIsReferenceOutputHidden, setIsReferenceInputHidden } =
    displaySettingsState;

  const renderRightActions = () => {
    if (isSimplified)
      return (
        <div className="flex">
          <FormControl orientation="horizontal">
            <FormLabel>Fancy rendering</FormLabel>
            <Switch
              title="Fancy rendering"
              checked={fancyRendering}
              onChange={() => setFancyRendering(!fancyRendering)}
              size="sm"
            />
          </FormControl>
        </div>
      );
    if (inputSourceType !== 'dataset') return null;
    return (
      <div className="flex flex-row items-center gap-2">
        {authed && (
          <DatasetAndSplitPicker
            onCreateNew={() => {
              onCreateNewDataset();
              setIsReferenceOutputHidden(false);
              setIsReferenceInputHidden(false);
            }}
            datasetId={datasetId}
            isCreatingNewDataset={isCreatingNewDataset}
            splits={splits}
            setSplits={setSplits}
            examples={examples}
            setDatasetIdAndUpdateUrl={setDatasetIdAndUpdateUrl}
            lightSkeletonTrigger={true}
          />
        )}

        {isCreatingNewDataset && (
          <ColumnVisibilityPopover
            displaySettingsState={displaySettingsState}
            aggregateFeedbackKeys={[]}
            noAttachments={true}
            sessions={[]}
            visibleCategories={[ColumnCategoryId.EXAMPLE_COLUMNS]}
          />
        )}

        {datasetId ? (
          <PlaygroundDatasetRules
            datasetId={datasetId}
            minimized={useSmallIcons}
          />
        ) : null}

        {isEditable && <AddRowButton />}

        {datasetId ? (
          <DatasetSessionControlHeader
            sessions={prompts
              .map((p, index) =>
                p.sessionId
                  ? ({
                      id: p.sessionId,
                      name: `Prompt ${index + 1}`,
                      run_facets: [],
                    } as Pick<SessionSchema, 'id' | 'name' | 'run_facets'>)
                  : null
              )
              .filter(
                (
                  session
                ): session is Pick<
                  SessionSchema,
                  'id' | 'name' | 'run_facets'
                > => session !== null
              )}
            datasetId={datasetId}
            displaySettingsState={displaySettingsState}
            datasetName={dataset?.name ?? ''}
            noAttachments={!anyAttachments}
            aggregateFeedbackKeys={evaluatorFeedbackKeys}
            isEditable={isEditable}
          />
        ) : null}

        {isEditable && (
          <SaveDatasetButton isNewDataset={isCreatingNewDataset} />
        )}
      </div>
    );
  };
  return (
    <PlaygroundSectionHeader
      ref={headerRef}
      title={renderLeftActions()}
      tooltipDescription="The language model's response based on your prompt and configured variables"
      rightActions={renderRightActions()}
      className="sticky top-0 min-h-12 w-full"
    />
  );
};

const AddRowButton = () => {
  const addNewExample = usePlaygroundStore((state) => state.addNewExample);
  return (
    <PlaygroundButton
      text="Row"
      onClick={() => {
        addNewExample();
        focusOnFirstExampleRowValue();
      }}
      startDecorator={<PlusIcon className="my-auto h-4 w-4" />}
      className="py-1"
      tooltipTitle="Add a new row to the dataset"
    />
  );
};
