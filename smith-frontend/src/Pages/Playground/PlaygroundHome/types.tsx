import type { SerializedConstructor } from '@langchain/core/load/serializable';

import { Dispatch, SetStateAction } from 'react';

import {
  EPromptType,
  MessageUnionType,
  RepoWithLookupsSchema,
  RunSchema,
} from '@/types/schema';

import {
  ExampleSchemaOrDraftWithEdited,
  PlaygroundInputSourceType,
  PlaygroundPromptType,
} from '../PlaygroundContext';
import { ManifestOptions } from '../utils/types';

export type PlaygroundMainProps = {
  forceReset?: boolean;
  promptManifest: SerializedConstructor;
  modelManifest: SerializedConstructor;
  options?: ManifestOptions;
  repo?: RepoWithLookupsSchema;
  type: EPromptType;
  datasetId?: string;
  useOutputForRunManifest?: boolean;
  disabledCompare?: boolean;
  showChatCompletionToggle?: boolean;
  run?: RunSchema;
  inputSourceType?: PlaygroundInputSourceType;
  commitHash?: string;
  // if true, we're rendering a simplified version of the playground and hide some UI elements
  isSimplified?: boolean;
  scrollRef?: Dispatch<SetStateAction<HTMLDivElement | null>>;
};

export type PlaygroundInputValueType =
  | string
  | boolean
  | MessageUnionType[]
  | Record<string, unknown>
  | undefined;

export type PlaygroundActions = {
  setIsAlignEvaluator: (isAlignEvaluator: boolean) => void;
  initializeState: (props: PlaygroundMainProps) => void;
  resetState: () => void;
  resetPrompt: (
    index: number,
    defaultModelManifest?: SerializedConstructor
  ) => void;
  setManifests: (
    manifests:
      | SerializedConstructor[]
      | ((prev: SerializedConstructor[]) => SerializedConstructor[])
  ) => void;
  resetAbortController: () => void;
  addPrompt: () => void;
  removePrompt: (columnId: string) => void;
  setLoadingState: (loading: 'loading' | 'done') => void;
  setInput: (input: Record<string, PlaygroundInputValueType>) => void;
  setSingleInput: (name: string, value: PlaygroundInputValueType) => void;
  setInputSourceTypeAndUpdateUrl: (
    inputSourceType: PlaygroundInputSourceType
  ) => void;
  setDatasetIdAndUpdateUrl: (datasetId?: string) => void;
  setDatasetSplits: (split: string[]) => void;
  setEvaluatorIdSelectionStates: (
    evaluatorIds: Record<string, boolean> | undefined
  ) => void;
  setSingleEvaluatorIdSelectionState: (
    evaluatorId: string,
    isSelected: boolean
  ) => void;
  setExamples: (examples: ExampleSchemaOrDraftWithEdited[]) => void;
  removeReferenceOutputFromExamples: () => void;
  setExample: (
    exampleId: string,
    example: (
      prevExample: ExampleSchemaOrDraftWithEdited
    ) => ExampleSchemaOrDraftWithEdited
  ) => void;
  setIsCreatingNewDataset: (isCreatingNewDataset: boolean) => void;
  clearRuns: (preserveSessionId?: boolean) => void;
  setPrompt: (
    index: number,
    prompt:
      | PlaygroundPromptType
      | ((prev: PlaygroundPromptType) => PlaygroundPromptType)
  ) => void;
  setEvaluatorFeedbackKeys: (keys: string[]) => void;
  setRepetitions: (num: number) => void;
  setManifest: (
    index: number,
    manifest:
      | SerializedConstructor
      | ((prev: SerializedConstructor) => SerializedConstructor)
  ) => void;
  setOptions: (index: number, options: ManifestOptions) => void;
  setEdited: (index: number, edited: boolean) => void;
  setCompareToOptimizedManifest: (
    optimizedManifest: SerializedConstructor
  ) => void;
  setIsStreaming: (isStreaming: boolean) => void;
  onCreateNewDataset: (useInputValues?: boolean) => void;
  addNewExample: () => void;
  setExampleCount: (exampleCount: number) => void;
  decreaseExampleCount: () => void;
  increaseExampleCount: () => void;
  markExamplesAsSaved: () => void;
  removeExampleFromDataset: (exampleId: string) => void;
};
