import { SerializedConstructor } from '@langchain/core/load/serializable';

import { DEFAULT_MODEL as DEFAULT_BEDROCK_CHAT_MODEL } from './components/manifest/serialized/chat_models/ManifestBedrockChat.utils';
import { DEFAULT_MODEL as DEFAULT_ANTHROPIC_MODEL } from './components/manifest/serialized/chat_models/ManifestChatAnthropic.utils';
import { DEFAULT_MODEL as DEFAULT_CHAT_DEEPSEEK_MODEL } from './components/manifest/serialized/chat_models/ManifestChatDeepSeek.utils';
import { DEFAULT_MODEL as DEFAULT_GOOGLE_GENAI_MODEL } from './components/manifest/serialized/chat_models/ManifestChatGoogleGenerativeAI.utils';
import { DEFAULT_MODEL as DEFAULT_VERTEX_AI_MODEL } from './components/manifest/serialized/chat_models/ManifestChatGoogleVertexAI.utils';
import { DEFAULT_MODEL as DEFAULT_CHAT_VERTEX_AI_MODEL } from './components/manifest/serialized/chat_models/ManifestChatGoogleVertexAI.utils';
import { DEFAULT_MODEL as DEFAULT_CHAT_GROQ_MODEL } from './components/manifest/serialized/chat_models/ManifestChatGroq.utils';
import { DEFAULT_MODEL as DEFAULT_CHAT_MISTRAL_AI_MODEL } from './components/manifest/serialized/chat_models/ManifestChatMistralAI.utils';
import { DEFAULT_MODEL as DEFAULT_OPENAI_MODEL } from './components/manifest/serialized/chat_models/ManifestChatOpenAI.utils';
import { DEFAULT_MODEL as DEFAULT_CHAT_XAI_MODEL } from './components/manifest/serialized/chat_models/ManifestChatXAI.utils';
import { DEFAULT_MODEL as DEFAULT_BEDROCK_MODEL } from './components/manifest/serialized/llms/ManifestBedrock.utils';

export const TEXT_MODEL_MAPPING = {
  OpenAI: 'langchain/llms/openai/OpenAI',
  AzureOpenAI: 'langchain/llms/openai/AzureOpenAI',
  Fireworks: 'langchain/llms/fireworks/Fireworks',
  VertexAI: 'langchain/llms/googlellm/VertexAI',
  Bedrock: 'langchain/llms/bedrock/Bedrock',
  CustomModel: 'langsmith/playground/CustomModel',
};

export const DEFAULT_TEXT_MODEL = TEXT_MODEL_MAPPING.OpenAI;

export const CHAT_MODEL_MAPPING = {
  ChatOpenAI: 'langchain/chat_models/openai/ChatOpenAI',
  AzureChatOpenAI: 'langchain/chat_models/azure_openai/AzureChatOpenAI',
  ChatAnthropic: 'langchain/chat_models/anthropic/ChatAnthropic',
  ChatGoogleGenerativeAI:
    'langchain_google_genai/chat_models/ChatGoogleGenerativeAI',
  ChatVertexAI: 'langchain/chat_models/vertexai/ChatVertexAI',
  ChatBedrockConverse: 'langchain_aws/chat_models/ChatBedrockConverse',
  // two of the same things, just different names in js and python
  BedrockChat: 'langchain/chat_models/bedrock/ChatBedrock',
  ChatBedrock: 'langchain/chat_models/bedrock/ChatBedrock',
  ChatMistralAI: 'langchain/chat_models/mistralai/ChatMistralAI',
  ChatFireworks: 'langchain/chat_models/fireworks/ChatFireworks',
  ChatGroq: 'langchain_groq/chat_models/ChatGroq',
  ChatDeepSeek: 'langchain_deepseek/chat_models/ChatDeepSeek',
  ChatCustomModel: 'langsmith/playground/ChatCustomModel',
  ChatXAI: 'langchain_xai/chat_models/ChatXAI',
};

export const DEFAULT_CHAT_MODEL = CHAT_MODEL_MAPPING.ChatOpenAI;

export const STRUCTURED_MODELS = [
  'langchain/chat_models/openai/ChatOpenAI',
  'langchain/chat_models/azure_openai/AzureChatOpenAI',
  'langchain/chat_models/anthropic/ChatAnthropic',
  'langchain/chat_models/vertexai/ChatVertexAI',
  'langchain_google_genai/chat_models/ChatGoogleGenerativeAI',
  'langchain/chat_models/fireworks/ChatFireworks',
  'langchain_aws/chat_models/ChatBedrockConverse',
  'langchain_groq/chat_models/ChatGroq',
  'langchain_xai/chat_models/ChatXAI',
  'langchain_deepseek/chat_models/ChatDeepSeek',
];

export const OPTIONAL_MODEL_SECRETS = ['AWS_SESSION_TOKEN'];

export const MODEL_SECRETS = {
  'langchain/llms/openai/OpenAI': { openai_api_key: 'OPENAI_API_KEY' },
  'langchain/llms/openai/AzureOpenAI': {
    openai_api_key: 'AZURE_OPENAI_API_KEY',
  },
  'langchain/chat_models/azure_openai/AzureChatOpenAI': {
    openai_api_key: 'AZURE_OPENAI_API_KEY',
  },
  'langchain/chat_models/openai/ChatOpenAI': {
    openai_api_key: 'OPENAI_API_KEY',
  },
  'langchain/chat_models/anthropic/ChatAnthropic': {
    anthropic_api_key: 'ANTHROPIC_API_KEY',
  },
  'langchain/llms/googlellm/VertexAI': {
    'authOptions.credentials': 'GOOGLE_VERTEX_AI_WEB_CREDENTIALS',
  },
  'langchain/chat_models/google_genai/ChatGoogleGenerativeAI': {
    google_api_key: 'GOOGLE_API_KEY',
  },
  'langchain/chat_models/mistral_ai/ChatMistralAI': {
    mistral_api_key: 'MISTRAL_API_KEY',
  },
  'langchain/chat_models/chat_integration/ChatVertexAI': {
    'authOptions.credentials': 'GOOGLE_VERTEX_AI_WEB_CREDENTIALS',
  },
  'langchain/llms/fireworks/Fireworks': {
    fireworks_api_key: 'FIREWORKS_API_KEY',
  },
  'langchain/chat_models/fireworks/ChatFireworks': {
    fireworks_api_key: 'FIREWORKS_API_KEY',
  },
  'langchain/llms/bedrock/Bedrock': {
    aws_access_key_id: 'AWS_ACCESS_KEY_ID',
    aws_secret_access_key: 'AWS_SECRET_ACCESS_KEY',
    aws_session_token: 'AWS_SESSION_TOKEN',
  },
  'langchain/chat_models/bedrock/BedrockChat': {
    aws_access_key_id: 'AWS_ACCESS_KEY_ID',
    aws_secret_access_key: 'AWS_SECRET_ACCESS_KEY',
    aws_session_token: 'AWS_SESSION_TOKEN',
  },
  'langsmith/playground/ChatCustomModel': {},
  'langsmith/playground/CustomModel': {},
  'langchain/chat_models/groq/ChatGroq': {
    groq_api_key: 'GROQ_API_KEY',
  },
  'langchain/chat_models/vertexai/ChatVertexAI': {
    credentials: 'GOOGLE_VERTEX_AI_WEB_CREDENTIALS',
  },
  'langchain/chat_models/mistralai/ChatMistralAI': {
    mistral_api_key: 'MISTRAL_API_KEY',
  },
  'langchain_google_genai/chat_models/ChatGoogleGenerativeAI': {
    google_api_key: 'GOOGLE_API_KEY',
  },
  'langchain/chat_models/bedrock/ChatBedrock': {
    aws_access_key_id: 'AWS_ACCESS_KEY_ID',
    aws_secret_access_key: 'AWS_SECRET_ACCESS_KEY',
    aws_session_token: 'AWS_SESSION_TOKEN',
  },
  'langchain_aws/chat_models/ChatBedrockConverse': {
    aws_access_key_id: 'AWS_ACCESS_KEY_ID',
    aws_secret_access_key: 'AWS_SECRET_ACCESS_KEY',
    aws_session_token: 'AWS_SESSION_TOKEN',
  },
  'langchain_groq/chat_models/ChatGroq': {
    groq_api_key: 'GROQ_API_KEY',
  },
  'langchain_xai/chat_models/ChatXAI': {
    api_key: 'XAI_API_KEY',
  },
  'langchain_deepseek/chat_models/ChatDeepSeek': {
    api_key: 'DEEPSEEK_API_KEY',
  },
};

// These are the values used in the UI components when switching between providers
// The default values are the values that will be recognized by the provider in the model config UI
// Anything else present in the manifest will go into the Extra Params section
export const MODEL_DEFAULT_KWARGS = {
  'langchain/llms/openai/OpenAI': {
    temperature: 1.0,
    max_tokens: undefined,
    top_p: 1.0,
    presence_penalty: 0.0,
    frequency_penalty: 0.0,
    model: DEFAULT_OPENAI_MODEL,
    seed: undefined,
  },
  'langchain/llms/openai/AzureOpenAI': {
    temperature: 1.0,
    max_tokens: undefined,
    top_p: 1.0,
    presence_penalty: 0.0,
    frequency_penalty: 0.0,
    deployment_name: '',
    azure_endpoint: '',
    openai_api_version: undefined,
    seed: undefined,
  },
  'langchain/chat_models/azure_openai/AzureChatOpenAI': {
    temperature: 1.0,
    max_tokens: undefined,
    top_p: 1.0,
    presence_penalty: 0.0,
    frequency_penalty: 0.0,
    max_completion_tokens: undefined,
    extra_headers: {},
    deployment_name: '',
    azure_endpoint: '',
    openai_api_version: undefined,
    response_format: undefined,
    seed: undefined,
  },
  'langchain/chat_models/openai/ChatOpenAI': {
    temperature: 1.0,
    max_tokens: undefined,
    top_p: 1.0,
    presence_penalty: 0.0,
    frequency_penalty: 0.0,
    model: DEFAULT_OPENAI_MODEL,
    extra_headers: {},
    base_url: undefined,
    response_format: undefined,
    seed: undefined,
    reasoning_effort: undefined,
  },
  'langchain/chat_models/anthropic/ChatAnthropic': {
    temperature: 1.0,
    max_tokens: 1024,
    top_p: null,
    top_k: null,
    model: DEFAULT_ANTHROPIC_MODEL,
  },
  'langchain/llms/googlellm/VertexAI': {
    temperature: 0.2,
    max_output_tokens: 1024,
    top_k: 40,
    top_p: -1,
    model: DEFAULT_VERTEX_AI_MODEL,
  },
  'langchain/chat_models/google_genai/ChatGoogleGenerativeAI': {
    temperature: 1,
    top_p: 1,
    max_tokens: undefined,
    model: DEFAULT_GOOGLE_GENAI_MODEL,
  },
  'langchain/chat_models/mistral_ai/ChatMistralAI': {
    temperature: 0.7,
    top_p: 1,
    max_tokens: undefined,
    model: DEFAULT_CHAT_MISTRAL_AI_MODEL,
  },
  'langchain/chat_models/chat_integration/ChatVertexAI': {
    temperature: 1,
    max_tokens: 1024,
    top_k: 40,
    top_p: 0.95,
    model: DEFAULT_CHAT_VERTEX_AI_MODEL,
  },
  'langchain/llms/fireworks/Fireworks': {
    temperature: 0.2,
    max_tokens: 1024,
    top_p: -1,
    model: undefined,
  },
  'langchain/chat_models/fireworks/ChatFireworks': {
    temperature: 0.2,
    max_tokens: 1024,
    top_p: 1,
    model: undefined,
  },
  'langchain/llms/bedrock/Bedrock': {
    temperature: 1,
    max_tokens: undefined,
    model_id: DEFAULT_BEDROCK_MODEL,
    region_name: undefined,
  },
  'langchain/chat_models/bedrock/BedrockChat': {
    temperature: 1,
    model_id: DEFAULT_BEDROCK_CHAT_MODEL,
    max_tokens: undefined,
    region_name: undefined,
  },
  'langsmith/playground/ChatCustomModel': {
    url: '/',
    configurable: {},
  },
  'langsmith/playground/CustomModel': {
    url: '/',
    configurable: {},
  },
  'langchain/chat_models/groq/ChatGroq': {
    temperature: 0.7,
    max_tokens: undefined,
    model: DEFAULT_CHAT_GROQ_MODEL,
  },
  'langchain/chat_models/vertexai/ChatVertexAI': {
    temperature: 1,
    max_tokens: 1024,
    top_k: 40,
    top_p: 0.95,
    model: DEFAULT_CHAT_VERTEX_AI_MODEL,
  },
  'langchain/chat_models/mistralai/ChatMistralAI': {
    temperature: 0.7,
    top_p: 1,
    max_tokens: undefined,
    model: DEFAULT_CHAT_MISTRAL_AI_MODEL,
  },
  'langchain_google_genai/chat_models/ChatGoogleGenerativeAI': {
    temperature: 1,
    top_p: 1,
    model: DEFAULT_GOOGLE_GENAI_MODEL,
    max_tokens: undefined,
  },
  'langchain/chat_models/bedrock/ChatBedrock': {
    temperature: 1,
    model_id: DEFAULT_BEDROCK_CHAT_MODEL,
    max_tokens: undefined,
    region_name: undefined,
  },
  'langchain_aws/chat_models/ChatBedrockConverse': {
    temperature: 1,
    model_id: DEFAULT_BEDROCK_CHAT_MODEL,
    max_tokens: undefined,
    region_name: undefined,
  },
  'langchain_groq/chat_models/ChatGroq': {
    temperature: 0.7,
    max_tokens: undefined,
    model: DEFAULT_CHAT_GROQ_MODEL,
  },
  'langchain_xai/chat_models/ChatXAI': {
    temperature: 1.0,
    max_tokens: null,
    top_p: 1.0,
    presence_penalty: 0.0,
    frequency_penalty: 0.0,
    model: DEFAULT_CHAT_XAI_MODEL,
  },
  'langchain_deepseek/chat_models/ChatDeepSeek': {
    temperature: 1,
    max_tokens: 4000,
    top_p: 1.0,
    presence_penalty: 0.0,
    frequency_penalty: 0.0,
    model: DEFAULT_CHAT_DEEPSEEK_MODEL,
  },
};

const modelsForPlayground = (chatModelMapping: Record<string, string>) => {
  return Array.from(new Set(Object.values(chatModelMapping)));
};

export const CHAT_MODELS_FOR_PLAYGROUND =
  modelsForPlayground(CHAT_MODEL_MAPPING);
export const TEXT_MODELS_FOR_PLAYGROUND =
  modelsForPlayground(TEXT_MODEL_MAPPING);

export const SUPPORTED_RUNNABLES = [
  {
    category: 'Chat Models',
    items: CHAT_MODELS_FOR_PLAYGROUND,
  },
  {
    category: 'LLMs',
    items: TEXT_MODELS_FOR_PLAYGROUND,
  },
  {
    category: 'Output Parsers',
    items: [
      'langchain/output_parsers/list/CommaSeparatedListOutputParser',
      'langchain/output_parsers/openai_functions/JsonOutputFunctionsParser',
      'langchain/output_parsers/openai_functions/JsonKeyOutputFunctionsParser',
      'langchain/output_parsers/openai_tools/JsonOutputToolsParser',
      'langchain/schema/output_parser/StrOutputParser',
      'langchain/output_parsers/regex/RegexParser',
    ],
    hiddenItems: [
      'langchain_core/output_parsers/list/CommaSeparatedListOutputParser',
      'langchain_core/output_parsers/string/StrOutputParser',
    ],
  },
  {
    category: 'Prompts',
    items: [
      'langchain/prompts/prompt/PromptTemplate',
      'langchain/prompts/chat/ChatPromptTemplate',
      'langchain_core/prompts/structured/StructuredPrompt',
    ],
    hiddenItems: [
      'langchain_core/prompts/prompt/PromptTemplate',
      'langchain_core/prompts/chat/ChatPromptTemplate',
    ],
  },
  {
    category: 'Structure',
    items: [
      'langchain/schema/runnable/RunnableSequence',
      'langchain/schema/runnable/RunnableMap',
    ],
    hiddenItems: [
      'langchain_core/runnables/RunnableSequence',
      'langchain_core/runnables/RunnableMap',
    ],
  },
];

export const DEFAULT_CHAT_MODEL_CONSTRUCTOR: SerializedConstructor = {
  lc: 1,
  type: 'constructor',
  id: DEFAULT_CHAT_MODEL?.split('/'),
  kwargs: MODEL_DEFAULT_KWARGS[DEFAULT_CHAT_MODEL],
};
