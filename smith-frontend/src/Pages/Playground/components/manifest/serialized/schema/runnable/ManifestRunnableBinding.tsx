import { SerializedConstructor } from '@langchain/core/load/serializable';

import { Manifest, ManifestPreview } from '../../../Manifest';
import {
  KwargsSerializedConstructor,
  ManifestEditorProps,
  ManifestPreviewProps,
} from '../../../Manifest.utils';

type RunnableBindingKwargs = {
  bound: SerializedConstructor;
  kwargs?: Record<string, unknown>;
  [key: string]: unknown;
};

type RunnableBindingConstructor =
  KwargsSerializedConstructor<RunnableBindingKwargs>;

function isRunnableBindingConstructor(
  value: SerializedConstructor
): value is RunnableBindingConstructor {
  return (
    value.id.join('/') === 'langchain/schema/runnable/RunnableBinding' ||
    value.id.join('/') === 'langchain_core/runnables/RunnableBinding'
  );
}

export function ManifestRunnableBinding(
  props: ManifestEditorProps<RunnableBindingConstructor | SerializedConstructor>
) {
  const kwargs = props.value.kwargs;

  if (!isRunnableBindingConstructor(props.value)) {
    return (
      <Manifest
        {...props}
        onOptionsChange={(kwargs) => {
          props.onChange({
            lc: 1,
            type: 'constructor',
            id: ['langchain', 'schema', 'runnable', 'RunnableBinding'],
            kwargs: {
              bound: props.value,
              kwargs,
            },
          });
        }}
      />
    );
  }

  // Combine the kwargs from kwargs.bound.kwargs and kwargs.kwargs
  // Going forward, we will be only using kwargs.bound.kwargs for the inner kwargs
  const innerKwargs = {
    ...props.value.kwargs.bound?.kwargs,
    ...props.value.kwargs.kwargs,
  };

  return (
    <Manifest
      {...props}
      value={kwargs.bound}
      onChange={(bound) =>
        props.onChange({
          ...props.value,
          kwargs: { ...props.value.kwargs, bound },
        })
      }
      options={innerKwargs}
      onOptionsChange={(kwargs) =>
        props.onChange({
          ...props.value,
          kwargs: {
            ...props.value.kwargs,
            bound: {
              ...props.value.kwargs.bound,
              kwargs: kwargs,
            },
            kwargs: undefined,
          },
        })
      }
    />
  );
}

export function ManifestRunnableBindingPreview(
  props: ManifestPreviewProps<RunnableBindingConstructor>
) {
  const kwargs = props.value.kwargs;
  return (
    <ManifestPreview
      {...props}
      value={kwargs.bound}
      options={kwargs.kwargs ?? {}}
    />
  );
}
