import { SerializedConstructor } from '@langchain/core/load/serializable';
import { FormLabel } from '@mui/joy';

import { SerializedFields } from 'node_modules/@langchain/core/dist/load/map_keys';
import { useMemo, useState } from 'react';
import { stringify } from 'yaml';

import { DEFAULT_CHAT_MODEL_CONSTRUCTOR } from '@/Pages/Playground/Playground.constants';
import { normalizeKwargsName } from '@/Pages/Playground/utils/Playground.utils';
import { CodeCard } from '@/components/Code/Code';
import { RunSchema } from '@/types/schema';
import { datadogLogs } from '@/utils/datadog/datadog';
import { parseLangSmithParamsFromRun } from '@/utils/langsmith-params';
import { cn } from '@/utils/tailwind';

import { Manifest, ManifestPreview } from '../Manifest';
import { ManifestEditorProps, ManifestPreviewProps } from '../Manifest.utils';
import { ManifestNavigation } from '../ManifestNavigation';
import { base, useManifestMap } from './ManifestConstructorMap';

export function ManifestConstructor(
  props: ManifestEditorProps<SerializedConstructor>
) {
  const manifestMap = useManifestMap();
  const name = props.value.id.at(-1);
  const kwargs = props.value.kwargs;

  const [activeKey, setActiveKey] = useState<string | null>(null);

  if (activeKey != null) {
    return (
      <Manifest
        {...props}
        navigation={{
          onBack: () => setActiveKey(null),
          parent: name,
          name: normalizeKwargsName(activeKey),
        }}
        value={kwargs[activeKey]}
        onChange={(value) => {
          const kwargs = (props.value as SerializedConstructor)?.kwargs;
          props.onChange({
            ...props.value,
            kwargs: { ...(kwargs ?? {}), [activeKey]: value },
          });
        }}
      />
    );
  }

  const ManifestSpecificEditor = manifestMap[props.value.id.join('/')]?.editor;

  if (ManifestSpecificEditor != null) {
    return <ManifestSpecificEditor {...props} />;
  } else if (ManifestSpecificEditor === null) {
    return null;
  }

  return (
    <CodeCard sx={{ p: 2, flexDirection: 'column', display: 'flex', gap: 2 }}>
      <ManifestNavigation navigation={props.navigation} />

      <span className="font-semibold">{name}</span>

      <div className="flex flex-col gap-3">
        {Object.keys(kwargs).map((key) => {
          return (
            <div key={key} className="flex flex-col gap-2">
              <FormLabel>{normalizeKwargsName(key)}</FormLabel>
              <ManifestPreview
                value={kwargs[key]}
                onClick={() => setActiveKey(key)}
                options={props.options}
              />
            </div>
          );
        })}
      </div>
    </CodeCard>
  );
}

export function ManifestConstructorPreview(
  props: ManifestPreviewProps<SerializedConstructor>
) {
  const manifestMap = useManifestMap();
  const id = props.value.id.at(-1) ?? '';

  const ManifestSpecificPreview =
    manifestMap[props.value.id.join('/')]?.preview;

  if (ManifestSpecificPreview != null) {
    return <ManifestSpecificPreview {...props} />;
  }

  if (props.variant === 'line') {
    return <>{id}</>;
  }

  return (
    <button
      type="button"
      onClick={props.onClick}
      className={cn(
        'flex w-auto flex-col rounded-md border bg-slate-50 p-3 shadow-sm dark:border-[rgb(24,24,24)] dark:bg-[rgb(9,9,13)]',
        props.onClick ? 'cursor-pointer' : 'cursor-default'
      )}
    >
      <span className="font-semibold">{id}</span>
      <pre className="max-w-[420px] overflow-hidden text-ellipsis">
        {stringify(props.value.kwargs).split('\n').slice(0, 3).join('\n')}
      </pre>
    </button>
  );
}

export function manifestModelAndProvider(
  props: ManifestPreviewProps<SerializedConstructor>
): {
  model: string | null;
  provider: string | null;
} | null {
  const manifestMap = base;
  const id = props.value.id.at(-1) ?? '';

  const info =
    manifestMap[props.value.id.join('/')]?.getModelAndProvider?.(props);

  if (info != null) {
    return info;
  }

  if (props.variant === 'line') {
    return { provider: id, model: null };
  }

  return {
    provider: id,
    model: stringify(props.value.kwargs).split('\n').slice(0, 3).join('\n'),
  };
}

const UNACCEPTED_KWARGS_MAPPING: Record<string, string[]> = {
  ChatOpenAI: ['azure_openai_api_key'],
};
function filterAndCombineKwargs(provider: string, kwargs?: SerializedFields) {
  // kwargs?.model_kwargs contains all of the invocation params that were unrecognized
  // so spread them here with the kwargs to ensure they're recognized in the playground
  // and then remove the model_kwargs field itself
  const allKwargs = { ...kwargs, ...kwargs?.model_kwargs };
  delete allKwargs.model_kwargs;

  if (!provider || !allKwargs) return allKwargs;
  const unacceptedKwargs =
    provider in UNACCEPTED_KWARGS_MAPPING
      ? UNACCEPTED_KWARGS_MAPPING[provider]
      : [];
  if (!unacceptedKwargs) return allKwargs;
  return Object.fromEntries(
    Object.entries(allKwargs).filter(([key]) => !unacceptedKwargs.includes(key))
  );
}

export function inferManifestFromRun(
  manifestMap: Record<string, any>,
  serializedModel?: SerializedConstructor | null,
  extra?: Pick<RunSchema, 'extra'> | null,
  runName?: string
): SerializedConstructor {
  try {
    const params = parseLangSmithParamsFromRun(extra);
    const fromMetadata = (() => {
      try {
        for (const key in manifestMap) {
          const inferManifest = manifestMap[key]?.inferManifestFromRun?.(
            params,
            serializedModel
          );
          if (inferManifest) return inferManifest;
        }
        return null;
      } catch (error) {
        datadogLogs?.logger?.error('manifest_inference_metadata_error', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          params: JSON.stringify(params),
          serializedModel: JSON.stringify(serializedModel),
        });
        return null;
      }
    })();

    const fromRun =
      typeof serializedModel === 'object' &&
      serializedModel &&
      'lc' in serializedModel
        ? (serializedModel as SerializedConstructor)
        : null;

    if (!fromMetadata && !fromRun) {
      datadogLogs?.logger?.warn('manifest_inference_fallback', {
        reason: 'no_metadata_or_run',
        params: JSON.stringify(params),
        serializedModel: JSON.stringify(serializedModel),
      });
      return DEFAULT_CHAT_MODEL_CONSTRUCTOR;
    }

    const graph = fromMetadata?.graph ?? fromRun?.graph ?? undefined;
    const id = fromMetadata?.id ?? fromRun?.id ?? [];
    const providerName = id.at(-1);
    return {
      lc: 1,
      type: 'constructor',
      id: id,
      name: fromMetadata?.name ?? fromRun?.name ?? runName ?? '',
      kwargs: {
        ...filterAndCombineKwargs(providerName, fromRun?.kwargs),
        ...fromMetadata?.kwargs,
        cache: undefined,
      },
      ...(graph !== undefined && { graph: graph }),
    };
  } catch (error) {
    datadogLogs?.logger?.error('manifest_inference_error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      params: JSON.stringify(extra),
      serializedModel: JSON.stringify(serializedModel),
    });
    return DEFAULT_CHAT_MODEL_CONSTRUCTOR;
  }
}

export function useInferManifestFromRun(
  serializedModel?: SerializedConstructor | null,
  extra?: Pick<RunSchema, 'extra'> | null,
  runName?: string
) {
  const manifestMap = useManifestMap();
  return useMemo(() => {
    try {
      return inferManifestFromRun(manifestMap, serializedModel, extra, runName);
    } catch (error) {
      datadogLogs?.logger?.error('manifest_inference_hook_error', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        params: JSON.stringify(extra),
        serializedModel: JSON.stringify(serializedModel),
      });
      return DEFAULT_CHAT_MODEL_CONSTRUCTOR;
    }
  }, [manifestMap, serializedModel, extra, runName]);
}
