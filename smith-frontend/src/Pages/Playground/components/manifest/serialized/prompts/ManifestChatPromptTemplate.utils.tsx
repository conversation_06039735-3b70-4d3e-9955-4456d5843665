import {
  AIMessage,
  BaseMessage,
  ChatMessage,
  FunctionMessage,
  HumanMessage,
  SystemMessage,
  ToolMessage,
} from '@langchain/core/messages';
import {
  AIMessagePromptTemplate,
  BaseMessagePromptTemplate,
  ChatMessagePromptTemplate,
  HumanMessagePromptTemplate,
  MessagesPlaceholder,
  SystemMessagePromptTemplate,
  TemplateFormat,
} from '@langchain/core/prompts';

import {
  MessageContentPart,
  MessageContentPartImageUrl,
  MessageContentPartLangChainMultimodal,
  MessageContentPartOpenAIMultimodal,
  MessageContentPartText,
  MessageUnionType,
  SerializedMessage,
  StoredMessage,
  isMessageContentPartLangChainMultimodal,
  isMessageContentPartOpenAIMultimodal,
} from '@/types/schema';
import { convertMessageContentToText, isStoredMessage } from '@/utils/messages';

import { KwargsSerializedConstructor } from '../../Manifest.utils';

export type SerializedChatPromptTemplateMessagesKwarg = Array<
  // MessagesPlaceholder
  | KwargsSerializedConstructor<{
      variable_name: string;
    }>
  // ChatMessagePromptTemplate | HumanMessagePromptTemplate | SystemMessagePromptTemplate | AIMessagePromptTemplate
  | KwargsSerializedConstructor<{
      prompt: KwargsSerializedConstructor<{
        template: string;
      }>;
    }>
  // BaseMessage
  | SerializedMessage
>;

export function convertMessagesToManifestKwargs(
  changeMsgs: MessageUnionType[],
  templateFormat?: TemplateFormat
) {
  const validMsgs = changeMsgs
    .map((value) => {
      if ('type' in value && 'data' in value) {
        return value;
      }

      return null;
    })
    .filter((i): i is StoredMessage => i != null);

  // the output type of this is the input type of template arg to .fromTemplate
  function convertMessageContentToTemplate(
    content:
      | string
      | Array<
          | MessageContentPartText
          | MessageContentPartImageUrl
          | MessageContentPartOpenAIMultimodal
          | MessageContentPartLangChainMultimodal
        >
  ):
    | string
    | Array<
        | { image_url?: string | Record<string, unknown> }
        | { text?: string }
        | Record<string, unknown>
      > {
    if (!Array.isArray(content)) return content;

    const formattedContent = content.map(
      (
        i:
          | MessageContentPartText
          | MessageContentPartImageUrl
          | MessageContentPartLangChainMultimodal
          | MessageContentPartOpenAIMultimodal
      ) => {
        switch (i.type) {
          case 'text': {
            return { text: i.text };
          }
          case 'image_url': {
            return {
              image_url:
                typeof i.image_url === 'string'
                  ? { url: i.image_url }
                  : i.image_url,
            };
          }
          case 'file':
            if (isMessageContentPartLangChainMultimodal(i)) {
              return {
                type: 'file',
                file: {
                  file_data: i.data,
                  filename: 'pdf_file',
                },
              };
            } else {
              return i;
            }
          case 'audio':
            // langchain audio format
            return {
              type: 'input_audio',
              input_audio: {
                data: i.data,
                format: i.mime_type.split('/')[1],
              },
            };
          case 'input_audio':
            // openai audio format
            return i;
        }
      }
    );

    // The text editor turns newlines into empty text blocks.
    // This merges those empty blocks into the previous text block,
    // restoring them when pulled via SDK.
    const mergedTextBlocks = formattedContent.reduce<
      Array<{
        text?: string;
        image_url?: string | Record<string, unknown>;
        [key: string]: unknown;
      }>
    >((acc, curr) => {
      if (curr && 'text' in curr && curr.text === '') {
        const previousBlock = acc.at(-1);
        if (
          previousBlock &&
          'text' in previousBlock &&
          typeof previousBlock.text === 'string'
        ) {
          previousBlock.text = previousBlock.text + '\n';
          return acc;
        } else {
          return [...acc, { text: '\n' }];
        }
      } else {
        return [
          ...acc,
          curr as {
            text?: string;
            image_url?: string | Record<string, unknown>;
            [key: string]: unknown;
          },
        ];
      }
    }, []);

    return mergedTextBlocks;
  }

  try {
    const messages = validMsgs.map(
      (value): BaseMessagePromptTemplate | BaseMessage => {
        if (value.type === 'human') {
          try {
            return HumanMessagePromptTemplate.fromTemplate(
              convertMessageContentToTemplate(
                value.data.content as
                  | string
                  | MessageContentPartText[]
                  | MessageContentPartImageUrl[]
                  | MessageContentPartOpenAIMultimodal[]
                  | MessageContentPartLangChainMultimodal[]
              ),
              { templateFormat: templateFormat }
            );
          } catch {
            return new HumanMessage({
              ...value.data,
              content: convertMessageContentToText(value.data.content),
            });
          }
        }

        if (value.type === 'ai' || value.type === 'aichunk') {
          try {
            const hasAdditionalKwargs =
              !!value.data.additional_kwargs &&
              value.data.additional_kwargs != null &&
              Object.keys(value.data.additional_kwargs).length > 0;

            const hasToolCalls =
              value.data.tool_calls != null && value.data.tool_calls.length > 0;

            if (hasAdditionalKwargs || hasToolCalls)
              throw new Error('Message is a function call');
            return AIMessagePromptTemplate.fromTemplate(
              convertMessageContentToTemplate(
                value.data.content as
                  | string
                  | MessageContentPartText[]
                  | MessageContentPartImageUrl[]
                  | MessageContentPartOpenAIMultimodal[]
                  | MessageContentPartLangChainMultimodal[]
              ),
              { templateFormat: templateFormat }
            );
          } catch {
            if (value.data.name?.length == 0) {
              delete value.data.name;
            }
            if (value.data.type != null) {
              delete value.data.type;
            }
            return new AIMessage({
              ...value.data,
              content: convertMessageContentToText(
                value.data.content as
                  | string
                  | MessageContentPartText[]
                  | MessageContentPartImageUrl[]
                  | MessageContentPartOpenAIMultimodal[]
                  | MessageContentPartLangChainMultimodal[]
              ),
            });
          }
        }

        if (value.type === 'system') {
          try {
            return SystemMessagePromptTemplate.fromTemplate(
              convertMessageContentToTemplate(
                value.data.content as
                  | string
                  | MessageContentPartText[]
                  | MessageContentPartImageUrl[]
                  | MessageContentPartOpenAIMultimodal[]
                  | MessageContentPartLangChainMultimodal[]
              ),
              { templateFormat: templateFormat }
            );
          } catch {
            return new SystemMessage({
              ...value.data,
              content: convertMessageContentToText(value.data.content),
            });
          }
        }

        if (value.type === 'function') {
          return new FunctionMessage({
            content: convertMessageContentToText(value.data.content),
            name: value.data.name ?? '',
          });
        }

        if (value.type === 'tool') {
          return new ToolMessage({
            content: convertMessageContentToText(value.data.content),
            tool_call_id: value.data.tool_call_id ?? '',
            name: value.data.name ?? '',
          });
        }

        if (value.type === 'messages list' || value.type === 'placeholder') {
          return new MessagesPlaceholder(
            convertMessageContentToText(value.data.content)
          );
        }

        if (value.type === 'chat') {
          // handle case of new chat message, where role is not set yet
          const role = value.data.role ?? '';
          try {
            return ChatMessagePromptTemplate.fromTemplate(
              convertMessageContentToText(value.data.content),
              role
            );
          } catch {
            return new ChatMessage({
              ...value.data,
              content: convertMessageContentToText(value.data.content),
              role,
            });
          }
        }

        throw new Error(`Unimplemented type on ${JSON.stringify(value)}`);
      }
    );

    return {
      messages: JSON.parse(JSON.stringify(messages)),
      input_variables: messages
        .filter((i): i is BaseMessagePromptTemplate => 'inputVariables' in i)
        .map((i) => i.inputVariables)
        .flat(),
    };
  } catch (err) {
    console.error(err);
    // This happens eg. while there is an unterminated template variable
    return null;
  }
}

export function extractTemplateFormatFromKwargs(kwargs: any) {
  return (
    kwargs.template_format ??
    (kwargs.messages
      ? kwargs.messages[0]?.kwargs?.prompt?.kwargs?.template_format ??
        'f-string'
      : kwargs?.template_format ?? 'f-string')
  );
}

// note this function does not work if values is of type StoredMessage, since StoredMessage does not have an id field
export function convertManifestToMessages(
  values: SerializedChatPromptTemplateMessagesKwarg | StoredMessage[]
): MessageUnionType[] {
  return values.flatMap((value) => {
    if (isStoredMessage(value)) return [value];
    const type = value.id?.join('.');
    const typeName = value.id?.at(-1);
    try {
      // MessagePlaceholder
      if ('variable_name' in value.kwargs) {
        return [
          {
            type: 'messages list',
            data: { content: `${value.kwargs.variable_name}` },
          },
        ];
      }

      // PromptTemplates
      if ('prompt' in value.kwargs) {
        return [
          {
            // e.g. AIMessagePromptTemplate -> ai
            type: typeName.replace('MessagePromptTemplate', '').toLowerCase(),
            data: {
              content: Array.isArray(value.kwargs.prompt)
                ? value.kwargs.prompt.map(
                    (messageContentPart): MessageContentPart => {
                      const template = messageContentPart.kwargs.template;
                      if (typeof template === 'string') {
                        return { type: 'text', text: template };
                      } else if (
                        isMessageContentPartOpenAIMultimodal(template)
                      ) {
                        return template;
                      } else if (
                        isMessageContentPartLangChainMultimodal(template)
                      ) {
                        switch (template.type) {
                          case 'file':
                            return {
                              type: 'file',
                              file: {
                                file_data: template.data ?? '',
                                filename: 'PDF file',
                              },
                            };
                          case 'audio':
                            return {
                              type: 'input_audio',
                              input_audio: {
                                data: template.data ?? '',
                                format: template.mime_type.split('/')[1] as
                                  | 'wav'
                                  | 'mp3',
                              },
                            };
                        }
                      }
                      return {
                        type: 'image_url',
                        image_url: template.url,
                      };
                    }
                  )
                : value.kwargs.prompt.kwargs.template,
              ...(value.id.at(-1) === 'ChatMessagePromptTemplate'
                ? { role: value.kwargs.role }
                : {}),
            },
          },
        ];
      }

      // Messages
      if ('content' in value.kwargs) {
        return [
          {
            // e.g. SystemMessage -> system
            type: typeName.replace('Message', '').toLowerCase(),
            data: value.kwargs,
          },
        ];
      }

      if ('messages' in value.kwargs) {
        return convertManifestToMessages(value.kwargs.messages);
      }
    } catch (e) {
      console.error(e);
    }
    // backup case - display error if bad type
    return [
      {
        type: `Unsupported ${typeName}`,
        data: { content: `Error while rendering this ${type}` },
      },
    ];
  });
}
