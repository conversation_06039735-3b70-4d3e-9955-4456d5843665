import { SerializedConstructor } from '@langchain/core/load/serializable';
import { Chip, FormLabel, Option, Select } from '@mui/joy';

import { TEXT_MODEL_MAPPING } from '@/Pages/Playground/Playground.constants';
import {
  ManifestCommonOpenAIProviderType,
  ManifestOpenAISeed,
} from '@/Pages/Playground/components/manifest/serialized/common/ManifestCommonOpenAI';
import OpenAILogo from '@/icons/OpenAILogo.svg?react';
import { cn } from '@/utils/tailwind';

import {
  ManifestEditorProps,
  ManifestPreviewProps,
  tableVariantContainerClass,
} from '../../Manifest.utils';
import { ManifestKeyValuesEditor } from '../../ManifestKeyValuesEditor';
import { ManifestNavigation } from '../../ManifestNavigation';
import { ManifestSelectModel } from '../../ManifestSelectModel';
import {
  ManifestProviderReplaceWithSwitch,
  getLabelFromIdList,
} from '../../ManifestSelectProvider';
import { ManifestSlider } from '../../ManifestSlider';
import { ManifestStopEditor } from '../../ManifestStopEditor';
import { ManifestJSAzureOpenAIEditor } from '../common/ManifestCommonAzureOpenAI';
import {
  ManifestChatOpenAIFunctions,
  ManifestChatOpenAIJsonMode,
} from '../common/ManifestCommonChatOpenAI';
import {
  DEFAULT_MODEL,
  MODELS,
  getOpenAIModelCapabilities,
} from './ManifestChatOpenAI.utils';

export function ManifestChatOpenAI(
  props: ManifestEditorProps<SerializedConstructor>
) {
  const kwargs = props.value.kwargs;

  const isEvaluator = props.variant === 'evaluator';

  const { isTemperatureSupported, isReasoningEffortSupported } =
    getOpenAIModelCapabilities(kwargs.model);

  return (
    <>
      <ManifestNavigation navigation={props.navigation} />

      <div
        className={cn(
          'flex flex-col gap-4',
          props.variant === 'table' && tableVariantContainerClass
        )}
      >
        <ManifestProviderReplaceWithSwitch
          variant={props.variant}
          onChange={props.onChange}
          value={props.value}
          readOnly={props.readOnly}
          isChatModel={true}
          otherModelId={TEXT_MODEL_MAPPING['OpenAI']}
        />

        <ManifestSelectModel
          variant={props.variant}
          models={MODELS}
          dynamic={{
            id: props.value.id,
            secrets: props.secrets,
          }}
          onChange={(model) => {
            const newKwargs = { ...kwargs, model } as {
              model: string;
            } & Record<string, unknown>;
            // Recompute in the onChange handler so that the new manifest
            // updates immediately
            const {
              isTemperatureSupported: isTemperatureSupportedForNewManifest,
              isReasoningEffortSupported:
                isReasoningEffortSupportedForNewManifest,
            } = getOpenAIModelCapabilities(newKwargs.model);
            if ('model_name' in newKwargs) {
              delete newKwargs.model_name;
            }
            if (
              !isTemperatureSupportedForNewManifest &&
              'temperature' in newKwargs
            ) {
              delete newKwargs.temperature;
            }
            if (
              !isReasoningEffortSupportedForNewManifest &&
              'reasoning_effort' in newKwargs
            ) {
              delete newKwargs.reasoning_effort;
            }
            props.onChange({ ...props.value, kwargs: newKwargs });
          }}
          value={kwargs.model ?? kwargs.model_name ?? DEFAULT_MODEL}
          readOnly={props.readOnly}
        />

        <div
          className={cn('pt-1', !isEvaluator ? 'grid grid-cols-2 gap-4' : '')}
        >
          <ManifestSlider
            title="Temperature"
            value={kwargs.temperature}
            min={0}
            max={2}
            step={0.1}
            onChange={(temperature) => {
              props.onChange({
                ...props.value,
                kwargs: { ...kwargs, temperature },
              });
            }}
            readOnly={props.readOnly}
            variant={props.variant}
            disabled={!isTemperatureSupported}
            disabledMessage={
              isTemperatureSupported
                ? ''
                : 'This model does not support temperature'
            }
          />

          {!isEvaluator && (
            <>
              <ManifestSlider
                title="Max Output Tokens"
                value={kwargs.max_tokens == null ? -1 : kwargs.max_tokens}
                min={-1}
                max={65536}
                step={1}
                onChange={(max_tokens) =>
                  props.onChange({
                    ...props.value,
                    kwargs: {
                      ...kwargs,
                      max_tokens: max_tokens === -1 ? null : max_tokens,
                    },
                  })
                }
                readOnly={props.readOnly}
                variant={props.variant}
              />

              <ManifestSlider
                title="Top P"
                value={kwargs.top_p}
                min={0}
                max={1}
                step={0.01}
                onChange={(top_p) =>
                  props.onChange({
                    ...props.value,
                    kwargs: { ...kwargs, top_p },
                  })
                }
                readOnly={props.readOnly}
                variant={props.variant}
              />

              <ManifestSlider
                title="Presence Penalty"
                value={kwargs.presence_penalty}
                min={-2}
                max={2}
                step={0.01}
                onChange={(presence_penalty) => {
                  props.onChange({
                    ...props.value,
                    kwargs: { ...kwargs, presence_penalty },
                  });
                }}
                readOnly={props.readOnly}
                variant={props.variant}
                checked={kwargs.presence_penalty != null}
                onCheckChange={(checked) => {
                  props.onChange({
                    ...props.value,
                    kwargs: {
                      ...kwargs,
                      presence_penalty: checked ? 0 : null,
                    },
                  });
                }}
                inputPosition="right"
              />

              <ManifestSlider
                title="Frequency Penalty"
                value={kwargs.frequency_penalty}
                min={-2}
                max={2}
                step={0.01}
                onChange={(frequency_penalty) => {
                  props.onChange({
                    ...props.value,
                    kwargs: { ...kwargs, frequency_penalty },
                  });
                }}
                readOnly={props.readOnly}
                variant={props.variant}
                checked={kwargs.frequency_penalty != null}
                onCheckChange={(checked) => {
                  props.onChange({
                    ...props.value,
                    kwargs: {
                      ...kwargs,
                      frequency_penalty: checked ? 0 : null,
                    },
                  });
                }}
                inputPosition="right"
              />

              {isReasoningEffortSupported && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm font-medium">Reasoning Effort</span>
                  <Select
                    value={kwargs.reasoning_effort}
                    placeholder="None"
                    onChange={(_, value) => {
                      if (value) {
                        props.onChange({
                          ...props.value,
                          kwargs: {
                            ...kwargs,
                            reasoning_effort: value,
                          },
                        });
                      }
                    }}
                    size="sm"
                  >
                    <Option value="low">Low</Option>
                    <Option value="medium">Medium</Option>
                    <Option value="high">High</Option>
                  </Select>
                </div>
              )}
            </>
          )}
        </div>
        {!isEvaluator && (
          <>
            {!props.hideTools && <ManifestChatOpenAIFunctions {...props} />}

            {!props.readOnly && <ManifestOpenAISeed {...props} />}

            {!props.readOnly && <ManifestChatOpenAIJsonMode {...props} />}

            {props.options && (!props.readOnly || !!props.options.stop) && (
              <div className="flex flex-col gap-2">
                <FormLabel>Stop Sequences</FormLabel>
                <ManifestStopEditor
                  value={props.options.stop}
                  onChange={(stop) =>
                    props.onOptionsChange({
                      ...props.options,
                      stop,
                    })
                  }
                  readOnly={props.readOnly}
                />
              </div>
            )}
          </>
        )}
        {!props.readOnly && (
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <div className="text-sm font-medium">Provider Options</div>
              <ManifestCommonOpenAIProviderType {...props} />
            </div>
            <ManifestKeyValuesEditor
              title="Extra Headers"
              value={kwargs.extra_headers}
              onChange={(extra_headers) => {
                props.onChange({
                  ...props.value,
                  kwargs: { ...kwargs, extra_headers },
                });
              }}
              variant={props.variant}
            />
          </div>
        )}
        <ManifestJSAzureOpenAIEditor {...props} />
      </div>
    </>
  );
}

export function ManifestChatOpenAIPreview(
  props: ManifestPreviewProps<SerializedConstructor>
) {
  const displayLabel =
    getLabelFromIdList(props.value.id) ?? props.value.id.at(-1) ?? '';
  const kwargs = props.value.kwargs;
  const model = kwargs.model ?? kwargs.model_name ?? DEFAULT_MODEL;

  if (props.variant === 'line') {
    return (
      <div className="flex flex-row items-center gap-1">
        <span className="font-semibold">{displayLabel}</span>
        <span>{model}</span>
      </div>
    );
  }

  return (
    <button
      type="button"
      onClick={props.onClick}
      className={cn(
        'flex w-auto select-none flex-col rounded-md bg-neutral-50 p-4 shadow-sm dark:bg-[rgb(9,9,13)]',
        props.onClick ? 'cursor-pointer' : 'cursor-default'
      )}
    >
      <span className="flex items-center gap-2">
        <OpenAILogo />
        <span className="font-semibold">{displayLabel}</span>
      </span>

      <span className="mt-3 flex items-center gap-1">
        <Chip variant="outlined" size="sm" color="neutral">
          Model: {model}
        </Chip>

        {kwargs.temperature != null && (
          <Chip variant="outlined" size="sm" color="neutral">
            Temperature: {kwargs.temperature}
          </Chip>
        )}

        {kwargs.max_tokens != null && (
          <Chip variant="outlined" size="sm" color="neutral">
            Maximum Length: {kwargs.max_tokens}
          </Chip>
        )}
      </span>
    </button>
  );
}
