import { describe, expect, it } from 'vitest';

import { isAnyOSeriesModel } from '../ManifestChatOpenAI.utils';

describe('isAnyOSeriesModel', () => {
  // Test cases for O1 series models
  describe('O1 series models', () => {
    it('should identify o1 model correctly', () => {
      const result = isAnyOSeriesModel('o1');
      expect(result.isO1).toBe(true);
      expect(result.isO1Generic).toBe(true);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });

    it('should identify o1-mini model correctly', () => {
      const result = isAnyOSeriesModel('o1-mini');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(true);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });

    it('should identify o1-preview model correctly', () => {
      const result = isAnyOSeriesModel('o1-preview');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(true);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });

    it('should identify o1-pro model correctly', () => {
      const result = isAnyOSeriesModel('o1-pro');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(true);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });
  });

  // Test cases for O3 series models
  describe('O3 series models', () => {
    it('should identify o3-mini model correctly', () => {
      const result = isAnyOSeriesModel('o3-mini');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(true);
      expect(result.isO4).toBe(false);
    });
  });

  // Test cases for O4 series models
  describe('O4 series models', () => {
    it('should identify o4-mini model correctly', () => {
      const result = isAnyOSeriesModel('o4-mini');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(true);
    });

    it('should identify o4 model correctly', () => {
      const result = isAnyOSeriesModel('o4');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(true);
    });
  });

  // Test cases for non-O-series models
  describe('Non-O-series models', () => {
    it('should identify gpt-4o model correctly', () => {
      const result = isAnyOSeriesModel('gpt-4o');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });

    it('should identify claude-3.7 model correctly', () => {
      const result = isAnyOSeriesModel('claude-3.7');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });

    it('should identify gpt-4-turbo model correctly', () => {
      const result = isAnyOSeriesModel('gpt-4-turbo');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });
  });

  // Test cases for edge cases
  describe('Edge cases', () => {
    it('should handle undefined model', () => {
      const result = isAnyOSeriesModel(undefined);
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });

    it('should handle empty string model', () => {
      const result = isAnyOSeriesModel('');
      expect(result.isO1).toBe(false);
      expect(result.isO1Generic).toBe(false);
      expect(result.isO3).toBe(false);
      expect(result.isO4).toBe(false);
    });
  });
});
