import { Key01Icon } from '@langchain/untitled-ui-icons';
import { Typography } from '@mui/joy';
import * as Popover from '@radix-ui/react-popover';

import { FormEvent, useCallback, useState } from 'react';

import { WorkspaceSecretsForm } from '@/Pages/Settings/components/EvaluatorSecrets';
import { useTenantSecretSet } from '@/Pages/Settings/utils/useTenantSecretSet';
import { GroupedTabs } from '@/components/GroupedTabs';
import { useOrganizationId } from '@/hooks/useSwr';

import { PlaygroundIcon } from '../PlaygroundHome/PlaygroundButtons';
import { PlaygroundSecretInput } from './PlaygroundSecretInput';
import { PlaygroundSecretsSource } from './constants';
import { SECRET_SPECIFIC_MAP } from './manifest/serialized/ManifestSecretsMap';

interface PlaygroundSecretsProps {
  value: Record<string, string>;
  onChange: (value: Record<string, string>) => void;
  selectedSecretsSource?: PlaygroundSecretsSource;
  forceWorkspaceSecrets?: boolean;
  setSelectedSecretsSource?: (value: PlaygroundSecretsSource) => void;
  openSecretsRef?: React.MutableRefObject<(() => void) | null>;
  usePortal?: boolean;
  align?: 'start' | 'center' | 'end';
}

export function PlaygroundSecrets(props: PlaygroundSecretsProps) {
  const {
    usePortal = true,
    selectedSecretsSource: _selectedSecretsSource,
    setSelectedSecretsSource,
    forceWorkspaceSecrets,
    openSecretsRef,
    value,
  } = props;
  const [open, setOpen] = useState(false);

  if (openSecretsRef) {
    openSecretsRef.current = () => setOpen(true);
  }

  const workspaceSecrets = useTenantSecretSet();
  const organizationId = useOrganizationId();

  const showSecretsSourceTabs =
    !!organizationId && !!setSelectedSecretsSource && !!_selectedSecretsSource;
  const selectedSecretsSource = forceWorkspaceSecrets
    ? PlaygroundSecretsSource.WORKSPACE
    : showSecretsSourceTabs
    ? _selectedSecretsSource
    : PlaygroundSecretsSource.BROWSER;

  const handleFormSubmit = useCallback((e: FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setOpen(false);
  }, []);

  const renderContent = () => {
    return (
      <Popover.Content
        sideOffset={5}
        className="relative z-10"
        align={props.align}
      >
        <div className="flex max-w-[420px] flex-col rounded-xl border border-secondary bg-popover shadow-lg">
          <div className="flex flex-col gap-2 border-b border-secondary p-4">
            <div className="flex items-center justify-between gap-2">
              <Typography fontWeight={600}>Secrets & API keys</Typography>
              {showSecretsSourceTabs && (
                <GroupedTabs
                  options={[
                    {
                      display: 'Browser',
                      value: PlaygroundSecretsSource.BROWSER,
                    },
                    {
                      display: 'Workspace',
                      value: PlaygroundSecretsSource.WORKSPACE,
                    },
                  ]}
                  value={selectedSecretsSource}
                  onChange={(value) =>
                    setSelectedSecretsSource(value as PlaygroundSecretsSource)
                  }
                />
              )}
            </div>

            <Typography color="neutral" fontSize="md">
              {selectedSecretsSource === PlaygroundSecretsSource.BROWSER
                ? 'Your API keys are stored in your browser and used solely to communicate directly to services.'
                : 'Your API keys are stored encrypted on our servers.'}
            </Typography>
          </div>

          {selectedSecretsSource === PlaygroundSecretsSource.BROWSER ? (
            <PlaygroundBrowserSecretsForm
              handleFormSubmit={handleFormSubmit}
              {...props}
            />
          ) : (
            <WorkspaceSecretsForm
              requiredSecrets={Object.keys(value)}
              secretKeys={workspaceSecrets}
              onSuccess={() => setOpen(false)}
              hideInputs={true}
            />
          )}
        </div>
      </Popover.Content>
    );
  };

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Popover.Trigger>
        <PlaygroundIcon tooltipTitle="Secrets & API keys">
          <Key01Icon className="mx-1 my-1.5 h-4 w-4" />
        </PlaygroundIcon>
      </Popover.Trigger>
      {usePortal ? (
        <Popover.Portal>{renderContent()}</Popover.Portal>
      ) : (
        renderContent()
      )}
    </Popover.Root>
  );
}

function PlaygroundBrowserSecretsForm(
  props: PlaygroundSecretsProps & {
    handleFormSubmit: (e: React.FormEvent) => void;
  }
) {
  return (
    <form onSubmit={props.handleFormSubmit}>
      <div className="flex flex-col p-4">
        <div className="peer flex flex-col gap-4">
          {Object.keys(props.value).map((key) => {
            const SpecificSecretInput = SECRET_SPECIFIC_MAP[key];
            if (SpecificSecretInput != null) {
              return <SpecificSecretInput key={key} {...props} />;
            }

            return (
              <PlaygroundSecretInput
                key={key}
                name={key}
                value={props.value[key]}
                onChange={(value) => {
                  const newSecrets = { ...props.value, [key]: value };
                  props.onChange(newSecrets);
                }}
              />
            );
          })}
        </div>
        <div className="hidden peer-empty:flex">
          <Typography color="neutral" fontSize="md">
            No secrets have been found in this run. You might be running an
            older verison of LangChain. Please update to the latest version and
            execute the run again with tracing enabled.
          </Typography>
        </div>
      </div>
    </form>
  );
}
