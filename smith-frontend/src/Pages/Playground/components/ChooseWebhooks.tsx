import { Checkbox } from '@mui/joy';

import { PromptWebhook } from '@/Pages/Prompts/components/Webhooks/types';

export function ChooseWebhooks({
  webhooks,
  excludedWebhookIds,
  setExcludedWebhookIds,
}: {
  webhooks: PromptWebhook[];
  excludedWebhookIds: string[];
  setExcludedWebhookIds: (webhookIds: string[]) => void;
}) {
  if (!webhooks || webhooks.length === 0) {
    return null;
  }

  const webhook = webhooks[0];

  return (
    <div className="mt-2 flex flex-col gap-2">
      <div className="text-xs font-medium">Trigger Webhook</div>
      <div className="text-xs text-tertiary">
        Webhook will be triggered on commit. Uncheck to skip.
      </div>
      <div className="flex flex-col gap-2">
        <WebhookCheckbox
          webhook={webhook}
          checked={!excludedWebhookIds.includes(webhook.id)}
          setChecked={(checked) => {
            if (checked) {
              setExcludedWebhookIds(
                excludedWebhookIds.filter((id) => id !== webhook.id)
              );
            } else {
              setExcludedWebhookIds([...excludedWebhookIds, webhook.id]);
            }
          }}
        />
      </div>
    </div>
  );
}

function WebhookCheckbox({
  webhook,
  checked,
  setChecked,
}: {
  webhook: PromptWebhook;
  checked: boolean;
  setChecked: (checked: boolean) => void;
}) {
  return (
    <div className="flex items-center gap-2 rounded-md border border-secondary p-1">
      <Checkbox
        checked={checked}
        onChange={(e) => setChecked(e.target.checked)}
        size="sm"
      />
      <div className="text-sm">{webhook.url}</div>
    </div>
  );
}
