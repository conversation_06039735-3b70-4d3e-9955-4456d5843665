import { ChevronRightIcon } from '@heroicons/react/24/outline';
import {
  ChevronDownIcon,
  Database02Icon,
  Loading01Icon,
  PlusIcon,
  SearchSmIcon,
  XIcon,
} from '@langchain/untitled-ui-icons';
import { Input, LinearProgress } from '@mui/joy';

import { useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';

import { isDatasetExamplesEdited } from '@/Pages/Playground/PlaygroundHome/hooks/usePlaygroundStore';
import { useSaveDataset } from '@/Pages/Playground/PlaygroundHome/hooks/useSaveDataset.ts';
import { focusOnFirstExampleRowValue } from '@/Pages/Playground/PlaygroundHome/utils.ts';
import { Badge } from '@/components/Badge.tsx';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import SaveConfirmationModal from '@/components/SaveConfirmationModal.tsx';
import { Skeleton } from '@/components/Skeleton.tsx';
import { TruncatedTextWithTooltip } from '@/components/TruncatedTextWithTooltip.tsx/index.tsx';
import { useStoredResourceTags } from '@/hooks/useStoredResourceTags/useStoredResourceTags';
import { useDataset, useDatasetSplits, useDatasets } from '@/hooks/useSwr';
import CheckIcon from '@/icons/CheckIcon.svg?react';
import { DatasetSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { ExampleSchemaOrDraftWithEdited } from '../PlaygroundContext';

function DatasetItem({
  dataset,
  selectedDatasetId,
  onClick,
}: {
  dataset: DatasetSchema;
  selectedDatasetId: string | undefined;
  onClick: () => void;
}) {
  return (
    <button
      type="button"
      onClick={onClick}
      key={dataset.id}
      className={cn(
        'flex w-full cursor-pointer items-center justify-between rounded-md px-4 py-2 hover:bg-secondary',
        selectedDatasetId === dataset.id && 'bg-secondary'
      )}
    >
      <div className="flex min-w-0 flex-1 flex-col gap-0.5">
        <div className="flex min-w-0 flex-row items-center gap-1">
          <TruncatedTextWithTooltip
            text={dataset.name}
            className="text-sm font-semibold"
            placement="left"
          />
        </div>
        <span className="mr-auto text-xs">
          {dataset.example_count} examples
        </span>
      </div>
      {selectedDatasetId === dataset.id && <Badge text="current" />}
      <ChevronRightIcon className="h-5 w-5 flex-shrink-0" />
    </button>
  );
}

const ALL_EXAMPLES_TEXT = 'All examples';

function SplitItem({
  split,
  selectedSplits,
  setSelectedSplits,
  selectedDatasetId,
  dataset,
  setDatasetId,
}: {
  split: string;
  selectedSplits: string[];
  setSelectedSplits: (splits: string[]) => void;
  selectedDatasetId: string | undefined;
  dataset: DatasetSchema;
  setDatasetId: (datasetId: string | undefined) => void;
}) {
  const splitIsSelected =
    dataset.id == selectedDatasetId &&
    (selectedSplits.some((s) => s === split) ||
      (selectedSplits.length === 0 && split === ALL_EXAMPLES_TEXT));

  const handleOnSplitSelect = () => {
    if (dataset.id !== selectedDatasetId) {
      setDatasetId(dataset.id);
    }

    // append to selected splits if still current dataset, otherwise reset
    const startingSplits =
      dataset.id === selectedDatasetId ? selectedSplits : [];

    if (split === ALL_EXAMPLES_TEXT) {
      setSelectedSplits([]);
    } else if (startingSplits.some((s) => s === split)) {
      setSelectedSplits(startingSplits.filter((s) => s !== split));
    } else {
      setSelectedSplits([...startingSplits, split]);
    }
  };

  return (
    <button
      key={split}
      type="button"
      onClick={handleOnSplitSelect}
      className={cn(
        'flex h-9 w-full cursor-pointer items-center gap-4 rounded-md p-2 text-left hover:bg-secondary',
        splitIsSelected && 'bg-secondary'
      )}
    >
      <span className="flex flex-1 items-center">
        {split === ALL_EXAMPLES_TEXT ? (
          <div className="line-clamp-1 flex items-center gap-1 text-xs">
            <span>{split}</span>
            <Badge text="default" />
          </div>
        ) : (
          <span className="line-clamp-1 text-xs">{split}</span>
        )}
      </span>
      {splitIsSelected && (
        <CheckIcon className="h-5 w-5 flex-shrink-0 stroke-brand-green-500" />
      )}
    </button>
  );
}

function SplitList({
  splits,
  splitsLoading,
  selectedSplits,
  setSelectedSplits,
  selectedDatasetId,
  dataset,
  setDatasetId,
}: {
  splits: string[] | undefined;
  splitsLoading: boolean;
  selectedSplits: string[];
  setSelectedSplits: (splits: string[]) => void;
  selectedDatasetId: string | undefined;
  dataset: DatasetSchema;
  setDatasetId: (datasetId: string | undefined) => void;
}) {
  const renderBody = () => {
    if (splitsLoading) {
      return <LinearProgress />;
    }

    if (!splits?.length) {
      return (
        <div className="m-auto p-4 text-sm text-gray-500">No splits found</div>
      );
    }

    return [ALL_EXAMPLES_TEXT, ...(splits ?? [])]?.map((split) => (
      <SplitItem
        key={split}
        split={split}
        selectedSplits={selectedSplits}
        setSelectedSplits={setSelectedSplits}
        selectedDatasetId={selectedDatasetId}
        dataset={dataset}
        setDatasetId={setDatasetId}
      />
    ));
  };

  return (
    <div className="max-h-[300px] overflow-y-auto">
      <div className="sticky top-0 flex items-center gap-1 bg-background p-2 pb-1 text-sm text-gray-500">
        <span className="mx-auto truncate text-xs font-medium text-tertiary">
          SELECT DATASET SPLIT
        </span>
      </div>
      {renderBody()}
    </div>
  );
}

function DatasetItemPopover({
  dataset,
  selectedDatasetId,
  setDatasetId,
  splits,
  setSplits,
  closePopover,
  isHovered,
  setHoveredDatasetId,
  isDatasetEdited,
}: {
  dataset: DatasetSchema;
  selectedDatasetId: string | undefined;
  setDatasetId: (datasetId: string | undefined) => void;
  splits: string[];
  setSplits: (splits: string[]) => void;
  closePopover: () => void;
  isHovered: boolean;
  setHoveredDatasetId: (datasetId: string | undefined) => void;
  isDatasetEdited: boolean;
}) {
  const { id: datasetId } = dataset;
  const { data: currSplits, isLoading: currSplitsLoading } = useDatasetSplits(
    undefined,
    isHovered ? datasetId : undefined
  );
  const { saveDataset, isLoading: isLoadingDatasetUpdate } = useSaveDataset({});

  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const onCloseConfirmationModal = () => setIsConfirmationModalOpen(false);

  const onClickDataset = () => {
    if (datasetId !== selectedDatasetId) {
      setDatasetId(datasetId);
    }
    closePopover();
  };

  return (
    <>
      <Popover key={datasetId} open={isHovered}>
        <PopoverTrigger asChild>
          <div
            onMouseEnter={() => {
              setHoveredDatasetId(datasetId);
            }}
          >
            <DatasetItem
              dataset={dataset}
              onClick={() => {
                if (isDatasetEdited) {
                  setIsConfirmationModalOpen(true);
                } else {
                  onClickDataset();
                }
              }}
              selectedDatasetId={selectedDatasetId}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="w-[215px] p-0"
          align="start"
          side="right"
          sideOffset={5}
          onInteractOutside={() => setHoveredDatasetId(undefined)}
          onMouseEnter={() => setHoveredDatasetId(datasetId)}
          onMouseLeave={() => setHoveredDatasetId(undefined)}
        >
          <SplitList
            splits={currSplits}
            splitsLoading={currSplitsLoading}
            selectedSplits={splits}
            setSelectedSplits={setSplits}
            selectedDatasetId={selectedDatasetId}
            dataset={dataset}
            setDatasetId={setDatasetId}
          />
        </PopoverContent>
      </Popover>
      <SaveConfirmationModal
        isOpen={isConfirmationModalOpen}
        title="Change datasets?"
        description="You have unsaved changes. Do you want to save them before creating a new dataset?"
        onClose={onCloseConfirmationModal}
        onConfirm={async () => {
          await saveDataset();
          onClickDataset();
        }}
        onDiscard={() => {
          onClickDataset();
          onCloseConfirmationModal();
        }}
        saveCopy={
          isLoadingDatasetUpdate ? (
            <Loading01Icon className="h-4 w-4 animate-spin" />
          ) : (
            'Save & Open New'
          )
        }
        discardCopy="Discard"
      />
    </>
  );
}

export function DatasetAndSplitPicker({
  onCreateNew,
  hideCreateNew = false,
  datasetId,
  isCreatingNewDataset,
  setDatasetIdAndUpdateUrl,
  splits,
  setSplits,
  examples,
  lightSkeletonTrigger = false,
}: {
  onCreateNew: () => void;
  hideCreateNew?: boolean;
  datasetId: string | undefined;
  isCreatingNewDataset: boolean;
  setDatasetIdAndUpdateUrl: (datasetId?: string) => void;
  splits: string[];
  setSplits: (splits: string[]) => void;
  examples?: ExampleSchemaOrDraftWithEdited[];
  lightSkeletonTrigger?: boolean;
}) {
  const isDatasetEdited = !!isDatasetExamplesEdited(examples);

  const [searchInput, setSearchInput] = useState('');
  const [searchDebounced] = useDebounce(searchInput, 300);
  const { selectedTagIds } = useStoredResourceTags();

  const [isOpen, setIsOpen] = useState(true);
  const [hoveredDatasetId, setHoveredDatasetId] = useState<string | undefined>(
    undefined
  );

  const { data: currentDataset, isLoading: currentDatasetLoading } =
    useDataset(datasetId);
  const currentDatasetName = isCreatingNewDataset
    ? ''
    : datasetId
    ? currentDataset?.name
    : '';

  const { data: datasets, isLoading: datasetsLoading } = useDatasets({
    ...(searchInput === '' || searchInput === currentDatasetName
      ? {}
      : { name_contains: searchDebounced }),
    tag_value_id: selectedTagIds,
  });

  const orderedDatasets = useMemo(() => {
    // if we have the selected dataset loaded, we want to show it first
    return currentDataset?.id === datasetId && datasets?.length
      ? [
          ...(currentDataset ? [currentDataset] : []),
          ...datasets.filter((ds) => ds.id !== currentDataset?.id),
        ]
      : datasets;
  }, [datasets, currentDataset, datasetId]);

  const { saveDataset, isLoading: isLoadingDataset } = useSaveDataset({});

  const renderContent = () => {
    if (datasetsLoading && !orderedDatasets?.length) {
      return <LinearProgress />;
    }

    if (!orderedDatasets?.length) {
      return (
        <div className="m-auto p-4 text-sm text-gray-500">
          No datasets found
        </div>
      );
    }

    return (
      <div className="max-h-[300px] overflow-y-auto">
        <div className="flex flex-col">
          {orderedDatasets.map((ds) => (
            <DatasetItemPopover
              key={ds.id}
              dataset={ds}
              selectedDatasetId={datasetId}
              setDatasetId={setDatasetIdAndUpdateUrl}
              splits={splits}
              setSplits={setSplits}
              closePopover={() => setIsOpen(false)}
              isHovered={hoveredDatasetId === ds.id}
              setHoveredDatasetId={setHoveredDatasetId}
              isDatasetEdited={isDatasetEdited}
            />
          ))}
        </div>
      </div>
    );
  };

  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const onCloseConfirmationForNewModal = () =>
    setIsConfirmationModalOpen(false);

  const onClickNew = () => {
    onCreateNew();
    setIsOpen(false);
    focusOnFirstExampleRowValue();
  };

  const renderTrigger = () => {
    if (currentDatasetLoading && !searchInput) {
      return (
        <Skeleton
          className={cn(
            'm-0 h-[30px] w-[250px]',
            lightSkeletonTrigger && 'bg-primary'
          )}
        />
      );
    }

    return (
      <Input
        placeholder="Select or create a dataset"
        value={currentDatasetName}
        readOnly
        className={cn(
          'w-[250px]',
          isOpen ? 'ring-4 ring-offset-0' : '',
          'ring-[#D9EEEC] dark:ring-[#1A3533]'
        )}
        size="sm"
        sx={{
          '--Input-minHeight': '30px',
          borderWidth: '2px',
          borderColor: isOpen
            ? 'var(--joy-palette-primary-500, #16A34A)'
            : 'var(--gray-300)',
          '&:hover': {
            borderColor: 'var(--joy-palette-primary-500, #16A34A)',
          },
          '&:focus-within': {
            borderWidth: '1px',
            paddingX: '9px',
          },
          paddingX: '8px',
        }}
        startDecorator={<Database02Icon className="h-4 w-4 text-secondary" />}
        endDecorator={
          currentDatasetName ? (
            <button
              type="button"
              onClick={onClickNew}
              className="rounded-md p-0.5 hover:bg-primary-hover focus:outline-none"
            >
              <XIcon className="h-4 w-4 text-gray-500 hover:text-gray-700" />
            </button>
          ) : (
            <ChevronDownIcon className="h-4 w-4 text-secondary" />
          )
        }
      />
    );
  };

  const newDatasetButton = () => {
    if (hideCreateNew) return null;
    return (
      <button
        type="button"
        className="mx-2 flex flex-row items-center gap-1 border-b border-[#1A1A1E] text-sm font-medium hover:bg-primary-hover"
        onClick={() => {
          if (isDatasetEdited) {
            setIsConfirmationModalOpen(true);
          } else {
            onClickNew();
          }
        }}
      >
        <PlusIcon className="h-4 w-4" />
        New
      </button>
    );
  };

  const renderSearchInput = () => (
    <div
      className="flex flex-row items-center justify-between gap-2 border-b border-secondary p-1"
      onMouseEnter={() => setHoveredDatasetId(undefined)}
    >
      <Input
        value={searchInput}
        onChange={(e) => setSearchInput(e.target.value)}
        placeholder="Search"
        startDecorator={<SearchSmIcon className="h-4 w-4" />}
        size="sm"
        sx={{ border: 'none' }}
      />
      {newDatasetButton()}
    </div>
  );

  return (
    <div className="w-full">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div>{renderTrigger()}</div>
        </PopoverTrigger>
        <PopoverContent
          className="w-[250px] p-0"
          align="start"
          sideOffset={5}
          onOpenAutoFocus={(event) => event.preventDefault()}
          onCloseAutoFocus={(event) => event.preventDefault()}
        >
          <div className="flex flex-col rounded-md">
            {renderSearchInput()}
            {renderContent()}
          </div>
        </PopoverContent>
      </Popover>
      <SaveConfirmationModal
        isOpen={isConfirmationModalOpen}
        title="Create a New Dataset?"
        description="You have unsaved changes. Do you want to save them before creating a new dataset?"
        onClose={onCloseConfirmationForNewModal}
        onConfirm={async () => {
          await saveDataset();
          onClickNew();
        }}
        onDiscard={() => {
          onClickNew();
          onCloseConfirmationForNewModal();
        }}
        saveCopy={
          isLoadingDataset ? (
            <Loading01Icon className="h-4 w-4 animate-spin" />
          ) : (
            'Save & Create New'
          )
        }
        discardCopy="Discard & Create New"
      />
    </div>
  );
}
