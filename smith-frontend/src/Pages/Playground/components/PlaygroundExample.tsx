import { TrashIcon } from '@heroicons/react/24/outline';
import { ExclamationTriangleIcon } from '@heroicons/react/24/solid';
import { Tooltip } from '@mui/joy';

import { RunOutput } from '@/Pages/Run/components/RunDetailsIO';
import { Message } from '@/components/Message';
import { MIME_TYPES } from '@/components/RichTextEditor/constants';
import { RepoExampleSchema } from '@/types/schema';
import {
  getMessageContent,
  getMessageFields,
  getMessageType,
  isMessageLike,
  maybeGetArrayOfMessages,
} from '@/utils/messages';
import { cn } from '@/utils/tailwind';

import {
  PlaygroundPromptInputChatItem,
  PlaygroundPromptInputItem,
  PlaygroundPromptInputMultimodalItem,
} from '../PlaygroundPrompt/PlaygroundPromptInput';

function PlaygroundExample(props: {
  example: RepoExampleSchema;
  index: number;
  showHeader: boolean;
  currentInputs?: string[];
  onClick?: (rs: RepoExampleSchema) => void;
  onDelete?: (rs: RepoExampleSchema) => void;
}) {
  const inputsDontMatch = !props.currentInputs
    ? false
    : !compareInputs(props.currentInputs, props.example.inputs);

  return (
    <div className="inline-flex w-full flex-col items-start justify-start gap-3">
      {props.showHeader && (
        <div className="inline-flex w-full items-center justify-between gap-3 self-stretch">
          <div className="text-base font-medium leading-snug">
            Example {props.index + 1}
          </div>
          <div className="flex items-center justify-center gap-3">
            {inputsDontMatch && (
              <Tooltip
                title="Inputs do not match prompt"
                placement="top"
                color="warning"
              >
                <div className="flex items-center justify-center rounded-md border border-red-400 p-1">
                  <ExclamationTriangleIcon color="orange" className="h-4 w-4" />
                </div>
              </Tooltip>
            )}
            <Tooltip title="Delete example" placement="top">
              <button
                type="button"
                onClick={() => {
                  props.onDelete && props.onDelete(props.example);
                }}
                className="flex items-center justify-center rounded-md border border-secondary p-1"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </Tooltip>
          </div>
        </div>
      )}
      <div
        className={cn(
          'inline-flex w-full flex-col items-start justify-start gap-3 rounded-lg p-2',
          props.onClick && '!cursor-pointer',
          props.onClick && 'hover:bg-gray-500 hover:bg-opacity-10'
        )}
        onClick={() => {
          props.onClick && props.onClick(props.example);
        }}
      >
        <div className="flex w-full flex-col items-stretch justify-start gap-4 rounded-lg bg-secondary bg-opacity-10 p-4">
          <div className="flex h-[15px] flex-col items-start justify-start gap-4 self-stretch">
            <div className="text-sm font-medium uppercase leading-[15px] tracking-wide">
              INPUT
            </div>
          </div>
          <PlaygroundExampleInputs
            input={props.example.inputs}
            cursorPointer={!!props.onClick}
          />
        </div>
        {props.example.outputs && (
          <div className="flex w-full flex-col items-stretch justify-start gap-4 rounded-lg bg-tertiary bg-opacity-10 p-4">
            <div className="flex h-[15px] flex-col items-start justify-start gap-4 self-stretch">
              <div className="text-sm font-medium uppercase leading-[15px] tracking-wide">
                OUTPUT
              </div>
            </div>
            {isMessageLike(props.example.outputs) ? (
              <Message
                role={getMessageType(props.example.outputs) ?? 'AI'}
                content={getMessageContent(props.example.outputs) ?? ''}
                fields={getMessageFields(props.example.outputs)}
              />
            ) : (
              <RunOutput outputs={props.example.outputs} />
            )}
          </div>
        )}
      </div>
    </div>
  );
}

function compareInputs(
  inputs: string[],
  exampleInputs: RepoExampleSchema['inputs']
) {
  if (exampleInputs == null) {
    return false;
  }

  const setA = new Set(inputs);
  const setB = new Set(
    Object.keys(exampleInputs).filter((key) => key !== 'signal')
  );
  if (setA.size !== setB.size) {
    return false;
  }
  for (const item of setA) {
    if (!setB.has(item)) {
      return false;
    }
  }
  return true;
}

export function PlaygroundExampleInputs(props: {
  input: RepoExampleSchema['inputs'];
  cursorPointer: boolean;
}) {
  const inputPairs = Object.entries(props.input ?? {}).filter(
    ([key]) => key !== 'signal'
  );

  const isCompact = inputPairs.every(([, value]) => {
    if (value != null && Array.isArray(value)) return value.length === 0;
    return (value?.length ?? 0) <= 50 && (value?.split('\n').length ?? 0) <= 3;
  });

  return (
    <div className="@container">
      <div
        className={cn(
          'flex flex-col',
          isCompact && '@xl:grid @xl:grid-cols-[auto,1fr] @xl:gap-y-2'
        )}
      >
        {inputPairs.map(([key, value], index) => {
          if (typeof value === 'string' && value.startsWith('data:image/')) {
            return (
              <PlaygroundPromptInputMultimodalItem
                key={index}
                name={key}
                isCompact={isCompact}
                value={value}
                canEdit={false}
                cursorPointer={props.cursorPointer}
                mimeType={MIME_TYPES.IMAGE}
              />
            );
          }

          const maybeMessagesArray = maybeGetArrayOfMessages(value);
          if (maybeMessagesArray) {
            return (
              <PlaygroundPromptInputChatItem
                key={index}
                name={key}
                isCompact={isCompact}
                value={maybeMessagesArray}
                canEdit={false}
                cursorPointer={props.cursorPointer}
              />
            );
          }

          return (
            <PlaygroundPromptInputItem
              key={index}
              name={key}
              isCompact={isCompact}
              value={value}
              canEdit={false}
              cursorPointer={props.cursorPointer}
              templateFormat={'f-string'}
            />
          );
        })}
      </div>
    </div>
  );
}

export default PlaygroundExample;
