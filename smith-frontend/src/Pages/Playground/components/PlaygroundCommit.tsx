import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { CloudArrowUpIcon, PlusCircleIcon } from '@heroicons/react/24/outline';
import type { SerializedConstructor } from '@langchain/core/load/serializable';
import {
  Button,
  CircularProgress,
  Modal,
  ModalClose,
  ModalDialog,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/joy';

import { ReactNode, useEffect, useId, useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';

import { SetHandleModal } from '@/Pages/Hub/SetHandle';
import { useFetchWorkspacePromptWebhook } from '@/Pages/Prompts/components/Webhooks/hooks/useFetchWorkspacePromptWebhook';
import {
  getPromptDisplayName,
  getPromptIcon,
} from '@/Pages/Prompts/utils/Prompts.utils';
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/Command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/Popover';
import useToast from '@/components/Toast';
import { FetcherParams } from '@/data/fetcher';
import { usePermissions } from '@/hooks/usePermissions';
import {
  useCreateCommitMutation,
  useCreateRepoMutation,
  useRepo,
  useRepos,
  useSelectedTenant,
} from '@/hooks/useSwr';
import { mutateWithInfiniteRelative } from '@/hooks/useSwr.utils';
import { RepoWithLookupsSchema } from '@/types/schema';
import { addQueryParams } from '@/utils/add-query-params';
import {
  appPromptsIndexPath,
  hubApiCommitsPath,
  hubApiReposPath,
} from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { PlaygroundIcon } from '../PlaygroundHome/PlaygroundButtons';
import { ChooseWebhooks } from './ChooseWebhooks';

function RepoSelect(props: {
  tenantHandle: string | null;
  value: RepoWithLookupsSchema | string | null;
  onChange: (value: typeof props.value) => void;
}) {
  const id = useId();
  const [open, setOpen] = useState(false);
  const repos = useRepos(
    props.tenantHandle ? { tenant_handle: props.tenantHandle } : null
  );
  const [input, setInput] = useState('');

  const slashIndex = input.lastIndexOf('/');
  const newRepoName = slashIndex === -1 ? input : input.slice(slashIndex + 1);
  const newTenantHandle =
    slashIndex === -1 ? props.tenantHandle : input.slice(0, slashIndex);

  const error =
    newTenantHandle !== props.tenantHandle
      ? `${newTenantHandle} is not your current tenant`
      : null;

  return (
    <div className="flex flex-col items-stretch gap-2">
      <label className="text-xs font-medium" htmlFor={id}>
        Prompt
      </label>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <button
            type="button"
            id={id}
            className="flex items-center gap-2 rounded-md border border-secondary p-2 px-3"
          >
            <span className="flex max-w-md grow items-center gap-2">
              {typeof props.value !== 'string' &&
                props.value &&
                getPromptIcon(props.value.is_public, 'h-5 w-5')}
              <span
                className={cn('truncate', props.value == null && 'opacity-50')}
              >
                {props.value == null
                  ? 'Select repo...'
                  : typeof props.value === 'string'
                  ? props.value
                  : getPromptDisplayName(
                      props.value.is_public,
                      props.value.full_name,
                      props.value.repo_handle
                    )}
              </span>
            </span>
            <ChevronDownIcon
              className={cn(
                'mr-0.5 h-5 w-5 transition-all',
                open && 'rotate-180'
              )}
            />
          </button>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="w-[508px] p-0"
          avoidCollisions={false}
          side={'bottom'}
        >
          <Command shouldFilter={false}>
            <label className="p-3 pb-0 text-sm font-medium leading-tight text-tertiary">
              Select an existing or create a new prompt
            </label>
            <CommandInput
              className="m-3 mb-0 mt-1.5"
              placeholder="Prompt name..."
              value={input}
              onValueChange={setInput}
            />
            {error && (
              <div className="m-3">
                <span className="text-error">{error}</span>
              </div>
            )}
            <CommandList>
              <CommandGroup
                className="p-1.5"
                style={{ overflowY: 'auto', maxHeight: '200px' }}
              >
                {!error && newRepoName && (
                  <CommandItem
                    key="new"
                    onSelect={() => {
                      props.onChange(newRepoName);
                      setOpen(false);
                    }}
                    className="flex cursor-pointer items-center gap-2 p-1.5 py-2"
                  >
                    <PlusCircleIcon className="h-5 w-5" />
                    Create new prompt "{props.tenantHandle}/{newRepoName}"
                  </CommandItem>
                )}
                {repos.data?.repos
                  ?.filter((a) => a.full_name.includes(input))
                  ?.map((repo) => (
                    <CommandItem
                      key={repo.id}
                      onSelect={() => {
                        props.onChange(repo);
                        setOpen(false);
                      }}
                      className="flex cursor-pointer items-center gap-2 p-1.5 py-2"
                    >
                      {getPromptIcon(repo.is_public)}
                      <span className="truncate">
                        {getPromptDisplayName(
                          repo.is_public,
                          repo.full_name,
                          repo.repo_handle
                        )}
                      </span>
                    </CommandItem>
                  ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

export function PlaygroundCommit(props: {
  currentRepo?: RepoWithLookupsSchema;
  manifest: SerializedConstructor | null;
  disabled?: boolean;
  onSuccess?: (newCommitHash: string) => void;
  className?: string;
  customButton?: (props) => ReactNode;
}) {
  const navigate = useNavigate();
  const selectedTenant = useSelectedTenant();

  const { authorize } = usePermissions();
  const canUpdate = authorize('prompts:update');

  const isPersonal = selectedTenant.data?.is_personal;
  const tenantHandle = selectedTenant.data?.tenant_handle ?? null;
  const { mutate: mutateRepo } = useRepo(
    props.currentRepo?.owner,
    props.currentRepo?.repo_handle
  );
  const doCommit = useCreateCommitMutation(
    tenantHandle ?? undefined,
    props.currentRepo?.repo_handle ?? '',
    {
      onSuccess: (data) => {
        props.onSuccess?.(data.commit.commit_hash);
        mutateWithInfiniteRelative((key: FetcherParams) =>
          key?.url?.includes(hubApiReposPath)
        );
      },
    }
  );
  const doCreateRepo = useCreateRepoMutation();
  const mutating = doCommit.isMutating || doCreateRepo.isMutating;
  const [repo, setRepo] = useState<RepoWithLookupsSchema | string | null>(null);

  const isMyRepo = props.currentRepo?.tenant_id === selectedTenant.data?.id;

  useEffect(() => {
    doCommit.reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.manifest]);
  useEffect(() => {
    if (props.currentRepo && isMyRepo) {
      setRepo(props.currentRepo);
    }
  }, [props.currentRepo, isMyRepo]);

  const [open, setOpen] = useState(false);
  const [isPublic, setIsPublic] = useState(true);
  const [searchParams, setSearchParams] = useSearchParams();
  const [setHandleOpen, setSetHandleOpen] = useState(false);
  const { createToast } = useToast();
  const isNewRepo = repo !== null && typeof repo === 'string';
  const datasetId = searchParams.get('datasetId');
  const [excludedWebhookIds, setExcludedWebhookIds] = useState<string[]>([]);

  const { data: webhooks } = useFetchWorkspacePromptWebhook({});

  async function onSubmit() {
    if (repo === null || props.manifest == null) return;
    if (isNewRepo) {
      if (isPublic && !selectedTenant.data?.tenant_handle) {
        setSetHandleOpen(true);
      } else {
        const newRepo = await doCreateRepo.trigger({
          json: {
            is_public: isPublic,
            repo_handle: repo,
          },
        });
        if (!newRepo) return;
        const commitRes = await doCommit.trigger({
          url: `${hubApiCommitsPath}/${
            selectedTenant.data?.tenant_handle
              ? newRepo?.repo.full_name
              : `-/${newRepo?.repo.repo_handle}`
          }`,
          json: {
            parent_commit: null,
            manifest: props.manifest,
            ignore_webhook_ids: excludedWebhookIds,
          },
        });
        setOpen(false);
        navigate(
          addQueryParams(
            `/${appPromptsIndexPath}/${newRepo?.repo.repo_handle}/playground`,
            {
              organizationId: newRepo?.repo.tenant_id,
              ...(datasetId ? { datasetId } : {}),
            }
          )
        );
        createToast({
          title: 'Prompt created',
          description: (
            <Link
              to={`/${appPromptsIndexPath}/${newRepo?.repo.repo_handle}/${commitRes?.commit.commit_hash}?organizationId=${newRepo?.repo.tenant_id}`}
              className="underline"
            >
              View in the prompt hub
            </Link>
          ),
        });
      }
    } else {
      // commit to existing repo
      const commitRes = await doCommit.trigger({
        url: `${hubApiCommitsPath}/${
          selectedTenant.data?.tenant_handle
            ? repo.full_name
            : `-/${repo.repo_handle}`
        }`,
        json: {
          parent_commit: repo.last_commit_hash,
          manifest: props.manifest,
          ignore_webhook_ids: excludedWebhookIds,
        },
      });
      setOpen(false);
      createToast({
        title: 'Prompt updated',
        description: (
          <Link
            to={`/${appPromptsIndexPath}/${repo.repo_handle}/${commitRes?.commit.commit_hash}?organizationId=${repo.tenant_id}`}
            className="underline"
          >
            View in the prompt hub
          </Link>
        ),
      });
      if (repo.id !== props.currentRepo?.id) {
        navigate(
          addQueryParams(
            `/${appPromptsIndexPath}/${repo.repo_handle}/playground`,
            {
              organizationId: repo.tenant_id,
              ...(datasetId ? { datasetId } : {}),
            }
          )
        );
      } else {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete('commit');
        setSearchParams(newSearchParams);
        mutateRepo();
        setRepo({
          ...repo,
          last_commit_hash: commitRes?.commit?.commit_hash ?? null,
        });
      }
    }
  }

  const errorMessage = doCreateRepo?.error?.message || doCommit?.error?.message;
  const commitDisabledMessage =
    props.disabled || !!doCommit.data?.commit
      ? 'No changes to save'
      : !canUpdate
      ? 'You do not have permission to update prompts'
      : undefined;

  return (
    <>
      <Modal open={open} onClose={() => setOpen(false)}>
        <ModalDialog variant="outlined" sx={{ width: 550 }}>
          <ModalClose />
          <div className={'flex flex-col items-start gap-3'}>
            <div className="flex flex-col gap-1">
              <h2 className="text-lg font-semibold">Commit Changes</h2>
              <p className="text-sm text-tertiary">
                Commit to the current prompt, an existing prompt, or create a
                new one.
              </p>
            </div>
          </div>

          <div className="mx-auto mt-4 flex w-full flex-col items-stretch gap-4">
            <RepoSelect
              tenantHandle={tenantHandle}
              value={repo}
              onChange={setRepo}
            />

            {webhooks?.length && webhooks.length > 0 ? (
              <ChooseWebhooks
                webhooks={webhooks}
                excludedWebhookIds={excludedWebhookIds}
                setExcludedWebhookIds={setExcludedWebhookIds}
              />
            ) : null}

            {errorMessage && (
              <div className="mb-2 content-center text-sm text-error">
                {errorMessage}
              </div>
            )}
            {isNewRepo && (
              <div className="items flex flex-col justify-start gap-2">
                <label className="text-sm font-medium text-tertiary">
                  New Repo Visibility
                </label>
                <div className="ml-3">
                  <RadioGroup
                    value={isPublic}
                    onChange={(e) => setIsPublic(e.target.value === 'true')}
                  >
                    <Radio
                      value={'false'}
                      label={
                        <>
                          <Typography level="body1">Private</Typography>{' '}
                          <Typography level="body2">
                            Only {isPersonal ? 'you' : 'this organization'} can
                            see this
                          </Typography>
                        </>
                      }
                    />
                    <Radio
                      value={'true'}
                      label={
                        <>
                          <Typography level="body1">Public</Typography>{' '}
                          <Typography level="body2">
                            Everyone can see this
                          </Typography>
                        </>
                      }
                    />
                  </RadioGroup>
                </div>
              </div>
            )}
            <div className="items flex justify-end gap-2">
              <Button
                type="button"
                variant="outlined"
                color="neutral"
                onClick={() => setOpen(false)}
                sx={{ px: 3 }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={onSubmit}
                startDecorator={
                  mutating ? (
                    <CircularProgress
                      size="sm"
                      sx={{
                        transform: 'scale(0.65)',
                        marginLeft: '-5.5px',
                        marginRight: '-2px',
                      }}
                    />
                  ) : (
                    <CloudArrowUpIcon className="h-5 w-5" />
                  )
                }
                sx={{ px: 2 }}
              >
                Commit
              </Button>
            </div>
          </div>
        </ModalDialog>
      </Modal>
      {props.customButton ? (
        props.customButton({
          onClick: () => setOpen(true),
          disabled: !!commitDisabledMessage,
        })
      ) : (
        <PlaygroundIcon
          className={cn(
            'my-1 border-brand text-brand-tertiary',
            commitDisabledMessage
              ? 'cursor-not-allowed opacity-50 hover:bg-background hover:opacity-50'
              : ''
          )}
          tooltipTitle={commitDisabledMessage}
        >
          <button
            type="button"
            onClick={() => setOpen(true)}
            className="px-1 py-[3px]"
            disabled={!!commitDisabledMessage}
          >
            Commit
          </button>
        </PlaygroundIcon>
      )}
      <SetHandleModal
        open={setHandleOpen}
        onClose={() => setSetHandleOpen(false)}
      />
    </>
  );
}
