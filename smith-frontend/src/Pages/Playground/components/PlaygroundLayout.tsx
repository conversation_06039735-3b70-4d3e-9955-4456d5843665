import { PlusIcon } from '@langchain/untitled-ui-icons';

import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

import { FancyRenderingProvider } from '@/components/RichTextEditor/utils/fancyRenderContext';
import { pluralize } from '@/utils/stringUtils';
import { cn } from '@/utils/tailwind';

import { PlaygroundInputSourceType } from '../PlaygroundContext';
import { PlaygroundButton } from '../PlaygroundHome/PlaygroundButtons';
import { PlaygroundSectionHeader } from '../PlaygroundHome/PlaygroundSectionHeader';
import { PromptInputSection } from '../PlaygroundHome/PromptInputSection';
import { PromptOutputSection } from '../PlaygroundHome/PromptOutputSection';
import { PromptPanel } from '../PlaygroundHome/PromptPanel';
import {
  useInput,
  useManifests,
  usePlaygroundStore,
} from '../PlaygroundHome/hooks/usePlaygroundStore';
import { usePlaygroundPromptTemplateVariables } from '../PlaygroundPrompt/hooks/usePlaygroundPromptTemplateVariables';
import { usePlaygroundParentContext } from '../context/PlaygroundParentContext';
import { PlaygroundPromptTopBar } from './PlaygroundPromptTopBar';

const PLAYGROUND_LAYOUT_SIZES = {
  horizontal: {
    input: {
      default: 50,
      min: 30,
    },
    output: {
      default: 50,
      min: 30,
    },
  },
  vertical: {
    input: {
      default: 40,
      min: 20,
    },
    output: {
      default: 60,
      min: 30,
    },
  },
};

const PLAYGROUND_INPUT_OUTPUT_SECTION_LAYOUT_SIZES = {
  inputs: {
    default: 30,
    min: 10,
  },
  output: {
    default: 70,
    min: 30,
  },
};

export function PlaygroundColumnDivider({
  orientation = 'vertical',
}: {
  orientation?: 'vertical' | 'horizontal';
}) {
  return (
    <PanelResizeHandle className="z-[10] text-secondary hover:text-brand-green-400 data-[resize-handle-active]:text-brand-green-400">
      <div
        className={cn(
          'group absolute flex justify-center',
          orientation === 'vertical' ? 'h-2 w-full' : '-mx-1 h-full w-2'
        )}
      >
        <div
          className={cn(
            'transition-colors group-hover:bg-current',
            orientation === 'vertical' ? 'h-[2px] w-full' : 'h-full w-[2px]'
          )}
        />
      </div>
    </PanelResizeHandle>
  );
}

export function PlaygroundLayout({
  scrollRef,
}: {
  scrollRef?: Dispatch<SetStateAction<HTMLDivElement | null>>;
}) {
  const { isSimplified } = usePlaygroundParentContext();
  const addPrompt = usePlaygroundStore((state) => state.addPrompt);
  const inputSourceType = usePlaygroundStore((state) => state.inputSourceType);
  const prompts = usePlaygroundStore((state) => state.prompts);
  const manifests = useManifests();
  const input = useInput();

  const { manifestInputVariables } =
    usePlaygroundPromptTemplateVariables(manifests);

  // todo: may want to also consider determining if we should stack vertically based on the width of the screen
  const stackVertically =
    isSimplified || inputSourceType === 'dataset' || prompts.length > 1;

  const inputSection = useMemo(() => {
    const hasInputs = manifestInputVariables.some((m) => m.names.length > 0);
    // in simplified playground, we only show the input section if there are variables
    const hideInputSection =
      inputSourceType === 'dataset' || (!hasInputs && isSimplified);
    if (hideInputSection) {
      return null;
    }
    return <PromptInputSection manifests={manifests} input={input} />;
  }, [manifests, input, isSimplified, inputSourceType, manifestInputVariables]);

  const outputSection = useMemo(() => {
    return (
      <FancyRenderingProvider>
        <PromptOutputSection />
      </FancyRenderingProvider>
    );
  }, []);

  const inputAndOutputSection = useMemo(() => {
    return (
      <div className="flex h-full flex-col overflow-x-auto">
        {inputSection ? (
          <PanelGroup direction="vertical">
            <Panel
              defaultSize={
                PLAYGROUND_INPUT_OUTPUT_SECTION_LAYOUT_SIZES.inputs.default
              }
              minSize={PLAYGROUND_INPUT_OUTPUT_SECTION_LAYOUT_SIZES.inputs.min}
            >
              {inputSection}
            </Panel>
            <PlaygroundColumnDivider orientation="vertical" />
            <Panel
              defaultSize={
                PLAYGROUND_INPUT_OUTPUT_SECTION_LAYOUT_SIZES.output.default
              }
              minSize={PLAYGROUND_INPUT_OUTPUT_SECTION_LAYOUT_SIZES.output.min}
            >
              {outputSection}
            </Panel>
          </PanelGroup>
        ) : (
          outputSection
        )}
      </div>
    );
  }, [inputSection, outputSection]);

  const [promptCollapseState, setPromptCollapseState] = useState<
    Record<number, boolean>
  >(Object.fromEntries(prompts.map((_, index) => [index, false])));

  const toggleCollapseAllMessagesForPrompt = useCallback(
    (index: number, isAllCollapsed?: boolean) => {
      setPromptCollapseState((prev) => ({
        ...prev,
        [index]: isAllCollapsed ?? !prev[index],
      }));
    },
    []
  );

  const [hidePromptSection, setHidePromptSection] = useState<boolean>(false);

  useEffect(() => {
    if (inputSourceType !== PlaygroundInputSourceType.DATASET) {
      setHidePromptSection(false);
    }
  }, [inputSourceType]);

  const promptsSectionRef = useRef<HTMLDivElement>(null);
  const promptsSection = (
    <div
      className={cn(
        'flex h-full w-full flex-col overflow-y-hidden',
        isSimplified ? 'h-fit' : 'h-full'
      )}
      ref={promptsSectionRef}
    >
      <PlaygroundSectionHeader
        title="Prompts"
        collapsed={
          inputSourceType === PlaygroundInputSourceType.DATASET
            ? hidePromptSection
            : undefined
        }
        setCollapsed={setHidePromptSection}
        tooltipDescription={`Messages that will be sent to the AI model. Any variables will be populated using values from ${
          inputSourceType === 'dataset' ? 'the dataset' : 'the input fields'
        } before sending to the model.`}
        className={cn('shrink-0 pl-6', isSimplified && 'pr-2')}
        rightActions={
          isSimplified ? (
            // note: simplified playground assumes there is only one prompt
            <PlaygroundPromptTopBar
              index={0}
              isAllCollapsed={promptCollapseState[0]}
              setIsAllCollapsed={() => toggleCollapseAllMessagesForPrompt(0)}
            />
          ) : (
            <PlaygroundButton
              onClick={addPrompt}
              text="Compare"
              startDecorator={<PlusIcon className="my-auto h-4 w-4" />}
              tooltipTitle={`Add another prompt to compare with the current ${pluralize(
                'prompt',
                prompts.length
              )}.`}
            />
          )
        }
      />
      <div
        className={
          hidePromptSection
            ? 'hidden'
            : 'flex h-full grow flex-row overflow-x-auto'
        }
      >
        {prompts?.map((prompt, index) => {
          return (
            <div
              key={`prompt-${prompt.columnId}`}
              id={`prompt-${prompt.columnId}`}
              className={cn(
                'flex w-full flex-col !overflow-x-auto',
                index > 0 && 'border-l border-secondary',
                'h-full',
                prompts.length > 1 && 'min-w-[500px]'
              )}
            >
              <PromptPanel
                index={index}
                promptCollapseState={promptCollapseState[index]}
                setPromptCollapseState={toggleCollapseAllMessagesForPrompt}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
  const orientation = stackVertically ? 'vertical' : 'horizontal';

  const layout = PLAYGROUND_LAYOUT_SIZES[orientation];

  const renderBody = () => {
    if (isSimplified) {
      return (
        <div className="flex flex-col overflow-y-hidden">
          {promptsSection}
          {inputSection}
          {outputSection}
        </div>
      );
    }
    return (
      <div
        className={cn(
          'relative flex h-full w-full flex-row justify-stretch border-secondary'
        )}
      >
        <PanelGroup
          direction={orientation}
          className={cn(stackVertically ? 'w-full' : 'h-full')}
          key={orientation + '-' + hidePromptSection}
        >
          <Panel
            defaultSize={hidePromptSection ? 5 : layout.input.default}
            style={{ overflowY: 'auto' }}
            minSize={hidePromptSection ? 5 : layout.input.min}
            maxSize={hidePromptSection ? 5 : undefined}
          >
            {promptsSection}
          </Panel>
          <PlaygroundColumnDivider orientation={orientation} />
          <Panel
            defaultSize={layout.output.default}
            style={{ overflowY: 'auto' }}
            className={cn('border-primary', !stackVertically && 'border-l')}
            minSize={layout.output.min}
          >
            {inputAndOutputSection}
          </Panel>
        </PanelGroup>
      </div>
    );
  };

  return (
    <div
      data-testid="playground-layout"
      className="h-full grow overflow-y-auto overflow-x-hidden"
      ref={scrollRef}
    >
      {renderBody()}
    </div>
  );
}
