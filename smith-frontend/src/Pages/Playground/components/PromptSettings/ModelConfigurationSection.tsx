import { SerializedConstructor } from '@langchain/core/load/serializable';

import { merge, omit } from 'lodash-es';
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';

import { usePlaygroundStore } from '../../PlaygroundHome/hooks/usePlaygroundStore';
import {
  getModelNameFromRunnableBinding,
  getProviderFromRunnableBinding,
} from '../../utils/Playground.utils';
import { ManifestOptions } from '../../utils/types';
import { Manifest } from '..//manifest/Manifest';
import {
  DEFAULT_TOOL_CHOICE,
  EToolChoice,
  SUPPORTS_PARALLEL_TOOL_CALLS,
  TOOL_CHOICE_MAPPING,
} from '..//manifest/serialized/common/constants';
import { usePlaygroundSecrets } from '../PlaygroundSecrets.utils';
import { ManifestSlider } from '../manifest/ManifestSlider';
import { getOpenAIModelCapabilities } from '../manifest/serialized/chat_models/ManifestChatOpenAI.utils';
import { ExtraParamsJsonEditor } from './ModelConfiguration/ExtraParamsJsonEditor';
import { ModelSettingsPopoverLayout } from './ModelConfiguration/ModelSettingsPopoverLayout';
import {
  getExtraParams,
  getSettingsFromModelManifest,
} from './ModelConfiguration/utils';
import SettingsModalSection from './SettingsModalSection';

type ModelConfigurationProps = {
  manifest: SerializedConstructor;
  setManifest: (manifest: SerializedConstructor) => void;
  setEdited: (edited: boolean) => void;
  options: ManifestOptions;
  setOptions: (options: ManifestOptions) => void;
  currentSavedConfigId: string | undefined;
  setCurrentSavedConfigId: (id: string | undefined) => void;
  onSettingsChange?: () => void;
};

const ModelConfigurationSection: FC<ModelConfigurationProps> = ({
  manifest,
  setManifest,
  setEdited,
  options,
  setOptions,
  currentSavedConfigId,
  setCurrentSavedConfigId,
  onSettingsChange,
}) => {
  const manifestLast: SerializedConstructor = manifest.kwargs.last;
  const [secrets] = usePlaygroundSecrets({ serialized: manifest });

  const setIsStreaming = usePlaygroundStore((state) => state.setIsStreaming);
  const userInitiatedChangeRef = useRef(false);

  const handleManifestChange = useCallback(
    useDebouncedCallback((modelConfig: SerializedConstructor) => {
      const modelType = getProviderFromRunnableBinding(modelConfig);
      const modelName = getModelNameFromRunnableBinding(modelConfig);

      const { isStreamingSupported, isSystemMessagesSupported } =
        getOpenAIModelCapabilities(modelName);

      if (!SUPPORTS_PARALLEL_TOOL_CALLS.includes(modelType)) {
        setOptions({
          ...options,
          parallel_tool_calls: undefined,
        });
      }

      // Map the old tool choice to a new tool choice if it's supported by the new model
      // If tool choice is a specific tool name, we don't change it
      if (
        options.tool_choice &&
        Object.values(EToolChoice).includes(options.tool_choice as EToolChoice)
      ) {
        const newToolChoice =
          TOOL_CHOICE_MAPPING[modelType]?.[options.tool_choice as EToolChoice];
        setOptions({
          ...options,
          tool_choice: newToolChoice ?? DEFAULT_TOOL_CHOICE,
        });
      }

      let newMessages = manifest.kwargs.first.kwargs.messages ?? [];
      let newModelConfig = modelConfig;
      if (!isSystemMessagesSupported) {
        newMessages = newMessages.map((message) => {
          const messageType = message.id.at(-1);
          if (messageType !== 'SystemMessagePromptTemplate') return message;
          return {
            ...message,
            id: [...message.id.slice(0, -1), 'HumanMessagePromptTemplate'],
          };
        });
        newModelConfig = merge({}, modelConfig, {
          kwargs: {
            bound: {
              kwargs: {
                temperature: 1,
              },
            },
          },
        });
        if (!isStreamingSupported) {
          setIsStreaming(false);
        }
      }

      const newManifest = {
        ...manifest,
        kwargs: {
          ...manifest.kwargs,
          first: {
            ...manifest.kwargs.first,
            kwargs: {
              ...manifest.kwargs.first.kwargs,
              messages: newMessages,
            },
          },
          last: newModelConfig,
        },
      };
      setEdited(true);
      if (userInitiatedChangeRef.current) {
        onSettingsChange?.();
      }
      setManifest(newManifest);
    }),
    [
      setEdited,
      setManifest,
      manifest,
      setOptions,
      options,
      setIsStreaming,
      onSettingsChange,
    ]
  );

  const handleOptionsChange = useCallback(
    (options: ManifestOptions) => {
      setEdited(true);
      if (userInitiatedChangeRef.current) {
        onSettingsChange?.();
      }
      setManifest({
        ...manifest,
        kwargs: {
          ...manifest.kwargs,
          last: {
            ...manifest.kwargs.last,
            kwargs: {
              ...manifest.kwargs.last.kwargs,
              kwargs: options,
            },
          },
        },
      });
    },
    [setEdited, setManifest, manifest, onSettingsChange]
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      userInitiatedChangeRef.current = true;
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const currentExtraParams = getExtraParams(
    getSettingsFromModelManifest(manifestLast)
  );

  const [error, setError] = useState<string | undefined>(undefined);

  return (
    <SettingsModalSection
      title="Model Configuration"
      subtitle="Create and save model configurations to use across prompts in your workspace."
    >
      <ModelSettingsPopoverLayout
        manifest={manifest}
        setManifest={setManifest}
        currentConfigId={currentSavedConfigId}
        setCurrentConfigId={setCurrentSavedConfigId}
        savedOptions={{
          requests_per_second: options.requests_per_second,
        }}
        setSavedOptions={(savedOptions) => {
          setOptions({
            ...options,
            requests_per_second: savedOptions?.requests_per_second,
          });
        }}
      >
        <div className="flex flex-col gap-3">
          <Manifest
            hideTools={true}
            variant="pane"
            secrets={secrets}
            value={manifestLast}
            onChange={handleManifestChange}
            options={{}}
            onOptionsChange={handleOptionsChange}
          />
          <ManifestSlider
            title="Requests Per Second"
            description="Maximum rate LangSmith will run this model (e.g. over a dataset)."
            value={options.requests_per_second}
            min={0}
            max={500}
            step={1}
            onChange={(rps) =>
              setOptions({
                ...options,
                requests_per_second: rps ? rps : undefined,
              })
            }
            variant={'pane'}
          />

          <ExtraParamsJsonEditor
            value={currentExtraParams}
            onChange={(newExtraParams) => {
              const modelManifest = {
                ...manifest.kwargs.last,
              } as SerializedConstructor;
              const settings = getSettingsFromModelManifest(modelManifest);
              // Error if we're setting a key that already exists in the model configuration
              // Update kwargs by removing all current extra params and adding new ones
              if (settings.kwargs) {
                const oldExtraParams = getExtraParams(settings);
                const normalParams = omit(
                  settings.kwargs,
                  Object.keys(oldExtraParams)
                );

                if (
                  Object.keys(newExtraParams).some((key) =>
                    Object.keys(normalParams).includes(key)
                  )
                ) {
                  setError('This parameter is already supported in the UI');
                } else {
                  setError(undefined);
                  settings.kwargs = {
                    ...Object.fromEntries(
                      Object.entries(settings.kwargs).filter(
                        ([key]) =>
                          !Object.keys(currentExtraParams).includes(key)
                      )
                    ),
                    ...newExtraParams,
                  };

                  if (modelManifest.id.at(-1) == 'RunnableBinding') {
                    modelManifest.kwargs.bound = settings;
                  } else {
                    modelManifest.kwargs = settings.kwargs;
                  }
                  handleManifestChange(modelManifest);
                }
              }
            }}
            error={error}
          />
        </div>
      </ModelSettingsPopoverLayout>
    </SettingsModalSection>
  );
};

export default ModelConfigurationSection;
