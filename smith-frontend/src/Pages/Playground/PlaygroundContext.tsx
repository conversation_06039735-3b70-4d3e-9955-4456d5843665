import { SerializedConstructor } from '@langchain/core/load/serializable';

import {
  ExampleSchemaOrDraft,
  RepoWithLookupsSchema,
  RunSchema,
} from '@/types/schema';

import { PlaygroundInputValueType } from './PlaygroundHome/types';
import { ManifestOptions } from './utils/types';

export type PlaygroundHubPromptType = {
  prompt: RepoWithLookupsSchema;
  edited: boolean;
  commitHash?: string;
};

export enum PlaygroundInputSourceType {
  MANUAL = 'manual',
  DATASET = 'dataset',
}

export type PlaygroundPromptType = {
  manifest: SerializedConstructor;
  options: ManifestOptions;
  hubPrompt?: PlaygroundHubPromptType;
  columnId: string;
  sessionId?: string;
  runs?: RunSchema[];
  customExperimentName?: string;
};

export type ExampleSchemaOrDraftWithEdited = ExampleSchemaOrDraft & {
  edited: boolean;
};

export type PlaygroundContextType = {
  isAlignEvaluator: boolean;
  defaultModelManifest?: SerializedConstructor;
  prompts: PlaygroundPromptType[];
  input: Record<string, PlaygroundInputValueType>;
  datasetId?: string;
  isCreatingNewDataset: boolean;
  datasetSplits: string[];
  evaluatorIdSelectionStates?: Record<string, boolean>;
  examples: ExampleSchemaOrDraftWithEdited[];
  exampleCount: number;
  inputSourceType: PlaygroundInputSourceType;
  repetitions: number;
  loadingState?: 'loading' | 'done';
  evaluatorFeedbackKeys: string[];
  abortController: AbortController;
  originalRunId: string | null;
  isStreaming: boolean;
};
