import { MIME_TYPES } from '@/components/RichTextEditor/constants';
import CSVIcon from '@/icons/CSVIcon.svg?react';
import HTMLIcon from '@/icons/HTMLIcon.svg?react';
import IMGIcon from '@/icons/IMGIcon.svg?react';
import JSONIcon from '@/icons/JSONIcon.svg?react';
import MP3Icon from '@/icons/MP3Icon.svg?react';
import MP4Icon from '@/icons/MP4Icon.svg?react';
import PDFIcon from '@/icons/PDFIcon.svg?react';
import TXTIcon from '@/icons/TXTIcon.svg?react';
import { S3DataSchema } from '@/types/schema';
import { cn } from '@/utils/tailwind';

export const renderAttachmentIcon = (
  contentType: string | null,
  size: 'small' | 'default'
) => {
  const smallIconSize = 'h-4 w-4';
  if (!contentType) {
    return null;
  }
  if (contentType.startsWith(MIME_TYPES.IMAGE)) {
    return <IMGIcon className={cn(size === 'small' && smallIconSize)} />;
  }
  if (contentType.startsWith(MIME_TYPES.AUDIO)) {
    return <MP3Icon className={cn(size === 'small' && smallIconSize)} />;
  }
  if (contentType.startsWith(MIME_TYPES.VIDEO)) {
    return <MP4Icon className={cn(size === 'small' && smallIconSize)} />;
  }
  if (contentType === MIME_TYPES.HTML) {
    return <HTMLIcon className={cn(size === 'small' && smallIconSize)} />;
  }
  if (contentType === MIME_TYPES.TEXT) {
    return <TXTIcon className={cn(size === 'small' && smallIconSize)} />;
  }
  if (contentType === MIME_TYPES.CSV) {
    return <CSVIcon className={cn(size === 'small' && smallIconSize)} />;
  }
  if (contentType === MIME_TYPES.PDF) {
    return <PDFIcon className={cn(size === 'small' && smallIconSize)} />;
  }
  if (contentType === MIME_TYPES.JSON) {
    return <JSONIcon className={cn(size === 'small' && smallIconSize)} />;
  }

  return null;
};

export const getContentSize = (contentLength: string | null) => {
  const sizeInBytes = parseInt(contentLength ?? '0');
  if (sizeInBytes === 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB'];
  const index = Math.floor(Math.log(sizeInBytes) / Math.log(1024));
  const size = (sizeInBytes / Math.pow(1024, index)).toFixed(0);

  return `${size}${units[index]}`;
};

export const handleDownloadAttachment = async (url) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('There was a problem with the download request:', error);
  }
};

export const getAttachments = (
  attachmentUrls?: Record<string, S3DataSchema>
) => {
  return Object.entries(attachmentUrls || {})
    .filter(([key, _]) => key.startsWith('attachment'))
    .map(([key, value]) => ({
      name: key.replace('attachment.', ''),
      url: value.presigned_url,
      s3_url: value.storage_url,
    }));
};
