import Tooltip from '@mui/joy/Tooltip';

import { ReactElement } from 'react';

import TokenIcon from '@/icons/TokenIcon.svg?react';
import { getTokenUsage } from '@/utils/messages';
import { cn } from '@/utils/tailwind';
import { utcTime } from '@/utils/utc-time';

import { RunSchema } from '../../../types/schema';

const formatter = new Intl.NumberFormat('en-US', {});
const usdFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
  maximumFractionDigits: 20,
});
const shorterUsdFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
  maximumFractionDigits: 3,
});

export function CostChip({
  run,
  isLoading,
}: {
  run: RunSchema | null | undefined;
  isLoading?: boolean;
}) {
  return (
    <RawCostChip
      totalTokens={run?.total_tokens}
      promptTokens={run?.prompt_tokens}
      completionTokens={run?.completion_tokens}
      promptCost={run?.prompt_cost}
      completionCost={run?.completion_cost}
      timeToFirstTokenMs={utcTime(run?.first_token_time).diff(
        utcTime(run?.start_time),
        'milliseconds'
      )}
      totalCost={run?.total_cost}
      isLoading={isLoading}
    />
  );
}

export function RawCostChip({
  totalTokens,
  promptTokens,
  completionTokens,
  audioTokens,
  cacheReadTokens,
  cacheCreationTokens,
  reasoningTokens,
  promptCost,
  completionCost,
  timeToFirstTokenMs,
  totalCost,
  isLoading,
  simple,
}: RawChipsTokenProps & {
  isLoading?: boolean;
  simple?: boolean;
}) {
  if (isLoading) {
    return (
      <div
        className={cn(
          'flex w-fit items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-background px-1 py-0.5 text-xs',
          simple && 'border-none'
        )}
      >
        <span>...</span>
      </div>
    );
  }

  return totalCost != null ? (
    <RawTokensTooltip
      promptCost={promptCost}
      completionCost={completionCost}
      timeToFirstTokenMs={timeToFirstTokenMs}
      totalTokens={totalTokens}
      promptTokens={promptTokens}
      completionTokens={completionTokens}
      audioTokens={audioTokens}
      cacheReadTokens={cacheReadTokens}
      cacheCreationTokens={cacheCreationTokens}
      reasoningTokens={reasoningTokens}
    >
      <div
        className={cn(
          'flex w-fit items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-background px-1 py-0.5 text-xs',
          simple && 'border-none'
        )}
      >
        {shorterUsdFormatter.format(totalCost)}
      </div>
    </RawTokensTooltip>
  ) : null;
}

export function TokensChip({
  run,
  isLoading,
}: {
  run: RunSchema | null | undefined;
  isLoading?: boolean;
}) {
  if (isLoading) {
    return (
      <div className="flex items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-background px-1 py-0.5 text-xs">
        <TokenIcon />
        <span>...</span>
      </div>
    );
  }

  return run?.total_tokens != null ? (
    <TokensTooltip run={run}>
      <div className="flex items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-background px-1 py-0.5 text-xs">
        <TokenIcon />
        {formatter.format(run.total_tokens)}
      </div>
    </TokensTooltip>
  ) : null;
}

type RawChipsTokenProps = {
  totalTokens?: number | null;
  promptTokens?: number | null;
  completionTokens?: number | null;
  audioTokens?: number | null;
  cacheReadTokens?: number | null;
  cacheCreationTokens?: number | null;
  reasoningTokens?: number | null;
  promptCost?: number | null;
  completionCost?: number | null;
  timeToFirstTokenMs?: number | null;
  totalCost?: number | null;
  promptTokenDetails?: Record<string, number> | null;
  completionTokenDetails?: Record<string, number> | null;
  promptCostDetails?: Record<string, number> | null;
  completionCostDetails?: Record<string, number> | null;
};

export function RawTokensChip({
  totalTokens,
  promptTokens,
  completionTokens,
  audioTokens,
  cacheReadTokens,
  cacheCreationTokens,
  reasoningTokens,
  promptCost,
  completionCost,
  timeToFirstTokenMs,
  promptTokenDetails,
  completionTokenDetails,
  promptCostDetails,
  completionCostDetails,
  isLoading,
  simple,
}: RawChipsTokenProps & {
  isLoading?: boolean;
  simple?: boolean;
}) {
  if (isLoading) {
    return (
      <div
        className={cn(
          'flex w-fit items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-background px-1 py-0.5 text-xs',
          simple && 'border-none'
        )}
      >
        <TokenIcon />
        <span>...</span>
      </div>
    );
  }

  return totalTokens != null ? (
    <RawTokensTooltip
      totalTokens={totalTokens}
      promptTokens={promptTokens}
      completionTokens={completionTokens}
      audioTokens={audioTokens}
      cacheReadTokens={cacheReadTokens}
      cacheCreationTokens={cacheCreationTokens}
      reasoningTokens={reasoningTokens}
      promptCost={promptCost}
      completionCost={completionCost}
      timeToFirstTokenMs={timeToFirstTokenMs}
      promptTokenDetails={promptTokenDetails}
      completionTokenDetails={completionTokenDetails}
      promptCostDetails={promptCostDetails}
      completionCostDetails={completionCostDetails}
    >
      <div
        className={cn(
          'flex w-fit items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-background px-1 py-0.5 text-xs',
          simple && 'border-none'
        )}
      >
        {simple ? null : <TokenIcon />}
        {formatter.format(totalTokens)}
      </div>
    </RawTokensTooltip>
  ) : null;
}

export function TokensTooltip({
  run,
  children,
}: {
  run: RunSchema;
  children: ReactElement;
}) {
  const { cacheReadTokens, cacheCreationTokens, reasoningTokens, audioTokens } =
    getTokenUsage(run);

  return run.total_tokens != null ? (
    <RawTokensTooltip
      totalTokens={run.total_tokens}
      promptTokens={run.prompt_tokens}
      completionTokens={run.completion_tokens}
      cacheReadTokens={cacheReadTokens}
      cacheCreationTokens={cacheCreationTokens}
      reasoningTokens={reasoningTokens}
      audioTokens={audioTokens}
      promptCost={run.prompt_cost}
      completionCost={run.completion_cost}
      promptTokenDetails={run.prompt_token_details}
      completionTokenDetails={run.completion_token_details}
      promptCostDetails={run.prompt_cost_details}
      completionCostDetails={run.completion_cost_details}
      timeToFirstTokenMs={utcTime(run.first_token_time).diff(
        utcTime(run.start_time),
        'milliseconds'
      )}
    >
      {children}
    </RawTokensTooltip>
  ) : null;
}

export function RawTokensTooltip({
  totalTokens,
  promptTokens,
  completionTokens,
  cacheReadTokens,
  cacheCreationTokens,
  reasoningTokens,
  audioTokens,
  promptCost,
  completionCost,
  timeToFirstTokenMs,
  children,
  promptTokenDetails,
  completionTokenDetails,
  promptCostDetails,
  completionCostDetails,
}: {
  totalTokens?: number | null;
  promptTokens?: number | null;
  completionTokens?: number | null;
  cacheReadTokens?: number | null;
  cacheCreationTokens?: number | null;
  reasoningTokens?: number | null;
  audioTokens?: number | null;
  promptCost?: number | null;
  completionCost?: number | null;
  timeToFirstTokenMs?: number | null;
  children: ReactElement;
  promptTokenDetails?: Record<string, number> | null;
  completionTokenDetails?: Record<string, number> | null;
  promptCostDetails?: Record<string, number> | null;
  completionCostDetails?: Record<string, number> | null;
}) {
  const hasOneSubTokenCount = [
    audioTokens,
    promptTokens,
    completionTokens,
    reasoningTokens,
    cacheReadTokens,
    cacheCreationTokens,
  ].some((tokenCount) => tokenCount != null && tokenCount > 0);

  if (!hasOneSubTokenCount && timeToFirstTokenMs == null) {
    return children;
  }

  return totalTokens != null ? (
    <Tooltip
      title={
        <div className="rounded px-2 py-3 text-xs">
          {promptTokens != null && (
            <div className="mb-3">
              <div className="mb-1 grid grid-cols-[auto,1fr] gap-4">
                <div className="font-bold">Prompt</div>
                <div className="text-right font-bold font-normal">
                  {formatter.format(promptTokens ?? 0)}
                  {promptCost != null && (
                    <span className="ml-1 font-normal opacity-60">
                      / {usdFormatter.format(promptCost)}
                    </span>
                  )}
                </div>
              </div>

              {(promptTokenDetails || promptCostDetails) == null &&
                audioTokens != null && (
                  <div className="mb-1 ml-1 grid grid-cols-[auto,1fr] gap-4">
                    <div>• audio</div>
                    <div className="text-right font-normal">
                      {formatter.format(audioTokens ?? 0)}
                    </div>
                  </div>
                )}

              {(promptTokenDetails || promptCostDetails) == null &&
                cacheReadTokens != null && (
                  <div className="mb-1 ml-1 grid grid-cols-[auto,1fr] gap-4">
                    <div>• cache read</div>
                    <div className="text-right font-normal">
                      {formatter.format(cacheReadTokens ?? 0)}
                    </div>
                  </div>
                )}

              {(promptTokenDetails || promptCostDetails) == null &&
                cacheCreationTokens != null && (
                  <div className="mb-1 ml-1 grid grid-cols-[auto,1fr] gap-4">
                    <div>• cache create</div>
                    <div className="text-right font-normal">
                      {formatter.format(cacheCreationTokens ?? 0)}
                    </div>
                  </div>
                )}

              {(promptTokenDetails || promptCostDetails) &&
                Object.keys({
                  ...(promptTokenDetails || {}),
                  ...(promptCostDetails || {}),
                }).map((tokenType) => (
                  <div
                    className="mb-1 ml-1 grid grid-cols-[auto,1fr] gap-4"
                    key={tokenType}
                  >
                    <div>• {tokenType.replace(/_/g, ' ')}</div>
                    <div className="text-right font-normal">
                      {promptTokenDetails?.[tokenType] != null
                        ? formatter.format(promptTokenDetails[tokenType])
                        : '-'}
                      {promptCostDetails?.[tokenType] != null && (
                        <span className="ml-1 font-normal opacity-60">
                          / {usdFormatter.format(promptCostDetails[tokenType])}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          )}

          {completionTokens != null && (
            <div className={timeToFirstTokenMs != null ? 'mb-3' : ''}>
              <div className="mb-1 grid grid-cols-[auto,1fr] gap-4">
                <div className="font-bold">Completion</div>
                <div className="text-right font-bold font-normal">
                  {formatter.format(completionTokens ?? 0)}
                  {completionCost != null && (
                    <span className="ml-1 font-normal opacity-60">
                      / {usdFormatter.format(completionCost)}
                    </span>
                  )}
                </div>
              </div>

              {(completionTokenDetails || completionCostDetails) == null &&
                reasoningTokens != null && (
                  <div className="mb-1 ml-1 grid grid-cols-[auto,1fr] gap-4">
                    <div>• reasoning</div>
                    <div className="text-right font-normal">
                      {formatter.format(reasoningTokens ?? 0)}
                    </div>
                  </div>
                )}

              {(completionTokenDetails || completionCostDetails) &&
                Object.keys({
                  ...(completionTokenDetails || {}),
                  ...(completionCostDetails || {}),
                }).map((tokenType) => (
                  <div
                    className="mb-1 ml-1 grid grid-cols-[auto,1fr] gap-4"
                    key={tokenType}
                  >
                    <div>• {tokenType.replace(/_/g, ' ')}</div>
                    <div className="text-right font-normal">
                      {completionTokenDetails?.[tokenType] != null
                        ? formatter.format(completionTokenDetails[tokenType])
                        : '-'}
                      {completionCostDetails?.[tokenType] != null && (
                        <span className="ml-1 font-normal opacity-60">
                          /{' '}
                          {usdFormatter.format(
                            completionCostDetails[tokenType]
                          )}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          )}

          {timeToFirstTokenMs != null && !Number.isNaN(timeToFirstTokenMs) && (
            <div>
              <div className="font-bold">
                Time to first token:{' '}
                <span className="font-normal">
                  {timeToFirstTokenMs != null &&
                  !Number.isNaN(timeToFirstTokenMs)
                    ? `${timeToFirstTokenMs}ms`
                    : 'N/A'}
                </span>
              </div>
            </div>
          )}
        </div>
      }
    >
      {children}
    </Tooltip>
  ) : null;
}
