import {
  Download01Icon,
  Pencil01Icon,
  XCloseIcon,
} from '@langchain/untitled-ui-icons';
import {
  Button,
  Input,
  Modal,
  ModalClose,
  ModalDialog,
  Tooltip,
} from '@mui/joy';

import { FC, useEffect, useState } from 'react';

import { DeleteConfirmationButton } from '@/components/Delete';
import { ErrorBanner } from '@/components/ErrorBanner';
import { MultimodalContent } from '@/components/RichTextEditor/MultimodalContent';
import { Skeleton } from '@/components/Skeleton';
import { TextOverflowTooltip } from '@/components/TextOverflowTooltip';
import { useAttachmentMetadata } from '@/hooks/useAttachmentMetadata';
import { cn } from '@/utils/tailwind';

import {
  getContentSize,
  handleDownloadAttachment,
  renderAttachmentIcon,
} from '../utils/multimodalRunUtils';

export type TAttachment = {
  name: string;
  url: string;
  s3_url?: string;
  contentType?: string;
  contentLength?: string;
};

const Attachment: FC<{
  attachment: TAttachment;
  onDelete?: (attachment: TAttachment) => void;
  onRename?: (attachment: TAttachment, newName: string) => boolean;
  size?: 'small' | 'default';
}> = ({ attachment, onDelete, onRename, size = 'default' }) => {
  const [expanded, setExpanded] = useState(false);

  const {
    data: attachmentMetadata,
    isLoading,
    error,
  } = useAttachmentMetadata(
    attachment.url,
    attachment.contentType && attachment.contentLength
      ? {
          contentType: attachment.contentType,
          contentLength: attachment.contentLength,
        }
      : undefined
  );
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [newName, setNewName] = useState(attachment.name);
  useEffect(() => {
    setNewName(attachment.name);
  }, [attachment.name]);

  // Loading/error states only apply to remote files
  if (isLoading) return <Skeleton className="h-[55px] w-full" />;
  if (error) return <ErrorBanner>{error.message}</ErrorBanner>;

  if (!attachmentMetadata) return null;
  const { contentType, contentLength } = attachmentMetadata;

  const handleSave = () => {
    onRename &&
      onRename(attachment, newName) === true &&
      setIsEditModalOpen(false);
  };

  return (
    <div>
      <div
        key={attachment.name}
        className={cn(
          'group relative flex cursor-pointer items-center justify-between gap-1 rounded-md bg-secondary text-sm hover:bg-secondary-hover',
          size === 'default' ? 'p-2' : 'px-2'
        )}
        onClick={(e) => {
          e.stopPropagation();
          setExpanded(true);
        }}
      >
        <div className="flex flex-row gap-2">
          <div className="my-auto">
            {renderAttachmentIcon(contentType, size)}
          </div>
          <div className="flex flex-col px-2">
            <span className="max-w-[165px] truncate text-sm font-medium">
              {attachment.name}
            </span>
            <div
              className={cn(
                'flex items-center',
                size === 'default' && 'w-[170px]'
              )}
            >
              <span className="text-xs text-secondary after:mx-1 after:content-['\2022']">
                {contentType?.split('/')[1].toUpperCase()}
              </span>
              <span className="text-xs text-secondary after:mx-1">
                {getContentSize(contentLength)}
              </span>
            </div>
          </div>
        </div>
        <div className="invisible ml-auto flex gap-2 group-hover:visible">
          {attachment.url && (
            <Tooltip title="Download">
              <button
                type="button"
                className="text-tertiary hover:opacity-80"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownloadAttachment(attachment.url);
                }}
              >
                <Download01Icon className="h-4 w-4" />
              </button>
            </Tooltip>
          )}
          {onRename && (
            <Tooltip title="Rename">
              <button
                type="button"
                className="text-tertiary hover:opacity-80"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditModalOpen(true);
                }}
              >
                <Pencil01Icon className="h-4 w-4" />
              </button>
            </Tooltip>
          )}
          {onDelete && (
            <DeleteConfirmationButton
              onDelete={() => onDelete(attachment)}
              title="Delete attachment"
              description="Are you sure you want to delete this attachment?"
              deleteButtonText="Delete"
              isDeleting={false}
              trigger={
                <Tooltip title="Delete">
                  <button
                    type="button"
                    className="text-tertiary hover:opacity-80"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(attachment);
                    }}
                  >
                    <XCloseIcon className="h-4 w-4" />
                  </button>
                </Tooltip>
              }
            />
          )}
        </div>
      </div>
      {expanded && (
        <Modal
          open={true}
          onClose={(e: React.MouseEvent<HTMLElement> | KeyboardEvent) => {
            e.stopPropagation();
            setExpanded(false);
          }}
        >
          <ModalDialog
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
            }}
          >
            <ModalClose />
            <div className="mb-4 flex items-center gap-2">
              <h2 className="max-w-[200px] truncate text-xl font-bold">
                <TextOverflowTooltip>{attachment.name}</TextOverflowTooltip>
              </h2>

              <span className="mr-8 mt-1 text-sm text-tertiary">
                {attachmentMetadata.contentType?.split('/')[1].toUpperCase()}
              </span>
            </div>
            <div className="flex h-full w-full items-center justify-center overflow-auto">
              <div className="max-h-full max-w-full overflow-auto">
                <MultimodalContent
                  contentType={attachmentMetadata.contentType}
                  url={attachment.url}
                  contentName={attachment.name}
                  unstyled={true}
                />
              </div>
            </div>
          </ModalDialog>
        </Modal>
      )}
      {isEditModalOpen && (
        <Modal
          open={true}
          onClose={() => setIsEditModalOpen(false)}
          disablePortal
        >
          <ModalDialog>
            <ModalClose />
            <h2 className="mb-4 text-xl font-bold">Rename Attachment</h2>
            <div className="flex flex-col gap-4">
              <Input
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
                placeholder="Enter new name"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSave();
                  }
                }}
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="plain"
                  color="neutral"
                  onClick={() => setIsEditModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button id="save-rename-attachment-button" onClick={handleSave}>
                  Save
                </Button>
              </div>
            </div>
          </ModalDialog>
        </Modal>
      )}
    </div>
  );
};

type TProps = {
  attachments: TAttachment[];
  className?: string;
  gridClassName?: string;
  onDelete?: (attachment: TAttachment) => void;
  onRename?: (attachment: TAttachment, newName: string) => boolean;
  attachmentsRef?: React.RefObject<HTMLDivElement>;
  hideTitle?: boolean;
  size?: 'small' | 'default';
};

const Attachments: FC<TProps> = ({
  attachments,
  className,
  gridClassName,
  onDelete,
  onRename,
  attachmentsRef,
  hideTitle,
  size = 'default',
}) => {
  return (
    <div className={cn('p-4', className)} ref={attachmentsRef}>
      {!hideTitle && (
        <div className="flex items-center gap-1">
          <span className="text-lg font-medium">Attachments</span>
          <div className="rounded-md bg-tertiary px-1 text-sm">
            {attachments.length}
          </div>
        </div>
      )}
      {attachments.length === 0 ? (
        <div className="text-sm text-tertiary">
          <i>No attachments</i>
        </div>
      ) : (
        <div
          className={cn(
            'flex flex-wrap gap-2 overflow-y-auto rounded-md pt-2',
            gridClassName
          )}
        >
          {attachments.map((a, idx) => (
            <Attachment
              key={idx}
              attachment={a}
              onDelete={onDelete}
              onRename={onRename}
              size={size}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Attachments;
