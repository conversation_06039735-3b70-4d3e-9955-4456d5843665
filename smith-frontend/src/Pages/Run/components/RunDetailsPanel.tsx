import { DecorationSet } from '@codemirror/view';
import { <PERSON>, Toolt<PERSON> } from '@mui/joy';
import Alert from '@mui/joy/Alert';

import { Dispatch, SetStateAction } from 'react';
import { Link as RouterLink } from 'react-router-dom';

import Header from '@/components/CodeHeader';
import { ElapsedTime } from '@/components/ElapsedTime';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { FeedbackChips } from '@/components/FeedbackChips';
import { FeedbackTable } from '@/components/FeedbackTable';
import { RunLatencyChip } from '@/components/RunLatencyChip';
import { RunStatusIcon } from '@/components/RunStatus';
import { RunTags } from '@/components/RunTags';
import { RunTimeToFirstToken } from '@/components/RunTimeToFirstToken';
import { RunTypeChip } from '@/components/RunTypeChip';
import {
  <PERSON>b<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>b<PERSON>ist,
  <PERSON>b<PERSON>ane<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useTab,
} from '@/components/Tabs';
import { usePermissions } from '@/hooks/usePermissions';
import SidebarIcon from '@/icons/SidebarIcon.svg?react';
import { ExampleSchema, RunSchema } from '@/types/schema';
import { formatTimestamp } from '@/utils';
import { isRunIncomplete } from '@/utils/get-run-incomplete-status';
import { getTokenUsage } from '@/utils/messages';
import { cn, utils } from '@/utils/tailwind';
import { localTime } from '@/utils/utc-time';

import { getAttachments } from '../utils/multimodalRunUtils';
import { isGraphInterrupt } from '../utils/utils';
import Attachments from './Attachments';
import { RunExtraCard } from './RunExtraCard';
import { RunHeader } from './RunHeader';
import { RunInputsAndOutputs } from './RunInputsAndOutputs';
import { TokensTooltip } from './TokensChip';
import { TAB_PREFIX, formatter, usdFormatter } from './constants';

function RunDetailSidebarToggle(props: {
  isActive: boolean;
  onClick: () => void;
  tabPrefix: string;
  runId?: string;
  traceId?: string;
  className?: string;
}) {
  const tab = useTab({ prefix: props.tabPrefix });
  return (
    <div
      className={cn('bottom-2.5 mr-5 flex items-center gap-3', props.className)}
    >
      {tab === 0 && (
        <button
          type="button"
          className={cn(
            'hover flex h-6 w-6 flex-row items-center justify-center',
            utils.button
          )}
          title="Toggle Sidebar"
          onClick={props.onClick}
        >
          <SidebarIcon
            className={cn(props.isActive ? 'opacity-70' : 'opacity-40')}
          />
        </button>
      )}
    </div>
  );
}

function RunErrorSection({ run }: { run?: RunSchema }) {
  if (!run?.error) {
    return null;
  }

  if (isGraphInterrupt(run.error)) {
    const shortMessage = run.error.split('Traceback')[0];
    return (
      <div className="border-b border-tertiary p-4">
        <Header text="Interrupt" copy={shortMessage} />
        <ExpandableErrorAlert error={shortMessage} color="info" />
      </div>
    );
  }

  return (
    <div className="border-b border-tertiary p-4">
      <Header text="Error" copy={run.error} />
      <ExpandableErrorAlert error={run.error} />
    </div>
  );
}

export function RunDetailsPanel({
  run,
  example,
  examplePath,
  isRunLoading,
  priorThreadRunIds,
  isPublicPage,
  shareToken,
  datasetShareToken,
  sidebarVisible,
  setSidebarVisible,
  isComparing,
  onClose,
  scrollRef,
  forceRawRender,
  getDiffValues,
  inputsLoading,
  outputsLoading,
  errorLoading,
  showSeparateAttachments,
}: {
  run?: RunSchema;
  example?: ExampleSchema;
  examplePath?: string;
  isRunLoading: boolean;
  priorThreadRunIds?: string[];
  isPublicPage: boolean;
  shareToken?: string;
  datasetShareToken?: string;
  sidebarVisible: boolean;
  setSidebarVisible: Dispatch<SetStateAction<boolean>>;
  isComparing?: boolean;
  onClose?: () => void;
  scrollRef?: React.RefObject<HTMLDivElement>;
  forceRawRender?: 'raw' | 'rendered';
  getDiffValues?: (type: 'inputs' | 'outputs') =>
    | {
        decoration: DecorationSet;
        generatedCode: string | undefined;
      }
    | undefined;
  inputsLoading?: boolean;
  outputsLoading?: boolean;
  errorLoading?: boolean;
  showSeparateAttachments?: boolean;
}) {
  const { authorize } = usePermissions();
  const start = localTime(run?.start_time);
  const end = localTime(run?.end_time);

  const { totalTokens } = getTokenUsage(run);

  const attachments = getAttachments(run?.s3_urls);
  return (
    <>
      <RunHeader
        run={run}
        isComparing={isComparing}
        priorThreadRunIds={
          run !== undefined && run.parent_run_id == null
            ? priorThreadRunIds
            : undefined
        }
        onClose={onClose}
      />

      <TabGroup prefix={TAB_PREFIX} setTabMethod="merge">
        <div className="flex justify-between border-b border-secondary @container">
          <TabList className="mx-0 mb-0 border-none">
            <TabLabel>Run</TabLabel>
            {authorize('feedback:read') && <TabLabel>Feedback</TabLabel>}
            <TabLabel>Metadata</TabLabel>
          </TabList>
          <RunDetailSidebarToggle
            tabPrefix={TAB_PREFIX}
            isActive={sidebarVisible}
            onClick={() => setSidebarVisible((state) => !state)}
            runId={run?.id}
            traceId={run?.trace_id}
          />
        </div>

        <TabPanels
          className="flex-grow overflow-y-auto break-anywhere"
          ref={scrollRef}
        >
          <TabPanel className="relative">
            <div
              className={cn(
                'grid h-full',
                sidebarVisible && 'grid-cols-[4fr,minmax(220px,1fr)]'
              )}
            >
              <div className="overflow-x-hidden">
                <RunErrorSection run={run} />

                {run && (
                  <RunInputsAndOutputs
                    run={run}
                    inputsLoading={inputsLoading}
                    outputsLoading={outputsLoading}
                    errorLoading={errorLoading}
                    example={example}
                    isRunLoading={isRunLoading}
                    forceRawRender={forceRawRender}
                    getDiffValues={getDiffValues}
                    showSeparateAttachments={showSeparateAttachments}
                  />
                )}
                {attachments.length > 0 && (
                  <Attachments attachments={attachments} />
                )}
              </div>
              {sidebarVisible && (
                <div className="sticky right-0 border-l border-tertiary bg-background pl-4 pt-4">
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Feedback
                      </span>
                      <div className="mr-2 flex flex-col items-start gap-2">
                        {run && 'feedback_stats' in run && (
                          <FeedbackChips
                            feedbackStats={run.feedback_stats}
                            feedbackSourceRunId={run.id}
                            allowTruncation
                            showErrorCount
                          />
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Start Time
                      </span>
                      <div className="font-medium">
                        {run?.start_time && (
                          <Tooltip
                            title={start && <ElapsedTime date={start} />}
                            variant="solid"
                          >
                            <span>{formatTimestamp(run?.start_time)}</span>
                          </Tooltip>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        End Time
                      </span>
                      <div className="font-medium">
                        {run?.end_time && (
                          <Tooltip
                            title={end && <ElapsedTime date={end} />}
                            variant="solid"
                          >
                            <span>{formatTimestamp(run?.end_time)}</span>
                          </Tooltip>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Time to First Token
                      </span>
                      <div className="font-medium">
                        {run && <RunTimeToFirstToken run={run} />}
                      </div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Status
                      </span>
                      <div className="flex items-center justify-center gap-1 font-medium capitalize">
                        {run && <RunStatusIcon run={run} />}{' '}
                        {run && isRunIncomplete(run)
                          ? 'incomplete'
                          : run?.status}
                      </div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Total tokens
                      </span>
                      <div className="font-medium">
                        {run && typeof totalTokens === 'number' && (
                          <TokensTooltip run={run}>
                            <span>
                              {formatter.format(totalTokens)} tokens
                              {run.total_cost != null && (
                                <span className="text-tertiary">
                                  {' / '}
                                  {usdFormatter.format(run.total_cost)}
                                </span>
                              )}
                            </span>
                          </TokensTooltip>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Latency
                      </span>
                      <div>{run && <RunLatencyChip run={run} size="sm" />}</div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Type
                      </span>
                      <div>{run && <RunTypeChip run={run} size="sm" />}</div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        Tags
                      </span>
                      <div className="flex flex-wrap items-center gap-2">
                        <RunTags run={run} isPublicPage={isPublicPage} />
                      </div>
                    </div>

                    <div className="flex flex-col items-start gap-1 [&:has(>div:last-child:empty)]:hidden">
                      <span className="text-xs font-medium uppercase tracking-wide text-slate-500">
                        From Example
                      </span>
                      <div>
                        {example && examplePath && (
                          <Link component={RouterLink} to={examplePath}>
                            {example.name}
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabPanel>
          {authorize('feedback:read') && (
            <TabPanel className="p-4">
              {run && (
                <FeedbackTable
                  shareToken={shareToken}
                  datasetShareToken={datasetShareToken}
                  filter={{
                    run: run.id,
                    session: run.session_id,
                  }}
                  emptyState={
                    <Alert variant="outlined">
                      There is no feedback recorded for this run.
                    </Alert>
                  }
                />
              )}
            </TabPanel>
          )}
          <TabPanel className="p-4">
            {run && <RunExtraCard run={run} />}
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </>
  );
}
