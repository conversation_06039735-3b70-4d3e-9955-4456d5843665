import { useParams } from 'react-router-dom';

import { useSessions } from '@/hooks/useSwr';

import { Breadcrumb, BreadcrumbData } from '../../components/Breadcrumbs';

export function SingleDashboardCrumbV2(props: Partial<BreadcrumbData>) {
  const { sessionId } = useParams();

  // For tracer sessions (prebuilt dashboards)
  const { data: sessions } = useSessions(
    sessionId
      ? {
          limit: 1,
          offset: 0,
          id: [sessionId],
        }
      : null
  );

  // If we have a session ID, this is a prebuilt dashboard
  if (sessionId && sessions?.rows?.length) {
    return (
      <Breadcrumb name={`Prebuilt for ${sessions.rows[0].name}`} {...props} />
    );
  }

  // Fallback
  return <Breadcrumb name="Dashboard" {...props} />;
}
