import { BarChart10Icon } from '@langchain/untitled-ui-icons';
import { Tooltip } from '@mui/joy';

import { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import Breadcrumbs from '@/components/Breadcrumbs';
import { DateTimeRangePicker } from '@/components/DateTimeRangePicker/DateTimeRangePicker';
import { DateTimeRangePickerValue } from '@/components/RunsTable/utils/getDateTimeRangeLabel';
import { ScrollSpy } from '@/components/ScrollSpy/ScrollSpy';
import { useScrollSpy } from '@/components/ScrollSpy/hooks/useScrollSpy';
import {
  ScrollSpyElement,
  ScrollSpyState,
} from '@/components/ScrollSpy/hooks/useScrollSpyState';
import { Skeleton } from '@/components/Skeleton';
import { useDefaultDuration } from '@/hooks/useDefaultDuration';
import { usePermissions } from '@/hooks/usePermissions';
import { usePrebuiltDashboardForSessionStream } from '@/hooks/useSwr';
import {
  CustomChartExistingInfo,
  RunStatsGroupBy,
  TimedeltaInput,
} from '@/types/schema';

import { PageTitle } from '../../components/PageTitle';
import { DashboardsLayout } from '../Dashboards/components/DashboardsLayout';
import { ChartsGrid } from '../SingleDashboard/ChartsGrid';
import { ChartsCrudPane } from '../SingleDashboard/CustomChartsCrudPane';
import {
  AddButton,
  SessionLinks,
} from '../SingleDashboard/components/CustomChartComponents';
import { DashboardGlobalGroupBy } from '../SingleDashboard/components/DashboardGlobalGroupBy';
import { DocsButton } from '../SingleDashboard/components/DocsButton';
import { useChartCrud } from '../SingleDashboard/hooks/useChartCrud';
import { useChartTimeFilter } from '../SingleDashboard/hooks/useChartTimeFilter';
import {
  getUniqueSessionIdsFromChart,
  isDashboardReadOnly,
} from '../SingleDashboard/utils/CustomChartsUtils.utils';
import { MAX_CHARTS_PER_SECTION } from '../SingleDashboard/utils/constants';
import { useSeriesColorsMap } from '../SingleDashboard/utils/getSeriesColor';

const DashboardHeader = ({
  title,
  description,
  dashboardId,
  sessionId,
  timeModel,
  setTimeModel,
  chartsExist,
  openExistingChart,
  atMaxCharts,
  isPrebuilt,
  scrollSpyState,
  elements,
  customChartsParams,
  groupBySelection,
  setGroupBySelection,
  isLoading,
  sessionIdsInDashboard,
}: {
  title: string;
  description?: string;
  dashboardId?: string;
  sessionId?: string;
  timeModel: DateTimeRangePickerValue;
  setTimeModel: (value: DateTimeRangePickerValue) => void;
  chartsExist: boolean;
  openExistingChart: (chartData: CustomChartExistingInfo) => void;
  atMaxCharts?: boolean;
  isPrebuilt?: boolean;
  isLoading?: boolean;
  scrollSpyState: ScrollSpyState;
  elements?: ScrollSpyElement[];
  customChartsParams: {
    start_time: string;
    end_time: string;
    stride: TimedeltaInput;
  };
  groupBySelection?: RunStatsGroupBy;
  setGroupBySelection: (groupBySelection?: RunStatsGroupBy) => void;
  sessionIdsInDashboard: string[];
}) => {
  // if title exists, the dashboard has been loaded once
  const initialLoaded = Boolean(title);

  return (
    <>
      <div className="flex items-center gap-2 px-4">
        <BarChart10Icon size={24} className="mr-2" />
        <PageTitle>
          {isLoading && !initialLoaded ? (
            <Skeleton className="h-6 w-40" />
          ) : (
            title
          )}
        </PageTitle>
        {!isPrebuilt && initialLoaded && (
          <Tooltip
            title={
              atMaxCharts
                ? `Maximum number of charts per dashboard reached (${MAX_CHARTS_PER_SECTION})`
                : ''
            }
          >
            <div className="py-4">
              <AddButton
                onClick={() =>
                  openExistingChart({ section_id: dashboardId || null })
                }
                text="Chart"
                variant="solid"
                disabled={atMaxCharts}
              />
            </div>
          </Tooltip>
        )}
        <div className="flex items-center gap-2">
          <SessionLinks
            sessionIds={sessionIdsInDashboard}
            hideProjectNames={false}
            buttonClassName="bg-primary border-secondary border rounded-md p-1.5"
            variant="dashboard"
          />
          <DocsButton link="https://docs.smith.langchain.com/observability/how_to_guides/dashboards#prebuilt-dashboards" />
        </div>
      </div>
      <p className="-mt-4 mb-4 max-w-[calc(100vw-400px)] truncate pl-4 text-base text-tertiary">
        {description}
      </p>

      <div className="sticky top-0 z-[10] -mt-4 flex flex-wrap items-center justify-between gap-2 bg-primary px-4 py-4">
        {chartsExist && (
          <>
            {elements && scrollSpyState.needsScrollSpy ? (
              <ScrollSpy
                {...scrollSpyState}
                elements={elements}
                horizontal
                elementClassName="lg:px-3 lg:py-1.5 lg:text-base sm:px-1.5 sm:py-1 sm:text-xs"
              />
            ) : (
              <div />
            )}

            <div className="flex gap-2">
              {sessionId && timeModel && (
                <DashboardGlobalGroupBy
                  sessionId={sessionId}
                  customChartsParams={customChartsParams}
                  groupBySelection={groupBySelection}
                  setGroupBySelection={setGroupBySelection}
                />
              )}
              <DateTimeRangePicker
                value={timeModel}
                onChange={setTimeModel}
                hideAllTime
                align="end"
                context="dashboard"
                triggerClassName="bg-primary text-xs text-primary hover:bg-secondary"
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

const SingleDashboard = () => {
  const { authorize } = usePermissions();
  const { sessionId } = useParams();

  const { isOpen, chartData, openExistingChart, closeChartCrudPane } =
    useChartCrud();

  const { defaultDurationTimeModel } = useDefaultDuration('dashboard');

  const timeFilter = useChartTimeFilter({
    timeRange: defaultDurationTimeModel,
  });

  const [groupBySelection, setGroupBySelection] = useState<
    RunStatsGroupBy | undefined
  >(undefined);

  const {
    data: dashboard,
    mutate: mutateChartData,
    isLoading,
    error,
  } = usePrebuiltDashboardForSessionStream(
    sessionId,
    {
      ...timeFilter.customChartsParams,
      group_by: groupBySelection,
    },
    { revalidateOnFocus: false }
  );

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [chartsRendered, setChartsRendered] = useState(false);

  const isPrebuilt = isDashboardReadOnly(dashboard);
  const sections = useMemo(() => {
    if (!isPrebuilt || !dashboard?.charts) return undefined;

    const sectionsModel = dashboard.sub_sections ?? [];

    return sectionsModel.map((section) => ({
      id: section.id,
      title: section.title,
    }));
  }, [dashboard?.charts, dashboard?.sub_sections, isPrebuilt]);

  const sessionIdsInDashboard = useMemo(() => {
    if (!dashboard) return [];

    const chartIds =
      dashboard.charts?.flatMap(getUniqueSessionIdsFromChart) || [];
    const subsectionIds =
      dashboard.sub_sections?.flatMap((section) =>
        section.charts.flatMap(getUniqueSessionIdsFromChart)
      ) || [];

    return Array.from(new Set([...chartIds, ...subsectionIds]));
  }, [dashboard]);

  // This is a hack to make sure the scroll spy renders if there are charts and the screen is scrollable.
  // For some reason, we were seeing a flaky issue where the scroll spy wasn't rendered, even though the
  // isVisible value seems to be correct. This bug doesn't occur in any of the other places where the scroll
  // spy is used, but I spent enough time trying to debug it that it seems to be not worth my time to spend
  // any more on it, leaving us with this useEffect.
  useEffect(() => {
    if (
      isPrebuilt &&
      (!!dashboard?.charts?.length || !!dashboard?.sub_sections?.length) &&
      !isLoading &&
      !!sections &&
      !!sections.length
    ) {
      const timer = setTimeout(() => {
        setChartsRendered(true);
      }, 0);
      return () => clearTimeout(timer);
    } else {
      setChartsRendered(false);
    }
  }, [
    dashboard?.charts,
    dashboard?.sub_sections?.length,
    isLoading,
    isPrebuilt,
    sections,
  ]);

  const { scrollSpyState, sectionRefs, scrollSpyElements } = useScrollSpy({
    scrollContainerRef,
    isElementVisible: chartsRendered,
    sections,
    scrollOffset: 60,
  });

  const hideProjectNames = sessionIdsInDashboard.length < 2;

  const seriesColorsMap = useSeriesColorsMap({
    dashboard,
    isPrebuilt,
  });

  if (!authorize('charts:read')) {
    return null;
  }

  return (
    <DashboardsLayout
      handleScroll={scrollSpyState.handleScroll}
      ref={scrollContainerRef}
    >
      <div className="pt-3" />
      <div className="px-4">
        <Breadcrumbs />
      </div>
      <DashboardHeader
        isLoading={isLoading}
        title={dashboard?.title ?? ''}
        description={dashboard?.description}
        sessionId={sessionId || dashboard?.session_id}
        timeModel={timeFilter.timeModel}
        setTimeModel={timeFilter.setTimeModel}
        customChartsParams={timeFilter.customChartsParams}
        chartsExist={
          !!dashboard?.charts?.length || !!dashboard?.sub_sections?.length
        }
        openExistingChart={openExistingChart}
        isPrebuilt={isPrebuilt || !!sessionId}
        scrollSpyState={scrollSpyState}
        elements={scrollSpyElements}
        groupBySelection={groupBySelection}
        setGroupBySelection={setGroupBySelection}
        sessionIdsInDashboard={sessionIdsInDashboard}
      />
      <ChartsGrid
        charts={dashboard?.charts ?? []}
        sub_sections={dashboard?.sub_sections}
        isLoading={isLoading}
        mutateChartData={mutateChartData}
        openExistingChart={openExistingChart}
        timeFilter={timeFilter}
        error={error}
        isPrebuilt={isPrebuilt || !!sessionId}
        sectionRefs={sectionRefs}
        dashboardId={sessionId}
        groupBySelection={groupBySelection}
        hideProjectNames={hideProjectNames}
        seriesColorsMap={seriesColorsMap}
      />

      <ChartsCrudPane
        key={chartData?.id}
        open={isOpen}
        onSuccess={mutateChartData}
        onClose={closeChartCrudPane}
        existingChart={chartData}
        seriesColorsMap={seriesColorsMap}
      />
    </DashboardsLayout>
  );
};

export default SingleDashboard;
