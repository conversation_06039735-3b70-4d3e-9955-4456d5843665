import { useMemo } from 'react';

import { RunSchema } from '@/types/schema';

export function getSortedRepetitionRuns(
  runs: RunSchema[],
  sortByDescending: boolean,
  sortByFeedbackKey?: string | null
) {
  return (
    runs?.sort((a, b) => {
      if (
        !sortByFeedbackKey ||
        (!a.feedback_stats?.[sortByFeedbackKey] &&
          !b.feedback_stats?.[sortByFeedbackKey])
      ) {
        const aStartTime = new Date(a.start_time).getTime();
        const bStartTime = new Date(b.start_time).getTime();
        return aStartTime - bStartTime;
      } else if (
        a.feedback_stats?.[sortByFeedbackKey] &&
        !b.feedback_stats?.[sortByFeedbackKey]
      ) {
        return -1;
      } else if (
        !a.feedback_stats?.[sortByFeedbackKey] &&
        b.feedback_stats?.[sortByFeedbackKey]
      ) {
        return 1;
      }
      return sortByDescending
        ? (b.feedback_stats?.[sortByFeedbackKey].avg ?? 0) -
            (a.feedback_stats?.[sortByFeedbackKey].avg ?? 0)
        : (a.feedback_stats?.[sortByFeedbackKey].avg ?? 0) -
            (b.feedback_stats?.[sortByFeedbackKey].avg ?? 0);
    }) ?? []
  );
}

export function useSortedRepetitionRuns(
  runs: RunSchema[],
  sortByDescending: boolean,
  sortByFeedbackKey?: string | null
) {
  return useMemo(
    () => getSortedRepetitionRuns(runs, sortByDescending, sortByFeedbackKey),
    [runs, sortByDescending, sortByFeedbackKey]
  );
}
