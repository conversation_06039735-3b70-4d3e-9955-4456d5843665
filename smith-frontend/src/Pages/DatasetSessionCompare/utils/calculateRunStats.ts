import { RunSchema } from '@/types/schema';
import { getTokenUsage } from '@/utils/messages';
import { utcTime } from '@/utils/utc-time';

// Explicitly specify which properties are numbers and which are records
export interface RunStats {
  // Numeric properties
  totalTokens: number | null;
  promptTokens: number | null;
  completionTokens: number | null;
  audioTokens: number | null;
  cacheReadTokens: number | null;
  cacheCreationTokens: number | null;
  reasoningTokens: number | null;
  promptCost: number | null;
  completionCost: number | null;
  timeToFirstTokenMs: number | null;
  totalCost: number | null;

  // Record properties
  promptTokenDetails: Record<string, number> | null;
  promptCostDetails: Record<string, number> | null;
  completionTokenDetails: Record<string, number> | null;
  completionCostDetails: Record<string, number> | null;
}

// Define types for our intermediate data structure
interface StatValue {
  sum: number;
  count: number;
}

type DetailedStats = Record<string, StatValue>;
type StatsAccumulator = Record<string, StatValue | DetailedStats>;

// Define numeric keys and detail keys for type safety
const numericKeys = [
  'totalTokens',
  'promptTokens',
  'completionTokens',
  'audioTokens',
  'cacheReadTokens',
  'cacheCreationTokens',
  'reasoningTokens',
  'promptCost',
  'completionCost',
  'timeToFirstTokenMs',
  'totalCost',
] as const;

const detailKeys = [
  'promptTokenDetails',
  'promptCostDetails',
  'completionTokenDetails',
  'completionCostDetails',
] as const;

export const calculateRunStats = (runs: RunSchema[]): RunStats => {
  const stats = runs.reduce((acc, run) => {
    const {
      totalTokens,
      promptTokens,
      completionTokens,
      audioTokens,
      cacheReadTokens,
      cacheCreationTokens,
      reasoningTokens,
    } = getTokenUsage(run);

    const updateStat = (key: string, value?: number) => {
      if (value != null) {
        if (!acc[key]) {
          acc[key] = { sum: 0, count: 0 };
        }
        const stat = acc[key] as StatValue;
        stat.sum += value;
        stat.count++;
      }
    };

    // Function to update detailed stats (for objects like promptTokenDetails)
    const updateDetailedStats = (
      key: string,
      details?: Record<string, number>
    ) => {
      if (details && Object.keys(details).length > 0) {
        if (!acc[key]) {
          acc[key] = {};
        }

        const detailedStat = acc[key] as DetailedStats;

        Object.entries(details).forEach(([detailKey, value]) => {
          if (value != null) {
            if (!detailedStat[detailKey]) {
              detailedStat[detailKey] = { sum: 0, count: 0 };
            }
            detailedStat[detailKey].sum += value;
            detailedStat[detailKey].count++;
          }
        });
      }
    };

    updateStat('totalTokens', totalTokens);
    updateStat('promptTokens', promptTokens);
    updateStat('completionTokens', completionTokens);
    updateStat('audioTokens', audioTokens);
    updateStat('cacheReadTokens', cacheReadTokens);
    updateStat('cacheCreationTokens', cacheCreationTokens);
    updateStat('reasoningTokens', reasoningTokens);
    updateStat('promptCost', run.prompt_cost);
    updateStat('completionCost', run.completion_cost);
    updateStat('totalCost', run.total_cost);

    // Update detailed token and cost stats
    updateDetailedStats('promptTokenDetails', run.prompt_token_details);
    updateDetailedStats('completionTokenDetails', run.completion_token_details);
    updateDetailedStats('promptCostDetails', run.prompt_cost_details);
    updateDetailedStats('completionCostDetails', run.completion_cost_details);

    if (run.first_token_time) {
      const timeToFirstToken = utcTime(run.first_token_time).diff(
        utcTime(run.start_time),
        'milliseconds'
      );
      updateStat('timeToFirstTokenMs', timeToFirstToken);
    }

    return acc;
  }, {} as StatsAccumulator);

  const result = {} as RunStats;

  // Process the accumulated stats - handle numeric values
  for (const key of numericKeys) {
    const value = stats[key] as StatValue | undefined;
    if (value && value.count > 0) {
      result[key] = value.sum / value.count;
    } else {
      result[key] = null;
    }
  }

  // Process the detailed stats separately
  for (const key of detailKeys) {
    const value = stats[key] as DetailedStats | undefined;
    if (value && Object.keys(value).length > 0) {
      const resultDetails: Record<string, number> = {};

      for (const [detailKey, detailValue] of Object.entries(value)) {
        resultDetails[detailKey] =
          detailValue.count > 0 ? detailValue.sum / detailValue.count : 0;
      }

      result[key] = resultDetails;
    } else {
      result[key] = null;
    }
  }

  return result;
};
