import { ArrowNarrowRightIcon } from '@langchain/untitled-ui-icons';
import { Loading01Icon } from '@langchain/untitled-ui-icons';
import { Tooltip } from '@mui/joy';

import { useMemo } from 'react';

import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { SingleFeedbackChipWithSourceRunAction } from '@/components/FeedbackChips';
import { RunSchema } from '@/types/schema';
import { isEvaluatorRunExpired } from '@/utils/isEvaluatorRunExpired';
import { cn } from '@/utils/tailwind';

import { useFeedbackSourceRunIds } from '../../hooks/useFeedbackSourceRunIds';
import { OnlyErrorsChip } from './OnlyErrorsChip';

export const SingleFeedbackWithSource = ({
  feedbackKey,
  run,
  setOpenRunId,
  chipClassName,
  valueClassName,
  isHeatmapVisible,
  maxWidth,
  isTextTruncated,
  onMutate,
  traceTier,
  experimentStartTime,
}: {
  feedbackKey: string;
  run: RunSchema;
  setOpenRunId: (runId: string | null) => void;
  chipClassName?: string;
  valueClassName?: string;
  isHeatmapVisible: boolean;
  maxWidth?: number;
  isTextTruncated?: boolean;
  onMutate?: () => void;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) => {
  const feedbackSourceRunIds = useFeedbackSourceRunIds(
    feedbackKey,
    [run.id],
    null
  );
  const feedbackStats = run.feedback_stats?.[feedbackKey];
  const feedbackSourceRun = feedbackSourceRunIds.data?.[0];
  const source = feedbackSourceRun?.feedback_source;
  const autoEvalRunId = source?.metadata?.__run?.run_id;
  const runComment = feedbackSourceRun?.comment;
  const feedbackComment = feedbackStats?.comments?.[0]?.replace(
    /^["']|["']$/g,
    ''
  );

  // Use the utility function to check if the evaluator run has expired
  const isRunExpired = useMemo(
    () => isEvaluatorRunExpired(traceTier, experimentStartTime),
    [traceTier, experimentStartTime]
  );

  return (
    <div className="flex w-full items-center gap-2">
      <div
        onMouseEnter={() => {
          if (!feedbackSourceRunIds.data) {
            feedbackSourceRunIds.trigger();
          }
        }}
      >
        {feedbackStats?.n ||
        (feedbackStats?.values &&
          Object.keys(feedbackStats?.values).length > 0) ? (
          <SingleFeedbackChipWithSourceRunAction
            feedbackKey={feedbackKey}
            avg={feedbackStats?.avg}
            n={feedbackStats?.n}
            values={feedbackStats?.values}
            errors={feedbackStats?.errors}
            feedbackSourceRunId={run.id}
            feedbackSourceRuns={[run]}
            hideFeedbackKey
            hideEllipsis
            startDecorator={<></>}
            chipClassName={chipClassName}
            valueClassName={valueClassName}
            tooltip={runComment}
            onMutate={onMutate}
          />
        ) : feedbackComment ? (
          feedbackStats?.errors ? (
            <Error
              text={feedbackComment}
              isTextTruncated={isTextTruncated}
              maxWidth={maxWidth}
            />
          ) : (
            <Text
              text={feedbackComment}
              isTextTruncated={isTextTruncated}
              maxWidth={maxWidth}
            />
          )
        ) : (
          <OnlyErrorsChip
            tooltipText={runComment ?? feedbackKey}
            numErrors={feedbackStats?.errors ?? 0}
            isHeatmapVisible={isHeatmapVisible}
            hideBlankScore={!isHeatmapVisible}
          />
        )}
      </div>
      {feedbackStats?.show_feedback_arrow && (
        <button
          type="button"
          className={
            'invisible h-fit rounded-md bg-gray-100 p-1 group-hover:visible dark:border dark:border-secondary dark:bg-background'
          }
          onClick={async () => {
            autoEvalRunId && setOpenRunId(autoEvalRunId);
          }}
        >
          {feedbackSourceRunIds.isMutating ? (
            <Loading01Icon className="h-4 w-4 animate-spin text-[#667085] dark:text-white" />
          ) : (
            <Tooltip
              title={
                isRunExpired ? 'Evaluator Run Expired' : 'View Evaluator Run'
              }
            >
              <div className="my-auto flex">
                <button
                  type="button"
                  className={
                    isRunExpired ? 'cursor-not-allowed opacity-75' : ''
                  }
                  disabled={isRunExpired}
                  onClick={async () => {
                    if (isRunExpired) return;

                    if (!feedbackSourceRunIds.data) {
                      await feedbackSourceRunIds.trigger();
                    }
                    const runId =
                      feedbackSourceRunIds.data?.[0]?.feedback_source?.metadata
                        ?.__run?.run_id;
                    if (runId) {
                      setOpenRunId(runId);
                    }
                  }}
                >
                  <ArrowNarrowRightIcon className="h-4 w-4 text-[#667085] dark:text-white" />
                </button>
              </div>
            </Tooltip>
          )}
        </button>
      )}
    </div>
  );
};

function Text({
  text,
  isTextTruncated,
  maxWidth,
  className,
}: {
  text: string;
  isTextTruncated?: boolean;
  maxWidth?: number;
  className?: string;
}) {
  return (
    <Tooltip
      title={text}
      style={{ maxWidth: '350px', maxHeight: '300px', overflow: 'auto' }}
      enterDelay={0}
      leaveDelay={50}
    >
      <div
        className={cn(
          'truncate text-sm',
          !isTextTruncated && 'text-wrap',
          className
        )}
        style={{ maxWidth: maxWidth ? maxWidth - 50 : undefined }}
      >
        {text}
      </div>
    </Tooltip>
  );
}

function Error({
  text,
  isTextTruncated,
  maxWidth,
}: {
  text: string;
  isTextTruncated?: boolean;
  maxWidth?: number;
}) {
  return isTextTruncated ? (
    <Text
      text={text}
      className="text-ls-red-420 font-medium"
      isTextTruncated={isTextTruncated}
      maxWidth={maxWidth}
    />
  ) : (
    <ExpandableErrorAlert error={text} />
  );
}
