import { useMemo } from 'react';

import { FEEDBACK_COLUMNS_DEFAULT_WIDTH } from '@/Pages/DatasetSessionCompare/constants';
import { isRealRun } from '@/Pages/DatasetSessionCompare/utils/isRealRun';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useFeedbackConfigs } from '@/hooks/useSwr';
import {
  ExampleSchemaWithRunsAndOptionalFields,
  FeedbackConfigSchema,
  RunSchema,
  SessionSchema,
} from '@/types/schema';
import { cn } from '@/utils/tailwind';

import { AggregateFeedbackChips } from '../../AggregateFeedbackChips';
import { Loader } from '../Loader';
import { SingleExperimentCell } from '../SingleExperimentCell';
import {
  HEATMAP_COLORS,
  getHeatmapStyleFromPercentage,
} from '../utils/getHeatmapStyleFromPercentage';
import { SingleFeedbackWithSource } from './SingleFeedbackWithSource';

const FeedbackCell = ({
  feedbackKey,
  runs,
  mutateInfiniteExamples,
  datasetShareToken,
  expectedFeedbackKeys,
  columnIndex,
  columnWidthStorage,
  isTruncated,
  setOpenRunId,
  isHeatmapVisible,
  experimentInProgress,
  feedbackConfig,
  sessions,
}: {
  feedbackKey: string;
  runs: RunSchema[];
  mutateInfiniteExamples: () => void;
  datasetShareToken?: string;
  expectedFeedbackKeys?: string[];
  columnIndex: number;
  columnWidthStorage: number[];
  isTruncated: boolean;
  setOpenRunId: (runId: string | null) => void;
  isHeatmapVisible: boolean;
  experimentInProgress: boolean;
  feedbackConfig?: FeedbackConfigSchema;
  sessions?: SessionSchema[];
}) => {
  const { isDarkMode } = useColorScheme();
  const allFeedbackStats = useMemo(
    () =>
      runs
        .filter(
          (
            run
          ): run is RunSchema & {
            feedback_stats: NonNullable<RunSchema['feedback_stats']>;
          } => !!run.feedback_stats?.[feedbackKey]
        )
        .map((run) => ({
          run_id: run.id,
          feedback_stats: run.feedback_stats[feedbackKey],
        })),
    [runs, feedbackKey]
  );

  const singleRunId = useMemo(() => {
    if (allFeedbackStats.length == 1) {
      return runs.find((run) => run.id === allFeedbackStats[0].run_id)!;
    }
    return null;
  }, [runs, allFeedbackStats]);

  const numErrors = allFeedbackStats.reduce(
    (sum, stats) => sum + (stats.feedback_stats?.errors ?? 0),
    0
  );
  const numN = allFeedbackStats.reduce(
    (sum, stats) =>
      sum +
      (stats.feedback_stats?.n ?? 0) +
      Object.values(stats.feedback_stats?.values ?? {}).reduce(
        (sum, value) => sum + (value ?? 0),
        0
      ),
    0
  );
  const showAggregateFeedbackChips = numErrors + numN > 1;

  const noFeedback = !(
    showAggregateFeedbackChips || allFeedbackStats.length === 1
  );
  // If there isn't a score, don't show heatmap styling
  const showHeatmap =
    isHeatmapVisible &&
    !noFeedback &&
    (allFeedbackStats.length !== 1 || !!allFeedbackStats[0]?.feedback_stats?.n);

  const {
    text: textStyle,
    background: backgroundStyle,
    hover: hoverStyle,
  } = useMemo(() => {
    if (!showHeatmap) return { text: '', background: '', hover: '' };
    if (!allFeedbackStats.length)
      return { text: '', background: '', hover: '' };
    // all errors
    if (!allFeedbackStats.some((stats) => !stats.feedback_stats?.errors))
      return HEATMAP_COLORS.middle[isDarkMode ? 'dark' : 'light'];
    const minScore =
      typeof feedbackConfig?.feedback_config.min === 'number'
        ? feedbackConfig?.feedback_config.min
        : allFeedbackStats[0]?.feedback_stats?.session_min_score;
    const maxScore =
      typeof feedbackConfig?.feedback_config.max === 'number'
        ? feedbackConfig?.feedback_config.max
        : allFeedbackStats[0]?.feedback_stats?.session_max_score;

    const avgScores = allFeedbackStats
      .map((fs) => fs.feedback_stats?.avg)
      .filter((avg): avg is number => avg != null);

    if (avgScores.length === 0) {
      return { text: '', background: '', hover: '' };
    }

    const { text, background, hover } = getHeatmapStyleFromPercentage({
      colorMode: isDarkMode ? 'dark' : 'light',
      min: minScore,
      max: maxScore,
      score: avgScores.reduce((sum, avg) => sum + avg, 0) / avgScores.length,
      isLowerBetter: !!feedbackConfig?.is_lower_score_better,
    });
    return { text, background, hover };
  }, [allFeedbackStats, isDarkMode, showHeatmap, feedbackConfig]);

  const isExpected = useMemo(
    () =>
      !allFeedbackStats.find((fs) =>
        Object.keys(fs.feedback_stats).includes(feedbackKey)
      ) && expectedFeedbackKeys?.includes(feedbackKey),
    [expectedFeedbackKeys, allFeedbackStats, feedbackKey]
  );

  // If there is only one run and it is not a real run, then we haven't actually run the experiment yet
  // We use fake runs for displaying errors pre-running the experiment
  const isLoading =
    experimentInProgress ||
    (isExpected &&
      runs.length > 0 &&
      !(runs.length === 1 && !isRealRun(runs[0]) && !runs[0].inputs));

  const session = useMemo(
    () => sessions?.find((session) => session.id === singleRunId?.session_id),
    [sessions, singleRunId]
  );

  return (
    <SingleExperimentCell
      columnIndex={columnIndex}
      columnWidthStorage={columnWidthStorage}
      isTruncated={isTruncated}
      minColumnWidth={FEEDBACK_COLUMNS_DEFAULT_WIDTH}
    >
      <div className="rounded-md">
        {showAggregateFeedbackChips ? (
          <div className="flex flex-row gap-2">
            <AggregateFeedbackChips
              runs={runs}
              showSingleFeedbackKey={feedbackKey}
              allowTruncation
              hideFeedbackKey
              showErrorCount
              showErrorsOutside={showHeatmap}
              onMutate={mutateInfiniteExamples}
              disablePopover={!!datasetShareToken}
              expectedFeedbackKeys={expectedFeedbackKeys}
              onViewEvaluatorRun={(runId: string) => {
                setOpenRunId(runId);
              }}
              startDecorator={<></>}
              chipClassName={cn(
                'w-fit rounded-md border-none',
                showHeatmap && backgroundStyle,
                showHeatmap && hoverStyle
              )}
              valueClassName={showHeatmap ? textStyle : undefined}
              hideEllipsis={true}
            />
          </div>
        ) : allFeedbackStats.length === 1 ? (
          <SingleFeedbackWithSource
            feedbackKey={feedbackKey}
            run={singleRunId!}
            setOpenRunId={setOpenRunId}
            onMutate={mutateInfiniteExamples}
            chipClassName={cn(
              'rounded-md border-none',
              showHeatmap
                ? cn(backgroundStyle, hoverStyle, 'min-w-12 justify-center')
                : 'px-0'
            )}
            valueClassName={showHeatmap ? textStyle : undefined}
            isHeatmapVisible={showHeatmap}
            maxWidth={columnWidthStorage[columnIndex]}
            isTextTruncated={isTruncated}
            traceTier={session?.trace_tier}
            experimentStartTime={session?.start_time}
          />
        ) : isLoading ? (
          <Loader />
        ) : (
          <span className="flex text-sm text-ls-gray-200">No feedback</span>
        )}
      </div>
    </SingleExperimentCell>
  );
};

export const FeedbackColumns = ({
  sessionId,
  example,
  hiddenFeedbackColumns,
  experimentInProgress,
  isTruncated,
  mutateInfiniteExamples,
  datasetShareToken,
  expectedFeedbackKeys,
  columnIndexStart,
  columnWidthStorage,
  aggregateFeedbackKeys,
  isHeatmapVisible,
  setOpenRunId,
  sessionsData,
}: {
  sessionId: string;
  example: ExampleSchemaWithRunsAndOptionalFields;
  hiddenFeedbackColumns: string[];
  experimentInProgress: boolean;
  isTruncated: boolean;
  mutateInfiniteExamples: () => void;
  datasetShareToken?: string;
  expectedFeedbackKeys?: string[];
  columnIndexStart: number;
  columnWidthStorage: number[];
  aggregateFeedbackKeys: string[];
  isHeatmapVisible: boolean;
  setOpenRunId: (runId: string | null) => void;
  sessionsData?: SessionSchema[];
}) => {
  const filteredRuns = useMemo(
    () => example.runs.filter((run) => run.session_id === sessionId),
    [example.runs, sessionId]
  );

  const fbKeys = new Set([
    ...aggregateFeedbackKeys,
    ...(expectedFeedbackKeys ?? []),
  ]);

  const feedbackConfigs = useFeedbackConfigs(
    {
      skip: !((aggregateFeedbackKeys?.length ?? 0) > 0),
    },
    {
      key: aggregateFeedbackKeys,
      read_after_write: true,
    }
  );

  return Array.from(fbKeys).map((feedbackKey, idx) => {
    if (hiddenFeedbackColumns.includes(feedbackKey)) return null;
    const feedbackConfig = feedbackConfigs.data?.find(
      (fc) => fc.feedback_key === feedbackKey
    );
    return (
      <FeedbackCell
        key={feedbackKey}
        feedbackKey={feedbackKey}
        runs={filteredRuns}
        mutateInfiniteExamples={mutateInfiniteExamples}
        datasetShareToken={datasetShareToken}
        expectedFeedbackKeys={expectedFeedbackKeys}
        columnIndex={columnIndexStart + idx}
        columnWidthStorage={columnWidthStorage}
        isTruncated={isTruncated}
        setOpenRunId={setOpenRunId}
        isHeatmapVisible={isHeatmapVisible}
        experimentInProgress={experimentInProgress}
        feedbackConfig={feedbackConfig}
        sessions={sessionsData}
      />
    );
  });
};
