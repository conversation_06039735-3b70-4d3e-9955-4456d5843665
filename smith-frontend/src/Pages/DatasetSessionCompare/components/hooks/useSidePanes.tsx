import { useCallback, useState } from 'react';

import {
  SetSearchParamsOverride,
  useStateFromSearchParams,
} from '@/hooks/useStateFromSearchParams';
import { ExampleSchemaWithRunsAndOptionalFields } from '@/types/schema';

import { useRepetitionSidePane } from './useRepetitionSidePane';

export const useSidePanes = ({
  setSearchParams,
  examples,
  logClick,
}: {
  setSearchParams: SetSearchParamsOverride;
  logClick: (componentName: string, clickMetadata?: object | undefined) => void;
  examples?: ExampleSchemaWithRunsAndOptionalFields[];
}) => {
  const { repetitionsSidePaneInfo, setRepetitionsSidePaneInfo } =
    useRepetitionSidePane(setSearchParams);

  // For the detail side panel with example and associated runs
  const [rowDetailExampleId, setRowDetailExampleId] = useStateFromSearchParams(
    'peekDetail',
    null,
    setSearchParams
  );

  const [exampleDetailInSidePaneOpen, setExampleDetailInSidePaneOpen] =
    useState(false);
  const [exampleDetailInNestedPaneOpen, setExampleDetailInNestedPaneOpen] =
    useState(false);

  // For the detail side panel with just the example
  const [detailSingleExampleId, setDetailSingleExampleId] =
    useStateFromSearchParams('peekExample', null, setSearchParams);

  const currDetailIdx = rowDetailExampleId
    ? examples?.findIndex((i) => i.id === rowDetailExampleId)
    : detailSingleExampleId
    ? examples?.findIndex((i) => i.id === detailSingleExampleId)
    : repetitionsSidePaneInfo?.example
    ? examples?.findIndex((i) => i.id === repetitionsSidePaneInfo?.example)
    : null;

  const detailExample =
    currDetailIdx != null && currDetailIdx >= 0
      ? examples?.[currDetailIdx]
      : null;

  const [detailOpenTime, setDetailOpenTime] = useState<Date | null>(null);

  const [openRunId, setOpenRunId] = useStateFromSearchParams(
    'trace',
    null,
    setSearchParams
  );

  const [openTraceId, setOpenTraceId] = useStateFromSearchParams(
    'peeked_trace_id',
    null,
    setSearchParams
  );

  const reset = useCallback(() => {
    setDetailSingleExampleId(null);
    setOpenRunId(null);
    setOpenTraceId(null);
    setRepetitionsSidePaneInfo(null, null, null);
    setRowDetailExampleId(null);
  }, [
    setDetailSingleExampleId,
    setOpenRunId,
    setOpenTraceId,
    setRepetitionsSidePaneInfo,
    setRowDetailExampleId,
  ]);

  const resetExampleDetail = () => {
    setExampleDetailInSidePaneOpen(false);
    setExampleDetailInNestedPaneOpen(false);
    if (detailOpenTime) {
      logClick('close_detail_pane', {
        timeSpent: Math.floor(
          (new Date().getTime() - detailOpenTime.getTime()) / 1000
        ),
      });
      setDetailOpenTime(null);
    }
  };

  const toggleRowExampleDetail = useCallback(
    (exampleId: string | null) => {
      reset();
      setRowDetailExampleId(exampleId);
      if (exampleId) {
        logClick('open_detail_pane', {
          exampleId: exampleId,
        });
        setDetailOpenTime(new Date());
      }
    },
    [reset, setRowDetailExampleId, logClick, setDetailOpenTime]
  );

  const toggleRunDetail = useCallback(
    (runId: string | null, traceId?: string | null) => {
      reset();
      setOpenRunId(runId);
      setOpenTraceId(traceId ?? null);
    },
    [reset, setOpenRunId, setOpenTraceId]
  );

  const openDetailsOrRepetitionsPane = useCallback(
    (
      example: ExampleSchemaWithRunsAndOptionalFields,
      runIndex: number,
      sessionId: string
    ) => {
      if (example.runs.filter((r) => r.session_id === sessionId).length > 1) {
        setRepetitionsSidePaneInfo(
          sessionId,
          example.id,
          example.runs[runIndex].id
        );
      } else {
        toggleRowExampleDetail(example.id);
      }
    },
    [toggleRowExampleDetail, setRepetitionsSidePaneInfo]
  );

  const nextExampleId =
    currDetailIdx != null && currDetailIdx + 1 < (examples?.length ?? 0)
      ? examples?.[currDetailIdx + 1]?.id
      : null;
  const prevExampleId =
    currDetailIdx != null && currDetailIdx >= 1
      ? examples?.[currDetailIdx - 1]?.id
      : null;

  return {
    reset,
    resetExampleDetail,
    toggleRowExampleDetail,
    toggleRunDetail,
    openDetailsOrRepetitionsPane,
    detailExample,
    detailOpenTime,
    openRunId,
    openTraceId,
    exampleDetailInSidePaneOpen,
    exampleDetailInNestedPaneOpen,
    detailSingleExampleId,
    rowDetailExampleId,
    repetitionsSidePaneInfo,

    nextExampleId,
    prevExampleId,
    currentExampleIndex: currDetailIdx,

    setDetailSingleExampleId,
    setOpenRunId,
    setOpenTraceId,
    setRepetitionsSidePaneInfo,
    setExampleDetailInSidePaneOpen,
    setExampleDetailInNestedPaneOpen,
  };
};
