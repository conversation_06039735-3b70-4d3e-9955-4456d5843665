import LinearProgress from '@mui/joy/LinearProgress';

import {
  ComponentProps,
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TableComponents, TableVirtuoso } from 'react-virtuoso';
import { useDebouncedCallback } from 'use-debounce';

import { Example } from '@/Pages/Example';
import { ExampleSchemaOrDraftWithEdited } from '@/Pages/Playground/PlaygroundContext';
import { CodeLanguageType } from '@/components/Code/types';
import { InfoTooltip } from '@/components/InfoTooltip/InfoTooltip';
import { LoadMoreButton } from '@/components/RunsTable/LoadMoreButton';
import { SplitViewPane } from '@/components/SplitViewPane';
import {
  useDataset,
  useDatasetFeedbackDelta,
  useOrganizationId,
  useSessions,
} from '@/hooks/useSwr';
import {
  ComparisonViewColumn,
  DatasetDataType,
  ExampleSchemaWithRunsAndOptionalFields,
} from '@/types/schema';
import {
  appDatasetsPath,
  appExamplePath,
  appOrganizationPath,
  appProjectsPath,
  appPublicDatasetsPath,
  appPublicPath,
  appSessionPath,
} from '@/utils/constants';
import { LogPageContext } from '@/utils/datadog/LogPageContext';
import { useClickLogger } from '@/utils/datadog/useClickLogger';
import { useInfoLogger } from '@/utils/datadog/useInfoLogger';
import { usePageTracking } from '@/utils/datadog/usePageTracking';
import { cn } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { DetailPaneHeader } from '../DetailPaneHeader';
import {
  ATTACHMENTS_COLUMN_IDX,
  BLANK_EXAMPLE,
  COMPARISON_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY,
  DatasetSessionCompareSettings,
  EXPERIMENT_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY,
  GENERIC_FACETS,
  INPUT_COLUMN_IDX,
  METRICS_COLUMNS,
  OUTPUT_START_COLUMN_IDX,
  REFERENCE_COLUMN_IDX,
  ROW_DETAIL_PANE_LANGUAGE_LOCAL_STORAGE_KEY,
  TextDisplayMode,
} from '../constants';
import { anyExampleHasAttachments } from '../utils/anyExampleHasAttachments';
import { findMostDistinctKeyByHeuristic } from '../utils/findMostDistinctKeyByHeuristic';
import { isInPlayground } from '../utils/isInPlayground';
import { useGetExperimentDiffs } from '../utils/useGetExperimentDiffs';
import { getSortedRepetitionRuns } from '../utils/useSortedRepetitionRuns';
import { ComparisonOutputColumnHeader } from './ComparisonOutputColumnHeader';
import { DatasetSessionCompareFilter } from './DatasetSessionCompareFilter';
import { DatasetSessionCompareRepetitionsDetailPane } from './DatasetSessionCompareRepetitionsDetailPane';
import { DatasetSessionCompareRowDetailPane } from './DatasetSessionCompareRowDetailPane/DatasetSessionCompareRowDetailPane';
import { ExperimentRunDetails } from './ExperimentRunDetails';
import { ExperimentColumnHeader } from './SingleExperimentColumns/ExperimentColumnHeader';
import { FeedbackColumnHeaders } from './SingleExperimentColumns/FeedbackColumns/FeedbackColumnHeaders';
import { MetricsColumnHeaders } from './SingleExperimentColumns/MetricsColumns/MetricsColumnHeaders';
import { RemoveReferenceOutputButton } from './SingleExperimentComponents/RemoveReferenceOutputButton';
import { TableRow } from './SingleExperimentComponents/TableRow';
import { TracingProjectPreview } from './TracingProjectPreview';
import { useSessionExtras } from './hooks/useSessionExtras';
import { useSessionFilters } from './hooks/useSessionFilters';
import { useSidePanes } from './hooks/useSidePanes';
import { useSortedColumns } from './hooks/useSortedColumns';

type TableComponentProps<Key extends keyof TableComponents> = ComponentProps<
  Exclude<TableComponents<unknown, unknown>[Key], undefined>
>;

type LoadMoreButtonProps = {
  hasMoreToLoad: boolean;
  isNextPageValidating: boolean;
  setExamplesSize: (f: (size: number) => number) => void;
  examples: ExampleSchemaWithRunsAndOptionalFields[];
};

const Table = forwardRef<HTMLTableElement, TableComponentProps<'Table'>>(
  (props, ref) => {
    const { hasMoreToLoad, isNextPageValidating, setExamplesSize, examples } =
      props.context as LoadMoreButtonProps;
    return (
      <>
        <table
          ref={ref}
          className="min-w-full border-separate border-spacing-0 whitespace-nowrap border-secondary text-sm"
        >
          {props.children}
        </table>
        {hasMoreToLoad && (examples?.length ?? 0 > 0) && (
          <div className="flex flex-col items-center border-t border-secondary">
            <LoadMoreButton
              isInitialLoad
              isLoading={isNextPageValidating}
              onClick={() => setExamplesSize((size) => size + 1)}
            />
          </div>
        )}
      </>
    );
  }
);

export const DatasetSessionCompareTable = ({
  datasetId,
  datasetShareToken,
  columns, // Passed in in sorted order
  sessions,
  selectedBaselineSessionId,

  examples,
  setExample,
  onRemoveReferenceOutput,
  areExamplesLoading,
  setExamplesSize,
  mutateInfiniteExamplesSwr,
  hasMoreToLoad,
  isNextPageValidating,

  displaySettingsState,
  pageTitle = 'Comparing Experiments',
  expectedFeedbackKeys,
  experimentInProgress,
  localStoragePrefix,
  regressionsFilterFeedbackKey,
  datadogPageName,
  setFilteringRegressionsOnSession,
  filteringRegressionsOnSession,
  setRegressionsFilteringToImprovements,
  regressionsFilteringToImprovements,
  filters: filterParams,
  setFilters: setFilterParamsState,
  useWindowScroll = true,
  addedSessions,
  className,
  dataType,
  onDeleteExample,
  isDeleteLoading,
  creatingNew = false,
  customExtraHeaderColumn,
  customExtraTableCell,
}: {
  datasetId?: string;
  datasetShareToken?: string;
  columns: ComparisonViewColumn[];
  sessions?: ReturnType<typeof useSessions>;
  selectedBaselineSessionId?: string | null;
  hoveredSession: number | null;
  setHoveredSession: (session: number | null) => void;
  examples?: ExampleSchemaWithRunsAndOptionalFields[];
  setExample?: (
    exampleId: string,
    example: (
      prevExample: ExampleSchemaOrDraftWithEdited
    ) => ExampleSchemaOrDraftWithEdited
  ) => void;
  onRemoveReferenceOutput?: () => void;
  areExamplesLoading: boolean;
  setExamplesSize: (f: (size: number) => number) => void;
  mutateInfiniteExamplesSwr: () => void;
  hasMoreToLoad: boolean;
  isNextPageValidating: boolean;
  displaySettingsState: DatasetSessionCompareSettings;
  pageTitle?: string;
  expectedFeedbackKeys?: string[];
  experimentInProgress?: boolean;
  localStoragePrefix?: string;
  regressionsFilterFeedbackKey?: string | null;
  datadogPageName: string;
  setFilteringRegressionsOnSession?: (sessionId: string | null) => void;
  filteringRegressionsOnSession: string | null;
  setRegressionsFilteringToImprovements?: (improvements: boolean) => void;
  regressionsFilteringToImprovements: boolean;
  filters?: Record<string, any>;
  setFilters?: (filters: Record<string, any>) => void;
  useWindowScroll?: boolean;
  addedSessions?: string[];
  className?: string;
  dataType?: DatasetDataType;
  onDeleteExample?: (example: ExampleSchemaWithRunsAndOptionalFields) => void;
  isDeleteLoading?: boolean;
  creatingNew?: boolean;
  customExtraHeaderColumn?: React.ReactNode;
  customExtraTableCell?: (
    example: ExampleSchemaWithRunsAndOptionalFields
  ) => React.ReactNode;
}) => {
  const {
    isReferenceInputHidden,
    isReferenceOutputHidden,
    setIsReferenceOutputHidden,
    isAttachmentsHidden: _isAttachmentsHidden,

    language,
    selectedCharts,

    textDisplayMode,
    setTextDisplayMode,

    hiddenFeedbackColumns,
    hiddenMetricsColumns,
  } = displaySettingsState;

  const inPlayground = isInPlayground();

  const isAttachmentsHidden = useMemo(() => {
    return _isAttachmentsHidden || !anyExampleHasAttachments(examples);
  }, [_isAttachmentsHidden, examples]);

  const organizationId = useOrganizationId();

  const [searchParams, setSearchParams] = useSearchParams();

  const comparativeExperiment = searchParams.get('comparativeExperiment');

  const dataset = useDataset(datasetId, { datasetShareToken });

  const sessionIds = useMemo(
    () => columns.map((col) => col['sessionId']).filter(Boolean),
    [columns]
  );
  const columnIds = useMemo(
    () => columns.map((col) => col['columnId']).filter(Boolean),
    [columns]
  );

  const { setFiltersForSession: _setFiltersForSession, filters } =
    useSessionFilters(
      searchParams,
      setSearchParams,
      filterParams,
      setFilterParamsState,
      sessionIds
    );

  const {
    aggregateFeedbackKeys,
    aggregateSessionLevelFeedbackKeys,
    feedbackRunFacets,
    metricsRunFacets,
  } = useSessionExtras(sessions, sessionIds[0]);

  const allFbKeys = Array.from(
    new Set([...aggregateFeedbackKeys, ...(expectedFeedbackKeys ?? [])])
  );

  const sessionsData = sessions?.data?.rows;
  const sessionsIsLoading = sessions?.isLoading;
  const sessionsIsValidating = sessions?.isValidating;

  const feedbackDelta = useDatasetFeedbackDelta(
    datasetId ?? null,
    (selectedBaselineSessionId || comparativeExperiment) &&
      regressionsFilterFeedbackKey
      ? {
          baseline_session_id: comparativeExperiment
            ? sessionIds[0]
            : selectedBaselineSessionId ?? '',
          comparison_session_ids: sessionIds.filter(
            (id) =>
              id !==
              (comparativeExperiment
                ? sessionIds[0]
                : selectedBaselineSessionId)
          ),
          comparative_experiment_id: comparativeExperiment ?? undefined,
          feedback_key: regressionsFilterFeedbackKey,
          filters,
        }
      : null,
    { datasetShareToken }
  );

  const mutateInfiniteExamples = useCallback(() => {
    mutateInfiniteExamplesSwr();
    feedbackDelta?.mutate();
  }, [mutateInfiniteExamplesSwr, feedbackDelta]);

  const [rowDetailLanguage, setRowDetailLanguage] = useLocalStorageState<
    CodeLanguageType | 'plaintext'
  >(ROW_DETAIL_PANE_LANGUAGE_LOCAL_STORAGE_KEY, 'plaintext');

  const navigate = useNavigate();

  const tableRef = useRef<HTMLDivElement>(null);

  const [tableHeight, _setTableHeight] = useState(0);
  const setTableHeight = useDebouncedCallback((height: number) => {
    _setTableHeight(height);
  }, 200);
  const boundingDivRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!tableRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setTableHeight(entry.contentRect.height - 3);
      }
    });

    resizeObserver.observe(tableRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    // Reset regressions filter when changing the baseline or feedback key
    setFilteringRegressionsOnSession?.(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedBaselineSessionId, regressionsFilterFeedbackKey]);

  const [columnWidthStorage, setColumnWidthStorage] = useLocalStorageState(
    `ls:${localStoragePrefix ? `${localStoragePrefix}:` : ''}${
      sessionIds.length > 1
        ? COMPARISON_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY
        : EXPERIMENT_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY
    }`,
    [] as number[]
  );

  const filterToImprovements = (improvements: boolean, sessionId?: string) => {
    setFilteringRegressionsOnSession?.(sessionId != null ? sessionId : null);
    setRegressionsFilteringToImprovements?.(improvements ?? false);
  };

  const baselineSession = sessionsData?.find(
    (s) => s.id === selectedBaselineSessionId
  );

  // If the user adds sessions from the "add" column, position them at the end of the table
  const columnsSorted = useSortedColumns(
    columns,
    selectedBaselineSessionId,
    addedSessions
  );

  const totalFiltersApplied = Object.values(filters).reduce(
    (acc, value) => acc + value.length,
    0
  );

  // Metadata available on load
  const datadogSimpleMetadata = {
    datasetId,
    datasetShareToken,
    organizationId,
    numSessions: columns.length,
    filters,
    totalFiltersApplied,
    numSessionsFilteredOn: Object.keys(filters).length,
  };

  const isRegressionTrackingUIEnabled =
    regressionsFilterFeedbackKey != null &&
    selectedBaselineSessionId != null &&
    !comparativeExperiment &&
    !!setRegressionsFilteringToImprovements &&
    !!setFilteringRegressionsOnSession;
  const isPairwiseUIEnabled =
    sessionIds.length === 2 &&
    !!comparativeExperiment &&
    !!regressionsFilterFeedbackKey;

  // Metadata available after data is loaded
  const datadogExpandedMetadata =
    sessionsIsLoading ||
    sessionsIsValidating ||
    !sessionsData ||
    selectedCharts === null
      ? null
      : {
          ...datadogSimpleMetadata,
          numRunLevelFeedbackKeys: aggregateFeedbackKeys.length,
          numSessionLevelFeedbackKeys:
            aggregateSessionLevelFeedbackKeys?.length,
          numChartsShown:
            columns.length > 1 ? selectedCharts?.length ?? 0 : null,
          chartsShown: selectedCharts,
          language,
          isRegressionTrackingUIEnabled,
        };
  usePageTracking([datadogPageName, datadogSimpleMetadata]);
  const logClick = useClickLogger([datadogPageName, datadogExpandedMetadata]);
  useInfoLogger([datadogPageName, datadogExpandedMetadata]);

  const setFiltersForSession = useCallback(
    (clauses: string[], sessionId: string) => {
      _setFiltersForSession(clauses, sessionId);
      logClick('session_filter_changed', {
        sessionId,
        newFilters: clauses,
        oldFilters: filters[sessionId],
        newFiltersCount: clauses.length,
        oldFiltersCount: filters[sessionId]?.length ?? 0,
      });
    },
    [_setFiltersForSession, filters, logClick]
  );

  const sidePanesControls = useSidePanes({
    setSearchParams,
    examples,
    logClick,
  });

  const {
    reset,
    resetExampleDetail,

    detailExample,
    toggleRowExampleDetail,
    rowDetailExampleId,

    exampleDetailInSidePaneOpen,
    exampleDetailInNestedPaneOpen,
    setExampleDetailInSidePaneOpen,
    setExampleDetailInNestedPaneOpen,

    detailSingleExampleId,
    setDetailSingleExampleId,

    nextExampleId,
    prevExampleId,
    currentExampleIndex,

    toggleRunDetail,
    openRunId,
    openTraceId,

    repetitionsSidePaneInfo,
    setRepetitionsSidePaneInfo,
  } = sidePanesControls;

  const exampleRuns = useMemo(
    () =>
      examples
        ?.map((example) => {
          const exampleRuns = example.runs;
          const sortedRuns = getSortedRepetitionRuns(
            exampleRuns,
            false,
            regressionsFilterFeedbackKey
          );
          return sortedRuns ?? [];
        })
        .flat() ?? [],
    [examples]
  );

  const openRunIndex = useMemo(
    () => exampleRuns?.findIndex((run) => run.trace_id === openTraceId),
    [exampleRuns, openTraceId]
  );

  // TODO: Remove once pagination is unnecessary in the future
  if (detailExample == null && rowDetailExampleId != null) {
    toggleRowExampleDetail(null);
  }

  const { getRunDiffs, storedDiffs } = useGetExperimentDiffs(language);

  const [openTracingProjectId, setOpenTracingProjectId] = useState<
    string | undefined
  >(undefined);
  const hasCurrentDatasetInfo = dataset.data && dataset.data.id === datasetId;

  const distinctKey = useMemo(() => {
    // Only calculate when we first get examples to avoid re-rendering with different
    // displayed values
    if (!examples?.length) return undefined;

    return findMostDistinctKeyByHeuristic(examples.map((e) => e.inputs));
  }, [(examples?.length ?? 0) > 0]);

  const handleNextRun = useMemo(
    () =>
      openRunIndex != null && openRunIndex < exampleRuns.length - 1
        ? () => {
            toggleRunDetail(
              exampleRuns[openRunIndex + 1].id,
              exampleRuns[openRunIndex + 1].trace_id
            );
            if (
              hasMoreToLoad &&
              (examples?.length ?? 0 > 0) &&
              openRunIndex != null &&
              openRunIndex >= exampleRuns.length - 10 &&
              !isNextPageValidating
            ) {
              setExamplesSize((size) => size + 1);
            }
          }
        : undefined,
    [
      openRunIndex,
      exampleRuns,
      toggleRunDetail,
      hasMoreToLoad,
      examples,
      isNextPageValidating,
      setExamplesSize,
    ]
  );

  const handlePreviousRun = useMemo(
    () =>
      openRunIndex != null && openRunIndex > 0
        ? () => {
            toggleRunDetail(
              exampleRuns[openRunIndex - 1].id,
              exampleRuns[openRunIndex - 1].trace_id
            );
          }
        : undefined,
    [openRunIndex, exampleRuns, toggleRunDetail]
  );

  if (dataset.isLoading && sessionsIsLoading && !hasCurrentDatasetInfo) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }

  const session = sessionsData?.find((s) => s.id === sessionIds[0]);
  const showFeedbackAndMetricsColumns =
    (sessionIds.length === 1 && columnIds.length === 0) ||
    (sessionIds.length === 0 && columnIds.length === 1);

  return (
    <LogPageContext.Provider value={[datadogPageName, datadogExpandedMetadata]}>
      <div
        className={cn('sticky top-0 flex-1', className)}
        ref={boundingDivRef}
      >
        {areExamplesLoading && (
          <div className="absolute inset-x-0 top-0 z-20">
            <LinearProgress />
          </div>
        )}
        <div className="h-full w-full" ref={tableRef}>
          <TableVirtuoso
            className="dataset-session-compare-table"
            useWindowScroll={useWindowScroll}
            context={{
              hasMoreToLoad,
              isNextPageValidating,
              setExamplesSize,
              examples,
            }}
            components={{
              Table: Table,
              // This is a workaround for https://github.com/petyosi/react-virtuoso/issues/609
              // If removed, and table is scrolled all the way to the right, downscroll will not work
              FillerRow: ({ height }) => {
                return (
                  <tr>
                    <td
                      colSpan={columnsSorted.length + 3}
                      style={{ height: height, padding: 0, border: 0 }}
                    />
                  </tr>
                );
              },
            }}
            data={examples ?? []}
            itemContent={(_, example) => {
              const displaySettingsStateWithAttachments = {
                ...displaySettingsState,
                isAttachmentsHidden,
              };

              return (
                <TableRow
                  example={example}
                  setExampleInputs={(inputs) => {
                    setExample?.(example.id, (prevExample) => ({
                      ...BLANK_EXAMPLE,
                      ...prevExample,
                      inputs,
                      edited: true,
                    }));
                  }}
                  setExampleOutputs={(outputs) => {
                    setExample?.(example.id, (prevExample) => ({
                      ...BLANK_EXAMPLE,
                      ...prevExample,
                      outputs,
                      edited: true,
                    }));
                  }}
                  displaySettingsState={displaySettingsStateWithAttachments}
                  sidePanesControls={sidePanesControls}
                  columnWidthStorage={columnWidthStorage}
                  dataset={dataset.data}
                  columnsSorted={columnsSorted}
                  columnIds={columnIds}
                  feedbackDelta={feedbackDelta?.data}
                  experimentInProgress={experimentInProgress}
                  expectedFeedbackKeys={expectedFeedbackKeys}
                  sessionIds={sessionIds}
                  isPairwiseUIEnabled={isPairwiseUIEnabled}
                  isRegressionTrackingUIEnabled={isRegressionTrackingUIEnabled}
                  regressionsFilterFeedbackKey={regressionsFilterFeedbackKey}
                  mutateInfiniteExamples={mutateInfiniteExamples}
                  comparativeExperiment={comparativeExperiment}
                  datasetShareToken={datasetShareToken}
                  aggregateFeedbackKeys={aggregateFeedbackKeys}
                  dataType={dataType}
                  isEditable={!!setExample && !!onDeleteExample}
                  heuristicCompactViewDisplayedKey={distinctKey}
                  onDelete={
                    onDeleteExample
                      ? () => onDeleteExample?.(example)
                      : undefined
                  }
                  isDeleteLoading={isDeleteLoading}
                  customExtraTableCell={
                    customExtraTableCell && customExtraTableCell(example)
                  }
                  sessionsData={sessionsData}
                />
              );
            }}
            fixedHeaderContent={() => (
              <tr>
                {!isReferenceInputHidden && (
                  <ExperimentColumnHeader
                    columnIndex={INPUT_COLUMN_IDX}
                    columnName="Inputs"
                    tableHeight={tableHeight}
                    columnWidthStorage={columnWidthStorage}
                    setColumnWidthStorage={setColumnWidthStorage}
                    className={cn(sessionIds.length === 1 && 'h-[45px]')}
                  >
                    <div
                      className={cn(
                        'h-full',
                        creatingNew && 'flex items-center'
                      )}
                    >
                      <div className="flex flex-row items-center gap-2">
                        Inputs
                        {creatingNew && (
                          <InfoTooltip
                            title="Create input variables to pass into the LLM call"
                            description="Enter an input key (e.g., “Question”) and its value (e.g., “What is the best Stanford dorm?”). The input key should match the placeholder variables in your prompt."
                          />
                        )}
                      </div>
                    </div>
                  </ExperimentColumnHeader>
                )}
                {!isAttachmentsHidden && (
                  <ExperimentColumnHeader
                    columnIndex={ATTACHMENTS_COLUMN_IDX}
                    columnName="Attachments"
                    tableHeight={tableHeight}
                    columnWidthStorage={columnWidthStorage}
                    setColumnWidthStorage={setColumnWidthStorage}
                    className={cn(sessionIds.length === 1 && 'h-[45px]')}
                  >
                    <div className="h-full">Attachments</div>
                  </ExperimentColumnHeader>
                )}
                {!isReferenceOutputHidden && (
                  <ExperimentColumnHeader
                    columnIndex={REFERENCE_COLUMN_IDX}
                    columnName="Reference Outputs"
                    tableHeight={tableHeight}
                    columnWidthStorage={columnWidthStorage}
                    setColumnWidthStorage={setColumnWidthStorage}
                    className={cn(sessionIds.length === 1 && 'h-[45px]')}
                  >
                    <div
                      className={cn(
                        'h-full',
                        creatingNew &&
                          'flex flex-row items-center justify-between'
                      )}
                    >
                      <div className="flex flex-row items-center gap-2">
                        Reference Outputs
                        {creatingNew && (
                          <InfoTooltip
                            title="Optionally, provide reference outputs"
                            description="Reference outputs are used in evaluators to compare against outputs from the LLM call."
                          />
                        )}
                      </div>
                      {creatingNew && (
                        <RemoveReferenceOutputButton
                          onRemoveReferenceOutput={onRemoveReferenceOutput}
                          setIsReferenceOutputHidden={
                            setIsReferenceOutputHidden
                          }
                        />
                      )}
                    </div>
                  </ExperimentColumnHeader>
                )}
                {columnsSorted.map((col, idx) => {
                  const sessionId = col['sessionId'];
                  const session = sessionId
                    ? sessionsData?.find((i) => i.id === sessionId)
                    : undefined;
                  const baseRunFacets = session?.run_facets
                    ? [...(session.run_facets ?? []), ...GENERIC_FACETS]
                    : [];
                  const runFacets =
                    sessionIds.length === 1
                      ? baseRunFacets.filter(
                          (facet) =>
                            ![
                              'feedback_key',
                              'feedback_key_score',
                              ...Object.values(METRICS_COLUMNS).map(
                                (metric) => metric.name
                              ),
                            ].includes(facet.key)
                        )
                      : baseRunFacets;
                  const reversed =
                    sessionId && sessionId === columnsSorted[0]['sessionId'];
                  const isFilteringToImprovementsPairwise = reversed
                    ? !regressionsFilteringToImprovements
                    : regressionsFilteringToImprovements;

                  const targetUrl = !sessionId
                    ? undefined
                    : datasetShareToken
                    ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${sessionId}/${appSessionPath}?test_run=true`
                    : `/${appOrganizationPath}/${organizationId}/${appProjectsPath}/${appSessionPath}/${sessionId}?test_run=true`;

                  if (showFeedbackAndMetricsColumns) {
                    return (
                      <ExperimentColumnHeader
                        columnIndex={idx + OUTPUT_START_COLUMN_IDX}
                        columnName={
                          typeof col['columnName'] === 'string'
                            ? col['columnName']
                            : session?.name ?? `compare-session-${idx}`
                        }
                        tableHeight={tableHeight}
                        minColumnWidth={128}
                        columnWidthStorage={columnWidthStorage}
                        setColumnWidthStorage={setColumnWidthStorage}
                        className="h-[45px]"
                      >
                        {sessionId && !!setFilterParamsState ? (
                          <div className="flex flex-col">
                            <span className="w-full truncate text-sm">
                              Outputs
                            </span>
                            <div className="flex items-center justify-end">
                              <DatasetSessionCompareFilter
                                label={undefined}
                                facets={runFacets}
                                currentFilters={filters[sessionId] ?? []}
                                onChange={(clauses) => {
                                  setFiltersForSession(clauses, sessionId);
                                }}
                                hideClearButton={!!(sessionIds.length === 1)}
                              />
                            </div>
                          </div>
                        ) : (
                          <div
                            className={cn(
                              'h-full',
                              creatingNew && 'flex items-center'
                            )}
                          >
                            Outputs
                          </div>
                        )}
                      </ExperimentColumnHeader>
                    );
                  }
                  return (
                    <ExperimentColumnHeader
                      columnIndex={idx + OUTPUT_START_COLUMN_IDX}
                      columnName={
                        typeof col['columnName'] === 'string'
                          ? col['columnName']
                          : session?.name ?? `compare-session-${idx}`
                      }
                      tableHeight={tableHeight}
                      columnWidthStorage={columnWidthStorage}
                      setColumnWidthStorage={setColumnWidthStorage}
                    >
                      <ComparisonOutputColumnHeader
                        col={col}
                        sessions={sessionsData}
                        session={session}
                        isPairwiseUIEnabled={isPairwiseUIEnabled}
                        feedbackDelta={feedbackDelta?.data}
                        regressionsFilterFeedbackKey={
                          regressionsFilterFeedbackKey ?? undefined
                        }
                        projectUrl={targetUrl}
                        setFilteringRegressionsOnSession={
                          setFilteringRegressionsOnSession
                        }
                        regressionsFilteringToImprovements={
                          regressionsFilteringToImprovements
                        }
                        isFilteringToImprovementsPairwise={
                          isFilteringToImprovementsPairwise
                        }
                        isReversed={reversed}
                        setOpenTracingProjectId={setOpenTracingProjectId}
                        filters={filters}
                        setFiltersForSession={setFiltersForSession}
                        runFacets={runFacets}
                        isRegressionTrackingUIEnabled={
                          isRegressionTrackingUIEnabled
                        }
                        selectedBaselineSessionId={
                          selectedBaselineSessionId ?? undefined
                        }
                        baselineSession={baselineSession}
                        filteringRegressionsOnSession={
                          filteringRegressionsOnSession
                        }
                        filterToImprovements={filterToImprovements}
                        setFilterParamsState={setFilterParamsState}
                      />
                    </ExperimentColumnHeader>
                  );
                })}
                {showFeedbackAndMetricsColumns && (
                  <FeedbackColumnHeaders
                    sessionId={sessionIds[0]}
                    startingIndex={OUTPUT_START_COLUMN_IDX + 1}
                    aggregateFeedbackKeys={allFbKeys}
                    hiddenFeedbackColumns={hiddenFeedbackColumns}
                    expectedFeedbackKeys={expectedFeedbackKeys}
                    tableHeight={tableHeight}
                    columnWidthStorage={columnWidthStorage}
                    setColumnWidthStorage={setColumnWidthStorage}
                    runFacets={feedbackRunFacets}
                    currentFilters={filters[sessionIds[0]] ?? []}
                    onChange={
                      inPlayground
                        ? undefined
                        : (clauses) => {
                            setFiltersForSession(clauses, sessionIds[0]);
                          }
                    }
                    canSort={!inPlayground}
                  />
                )}
                {showFeedbackAndMetricsColumns && (
                  <MetricsColumnHeaders
                    session={session}
                    isSessionValidating={sessionsIsValidating}
                    startingIndex={
                      OUTPUT_START_COLUMN_IDX +
                      aggregateFeedbackKeys.filter(
                        (key) => !hiddenFeedbackColumns.includes(key)
                      ).length +
                      1
                    }
                    tableHeight={tableHeight}
                    columnWidthStorage={columnWidthStorage}
                    setColumnWidthStorage={setColumnWidthStorage}
                    hiddenMetricsColumns={hiddenMetricsColumns}
                    runFacets={metricsRunFacets}
                    currentFilters={filters[sessionIds[0]] ?? []}
                    onChange={
                      inPlayground
                        ? undefined
                        : (clauses) => {
                            setFiltersForSession(clauses, sessionIds[0]);
                          }
                    }
                  />
                )}
                {customExtraHeaderColumn}

                {/* Adds an empty column on the right of the table to fill blank space and allow for column resizing */}
                {onDeleteExample && (
                  <th className="sticky top-0 w-full whitespace-nowrap border-y border-secondary bg-secondary text-left text-sm font-semibold capitalize"></th>
                )}
                <th className="sticky top-0 w-full whitespace-nowrap border-y border-secondary bg-secondary text-left text-sm font-semibold capitalize"></th>
              </tr>
            )}
          />
        </div>
      </div>

      <TracingProjectPreview
        isOpen={!!openTracingProjectId}
        onClose={() => setOpenTracingProjectId(undefined)}
        sessionId={openTracingProjectId}
        tracingProjectTitle={
          sessionsData?.find((s) => s.id === openTracingProjectId)?.name ??
          'Comparing experiments'
        }
      />

      <ExperimentRunDetails
        onNext={handleNextRun}
        onPrevious={handlePreviousRun}
        runId={openRunId}
        setOpenRunId={toggleRunDetail}
        datasetShareToken={datasetShareToken}
      />

      <SplitViewPane
        open={!!repetitionsSidePaneInfo}
        sidePaneId="repetitions"
        onClose={() => setRepetitionsSidePaneInfo(null, null, null)}
        title={
          <DetailPaneHeader
            dataset={dataset.data}
            rowDetailLanguage={rowDetailLanguage}
            setRowDetailLanguage={setRowDetailLanguage}
            setExampleDetailInSidePaneOpen={setExampleDetailInSidePaneOpen}
            isShowingDiff={textDisplayMode === TextDisplayMode.DIFF}
            setIsShowingDiff={() => setTextDisplayMode(TextDisplayMode.DIFF)}
          >
            <span className="mx-3 line-clamp-1 whitespace-normal break-words break-all">
              {sessionsData?.find(
                (s) => s.id === repetitionsSidePaneInfo?.session
              )?.name ?? ''}
            </span>
          </DetailPaneHeader>
        }
        className="relative"
      >
        {detailExample && (
          <DatasetSessionCompareRepetitionsDetailPane
            detailExample={detailExample}
            datasetShareToken={datasetShareToken}
            sessions={sessionsData}
            columns={columns}
            columnId={repetitionsSidePaneInfo?.session}
            selectedFeedbackKey={regressionsFilterFeedbackKey}
            dataset={dataset.data}
            language={rowDetailLanguage}
            mutateInfiniteExamples={() => mutateInfiniteExamples()}
            exampleDetailPaneOpen={exampleDetailInSidePaneOpen}
            setExampleDetailPaneOpen={setExampleDetailInSidePaneOpen}
            diffs={
              textDisplayMode === TextDisplayMode.DIFF
                ? storedDiffs[detailExample.id]
                : undefined
            }
            getRunDiffs={getRunDiffs}
          />
        )}
      </SplitViewPane>
      <SplitViewPane
        open={!!rowDetailExampleId}
        sidePaneId="datasetCompare"
        onClose={() => {
          reset();
          resetExampleDetail();
        }}
        title={
          <DetailPaneHeader
            dataset={dataset.data}
            rowDetailLanguage={rowDetailLanguage}
            setRowDetailLanguage={setRowDetailLanguage}
            setExampleDetailInSidePaneOpen={setExampleDetailInSidePaneOpen}
            isShowingDiff={textDisplayMode === TextDisplayMode.DIFF}
            setIsShowingDiff={(diffMode) =>
              setTextDisplayMode(
                diffMode ? TextDisplayMode.DIFF : TextDisplayMode.FULL
              )
            }
          >
            <span className="mx-3 line-clamp-1 whitespace-normal break-words break-all">
              {comparativeExperiment
                ? 'Viewing Pairwise Experiment'
                : pageTitle}
              : Example {detailExample && detailExample.name?.split(' @')[0]}
            </span>
          </DetailPaneHeader>
        }
        onNext={() => {
          if (nextExampleId != null) {
            toggleRowExampleDetail(nextExampleId);
          }
          if (
            hasMoreToLoad &&
            (examples?.length ?? 0 > 0) &&
            currentExampleIndex != null &&
            currentExampleIndex >= examples!.length - 10 &&
            !isNextPageValidating
          ) {
            setExamplesSize((size) => size + 1);
          }
        }}
        onPrevious={
          prevExampleId != null
            ? () => {
                toggleRowExampleDetail(prevExampleId);
              }
            : undefined
        }
        className="relative"
      >
        {detailExample && (
          <DatasetSessionCompareRowDetailPane
            detailExample={detailExample}
            datasetShareToken={datasetShareToken}
            columnsSorted={columnsSorted}
            sessions={sessionsData}
            dataset={dataset.data}
            language={rowDetailLanguage}
            setLanguage={setRowDetailLanguage}
            mutateInfiniteExamples={() => mutateInfiniteExamples()}
            exampleDetailPaneOpen={exampleDetailInSidePaneOpen}
            setExampleDetailPaneOpen={setExampleDetailInSidePaneOpen}
            exampleDetailInNestedPaneOpen={exampleDetailInNestedPaneOpen}
            setExampleDetailInNestedPaneOpen={setExampleDetailInNestedPaneOpen}
            pairwiseFeedbackKey={
              isPairwiseUIEnabled ? regressionsFilterFeedbackKey : undefined
            }
            selectedFeedbackKey={regressionsFilterFeedbackKey}
            feedbackDelta={feedbackDelta?.data}
            pairwiseNonBaseline={sessionIds[1]}
            diffs={
              textDisplayMode === TextDisplayMode.DIFF
                ? storedDiffs[detailExample.id]
                : undefined
            }
            getRunDiffs={getRunDiffs}
          />
        )}
      </SplitViewPane>

      <SplitViewPane
        open={!!detailSingleExampleId}
        sidePaneId="datasetSessionCompareExample"
        onClose={() => {
          setDetailSingleExampleId(null);
        }}
        onExpand={() => {
          if (detailSingleExampleId) {
            const targetUrl = datasetShareToken
              ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${detailSingleExampleId}/${appExamplePath}`
              : `/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${datasetId}/${appExamplePath}/${detailSingleExampleId}`;
            navigate(targetUrl);
          }
        }}
        title={null}
        onNext={
          nextExampleId != null
            ? () => {
                setDetailSingleExampleId(nextExampleId);
              }
            : undefined
        }
        onPrevious={
          prevExampleId != null
            ? () => {
                setDetailSingleExampleId(prevExampleId);
              }
            : undefined
        }
        className="relative"
      >
        <div className="absolute inset-0 flex flex-grow flex-col gap-4 overflow-auto p-4">
          <Example
            exampleId={detailSingleExampleId}
            datasetId={datasetId}
            onMutate={() => mutateInfiniteExamples()}
          />
        </div>
      </SplitViewPane>
    </LogPageContext.Provider>
  );
};
