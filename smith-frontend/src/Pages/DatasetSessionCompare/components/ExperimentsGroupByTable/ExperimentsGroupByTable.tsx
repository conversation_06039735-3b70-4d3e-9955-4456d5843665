import LinearProgress from '@mui/joy/LinearProgress';

import { useCallback, useMemo, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Virtuoso } from 'react-virtuoso';

import { Example } from '@/Pages/Example';
import { CodeLanguageType } from '@/components/Code/types';
import { ExpandableErrorAlert } from '@/components/ExpandableErrorAlert';
import { LoadMoreButton } from '@/components/RunsTable/LoadMoreButton';
import { SplitViewPane } from '@/components/SplitViewPane';
import { useDataset, useOrganizationId } from '@/hooks/useSwr';
import {
  ExampleSchemaWithRunsAndOptionalFields,
  ExamplesGroup,
} from '@/types/schema';
import {
  appDatasetsPath,
  appExamplePath,
  appOrganizationPath,
  appPublicDatasetsPath,
  appPublicPath,
} from '@/utils/constants';
import { useClickLogger } from '@/utils/datadog/useClickLogger';
import { cn } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { DetailPaneHeader } from '../../DetailPaneHeader';
import {
  COMPARISON_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY,
  EXPERIMENT_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY,
  ROW_DETAIL_PANE_LANGUAGE_LOCAL_STORAGE_KEY,
  TextDisplayMode,
} from '../../constants';
import { anyExampleHasAttachments } from '../../utils/anyExampleHasAttachments';
import { useGetExperimentDiffs } from '../../utils/useGetExperimentDiffs';
import { DatasetSessionCompareRepetitionsDetailPane } from '../DatasetSessionCompareRepetitionsDetailPane';
import { DatasetSessionCompareRowDetailPane } from '../DatasetSessionCompareRowDetailPane/DatasetSessionCompareRowDetailPane';
import { ExperimentRunDetails } from '../ExperimentRunDetails';
import {
  TableRow,
  TableRowProps,
} from '../SingleExperimentComponents/TableRow';
import { useSessionExtras } from '../hooks/useSessionExtras';
import { useSidePanes } from '../hooks/useSidePanes';
import { useSortedColumns } from '../hooks/useSortedColumns';
import { useTableHeight } from '../hooks/useTableHeight';
import { ExperimentGroupByHeader } from './ExperimentGroupByHeader';
import {
  SingleGroupHeaderWithColumns,
  SingleGroupLoadButton,
} from './SingleGroupWrapper';
import type { ExperimentGroupByTableProps } from './types';
import { useGroupByExamples } from './useGroupByExamples';
import { useTableWidthStore } from './useTableFullWidth';

export const ExperimentsGroupByTable = ({
  datasetId,
  datasetShareToken,
  columns,
  sessions,
  selectedBaselineSessionId,
  displaySettingsState,
  expectedFeedbackKeys,
  experimentInProgress,
  localStoragePrefix,
  addedSessions,
  className,
}: ExperimentGroupByTableProps) => {
  const logClick = useClickLogger();

  // Display settings and UI state
  const {
    isAttachmentsHidden: _isAttachmentsHidden,
    language,
    textDisplayMode,
    setTextDisplayMode,
  } = displaySettingsState;

  const isShowingDiff = textDisplayMode === TextDisplayMode.DIFF;
  const setIsShowingDiff = useCallback(
    (isShowingDiff: boolean) => {
      setTextDisplayMode(
        isShowingDiff ? TextDisplayMode.DIFF : TextDisplayMode.FULL
      );
    },
    [setTextDisplayMode]
  );

  // Core data fetching and state
  const organizationId = useOrganizationId();
  const [_, setSearchParams] = useSearchParams();
  const dataset = useDataset(datasetId, { datasetShareToken });
  const sessionsData = sessions.data?.rows;
  const sessionsIsLoading = sessions.isLoading;
  const hasCurrentDatasetInfo = dataset.data && dataset.data.id === datasetId;

  // Session and column management
  const sessionIds: string[] = useMemo(
    () => columns.map((col) => col['sessionId']).filter(Boolean),
    [columns]
  );

  const columnIds: string[] = useMemo(
    () => columns.map((col) => col['columnId']).filter(Boolean),
    [columns]
  );

  const columnsSorted = useSortedColumns(
    columns,
    selectedBaselineSessionId,
    addedSessions
  );
  const [columnWidthStorage, setColumnWidthStorage] = useLocalStorageState(
    `ls:${localStoragePrefix ? `${localStoragePrefix}:` : ''}${
      sessionIds.length > 1
        ? COMPARISON_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY
        : EXPERIMENT_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY
    }`,
    [] as number[]
  );

  const {
    groups,
    flatExamples,
    hasMoreToLoad,
    isNextPageValidating,
    areGroupsLoading,
    setGroupsSize,
    groupsError,
    boundSWRMutates,
  } = useGroupByExamples(datasetId, sessionIds, datasetShareToken);

  // Derived states and filters
  const isAttachmentsHidden = useMemo(() => {
    return _isAttachmentsHidden || !anyExampleHasAttachments(flatExamples);
  }, [_isAttachmentsHidden, flatExamples]);
  const displaySettingsStateWithAttachments = {
    ...displaySettingsState,
    isAttachmentsHidden,
  };

  // Session metadata and facets
  const sessionExtras = useSessionExtras(sessions, sessionIds[0]);
  const { aggregateFeedbackKeys } = sessionExtras;

  // UI refs and measurements
  const boundingDivRef = useRef<HTMLDivElement>(null);

  // Side pane and detail view state management
  const navigate = useNavigate();
  const { getRunDiffs, storedDiffs } = useGetExperimentDiffs(language);
  const [rowDetailLanguage, setRowDetailLanguage] = useLocalStorageState<
    CodeLanguageType | 'plaintext'
  >(ROW_DETAIL_PANE_LANGUAGE_LOCAL_STORAGE_KEY, 'plaintext');

  const sidePanesControls = useSidePanes({
    setSearchParams,
    examples: flatExamples ?? [],
    logClick,
  });

  const {
    // Detail view controls
    reset,
    resetExampleDetail,
    detailExample,
    toggleRowExampleDetail,
    rowDetailExampleId,

    // Side pane states
    exampleDetailInSidePaneOpen,
    exampleDetailInNestedPaneOpen,
    setExampleDetailInSidePaneOpen,
    setExampleDetailInNestedPaneOpen,

    // Single example view controls
    detailSingleExampleId,
    setDetailSingleExampleId,
    nextExampleId,
    prevExampleId,

    // Run details
    toggleRunDetail,
    openRunId,

    // Repetitions pane
    repetitionsSidePaneInfo,
    setRepetitionsSidePaneInfo,
  } = sidePanesControls;

  const { tableHeight, tableRef } = useTableHeight({
    columnsCount: columnsSorted.length,
    examplesCount: flatExamples?.length,
    isTextTruncated: false,
    hideMetrics: false,
  });

  const activeGroupKey = groups?.find((g) =>
    g.examples.some((e) => e.id === detailExample?.id)
  )?.group_key;
  const exampleMutate = boundSWRMutates[activeGroupKey ?? ''];

  const scrollerRef = useRef<HTMLElement | Window | null>(null);

  if (dataset.isLoading && sessionsIsLoading && !hasCurrentDatasetInfo) {
    return (
      <div>
        <LinearProgress />
      </div>
    );
  }

  if (groupsError) {
    return (
      <div className={cn('p-4', className)} ref={boundingDivRef}>
        <ExpandableErrorAlert
          error={`Error fetching groups: ${groupsError.message}`}
        />
      </div>
    );
  }

  return (
    <>
      <div
        className={cn('sticky top-0 flex-1', className)}
        ref={boundingDivRef}
      >
        {areGroupsLoading && (
          <div className="absolute inset-x-0 top-0 z-20">
            <LinearProgress />
          </div>
        )}
        <div className="h-full w-full text-sm" ref={tableRef}>
          <Virtuoso
            scrollerRef={(ref) => {
              scrollerRef.current = ref;
            }}
            style={{ height: '100%' }}
            context={{
              hasMoreToLoad,
              isNextPageValidating,
              setGroupsSize,
              datasetId,
              groups,
              sessionId: sessionIds[0],
            }}
            topItemCount={1}
            data={[{}, ...(flatExamples ?? [])]} // Add an empty object to the beginning of the list since this is the header
            itemContent={(_index) => {
              const index = _index - 1;
              if (_index === 0) {
                return (
                  <ExperimentGroupByHeader
                    displaySettingsState={displaySettingsStateWithAttachments}
                    columnWidthStorage={columnWidthStorage}
                    setColumnWidthStorage={setColumnWidthStorage}
                    sessionIds={sessionIds}
                    sessionExtras={sessionExtras}
                    columnsSorted={columnsSorted}
                    tableHeight={tableHeight}
                  />
                );
              }

              const example = flatExamples?.[index];
              if (!example) return null;

              const group = groups?.[example.groupIndex];
              if (!group) return null;

              return (
                <ItemContent
                  example={example}
                  group={group}
                  datasetId={datasetId}
                  sessionIds={sessionIds}
                  columnIds={columnIds}
                  displaySettingsState={displaySettingsStateWithAttachments}
                  sidePanesControls={sidePanesControls}
                  columnWidthStorage={columnWidthStorage}
                  dataset={dataset.data}
                  columnsSorted={columnsSorted}
                  feedbackDelta={undefined}
                  experimentInProgress={experimentInProgress}
                  expectedFeedbackKeys={expectedFeedbackKeys}
                  isPairwiseUIEnabled={false}
                  isRegressionTrackingUIEnabled={false}
                  regressionsFilterFeedbackKey={undefined}
                  mutateInfiniteExamples={() => {}}
                  comparativeExperiment={null}
                  datasetShareToken={datasetShareToken}
                  aggregateFeedbackKeys={aggregateFeedbackKeys}
                  sessionExtras={sessionExtras}
                />
              );
            }}
            components={{
              Footer: () => (
                <TableFooter
                  hasMoreToLoad={hasMoreToLoad}
                  isNextPageValidating={isNextPageValidating}
                  setGroupsSize={setGroupsSize}
                  groups={groups ?? []}
                />
              ),
            }}
          />
        </div>
      </div>

      <ExperimentRunDetails
        runId={openRunId}
        setOpenRunId={toggleRunDetail}
        datasetShareToken={datasetShareToken}
      />

      <SplitViewPane
        open={!!repetitionsSidePaneInfo}
        sidePaneId="repetitions"
        onClose={() => setRepetitionsSidePaneInfo(null, null, null)}
        title={
          <DetailPaneHeader
            dataset={dataset.data}
            rowDetailLanguage={rowDetailLanguage}
            setRowDetailLanguage={setRowDetailLanguage}
            setExampleDetailInSidePaneOpen={setExampleDetailInSidePaneOpen}
            isShowingDiff={isShowingDiff}
            setIsShowingDiff={setIsShowingDiff}
          >
            <span className="mx-3 line-clamp-1 whitespace-normal break-words break-all">
              {sessionsData?.[0]?.name}
            </span>
          </DetailPaneHeader>
        }
        className="relative"
      >
        {detailExample && (
          <DatasetSessionCompareRepetitionsDetailPane
            detailExample={detailExample}
            datasetShareToken={datasetShareToken}
            sessions={sessionsData}
            columns={columns}
            columnId={repetitionsSidePaneInfo?.session}
            selectedFeedbackKey={null}
            dataset={dataset.data}
            language={rowDetailLanguage}
            mutateInfiniteExamples={() => {
              exampleMutate?.();
            }}
            exampleDetailPaneOpen={exampleDetailInSidePaneOpen}
            setExampleDetailPaneOpen={setExampleDetailInSidePaneOpen}
            diffs={isShowingDiff ? storedDiffs[detailExample.id] : undefined}
            getRunDiffs={getRunDiffs}
          />
        )}
      </SplitViewPane>

      <SplitViewPane
        open={!!rowDetailExampleId}
        sidePaneId="datasetCompare"
        onClose={() => {
          reset();
          resetExampleDetail();
        }}
        title={
          <DetailPaneHeader
            dataset={dataset.data}
            rowDetailLanguage={rowDetailLanguage}
            setRowDetailLanguage={setRowDetailLanguage}
            setExampleDetailInSidePaneOpen={setExampleDetailInSidePaneOpen}
            isShowingDiff={isShowingDiff}
            setIsShowingDiff={setIsShowingDiff}
          >
            <span className="mx-3 line-clamp-1 whitespace-normal break-words break-all">
              Example {detailExample && detailExample.name?.split(' @')[0]}
            </span>
          </DetailPaneHeader>
        }
        onNext={
          nextExampleId != null
            ? () => {
                toggleRowExampleDetail(nextExampleId);
              }
            : undefined
        }
        onPrevious={
          prevExampleId != null
            ? () => {
                toggleRowExampleDetail(prevExampleId);
              }
            : undefined
        }
        className="relative"
      >
        {detailExample && (
          <DatasetSessionCompareRowDetailPane
            detailExample={detailExample}
            datasetShareToken={datasetShareToken}
            columnsSorted={columns}
            sessions={sessionsData}
            dataset={dataset.data}
            language={rowDetailLanguage}
            setLanguage={setRowDetailLanguage}
            mutateInfiniteExamples={() => {
              exampleMutate?.();
            }}
            exampleDetailPaneOpen={exampleDetailInSidePaneOpen}
            setExampleDetailPaneOpen={setExampleDetailInSidePaneOpen}
            exampleDetailInNestedPaneOpen={exampleDetailInNestedPaneOpen}
            setExampleDetailInNestedPaneOpen={setExampleDetailInNestedPaneOpen}
            pairwiseFeedbackKey={undefined}
            selectedFeedbackKey={null}
            feedbackDelta={undefined}
            pairwiseNonBaseline={sessionIds[1]}
            diffs={isShowingDiff ? storedDiffs[detailExample.id] : undefined}
            getRunDiffs={getRunDiffs}
          />
        )}
      </SplitViewPane>

      <SplitViewPane
        open={!!detailSingleExampleId}
        sidePaneId="datasetSessionCompareExample"
        onClose={() => {
          setDetailSingleExampleId(null);
        }}
        onExpand={() => {
          if (detailSingleExampleId) {
            const targetUrl = datasetShareToken
              ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${detailSingleExampleId}/${appExamplePath}`
              : `/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${dataset.data?.id}/${appExamplePath}/${detailSingleExampleId}`;
            navigate(targetUrl);
          }
        }}
        title={null}
        onNext={
          nextExampleId != null
            ? () => {
                setDetailSingleExampleId(nextExampleId);
              }
            : undefined
        }
        onPrevious={
          prevExampleId != null
            ? () => {
                setDetailSingleExampleId(prevExampleId);
              }
            : undefined
        }
        className="relative"
      >
        <div className="absolute inset-0 flex flex-grow flex-col gap-4 overflow-auto p-4">
          <Example
            exampleId={detailSingleExampleId}
            datasetId={dataset.data?.id}
            onMutate={() => {
              exampleMutate?.();
            }}
          />
        </div>
      </SplitViewPane>
    </>
  );
};

export const ItemContent = ({
  example,
  datasetId,
  sessionIds,
  datasetShareToken,
  sessionExtras,
  group,
  ...rest
}: Omit<TableRowProps, 'example' | 'idx' | 'isEditable'> & {
  datasetId?: string;
  sessionIds?: string[];
  datasetShareToken?: string;
  example: ExampleSchemaWithRunsAndOptionalFields & {
    isFirstExampleInGroup: boolean;
    isLastExampleInGroup: boolean;
    isGroupCollapsed: boolean;
    groupIndex: number;
  };
  group: ExamplesGroup;
  sessionExtras: ReturnType<typeof useSessionExtras>;
}) => {
  if (!example) return null;

  return (
    <>
      {example.isFirstExampleInGroup && (
        <SingleGroupHeaderWithColumns
          displaySettingsState={rest.displaySettingsState}
          columns={rest.columnsSorted}
          columnWidthStorage={rest.columnWidthStorage}
          setColumnWidthStorage={() => {}}
          sessionIds={sessionIds}
          sessionExtras={sessionExtras}
          group={group}
          context={{
            datasetId,
            sessionId: sessionIds[0],
            datasetShareToken,
          }}
        />
      )}
      {example.isGroupCollapsed ? null : (
        <TableRow
          {...rest}
          example={example}
          sessionIds={sessionIds}
          feedbackDelta={undefined}
          isPairwiseUIEnabled={false}
          isRegressionTrackingUIEnabled={false}
          regressionsFilterFeedbackKey={undefined}
          mutateInfiniteExamples={() => {}}
          comparativeExperiment={null}
          isEditable={false}
        />
      )}
      {example.isLastExampleInGroup && !example.isGroupCollapsed && (
        <SingleGroupLoadButton
          context={{
            group,
            datasetId,
            sessionId: sessionIds[0],
            datasetShareToken,
          }}
        />
      )}
    </>
  );
};

export const TableFooter = ({
  hasMoreToLoad,
  isNextPageValidating,
  setGroupsSize,
  groups,
}: {
  hasMoreToLoad: boolean;
  isNextPageValidating: boolean;
  setGroupsSize: ReturnType<typeof useGroupByExamples>['setGroupsSize'];
  groups: ExamplesGroup[];
}) => {
  const { fullWidth } = useTableWidthStore((state) => state);

  return (
    hasMoreToLoad &&
    groups &&
    groups.length > 0 && (
      <div
        className="relative z-10 flex flex-col items-center border-secondary bg-background"
        style={{ width: fullWidth ?? '100%' }}
      >
        <LoadMoreButton
          isInitialLoad={false}
          isLoading={isNextPageValidating}
          onClick={() =>
            setGroupsSize((size) => {
              return size + 1;
            })
          }
          loadMoreText="Load more groups"
          className="mt-0 w-full p-4 hover:bg-secondary"
        />
      </div>
    )
  );
};
