import { useMemo } from 'react';

import { RunSchema } from '@/types/schema';

import { MAX_CHIPS } from '../constants';
import { usePollSessionForFeedback } from '../utils/usePollSession';
import { AggregateFeedbackChips } from './AggregateFeedbackChips';

export function AggregateFeedbackChipsWithExpandButton(props: {
  sessionId?: string;
  runs?: RunSchema[];
  allowTruncation?: boolean;
  showNotes?: boolean;
  sortMode?: 'key' | 'n';
  comparativeExperimentId?: string | null;
  iconType?: 'session' | 'run';
  className?: string;
  disablePopover?: boolean;
  expanded: boolean;
  setExpanded: (expanded: boolean | ((prev) => boolean)) => void;
  hiddenFeedbackKeys: string[];
  onMutate?: () => void;
  maxNumberOfCategories?: number;
  expectedFeedbackKeys?: string[];
  maxChips?: number;
  isNested?: boolean;
  onViewEvaluatorRun?: (runId: string) => void;
  firstFeedbackKey?: string;
  showErrorCount?: boolean;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) {
  const { session, sessionFeedbackLoadStates, isSessionFinished } =
    usePollSessionForFeedback(props.sessionId, props.expectedFeedbackKeys);
  const maxChips = props.maxChips ?? MAX_CHIPS;

  const allFeedbackKeys = useMemo(() => {
    if (props.runs) {
      return new Set(
        props.runs
          .flatMap((run) => Object.keys(run.feedback_stats ?? {}))
          .concat(props.expectedFeedbackKeys ?? [])
          .filter((key) => !props.hiddenFeedbackKeys.includes(key))
      );
    }
    // if we have session feedback stats or the session is finished, don't show the expected feedback keys
    if (session?.feedback_stats || isSessionFinished) {
      return new Set(
        Object.keys(session?.feedback_stats ?? {}).filter(
          (key) => !props.hiddenFeedbackKeys.includes(key)
        )
      );
    }
    return new Set();
  }, [
    session,
    props.runs,
    props.expectedFeedbackKeys,
    isSessionFinished,
    props.hiddenFeedbackKeys,
  ]);

  const numFeedbackKeys = useMemo(() => {
    const allFeedbackKeysIncludingExpected = new Set(
      Array.from(allFeedbackKeys)
    );
    props.expectedFeedbackKeys?.forEach((key) =>
      allFeedbackKeysIncludingExpected.add(key)
    );
    return allFeedbackKeysIncludingExpected.size;
  }, [allFeedbackKeys, props.expectedFeedbackKeys]);

  const chips = (
    <AggregateFeedbackChips
      {...props}
      session={session}
      sessionFeedbackLoadStates={sessionFeedbackLoadStates}
      isSessionFinished={isSessionFinished}
      maxChips={props.expanded ? undefined : maxChips}
      onViewEvaluatorRun={props.onViewEvaluatorRun}
      showErrorCount={props.showErrorCount}
    />
  );

  const content = (
    <>
      {chips}
      {numFeedbackKeys > maxChips && (
        <button
          type="button"
          className="bg-none text-tertiary underline"
          onClick={(e) => {
            props.setExpanded((prev) => !prev);
            e.stopPropagation();
          }}
        >
          {props.expanded
            ? 'Show less'
            : `Show ${numFeedbackKeys - maxChips} more...`}
        </button>
      )}
    </>
  );

  return props.isNested ? (
    content
  ) : (
    <div className="flex flex-wrap items-center gap-2">{content}</div>
  );
}
