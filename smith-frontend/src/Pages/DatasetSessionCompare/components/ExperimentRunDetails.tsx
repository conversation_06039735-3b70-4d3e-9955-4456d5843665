import { useNavigate } from 'react-router-dom';

import { Run } from '@/Pages/Run';
import { emulateNativeClick } from '@/components/DataGrid.utils';
import { SplitViewPane } from '@/components/SplitViewPane';
import { useRun } from '@/hooks/useSwr';
import {
  appPublicDatasetsPath,
  appPublicPath,
  appRunPath,
  appSessionPath,
} from '@/utils/constants';

export const ExperimentRunDetails = ({
  runId,
  setOpenRunId,
  datasetShareToken,
  onNext,
  onPrevious,
}: {
  runId: string | null;
  setOpenRunId: (runId: string | null, traceId?: string | null) => void;
  datasetShareToken?: string;
  onNext?: () => void;
  onPrevious?: () => void;
}) => {
  const run = useRun({
    id: runId,
    shareToken: { datasetShareToken },
    withContents: false,
    excludeSerialized: true,
  });

  const navigate = useNavigate();

  return (
    <SplitViewPane
      open={!!runId}
      sidePaneId="experimentRunDetails"
      onClose={() => setOpenRunId(null, null)}
      onNext={onNext}
      onPrevious={onPrevious}
      className="relative"
      title={''}
      onExpand={(e) => {
        if (run.data) {
          const targetUrl = datasetShareToken
            ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${run.data.session_id}/${appSessionPath}/${appRunPath}/${run.data.id}?trace_id=${run.data.trace_id}&start_time=${run.data.start_time}`
            : run.data?.app_path ?? '';

          if (!emulateNativeClick(targetUrl, e.nativeEvent)) {
            navigate(targetUrl);
          }
        }
      }}
    >
      <div className="absolute inset-0 flex flex-grow flex-col overflow-auto px-4">
        <Run
          runId={runId}
          navigateToRun={(runId, traceId) => {
            setOpenRunId(runId, traceId);
          }}
          shareTokenProp={{ datasetShareToken }}
        />
      </div>
    </SplitViewPane>
  );
};
