import { useEffect, useMemo, useState } from 'react';

import { emulateNativeClick } from '@/components/DataGrid.utils';
import { SingleFeedbackChipWithSourceRunAction } from '@/components/FeedbackChips';
import { useColorScheme } from '@/hooks/useColorScheme';
import {
  ComparisonViewColumn,
  DatasetDataType,
  ExampleSchemaWithRunsAndOptionalFields,
  SessionFeedbackDelta,
  SessionSchema,
} from '@/types/schema';
import {
  appPublicDatasetsPath,
  appPublicPath,
  appRunPath,
  appSessionPath,
} from '@/utils/constants';
import { useClickLogger } from '@/utils/datadog/useClickLogger';
import { cn } from '@/utils/tailwind';

import { CELL_PADDING } from '../DatasetSessionCompare.utils';
import { DatasetSessionCompareCellActions } from '../DatasetSessionCompareCellActions';
import { DatasetSessionCompareOutputs } from '../DatasetSessionCompareOutputs';
import {
  COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW,
  METRICS_COLUMNS,
  OUTPUT_CELL_PRE_RUN_PLACEHOLDER_TEXT,
} from '../constants';
import { TRunDiff } from '../types';
import { isInPlayground } from '../utils/isInPlayground';
import { TGetRunDiffArgs } from '../utils/useGetExperimentDiffs';
import { useIsRunFinished } from '../utils/useIsRunFinished';
import { useSortedRepetitionRuns } from '../utils/useSortedRepetitionRuns';
import {
  AggregateFeedbackChips,
  AggregateMetricsChips,
  MetricsChips,
} from './AggregateFeedbackChips';
import { AggregateFeedbackChipsWithExpandButton } from './AggregateFeedbackChipsWithExpandButton';
import { DatasetSessionCompareRunCellActions } from './DatasetSessionCompareRunCellActions';
import { FeedbackChipsWithExpandButton } from './FeedbackChipsWithExpandButton';
import { Loader } from './SingleExperimentColumns/Loader';

export const DatasetSessionCompareRunCell = ({
  example,
  column,
  isPairwiseUIEnabled,
  feedbackDelta,
  isRegressionTrackingUIEnabled,
  sessionIds,
  regressionsFilterFeedbackKey,
  isTextTruncated,
  hideFeedback,
  hideMetrics,
  diffs,
  getRunDiffs,
  columnIndex,
  backgroundStyles,
  language,
  datasetShareToken,
  comparativeExperiment,
  expanded,
  setExpanded,
  onOpenTraceDetailSidebar,
  onOpenDetailSidebar,
  mutateInfiniteExamples,
  style,
  className,
  dataType,
  expectedFeedbackKeys: _expectedFeedbackKeys,
  experimentInProgress,
  openDetailsOrRepetitionsPane,
  hiddenFeedbackKeys,
  hiddenMetrics,
  onViewEvaluatorRun,
  sessions,
}: {
  example: ExampleSchemaWithRunsAndOptionalFields;
  column: ComparisonViewColumn;
  isPairwiseUIEnabled: boolean;
  feedbackDelta?: SessionFeedbackDelta;
  isRegressionTrackingUIEnabled: boolean;
  sessionIds: string[];
  regressionsFilterFeedbackKey?: string | null;
  isTextTruncated: boolean;
  hideFeedback: boolean;
  hideMetrics: boolean;
  diffs: { [runId: string]: TRunDiff } | undefined;
  getRunDiffs: (args: TGetRunDiffArgs) => void;
  columnIndex: number;
  backgroundStyles: string;
  language: 'json' | 'yaml';
  datasetShareToken?: string;
  comparativeExperiment?: string;
  expanded: boolean;
  setExpanded: (expanded: boolean | ((prev) => boolean)) => void;
  onOpenTraceDetailSidebar?: (runId: string, traceId: string) => void;
  onOpenDetailSidebar: (exampleId: string) => void;
  mutateInfiniteExamples: () => void;
  style: React.CSSProperties;
  className?: string;
  dataType: DatasetDataType;
  expectedFeedbackKeys?: string[];
  experimentInProgress?: boolean;
  openDetailsOrRepetitionsPane: (
    example: ExampleSchemaWithRunsAndOptionalFields,
    runIndex: number,
    sessionId: string
  ) => void;
  hiddenFeedbackKeys: string[];
  hiddenMetrics: string[];
  onViewEvaluatorRun?: (runId: string) => void;
  sessions?: SessionSchema[];
}) => {
  const inPlayground = isInPlayground();
  const sessionId = column['sessionId'] || column['columnId'];
  const { isDarkMode } = useColorScheme();

  // Get the session object for this column
  const session = useMemo(() => {
    if (!sessions || !sessionId) return undefined;
    return sessions.find((s) => s.id === sessionId);
  }, [sessions, sessionId]);

  const runs = example.runs.filter((run) => run.session_id === sessionId);
  const isRunFinished = useIsRunFinished({
    runs,
    expectedFeedbackKeys: _expectedFeedbackKeys,
  });
  const logClick = useClickLogger();
  const handleTraceButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    logClick('open_trace_panel_button_main_table', {
      runId: run.id,
      sessionId: run.session_id,
    });

    const targetUrl = datasetShareToken
      ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${sessionId}/${appSessionPath}/${appRunPath}/${run.id}?trace_id=${run?.trace_id}&start_time=${run?.start_time}`
      : run.app_path;

    if (!emulateNativeClick(targetUrl, e.nativeEvent)) {
      onOpenTraceDetailSidebar?.(run.id, run.trace_id);
    }
  };

  const expectedFeedbackKeys = useMemo(
    () => (isRunFinished ? [] : _expectedFeedbackKeys),
    [isRunFinished, _expectedFeedbackKeys]
  );

  const sortedRuns = useSortedRepetitionRuns(
    runs,
    false,
    regressionsFilterFeedbackKey
  );

  const showRepetitionsUI = sortedRuns.length > 1;

  const [runInView, setRunInView] = useState(0);

  const run = sortedRuns[runInView];

  useEffect(() => {
    getRunDiffs({
      exampleId: example.id,
      runOutputs: run?.outputs,
      runOutputsPreview: run?.outputs_preview,
      exampleOutputs: example.outputs,
      runId: run?.id,
    });
  }, [run, getRunDiffs, example.id, example.outputs]);

  const reversed = sessionId === sessionIds[0];

  let isRegression =
    isRegressionTrackingUIEnabled &&
    feedbackDelta?.feedback_deltas?.[sessionId]?.regressed_examples?.includes(
      example.id
    );
  let isImprovement =
    isRegressionTrackingUIEnabled &&
    feedbackDelta?.feedback_deltas?.[sessionId]?.improved_examples?.includes(
      example.id
    );

  if (isPairwiseUIEnabled) {
    isRegression = feedbackDelta?.feedback_deltas?.[
      sessionIds[1]
    ]?.regressed_examples?.includes(example.id);
    isImprovement = feedbackDelta?.feedback_deltas?.[
      sessionIds[1]
    ]?.improved_examples?.includes(example.id);
    if (reversed) {
      const temp = isRegression;
      isRegression = isImprovement;
      isImprovement = temp;
    }
  }

  const regressionFeedbackKeyAvg = regressionsFilterFeedbackKey
    ? run?.feedback_stats?.[regressionsFilterFeedbackKey]?.avg
    : undefined;
  const regressionFeedbackKeyValues = regressionsFilterFeedbackKey
    ? run?.feedback_stats?.[regressionsFilterFeedbackKey]?.values
    : undefined;
  const regressionFeedbackKeyN = regressionsFilterFeedbackKey
    ? run?.feedback_stats?.[regressionsFilterFeedbackKey]?.n
    : undefined;
  const regressionFeedbackKeyErrors = regressionsFilterFeedbackKey
    ? run?.feedback_stats?.[regressionsFilterFeedbackKey]?.errors
    : undefined;

  const showSingleFeedbackChip =
    regressionsFilterFeedbackKey &&
    (run?.feedback_stats?.[regressionsFilterFeedbackKey] ||
      expectedFeedbackKeys?.includes(regressionsFilterFeedbackKey));
  const showFullFeedbackChips =
    !hideFeedback &&
    (Object.keys(run?.feedback_stats ?? {}).length > 0 ||
      (expectedFeedbackKeys ?? [])?.length > 0);

  const showExpandedSingleFeedbackChip =
    !isTextTruncated && hideFeedback && showSingleFeedbackChip;
  const showMetrics = !hideMetrics;

  const someMetricShowing = useMemo(() => {
    return (
      Object.values(METRICS_COLUMNS)
        .map((m) => m.name)
        .filter((m) => !hiddenMetrics.includes(m)).length > 0
    );
  }, [hiddenMetrics]);

  const someFeedbackKeyShowing = useMemo(() => {
    return runs.some((run) => {
      return (
        Object.keys(run.feedback_stats ?? {})
          .concat(expectedFeedbackKeys ?? [])
          .filter((key) => !hiddenFeedbackKeys.includes(key)).length > 0
      );
    });
  }, [runs, expectedFeedbackKeys, hiddenFeedbackKeys]);

  const someFeedbackKeyOrMetricShowing = useMemo(() => {
    return someMetricShowing || someFeedbackKeyShowing;
  }, [someMetricShowing, someFeedbackKeyShowing]);

  const runCellActions = (
    <DatasetSessionCompareRunCellActions
      run={run}
      sessionId={sessionId}
      showRepetitionsUI={showRepetitionsUI}
      handleTraceButtonClick={handleTraceButtonClick}
      runInViewIdx={runInView}
      setRunInViewIdx={setRunInView}
      totalRuns={sortedRuns.length}
    />
  );

  return (
    <td
      className={cn(
        'group relative border-b border-r border-secondary transition-colors',
        !isTextTruncated && 'align-top',
        !isRegression && !isImprovement && backgroundStyles,
        isRegression &&
          (isDarkMode ? 'bg-[var(--red-50)]' : 'bg-[var(--red-300)]'),
        isImprovement &&
          (isDarkMode ? 'bg-[var(--green-100)]' : 'bg-[var(--green-300)]'),
        className,
        'bg-opacity-30'
      )}
      style={style}
      data-column={columnIndex}
    >
      {run ? (
        <div
          className="flex h-full max-w-full flex-col flex-wrap items-stretch justify-between gap-1"
          style={{ ...CELL_PADDING(inPlayground ? 'lg' : 'sm') }}
        >
          <div className="flex h-full max-w-full shrink justify-between gap-1 overflow-hidden">
            <div className="relative top-[2px] w-full">
              <DatasetSessionCompareOutputs
                outputs={run?.outputs}
                outputsPreview={run?.outputs_preview}
                diffs={diffs?.[run?.id]?.runOutput}
                onOpenDetailSidebar={() => onOpenDetailSidebar(example.id)}
                error={run?.error}
                dataType={dataType}
                language={language}
                truncate={isTextTruncated}
                inputs={run?.inputs ?? run?.inputs_preview}
                lineClamp={1}
                metricsHidden={hideMetrics}
              />
            </div>
            {isTextTruncated && runCellActions}
          </div>
          {(showFullFeedbackChips ||
            showExpandedSingleFeedbackChip ||
            showMetrics) &&
            sessionIds.length > 1 &&
            !!someFeedbackKeyOrMetricShowing && (
              <div className="flex max-w-full shrink-0 flex-col gap-2">
                {showFullFeedbackChips && (
                  <div className="flex max-w-full shrink-0 flex-wrap gap-2">
                    {showRepetitionsUI ? (
                      <AggregateFeedbackChipsWithExpandButton
                        runs={runs}
                        allowTruncation
                        className="rounded-md bg-background"
                        comparativeExperimentId={comparativeExperiment}
                        sortMode="key"
                        onMutate={mutateInfiniteExamples}
                        disablePopover={!!datasetShareToken}
                        maxNumberOfCategories={
                          COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW
                        }
                        expectedFeedbackKeys={expectedFeedbackKeys}
                        hiddenFeedbackKeys={hiddenFeedbackKeys}
                        expanded={expanded}
                        setExpanded={setExpanded}
                        onViewEvaluatorRun={onViewEvaluatorRun}
                        maxChips={isTextTruncated ? 1 : undefined}
                        firstFeedbackKey={
                          regressionsFilterFeedbackKey ?? undefined
                        }
                        showErrorCount={true}
                        traceTier={session?.trace_tier}
                        experimentStartTime={session?.created_at}
                      />
                    ) : (
                      <FeedbackChipsWithExpandButton
                        allowTruncation
                        sortMode="key"
                        feedbackStats={run?.feedback_stats}
                        showErrorCount={true}
                        feedbackSourceRunId={
                          !datasetShareToken ? run.id : undefined
                        }
                        expanded={expanded}
                        setExpanded={setExpanded}
                        showNotes
                        className="rounded-md bg-background"
                        comparativeExperimentId={comparativeExperiment}
                        onMutate={mutateInfiniteExamples}
                        disablePopover={!!datasetShareToken}
                        maxNumberOfCategories={
                          COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW
                        }
                        expectedFeedbackKeys={expectedFeedbackKeys}
                        hiddenFeedbackKeys={hiddenFeedbackKeys}
                        onViewEvaluatorRun={onViewEvaluatorRun}
                        maxChips={isTextTruncated ? 1 : undefined}
                        firstFeedbackKey={
                          regressionsFilterFeedbackKey ?? undefined
                        }
                        traceTier={session?.trace_tier}
                        experimentStartTime={session?.created_at}
                      />
                    )}
                  </div>
                )}
                {showExpandedSingleFeedbackChip && (
                  <div className="flex max-w-full overflow-hidden">
                    {showRepetitionsUI ? (
                      <AggregateFeedbackChips
                        runs={runs}
                        showSingleFeedbackKey={regressionsFilterFeedbackKey}
                        allowTruncation
                        className="rounded-md bg-background"
                        comparativeExperimentId={comparativeExperiment}
                        onMutate={mutateInfiniteExamples}
                        disablePopover={!!datasetShareToken}
                        maxNumberOfCategories={
                          COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW
                        }
                        hiddenFeedbackKeys={hiddenFeedbackKeys}
                        expectedFeedbackKeys={expectedFeedbackKeys}
                      />
                    ) : (
                      <SingleFeedbackChipWithSourceRunAction
                        feedbackKey={regressionsFilterFeedbackKey}
                        avg={regressionFeedbackKeyAvg}
                        values={regressionFeedbackKeyValues}
                        n={regressionFeedbackKeyN}
                        errors={regressionFeedbackKeyErrors}
                        showErrorCount={true}
                        feedbackSourceRunId={
                          !datasetShareToken ? run.id : undefined
                        }
                        className="rounded-md bg-background"
                        allowTruncation
                        showNotes
                        showFeedbackArrow={
                          run.feedback_stats?.[regressionsFilterFeedbackKey]
                            ?.show_feedback_arrow
                        }
                        comparativeExperimentId={comparativeExperiment}
                        onMutate={mutateInfiniteExamples}
                        disablePopover={!!datasetShareToken}
                        maxNumberOfCategories={
                          COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW
                        }
                        loading={
                          !run?.feedback_stats?.[regressionsFilterFeedbackKey]
                        }
                        traceTier={session?.trace_tier}
                        experimentStartTime={session?.created_at}
                      />
                    )}
                  </div>
                )}
                {showMetrics && someMetricShowing && (
                  <div className="flex max-w-full flex-wrap items-center gap-1">
                    {showRepetitionsUI ? (
                      <AggregateMetricsChips
                        runs={runs}
                        hiddenMetrics={hiddenMetrics}
                      />
                    ) : (
                      <MetricsChips run={run} hiddenMetrics={hiddenMetrics} />
                    )}
                  </div>
                )}
              </div>
            )}
          {!isTextTruncated && <div className="ml-auto">{runCellActions}</div>}
          {example.dataset_id && (
            <DatasetSessionCompareCellActions
              example={example}
              onOpenDetailSidebar={() =>
                openDetailsOrRepetitionsPane(example, runInView, sessionId)
              }
              setDetailRunId={(runId) => {
                onOpenTraceDetailSidebar?.(runId, run.trace_id);
              }}
              className="pointer-events-none z-50 opacity-0 focus-within:pointer-events-auto focus-within:opacity-100 group-hover:pointer-events-auto group-hover:opacity-100"
            />
          )}
        </div>
      ) : (
        <div
          style={{ ...CELL_PADDING(inPlayground ? 'lg' : 'sm') }}
          className="flex h-full w-full px-4 align-top"
        >
          {experimentInProgress ? (
            <div className="w-full">
              <Loader />
            </div>
          ) : (
            <span className="whitespace-normal italic text-quaternary">
              {inPlayground
                ? OUTPUT_CELL_PRE_RUN_PLACEHOLDER_TEXT
                : 'Experiment has no run for this example'}
            </span>
          )}
        </div>
      )}
    </td>
  );
};
