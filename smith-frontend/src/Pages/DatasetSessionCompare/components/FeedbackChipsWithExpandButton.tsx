import { FeedbackChips } from '@/components/FeedbackChips';
import { FeedbackStatsField } from '@/types/schema';

import { MAX_CHIPS } from '../constants';

export function FeedbackChipsWithExpandButton(props: {
  feedbackStats: FeedbackStatsField | undefined;
  allowTruncation?: boolean;
  showNotes?: boolean;
  sortMode?: 'key' | 'n';
  feedbackSourceRunId?: string;
  showErrorCount?: boolean;
  comparativeExperimentId?: string | null;
  iconType?: 'session' | 'run';
  className?: string;
  disablePopover?: boolean;
  maxNumberOfCategories?: number;
  expanded: boolean;
  setExpanded: (expanded: boolean | ((prev) => boolean)) => void;
  onMutate?: () => void;
  expectedFeedbackKeys?: string[];
  maxChips?: number;
  hiddenFeedbackKeys?: string[];
  onViewEvaluatorRun?: (runId: string) => void;
  firstFeedbackKey?: string;
  traceTier?: string | null;
  experimentStartTime?: string | null;
}) {
  const maxChips = props.maxChips ?? MAX_CHIPS;
  const allFeedbackKeys = new Set(
    Object.keys(props.feedbackStats ?? {})
      .concat(props.expectedFeedbackKeys ?? [])
      .filter((key) => !props.hiddenFeedbackKeys?.includes(key))
  );
  const numKeys = allFeedbackKeys.size;
  const chips = (
    <FeedbackChips
      {...props}
      maxChips={props.expanded ? undefined : maxChips}
      hiddenFeedbackKeys={props.hiddenFeedbackKeys}
      onViewEvaluatorRun={props.onViewEvaluatorRun}
    />
  );

  return (
    <div className="flex flex-wrap items-center gap-2">
      {chips}
      {numKeys > maxChips && (
        <button
          type="button"
          className="bg-none text-tertiary underline"
          onClick={(e) => {
            props.setExpanded((prev) => !prev);
            e.stopPropagation();
          }}
        >
          {props.expanded ? 'Show less' : `Show ${numKeys - maxChips} more...`}
        </button>
      )}
    </div>
  );
}
