import { Fragment, useMemo } from 'react';

import Attachments from '@/Pages/Run/components/Attachments';
import { getAttachments } from '@/Pages/Run/utils/multimodalRunUtils';
import { PrettyJSONEditor } from '@/components/PrettyJSONEditor/PrettyJSONEditor';
import { useDatasetFeedbackDelta } from '@/hooks/useSwr';
import {
  ComparisonViewColumn,
  DatasetDataType,
  DatasetSchema,
  ExampleSchemaWithRunsAndOptionalFields,
  SessionSchema,
} from '@/types/schema';
import { isMessageLike } from '@/utils/messages';
import { cn } from '@/utils/tailwind';
import { useLocalStorageState } from '@/utils/use-local-storage-state';

import { DatasetSessionCompareCellActions } from '../../DatasetSessionCompareCellActions';
import { DatasetSessionCompareInputs } from '../../DatasetSessionCompareInputs';
import { DatasetSessionCompareOutputs } from '../../DatasetSessionCompareOutputs';
import {
  DEFAULT_COLUMN_WIDTHS,
  DatasetSessionCompareSettings,
  OUTPUT_START_COLUMN_IDX,
  TextDisplayMode,
} from '../../constants';
import { isDatasetExample } from '../../utils/isDatasetExample';
import { useGetExperimentDiffs } from '../../utils/useGetExperimentDiffs';
import { DatasetExampleButton } from '../DatasetExampleButton';
import { DatasetSessionCompareRunCell } from '../DatasetSessionCompareRunCell';
import { FeedbackColumns } from '../SingleExperimentColumns/FeedbackColumns/FeedbackColumns';
import { MetricsColumns } from '../SingleExperimentColumns/MetricsColumns/MetricsColumns';
import { useSidePanes } from '../hooks/useSidePanes';
import { AttachmentsCell } from './AttachmentsCell';
import { InputsCell } from './InputsCell';
import { MoreActionsButton } from './MoreActionsButton';
import { OutputsCell } from './OutputsCell';

export interface TableRowProps {
  example: ExampleSchemaWithRunsAndOptionalFields;
  setExampleInputs?: (inputs: Record<string, string>) => void;
  setExampleOutputs?: (outputs: Record<string, string>) => void;
  displaySettingsState: DatasetSessionCompareSettings;
  sidePanesControls: ReturnType<typeof useSidePanes>;
  columnWidthStorage: number[];
  dataset?: DatasetSchema;
  columnsSorted: ComparisonViewColumn[];
  feedbackDelta: ReturnType<typeof useDatasetFeedbackDelta>['data'];
  experimentInProgress?: boolean;
  expectedFeedbackKeys?: string[];
  sessionIds: string[];
  columnIds: string[];
  isPairwiseUIEnabled: boolean;
  isRegressionTrackingUIEnabled: boolean;
  regressionsFilterFeedbackKey?: string | null;
  mutateInfiniteExamples: () => void;
  comparativeExperiment: string | null;
  datasetShareToken?: string;
  aggregateFeedbackKeys: string[];
  dataType?: DatasetDataType;
  isEditable: boolean;
  heuristicCompactViewDisplayedKey?: string;
  onDelete?: () => void;
  isDeleteLoading?: boolean;
  customExtraTableCell?: React.ReactNode;
  sessionsData?: SessionSchema[];
}

export function TableRow({
  example,
  setExampleInputs,
  setExampleOutputs,
  displaySettingsState,
  sidePanesControls,
  columnWidthStorage,
  dataset,
  columnsSorted,
  feedbackDelta,
  experimentInProgress,
  expectedFeedbackKeys,
  sessionIds,
  columnIds,
  isPairwiseUIEnabled,
  isRegressionTrackingUIEnabled,
  regressionsFilterFeedbackKey,
  mutateInfiniteExamples,
  comparativeExperiment,
  datasetShareToken,
  aggregateFeedbackKeys,
  dataType,
  isEditable,
  heuristicCompactViewDisplayedKey,
  onDelete,
  isDeleteLoading,
  customExtraTableCell,
  sessionsData,
}: TableRowProps) {
  const {
    isReferenceInputHidden,
    isReferenceOutputHidden,
    isAttachmentsHidden,
    hideFeedback,
    hideMetrics,
    language,
    textDisplayMode,
    hiddenFeedbackColumns,
    hiddenMetricsColumns,
    isHeatmapVisible,
  } = displaySettingsState;

  const { getRunDiffs, storedDiffs } = useGetExperimentDiffs(language);

  const {
    reset,
    openDetailsOrRepetitionsPane,
    setDetailSingleExampleId,
    toggleRunDetail,
    openTraceId,
    rowDetailExampleId,
    detailSingleExampleId,
    toggleRowExampleDetail,
  } = sidePanesControls;

  const [feedbackChipsExpanded, setFeedbackChipsExpanded] =
    useLocalStorageState('ls:comparisonViewFeedbackChipsExpanded', false);

  const isDetailExample = useMemo(
    () =>
      rowDetailExampleId === example?.id ||
      detailSingleExampleId === example?.id ||
      openTraceId ===
        example?.runs.find((r) => r.trace_id === openTraceId)?.trace_id,
    [rowDetailExampleId, detailSingleExampleId, openTraceId, example]
  );

  let backgroundStyles = '';
  if (isDetailExample) {
    backgroundStyles = 'active';
  }

  const attachments = getAttachments(example.attachment_urls);

  const hoverButton = example.dataset_id ? (
    <DatasetSessionCompareCellActions
      example={example}
      onOpenDetailSidebar={toggleRowExampleDetail}
      className="pointer-events-none z-50 opacity-0 focus-within:pointer-events-auto focus-within:opacity-100 group-hover:pointer-events-auto group-hover:opacity-100"
    />
  ) : undefined;

  const showFeedbackAndMetricsColumns =
    (sessionIds.length === 1 && columnIds.length === 0) ||
    (sessionIds.length === 0 && columnIds.length === 1);

  const isTextTruncated = textDisplayMode === TextDisplayMode.COMPACT;

  return (
    <Fragment key={`table-row-${example.id}`}>
      <InputsCell
        isReferenceInputHidden={isReferenceInputHidden}
        isTextTruncated={isTextTruncated}
        backgroundStyles={backgroundStyles}
        columnWidthStorage={columnWidthStorage}
        hideFeedback={hideFeedback}
        isDetailExample={isDetailExample}
        inputContent={
          isEditable && setExampleInputs ? (
            <PrettyJSONEditor
              readOnly={!isEditable}
              value={example.inputs}
              setValue={setExampleInputs}
              keyPlaceholder="input"
              valuePlaceholder="Type input value here..."
            />
          ) : (
            <DatasetSessionCompareInputs
              inputs={example.inputs}
              heuristicCompactViewDisplayedKey={
                heuristicCompactViewDisplayedKey
              }
              dataType={dataset?.data_type ?? 'kv'}
              language={language}
              truncate={isTextTruncated}
              lineClamp={1}
            />
          )
        }
        hoverButton={hoverButton}
        exampleButton={
          isDatasetExample(example) && (
            <DatasetExampleButton
              example={example}
              isTextTruncated={isTextTruncated}
              hideFeedback={hideFeedback}
              onClick={() => {
                reset();
                setDetailSingleExampleId(example.id ?? null);
              }}
            />
          )
        }
      />
      <AttachmentsCell
        isAttachmentsHidden={isAttachmentsHidden}
        isTextTruncated={isTextTruncated}
        backgroundStyles={backgroundStyles}
        columnWidthStorage={columnWidthStorage}
        hasAttachments={attachments.length > 0}
        hoverButton={hoverButton}
        attachmentContent={
          <Attachments
            attachments={attachments}
            className="p-0"
            gridClassName={cn(
              'flex gap-2 p-0',
              isTextTruncated && 'flex-nowrap'
            )}
            hideTitle
            size="small"
          />
        }
      />
      <OutputsCell
        isReferenceOutputHidden={isReferenceOutputHidden}
        isTextTruncated={isTextTruncated}
        backgroundStyles={backgroundStyles}
        columnWidthStorage={columnWidthStorage}
        hoverButton={hoverButton}
        outputContent={
          isEditable && setExampleOutputs ? (
            <PrettyJSONEditor
              readOnly={!isEditable}
              value={example.outputs}
              setValue={setExampleOutputs}
              keyPlaceholder="output"
              valuePlaceholder="Type output value here..."
            />
          ) : (
            <DatasetSessionCompareOutputs
              outputs={example.outputs}
              dataType={dataset?.data_type ?? 'kv'}
              language={language}
              truncate={isTextTruncated}
              inputs={example.inputs}
              lineClamp={1}
              diffs={
                textDisplayMode === TextDisplayMode.DIFF
                  ? storedDiffs[example.id]?.exampleOutput
                  : undefined
              }
            />
          )
        }
      />
      {columnsSorted.map((col, sessionIndex) => {
        const sessionId: string | undefined =
          col['sessionId'] ?? col['columnId'];
        const width =
          columnWidthStorage[sessionIndex + OUTPUT_START_COLUMN_IDX] ??
          DEFAULT_COLUMN_WIDTHS;
        return (
          <DatasetSessionCompareRunCell
            key={`run-cell-${sessionId}-${example.id}`}
            hiddenFeedbackKeys={displaySettingsState.hiddenFeedbackColumns}
            hiddenMetrics={displaySettingsState.hiddenMetricsColumns}
            experimentInProgress={experimentInProgress}
            expectedFeedbackKeys={
              example.runs.every((r) => r.error) ? [] : expectedFeedbackKeys
            }
            expanded={feedbackChipsExpanded}
            setExpanded={setFeedbackChipsExpanded}
            openDetailsOrRepetitionsPane={openDetailsOrRepetitionsPane}
            example={example}
            column={col}
            sessionIds={sessionIds}
            feedbackDelta={feedbackDelta}
            isPairwiseUIEnabled={isPairwiseUIEnabled}
            isRegressionTrackingUIEnabled={isRegressionTrackingUIEnabled}
            regressionsFilterFeedbackKey={regressionsFilterFeedbackKey}
            isTextTruncated={isTextTruncated}
            getRunDiffs={getRunDiffs}
            diffs={
              textDisplayMode === TextDisplayMode.DIFF
                ? storedDiffs[example.id]?.runs
                : undefined
            }
            language={language}
            onOpenDetailSidebar={toggleRowExampleDetail}
            onOpenTraceDetailSidebar={toggleRunDetail}
            backgroundStyles={backgroundStyles}
            hideFeedback={hideFeedback}
            hideMetrics={hideMetrics}
            columnIndex={sessionIndex + OUTPUT_START_COLUMN_IDX}
            dataType={
              dataType ??
              (example.runs.every((r) => isMessageLike(r.outputs?.output))
                ? 'chat'
                : 'kv')
            }
            mutateInfiniteExamples={mutateInfiniteExamples}
            comparativeExperiment={comparativeExperiment ?? undefined}
            datasetShareToken={datasetShareToken}
            style={{
              width,
              maxWidth: width,
              minWidth: width,
            }}
            onViewEvaluatorRun={toggleRunDetail}
            sessions={sessionsData}
          />
        );
      })}

      {showFeedbackAndMetricsColumns && (
        <FeedbackColumns
          sessionId={sessionIds[0]}
          example={example}
          aggregateFeedbackKeys={aggregateFeedbackKeys}
          hiddenFeedbackColumns={hiddenFeedbackColumns}
          isTruncated={isTextTruncated}
          mutateInfiniteExamples={mutateInfiniteExamples}
          datasetShareToken={datasetShareToken}
          expectedFeedbackKeys={expectedFeedbackKeys}
          columnIndexStart={OUTPUT_START_COLUMN_IDX + 1}
          columnWidthStorage={columnWidthStorage}
          isHeatmapVisible={isHeatmapVisible}
          setOpenRunId={toggleRunDetail}
          experimentInProgress={!!experimentInProgress}
          sessionsData={sessionsData}
        />
      )}

      {showFeedbackAndMetricsColumns && (
        <MetricsColumns
          sessionId={sessionIds[0]}
          example={example}
          columnWidthStorage={columnWidthStorage}
          columnIndexStart={
            OUTPUT_START_COLUMN_IDX +
            1 +
            aggregateFeedbackKeys.filter(
              (key) => !hiddenFeedbackColumns.includes(key)
            ).length
          }
          isTruncated={isTextTruncated}
          hiddenMetricsColumns={hiddenMetricsColumns}
          experimentInProgress={!!experimentInProgress}
        />
      )}
      {customExtraTableCell}

      {onDelete && (
        <MoreActionsButton
          isLoading={isDeleteLoading ?? false}
          onDelete={onDelete}
        />
      )}
      <td
        className={
          'group h-full overflow-hidden border-b border-r border-secondary'
        }
      />
    </Fragment>
  );
}
