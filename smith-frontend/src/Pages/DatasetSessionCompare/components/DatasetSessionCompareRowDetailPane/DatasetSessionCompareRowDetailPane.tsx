import { useEffect, useMemo, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { Example } from '@/Pages/Example';
import { Run } from '@/Pages/Run';
import Attachments from '@/Pages/Run/components/Attachments';
import { getAttachments } from '@/Pages/Run/utils/multimodalRunUtils';
import { CodeLanguageType } from '@/components/Code/types';
import { emulateNativeClick } from '@/components/DataGrid.utils';
import { IFrameTraceRenderer } from '@/components/IFrameTraceRenderer';
import { RunAnnotationsCrudPane } from '@/components/RunAnnotationCrudPane/RunAnnotationsCrudPane';
import { SplitViewPane } from '@/components/SplitViewPane';
import { useIFrameTraceRendering } from '@/hooks/useIFrameTraceRendering';
import { useStateFromSearchParams } from '@/hooks/useStateFromSearchParams';
import { useOrganizationId, useRun } from '@/hooks/useSwr';
import {
  ComparisonViewColumn,
  DatasetSchema,
  ExampleSchemaWithRunsAndOptionalFields,
  SessionFeedbackDelta,
  SessionSchema,
} from '@/types/schema';
import {
  appDatasetsPath,
  appExamplePath,
  appOrganizationPath,
  appPublicDatasetsPath,
  appPublicPath,
  appRunPath,
  appSessionPath,
} from '@/utils/constants';
import { cn } from '@/utils/tailwind';

import { DetailPaneHeader } from '../../DetailPaneHeader';
import {
  COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW,
  OUTPUT_CELL_PRE_RUN_PLACEHOLDER_TEXT,
} from '../../constants';
import { TExampleDiff } from '../../types';
import { isInPlayground } from '../../utils/isInPlayground';
import { TGetRunDiffArgs } from '../../utils/useGetExperimentDiffs';
import { DatasetSessionCompareRepetitionsDetailPane } from '../DatasetSessionCompareRepetitionsDetailPane';
import { DatasetSessionCompareRunActionButtons } from '../DatasetSessionCompareRunActionButtons';
import { DatasetSessionCompareSidePaneDetailInputs } from '../DatasetSessionCompareSidePaneDetailInputs';
import { DatasetSessionCompareSidePaneDetailOutputs } from '../DatasetSessionCompareSidePaneDetailOutputs';
import { CompareOutputContent } from './CompareOutputContent';
import { OutputChips } from './OutputChips';
import { PairwiseFeedbackChip } from './PairwiseFeedbackChip';
import { ReferenceSection } from './ReferenceSection';
import { ResizeHandle } from './ResizeHandle';
import { SessionTabs } from './SessionTabs';
import { useRepetitionsSidePane } from './hooks/useRepetitionsSidePane';
import { useResizeHandler } from './hooks/useResizeHandler';
import { useScrollSync } from './hooks/useScrollSync';

const EXPERIMENT_OUTPUT_WIDTH_NUMBER = 495;
const EXPERIMENT_OUTPUT_WIDTH = `${EXPERIMENT_OUTPUT_WIDTH_NUMBER}px`;

interface DatasetSessionCompareRowDetailPaneProps {
  detailExample: ExampleSchemaWithRunsAndOptionalFields;
  datasetShareToken?: string;
  columnsSorted: ComparisonViewColumn[];
  sessions?: SessionSchema[];
  dataset?: DatasetSchema;
  language?: CodeLanguageType | 'plaintext';
  setLanguage: (language: CodeLanguageType | 'plaintext') => void;
  exampleDetailPaneOpen: boolean;
  exampleDetailInNestedPaneOpen: boolean;
  pairwiseFeedbackKey?: string;
  feedbackDelta?: SessionFeedbackDelta;
  pairwiseNonBaseline?: string;
  selectedFeedbackKey?: string | null;
  setExampleDetailPaneOpen: (open: boolean) => void;
  setExampleDetailInNestedPaneOpen: (open: boolean) => void;
  mutateInfiniteExamples: () => void;
  diffs?: TExampleDiff;
  getRunDiffs: (args: TGetRunDiffArgs) => void;
}

export function DatasetSessionCompareRowDetailPane({
  detailExample,
  datasetShareToken,
  columnsSorted,
  sessions,
  dataset,
  language = 'plaintext',
  setLanguage,
  exampleDetailPaneOpen,
  exampleDetailInNestedPaneOpen,
  pairwiseFeedbackKey,
  feedbackDelta,
  pairwiseNonBaseline,
  selectedFeedbackKey,
  setExampleDetailPaneOpen,
  setExampleDetailInNestedPaneOpen,
  mutateInfiniteExamples,
  diffs,
  getRunDiffs,
}: DatasetSessionCompareRowDetailPaneProps) {
  const inPlayground = isInPlayground();
  const [, setSearchParams] = useSearchParams();

  const attachments = useMemo(
    () => getAttachments(detailExample.attachment_urls),
    [detailExample.attachment_urls]
  );
  const hasAttachments = attachments.length > 0;

  const referenceInputRef = useRef<HTMLDivElement>(null);
  const exampleAttachmentsRef = useRef<HTMLDivElement>(null);
  const referenceOutputRef = useRef<HTMLDivElement>(null);
  const rootComponentRef = useRef<HTMLDivElement>(null);

  const { dragging, handlePointerDown, handlePointerUp } = useResizeHandler({
    referenceInputRef,
    referenceOutputRef,
    exampleAttachmentsRef,
    rootComponentRef,
  });

  const allColumnIds = useMemo(
    () =>
      columnsSorted.map((column) => column['sessionId'] ?? column['columnId']),
    [columnsSorted]
  );
  const firstColumnId = allColumnIds[0];
  const [activeSection, setActiveSection] = useStateFromSearchParams(
    'activeSession',
    firstColumnId ?? null,
    setSearchParams
  );

  const { itemRefs, scrollContainerRef, scrollToItem } = useScrollSync({
    allColumnIds,
    activeSection,
    setActiveSection,
  });

  const [detailRunId, setDetailRunId] = useStateFromSearchParams(
    'detailPaneDetailRunId',
    null,
    setSearchParams
  );
  const [annotationDetailRunId, setAnnotationDetailRunId] =
    useStateFromSearchParams(
      'detailPaneAnnotationRunId',
      null,
      setSearchParams
    );

  useEffect(() => {
    if (annotationDetailRunId) {
      setAnnotationDetailRunId(detailExample?.runs?.[0].id ?? null);
    }
  }, [detailExample]);

  const annotationDetailRun = annotationDetailRunId
    ? detailExample?.runs?.find((r) => r.id === annotationDetailRunId)
    : null;

  /* TODO(eric): we're only fetching detailRun for getting the right path on expand
  of the run details pane. can this be refactored away? */
  let detailRun = detailRunId
    ? detailExample?.runs?.find((r) => r.id === detailRunId)
    : null;

  const shouldFetchDetailRun = detailRunId && !detailRun;

  const detailRunSwr = useRun({
    id: shouldFetchDetailRun ? detailRunId : null,
    shareToken: {
      datasetShareToken: datasetShareToken,
    },
    withContents: true,
  });

  if (shouldFetchDetailRun) {
    detailRun = detailRunSwr.data;
  }

  const navigate = useNavigate();
  const organizationId = useOrganizationId();

  const { currIFrameConfig } = useIFrameTraceRendering(dataset?.id);

  const { onOpenRepetitionsSidebar, repetitionsSidePaneInfo } =
    useRepetitionsSidePane({
      detailExample,
      setDetailRunId,
      setSearchParams,
    });

  const outputTitles = useMemo(() => {
    return allColumnIds.map((columnId) => {
      const session = sessions?.find((session) => session.id === columnId);

      const column = columnsSorted.find(
        (column) =>
          column['sessionId'] === columnId || column['columnId'] === columnId
      );
      return session?.name ?? (column?.['columnName'] as string);
    });
  }, [allColumnIds, columnsSorted, sessions]);

  return (
    <div
      className={cn(
        'inset-0 flex h-[calc(100vh-60px)] flex-grow flex-col overflow-hidden'
      )}
      ref={rootComponentRef}
    >
      <div
        className={cn(
          'relative grid',
          hasAttachments ? 'grid-cols-3' : 'grid-cols-2'
        )}
      >
        <ReferenceSection title="Input" referenceRef={referenceInputRef}>
          <DatasetSessionCompareSidePaneDetailInputs
            inputs={detailExample.inputs}
            dataType={dataset?.data_type ?? 'kv'}
            language={language}
            height="100%"
          />
        </ReferenceSection>
        {hasAttachments && (
          <ReferenceSection
            title="Attachments"
            referenceRef={exampleAttachmentsRef}
          >
            <Attachments
              attachments={attachments}
              className="p-0"
              gridClassName={cn('flex gap-2 p-0')}
              hideTitle
              size="small"
            />
          </ReferenceSection>
        )}

        <ReferenceSection
          title="Reference Output"
          referenceRef={referenceOutputRef}
        >
          {currIFrameConfig?.enabled &&
          dataset?.id &&
          language === 'plaintext' ? (
            <IFrameTraceRenderer
              config={currIFrameConfig}
              key={detailExample.id}
              payload={detailExample.outputs}
              style={{ pointerEvents: dragging ? 'none' : 'auto' }}
              height="100%"
              type="reference"
            />
          ) : (
            <DatasetSessionCompareSidePaneDetailOutputs
              inputs={detailExample.inputs}
              outputs={detailExample.outputs}
              dataType={dataset?.data_type ?? 'kv'}
              language={language}
              diffs={diffs?.exampleOutput}
            />
          )}
        </ReferenceSection>

        <ResizeHandle
          dragging={dragging}
          onMouseDown={handlePointerDown}
          onMouseUp={handlePointerUp}
        />
      </div>

      <h2 className="flex w-full items-center bg-secondary text-sm font-medium">
        <span className="px-4 py-2 text-sm font-medium">Output</span>
        {/* {allColumnIds.length > 2 && !haveComponentsExpanded && (} */}
        {allColumnIds.length > 2 && (
          <SessionTabs
            allColumnIds={allColumnIds}
            titles={outputTitles}
            activeSection={activeSection ?? ''}
            onTabClick={scrollToItem}
          />
        )}
      </h2>
      <div className="flex flex-1 flex-col overflow-hidden">
        <div
          className="flex gap-3 overflow-x-auto p-3 pb-9"
          style={{ flex: 1 }}
          ref={scrollContainerRef}
        >
          {allColumnIds.map((columnId, idx) => {
            if (!detailExample) return null;

            const runs = detailExample.runs.filter(
              (run) => run.session_id === columnId
            );
            const run = runs?.[0];

            const title = outputTitles[idx];

            const feedbackStats = { ...run?.feedback_stats };
            if (pairwiseFeedbackKey) delete feedbackStats[pairwiseFeedbackKey];

            const widths = ['100%', '50%', '33.33%', '25%', '20%'];
            return (
              <div
                key={columnId}
                className="flex flex-col gap-3 rounded-xl border border-secondary p-4"
                style={{
                  width:
                    widths[columnsSorted.length - 1] ?? EXPERIMENT_OUTPUT_WIDTH,
                  minWidth: EXPERIMENT_OUTPUT_WIDTH,
                }}
                ref={(el) => {
                  itemRefs.current[idx] = el;
                }}
              >
                <div className="flex items-center justify-between gap-6">
                  <h2 className="line-clamp-1 whitespace-normal break-words break-all text-lg font-semibold">
                    {title}
                  </h2>
                  {run != null && (
                    <DatasetSessionCompareRunActionButtons
                      run={run}
                      datasetShareToken={datasetShareToken}
                      setDetailRunId={setDetailRunId}
                      setAnnotationDetailRunId={setAnnotationDetailRunId}
                    />
                  )}
                </div>
                {pairwiseFeedbackKey && (
                  <PairwiseFeedbackChip
                    exampleId={detailExample.id}
                    columnId={columnId}
                    pairwiseNonBaseline={pairwiseNonBaseline}
                    feedbackDeltas={feedbackDelta?.feedback_deltas}
                    feedbackKey={pairwiseFeedbackKey}
                    feedbackField={
                      run?.feedback_stats?.[pairwiseFeedbackKey ?? '']
                    }
                    maxCategories={COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW}
                    runId={run?.id}
                    datasetShareToken={datasetShareToken}
                  />
                )}
                {run ? (
                  <div className="flex flex-1 flex-col justify-between gap-3 overflow-y-auto">
                    <CompareOutputContent
                      detailExample={detailExample}
                      columnId={columnId}
                      run={run}
                      dragging={dragging}
                      language={language}
                      dataType={dataset?.data_type ?? 'kv'}
                      onOpenRepetitionsSidebar={onOpenRepetitionsSidebar}
                      diffs={diffs?.runs[run.id]?.runOutput}
                    />

                    <OutputChips
                      runs={runs}
                      feedbackStats={feedbackStats}
                      run={run}
                      mutateInfiniteExamples={() => mutateInfiniteExamples()}
                      datasetShareToken={datasetShareToken}
                    />
                  </div>
                ) : (
                  <div className="flex flex-1 items-start justify-start p-4">
                    <p className="text-sm italic text-quaternary">
                      {inPlayground
                        ? OUTPUT_CELL_PRE_RUN_PLACEHOLDER_TEXT
                        : 'Experiment has no run for this example'}
                    </p>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        <SplitViewPane
          open={!!detailRunId}
          sidePaneId="detailPaneTraceDetail"
          onClose={() => {
            setDetailRunId(null);
          }}
          onExpand={(e) => {
            if (detailRun) {
              const targetUrl = datasetShareToken
                ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${detailRun.session_id}/${appSessionPath}/${appRunPath}/${detailRun?.id}?trace_id=${detailRun?.trace_id}&start_time=${detailRun?.start_time}`
                : detailRun?.app_path ?? '';

              if (!emulateNativeClick(targetUrl, e.nativeEvent)) {
                navigate(targetUrl);
              }
            }
          }}
          className="relative"
          title={''}
        >
          <div className="absolute inset-0 flex flex-grow flex-col overflow-auto px-4">
            <Run
              runId={detailRun?.id}
              traceId={detailRun?.trace_id}
              runStartTime={detailRun?.start_time}
              navigateToRun={(runId) => setDetailRunId(runId)}
              shareTokenProp={{ datasetShareToken: datasetShareToken }}
              runSessionId={detailRun?.session_id}
            />
          </div>
        </SplitViewPane>
        <RunAnnotationsCrudPane
          isOpen={!!annotationDetailRunId}
          run={annotationDetailRun}
          onClose={() => setAnnotationDetailRunId(null)}
          onFeedbackGiven={mutateInfiniteExamples}
        />
        <SplitViewPane
          open={exampleDetailPaneOpen}
          sidePaneId="datasetSessionCompareExampleInDetailPane"
          onClose={() => {
            setExampleDetailPaneOpen(false);
          }}
          onExpand={(e) => {
            const targetUrl = datasetShareToken
              ? `/${appPublicPath}/${datasetShareToken}/${appPublicDatasetsPath}/${detailExample?.id}/${appExamplePath}`
              : `/${appOrganizationPath}/${organizationId}/${appDatasetsPath}/${dataset?.id}/${appExamplePath}/${detailExample?.id}`;

            if (!emulateNativeClick(targetUrl, e.nativeEvent)) {
              navigate(targetUrl);
            }
          }}
          title={null}
          className="relative"
        >
          <div className="absolute inset-0 flex flex-grow flex-col gap-4 overflow-auto p-4">
            <Example
              exampleId={detailExample.id}
              datasetId={dataset?.id}
              onMutate={() => mutateInfiniteExamples()}
            />
          </div>
        </SplitViewPane>
        <SplitViewPane
          open={!!repetitionsSidePaneInfo}
          sidePaneId="repetitions"
          onClose={() => {
            onOpenRepetitionsSidebar(null);
            setExampleDetailInNestedPaneOpen(false);
          }}
          title={
            <DetailPaneHeader
              dataset={dataset}
              rowDetailLanguage={language ?? 'plaintext'}
              setRowDetailLanguage={setLanguage}
              setExampleDetailInSidePaneOpen={setExampleDetailInNestedPaneOpen}
            >
              <span className="mx-3 line-clamp-1 whitespace-normal break-words break-all">
                {sessions?.find(
                  (s) => s.id === repetitionsSidePaneInfo?.session
                )?.name ?? ''}
              </span>
            </DetailPaneHeader>
          }
          className="relative"
        >
          <DatasetSessionCompareRepetitionsDetailPane
            detailExample={detailExample}
            datasetShareToken={datasetShareToken}
            sessions={sessions}
            columns={columnsSorted}
            columnId={repetitionsSidePaneInfo?.session}
            selectedFeedbackKey={selectedFeedbackKey}
            dataset={dataset}
            language={language}
            mutateInfiniteExamples={() => mutateInfiniteExamples()}
            exampleDetailPaneOpen={exampleDetailInNestedPaneOpen}
            setExampleDetailPaneOpen={setExampleDetailInNestedPaneOpen}
            diffs={diffs}
            getRunDiffs={getRunDiffs}
          />
        </SplitViewPane>
      </div>
    </div>
  );
}
