import { AxisBottom, AxisLeft } from '@visx/axis';
import { localPoint } from '@visx/event';
import { GridRows } from '@visx/grid';
import { Group } from '@visx/group';
import { scaleBand, scaleLinear } from '@visx/scale';
import { BarRounded } from '@visx/shape';
import { defaultStyles, useTooltip, useTooltipInPortal } from '@visx/tooltip';

import { FeedbackChip } from '@/components/FeedbackChips';
import { SessionTooltipComponent } from '@/components/SessionsTable/components/SessionsTableChart';
import { getTickFormat } from '@/components/SessionsTable/utils/getTickFormat';
import { useChartData } from '@/components/SessionsTable/utils/useChartData';
import { useColorScheme } from '@/hooks/useColorScheme';
import { SessionSchema } from '@/types/schema';
import { getAdjustedYDomain } from '@/utils/get-adjusted-y-domain';

const compactFormatter = new Intl.NumberFormat('en-US', {
  maximumFractionDigits: 3,
  notation: 'compact',
  compactDisplay: 'short',
});

const HEIGHT = 400;
const TITLE_PADDING_X_AXIS = 70;
const BAR_WIDTH = 10;
const BAR_RADIUS = 3;
const BAR_SPACING = 40;
const MIN_BAR_HEIGHT = 2;

export function DatasetSessionCompareChart(props: {
  rowData: SessionSchema[];
  accessorFn: (row: SessionSchema) => number | null | undefined;
  minimumValue?: number;
  maximumValue?: number;
  yAxisLabel: string;
  chartGroupTitle?: string;
  isFeedback?: boolean;
  isSessionLevelFeedback?: boolean;
  feedbackKey?: string;
  isLatency?: boolean;
  latencyType?: string;
  isErrorRate?: boolean;
  selectedBaselineSessionId?: string | null;
  setHoveredSession?: (num: number | null) => void;
  aggregateMetadataKeys?: string[];
  experimentMetadataPath?: string;
  setExperimentMetadataPath?: (value?: string) => void;
  noChip?: boolean;
}) {
  const { rowData: rowDataProp, accessorFn } = props;
  const { rowData } = useChartData(rowDataProp, accessorFn);

  const chartWidth = BAR_SPACING * rowData.length + 120;
  rowData.sort((a, b) => (a.test_run_number ?? 0) - (b.test_run_number ?? 0));
  const { isDarkMode } = useColorScheme();
  const margin = {
    top: 10,
    bottom: 30 + TITLE_PADDING_X_AXIS,
    left: 50,
    right: 10,
  };

  const x = (data: SessionSchema) => {
    const x = data.test_run_number;
    return x;
  };
  const y = (data: SessionSchema) => {
    const y = props.accessorFn(data);
    return y;
  };

  const isEmpty = !rowData.some((row) => props.accessorFn(row) != null);

  const smallestY = Math.min(
    ...(rowData.map(y).filter((r) => r != null) as number[]),
    ...(props.minimumValue != null ? [props.minimumValue] : [])
  );
  const largestY = Math.max(
    ...(rowData.map(y).filter((r) => r != null) as number[]),
    ...(props.maximumValue ? [props.maximumValue] : [])
  );

  const chartHeight = HEIGHT;

  const xMax = chartWidth - margin.left - margin.right;
  const yMax = chartHeight - margin.top - margin.bottom;
  const xScale = scaleBand({
    range: [0, xMax],
    round: true,
    domain: [...(rowData.map(x).filter((r) => r != null) as number[])],
    padding: 1,
  });

  const { domain, numTicks } = getAdjustedYDomain(
    smallestY,
    largestY,
    props.minimumValue,
    props.maximumValue
  );

  const yScale = scaleLinear({
    range: [yMax, 0],
    round: false,
    domain,
  });

  const tooltipStyles = {
    ...defaultStyles,
    backgroundColor: 'transparent',
    opacity: 1,
    boxShadow: 'none',
  };

  const compose = (scale, accessor) => (data) => scale(accessor(data));
  const xPoint = compose(xScale, x);
  const yPoint = compose(yScale, y);

  const {
    tooltipData,
    tooltipLeft,
    tooltipTop,
    tooltipOpen,
    showTooltip,
    hideTooltip,
  } = useTooltip<SessionSchema>();

  const { containerRef, TooltipInPortal } = useTooltipInPortal({
    detectBounds: true,
    scroll: true,
  });

  const handleMouseOver = (event, datum) => {
    const coords = localPoint(event.target.ownerSVGElement, event);
    const yCoords = coords?.y;
    showTooltip({
      tooltipLeft: coords?.x,
      tooltipTop: yCoords != null ? yCoords - 100 : 100,
      tooltipData: datum,
    });
    props.setHoveredSession?.(datum.test_run_number);
  };

  return (
    <div
      className="relative flex flex-col gap-5 overflow-visible rounded-xl border border-secondary bg-background p-4 pl-3"
      ref={containerRef}
    >
      <div className="flex pl-5 text-sm font-semibold">
        {(props.isFeedback || props.isSessionLevelFeedback) &&
        props.feedbackKey ? (
          <div className="flex max-w-[300px] items-center gap-3">
            <FeedbackChip
              feedback_key={props.feedbackKey}
              iconType={
                props.noChip
                  ? undefined
                  : props.isSessionLevelFeedback
                  ? 'session'
                  : 'run'
              }
              allowTruncation={true}
              startDecorator={props.noChip ? <></> : undefined}
              className={
                props.noChip ? 'border-none bg-transparent px-0' : undefined
              }
            />
          </div>
        ) : (
          props.yAxisLabel
        )}
      </div>
      {isEmpty ? (
        <div
          className="flex flex-col items-center justify-center gap-1"
          style={{ height: chartHeight }}
        >
          <div className="self-stretch text-center text-base font-semibold text-opacity-90">
            No metrics to show
          </div>
          <div className="text-sm font-normal leading-tight text-tertiary">
            Try a different set of filters
          </div>
        </div>
      ) : (
        <div style={{ height: chartHeight }}>
          <svg width={chartWidth} height={chartHeight}>
            <Group top={margin.top} left={margin.left}>
              <GridRows
                scale={yScale}
                width={xMax}
                height={yMax}
                numTicks={numTicks}
                lineStyle={{
                  stroke: isDarkMode ? 'white' : 'black',
                  strokeOpacity: 0.05,
                }}
              />
              <AxisBottom
                top={yMax}
                orientation="bottom"
                scale={xScale}
                labelOffset={20}
                axisLineClassName="stroke-ls-gray-300"
                labelClassName="text-sm font-normal"
                tickLabelProps={{
                  fill: isDarkMode ? '#E9D1D1' : '#162E2E',
                  overflow: 'visible',
                  fontSize: 12,
                  angle: -45,
                  textAnchor: 'end',
                  dy: '0.3em',
                  dx: '-0.5em',
                }}
                tickFormat={(tick) =>
                  getTickFormat(
                    tick,
                    15,
                    rowData,
                    props.experimentMetadataPath,
                    true
                  )
                }
              />
              <AxisLeft
                label={props.yAxisLabel}
                scale={yScale}
                labelOffset={50}
                axisLineClassName="opacity-0"
                tickLineProps={{
                  opacity: 0,
                }}
                numTicks={numTicks}
                labelProps={{
                  fill: isDarkMode
                    ? 'hsl(215 20.2% 65.1%)'
                    : 'hsl(215.4, 16.3%, 46.9%)',
                }}
                tickLabelProps={{
                  fill: isDarkMode ? '#E9D1D1' : '#162E2E',
                  overflow: 'visible',
                  fontSize: 12,
                }}
                labelClassName="text-sm font-normal"
                tickFormat={(tick) => {
                  return compactFormatter.format(tick.valueOf());
                }}
              />
              {rowData.map((d, i) => {
                const barHeight = Math.max(
                  yMax - (yPoint(d) ?? 0),
                  MIN_BAR_HEIGHT
                );
                const isBaseline = d.id === props.selectedBaselineSessionId;

                return (
                  <Group key={`bar-${i}`}>
                    <BarRounded
                      className="rounded-md"
                      colorRendering={'gradient'}
                      x={xPoint(d) + xScale.bandwidth() / 2 - BAR_WIDTH / 2}
                      y={yMax - barHeight}
                      height={barHeight}
                      width={BAR_WIDTH}
                      fill={'#2563EB'}
                      radius={BAR_RADIUS}
                      style={{
                        opacity:
                          props.selectedBaselineSessionId == null
                            ? 1
                            : isBaseline
                            ? 1
                            : 0.3,
                      }}
                      top={true}
                    />
                    <BarRounded
                      className="rounded-md opacity-0"
                      x={xPoint(d) + xScale.bandwidth() / 2 - BAR_SPACING / 2}
                      y={0}
                      height={yMax}
                      width={BAR_SPACING + 5}
                      fill={'#4499F7'}
                      radius={BAR_RADIUS}
                      top={true}
                      onMouseOver={(event) => {
                        handleMouseOver(event, d);
                      }}
                      onMouseMove={(event) => {
                        handleMouseOver(event, d);
                      }}
                      onMouseOut={() => {
                        hideTooltip();
                        props.setHoveredSession?.(null);
                      }}
                    />
                  </Group>
                );
              })}
              {tooltipOpen && tooltipData && (
                <TooltipInPortal
                  style={tooltipStyles}
                  top={tooltipTop}
                  left={tooltipLeft}
                >
                  <div className="inline-flex flex-col items-start justify-start rounded-xl border border-secondary border-opacity-60 bg-white p-2 text-black shadow dark:bg-[#0F1016] dark:text-white">
                    <SessionTooltipComponent
                      session={tooltipData}
                      yAxisLabel={props.chartGroupTitle ?? ''}
                      value={props.accessorFn(tooltipData) ?? 0}
                      isFeedback={props.isFeedback}
                      isSessionLevelFeedback={props.isSessionLevelFeedback}
                      feedbackKey={props.feedbackKey}
                      isErrorRate={props.isErrorRate}
                      isClickable={false}
                      isLatency={props.isLatency}
                      latencyType={props.latencyType}
                    />
                  </div>
                </TooltipInPortal>
              )}
            </Group>
          </svg>
        </div>
      )}
    </div>
  );
}
