import { Tooltip } from '@mui/joy';

import { ReactNode, useMemo } from 'react';

import { calculateLatencyNumber } from '@/Pages/Run/components/RunStatsCard';
import {
  CostChip,
  RawCostChip,
  RawTokensChip,
  TokensChip,
} from '@/Pages/Run/components/TokensChip';
import { FeedbackChips } from '@/components/FeedbackChips';
import { LatencyChip, RunLatencyChip } from '@/components/RunLatencyChip';
import { RunStatusChip, StatusChip } from '@/components/RunStatusChip';
import { FeedbackStatsField, RunSchema, SessionSchema } from '@/types/schema';

import { calculateRunStats } from '../utils/calculateRunStats';

export type AggregateFeedbackChipsProps = {
  runs?: RunSchema[];
  showSingleFeedbackKey?: string;
  allowTruncation?: boolean;
  showNotes?: boolean;
  sortMode?: 'key' | 'n';
  comparativeExperimentId?: string | null;
  iconType?: 'session' | 'run';
  className?: string;
  maxChips?: number;
  hideFeedbackKey?: boolean;
  onSort?: (feedbackKey: string, descending: boolean) => void;
  sortingByFeedbackKey?: string;
  sortingByDescending?: boolean;
  disablePopover?: boolean;
  onMutate?: () => void;
  maxNumberOfCategories?: number;
  expectedFeedbackKeys?: string[];
  onViewEvaluatorRun?: (runId: string) => void;
  showErrorCount?: boolean;
  hiddenFeedbackKeys?: string[];
  startDecorator?: ReactNode;
  chipClassName?: string;
  valueClassName?: string;
  showErrorsOutside?: boolean;
  hideEllipsis?: boolean;
  firstFeedbackKey?: string;
  traceTier?: string | null;
  experimentStartTime?: string | null;
} & (
  | {
      session: SessionSchema | undefined;
      sessionFeedbackLoadStates: Record<string, boolean>;
      isSessionFinished: boolean;
    }
  | {
      session?: never;
      sessionFeedbackLoadStates?: never;
      isSessionFinished?: never;
    }
);

export function AggregateFeedbackChips({
  runs,
  session,
  sessionFeedbackLoadStates,
  isSessionFinished,
  hiddenFeedbackKeys,
  ...props
}: AggregateFeedbackChipsProps) {
  const combinedFeedbackStats: FeedbackStatsField | undefined = useMemo(() => {
    // if we have session feedback stats, just use those
    if (session) {
      // return just the key/value pairs from session.feedback_stats for the keys that have been loaded
      return Object.fromEntries(
        Object.entries(session.feedback_stats ?? {}).filter(
          ([key]) => sessionFeedbackLoadStates[key]
        )
      );
    }
    const feedbackStats: {
      [key: string]: {
        n: number;
        stdev: number;
        avg: number[];
        errors: number;
        showErrorCount?: boolean;
        show_feedback_arrow?: boolean;
        values: {
          [key: string]: number;
        };
      };
    } = {};
    runs?.forEach((run) => {
      Object.entries(run.feedback_stats ?? {}).forEach(([key, value]) => {
        if (props.onSort && value.avg == null) {
          return;
        }
        if (
          !feedbackStats[key] &&
          (props.showSingleFeedbackKey == null ||
            props.showSingleFeedbackKey === key)
        ) {
          feedbackStats[key] = {
            n: 0,
            stdev: 0,
            avg: [],
            errors: 0,
            show_feedback_arrow: value.show_feedback_arrow,
            values: {},
          };
        }
        if (feedbackStats[key]) {
          if (value.avg != null) feedbackStats[key].avg.push(value.avg);
          if (value.errors != null) feedbackStats[key].errors += value.errors;
          feedbackStats[key].n += value.n;
          feedbackStats[key].show_feedback_arrow =
            feedbackStats[key].show_feedback_arrow || value.show_feedback_arrow;
          Object.entries(value.values).forEach(([k, v]) => {
            if (feedbackStats[key].values[k] == null) {
              feedbackStats[key].values[k] = 0;
            }
            feedbackStats[key].values[k] += v;
          });
        }
      });
    });

    const feedbackStatsToReturn: FeedbackStatsField = {};
    Object.entries(feedbackStats).forEach(([key, value]) => {
      const mean =
        value.avg.reduce((acc, val) => acc + val, 0) / value.avg.length;
      feedbackStatsToReturn[key] = {
        n: value.n,
        avg: mean,
        stdev: Math.sqrt(
          value.avg.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) /
            value.avg.length
        ),
        errors: value.errors,
        showErrorCount: true,
        show_feedback_arrow: value.show_feedback_arrow,
        values: value.values,
      };
    });
    return feedbackStatsToReturn;
  }, [
    runs,
    session,
    props.showSingleFeedbackKey,
    props.onSort,
    sessionFeedbackLoadStates,
  ]);

  return (
    <FeedbackChips
      {...props}
      feedbackStats={combinedFeedbackStats}
      feedbackSourceRuns={runs}
      expectedFeedbackKeys={
        isSessionFinished
          ? undefined
          : props.showSingleFeedbackKey
          ? [props.showSingleFeedbackKey]
          : props.expectedFeedbackKeys
      }
      onViewEvaluatorRun={props.onViewEvaluatorRun}
      hiddenFeedbackKeys={hiddenFeedbackKeys}
    />
  );
}

export const StatusMetricChips = ({
  numSuccess,
  numError,
  numPending,
  numInterrupted,
  simple,
}: {
  numSuccess: number;
  numError: number;
  numPending: number;
  numInterrupted: number;
  simple?: boolean;
}) => {
  return (
    <>
      {numSuccess > 0 && (
        <StatusChip
          status="success"
          className="px-1.5"
          size="xs"
          repetitions={numSuccess}
          simple={simple}
        />
      )}
      {numError > 0 && (
        <StatusChip
          status="error"
          className="px-1.5"
          size="xs"
          repetitions={numError}
          simple={simple}
        />
      )}
      {numPending > 0 && (
        <StatusChip
          status="pending"
          className="px-1.5"
          size="xs"
          repetitions={numPending}
          simple={simple}
        />
      )}
      {numInterrupted > 0 && (
        <StatusChip
          status="interrupted"
          className="px-1.5"
          size="xs"
          repetitions={numInterrupted}
          simple={simple}
        />
      )}
    </>
  );
};

export function AggregateMetricsChips({
  runs,
  hiddenMetrics,
}: {
  runs: RunSchema[];
  hiddenMetrics?: string[];
}) {
  const allLatencyValues = runs.reduce((acc, run) => {
    const { start_time, end_time } = run;
    const latency = end_time
      ? calculateLatencyNumber(start_time, end_time)
      : undefined;
    if (latency != null) {
      acc.push(latency);
    }
    return acc;
  }, [] as number[]);
  const avgLatency =
    allLatencyValues.reduce((acc, val) => acc + val, 0) /
    allLatencyValues.length;
  const numSuccess = runs.filter((run) => run.status === 'success').length;
  const numError = runs.filter((run) => run.status === 'error').length;
  const numPending = runs.filter((run) => run.status === 'pending').length;
  const numInterrupted = runs.filter(
    (run) => run.status === 'interrupted'
  ).length;
  const tokenStats = useMemo(() => {
    const stats = calculateRunStats(runs);
    return stats;
  }, [runs]);

  if (runs.length === 0) {
    return null;
  } else if (runs.length === 1) {
    return <MetricsChips run={runs[0]} hiddenMetrics={hiddenMetrics} />;
  }
  return (
    <>
      {!hiddenMetrics?.includes('latency') && (
        <LatencyChip latency={avgLatency} />
      )}
      {!hiddenMetrics?.includes('status') && (
        <StatusMetricChips
          numSuccess={numSuccess}
          numError={numError}
          numPending={numPending}
          numInterrupted={numInterrupted}
        />
      )}
      {!hiddenMetrics?.includes('tokens') && <RawTokensChip {...tokenStats} />}
      {!hiddenMetrics?.includes('cost') && <RawCostChip {...tokenStats} />}
    </>
  );
}

export function MetricsChips({
  run,
  hiddenMetrics,
}: {
  run: RunSchema;
  hiddenMetrics?: string[];
}) {
  return (
    <>
      {!hiddenMetrics?.includes('latency') && <RunLatencyChip run={run} />}
      {!hiddenMetrics?.includes('status') && (
        <Tooltip
          title={
            run.error ? (
              <div className="line-clamp-3">{run.error}</div>
            ) : undefined
          }
          sx={{
            padding: '12px',
            maxWidth: '500px',
            maxHeight: '300px',
            overflow: 'hidden',
          }}
        >
          <span>
            <RunStatusChip run={run} className="px-1.5" size="xs" />
          </span>
        </Tooltip>
      )}
      {!hiddenMetrics?.includes('tokens') && <TokensChip run={run} />}
      {!hiddenMetrics?.includes('cost') && <CostChip run={run} />}
    </>
  );
}
