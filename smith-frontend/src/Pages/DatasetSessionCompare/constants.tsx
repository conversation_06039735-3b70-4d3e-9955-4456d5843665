import { CodeLanguageType } from '@/components/Code/types';
import { <PERSON><PERSON><PERSON><PERSON>Field, SessionSchema } from '@/types/schema';

import { ExampleSchemaOrDraftWithEdited } from '../Playground/PlaygroundContext';

export const COMPARISON_VIEW_MAX_NUM_CATEGORIES_TO_SHOW = 2;
export const MAX_CHIPS = 3;

export const EXPERIMENT_OUTPUT_WIDTH_NUMBER = 495;
export const EXPERIMENT_OUTPUT_WIDTH = `${EXPERIMENT_OUTPUT_WIDTH_NUMBER}px`;

export const DEFAULT_COLUMN_WIDTHS = 300;
export const FEEDBACK_COLUMNS_DEFAULT_WIDTH = 120;

// Column indices
export const INPUT_COLUMN_IDX = 0;
export const ATTACHMENTS_COLUMN_IDX = 1;
export const REFERENCE_COLUMN_IDX = 2;
export const OUTPUT_START_COLUMN_IDX = 3;

export const TEXT_DISPLAY_MODE_SEARCH_PARAM = 'textDisplayMode';

export const METRICS_HIDDEN_LOCAL_STORAGE_KEY =
  'ls:dataset_session_compare_metrics_hidden';
export const FEEDBACK_HIDDEN_LOCAL_STORAGE_KEY =
  'ls:dataset_session_compare_feedback_hidden';
export const REFERENCE_INPUT_HIDDEN_LOCAL_STORAGE_KEY =
  'ls:dataset_session_compare_reference_input_hidden';
export const ATTACHMENTS_HIDDEN_LOCAL_STORAGE_KEY =
  'ls:dataset_session_compare_attachments_hidden';
export const REFERENCE_OUTPUT_HIDDEN_LOCAL_STORAGE_KEY =
  'ls:dataset_session_compare_reference_output_hidden';
export const SELECTED_CHARTS_LOCAL_STORAGE_KEY =
  'ls:dataset_compare_selected_charts';
export const ROW_DETAIL_PANE_LANGUAGE_LOCAL_STORAGE_KEY =
  'ls:dataset_session_compare_row_detail_pane_language';
export const EXPERIMENT_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY =
  'dataset_experiment_view_column_widths';
export const COMPARISON_PAGE_COLUMN_WIDTHS_LOCAL_STORAGE_KEY =
  'dataset_comparison_view_column_widths';
export const HIDDEN_FEEDBACK_COLUMNS_LOCAL_STORAGE_KEY =
  'dataset_session_compare_hidden_feedback_columns';
export const HIDDEN_METRICS_COLUMNS_LOCAL_STORAGE_KEY =
  'dataset_session_compare_hidden_metrics_columns';
export const HEATMAP_HIDDEN_LOCAL_STORAGE_KEY =
  'dataset_session_compare_heatmap_hidden';
export const TEXT_DISPLAY_MODE_LOCAL_STORAGE_KEY =
  'dataset_session_compare_text_display_mode';

export type DatasetSessionCompareBarChartSpec = {
  accessor: (row: SessionSchema) => number | null | undefined;
  name: string;
  isLatency: boolean;
  latencyType?: 'P50' | 'P99';
  isErrorRate: boolean;
  isFeedback: boolean;
  isSessionLevelFeedback: boolean;
  feedbackKey?: string;
  minimumValue?: number;
  maximumValue?: number;
  yAxisLabel: string;
};

export type DatasetSessionCompareSettings = {
  textDisplayMode: TextDisplayMode;
  setTextDisplayMode: (value: TextDisplayMode) => void;
  hideMetrics: boolean;
  setHideMetrics: React.Dispatch<React.SetStateAction<boolean>>;
  hideFeedback: boolean;
  setHideFeedback: React.Dispatch<React.SetStateAction<boolean>>;
  isReferenceInputHidden: boolean;
  setIsReferenceInputHidden: React.Dispatch<React.SetStateAction<boolean>>;
  isAttachmentsHidden: boolean;
  setIsAttachmentsHidden: React.Dispatch<React.SetStateAction<boolean>>;
  isReferenceOutputHidden: boolean;
  setIsReferenceOutputHidden: React.Dispatch<React.SetStateAction<boolean>>;
  language: CodeLanguageType;
  setLanguage: (value: CodeLanguageType) => void;
  selectedCharts: DatasetSessionCompareBarChartSpec[];
  setSelectedCharts: React.Dispatch<
    React.SetStateAction<DatasetSessionCompareBarChartSpec[]>
  >;
  barChartSpecs: DatasetSessionCompareBarChartSpec[] | null;
  canResetDefaults: boolean;
  onResetDefaults: () => void;
  hiddenFeedbackColumns: string[];
  setHiddenFeedbackColumns: React.Dispatch<React.SetStateAction<string[]>>;
  hiddenMetricsColumns: string[];
  setHiddenMetricsColumns: React.Dispatch<React.SetStateAction<string[]>>;
  isHeatmapVisible: boolean;
  setIsHeatmapVisible: React.Dispatch<React.SetStateAction<boolean>>;
};

export type DatasetSessionCompareDefaultsProps = {
  textDisplayMode?: TextDisplayMode;
  hideMetrics?: boolean;
  hideFeedback?: boolean;
  isReferenceInputHidden?: boolean;
  isReferenceOutputHidden?: boolean;
  language?: CodeLanguageType;
  hiddenMetricsColumns?: string[];
  hiddenFeedbackColumns?: string[];
  isHeatmapVisible?: boolean;
  isAttachmentsHidden?: boolean;
};

export enum TextDisplayMode {
  COMPACT = 'compact',
  FULL = 'full',
  DIFF = 'diff',
}

export type DatasetSessionCompareDefaults = {
  textDisplayMode: TextDisplayMode;
  hideMetrics: boolean;
  hideFeedback: boolean;
  isReferenceInputHidden: boolean;
  isReferenceOutputHidden: boolean;
  isAttachmentsHidden: boolean;
  language: CodeLanguageType;
  hiddenMetricsColumns: string[];
  hiddenFeedbackColumns: string[];
  isHeatmapVisible: boolean;
};

export const DEFAULT_SESSION_COMPARE_SETTINGS_VALUES: DatasetSessionCompareDefaults =
  {
    textDisplayMode: TextDisplayMode.COMPACT,
    hideMetrics: false,
    hideFeedback: false,
    isReferenceInputHidden: false,
    isReferenceOutputHidden: false,
    isAttachmentsHidden: false,
    language: 'json',
    hiddenMetricsColumns: ['status'],
    hiddenFeedbackColumns: [],
    isHeatmapVisible: true,
  };

export const METRICS_COLUMNS = {
  latency: {
    name: 'latency',
    minWidth: 120,
  },
  status: {
    name: 'status',
    minWidth: 125,
  },
  tokens: {
    name: 'tokens',
    minWidth: 80,
  },
  cost: {
    name: 'cost',
    minWidth: 80,
  },
};

export const GENERIC_FACETS: RunFacetField[] = [
  {
    query: 'lt(latency, "10s")',
    key: 'latency',
    value: 'Latency < 10s',
  },
  {
    query: 'gte(latency, "10s")',
    key: 'latency',
    value: 'Latency >= 10s',
  },
];

export const BLANK_EXAMPLE: ExampleSchemaOrDraftWithEdited = {
  id: '',
  name: '',
  created_at: '',
  modified_at: '',
  source_run_id: '',
  metadata: {},
  outputs: {},
  attachment_urls: {},
  inputs: {},
  edited: true,
};

export const OUTPUT_CELL_PRE_RUN_PLACEHOLDER_TEXT =
  'Press Start to run your prompt over the dataset';
