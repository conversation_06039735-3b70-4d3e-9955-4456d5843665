{"VERTEX_BUILTIN_BOUND_TOOLS": {"name": "ChatVertexAI", "inputs": {"messages": [[{"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "HumanMessage"], "kwargs": {"content": [{"type": "image_url", "image_url": {"url": "https://www.dupont.co.uk/content/dam/dupont/amer/us/en/personal-protection/public/documents/en/tychem-6000-f-accessory-tfpobasgy00_GB.pdf"}}], "type": "human"}}, {"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "HumanMessage"], "kwargs": {"content": "As a safety engineering expert with extensive experience in regulatory compliance, please extract and explain all relevant safety standards from the provided information.\n\nFirst, think step by step about:\n1. What type of product/system is being described\n2. Which industries or sectors this product belongs to\n3. What potential safety hazards might exist\n4. Which safety standards would typically apply to this type of product (considering regional and international standards)\n5. Only extract standards which are relevant and applicable to the product\n6. Explain all standards in the output\n\nThen, provide your comprehensive analysis within <standard descriptions></standard descriptions> tags.\nIf you can't find any safety standards in the provided information, simply respond with <standard descriptions>None</standard descriptions>\n\nAn example would be:\n<standard descriptions>\nEN 14126: This European standard specifies the requirements and test methods for protective clothing against infective agents. Compliance with EN 14126 indicates that the boot cover provides a barrier against penetration by biologically contaminated aerosols, liquids, and solid particles.\nEN 1149-1: This standard specifies requirements for electrostatic properties of protective clothing. The boot cover has an antistatic treatment on the inside, which helps to prevent the build-up of static electricity and reduce the risk of electrostatic discharge in potentially explosive atmospheres.\n</standard descriptions>\n\n\n\nRemember: Only extract relevant standards and explain each of them in the final output.\n", "type": "human"}}]]}, "inputs_preview": "human: As a safety engineering expert with extensive experience in regulatory compliance, please extract and explain all relevant safety standards ...", "run_type": "llm", "start_time": "2025-04-08T15:01:01.503930", "end_time": "2025-04-08T15:01:10.436061", "extra": {"batch_size": 1, "inputs_is_truthy": true, "invocation_params": {"_type": "vertexai", "candidate_count": 1, "max_output_tokens": 1024, "model_name": "gemini-2.0-flash-001", "stop": null, "temperature": 0.2, "tools": ["google_search {\n}\n"]}, "metadata": {"LANGSMITH_ENDPOINT": "https://api.smith.langchain.com", "LANGSMITH_PROJECT": "Descriptions", "LANGSMITH_TRACING": "true", "ls_max_tokens": 1024, "ls_model_name": "gemini-2.0-flash-001", "ls_model_type": "chat", "ls_provider": "google_vertexai", "ls_temperature": 0.2, "revision_id": "5967ff2-dirty"}, "options": {"stop": null}, "runtime": {"langchain_core_version": "0.3.50", "langchain_version": null, "library": "langsmith", "platform": "Windows-11-10.0.26100-SP0", "py_implementation": "CPython", "runtime": "python", "runtime_version": "3.12.4", "sdk": "langsmith-py", "sdk_version": "0.3.24"}}, "error": null, "execution_order": 1, "serialized": {"id": ["langchain", "chat_models", "vertexai", "ChatVertexAI"], "kwargs": {"default_metadata": [], "endpoint_version": "v1beta1", "location": "us-central1", "max_output_tokens": 1024, "max_retries": 2, "model_family": "2", "model_name": "gemini-2.0-flash-001", "n": 1, "perform_literal_eval_on_string_raw_content": true, "project": "masterdata-pipeline", "request_parallelism": 5, "temperature": 0.2}, "lc": 1, "name": "ChatVertexAI", "type": "constructor"}, "outputs": {"generations": [[{"text": "Okay, I will analyze the provided document for relevant safety standards applicable to the DuPont™ Tychem® 6000 F Accessory boot cover.\n\n1.  **Product Type:** The document describes a boot cover, specifically the DuPont™ Tychem® 6000 F Accessory. It's designed as an accessory to protective clothing.\n2.  **Industries/Sectors:** This product is likely used in industries where chemical splash protection, biological hazards, or controlled environments are concerns. Examples include chemical handling, pharmaceutical manufacturing, healthcare, and hazardous waste management.\n3.  **Potential Safety Hazards:** Potential hazards include chemical exposure to the feet and lower legs, exposure to infective agents, electrostatic discharge in explosive environments, and slips/falls.\n4.  **Applicable Safety Standards:** Considering the product type and potential hazards, the following safety standards appear relevant:\n\n<standard descriptions>\n**Regulation (EU) 2016/425:** This regulation lays down the requirements for the design and manufacture of personal protective equipment (PPE). The boot cover is certified according to this regulation, indicating that it meets the essential health and safety requirements for PPE.\n\n**Category III, Type PB [3-B]:** This refers to the PPE category under Regulation (EU) 2016/425. Category III PPE is designed to protect against high-risk hazards. Type PB [3-B] indicates partial body protection, specifically providing protection against liquid chemicals (Type 3).\n\n**EN 14126:** This European standard specifies the requirements and test methods for protective clothing against infective agents. Compliance with EN 14126 indicates that the boot cover provides a barrier against penetration by biologically contaminated aerosols, liquids, and solid particles.\n\n**EN 1149-1:** This standard specifies requirements for electrostatic properties of protective clothing. The boot cover has an antistatic treatment on the inside, which helps to prevent the build-up of static electricity and reduce the risk of electrostatic discharge in potentially explosive atmospheres.\n\n**EN 14605:** This standard specifies the performance requirements for chemical protective suits with liquid-tight (Type 3) or spray-tight (Type 4) connections between different parts of the suit. The \"Pass\" result indicates that the boot cover, when used as part of a larger protective system, meets the requirements for Type PB [3] partial body protection.\n</standard descriptions>", "generation_info": {"is_blocked": false, "safety_ratings": [], "citation_metadata": {"citations": [{"start_index": 2151, "end_index": 2275, "uri": "https://safecomfort-schutzanzug.de/en/safecomfort-chemieschutzanzug-modell-t/", "title": "", "license_": ""}]}, "usage_metadata": {"prompt_token_count": 4428, "candidates_token_count": 509, "total_token_count": 4937, "prompt_tokens_details": [{"modality": 1, "token_count": 300}, {"modality": 5, "token_count": 4128}], "candidates_tokens_details": [{"modality": 1, "token_count": 509}], "cached_content_token_count": 0, "cache_tokens_details": []}, "finish_reason": "STOP"}, "type": "ChatGeneration", "message": {"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "AIMessage"], "kwargs": {"content": "Okay, I will analyze the provided document for relevant safety standards applicable to the DuPont™ Tychem® 6000 F Accessory boot cover.\n\n1.  **Product Type:** The document describes a boot cover, specifically the DuPont™ Tychem® 6000 F Accessory. It's designed as an accessory to protective clothing.\n2.  **Industries/Sectors:** This product is likely used in industries where chemical splash protection, biological hazards, or controlled environments are concerns. Examples include chemical handling, pharmaceutical manufacturing, healthcare, and hazardous waste management.\n3.  **Potential Safety Hazards:** Potential hazards include chemical exposure to the feet and lower legs, exposure to infective agents, electrostatic discharge in explosive environments, and slips/falls.\n4.  **Applicable Safety Standards:** Considering the product type and potential hazards, the following safety standards appear relevant:\n\n<standard descriptions>\n**Regulation (EU) 2016/425:** This regulation lays down the requirements for the design and manufacture of personal protective equipment (PPE). The boot cover is certified according to this regulation, indicating that it meets the essential health and safety requirements for PPE.\n\n**Category III, Type PB [3-B]:** This refers to the PPE category under Regulation (EU) 2016/425. Category III PPE is designed to protect against high-risk hazards. Type PB [3-B] indicates partial body protection, specifically providing protection against liquid chemicals (Type 3).\n\n**EN 14126:** This European standard specifies the requirements and test methods for protective clothing against infective agents. Compliance with EN 14126 indicates that the boot cover provides a barrier against penetration by biologically contaminated aerosols, liquids, and solid particles.\n\n**EN 1149-1:** This standard specifies requirements for electrostatic properties of protective clothing. The boot cover has an antistatic treatment on the inside, which helps to prevent the build-up of static electricity and reduce the risk of electrostatic discharge in potentially explosive atmospheres.\n\n**EN 14605:** This standard specifies the performance requirements for chemical protective suits with liquid-tight (Type 3) or spray-tight (Type 4) connections between different parts of the suit. The \"Pass\" result indicates that the boot cover, when used as part of a larger protective system, meets the requirements for Type PB [3] partial body protection.\n</standard descriptions>", "response_metadata": {"is_blocked": false, "safety_ratings": [], "citation_metadata": {"citations": [{"start_index": 2151, "end_index": 2275, "uri": "https://safecomfort-schutzanzug.de/en/safecomfort-chemieschutzanzug-modell-t/", "title": "", "license_": ""}]}, "usage_metadata": {"prompt_token_count": 4428, "candidates_token_count": 509, "total_token_count": 4937, "prompt_tokens_details": [{"modality": 1, "token_count": 300}, {"modality": 5, "token_count": 4128}], "candidates_tokens_details": [{"modality": 1, "token_count": 509}], "cached_content_token_count": 0, "cache_tokens_details": []}, "finish_reason": "STOP", "model_name": "gemini-2.0-flash-001"}, "type": "ai", "id": "run-24a07b39-b3da-4b2a-906b-c03aab4916b4-0", "usage_metadata": {"input_tokens": 4428, "output_tokens": 509, "total_tokens": 4937}, "tool_calls": [], "invalid_tool_calls": []}}}]], "llm_output": null, "run": null, "type": "LLMResult"}, "outputs_preview": "ai: Okay, I will analyze the provided document for relevant safety standards applicable to the DuPont™ Tychem® 6000 F Accessory boot cover.\n\n1.  **...", "parent_run_id": "a1f3bb6b-3212-4247-8d6d-11871b0233d2", "manifest_id": null, "manifest_s3_id": null, "events": [{"name": "start", "time": "2025-04-08T15:01:01.503930+00:00"}, {"name": "end", "time": "2025-04-08T15:01:10.436061+00:00"}], "tags": ["seq:step:2"], "inputs_s3_urls": null, "outputs_s3_urls": null, "s3_urls": null, "trace_id": "c6573fdf-580f-4479-9df9-8720f4c1c09a", "dotted_order": "20250408T150101386253Zc6573fdf-580f-4479-9df9-8720f4c1c09a.20250408T150101500615Z98bf02f5-97fb-48fd-bc29-32efaa6ac896.20250408T150101501177Z14b07318-805c-4cef-9f03-2b5853eab450.20250408T150101501738Za1f3bb6b-3212-4247-8d6d-11871b0233d2.20250408T150101503930Z24a07b39-b3da-4b2a-906b-c03aab4916b4", "id": "24a07b39-b3da-4b2a-906b-c03aab4916b4", "status": "success", "child_run_ids": null, "direct_child_run_ids": null, "parent_run_ids": ["c6573fdf-580f-4479-9df9-8720f4c1c09a", "98bf02f5-97fb-48fd-bc29-32efaa6ac896", "14b07318-805c-4cef-9f03-2b5853eab450", "a1f3bb6b-3212-4247-8d6d-11871b0233d2"], "feedback_stats": null, "reference_example_id": null, "total_tokens": 4937, "prompt_tokens": 4428, "completion_tokens": 509, "total_cost": null, "prompt_cost": null, "completion_cost": null, "price_model_id": null, "first_token_time": null}, "OPENAI_FORMAT_MULTIMODAL_CONTENT": {"name": "ChatOpenAI", "inputs": {"messages": [{"role": "user", "content": [{"type": "file", "file": {"filename": "dummy.pdf", "file_data": "data:application/pdf;base64,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"}}, {"type": "text", "text": "What is in the above PDF? Here it is again:"}]}], "model": "gpt-4.1"}, "inputs_preview": null, "run_type": "llm", "start_time": "2025-04-21T20:03:10.306832", "end_time": "2025-04-21T20:03:12.493425", "extra": {"metadata": {"LANGSMITH_ENDPOINT": "http://localhost:1980/api/v1", "LANGSMITH_TRACING": "true", "ls_method": "traceable", "ls_model_name": "gpt-4.1", "ls_model_type": "chat", "ls_provider": "openai", "revision_id": "v0.3.19-32-g48edbfd2-dirty", "ls_run_depth": 0}, "runtime": {"langchain_core_version": "0.3.49", "langchain_version": null, "library": "langsmith", "platform": "macOS-15.3.2-arm64-arm-64bit", "py_implementation": "CPython", "runtime": "python", "runtime_version": "3.11.3", "sdk": "langsmith-py", "sdk_version": "0.3.31"}}, "error": null, "execution_order": 1, "serialized": null, "outputs": {"id": "chatcmpl-BOreBOplZ3vTxIah3wsRxopjv7w2d", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "The PDF file you provided contains only the text \"Dummy PDF file\" on a single page (page 1). There are no images, charts, tables, diagrams, or any additional content present in the document. It appears to be a placeholder or example file.", "refusal": null, "role": "assistant", "annotations": [], "audio": null, "function_call": null, "tool_calls": null}}], "created": **********, "model": "gpt-4.1-2025-04-14", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_beec22d258", "usage_metadata": {"input_tokens": 457, "output_tokens": 54, "total_tokens": 511, "input_token_details": {"audio": 0, "cache_read": 0}, "output_token_details": {"audio": 0, "reasoning": 0}}}, "outputs_preview": null, "parent_run_id": null, "manifest_id": null, "manifest_s3_id": null, "events": [], "tags": [], "inputs_s3_urls": {"ROOT": {"presigned_url": "http://example.com/public/download", "s3_url": "ttl_l/inputs/"}}, "outputs_s3_urls": null, "s3_urls": null, "trace_id": "ecffa43e-f4d1-4bfd-92dc-5c031a223380", "dotted_order": "20250421T200310306832Zecffa43e-f4d1-4bfd-92dc-5c031a223380", "id": "ecffa43e-f4d1-4bfd-92dc-5c031a223380", "status": "success", "child_run_ids": null, "direct_child_run_ids": null, "parent_run_ids": [], "feedback_stats": null, "reference_example_id": null, "total_tokens": 511, "prompt_tokens": 457, "completion_tokens": 54, "total_cost": 0.001346, "prompt_cost": 0.000914, "completion_cost": 0.000432, "price_model_id": "e8a088bd-e732-4636-93ab-98b30b0ab04d", "first_token_time": null, "session_id": "573c0a74-ec35-4e19-91cf-a95646f3fb01", "app_path": "/o/36bc5dc2-4444-4162-82c2-cde7fa94ed6a/projects/p/573c0a74-ec35-4e19-91cf-a95646f3fb01/r/ecffa43e-f4d1-4bfd-92dc-5c031a223380?trace_id=ecffa43e-f4d1-4bfd-92dc-5c031a223380&start_time=2025-04-21T20:03:10.306832", "last_queued_at": "2025-04-24T09:52:11.388775", "in_dataset": true, "share_token": null, "trace_tier": "longlived", "trace_first_received_at": "2025-04-21T20:03:11.149574", "ttl_seconds": 34560000, "trace_upgrade": true, "reference_dataset_id": null, "thread_id": null}, "LANGCHAIN_CHAT_ANTHROPIC_MULTIMODAL_CONTENT": {"name": "ChatAnthropic", "inputs": {"messages": [[{"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "HumanMessage"], "kwargs": {"content": [{"type": "text", "text": "Describe the document:"}, {"type": "file", "source_type": "base64", "data": "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", "mime_type": "application/pdf"}], "type": "human"}}]]}, "inputs_preview": null, "run_type": "llm", "start_time": "2025-04-22T00:48:02.645847", "end_time": "2025-04-22T00:48:08.698947", "extra": {"batch_size": 1, "inputs_is_truthy": true, "invocation_params": {"_type": "anthropic-chat", "default_request_timeout": null, "max_retries": 2, "max_tokens": 1024, "model": "claude-3-5-sonnet-latest", "model_kwargs": {}, "stop": null, "streaming": false, "temperature": null, "thinking": null, "top_k": null, "top_p": null}, "metadata": {"LANGSMITH_ENDPOINT": "http://localhost:1980/api/v1", "LANGSMITH_PROJECT": "openevals", "LANGSMITH_TRACING": "true", "ls_max_tokens": 1024, "ls_model_name": "claude-3-5-sonnet-latest", "ls_model_type": "chat", "ls_provider": "anthropic", "ls_temperature": null, "revision_id": "f287232-dirty", "ls_run_depth": 0}, "options": {"stop": null}, "runtime": {"langchain_core_version": "0.3.53", "langchain_version": "0.3.23", "library": "langsmith", "platform": "macOS-15.3.2-arm64-arm-64bit", "py_implementation": "CPython", "runtime": "python", "runtime_version": "3.11.3", "sdk": "langsmith-py", "sdk_version": "0.3.32"}}, "error": null, "execution_order": 1, "serialized": {"id": ["langchain", "chat_models", "anthropic", "ChatAnthropic"], "kwargs": {"anthropic_api_key": {"id": ["ANTHROPIC_API_KEY"], "lc": 1, "type": "secret"}, "anthropic_api_url": "https://api.anthropic.com", "max_retries": 2, "max_tokens": 1024, "model": "claude-3-5-sonnet-latest", "stream_usage": true}, "lc": 1, "name": "ChatAnthropic", "type": "constructor"}, "outputs": {"generations": [[{"text": "This appears to be a sample PDF document containing Lorem ipsum placeholder text. The document begins with a header \"Sample PDF\" followed by the line \"This is a simple PDF file. Fun fun fun.\"\n\nThe rest of the document consists of four paragraphs of standard Lorem ipsum text, which is commonly used as placeholder text in design and publishing. The text is formatted in a clean, readable layout with consistent paragraph spacing.\n\nThe document appears to be a single page with black text on a white background, using what seems to be a standard serif font. The layout is simple and minimal, with uniform margins and spacing throughout.\n\nThis kind of document is typically used for testing layouts, fonts, or document formatting without the distraction of meaningful content.", "generation_info": null, "type": "ChatGeneration", "message": {"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "AIMessage"], "kwargs": {"content": "This appears to be a sample PDF document containing Lorem ipsum placeholder text. The document begins with a header \"Sample PDF\" followed by the line \"This is a simple PDF file. Fun fun fun.\"\n\nThe rest of the document consists of four paragraphs of standard Lorem ipsum text, which is commonly used as placeholder text in design and publishing. The text is formatted in a clean, readable layout with consistent paragraph spacing.\n\nThe document appears to be a single page with black text on a white background, using what seems to be a standard serif font. The layout is simple and minimal, with uniform margins and spacing throughout.\n\nThis kind of document is typically used for testing layouts, fonts, or document formatting without the distraction of meaningful content.", "response_metadata": {"id": "msg_01Y73cRmH5krDMkpfhpdWb6x", "model": "claude-3-5-sonnet-20241022", "stop_reason": "end_turn", "stop_sequence": null, "usage": {"cache_creation_input_tokens": 0, "cache_read_input_tokens": 0, "input_tokens": 2677, "output_tokens": 154}, "model_name": "claude-3-5-sonnet-20241022"}, "type": "ai", "id": "run-ed501d83-bbbf-4c42-903f-1930d28de876-0", "usage_metadata": {"input_tokens": 2677, "output_tokens": 154, "total_tokens": 2831, "input_token_details": {"cache_read": 0, "cache_creation": 0}}, "tool_calls": [], "invalid_tool_calls": []}}}]], "llm_output": {"id": "msg_01Y73cRmH5krDMkpfhpdWb6x", "model": "claude-3-5-sonnet-20241022", "stop_reason": "end_turn", "stop_sequence": null, "usage": {"cache_creation_input_tokens": 0, "cache_read_input_tokens": 0, "input_tokens": 2677, "output_tokens": 154}, "model_name": "claude-3-5-sonnet-20241022"}, "run": null, "type": "LLMResult"}, "outputs_preview": null, "parent_run_id": null, "manifest_id": null, "manifest_s3_id": null, "events": [{"name": "start", "time": "2025-04-22T00:48:02.645847+00:00"}, {"name": "end", "time": "2025-04-22T00:48:08.698947+00:00"}], "tags": [], "inputs_s3_urls": null, "outputs_s3_urls": null, "s3_urls": null, "trace_id": "ed501d83-bbbf-4c42-903f-1930d28de876", "dotted_order": "20250422T004802645847Zed501d83-bbbf-4c42-903f-1930d28de876", "id": "ed501d83-bbbf-4c42-903f-1930d28de876", "status": "success", "child_run_ids": null, "direct_child_run_ids": null, "parent_run_ids": [], "feedback_stats": null, "reference_example_id": null, "total_tokens": 2831, "prompt_tokens": 2677, "completion_tokens": 154, "total_cost": 0.010341, "prompt_cost": 0.008031, "completion_cost": 0.00231, "price_model_id": "0f903383-4c43-4ea8-b925-16a950b403e0", "first_token_time": null, "session_id": "22454017-d0d8-4de7-a128-249cd65cc7d7", "app_path": "/o/36bc5dc2-4444-4162-82c2-cde7fa94ed6a/projects/p/22454017-d0d8-4de7-a128-249cd65cc7d7/r/ed501d83-bbbf-4c42-903f-1930d28de876?trace_id=ed501d83-bbbf-4c42-903f-1930d28de876&start_time=2025-04-22T00:48:02.645847", "last_queued_at": "2025-04-24T09:51:18.193502", "in_dataset": true, "share_token": "7da7203d-24ae-4d63-86f7-fe0e26b4844a", "trace_tier": "longlived", "trace_first_received_at": "2025-04-22T00:48:03.745358", "ttl_seconds": 34560000, "trace_upgrade": true, "reference_dataset_id": null, "thread_id": null}, "AI_SDK_WITH_IMAGES": {"name": "openai.chat", "inputs": {"messages": [{"type": "human", "data": {"content": [{"type": "text", "text": "What is this?"}, {"type": "image_url", "image_url": {"type": "<PERSON><PERSON><PERSON>", "data": [137]}}]}}]}, "inputs_preview": null, "run_type": "llm", "start_time": "2025-05-02T02:39:35.958000", "end_time": "2025-05-02T02:39:36.835000", "extra": {"batch_size": 1, "invocation_params": {}, "metadata": {"LANGSMITH_TRACING": "true", "ai.operationId": "ai.generateText.doGenerate", "ls_model_name": "gpt-4.1-mini", "ls_model_type": "chat", "ls_provider": "openai", "ls_run_depth": 4}, "runtime": {"library": "langsmith", "runtime": "node", "sdk": "langsmith-js", "sdk_version": "0.3.24"}}, "error": null, "execution_order": 1, "serialized": null, "outputs": {"llm_output": {"type": "ai", "data": {"content": "This is an emoji of a parrot. It depicts a colorful bird with green, yellow, and orange feathers, standing on its feet. Parrots are known for their bright plumage and ability to mimic sounds."}, "token_usage": {"completion_tokens": 44, "prompt_tokens": 116}}}, "outputs_preview": null, "parent_run_id": "52247243-4e86-5554-9816-6637e62ca0a7", "manifest_id": null, "manifest_s3_id": null, "events": [], "tags": [], "inputs_s3_urls": null, "outputs_s3_urls": null, "s3_urls": null, "trace_id": "9bdc7e75-c2c2-4fc9-ac9b-e0a7e0804a7d", "dotted_order": "20250502T023934383001Z9bdc7e75-c2c2-4fc9-ac9b-e0a7e0804a7d.20250502T023934386000Z9ebf63b5-7e11-5565-9703-0c21859dd7f7.20250502T023935944003Zffad74d2-945d-5dc6-8580-709019486f56.20250502T023935954000Z52247243-4e86-5554-9816-6637e62ca0a7.20250502T023935958000Z01225199-3c00-5f24-b3b0-acb1926e6525", "id": "01225199-3c00-5f24-b3b0-acb1926e6525", "status": "success", "child_run_ids": null, "direct_child_run_ids": null, "parent_run_ids": ["9bdc7e75-c2c2-4fc9-ac9b-e0a7e0804a7d", "9ebf63b5-7e11-5565-9703-0c21859dd7f7", "ffad74d2-945d-5dc6-8580-709019486f56", "52247243-4e86-5554-9816-6637e62ca0a7"], "feedback_stats": null, "reference_example_id": null, "total_tokens": 160, "prompt_tokens": 116, "completion_tokens": 44, "total_cost": 0.0001168, "prompt_cost": 4.64e-05, "completion_cost": 7.04e-05, "price_model_id": "42449e38-db5d-40a9-9b27-5ec0e16ab55b", "first_token_time": null, "session_id": "573c0a74-ec35-4e19-91cf-a95646f3fb01", "app_path": "/o/36bc5dc2-4444-4162-82c2-cde7fa94ed6a/projects/p/573c0a74-ec35-4e19-91cf-a95646f3fb01/r/01225199-3c00-5f24-b3b0-acb1926e6525?trace_id=9bdc7e75-c2c2-4fc9-ac9b-e0a7e0804a7d&start_time=2025-05-02T02:39:34.383001", "last_queued_at": null, "in_dataset": false, "share_token": null, "trace_tier": "shortlived", "trace_first_received_at": "2025-05-02T02:39:34.774377", "ttl_seconds": 1209600, "trace_upgrade": false, "reference_dataset_id": null, "thread_id": null}, "PLAYGROUND_RUN_WITH_AUDIO_GEMINI": {"name": "ChatGoogleGenerativeAI", "inputs": {"messages": [[{"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "SystemMessage"], "kwargs": {"content": "You are a chatbot.", "type": "system"}}, {"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "HumanMessage"], "kwargs": {"content": [{"type": "text", "text": "\n"}, {"type": "input_audio", "input_audio": {"data": "UklGRub/DwBXQVZFZm10IBAAAAABAAIARKwAABCxAgAEABAAZGF0YTj/DwAAAAAAAAAAAAAAAAAAAAAAAQD///7/AgADAP7//P8CAAMA/f8AAAMA/v/+/wMAAgD9//7/AQACAAEA", "format": "wav"}}], "type": "human"}}]]}, "inputs_preview": null, "run_type": "llm", "start_time": "2025-05-07T18:06:25.775016", "end_time": "2025-05-07T18:06:26.757149", "extra": {"invocation_params": {"model": "models/gemini-2.0-flash", "temperature": 1, "top_k": null, "n": 1, "safety_settings": null, "response_modalities": null, "thinking_budget": null, "_type": "chat-google-generative-ai", "stop": null}, "options": {"stop": null}, "batch_size": 1, "metadata": {"ls_provider": "google_genai", "ls_model_name": "gemini-2.0-flash", "ls_model_type": "chat", "ls_temperature": 1, "ls_run_depth": 1}}, "error": null, "execution_order": 1, "serialized": {"lc": 1, "type": "constructor", "id": ["langchain_google_genai", "chat_models", "ChatGoogleGenerativeAI"], "kwargs": {"model": "models/gemini-2.0-flash", "google_api_key": {"lc": 1, "type": "secret", "id": ["GOOGLE_API_KEY"]}, "temperature": 1, "top_p": 1, "n": 1, "max_retries": 6, "default_metadata": []}, "name": "ChatGoogleGenerativeAI"}, "outputs": {"generations": [[{"text": "This sounds like some quirky electronic music with a playful melody. It's almost like background music for a lighthearted video game! Very creative.", "generation_info": {"safety_ratings": [], "finish_reason": "STOP", "model_name": "gemini-2.0-flash"}, "type": "ChatGenerationChunk", "message": {"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "AIMessageChunk"], "kwargs": {"content": "This sounds like some quirky electronic music with a playful melody. It's almost like background music for a lighthearted video game! Very creative.", "response_metadata": {"safety_ratings": [], "finish_reason": "STOP", "model_name": "gemini-2.0-flash"}, "type": "AIMessageChunk", "id": "run-740c3279-c0bc-433e-b232-584bfb25b37f", "usage_metadata": {"input_tokens": 131, "output_tokens": 29, "total_tokens": 160, "input_token_details": {"cache_read": 0}}, "tool_calls": [], "invalid_tool_calls": []}}}]], "llm_output": null, "run": null, "type": "LLMResult"}, "outputs_preview": null, "parent_run_id": "96b9a842-0709-4779-85fb-7f3bffeac5c8", "manifest_id": null, "manifest_s3_id": null, "events": [{"name": "start", "time": "2025-05-07T18:06:25.775016+00:00"}, {"name": "new_token", "time": "2025-05-07T18:06:26.672086+00:00", "kwargs": {"token": "This"}}, {"name": "new_token", "time": "2025-05-07T18:06:26.744407+00:00", "kwargs": {"token": " sounds like some"}}, {"name": "new_token", "time": "2025-05-07T18:06:26.746262+00:00", "kwargs": {"token": " quirky electronic music with a playful melody. It's almost like background music for a light"}}, {"name": "new_token", "time": "2025-05-07T18:06:26.748853+00:00", "kwargs": {"token": "hearted video game! Very creative."}}, {"name": "end", "time": "2025-05-07T18:06:26.757149+00:00"}], "tags": ["seq:step:2"], "inputs_s3_urls": null, "outputs_s3_urls": null, "s3_urls": null, "trace_id": "96b9a842-0709-4779-85fb-7f3bffeac5c8", "dotted_order": "20250507T180625756369Z96b9a842-0709-4779-85fb-7f3bffeac5c8.20250507T180625775016Z740c3279-c0bc-433e-b232-584bfb25b37f", "id": "740c3279-c0bc-433e-b232-584bfb25b37f", "status": "success", "child_run_ids": null, "direct_child_run_ids": null, "parent_run_ids": ["96b9a842-0709-4779-85fb-7f3bffeac5c8"], "feedback_stats": null, "reference_example_id": null, "total_tokens": 160, "prompt_tokens": 131, "completion_tokens": 29, "total_cost": 2.47e-05, "prompt_cost": 1.31e-05, "completion_cost": 1.16e-05, "price_model_id": "9bc138c7-49c9-4ec2-9433-b7302d78896f", "first_token_time": "2025-05-07T18:06:26.672086", "session_id": "845be3f1-d0fa-46ca-bf13-6026898b4770", "app_path": "/o/369e024c-f8fd-58c3-b6de-3da1b9ad9a64/projects/p/845be3f1-d0fa-46ca-bf13-6026898b4770/r/740c3279-c0bc-433e-b232-584bfb25b37f?trace_id=96b9a842-0709-4779-85fb-7f3bffeac5c8&start_time=2025-05-07T18:06:25.756369", "last_queued_at": null, "in_dataset": false, "share_token": null, "trace_tier": "longlived", "trace_first_received_at": "2025-05-07T18:06:27.613487", "ttl_seconds": 34560000, "trace_upgrade": false, "reference_dataset_id": null, "thread_id": null}, "CHAT_PROMPT_TEMPLATE_WITH_AUDIO_VARIABLE": {"name": "ChatPromptTemplate", "inputs": {"asdf": "UklGRub/DwBXQVZFZm10IBAAAAABAAIARKwAABCxAgAEABAAZGF0YTj/DwAAAAAAAAAAAAAAAAAAAAAAAQD///7/AgADAP7//P8CAAMA/f8AAAMA/v/+/wMAAgD9//7/AQACAAEA/f/+/wQAAgD8////AwAAAP//AAAAAAEAAAD+/wAAAwD///3/AwACAP3/AAACAP7//v8DAAIA/f///wMAAAD+/wAAAAAAAAEAAAD//wAAAAABAAIA/f/9/wQAAgD9////AgAAAP//AAAAAAIAAAD9/wEAAgD///7/AAACAAEA/////wEAAQD/////AAAAAAEAAAD+/wEAAgD+////AwABAP3/AAABAP//AAAAAP//AQACAP////8CAP///f8BAAQAAAD7////BQACAPz//f8CAAMAAAD9/wAABAD///z/AgACAP3/AAADAP///v8BAAEAAQD///3/AQADAP///v8BAAEA//8AAAEAAAD/////AQABAP7/AAACAP////8BAAEAAAD///////8CAAIA/f/+/wIAAgAAAP///////wEAAgAAAP///v///wMAAwD9//z/AwAEAP7//f8BAAIA/////wEAAQAAAP////8AAAIAAAD9/wEAAwD///7/AQABAP////8AAAIAAQD+////AQAAAAAAAQD/////AgAxOA==", "question": "What is this?"}, "inputs_preview": null, "run_type": "prompt", "start_time": "2025-05-07T00:48:30.249854", "end_time": "2025-05-07T00:48:30.254382", "extra": null, "error": null, "execution_order": 1, "serialized": {"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"], "kwargs": {"input_variables": ["asdf", "question"], "messages": [{"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "chat", "SystemMessagePromptTemplate"], "kwargs": {"prompt": {"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "prompt", "PromptTemplate"], "kwargs": {"input_variables": [], "template": "You are a chatbot.", "template_format": "mustache"}, "name": "PromptTemplate"}}}, {"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "chat", "HumanMessagePromptTemplate"], "kwargs": {"prompt": [{"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "prompt", "PromptTemplate"], "kwargs": {"input_variables": ["question"], "template": "{{question}}", "template_format": "mustache"}, "name": "PromptTemplate"}, {"lc": 1, "type": "constructor", "id": ["langchain_core", "prompts", "dict", "DictPromptTemplate"], "kwargs": {"template": {"type": "input_audio", "input_audio": {"data": "{{asdf}}", "format": "wav"}}, "template_format": "mustache"}, "name": "DictPromptTemplate"}]}}]}, "name": "ChatPromptTemplate"}, "outputs": {"messages": [{"content": "You are a chatbot.", "additional_kwargs": {}, "response_metadata": {}, "type": "system", "name": null, "id": null}, {"content": [{"type": "text", "text": "What is this?"}, {"type": "input_audio", "input_audio": {"data": "UklGRub/DwBXQVZFZm10IBAAAAABAAIARKwAABCxAgAEABAAZGF0YTj/DwAAAAAAAAAAAAAAAAAAAAAAAQD///7/AgADAP7//P8CAAMA/f8AAAMA/v/+/wMAAgD9//7/AQACAAEA/f/+/wQAAgD8////AwAAAP//AAAAAAEAAAD+/wAAAwD///3/AwACAP3/AAACAP7//v8DAAIA/f///wMAAAD+/wAAAAAAAAEAAAD//wAAAAABAAIA/f/9/wQAAgD9////AgAAAP//AAAAAAIAAAD9/wEAAgD///7/AAACAAEA/////wEAAQD/////AAAAAAEAAAD+/wEAAgD+////AwABAP3/AAABAP//AAAAAP//AQACAP////8CAP///f8BAAQAAAD7////BQACAPz//f8CAAMAAAD9/wAABAD///z/AgACAP3/AAADAP///v8BAAEAAQD///3/AQADAP///v8BAAEAA==", "format": "wav"}}], "additional_kwargs": {}, "response_metadata": {}, "type": "human", "name": null, "id": null}]}, "outputs_preview": null, "parent_run_id": "86a24e44-2654-4b81-9282-7168205a4898", "manifest_id": null, "manifest_s3_id": null, "events": [{"name": "start", "time": "2025-05-07T00:48:30.249854+00:00"}, {"name": "end", "time": "2025-05-07T00:48:30.254382+00:00"}], "tags": [], "inputs_s3_urls": null, "outputs_s3_urls": null, "s3_urls": null, "trace_id": "db932b4f-cec5-48eb-88eb-5d2a634ef1d4", "dotted_order": "20250507T004830244489Zdb932b4f-cec5-48eb-88eb-5d2a634ef1d4.20250507T004830246417Z86a24e44-2654-4b81-9282-7168205a4898.20250507T004830249854Z26ea8828-762f-408d-8abf-e0e1cba2aab4", "id": "26ea8828-762f-408d-8abf-e0e1cba2aab4", "status": "success", "child_run_ids": null, "direct_child_run_ids": null, "parent_run_ids": ["db932b4f-cec5-48eb-88eb-5d2a634ef1d4", "86a24e44-2654-4b81-9282-7168205a4898"], "feedback_stats": null, "reference_example_id": null, "total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0, "total_cost": null, "prompt_cost": null, "completion_cost": null, "price_model_id": null, "first_token_time": null, "session_id": "086180e8-bd7d-4bc0-acf0-ef649fc44057", "app_path": "/o/d6684905-655c-429b-bd14-ba302d94c03b/projects/p/086180e8-bd7d-4bc0-acf0-ef649fc44057/r/26ea8828-762f-408d-8abf-e0e1cba2aab4?trace_id=db932b4f-cec5-48eb-88eb-5d2a634ef1d4&start_time=2025-05-07T00:48:30.244489", "last_queued_at": null, "in_dataset": false, "share_token": null, "trace_tier": "longlived", "trace_first_received_at": "2025-05-07T00:48:31.682068", "ttl_seconds": 34560000, "trace_upgrade": false, "reference_dataset_id": null, "thread_id": null}, "STRUCTURED_PROMPT": {"name": "StructuredPrompt", "inputs": null, "inputs_preview": null, "run_type": "prompt", "start_time": "2025-03-19T17:12:25.219755", "end_time": "2025-03-19T17:12:25.221439", "extra": null, "error": null, "execution_order": 1, "serialized": {"lc": 1, "type": "constructor", "id": ["langchain_core", "prompts", "structured", "StructuredPrompt"], "kwargs": {"input_variables": [], "messages": [{"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "chat", "HumanMessagePromptTemplate"], "kwargs": {"prompt": {"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "prompt", "PromptTemplate"], "kwargs": {"input_variables": [], "template": "how are ya", "template_format": "f-string"}, "name": "PromptTemplate"}}}], "schema_": {"description": "format to respond in", "properties": {"response": {"type": "string"}}, "required": ["response"], "title": "Format", "type": "object"}, "structured_output_kwargs": {"method": "function_calling"}}, "name": "StructuredPrompt"}, "outputs": {"output": {"messages": [{"content": "how are ya", "additional_kwargs": {}, "response_metadata": {}, "type": "human", "name": null, "id": null}]}}, "outputs_preview": "{\"messages\":[{\"content\":\"how are ya\",\"additional_kwargs\":{},\"response_metadata\":{},\"type\":\"human\",\"name\":null,\"id\":null}]}", "parent_run_id": null, "manifest_id": null, "manifest_s3_id": null, "events": [{"name": "start", "time": "2025-03-19T17:12:25.219755+00:00"}, {"name": "end", "time": "2025-03-19T17:12:25.221439+00:00"}], "tags": ["seq:step:1"], "inputs_s3_urls": null, "outputs_s3_urls": null, "s3_urls": null, "trace_id": "283122c5-667d-4e5a-b2d0-8792b9a5ee40", "dotted_order": "20250319T171225217514Z283122c5-667d-4e5a-b2d0-8792b9a5ee40.20250319T171225219755Z095c4207-7f74-496f-89f5-8f151f91cb95", "id": "095c4207-7f74-496f-89f5-8f151f91cb95", "status": "success", "child_run_ids": null, "direct_child_run_ids": null, "parent_run_ids": [], "feedback_stats": null, "reference_example_id": null, "total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0, "total_cost": null, "prompt_cost": null, "completion_cost": null, "price_model_id": null, "first_token_time": null}}