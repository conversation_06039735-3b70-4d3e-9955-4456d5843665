import '@testing-library/jest-dom';
import { render } from '@testing-library/react';

import { MemoryRouter } from 'react-router-dom';
import { describe, expect, test, vi } from 'vitest';

import { RunDetails } from '@/Pages/Run/components/RunDetails';
import { RunSchema } from '@/types/schema';

import RUNS from './data/runs.json';
import { MockOrgIdProvider, MockStoredResourceTagsProvider } from './utils';

// Mock Worker since it's not available in test environment
vi.mock('../workers/foreignDataParser.ts', () => ({
  default: vi.fn(() => ({
    postMessage: vi.fn(),
    terminate: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
    onmessage: null,
    onmessageerror: null,
    onerror: null,
  })),
}));

// Mock global Worker
global.Worker = class Worker {
  postMessage = vi.fn();
  terminate = vi.fn();
  addEventListener = vi.fn();
  removeEventListener = vi.fn();
  dispatchEvent = vi.fn();
  onmessage = null;
  onmessageerror = null;
  onerror = null;

  constructor(_scriptURL: string | URL, _options?: WorkerOptions) {}
};

const renderRunDetails = (run: RunSchema) => {
  return render(
    <MemoryRouter>
      <MockOrgIdProvider>
        <MockStoredResourceTagsProvider>
          <RunDetails
            run={run}
            onNavigate={vi.fn()}
            searchFilter={''}
            onSearchFilterChange={vi.fn()}
            filteredRuns={[run as unknown as RunSchema]}
            isRunLoading={false}
            isChildRunsLoading={false}
            isFilteredRunsLoading={false}
          />
        </MockStoredResourceTagsProvider>
      </MockOrgIdProvider>
    </MemoryRouter>
  );
};

describe('RunDetails', () => {
  test.each(Object.entries(RUNS))(
    'No errors thrown in playground render for run: %s',
    (_, run) => {
      renderRunDetails(run as unknown as RunSchema);
    }
  );

  test.each([
    'LANGCHAIN_CHAT_ANTHROPIC_MULTIMODAL_CONTENT',
    'OPENAI_FORMAT_MULTIMODAL_CONTENT',
  ])('Multimodal rendering for run: %s', (runName) => {
    const run = RUNS[runName as keyof typeof RUNS];
    const { container } = renderRunDetails(run as unknown as RunSchema);
    const iframes = [...container.querySelectorAll('iframe')];
    const iframeWithPdf = iframes.find((iframe) =>
      iframe.src.startsWith('data:application/pdf;base64,')
    );
    expect(iframeWithPdf).toBeDefined();
  });

  test.each([
    'PLAYGROUND_RUN_WITH_AUDIO_GEMINI',
    'CHAT_PROMPT_TEMPLATE_WITH_AUDIO_VARIABLE',
  ])('Audio element rendering for run: %s', (runName) => {
    const run = RUNS[runName as keyof typeof RUNS];
    const { container } = renderRunDetails(run as unknown as RunSchema);

    const audioElements = container.querySelectorAll('audio');
    expect(audioElements.length).toBeGreaterThan(0);

    expect(container.textContent).not.toContain('Unsupported media type');
  });
});
