import '@testing-library/jest-dom';
import { render } from '@testing-library/react';

import { MemoryRouter } from 'react-router-dom';
import { describe, expect, test } from 'vitest';

import { RunsPlayground } from '@/Pages/Playground/RunsPlayground';
import { RunSchema } from '@/types/schema';

import RUNS from './data/runs.json';
import { MockOrgIdProvider, MockStoredResourceTagsProvider } from './utils';

const renderPlayground = (run: RunSchema) => {
  return render(
    <MemoryRouter>
      <MockOrgIdProvider>
        <MockStoredResourceTagsProvider>
          <RunsPlayground run={run} />
        </MockStoredResourceTagsProvider>
      </MockOrgIdProvider>
    </MemoryRouter>
  );
};
describe('RunsPlayground', () => {
  test.each(Object.entries(RUNS))(
    'No errors thrown in playground render for run: %s',
    (_, run) => {
      // TODO: Fix types
      renderPlayground(run as unknown as RunSchema);
    }
  );

  test.each([
    'LANGCHAIN_CHAT_ANTHROPIC_MULTIMODAL_CONTENT',
    'OPENAI_FORMAT_MULTIMODAL_CONTENT',
  ])('Multimodal rendering for run: %s', (runName) => {
    const run = RUNS[runName as keyof typeof RUNS];
    const { container } = renderPlayground(run as unknown as RunSchema);
    const iframes = [...container.querySelectorAll('iframe')];
    const iframeWithPdf = iframes.find((iframe) =>
      iframe.src.startsWith('data:application/pdf;base64,')
    );
    expect(iframeWithPdf).toBeDefined();
    expect(container.textContent).not.toContain('Unsupported media type');
  });

  test.each([
    'PLAYGROUND_RUN_WITH_AUDIO_GEMINI',
    'CHAT_PROMPT_TEMPLATE_WITH_AUDIO_VARIABLE',
  ])('Audio element rendering for run: %s', (runName) => {
    const run = RUNS[runName as keyof typeof RUNS];
    const { container } = renderPlayground(run as unknown as RunSchema);

    const audioElements = container.querySelectorAll('audio');
    expect(audioElements.length).toBeGreaterThan(0);

    expect(container.textContent).not.toContain('Unsupported media type');
  });
});
