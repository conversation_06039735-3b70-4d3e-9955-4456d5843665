import type { SerializedConstructor } from '@langchain/core/load/serializable';
import { MessageContent } from '@langchain/core/messages';
import { ToolCallChunk } from '@langchain/core/messages/tool';
import { TemplateFormat } from '@langchain/core/prompts';

import { OpenAI as OpenAIClient } from 'openai';

import { LogLevel } from '@/Pages/HostRevision/types';
import { SchemaDefinition } from '@/components/EvaluatorCrudPane/utils/serializeFeedbackUIToLC';
import {
  TArtifact,
  TArtifactLengthOptions,
  THighlight,
  TMessage,
  TReadingLevelOptions,
} from '@/hooks/promptCanvas/types';

export interface UnknownSerializedConstructor extends SerializedConstructor {
  kwargs: { [key: string]: unknown };
}

export interface GetRunsQueryParams {
  // sort
  order?: 'desc' | 'asc';
  // filter
  id?: string[];
  session?: string[];
  trace?: string;
  reference_example?: string[];
  run_type?: string;
  error?: true;
  is_root?: boolean;
  start_time?: string;
  end_time?: string;
  filter?: string;
  trace_filter?: string;
  tree_filter?: string;
  search_filter?: string;
  use_experimental_search?: boolean;
  // pagination
  offset?: number;
  limit?: number;
  cursor?: string;
  skip_pagination?: boolean;
  skip_prev_cursor?: boolean;
  // projection
  select?: string[];
  data_source_type?: 'current' | 'historical' | null;
}

export interface GetSessionsQueryParams {
  // filter
  id?: string[];
  name?: string;
  name_contains?: string;
  mode?: string;
  reference_free?: boolean;
  reference_dataset?: string[];
  sort_by?: string;
  sort_by_desc?: boolean;
  dataset_version?: string;
  tag_value_id?: string[];
  filter?: string; // stringified json
  // pagination
  offset?: number;
  limit?: number;
  facets?: boolean;
  use_approx_stats?: boolean;
}

export interface GetSingleSessionQueryParams {
  include_stats?: boolean;
}

export interface GetSessionsMetadataQueryParams {
  metadata_keys?: string[];
  start_time?: string;
  k?: number;
  root_runs_only?: boolean;
}

export interface SessionMetadataResponse {
  [key: string]: string[];
}

export type DatasetDataType = 'llm' | 'chat' | 'kv';

export interface GetDatasetsQueryParams {
  // filter
  id?: string[];
  name?: string;
  name_contains?: string;
  data_type?: DatasetDataType | DatasetDataType[];
  sort_by?: string;
  sort_by_desc?: boolean;
  tag_value_id?: string[];
  // pagination
  offset?: number;
  limit?: number;
}

export interface GetDatasetVersionsQueryParams {
  search?: string;
  // pagination
  offset?: number;
  limit?: number;
}

export interface GetSingleDatasetVersionsQueryParams {
  as_of: string;
}

export interface GetDatasetVersionsDiffQueryParams {
  from_version: string;
  to_version: string;
}

export interface DatasetVersionsDiffSchema {
  examples_modified: string[];
  examples_added: string[];
  examples_removed: string[];
}

export interface GroupedDatasetVersionSchema {
  tags?: string[];
  as_of: string;
}

export interface DatasetVersionPutBody {
  as_of: string;
  tag: string;
}

export interface DatasetSplitsUpdateSchema {
  examples: string[];
  split_name: string;
  remove: boolean;
}

export interface GetExamplesQueryParams {
  // filter
  dataset?: string;
  as_of?: string;
  id?: string[];
  splits?: string[];
  order?: 'recent' | 'random' | 'recently_created';
  // pagination
  offset?: number;
  limit?: number;
  select?: string[];
}

export interface GetExampleQueryParams {
  as_of?: string;
}

export interface GetFeedbackQueryParams {
  // filter
  run?: string | string[];
  session?: string | string[];
  key?: string | string[];
  source?: string | string[];
  user?: string | string[];
  has_comment?: boolean;
  has_score?: boolean;
  include_user_names?: boolean;
  // pagination
  offset?: number;
  limit?: number;
}

export interface PostMemberBody {
  email: string;
  role_id?: string;
}

export interface PostWorkspaceBatchMemberBody {
  email: string;
  workspace_role_id?: string;
}

export interface PostWorkspaceMemberBody {
  user_id: string;
  role_id?: string;
}

export interface PostOrgMemberBody extends PostMemberBody {
  workspace_ids: string[];
  workspace_role_id?: string;
}

export interface DatasetSessionCompareDisplaySetting {
  name: string;
  selected: boolean;
  desc: string;
  section: string;
  onSelect?: () => void;
  customCell?: React.ReactElement;
  preventSelection?: boolean;
}

export interface DisplaySettings {
  name: string;
  selected: boolean;
  onSelect: () => void;
}

export interface PostFeedbackBody {
  run_id: string;
  key: string;
  comment?: string;
  score?: number;
  value?: string;
  feedback_source: {
    type: 'app';
  };
  feedback_config?: FeedbackConfigPayloadSchema;
}

export interface GetPlaygroundAPIKey {
  run_id: string;
  share_token: string;
}

export interface GetRunShareBody {
  run_id: string;
  share_token: string;
}

export interface GetDatasetShareBody {
  dataset_id: string;
  share_token: string;
}

export interface PatchFeedbackBody {
  score?: number;
  correction?: Record<string, unknown> | string;
}

export interface PostSessionBody {
  name: string;
  default_dataset_id?: string;
  reference_dataset_id?: string;
  description?: string;
  extra?: {
    [key: string]: unknown;
  };
}

export interface PatchSessionBody {
  name?: string;
  description?: string;
  default_dataset_id?: string;
  default_dashboard_id?: string | null;
  end_time?: string;
  trace_tier?: TraceTTLTier;
  extra?: {
    [key: string]: unknown;
  };
}

export interface TenantsStatsResponse {
  tenant_id: string;
  dataset_count: number;
  tracer_session_count: number;
  repo_count: number;
  annotation_queue_count: number;
  deployment_count: number;
  dashboards_count: number;
}

export interface TenantUsageLimitsResponse {
  in_reject_set: boolean;
  usage_limit_type?:
    | 'payload_size'
    | 'events_ingested_per_hour'
    | 'events_ingested_per_minute'
    | 'total_unique_traces'
    | 'user_defined_monthly_traces'
    | 'user_defined_monthly_longlived_traces';
  tenant_limit?: number;
}

export type UserConfiguredUsageLimitType =
  | 'monthly_traces'
  | 'monthly_longlived_traces';
export type UserConfiguredUsageLimitsResponse = UserConfiguredUsageLimit[];

export interface UpsertUserConfiguredUsageLimit {
  limit_type: UserConfiguredUsageLimitType;
  limit_value: number;
  id?: string;
}

export interface UserConfiguredUsageLimit
  extends UpsertUserConfiguredUsageLimit {
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

export enum TraceTTLTier {
  longlived = 'longlived',
  shortlived = 'shortlived',
}
export interface UpsertTTLSettingsRequest {
  tenant_id?: string;
  default_trace_tier: TraceTTLTier;
  apply_to_all_projects: boolean;
}

export enum TTLConfiguredBy {
  system = 'system',
  user = 'user',
}
export interface TTLSetting extends UpsertTTLSettingsRequest {
  id: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  configured_by: TTLConfiguredBy;
}

export interface LoginMethodSchema {
  id: string;
  provider: string | null;
  ls_user_id: string;
  saml_provider_id: string | null;
  provider_user_id: string | null;
  created_at: string;
  updated_at: string;
  email: string | null;
  full_name: string | null;
  email_confirmed_at: string | null;
}

export interface UserInfoSchema {
  name: string;
  user_id: string;
  linked_user_ids: string[];
}

export interface IdentitySchema {
  id: string;
  tenant_id: string;
  created_at: string;
  user_id: string;
  full_name?: string | null;
  email?: string | null;
  avatar_url?: string | null;
  read_only: boolean;
  password?: string | null;
  role_id?: string;
  role_name?: string;
  tenant_ids?: string[];
  linked_login_methods?: LoginMethodSchema[];
  ls_user_id?: string;
  org_role_id?: string;
  org_role_name?: string;
}

export interface PendingIdentitySchema {
  id: string;
  tenant_id: string;
  created_at: string;
  email: string;
  read_only: boolean;
  role_id?: string;
  role_name?: string;
  tenant_ids?: string[];
  org_role_id?: null;
  org_role_name?: null;
}

export const isIdentitySchema = (value: unknown): value is IdentitySchema => {
  return (
    typeof value === 'object' &&
    value !== null &&
    'full_name' in value &&
    Boolean(value.full_name)
  );
};

export interface OrganizationMembersResponse {
  organization_id: string;
  members: IdentitySchema[];
  pending: PendingIdentitySchema[];
}

export interface TenantsMembersResponse {
  tenant_id: string;
  members: IdentitySchema[];
  pending: PendingIdentitySchema[];
}

export interface FeedbackStatsFieldValues {
  n: number;
  stdev?: number | null;
  avg: number | null;
  errors?: number;
  showErrorCount?: boolean;
  show_feedback_arrow?: boolean;
  session_min_score?: number;
  session_max_score?: number;
  values: {
    [key: string]: number;
  };
  comments?: string[];
}

export interface FeedbackStatsField {
  [key: string]: FeedbackStatsFieldValues;
}

export interface RunFacetField {
  key: string;
  value: string;
  query: string;
  n?: number;
  avg?: number;
}

export interface TenantSchema {
  id: string;
  created_at: string;
  display_name: string;
  tenant_handle: string | null;
  organization_id: string | null;
  read_only: boolean;
  is_personal: boolean;

  role_id?: string;
  role_name?: string;
  permissions?: string[];
}

export interface PatchWorkspaceBody {
  display_name: string;
}

export interface PostTenantBody {
  display_name: string;
  organization_id?: string;
  is_personal?: boolean;
}

export interface PaymentMethodInfoSchema {
  brand: string | null;
  last4: string | null;
  exp_month: number | null;
  exp_year: number | null;
  email: string | null;
}

export type PaymentPlanTier =
  | 'developer'
  | 'plus'
  | 'enterprise'
  | 'developer_legacy'
  | 'plus_legacy'
  | 'enterprise_legacy'
  | 'free'
  | 'startup'
  | 'partner'
  | 'premier'
  | 'no_plan';

export const paymentPlanCardTiers = {
  developer: 'developer',
  plus: 'plus',
  applyStartup: 'applyStartup',
  startup: 'startup',
  partner: 'partner',
  premier: 'premier',
};

export type PaymentPlanCardTiers = keyof typeof paymentPlanCardTiers;

const ChangePaymentPlanTiers = [
  'disabled',
  'developer',
  'plus',
  'startup',
  'partner',
  'premier',
  'free',
] as const;
export type ChangePaymentPlanTierSchema =
  (typeof ChangePaymentPlanTiers)[number];
export function isChangePaymentPlanTier(
  value: any
): value is ChangePaymentPlanTierSchema {
  return ChangePaymentPlanTiers.includes(value);
}

export interface PostOrganizationBody {
  display_name: string;
  is_personal: boolean;
}

export interface UpdateOrganizationInfoBody {
  display_name: string;
}

export interface OrganizationBaseSchema {
  id: string;
  display_name: string;
  created_at: string;
  modified_at: string;
  is_personal: boolean;
}

export interface OrganizationSchema {
  id: string;
  display_name: string;
  tier: PaymentPlanTier | null;
  config: OrganizationConfigSchema;
  is_personal: boolean;
  reached_max_workspaces?: boolean;
  permissions: string[];
  disabled: boolean;
  sso_login_slug: string | null;
  sso_only?: boolean;
  marketplace_payouts_enabled?: boolean;
  wallet?: {
    credit_balance_micros: number;
    inflight_balance_micros: number;
  };
}

export interface OrganizationBillingSchema extends OrganizationSchema {
  connected_to_stripe: boolean;
  connected_to_metronome: boolean;
  payment_method: PaymentMethodInfoSchema | null;
  end_of_billing_period?: string; // Midnight UTC
  current_plan: OrganizationPlanSchema;
  upcoming_plan: OrganizationPlanSchema;
}

export interface OrganizationConfigSchema {
  startup_plan_approval_date: string | null;
  partner_plan_approval_date: string | null;
  premier_plan_approval_date: string | null;
  max_identities: number;
  can_use_rbac: boolean;
  can_add_seats: boolean;
  can_use_saml_sso: boolean;
  kv_dataset_message_support: boolean;
  flags: {
    [key: string]: boolean;
  };

  use_python_playground_service?: boolean;
  show_playground_prompt_canvas?: boolean;
  enable_select_all_traces?: boolean;
  langsmith_alerts_poc_enabled?: boolean;
  langsmith_alerts_legacy_poc_enabled?: boolean;
  langsmith_experimental_search_enabled?: boolean;
  enable_monthly_usage_charts?: boolean;
}

export interface OrganizationChangePaymentPlanSchema {
  tier: ChangePaymentPlanTierSchema;
}

export interface OrganizationPlanSchema {
  tier: PaymentPlanTier;
  started_on: string;
  ends_on: string;
}

export interface OrganizationDashboardSchema {
  embeddable_url: string;
}

export interface StripeSetupIntentResponse {
  client_secret: string;
}

export interface StripeAddress {
  line1: string;
  line2: string | null;
  city: string;
  state: string | null;
  postal_code: string;
  country: string;
}
export interface StripeCustomerBillingInfo {
  name: string;
  address: StripeAddress;
}

export interface StripeTaxId {
  value: string;
  type: string;
}

export interface StripePaymentInformation {
  billing_info: StripeCustomerBillingInfo;
  setup_intent: string;
}

export interface StripeBusinessInfo {
  company_info?: Partial<StripeCustomerBillingInfo>;
  tax_id?: StripeTaxId;
  invoice_email?: string;
  is_business?: boolean;
}
export interface RunStatsSchema {
  run_count?: number;
  latency_p50?: number;
  latency_p99?: number;
  first_token_p50?: number;
  first_token_p99?: number;
  error_rate?: number;
  streaming_rate?: number;
  total_tokens?: number;
  completion_tokens?: number;
  prompt_tokens?: number;
  median_tokens?: number;
  feedback_stats?: FeedbackStatsField;
  run_facets?: RunFacetField[];
  last_run_start_time?: string;
  total_cost?: number;
  completion_cost?: number;
  prompt_cost?: number;
}

export interface RunGroupStatsSchema extends RunStatsSchema {
  group_count?: number;
}

export interface SessionSchema extends RunStatsSchema {
  id: string;
  tenant_id: string;
  default_dataset_id?: string;
  // Stored in metadata, is massaged into this field from extras.
  // when receiving the response
  default_dashboard_id?: string;
  created_at: string;
  name: string;
  description?: string;
  start_time: string;
  end_time?: string;
  reference_dataset_id?: string;
  last_run_start_time_live?: string;
  test_run_number?: number;
  session_feedback_stats?: FeedbackStatsField;
  trace_tier?: TraceTTLTier;
  extra?: {
    metadata?: {
      [key: string]: unknown;
    };
    conversations?: {
      input?: unknown;
      output?: unknown;
    };
    [key: string]: unknown;
  };
}

export interface TimedeltaInput {
  days?: number;
  seconds?: number;
  microseconds?: number;
  milliseconds?: number;
  minutes?: number;
  hours?: number;
  weeks?: number;
}

export interface MonitorGroupSpec {
  session: string;
  tag?: string;
}

export interface MonitorQueryParams {
  groups: MonitorGroupSpec[];
  interval?: TimedeltaInput;
  stride?: TimedeltaInput;
  timezone?: string;
}

export interface MonitorBlock {
  section: string;
  title: string;
  subtitle?: string;
  columns: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rows: any[][];
  chart_spec: object;
  click_target?: string;
  toggleable_marks?: { [key: string]: number[] };
}

export interface MonitorResponse {
  blocks: MonitorBlock[];
}

export interface MessageContentPartTextDelta {
  type: 'text_delta';
  text?: unknown;
}

export interface MessageContentPartText {
  type: 'text';
  text?: unknown;
}

export interface MessageContentPartOutputText {
  type: 'output_text';
  text?: unknown;
}

export interface MessageContentPartInputImage {
  type: 'input_image';
  image_url: string;
}

export interface MessageContentPartImageUrl {
  type: 'image_url';
  image_url: string | { url: string; detail?: 'auto' | 'low' | 'high' };
}
export interface MessageContentPartOpenAIFile {
  type: 'file';
  file: {
    filename: string;
    file_data: string;
  };
}

export function isMessageContentPartOpenAIFile(
  part: MessageContentPart
): part is MessageContentPartOpenAIFile {
  return (
    part.type === 'file' &&
    typeof (part as MessageContentPartOpenAIFile).file === 'object' &&
    (part as MessageContentPartOpenAIFile).file != null &&
    typeof (part as MessageContentPartOpenAIFile).file.file_data === 'string'
  );
}

export interface MessageContentPartOpenAIAudio {
  type: 'input_audio';
  input_audio: {
    data: string; // base64 encoded audio data without data url prefix
    format?: 'wav' | 'mp3'; // currently only wav and mp3 are supported
  };
}

export function isMessageContentPartOpenAIAudio(
  part: MessageContentPart
): part is MessageContentPartOpenAIAudio {
  return (
    part.type === 'input_audio' &&
    typeof (part as MessageContentPartOpenAIAudio).input_audio === 'object' &&
    (part as MessageContentPartOpenAIAudio).input_audio != null &&
    typeof (part as MessageContentPartOpenAIAudio).input_audio.data ===
      'string' &&
    typeof (part as MessageContentPartOpenAIAudio).input_audio.format ===
      'string'
  );
}

export type MessageContentPartOpenAIMultimodal =
  | MessageContentPartOpenAIFile
  | MessageContentPartOpenAIAudio;

export function isMessageContentPartOpenAIMultimodal(
  part: MessageContentPart
): part is MessageContentPartOpenAIMultimodal {
  return (
    isMessageContentPartOpenAIFile(part) ||
    isMessageContentPartOpenAIAudio(part)
  );
}

// TODO: Import from LangChain.js when released
export interface MessageContentPartLangChainMultimodal {
  type: 'file' | 'audio' | 'image';
  source_type: 'base64' | 'url';
  data?: string;
  url?: string;
  mime_type: string;
}

export function isMessageContentPartLangChainMultimodal(
  part: MessageContentPart
): part is MessageContentPartLangChainMultimodal {
  return (
    (part as MessageContentPartLangChainMultimodal).source_type !== undefined
  );
}

export interface MessageContentPartTool {
  id: string;
  name: string;
  type: 'tool_use';
  input?: Record<string, any> | string;
  args?: Record<string, any>;
}

export type MessageContentPart =
  | MessageContentPartText
  | MessageContentPartTextDelta
  | MessageContentPartImageUrl
  | MessageContentPartTool
  | MessageContentPartOutputText
  | MessageContentPartInputImage
  | MessageContentPartOpenAIMultimodal
  | MessageContentPartLangChainMultimodal;

export interface ToolCall {
  id?: string;
  name: string;
  args: Record<string, unknown>;
}

export interface InvalidToolCall {
  id?: string;
  name?: string | undefined;
  args?: string | undefined;
  error?: string;
}

export interface MessageFields {
  content: string | Array<MessageContentPart>;
  additional_kwargs?: {
    function_call?: OpenAIClient.Chat.ChatCompletionMessage.FunctionCall;
    tool_calls?: Array<OpenAIClient.Chat.ChatCompletionMessageToolCall>;
    name?: string;
    // Deepseek R1 only (2025-02-04)
    reasoning_content?: string;
    [key: string]: unknown;
  };

  prompt?: SerializedConstructor & { kwargs: { template: string } };

  usage_metadata?: UsageMetadata;

  name?: string;

  // generic chat message
  role?: string;

  // tool chat message
  tool_call_id?: string;

  tool_calls?: ToolCall[];

  invalid_tool_calls?: InvalidToolCall[];

  // Python serializations can include type in data
  type?: string;
}

/**
 * StoredMessage is a message format used by earlier runs.
 */
export interface StoredMessage {
  type?: string;
  role?: string;
  data: MessageFields;
}

/**
 * SerializedMessage is the message format currently in use.
 */
export interface SerializedMessage extends SerializedConstructor {
  lc: number;
  id: string[];
  type: 'constructor';
  kwargs: MessageFields;
}

export type MessageTuple = [string, string];

export type OpenAIMessage = OpenAIClient.Chat.ChatCompletionMessageParam;

export type OpenAIResponsesAPIOutput =
  | {
      type: 'function_call';
      arguments: string;
      call_id: string;
      name: string;
    }
  | {
      type: 'computer_call';
      status: string;
      action: Record<string, unknown>;
      call_id: string;
      id: string;
    }
  | {
      type: 'message';
      role: string;
      content: Array<MessageContentPart>;
      status: string;
    };

export type MessageUnionType =
  | StoredMessage
  | SerializedMessage
  | MessageTuple
  | MessageFields
  | OpenAIMessage
  | OpenAIResponsesAPIOutput;

export interface Generation {
  text?: string;
  message?: MessageUnionType;
}

// openai llm response type
export type Choice = OpenAIClient.Chat.ChatCompletion.Choice;

export type InputTokenDetails = {
  audio?: number;
  cache_read?: number;
  cache_creation?: number;
};

export type OutputTokenDetails = {
  audio?: number;
  reasoning?: number;
};
export type UsageMetadata = {
  input_tokens: number;
  output_tokens: number;
  total_tokens: number;
  input_token_details?: InputTokenDetails;
  output_token_details?: OutputTokenDetails;
};

export interface DifyOutputChoice {
  role: string;
  content: string;
  usage_metadata?: UsageMetadata;
  file_list?: string[];
}

export interface RunOutputSchema {
  generations?: Generation[] | Generation[][];
  choices?: Choice[] | DifyOutputChoice;
  usage_metadata?: UsageMetadata;
  output?: string | RunOutputSchema;
  text?: string;
  [key: string]: any;
}

export interface RunInputSchema {
  input?: unknown;
  prompt?: string;
  prompts?: Array<string>;
  // multipart messages have an array of array of messages here
  messages?: Array<MessageUnionType> | Array<Array<MessageUnionType>>;
  [key: string]: any;
}
export interface RunSchema {
  id: string;
  status: 'pending' | 'error' | 'success' | 'interrupted' | null;
  session_id: string;
  extra?: {
    [key: string]: any;
  };
  inputs?: RunInputSchema;
  outputs?: RunOutputSchema;
  error?: string;
  serialized?: SerializedConstructor;
  events?: Array<{
    name: string;
    time: string;
    kwargs?: Record<string, any>;
  }>;
  tags?: Array<string>;
  run_type: string;
  name: string;
  start_time: string;
  end_time?: string;
  trace_id: string;
  dotted_order: string;
  parent_run_id?: string;
  parent_run_ids?: Array<string>;
  reference_example_id?: string;
  reference_dataset_id?: string;
  manifest_id?: string;
  child_run_ids?: Array<string>;
  direct_child_run_ids?: Array<string>;
  thread_id?: string;
  app_path: string;
  feedback_stats?: FeedbackStatsField;
  prompt_tokens?: number;
  prompt_cost?: number;
  completion_tokens?: number;
  completion_cost?: number;
  total_tokens?: number;
  total_cost?: number;
  first_token_time?: string;
  in_dataset?: boolean;
  last_queued_at?: string;
  inputs_preview?: string;
  inputs_s3_urls?: Record<string, S3DataSchema>;
  outputs_preview?: string;
  outputs_s3_urls?: Record<string, S3DataSchema>;
  share_token?: string;
  s3_urls?: {
    [key: string]: S3DataSchema;
  };
}

export interface RunSchemaWithExample extends RunSchema {
  reference_example?: ExampleSchema;
}

export interface RunSchemaWithContents extends RunSchema {}

export interface S3DataSchema {
  presigned_url: string;
  storage_url: string;
}

export interface RepoExampleSchema {
  id: string;
  start_time: string;
  inputs?:
    | {
        input?: string;
        prompt?: string;
        prompts?: Array<string>;
        messages?: Array<MessageUnionType>;
        [key: string]: any;
      }
    | RunInputSchema;
  outputs?: {
    generations?: Generation[];
    output?: string;
    text?: string;
    [key: string]: any;
  };
  session_id: string;
}

export interface RepoExampleCreateSchema {
  dataset_id: string;
  inputs?: any;
  outputs?: any;
  source_run_id?: string;
  use_source_run_io?: boolean;
}

export interface RunManifestSchema {
  id: string;
  manifest: object;
}

// ['inputs', 'messages', '*']
export enum DatasetTransformationType {
  // list of messages in inputs at any level
  // default: off
  REMOVE_SYSTEM_MESSAGES = 'remove_system_messages',

  // list of messages or message fields in inputs/outputs at any level
  // default: on
  CONVERT_TO_OPENAI_MESSAGE = 'convert_to_openai_message',

  // list of tools in inputs at any level
  // default: on
  CONVERT_TO_OPENAI_TOOL = 'convert_to_openai_tool',

  // can be applied to any object
  // default: off, default on for chat template
  REMOVE_EXTRA_FIELDS = 'remove_extra_fields',

  // DEPRECATED
  EXTRACT_TOOLS_FROM_RUN = 'extract_tools_from_run',
}

export interface CreateDatasetRequest {
  name: string;
  description: string;
  data_type?: DatasetSchema['data_type'];
  inputs_schema_definition?: Record<string, unknown> | null;
  outputs_schema_definition?: Record<string, unknown> | null;
  transformations?: DatasetTransformationSchema[] | null;
}

export interface CreateDatasetWithExamplesRequest extends CreateDatasetRequest {
  examples: { inputs: string; outputs: string; metadata: string }[];
}

export interface DatasetTransformationSchema {
  path: string[];
  transformation_type: DatasetTransformationType;
}

export interface DatasetSchema {
  id: string;
  tenant_id: string;
  created_at: string;
  name: string;
  description: string;
  example_count: number;
  session_count: number;
  data_type: DatasetDataType;
  last_session_start_time?: string;
  inputs_schema_definition?: Record<string, unknown>;
  outputs_schema_definition?: Record<string, unknown>;
  transformations?: DatasetTransformationSchema[];
}

export interface CloneDatasetSchema {
  source_dataset_id: string;
  target_dataset_id: string;
  examples?: string[];
  as_of?: string;
}

export interface ExampleUpdateSchema {
  dataset_id?: string;
  inputs?: Record<string, unknown>;
  outputs?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  split?: DatasetSplitType;
  overwrite?: boolean;
}

export interface ExampleValidateSchema {
  dataset_id?: string;
  inputs?: Record<string, unknown>;
  outputs?: Record<string, unknown>;
  use_source_run_attachments?: string[];
  metadata?: Record<string, unknown>;
  split?: DatasetSplitType;
  overwrite?: boolean;
  source_run_id?: string;
}

interface ExampleSchemaBase {
  id: string;
  name: string;
  created_at: string;
  modified_at: string;
  source_run_id?: string;
  metadata?: {
    [key: string]: any;
  };
  inputs: {
    [key: string]: any;
  };
  outputs: {
    [key: string]: any;
  };
  attachment_urls?: {
    [key: string]: S3DataSchema;
  };
}
export interface ExampleSchema extends ExampleSchemaBase {
  dataset_id: string;
}

export interface ExampleSchemaOrDraft extends ExampleSchemaBase {
  dataset_id?: string;
}

export interface ExampleSchemaWithRuns extends ExampleSchema {
  runs: RunSchema[];
}

export interface ExampleSchemaWithRunsAndOptionalFields {
  id: string;
  name?: string;
  dataset_id?: string;
  created_at?: string;
  modified_at?: string;
  source_run_id?: string;
  metadata?: {
    [key: string]: any;
  };
  inputs: {
    [key: string]: any;
  };
  outputs: {
    [key: string]: any;
  };
  attachment_urls?: {
    [key: string]: S3DataSchema;
  };
  runs: RunSchema[];
  error?: string; // for errors before the run (e.g. mismatching input schema)
}

export type DatasetSplitType = string[] | string | null;

export interface ExampleValidationResult {
  dataset_id: string | null;
  inputs?: Record<string, any> | null;
  outputs?: Record<string, any> | null;
  created_at: Date | null;
  metadata?: Record<string, any> | null;
  source_run_id: string | null;
  split?: DatasetSplitType;
  id?: string;
  use_source_run_io: boolean;
  overwrite: boolean;
}

export interface ComparisonViewSessionColumn {
  columnName?: string | JSX.Element;
  disableHeaderClick?: boolean;
  sessionId: string;
}

export interface ComparisonViewColumnInformation {
  columnName: string;
  columnId: string;
}

export type ComparisonViewColumn =
  | ComparisonViewSessionColumn
  | ComparisonViewColumnInformation;

export interface FeedbackDelta {
  improved_examples: string[];
  regressed_examples: string[];
}

export interface SessionFeedbackDelta {
  feedback_deltas: Record<string, FeedbackDelta>;
}

export interface SortParamsForRunsComparisonView {
  sort_by: string;
  sort_order: string;
}

export interface FeedbackConfigsRequest {
  key?: string[];
  read_after_write?: boolean;
}

export interface QueryFeedbackDelta {
  baseline_session_id: string;
  comparison_session_ids: string[];
  feedback_key: string;
  filters?: Record<string, string[]>;
  comparative_experiment_id?: string;
  offset?: number;
  limit?: number;
}

export interface GetExampleRunsQueryParams {
  session_ids: string[];
  comparative_experiment_id?: string;
  filters?: Record<string, string[]>;
  sort_params?: SortParamsForRunsComparisonView;
  preview?: boolean;
  // pagination
  offset?: number;
  limit?: number;
}

export interface ComparativeExperimentSchema {
  id: string;
  experiments_info: {
    name: string;
    id: string;
  }[];
  name?: string;
  description?: string;
  tenant_id?: string;
  created_at: string;
  end_time?: string;
  reference_dataset_id?: string;
  extra?: {
    [key: string]: unknown;
  };
  feedback_stats?: {
    [key: string]: {
      [session: string]: number;
    };
  };
}

export interface GetComparativeExperimentsQueryParams {
  name?: string;
  name_contains?: string;
  limit?: number;
  offset?: number;
  sort_by?: string;
  sort_by_desc?: boolean;
  id?: string[];
}

export interface FeedbackSchema {
  id: string;
  run_id: string;
  key: string;
  score: number;
  value?: string;
  feedback_source: {
    type: string;
    [key: string]: any;
  };
  created_at: string;
  modified_at: string;
  comment?: string;
  correction?: Record<string, unknown>;
  comparative_experiment_id?: string;
  extra: Record<string, any>;
}

export interface ApiKeySchema {
  id: string; // uuid
  created_at: string | null;
  short_key: string;
  description: string | '';
  last_used_at: string | null;
}

export interface NewApiKeySchema extends ApiKeySchema {
  key: string;
}

export interface NewApiKeyBody {
  description: string;
}

// Hub Schemas
export enum EPromptType {
  CHAT = 'chat',
  INSTRUCT = 'instruct',
  STRUCTURED = 'structured',
}

export interface RepoSchema {
  id: string; // uuid
  repo_handle: string;
  description: string | null;
  readme?: string | null;
  tenant_id: string; // uuid
  created_at: string;
  updated_at: string;
  is_public: boolean;
  is_archived: boolean;
  tags: string[];
  original_repo_id: string | null; // uuid
  upstream_repo_id: string | null; // uuid
}

export interface RepoWithLookupsSchema extends RepoSchema {
  num_likes: number;
  num_downloads: number;
  num_views: number;
  owner: string;
  tenant_id: string;
  full_name: string;
  liked_by_auth_user: boolean | null;
  last_commit_hash: string | null;
  num_commits: number;
  original_repo_full_name: string | null;
  upstream_repo_full_name: string | null;
  latest_commit_manifest?: CommitManifestResponse;
}

export interface PaginationQueryParams {
  limit?: number;
  offset?: number;
  status?: string;
}

export type IsArchivedFilterOptions = 'true' | 'allow' | 'false';

export interface ListRepoTagsQueryParams extends PaginationQueryParams {
  tenant_handle?: string;
  tenant_id?: string;
  query?: string;
  tags?: string[];
  has_commits?: boolean;
  is_archived?: IsArchivedFilterOptions;
  is_public?: boolean;
  upstream_repo_handle?: string;
  upstream_repo_owner?: string;
  tag_value_id?: string[];
}

export interface ListReposQueryParams extends ListRepoTagsQueryParams {
  sort_field?:
    | 'updated_at'
    | 'num_views'
    | 'num_downloads'
    | 'num_likes'
    | 'relevance';
  sort_direction?: 'asc' | 'desc';
  with_latest_manifest?: boolean;
}

export interface ListReposResponse {
  repos: RepoWithLookupsSchema[];
  total: number;
}

export interface GetRepoResponse {
  repo: RepoWithLookupsSchema;
}

export interface CreateRepoBody {
  repo_handle: string;
  description?: string;
  readme?: string;
  is_public: boolean;
  tags?: string[];
}

export interface UpdateRepoBody {
  description?: string;
  readme?: string;
  tags?: string[];
  is_public?: boolean;
  is_archived?: boolean;
}

export interface CommitSchema {
  id: string; // uuid
  manifest_id: string; // uuid
  repo_id: string; // uuid
  parent_id: string | null; // uuid
  commit_hash: string;
  created_at: string;
  updated_at: string;
  example_run_ids: string[];
}

export interface CommitWithLookupsSchema extends CommitSchema {
  num_downloads: number;
  num_views: number;
  parent_commit_hash: string | null;
  manifest: SerializedConstructor;
  full_name?: string;
}

export interface CommitManifestResponse {
  commit_hash: string;
  manifest: SerializedConstructor;
  examples: RepoExampleSchema[];
}

export interface PromptSchema {
  manifest: SerializedConstructor;
  examples: RepoExampleSchema[];
  details: RepoWithLookupsSchema;
}

export interface RepoLikeSchema {
  id: string; // uuid
  repo_id: string; // uuid
  liked_by: string; // uuid
  created_at: string;
  modified_at: string;
}

export interface CreateRepoCommitBody {
  manifest: object;
  parent_commit: string | null;
  ignore_webhook_ids: string[];
}

export interface CreateRepoCommitResponse {
  commit: CommitWithLookupsSchema;
}

export interface LikeRepoBody {
  like: boolean;
}

export interface LikeRepoResponse {
  likes: number;
}

export interface ListCommitsResponse {
  commits: CommitWithLookupsSchema[];
  total: number;
}

export interface SetTenantHandleBody {
  tenant_handle: string;
}

interface TagCount {
  tag: string;
  count: number;
}
export interface ListRepoTagsResponse {
  tags: TagCount[];
}

export interface RepoCommitTag {
  id: string;
  tag_name: string;
  commit_id: string;
  commit_hash: string;
  repo_id: string;
  created_at: string;
  updated_at: string;
}

export interface RepoCommitTagCreateBody {
  tag_name: string;
  commit_id: string;
}

export interface RepoCommitTagUpdateBody {
  commit_id: string;
}

export interface CreateEventBody {
  event_type: 'playground-view' | 'playground-run';
  owner: string;
  repo: string;
  commit: string | null;
}

export interface CommentSchema {
  id: string; // uuid
  comment_by?: string; // uuid
  comment_on: string; // uuid
  parent_id: string | null; // uuid
  content: string;
  created_at: string;
  updated_at: string;
  comment_by_name?: string;
  num_sub_comments: number;
  num_likes: number;
  liked_by_auth_user: boolean | null;
}

export interface ListCommentsResponse {
  comments: CommentSchema[];
  total: number;
}

export interface LikeResponse {
  status: 'success';
}

export interface CreateCommentBody {
  content: string;
}

export interface AnnotationQueueSchema {
  id: string; // uuid
  tenant_id: string; // uuid
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  total_runs: number;
  default_dataset: string | null; // uuid
  num_reviewers_per_item: number | undefined;
  enable_reservations: boolean;
  reservation_minutes: number | null;
  rubric_items?: AnnotationQueueRubricItem[];
  rubric_instructions?: string;
  source_rule_id?: string;
}

export type AnnotationQueueRubricItemType =
  | 'text'
  | 'continuous'
  | 'categorical';

export interface AnnotationQueueRubricItem {
  feedback_key?: string;
  description?: string;
  type?: AnnotationQueueRubricItemType;
  value_descriptions?: Record<string, string>;
  score_descriptions?: Record<string, string>;
}

export interface AnnotationQueueSizeSchema {
  size: number;
}

export interface GetAnnotationQueuesQueryParams {
  // filter
  ids?: string[];
  name?: string;
  name_contains?: string;
  tag_value_id?: string[];
  // pagination
  offset?: number;
  limit?: number;
  // sort
  dataset_id?: string;
}

export interface PostAnnotationQueueBody {
  name: string;
  description: string;
  default_dataset?: string;
  num_reviewers_per_item: number | null;
  enable_reservations: boolean;
  reservation_minutes?: number | null;
  rubric_items?: AnnotationQueueRubricItem[];
  rubric_instructions?: string;
  session_ids?: string[];
}

export interface PopulateAnnotationQueueBody {
  queue_id: string;
  session_ids: string[];
}

export interface PatchAnnotationQueueBody {
  name?: string;
  description?: string;
  default_dataset?: string;
  num_reviewers_per_item?: number | null;
  enable_reservations?: boolean;
  reservation_minutes?: number | null;
  rubric_items?: AnnotationQueueRubricItem[];
  rubric_instructions?: string;
}

export interface PatchAnnotationQueueRunBody {
  added_at?: string;
  last_reviewed_time?: string;
}

export interface DeleteAnnotationQueueRunsBody {
  run_ids?: string[];
  exclude_run_ids?: string[];
  delete_all?: boolean;
}

export interface GetAnnotationQueueRunsQueryParams {
  archived?: boolean;
  include_stats?: boolean;
  // pagination
  offset?: number;
  limit?: number;
}

export interface GetAnnotationQueueArchivedSizeQueryParams {
  start_time?: string;
  end_time?: string;
}

export interface AnnotationQueueRunSchema extends RunSchema {
  queue_run_id: string;
  last_reviewed_time?: string;
  added_at: string;
  effective_added_at: string | undefined;
}

export interface CreateIdentityAnnotationQueueRunStatusBody {
  status: 'completed' | 'viewed' | null;
  override_added_at?: string;
}

export interface HostEnvVarSchema {
  name: string;
  value: string;

  // valid values: default, secret
  type: string;
}

export type HostRevisionStatus =
  | 'CREATING'
  | 'AWAITING_BUILD'
  | 'BUILDING'
  | 'AWAITING_DEPLOY'
  | 'DEPLOYING'
  | 'CREATE_FAILED'
  | 'BUILD_FAILED'
  | 'DEPLOY_FAILED'
  | 'DEPLOYED'
  | 'INTERRUPTED'
  | 'UNKNOWN'
  | undefined;
export interface HostRevisionSchema {
  id: string;
  created_at: string;
  updated_at: string;
  repo_path?: string;
  repo_commit?: string;
  image_path?: string;
  status?: HostRevisionStatus;
  status_message?: string;
  metadata: Record<string, any>;
  resource?: {
    id: {
      name: string;
    };
    ready: string;
  };
}

export interface HostProjectSchema {
  id: string; // uuid
  lc_hosted: boolean;
  tenant_id: string; // uuid
  created_at: string;
  updated_at: string;
  name: string;
  repo_url?: string;
  repo_path?: string;
  repo_branch?: string;
  build_on_push: boolean;
  tracer_session_id: string;
  metadata: Record<string, any>;
  tool_name?: string;
  display_name?: string;
  description?: string;
  example_input?: Record<string, any>;
  input_json_schemas?: Record<string, any>;
  output_json_schemas?: Record<string, any>;
  custom_url?: string;
  resource?: {
    id: {
      type: string;
      name: string;
    };
    url?: string;
    status: {
      type: string;
      status: string;
      message?: string;
    }[];
    latest_revision?: {
      id: {
        name: string;
      };
      env_vars: HostEnvVarSchema[];
      hosted_langserve_revision_id?: string; // uuid
    };
    latest_active_revision?: {
      hosted_langserve_revision_id?: string; // uuid
    };
  };
  container_spec: ContainerSpec;
}

export interface HostProjectPriceSchema {
  price_per_request_micros: number;
}

export interface HostProjectPatchBody {
  repo_branch?: string;
  build_on_push?: boolean;
  custom_url?: string;
}

export interface HostAccessTokenSchema {
  access_token: string;
  expiry: string;
}

export type HostProjectImageSource =
  | 'github'
  | 'internal_docker'
  | 'external_docker';

export interface ProjectPlaygroundSchema {
  name: string;
  url: string;
}

export enum DeploymentPlatformId {
  cloudRun = 'cloud_run',
  K8s = 'k8s',
  K8sVanilla = 'k8s_vanilla',
  AWSECS = 'aws_ecs',
}

export interface CreateProjectPlatformRequest {
  deployment_platform: DeploymentPlatformId;
  region: string;
  aws_account_id: string;
  public?: boolean;
}

export interface ContainerSpec {
  cpu?: number;
  memory_mb?: number;
  min_scale?: number;
  max_scale?: number;
}

export interface HostProjectCreateBody {
  name: string;
  lc_hosted?: boolean;
  repo_url?: string;
  repo_path?: string;
  repo_commit?: string;
  env_vars?: HostEnvVarSchema[];
  host_integration_id?: string;
  deployment_type: string;
  shareable: boolean;
  image_path?: string;
  build_on_push?: boolean;
  platform?: CreateProjectPlatformRequest;
  container_spec?: ContainerSpec;
}

export interface HostRevisionCreateBody {
  repo_path?: string;
  image_path?: string;
  env_vars?: HostEnvVarSchema[];
  shareable: boolean;
  container_spec?: ContainerSpec;
}

export interface HostRevisionLogsRequest {
  start_time?: string;
  end_time?: string;
  order?: 'asc' | 'desc';
  limit?: number;
  offset?: string;
  query?: string;
  level?: LogLevel;
}

export type HostRevisionLog = {
  id: string;
  timestamp: number;
  message: string;
  level: LogLevel;
};

export interface HostRevisionLogsResponse {
  logs: HostRevisionLog[];
  next_offset: string | null;
}

export interface HostGetProjectsQueryParams {
  name_contains?: string;
  // pagination
  offset?: number;
  limit?: number;
  tag_value_id?: string[];
}

export interface HostGithubRepoSchema {
  host_integration_id: string;
  id: number;
  name: string;
  owner: string;
  url: string;
  default_branch: string;
}

export interface HostGithubInstallLinkSchema {
  install_url: string;
}

export interface HostGithubNamespaceSchema {
  id: string;
  installation_id: string;
  name: string;
}

type FeedbackConfigPayloadSchema = {
  [key: string]: unknown;
} & (
  | {
      type: 'continuous';
      min?: number | null;
      max?: number | null;
      categories?: Array<{ value: number; label?: string }> | null;
    }
  | {
      type: 'categorical';
      categories?: Array<{ value: number; label?: string }> | null;
    }
  | {
      type: 'freeform';
    }
);

export interface UpdateFeedbackConfigSchema {
  feedback_key: string;
  is_lower_score_better?: boolean | null;
}

export interface FeedbackConfigSchema {
  feedback_key: string;
  feedback_config: FeedbackConfigPayloadSchema;
  tenant_id?: string;
  modified_at?: string;
  is_lower_score_better?: boolean;
}

export interface FeedbackConfigCreateBody {
  feedback_key: string;
  feedback_config: FeedbackConfigPayloadSchema;
}

export interface ModelPriceMapSchema {
  id: string;
  name: string;
  start_time?: string;

  tenant_id?: string;

  match_path: string[];
  match_pattern: string;

  prompt_cost: number;
  completion_cost: number;

  provider?: string;
}

export interface ModelPriceMapUpdateBody {
  name: string;
  start_time?: string;

  match_path: string[];
  match_pattern: string;

  prompt_cost: number;
  completion_cost: number;

  provider?: string;
}

export interface CommonRuleMutateBody {
  display_name: string;
  is_enabled: boolean;
  sampling_rate: number;

  filter: string;
  trace_filter?: string;
  tree_filter?: string;

  backfill_from?: string;

  alerts?: Array<{
    type: 'pagerduty';
    routing_key: string;
    summary?: string;
    severity?: 'critical' | 'warning' | 'error' | 'info';
  }>;
  evaluator_version?: number;
  corrections_dataset_id?: string;
  use_corrections_dataset?: boolean;
  num_few_shot_examples?: number;
  add_to_annotation_queue_id?: string | null;
  add_to_dataset_id?: string | null;
  add_to_dataset_prefer_correction?: boolean | null;
  evaluators?: Array<{
    structured?: StructuredEvaluatorSchema;
  }> | null;
  code_evaluators?: Array<{
    code?: string;
  }> | null;
  webhooks?: Array<{
    url: string;
    headers?: Record<string, string>;
  }>;
  extend_only?: boolean | null;
}

export interface DatasetRuleMutateBody extends CommonRuleMutateBody {
  dataset_id: string;
}

export interface SessionRuleMutateBody extends CommonRuleMutateBody {
  session_id: string;
}

export type RuleMutateBody = DatasetRuleMutateBody | SessionRuleMutateBody;

export type RuleTriggerBody = {
  rule_ids: string[];
};

export type AceResponseBody = {
  status: string;
  stacktrace?: string;
  result?: unknown;
};

export type AceInvokeBody = {
  code: string;
  args: unknown[];
};

export interface StructuredEvaluatorSchemaHub {
  hub_ref: string;
  model: SerializedConstructor;
  variable_mapping?: Record<string, string>;
  // few_shot_examples?: Record<string, any>; // Will be added later
}

export interface StructuredEvaluatorSchemaPrompt {
  model: SerializedConstructor;
  prompt: MessageTuple[];
  schema: SchemaDefinition;
  template_format?: TemplateFormat;
  variable_mapping?: Record<string, string>;
  // few_shot_examples?: Record<string, any>; // Will be added later
}

export type StructuredEvaluatorSchema =
  | StructuredEvaluatorSchemaHub
  | StructuredEvaluatorSchemaPrompt;

export interface GetRunRulesQueryParams {
  session_id?: string;
  dataset_id?: string;
  type?: 'session' | 'dataset';
  name_contains?: string;
  id?: string[];
}

export interface RuleSchema {
  id: string;
  is_enabled: boolean;
  display_name: string;
  filter: string;
  sampling_rate: number;
  evaluator_version: number;

  backfill_from?: string;

  session_id?: string;
  session_name?: string;

  dataset_id?: string;
  dataset_name?: string;

  alerts?: Array<{
    type: 'pagerduty';
    routing_key: string;
    summary?: string;
    severity?: 'critical' | 'warning' | 'error' | 'info';
  }>;

  webhooks?: Array<{
    url: string;
    headers?: Record<string, string>;
  }>;

  add_to_annotation_queue_id?: string | null;
  add_to_annotation_queue_name?: string | null;

  add_to_dataset_id?: string | null;
  add_to_dataset_name?: string | null;

  use_corrections_dataset?: boolean;
  num_few_shot_examples?: number;
  corrections_dataset_id?: string;

  extend_only?: boolean | null;

  evaluators?: Array<{
    structured?: StructuredEvaluatorSchema;
  }> | null;
  code_evaluators?: Array<{
    code: string;
  }> | null;
}

export type EvaluatorRuleSchema = Omit<
  RuleSchema,
  | 'alerts'
  | 'webhooks'
  | 'add_to_annotation_queue_id'
  | 'add_to_annotation_queue_name'
  | 'add_to_dataset_id'
  | 'add_to_dataset_name'
  | 'code_evaluators'
>;

export interface RuleLogActionSchema {
  outcome: 'success' | 'error' | 'skipped';
  payload?: Record<string, unknown> | undefined | null;
}

export interface RuleLogSchema {
  rule_id: string;
  is_enabled: boolean;

  run_id: string;
  run_name?: string;
  run_type?: string;
  run_session_id?: string;

  start_time: string;
  end_time: string;
  application_time: string;

  add_to_annotation_queue?: RuleLogActionSchema;
  add_to_dataset?: RuleLogActionSchema;
  evaluators?: RuleLogActionSchema;
  alerts?: RuleLogActionSchema;
  webhooks?: RuleLogActionSchema;
  extend_only?: RuleLogActionSchema;
}

export interface GetRuleLogsQueryParams {
  // pagination
  offset?: number;
  limit?: number;
}

export interface ExportAnnotationQueueRunsRequest {
  start_time?: string;
  end_time?: string;
}

export interface RunGroupSchema {
  group_key: string;
  filter: string;
  count?: number;
  total_tokens?: number;
  total_cost?: number;
  min_start_time?: string;
  max_start_time?: string;
  latency_p50?: number;
  latency_p99?: number;
  feedback_stats?: RunSchema['feedback_stats'];
  first_inputs?: RunSchema['inputs'];
  last_outputs?: RunSchema['outputs'];
  last_error?: RunSchema['error'];
  first_run_id?: string;
  last_run_id?: string;
}

export interface RunGroupsRequest {
  session_id: string;
  group_by: 'conversation';

  filter?: string | undefined;

  start_time?: string;
  end_time?: string;

  offset?: number;
  limit?: number;
}

export interface RunGroupsStatsRequest {
  session_id: string;
  group_by: 'conversation';

  filter?: string | undefined;

  start_time?: string;
  end_time?: string;
}

export interface RunGroupsResponse {
  groups: Array<RunGroupSchema>;
  total: number;
}

export interface TenantSecret {
  key: string;
  value?: string | null;
}

interface TenantSharedDataset {
  type: 'dataset';
  share_token: string;
  created_at: string;

  dataset_id: string;
  dataset_name?: string;
}

interface TenantSharedRun {
  type: 'run';
  share_token: string;
  created_at: string;

  run_id: string;
  run_name?: string;
  run_type?: string;

  session_id?: string;
  session_name?: string;
}

export type TenantSharedEntity = TenantSharedDataset | TenantSharedRun;

export interface TenantSharedEntityResponse {
  entities: TenantSharedEntity[];
}

export interface TenantBulkUnshareRequest {
  share_tokens: string[];
}

export interface OrganizationCreateMemberRequest {
  email: string;
  password: string;
  read_only?: boolean;
  full_name?: string;
}

export interface OrganizationPatchMemberRequest {
  password?: string;
  read_only?: boolean;
  full_name?: string;
  identity_id?: string;
  role_id?: string;
}

export interface OrganizationRoleSchema {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  organization_id?: string | undefined;
  permissions: string[];
}

export interface OrganizationCreateRoleRequest {
  display_name: string;
  description: string;
  permissions: string[];
}

export type OrganizationPatchRoleRequest = OrganizationCreateRoleRequest;

export interface PermissionSchema {
  name: string;
  description: string;
}

export interface InvokePromptRequest {
  messages: MessageTuple[];
  template_format: string;
  inputs: Record<string, unknown>;
}

export interface InvokePromptResponse {
  result: { messages: MessageFields[] };
}

export interface InfoResponse {
  version: string;
  license_expiration_time: string;
  instance_flags: Record<string, unknown>;
}

export interface HealthInfoGetResponse {
  clickhouse_disk_free_pct: number;
}

export interface SSOSettingsUpdate {
  id: string;
  default_workspace_role_id?: string;
  default_workspace_ids?: string[];
}

export type SSOSettingsCreate =
  | {
      metadata_url: string;
      metadata_xml?: never;
      default_workspace_role_id: string;
      default_workspace_ids: string[];
    }
  | {
      metadata_url?: never;
      metadata_xml: string;
      default_workspace_role_id: string;
      default_workspace_ids: string[];
    };

export interface SSOSettings {
  id: string;
  attribute_mapping: Record<string, string>;
  metadata_url: string;
  metadata_xml: string;
  default_workspace_role_id: string;
  default_workspace_ids: string[];
}

export interface SSOLoginSettings {
  provider_id: string;
  organization_id: string;
  organization_display_name: string;
}

export interface UpdateSSOOnlyRequest {
  sso_only: boolean;
}

export interface SSOEmailVerificationConfirmRequest {
  token: string;
}

export interface SSOEmailVerificationStatusRequest {
  email: string;
  saml_provider_id: string;
}

export interface SSOEmailVerificationStatusResponse {
  email_confirmed_at: string | null;
}

export interface SSOEmailVerificationSendRequest {
  email: string;
  saml_provider_id: string;
}

export interface PlaygroundSavedOptionsSchema {
  requests_per_second?: number;
}

export interface PlaygroundSettingsSchema {
  id: string;
  name?: string;
  description?: string;
  settings: SerializedConstructor;
  options?: PlaygroundSavedOptionsSchema;
  created_at: string;
  updated_at: string;
}

export interface CreatePlaygroundSettingsBody {
  settings: SerializedConstructor;
  name?: string;
  description?: string;
  options?: PlaygroundSavedOptionsSchema;
}

export interface UpdatePlaygroundSettingsBody {
  settings?: SerializedConstructor;
  name?: string;
  description?: string;
  options?: PlaygroundSavedOptionsSchema;
}

export interface GenerateSyntheticExamplesBody {
  example_ids: string[] | null;
  num_examples: number;
}

export interface GeneratedExampleIO {
  id: number;
  inputs: string;
  outputs: string;
}

export interface GenerateSyntheticExamplesResponse {
  examples: GeneratedExampleIO[];
}

export interface BulkCreateExampleBody {
  dataset_id: string;
  examples: { inputs: string; outputs: string; metadata: string }[];
}

export enum FilterViewType {
  RUNS = 'runs',
  THREADS = 'threads',
}

export interface FilterViewCreateBody {
  filter_string?: string;
  display_name: string;
  description?: string;
  trace_filter_string?: string;
  tree_filter_string?: string;
  type?: FilterViewType;
}

export interface FilterViewSchema extends FilterViewCreateBody {
  id: string;
  session_id?: string;
  created_at: string;
  updated_at: string;
  type: FilterViewType;
}

export interface FilterViewUpdateBody {
  filter_string?: string;
  display_name?: string;
  description?: string;
  trace_filter_string?: string;
  tree_filter_string?: string;
  type?: FilterViewType;
}

export interface FewShotSyncStatusSchema {
  dataset_id: string;
  last_updated_version: string;
  tag: string;
}

export interface RunStatsGroupBy {
  attribute: 'tag' | 'metadata' | 'name' | 'run_type';
  path: string;
  max_groups?: number;
}

export interface RunStatsGroupByResponse extends RunStatsGroupBy {
  set_by?: 'series' | 'section';
}

export interface BaseCustomChartsRequest {
  tag_value_id?: string[];
  group_by?: RunStatsGroupBy;
}

export interface CustomChartsDataRequest extends BaseCustomChartsRequest {
  start_time: string;
  end_time?: string;
  stride: TimedeltaInput;
  omit_data?: false;
}

export interface CustomChartsNoDataRequest extends BaseCustomChartsRequest {
  start_time?: string;
  end_time?: string;
  stride?: TimedeltaInput;
  omit_data: true;
}

export type CustomChartsRequest =
  | CustomChartsDataRequest
  | CustomChartsNoDataRequest;

export type DashboardsSortBy = 'created_at' | 'modified_at';

export interface DashboardsRequest {
  limit: number;
  offset: number;
  sort_by?: DashboardsSortBy;
  sort_by_desc?: boolean;
  title_contains?: string;
  ids?: string[];
  tag_value_id?: string[];
}

export type CustomChartMetric =
  | 'run_count'
  | 'latency_avg'
  | 'latency_p50'
  | 'latency_p99'
  | 'first_token_p50'
  | 'first_token_p99'
  | 'total_tokens'
  | 'prompt_tokens'
  | 'completion_tokens'
  | 'median_tokens'
  | 'feedback'
  | 'feedback_score_avg'
  | 'feedback_values'
  | 'total_cost'
  | 'prompt_cost'
  | 'completion_cost'
  | 'error_rate'
  | 'streaming_rate'
  | 'cost_p50'
  | 'cost_p99';

export type CustomChartType = 'line' | 'bar';

export interface CustomChartsSectionCreate {
  title: string;
  description: string;
  index: number | null;
}

export interface CustomChartsSectionResponse extends CustomChartsSectionCreate {
  id: string;
  created_at: string;
  modified_at: string;
  chart_count: number;
  session_id?: string;
}

export interface CustomChartsSubSectionSchema {
  id: string;
  title: string;
  index: number;
  description?: string;
  charts: CustomChartSchema[];
}

export interface CustomChartsSectionSchema extends CustomChartsSectionCreate {
  id: string;
  charts: CustomChartSchema[];
  sub_sections?: CustomChartsSubSectionSchema[];
  session_id?: string;
}

export interface CustomChartsSectionUpdate {
  id: string;
  title?: string;
  description?: string;
  index?: number | null;
}

export interface CustomChartPreviewSchema {
  title: string;
  description: string;
  chart_type: CustomChartType;
  series: CustomChartSeriesSchema[];
  data: CustomChartDataSchema[];
  group_by?: RunStatsGroupBy;
}

export interface CustomChartPreviewThresholdConfig {
  metric: CustomChartMetric;
  data: CustomChartDataSchema;
  type: 'gte' | 'lte';
  color: string;
}

export interface CustomChartCreatePreview {
  series: CustomChartSeriesSchema[];
  common_filters: CustomChartSeriesFilters;
}

export interface OrgUsageRequest {
  starting_on: string;
  ending_before?: string;
  on_current_plan?: boolean;
}

export interface OrgUsageGroups {
  [tenant_id: string]: number;
}

// OrgUsage types map directly to metronome usage api: https://docs.metronome.com/api/#usage
export interface OrgUsage {
  customer_id: string;
  billable_metric_id: string;
  billable_metric_name: string;
  start_timestamp: string;
  end_timestamp: string;
  value: number | null;
  groups: OrgUsageGroups | null;
}

export interface CustomChartsPreviewRequest {
  bucket_info: CustomChartsRequest;
  chart: CustomChartCreatePreview;
}

export interface CustomChartsPreviewResponse {
  data: CustomChartDataSchema[];
}

export interface CustomChartSchema {
  id: string;
  title: string;
  description: string;
  index: number;
  chart_type: CustomChartType;
  series: CustomChartSeriesSchema[];
  common_filters: CustomChartSeriesFilters;
  data: CustomChartDataSchema[];
}

export interface CustomChartSeriesFilters {
  filter?: string;
  tree_filter?: string;
  trace_filter?: string;
  session?: string[];
}

export interface CustomChartSeriesBase {
  name: string;
  filters?: CustomChartSeriesFilters;
  metric: CustomChartMetric;
  feedback_key: string | null;
  group_by?: RunStatsGroupBy;
}
export interface CustomChartSeriesCreate extends CustomChartSeriesBase {
  group_by?: RunStatsGroupBy;
}

export interface CustomChartSeriesSchema extends CustomChartSeriesBase {
  group_by?: RunStatsGroupByResponse;
  id: string;
}

export type CustomChartDataPoint =
  | number
  | {
      [key: string]: {
        n: number;
        avg: number | null;
        values: Record<string, number>;
      };
    };

export interface CustomChartDataSchema {
  series_id: string;
  timestamp: string;
  value: CustomChartDataPoint;
  group?: string;
}

export interface CustomChartCreate {
  title: string;
  description: string;
  series: CustomChartSeriesCreate[];
  common_filters: CustomChartSeriesFilters;
  chart_type: CustomChartType;
  section_id: string | null;
  index: number | null;
}

export interface CustomChartUpdateBase {
  title?: string;
  description?: string;
  series?: CustomChartSeriesCreate[];
  common_filters?: CustomChartSeriesFilters;
  chart_type?: CustomChartType;
  section_id?: string | null;
  index?: number | null;
}

export interface CustomChartExistingInfo extends CustomChartUpdateBase {
  id?: string;
}

export interface TagKeyCreate {
  key: string;
  description?: string | null;
}
export interface ResourceTagKeyCreate {
  key: string;
  description?: string;
}

export interface ResourceTagKey extends ResourceTagKeyCreate {
  id: string;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
}

export interface ResourceTagKeyWithValues extends ResourceTagKey {
  values: ResourceTagValue[];
}

export interface ResourceTagKeyWithValuesAndTaggings extends ResourceTagKey {
  values: ResourceTagValueWithTaggings[];
}

export interface ResourceTagKeyUpdate {
  key?: string | null;
  description?: string | null;
}

export interface ResourceTagValueCreate {
  value: string;
  description?: string;
}

export interface ResourceTagValueUpdate {
  value?: string | null;
  description?: string | null;
}

export interface ResourceTagValue extends ResourceTagValueCreate {
  id: string;
  tag_key_id: string;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
}

export interface ResourceTagValueWithTaggings extends ResourceTagValue {
  taggings: ResourceTagging[];
}

export interface ResourceTaggingCreate {
  tag_value_id: string;
  resource_type: ResourceType;
  resource_id: string;
}

export interface ResourceTagging extends ResourceTaggingCreate {
  id: string;
  created_at: string; // ISO 8601 date string
}

export interface TaggingCreate {
  resource_id: string;
}
export interface Tagging extends TaggingCreate {
  tagging_id?: string;
  resource_name: string;
}

export interface TaggingsByResourceType {
  prompts: Tagging[];
  projects: Tagging[];
  queues: Tagging[];
  deployments: Tagging[];
  experiments: Tagging[];
  datasets: Tagging[];
  dashboards: Tagging[];
}

export interface ResourceTag {
  tag_key_id: string;
  tag_value_id: string;
  tag_key: string;
  tag_value: string;
  resources?: TaggingsByResourceType;
}

export enum ResourceType {
  Project = 'project',
  Queue = 'queue',
  Deployment = 'deployment',
  Experiment = 'experiment',
  Dataset = 'dataset',
  Prompt = 'prompt',
  Dashboard = 'dashboard',
}

export interface SearchDatasetRequest {
  inputs: Record<string, any>;
  limit?: number;
  debug?: boolean;
}

export interface SearchedFewShotExample {
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  id: string;
  debug_info?: Record<string, any>;
}

export interface SearchDatasetResponse {
  examples: SearchedFewShotExample[];
}

export interface SingleExampleData {
  input: string;
  reference_output: string;
  output: string;
  feedback: string;
  run_id: string;
}

export enum EPromptOptimizationAlgorithm {
  PROMPTIM = 'promptim',
  DEMO = 'demo',
}

export interface BaseOptimizatonAlgorithmConfig {
  message_index: number;
}

export interface PromptimConfig extends BaseOptimizatonAlgorithmConfig {
  task_description: string;
  dataset_name: string;
  train_split: string | null;
  dev_split: string | null;
  test_split: string | null;
  evaluators: string[];
  num_epochs: number;
  auto_commit: boolean;
}

export interface PromptimConfigForForm extends PromptimConfig {
  dataset_id: string;
}

export interface DemoConfig {
  metaprompt: Record<string, any>;
  examples: Record<string, any>[];
}

export interface BasePromptOptimizationRequest {
  prompt_name: string;
  algorithm: EPromptOptimizationAlgorithm;
}

export interface PromptOptimizationRequest
  extends BasePromptOptimizationRequest {
  config: PromptimConfig | DemoConfig;
}

export interface OptimizationConfigForForm
  extends BasePromptOptimizationRequest {
  config: PromptimConfigForForm | DemoConfig;
}

export interface PromptOptimizationResponse {
  optimization_job_id: string;
}

export enum EPromptOptimizationJobStatus {
  CREATED = 'created',
  RUNNING = 'running',
  SUCCESSFUL = 'successful',
  FAILED = 'failed',
}

export enum EPromptOptimizationLogType {
  INFO = 'info',
  ERROR = 'error',
  RESULT = 'result',
}

export interface PromptOptimizationLog {
  log_type: EPromptOptimizationLogType;
  message: string;
  created_at: string;
  data?: Record<string, any>;
}

export interface PromptOptimizationResult {
  timestamp: string;
  x: number;
  y: number;
}

export interface PromptOptimizationJob {
  id: string;
  repo_id: string;
  status: EPromptOptimizationJobStatus;
  tenant_id: string;
  algorithm: EPromptOptimizationAlgorithm;
  config: PromptimConfig | DemoConfig;
  results: PromptOptimizationResult[];
  created_at: string;
  updated_at: string;
}

export interface PromptOptimizationJobWithLogs extends PromptOptimizationJob {
  logs: PromptOptimizationLog[];
}

export interface PlaygroundRequestSchema {
  manifest: SerializedConstructor;
  secrets: Record<string, string>;
  run_id?: string;
  tools?: Array<SerializedConstructor>;
  tool_choice?: string;
  parallel_tool_calls?: boolean;
  options: Record<string, any>;
  repo_handle?: string | null;
  owner?: string | null;
  commit?: string | null;
}

export interface PlaygroundInvokeRequestSchema extends PlaygroundRequestSchema {
  input: Record<string, any>;
  repetitions?: number;
}

export interface PlaygroundBatchRequestSchema extends PlaygroundRequestSchema {
  input: Array<Record<string, any>>;
}

export interface PlaygroundStreamRequestSchema extends PlaygroundRequestSchema {
  input: Record<string, any>;
}

export interface PlaygroundInvokeDatasetRequestSchema
  extends PlaygroundRequestSchema {
  project_name: string;
  dataset_id: string;
  dataset_splits?: string[];
  repetitions?: number;
  evaluator_rules?: string[];
}

export interface PlaygroundStreamDatasetRequestSchema
  extends PlaygroundRequestSchema {
  project_name: string;
  dataset_id: string;
  dataset_splits?: string[];
  evaluator_rules?: string[];
}

export type TPromptCanvasAgentPayload = {
  messages: TMessage[];
  highlighted?: THighlight;
  artifact: TArtifact;
  artifact_length?: TArtifactLengthOptions;
  reading_level?: TReadingLevelOptions;
  custom_action?: string;
  template_format: TemplateFormat;
  secrets: Record<string, string>;
};

export type TPromptCanvasAgentResponse = {
  messages: TMessage[];
  highlighted?: THighlight;
  artifact: TArtifact;
  artifact_length?: TArtifactLengthOptions;
  reading_level?: TReadingLevelOptions;
  custom_action?: string;
};

export enum MessageCapability {
  IMAGE = 'image',
  CANVAS = 'canvas',
}

type LangGraphStreamEventMetadata = {
  langgraph_node: string;
  checkpoint_ns: string;
  langgraph_checkpoint_ns: string;
  langgraph_path: string[];
  langgraph_step: number;
  langgraph_triggers: string[];
};

type StreamEventMessage = {
  content: MessageContent;
  id?: string;
  additional_kwargs?: Record<string, any>;
  response_metadata?: Record<string, any>;
  usage_metadata?: UsageMetadata;
  name?: string;
  type: string;
  tool_calls?: ToolCall[];
  tool_call_chunks?: ToolCallChunk[];
  invalid_tool_calls?: InvalidToolCall[];
};

export interface OnChatModelStreamEvent {
  event: 'chat_model_stream';
  data: {
    chunk?: StreamEventMessage;
  };
  metadata?: LangGraphStreamEventMetadata;
  name: string;
  parent_ids: string[];
  run_id: string;
  tags: string[];
}

export enum PromptCanvasNode {
  GENERATE_PATH = 'generate_path',
  RESPOND_TO_QUERY = 'respond_to_query',
  REWRITE_ARTIFACT = 'rewrite_artifact',
  REWRITE_ARTIFACT_THEME = 'rewrite_artifact_theme',
  REWRITE_ARTIFACT_CUSTOM_ACTION = 'rewrite_artifact_custom_action',
  UPDATE_ARTIFACT = 'update_artifact',
  GENERATE_FOLLOWUP = 'generate_followup',
  CLEAN_STATE = 'clean_state',
}

export enum StreamEvent {
  ON_CHAT_MODEL_START = 'on_chat_model_start',
  ON_CHAT_MODEL_STREAM = 'on_chat_model_stream',
  ON_CHAT_MODEL_END = 'on_chat_model_end',
  ON_CHAIN_START = 'on_chain_start',
  ON_CHAIN_END = 'on_chain_end',
}

export interface DatasetGroupRunsRequest {
  session_ids: string[];
  filters?: Record<string, string[]>;
  group_by: 'run_metadata' | 'example_metadata';
  metadata_key: string;
  offset?: number;
  limit?: number;
}

export interface ExamplesGroup {
  group_key: string;
  examples: ExampleSchemaWithRunsAndOptionalFields[];
  filter: string;
  count: number;
  total_tokens?: number;
  total_cost?: number;
  min_start_time?: string;
  max_start_time?: string;
  latency_p50?: number;
  latency_p99?: number;
  feedback_stats?: {
    [key: string]: {
      n: number;
      avg?: number;
      stdev?: number;
      values: Record<string, number>;
    };
  };
}

export interface DatasetGroupRunsResponse {
  groups: ExamplesGroup[];
  total: number;
}
