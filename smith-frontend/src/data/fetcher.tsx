import {
  EventSourceMessage,
  EventStreamContentType,
  fetchEventSource,
} from '@microsoft/fetch-event-source';

import { z } from 'zod';

import { SWRObservableOptions } from '@/hooks/useSwrObservable';
import { desktopAuthManager, supabase } from '@/utils/backend-auth';

import { backendUrl, mixedLoginMethods } from '../utils/constants';
import { eventBus } from './eventBus';
import {
  OAUTH_SESSION_EXPIRED,
  SUPABASE_SESSION_EXPIRED,
} from './eventConstants';
import { getAuthHeaders } from './getAuthHeaders';

export interface FetcherParams<P = never, B = never> {
  url: string;
  method?: string;
  params?: P;
  json?: B;
  body?: BodyInit;
  resolveWithResponse?: boolean;
  headers?: Record<string, unknown>;
  skipTenantHeaders?: boolean;
  skipOrgHeaders?: boolean;
  templateUrlParams?: Record<string, string>;
  templateQueryParams?: Record<string, string>;
}

export interface Op {
  op: 'add';
  path: string;
  value: unknown;
}

export function applyPatch<R>(current: R | undefined, patch: Op[]): R {
  let newDocument = current;
  patch.forEach(({ op, path, value }) => {
    if (op === 'add') {
      const parts = path.split('/').slice(1);
      if (parts.length === 0) {
        newDocument = value as R;
      } else if (newDocument) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const last = parts.pop()!;
        let target = newDocument;
        parts.forEach((part) => {
          target = target[part];
        });
        if (last === '-' && Array.isArray(target)) {
          target.push(value);
        } else {
          target[last] = value;
        }
      }
    } else if (op === 'replace') {
      const parts = path.split('/').slice(1);
      if (parts.length === 0) {
        newDocument = value as R;
      } else if (newDocument) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const last = parts.pop()!;
        let target = newDocument;
        parts.forEach((part) => {
          target = target[part];
        });
        target[last] = value;
      }
    }
  });
  return JSON.parse(JSON.stringify(newDocument)) as R;
}

export function getCustomHeaders(headers: Headers) {
  const customHeaders: Record<string, string> = {};
  headers.forEach((value, key) => {
    if (key.startsWith('x-')) {
      customHeaders[key] = value;
    }
  });
  if (Object.keys(customHeaders).length === 0) {
    return null;
  }
  return customHeaders;
}

interface CommonRequestInit {
  method?: string;
  body?: BodyInit;
  headers?: Record<string, string>;
  credentials?: RequestCredentials;
}

export function prepareUrl(url: string, params: URLSearchParams) {
  const hasParams = params.keys().next().done === false;
  return backendUrl.startsWith('http')
    ? new URL(`${url}${hasParams ? `?${params}` : ''}`, backendUrl)
    : `${backendUrl}${url}${hasParams ? `?${params}` : ''}`;
}

async function prepare<P = never, B = never>(
  arg: string | FetcherParams<P, B>,
  headers?: Record<string, string>
): Promise<[URL | string, CommonRequestInit]> {
  const info: FetcherParams<P, B> =
    typeof arg === 'string' ? { url: arg } : arg;
  const init: CommonRequestInit = {
    method: info.method,
    body: info.json ? JSON.stringify(info.json) : info.body,
    headers: Object.assign(
      info.json ? { 'Content-Type': 'application/json' } : {},
      await getAuthHeaders(),
      headers,
      info.headers ?? {}
    ),
    credentials: mixedLoginMethods.customOidc ? 'include' : 'same-origin',
  };
  if (supabase && !init.headers?.Authorization) {
    eventBus.emit(SUPABASE_SESSION_EXPIRED);
  }
  const params = new URLSearchParams();
  if (info.params) {
    Object.entries(info.params).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach((v) => params.append(key, v));
      } else if (value != null && value !== '') {
        params.append(key, value as string);
      }
    });
  }
  const url = prepareUrl(info.url, params);
  if (desktopAuthManager != null)
    return [desktopAuthManager.resolveUrl(url), init];
  return [url, init];
}

export class FetchError extends Error {
  status: number;
  constructor(message, status) {
    super(message);
    this.status = status;

    Object.setPrototypeOf(this, FetchError.prototype);
  }
}

const handleError = async (url: string | URL, r: Response) => {
  const body = await r.text();
  if (r.status === 401 && mixedLoginMethods.customOidc) {
    eventBus.emit(OAUTH_SESSION_EXPIRED);
  }
  if (body) {
    try {
      if (r.status === 0 && !body) {
        const JWTSchema = z.object({
          exp: z.number(),
          iat: z.number(),
        });
        const headers = await getAuthHeaders();
        const token = headers.Authorization?.split(' ')[1];
        if (token) {
          const payload = JWTSchema.safeParse(
            JSON.parse(atob(token.split('.')[1]))
          );
          if (payload.success) {
            console.error(
              `Detected status 0 with JWT iat ${payload.data.iat}, exp ${
                payload.data.exp
              }, and now ${Date.now() / 1000}`
            );
          }
        }
      }

      const json = JSON.parse(body);

      if (json.detail || json.error) {
        const errorContent = json.detail || json.error;
        const errorMessage =
          typeof errorContent === 'string'
            ? errorContent
            : JSON.stringify(errorContent);
        return Promise.reject(new FetchError(errorMessage, r.status));
      }

      return Promise.reject(
        new FetchError(
          `Failed to fetch ${url} ${r.status} ${r.statusText}`,
          r.status
        )
      );
    } catch (e) {
      if (body) {
        return Promise.reject(new FetchError(body, r.status));
      } else if (r.status) {
        if (r.status !== 401 && r.status !== 403) {
          console.error(e);
        }
        return Promise.reject(
          new FetchError(
            `Failed to fetch ${url} ${r.status} ${r.statusText}`,
            r.status
          )
        );
      }
    }
  }
};

export async function fetcher<R, P = never, B = never>(
  arg: string | FetcherParams<P, B>,
  abortController?: AbortController
  // TODO infer return type from resolveWithResponse
): Promise<R & { headers?: Record<string, string> }> {
  const [url, init] = (await prepare(arg, { Accept: 'application/json' })) as [
    string,
    RequestInit
  ];
  const info: FetcherParams<P, B> =
    typeof arg === 'string' ? { url: arg } : arg;

  if (abortController) {
    init.signal = abortController.signal;
  }

  return fetch(url, init)
    .then(async (r) => {
      if (info.resolveWithResponse) {
        return r;
      }

      if (r.ok) {
        if (r.status === 204) {
          return Promise.resolve(null);
        } else {
          const payload =
            r.headers.get('Content-Length') !== '0'
              ? r.json()
              : Promise.resolve(null);

          return payload.then((json) => {
            if (!json) {
              return json;
            }
            const headers = getCustomHeaders(r.headers);
            if (headers && typeof json === 'object') {
              Object.defineProperty(json, 'headers', {
                value: headers,
                enumerable: false,
              });
            }

            return json;
          });
        }
      } else {
        return handleError(url, r);
      }
    })
    .catch((error) => {
      if (error.name === 'AbortError' || error.name === 'TimeoutError') {
        return null;
      }
      throw error;
    });
}

export interface SubscriberParams<P = never, B = never>
  extends FetcherParams<P, B> {
  subscriberParser: keyof typeof subscriberParser;
}

const subscriberParser = {
  passthrough: (ev) => ev.data,
  jsonpatch: (ev, value) => {
    try {
      const { patch } = JSON.parse(ev.data);
      return applyPatch(value, patch);
    } catch (e) {
      console.error('error parsing jsonpatch', e);
      return value;
    }
  },
  base64: (ev, value) => {
    const decoder = new TextDecoder();
    const decoded = decoder.decode(
      Uint8Array.from(atob(ev.data), (m) => m.codePointAt(0) ?? 0)
    );

    if (value != null && typeof value !== 'string') {
      throw new Error('subscriberBase64 received a non-string');
    }

    return (value ?? '') + decoded;
  },
} satisfies Record<
  string,
  <R>(ev: EventSourceMessage, value: R | undefined) => unknown
>;

export function subscriber<Response, Payload = never, Body = never>(
  arg: string | SubscriberParams<Payload, Body>,
  { next }: SWRObservableOptions<Response, Error>,
  abortController?: AbortController
) {
  const parser =
    subscriberParser[
      typeof arg === 'string' ? 'jsonpatch' : arg.subscriberParser
    ];

  prepare(arg).then(([url, init]) =>
    fetchEventSource(url.toString(), {
      ...init,
      openWhenHidden: true,
      signal: abortController?.signal,
      async onopen(response) {
        if (
          response.ok &&
          response.headers.get('content-type') === EventStreamContentType
        ) {
          return; // everything's good
        } else if (
          response.status >= 400 &&
          response.status < 500 &&
          response.status !== 429
        ) {
          // client-side errors are usually non-retriable:
          await handleError(url, response);
        }
      },
      onmessage(ev) {
        if (ev.event === 'data') {
          next(null, (value) => ({
            value: parser(ev, value?.value ?? undefined) as Response,
            done: false,
          }));
        } else if (ev.event === 'error') {
          next(new Error(ev.data));
        }
      },
      onerror: (e) => {
        next(e);
        throw e;
      },
      onclose() {
        next(null, (value) => ({ value: value?.value, done: true }));
      },
    })
  );
}
