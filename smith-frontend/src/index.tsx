import { Suspense, lazy } from 'react';
import ReactDOM from 'react-dom/client';
import {
  RouteObject,
  RouterProvider,
  createBrowserRouter,
  redirect,
} from 'react-router-dom';

import { HostProjectCrumb } from '@/Pages/HostProject/HostProjectCrumb.tsx';

import { App } from './App';
import { RouteErrorElement } from './ErrorElement';
import { AnnotationQueueCrumb } from './Pages/AnnotationQueue/AnnotationQueueCrumb';
import { AnnotationQueueBulkViewCrumb } from './Pages/AnnotationQueueBulkView/AnnotationQueueBulkViewCrumb';
import { AnnotationQueuesCrumb } from './Pages/AnnotationQueues/AnnotationQueuesCrumb';
import { ConversationCrumb } from './Pages/Conversation/ConversationCrumb';
import { DashboardsCrumb } from './Pages/Dashboards/DashboardsCrumb';
import { DatasetCrumb } from './Pages/Dataset/DatasetCrumb';
import { DatasetSessionCompareCrumb } from './Pages/DatasetSessionCompare/DatasetSessionCompareCrumb';
import { DatasetsCrumb } from './Pages/Datasets/DatasetsCrumb';
import { ExampleCrumb } from './Pages/Example/ExampleCrumb';
import { GenerateEvaluatorCrumb } from './Pages/GenerateEvaluator/GenerateEvaluatorCrumb';
import { GraphCrumb } from './Pages/Graph/GraphCrumb';
import { HostNewCrumb } from './Pages/HostNew/HostNewCrumb';
import { HostProjectsCrumb } from './Pages/HostProjects/HostProjectsCrumb';
import { HostRevisionCrumb } from './Pages/HostRevision/HostRevisionCrumb';
import { HubCrumb } from './Pages/Hub/HubCrumb';
import { HubEditRepoCrumb } from './Pages/HubEditRepo/HubEditRepoCrumb';
import { HubForkRepoCrumb } from './Pages/HubForkRepo/HubForkRepoCrumb';
import { HubPlaygroundCrumb } from './Pages/HubPlayground/HubPlaygroundCrumb';
import { HubCommitCrumb } from './Pages/HubRepo/HubCommitCrumb';
import { HubRepoCrumb } from './Pages/HubRepo/HubRepoCrumb';
import { HubSearchCrumb } from './Pages/HubSearch/HubSearchCrumb';
import { HubTenantCrumb } from './Pages/HubTenant/HubTenantCrumb';
import { KEYBOARD_SHORTCUTS } from './Pages/KeyboardShortcutsSidePane/keyboard_shortcuts';
import { NoMatch } from './Pages/NoMatch';
import { RedirectNoMatch } from './Pages/NoMatch/RedirectNoMatch';
import { OauthCallback } from './Pages/OauthCallback';
import { OrganizationCrumb } from './Pages/Organization/OrganizationCrumb';
import { OrganizationLayout } from './Pages/Organization/OrganizationLayout';
import { PlaygroundCrumb } from './Pages/Playground/PlaygroundCrumb';
import { PlaygroundPageCrumb } from './Pages/Playground/PlaygroundPageCrumb';
import { SessionCrumb } from './Pages/Project/ProjectCrumb';
import { ProjectsCrumb } from './Pages/Projects/ProjectsCrumb';
import { PromptsCrumb } from './Pages/Prompts/PromptsCrumb';
import { PromptRepoCrumb } from './Pages/Prompts/components/PromptRepoCrumb';
import { PublicDatasetCrumb } from './Pages/PublicDataset/PublicDatasetCrumb';
import { PublicDatasetRunCrumb } from './Pages/PublicDatasetRun/PublicDatasetRunCrumb';
import { PublicDatasetRunPlaygroundCrumb } from './Pages/PublicDatasetRunPlayground/PublicDatasetRunPlaygroundCrumb';
import { PublicExampleCrumb } from './Pages/PublicExample/PublicExampleCrumb';
import { PublicProjectCrumb } from './Pages/PublicProject/PublicProjectCrumb';
import { PublicRunCrumb } from './Pages/PublicRun/PublicRunCrumb';
import { PublicRunPlaygroundCrumb } from './Pages/PublicRunPlayground/PublicRunPlaygroundCrumb';
import { RunCrumb } from './Pages/Run/RunCrumb';
import { SettingsCrumb } from './Pages/Settings/SettingsCrumb';
import { SingleDashboardCrumb } from './Pages/SingleDashboard/SingleDashboardCrumb';
import { SingleDashboardCrumbV2 } from './Pages/SingleDashboardV2/SingleDashboardCrumb.tsx';
import Breadcrumbs from './components/Breadcrumbs';
import './index.css';
import reportWebVitals from './reportWebVitals';
import {
  appAgentMarketplacePath,
  appAnnotationQueuesBulkViewPath,
  appAnnotationQueuesPath,
  appComparePath,
  appDashboardsIndexPath,
  appDatasetsPath,
  appExamplePath,
  appGraphIndexPath,
  appHubIndexPath,
  appIndexPath,
  appOrganizationPath,
  appPlaygroundPath,
  appProjectsPath,
  appPromptsIndexPath,
  appPublicDatasetsPath,
  appPublicPath,
  appRunPath,
  appSessionPath,
  appSettingsPath,
  appThreadPath,
  backendAuthType,
  baseUrl,
  deploymentsPath,
  hostAppIndexPath,
  hostAppRevisionPath,
  hostEnabled,
  isPaymentEnabled,
  mixedLoginMethods,
  newPath,
} from './utils/constants';
import { graphPublic } from './utils/routeUtils';

// Lazy load dashboard router components
const DashboardsRouterLazy = lazy(() =>
  import('./Pages/Dashboards/DashboardsRouter').then((module) => ({
    default: module.default,
  }))
);

const runRoute: RouteObject = {
  path: `${appRunPath}/:runId`,
  handle: {
    crumb: <RunCrumb />,
  },
  lazy: () =>
    import('./Pages/Run').then((m) => ({
      element: <m.default />,
    })),
};

const sessionRoute: RouteObject = {
  path: `${appSessionPath}/:sessionId`,
  handle: {
    crumb: <SessionCrumb />,
    inAssignMode: true,
  },
  children: [
    {
      index: true,
      lazy: () =>
        import('./Pages/Project/Project').then((m) => ({
          element: <m.default />,
        })),
    },
    {
      path: `${appThreadPath}/:conversationId?`,
      handle: {
        crumb: <ConversationCrumb />,
      },
      lazy: () =>
        import('@/Pages/Conversation/Conversation').then((m) => ({
          element: <m.Conversation />,
        })),
    },
    { ...runRoute },
  ],
};

const playgroundRoute: RouteObject = {
  path: `${appPlaygroundPath}/${appRunPath}/:runId`,
  handle: {
    crumb: <PlaygroundCrumb />,
  },
  lazy: () =>
    import('./Pages/Playground').then((m) => ({
      element: <m.PlaygroundPage />,
    })),
};

const routes: RouteObject[] = [
  {
    path: appIndexPath,
    element: <App />,
    errorElement: <RouteErrorElement />,
    children: [
      {
        path: `${appOrganizationPath}/:organizationId`,
        handle: { crumb: <OrganizationCrumb /> },
        element: <OrganizationLayout />,
        children: [
          // Home
          {
            index: true,
            lazy: () =>
              import('./Pages/Home').then((m) => ({
                element: <m.Home />,
              })),
            handle: { universal: true },
          },
          // Agent Marketplace
          {
            path: appAgentMarketplacePath,
            children: [
              {
                index: true,
                handle: { universal: true },
                lazy: () =>
                  import('./Pages/AgentMarketplace/').then((m) => ({
                    element: <m.default />,
                  })),
              },
            ],
          },

          // Playground
          {
            path: appPlaygroundPath,
            children: [
              {
                index: true,
                handle: { crumb: <PlaygroundPageCrumb /> },
                lazy: () =>
                  import('./Pages/Playground/PlaygroundPage').then((m) => ({
                    element: <m.PlaygroundPage />,
                  })),
              },
            ],
          },
          // Projects
          {
            path: appProjectsPath,
            handle: { crumb: <ProjectsCrumb />, universal: true },
            children: [
              {
                index: true,
                handle: { universal: true },
                lazy: () =>
                  import('./Pages/Projects/ProjectsHome').then((m) => ({
                    element: <m.ProjectsHome />,
                  })),
              },
              { ...sessionRoute },
            ],
          },
          // Datasets
          {
            path: appDatasetsPath,
            handle: { crumb: <DatasetsCrumb />, universal: true },
            children: [
              {
                index: true,
                handle: { universal: true },
                lazy: () =>
                  import('./Pages/Datasets').then((m) => ({
                    element: <m.default />,
                  })),
              },
              {
                path: `:datasetId`,
                handle: {
                  crumb: <DatasetCrumb />,
                  inAssignMode: true,
                },
                children: [
                  {
                    index: true,
                    lazy: () =>
                      import('./Pages/Dataset').then((m) => ({
                        element: <m.default />,
                      })),
                  },

                  {
                    path: `compare`,
                    handle: { crumb: <DatasetSessionCompareCrumb /> },
                    lazy: () =>
                      import('./Pages/DatasetSessionCompare').then((m) => ({
                        element: <m.DatasetSessionCompare />,
                      })),
                  },

                  {
                    path: `${appExamplePath}/:exampleId`,
                    handle: {
                      crumb: <ExampleCrumb />,
                    },
                    lazy: () =>
                      import('./Pages/Example').then((m) => ({
                        element: <m.default />,
                      })),
                  },
                ],
              },
            ],
          },
          // Generate Evaluator
          {
            path: 'generate-evaluator/:experimentId',
            handle: {
              crumb: <GenerateEvaluatorCrumb />,
              universal: true,
            },
            lazy: () =>
              import('./Pages/GenerateEvaluator').then((m) => ({
                element: <m.default />,
              })),
          },
          // Annotation Queues
          {
            path: appAnnotationQueuesPath,
            handle: { crumb: <AnnotationQueuesCrumb />, universal: true },
            children: [
              {
                index: true,
                handle: { universal: true },
                lazy: () =>
                  import('./Pages/AnnotationQueues').then((m) => ({
                    element: <m.default />,
                  })),
              },

              {
                path: `:annotationQueueId`,
                handle: {
                  crumb: <AnnotationQueueCrumb />,
                  hideBreadcrumb: true,
                  inAssignMode: true,
                  hotkeys: KEYBOARD_SHORTCUTS['annotation-queues'],
                },
                children: [
                  {
                    index: true,
                    lazy: () =>
                      import('./Pages/AnnotationQueue').then((m) => ({
                        element: <m.default />,
                      })),
                  },
                  {
                    path: `${appAnnotationQueuesBulkViewPath}`,
                    handle: {
                      crumb: <AnnotationQueueBulkViewCrumb />,
                      universal: true,
                    },
                    lazy: () =>
                      import('./Pages/AnnotationQueueBulkView').then((m) => ({
                        element: <m.default />,
                      })),
                  },
                  { ...runRoute },
                  { ...sessionRoute },
                ],
              },
            ],
          },
          // Playground routes from run
          playgroundRoute,
          {
            ...playgroundRoute,
            path: `${appAnnotationQueuesPath}/:annotationQueueId/${appPlaygroundPath}/${appRunPath}/:runId`,
          },
          {
            ...playgroundRoute,
            path: `${appAnnotationQueuesPath}/:annotationQueueId/${appSessionPath}/:sessionId/${appPlaygroundPath}/${appRunPath}/:runId`,
          },
          {
            ...playgroundRoute,
            path: `${appGraphIndexPath}/thread/:threadId/${appPlaygroundPath}/${appRunPath}/:runId`,
          },

          {
            path: appDashboardsIndexPath,
            handle: { crumb: <DashboardsCrumb />, universal: true },
            children: [
              {
                index: true,
                handle: { universal: true },
                element: (
                  <Suspense>
                    <DashboardsRouterLazy />
                  </Suspense>
                ),
              },
              {
                path: `projects/:sessionId`,
                handle: {
                  crumb: <SingleDashboardCrumbV2 />,
                  inAssignMode: true,
                },
                children: [
                  {
                    index: true,
                    handle: { universal: true },
                    lazy: () =>
                      import('./Pages/SingleDashboardV2').then((m) => ({
                        element: <m.default />,
                      })),
                  },
                ],
              },
              {
                path: `:dashboardId`,
                handle: {
                  crumb: <SingleDashboardCrumb />,
                  inAssignMode: true,
                },
                children: [
                  {
                    index: true,
                    handle: { universal: true },
                    lazy: () =>
                      import('./Pages/SingleDashboard').then((m) => ({
                        element: <m.default />,
                      })),
                  },
                ],
              },
            ],
          },
          // Settings for LangSmith
          {
            path: appSettingsPath,
            handle: { crumb: <SettingsCrumb />, universal: true },
            lazy: () =>
              import('./Pages/Settings/SettingsLayout').then((m) => ({
                element: <m.SettingsLayoutWrapper />,
              })),
            children: [
              {
                index: true,
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/OrganizationApiKeys').then((m) => ({
                    element: <m.OrganizationApiKeys />,
                  })),
              },

              {
                path: 'members',
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/OrganizationMembers').then((m) => ({
                    element: <m.OrganizationMembers />,
                  })),
              },

              {
                path: 'models',
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/OrganizationModels').then((m) => ({
                    element: <m.OrganizationModels />,
                  })),
              },

              {
                path: 'shared',
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/OrganizationShared').then((m) => ({
                    element: <m.OrganizationShared />,
                  })),
              },

              {
                path: 'secrets',
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/OrganizationSecrets').then((m) => ({
                    element: <m.OrganizationSecrets />,
                  })),
              },

              {
                path: 'feedbacks',
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/OrganizationFeedbackConfigs').then(
                    (m) => ({
                      element: <m.OrganizationFeedbackConfigs />,
                    })
                  ),
              },

              {
                path: 'rules',
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/OrganizationRules').then((m) => ({
                    element: <m.OrganizationRules />,
                  })),
              },
              {
                path: 'usage',
                handle: { universal: true },
                lazy: () =>
                  import('@/Pages/Settings/EnterpriseOrgUsage').then((m) => ({
                    element: <m.EnterpriseOrgUsage />,
                  })),
              },

              ...(isPaymentEnabled
                ? [
                    {
                      path: 'payments',
                      handle: { universal: true },
                      children: [
                        {
                          index: true,
                          handle: { universal: true },
                          lazy: () =>
                            import(
                              '@/Pages/Settings/OrganizationPlansAndBilling'
                            ).then((m) => ({
                              element: <m.OrganizationPlansAndBilling />,
                            })),
                        },
                        {
                          path: 'setup-success',
                          handle: { universal: true },
                          children: [
                            {
                              index: true,
                              handle: { universal: true },
                              lazy: () =>
                                import('@/Pages/PaymentSetupSuccess').then(
                                  (m) => ({
                                    element: <m.default />,
                                  })
                                ),
                            },
                          ],
                        },
                      ],
                    },
                  ]
                : []),
              {
                path: 'workspaces',
                lazy: () =>
                  import('@/Pages/Settings/WorkspaceSettings').then((m) => ({
                    element: <m.WorkspaceSettings />,
                  })),
                children: [
                  {
                    path: ':workspaceSetting',
                    handle: { universal: true },
                    lazy: () =>
                      import(
                        '@/Pages/Settings/components/WorkspaceSettingsTab'
                      ).then((m) => ({
                        element: <m.WorkspaceSettingsTab />,
                      })),
                  },
                ],
              },
            ],
          },
          {
            path: '*',
            element: (
              <div className="px-4 py-6 pt-3">
                <Breadcrumbs />
                <NoMatch />
              </div>
            ),
          },
        ],
      },
      {
        path: 'onboarding',
        handle: {
          fullPage: true,
        },
        lazy: () =>
          import('./components/Onboarding').then((m) => ({
            element: <m.Onboarding />,
          })),
      },
      {
        path: `${appPublicPath}/:shareToken/${appPlaygroundPath}/${appRunPath}/:runId?`,
        handle: {
          crumb: <PublicRunPlaygroundCrumb />,
          public: true,
        },
        lazy: () =>
          import('./Pages/PublicRunPlayground').then((m) => ({
            element: <m.PublicRunPlayground />,
          })),
      },
      {
        path: `${appPublicPath}/:shareToken/${appRunPath}/:runId?`,
        handle: {
          crumb: <PublicRunCrumb />,
          public: true,
        },
        lazy: () =>
          import('./Pages/PublicRun').then((m) => ({
            element: <m.PublicRun />,
          })),
      },
      {
        // Public Datasets
        path: `${appPublicPath}/:datasetShareToken/${appPublicDatasetsPath}`,
        handle: {
          crumb: <PublicDatasetCrumb />,
          public: true,
        },
        children: [
          {
            index: true,
            handle: {
              public: true,
            },
            lazy: () =>
              import('./Pages/PublicDataset').then((m) => ({
                element: <m.PublicDataset />,
              })),
          },
          {
            // Public Examples
            path: `:exampleId/${appExamplePath}`,
            handle: {
              crumb: <PublicExampleCrumb />,
              public: true,
            },
            lazy: () =>
              import('./Pages/PublicExample').then((m) => ({
                element: <m.PublicExample />,
              })),
          },
          {
            path: `${appComparePath}`,
            handle: {
              public: true,
              crumb: <DatasetSessionCompareCrumb />,
            },
            lazy: () =>
              import('./Pages/PublicDatasetSessionCompare').then((m) => ({
                element: <m.default />,
              })),
          },
          {
            // Public Projects
            // e.g., /public/datasetShareToken/d/sessionId/p
            path: `:sessionId/${appSessionPath}`,
            handle: {
              crumb: <PublicProjectCrumb />,
              public: true,
            },
            children: [
              {
                index: true,
                handle: {
                  public: true,
                },
                lazy: () =>
                  import('./Pages/PublicProject').then((m) => ({
                    element: <m.default />,
                  })),
              },
              {
                // e.g., /public/datasetShareToken/d/sessionId/p/r/runId
                path: `${appRunPath}/:runId`,
                handle: {
                  crumb: <PublicDatasetRunCrumb />,
                  public: true,
                },
                children: [
                  {
                    index: true,
                    lazy: () =>
                      import('./Pages/PublicDatasetRun').then((m) => ({
                        element: <m.default />,
                      })),
                    handle: {
                      public: true,
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        path: `${appPublicPath}/:datasetShareToken/${appPublicDatasetsPath}/:sessionId/${appSessionPath}/${appRunPath}/:runId/${appPlaygroundPath}`,
        lazy: () =>
          import('./Pages/PublicDatasetRunPlayground').then((m) => ({
            element: <m.default />,
          })),
        handle: {
          public: true,
          crumb: <PublicDatasetRunPlaygroundCrumb />,
        },
      },
      {
        path: 'reset-password',
        handle: {
          public: true,
        },
        lazy: () =>
          import('./Pages/ResetPassword').then((m) => ({
            element: <m.default />,
          })),
      },
      {
        path: 'accept-invite',
        handle: {
          public: false,
        },
        lazy: () =>
          import('./Pages/AcceptInvite').then((m) => ({
            element: <m.AcceptInvite />,
          })),
      },
      {
        path: 'sso/login/:ssoLoginSlug',
        handle: {
          public: true,
        },
        lazy: () =>
          import('./Pages/SSO/SSOLogin').then((m) => ({
            element: <m.default />,
          })),
      },
      {
        path: 'sso/email-verification/confirm',
        lazy: () =>
          import('./Pages/SSO/SSOEmailVerification').then((m) => ({
            element: <m.default />,
          })),
      },
      {
        path: 'confirm-signup',
        handle: {
          public: true,
          fullPage: true,
        },
        lazy: () =>
          import('./Pages/ConfirmSignup').then((m) => ({
            element: <m.default />,
          })),
      },
      {
        path: '*?',
        element: (
          <div className="px-4 py-6 pt-3">
            <RedirectNoMatch>
              <NoMatch />
            </RedirectNoMatch>
          </div>
        ),
      },
    ],
  },
];

routes.push(
  // For Hub
  {
    path: appHubIndexPath,
    element: <App />,
    errorElement: <RouteErrorElement />,
    children: [
      {
        lazy: () => {
          return import('./Pages/Hub/HubLayout').then((m) => ({
            element: <m.HubLayout />,
          }));
        },
        handle: {
          crumb: <HubCrumb />,
          public: true,
        },
        children: [
          // Home
          {
            index: true,
            lazy: () => {
              return import('./Pages/Hub').then((m) => ({
                element: <m.default />,
              }));
            },
            handle: { public: true, hideSearch: true },
          },
          // Search
          {
            path: 'search',
            lazy: () =>
              import('./Pages/HubSearch').then((m) => ({
                element: <m.default />,
              })),
            handle: { crumb: <HubSearchCrumb />, public: true },
          },
          // Tenant Profile
          {
            path: ':owner',
            handle: { crumb: <HubTenantCrumb />, public: true },
            children: [
              {
                index: true,
                lazy: () =>
                  import('./Pages/HubTenant').then((m) => ({
                    element: <m.default />,
                  })),
                handle: { public: true, hideSearch: true },
              },
              // Repo Page
              {
                path: ':repo',
                handle: { crumb: <HubRepoCrumb />, public: true },
                children: [
                  {
                    index: true,
                    lazy: () =>
                      import('./Pages/HubRepo').then((m) => ({
                        element: <m.default inHub={true} />,
                      })),
                    handle: { public: true, hideSearch: true },
                  },
                  {
                    path: 'playground',
                    handle: {
                      crumb: <HubPlaygroundCrumb />,
                      public: true,
                      hideSearch: true,
                    },
                    lazy: () =>
                      import('./Pages/HubPlayground/HubPlayground').then(
                        (m) => ({
                          element: <m.HubPlayground />,
                        })
                      ),
                  },
                  {
                    path: 'edit',
                    handle: { crumb: <HubEditRepoCrumb />, hideSearch: true },
                    lazy: () =>
                      import('./Pages/HubEditRepo').then((m) => ({
                        element: <m.default inHub />,
                      })),
                  },
                  {
                    path: 'fork',
                    handle: { crumb: <HubForkRepoCrumb />, hideSearch: true },
                    lazy: () =>
                      import('./Pages/HubForkRepo/HubForkRepo').then((m) => ({
                        element: <m.HubForkRepo inHub />,
                      })),
                  },
                  {
                    path: ':commit',
                    handle: {
                      crumb: <HubCommitCrumb />,
                      public: true,
                      hideSearch: true,
                    },
                    lazy: () =>
                      import('./Pages/HubRepo').then((m) => ({
                        element: <m.default inHub={true} />,
                      })),
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  }
);

// prompts
routes.push({
  path: appPromptsIndexPath,
  element: <App />,
  errorElement: <RouteErrorElement />,
  children: [
    {
      lazy: () => {
        return import('./Pages/Prompts').then((m) => ({
          element: <m.default />,
        }));
      },
      handle: {
        crumb: <PromptsCrumb />,
        public: false,
      },
      children: [
        {
          index: true,
          lazy: () => {
            return import('./Pages/Prompts/components/PromptsPage').then(
              (m) => ({
                element: <m.default />,
              })
            );
          },
          handle: { public: false },
        },
        // Tenant Profile
        {
          children: [
            {
              index: true,
              lazy: () =>
                import('./Pages/HubTenant').then((m) => ({
                  element: <m.default />,
                })),
              handle: { public: false },
            },
            // Repo Page
            {
              path: ':repo',
              handle: {
                crumb: <PromptRepoCrumb />,
                public: false,
                inAssignMode: true,
              },
              children: [
                {
                  index: true,
                  lazy: () =>
                    import('./Pages/HubRepo').then((m) => ({
                      element: <m.default inHub={false} />,
                    })),
                  handle: { public: false },
                },
                {
                  path: 'playground',
                  handle: {
                    crumb: <HubPlaygroundCrumb />,
                    public: false,
                    hideSearch: true,
                  },
                  lazy: () =>
                    import('./Pages/HubPlayground/HubPlayground').then((m) => ({
                      element: <m.HubPlayground />,
                    })),
                },
                {
                  path: 'edit',
                  handle: { crumb: <HubEditRepoCrumb />, hideSearch: true },
                  lazy: () =>
                    import('./Pages/HubEditRepo').then((m) => ({
                      element: <m.default inHub={false} />,
                    })),
                },
                {
                  path: 'fork',
                  handle: { crumb: <HubForkRepoCrumb />, hideSearch: true },
                  lazy: () =>
                    import('./Pages/HubForkRepo/HubForkRepo').then((m) => ({
                      element: <m.HubForkRepo inHub={false} />,
                    })),
                },
                {
                  path: ':commit',
                  handle: {
                    crumb: <HubCommitCrumb />,
                    public: false,
                    hideSearch: true,
                  },
                  lazy: () =>
                    import('./Pages/HubRepo').then((m) => ({
                      element: <m.default inHub={false} />,
                    })),
                },
              ],
            },
          ],
        },
      ],
    },
  ],
});

if (hostEnabled === '1') {
  routes[0].children?.[0].children?.push(
    // Host
    {
      path: hostAppIndexPath,
      children: [
        {
          index: true,
          loader: () => {
            return redirect(`${deploymentsPath}`);
          },
        },
        {
          path: deploymentsPath,
          handle: {
            crumb: <HostProjectsCrumb />,
          },
          children: [
            {
              index: true,
              handle: { universal: true },
              lazy: () =>
                import('./Pages/HostProjects/HostProjects').then((m) => ({
                  element: <m.default />,
                })),
            },
            {
              path: `:hostProjectId`,
              handle: {
                crumb: <HostProjectCrumb />,
                inAssignMode: true,
              },
              children: [
                {
                  index: true,
                  lazy: () =>
                    import('./Pages/HostProject/HostProject').then((m) => ({
                      element: <m.default />,
                    })),
                },
                {
                  path: `${hostAppRevisionPath}/:revisionId`,
                  handle: { crumb: <HostRevisionCrumb /> },
                  lazy: () =>
                    import('./Pages/HostRevision/HostRevision').then((m) => ({
                      element: <m.default />,
                    })),
                },
              ],
            },
            {
              path: newPath,
              handle: {
                crumb: <HostNewCrumb />,
              },
              lazy: () =>
                import('./Pages/HostNew/HostNew').then((m) => ({
                  element: <m.default />,
                })),
            },
          ],
        },
      ],
    }
  );
}

routes[0].children?.push({
  path: appGraphIndexPath,
  handle: { public: graphPublic, crumb: <GraphCrumb /> },
  lazy: () =>
    import('./Pages/Graph/GraphLayout').then((m) => ({
      element: <m.GraphLayout />,
    })),
  children: [
    {
      path: '',
      handle: { public: graphPublic },
      loader: ({ request }) => {
        const params = new URLSearchParams(request.url.split('?')[1]);
        const query = params.size > 0 ? `?${params.toString()}` : '';
        return redirect(`/${appGraphIndexPath}/thread${query}`);
      },
    },
    {
      path: 'thread?',
      handle: { public: graphPublic },
      lazy: () =>
        import('./Pages/Graph/index').then((m) => ({
          element: <m.StudioPage />,
        })),
    },
    {
      path: 'auth',
      lazy: () =>
        import('./Pages/Graph/src/desktop/GraphDesktopAuthPage').then((m) => ({
          element: <m.GraphDesktopAuthPage />,
        })),
    },
  ],
});

if (backendAuthType === 'oauth' || mixedLoginMethods.customOidc) {
  routes[0]?.children?.push({
    path: 'oauth-callback',
    handle: { public: true },
    element: <OauthCallback />,
  });
}
// https://reactrouter.com/en/main/routers/create-browser-router
const router = createBrowserRouter(routes, { basename: baseUrl });

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(<RouterProvider router={router} />);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
