// Code from: https://reactflow.dev/examples/edges/floating-edges
import dagre from 'dagre';
import { useEffect } from 'react';
import {
  MarkerType,
  Position,
  Edge as ReactFlowEdge,
  ReactFlowInstance,
  Node as ReactFlowNode,
} from 'reactflow';

import { getCollapsedGraph } from '@/Pages/Graph/src/components/debug/graph/subgraph.utils';
import { Graph } from '@/Pages/Graph/src/data/misc';

export const hasNoPosition = (node: ReactFlowNode) =>
  node.position.x === 0 && node.position.y === 0;

export const getSelfLinkedPath = (sourceNode: ReactFlowNode) => {
  const position = sourceNode.positionAbsolute;
  const width = sourceNode.width;
  const height = sourceNode.height;

  if (!position || !width || !height)
    return ['', 0, 0] as [string, number, number];

  const startX = position.x + width;
  const startY = position.y + height - 6;

  const endX = position.x + width;
  const endY = position.y + 6;

  const labelX = position.x + width + 30;
  const labelY = position.y + height / 2;

  const path = [
    `M ${startX} ${startY}`,
    `C ${startX + 40} ${startY + 30} ${endX + 40} ${endY - 30} ${endX} ${endY}`,
  ].join(' ');

  return [path, labelX, labelY] as [string, number, number];
};

export const getBidirectionPath = ({
  sourceX,
  sourceY,
  targetX,
  targetY,
}: {
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
}) => {
  const centerX = (sourceX + targetX) / 2;
  const centerY = (sourceY + targetY) / 2;

  const diffX = targetX - sourceX;
  const diffY = targetY - sourceY;

  const sgn = Math.sign(diffX) === Math.sign(diffY) ? -1 : 1;

  const offsetX = centerX - (sgn * diffY) / 2;
  const offsetY = centerY + (sgn * diffX) / 2;

  const labelX = 0.25 * sourceX + 0.5 * offsetX + 0.25 * targetX;
  const labelY = 0.25 * sourceY + 0.5 * offsetY + 0.25 * targetY;

  return [
    `M ${sourceX} ${sourceY} Q ${offsetX} ${offsetY} ${targetX} ${targetY}`,
    labelX,
    labelY,
  ] as [string, number, number];
};

// returns the parameters (sx, sy, tx, ty, sourcePos, targetPos) you need to create an edge
export function getEdgeParams(source: ReactFlowNode, target: ReactFlowNode) {
  const sourceIntersectionPoint = getNodeIntersection(source, target);
  const targetIntersectionPoint = getNodeIntersection(target, source);

  const sourcePos = getEdgePosition(source, sourceIntersectionPoint);
  const targetPos = getEdgePosition(target, targetIntersectionPoint);

  return {
    sx: sourceIntersectionPoint.x,
    sy: sourceIntersectionPoint.y,
    tx: targetIntersectionPoint.x,
    ty: targetIntersectionPoint.y,
    sourcePos,
    targetPos,
  };
}

// returns the position (top,right,bottom or right) passed node compared to the intersection point
function getEdgePosition(
  node: ReactFlowNode,
  intersectionPoint: { x: number; y: number }
) {
  const n = { ...node.positionAbsolute, ...node };

  if (
    typeof n.x !== 'number' ||
    typeof n.y !== 'number' ||
    typeof n.width !== 'number' ||
    typeof n.height !== 'number'
  ) {
    return Position.Top;
  }

  const nx = Math.round(n.x);
  const ny = Math.round(n.y);
  const px = Math.round(intersectionPoint.x);
  const py = Math.round(intersectionPoint.y);

  if (px <= nx + 1) {
    return Position.Left;
  }
  if (px >= nx + n.width - 1) {
    return Position.Right;
  }
  if (py <= ny + 1) {
    return Position.Top;
  }
  if (py >= n.y + n.height - 1) {
    return Position.Bottom;
  }

  return Position.Top;
}

// this helper function returns the intersection point
// of the line between the center of the intersectionNode and the target node
function getNodeIntersection(
  intersectionNode: ReactFlowNode,
  targetNode: ReactFlowNode
) {
  // https://math.stackexchange.com/questions/1724792/an-algorithm-for-finding-the-intersection-point-between-a-center-of-vision-and-a
  const {
    width: intersectionNodeWidth,
    height: intersectionNodeHeight,
    positionAbsolute: intersectionNodePosition,
  } = intersectionNode;

  const targetPosition = targetNode.positionAbsolute;

  if (
    typeof intersectionNodeWidth !== 'number' ||
    typeof intersectionNodeHeight !== 'number' ||
    intersectionNodePosition == null ||
    targetPosition == null ||
    typeof targetNode.width !== 'number' ||
    typeof targetNode.height !== 'number'
  ) {
    return { x: 0, y: 0 };
  }

  const w = intersectionNodeWidth / 2;
  const h = intersectionNodeHeight / 2;

  const x2 = intersectionNodePosition.x + w;
  const y2 = intersectionNodePosition.y + h;
  const x1 = targetPosition.x + targetNode.width / 2;
  const y1 = targetPosition.y + targetNode.height / 2;

  const xx1 = (x1 - x2) / (2 * w) - (y1 - y2) / (2 * h);
  const yy1 = (x1 - x2) / (2 * w) + (y1 - y2) / (2 * h);

  const a = 1 / (Math.abs(xx1) + Math.abs(yy1) + 1e-32);
  const xx3 = a * xx1;
  const yy3 = a * yy1;
  const x = w * (xx3 + yy3) + x2;
  const y = h * (-xx3 + yy3) + y2;

  return { x, y };
}

export const layoutGraph = (
  nodes: ReactFlowNode[],
  edges: ReactFlowEdge[],
  subgraphs: string[],
  needsNodePositions?: boolean
) => {
  const g = new dagre.graphlib.Graph({ compound: true });

  const newNodes = [...nodes];
  const newEdges = [...edges];

  g.setGraph({ rankdir: 'TB' });
  g.setDefaultEdgeLabel(() => ({}));

  newNodes.forEach((node) => {
    if (subgraphs.includes(node.id)) {
      g.setNode(node.id, { height: 32 });
    } else {
      g.setNode(node.id, {
        width: Math.max(0, node.data.label.length * 7 + 64),
        height: 32,
      });
    }
    if (node.parentId) g.setParent(node.id, node.parentId);
  });

  newEdges.forEach((edge) => {
    g.setEdge(edge.source, edge.target);
  });

  dagre.layout(g);

  newNodes.forEach((node) => {
    const nodeWithPosition = g.node(node.id);

    let nodeX = nodeWithPosition.x - nodeWithPosition.width / 2;
    let nodeY = nodeWithPosition.y - nodeWithPosition.height / 2;

    const parent = node.parentId ? g.node(node.parentId) : null;
    if (parent) {
      nodeX -= parent.x - parent.width / 2;
      nodeY -= parent.y - parent.height / 2;
    }
    if (needsNodePositions || hasNoPosition(node)) {
      node.position = { x: nodeX, y: nodeY };
    } else {
      node.position = { x: node.position.x, y: node.position.y };
    }

    node.style = {
      ...node.style,
      width: nodeWithPosition.width,
      height: nodeWithPosition.height,
    };

    return node;
  });

  return { nodes: newNodes, edges: newEdges };
};

export const useInitializeGraph = ({
  graphData,
  subgraphSelection,
  setNodes,
  setEdges,
  needsInitialLayoutRef,
  nodePositions,
  setNodePositions,
}: {
  graphData: Graph | undefined;
  subgraphSelection: Set<string>;
  setNodes: (
    nodes: ReactFlowNode[] | ((nodes: ReactFlowNode[]) => ReactFlowNode[])
  ) => void;
  setEdges: (
    edges: ReactFlowEdge[] | ((edges: ReactFlowEdge[]) => ReactFlowEdge[])
  ) => void;
  needsInitialLayoutRef?: React.MutableRefObject<boolean>;
  nodePositions?: Record<string, { x: number; y: number }>;
  setNodePositions?: (
    positions: Record<string, { x: number; y: number }>
  ) => void;
}) => {
  // reset node positions when subgraph selection changes
  useEffect(() => {
    if (needsInitialLayoutRef) {
      needsInitialLayoutRef.current = true;
      setNodePositions?.({});
    }
  }, [subgraphSelection]);

  // Process graph data to create nodes and edges
  useEffect(() => {
    if (!graphData) return;
    const collapsed = getCollapsedGraph(
      graphData,
      Array.from(subgraphSelection)
    );

    setNodes([]);
    setEdges([]);

    const animationFrameId = window.requestAnimationFrame(() => {
      // now, we convert graph nodes into react-flow objects
      const flowNodes: ReactFlowNode[] = [];
      // let flowEdges: ReactFlowEdge[] = [];
      const presentSubgraphs = new Set<string>();
      for (const node of collapsed.nodes) {
        const segments = node.id.split(':');
        const parentIds = segments.slice(0, -1);
        const id = segments.at(-1)!;

        const subgraphId = parentIds.join(':');

        const isInSubgraph = (id: string) =>
          collapsed.subgraphs.includes(id) && subgraphSelection.has(id);

        let label = node.id;
        if (isInSubgraph(subgraphId)) {
          presentSubgraphs.add(subgraphId);

          // Add all ancestor subgraphs as well
          for (let i = 1; i < parentIds.length; i++) {
            const ancestorSubgraphId = parentIds.slice(0, i).join(':');
            if (isInSubgraph(ancestorSubgraphId)) {
              presentSubgraphs.add(ancestorSubgraphId);
            }
          }

          label = id;
        }

        flowNodes.push({
          id: node.id,
          position: { x: 0, y: 0 },
          data: {
            label,
            meta: node.data,
            isSubgraph: collapsed.subgraphs.includes(node.id),
          },
          parentId: isInSubgraph(subgraphId) ? subgraphId : undefined,
          type: 'node',
          deletable: false,
          extent: isInSubgraph(subgraphId) ? 'parent' : undefined,
        });
      }

      const maxDepth = Math.max(
        ...collapsed.subgraphs.map((i) => i.split(':').length)
      );

      for (const subgraph of collapsed.subgraphs) {
        if (!presentSubgraphs.has(subgraph)) continue;
        const segments = subgraph.split(':');
        const parentId = segments.slice(0, -1).join(':') || undefined;

        flowNodes.unshift({
          id: segments.join(':'),
          parentId,
          data: { label: segments.at(-1) || subgraph },
          position: { x: 0, y: 0 },
          type: 'subgraph',
          style: {
            border: 'none',
            background: 'transparent',
            boxShadow: 'none',
            zIndex: -1 - maxDepth + segments.length,
          },
          deletable: false,
          extent: parentId ? 'parent' : undefined,
        });
      }

      // Set edges immediately after nodes
      const flowEdges = collapsed.edges.map((edge) => ({
        type: 'edge',
        id: `${edge.source}-${edge.target}`,
        source: edge.source,
        target: edge.target,
        sourceHandle: `${edge.source}-source`,
        targetHandle: `${edge.target}-target`,
        data: { meta: edge.data, conditional: edge.conditional },
        deletable: false,
        markerEnd: { type: MarkerType.ArrowClosed, width: 20, height: 20 },
        style: { strokeDasharray: edge.conditional ? '3 1' : undefined },
      }));

      // // Only apply layout to nodes that don't have saved positions
      const nodesToLayout = flowNodes.map((node) => {
        const position = nodePositions?.[node.id] || node.position;
        return { ...node, position };
      });

      const layout = layoutGraph(
        nodesToLayout,
        flowEdges,
        collapsed.subgraphs.filter((i) => subgraphSelection.has(i)),
        needsInitialLayoutRef?.current
      );
      setNodes(
        (nodes) => layout.nodes?.map((n, i) => ({ ...nodes[i], ...n })) ?? []
      );
      setEdges(layout.edges ?? []);
      if (needsInitialLayoutRef) {
        needsInitialLayoutRef.current = false;
      }
    });
    return () => {
      window.cancelAnimationFrame(animationFrameId);
    };
  }, [
    graphData,
    setNodes,
    setEdges,
    subgraphSelection,
    needsInitialLayoutRef,
    nodePositions,
  ]);
};

export const useResizeGraph = (
  ref: React.RefObject<HTMLDivElement>,
  instanceRef: React.RefObject<ReactFlowInstance>
) => {
  useEffect(() => {
    if (ref.current) {
      let timerRef = 0;
      const resizeObserver = new ResizeObserver(() => {
        if (ref.current) {
          window.clearTimeout(timerRef);
          timerRef = window.setTimeout(() => {
            instanceRef.current?.fitView({ duration: 300 });
          }, 100);
        }
      });
      resizeObserver.observe(ref.current);
      return () => resizeObserver.disconnect();
    }
  }, []);
};
