import omit from 'lodash/omit';

import { EMessageType } from '@/components/EditableMessage/types';
import { MessageProps } from '@/components/Message';
import { isSerializedConstructor } from '@/utils/serialized';

import {
  Generation,
  MessageContentPart,
  MessageFields,
  MessageTuple,
  MessageUnionType,
  OnChatModelStreamEvent,
  OpenAIMessage,
  OpenAIResponsesAPIOutput,
  RunOutputSchema,
  RunSchema,
  SerializedMessage,
  StoredMessage,
  StreamEvent,
  UsageMetadata,
  isMessageContentPartOpenAIAudio,
  isMessageContentPartOpenAIFile,
} from '../types/schema';

export function getMessageAsStoredMessage(
  message: MessageUnionType | undefined
): StoredMessage | undefined {
  const type = getMessageType(message),
    data = getMessageFields(message);
  if (!message || !type || !data) return;
  return { type, data };
}

export function getMessageAsSerializedMessage(
  message: unknown
): SerializedMessage | undefined {
  if (!isMessageLike(message)) return undefined;

  const type = getMessageType(message);
  const data = getMessageFields(message);

  if (!type || !data) return undefined;
  return {
    lc: 1,
    id: [
      'langchain',
      'schema',
      type[0].toUpperCase() + type.slice(1) + 'Message',
    ],
    type: 'constructor',
    kwargs: data,
  };
}

export function isMessageLike(x: unknown): x is MessageUnionType {
  return (
    isStoredMessage(x) ||
    isSerializedMessage(x) ||
    isMessageTuple(x) ||
    isMessageFields(x) ||
    isOpenAIMessage(x) ||
    isOpenAIResponsesAPIOutput(x)
  );
}

function isMessageFields(
  data: unknown,
  inferredType?: string
): data is MessageFields {
  if (typeof data !== 'object' || data == null || !('content' in data))
    return false;

  const content = data?.['content'];
  if (
    Array.isArray(content) &&
    content.every((x) => typeof x === 'string' || isImageObject(x))
  ) {
    return true;
  }

  const type = getMessageType(data) ?? inferredType;

  const containsToolCalls =
    type === 'assistant' &&
    !!(
      ('additional_kwargs' in data &&
        typeof data['additional_kwargs'] === 'object' &&
        data.additional_kwargs &&
        'tool_calls' in data.additional_kwargs &&
        Array.isArray(data.additional_kwargs.tool_calls)) ||
      ('tool_calls' in data && Array.isArray(data.tool_calls))
    );

  return (
    (typeof content === 'string' && !!type) ||
    !!(type && ['tool', 'function'].includes(type)) ||
    containsToolCalls
  );
}

export function isStoredMessage(x: unknown): x is StoredMessage {
  if (typeof x !== 'object' || x == null) return false;
  if (!('data' in x) || typeof x.data !== 'object' || x.data == null) {
    return false;
  }

  let type: string | undefined = undefined;
  if ('type' in x && typeof x.type === 'string') {
    type = x.type;
  }

  if ('role' in x && typeof x.role === 'string') {
    type = x.role;
  }

  if (!type) return false;
  return isMessageFields(x.data, type);
}

function isImageObject(x: unknown): x is MessageContentPart {
  if (typeof x !== 'object' || x == null) return false;
  if (!('type' in x) || typeof x.type !== 'string') return false;
  return true;
}

function isSerializedMessage(x: unknown): x is SerializedMessage {
  if (!isSerializedConstructor(x)) return false;
  if (!('content' in x.kwargs)) return false;

  if (
    Array.isArray(x.kwargs.content) &&
    x.kwargs.content.every((x) => typeof x === 'string' || isImageObject(x))
  ) {
    return true;
  }
  return typeof x.kwargs.content === 'string';
}

const ACCEPTED_MESSAGE_ROLES = [
  'human',
  'system',
  'ai',
  'user',
  'assistant',
  'function',
  'tool',
  'chat',
  'platform',
  'developer',
];

function isMessageTuple(x: unknown): x is MessageTuple {
  if (!Array.isArray(x)) return false;
  if (x.length !== 2) return false;
  if (typeof x[0] !== 'string') return false;
  if (typeof x[1] !== 'string') return false;

  // adding this check in because mustache allows input of a list with two string elements ex. ["a", "b"]
  // which is the same structure that we use for messages ex. ["user", "hi"]
  // as an incomplete fix, we check if the first element is an accepted role
  // this breaks when the first string element is an accepted role and does not intend to be a message
  const isRoleAccepted = ACCEPTED_MESSAGE_ROLES.some(
    (role) => role.toLowerCase() === x[0].toLowerCase()
  );
  if (!isRoleAccepted) return false;

  return true;
}

export function isOpenAIMessage(x: unknown): x is OpenAIMessage {
  if (typeof x !== 'object' || x == null) return false;

  if (
    !('role' in x) ||
    !('content' in x || 'tool_calls' in x || 'function_call' in x)
  ) {
    return false;
  }

  return true;
}

export function isOpenAIResponsesAPIOutput(
  x: unknown
): x is OpenAIResponsesAPIOutput {
  if (typeof x !== 'object' || x == null) return false;

  if (
    'type' in x &&
    typeof x.type === 'string' &&
    [
      'function_call',
      'computer_call',
      'computer_call_output',
      'function_call_output',
      'message',
      'reasoning',
      'web_search_call',
      'file_search_call',
    ].includes(x.type)
  ) {
    return true;
  }

  return false;
}

function getLangSmithRoleFromOpenAIRole(x: string): string {
  if (x === 'user') {
    return 'human';
  } else if (x === 'assistant') {
    return 'ai';
  } else {
    return x;
  }
}

export function getOpenAIRoleFromLangSmithRole(x: string): string {
  if (x === 'human') {
    return 'user';
  } else if (x === 'ai') {
    return 'assistant';
  } else {
    return x;
  }
}

export function getMessageType(
  message: MessageUnionType | Record<string, any> | undefined
): EMessageType | undefined {
  let type: string | undefined;
  if (!message || typeof message !== 'object') return;
  if ('lc' in message) {
    const serializedMessage = message as SerializedMessage;
    type = serializedMessage.id?.[serializedMessage.id.length - 1]
      ?.split('Message', 2)
      ?.at(0)
      ?.toLowerCase();
  } else if (isMessageTuple(message)) {
    type = message[0];
  } else if (isOpenAIResponsesAPIOutput(message)) {
    if ('role' in message) {
      type = getLangSmithRoleFromOpenAIRole(message.role);
    } else {
      type = message.type;
    }
  } else if (isOpenAIMessage(message)) {
    type = getLangSmithRoleFromOpenAIRole(message.role);
  } else if ('type' in message && message.type != null) {
    type = message.type;
  } else {
    type = message.role;
  }
  if (typeof type !== 'string') {
    return undefined;
  }
  if (type?.toLowerCase()?.endsWith('messagechunk')) {
    type = type.slice(0, -12);
  } else if (type?.includes('_')) {
    type = type.split('_').join(' ');
  }
  return type?.toLowerCase() as EMessageType;
}

export function addToolCallToMessage(
  message: MessageUnionType | undefined,
  name: string,
  id: string,
  args: Record<string, any>
): MessageUnionType | undefined {
  if (!message || typeof message !== 'object') return;
  if ('lc' in message) {
    const newMessage = { ...message };
    const newToolCall = {
      name,
      id,
      args,
      type: 'tool_call',
    };
    if (newMessage.kwargs && 'tool_calls' in newMessage.kwargs) {
      newMessage.kwargs = {
        ...newMessage.kwargs,
        tool_calls: [...(newMessage.kwargs.tool_calls ?? []), newToolCall],
      };
    } else {
      newMessage.kwargs = { ...newMessage.kwargs, tool_calls: [newToolCall] };
    }
    return newMessage;
  } else if (isMessageTuple(message)) {
    return {
      role: message[0],
      content: message[1],
      tool_calls: [
        {
          id: id,
          function: {
            arguments: JSON.stringify(args),
            name: name,
          },
          type: 'function',
        },
      ],
    } as MessageUnionType;
  } else if (isOpenAIMessage(message)) {
    return {
      ...message,
      tool_calls: [
        ...(message['tool_calls'] ?? []),
        {
          id: id,
          function: {
            arguments: JSON.stringify(args),
            name: name,
          },
          type: 'function',
        },
      ],
    } as MessageUnionType;
  } else {
    return {
      role: getOpenAIRoleFromLangSmithRole(
        getMessageType(message) ?? 'assistant'
      ),
      content: getMessageContent(message),
      tool_calls: [
        {
          id: '',
          function: {
            arguments: JSON.stringify(args),
            name: '',
          },
          type: 'function',
        },
      ],
    } as MessageUnionType;
  }
}

export function setMessageType(
  message: MessageUnionType | Record<string, any> | undefined,
  type: string
): MessageUnionType | Record<string, any> | undefined {
  if (!message || typeof message !== 'object') return;
  const newType = type.toLowerCase();

  if ('lc' in message) {
    const newMessage = { ...message };
    // Handle both the id array and the type in kwargs if it exists
    if (Array.isArray(newMessage.id)) {
      const lastId = newMessage.id[newMessage.id.length - 1] as string;
      const [_, messageType] = lastId.split('Message', 2);
      const typeToInsert =
        type === 'ai' ? 'AI' : type.slice(0, 1).toUpperCase() + type.slice(1);
      newMessage.id[newMessage.id.length - 1] = `${typeToInsert}Message${
        messageType || ''
      }`;
    }
    if (newMessage.kwargs && 'type' in newMessage.kwargs) {
      newMessage.kwargs = { ...newMessage.kwargs, type: newType };
    }
    return newMessage;
  } else if (isMessageTuple(message)) {
    return [newType, message[1]];
  } else if ('type' in message) {
    return { ...message, type: newType };
  } else if (isOpenAIMessage(message)) {
    return {
      ...message,
      role: getOpenAIRoleFromLangSmithRole(newType),
    };
  } else {
    return { ...message, role: newType };
  }
}

export function getMessageFields(
  message: MessageUnionType | undefined
): MessageFields | undefined {
  if (!message || typeof message !== 'object') return;
  if ('lc' in message) {
    // serialized message
    return message.kwargs;
  } else if ('data' in message) {
    // stored message
    return message.data;
  } else if (isMessageTuple(message)) {
    // message tuple
    return { content: message[1] };
  } else if (isOpenAIMessage(message)) {
    // openai message
    return {
      content: message.content ?? '',
      additional_kwargs: omit(message, 'role', 'content'),
    };
  } else if (isOpenAIResponsesAPIOutput(message)) {
    // openai responses api output
    return {
      content: 'content' in message ? message.content : '',
      additional_kwargs: omit(message, 'role', 'content'),
    };
  } else {
    // unknown message format
    return message;
  }
}

export function setMessageFields(
  message: MessageUnionType | undefined,
  fields: MessageFields
): MessageUnionType | undefined {
  if (!message || typeof message !== 'object') return;
  if ('lc' in message) {
    // serialized message
    return { ...message, kwargs: fields };
  } else if ('data' in message) {
    // stored message
    return { ...message, data: fields };
  } else if (isMessageTuple(message)) {
    // message tuple
    return [
      message[0],
      typeof fields.content === 'string' ? fields.content : '',
    ];
  } else if (isOpenAIMessage(message)) {
    // openai message
    const { content, additional_kwargs = {} } = fields;
    const baseMessage = {
      role: message.role,
      content: typeof content === 'string' ? content : '',
      ...('name' in message && { name: message.name }),
    };

    return Object.keys(additional_kwargs).length > 0
      ? { ...baseMessage, ...additional_kwargs }
      : baseMessage;
  } else {
    // unknown message format
    return fields;
  }
}

export function convertOpenAIMessageToStoredMessage(
  x: OpenAIMessage
): StoredMessage {
  const role =
    x.role === 'user' ? 'human' : x.role === 'assistant' ? 'ai' : x.role;
  const storedMessage = {
    type: role,
    role,
    data: {
      content: x.content ?? '',
      additional_kwargs: omit(x, 'role', 'content'),
    },
  };
  if (x.role === 'tool') {
    (storedMessage.data as any).tool_call_id = x.tool_call_id;
  }
  return storedMessage;
}

export function getMessageContent(
  message: MessageUnionType | undefined
): string | Array<MessageContentPart> | undefined {
  return getMessageFields(message)?.content;
}

export function setMessageContent(
  message: MessageUnionType | undefined,
  content: string | Array<MessageContentPart>
): MessageUnionType | undefined {
  if (!message || typeof message !== 'object') return;
  if ('lc' in message) {
    // serialized message
    return {
      ...message,
      kwargs: {
        ...message.kwargs,
        content,
      },
    };
  } else if ('data' in message) {
    // stored message
    return {
      ...message,
      data: {
        ...message.data,
        content,
      },
    };
  } else if (isMessageTuple(message)) {
    // message tuple
    return [message[0], content as string];
  } else if (isOpenAIMessage(message)) {
    // openai message
    return {
      ...message,
      content: content as string, // TODO: this can actually be message content part array?
    };
  } else if (isOpenAIResponsesAPIOutput(message)) {
    // openai responses api output
    return {
      ...message,
    };
  } else {
    // unknown message format, set content directly
    return {
      ...message,
      content,
    };
  }
}

export function getMessageContentAsText(message: MessageUnionType | undefined) {
  const content = getMessageContent(message);
  return content ? convertMessageContentToText(content) : content;
}

export function convertMessageContentToText(content: MessageFields['content']) {
  if (!Array.isArray(content)) return content;
  return content
    .map((i) => {
      switch (i.type) {
        case 'output_text':
        case 'text': {
          if (i.text && typeof i.text === 'object' && 'value' in i.text) {
            return i.text.value;
          }
          return i.text;
        }
        case 'image_url': {
          const image =
            typeof i.image_url === 'string' ? i.image_url : i.image_url?.url;
          return `![image](${image})`;
        }
        case 'input_audio': {
          if (isMessageContentPartOpenAIAudio(i)) {
            return `[audio](.${i.input_audio?.format})`;
          } else {
            return `[audio file]`;
          }
        }
        case 'file': {
          if (isMessageContentPartOpenAIFile(i)) {
            return `[${i.file?.filename}]`;
          } else {
            return `[file](${i.url})`;
          }
        }
      }
    })
    .join('\n');
}

export function maybeGetArrayOfMessages(x: unknown): MessageUnionType[] | null {
  if (isMessageLike(x)) return [x];
  if (!Array.isArray(x) || x.length === 0) return null;
  if (x.length !== 1) {
    if (x.every(isMessageLike)) {
      return x;
    } else {
      return null;
    }
  } else {
    if (isMessageLike(x[0])) {
      return x;
    } else if (Array.isArray(x[0])) {
      if (x[0].some((m) => !isMessageLike(m))) return null;
      return x[0];
    } else {
      return null;
    }
  }
}
export function maybeGetOpenAIJSInputMessages(inputs: RunSchema['inputs']) {
  if (inputs?.args) {
    const firstArg = inputs.args[0];
    return maybeGetArrayOfMessages(firstArg?.messages);
  }
  return null;
}

export function maybeGetOpenAIResponsesInputMessages(
  x: unknown
): MessageUnionType[] | null {
  if (!x || typeof x !== 'object') return null;

  const result: MessageUnionType[] = [];

  // Check for instruction (should be first in the array if present)
  if ('instructions' in x && typeof x.instructions === 'string') {
    result.push({
      role: 'system',
      content: x.instructions,
    });
  }

  // Check for input
  if ('input' in x) {
    const input = x.input;

    // Case: input is a string
    if (typeof input === 'string') {
      result.push({
        role: 'user',
        content: input,
      });
    }
    // Case: input is an array of objects
    else if (Array.isArray(input)) {
      const messages = maybeGetArrayOfMessages(input);
      if (!messages) {
        return null;
      }
      result.push(...messages);
    } else {
      // If input is neither a string nor a valid array, return null
      return null;
    }
  }

  // Return null if no valid messages were found
  return result.length > 0 ? result : null;
}

export function getFlattenedOutputs(
  x: Pick<
    RunOutputSchema,
    'generations' | 'choices' | 'output' | 'usage_metadata'
  >
) {
  return x.output &&
    typeof x.output === 'object' &&
    !x.generations &&
    !x.choices
    ? x.output
    : x;
}

export function getFlattenedArrayOfGenerations(
  x?: Pick<RunOutputSchema, 'generations' | 'choices' | 'output'>
): Generation[] {
  if (!x) return [];
  // if outputs.output is an object with either choices or generations and we don't have outputs.choices or outputs.generations, use that
  const output = getFlattenedOutputs(x);
  if (output.generations) {
    const generations = output.generations;
    return Array.isArray(generations) ? generations.flat() : [];
  } else if (output.choices) {
    const choices = output.choices;
    if (Array.isArray(choices)) {
      return choices.map((c) => {
        const msg = c.message;
        return {
          message: msg,
        } as Generation;
      });
    } else {
      return [{ message: choices }];
    }
  } else {
    return [];
  }
}

export function updateMessageInOutputs(
  outputs: RunOutputSchema,
  outputIndex: number,
  newMessage: MessageUnionType
): RunOutputSchema {
  // Create a deep copy of outputs to avoid mutations
  const newOutputs = JSON.parse(JSON.stringify(outputs));

  // Helper function to update message in generations array
  const updateGenerationMessage = (generations: Generation[]) => {
    const flatGenerations = generations.flat();
    if (
      flatGenerations[outputIndex] &&
      'message' in flatGenerations[outputIndex]
    ) {
      flatGenerations[outputIndex].message = newMessage;
    }
    return Array.isArray(generations[0]) ? [flatGenerations] : flatGenerations;
  };

  // Handle different output structures
  if (newOutputs.generations) {
    newOutputs.generations = updateGenerationMessage(newOutputs.generations);
  } else if (newOutputs.choices) {
    newOutputs.choices = newOutputs.choices.map((choice, index) => {
      if (index === outputIndex) {
        return { ...choice, message: newMessage };
      }
      return choice;
    });
  } else if (
    newOutputs.output &&
    typeof newOutputs.output === 'object' &&
    !Array.isArray(newOutputs.output)
  ) {
    // Handle nested output object
    if ('generations' in newOutputs.output) {
      newOutputs.output.generations = updateGenerationMessage(
        newOutputs.output.generations
      );
    } else if ('choices' in newOutputs.output) {
      newOutputs.output.choices = newOutputs.output.choices.map(
        (choice, index) => {
          if (index === outputIndex) {
            return { ...choice, message: newMessage };
          }
          return choice;
        }
      );
    }
  }

  return newOutputs;
}

export function getInvocationParams(
  run: Pick<RunSchema, 'inputs' | 'extra'> | undefined
) {
  return run?.extra?.['invocation_params'] ?? run?.inputs;
}

export function getTokensFromUsageMetadata(usageMetadata: UsageMetadata) {
  return {
    totalTokens: usageMetadata.total_tokens,
    promptTokens: usageMetadata.input_tokens,
    completionTokens: usageMetadata.output_tokens,
    reasoningTokens: usageMetadata.output_token_details?.reasoning,
    cacheReadTokens: usageMetadata.input_token_details?.cache_read,
    cacheCreationTokens: usageMetadata.input_token_details?.cache_creation,
    audioTokens: usageMetadata.input_token_details?.audio,
  };
}

function getDefaultTokens() {
  return {
    totalTokens: undefined,
    promptTokens: undefined,
    completionTokens: undefined,
    cacheReadTokens: undefined,
    cacheCreationTokens: undefined,
    reasoningTokens: undefined,
    audioTokens: undefined,
  };
}

export function getTokenUsage(
  run:
    | Pick<
        RunSchema,
        | 'outputs'
        | 'total_tokens'
        | 'prompt_tokens'
        | 'completion_tokens'
        | 'completion_token_details'
        | 'prompt_token_details'
      >
    | undefined
) {
  if (!run) return getDefaultTokens();

  if (run.prompt_token_details) {
    return {
      totalTokens: run.total_tokens,
      promptTokens: run.prompt_tokens,
      completionTokens: run.completion_tokens,
      promptTokenDetails: run.prompt_token_details,
      completion_token_details: run.completion_token_details,
      cacheReadTokens: undefined,
      cacheCreationTokens: undefined,
      reasoningTokens: undefined,
      audioTokens: undefined,
    };
  }

  if (run.outputs) {
    const outputMessage = getFlattenedArrayOfGenerations(run.outputs)?.[0]
      ?.message;

    if (isSerializedMessage(outputMessage)) {
      // case for all python langchain runs.
      if (outputMessage.kwargs.usage_metadata) {
        const usageMetadata = outputMessage.kwargs.usage_metadata;
        return getTokensFromUsageMetadata(usageMetadata);
      }
    }

    // case for wrapped_openai runs.
    const output = getFlattenedOutputs(run.outputs);
    const usageMetadata = output?.usage_metadata ?? run.outputs.usage_metadata;
    if (usageMetadata) {
      return getTokensFromUsageMetadata(usageMetadata);
    }
  }

  // old default
  if (typeof run.total_tokens === 'number') {
    return {
      totalTokens: run.total_tokens,
      promptTokens: run.prompt_tokens,
      completionTokens: run.completion_tokens,
      cacheReadTokens: undefined,
      cacheCreationTokens: undefined,
      reasoningTokens: undefined,
      audioTokens: undefined,
    };
  }

  return getDefaultTokens();
}

export function isOnChatModelStreamEvent(
  event: unknown
): event is OnChatModelStreamEvent {
  return !!(
    typeof event === 'object' &&
    event &&
    'event' in event &&
    'metadata' in event &&
    typeof event.metadata === 'object' &&
    event.metadata &&
    'data' in event &&
    typeof event.data === 'object' &&
    event.data &&
    event.event === StreamEvent.ON_CHAT_MODEL_STREAM &&
    'langgraph_node' in event.metadata &&
    'chunk' in event.data
  );
}

export function getThinkingTokens(props: MessageProps): string {
  let reasoningContent: string = '';
  // Try to extract from DeepSeek
  reasoningContent =
    props.fields?.additional_kwargs?.reasoning_content ?? reasoningContent;
  // Next, try to extract from Anthropic
  if (!reasoningContent && Array.isArray(props.content)) {
    reasoningContent = props.content.reduce(
      (previousValue, currentValue) =>
        previousValue +
        (Object.keys(currentValue).includes('thinking')
          ? currentValue['thinking']
          : ''),
      ''
    );
  }

  return reasoningContent;
}

export function getFunctionsUsed(outputs: RunOutputSchema): string[] {
  if (!outputs) return [];
  const generations = getFlattenedArrayOfGenerations(outputs);
  if (generations.length > 0) {
    return (
      getMessageAsSerializedMessage(
        generations[0].message
      )?.kwargs.tool_calls?.map((tool_call) => tool_call.name) ?? []
    );
  }
  if (
    'output' in outputs &&
    Array.isArray(outputs.output) &&
    outputs.output.every((x) => typeof x === 'object')
  ) {
    return outputs.output
      .map((item) => {
        return item.type == 'function_call'
          ? item.name
          : item.type == 'web_search_call'
          ? 'web_search_preview'
          : item.type == 'file_search_call'
          ? 'file_search'
          : item.type == 'computer_call'
          ? 'computer_use'
          : null;
      })
      .filter((name) => name !== null);
  }
  return [];
}
