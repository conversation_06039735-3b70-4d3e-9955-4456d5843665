import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { TraceTTLTier } from '@/types/schema';

import { isEvaluatorRunExpired } from '../isEvaluatorRunExpired';

describe('isEvaluatorRunExpired', () => {
  let fixedDate: Date;

  beforeEach(() => {
    // Mock the current date to be 2023-01-15
    fixedDate = new Date('2023-01-15T12:00:00Z');
    vi.useFakeTimers();
    vi.setSystemTime(fixedDate);
  });

  afterEach(() => {
    // Restore the original timer implementation
    vi.useRealTimers();
  });

  it('should return false when traceTier is null', () => {
    expect(isEvaluatorRunExpired(null, '2023-01-01T00:00:00Z')).toBe(false);
  });

  it('should return false when experimentStartTime is null', () => {
    expect(isEvaluatorRunExpired(TraceTTLTier.shortlived, null)).toBe(false);
  });

  it('should return false when both traceTier and experimentStartTime are null', () => {
    expect(isEvaluatorRunExpired(null, null)).toBe(false);
  });

  it('should return false for a shortlived experiment that is less than 14 days old', () => {
    // Start date: 2023-01-05 (10 days before mock current date)
    expect(
      isEvaluatorRunExpired(TraceTTLTier.shortlived, '2023-01-05T00:00:00Z')
    ).toBe(false);
  });

  it('should return true for a shortlived experiment that is more than 14 days old', () => {
    // Start date: 2022-12-25 (21 days before mock current date)
    expect(
      isEvaluatorRunExpired(TraceTTLTier.shortlived, '2022-12-25T00:00:00Z')
    ).toBe(true);
  });

  it('should return false for a longlived experiment that is less than 400 days old', () => {
    // Start date: 2022-01-01 (about 380 days before mock current date)
    expect(
      isEvaluatorRunExpired(TraceTTLTier.longlived, '2022-01-01T00:00:00Z')
    ).toBe(false);
  });

  it('should return true for a longlived experiment that is more than 400 days old', () => {
    // Start date: 2021-12-01 (more than 400 days before mock current date)
    expect(
      isEvaluatorRunExpired(TraceTTLTier.longlived, '2021-12-01T00:00:00Z')
    ).toBe(true);
  });
});
