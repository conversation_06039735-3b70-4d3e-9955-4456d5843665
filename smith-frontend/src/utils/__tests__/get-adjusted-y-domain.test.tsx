import { describe, expect, it } from 'vitest';

import { getAdjustedYDomain } from '../get-adjusted-y-domain';

describe('getAdjustedYDomain', () => {
  it('should handle equal positive values', () => {
    const result = getAdjustedYDomain(5, 5);
    expect(result.domain[0]).toBe(0);
    expect(result.domain[1]).toBeGreaterThan(5); // Should have padding
    expect(result.numTicks).toBeGreaterThanOrEqual(2);
  });

  it('should handle equal negative values', () => {
    const result = getAdjustedYDomain(-5, -5);
    expect(result.domain[0]).toBeLessThan(-5); // Should have padding
    expect(result.domain[1]).toBeGreaterThan(-5); // Should have padding
    expect(result.numTicks).toBeGreaterThanOrEqual(2);
  });

  it('should handle range with zero crossing', () => {
    const result = getAdjustedYDomain(-10, 10);
    expect(result.domain[0]).toBeLessThanOrEqual(-10);
    expect(result.domain[1]).toBeGreaterThanOrEqual(10);
    expect(result.numTicks).toBeGreaterThanOrEqual(2);
  });

  it('should respect minimum and maximum values', () => {
    const result = getAdjustedYDomain(0, 100, 0, 100);
    expect(result.domain[0]).toBe(0);
    expect(result.domain[1]).toBeGreaterThan(100); // Still adds padding even with min/max
  });

  it('should handle very small ranges', () => {
    const result = getAdjustedYDomain(0.001, 0.002);
    expect(result.domain[1] - result.domain[0]).toBeGreaterThanOrEqual(0.01);
    expect(result.numTicks).toBeGreaterThanOrEqual(2);
  });

  it('should handle very large ranges', () => {
    const result = getAdjustedYDomain(1000, 2000);
    expect(result.domain[1]).toBe(Math.ceil(result.domain[1] * 10) / 10);
    expect(result.numTicks).toBeLessThanOrEqual(6);
  });

  it('should ensure domain start is 0 when all values are positive', () => {
    const result = getAdjustedYDomain(10, 20);
    expect(result.domain[0]).toBe(0);
  });

  it('should maintain minimum spacing between domain start and end', () => {
    const result = getAdjustedYDomain(0.001, 0.001);
    expect(result.domain[1] - result.domain[0]).toBeGreaterThanOrEqual(0.01);
  });

  it('should calculate appropriate number of ticks', () => {
    const result = getAdjustedYDomain(0, 1);
    expect(result.numTicks).toBeGreaterThanOrEqual(2);
    expect(result.numTicks).toBeLessThanOrEqual(6);
  });

  describe('minimum and maximum value handling', () => {
    it('should use default padding when no min/max values provided', () => {
      const result = getAdjustedYDomain(0, 100);
      expect(result.domain[0]).toBe(0);
      expect(result.domain[1]).toBeGreaterThan(100); // Should have 15% padding
    });

    it('should use smaller padding when min/max values provided', () => {
      const result = getAdjustedYDomain(0, 100, 0, 100);
      expect(result.domain[0]).toBe(0);
      expect(result.domain[1]).toBeGreaterThan(100); // Still adds padding
    });

    it('should respect minimum value but allow padding for maximum', () => {
      const result = getAdjustedYDomain(0, 100, 0);
      expect(result.domain[0]).toBe(0);
      expect(result.domain[1]).toBeGreaterThan(100); // Should have padding
    });

    it('should respect maximum value but allow padding for minimum', () => {
      const result = getAdjustedYDomain(0, 100, undefined, 100);
      expect(result.domain[0]).toBe(0); // For positive values, domain starts at 0
      expect(result.domain[1]).toBeGreaterThan(100); // Should have padding
    });

    it('should handle partial min/max values with negative numbers', () => {
      const result = getAdjustedYDomain(-100, 100, -100);
      expect(result.domain[0]).toBeLessThan(-100); // Should have padding
      expect(result.domain[1]).toBeGreaterThan(100); // Should have padding
    });

    it('should handle partial min/max values with small numbers', () => {
      const result = getAdjustedYDomain(0.001, 0.002, 0.001);
      expect(result.domain[0]).toBe(0); // For positive values, domain starts at 0
      expect(result.domain[1] - result.domain[0]).toBeGreaterThanOrEqual(0.01);
    });
  });
});
