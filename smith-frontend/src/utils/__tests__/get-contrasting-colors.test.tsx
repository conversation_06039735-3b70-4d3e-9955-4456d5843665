import { describe, expect, it } from 'vitest';

import { getContrastingColorByString } from '../get-contrasting-colors';

describe('getContrastingColorByString', () => {
  it('should return a valid color string for a given input', () => {
    const color = getContrastingColorByString('test');
    expect(color).toMatch(/^rgb\(\d+,\s*\d+,\s*\d+\)$/);
  });

  it('should return consistent colors for the same input', () => {
    const color1 = getContrastingColorByString('test');
    const color2 = getContrastingColorByString('test');
    expect(color1).toBe(color2);
  });

  it('should return different colors for different inputs', () => {
    const color1 = getContrastingColorByString('test1');
    const color2 = getContrastingColorByString('test2');
    expect(color1).not.toBe(color2);
  });

  it('should handle empty string input', () => {
    const color = getContrastingColorByString('');
    expect(color).toMatch(/^rgb\(\d+,\s*\d+,\s*\d+\)$/);
  });

  it('should return rgba color when opacity is provided', () => {
    const color = getContrastingColorByString('test', 0.5);
    expect(color).toMatch(/^rgba\(\d+,\s*\d+,\s*\d+,\s*0\.5\)$/);
  });

  it('should handle different opacity values', () => {
    const color1 = getContrastingColorByString('test', 0.2);
    const color2 = getContrastingColorByString('test', 0.8);
    expect(color1).toMatch(/^rgba\(\d+,\s*\d+,\s*\d+,\s*0\.2\)$/);
    expect(color2).toMatch(/^rgba\(\d+,\s*\d+,\s*\d+,\s*0\.8\)$/);
  });
});
