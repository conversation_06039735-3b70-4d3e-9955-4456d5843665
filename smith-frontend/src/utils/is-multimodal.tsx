import { MessageContentPart, RunSchema } from '@/types/schema';

import {
  getFlattenedArrayOfGenerations,
  getMessageContent,
  maybeGetArrayOfMessages,
} from './messages';

function isMessageTextOnly(
  message: string | Array<MessageContentPart> | undefined
) {
  if (typeof message === 'string') return true;
  if (Array.isArray(message))
    return message.every(
      (i) => !['image_url', 'file', 'input_audio'].includes(i.type)
    );
  return true;
}

export function isRunMultimodal(run: RunSchema) {
  const messages = maybeGetArrayOfMessages(run.inputs?.messages);
  if (
    messages &&
    messages.some((i) => !isMessageTextOnly(getMessageContent(i)))
  ) {
    return true;
  }

  const generation = getFlattenedArrayOfGenerations(run.outputs)[0];
  if (
    generation?.['message'] != null &&
    !isMessageTextOnly(getMessageContent(generation['message']))
  ) {
    return true;
  }

  return false;
}
