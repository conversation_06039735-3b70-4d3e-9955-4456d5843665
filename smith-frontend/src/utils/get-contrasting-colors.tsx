import { interpolate<PERSON>ainbow } from 'd3-scale-chromatic';

/**
 * Maps strings to colors ensuring that similar strings get highly contrasting colors.
 * Uses a hash function that amplifies small differences in input strings.
 * @param str The input string to generate a color for
 * @param opacity Optional opacity value (0-1) to append to the color
 * @returns An rgba or hex color string depending on whether opacity is provided
 */
export function getContrastingColorByString(str: string, opacity?: number) {
  // Create a hash value from the string
  const hash = str.split('').reduce((acc, char, _) => {
    // Use prime numbers to create more variation
    return (acc + char.charCodeAt(0) * 31) % 1000;
  }, 0);

  // Convert hash to a float between 0-1
  const normalizedHash = hash / 1000;

  // Use the golden ratio to create well-distributed colors
  // The golden ratio (≈0.618033988749895) helps spread colors evenly
  const goldenRatioConjugate = 0.618033988749895;
  const hue = (normalizedHash + goldenRatioConjugate) % 1;

  // Get the color from the rainbow scale
  const color = interpolateRainbow(hue);

  // If opacity is provided, convert the color to rgba format
  if (opacity !== undefined) {
    // Parse the rgb values from the color string (format is "rgb(r, g, b)")
    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
      const [_, r, g, b] = rgbMatch;
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
  }

  // Return the original color if opacity wasn't provided or if color parsing failed
  return color;
}
