import { ReactNode, createContext, useContext } from 'react';

import { MessageCapability } from '@/types/schema';

interface MessageCapabilitiesContextType {
  disabledCapabilities: MessageCapability[];
  isCapabilityDisabled: (capability: MessageCapability) => boolean;
}

const MessageCapabilitiesContext = createContext<
  MessageCapabilitiesContextType | undefined
>(undefined);

interface MessageCapabilitiesProviderProps {
  children: ReactNode;
  disabledCapabilities: MessageCapability[];
}

export function MessageCapabilitiesProvider({
  children,
  disabledCapabilities,
}: MessageCapabilitiesProviderProps) {
  const isCapabilityDisabled = (capability: MessageCapability): boolean => {
    return disabledCapabilities.includes(capability);
  };

  return (
    <MessageCapabilitiesContext.Provider
      value={{
        disabledCapabilities,
        isCapabilityDisabled,
      }}
    >
      {children}
    </MessageCapabilitiesContext.Provider>
  );
}

export function useMessageCapabilities() {
  const context = useContext(MessageCapabilitiesContext);
  return context;
}
