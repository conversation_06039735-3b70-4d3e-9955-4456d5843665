export const getAdjustedYDomain = (
  smallestY: number,
  largestY: number,
  minimumValue?: number,
  maximumValue?: number
) => {
  let useDefaultDomain = false;
  let localSmallestY = smallestY;
  let localLargestY = largestY;

  if (smallestY === largestY && smallestY > 0) {
    localSmallestY = 0;
    useDefaultDomain = true;
  } else if (smallestY === largestY) {
    localLargestY = localLargestY + 1;
    useDefaultDomain = true;
  }
  const yDifference = localLargestY - localSmallestY;
  let domainStart = Math.min(
    0,
    Math.floor(
      (localSmallestY - (minimumValue != null ? 0.01 : 0.15) * yDifference) *
        100
    ) / 100
  );

  let domainEnd =
    Math.ceil(
      (localLargestY + (maximumValue != null ? 0.01 : 0.15) * yDifference) * 100
    ) / 100;
  if (domainStart < 0 && localSmallestY >= 0) {
    domainStart = 0;
  }
  if (Math.abs(domainEnd - domainStart) < 0.01) {
    domainEnd = domainStart + 0.01;
  } else if (!useDefaultDomain && domainEnd > 0.1) {
    // Visx doesn't display the top tick when the domain
    // has many decimal places, so round up to the nearest tenth
    // for larger scales
    domainEnd = Math.ceil(domainEnd * 10) / 10;
  }
  const numTicks = Math.max(
    Math.min(Math.ceil((domainEnd - domainStart) / 0.01), 6),
    2
  );
  const domain = [domainStart, domainEnd];

  return { domain, numTicks };
};
