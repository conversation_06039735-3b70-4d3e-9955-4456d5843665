import {
  LONG_LIVED_EXPIRATION_DAYS,
  SHORT_LIVED_EXPIRATION_DAYS,
} from '@/constants/runExpiredConstants';
import { TraceTTLTier } from '@/types/schema';

/**
 * Checks if an evaluator run has expired based on the experiment start time and trace tier
 * @param traceTier - The tier of the trace (e.g., 'shortlived', 'longlived')
 * @param experimentStartTime - The start time of the experiment as a string
 * @returns boolean indicating if the evaluator run has expired
 */
export function isEvaluatorRunExpired(
  traceTier: string | null | undefined,
  experimentStartTime: string | null | undefined
): boolean {
  if (!traceTier || !experimentStartTime) {
    return false;
  }

  // Check if the session is short-lived
  const isShortLived = traceTier === TraceTTLTier.shortlived;

  // Check if the experiment is older than the expiration limit
  const EXPIRATION_DAYS = isShortLived
    ? SHORT_LIVED_EXPIRATION_DAYS
    : LONG_LIVED_EXPIRATION_DAYS;
  const experimentDate = new Date(experimentStartTime);
  const currentDate = new Date();
  const diffTime = currentDate.getTime() - experimentDate.getTime();
  const diffDays = diffTime / (1000 * 60 * 60 * 24);

  return diffDays > EXPIRATION_DAYS;
}
