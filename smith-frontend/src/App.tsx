import '@fontsource/public-sans';
import { Provider as TooltipProvider } from '@radix-ui/react-tooltip';

import { Suspense, lazy, useEffect } from 'react';
import { Outlet, useLocation, useMatches, useNavigate } from 'react-router-dom';
import { SWRConfig } from 'swr';

import { BaseApp } from './BaseApp';
import { StorageLastTenant } from './Pages/NoMatch/RedirectNoMatch';
import { SegmentAnalyticsProvider } from './SegmentAnalyticsProvider';
import { Auth, AuthLayout } from './components/Auth';
import { PlatformInfoBanners } from './components/Banners/PlatformInfoBanners';
import Layout from './components/Layout';
import NavHeader from './components/NavSidebar/NavSidebar';
import {
  GlobalAuthProvider,
  SupabaseAuthStateChangeProvider,
} from './hooks/useAuth.utils';
import { useOrgRequiresPayment } from './hooks/useCurrentTier';
import { useDelightedSurvey } from './hooks/useDelightedSurvey';
import { LanguagePreferenceProvider } from './hooks/useLanguagePreferenceContext';
import { useOrgDisabled } from './hooks/useOrgConfig';
import { OrgIdContextProvider } from './hooks/useStoredOrganizationId/OrgIdContext';
import { StoredResourceTagsProvider } from './hooks/useStoredResourceTags/ResourceTagContext';
import { useOrganizationId } from './hooks/useSwr';
import { cleanupRefreshParam } from './utils/cleanup-refresh-param';
import { appOrganizationPath } from './utils/constants';
import { DatadogInitializer } from './utils/datadog/DatadogInitializer';
import { isFullPageMatch, isPublicMatch } from './utils/is-public-match';
import { globalGC } from './utils/swr-gc-middleware';
import { useLocalStorageState } from './utils/use-local-storage-state';

const OrganizationSettingsSideNav = lazy(
  () => import('./Pages/Settings/OrganizationSettingsSideNav')
);

function AppContent() {
  const [expanded, setExpanded] = useLocalStorageState(
    'ls:navbarExpanded',
    false
  );
  const { requiresPayment } = useOrgRequiresPayment();
  const { isDisabled } = useOrgDisabled();
  const navigate = useNavigate();
  const tenantId = useOrganizationId();
  const location = useLocation();
  const currentURL = location.pathname;
  const onSettings = currentURL.includes('/settings');

  useEffect(() => {
    cleanupRefreshParam();
  }, []);

  useDelightedSurvey();

  useEffect(() => {
    if (
      requiresPayment &&
      !currentURL.includes('/settings') &&
      !currentURL.includes('/onboarding') &&
      tenantId
    ) {
      navigate(`/${appOrganizationPath}/${tenantId}/settings/payments`);
    }
  }, [currentURL, requiresPayment, navigate, tenantId]);

  // Scroll to top on route change
  const { pathname } = useLocation();
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  const matches = useMatches();
  const isPublic = matches
    .slice(1)
    .every((match) => isPublicMatch(match.handle, location));

  const isSSO = location.pathname.includes('/sso/');
  // for /sso/login/:orgSSOSlug and /sso/email-verification/confirm
  if (isSSO) {
    return (
      <AuthLayout>
        <Outlet />
      </AuthLayout>
    );
  }

  let content;
  if (isDisabled && !currentURL.includes('hub')) {
    content = (
      <Layout.Root>
        <NavHeader expanded={expanded} setExpanded={setExpanded} />
        <Layout.Main expanded={expanded}>
          <div className="p-3">
            Organization is disabled, please contact your administrator or
            <EMAIL>
          </div>
        </Layout.Main>
      </Layout.Root>
    );
  } else if (matches.slice(1).some((match) => isFullPageMatch(match.handle))) {
    content = <Outlet />;
  } else {
    content = (
      <Layout.Root key={`${onSettings}`}>
        {!onSettings && (
          <NavHeader expanded={expanded} setExpanded={setExpanded} />
        )}
        <Layout.Main expanded={expanded || onSettings}>
          <PlatformInfoBanners />
          {onSettings && (
            <Suspense>
              <OrganizationSettingsSideNav />
            </Suspense>
          )}
          <Outlet />
        </Layout.Main>
      </Layout.Root>
    );
  }

  if (isPublic) {
    return <>{content}</>;
  }

  return <Auth>{content}</Auth>;
}

export const App = () => {
  return (
    /* Import `cacheTracker` to log internal SWR cache size */
    <SWRConfig value={{ use: [globalGC] }}>
      <BaseApp>
        <TooltipProvider>
          <OrgIdContextProvider>
            <SupabaseAuthStateChangeProvider>
              <GlobalAuthProvider>
                <SegmentAnalyticsProvider>
                  <StoredResourceTagsProvider>
                    <LanguagePreferenceProvider>
                      <DatadogInitializer />
                      <StorageLastTenant />
                      <AppContent />
                    </LanguagePreferenceProvider>
                  </StoredResourceTagsProvider>
                </SegmentAnalyticsProvider>
              </GlobalAuthProvider>
            </SupabaseAuthStateChangeProvider>
          </OrgIdContextProvider>
        </TooltipProvider>
      </BaseApp>
    </SWRConfig>
  );
};
