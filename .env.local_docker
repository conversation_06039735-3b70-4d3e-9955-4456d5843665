AUTH_TYPE=none
POSTGRES_DATABASE_URI=**********************************************/postgres
FF_WAITLIST_ENABLED=false
REDIS_DATABASE_URI=redis://langchain-redis:6379
LANGSMITH_LICENSE_KEY=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************.JcdSNz6WfKKolHa9HemlWcJd8z1WLZ3ME9lLn2h2gqShfz63POowUJZf_MJZXtSIjQ0E48qsWuG_k_SM6S8tj2Kb-seAgazsCwoqwHdVlSs4FJ6fIt7ZTetuZtClNMPVB2MtDzr_zxFLwZ1WbYUIRlHB0ruWTH6m-YB9AMi7SUpEn2g_xJ5yrQIcux2LM-RGDIIf3sP5n2o_ReG-_a1AHLShFFtmZW-Wqea-AiqdUPWC8tBjDsWGuWm7l9GNo2NdB_zFUzfKgbkjsfXo7YX2L0B-QgN0xcWfFX1VziMq5ziMAJl5dVR8rlxEAxlCM5UqGaRBCI2n0BTgfoacPyaR1EkqZDYZL79QLVEX7v144jNfFI1vMqwelxcW-ohtZ0IqlKoL5sjaT6EVrzQU0_SiL-h54RuybwR5VUrrHtzmx5j1DaA4bXFF9tzvSUMBv2P1KhsLVqeA8SD51NgUrBsAX3YVveSgMQqoSsSP5CVyvyheQncgWGr9oPWNd0HT0CReiH4ktJI_0P8klxYkfbhynNgVptfNm27E2ksmTbR6j3pBvELPMLxAbCPsZ1pVFZXoIt63Y3izOErZtB5JHb154HPT0UxU8BUqc7PuDX_dOpx25SVjvCkWbrbMgudBjArZfYic6d0JnoEbeQQig3STBd7UEazquq4Pxn2e7ofxSKY
CLICKHOUSE_HOST=langchain-clickhouse
CLICKHOUSE_PORT=8123
CLICKHOUSE_NATIVE_PORT=9000
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=password
CLICKHOUSE_DB=default
SMITH_BACKEND_ENDPOINT='http://localhost:1984'
HOST_BACKEND_ENDPOINT='http://localhost:8300'
MAX_ASYNC_JOBS_PER_WORKER=10
SHARED_TENANT_DEFAULT_CONFIG='{"max_identities": 10000, "can_use_rbac": true, "can_add_seats": true, "can_use_bulk_export": true, "can_disable_public_sharing": true}'
SHARED_ORG_DEFAULT_CONFIG='{"max_workspaces": -1, "max_identities": 10000, "can_use_rbac": true, "can_add_seats": true, "can_use_bulk_export": true, "can_disable_public_sharing": true, "enable_org_usage_charts": true}'
INGESTION_QUEUE=default
ADHOC_QUEUE=default
EXPORT_QUEUE=default
RUN_RULES_QUEUE=default
UPGRADES_QUEUE=default
HOST_QUEUE=default
DEFAULT_FEATURE_FLAGS='{"run_rules_enabled": true, "conversation_view_enabled": true, "pat_enabled": true, "rbac_enabled": true}'
DEFAULT_ORG_FEATURE_FLAGS='{"run_rules_enabled": true, "conversation_view_enabled": true, "pat_enabled": true, "rbac_enabled": true}'
GO_ENDPOINT=http://langchain-platform-backend:1986
GO_ACE_ENDPOINT=http://langchain-ace-backend:1987
PLAYGROUND_ENDPOINT=http://langchain-playground:3001
DOCS_PREFIX="/api"
BEACON_ENDPOINT=https://beacon.langchain.com
HOST_WORKER_HEARTBEAT_CRON_ENABLED=false
HOST_WORKER_RECONCILIATION_CRON_ENABLED=false
HOST_WORKER_DELETE_UNUSED_PROJECTS_CRON_ENABLED=false
INFO_CACHE_MAX_AGE_SEC=0
ENABLE_ORG_CHARTS_CRON=true
# Non-JSON list types
OAUTH_SCOPES='email,profile,openid'
DATADOG_IGNORE_ROUTES='/ok,/health'
HTTP_LOG_QUIET_ROUTES='/ok,/health'