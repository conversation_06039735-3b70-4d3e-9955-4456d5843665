# Continuous Benchmarking Implementation

This document summarizes the continuous benchmarking setup implemented for the smith-go project, following the guide from [dev.to](https://dev.to/vearutop/continuous-benchmarking-with-go-and-github-actions-41ok).

## 🎯 What Was Implemented

### 1. GitHub Actions Workflows

#### Updated CI Test Workflow (`.github/workflows/ci_test_go.yaml`)
- Added benchmark execution to existing test workflow
- Stores benchmark results for main branch commits
- Integrates with `benchmark-action/github-action-benchmark`
- Publishes results to GitHub Pages

#### New Continuous Benchmark Workflow (`.github/workflows/continuous-benchmark.yaml`)
- Dedicated workflow for comprehensive benchmarking
- Runs on pushes to main and pull requests
- Compares PR performance against main branch
- Posts comparison results as PR comments
- Includes full test environment setup (Postgres, Redis, ClickHouse, MinIO, Azurite)

### 2. Enhanced Makefile Targets

```bash
# New benchmark targets added to smith-go/Makefile
make bench-storage              # Run storage benchmarks with multiple iterations
make bench-storage-compare      # Compare against main branch
make bench-analysis            # Run advanced analysis script
make bench-clean              # Clean up result files
make bench-help               # Show help for all benchmark targets
```

### 3. Advanced Benchmark Analysis Script

**Location**: `smith-go/scripts/benchmark-analysis.sh`

Features:
- Compare current branch against any target branch
- Configurable benchmark parameters (benchtime, count, packages)
- Statistical analysis using `benchcmp` and `benchstat`
- Automatic tool installation
- Colored output and progress indicators
- Git branch management with automatic restoration

Usage examples:
```bash
# Full comparison against main
./scripts/benchmark-analysis.sh

# Custom parameters
./scripts/benchmark-analysis.sh -t 100x -c 3 -p "./ingestion/..."

# Compare against different branch
./scripts/benchmark-analysis.sh -b develop

# Current branch only
./scripts/benchmark-analysis.sh --current-only
```

### 4. Configuration Files

#### `.benchmarkrc`
- Default benchmark parameters
- Performance thresholds
- Environment configurations
- Package definitions

#### `benchmark-config.json`
- Structured configuration for CI
- Benchmark suite definitions
- Reporting settings
- Retention policies

### 5. Documentation and Dashboard

#### `smith-go/BENCHMARKING.md`
- Comprehensive guide for using the benchmark system
- Best practices for writing benchmarks
- Troubleshooting guide
- Tool installation instructions

#### `smith-go/benchmark-dashboard.html`
- HTML dashboard for visualizing benchmark results
- Real-time performance metrics
- Historical trend charts
- Integration with GitHub Pages

## 🚀 Key Features

### Automatic Performance Monitoring
- **Regression Detection**: Alerts when performance degrades beyond thresholds
- **Trend Tracking**: Long-term performance monitoring via GitHub Pages
- **PR Integration**: Automatic benchmark comparison on pull requests

### Flexible Configuration
- **Configurable Thresholds**: Customize alert levels for different metrics
- **Multiple Environments**: Support for CI, local, and nightly benchmark runs
- **Package-Specific Settings**: Different configurations for storage, ingestion, compression

### Developer-Friendly Tools
- **Local Comparison**: Easy branch-to-branch performance comparison
- **Statistical Analysis**: Built-in statistical significance testing
- **Visual Dashboard**: Web-based performance monitoring

## 📊 Benchmark Coverage

### Storage Benchmarks (`./storage/...`)
- Continuous uploader performance
- Upload size scaling
- Concurrency performance
- Multipart vs single upload strategies

### Compression Benchmarks (`./compression/...`)
- Compression algorithm performance
- Size-based compression decisions
- Memory usage optimization

### Ingestion Benchmarks (`./ingestion/...`)
- Queue processing performance
- Batch processing efficiency
- Redis transaction optimization

## 🔧 Setup Requirements

### GitHub Repository Settings
1. **Enable GitHub Pages**: Set source to `gh-pages` branch
2. **Permissions**: Ensure workflow has write access to repository
3. **Secrets**: Configure `LANGSMITH_LICENSE_KEY` if needed

### Local Development
```bash
# Install required tools
go install golang.org/x/tools/cmd/benchcmp@latest
go install golang.org/x/perf/cmd/benchstat@latest

# Make analysis script executable
chmod +x smith-go/scripts/benchmark-analysis.sh
```

## 📈 Performance Monitoring

### Automatic Alerts
- **Time Regression**: >150% increase in execution time
- **Memory Regression**: >150% increase in memory usage
- **Allocation Regression**: >200% increase in allocations

### Reporting Channels
- **GitHub PR Comments**: Automatic benchmark comparison
- **GitHub Pages**: Historical performance dashboard
- **Commit Status**: Pass/fail based on regression thresholds

## 🎛️ Customization

### Adjusting Thresholds
Edit `.benchmarkrc` or `benchmark-config.json`:
```bash
# Performance thresholds (percentage increase)
ALERT_THRESHOLD_TIME=150
ALERT_THRESHOLD_MEMORY=150
ALERT_THRESHOLD_ALLOCS=200
```

### Adding New Benchmarks
1. Write benchmark following Go conventions
2. Add to appropriate package (`./storage/...`, `./ingestion/...`, etc.)
3. Update `benchmark-config.json` if needed
4. Test locally with `make bench-storage-compare`

### Custom Analysis
Use the analysis script with custom parameters:
```bash
# Benchmark specific package with custom settings
./scripts/benchmark-analysis.sh \
  -p "./your-package/..." \
  -t 1000x \
  -c 10 \
  -b your-base-branch
```

## 🔍 Monitoring and Maintenance

### GitHub Pages Dashboard
Access at: `https://your-org.github.io/your-repo/dev/bench/`

### Regular Maintenance
- **Review Alerts**: Investigate performance regressions
- **Update Thresholds**: Adjust based on acceptable performance changes
- **Clean Old Data**: Benchmark results are retained for 90 days by default

### Troubleshooting
- **Noisy Benchmarks**: Increase `-count` parameter
- **CI Timeouts**: Reduce `-benchtime` for faster CI runs
- **Missing Results**: Check GitHub Actions logs and permissions

## 📚 References

- [Original Guide](https://dev.to/vearutop/continuous-benchmarking-with-go-and-github-actions-41ok)
- [Go Benchmarking Documentation](https://pkg.go.dev/testing#hdr-Benchmarks)
- [GitHub Action Benchmark](https://github.com/benchmark-action/github-action-benchmark)
- [benchstat Tool](https://pkg.go.dev/golang.org/x/perf/cmd/benchstat)

## 🎉 Next Steps

1. **Test the Setup**: Create a PR to see benchmark comparison in action
2. **Customize Thresholds**: Adjust alert levels based on your performance requirements
3. **Add More Benchmarks**: Expand coverage to other critical code paths
4. **Monitor Trends**: Use the GitHub Pages dashboard to track long-term performance
5. **Integrate with Development Workflow**: Make benchmark review part of your PR process
