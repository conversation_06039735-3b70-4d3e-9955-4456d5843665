apiVersion: v1
kind: Pod
metadata:
  name: clickhouse-to-postgres-migration-1
  namespace: default
spec:
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - preference:
          matchExpressions:
          - key: heavy-compute-spot
            operator: In
            values:
            - "true"
        weight: 100
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: heavy-compute
            operator: In
            values:
            - "true"
          - key: private
            operator: In
            values:
            - "true"
  serviceAccountName: langsmith-backend
  nodeSelector:
    private: "true"
  containers:
  - name: clickhouse-to-postgres-migration-1
    image: gcr.io/langchain-prod/langsmith-backend:0.10.55-e523979-linux-amd64
    imagePullPolicy: IfNotPresent
    command:
    - sleep
    - "14400"
    env:
      - name: DD_INTERNAL_POD_UID
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.uid
      - name: DD_EXTERNAL_ENV
        value: it-false,cn-backend,pu-$(DD_INTERNAL_POD_UID)
      - name: DD_ENTITY_ID
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.uid
      - name: DD_DOGSTATSD_URL
        value: unix:///var/run/datadog/dsd.socket
      - name: DD_TRACE_AGENT_URL
        value: unix:///var/run/datadog/apm.socket
      - name: AUTH_TYPE
        value: supabase
      - name: LANGCHAIN_ENV
        value: production.web
      - name: LANGCHAIN_ENDPOINT
        value: https://eu.api.smith.langchain.com
      - name: LANGSMITH_URL
        value: https://eu.smith.langchain.com
      - name: PRIVATE_GITHUB_BUILD_TRIGGER_ID
        value: 6a8e7c68-a0a2-4f72-b7c9-e42eb3da8ad5
      - name: S3_RUN_MANIFEST_BUCKET_NAME
        value: eu-prod-langsmith-static-assets
      - name: S3_BUCKET_NAME
        value: eu-prod-langsmith-static-assets
      - name: KNATIVE_ENDPOINT_URL
        value: https://europe-west4-run.googleapis.com:443
      - name: PRIVATE_GITHUB_BUILD_TRIGGER_LOCATION
        value: europe-west4
      - name: GITHUB_APP_PUBLIC_LINK
        value: https://github.com/apps/hosted-langserve-eu
      - name: GITHUB_CLIENT_ID
        value: Iv23liRzxhXxcoUrGVFw
      - name: GITHUB_APP_ID
        value: "941905"
      - name: GITHUB_APP_PEM_SECRET_NAME
        value: hosted-langserve-github-app-pem-eu
      - name: GITHUB_CALLBACK_URL
        value: https://eu.api.host.langchain.com/integrations/github/callback
      - name: QUERY_GEN_TRACING_TENANT_ID
        value: 32c8acb8-9088-4e49-9172-92b6c4b9add0
      - name: QUERY_GEN_TRACING_SESSION_NAME
        value: LCP-Query-Runs
      - name: POSTMARK_SERVER_TOKEN
        valueFrom:
          secretKeyRef:
            key: postmark_server_token
            name: langsmith-external
      - name: SUPABASE_JWT_SECRET
        valueFrom:
          secretKeyRef:
            key: supabase_jwt_secret
            name: langsmith-external
      - name: S3_ACCESS_KEY
        valueFrom:
          secretKeyRef:
            key: gcs_access_key
            name: langsmith-external
      - name: S3_ACCESS_KEY_SECRET
        valueFrom:
          secretKeyRef:
            key: gcs_access_key_secret
            name: langsmith-external
      - name: DD_TRACE_LANGCHAIN_ENABLED
        value: "false"
      - name: DD_TRACE_OPENAI_ENABLED
        value: "false"
      - name: DD_LLMOBS_ENABLED
        value: "false"
      - name: DD_ENV
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.labels['tags.datadoghq.com/env']
      - name: DD_SERVICE
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.labels['tags.datadoghq.com/service']
      - name: DD_VERSION
        valueFrom:
          fieldRef:
            apiVersion: v1
            fieldPath: metadata.labels['tags.datadoghq.com/version']
      - name: DD_TRACE_128_BIT_TRACEID_GENERATION_ENABLED
        value: "False"
      - name: DD_TRACE_HEADER_TAGS
        value: origin,x-tls-version,x-tls-cipher-suite
      - name: REDIS_RUNS_EXPIRY_SECONDS
        value: "10800"
      - name: STRIPE_SECRET_KEY
        valueFrom:
          secretKeyRef:
            key: stripe_secret_key
            name: langsmith-external
      - name: METRONOME_API_KEY
        valueFrom:
          secretKeyRef:
            key: metronome_api_key
            name: langsmith-external
      - name: X_SERVICE_AUTH_JWT_SECRET
        valueFrom:
          secretKeyRef:
            key: x_service_auth_jwt_secret
            name: langsmith-external
      - name: ELASTIC_CLOUD_ID
        value: LangSmith_Prod_EU:ZXVyb3BlLXdlc3Q0LmdjcC5lbGFzdGljLWNsb3VkLmNvbTo0NDMkYTUyNzMyMmNjYjVlNDhiMTlmNTc3ZTEwNzEzZmE5ZjkkYzQyOTM2MDkzMTJkNGM2Y2E0M2E3NTJmNTg4MjA3ZDA=
      - name: ELASTIC_API_KEY
        valueFrom:
          secretKeyRef:
            key: elastic_api_key
            name: langsmith-external
      - name: REDIS_CACHE_DATABASE_URI
        value: redis://************:6379
      - name: REDIS_SHARDING_ENABLED
        value: "true"
      - name: REDIS_DUAL_WRITE_INGESTION
        value: "false"
      - name: REDIS_DUAL_WRITE_UPGRADES
        value: "false"
      - name: REDIS_DUAL_WRITE_USAGE_LIMITS
        value: "false"
      - name: REDIS_SHARDED_QUEUES_ENABLED
        value: "true"
      - name: REDIS_SHARDED_READS_ENABLED
        value: "true"
      - name: REDIS_SHARDED_WRITES_ENABLED
        value: "true"
      - name: REDIS_SHARD_URIS
        value: '{"node-0": "redis://**************:6379", "node-1": "redis://*************:6379",
          "node-2": "redis://*************:6379"}'
      - name: CLICKHOUSE_INTERNAL_ANALYTICS_HOST
        valueFrom:
          secretKeyRef:
            key: clickhouse_internal_analytics_host
            name: langsmith-external
      - name: CLICKHOUSE_INTERNAL_ANALYTICS_PORT
        valueFrom:
          secretKeyRef:
            key: clickhouse_internal_analytics_port
            name: langsmith-external
      - name: CLICKHOUSE_API_KEY_ID
        valueFrom:
          secretKeyRef:
            key: clickhouse_autoscale_api_key_id
            name: langsmith-external
      - name: CLICKHOUSE_API_KEY_SECRET
        valueFrom:
          secretKeyRef:
            key: clickhouse_autoscale_api_key_secret
            name: langsmith-external
      - name: AUTOSCALE_CLICKHOUSE_CRON
        value: '* * * * *'
      - name: HOST_LANGCHAIN_API_ENDPOINT
        value: https://eu.api.smith.langchain.com
      - name: CLICKHOUSE_CLUSTERS
        value: |-
          {
            "eu-prod-langsmith-clickhouse-smt": {
              "client": "DEFAULT",
              "enabled": true,
              "org_id": "2230deff-96b1-4d32-8274-d35fd3e1c438",
              "service_id": "15dcca72-52b1-4f0e-b086-95be733b306f",
              "replica_limits": {
                "min": 3,
                "max": 5
              },
              "scaling_config": {
                "cpu_up": 75,
                "mem_up": 75,
                "cpu_down": 45,
                "mem_down": 45,
                "dry_run": false,
                "stabilization_minutes": 5,
                "force": false
              }
            }
          }
      - name: FF_CLICKHOUSE_USE_MULTISERVICE
        value: "true"
      - name: MIN_BLOB_STORAGE_SIZE_KB
        value: "20"
      - name: FF_CH_INTERNAL_ANALYTICS
        value: "true"
      - name: FF_METRONOME_BATCH_SEAT_REPORTING_ENABLED
        value: "true"
      - name: FF_USE_SUBQUERY_FILTERING
        value: "true"
      - name: FF_USE_MIN_MAX_TIME_FILTERED_QUERY
        value: "false"
      - name: BULK_EXPORT_WORKFLOW_TIMEOUT_SEC
        value: "172800"
      - name: MAX_CONCURRENT_BULK_EXPORTS_PER_WS
        value: "3"
      - name: BULK_EXPORT_MAX_CONCURRENT_RUNS
        value: "5"
      - name: REDIS_CLUSTER_DISCOVER_IP
        value: *********
      - name: REDIS_CLUSTER_DISCOVER_PORT
        value: "6379"
      - name: REDIS_CLUSTER_IAM_AUTH_ENABLED
        value: "true"
      - name: REDIS_CLUSTER_ENABLED
        value: "true"
      - name: REDIS_CLUSTER_ALERTS_ENABLED
        value: "true"
      - name: REDIS_CLUSTER_INGESTION_GLOBAL_ENABLED
        value: "true"
      - name: RUN_RULES_MAX_BACKFILL_MINUTES_TO_PROCESS
        value: "1440"
      - name: CH_UPGRADE_BATCH_LIMIT
        value: "750"
      - name: FF_ENABLE_BLOB_STORAGE_BATCHING_GLOBALLY
        value: "true"
      - name: FF_RUN_STATS_GROUP_BY_ENABLED_ALL
        value: "true"
      - name: S3_SINGLE_REGION_BUCKET_NAME
      - name: POSTGRES_DATABASE_URI
        valueFrom:
          secretKeyRef:
            key: connection_url
            name: langsmith-external
      - name: POSTGRES_SCHEMA
        value: public
      - name: REDIS_DATABASE_URI
        valueFrom:
          secretKeyRef:
            key: connection_url
            name: langsmith-redis
      - name: CLICKHOUSE_HYBRID
        value: "false"
      - name: CLICKHOUSE_DB
        valueFrom:
          secretKeyRef:
            key: clickhouse_db
            name: langsmith-external
      - name: CLICKHOUSE_HOST
        valueFrom:
          secretKeyRef:
            key: clickhouse_host
            name: langsmith-external
      - name: CLICKHOUSE_PORT
        valueFrom:
          secretKeyRef:
            key: clickhouse_port
            name: langsmith-external
      - name: CLICKHOUSE_NATIVE_PORT
        valueFrom:
          secretKeyRef:
            key: clickhouse_native_port
            name: langsmith-external
      - name: CLICKHOUSE_USER
        valueFrom:
          secretKeyRef:
            key: clickhouse_user
            name: langsmith-external
      - name: CLICKHOUSE_PASSWORD
        valueFrom:
          secretKeyRef:
            key: clickhouse_password
            name: langsmith-external
      - name: CLICKHOUSE_TLS
        valueFrom:
          secretKeyRef:
            key: clickhouse_tls
            name: langsmith-external
      - name: CLICKHOUSE_CLUSTER
      - name: LOG_LEVEL
        value: info
      - name: LANGSMITH_LICENSE_KEY
        valueFrom:
          secretKeyRef:
            key: langsmith_license_key
            name: langsmith-external
      - name: API_KEY_SALT
        valueFrom:
          secretKeyRef:
            key: api_key_salt
            name: langsmith-external
      - name: BASIC_AUTH_ENABLED
        value: "false"
      - name: FF_ORG_CREATION_DISABLED
        value: "false"
      - name: FF_PERSONAL_ORGS_DISABLED
        value: "false"
      - name: FF_TRACE_TIERS_ENABLED
        value: "true"
      - name: FF_UPGRADE_TRACE_TIER_ENABLED
        value: "true"
      - name: TRACE_TIER_TTL_DURATION_SEC_MAP
        value: '{ "longlived": 34560000, "shortlived": 1209600 }'
      - name: FF_CH_SEARCH_ENABLED
        value: "true"
      - name: PORT
        value: "1984"
      - name: SUPABASE_PROJECT_REF
        value: ehhmgrdzqonzffdwpvcw
      - name: SUPABASE_ACCESS_TOKEN
        valueFrom:
          secretKeyRef:
            key: supabase_access_token
            name: langsmith-external
      - name: FF_PYTHON_PLAYGROUND_PCT
        value: "100"
      - name: NEW_AI_QUERY_PCT
        value: "100"
      - name: ASYNCPG_POOL_MAX_SIZE
        value: "30"
      - name: REDIS_CACHE_MAX_CONNECTIONS
        value: "250"
      - name: STREAMING_NO_CONTENT_ENABLED
        value: "true"
    envFrom:
    - configMapRef:
        name: langsmith-config
    - secretRef:
        name: langsmith-external
    resources:
      requests:
        cpu: "2000m"
        memory: "40000Mi"
      limits:
        cpu: "3000m"
        memory: "40000Mi"
    securityContext: {}
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File

