import logging
import os
import re
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Optional

import google.auth.credentials
import google.auth.transport.requests
import googleapiclient.discovery
import httpx
import kubernetes_asyncio.client
import kubernetes_asyncio.config
from ddtrace import tracer  # noqa: E402
from ddtrace.constants import SPAN_KIND  # noqa: E402
from ddtrace.ext import SpanKind  # noqa: E402
from httpcore import AsyncIOBackend
from lc_database.service_client import retry_httpx

from host.config import settings
from host.models.deployment_type import ProjectPlatform
from host.platforms.base.args import PlatformArgs

logger = logging.getLogger(__name__)

UUID_REGEX = re.compile(r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")

_metrics_client: httpx.AsyncClient
_creds: google.auth.credentials.Credentials | None = None
_gcp_sqladmin_client: googleapiclient.discovery.Resource | None = None


class K8sOperatorPlatformArgs(PlatformArgs):
    k8s_cluster: Optional[str]
    k8s_namespace: Optional[str]
    region: Optional[str]
    labels: dict[str, str]

    # project_id and revision_id are used for retrieving the K8s Deployment.
    # These values are used because the K8s Deployment name is not static
    # across Knative revisions. Set both values if the underlying K8s
    # Deployment of the Knative service needs to be retrieved.
    project_id: Optional[str]
    revision_id: Optional[str]

    @classmethod
    def from_platform(
        cls,
        platform: ProjectPlatform,
        labels: dict[str, str] = {},
        project_id: Optional[str] = None,
        revision_id: Optional[str] = None,
    ):
        k8s_namespace = platform.k8s_namespace or settings.HOSTED_K8S_NAMESPACE
        if settings.IS_SELF_HOSTED:
            # For self-hosted host-backend and remote reconciled projects
            # (langgraph-dataplane), override k8s_namespace with the current
            # namespace where the application is deployed.
            namespace_file = "/var/run/secrets/kubernetes.io/serviceaccount/namespace"
            if os.path.exists(namespace_file):
                with open(namespace_file, "r") as f:
                    k8s_namespace = f.read().strip()

        return cls(
            k8s_cluster=platform.k8s_cluster,
            k8s_namespace=k8s_namespace,
            region=platform.region,
            labels=labels,
            project_id=project_id,
            revision_id=revision_id,
        )


def _load_gcp_credentials() -> google.auth.credentials.Credentials:
    global _creds

    if not _creds or not _creds.valid:
        _creds, _ = google.auth.default()
        # Refresh credentials
        auth_req = google.auth.transport.requests.Request()
        _creds.refresh(auth_req)

    return _creds


def load_k8s_client(
    platform_args: K8sOperatorPlatformArgs,
) -> kubernetes_asyncio.client.ApiClient:
    """ApiClient implements async context manager protocol."""
    if settings.IS_SELF_HOSTED:
        kubernetes_asyncio.config.load_incluster_config()
        return kubernetes_asyncio.client.ApiClient()
    else:
        k8s_cluster = platform_args.k8s_cluster
        if k8s_cluster is None:
            k8s_cluster = settings.HOSTED_K8S_CLUSTER

        host = settings.HOSTED_K8S_ENDPOINT_URLS[k8s_cluster]
        ca_cert_path = settings.HOSTED_K8S_CA_CERT_PATHS[k8s_cluster]
        configuration = kubernetes_asyncio.client.Configuration(host=host)
        if ca_cert_path:
            configuration.ssl_ca_cert = ca_cert_path
        else:
            configuration.verify_ssl = False

        creds = _load_gcp_credentials()
        return kubernetes_asyncio.client.ApiClient(
            configuration,
            header_name="authorization",
            header_value=f"Bearer {creds.token}",
        )


def load_cloudsql_client() -> googleapiclient.discovery.Resource:
    global _gcp_sqladmin_client
    creds = _load_gcp_credentials()

    if not _gcp_sqladmin_client:
        _gcp_sqladmin_client = googleapiclient.discovery.build(
            "sqladmin", "v1beta4", credentials=creds
        )

    return _gcp_sqladmin_client


def create_metrics_client() -> httpx.AsyncClient:
    creds = _load_gcp_credentials()

    headers = {
        "Authorization": f"Bearer {creds.token}",
        "Content-Type": "application/json",
    }

    transport = httpx.AsyncHTTPTransport()
    transport._pool._network_backend = AsyncIOBackend()
    return httpx.AsyncClient(
        base_url="https://monitoring.googleapis.com/v1/",
        headers=headers,
        transport=transport,
    )


async def close_metrics_client() -> None:
    global _metrics_client
    try:
        await _metrics_client.aclose()
    except NameError:
        pass


async def initialize_metrics_client() -> None:
    await close_metrics_client()
    global _metrics_client
    _metrics_client = create_metrics_client()


@asynccontextmanager
async def get_metrics_client() -> AsyncGenerator[httpx.AsyncClient, None]:
    try:
        if not _metrics_client.is_closed:
            found = True
        else:
            found = False
    except NameError:
        found = False
    if found:
        yield JsonHttpClient(_metrics_client)
    else:
        await initialize_metrics_client()
        yield JsonHttpClient(_metrics_client)


class JsonHttpClient:
    def __init__(self, client: httpx.AsyncClient) -> None:
        self.client = client

    async def _post(self, path: str, data: Any | None = None) -> dict:
        res = await self.client.post(path, json=data)
        if res.status_code >= 400:
            logger.error(
                f"monitoring.googleapis POST {path} failed with status code {res.status_code}\n"
                f"Response: {res.text}"
            )
        res.raise_for_status()
        return res.json()

    @retry_httpx
    async def post(self, path: str, data: Any | None = None) -> dict:
        if not settings.DATADOG_ENABLED:
            return await self._post(path, data)

        path_without_uuids = UUID_REGEX.sub("<uuid>", path)
        with tracer.trace(
            "monitoring.googleapis.request",
            service="monitoring.googleapis",
            resource="POST: " + path_without_uuids,
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            return await self._post(path, data)
