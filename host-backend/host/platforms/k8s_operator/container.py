import asyncio
import logging
import time
from typing import Any, Optional
from uuid import UUID

import httpx
import kubernetes_asyncio.client
import kubernetes_asyncio.client.exceptions

from host.config import settings
from host.models.build import BuildResult
from host.models.deploy import CreateServiceRequest, K8sEnvVar, KnativeEnvVar
from host.platforms.base.container import Container
from host.platforms.k8s_operator.client import K8sOperatorPlatformArgs, load_k8s_client
from host.platforms.k8s_operator.schema import (
    K8sOperatorResourceId,
    K8sOperatorRevision,
    K8sOperatorService,
)
from host.platforms.k8s_vanilla.secrets import K8sVanillaSecrets

logger = logging.getLogger(__name__)


class K8sOperatorContainer(Container):
    @staticmethod
    def _make_operator_template(
        request: CreateServiceRequest,
        service: Optional[dict] = None,
    ) -> dict:
        """
        Create an LGP custom resource based on the request parameters.
        If service is provided, it will update the existing service while preserving certain fields.
        """
        labels = {
            "app": request.service_name,
            "ls_revision_id": str(request.revision_id),
            "ls_timestamp": str(int(time.time())),
            **request.labels,
        }
        metadata = {
            "name": request.service_name,
            "labels": labels,
        }

        if service:
            if "resourceVersion" in service["metadata"]:
                metadata["resourceVersion"] = service["metadata"]["resourceVersion"]

        # If NUM_JOBS_PER_WORKER is set in env_vars, remove it as an env var and set in spec directly
        num_jobs_per_worker = "10"
        if request.env_vars:
            for env_var in request.env_vars:
                if env_var.name == "NUM_JOBS_PER_WORKER":
                    num_jobs_per_worker = env_var.value or "10"
                    request.env_vars.remove(env_var)
                    break

        # Create the LGP custom resource template
        template: dict[str, Any] = {
            "apiVersion": "apps.langchain.ai/v1alpha1",
            "kind": "LGP",
            "metadata": metadata,
            "spec": {
                "serverSpec": {
                    "queueEnabled": True,
                    "numJobsPerWorker": int(num_jobs_per_worker),
                    "image": request.build.image_path,
                    "resources": {
                        "limits": {
                            "cpu": str(request.container_cpu),
                            "memory": f"{request.container_memory_mb * 2}Mi",
                        },
                        "requests": {
                            "cpu": str(request.container_cpu / 2),
                            "memory": f"{request.container_memory_mb}Mi",
                        },
                    },
                    "env": [
                        env_var.model_dump(exclude=["type"])
                        for env_var in request.env_vars or []
                    ],
                },
                "autoscaling": {
                    "enabled": True,
                    "autoscalerType": "KEDA",  # Default to HPA, can be made configurable
                    "minReplicas": request.container_min_scale,
                    "maxReplicas": request.container_max_scale,
                },
                "ingress": {"enabled": settings.LANGGRAPH_CLOUD_INGRESS_ENABLED},
                "labels": {
                    **labels,
                },
            },
        }
        # If not self-hosted, add node affinity/tolerations to the template if a dev deployment.
        if not settings.IS_SELF_HOSTED:
            if request.labels.get("deployment_type") in ["dev", "dev_free"]:
                template["spec"]["affinity"] = {
                    "nodeAffinity": {
                        "preferredDuringSchedulingIgnoredDuringExecution": [
                            {
                                "weight": 100,
                                "preference": {
                                    "matchExpressions": [
                                        {
                                            "key": "cloud.google.com/gke-spot",
                                            "operator": "In",
                                            "values": ["true"],
                                        }
                                    ]
                                },
                            }
                        ]
                    }
                }
                template["spec"]["tolerations"] = [
                    {
                        "key": "interruptible",
                        "operator": "Equal",
                        "value": "true",
                        "effect": "NoSchedule",
                    }
                ]

        return template

    @staticmethod
    async def upsert_container(
        service_name: str,
        revision_id: str,
        *,
        image_name: str,
        container_cpu: float,
        container_memory_mb: int,
        container_min_scale: int,
        container_max_scale: int,
        env_vars: list[KnativeEnvVar],
        platform_args: K8sOperatorPlatformArgs,
    ) -> K8sOperatorResourceId:
        async with load_k8s_client(platform_args) as api_client:
            api_instance = kubernetes_asyncio.client.CustomObjectsApi(api_client)

            request = CreateServiceRequest(
                service_name=service_name,
                build=BuildResult(image_path=image_name),
                env_vars=env_vars,
                revision_id=UUID(revision_id),
                labels=platform_args.labels,
                container_min_scale=container_min_scale,
                container_max_scale=container_max_scale,
                container_cpu=container_cpu,
                container_memory_mb=container_memory_mb,
            )

            # Create or update operator
            try:
                service = await api_instance.get_namespaced_custom_object(
                    group="apps.langchain.ai",
                    version="v1alpha1",
                    plural="lgps",
                    namespace=platform_args.k8s_namespace,
                    name=service_name,
                )
                await api_instance.replace_namespaced_custom_object(
                    group="apps.langchain.ai",
                    version="v1alpha1",
                    plural="lgps",
                    namespace=platform_args.k8s_namespace,
                    name=service_name,
                    body=K8sOperatorContainer._make_operator_template(request, service),
                )
                logger.info(f"Updated K8s LGP workload {service_name}")

            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    try:
                        await api_instance.create_namespaced_custom_object(
                            group="apps.langchain.ai",
                            version="v1alpha1",
                            plural="lgps",
                            namespace=platform_args.k8s_namespace,
                            body=K8sOperatorContainer._make_operator_template(request),
                        )
                        logger.info(f"Created K8s LGP workload {service_name}")
                    except kubernetes_asyncio.client.exceptions.ApiException as api_e:
                        if api_e.status == 409:
                            logger.info(
                                f"K8s LGP workload {service_name} already exists",
                                exc_info=False,
                            )
                        else:
                            logger.exception(
                                f"Unexpected K8s API error for revision ID {revision_id}"
                            )
                            raise api_e
                else:
                    logger.exception(
                        f"Unexpected K8s API error for revision ID {revision_id}"
                    )
                    raise e

        # wait for new revision to be healthy
        await K8sOperatorContainer.wait_for_health_check(service_name, platform_args)

        # return latest revision ID
        service = await K8sOperatorContainer.get_service(service_name, platform_args)
        assert service.latest_revision is not None
        return service.latest_revision.id

    @staticmethod
    async def _wait_with_timeout(
        start_time_sec: float,
        wait_time_sec: float = 1.0,
        timeout_sec: int = 600,
        error_msg: str = "",
    ):
        current_time = time.time()
        if current_time - start_time_sec + wait_time_sec > timeout_sec:
            raise TimeoutError(error_msg)

        await asyncio.sleep(wait_time_sec)

    @staticmethod
    async def wait_for_health_check(
        service_name: str, platform_args: K8sOperatorPlatformArgs
    ) -> None:
        async with load_k8s_client(platform_args) as api_client:
            api_instance = kubernetes_asyncio.client.CustomObjectsApi(api_client)
            app_v1_api = kubernetes_asyncio.client.AppsV1Api(api_client)
            # Wait for deployment to be ready
            start_time_sec = time.time()
            while True:
                try:
                    lgp_workload = await api_instance.get_namespaced_custom_object(
                        group="apps.langchain.ai",
                        version="v1alpha1",
                        plural="lgps",
                        namespace=platform_args.k8s_namespace,
                        name=service_name,
                    )
                    # Check the lastCreatedReplicaSet has been updated to latest ls revision
                    last_created_replica_set = (
                        lgp_workload.get("status", {})
                        .get("lastCreatedReplicaSet", {})
                        .get("name")
                    )
                    if last_created_replica_set:
                        replica_set = await app_v1_api.read_namespaced_replica_set(
                            name=last_created_replica_set,
                            namespace=platform_args.k8s_namespace,
                        )
                        if replica_set.metadata.labels.get("ls_revision_id") == str(
                            lgp_workload.get("metadata", {})
                            .get("labels", {})
                            .get("ls_revision_id")
                        ):
                            # Check that this is the same as the lastSuccessfulReplicaSet
                            last_active_replica_set = (
                                lgp_workload.get("status", {})
                                .get("lastSuccessfulReplicaSet", {})
                                .get("name")
                            )
                            if last_created_replica_set == last_active_replica_set:
                                break

                    await K8sOperatorContainer._wait_with_timeout(
                        start_time_sec,
                        15,
                        settings.HOSTED_K8S_DEPLOYMENT_READY_TIMEOUT_SECONDS,
                        (
                            "Timeout: Deployment is not ready after "
                            f"{settings.HOSTED_K8S_DEPLOYMENT_READY_TIMEOUT_SECONDS} seconds."
                        ),
                    )

                except kubernetes_asyncio.client.exceptions.ApiException as e:
                    logger.warning(f"Error checking deployment status: {e}")
                    await K8sOperatorContainer._wait_with_timeout(
                        start_time_sec,
                        15,
                        300,
                        "Timeout: Failed to check deployment status after 300 seconds.",
                    )

            # check healthcheck endpoint
            start_time_sec = time.time()
            service = await K8sOperatorContainer.get_service(
                service_name, platform_args
            )
            async with httpx.AsyncClient(timeout=httpx.Timeout(20)) as aclient:
                status_code = 0
                while status_code != 200:
                    response = await aclient.get(f"{service.url}/ok?check_db=1")

                    status_code = response.status_code
                    if status_code == 200:
                        break

                    logger.info(
                        f"Waiting for service {service_name} health check to "
                        f"succeed. Status code: {status_code}"
                    )
                    await K8sOperatorContainer._wait_with_timeout(
                        start_time_sec,
                        15,
                        600,
                        "Timeout: New revision health check did not succeed after 600 seconds.",
                    )
                logger.info(
                    "Successfully called health check endpoint "
                    f"for K8s service {service_name}"
                )

    @staticmethod
    async def delete_container(
        service_name: str, platform_args: K8sOperatorPlatformArgs
    ) -> None:
        async with load_k8s_client(platform_args) as api_client:
            api_instance = kubernetes_asyncio.client.CustomObjectsApi(api_client)
            try:
                await api_instance.delete_namespaced_custom_object(
                    group="apps.langchain.ai",
                    version="v1alpha1",
                    plural="lgps",
                    namespace=platform_args.k8s_namespace,
                    name=service_name,
                )
                logger.info(f"Deleted K8s LGP workload {service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    logger.info(f"K8s LGP workload {service_name} is already deleted")
                else:
                    logger.warning(f"Failed to delete K8s LGP workload {service_name}")

    @staticmethod
    async def get_revision(
        service_name: str,
        resource_id: K8sOperatorResourceId,
        platform_args: K8sOperatorPlatformArgs,
    ) -> K8sOperatorRevision:
        async with load_k8s_client(platform_args) as api_client:
            api_instance = kubernetes_asyncio.client.AppsV1Api(api_client)
            try:
                lgp_revision = await api_instance.read_namespaced_replica_set(
                    name=resource_id.name,
                    namespace=platform_args.k8s_namespace,
                )
                container = lgp_revision.spec.template.spec.containers[0]
                env_vars = [
                    K8sEnvVar.model_validate(env_var.to_dict())
                    for env_var in container.env
                ]
                # decode secret values
                decoded_secrets = await K8sVanillaSecrets.get_secret_values(
                    service_name, platform_args
                )
                for env_var in env_vars:
                    if env_var.type == "secret" and decoded_secrets.get(env_var.name):
                        env_var.value = decoded_secrets[env_var.name]

                hosted_langserve_revision_id = lgp_revision.metadata.labels.get(
                    "ls_revision_id"
                )
                return K8sOperatorRevision(
                    id=resource_id,
                    env_vars=env_vars,
                    hosted_langserve_revision_id=UUID(hosted_langserve_revision_id)
                    if hosted_langserve_revision_id
                    else None,
                )
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                # Handle case where LGP resource does not exist
                if e.status == 404:
                    return K8sOperatorRevision(
                        id=resource_id,
                        env_vars=[],
                        hosted_langserve_revision_id=None,
                        statuses=[],
                    )
                else:
                    raise e

    @staticmethod
    async def get_service(
        service_name: str, platform_args: K8sOperatorPlatformArgs
    ) -> K8sOperatorService:
        async with load_k8s_client(platform_args) as api_client:
            custom_objects_api = kubernetes_asyncio.client.CustomObjectsApi(api_client)

            try:
                # Get the LGP CRD directly
                lgp = await custom_objects_api.get_namespaced_custom_object(
                    group="apps.langchain.ai",
                    version="v1alpha1",
                    plural="lgps",
                    namespace=platform_args.k8s_namespace,
                    name=service_name,
                )
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                logger.warning(f"Failed to get LGP {service_name}: {e}")
                if e.status == 404:
                    return K8sOperatorService(
                        id=K8sOperatorResourceId(type="services", name=service_name),
                        url=None,
                        latest_revision=None,
                        latest_active_revision=None,
                    )
                else:
                    raise e

            # Extract LGP status information
            status = lgp.get("status", {})
            # Get the latest revision information from LGP CRD
            latest_revision = None
            latest_active_revision = None
            if status.get("lastCreatedReplicaSet"):
                replica_set_name = status["lastCreatedReplicaSet"].get("name")
                if replica_set_name:
                    latest_revision = await K8sOperatorContainer.get_revision(
                        service_name,
                        K8sOperatorResourceId(type="revisions", name=replica_set_name),
                        platform_args,
                    )
            if status.get("lastSuccessfulReplicaSet"):
                replica_set_name = status["lastSuccessfulReplicaSet"].get("name")
                if replica_set_name:
                    latest_active_revision = await K8sOperatorContainer.get_revision(
                        service_name,
                        K8sOperatorResourceId(type="revisions", name=replica_set_name),
                        platform_args,
                    )

            # Get the URL
            url = status.get("endpoint")
            return K8sOperatorService(
                id=K8sOperatorResourceId(type="services", name=service_name),
                url=url,
                latest_revision=latest_revision,
                latest_active_revision=latest_active_revision,
            )
