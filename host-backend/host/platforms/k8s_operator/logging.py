from datetime import datetime, timezone
from typing import Literal, Optional, Tuple

from host.config import settings
from host.models.cloudlogging import list_logs
from host.platforms.base.logging import LOG_LEVEL, STAT_LOG_LINES, Logging
from host.platforms.k8s_operator.client import K8sOperatorPlatformArgs

_BUILD_NONCE = "31LL8VNCPE5NVCVJDXHNXKGCB4Q7TDKNJPQVLW4QGGMQ0K75WE28LPCFJX2V"
"""
A special string seen when streaming output logs from our private repo build
trigger. This is needed so that we can have potentially secret output in our
LangGraph Platform build _before_ we stream logs to the user. For example, if we
have code that is at risk to leak secrets, or we have want to introduce build
steps in the future that we don't think customers should know about, we can
put it before the build nonce.

See https://github.com/langchain-ai/hosted-langserve-build/blob/main/cloudbuild/cloudbuild_langgraph_api.yaml
for the build steps run, and where the build nonce is printed. Any step
whose output is before the build nonce will not be sent to customers.
"""


class K8sOperatorLogging(Logging):
    @staticmethod
    def _get_deployment_logs_filter(
        service_name: str,
        revision_id: Optional[str] = None,
        query: Optional[str] = None,
    ) -> str:
        """Return the filter string used for retrieving deployment logs.

        If revision_id is None, then the filter will return logs across all
        revisions of the deployment.
        """
        filter_str = 'resource.type="k8s_container"'
        queue_name = f"{service_name[:57]}-queue"

        # Wrap the OR group in parentheses so it’s evaluated together
        filter_str += (
            f' AND (labels."k8s-pod/app"="{service_name}" '
            f'OR labels."k8s-pod/app"="{queue_name}")'
        )
        if revision_id:
            filter_str += f' AND labels."k8s-pod/ls_revision_id"="{revision_id}"'

        if query:
            filter_str += f' AND jsonPayload.event:"{query}"'

        # filter out internal stats logs (e.g. worker stats)
        stat_log_line_filters = []
        for log_line in STAT_LOG_LINES:
            stat_log_line_filters.append(f'jsonPayload.event="{log_line}"')

        stat_log_line_filter = " OR ".join(stat_log_line_filters)
        filter_str += f" AND NOT jsonPayload AND NOT ({stat_log_line_filter})"

        return filter_str

    @staticmethod
    def _get_build_logs_filter(build_id: str, nonce: bool = False) -> str:
        """Return the filter string used for retrieving build logs.

        If a nonce is specified, then the filter string will return the exact
        log line containing the nonce. This functionality can be used to
        retrieve the timestamp of the nonce log line for downstream filtering
        purposes.
        """
        filter_str = 'resource.type="build"'
        filter_str += f' AND resource.labels.build_trigger_id="{settings.PRIVATE_GITHUB_BUILD_TRIGGER_ID}"'
        filter_str += f' AND resource.labels.build_id="{build_id}"'

        if nonce:
            # textPayload contains nonce
            filter_str += f' AND textPayload:"{_BUILD_NONCE}"'
        else:
            # exclude the nonce log line so the value isn't exposed
            filter_str += f' AND NOT textPayload:"{_BUILD_NONCE}"'

        return filter_str

    @staticmethod
    async def list_deployment_logs(
        platform_args: K8sOperatorPlatformArgs,
        service_name: str,
        revision_id: Optional[str],
        start_time: datetime,
        end_time: Optional[datetime] = None,
        order: Literal["asc", "desc"] = "asc",
        limit: int = 50,
        offset: Optional[str] = None,
        query: Optional[str] = None,
        level: Optional[LOG_LEVEL] = None,
    ) -> Tuple[list[dict], Optional[str]]:
        filter_str = K8sOperatorLogging._get_deployment_logs_filter(
            service_name, revision_id, query
        )
        return await list_logs(
            start_time, end_time, order, limit, offset, filter_str, level
        )

    @staticmethod
    async def list_build_logs(
        platform_args: K8sOperatorPlatformArgs,
        service_name: str,
        build_id: str,
        start_time: datetime,
        end_time: Optional[datetime] = None,
        order: Literal["asc", "desc"] = "asc",
        limit: int = 50,
        offset: Optional[str] = None,
    ) -> Tuple[list[dict], Optional[str]]:
        """Build logs are retrieved from K8sLogging.list_build_logs()."""
        logs_after_nonce: list[dict] = []
        next_offset = None

        # get the timestamp of the nonce log line
        filter_str_nonce = K8sOperatorLogging._get_build_logs_filter(build_id, True)
        logs_with_nonce, _ = await list_logs(
            start_time, end_time, "asc", 1, None, filter_str_nonce
        )

        # override the start time to the timestamp of the nonce log line
        if len(logs_with_nonce) > 0:
            nonce_ts = int(logs_with_nonce[0]["timestamp"])
            nonce_start_time = datetime.fromtimestamp(nonce_ts / 1000)
            nonce_start_time_utc = nonce_start_time.astimezone(timezone.utc)

            filter_str = K8sOperatorLogging._get_build_logs_filter(build_id)
            logs_after_nonce, next_offset = await list_logs(
                nonce_start_time_utc, end_time, order, limit, offset, filter_str, None
            )

        return (logs_after_nonce, next_offset)
