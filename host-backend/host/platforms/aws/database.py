import asyncio
import logging

import botocore.exceptions

from host.config import settings
from host.models.databases import get_db_name, get_postgres_uri
from host.models.deployment_type import DeploymentType
from host.platforms.aws.client import (
    AWS_EC2,
    AWS_RDS,
    AwsPlatformArgs,
    get_aws_client,
)
from host.platforms.aws.secrets import AwsSecrets
from host.platforms.base.database import Database
from host.platforms.base.secrets import Secret

logger = logging.getLogger(__name__)


class AwsDatabase(Database):
    @staticmethod
    async def add_postgres_uri_secret(
        service_name: str,
        instance_address: str,
        platform_args: AwsPlatformArgs,
    ) -> None:
        from host.models.env_var import (
            POSTGRES_PASSWORD,
            POSTGRES_URI,
            POSTGRES_URI_CUSTOM,
        )

        # check for postgres uri, save if not found
        secret = await AwsSecrets.get_secret_values(service_name, platform_args)

        # get POSTGRES_URI value
        if instance_address:
            if secret.get(POSTGRES_PASSWORD):
                postgres_uri = get_postgres_uri(
                    secret[POSTGRES_PASSWORD], instance_address
                )
            elif secret.get(POSTGRES_URI):
                postgres_uri = secret[POSTGRES_URI]
            logger.info(
                f"Set POSTGRES_URI host to {instance_address} "
                f"for service {service_name}"
            )
        else:
            postgres_uri = secret.get(POSTGRES_URI_CUSTOM, "")
            logger.info(f"Setting custom POSTGRES_URI for service {service_name}")

        # overwrite POSTGRES_URI
        await AwsSecrets.append_secrets(
            service_name,
            [Secret(name=POSTGRES_URI, value=postgres_uri)],
            platform_args,
        )

    @staticmethod
    async def create_database(
        service_name: str,
        *,
        root_password: str,
        deployment_type: DeploymentType,
        platform_args: AwsPlatformArgs,
    ) -> None:
        from host.models.env_var import POSTGRES_PASSWORD

        secrets = await AwsSecrets.get_secret_values(service_name, platform_args)
        if secrets.get(POSTGRES_PASSWORD):
            logger.info(
                f"Root password for {get_db_name(service_name)} "
                "already saved to secrets."
            )
            root_password = secrets[POSTGRES_PASSWORD]
        else:
            await AwsSecrets.append_secrets(
                service_name,
                [
                    Secret(
                        name=POSTGRES_PASSWORD,
                        value=root_password,
                    )
                ],
                platform_args,
            )

        if deployment_type == DeploymentType.prod:
            tier = "db.m5d.large"
        elif deployment_type in [DeploymentType.dev, DeploymentType.dev_free]:
            tier = "db.c6gd.medium"
        # Get VPC Security Group
        async with get_aws_client(AWS_EC2, platform_args) as ec2_client:
            security_groups = await ec2_client.describe_security_groups(
                Filters=[
                    {
                        "Name": "group-name",
                        "Values": [
                            settings.LANGGRAPH_CLOUD_SERVICE_SECURITY_GROUP_NAME
                        ],
                    }
                ]
            )
            if not security_groups["SecurityGroups"]:
                raise ValueError("No security groups found")
            security_group_id = security_groups["SecurityGroups"][0]["GroupId"]
        async with get_aws_client(AWS_RDS, platform_args) as client:
            db_name = get_db_name(service_name)
            try:
                await client.create_db_instance(
                    DBInstanceIdentifier=db_name,
                    Engine="postgres",
                    EngineVersion="15.8",
                    DBInstanceClass=tier,
                    DBSubnetGroupName=settings.LANGGRAPH_CLOUD_DB_SUBNET_GROUP_NAME,
                    VpcSecurityGroupIds=[security_group_id],
                    MasterUsername="postgres",
                    MasterUserPassword=root_password,
                    AllocatedStorage=20,
                    PubliclyAccessible=False,
                    MultiAZ=deployment_type == DeploymentType.prod,
                    EnablePerformanceInsights=True,
                    StorageEncrypted=True,
                    Tags=[
                        {"Key": "langgraph-cloud", "Value": service_name},
                    ],
                )
                logger.info(
                    f"Creating RDS instance {db_name} for service {service_name}."
                )
            except client.exceptions.DBInstanceAlreadyExistsFault:
                logger.info(f"RDS instance {db_name} already exists.")

    @staticmethod
    async def delete_database(
        service_name: str, platform_args: AwsPlatformArgs
    ) -> None:
        async with get_aws_client(AWS_RDS, platform_args) as client:
            instance_name = get_db_name(service_name)
            try:
                await client.delete_db_instance(
                    DBInstanceIdentifier=instance_name,
                    DeleteAutomatedBackups=True,
                    SkipFinalSnapshot=True,
                )
            except (
                client.exceptions.DBInstanceNotFoundFault,
                client.exceptions.InvalidDBInstanceStateFault,
            ):
                logger.info(
                    f"RDS instance {instance_name} not found in region "
                    f"{platform_args.region_name} or is in an invalid state. "
                    "Skipping deletion."
                )
            except botocore.exceptions.ClientError as e:
                if e.response["Error"]["Code"] == "AccessDenied":
                    logger.warning(
                        f"AccessDenied when attempting to delete RDS instance {instance_name}. Error: {e}"
                    )
                else:
                    raise e

    @staticmethod
    async def wait_for_address(
        service_name: str, platform_args: AwsPlatformArgs
    ) -> str:
        async with get_aws_client(AWS_RDS, platform_args) as client:
            # wait until db exists
            db_name = get_db_name(service_name)
            for _ in range(30):
                try:
                    await client.describe_db_instances(
                        DBInstanceIdentifier=db_name,
                    )
                    break
                except client.exceptions.DBInstanceNotFoundFault:
                    logger.info(
                        f"RDS instance {db_name} not found for service "
                        f"{service_name}. Retrying..."
                    )
                    await asyncio.sleep(30)

            # wait until db is available
            logger.info(
                f"Waiting for RDS instance {db_name} for service "
                f"{service_name} to be available..."
            )
            waiter = client.get_waiter("db_instance_available")
            await waiter.wait(DBInstanceIdentifier=db_name)

            # get instance address
            res = await client.describe_db_instances(
                DBInstanceIdentifier=db_name,
            )
        return res["DBInstances"][0]["Endpoint"]["Address"]
