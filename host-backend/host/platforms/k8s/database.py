import asyncio
import logging
import time
from ssl import <PERSON><PERSON><PERSON><PERSON>
from typing import Literal, Optional

import googleapiclient.errors
from pydantic import BaseModel

from host.config import settings
from host.models.databases import get_db_name, get_postgres_uri
from host.models.deployment_type import CONTAINER_SPECS, DeploymentType
from host.platforms.base.database import CreateDatabaseError, Database
from host.platforms.k8s.client import (
    K8sPlatformArgs,
    load_cloudsql_client,
)
from host.platforms.k8s.secrets import (
    K8sSecrets,
    Secret,
)

logger = logging.getLogger(__name__)

CREATE_INSTANCE_TIMEOUT_SEC = 1200

# Machine tiers: https://cloud.google.com/sql/docs/postgres/instance-settings
# format: db-custom-{cpu}-{memory MB}
DB_CUSTOM_1_3840 = "db-custom-1-3840"
DB_F1_MICRO = "db-f1-micro"

# The LangGraph Server implementation configures 150 max connections
# to the database.
MAX_CONNECTIONS = 150
MAX_CONNECTIONS_BUFFER = 200


class CreateCloudSqlInstanceRequest(BaseModel):
    """This class abstracts the Cloud SQL DatabaseInstance resource.

    Reference: https://cloud.google.com/sql/docs/postgres/admin-api/rest/v1beta4/instances#DatabaseInstance
    """

    name: str
    root_password: str
    region: str
    labels: dict[str, str]
    tier: str
    high_availability: bool
    database_flags: list[dict[str, str]]


class CloudSqlInstance(BaseModel):
    """This class models the Cloud SQL DatabaseInstance resource.

    https://cloud.google.com/sql/docs/postgres/admin-api/rest/v1beta4/instances#DatabaseInstance
    """

    name: str
    state: Literal[
        "SQL_INSTANCE_STATE_UNSPECIFIED",
        "RUNNABLE",
        "SUSPENDED",
        "PENDING_DELETE",
        "PENDING_CREATE",
        "MAINTENANCE",
        "FAILED",
    ]
    private_ip: Optional[str] = None


class K8sDatabase(Database):
    @staticmethod
    def _database_instance(
        request: CreateCloudSqlInstanceRequest,
    ) -> dict:
        """DatabaseInstance schema

        High-availability:
        settings.availabilityType = REGIONAL
        settings.backupConfiguration.enabled = true
        settings.backupConfiguration.pointInTimeRecoveryEnabled = true

        Reference: https://cloud.google.com/sql/docs/postgres/admin-api/rest/v1beta4/instances#DatabaseInstance
        """
        # high availability config
        availability_type = "REGIONAL" if request.high_availability else "ZONAL"
        backup_enabled = "true" if request.high_availability else "false"
        point_in_time_recovery_enabled = (
            "true" if request.high_availability else "false"
        )

        return {
            "name": request.name,
            "databaseVersion": "POSTGRES_15",
            "rootPassword": request.root_password,
            "region": request.region,
            "dataDiskType": "PD_SSD",
            "dataDiskSizeGb": "10",
            "settings": {
                "userLabels": {
                    "product": "hosted-langgraph-api",
                    "vanta-no-alert": "hosted-langgraph-api-deployments-are-excluded-from-audit",
                    **request.labels,
                },
                "tier": request.tier,
                "availabilityType": availability_type,
                "backupConfiguration": {
                    "enabled": backup_enabled,
                    "pointInTimeRecoveryEnabled": point_in_time_recovery_enabled,
                },
                "databaseFlags": request.database_flags,
                "insightsConfig": {
                    "queryInsightsEnabled": "true",
                },
                "ipConfiguration": {
                    "ipv4Enabled": "false",
                    "privateNetwork": f"projects/{settings.GCP_PROJECT_ID}/global/networks/{settings.GCP_HOST_VPC}",
                },
            },
        }

    @staticmethod
    def get_tier(deployment_type: DeploymentType) -> str:
        if deployment_type == DeploymentType.prod:
            return DB_CUSTOM_1_3840
        elif deployment_type == DeploymentType.dev:
            return DB_F1_MICRO
        elif deployment_type == DeploymentType.dev_free:
            return DB_F1_MICRO

    @staticmethod
    def get_max_connections(deployment_type: DeploymentType) -> str:
        return str(
            (MAX_CONNECTIONS * CONTAINER_SPECS[deployment_type]["max_scale"])
            + MAX_CONNECTIONS_BUFFER
        )

    @staticmethod
    async def add_postgres_uri_secret(
        service_name: str,
        instance_address: str,
        platform_args: K8sPlatformArgs,
    ) -> None:
        from host.models.env_var import (
            POSTGRES_PASSWORD,
            POSTGRES_URI,
            POSTGRES_URI_CUSTOM,
        )

        secret = await K8sSecrets.get_secret_values(service_name, platform_args)

        # get POSTGRES_URI value
        if instance_address:
            if secret.get(POSTGRES_PASSWORD):
                postgres_uri = get_postgres_uri(
                    secret[POSTGRES_PASSWORD], instance_address
                )
            elif secret.get(POSTGRES_URI):
                postgres_uri = secret[POSTGRES_URI]
            logger.info(
                f"Set POSTGRES_URI host to {instance_address} "
                f"for service {service_name}"
            )
        else:
            postgres_uri = secret.get(POSTGRES_URI_CUSTOM, "")
            logger.info(f"Setting custom POSTGRES_URI for service {service_name}")

        # overwrite POSTGRES_URI
        await K8sSecrets.append_secrets(
            service_name,
            [Secret(name=POSTGRES_URI, value=postgres_uri)],
            platform_args,
        )

    @staticmethod
    async def create_database(
        service_name: str,
        *,
        root_password: str,
        deployment_type: DeploymentType,
        platform_args: K8sPlatformArgs,
    ) -> None:
        """Create a new Cloud SQL instance.

        insert(): https://cloud.google.com/sql/docs/postgres/admin-api/rest/v1beta4/instances/insert
        Operation (response): https://cloud.google.com/sql/docs/postgres/admin-api/rest/v1beta4/operations#Operation
        """
        # save root password to K8s secrets
        from host.models.env_var import POSTGRES_PASSWORD

        secrets = await K8sSecrets.get_secret_values(service_name, platform_args)
        if secrets.get(POSTGRES_PASSWORD):
            logger.info(
                f"Root password for {get_db_name(service_name)} "
                "already saved to secrets."
            )
            root_password = secrets[POSTGRES_PASSWORD]
        else:
            await K8sSecrets.append_secrets(
                service_name,
                [
                    Secret(
                        name=POSTGRES_PASSWORD,
                        value=root_password,
                    )
                ],
                platform_args,
            )

        # create database if it doesn't exist
        api = load_cloudsql_client()
        try:
            insert_req = api.instances().insert(
                project=settings.GCP_PROJECT_ID,
                body=K8sDatabase._database_instance(
                    CreateCloudSqlInstanceRequest(
                        name=get_db_name(service_name),
                        root_password=root_password,
                        region=platform_args.region,
                        labels=platform_args.labels,
                        tier=K8sDatabase.get_tier(deployment_type),
                        high_availability=deployment_type == DeploymentType.prod,
                        database_flags=[
                            {
                                "name": "max_connections",
                                "value": K8sDatabase.get_max_connections(
                                    deployment_type
                                ),
                            }
                        ],
                    )
                ),
            )

            await asyncio.get_running_loop().run_in_executor(None, insert_req.execute)
            logger.info(
                f"Creating Cloud SQL instance {get_db_name(service_name)} "
                f"for service {service_name}"
            )
        except googleapiclient.errors.HttpError as e:
            if e.status_code == 409:
                logger.info(
                    f"Cloud SQL instance {get_db_name(service_name)} already exists."
                )
            else:
                raise e

    @staticmethod
    async def delete_database(
        service_name: str, platform_args: K8sPlatformArgs
    ) -> None:
        """Delete a Cloud SQL instance.

        delete(): https://cloud.google.com/sql/docs/postgres/admin-api/rest/v1beta4/instances/delete
        """
        api = load_cloudsql_client()

        delete_req = api.instances().delete(
            project=settings.GCP_PROJECT_ID,
            instance=get_db_name(service_name),
        )

        try:
            await asyncio.get_running_loop().run_in_executor(None, delete_req.execute)
        except googleapiclient.errors.HttpError as e:
            if e.status_code == 403:
                # Oddly, a 403 status is returned if the database doesn't exist.
                logger.info(
                    f"Cloud SQL instance {get_db_name(service_name)} does not exist."
                )
            else:
                logger.error(f"Error deleting Cloud SQL instance: {e}")
                raise e

    @staticmethod
    async def wait_for_address(
        service_name: str, platform_args: K8sPlatformArgs
    ) -> str:
        """Wait for Cloud SQL instance to be created."""
        api = load_cloudsql_client()

        start_time_sec = time.time()
        sql_instance: Optional[CloudSqlInstance] = None
        instance_name = get_db_name(service_name)

        get_req = api.instances().get(
            project=settings.GCP_PROJECT_ID,
            instance=instance_name,
        )

        async def _wait_with_timeout(start_time_sec: float, wait_time_sec: float = 1.0):
            current_time = time.time()
            if (
                current_time - start_time_sec + wait_time_sec
                > CREATE_INSTANCE_TIMEOUT_SEC
            ):
                raise TimeoutError("Timed out waiting for new instance to be created.")

            await asyncio.sleep(wait_time_sec)

        while (
            sql_instance is None
            or sql_instance.state != "RUNNABLE"
            or sql_instance.private_ip is None
        ):
            # get Cloud SQL instance
            try:
                get_resp = await asyncio.wait_for(
                    asyncio.get_running_loop().run_in_executor(None, get_req.execute),
                    timeout=5,
                )

                # get private IP address if it's available
                private_ip = None
                ip_addresses = get_resp.get("ipAddresses", [])
                if len(ip_addresses) > 0:
                    ip_mapping = get_resp["ipAddresses"][
                        0
                    ]  # there should only be one IP address
                    if ip_mapping["type"] == "PRIVATE":
                        private_ip = ip_mapping["ipAddress"]

                sql_instance = CloudSqlInstance(
                    name=get_resp["name"],
                    state=get_resp["state"],
                    private_ip=private_ip,
                )
            except SSLError:
                # this is an intermittent error
                logger.warning(
                    "Received SSLError while retrieving new Cloud SQL instance "
                    f"{instance_name}."
                )
            except (asyncio.TimeoutError, TimeoutError):
                # this is an intermittent error that we want to retry
                logger.warning(
                    "Timed out while retrieving new Cloud SQL instance "
                    f"{instance_name}."
                )
            except googleapiclient.errors.HttpError as e:
                if e.status_code == 404:
                    logger.info(
                        "Cloud SQL instance creation has not started for "
                        f"{instance_name}."
                    )
                else:
                    raise e

            # check if instance is in RUNNABLE state
            if sql_instance:
                if sql_instance.state == "RUNNABLE":
                    logger.info(f"Cloud SQL instance {instance_name} in RUNNABLE state")
                    break
                elif sql_instance.state == "FAILED":
                    logger.error(f"Failed to create Cloud SQL instance {instance_name}")
                    project_name = service_name.rsplit("-", 1)[0]
                    err_msg = (
                        f"Failed to create database for deployment {project_name}. "
                        f"Please delete deployment {project_name} and create a new one."
                    )
                    raise CreateDatabaseError(err_msg)
                logger.info(
                    f"Waiting for new Cloud SQL instance {instance_name} "
                    f"to be created. Instance state: {sql_instance.state}"
                )

            # wait before trying again or timing out
            try:
                await _wait_with_timeout(start_time_sec, 30)
            except TimeoutError as e:
                logging.error(
                    "Deploy timed out while waiting for new "
                    f"Cloud SQL instance {instance_name} to be created."
                )
                raise e

        logger.info(f"Address available for Cloud SQL instance {instance_name}")

        assert sql_instance.private_ip is not None
        return sql_instance.private_ip
