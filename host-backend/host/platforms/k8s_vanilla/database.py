import asyncio
import logging
import time

import kubernetes_asyncio.client.exceptions
from kubernetes_asyncio.client import (
    AppsV1<PERSON><PERSON>,
    CoreV1<PERSON>pi,
    V1ObjectMeta,
    V1Service,
    V1ServicePort,
    V1ServiceSpec,
    V1StatefulSet,
)

from host import config
from host.kubernetes_template_manager import KubernetesTemplateManager
from host.models.databases import get_db_name, get_postgres_uri
from host.models.deployment_type import CONTAINER_SPECS, DeploymentType
from host.platforms.base.database import Database
from host.platforms.base.secrets import Secret, get_project_level_secret_base_name
from host.platforms.k8s_vanilla.client import (
    K8sVanillaPlatformArgs,
    load_k8s_client,
)
from host.platforms.k8s_vanilla.secrets import K8sVanillaSecrets

logger = logging.getLogger(__name__)

CREATE_INSTANCE_TIMEOUT_SEC = 1200

# The LangGraph Server implementation configures 150 max connections
# to the database.
MAX_CONNECTIONS = 150
MAX_CONNECTIONS_BUFFER = 200


class K8sVanillaDatabase(Database):
    @staticmethod
    def _pg_service_name(service_name: str) -> str:
        """Return name of the K8s Postgres StatefulSet/Service."""
        return get_db_name(service_name)

    @staticmethod
    def _get_max_connections(deployment_type: DeploymentType) -> int:
        return (
            int(MAX_CONNECTIONS * CONTAINER_SPECS[deployment_type]["max_scale"])
            + MAX_CONNECTIONS_BUFFER
        )

    @staticmethod
    async def _create_service(
        core_v1_api: CoreV1Api,
        service_name: str,
        labels: dict[str, str],
        platform_args: K8sVanillaPlatformArgs,
    ) -> None:
        """Create Service for K8s Postgres StatefulSet."""
        pg_service_name = K8sVanillaDatabase._pg_service_name(service_name)
        service = V1Service(
            api_version="v1",
            kind="Service",
            metadata=V1ObjectMeta(
                name=pg_service_name,
                labels=labels,
            ),
            spec=V1ServiceSpec(
                cluster_ip="None",
                selector={"app": pg_service_name},
                ports=[
                    V1ServicePort(
                        port=5432,
                        target_port=5432,
                    )
                ],
            ),
        )

        await core_v1_api.create_namespaced_service(
            namespace=platform_args.k8s_namespace, body=service
        )
        logger.info(f"Created K8s Postgres Service {pg_service_name}")

    @staticmethod
    async def _create_stateful_set(
        apps_v1_api: AppsV1Api,
        service_name: str,
        postgres_user: str,
        postgres_db: str,
        replicas: int,
        cpu: float,
        memory_mb: int,
        storage_gi: int,
        max_connections: int,
        labels: dict[str, str],
        platform_args: K8sVanillaPlatformArgs,
    ) -> None:
        """Create K8s Postgres StatefulSet from template."""
        pg_service_name = K8sVanillaDatabase._pg_service_name(service_name)
        secret_name = get_project_level_secret_base_name(service_name)

        try:
            # Get the template manager singleton
            template_mgr = KubernetesTemplateManager.get_instance()

            # Render the template to a dict
            stateful_set_dict = template_mgr.render_template(
                "db-statefulset.yaml.tmpl",
                service_name=pg_service_name,
                postgres_user=postgres_user,
                postgres_db=postgres_db,
                replicas=replicas,
                max_connections=max_connections,
                secret_name=secret_name,
                cpu=cpu,
                memory_mb=memory_mb,
                cpu_limit=cpu * 2,
                memory_limit=memory_mb * 2,
                storage_gi=storage_gi,
            )

            # If dev deployment type, set affinity/tolerations for spot
            if not config.settings.IS_SELF_HOSTED and labels.get("deployment_type") in [
                "dev",
                "dev_free",
            ]:
                preferred_terms = (
                    stateful_set_dict.setdefault("spec", {})
                    .setdefault("template", {})
                    .setdefault("spec", {})
                    .setdefault("affinity", {})
                    .setdefault("nodeAffinity", {})
                    .setdefault("preferredDuringSchedulingIgnoredDuringExecution", [])
                )

                # Append the spot-node preference
                preferred_terms.append(
                    {
                        "weight": 100,  # 1-100: higher → stronger preference
                        "preference": {
                            "matchExpressions": [
                                {
                                    "key": "cloud.google.com/gke-spot",
                                    "operator": "In",
                                    "values": ["true"],
                                }
                            ]
                        },
                    }
                )
                stateful_set_dict["spec"]["template"]["spec"].setdefault(
                    "tolerations", []
                ).append(
                    {
                        "key": "interruptible",
                        "operator": "Equal",
                        "value": "true",
                        "effect": "NoSchedule",
                    }
                )

            # Add labels to the stateful set without overwriting the existing labels
            if "labels" in stateful_set_dict["metadata"]:
                stateful_set_dict["metadata"]["labels"].update(labels)
            else:
                stateful_set_dict["metadata"]["labels"] = labels
            if "labels" in stateful_set_dict["spec"]["template"]["metadata"]:
                stateful_set_dict["spec"]["template"]["metadata"]["labels"].update(
                    labels
                )
            else:
                stateful_set_dict["spec"]["template"]["metadata"]["labels"] = labels

            # Create the stateful set
            await apps_v1_api.create_namespaced_stateful_set(
                namespace=platform_args.k8s_namespace, body=stateful_set_dict
            )
        except Exception as e:
            print(f"Error creating stateful set: {e}")
            raise

    @staticmethod
    async def add_postgres_uri_secret(
        service_name: str,
        instance_address: str,
        platform_args: K8sVanillaPlatformArgs,
    ) -> None:
        from host.models.env_var import (
            POSTGRES_PASSWORD,
            POSTGRES_URI,
            POSTGRES_URI_CUSTOM,
        )

        secret = await K8sVanillaSecrets.get_secret_values(service_name, platform_args)

        # get POSTGRES_URI value
        if instance_address:
            if secret.get(POSTGRES_PASSWORD):
                postgres_uri = get_postgres_uri(
                    secret[POSTGRES_PASSWORD], instance_address
                )
            elif secret.get(POSTGRES_URI):
                postgres_uri = secret[POSTGRES_URI]
            logger.info(
                f"Set POSTGRES_URI host to {instance_address} "
                f"for service {service_name}"
            )
        else:
            postgres_uri = secret.get(POSTGRES_URI_CUSTOM, "")
            logger.info(f"Setting custom POSTGRES_URI for service {service_name}")

        # overwrite POSTGRES_URI
        await K8sVanillaSecrets.append_secrets(
            service_name,
            [Secret(name=POSTGRES_URI, value=postgres_uri)],
            platform_args,
        )

    @staticmethod
    async def create_database(
        service_name: str,
        *,
        root_password: str,
        deployment_type: DeploymentType,
        platform_args: K8sVanillaPlatformArgs,
    ) -> None:
        """Create K8s Postgres deployment."""
        # save root password to K8s secrets
        from host.models.env_var import POSTGRES_PASSWORD

        secrets = await K8sVanillaSecrets.get_secret_values(service_name, platform_args)
        if secrets.get(POSTGRES_PASSWORD):
            logger.info(
                f"Root password for {get_db_name(service_name)} "
                "already saved to secrets."
            )
            root_password = secrets[POSTGRES_PASSWORD]
        else:
            await K8sVanillaSecrets.append_secrets(
                service_name,
                [
                    Secret(
                        name=POSTGRES_PASSWORD,
                        value=root_password,
                    )
                ],
                platform_args,
            )

        # create database if it doesn't exist
        async with load_k8s_client(platform_args) as api_client:
            core_v1_api = CoreV1Api(api_client)
            apps_v1_api = AppsV1Api(api_client)

            pg_service_name = K8sVanillaDatabase._pg_service_name(service_name)
            try:
                await apps_v1_api.read_namespaced_stateful_set(
                    name=pg_service_name,
                    namespace=platform_args.k8s_namespace,
                )
                logger.info(
                    f"K8s Postgres StatefulSet {pg_service_name} already exists."
                )
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    if deployment_type == DeploymentType.prod:
                        replicas = 1
                        cpu = 1.0
                        memory_mb = 3840
                        storage_gi = 20
                    elif deployment_type in [
                        DeploymentType.dev,
                        DeploymentType.dev_free,
                    ]:
                        replicas = 1
                        cpu = 0.75
                        memory_mb = 640
                        storage_gi = 10

                    await K8sVanillaDatabase._create_stateful_set(
                        apps_v1_api=apps_v1_api,
                        service_name=service_name,
                        postgres_user="postgres",
                        postgres_db="postgres",
                        replicas=replicas,
                        cpu=cpu,
                        memory_mb=memory_mb,
                        storage_gi=storage_gi,
                        max_connections=K8sVanillaDatabase._get_max_connections(
                            deployment_type
                        ),
                        labels=platform_args.labels,
                        platform_args=platform_args,
                    )
                    await K8sVanillaDatabase._create_service(
                        core_v1_api,
                        pg_service_name,
                        platform_args.labels,
                        platform_args,
                    )
                    logger.info(
                        f"Creating K8s Postgres StatefulSet and Service {pg_service_name} "
                        f"for service {service_name}",
                        exc_info=False,
                    )
                else:
                    raise e

    @staticmethod
    async def delete_database(
        service_name: str, platform_args: K8sVanillaPlatformArgs
    ) -> None:
        """Delete K8s Postgres deployment."""
        async with load_k8s_client(platform_args) as api_client:
            core_v1_api = CoreV1Api(api_client)
            apps_v1_api = AppsV1Api(api_client)

            pg_service_name = K8sVanillaDatabase._pg_service_name(service_name)

            # delete K8s Postgres Service
            try:
                await core_v1_api.delete_namespaced_service(
                    name=pg_service_name,
                    namespace=platform_args.k8s_namespace,
                )
                logger.info(f"Deleted K8s Postgres Service {pg_service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    logger.info(
                        f"K8s Postgres Service {pg_service_name} already deleted",
                        exc_info=False,
                    )
                else:
                    raise e

            # delete K8s Postgres StatefulSet
            try:
                await apps_v1_api.delete_namespaced_stateful_set(
                    name=pg_service_name,
                    namespace=platform_args.k8s_namespace,
                )
                logger.info(f"Deleted K8s Postgres StatefulSet {pg_service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    logger.info(
                        f"K8s Postgres StatefulSet {pg_service_name} already deleted",
                        exc_info=False,
                    )
                else:
                    raise e

    @staticmethod
    async def wait_for_address(
        service_name: str, platform_args: K8sVanillaPlatformArgs
    ) -> str:
        """Wait for K8s Postgres deployment to be created."""
        async with load_k8s_client(platform_args) as api_client:
            apps_api = AppsV1Api(api_client)
            start_time = time.time()

            pg_service_name = K8sVanillaDatabase._pg_service_name(service_name)

            while (time.time() - start_time) < CREATE_INSTANCE_TIMEOUT_SEC:
                stateful_set: V1StatefulSet = (
                    await apps_api.read_namespaced_stateful_set(
                        name=pg_service_name,
                        namespace=platform_args.k8s_namespace,
                    )
                )

                desired_replicas = stateful_set.spec.replicas or 1
                ready_replicas = stateful_set.status.ready_replicas or 0

                if ready_replicas == desired_replicas:
                    logger.info(
                        f"K8s Postgres StatefulSet {pg_service_name} is ready: "
                        f"{ready_replicas}/{desired_replicas} replicas ready."
                    )
                    return f"{pg_service_name}.{platform_args.k8s_namespace}.svc.cluster.local"

                else:
                    logger.info(
                        f"Waiting for K8s Postgres StatefulSet {pg_service_name} "
                        f"replicas to become ready: {ready_replicas}/{desired_replicas}."
                    )

                await asyncio.sleep(30)

            raise TimeoutError(
                f"K8s Postgres StatefulSet {pg_service_name} not healthy "
                f"after {CREATE_INSTANCE_TIMEOUT_SEC} seconds."
            )
