import logging

import kubernetes_asyncio.client
import kubernetes_asyncio.client.exceptions
from kubernetes_asyncio import client

from host import config
from host.kubernetes_template_manager import KubernetesTemplateManager
from host.platforms.base.redis import Redis
from host.platforms.k8s_vanilla.client import K8sVanillaPlatformArgs, load_k8s_client
from host.platforms.k8s_vanilla.secrets import K8sVanillaSecrets, Secret

logger = logging.getLogger(__name__)


class K8sVanillaRedis(Redis):
    @staticmethod
    def _redis_service_name(service_name: str) -> str:
        """Return Redis K8s Service/Deployment name (using the same name).

        The Service resource name must be 63 characters or less.
        """
        return f"{service_name[:57]}-redis"

    @staticmethod
    def redis_uri(service_name: str) -> str:
        return f"redis://{K8sVanillaRedis._redis_service_name(service_name)}:6379"

    @staticmethod
    def _make_service_template(
        service_name: str,
        labels: dict[str, str],
    ) -> client.V1Service:
        """Return Redis K8s Service resource."""
        return client.V1Service(
            metadata=client.V1ObjectMeta(
                name=K8sVanillaRedis._redis_service_name(service_name),
                labels=labels,
            ),
            spec=client.V1ServiceSpec(
                selector={"app": K8sVanillaRedis._redis_service_name(service_name)},
                ports=[client.V1ServicePort(port=6379, target_port=6379)],
                type="ClusterIP",
            ),
        )

    @staticmethod
    def _make_deployment_template(
        service_name: str,
        k8s_owner_service_uid: str,
        labels: dict[str, str],
    ) -> dict:
        """Return Redis K8s Deployment resource using template."""
        redis_service_name = K8sVanillaRedis._redis_service_name(service_name)

        # Get the template manager instance
        template_mgr = KubernetesTemplateManager.get_instance()

        # Render the template to a dict
        deployment_dict = template_mgr.render_template(
            "redis-deployment.yaml.tmpl",
            service_name=redis_service_name,
        )

        # Set the owner reference for the redis service
        deployment_dict["metadata"]["ownerReferences"] = [
            {
                "apiVersion": "v1",
                "kind": "Service",
                "name": redis_service_name,
                "uid": k8s_owner_service_uid,
            }
        ]
        # If a dev instance, add affinity/tolerations to be placed on spot
        if not config.settings.IS_SELF_HOSTED and labels.get("deployment_type") in [
            "dev",
            "dev_free",
        ]:
            preferred_terms = (
                deployment_dict.setdefault("spec", {})
                .setdefault("template", {})
                .setdefault("spec", {})
                .setdefault("affinity", {})
                .setdefault("nodeAffinity", {})
                .setdefault("preferredDuringSchedulingIgnoredDuringExecution", [])
            )

            # Append the spot-node preference
            preferred_terms.append(
                {
                    "weight": 100,  # 1-100: higher → stronger preference
                    "preference": {
                        "matchExpressions": [
                            {
                                "key": "cloud.google.com/gke-spot",
                                "operator": "In",
                                "values": ["true"],
                            }
                        ]
                    },
                }
            )
            deployment_dict["spec"]["template"]["spec"].setdefault(
                "tolerations", []
            ).append(
                {
                    "key": "interruptible",
                    "operator": "Equal",
                    "value": "true",
                    "effect": "NoSchedule",
                }
            )

        # Set the labels, updating if they already exist
        if "labels" in deployment_dict["metadata"]:
            deployment_dict["metadata"]["labels"].update(labels)
        else:
            deployment_dict["metadata"]["labels"] = labels
        if "labels" in deployment_dict["spec"]["template"]["metadata"]:
            deployment_dict["spec"]["template"]["metadata"]["labels"].update(labels)
        else:
            deployment_dict["spec"]["template"]["metadata"]["labels"] = labels

        return deployment_dict

    @staticmethod
    async def _create_k8s_service(
        service_name: str,
        platform_args: K8sVanillaPlatformArgs,
    ) -> client.V1Service:
        """Create Redis K8s Service."""
        async with load_k8s_client(platform_args) as api_client:
            core_v1_api = kubernetes_asyncio.client.CoreV1Api(api_client)

            service: client.V1Service
            try:
                # get the service
                service = await core_v1_api.read_namespaced_service(
                    namespace=platform_args.k8s_namespace,
                    name=K8sVanillaRedis._redis_service_name(service_name),
                )
                return service
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    # service not found, create the service
                    service = await core_v1_api.create_namespaced_service(
                        namespace=platform_args.k8s_namespace,
                        body=K8sVanillaRedis._make_service_template(
                            service_name, platform_args.labels
                        ),
                    )
                    logger.info(
                        f"Created Redis K8s Service for LangGraph Platform service {service_name}",
                        exc_info=False,
                    )
                    return service
                else:
                    raise e

    @staticmethod
    async def _create_k8s_deployment(
        service_name: str,
        k8s_owner_service_uid: str,
        platform_args: K8sVanillaPlatformArgs,
    ) -> client.V1Deployment:
        """Create Redis K8s Deployment."""
        async with load_k8s_client(platform_args) as api_client:
            api_instance = kubernetes_asyncio.client.AppsV1Api(api_client)

            deployment: client.V1Deployment
            try:
                # get the deployment
                deployment = await api_instance.read_namespaced_deployment(
                    namespace=platform_args.k8s_namespace,
                    name=K8sVanillaRedis._redis_service_name(service_name),
                )
                return deployment
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    # deployment not found, create the deployment
                    deployment = await api_instance.create_namespaced_deployment(
                        namespace=platform_args.k8s_namespace,
                        body=K8sVanillaRedis._make_deployment_template(
                            service_name,
                            k8s_owner_service_uid,
                            platform_args.labels,
                        ),
                    )
                    logger.info(
                        f"Created Redis K8s Deployment for Knative service {service_name}",
                        exc_info=False,
                    )
                    return deployment
                else:
                    raise e

    @staticmethod
    async def add_redis_uri_secret(
        service_name: str,
        redis_uri: str,
        platform_args: K8sVanillaPlatformArgs,
    ) -> None:
        from host.models.env_var import (
            REDIS_URI,
            REDIS_URI_CUSTOM,
        )

        # get REDIS_URI_CUSTOM value
        if not redis_uri:
            secret = await K8sVanillaSecrets.get_secret_values(
                service_name, platform_args
            )
            if secret.get(REDIS_URI_CUSTOM):
                redis_uri = secret[REDIS_URI_CUSTOM]
                logger.info(
                    f"Set REDIS_URI to value of REDIS_URI_CUSTOM for service {service_name}"
                )
        else:
            logger.info(f"Set REDIS_URI to default value for service {service_name}")

        # overwrite REDIS_URI
        await K8sVanillaSecrets.append_secrets(
            service_name,
            [Secret(name=REDIS_URI, value=redis_uri)],
            platform_args,
        )

    @staticmethod
    async def create_redis(
        service_name: str, platform_args: K8sVanillaPlatformArgs
    ) -> None:
        service = await K8sVanillaRedis._create_k8s_service(service_name, platform_args)
        await K8sVanillaRedis._create_k8s_deployment(
            service_name, service.metadata.uid, platform_args
        )

    @staticmethod
    async def delete_redis(
        service_name: str, platform_args: K8sVanillaPlatformArgs
    ) -> None:
        async with load_k8s_client(platform_args) as api_client:
            core_v1_api = kubernetes_asyncio.client.CoreV1Api(api_client)

            name = K8sVanillaRedis._redis_service_name(service_name)
            try:
                await core_v1_api.delete_namespaced_service(
                    name=name,
                    namespace=platform_args.k8s_namespace,
                )
                logger.info(f"Deleted Redis K8s Service {name}")
                # K8s Deployment is automatically deleted
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    logger.info(
                        f"Redis K8s Service {name} already deleted", exc_info=False
                    )
                else:
                    raise e
