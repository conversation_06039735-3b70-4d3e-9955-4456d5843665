import asyncio
import logging
import time
from typing import Optional
from uuid import UUID

import httpx
import kubernetes_asyncio.client
import kubernetes_asyncio.client.exceptions

from host.config import settings
from host.models.build import BuildResult
from host.models.databases import get_postgres_uri
from host.models.deploy import CreateServiceRequest, K8sEnvVar, KnativeEnvVar
from host.platforms.base.container import Container
from host.platforms.base.secrets import Secret
from host.platforms.k8s_vanilla.autoscaling import K8sVanillaAutoScaling
from host.platforms.k8s_vanilla.client import K8sVanillaPlatformArgs, load_k8s_client
from host.platforms.k8s_vanilla.schema import (
    K8sVanillaResourceId,
    K8sVanillaRevision,
    K8sVanillaService,
)
from host.platforms.k8s_vanilla.secrets import K8sVanillaSecrets

logger = logging.getLogger(__name__)


class K8sVanillaContainer(Container):
    @staticmethod
    def _get_host(service_name: str) -> str:
        return f"{service_name}.{settings.HOSTED_K8S_ROOT_DOMAIN}"

    @staticmethod
    def _get_url(service_name: str) -> str:
        return f"https://{K8sVanillaContainer._get_host(service_name)}"

    @staticmethod
    def _get_internal_url(service_name: str, namespace: str) -> str:
        return f"http://{service_name}.{namespace}:8000"

    @staticmethod
    def _make_deployment_template(
        request: CreateServiceRequest,
        deployment: Optional[dict] = None,
    ) -> dict:
        metadata = {
            "name": request.service_name,
            "labels": {
                "app": request.service_name,
                "product": "hosted-langgraph-api",
                "vanta-no-alert": "hosted-langgraph-api-deployments-are-excluded-from-audit",
                "ls_revision_id": str(request.revision_id),
                **request.labels,
                "ls_timestamp": str(int(time.time())),
            },
        }

        if deployment:
            if "resourceVersion" in deployment["metadata"]:
                metadata["resourceVersion"] = deployment["metadata"]["resourceVersion"]

        return {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": metadata,
            "spec": {
                "replicas": request.container_min_scale,
                "selector": {"matchLabels": {"app": request.service_name}},
                "template": {
                    "metadata": {"labels": metadata["labels"]},
                    "spec": {
                        "containers": [
                            {
                                "name": "api-server",
                                "image": request.build.image_path,
                                "resources": {
                                    "requests": {
                                        "cpu": str(request.container_cpu),
                                        "memory": f"{request.container_memory_mb}Mi",
                                    },
                                    "limits": {
                                        "cpu": str(request.container_cpu * 2),
                                        "memory": f"{request.container_memory_mb * 2}Mi",
                                    },
                                },
                                "ports": [
                                    {
                                        "name": "api-server",
                                        "containerPort": 8000,
                                        "protocol": "TCP",
                                    }
                                ],
                                "env": [
                                    env_var.model_dump(exclude=["type"])
                                    for env_var in request.env_vars or []
                                ],
                                "readinessProbe": {
                                    "httpGet": {
                                        "path": "/ok?check_db=1",
                                        "port": 8000,
                                    },
                                    "initialDelaySeconds": 15,
                                    "successThreshold": 1,
                                    "failureThreshold": 6,
                                    "periodSeconds": 5,
                                    "timeoutSeconds": 5,
                                },
                                "livenessProbe": {
                                    "httpGet": {
                                        "path": "/ok?check_db=1",
                                        "port": 8000,
                                    },
                                    "initialDelaySeconds": 15,
                                    "successThreshold": 1,
                                    "failureThreshold": 6,
                                    "periodSeconds": 5,
                                    "timeoutSeconds": 5,
                                },
                            }
                        ],
                        "enableServiceLinks": False,
                    },
                },
            },
        }

    @staticmethod
    def _make_service_template(
        request: CreateServiceRequest,
        service: Optional[dict] = None,
    ) -> dict:
        metadata = {
            "name": request.service_name,
            "labels": {
                "app": request.service_name,
                "product": "hosted-langgraph-api",
                "vanta-no-alert": "hosted-langgraph-api-deployments-are-excluded-from-audit",
                "ls_revision_id": str(request.revision_id),
                **request.labels,
                "ls_timestamp": str(int(time.time())),
            },
        }

        if service:
            if "resourceVersion" in service["metadata"]:
                metadata["resourceVersion"] = service["metadata"]["resourceVersion"]

        return {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": metadata,
            "spec": {
                "selector": {"app": request.service_name},
                "ports": [{"protocol": "TCP", "port": 8000, "targetPort": 8000}],
            },
        }

    @staticmethod
    def _make_ingress_template(
        request: CreateServiceRequest,
        ingress: Optional[dict] = None,
    ) -> dict:
        metadata = {
            "name": request.service_name,
            "labels": {
                "app": request.service_name,
                "product": "hosted-langgraph-api",
                "vanta-no-alert": "hosted-langgraph-api-deployments-are-excluded-from-audit",
                "ls_revision_id": str(request.revision_id),
                **request.labels,
                "ls_timestamp": str(int(time.time())),
            },
            "annotations": {
                "nginx.ingress.kubernetes.io/proxy-read-timeout": "3600",  # 3600 seconds (1 hour)
            },
        }

        if ingress:
            if "resourceVersion" in ingress["metadata"]:
                metadata["resourceVersion"] = ingress["metadata"]["resourceVersion"]
        host = K8sVanillaContainer._get_host(request.service_name)

        ingress_spec = {
            "rules": [
                {
                    "host": host,
                    "http": {
                        "paths": [
                            {
                                "path": "/",
                                "pathType": "Prefix",
                                "backend": {
                                    "service": {
                                        "name": request.service_name,
                                        "port": {"number": 8000},
                                    }
                                },
                            }
                        ]
                    },
                }
            ],
            "tls": [{"hosts": [host]}],
        }

        if not settings.IS_SELF_HOSTED:
            ingress_spec["ingressClassName"] = "nginx"

        return {
            "apiVersion": "networking.k8s.io/v1",
            "kind": "Ingress",
            "metadata": metadata,
            "spec": ingress_spec,
        }

    @staticmethod
    def _get_network_policy_name(namespace: str) -> str:
        return f"default-{namespace}"

    @staticmethod
    def _make_network_policy(
        namespace: str,
        existing_policy: Optional[dict] = None,
    ) -> dict:
        """
        Creates (or updates) a Kubernetes NetworkPolicy that only cares about
        restricting Ingress (allowing traffic only from pods labeled with
        'app: <service_name>'). Egress is unrestricted.
        """
        metadata = {
            "name": K8sVanillaContainer._get_network_policy_name(namespace),
        }

        # If we’re updating an existing NetworkPolicy, carry over resourceVersion.
        if existing_policy and "metadata" in existing_policy:
            rv = existing_policy["metadata"].get("resourceVersion")
            if rv:
                metadata["resourceVersion"] = rv

        ingress = [
            {
                "from": [
                    # (1) Pods in the same namespace (empty podSelector)
                    {"podSelector": {}},
                    # (2) All Pods in the kube-system namespace
                    {
                        "namespaceSelector": {
                            "matchLabels": {
                                "kubernetes.io/metadata.name": "kube-system"
                            }
                        }
                    },
                ]
            }
        ]
        if settings.IS_SELF_HOSTED:
            ingress[0]["from"].append(
                # (3) All Pods in the host-backend deployment
                {
                    "podSelector": {
                        "matchLabels": {
                            "app.kubernetes.io/component": "langsmith-host-backend"
                        }
                    }
                }
            )

        return {
            "apiVersion": "networking.k8s.io/v1",
            "kind": "NetworkPolicy",
            "metadata": metadata,
            "spec": {
                "podSelector": {},  # matches all pods in this namespace
                "policyTypes": ["Ingress"],  # only restricting inbound traffic
                "ingress": ingress,
            },
        }

    @staticmethod
    async def _add_postgres_uri_secret(
        service_name: str,
        instance_address: str,
        platform_args: K8sVanillaPlatformArgs,
    ) -> None:
        from host.models.env_var import POSTGRES_PASSWORD, POSTGRES_URI

        secret = await K8sVanillaSecrets.get_secret_values(service_name, platform_args)

        if not secret.get(POSTGRES_URI):
            await K8sVanillaSecrets.append_secrets(
                service_name,
                [
                    Secret(
                        name=POSTGRES_URI,
                        value=get_postgres_uri(
                            secret[POSTGRES_PASSWORD], instance_address
                        ),
                    )
                ],
                platform_args,
            )
            logger.info(
                f"Set POSTGRES_URI host to {instance_address} "
                f"for service {service_name}"
            )

    @staticmethod
    async def upsert_container(
        service_name: str,
        revision_id: str,
        *,
        image_name: str,
        container_cpu: float,
        container_memory_mb: int,
        container_min_scale: int,
        container_max_scale: int,
        env_vars: list[KnativeEnvVar],
        platform_args: K8sVanillaPlatformArgs,
    ) -> K8sVanillaResourceId:
        async with load_k8s_client(platform_args) as api_client:
            apps_v1_api = kubernetes_asyncio.client.AppsV1Api(api_client)
            core_v1_api = kubernetes_asyncio.client.CoreV1Api(api_client)
            network_v1_api = kubernetes_asyncio.client.NetworkingV1Api(api_client)

            request = CreateServiceRequest(
                service_name=service_name,
                build=BuildResult(image_path=image_name),
                env_vars=env_vars,
                revision_id=UUID(revision_id),
                labels=platform_args.labels,
                container_min_scale=container_min_scale,
                container_max_scale=container_max_scale,
                container_cpu=container_cpu,
                container_memory_mb=container_memory_mb,
            )

            # Create or update deployment
            try:
                deployment = await apps_v1_api.read_namespaced_deployment(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                )
                deployment_body = K8sVanillaContainer._make_deployment_template(
                    request, deployment.to_dict()
                )
                await apps_v1_api.replace_namespaced_deployment(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                    body=deployment_body,
                )
                logger.info(f"Updated K8s Deployment {service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    deployment_body = K8sVanillaContainer._make_deployment_template(
                        request
                    )
                    await apps_v1_api.create_namespaced_deployment(
                        namespace=platform_args.k8s_namespace,
                        body=deployment_body,
                    )
                    logger.info(f"Created K8s Deployment {service_name}")
                else:
                    logger.exception(
                        f"Unexpected K8s API error for revision ID {revision_id}"
                    )
                    raise e

            # Create or update service
            try:
                service = await core_v1_api.read_namespaced_service(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                )
                service_body = K8sVanillaContainer._make_service_template(
                    request, service.to_dict()
                )
                await core_v1_api.replace_namespaced_service(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                    body=service_body,
                )
                logger.info(f"Updated K8s Service {service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    service_body = K8sVanillaContainer._make_service_template(request)
                    await core_v1_api.create_namespaced_service(
                        namespace=platform_args.k8s_namespace,
                        body=service_body,
                    )
                    logger.info(f"Created K8s Service {service_name}")
                else:
                    logger.exception(
                        f"Unexpected K8s API error for revision ID {revision_id}"
                    )
            # Create or update ingress
            try:
                ingress = await network_v1_api.read_namespaced_ingress(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                )
                ingress_body = K8sVanillaContainer._make_ingress_template(
                    request, ingress.to_dict()
                )
                await network_v1_api.replace_namespaced_ingress(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                    body=ingress_body,
                )
                logger.info(f"Updated K8s Service {service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    ingress_body = K8sVanillaContainer._make_ingress_template(request)
                    await network_v1_api.create_namespaced_ingress(
                        namespace=platform_args.k8s_namespace,
                        body=ingress_body,
                    )
                    logger.info(f"Created K8s ingress {service_name}")
                else:
                    logger.exception(
                        f"Unexpected K8s API error for revision ID {revision_id}"
                    )

        await K8sVanillaAutoScaling.upsert_autoscalers(
            service_name,
            container_min_scale,
            container_max_scale,
            platform_args,
        )

        # wait for new revision to be healthy
        await K8sVanillaContainer.wait_for_health_check(service_name, platform_args)

        # return latest revision ID
        service = await K8sVanillaContainer.get_service(service_name, platform_args)
        assert service.latest_revision is not None
        return service.latest_revision.id

    @staticmethod
    async def ensure_namespace(platform_args: K8sVanillaPlatformArgs) -> None:
        namespace = platform_args.k8s_namespace
        if not namespace or namespace == settings.HOSTED_K8S_NAMESPACE:
            return
        async with load_k8s_client(platform_args) as api_client:
            core_v1_api = kubernetes_asyncio.client.CoreV1Api(api_client)
            try:
                await core_v1_api.create_namespace(
                    body=kubernetes_asyncio.client.V1Namespace(
                        metadata={"name": namespace}
                    )
                )
                logger.info(f"Created K8s Namespace {namespace}")

            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 409:
                    logger.info(f"K8s Namespace {namespace} already exists")
                else:
                    logger.warning(f"Failed to create K8s Namespace {namespace}: {e}")
                    raise e

            # Create network policy for namespace
            network_v1_api = kubernetes_asyncio.client.NetworkingV1Api(api_client)
            network_policy_name = K8sVanillaContainer._get_network_policy_name(
                namespace
            )
            # Create or update network policy
            try:
                network_policy = await network_v1_api.read_namespaced_network_policy(
                    name=network_policy_name,
                    namespace=namespace,
                )
                network_policy_body = K8sVanillaContainer._make_network_policy(
                    namespace, network_policy.to_dict()
                )
                await network_v1_api.replace_namespaced_network_policy(
                    name=network_policy_name,
                    body=network_policy_body,
                    namespace=namespace,
                )
                logger.info(f"Updated K8s NetworkPolicy: {network_policy_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    network_policy_body = K8sVanillaContainer._make_network_policy(
                        namespace
                    )
                    await network_v1_api.create_namespaced_network_policy(
                        namespace=namespace,
                        body=network_policy_body,
                    )
                    logger.info(f"Created K8s NetworkPolicy {network_policy_name}")
                else:
                    logger.exception(
                        f"Unexpected K8s API error for revision ID {network_policy_name}, {e}"
                    )
                    raise e

    @staticmethod
    async def _wait_with_timeout(
        start_time_sec: float,
        wait_time_sec: float = 1.0,
        timeout_sec: int = 600,
        error_msg: str = "",
    ):
        current_time = time.time()
        if current_time - start_time_sec + wait_time_sec > timeout_sec:
            raise TimeoutError(error_msg)

        await asyncio.sleep(wait_time_sec)

    @staticmethod
    async def wait_for_health_check(
        service_name: str, platform_args: K8sVanillaPlatformArgs
    ) -> None:
        async with load_k8s_client(platform_args) as api_client:
            apps_v1_api = kubernetes_asyncio.client.AppsV1Api(api_client)
            # Wait for deployment to be ready
            start_time_sec = time.time()
            while True:
                try:
                    deployment = await apps_v1_api.read_namespaced_deployment_status(
                        name=service_name,
                        namespace=platform_args.k8s_namespace,
                    )
                    status = deployment.status
                    # Updated replicas is the number of replicas that have been updated on the latest change
                    # Available replicas is the number of replicas that are available to service traffic
                    # Replicas is the number of replicas desired for the deployment
                    # Observed generation is the most recent generation observed for this deployment
                    # We check that the deployment has been updated to the latest generation
                    # and that all replicas are updated/available
                    if (
                        status.updated_replicas == deployment.spec.replicas
                        and status.replicas == deployment.spec.replicas
                        and status.available_replicas == deployment.spec.replicas
                        and status.observed_generation >= deployment.metadata.generation
                    ):
                        logger.info(f"Deployment {service_name} is ready")
                        break

                    logger.info(
                        f"Waiting for deployment {service_name} to be ready. "
                        f"Available replicas: {deployment.status.available_replicas}"
                    )

                    await K8sVanillaContainer._wait_with_timeout(
                        start_time_sec,
                        15,
                        settings.HOSTED_K8S_DEPLOYMENT_READY_TIMEOUT_SECONDS,
                        (
                            "Timeout: Deployment is not ready after "
                            f"{settings.HOSTED_K8S_DEPLOYMENT_READY_TIMEOUT_SECONDS} seconds."
                        ),
                    )

                except kubernetes_asyncio.client.exceptions.ApiException as e:
                    logger.warning(f"Error checking deployment status: {e}")
                    await K8sVanillaContainer._wait_with_timeout(
                        start_time_sec,
                        15,
                        300,
                        "Timeout: Failed to check deployment status after 300 seconds.",
                    )

            # check healthcheck endpoint
            start_time_sec = time.time()
            service = await K8sVanillaContainer.get_service(service_name, platform_args)
            async with httpx.AsyncClient(timeout=httpx.Timeout(20)) as aclient:
                status_code = 0
                while status_code != 200:
                    response = await aclient.get(f"{service.url}/ok?check_db=1")

                    status_code = response.status_code
                    if status_code == 200:
                        break

                    logger.info(
                        f"Waiting for service {service_name} health check to "
                        f"succeed. Status code: {status_code}"
                    )
                    await K8sVanillaContainer._wait_with_timeout(
                        start_time_sec,
                        15,
                        600,
                        "Timeout: New revision health check did not succeed after 600 seconds.",
                    )
                logger.info(
                    "Successfully called health check endpoint "
                    f"for K8s service {service_name}"
                )

    @staticmethod
    async def delete_container(
        service_name: str, platform_args: K8sVanillaPlatformArgs
    ) -> None:
        async with load_k8s_client(platform_args) as api_client:
            apps_v1_api = kubernetes_asyncio.client.AppsV1Api(api_client)
            core_v1_api = kubernetes_asyncio.client.CoreV1Api(api_client)
            network_v1_api = kubernetes_asyncio.client.NetworkingV1Api(api_client)

            # Delete deployment
            try:
                await apps_v1_api.delete_namespaced_deployment(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                )
                logger.info(f"Deleted K8s Deployment {service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    logger.info(f"K8s Deployment {service_name} is already deleted")
                else:
                    logger.warning(
                        f"Failed to delete K8s Deployment {service_name}: {e}"
                    )

            # Delete service
            try:
                await core_v1_api.delete_namespaced_service(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                )
                logger.info(f"Deleted K8s Service {service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    logger.info(f"K8s Service {service_name} is already deleted")
                else:
                    logger.warning(f"Failed to delete K8s Service {service_name}: {e}")

            # Delete ingress
            try:
                await network_v1_api.delete_namespaced_ingress(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                )
                logger.info(f"Deleted K8s Ingress {service_name}")
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    logger.info(f"K8s Ingress {service_name} is already deleted")
                else:
                    logger.warning(f"Failed to delete K8s Ingress {service_name}: {e}")

    @staticmethod
    async def get_revision(
        service_name: str,
        resource_id: K8sVanillaResourceId,
        platform_args: K8sVanillaPlatformArgs,
    ) -> K8sVanillaRevision:
        # Get the replicaset for the revision
        async with load_k8s_client(platform_args) as api_client:
            apps_v1_api = kubernetes_asyncio.client.AppsV1Api(api_client)
            try:
                replica_set = await apps_v1_api.read_namespaced_replica_set(
                    name=resource_id.name,
                    namespace=platform_args.k8s_namespace,
                )
                container = replica_set.spec.template.spec.containers[0]
                env_vars = [
                    K8sEnvVar.model_validate(env_var.to_dict())
                    for env_var in container.env
                ]
                # decode secret values
                decoded_secrets = await K8sVanillaSecrets.get_secret_values(
                    service_name, platform_args
                )
                for env_var in env_vars:
                    if env_var.type == "secret" and decoded_secrets.get(env_var.name):
                        env_var.value = decoded_secrets[env_var.name]

                hosted_langserve_revision_id = replica_set.metadata.labels.get(
                    "ls_revision_id"
                )
                return K8sVanillaRevision(
                    id=resource_id,
                    env_vars=env_vars,
                    hosted_langserve_revision_id=UUID(hosted_langserve_revision_id)
                    if hosted_langserve_revision_id
                    else None,
                )
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                if e.status == 404:
                    return K8sVanillaRevision(
                        id=resource_id,
                    )
                else:
                    raise e

    @staticmethod
    async def get_service(
        service_name: str, platform_args: K8sVanillaPlatformArgs
    ) -> K8sVanillaService:
        async with load_k8s_client(platform_args) as api_client:
            apps_v1_api = kubernetes_asyncio.client.AppsV1Api(api_client)
            try:
                deployment = await apps_v1_api.read_namespaced_deployment(
                    name=service_name,
                    namespace=platform_args.k8s_namespace,
                )
            except kubernetes_asyncio.client.exceptions.ApiException as e:
                logger.warning(f"Failed to get deployment {service_name}: {e}")
                if e.status == 404:
                    return K8sVanillaService(
                        id=K8sVanillaResourceId(type="services", name=service_name),
                        url=None,
                        latest_revision=None,
                        latest_active_revision=None,
                    )
                else:
                    raise e
            latest_revision = None
            latest_active_revision = None
            latest_revision_number = deployment.metadata.annotations.get(
                "deployment.kubernetes.io/revision"
            )
            replica_sets = await apps_v1_api.list_namespaced_replica_set(
                namespace=platform_args.k8s_namespace,
                label_selector=f"app={service_name}",
            )
            sorted_replica_sets = sorted(
                replica_sets.items,
                key=lambda rs: rs.metadata.creation_timestamp,
                reverse=True,
            )
            for replica_set in sorted_replica_sets:
                if (
                    replica_set.metadata.annotations.get(
                        "deployment.kubernetes.io/revision"
                    )
                    == latest_revision_number
                ):
                    latest_revision = await K8sVanillaContainer.get_revision(
                        service_name,
                        K8sVanillaResourceId(
                            type="revisions", name=replica_set.metadata.name
                        ),
                        platform_args,
                    )
                # Find the latest active revision
                if (
                    replica_set.status.ready_replicas
                    and replica_set.status.ready_replicas >= 1
                ):
                    latest_active_revision = await K8sVanillaContainer.get_revision(
                        service_name,
                        K8sVanillaResourceId(
                            type="revisions", name=replica_set.metadata.name
                        ),
                        platform_args,
                    )
        url = K8sVanillaContainer._get_url(service_name)
        if settings.IS_SELF_HOSTED:
            url = K8sVanillaContainer._get_internal_url(
                service_name,
                platform_args.k8s_namespace or settings.HOSTED_K8S_NAMESPACE,
            )

        return K8sVanillaService(
            id=K8sVanillaResourceId(type="services", name=service_name),
            url=url,
            latest_revision=latest_revision,
            latest_active_revision=latest_active_revision,
        )
