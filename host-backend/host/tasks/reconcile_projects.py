import logging
from typing import Optional
from uuid import UUID

from lc_config.settings import shared_settings
from lc_database import redis
from saq.job import Status as SaqJobStatus

from host.config import settings
from host.models import build, env_var
from host.models.databases import generate_password
from host.models.deployment_type import (
    CONTAINER_SPECS,
    CONTAINER_SPECS_AWS,
    DeploymentPlatformId,
    DeploymentType,
)
from host.models.host_metadata_crud import (
    ProjectStatus,
    RevisionStatus,
    get_project_platform,
    service_name,
)
from host.tasks import build_image, deploy_image
from host.tasks.utils import (
    get_project,
    get_project_secrets,
    get_projects_by_status,
    get_revision,
    get_revisions_by_status,
    set_project_status,
)

logger = logging.getLogger(__name__)


async def _reconcile_awaiting_database() -> None:
    """Reconcile projects in AWAITING_DATABASE status."""
    logger.debug("Reconciling projects in AWAITING_DATABASE status...")

    projects_awaiting_database = await get_projects_by_status(
        ProjectStatus.AWAITING_DATABASE.value.status
    )
    for project in projects_awaiting_database:
        try:
            platform = get_project_platform(project)
            service_name_ = service_name(project.tenant_id, project.name)
            deployment_type = project.metadata.get("deployment_type", "prod")

            # check for POSTGRES_URI_CUSTOM
            secrets_ = await get_project_secrets(project.id)
            if env_var.POSTGRES_URI_CUSTOM in secrets_:
                logger.info(
                    f"POSTGRES_URI_CUSTOM is set for project ID {project.id}. "
                    "Skipping database creation. Setting project status to READY."
                )
                await set_project_status(project.id, ProjectStatus.READY.value.status)
                continue

            async with redis.async_queue(shared_settings.HOST_QUEUE) as queue:
                await queue.enqueue(
                    job_or_func="create_db_instance",
                    pipeline=None,
                    key=f"create_db_instance:{str(project.id)}",
                    retries=3,
                    retry_delay=5,
                    retry_backoff=True,
                    timeout=60,
                    # func params
                    request={
                        "root_password": generate_password(),
                        "region": platform.region,
                        "labels": {
                            "deployment_type": deployment_type,
                            "service": service_name_,
                            "ls_tenant_id": str(project.tenant_id),
                            "ls_project_id": str(project.id),
                            "region": settings.HOST_DEPLOYMENT_REGION,
                            "env": settings.DD_ENV,
                        },
                        "deployment_type": deployment_type,
                    },
                    service_name=service_name_,
                    tenant_id=str(project.tenant_id),
                    project_id=str(project.id),
                    database_platform=platform.database_platform,
                )
            logger.info(f"Enqueued create database job for project ID {project.id}")
        except Exception as e:
            logger.error(f"Error reconciling project ID {project.id}: {e}")
            continue


async def _reconcile_awaiting_build(revision_id: Optional[UUID] = None) -> None:
    """Reconcile revisions in AWAITING_BUILD status.

    This function is also used to reconcile individual revision IDs in
    BUILDING status.
    """
    revisions_awaiting_build = []
    if revision_id:
        logger.info(f"Reconciling individual revision ID {str(revision_id)}")
        revision = await get_revision(revision_id)
        if revision and revision.status in [
            RevisionStatus.AWAITING_BUILD.value.status,
            RevisionStatus.BUILDING.value.status,
        ]:
            revisions_awaiting_build.append(revision)
    else:
        logger.debug("Reconciling revisions in AWAITING_BUILD status...")
        revisions_awaiting_build = await get_revisions_by_status(
            RevisionStatus.AWAITING_BUILD.value.status
        )

    for revision in revisions_awaiting_build:
        try:
            project = await get_project(revision.project_id)
            if project is None:
                logger.warning(
                    f"Project ID {revision.project_id} not found for revision ID {revision.id}"
                )
                continue

            service_name_ = service_name(project.tenant_id, project.name)

            # Set repo_commit to the repo_commit_sha if it exists. This ensures that
            # the repo_commit_sha metadata is in sync with the actual commit sha that
            # is built by Cloud Build.
            repo_commit = revision.repo_commit
            if revision.metadata.get("repo_commit_sha"):
                repo_commit = revision.metadata["repo_commit_sha"]

            async with redis.async_queue(shared_settings.HOST_QUEUE) as queue:
                await queue.enqueue(
                    job_or_func="build_image",
                    pipeline=None,
                    key=f"build_image:{str(revision.id)}",
                    retries=build_image.MAX_RETRIES,
                    retry_delay=5,
                    retry_backoff=True,
                    timeout=3600,  # 1 hour
                    heartbeat=120,  # 2 minutes
                    # func params
                    tenant_id=str(project.tenant_id),
                    revision_id=str(revision.id),
                    request={
                        "service_name": service_name_,
                        "repo_url": project.repo_url,
                        "repo_path": revision.repo_path,
                        "repo_commit": repo_commit,
                        "revision_id": str(revision.id),
                    },
                )
            logger.info(f"Enqueued build job for revision ID {revision.id}")
        except Exception as e:
            logger.error(f"Error reconciling revision ID {revision.id}: {e}")
            continue


async def _reconcile_awaiting_deploy(revision_id: Optional[UUID] = None) -> None:
    """Reconcile revisions in AWAITING_DEPLOY status.

    This function is also used to reconcile individual revision IDs in
    DEPLOYING status.
    """
    revisions_awaiting_deploy = []
    if revision_id:
        logger.info(f"Reconciling individual revision ID {str(revision_id)}")
        revision = await get_revision(revision_id)
        if revision and revision.status in [
            RevisionStatus.AWAITING_DEPLOY.value.status,
            RevisionStatus.DEPLOYING.value.status,
        ]:
            revisions_awaiting_deploy.append(revision)
    else:
        logger.debug("Reconciling revisions in AWAITING_DEPLOY status...")
        revisions_awaiting_deploy = await get_revisions_by_status(
            RevisionStatus.AWAITING_DEPLOY.value.status
        )

    for revision in revisions_awaiting_deploy:
        try:
            project = await get_project(revision.project_id)
            if project is None:
                logger.warning(
                    f"Project ID {revision.project_id} not found for revision ID {revision.id}"
                )
                continue

            platform = get_project_platform(project)
            service_name_ = service_name(project.tenant_id, project.name)

            # get image path
            image_path = revision.image_path
            if image_path is None:
                build_ = await build.get_build(revision.gcp_build_name or "")
                image_path = build_.images[0]

            # get container resource specs
            container_spec = project.container_spec
            deployment_type = project.metadata.get("deployment_type", "prod")
            if platform.deployment_platform == DeploymentPlatformId.aws_ecs:
                default_container_spec = CONTAINER_SPECS_AWS[
                    DeploymentType[deployment_type]
                ]
            else:
                default_container_spec = CONTAINER_SPECS[
                    DeploymentType[deployment_type]
                ]

            # check for POSTGRES_URI_CUSTOM and REDIS_URI_CUSTOM
            secrets_ = await get_project_secrets(project.id)
            postgres_uri_custom = env_var.POSTGRES_URI_CUSTOM in secrets_
            redis_uri_custom = env_var.REDIS_URI_CUSTOM in secrets_

            async with redis.async_queue(shared_settings.HOST_QUEUE) as queue:
                await queue.enqueue(
                    job_or_func="deploy_image",
                    pipeline=None,
                    key=f"deploy_image:{str(revision.id)}",
                    retries=deploy_image.MAX_RETRIES,
                    retry_delay=30,
                    retry_backoff=True,
                    timeout=2400,  # 40 minutes
                    heartbeat=120,  # 2 minutes
                    # func params
                    tenant_id=str(project.tenant_id),
                    op="update",
                    request={
                        "service_name": service_name_,
                        "build": {
                            "image_path": image_path,
                        },
                        "revision_id": str(revision.id),
                        "labels": {
                            "ls_tenant_id": str(project.tenant_id),
                            "ls_project_id": str(project.id),
                            "deployment_type": project.metadata.get(
                                "deployment_type", "prod"
                            ),
                            "region": settings.HOST_DEPLOYMENT_REGION,
                            "env": shared_settings.DD_ENV,
                        },
                        "container_min_scale": container_spec.get("min_scale") or 1,
                        "container_max_scale": container_spec.get("max_scale")
                        or default_container_spec["max_scale"],
                        "container_cpu": container_spec.get("cpu")
                        or default_container_spec["cpu"],
                        "container_memory_mb": container_spec.get("memory_mb")
                        or default_container_spec["memory_mb"],
                    },
                    revision_id=str(revision.id),
                    wait_for_db=not postgres_uri_custom,
                    create_redis=not redis_uri_custom,
                )
            logger.info(f"Enqueued deploy job for revision ID {revision.id}")
        except Exception as e:
            logger.error(f"Error reconciling revision ID {revision.id}: {e}")
            continue


async def _reconcile_building() -> None:
    """Reconcile revisions in BUILDING status."""
    logger.debug("Reconciling revisions in BUILDING status...")

    revisions_building = await get_revisions_by_status(
        RevisionStatus.BUILDING.value.status
    )
    for revision in revisions_building:
        try:
            job_id = (
                f"saq:job:{shared_settings.HOST_QUEUE}:build_image:{str(revision.id)}"
            )
            async with redis.async_queue(shared_settings.HOST_QUEUE) as queue:
                job = await queue._get_job_by_id(job_id)

                if job is None:
                    logger.warning(
                        f"Build Job ID {job_id} not found for revision ID "
                        f"{revision.id}. Enqueuing build job..."
                    )
                    await _reconcile_awaiting_build(revision.id)
                else:
                    if job.stuck:
                        logger.warning(
                            f"Build Job ID {job_id} ({job.status}) is stuck for revision ID "
                            f"{revision.id}. This job will be aborted (swept) by SAQ."
                        )
                        # The job must be aborted (swept) before a new job can be
                        # queued. SAQ will sweep the job.

                    if job.status in [
                        SaqJobStatus.NEW,
                        SaqJobStatus.QUEUED,
                        SaqJobStatus.ACTIVE,
                    ]:
                        logger.info(
                            f"Build Job ID {job_id} ({job.status}) "
                            f"in progress for revision ID {revision.id}"
                        )
                    elif job.status == SaqJobStatus.ABORTED and job.error == "swept":
                        logger.info(
                            f"Build Job ID {job_id} ({job.status}) will be "
                            f"requeued for revision ID {revision.id}"
                        )
                        await _reconcile_awaiting_build(revision.id)
        except Exception as e:
            logger.error(f"Error reconciling revision ID {revision.id}: {e}")
            continue


async def _reconcile_deploying() -> None:
    """Reconcile revisions in DEPLOYING status."""
    logger.debug("Reconciling revisions in DEPLOYING status...")

    revisions_deploying = await get_revisions_by_status(
        RevisionStatus.DEPLOYING.value.status
    )
    for revision in revisions_deploying:
        try:
            job_id = (
                f"saq:job:{shared_settings.HOST_QUEUE}:deploy_image:{str(revision.id)}"
            )
            async with redis.async_queue(shared_settings.HOST_QUEUE) as queue:
                job = await queue._get_job_by_id(job_id)

                if job is None:
                    logger.warning(
                        f"Deploy Job ID {job_id} not found for revision ID "
                        f"{revision.id}. Enqueuing deploy job..."
                    )
                    await _reconcile_awaiting_deploy(revision.id)
                else:
                    if job.stuck:
                        logger.warning(
                            f"Deploy Job ID {job_id} ({job.status}) is stuck for revision ID "
                            f"{revision.id}. This job will be aborted (swept) by SAQ."
                        )
                        # The job must be aborted (swept) before a new job can be
                        # queued. SAQ will sweep the job.

                    if job.status in [
                        SaqJobStatus.NEW,
                        SaqJobStatus.QUEUED,
                        SaqJobStatus.ACTIVE,
                    ]:
                        logger.info(
                            f"Deploy Job ID {job_id} ({job.status}) "
                            f"in progress for revision ID {revision.id}"
                        )
                    elif job.status == SaqJobStatus.ABORTED and job.error == "swept":
                        logger.info(
                            f"Deploy Job ID {job_id} ({job.status}) will be "
                            f"requeued for revision ID {revision.id}"
                        )
                        await _reconcile_awaiting_deploy(revision.id)
        except Exception as e:
            logger.error(f"Error reconciling revision ID {revision.id}: {e}")
            continue


async def _reconcile_deleted_projects() -> None:
    """Reconcile projects in AWAITING_DELETE status."""
    logger.debug("Reconciling projects in AWAITING_DELETE status...")

    deleted_projects = await get_projects_by_status(
        ProjectStatus.AWAITING_DELETE.value.status
    )
    for project in deleted_projects:
        try:
            platform = get_project_platform(project)

            async with redis.async_queue(shared_settings.HOST_QUEUE) as queue:
                await queue.enqueue(
                    job_or_func="delete_project_resources",
                    pipeline=None,
                    key=f"delete_project_resources:{str(project.id)}",
                    retries=3,
                    retry_delay=5,
                    retry_backoff=True,
                    timeout=300,
                    # func params
                    tenant_id=str(project.tenant_id),
                    project_id=str(project.id),
                    project_name=project.name,
                    project_platform=platform.model_dump(),
                )
            logger.info(f"Enqueued delete job for project ID {str(project.id)}")
        except Exception as e:
            logger.error(f"Error reconciling project ID {str(project.id)}: {e}")
            continue


async def run(ctx: dict) -> None:
    """Reconcile deployments."""
    logger.debug("Reconciling projects...")

    # Get all projects in AWAITING_DELETE status and enqueue job to
    # delete the cloud resources.
    await _reconcile_deleted_projects()

    # Get all revisions in DEPLOYING status and requeue job for
    # revisions without tasks in the queue.
    await _reconcile_deploying()

    # Get all revisions in AWAITING_DEPLOY status and enqueue job
    # to deploy them.
    await _reconcile_awaiting_deploy()

    # Get all revisions in BUILDING status and requeue job for
    # revisions without tasks in the queue.
    await _reconcile_building()

    # Get all projects in AWAITING_DATABASE status and enqueue job
    # to create the databases.
    await _reconcile_awaiting_database()

    # Get all revisions in AWAITING_BUILD status and enqueue job
    # to build them.
    await _reconcile_awaiting_build()
