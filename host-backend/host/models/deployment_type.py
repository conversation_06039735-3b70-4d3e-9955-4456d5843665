from enum import Enum
from typing import Optional

from pydantic import BaseModel, model_validator

from host.config import settings


class DeploymentType(str, Enum):
    dev = "dev"
    dev_free = "dev_free"
    prod = "prod"


class DeploymentPlatformId(str, Enum):
    k8s = "k8s"
    aws_ecs = "aws_ecs"
    k8s_vanilla = "k8s_vanilla"
    k8s_operator = "k8s_operator"


class ImageSourceId(str, Enum):
    github = "github"
    internal_docker = "internal_docker"
    external_docker = "external_docker"


class SecretStoreId(str, Enum):
    k8s = "k8s"
    aws = "aws"


class DatabasePlatformId(str, Enum):
    gcp_cloud_sql = "gcp_cloud_sql"
    aws_rds = "aws_rds"
    k8s = "k8s"


K8S_PLATFORMS = [
    DeploymentPlatformId.k8s,
    DeploymentPlatformId.k8s_vanilla,
    DeploymentPlatformId.k8s_operator,
]


CONTAINER_SPECS = {
    DeploymentType.prod: {
        "max_scale": 10,
        "cpu": 1.0,
        "memory_mb": 2048,
    },
    DeploymentType.dev: {
        "max_scale": 1,
        "cpu": 1.0,
        "memory_mb": 1024,
    },
    DeploymentType.dev_free: {
        "max_scale": 1,
        "cpu": 1.0,
        "memory_mb": 1024,
    },
}

# ECS + Fargate only supports fractional vCPU configurations
# at 0.25 and 0.5. Otherwise, whole number vCPU must be specified
# (e.g. 1.0, 2.0).
#
# WARNING: ECS + Fargate only supports specific configurations
# for CPU and memory. See: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definition_parameters.html
CONTAINER_SPECS_AWS = {
    DeploymentType.prod: {
        "max_scale": 10,
        "cpu": 1.0,
        "memory_mb": 2048,
    },
    DeploymentType.dev: {
        "max_scale": 1,
        "cpu": 1.0,
        "memory_mb": 2048,
    },
    DeploymentType.dev_free: {
        "max_scale": 1,
        "cpu": 1.0,
        "memory_mb": 2048,
    },
}


class ProjectPlatform(BaseModel):
    """Helper class that contains project platform metadata."""

    image_source: ImageSourceId
    deployment_platform: DeploymentPlatformId
    k8s_cluster: Optional[str]
    k8s_namespace: Optional[str]
    region: str = settings.HOST_DEPLOYMENT_REGION
    database_platform: DatabasePlatformId
    public: Optional[bool] = False

    # AWS
    aws_account_id: Optional[str] = None
    aws_external_id: Optional[str] = None

    @property
    def secret_store(self) -> SecretStoreId:
        if self.deployment_platform in K8S_PLATFORMS:
            return SecretStoreId.k8s
        elif self.deployment_platform == DeploymentPlatformId.aws_ecs:
            return SecretStoreId.aws
        else:
            raise ValueError(f"Invalid deployment platform: {self.deployment_platform}")

    @model_validator(mode="before")
    def validate_aws_ids(cls, values):
        aws_account_id = values.get("aws_account_id")
        aws_external_id = values.get("aws_external_id")

        if aws_account_id and not aws_external_id:
            raise ValueError(
                "If aws_account_id is provided, aws_external_id must also be provided."
            )

        return values
