import json
import logging
from datetime import datetime, timezone
from typing import Literal, Optional

import google.api_core.exceptions
import kubernetes_asyncio.client
from fastapi import HTTPException
from google.cloud.logging_v2.services.logging_service_v2 import (
    LoggingServiceV2AsyncClient,
)
from google.cloud.logging_v2.types import ListLogEntriesRequest
from google.logging.type import log_severity_pb2

from host.config import settings
from host.platforms.base.logging import LOG_LEVEL, STAT_LOG_LINES

logger = logging.getLogger(__name__)


def _add_filters(
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    level: Optional[LOG_LEVEL] = None,
    filter: Optional[str] = None,
) -> str:
    """Helper function for adding time and level filters to the main
    filter string.

    Returns the final filter string.
    """
    expressions = []

    if start_time:
        expressions.append(f'timestamp >= "{start_time.isoformat()}"')
    if end_time:
        expressions.append(f'timestamp <= "{end_time.isoformat()}"')
    if level:
        expressions.append(f'severity >= "{level}"')
    if filter:
        expressions.append(filter)
    return " AND ".join(expressions)


async def list_logs(
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    order: Literal["asc", "desc"] = "asc",
    limit: int = 50,
    offset: Optional[str] = None,
    filter: Optional[str] = None,
    level: Optional[LOG_LEVEL] = None,
) -> tuple[list[dict], Optional[str]]:
    """List the logs.

    The function returns a tuple. The first element is a list of log entries
    (dict). The second element is an optional offset string value, used by the
    caller to retrieve the next page of log entries.
    """
    entries = []
    next_page_token = None
    client = LoggingServiceV2AsyncClient()

    try:
        request = ListLogEntriesRequest(
            resource_names=[f"projects/{settings.GCP_PROJECT_ID}"],
            filter=_add_filters(start_time, end_time, level, filter),
            order_by="timestamp asc" if order == "asc" else "timestamp desc",
            page_size=limit,
        )
        if offset:
            request.page_token = offset

        pager = await client.list_log_entries(request=request)

        async for page in pager.pages:
            next_page_token = page.next_page_token
            for entry in page.entries:
                log_line = ""

                if entry.text_payload:
                    log_line = entry.text_payload
                elif entry.json_payload:
                    log_line = entry.json_payload.get("event", "")
                    exception = entry.json_payload.get("exception", "")
                    if exception:
                        log_line = f"{log_line}\n{exception}"

                if log_line:
                    timestamp = int(entry.timestamp.timestamp() * 1000)
                    entries.append(
                        {
                            "id": entry.insert_id,
                            "timestamp": timestamp,
                            "message": log_line,
                            "level": str(
                                log_severity_pb2.LogSeverity.Name(entry.severity)
                            ).upper(),
                        }
                    )

            if len(entries) >= limit:
                break

        return (entries, next_page_token)
    except google.api_core.exceptions.InvalidArgument:
        raise HTTPException(status_code=400, detail="Invalid or expired offset")
    except HTTPException as e:
        if e.status_code == 408:
            raise e
        else:
            logger.error(f"Unexpected error while listing logs: {e}")
            raise ValueError("Unexpected error while listing logs")


async def list_k8s_logs(
    core_v1_api: kubernetes_asyncio.client.CoreV1Api,
    pod_name: str,
    namespace: str,
    start_time: datetime,
    end_time: Optional[datetime] = None,
    level: Optional[LOG_LEVEL] = None,
) -> list[dict]:
    """List K8s logs for a single pod.

    This function also filters the list of log lines based
    on the end_time and level parameter.
    """
    now_utc = datetime.now(timezone.utc)
    delta = now_utc - start_time
    since_seconds = max(0, int(delta.total_seconds()))

    raw_log_text = await core_v1_api.read_namespaced_pod_log(
        name=pod_name,
        namespace=namespace,
        timestamps=True,
        since_seconds=since_seconds,
    )

    log_lines_to_keep: list[dict] = []

    for log_line in str(raw_log_text).splitlines():
        # Each log line has this format:
        # 2025-05-28T18:50:20.785857402Z {"event":"Using langgraph_runtime_postgres","thread_name":"MainThread","logger":"langgraph_runtime","level":"info","api_variant":"cloud","api_revision":"4559315","timestamp":"2025-05-28T18:50:20.785314Z"}

        # skip empty lines
        if not log_line:
            continue

        # parse raw K8s log line
        k8s_ts_str, _, app_log_line_str = log_line.partition(" ")
        k8s_ts = datetime.fromisoformat(k8s_ts_str)

        try:
            app_log_line_dict = dict(json.loads(app_log_line_str))
        except json.JSONDecodeError:
            app_log_line_dict = {
                "event": app_log_line_str,
                "level": "INFO",
                "timestamp": k8s_ts_str,
            }

        # apply end_date filter
        if end_time and k8s_ts > end_time:
            # stop early, lines are already chronological
            break

        # apply level filter
        if level:
            log_line_level = str(app_log_line_dict.get("level", "INFO")).upper()
            if level == "DEBUG":
                if log_line_level not in [
                    "DEBUG",
                    "INFO",
                    "WARNING",
                    "ERROR",
                    "CRITICAL",
                ]:
                    continue
            elif level == "INFO":
                if log_line_level not in ["INFO", "WARNING", "ERROR", "CRITICAL"]:
                    continue
            elif level == "WARNING":
                if log_line_level not in ["WARNING", "ERROR", "CRITICAL"]:
                    continue
            elif level == "ERROR":
                if log_line_level not in ["ERROR", "CRITICAL"]:
                    continue
            elif level == "CRITICAL":
                if log_line_level not in ["CRITICAL"]:
                    continue

        # apply stat log line filter
        if app_log_line_dict.get("event") in STAT_LOG_LINES:
            continue

        # keep app log line if it passes all filters
        log_lines_to_keep.append(app_log_line_dict)

    return log_lines_to_keep
