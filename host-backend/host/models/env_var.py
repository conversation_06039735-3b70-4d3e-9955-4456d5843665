import enum
from typing import List
from uuid import UUID

from pydantic import BaseModel

import host.config
from host.models.deploy import (
    AwsEnvVar,
    KnativeEnvVar,
    KnativeEnvVarSource,
    KnativeSecretKeyRef,
)
from host.models.deployment_type import (
    DeploymentPlatformId,
    SecretStoreId,
)
from host.models.host_metadata_crud import (
    ProjectMetadata,
    RevisionMetadata,
    get_project_platform,
    service_name,
)
from host.platforms.aws.client import AwsPlatformArgs
from host.platforms.aws.secrets import AwsSecrets
from host.platforms.base.secrets import Secret, get_project_level_secret_base_name


class LangGraphEnvVarType(str, enum.Enum):
    default = "default"
    secret = "secret"


class LangGraphEnvVar(BaseModel):
    name: str
    value: str
    type: LangGraphEnvVarType = LangGraphEnvVarType.default


# for displaying secrets to users
_HIDDEN_SECRET_CONSTANT = "*******"

# Non Secret Basic Env Vars
_LANGCHAIN_TRACING_V2 = "LANGCHAIN_TRACING_V2"
_LANGSMITH_TRACING = "LANGSMITH_TRACING"
_LANGSMITH_TRACING_V2 = "LANGSMITH_TRACING_V2"
_LANGCHAIN_ENDPOINT = "LANGCHAIN_ENDPOINT"
_LANGSMITH_ENDPOINT = "LANGSMITH_ENDPOINT"
_LANGCHAIN_PROJECT = "LANGCHAIN_PROJECT"
_LANGSMITH_PROJECT = "LANGSMITH_PROJECT"

# Non Secret Hosting Env Vars
_GIT_REPO_ENV_VAR = "HOSTED_LANGSERVE_GIT_REPO"
_GIT_REPO_PATH_ENV_VAR = "HOSTED_LANGSERVE_GIT_REPO_PATH"

# DEPRECATED: use _GIT_REF_ENV_VAR instead
# kept around so it can be filtered out from being shown to users
_GIT_COMMIT_ENV_VAR = "HOSTED_LANGSERVE_GIT_COMMIT"

_IS_HOSTED_LANGSERVE = "HOSTED_LANGSERVE_ENABLED"
_GIT_REF_ENV_VAR = "HOSTED_LANGSERVE_GIT_REF"
_GIT_REF_SHA = "HOSTED_LANGSERVE_GIT_REF_SHA"

# Secret Env Vars
_LANGCHAIN_API_KEY_ENV_VAR = "LANGCHAIN_API_KEY"
_LANGSMITH_API_KEY = "LANGSMITH_API_KEY"
_LANGGRAPH_CLOUD_LICENSE_KEY = (
    "LANGGRAPH_CLOUD_LICENSE_KEY"  # for self-hosted host-backend
)
POSTGRES_URI = "POSTGRES_URI"
# If POSTGRES_URI_CUSTOM is set, then POSTGRES_URI is automatically set to
# the same value.
POSTGRES_URI_CUSTOM = "POSTGRES_URI_CUSTOM"
POSTGRES_PASSWORD = "POSTGRES_PASSWORD"
_DATABASE_URI = "DATABASE_URI"

# LangGraph API auth configuration
_LANGGRAPH_AUTH_TYPE = "LANGGRAPH_AUTH_TYPE"
_LANGSMITH_AUTH_ENDPOINT = "LANGSMITH_AUTH_ENDPOINT"
_LANGSMITH_TENANT_ID = "LANGSMITH_TENANT_ID"
_LANGSMITH_AUTH_VERIFY_TENANT_ID = "LANGSMITH_AUTH_VERIFY_TENANT_ID"

# Logging configuration
_LOG_JSON = "LOG_JSON"

# Hosted LangSmith Env Vars
_LANGSMITH_HOST_PROJECT_ID = "LANGSMITH_HOST_PROJECT_ID"
_LANGSMITH_HOST_PROJECT_NAME = "LANGSMITH_HOST_PROJECT_NAME"
_LANGSMITH_HOST_REVISION_ID = "LANGSMITH_HOST_REVISION_ID"

# If LANGSMITH_RUNS_ENDPOINTS is set, then LANGCHAIN_ENDPOINT will not be set
# since the LangSmith SDK does not allow setting both at the same time. This
# functionality is not publicized. The value of LANGSMITH_RUNS_ENDPOINTS is a
# JSON object of LangChain API endpoint (key) to API key (value).
#
# Example: {"https://api.smith.langchain.com": "api_key"}
_LANGSMITH_RUNS_ENDPOINTS = "LANGSMITH_RUNS_ENDPOINTS"

REDIS_URI = "REDIS_URI"
# If REDIS_URI_CUSTOM is set, then REDIS_URI is automatically set to
# the same value.
REDIS_URI_CUSTOM = "REDIS_URI_CUSTOM"

# JS specific environment variables
_LANGCHAIN_CALLBACKS_BACKGROUND = "LANGCHAIN_CALLBACKS_BACKGROUND"

# ddtrace (Datadog) environment variables
_DD_API_KEY = "DD_API_KEY"
_DD_TRACE_PSYCOPG_ENABLED = "DD_TRACE_PSYCOPG_ENABLED"
_DD_TRACE_REDIS_ENABLED = "DD_TRACE_REDIS_ENABLED"

# OS specific environment variables
_PATH = "PATH"
_PORT = "PORT"
_MOUNT_PREFIX = "MOUNT_PREFIX"

LANGCHAIN_RESERVED_ENV_VARS = [
    _LANGCHAIN_TRACING_V2,
    _LANGSMITH_TRACING_V2,
    _LANGCHAIN_ENDPOINT,
    _LANGSMITH_ENDPOINT,
    _LANGCHAIN_PROJECT,
    _LANGSMITH_PROJECT,
    _GIT_REPO_ENV_VAR,
    _GIT_REPO_PATH_ENV_VAR,
    _GIT_COMMIT_ENV_VAR,
    _IS_HOSTED_LANGSERVE,
    _LANGCHAIN_API_KEY_ENV_VAR,
    _LANGSMITH_API_KEY,
    POSTGRES_URI,
    POSTGRES_PASSWORD,
    _DATABASE_URI,
    _GIT_REF_ENV_VAR,
    _GIT_REF_SHA,
    _LANGGRAPH_AUTH_TYPE,
    _LANGSMITH_AUTH_ENDPOINT,
    _LANGSMITH_TENANT_ID,
    _LANGSMITH_AUTH_VERIFY_TENANT_ID,
    _LANGSMITH_HOST_PROJECT_ID,
    _LANGSMITH_HOST_PROJECT_NAME,
    _LANGSMITH_HOST_REVISION_ID,
    _LOG_JSON,
    REDIS_URI,
    _LANGCHAIN_CALLBACKS_BACKGROUND,
    _DD_TRACE_PSYCOPG_ENABLED,
    _DD_TRACE_REDIS_ENABLED,
    _LANGGRAPH_CLOUD_LICENSE_KEY,
    _PATH,
    _PORT,
    _MOUNT_PREFIX,
]

LANGCHAIN_RESERVED_SECRETS = [
    _LANGCHAIN_API_KEY_ENV_VAR,
    _LANGSMITH_API_KEY,
    POSTGRES_URI,
    POSTGRES_PASSWORD,
    REDIS_URI,
    _LANGGRAPH_CLOUD_LICENSE_KEY,
]

# Environment variables that can be set for Self-Hosted Data Plane and
# Self-Hosted Control Plane deployments, but not Cloud SaaS deployments.
ALLOWED_SELF_HOSTED_ENV_VARS = [
    POSTGRES_URI_CUSTOM,
    REDIS_URI_CUSTOM,
    _LANGSMITH_TRACING,
]


async def get_all_deployable_env_vars(
    tenant_id: UUID,
    project_metadata: ProjectMetadata,
    revision_metadata: RevisionMetadata,
    requested_env_vars: List[LangGraphEnvVar],
) -> list[KnativeEnvVar] | list[AwsEnvVar]:
    platform = get_project_platform(project_metadata)
    settable_env_vars = [
        env_var
        for env_var in requested_env_vars
        if env_var.name not in LANGCHAIN_RESERVED_ENV_VARS
    ]
    settable_env_vars_names = {env_var.name for env_var in settable_env_vars}

    # if LANGSMITH_RUNS_ENDPOINTS is not set, then set LANGCHAIN_ENDPOINT
    if _LANGSMITH_RUNS_ENDPOINTS not in settable_env_vars_names:
        settable_env_vars.append(
            LangGraphEnvVar(
                name=_LANGCHAIN_ENDPOINT,
                value=host.config.settings.HOST_LANGCHAIN_API_ENDPOINT,
            ),
        )
        settable_env_vars.append(
            LangGraphEnvVar(
                name=_LANGSMITH_ENDPOINT,
                value=host.config.settings.HOST_LANGCHAIN_API_ENDPOINT,
            ),
        )

    if _LANGSMITH_TRACING not in settable_env_vars_names:
        settable_env_vars.append(
            LangGraphEnvVar(name=_LANGSMITH_TRACING, value="true"),
        )

    # GitHub repo environment variables
    if project_metadata.repo_url:
        settable_env_vars = settable_env_vars + [
            LangGraphEnvVar(name=_GIT_REPO_ENV_VAR, value=project_metadata.repo_url),
            LangGraphEnvVar(
                name=_GIT_REPO_PATH_ENV_VAR,
                value=revision_metadata.repo_path,
            ),
            LangGraphEnvVar(
                # repo commit actually refers to a git ref, not a commit, unfortunately
                name=_GIT_REF_ENV_VAR,
                value=revision_metadata.repo_commit,
            ),
            LangGraphEnvVar(
                name=_GIT_REF_SHA,
                value=revision_metadata.metadata.get("repo_commit_sha") or "",
            ),
        ]

    # ddtrace (Datadog) environment variables
    if _DD_API_KEY in settable_env_vars_names:
        # We don't want to expose the implementation of Postgres or Redis, so
        # we forcefully disable tracing for them.
        settable_env_vars.append(
            LangGraphEnvVar(
                name=_DD_TRACE_PSYCOPG_ENABLED,
                value="false",
            ),
        )
        settable_env_vars.append(
            LangGraphEnvVar(
                name=_DD_TRACE_REDIS_ENABLED,
                value="false",
            ),
        )

    if host.config.settings.IS_SELF_HOSTED and not project_metadata.remote_reconciled:
        settable_env_vars = settable_env_vars + [
            LangGraphEnvVar(
                name=_LANGGRAPH_CLOUD_LICENSE_KEY,
                value=_HIDDEN_SECRET_CONSTANT,
                type=LangGraphEnvVarType.secret,
            ),
        ]

    all_revision_env_vars = settable_env_vars + [
        LangGraphEnvVar(name=_LOG_JSON, value="true"),
        LangGraphEnvVar(name=_LANGCHAIN_PROJECT, value=project_metadata.name),
        LangGraphEnvVar(name=_LANGSMITH_PROJECT, value=project_metadata.name),
        LangGraphEnvVar(name=_IS_HOSTED_LANGSERVE, value="true"),
        # We set the value to **** since the actual value doesn't matter
        LangGraphEnvVar(
            name=_LANGCHAIN_API_KEY_ENV_VAR,
            value=_HIDDEN_SECRET_CONSTANT,
            type=LangGraphEnvVarType.secret,
        ),
        LangGraphEnvVar(
            name=_LANGGRAPH_AUTH_TYPE,
            value="noop"
            if (
                platform.deployment_platform == DeploymentPlatformId.aws_ecs
                and not platform.public
            )
            or host.config.settings.LANGCHAIN_ENV == "local_dev"
            else "langsmith",
        ),
        LangGraphEnvVar(
            name=_LANGSMITH_AUTH_ENDPOINT,
            value=host.config.settings.HOST_LANGCHAIN_API_ENDPOINT,
        ),
        LangGraphEnvVar(
            name=_LANGSMITH_TENANT_ID,
            value=str(tenant_id),
        ),
        LangGraphEnvVar(
            name=_LANGSMITH_AUTH_VERIFY_TENANT_ID,
            # If shareable is False (default), then tenant ID is verified
            # during auth. If shareable is True, then tenant ID is not verified.
            value=str(not project_metadata.metadata.get("shareable", False)),
        ),
        LangGraphEnvVar(
            name=_LANGSMITH_HOST_PROJECT_ID,
            value=str(project_metadata.id),
        ),
        LangGraphEnvVar(
            name=_LANGSMITH_HOST_PROJECT_NAME,
            value=str(project_metadata.name),
        ),
        LangGraphEnvVar(
            name=_LANGSMITH_HOST_REVISION_ID,
            value=str(revision_metadata.id),
        ),
        LangGraphEnvVar(
            name=POSTGRES_URI,
            value=_HIDDEN_SECRET_CONSTANT,
            type=LangGraphEnvVarType.secret,
        ),
        LangGraphEnvVar(
            name=REDIS_URI,
            value=_HIDDEN_SECRET_CONSTANT,
            type=LangGraphEnvVarType.secret,
        ),
        LangGraphEnvVar(
            name=_LANGCHAIN_CALLBACKS_BACKGROUND,
            value="true",
        ),
    ]

    service_name_str = service_name(project_metadata.tenant_id, project_metadata.name)
    if platform.secret_store == SecretStoreId.aws:
        secret = await AwsSecrets.get_secret_metadata(
            service_name_str, AwsPlatformArgs.from_platform(platform)
        )
        secret_arn = secret["ARN"]
        return [_to_aws_env_var(ev, secret_arn) for ev in all_revision_env_vars]
    else:
        return [
            _to_knative_env_var(ev, service_name_str) for ev in all_revision_env_vars
        ]


def _to_knative_env_var(
    env_var: LangGraphEnvVar, service_name_str: str
) -> KnativeEnvVar:
    if env_var.type == LangGraphEnvVarType.default:
        return KnativeEnvVar(name=env_var.name, value=env_var.value)
    elif env_var.type == LangGraphEnvVarType.secret:
        return KnativeEnvVar(
            name=env_var.name,
            valueFrom=KnativeEnvVarSource(
                secretKeyRef=KnativeSecretKeyRef(
                    name=get_project_level_secret_base_name(service_name_str),
                    key=env_var.name,
                )
            ),
        )
    else:
        raise AssertionError(f"Encountered unsupported env var: {env_var}")


def _to_aws_env_var(env_var: LangGraphEnvVar, secret_arn: str) -> AwsEnvVar:
    if env_var.type == LangGraphEnvVarType.default:
        return AwsEnvVar(name=env_var.name, value=env_var.value)
    elif env_var.type == LangGraphEnvVarType.secret:
        return AwsEnvVar(
            name=env_var.name,
            value_from=f"{secret_arn}:{env_var.name}::",
        )
    else:
        raise AssertionError(f"Encountered unsupported env var: {env_var}")


def get_secrets_from_incoming_env_vars(
    env_vars: list[LangGraphEnvVar],
) -> list[Secret]:
    """Convert list of LangGraphEnvVars to list of Secrets.

    All incoming environment variables, including non-secrets, are returned
    as Secrets.
    """
    return [Secret(name=env_var.name, value=env_var.value) for env_var in env_vars]
