import asyncio
from datetime import datetime
from typing import Literal, Optional
from uuid import UUID

from app.api.auth.schemas import AuthInfo
from fastapi import HTTPException

from host.api import schemas
from host.config import settings
from host.models import host_metadata_crud
from host.models.deployment_type import K8S_PLATFORMS, DeploymentPlatformId
from host.models.host_metadata_crud import (
    RevisionMetadata,
    RevisionStatus,
    StateTransitionError,
    get_project_platform,
    service_name,
)
from host.platforms.aws.client import AwsPlatformArgs
from host.platforms.aws.logging import AwsLogging
from host.platforms.base.logging import LOG_LEVEL
from host.platforms.base.schema import PlatformRevision, ResourceId
from host.platforms.k8s.client import K8sPlatformArgs
from host.platforms.k8s.logging import K8sLogging
from host.platforms.k8s_operator.client import K8sOperatorPlatformArgs
from host.platforms.k8s_operator.logging import K8sOperatorLogging
from host.platforms.k8s_vanilla.client import K8sVanillaPlatformArgs
from host.platforms.k8s_vanilla.logging import K8sVanillaLogging


class RevisionExtended(host_metadata_crud.RevisionBaseMetadata):
    resource: PlatformRevision | None = None


async def get_platform_revision(
    revision_metadata: RevisionMetadata,
) -> PlatformRevision:
    """This function returns the PlatformRevision for a revision without
    accessing cloud resources (e.g. GCP) or deployment infrastructure
    (e.g. K8s).

    This function can be used where latency/performance of an operation
    is critical (e.g. listing all revisions for a project).
    """
    id = {"type": "revisions", "name": ""}
    if revision_metadata.knative:
        id = ResourceId(**revision_metadata.knative)
    return PlatformRevision(
        id=id,
        env_vars=None,  # retrieving env vars is not supported
        hosted_langserve_revision_id=revision_metadata.id,
    )


async def get_revision(
    revision_id: UUID, tenant_id: UUID | None = None
) -> RevisionExtended:
    # get revision platform metadata
    revision = await host_metadata_crud.get_revision_metadata(revision_id, tenant_id)

    platform_revision: PlatformRevision | None = None
    if revision.knative:
        platform_revision = await get_platform_revision(revision)

    return RevisionExtended(**revision.model_dump(), resource=platform_revision)


async def list_revisions(
    params: schemas.ListRevisionsQueryParams,
    project_id: UUID | None = None,
    tenant_id: UUID | None = None,
) -> tuple[list[RevisionExtended], int]:
    # query for revisions
    revisions_to_return, total_count = await host_metadata_crud.list_revisions_metadata(
        params.limit,
        params.offset,
        project_id,
        tenant_id,
        params.status,
        params.remote_reconciled,
    )

    # construct retrieval tasks to run in parallel
    tasks = []
    for revision in revisions_to_return:
        tasks.append(get_platform_revision(revision))

    platform_revisions = await asyncio.gather(*tasks)

    return [
        RevisionExtended(**revision.model_dump(), resource=platform_revision)
        for revision, platform_revision in zip(revisions_to_return, platform_revisions)
    ], params.offset + total_count


async def deploy_revision(auth: AuthInfo, project_id: UUID, revision_id: UUID) -> None:
    revision = await host_metadata_crud.get_latest_revision_metadata(project_id)

    if revision is None:
        raise HTTPException(
            status_code=404,
            detail="Revision not found.",
        )

    if revision_id != revision.id:
        raise HTTPException(
            status_code=400,
            detail="Revision must be the latest revision to be redeployed.",
        )

    if revision.status != host_metadata_crud.RevisionStatus.DEPLOYED.value.status:
        raise HTTPException(
            status_code=400,
            detail="Revision must be in DEPLOYED state to be redeployed.",
        )

    await host_metadata_crud.set_revision_status_awaiting_deploy(
        revision_id, "Waiting for deploy", auth.tenant_id
    )


async def interrupt_revision(auth: AuthInfo, revision_id: UUID) -> None:
    # get the revision to ensure it exists
    await host_metadata_crud.get_revision_metadata(revision_id, auth.tenant_id)

    # set the revision status to interrupted
    await host_metadata_crud.set_revision_status_interrupted(auth, revision_id)

    # TODO: in the future, actually interrupt the deployment process.
    # For now, we just rely on our state transitions to ensure that the deployment process
    # can't continue unless the revision is actively deploying


async def list_build_logs(
    auth: AuthInfo,
    revision_id: UUID,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    order: Literal["asc", "desc"] = "asc",
    limit: int = 50,
    offset: Optional[str] = None,
) -> schemas.LogsResponse:
    revision_metadata = await host_metadata_crud.get_revision_metadata(
        revision_id, auth.tenant_id
    )
    project_metadata = await host_metadata_crud.get_project_metadata(
        revision_metadata.project_id, auth.tenant_id
    )
    platform = get_project_platform(project_metadata)
    full_service_name = service_name(auth.tenant_id, project_metadata.name)

    if settings.IS_SELF_HOSTED or project_metadata.remote_reconciled:
        raise HTTPException(
            status_code=400,
            detail="Build logs not supported for self-hosted LangGraph Control Plane.",
        )
    elif platform.deployment_platform in K8S_PLATFORMS:
        if not revision_metadata.gcp_build_name:
            raise HTTPException(status_code=404, detail="Build logs not found")

        k8s_platform_args = K8sPlatformArgs.from_platform(platform)
        build_id = revision_metadata.gcp_build_name.split("/")[-1]

        k8s_platform_args = K8sPlatformArgs.from_platform(platform)
        logs, next_offset = await K8sLogging.list_build_logs(
            platform_args=k8s_platform_args,
            service_name=full_service_name,
            build_id=build_id,
            start_time=start_time or revision_metadata.created_at,
            end_time=end_time,
            order=order,
            limit=limit,
            offset=offset,
        )
    elif platform.deployment_platform == DeploymentPlatformId.aws_ecs:
        raise HTTPException(
            status_code=400, detail="Build logs not supported for AWS deployments"
        )

    return schemas.LogsResponse(logs=logs, next_offset=next_offset)


async def list_deploy_logs(
    auth: AuthInfo,
    project_id: UUID,
    revision_id: Optional[UUID],
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    order: Literal["asc", "desc"] = "asc",
    limit: int = 50,
    offset: Optional[str] = None,
    query: Optional[str] = None,
    level: Optional[LOG_LEVEL] = None,
) -> schemas.LogsResponse:
    project_metadata = await host_metadata_crud.get_project_metadata(
        project_id, auth.tenant_id
    )
    platform = get_project_platform(project_metadata)
    full_service_name = service_name(auth.tenant_id, project_metadata.name)

    revision_metadata = (
        await host_metadata_crud.get_revision_metadata(revision_id, auth.tenant_id)
        if revision_id
        else None
    )

    # Determine start time based on if logs are being retrieved for a specific
    # revision or the entire project (all revisions).
    if start_time is None:
        if revision_metadata:
            start_time = revision_metadata.created_at
        else:
            start_time = project_metadata.created_at

    if project_metadata.remote_reconciled:
        raise HTTPException(
            status_code=400,
            detail="Server logs are not supported for Self-Hosted Data Plane deployments.",
        )
    elif platform.deployment_platform == DeploymentPlatformId.k8s:
        k8s_platform_args = K8sPlatformArgs.from_platform(platform)
        logs, next_offset = await K8sLogging.list_deployment_logs(
            platform_args=k8s_platform_args,
            service_name=full_service_name,
            revision_id=str(revision_id) if revision_id else None,
            start_time=start_time,
            end_time=end_time,
            order=order,
            limit=limit,
            offset=offset,
            query=query,
            level=level,
        )
    elif platform.deployment_platform == DeploymentPlatformId.aws_ecs:
        aws_platform_args = AwsPlatformArgs.from_platform(platform)
        logs, next_offset = await AwsLogging.list_deployment_logs(
            platform_args=aws_platform_args,
            service_name=full_service_name,
            revision_id=str(revision_id) if revision_id else None,
            start_time=start_time,
            end_time=end_time,
            order=order,
            limit=limit,
            offset=offset,
            query=query,
            level=level,
        )
    elif platform.deployment_platform == DeploymentPlatformId.k8s_vanilla:
        k8s_vanilla_platform_args = K8sVanillaPlatformArgs.from_platform(platform)
        logs, next_offset = await K8sVanillaLogging.list_deployment_logs(
            platform_args=k8s_vanilla_platform_args,
            service_name=full_service_name,
            revision_id=str(revision_id) if revision_id else None,
            start_time=start_time,
            end_time=end_time,
            order=order,
            limit=limit,
            offset=offset,
            query=query,
            level=level,
        )
    elif platform.deployment_platform == DeploymentPlatformId.k8s_operator:
        k8s_operator_platform_args = K8sOperatorPlatformArgs.from_platform(platform)
        logs, next_offset = await K8sOperatorLogging.list_deployment_logs(
            platform_args=k8s_operator_platform_args,
            service_name=full_service_name,
            revision_id=str(revision_id) if revision_id else None,
            start_time=start_time,
            end_time=end_time,
            order=order,
            limit=limit,
            offset=offset,
            query=query,
            level=level,
        )

    return schemas.LogsResponse(logs=logs, next_offset=next_offset)


async def set_revision_status(
    revision_id: UUID,
    status: str,
    status_message: str | None = None,
    gcp_build_name: str | None = None,
    resource_id: ResourceId | None = None,
    tenant_id: UUID | None = None,
) -> RevisionExtended:
    """Update status of a revision.

    This method is used for managing the deployment (state machine) of a revision.
    """
    try:
        if status == RevisionStatus.BUILDING.value.status:
            if gcp_build_name is None:
                raise HTTPException(
                    status_code=400,
                    detail="GCP build name must be provided when setting revision status to BUILDING.",
                )
            revision_metadata = await host_metadata_crud.set_revision_status_building(
                revision_id, gcp_build_name, tenant_id
            )
        elif status == RevisionStatus.AWAITING_DEPLOY.value.status:
            revision_metadata = (
                await host_metadata_crud.set_revision_status_awaiting_deploy(
                    revision_id, status_message, tenant_id
                )
            )
        elif status == RevisionStatus.DEPLOYING.value.status:
            revision_metadata = await host_metadata_crud.set_revision_status_deploying(
                revision_id, status_message, tenant_id
            )
        elif status == RevisionStatus.DEPLOYED.value.status:
            revision_metadata = await host_metadata_crud.set_revision_status_deployed(
                revision_id, resource_id, tenant_id
            )
        elif status == RevisionStatus.BUILD_FAILED.value.status:
            if status_message is None:
                raise HTTPException(
                    status_code=400,
                    detail="Error message must be provided when setting revision status to BUILD_FAILED.",
                )
            revision_metadata = (
                await host_metadata_crud.set_revision_status_build_failed(
                    revision_id, status_message, tenant_id
                )
            )
        elif status == RevisionStatus.DEPLOY_FAILED.value.status:
            if status_message is None:
                raise HTTPException(
                    status_code=400,
                    detail="Error message must be provided when setting revision status to DEPLOY_FAILED.",
                )
            revision_metadata = (
                await host_metadata_crud.set_revision_status_deploy_failed(
                    revision_id, status_message, tenant_id
                )
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported status: Cannot update revision to status '{status}'",
            )
    except StateTransitionError as e:
        raise HTTPException(
            status_code=400,
            detail=f"State transition error: Cannot update revision status from '{e.old_status}' to '{e.new_status}'",
        )

    return RevisionExtended(
        **revision_metadata.model_dump(),
        resource=None,
    )
