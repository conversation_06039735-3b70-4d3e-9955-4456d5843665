import enum
import re
from base64 import urlsafe_b64encode
from datetime import datetime, timezone
from hashlib import sha256
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid5

import asyncpg
from app.api.auth.schemas import AuthInfo
from app.models import tracer_sessions
from app.models.api_keys.crud import (
    create_api_key_within_existing_transaction,
    delete_api_key_within_existing_transaction,
)
from app.models.service_accounts.crud import (
    delete_service_account_within_existing_transaction,
)
from app.schemas import APIKeyCreateRequest, TracerSessionCreate
from cryptography.fernet import Fernet
from fastapi import HTTPException
from lc_database.database import asyncpg_conn, kwargs_to_pgpos
from pydantic import BaseModel

from host.api.schemas import ListProjectsQueryParams
from host.config import settings
from host.models.deployment_type import (
    DatabasePlatformId,
    DeploymentPlatformId,
    DeploymentType,
    ImageSourceId,
    ProjectPlatform,
)
from host.platforms.base.schema import ResourceId

fernet = Fernet(urlsafe_b64encode(sha256(settings.API_KEY_SALT.encode()).digest()[:32]))


class RevisionBaseMetadata(BaseModel):
    id: UUID
    project_id: UUID
    created_at: datetime
    updated_at: datetime
    repo_path: str | None = None
    repo_commit: str | None = None
    status: str | None = None
    status_message: str | None = None
    gcp_build_name: str | None = None
    metadata: Dict[str, Any]
    image_path: str | None = None

    # TODO: Maintain backward compatibility with existing
    # data plane listeners. Eventually, migrate data plane
    # listeners to use separate schema.
    container_spec: dict | None = None


class RevisionMetadata(RevisionBaseMetadata):
    knative: dict[str, Any] | None = None


class CreateProjectMetadataRequest(BaseModel):
    name: str
    lc_hosted: bool = True
    image_path: str | None = None
    repo_url: str | None = None
    repo_path: str | None = None
    repo_commit: str | None = None
    repo_commit_sha: str | None = None
    host_integration_id: UUID | None = None
    build_on_push: bool = False
    readme_markdown: str | None = None
    remote_reconciled: bool = False
    secrets: dict[str, str]
    container_spec: dict

    # saved to metadata column
    deployment_type: DeploymentType = DeploymentType.prod
    platform: ProjectPlatform
    shareable: bool = False
    created_by: dict
    database_platform: DatabasePlatformId

    def __post_init__(self) -> None:
        if self.image_path is None and self.repo_url is None:
            raise ValueError("Either image_path or repo_url must be provided")


class ProjectMetadata(BaseModel):
    id: UUID
    tool_name: str | None = None
    display_name: str | None = None
    description: str | None = None
    example_input: dict | None = None
    tenant_id: UUID
    created_at: datetime
    updated_at: datetime
    name: str
    lc_hosted: bool
    repo_url: str | None = None
    repo_branch: str | None = None
    tracer_session_id: UUID | None = None
    api_key_id: UUID | None = None
    build_on_push: bool
    input_json_schemas: dict | None = None
    output_json_schemas: dict | None = None
    status: str | None = None
    custom_url: str | None = None
    remote_reconciled: bool
    container_spec: dict

    # This field is optional for backwards compatibility
    # It should always exist for new projects
    host_integration_id: UUID | None = None
    metadata: Dict[str, Any]


class PlaygroundMetadata(BaseModel):
    name: str
    url: str


class ProjectMetadataCreateResponse(BaseModel):
    project_metadata: ProjectMetadata
    revision_metadata: RevisionMetadata | None


class ProjectMetadataUpdateResponse(BaseModel):
    project_metadata: ProjectMetadata
    revision_metadata: RevisionMetadata


#######################################################################
#              PUBLIC METHODS FOR PROJECT METADATA                    #
#######################################################################


def service_name(tenant_id: UUID, name: str) -> str:
    # Knative service name has to start with a letter and we want it to be unique cross-tenant.
    # We also want to limit the length of the name to 64 characters.
    #
    # Cloud Run service names can only contain lowecase letters, numbers,
    # and hyphens.
    return f"{name[:27]}-{uuid5(tenant_id, name).hex}".lower()


def get_project_platform(project_metadata: ProjectMetadata) -> ProjectPlatform:
    """Get platform metadata from project metadata."""
    return get_project_platform_from_metadata(project_metadata.metadata)


def get_project_platform_from_metadata(metadata: Dict[str, Any]) -> ProjectPlatform:
    """Get platform metadata from project metadata."""
    deployment_platform = DeploymentPlatformId[metadata["platform"]]

    if deployment_platform == DeploymentPlatformId.aws_ecs:
        database_platform = DatabasePlatformId.aws_rds
    else:
        database_platform = DatabasePlatformId[
            metadata.get("database_platform", "gcp_cloud_sql")
        ]

    return ProjectPlatform(
        image_source=ImageSourceId[metadata.get("image_source", "github")],
        deployment_platform=deployment_platform,
        k8s_cluster=metadata.get("k8s_cluster"),
        k8s_namespace=metadata.get("k8s_namespace"),
        region=metadata.get("region", settings.HOST_DEPLOYMENT_REGION),
        aws_account_id=metadata.get("aws_account_id"),
        aws_external_id=metadata.get("aws_external_id"),
        database_platform=database_platform,
        public=metadata.get("public"),
    )


###############################################################################
#             CLASSES FOR STATE MACHINE STATUS                                #
###############################################################################


class StatusModel(BaseModel):
    status: str
    is_terminal: bool


class StateTransitionError(Exception):
    def __init__(
        self,
        old_status: enum.Enum,
        new_status: enum.Enum,
        message: str,
    ):
        super().__init__(message)
        self.old_status = old_status
        self.new_status = new_status


###############################################################################
#             PUBLIC METHODS FOR PROJECT METADATA CRUD                        #
###############################################################################

"""
*** README ***

The `host_projects` table contains a column `status`, which is used by
the reconciler to determine which projects to permanently delete. Permanently
deleting a project means the cloud resources for the project are deleted and the
rows in `host_projects` and other tables are deleted (i.e. `DELETE FROM`).

All `SELECT` statements for `host_projects` should include the clause
`WHERE status != 'AWAITING_DELETE'` so that projects that are marked for deletion
are not returned in the result set. This ensures that API requests do not return
"deleted" projects to end users and other parts of the control plane do not
process "deleted" projects.
"""


class ProjectStatus(enum.Enum):
    AWAITING_DATABASE = StatusModel(status="AWAITING_DATABASE", is_terminal=False)
    READY = StatusModel(status="READY", is_terminal=False)
    UNUSED = StatusModel(status="UNUSED", is_terminal=False)
    AWAITING_DELETE = StatusModel(status="AWAITING_DELETE", is_terminal=False)
    UNKNOWN = StatusModel(status="UNKNOWN", is_terminal=False)


class ProjectStatusValidator:
    ALL_STATUSES = [
        ProjectStatus.AWAITING_DATABASE,
        ProjectStatus.READY,
        ProjectStatus.UNUSED,
        ProjectStatus.AWAITING_DELETE,
        ProjectStatus.UNKNOWN,
    ]

    _TRANSLATION_MAP: Dict[str, ProjectStatus] = {
        status.name: status for status in ALL_STATUSES
    }

    _VALID_TRANSITION_MAP: Dict[ProjectStatus, List[ProjectStatus]] = {
        ProjectStatus.AWAITING_DATABASE: [
            ProjectStatus.READY,
            ProjectStatus.AWAITING_DELETE,
        ],
        ProjectStatus.READY: [
            ProjectStatus.UNUSED,
            ProjectStatus.AWAITING_DELETE,
        ],
        ProjectStatus.UNUSED: [
            ProjectStatus.READY,
            ProjectStatus.AWAITING_DELETE,
        ],
    }

    @classmethod
    def _from_string(cls, status: Optional[str]) -> ProjectStatus:
        if not status:
            return ProjectStatus.UNKNOWN

        return cls._TRANSLATION_MAP.get(status, ProjectStatus.UNKNOWN)

    @classmethod
    def validate_state_transition(
        cls, old_state_str: Optional[str], new_state_str: Optional[str]
    ) -> None:
        old_state = cls._from_string(old_state_str)
        new_state = cls._from_string(new_state_str)

        # Always allow transitioning to the same state. This enables tasks
        # that update project state to run idempotently.
        if old_state == new_state:
            return

        if new_state not in cls._VALID_TRANSITION_MAP[old_state]:
            raise StateTransitionError(
                old_state,
                new_state,
                f"Project cannot transition from {old_state} to {new_state}",
            )


async def partial_update_project_metadata(
    auth: AuthInfo,
    current_project: ProjectMetadata,
    repo_path: Optional[str],
    repo_commit: Optional[str],
    repo_commit_sha: Optional[str],
    image_path: Optional[str],
    created_by: dict,
    container_spec: dict,
    readme_markdown: Optional[str],
    secrets: dict[str, str],
    shareable: bool = False,
) -> ProjectMetadataUpdateResponse:
    async with asyncpg_conn() as db, db.transaction():
        knative = ResourceId(
            type="services", name=service_name(auth.tenant_id, current_project.name)
        )

        # update metadata
        metadata = current_project.metadata.copy()
        metadata["shareable"] = shareable

        project_from_db = await db.fetchrow(
            "UPDATE host_projects SET repo_url = $3, knative = $4, name = $5, updated_at = $6, metadata = $7, readme_markdown = $8, secrets = $9, container_spec = $10 WHERE tenant_id = $1 AND id = $2 RETURNING *",
            auth.tenant_id,
            current_project.id,
            current_project.repo_url,
            knative.model_dump(),
            # Note that currently updating the name of a project is not supported,
            # and we should not support it until we also update the name of the
            # upstream LangSmith session.
            current_project.name,
            datetime.now(tz=timezone.utc),
            metadata,
            readme_markdown,
            {
                name: fernet.encrypt(decrypted_value.encode()).decode()
                for name, decrypted_value in secrets.items()
            },
            container_spec,
        )

        if not project_from_db:
            raise HTTPException(404, "Project not found")

        project_metadata = ProjectMetadata(**project_from_db)

        revision_metadata = (
            await _create_revision_for_project_within_existing_transaction(
                auth,
                project_metadata.id,
                repo_path,
                repo_commit,
                repo_commit_sha,
                image_path,
                created_by,
                db,
            )
        )

        return ProjectMetadataUpdateResponse(
            project_metadata=project_metadata, revision_metadata=revision_metadata
        )


async def list_projects_metadata(
    params: ListProjectsQueryParams,
    tenant_id: Optional[UUID] = None,
    tag_value_id: List[UUID] | None = None,
) -> tuple[List[ProjectMetadata], int]:
    """Return list of projects."""
    sql_params: dict[str, Any] = {}

    if params.status:
        where_clause = f"WHERE status = '{params.status}'"
    else:
        # By default, exclude "deleted" projects
        where_clause = "WHERE status != 'AWAITING_DELETE'"

    if tenant_id:
        where_clause += " AND tenant_id = $tenant_id"
        sql_params = {"tenant_id": tenant_id}

    if params.name_contains:
        where_clause += " AND name ILIKE $name_contains"
        sql_params["name_contains"] = f"%{params.name_contains}%"

    if params.remote_reconciled is not None:
        where_clause += " AND remote_reconciled = $remote_reconciled"
        sql_params["remote_reconciled"] = params.remote_reconciled

    if tag_value_id:
        where_clause += """
        AND id IN (
            SELECT t.resource_id
            FROM taggings t
            JOIN tag_values tv ON t.tag_value_id = tv.id
            JOIN tag_keys tk ON tv.tag_key_id = tk.id
            WHERE t.resource_type = 'deployment'
            AND t.tag_value_id = ANY($tag_value_id::uuid[])
            AND tk.tenant_id = $tenant_id
            GROUP BY t.resource_id
            HAVING COUNT(*) = (
                SELECT COUNT(*) FROM unnest($tag_value_id::uuid[])
            )
        )
        """
        sql_params["tag_value_id"] = tag_value_id

    sql_params["limit"] = params.limit + 1
    sql_params["offset"] = params.offset

    query = f"""
    SELECT * FROM host_projects
    {where_clause}
    ORDER BY updated_at DESC
    LIMIT $limit OFFSET $offset
    """

    sql_query_pos = kwargs_to_pgpos(query, sql_params)

    async with asyncpg_conn() as db:
        projects = await db.fetch(sql_query_pos.sql, *sql_query_pos.args)
        projects_to_return = projects[: params.limit]

    return [
        ProjectMetadata(**project) for project in projects_to_return
    ], params.offset + len(projects)


async def set_project_status(
    project_id: UUID, status: ProjectStatus, tenant_id: UUID | None = None
) -> ProjectMetadata:
    """Set project status."""
    async with asyncpg_conn() as db, db.transaction():
        project_metadata = await get_project_metadata(project_id, tenant_id)

        ProjectStatusValidator.validate_state_transition(
            project_metadata.status, status.value.status
        )

        where_clause = "WHERE id = $2"
        sql_params = [project_id]

        if tenant_id:
            where_clause += " AND tenant_id = $3"
            sql_params.append(tenant_id)

        project_metadata = await db.fetchrow(
            f"""
            UPDATE host_projects
            SET status = $1, updated_at = now()
            {where_clause}
            RETURNING *
            """,
            status.value.status,
            *sql_params,
        )

        return ProjectMetadata(**project_metadata)


async def delete_project_metadata(auth: AuthInfo, project_id: UUID) -> None:
    """Delete project metadata.

    The implementation of this function must be idempotent because it can be
    called multiple times by the reconciler (although this is not likely to
    happen in practice).

    Do not use this function outside of the reconciler.
    """
    async with asyncpg_conn() as db, db.transaction():
        api_key_id = await db.fetchval(
            """
            DELETE FROM host_projects
            WHERE status = 'AWAITING_DELETE' AND id = $1 AND tenant_id = $2
            RETURNING api_key_id
            """,
            project_id,
            auth.tenant_id,
        )
        # rows in host_revisions will also be deleted (cascading deletes)

        if api_key_id:
            service_account_id = await db.fetchval(
                """
                SELECT service_account_id
                FROM api_keys
                WHERE id = $1
                """,
                api_key_id,
            )
            await delete_api_key_within_existing_transaction(
                auth, api_key_id, db, throw_if_not_found=False
            )

            if service_account_id:
                await delete_service_account_within_existing_transaction(
                    auth, service_account_id, db
                )


async def get_project_metadata(
    project_id: UUID,
    tenant_id: Optional[UUID] = None,
) -> ProjectMetadata:
    where_clause = "WHERE status != 'AWAITING_DELETE' AND id = $1"
    params = [project_id]

    if tenant_id:
        where_clause += " AND tenant_id = $2"
        params.append(tenant_id)

    async with asyncpg_conn() as db:
        project = await db.fetchrow(
            f"SELECT * FROM host_projects {where_clause}",
            *params,
        )

        if not project:
            raise HTTPException(status_code=404, detail="project not found")

        return ProjectMetadata(**project)


async def create_project_metadata(
    auth: AuthInfo, request: CreateProjectMetadataRequest
) -> ProjectMetadataCreateResponse:
    async with asyncpg_conn() as db, db.transaction():
        total_projects_for_tenant = await db.fetchval(
            "SELECT COUNT(*) FROM host_projects WHERE status != 'AWAITING_DELETE' AND tenant_id = $1",
            auth.tenant_id,
        )
        max_deployments = (
            auth.tenant_config.organization_config.max_langgraph_cloud_deployments
        )

        if total_projects_for_tenant >= max_deployments:
            raise HTTPException(
                status_code=429,
                detail=f"Limit of {max_deployments} deployment(s) per organization exceeded. Please reach <NAME_EMAIL> to discuss increased quota.",
            )

        if request.deployment_type == DeploymentType.dev_free and (
            not settings.IS_SELF_HOSTED and not request.remote_reconciled
        ):
            total_free_projects_for_org = await db.fetchval(
                "SELECT COUNT(*) FROM host_projects AS hp JOIN tenants AS t ON hp.tenant_id = t.id WHERE hp.status != 'AWAITING_DELETE' AND t.organization_id = $1 AND hp.metadata ->> 'deployment_type' = 'dev_free'",
                auth.organization_id,
            )
            max_free_deployments = auth.tenant_config.organization_config.max_free_langgraph_cloud_deployments

            if total_free_projects_for_org >= max_free_deployments:
                raise HTTPException(
                    status_code=429,
                    detail=f"Limit of {max_free_deployments} free Development deployment type(s) per organization exceeded.",
                )

        api_key = await create_api_key_within_existing_transaction(
            auth,
            APIKeyCreateRequest(description=f"Hosted LangServe: {request.name}"),
            db,
        )
        project_metadata = await _create_project_metadata_within_existing_transaction(
            auth, request, api_key.id, db
        )

        revision = None
        if request.platform.image_source in (
            ImageSourceId.github,
            ImageSourceId.external_docker,
        ):
            # Create initial revision and start deployment proces
            revision = await _create_revision_for_project_within_existing_transaction(
                auth,
                project_metadata.id,
                request.repo_path,
                request.repo_commit,
                request.repo_commit_sha,
                request.image_path,
                request.created_by,
                db,
            )

        # Temporarily add LANGCHAIN_API_KEY to the secrets column. During
        # reconciliation, LANGCHAIN_API_KEY will be saved to the respective
        # secrets store. When a new revision is created, the secrets column
        # is overwritten, but the reconciler will preserve LANGCHAIN_API_KEY
        # in the secrets store.
        await db.execute(
            """UPDATE host_projects SET secrets = jsonb_set(secrets, '{LANGCHAIN_API_KEY}', $2) WHERE id = $1""",
            project_metadata.id,
            fernet.encrypt(api_key.key.encode()).decode(),
        )

        return ProjectMetadataCreateResponse(
            project_metadata=project_metadata,
            revision_metadata=revision,
        )


async def update_project_metadata(
    project_id: UUID,
    tenant_id: UUID | None = None,
    repo_branch: str | None = None,
    build_on_push: bool | None = None,
    tool_name: str | None = None,
    example_input: dict | None = None,
    display_name: str | None = None,
    description: str | None = None,
    custom_url: str | None = None,
) -> ProjectMetadata:
    """Update host_projects table.

    This function does not create a new revision. It is meant for updating
    columns that do not require triggering a new revision.
    """
    if tool_name:
        if not re.match(r"^[a-zA-Z0-9_-]+$", tool_name):
            raise HTTPException(
                status_code=400,
                detail="Tool name can only contain letters, numbers, underscores, and hyphens",
            )
        if len(tool_name) > 64:
            raise HTTPException(
                status_code=400, detail="Tool name cannot be longer than 64 characters"
            )

    where_clause = "WHERE id = $8"
    where_params = [project_id]
    if tenant_id:
        where_clause += " AND tenant_id = $9"
        where_params.append(tenant_id)

    async with asyncpg_conn() as db, db.transaction():
        project = await db.fetchrow(
            f"""
            UPDATE host_projects
            SET repo_branch = COALESCE($1, repo_branch),
                build_on_push = COALESCE($2, build_on_push),
                display_name = COALESCE($3, display_name),
                description = COALESCE($4, description),
                tool_name = COALESCE($5, tool_name),
                example_input = COALESCE($6, example_input),
                custom_url = COALESCE($7, custom_url)
            {where_clause}
            RETURNING *
            """,
            repo_branch,
            build_on_push,
            display_name,
            description,
            tool_name,
            example_input,
            custom_url,
            *where_params,
        )
        if not project:
            raise HTTPException(404, "Project not found")
        return ProjectMetadata(**project)


async def list_projects_metadata_build_on_push(
    auth: AuthInfo,
    repo_url: str,
    repo_branch: str,
) -> List[ProjectMetadata]:
    async with asyncpg_conn() as db:
        projects = await db.fetch(
            """
            SELECT * FROM host_projects
            WHERE status != 'AWAITING_DELETE' AND tenant_id = $1 AND repo_url = $2 AND repo_branch = $3 AND build_on_push = true
            """,
            auth.tenant_id,
            repo_url,
            repo_branch,
        )
        return [ProjectMetadata(**project) for project in projects]


async def list_unused_projects(
    last_n_days: int, status: str
) -> List[tuple[ProjectMetadata, datetime]]:
    """Return projects by status and where the API key is last used more
    than n days ago.

    This function returns a list of tuples. The first element of the tuple
    is the project. The second element of the tuple is the last used at date.

    This function should not be used to return projects in AWAITING_DELETE
    status externally.
    """
    async with asyncpg_conn() as db:
        query = """
        SELECT hp.*, COALESCE(ak.last_used_at, ak.created_at) as last_used_at
        FROM host_projects AS hp JOIN api_keys AS ak ON hp.api_key_id = ak.id
        WHERE hp.status = $1 AND COALESCE(ak.last_used_at, ak.created_at) < NOW() - $2 * INTERVAL '1 day'
        """
        projects = await db.fetch(query, status, last_n_days)
        return [
            (ProjectMetadata(**project), project["last_used_at"])
            for project in projects
        ]


async def get_project_creator_email(project_id: UUID) -> Optional[str]:
    """Return email address of project creator.

    If the project was created programmatically, return the email address
    of the LangSmith organization owner.
    """
    email = None

    async with asyncpg_conn() as db:
        # The first revision of the project is always created by a human user.
        # The first revision's metadata will contain human user identity metadata.
        query = """
        SELECT metadata
        FROM host_revisions
        WHERE project_id = $1
        ORDER BY created_at ASC
        LIMIT 1
        """
        metadata = await db.fetchval(
            query,
            project_id,
        )
        metadata_dict = dict(metadata or {})

        # retrieve email from identity metadata
        created_by: dict = metadata_dict.get("created_by", {})
        if created_by.get("type") == "user":
            identity_id = created_by.get("identity_id")
            if identity_id:
                email = await db.fetchval(
                    """
                    SELECT u.email
                    FROM identities AS i JOIN users AS u ON i.ls_user_id = u.ls_user_id
                    WHERE i.id = $1
                    """,
                    identity_id,
                )
        else:
            # fallback to getting email from someone with organization access scope
            email = await db.fetchval(
                """
                SELECT u.email
                FROM users AS u
                    JOIN identities AS i ON u.id = i.user_id
                    JOIN organizations AS o ON i.organization_id = o.id
                    JOIN tenants AS t ON o.id = t.organization_id
                    JOIN host_projects AS hp ON t.id = hp.tenant_id
                WHERE hp.id = $1
                    AND i.access_scope = 'organization'
                LIMIT 1
                """,
                project_id,
            )

    return email


async def reset_unused_projects(last_n_days: int) -> List[ProjectMetadata]:
    """Reset project status to READY for projects where the API key is used
    within last n days.

    Return all updated projects.
    """
    async with asyncpg_conn() as db:
        query = """
        UPDATE host_projects AS hp
        SET status = 'READY'
        FROM api_keys AS ak
        WHERE hp.status = 'UNUSED' AND hp.api_key_id = ak.id and COALESCE(ak.last_used_at, ak.created_at) >= NOW() - $1 * INTERVAL '1 day'
        RETURNING hp.*
        """
        projects = await db.fetch(query, last_n_days)
        return [ProjectMetadata(**project) for project in projects]


async def get_project_secrets(
    project_id: UUID, tenant_id: UUID | None = None
) -> dict[str, str] | None:
    async with asyncpg_conn() as db:
        where_clause = "WHERE status != 'AWAITING_DELETE' AND id = $1"
        params = [project_id]

        if tenant_id:
            where_clause += " AND tenant_id = $2"
            params.append(tenant_id)

        async with asyncpg_conn() as db:
            secrets = await db.fetchval(
                f"SELECT secrets FROM host_projects {where_clause}",
                *params,
            )

            if secrets is None:
                # The project has not been migrated to store secrets in the
                # database.
                return None

            secrets_dict: dict[str, str] = dict(secrets)
            return {
                name: fernet.decrypt(encrypted_value.encode()).decode()
                for name, encrypted_value in secrets_dict.items()
            }


###############################################################################
#             PUBLIC METHODS FOR REVISION METADATA CRUD                       #
###############################################################################

DEFAULT_BRANCH = "main"
DEFAULT_SUBDIR = "langgraph.json"


class RevisionStatus(enum.Enum):
    CREATING = StatusModel(status="CREATING", is_terminal=False)
    AWAITING_BUILD = StatusModel(status="AWAITING_BUILD", is_terminal=False)
    BUILDING = StatusModel(status="BUILDING", is_terminal=False)
    AWAITING_DEPLOY = StatusModel(status="AWAITING_DEPLOY", is_terminal=False)
    DEPLOYING = StatusModel(status="DEPLOYING", is_terminal=False)

    CREATE_FAILED = StatusModel(status="CREATE_FAILED", is_terminal=True)
    BUILD_FAILED = StatusModel(status="BUILD_FAILED", is_terminal=True)
    DEPLOY_FAILED = StatusModel(status="DEPLOY_FAILED", is_terminal=True)

    DEPLOYED = StatusModel(status="DEPLOYED", is_terminal=True)
    INTERRUPTED = StatusModel(status="INTERRUPTED", is_terminal=False)

    # TODO: CHANGE
    UNKNOWN = StatusModel(status="UNKNOWN", is_terminal=False)


class RevisionStatusValidator:
    ALL_STATUSES = [
        RevisionStatus.CREATING,
        RevisionStatus.AWAITING_BUILD,
        RevisionStatus.BUILDING,
        RevisionStatus.AWAITING_DEPLOY,
        RevisionStatus.DEPLOYING,
        RevisionStatus.CREATE_FAILED,
        RevisionStatus.BUILD_FAILED,
        RevisionStatus.DEPLOY_FAILED,
        RevisionStatus.DEPLOYED,
        RevisionStatus.INTERRUPTED,
        RevisionStatus.UNKNOWN,
    ]

    _TRANSLATION_MAP: Dict[str, RevisionStatus] = {
        status.name: status for status in ALL_STATUSES
    }

    _VALID_TRANSITION_MAP: Dict[RevisionStatus, List[RevisionStatus]] = {
        RevisionStatus.CREATING: [
            RevisionStatus.AWAITING_BUILD,
            RevisionStatus.AWAITING_DEPLOY,
            RevisionStatus.CREATE_FAILED,
            RevisionStatus.INTERRUPTED,
            RevisionStatus.AWAITING_DEPLOY,  # for projects w external builds
        ],
        RevisionStatus.AWAITING_BUILD: [
            RevisionStatus.BUILDING,
            RevisionStatus.INTERRUPTED,
            RevisionStatus.BUILD_FAILED,
        ],
        RevisionStatus.BUILDING: [
            RevisionStatus.AWAITING_DEPLOY,
            RevisionStatus.BUILD_FAILED,
            RevisionStatus.INTERRUPTED,
        ],
        RevisionStatus.AWAITING_DEPLOY: [
            # TODO: Remove this state transition (AWAITING_DEPLOY -> BUILDING)
            # after migrating task scheduling to a cron based scheduler.
            RevisionStatus.BUILDING,  # needed if build_image is retried
            RevisionStatus.DEPLOYING,
            RevisionStatus.CREATE_FAILED,
            RevisionStatus.INTERRUPTED,
        ],
        RevisionStatus.DEPLOYING: [
            RevisionStatus.DEPLOYED,
            RevisionStatus.DEPLOY_FAILED,
            RevisionStatus.INTERRUPTED,
        ],
        # TODO: FIGURE OUT HOW TO DEAL WITH UNKNOWN
        RevisionStatus.UNKNOWN: ALL_STATUSES,
        # TERMINAL STATUSES CANNOT CHANGE
        RevisionStatus.CREATE_FAILED: [],
        RevisionStatus.BUILD_FAILED: [],
        RevisionStatus.DEPLOY_FAILED: [],
        RevisionStatus.DEPLOYED: [
            # allow revisions in DEPLOYED status to be redeployed
            RevisionStatus.AWAITING_DEPLOY,
        ],
        RevisionStatus.INTERRUPTED: [
            # INTERRUPTED can fail, or if it is in progress, it can transition
            # to either:
            #    - a failed state
            #    - deployed - if the abort was run while DEPLOYING was in progress
            RevisionStatus.CREATE_FAILED,
            RevisionStatus.BUILD_FAILED,
            RevisionStatus.DEPLOY_FAILED,
            RevisionStatus.DEPLOYED,
        ],
    }

    @classmethod
    def _from_string(cls, status: Optional[str]) -> RevisionStatus:
        if not status:
            return RevisionStatus.UNKNOWN

        return cls._TRANSLATION_MAP.get(status, RevisionStatus.UNKNOWN)

    @classmethod
    def validate_state_transition(
        cls, old_state_str: Optional[str], new_state_str: Optional[str]
    ) -> None:
        old_state = cls._from_string(old_state_str)
        new_state = cls._from_string(new_state_str)

        # Always allow transitioning to the same state. This enables tasks
        # that update revision state to run idempotently.
        if old_state == new_state:
            return

        if new_state not in cls._VALID_TRANSITION_MAP[old_state]:
            raise StateTransitionError(
                old_state,
                new_state,
                f"Revision cannot transition from {old_state} to {new_state}",
            )


async def list_revisions_metadata(
    limit: int,
    offset: int,
    project_id: UUID | None = None,
    tenant_id: UUID | None = None,
    status: str | None = None,
    remote_reconciled: bool | None = None,
) -> tuple[list[RevisionMetadata], int]:
    """Return list of revisions."""
    where_clause = "WHERE p.status != 'AWAITING_DELETE'"
    sql_params: dict[str, Any] = {}

    if project_id:
        where_clause += " AND r.project_id = $project_id"
        sql_params["project_id"] = project_id

    if tenant_id:
        where_clause += " AND p.tenant_id = $tenant_id"
        sql_params["tenant_id"] = tenant_id

    if status:
        where_clause += " AND r.status = $status"
        sql_params["status"] = status

    if remote_reconciled is not None:
        where_clause += " AND p.remote_reconciled = $remote_reconciled"
        sql_params["remote_reconciled"] = remote_reconciled

    sql_params["limit"] = limit + 1
    sql_params["offset"] = offset

    query = f"""
    SELECT r.*
    FROM host_revisions AS r
    INNER JOIN host_projects AS p ON r.project_id = p.id
    {where_clause}
    ORDER BY r.created_at DESC LIMIT $limit OFFSET $offset
    """

    sql_query_pos = kwargs_to_pgpos(query, sql_params)

    async with asyncpg_conn() as db:
        revisions = await db.fetch(sql_query_pos.sql, *sql_query_pos.args)
        sorted_revisions = sorted(
            revisions, key=lambda r: r["created_at"], reverse=True
        )
        revisions_to_return = sorted_revisions[:limit]
        return ([RevisionMetadata(**r) for r in revisions_to_return], len(revisions))


async def get_revision_metadata(
    revision_id: UUID, tenant_id: UUID | None = None
) -> RevisionMetadata:
    async with asyncpg_conn() as db:
        return await _get_revision_within_existing_transaction(
            revision_id, db, tenant_id
        )


async def get_latest_revision_metadata(
    project_id: UUID,
    revision_status: Optional[str] = None,
) -> Optional[RevisionMetadata]:
    """Get the latest revision of a project.

    Optionally specify a revision status to filter by. For example, get the
    latest DEPLOYED revision.
    """
    async with asyncpg_conn() as db:
        where = "WHERE p.status != 'AWAITING_DELETE' AND p.id = $1"
        params: list[Any] = [project_id]

        if revision_status:
            where += " AND r.status = $2"
            params.append(revision_status)

        query = f"""
            SELECT r.* FROM host_revisions AS r
            INNER JOIN host_projects AS p ON r.project_id = p.id
            {where}
            ORDER BY r.created_at DESC
            LIMIT 1
        """
        revision = await db.fetchrow(
            query,
            *params,
        )
        return RevisionMetadata(**revision) if revision else None


async def set_revision_status_awaiting_build(
    auth: AuthInfo, revision_id: UUID
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        return await _update_revision_status_within_existing_transaction(
            revision_id, RevisionStatus.AWAITING_BUILD, db, auth.tenant_id
        )


async def set_revision_status_building(
    revision_id: UUID, gcp_build_name: str, tenant_id: UUID | None = None
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        await _update_revision_status_within_existing_transaction(
            revision_id, RevisionStatus.BUILDING, db, tenant_id
        )
        await db.execute(
            """
            UPDATE host_revisions SET gcp_build_name = $2 WHERE id = $1
            """,
            revision_id,
            gcp_build_name,
        )
        return await _get_revision_within_existing_transaction(
            revision_id, db, tenant_id
        )


async def set_revision_status_awaiting_deploy(
    revision_id: UUID, message: str | None = None, tenant_id: UUID | None = None
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        if message is not None:
            return (
                await _update_revision_status_with_message_within_existing_transaction(
                    revision_id,
                    RevisionStatus.AWAITING_DEPLOY,
                    message,
                    db,
                    tenant_id,
                )
            )
        else:
            return await _update_revision_status_within_existing_transaction(
                revision_id, RevisionStatus.AWAITING_DEPLOY, db, tenant_id
            )


async def set_revision_status_deploying(
    revision_id: UUID, message: str | None = None, tenant_id: UUID | None = None
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        if message is not None:
            return (
                await _update_revision_status_with_message_within_existing_transaction(
                    revision_id,
                    RevisionStatus.DEPLOYING,
                    message,
                    db,
                    tenant_id,
                )
            )
        else:
            return await _update_revision_status_within_existing_transaction(
                revision_id, RevisionStatus.DEPLOYING, db, tenant_id
            )


async def set_revision_status_deployed(
    revision_id: UUID,
    resource_id: ResourceId | None,
    tenant_id: UUID | None = None,
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        if resource_id is not None:
            await db.execute(
                "UPDATE host_revisions SET knative = $1 WHERE id = $2",
                resource_id.model_dump(),
                revision_id,
            )

        return await _update_revision_status_with_message_within_existing_transaction(
            revision_id,
            RevisionStatus.DEPLOYED,
            "Revision deployed successfully",
            db,
            tenant_id,
        )


async def set_revision_status_create_failed(
    auth: AuthInfo, revision_id: UUID, message: str
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        return await _update_revision_status_with_message_within_existing_transaction(
            revision_id, RevisionStatus.CREATE_FAILED, message, db, auth.tenant_id
        )


async def set_revision_status_build_failed(
    revision_id: UUID, message: str, tenant_id: UUID | None = None
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        return await _update_revision_status_with_message_within_existing_transaction(
            revision_id, RevisionStatus.BUILD_FAILED, message, db, tenant_id
        )


async def set_revision_status_deploy_failed(
    revision_id: UUID, message: str, tenant_id: UUID | None = None
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        return await _update_revision_status_with_message_within_existing_transaction(
            revision_id, RevisionStatus.DEPLOY_FAILED, message, db, tenant_id
        )


async def set_revision_status_interrupted(
    auth: AuthInfo, revision_id: UUID
) -> RevisionMetadata:
    async with asyncpg_conn() as db, db.transaction():
        return await _update_revision_status_with_message_within_existing_transaction(
            revision_id,
            RevisionStatus.INTERRUPTED,
            (
                "Revision interrupted by user. This status can change to a "
                "failed state if the revision was in progress when interrupted, "
                "or deployed if it was in the process of being deployed while "
                "interrupted."
            ),
            db,
            auth.tenant_id,
        )


#######################################################################
#                    PUBLIC METHODS FOR INTEGRATIONS                  #
#######################################################################


class HostIntegrationProvider(enum.Enum):
    GITHUB = "github"
    UNKNOWN = "unknown"


class CreateHostIntegrationRequest(BaseModel):
    namespace_id: str
    provider: HostIntegrationProvider


class HostIntegration(BaseModel):
    id: UUID
    tenant_id: UUID
    namespace_id: str
    provider: HostIntegrationProvider


async def create_integration(
    tenant_id: UUID, req: CreateHostIntegrationRequest
) -> HostIntegration:
    async with asyncpg_conn() as db:
        integration_row = await db.fetchrow(
            """
            INSERT INTO host_integration_namespace_ids (tenant_id, namespace_id, provider)
            VALUES ($1, $2, $3)
            ON CONFLICT (tenant_id, namespace_id, provider)
            DO UPDATE SET tenant_id = EXCLUDED.tenant_id
            RETURNING *
            """,
            tenant_id,
            req.namespace_id,
            req.provider.value,
        )
        return HostIntegration(**integration_row)


async def get_integration(
    tenant_id: UUID, host_integration_id: UUID
) -> HostIntegration:
    async with asyncpg_conn() as db:
        integration_row = await db.fetchrow(
            """
            SELECT * FROM host_integration_namespace_ids
            WHERE id = $1 AND tenant_id = $2
            """,
            host_integration_id,
            tenant_id,
        )

        if not integration_row:
            raise HTTPException(
                status_code=400,
                detail="integration not found",
            )

        return HostIntegration(**integration_row)


async def get_integrations_by_namespace_id(
    namespace_id: str, provider: HostIntegrationProvider
) -> list[HostIntegration]:
    async with asyncpg_conn() as db:
        rows = await db.fetch(
            """
            SELECT * FROM host_integration_namespace_ids
            WHERE namespace_id = $1 AND provider = $2
            """,
            namespace_id,
            provider.value,
        )
        return [HostIntegration(**row) for row in rows]


async def list_integrations_for_provider(
    tenant_id: UUID, provider: HostIntegrationProvider
) -> List[HostIntegration]:
    async with asyncpg_conn() as db:
        integration_rows = await db.fetch(
            """
            SELECT * FROM host_integration_namespace_ids
            WHERE tenant_id = $1 AND provider = $2
            """,
            tenant_id,
            provider.value,
        )
        return [HostIntegration(**row) for row in integration_rows]


#######################################################################
#                            PRIVATE METHODS                          #
#######################################################################


async def _update_revision_status_within_existing_transaction(
    revision_id: UUID,
    status: RevisionStatus,
    db: asyncpg.Connection,
    tenant_id: UUID | None = None,
) -> RevisionMetadata:
    revision = await _get_revision_within_existing_transaction(
        revision_id, db, tenant_id
    )

    RevisionStatusValidator.validate_state_transition(
        revision.status, status.value.status
    )
    revision = await db.fetchrow(
        "UPDATE host_revisions SET status = $2, updated_at = now() WHERE id = $1 RETURNING *",
        revision_id,
        status.name,
    )
    return RevisionMetadata(**revision)


async def _update_revision_status_with_message_within_existing_transaction(
    revision_id: UUID,
    status: RevisionStatus,
    message: str,
    db: asyncpg.Connection,
    tenant_id: UUID | None = None,
) -> RevisionMetadata:
    revision = await _get_revision_within_existing_transaction(
        revision_id, db, tenant_id
    )

    RevisionStatusValidator.validate_state_transition(
        revision.status, status.value.status
    )

    completed_at_str = (
        ", completed_at = now()" if not (status == RevisionStatus.INTERRUPTED) else ""
    )

    revision = await db.fetchrow(
        f"UPDATE host_revisions SET status = $2, status_message = $3, updated_at = now(){completed_at_str} WHERE id = $1 RETURNING *",
        revision_id,
        status.name,
        message,
    )
    return RevisionMetadata(**revision)


async def _update_revision_message_within_existing_transaction(
    auth: AuthInfo,
    revision_id: UUID,
    message: str,
    db: asyncpg.Connection,
) -> RevisionMetadata:
    revision = await _get_revision_within_existing_transaction(
        revision_id, db, auth.tenant_id
    )

    revision = await db.fetchrow(
        "UPDATE host_revisions SET status_message = $2, updated_at = now() WHERE id = $1 RETURNING *",
        revision_id,
        message,
    )
    return RevisionMetadata(**revision)


async def _get_revision_within_existing_transaction(
    revision_id: UUID,
    db: asyncpg.Connection,
    tenant_id: UUID | None = None,
) -> RevisionMetadata:
    where_clause = "WHERE r.id = $1 AND p.status != 'AWAITING_DELETE'"
    sql_params = [revision_id]

    if tenant_id:
        where_clause += " AND p.tenant_id = $2"
        sql_params.append(tenant_id)

    query = f"""
        SELECT r.* FROM host_revisions AS r
        INNER JOIN host_projects AS p ON r.project_id = p.id
        {where_clause}
    """
    revision = await db.fetchrow(
        query,
        *sql_params,
    )
    if not revision:
        raise HTTPException(status_code=404, detail="revision not found")

    return RevisionMetadata(**revision)


def _create_revision_metadata_column(
    repo_commit_sha: Optional[str],
    created_by: dict,
) -> dict[str, Any]:
    """Create the value of the host_revisions.metadata column."""
    metadata: dict = {
        "created_by": created_by,
    }

    if repo_commit_sha:
        metadata["repo_commit_sha"] = repo_commit_sha

    return metadata


async def _create_revision_for_project_within_existing_transaction(
    auth: AuthInfo,
    project_id: UUID,
    repo_path: Optional[str],
    repo_commit: Optional[str],
    repo_commit_sha: Optional[str],
    image_path: Optional[str],
    created_by: dict,
    db: asyncpg.Connection,
) -> RevisionMetadata:
    non_terminal_statuses = [
        status
        for status in RevisionStatusValidator.ALL_STATUSES
        if status.value.is_terminal is False
        and status
        not in (
            RevisionStatus.INTERRUPTED,
            RevisionStatus.UNKNOWN,
        )
    ]

    query_replacement_str = ", ".join(
        [f"${i}" for i in range(2, len(non_terminal_statuses) + 2)]
    )

    in_progress_revisions = await db.fetch(
        f"SELECT * FROM host_revisions WHERE project_id = $1 AND status IN ({query_replacement_str})",
        project_id,
        *[status.name for status in non_terminal_statuses],
    )

    if in_progress_revisions and len(in_progress_revisions) > 0:
        raise HTTPException(
            status_code=409,
            detail="There is already a revision in progress for this project",
        )

    revision_from_db = await db.fetchrow(
        "INSERT INTO host_revisions (project_id, repo_path, repo_commit, status, metadata, image_path) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *",
        project_id,
        repo_path,
        repo_commit,
        RevisionStatus.CREATING.name,
        _create_revision_metadata_column(repo_commit_sha, created_by),
        image_path,
    )

    return RevisionMetadata(**revision_from_db)


def _create_project_metadata_column(
    request: CreateProjectMetadataRequest,
) -> dict[str, Any]:
    """Create the value of the host_projects.metadata column."""
    metadata = {
        "deployment_type": request.deployment_type.value,
        "image_source": request.platform.image_source.value,
        "platform": request.platform.deployment_platform.value,
        "shareable": request.shareable,
        "database_platform": request.platform.database_platform.value,
    }

    if request.platform.region:
        metadata["region"] = request.platform.region
    if request.platform.k8s_cluster is not None:
        metadata["k8s_cluster"] = request.platform.k8s_cluster
    if request.platform.k8s_namespace is not None:
        metadata["k8s_namespace"] = request.platform.k8s_namespace
    if request.platform.aws_account_id is not None:
        metadata["aws_account_id"] = request.platform.aws_account_id
    if request.platform.aws_external_id is not None:
        metadata["aws_external_id"] = request.platform.aws_external_id
    if request.platform.public is not None:
        metadata["public"] = request.platform.public

    return metadata


async def _create_project_metadata_within_existing_transaction(
    auth: AuthInfo,
    request: CreateProjectMetadataRequest,
    api_key_id: UUID,
    db: asyncpg.Connection,
) -> ProjectMetadata:
    try:
        langsmith_project = await tracer_sessions.create.create_tracer_session_within_existing_transaction(
            db,
            auth,
            TracerSessionCreate(name=request.name),
            upsert=False,
        )
    except HTTPException as e:
        if e.status_code == 409:
            raise HTTPException(
                status_code=409,
                detail="LangGraph Platform deployments create a project in LangSmith "
                + "with the same name as the deployment. There already exists "
                + f"a project in LangSmith named: {request.name}. Please "
                + "specify a different name for the deployment.",
            )
        else:
            raise e
    knative = ResourceId(
        type="services", name=service_name(auth.tenant_id, request.name)
    )

    project_metadata_row = await db.fetchrow(
        "INSERT INTO host_projects (tenant_id, lc_hosted, repo_url, repo_branch, knative, name, tracer_session_id, api_key_id, host_integration_id, metadata, build_on_push, readme_markdown, status, remote_reconciled, secrets, container_spec) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16) RETURNING *",
        auth.tenant_id,
        request.lc_hosted,
        request.repo_url,
        request.repo_commit,  # repo branch
        knative.model_dump(),
        request.name,
        langsmith_project.id,
        api_key_id,
        request.host_integration_id,
        _create_project_metadata_column(request),
        request.build_on_push,
        request.readme_markdown,
        ProjectStatus.AWAITING_DATABASE.value.status,
        request.remote_reconciled,
        {
            name: fernet.encrypt(decrypted_value.encode()).decode()
            for name, decrypted_value in request.secrets.items()
        },
        request.container_spec,
    )
    return ProjectMetadata(**project_metadata_row)


async def delete_integration(auth: AuthInfo, integration_namespace_id: UUID):
    async with asyncpg_conn() as db, db.transaction():
        # delete the integration
        await db.execute(
            "DELETE FROM host_integration_namespace_ids WHERE id = $1 AND tenant_id = $2",
            integration_namespace_id,
            auth.tenant_id,
        )
