from datetime import datetime
from typing import Any
from uuid import UUID

from lc_database.database import asyncpg_conn


async def get_project_usage(
    project_id: UUID,
    tenant_id: UUID,
    start_time: datetime,
    end_time: datetime,
) -> dict[str, Any]:
    query = f"""
    SELECT
        SUM( (measures ->> 'langgraph.platform.runs') ::bigint )  AS runs_executed,
        SUM( (measures ->> 'langgraph.platform.nodes')::bigint )  AS nodes_executed,
        CEIL(EXTRACT(epoch FROM ( MAX(to_timestamp) - MIN(from_timestamp) )) / 60)::int AS standby_minutes
    FROM
        remote_metrics
    WHERE
        tags->>'langgraph.platform.project_id' = '{str(project_id)}' AND
        tags->>'langgraph.platform.tenant_id' = '{str(tenant_id)}' AND
        from_timestamp >= '{start_time.isoformat()}' AND
        to_timestamp < '{end_time.isoformat()}'
    """

    async with asyncpg_conn() as db:
        row = await db.fetchrow(query)
        return {
            "runs_executed": row["runs_executed"],
            "nodes_executed": row["nodes_executed"],
            "standby_minutes": row["standby_minutes"],
        }
