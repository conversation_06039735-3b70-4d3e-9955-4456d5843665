import asyncio
from datetime import datetime, timezone
from typing import Any
from uuid import UUID

from app.schemas import (
    CustomChartMetric,
    CustomChartsDataPoint,
    CustomChartSeries,
    CustomChartsSection,
    CustomChartType,
    HostProjectChartMetric,
    SingleCustomChartResponse,
)
from fastapi import HTTPException

from host.api.schemas import ProjectMetricsGroup
from host.config import settings
from host.models import host_metadata_crud
from host.models.deployment_type import DeploymentPlatformId
from host.models.host_metadata_crud import get_project_platform
from host.platforms.k8s_operator.client import (
    K8sOperatorPlatformArgs,
    get_metrics_client,
)


async def get_project_metrics(
    project_id: UUID,
    tenant_id: UUID,
    start_time: datetime | None = None,
    end_time: datetime | None = None,
    step: str | None = None,
) -> CustomChartsSection:
    if start_time is None:
        # set start_time to the beginning of the current hour
        start_time = datetime.now(tz=timezone.utc).replace(
            minute=0, second=0, microsecond=0
        )

    if end_time is None:
        end_time = datetime.now(tz=timezone.utc)

    if step is None:
        step = "60s"

    queries: dict[ProjectMetricsGroup, dict[HostProjectChartMetric, str]] = {
        ProjectMetricsGroup.queue_worker: {
            HostProjectChartMetric.memory_usage: "".join(
                f"""
                sum(
                    kubernetes_io:container_memory_used_bytes{{
                        monitored_resource = "k8s_container",
                        metadata_user_ls_project_id = "{project_id}",
                        memory_type = "non-evictable",
                        metadata_user_ls_tenant_id = "{tenant_id}",
                        container_name = "api-server",
                        metadata_user_app =~ "^.*queue$"
                    }}
                )
            """.split()
            ),
            HostProjectChartMetric.cpu_usage: "".join(
                f"""
                sum(
                    kubernetes_io:container_cpu_request_utilization{{
                        monitored_resource = "k8s_container",
                        metadata_user_ls_project_id = "{project_id}",
                        metadata_user_ls_tenant_id = "{tenant_id}",
                        container_name = "api-server",
                        metadata_user_app =~ "^.*queue$"
                    }}
                )
            """.split()
            ),
        },
        ProjectMetricsGroup.api_server: {
            HostProjectChartMetric.memory_usage: "".join(
                f"""
                sum(
                    kubernetes_io:container_memory_used_bytes{{
                        monitored_resource = "k8s_container",
                        metadata_user_ls_project_id = "{project_id}",
                        memory_type = "non-evictable",
                        metadata_user_ls_tenant_id = "{tenant_id}",
                        container_name = "api-server",
                        metadata_user_app !~ "^.*queue$"
                    }}
                )
            """.split()
            ),
            HostProjectChartMetric.cpu_usage: "".join(
                f"""
                sum(
                    kubernetes_io:container_cpu_request_utilization{{
                        monitored_resource = "k8s_container",
                        metadata_user_ls_project_id = "{project_id}",
                        metadata_user_ls_tenant_id = "{tenant_id}",
                        container_name = "api-server",
                        metadata_user_app !~ "^.*queue$"
                    }}
                )
            """.split()
            ),
        },
    }

    project_metadata = await host_metadata_crud.get_project_metadata(
        project_id, tenant_id
    )
    platform = get_project_platform(project_metadata)

    start_rfc3339 = start_time.isoformat()
    end_rfc3339 = end_time.isoformat()

    gcp_project = f"projects/{settings.GCP_PROJECT_ID}"

    k8s_operator_platform_args = K8sOperatorPlatformArgs.from_platform(platform)
    location = k8s_operator_platform_args.region or settings.HOST_DEPLOYMENT_REGION

    tasks = []
    task_metadata = []
    charts: list[SingleCustomChartResponse] = []

    if platform.deployment_platform == DeploymentPlatformId.k8s_operator:
        async with get_metrics_client() as client:
            for group, metric_to_query in queries.items():
                for metric, query in metric_to_query.items():
                    url = f"{gcp_project}/location/{location}/prometheus/api/v1/query_range"
                    payload = {
                        "query": query,
                        "start": start_rfc3339,
                        "end": end_rfc3339,
                        "step": step,
                    }
                    tasks.append(asyncio.create_task(client.post(url, payload)))
                    task_metadata.append((metric, group))

            # switch to asyncio.as_completed if we have a ton of charts we want to stream later
            results = await asyncio.gather(*tasks, return_exceptions=True)

            if all(isinstance(result, Exception) for result in results):
                raise HTTPException(
                    status_code=502,
                    detail="Failed to retrieve metrics data",
                )
            else:
                for result, (metric, group) in zip(results, task_metadata):
                    if isinstance(result, dict):
                        chart = convert_raw_metrics_to_chart_schema(
                            project_id=project_id,
                            raw_metrics_data=result,
                            metric=metric,
                            group=group,
                        )

                        if chart:
                            charts.append(chart)
    else:
        raise HTTPException(
            status_code=400,
            detail=f"Metrics not supported for platform: {platform.deployment_platform}",
        )

    return CustomChartsSection(
        id=f"{project_id}-metrics",
        title=f"Metrics for {project_metadata.name}",
        charts=charts,
    )


def convert_raw_metrics_to_chart_schema(
    project_id: UUID,
    raw_metrics_data: dict[str, Any],
    metric: HostProjectChartMetric,
    group: ProjectMetricsGroup,
) -> SingleCustomChartResponse | None:
    # we use the "sum" aggregate function when we query for metrics, so we only expect one result
    if (
        raw_metrics_data.get("status") == "success"
        and "data" in raw_metrics_data
        and "result" in raw_metrics_data["data"]
        and isinstance(raw_metrics_data["data"]["result"], list)
        and len(raw_metrics_data["data"]["result"]) == 1
    ):
        result = raw_metrics_data["data"]["result"][0]
        data: list[CustomChartsDataPoint] = []

        series_name = f"{group.value}-{metric.value}"
        series_id = f"{project_id}-{series_name}"

        series = CustomChartSeries(
            id=series_id,
            name=series_name,
            project_metric=metric,
            metric=CustomChartMetric.run_count,  # just using this as a placeholder as its required, we will use project_metric instead on the frontend
        )

        for timestamp, value in result.get("values", []):
            try:
                float_value = float(value)
            except (ValueError, TypeError):
                float_value = 0.0

            dt_object = datetime.fromtimestamp(timestamp, tz=timezone.utc)
            data_point = CustomChartsDataPoint(
                series_id=series_id,
                timestamp=dt_object,
                value=float_value,
            )
            data.append(data_point)

        return SingleCustomChartResponse(
            id=series_id,
            title=series_name,
            index=0,
            chart_type=CustomChartType.line,
            series=[series],
            data=data,
        )
    return None
