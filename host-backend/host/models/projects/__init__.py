import asyncio
import logging
import re
from datetime import datetime, timezone
from typing import Any, Optional, cast
from urllib.parse import urlparse
from uuid import UUID

import botocore.exceptions
import googleapiclient.errors  # type: ignore
from app.api.auth.schemas import AuthInfo
from fastapi import HTTPException
from pydantic import BaseModel, field_validator, model_validator

from host.api.schemas import (
    ListProjectsQueryParams,
    ProjectUsageResponse,
)
from host.config import settings
from host.models import (
    artifact_registry,
    env_var,
    groups,
    host_metadata_crud,
    host_project_usage,
    revisions,
    secrets,
    serviceaccounts,
)
from host.models.deployment_type import (
    K8S_PLATFORMS,
    DatabasePlatformId,
    DeploymentPlatformId,
    DeploymentType,
    ImageSourceId,
    ProjectPlatform,
)
from host.models.host_metadata_crud import (
    ProjectMetadata,
    ProjectStatus,
    RevisionStatus,
    StateTransitionError,
    get_project_platform,
)
from host.models.projects.bootstrap import (
    bootstrap_for_image_external_docker,
    bootstrap_for_image_github,
    bootstrap_for_image_internal_docker,
)
from host.platforms.aws.client import (
    AWS_ECS,
    AwsPlatformArgs,
    get_aws_client,
)
from host.platforms.base.schema import (
    EnvVar,
    PlatformService,
    ResourceId,
)
from host.platforms.base.secrets import Secret

logger = logging.getLogger(__name__)


class ProjectExtended(ProjectMetadata):
    resource: PlatformService | None


class CreateProjectPlatformRequest(BaseModel):
    """User-controllable parts of ProjectPlatform.
    If not specified, defaults are used."""

    deployment_platform: DeploymentPlatformId
    region: str
    aws_account_id: str
    public: bool = False  # Whether to create a publicly accessible load balancer

    @field_validator("aws_account_id")
    @classmethod
    def validate_aws_account_id(cls, v: str) -> str:
        if not v.isdigit() or len(v) != 12:
            raise ValueError("AWS account ID must be exactly 12 digits")
        return v

    def __post_init__(self) -> None:
        if self.deployment_platform not in (
            DeploymentPlatformId[settings.HOST_COMPUTE_PLATFORM],
            DeploymentPlatformId.aws_ecs,
        ):
            raise ValueError(
                f"Invalid deployment platform: {self.deployment_platform}. "
                f"Supported platforms: {settings.HOST_COMPUTE_PLATFORM}, aws_ecs"
            )


class ContainerSpecRequest(BaseModel):
    min_scale: int | None = None
    max_scale: int | None = None
    cpu: int | None = None
    memory_mb: int | None = None

    # WARNING: The following validation logic DOES NOT WORK for AWS ECS (BYOC)
    # deployments. AWS ECS deployments run on Fargate, which has strict
    # requirements for CPU and memory configuration.

    @model_validator(mode="after")
    def validate_min_max_scale(self):
        if self.min_scale is not None and not (1 <= self.min_scale <= 1000):
            raise HTTPException(
                status_code=400,
                detail="Minimum scale (min_scale) must be between 1 and 1000",
            )

        if self.max_scale is not None and not (1 <= self.max_scale <= 1000):
            raise HTTPException(
                status_code=400,
                detail="Maximum scale (max_scale) must be between 1 and 1000",
            )

        if (
            self.min_scale is not None
            and self.max_scale is not None
            and self.min_scale > self.max_scale
        ):
            raise HTTPException(
                status_code=400,
                detail="Minimum scale (min_scale) cannot be greater than maximum scale (max_scale)",
            )

        return self

    @field_validator("cpu")
    def validate_cpu(cls, cpu):
        if cpu is not None and not (1 <= cpu <= 8):
            raise HTTPException(
                status_code=400,
                detail="CPU (cpu) must be between 1 and 8",
            )
        return cpu

    @field_validator("memory_mb")
    def validate_memory(cls, memory_mb):
        if memory_mb is not None:
            # memory must be between 128 MB and 32 GB
            if not (128 <= memory_mb <= 32768):
                raise HTTPException(
                    status_code=400,
                    detail="Memory (memory_mb) must be between 128 and 32768",
                )
            if memory_mb % 128 != 0:
                raise HTTPException(
                    status_code=400,
                    detail="Memory (memory_mb) must be a multiple of 128",
                )
        return memory_mb


class CreateProjectRequest(BaseModel):
    name: str
    lc_hosted: bool = True
    repo_url: str | None = None
    repo_path: str | None = None
    repo_commit: str | None = None  # branch name
    env_vars: list[env_var.LangGraphEnvVar] | None = (
        None  # if None copied from previous revision
    )

    # This field used to be optional, but now it is required
    # We validate this in code instead of pydantic to give better
    # error messages based on context
    host_integration_id: UUID | None = None

    # metadata
    deployment_type: DeploymentType = DeploymentType.prod
    shareable: bool = False
    platform: CreateProjectPlatformRequest | None = None

    image_path: str | None = None
    build_on_push: bool = False
    container_spec: ContainerSpecRequest | None = None

    def model_post_init(self, __context: Any) -> None:
        if self.image_path is None and self.repo_url is None:
            raise ValueError("Either image_path or repo_url must be provided")

        if self.image_path is not None:
            if self.repo_path is not None or self.repo_commit is not None:
                raise ValueError("Cannot set image_path and repo_path or repo_commit")

        if self.repo_url:
            self.repo_path = self.repo_path or host_metadata_crud.DEFAULT_SUBDIR
            self.repo_commit = self.repo_commit or host_metadata_crud.DEFAULT_BRANCH

    @property
    def image_source(self) -> ImageSourceId:
        if self.repo_url is not None:
            return ImageSourceId.github
        elif self.image_path is not None:
            if self.lc_hosted:
                return ImageSourceId.internal_docker
            else:
                return ImageSourceId.external_docker
        else:
            raise NotImplementedError


class PatchProjectRequest(BaseModel):
    display_name: str | None = None
    tool_name: str | None = None
    description: str | None = None
    example_input: dict | None = None
    repo_branch: str | None = None
    build_on_push: bool | None = None
    custom_url: str | None = None


async def generate_access_token(
    auth: AuthInfo,
    project_id: UUID,
) -> dict:
    current_project = await host_metadata_crud.get_project_metadata(
        project_id, auth.tenant_id
    )
    knative_service_name = host_metadata_crud.service_name(
        auth.tenant_id, current_project.name
    )

    # Check that service account exists - will raise error if not
    try:
        await serviceaccounts.get(knative_service_name)
    except googleapiclient.errors.HttpError:
        raise HTTPException(status_code=404, detail="service account not found")
    except Exception as e:
        raise e

    return serviceaccounts.get_access_token(knative_service_name)


class PartialUpdateProjectRequest(BaseModel):
    image_path: str | None = None
    repo_path: str | None = None
    env_vars: list[env_var.LangGraphEnvVar] | None = None
    shareable: bool | None = None
    container_spec: ContainerSpecRequest | None = None

    def model_post_init(self, __context: Any) -> None:
        if self.image_path is not None:
            if self.repo_path is not None:
                raise ValueError("Cannot set image_path and repo_path")


ALPHANUMERIC_AND_DASH_REGEX = re.compile("^[a-zA-Z][a-zA-Z0-9-]+$")
ENV_VAR_NAME_REGEX = re.compile("^[-._a-zA-Z0-9]+$")


def _validate_project_name(name: str) -> None:
    if len(name) > 63:
        raise HTTPException(
            status_code=400, detail="Project name must be 63 characters or less"
        )

    if not ALPHANUMERIC_AND_DASH_REGEX.match(name):
        raise HTTPException(
            status_code=400,
            detail=(
                "Project name must only contain alphanumeric characters and "
                "dashes and must start with an alphabetic character"
            ),
        )

    if name.startswith("-") or name.endswith("-"):
        raise HTTPException(
            status_code=400, detail="Project name cannot start or end with a dash"
        )


def _validate_incoming_env_vars(
    env_vars: list[env_var.LangGraphEnvVar],
    platform: ProjectPlatform,
    remote_reconciled: bool,
) -> None:
    for ev in env_vars:
        if not ENV_VAR_NAME_REGEX.match(ev.name):
            raise HTTPException(
                status_code=400,
                detail=(
                    f"Environment variable name '{ev.name}' is invalid. "
                    "Environment variable names must only consist of "
                    "alphanumeric characters, dashes, underscores, or "
                    "periods. Please use a different name."
                ),
            )

        if not ev.value:
            raise HTTPException(status_code=400, detail="env var value must be set")

        # Reject request if environment variable is reserved.
        #
        # POSTGRES_URI_CUSTOM is not in the list of reserved environment
        # variables. However, it can only be set for BYOC deployments or
        # self-hosted deployments.
        if (
            ev.name in env_var.LANGCHAIN_RESERVED_ENV_VARS
            or ev.name in env_var.LANGCHAIN_RESERVED_SECRETS
            or (ev.name in env_var.ALLOWED_SELF_HOSTED_ENV_VARS)
            and (
                platform.deployment_platform != DeploymentPlatformId.aws_ecs
                and not (settings.IS_SELF_HOSTED or remote_reconciled)
            )
        ):
            raise HTTPException(
                status_code=400,
                detail=(
                    f"Environment variable '{ev.name}' is reserved. "
                    "Please use a different name.",
                ),
            )


async def _validate_aws_env(platform_args: AwsPlatformArgs) -> None:
    """Validate the AWS environment for BYOC deployments."""
    try:
        async with get_aws_client(AWS_ECS, platform_args) as client:
            response = await client.describe_clusters(
                clusters=[
                    f"arn:aws:ecs:{platform_args.region_name}:{platform_args.account_id}:cluster/{settings.ECS_CLUSTER_NAME}"
                ],
            )
            if len(response["clusters"]) == 0 and len(response["failures"]) > 0:
                logger.warning(
                    f"Failed to describe ECS clusters. Failures: {response['failures']}"
                )
                raise HTTPException(
                    status_code=404,
                    detail=(
                        f"ECS cluster '{settings.ECS_CLUSTER_NAME}' not found in region "
                        f"{platform_args.region_name}. Please ensure that the cluster "
                        f"'{settings.ECS_CLUSTER_NAME}' is created in region {platform_args.region_name}."
                    ),
                )
    except botocore.exceptions.ClientError as e:
        # assume role operation failed
        raise HTTPException(
            status_code=403,
            detail=(
                f"Please ensure the IAM role '{settings.AWS_BYOC_ROLE_NAME}' is configured "
                f"correctly and the correct AWS account ID is specified.\n\nRaw error: {str(e)}"
            ),
        )


def _get_default_project_platform(
    platform_request: CreateProjectPlatformRequest | None,
    image_source: ImageSourceId,
    organization_id: UUID,
    deployment_type: DeploymentType,
    tenant_id: UUID,
    remote_reconciled: bool,
) -> ProjectPlatform:
    """Get default Knative platform metadata for creating new projects."""
    if platform_request is not None:
        platform = platform_request.deployment_platform
    else:
        platform = DeploymentPlatformId[settings.HOST_COMPUTE_PLATFORM]

    k8s_cluster = None
    k8s_namespace = None
    if platform in K8S_PLATFORMS:
        if not (settings.IS_SELF_HOSTED or remote_reconciled):
            k8s_cluster = settings.HOSTED_K8S_CLUSTER
        k8s_namespace = str(tenant_id)

    region = settings.HOST_DEPLOYMENT_REGION
    if settings.IS_SELF_HOSTED or remote_reconciled:
        region = ""
    elif platform_request:
        region = platform_request.region

    # get default database host
    if platform == DeploymentPlatformId.aws_ecs:
        database_platform = DatabasePlatformId.aws_rds
    else:
        if settings.IS_SELF_HOSTED or remote_reconciled:
            database_platform = DatabasePlatformId.k8s
        else:
            if deployment_type == DeploymentType.prod:
                database_platform = DatabasePlatformId.gcp_cloud_sql
            else:
                database_platform = DatabasePlatformId.k8s

    return ProjectPlatform(
        deployment_platform=platform,
        k8s_cluster=k8s_cluster,
        k8s_namespace=k8s_namespace,
        image_source=image_source,
        region=region,
        aws_account_id=platform_request.aws_account_id if platform_request else None,
        aws_external_id=str(organization_id)
        if platform == DeploymentPlatformId.aws_ecs
        else None,
        public=platform_request.public if platform_request else False,
        database_platform=database_platform,
    )


def _get_auth_identity(auth: AuthInfo) -> dict:
    if auth.identity_id:
        return {
            "type": "user",
            "identity_id": str(auth.identity_id),
        }
    elif auth.service_identity:
        return {
            "type": "service",
            "name": auth.service_identity,
        }
    else:
        return {}


async def _update_partial_update_project_request(
    auth: AuthInfo,
    project_id: UUID,
    request: PartialUpdateProjectRequest,
) -> PartialUpdateProjectRequest:
    """Return a new PartialUpdateProjectRequest object with fields equal to
    None replaced with values from the latest revision.
    """
    new_request = PartialUpdateProjectRequest(
        image_path=request.image_path,
        repo_path=request.repo_path,
        env_vars=request.env_vars,
        shareable=request.shareable,
        container_spec=request.container_spec,
    )

    # populate null request fields from latest revision
    latest_revision = await host_metadata_crud.get_latest_revision_metadata(project_id)
    if latest_revision:
        if new_request.image_path is None:
            new_request.image_path = latest_revision.image_path

        if new_request.repo_path is None:
            new_request.repo_path = latest_revision.repo_path

        if new_request.shareable is None:
            project = await host_metadata_crud.get_project_metadata(
                project_id, auth.tenant_id
            )
            new_request.shareable = project.metadata.get("shareable", False)

        if new_request.env_vars is None:
            latest_revision_secrets = await get_project_secrets(project_id)
            new_request.env_vars = [
                env_var.LangGraphEnvVar(
                    name=name, value=value, type=env_var.LangGraphEnvVarType.secret
                )
                for name, value in latest_revision_secrets.items()
                if name not in env_var.LANGCHAIN_RESERVED_ENV_VARS
            ]

    return new_request


async def create_project(auth: AuthInfo, request: CreateProjectRequest) -> UUID:
    logger.info(f"Creating project in tenant: {auth.tenant_id}")
    remote_reconciled = (
        auth.tenant_config.organization_config.langgraph_remote_reconciler_enabled
    )

    # determine platform and secrets store type
    platform = _get_default_project_platform(
        request.platform,
        request.image_source,
        auth.organization_id,
        request.deployment_type,
        auth.tenant_id,
        remote_reconciled,
    )
    _validate_project_name(request.name)
    _validate_incoming_env_vars(
        request.env_vars or [],
        platform,
        remote_reconciled,
    )
    if platform.deployment_platform == DeploymentPlatformId.aws_ecs:
        await _validate_aws_env(AwsPlatformArgs.from_platform(platform))

    # overwrite the deployment type if the project is remote reconciled
    if remote_reconciled and request.deployment_type == DeploymentType.dev_free:
        request.deployment_type = DeploymentType.dev

    readme_markdown = None
    # perform validation on github projects
    if platform.image_source == ImageSourceId.github:
        ref_sha, readme_markdown = await bootstrap_for_image_github(
            auth,
            request.host_integration_id,
            cast(str, request.repo_url),
            request.repo_path,
            request.repo_commit,
        )
    elif platform.image_source == ImageSourceId.internal_docker:
        ref_sha = await bootstrap_for_image_internal_docker(
            request.image_path,
        )
    elif platform.image_source == ImageSourceId.external_docker:
        ref_sha = await bootstrap_for_image_external_docker(auth)

    # add secrets
    secrets_to_add = env_var.get_secrets_from_incoming_env_vars(request.env_vars or [])
    if settings.IS_SELF_HOSTED:
        secrets_to_add.append(
            Secret(
                name="LANGGRAPH_CLOUD_LICENSE_KEY",
                value=settings.LANGGRAPH_CLOUD_LICENSE_KEY,
            )
        )

    # creating the project and revision metadata transactionally
    create_project_resp = await host_metadata_crud.create_project_metadata(
        auth,
        host_metadata_crud.CreateProjectMetadataRequest(
            name=request.name,
            lc_hosted=request.lc_hosted,
            repo_url=request.repo_url,
            repo_path=request.repo_path,
            repo_commit=request.repo_commit,
            repo_commit_sha=ref_sha,
            host_integration_id=request.host_integration_id,
            deployment_type=request.deployment_type,
            platform=platform,
            shareable=request.shareable,
            image_path=request.image_path,
            build_on_push=request.build_on_push,
            created_by=_get_auth_identity(auth),
            readme_markdown=readme_markdown,
            remote_reconciled=auth.tenant_config.organization_config.langgraph_remote_reconciler_enabled,
            secrets={secret.name: secret.value for secret in secrets_to_add},
            container_spec=request.container_spec.model_dump()
            if request.container_spec
            else {},
            database_platform=platform.database_platform,
        ),
    )
    project_metadata = create_project_resp.project_metadata
    revision_metadata = create_project_resp.revision_metadata
    logger.info(f"Created project {project_metadata.id} in tenant {auth.tenant_id}")

    try:
        # create cloud resources
        knative_service_name = host_metadata_crud.service_name(
            auth.tenant_id, request.name
        )
        labels = {
            "service": knative_service_name,
            "ls_organization_id": str(auth.organization_id),
            "ls_tenant_id": str(auth.tenant_id),
            "ls_project_id": str(project_metadata.id),
            "region": settings.HOST_DEPLOYMENT_REGION,
            "env": settings.DD_ENV,
        }

        # create service account
        if platform.image_source == ImageSourceId.internal_docker:
            await serviceaccounts.create(knative_service_name)
            await groups.add_member(serviceaccounts.get_name(knative_service_name))
            await artifact_registry.create_repository(knative_service_name, labels)

        # set revision status
        assert revision_metadata is not None
        if platform.image_source == ImageSourceId.github:
            await host_metadata_crud.set_revision_status_awaiting_build(
                auth, revision_metadata.id
            )
        elif platform.image_source == ImageSourceId.external_docker:
            await host_metadata_crud.set_revision_status_awaiting_deploy(
                revision_metadata.id, None, auth.tenant_id
            )

        return project_metadata.id

    except Exception as e:
        if revision_metadata:
            await host_metadata_crud.set_revision_status_create_failed(
                auth, revision_metadata.id, str(e)
            )
        raise e


async def create_revision(
    auth: AuthInfo,
    project_id: UUID,
    incoming_partial_update_req: PartialUpdateProjectRequest,
) -> UUID:
    # TODO: set only one update to occur at once with a db level flag in a transaction
    logger.info(f"Updating project in tenant: {auth.tenant_id}")

    current_project = await host_metadata_crud.get_project_metadata(
        project_id, auth.tenant_id
    )
    platform = get_project_platform(current_project)

    # populate null request fields from latest revision
    incoming_partial_update_req = await _update_partial_update_project_request(
        auth, project_id, incoming_partial_update_req
    )

    # perform request validation
    _validate_incoming_env_vars(
        incoming_partial_update_req.env_vars or [],
        platform,
        current_project.remote_reconciled,
    )
    if platform.deployment_platform == DeploymentPlatformId.aws_ecs:
        await _validate_aws_env(AwsPlatformArgs.from_platform(platform))

    secrets_to_add = env_var.get_secrets_from_incoming_env_vars(
        incoming_partial_update_req.env_vars or []
    )

    readme_markdown = None
    if platform.image_source == ImageSourceId.github:
        ref_sha, readme_markdown = await bootstrap_for_image_github(
            auth,
            current_project.host_integration_id,
            cast(str, current_project.repo_url),
            incoming_partial_update_req.repo_path,
            current_project.repo_branch,
        )
    elif platform.image_source == ImageSourceId.internal_docker:
        ref_sha = await bootstrap_for_image_internal_docker(
            incoming_partial_update_req.image_path,
        )
    elif platform.image_source == ImageSourceId.external_docker:
        ref_sha = await bootstrap_for_image_external_docker(auth)

    container_spec = current_project.container_spec
    if incoming_partial_update_req.container_spec:
        container_spec = incoming_partial_update_req.container_spec.model_dump()

    project_update_response = await host_metadata_crud.partial_update_project_metadata(
        auth,
        current_project,
        incoming_partial_update_req.repo_path,
        current_project.repo_branch,
        ref_sha,
        incoming_partial_update_req.image_path,
        _get_auth_identity(auth),
        container_spec,
        readme_markdown,
        {secret.name: secret.value for secret in secrets_to_add},
        incoming_partial_update_req.shareable or False,
    )
    new_revision_metadata = project_update_response.revision_metadata
    logger.info(
        f"Updated project {project_id} and created new revision {new_revision_metadata.id} in tenant {auth.tenant_id}"
    )

    try:
        if platform.image_source == ImageSourceId.github:
            await host_metadata_crud.set_revision_status_awaiting_build(
                auth, new_revision_metadata.id
            )
        elif platform.image_source in [
            ImageSourceId.internal_docker,
            ImageSourceId.external_docker,
        ]:
            await host_metadata_crud.set_revision_status_awaiting_deploy(
                new_revision_metadata.id, None, auth.tenant_id
            )

        return project_id

    except Exception as e:
        await host_metadata_crud.set_revision_status_create_failed(
            auth, new_revision_metadata.id, str(e)
        )
        raise HTTPException(status_code=500, detail=str(e))


async def get_platform_service(project_metadata: ProjectMetadata) -> PlatformService:
    """This function returns the PlatformService for a project without
    accessing cloud resources (e.g. GCP) or deployment infrastructure
    (e.g. K8s).

    This function can be used where latency/performance of an operation
    is critical (e.g. listing all projects) or where cloud resources
    cannot be accessed (e.g. remote reconciled projects).
    """
    service_name = host_metadata_crud.service_name(
        project_metadata.tenant_id, project_metadata.name
    )

    latest_revision_metadata = await host_metadata_crud.get_latest_revision_metadata(
        project_metadata.id
    )
    latest_active_revision_metadata = (
        await host_metadata_crud.get_latest_revision_metadata(
            project_metadata.id, RevisionStatus.DEPLOYED.value.status
        )
    )

    latest_revision = None
    if latest_revision_metadata:
        latest_revision = await revisions.get_platform_revision(
            latest_revision_metadata
        )

        # set env vars for latest revision so they can be displayed in the UI
        secret = await get_project_secrets(
            project_metadata.id, project_metadata.tenant_id
        )
        latest_revision.env_vars = [
            EnvVar(name=name, value=value, value_from="secret")
            for name, value in secret.items()
            if name not in env_var.LANGCHAIN_RESERVED_ENV_VARS
        ]

    latest_active_revision = None
    url = None
    if latest_active_revision_metadata:
        latest_active_revision = await revisions.get_platform_revision(
            latest_active_revision_metadata
        )

        # only show the URL if the custom URL is set
        if project_metadata.custom_url:
            url = project_metadata.custom_url
        else:
            platform = get_project_platform(project_metadata)
            if not project_metadata.remote_reconciled:
                if platform.deployment_platform == DeploymentPlatformId.k8s:
                    url = f"https://{service_name}.{settings.HOSTED_K8S_NAMESPACE}.{settings.HOSTED_K8S_ROOT_DOMAIN}"
                elif platform.deployment_platform in [
                    DeploymentPlatformId.k8s_vanilla,
                    DeploymentPlatformId.k8s_operator,
                ]:
                    # Use internal DNS if no root domain is set
                    if not settings.HOSTED_K8S_ROOT_DOMAIN:
                        url = f"http://{service_name}:8000"
                    else:
                        url = (
                            f"https://{service_name}.{settings.HOSTED_K8S_ROOT_DOMAIN}"
                        )

    return PlatformService(
        id=ResourceId(type="services", name=service_name),
        url=url,
        latest_revision=latest_revision,
        latest_active_revision=latest_active_revision,
    )


async def get_project(
    project_id: UUID, tenant_id: UUID | None = None
) -> ProjectExtended:
    project_metadata = await host_metadata_crud.get_project_metadata(
        project_id, tenant_id
    )

    service = await get_platform_service(project_metadata)

    return ProjectExtended(
        **(project_metadata.model_dump()),
        resource=service,
    )


async def list_projects(
    params: ListProjectsQueryParams,
    tenant_id: UUID | None = None,
    tag_value_id: list[UUID] | None = None,
) -> tuple[list[ProjectExtended], int]:
    base_projects, next_offset = await host_metadata_crud.list_projects_metadata(
        params, tenant_id, tag_value_id
    )

    # construct retrieval tasks to run in parallel
    tasks = []
    for project in base_projects:
        tasks.append(get_platform_service(project))

    services = await asyncio.gather(*tasks)

    return [
        ProjectExtended(
            **(base_project.model_dump()),
            resource=service,
        )
        for base_project, service in zip(base_projects, services)
    ], next_offset


async def delete_project(
    auth: AuthInfo, project_id: UUID, force: bool = False
) -> Optional[ProjectExtended]:
    if force:
        # Hard delete row from database. This should only be done after
        # all cloud resources have been deleted.
        try:
            await host_metadata_crud.get_project_metadata(project_id, auth.tenant_id)
            # if an exception is not thrown, then the project has not been soft deleted
            raise HTTPException(status_code=400, detail="Cannot force delete project")
        except HTTPException:
            # project has been "soft deleted" (status set to AWAITING_DELETE)
            await host_metadata_crud.delete_project_metadata(auth, project_id)
            return None
    else:
        # Mark the project as deleted (set status to AWAITING_DELETE). The
        # reconciler will schedule deletion of cloud resources and project
        # metadata.
        project_metadata = await host_metadata_crud.set_project_status(
            project_id, ProjectStatus.AWAITING_DELETE, auth.tenant_id
        )
        return ProjectExtended(
            **(project_metadata.model_dump()),
            resource=None,
        )


async def patch_project(
    project_id: UUID,
    payload: PatchProjectRequest,
    tenant_id: UUID | None = None,
) -> ProjectExtended:
    """Patch project metadata."""
    # validate request body
    if payload.custom_url:
        parsed_url = urlparse(payload.custom_url)
        if not bool(parsed_url.scheme and parsed_url.netloc):
            raise HTTPException(
                status_code=400,
                detail="Invalid Custom URL. Specify scheme (e.g. http://) and domain (e.g. langgraph.com).",
            )

        # trim trailing slash if present
        payload.custom_url = payload.custom_url.rstrip("/")

    await host_metadata_crud.update_project_metadata(
        project_id,
        tenant_id,
        repo_branch=payload.repo_branch,
        build_on_push=payload.build_on_push,
        tool_name=payload.tool_name,
        display_name=payload.display_name,
        description=payload.description,
        example_input=payload.example_input,
        custom_url=payload.custom_url,
    )
    return await get_project(project_id, tenant_id)


async def set_project_status(
    project_id: UUID,
    status: str,
    tenant_id: UUID | None = None,
) -> ProjectExtended:
    """Update status of a project.

    This method is used for managing the deployment (state machine) of a project.
    """
    try:
        project_metadata = await host_metadata_crud.set_project_status(
            project_id, ProjectStatus[status], tenant_id
        )
    except StateTransitionError as e:
        raise HTTPException(
            status_code=400,
            detail=f"State transition error: Cannot update project status from '{e.old_status}' to '{e.new_status}'",
        )

    return ProjectExtended(
        **(project_metadata.model_dump()),
        resource=None,
    )


async def get_project_secrets(
    project_id: UUID, tenant_id: UUID | None = None
) -> dict[str, str]:
    """Retrieve secrets for a project.

    First, attempt to retrieve secrets from the database. If None is returned,
    the project has not been migrated to store secrets in the database yet.
    """
    secrets_ = await host_metadata_crud.get_project_secrets(project_id, tenant_id)
    if secrets_ is None:
        project_metadata = await host_metadata_crud.get_project_metadata(
            project_id, tenant_id
        )
        service_name = host_metadata_crud.service_name(
            project_metadata.tenant_id, project_metadata.name
        )
        platform = host_metadata_crud.get_project_platform(project_metadata)
        secrets_ = await secrets.get_secret_values(service_name, platform)

    return secrets_


async def get_project_usage(
    project_id: UUID,
    tenant_id: UUID,
    start_time: Optional[datetime],
    end_time: Optional[datetime],
) -> ProjectUsageResponse:
    """Get usage for a project."""
    if start_time is None:
        # set start_time to the beginning of the current month
        start_time = datetime.now(tz=timezone.utc).replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )

    if end_time is None:
        # set end_time to the current time
        end_time = datetime.now(tz=timezone.utc)

    usage = await host_project_usage.get_project_usage(
        project_id=project_id,
        tenant_id=tenant_id,
        start_time=start_time,
        end_time=end_time,
    )

    return ProjectUsageResponse(
        runs_executed=usage.get("runs_executed"),
        nodes_executed=usage.get("nodes_executed"),
        standby_minutes=usage.get("standby_minutes"),
    )
