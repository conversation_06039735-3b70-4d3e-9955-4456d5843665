from typing import Optional

from lc_config.settings import (
    ENV_FILE_PATH,
    SECRETS_DIR_PATH,
    SharedSettings,
    shared_settings,
)
from pydantic import field_validator
from pydantic_core.core_schema import ValidationInfo


class Settings(SharedSettings):
    """Settings for the host application."""

    PROJECT_NAME: str = "LangServeHost"
    GCP_PROJECT_ID: str = "langchain-test-387119"
    GCP_PROJECT_NUMBER: str = "570601939772"
    # Google Group <NAME_EMAIL>
    SA_GROUP_ID: str = "037m2jsg28fgzsf"
    # VPC that Cloud SQL instances will be connected to
    GCP_HOST_VPC: str = "langsmith-network"
    HOST_COMPUTE_PLATFORM: str = "k8s_operator"
    ARTIFACT_REGISTRY_LOCATION: str = "us-central1"

    # GCP region for the Cloud SQL instance and K8s deployment.
    # If the Cloud SQL instance quota limit is close to being
    # exceeded (e.g. 900 instances), the HOST_DEPLOYMENT_REGION
    # and HOSTED_K8S_CLUSTER environment variables must be updated
    # to point to a different region.
    HOST_DEPLOYMENT_REGION: str = "us-central1"

    # K8S settings
    HOSTED_K8S_CLUSTER: str = "langgraph-cloud-test"
    HOSTED_K8S_ENDPOINT_URLS: dict[str, str] = {
        "langgraph-cloud-test": "https://**********:443",  # us-central1
        "langgraph-cloud-us-west1": "https://*************:443",  # us-west1
    }
    HOSTED_K8S_ROOT_DOMAIN: str = ""
    HOSTED_K8S_NAMESPACE: str = "default"
    HOSTED_K8S_CA_CERT_PATHS: dict[str, str] = {
        "langgraph-cloud-test": "",
        "langgraph-cloud-us-west1": "",
    }
    HOSTED_K8S_DEPLOYMENT_READY_TIMEOUT_SECONDS: int = 600

    # Default to prod to match langchain default behavior - this will not work
    # on local test environments since cloudrun cannot access the local test
    # environment. Set to LANGCHAIN_ENDPOINT if not specified.
    HOST_LANGCHAIN_API_ENDPOINT: str = "https://api.smith.langchain.com"

    @field_validator("HOST_LANGCHAIN_API_ENDPOINT", mode="before")
    @classmethod
    def set_langchain_platform_endpoint(
        cls, v: Optional[str], info: ValidationInfo
    ) -> str:
        if v:
            return v
        return info.data.get("LANGCHAIN_ENDPOINT")

    HOSTED_LANGSERVE_PRIVATE_REPOS_ENABLED: bool = True
    GITHUB_APP_PUBLIC_LINK: str = ""
    GITHUB_CLIENT_ID: str = ""
    GITHUB_CLIENT_SECRET: str = ""
    GITHUB_STATE_JWT_SECRET: str = ""
    GITHUB_WEBHOOK_SECRET: str = ""

    MAX_STORED_PROJECT_README_LENGTH: int = 1024 * 100  # 100KB limit

    # AWS settings
    # We expect these roles/values/tags to be created in customers account as a prerequisite using our terraform module.
    # TODO: Autocreate these roles when user "connects" their account
    AWS_BYOC_ROLE_NAME: str = "LangGraphCloudBYOCRole"
    AWS_PROFILE: str = "HostBackendAccess"
    ECS_TASK_EXECUTION_ROLE_NAME: str = "LangGraphCloudECSTaskExecutionRole"
    LANGGRAPH_CLOUD_DB_SUBNET_GROUP_NAME: str = "langgraph-cloud-db-subnet-group"
    ECS_CLUSTER_NAME: str = "langgraph-cloud-cluster"
    ECS_LOG_GROUP_NAME: str = "/aws/ecs/langgraph-cloud"
    LANGGRAPH_CLOUD_ENABLED_TAG: str = "langgraph-cloud-enabled"
    LANGGRAPH_CLOUD_PRIVATE_SUBNET_TAG: str = "langgraph-cloud-private"
    LANGGRAPH_CLOUD_SERVICE_SECURITY_GROUP_NAME: str = "langgraph-cloud-service-sg"
    LANGGRAPH_CLOUD_LB_SECURITY_GROUP_NAME: str = "langgraph-cloud-lb-sg"
    LANGGRAPH_CLOUD_CLOUD_MAP_NAMESPACE_REDIS: str = "langgraph-cloud-redis"
    LANGGRAPH_CLOUD_DEFAULT_CONCURRENCY_TARGET: int = 10
    LANGGRAPH_CLOUD_INGRESS_ENABLED: bool = True

    # For self-hosted host-backend deployments
    LANGGRAPH_CLOUD_LICENSE_KEY: str | None = None

    # Enable/disable email notifications for unused projects
    ENABLE_UNUSED_PROJECT_NOTIFICATION_EMAILS: bool = False


LANGCHAIN_ENV = shared_settings.LANGCHAIN_ENV
settings = Settings(  # type: ignore
    LANGCHAIN_ENV=LANGCHAIN_ENV,
    _env_file=ENV_FILE_PATH,  # type: ignore
    _secrets_dir=SECRETS_DIR_PATH,  # type: ignore
)
