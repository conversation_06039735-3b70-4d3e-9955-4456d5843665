import logging
from typing import Annotated, Optional
from uuid import UUID

from app.api import deps
from app.api.auth.schemas import Permissions, ServiceIdentity
from app.api.deps import x_service_authorize
from app.schemas import CustomChartsSection
from fastapi import Body, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.params import Query
from fastapi.responses import ORJSONResponse
from lc_database import api_router

from host.api import pagination, schemas
from host.models.host_project_dataset_links import (
    CreateProjectDatasetLinkRequest,
    link_to_dataset,
)
from host.models.host_project_prices import (
    CreateProjectPriceRequest,
    create_project_price,
    get_project_price,
)
from host.models.playgrounds import get_project_playgrounds
from host.models.projects import (
    CreateProjectRequest,
    PartialUpdateProjectRequest,
    PatchProjectRequest,
    ProjectExtended,
    create_project,
    create_revision,
    delete_project,
    generate_access_token,
    get_project,
    get_project_secrets,
    get_project_usage,
    list_projects,
    patch_project,
    set_project_status,
)
from host.models.projects.metrics import get_project_metrics
from host.models.revisions import (
    RevisionExtended,
    deploy_revision,
    get_revision,
    interrupt_revision,
    list_build_logs,
    list_deploy_logs,
    list_revisions,
)

logger = logging.getLogger(__name__)
router = api_router.TrailingSlashRouter()
router_internal = api_router.TrailingSlashRouter()


async def _check_feature_flag(auth: deps.AuthInfo, allow_disabled: bool = False):
    if (
        not auth.tenant_config.organization_config.can_use_langgraph_cloud
        and not allow_disabled
    ):
        raise HTTPException(
            status_code=403,
            detail="LangGraph Platform is not enabled for this organization",
        )


@router.post("")
async def create_project_endpoint(
    payload: CreateProjectRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_CREATE)),
) -> ProjectExtended:
    """Create a new project."""
    await _check_feature_flag(auth)
    project_id = await create_project(auth, payload)
    return await get_project(project_id, auth.tenant_id)


@router.get("/{project_id}/prices")
async def get_project_price_endpoint(
    project_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_CREATE)),
):
    await _check_feature_flag(auth)
    return await get_project_price(project_id)


@router.post("/{project_id}/prices")
async def create_project_price_endpoint(
    project_id: UUID,
    payload: CreateProjectPriceRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_CREATE)),
):
    await _check_feature_flag(auth)
    return await create_project_price(auth, project_id, payload)


@router.post("/{project_id}/access_token")
async def create_access_token_endpoint(
    project_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_CREATE)),
) -> ORJSONResponse:
    """Create a new access token for client."""
    await _check_feature_flag(auth)
    return await generate_access_token(auth, project_id)


@router.post("/{project_id}/deploy_logs")
async def list_deploy_logs_endpoint(
    project_id: UUID,
    payload: schemas.LogsRequest = Body(default=schemas.LogsRequest()),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
) -> schemas.LogsResponse:
    """List deploy logs for a project (across all revisions)."""
    await _check_feature_flag(auth)
    return await list_deploy_logs(
        auth=auth,
        project_id=project_id,
        revision_id=None,
        start_time=payload.start_time,
        end_time=payload.end_time,
        order=payload.order,
        limit=payload.limit or 50,
        offset=payload.offset,
        query=payload.query,
        level=payload.level,
    )


@router.patch("/{project_id}")
async def patch_project_endpoint(
    project_id: UUID,
    payload: PatchProjectRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_UPDATE)),
) -> ProjectExtended:
    """Patch a project."""
    await _check_feature_flag(auth)
    return await patch_project(project_id, payload, auth.tenant_id)


@router_internal.patch("/{project_id}", dependencies=[Depends(x_service_authorize)])
async def patch_project_endpoint_internal(
    project_id: UUID,
    payload: PatchProjectRequest,
) -> ProjectExtended:
    """Patch a project."""
    return await patch_project(project_id, payload)


@router.get("/{project_id}")
async def get_project_endpoint(
    project_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
) -> ProjectExtended:
    """Get project by ID for current tenant."""
    await _check_feature_flag(auth)
    return await get_project(project_id, auth.tenant_id)


@router_internal.get("/{project_id}", dependencies=[Depends(x_service_authorize)])
async def get_project_endpoint_internal(project_id: UUID) -> ProjectExtended:
    """Get project by ID."""
    return await get_project(project_id)


@router.get("")
async def list_projects_endpoint(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
    params: schemas.ListProjectsQueryParams = Depends(),
    tag_value_id: Annotated[list[UUID] | None, Query()] = None,
) -> ORJSONResponse:
    """List projects for current tenant."""
    await _check_feature_flag(auth)

    # list projects for current tenant
    projects, next_offset = await list_projects(params, auth.tenant_id, tag_value_id)

    return ORJSONResponse(
        content=jsonable_encoder(projects),
        # X-Pagination-Total is supposed to be the next offset value. The
        # name is misleading.
        headers={"X-Pagination-Total": str(next_offset)},
    )


@router_internal.get("", dependencies=[Depends(x_service_authorize)])
async def list_projects_endpoint_internal(
    params: schemas.ListProjectsQueryParams = Depends(),
    tag_value_id: Annotated[list[UUID] | None, Query()] = None,
) -> ORJSONResponse:
    """List projects for all tenants."""
    projects, next_offset = await list_projects(params, None, tag_value_id)

    return ORJSONResponse(
        content=jsonable_encoder(projects),
        # X-Pagination-Total is supposed to be the next offset value. The
        # name is misleading.
        headers={"X-Pagination-Total": str(next_offset)},
    )


@router.delete("/{project_id}")
async def delete_project_endpoint(
    project_id: UUID,
    force: bool = Query(default=False),
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            permission=Permissions.DEPLOYMENTS_DELETE,
            allowed_services=[ServiceIdentity.RETOOL],
        )
    ),
) -> Optional[ProjectExtended]:
    """Delete a project for current tenant."""
    await _check_feature_flag(auth)
    return await delete_project(auth, project_id, force)


@router_internal.delete("/{project_id}")
async def delete_project_endpoint_internal(
    project_id: UUID,
    force: bool = Query(default=False),
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            permission=Permissions.DEPLOYMENTS_DELETE,
            allowed_services=[ServiceIdentity.RETOOL, ServiceIdentity.UNSPECIFIED],
            allow_disabled=True,
        )
    ),
) -> Optional[ProjectExtended]:
    """Delete a project."""
    # Some tenants are disabled and don't have LangGraph Platform enabled
    # (for whatever reason), but we should allow the reconciler to delete
    # these projects.
    return await delete_project(auth, project_id, force)


@router.post("/{project_id}/revisions")
async def create_revision_endpoint(
    project_id: UUID,
    payload: PartialUpdateProjectRequest,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            permission=Permissions.DEPLOYMENTS_UPDATE,
            allowed_services=[ServiceIdentity.UNSPECIFIED],
        )
    ),
) -> ProjectExtended:
    """Create a new revision for a project."""
    await _check_feature_flag(auth)
    project_id = await create_revision(auth, project_id, payload)
    return await get_project(project_id, auth.tenant_id)


@router.get("/{project_id}/revisions")
async def list_revisions_for_project_endpoint(
    project_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
    params: schemas.ListRevisionsQueryParams = Depends(),
) -> ORJSONResponse:
    """List revisions for a project for current tenant."""
    await _check_feature_flag(auth)
    revisions, count = await list_revisions(params, project_id, auth.tenant_id)
    return ORJSONResponse(
        content=jsonable_encoder(revisions), headers={"X-Pagination-Total": str(count)}
    )


@router.get("/{project_id}/revisions/{revision_id}")
async def get_revision_for_project_endpoint(
    project_id: UUID,
    revision_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
) -> RevisionExtended:
    """Get revision for a project."""
    await _check_feature_flag(auth)
    return await get_revision(revision_id, auth.tenant_id)


@router.get("/{project_id}/playgrounds")
async def list_playgrounds_for_project_endpoint(
    project_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
    params: pagination.PaginationQueryParams = Depends(),
) -> ORJSONResponse:
    """Get available playgrounds for a project."""
    await _check_feature_flag(auth)
    playgrounds, count = await get_project_playgrounds(auth, project_id)
    return ORJSONResponse(
        content=jsonable_encoder(playgrounds),
        headers={"X-Pagination-Total": str(count)},
    )


@router.post("/{project_id}/revisions/{revision_id}/interrupt")
async def interrupt_revision_endpoint(
    project_id: UUID,
    revision_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_UPDATE)),
) -> ORJSONResponse:
    """Interrupt a revision."""
    await _check_feature_flag(auth)
    return await interrupt_revision(auth, revision_id)


@router.post("/{project_id}/revisions/{revision_id}/deploy")
async def deploy_revision_endpoint(
    project_id: UUID,
    revision_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_UPDATE)),
) -> ORJSONResponse:
    """(Re)deploy a revision.

    Note: This API does not create a new revision.
    """
    await _check_feature_flag(auth)
    return await deploy_revision(auth, project_id, revision_id)


@router.post("/{project_id}/revisions/{revision_id}/build_logs")
async def list_revision_build_logs_endpoint(
    project_id: UUID,
    revision_id: UUID,
    payload: schemas.LogsRequest = Body(default=schemas.LogsRequest()),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
) -> schemas.LogsResponse:
    """List build logs for a specific revision."""
    await _check_feature_flag(auth)
    return await list_build_logs(
        auth=auth,
        revision_id=revision_id,
        start_time=payload.start_time,
        end_time=payload.end_time,
        order=payload.order,
        limit=payload.limit or 50,
        offset=payload.offset,
    )


@router.post("/{project_id}/revisions/{revision_id}/deploy_logs")
async def list_revision_deploy_logs_endpoint(
    project_id: UUID,
    revision_id: UUID,
    payload: schemas.LogsRequest = Body(default=schemas.LogsRequest()),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
) -> schemas.LogsResponse:
    """List deploy logs for a specific revision."""
    await _check_feature_flag(auth)
    return await list_deploy_logs(
        auth=auth,
        project_id=project_id,
        revision_id=revision_id,
        start_time=payload.start_time,
        end_time=payload.end_time,
        order=payload.order,
        limit=payload.limit or 50,
        offset=payload.offset,
        query=payload.query,
        level=payload.level,
    )


@router.post("/{project_id}/dataset_links")
async def create_dataset_link(
    project_id: UUID,
    payload: CreateProjectDatasetLinkRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_UPDATE)),
):
    """Link a host project to an owned public dataset."""
    await _check_feature_flag(auth)
    return await link_to_dataset(auth, project_id, payload)


@router.patch("/{project_id}/status")
async def patch_project_status_endpoint(
    project_id: UUID,
    status: str = Body(..., embed=True),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_UPDATE)),
) -> ProjectExtended:
    """Update status of a project for current tenant."""
    await _check_feature_flag(auth)
    return await set_project_status(project_id, status, auth.tenant_id)


@router_internal.patch(
    "/{project_id}/status", dependencies=[Depends(x_service_authorize)]
)
async def patch_project_status_endpoint_internal(
    project_id: UUID,
    status: str = Body(..., embed=True),
) -> ProjectExtended:
    """Update status of a project."""
    return await set_project_status(project_id, status)


@router.get("/{project_id}/secrets", include_in_schema=False)
async def get_project_secrets_endpoint(
    project_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_UPDATE)),
) -> ORJSONResponse:
    """Get secrets for a project for current tenant."""
    await _check_feature_flag(auth)
    return await get_project_secrets(project_id, auth.tenant_id)


@router_internal.get(
    "/{project_id}/secrets", dependencies=[Depends(x_service_authorize)]
)
async def get_project_secrets_endpoint_internal(
    project_id: UUID,
) -> ORJSONResponse:
    """Get secrets for a project."""
    return await get_project_secrets(project_id)


@router.get("/{project_id}/usage")
async def get_project_usage_endpoint(
    project_id: UUID,
    params: schemas.ProjectUsageQueryParams = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
) -> schemas.ProjectUsageResponse:
    await _check_feature_flag(auth)
    return await get_project_usage(
        project_id=project_id,
        tenant_id=auth.tenant_id,
        start_time=params.start_time,
        end_time=params.end_time,
    )


@router.get("/{project_id}/metrics")
async def get_project_metrics_endpoint(
    project_id: UUID,
    params: schemas.ProjectMetricsQueryParams = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DEPLOYMENTS_READ)),
) -> CustomChartsSection:
    await _check_feature_flag(auth)
    return await get_project_metrics(
        project_id=project_id,
        tenant_id=auth.tenant_id,
        start_time=params.start_time,
        end_time=params.end_time,
        step=params.step,
    )
