import re
from datetime import datetime
from enum import Enum
from typing import Literal, Optional

from fastapi import HTTPException
from pydantic import BaseModel, field_validator

from host.api.pagination import PaginationQueryParams
from host.platforms.base.logging import LOG_LEVEL

PROMETHEUS_DURATION_REGEX = re.compile(r"^(\d+)([smhdw])$")


class LogStreamRequest(BaseModel):
    """Request body for log streaming endpoint."""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class LogsRequest(BaseModel):
    """Request body for list logs endpoint."""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    order: Literal["asc", "desc"] = "asc"
    limit: Optional[int] = 50
    offset: Optional[str] = None
    query: Optional[str] = None
    level: Optional[LOG_LEVEL] = None


class LogsResponse(BaseModel):
    """Response body for list logs endpoint."""

    logs: list[dict] = []
    next_offset: Optional[str] = None


class ListProjectsQueryParams(PaginationQueryParams):
    """Query parameters for GET /projects endpoint."""

    name_contains: Optional[str] = None
    status: Optional[str] = None
    remote_reconciled: Optional[bool] = None


class ListRevisionsQueryParams(PaginationQueryParams):
    """Query parameters for GET /projects/{project_id}/revisions
    and GET /revisions endpoints.
    """

    status: Optional[str] = None
    remote_reconciled: Optional[bool] = None


class ProjectMetricsGroup(str, Enum):
    queue_worker = "queue-worker"
    api_server = "api-server"


class ProjectUsageQueryParams(BaseModel):
    """Query parameters for GET /projects/<project_id>/usage endpoint."""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class ProjectUsageResponse(BaseModel):
    """Response body for GET /projects/<project_id>/usage endpoint."""

    runs_executed: Optional[int] = None
    nodes_executed: Optional[int] = None
    standby_minutes: Optional[int] = None


class ProjectMetricsQueryParams(BaseModel):
    """Query parameters for GET /projects/<project_id>/metrics endpoint."""

    start_time: datetime | None = None
    end_time: datetime | None = None
    step: str | None = None

    @field_validator("step")
    @classmethod
    def validate_step(cls, step: str | None):
        if step is not None and not PROMETHEUS_DURATION_REGEX.match(step):
            raise HTTPException(
                status_code=400,
                detail="Invalid PromQL duration format. Use format like: 1s, 1m, 1h, 1d, 1w.",
            )
        return step
