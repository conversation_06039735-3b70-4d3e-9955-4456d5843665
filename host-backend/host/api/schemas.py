from datetime import datetime
from typing import Literal, Optional

from pydantic import BaseModel

from host.api.pagination import PaginationQueryParams
from host.platforms.base.logging import LOG_LEVEL


class LogStreamRequest(BaseModel):
    """Request body for log streaming endpoint."""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class LogsRequest(BaseModel):
    """Request body for list logs endpoint."""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    order: Literal["asc", "desc"] = "asc"
    limit: Optional[int] = 50
    offset: Optional[str] = None
    query: Optional[str] = None
    level: Optional[LOG_LEVEL] = None


class LogsResponse(BaseModel):
    """Response body for list logs endpoint."""

    logs: list[dict] = []
    next_offset: Optional[str] = None


class ListProjectsQueryParams(PaginationQueryParams):
    """Query parameters for GET /projects endpoint."""

    name_contains: Optional[str] = None
    status: Optional[str] = None
    remote_reconciled: Optional[bool] = None


class ListRevisionsQueryParams(PaginationQueryParams):
    """Query parameters for GET /projects/{project_id}/revisions
    and GET /revisions endpoints.
    """

    status: Optional[str] = None
    remote_reconciled: Optional[bool] = None


class ProjectUsageQueryParams(BaseModel):
    """Query parameters for GET /project/<project_id>/usage endpoint."""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class ProjectUsageResponse(BaseModel):
    """Response body for GET /project/<project_id>/usage endpoint."""

    runs_executed: Optional[int] = None
    nodes_executed: Optional[int] = None
    standby_minutes: Optional[int] = None
