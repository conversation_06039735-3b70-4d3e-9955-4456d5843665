import uuid
from datetime import datetime, timezone
from unittest import TestCase

from app.schemas import (
    CustomChartsDataPoint,
    CustomChartSeries,
    CustomChartType,
    HostProjectChartMetric,
    SingleCustomChartResponse,
)

from host.api.schemas import ProjectMetricsGroup
from host.models.projects.metrics import convert_raw_metrics_to_chart_schema


class TestMetricsConversion(TestCase):
    def test_convert_raw_metrics_to_chart_schema_success(self):
        project_id = uuid.uuid4()
        raw_metrics_data = {
            "status": "success",
            "data": {
                "resultType": "matrix",
                "result": [
                    {
                        "metric": {},
                        "values": [
                            [1748797200, "106393600"],
                            [1748797260, "106393600"],
                            [1748797320, "106393600"],
                        ],
                    }
                ],
            },
        }
        metric = HostProjectChartMetric.memory_usage
        group = ProjectMetricsGroup.api_server

        result = convert_raw_metrics_to_chart_schema(
            project_id=project_id,
            raw_metrics_data=raw_metrics_data,
            metric=metric,
            group=group,
        )

        self.assertIsNotNone(result)
        self.assertIsInstance(result, SingleCustomChartResponse)
        self.assertEqual(result.chart_type, CustomChartType.line)
        self.assertEqual(result.title, f"{group.value}-{metric.value}")
        self.assertEqual(result.id, f"{project_id}-{group.value}-{metric.value}")
        self.assertEqual(len(result.series), 1)
        self.assertEqual(len(result.data), 3)

        series = result.series[0]
        self.assertIsInstance(series, CustomChartSeries)
        self.assertEqual(series.id, f"{project_id}-{group.value}-{metric.value}")
        self.assertEqual(series.name, f"{group.value}-{metric.value}")
        self.assertEqual(series.project_metric, metric)

        for i, data_point in enumerate(result.data):
            self.assertIsInstance(data_point, CustomChartsDataPoint)
            self.assertEqual(
                data_point.series_id, f"{project_id}-{group.value}-{metric.value}"
            )
            self.assertEqual(data_point.value, 106393600.0)
            expected_timestamp = datetime.fromtimestamp(
                raw_metrics_data["data"]["result"][0]["values"][i][0], tz=timezone.utc
            )
            self.assertEqual(data_point.timestamp, expected_timestamp)

    def test_convert_raw_metrics_to_chart_schema_invalid_value(self):
        project_id = uuid.uuid4()
        raw_metrics_data = {
            "status": "success",
            "data": {
                "resultType": "matrix",
                "result": [
                    {
                        "metric": {},
                        "values": [
                            [1748797200, "not_a_number"],
                            [1748797260, "106393600"],
                        ],
                    }
                ],
            },
        }
        metric = HostProjectChartMetric.memory_usage
        group = ProjectMetricsGroup.queue_worker

        result = convert_raw_metrics_to_chart_schema(
            project_id=project_id,
            raw_metrics_data=raw_metrics_data,
            metric=metric,
            group=group,
        )

        self.assertIsNotNone(result)
        self.assertEqual(len(result.data), 2)
        # First data point should have value 0.0 due to conversion error
        self.assertEqual(result.data[0].value, 0.0)
        # Second data point should have the correct value
        self.assertEqual(result.data[1].value, 106393600.0)

    def test_convert_raw_metrics_to_chart_schema_empty_values(self):
        project_id = uuid.uuid4()
        raw_metrics_data = {
            "status": "success",
            "data": {
                "resultType": "matrix",
                "result": [
                    {
                        "metric": {},
                        "values": [],
                    }
                ],
            },
        }
        metric = HostProjectChartMetric.cpu_usage
        group = ProjectMetricsGroup.api_server

        result = convert_raw_metrics_to_chart_schema(
            project_id=project_id,
            raw_metrics_data=raw_metrics_data,
            metric=metric,
            group=group,
        )

        self.assertIsNotNone(result)
        self.assertEqual(len(result.data), 0)
        self.assertEqual(len(result.series), 1)

    def test_convert_raw_metrics_to_chart_schema_missing_result(self):
        project_id = uuid.uuid4()
        raw_metrics_data = {
            "status": "success",
            "data": {
                "resultType": "matrix",
                "result": [],
            },
        }
        metric = HostProjectChartMetric.memory_usage
        group = ProjectMetricsGroup.queue_worker

        result = convert_raw_metrics_to_chart_schema(
            project_id=project_id,
            raw_metrics_data=raw_metrics_data,
            metric=metric,
            group=group,
        )

        self.assertIsNone(result)

    def test_convert_raw_metrics_to_chart_schema_error_status(self):
        project_id = uuid.uuid4()
        raw_metrics_data = {
            "status": "error",
            "data": {
                "resultType": "matrix",
                "result": [
                    {
                        "metric": {},
                        "values": [
                            [1748797200, "106393600"],
                        ],
                    }
                ],
            },
        }
        metric = HostProjectChartMetric.memory_usage
        group = ProjectMetricsGroup.queue_worker

        result = convert_raw_metrics_to_chart_schema(
            project_id=project_id,
            raw_metrics_data=raw_metrics_data,
            metric=metric,
            group=group,
        )

        self.assertIsNone(result)

    # we expect all promql queries to use aggregate functions over all pods and return a single result
    def test_convert_raw_metrics_to_chart_schema_multiple_results(self):
        project_id = uuid.uuid4()
        raw_metrics_data = {
            "status": "success",
            "data": {
                "resultType": "matrix",
                "result": [
                    {
                        "metric": {},
                        "values": [[1748797200, "106393600"]],
                    },
                    {  # Extra result that should be ignored
                        "metric": {},
                        "values": [[1748797200, "200000000"]],
                    },
                ],
            },
        }
        metric = HostProjectChartMetric.memory_usage
        group = ProjectMetricsGroup.queue_worker

        result = convert_raw_metrics_to_chart_schema(
            project_id=project_id,
            raw_metrics_data=raw_metrics_data,
            metric=metric,
            group=group,
        )

        self.assertIsNone(result)
