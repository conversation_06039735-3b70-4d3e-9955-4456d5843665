"""
This test performs the following actions to test/verify the LangGraph
Cloud control plane (host-backend), the LangGraph Cloud deployment
infrastructure (GCP, K8s/Operator), and the LangGraph API (i.e. latest API
image):

1. Create a project.
2. Invoke the graph of the LangGraph API deployment.
3. Create a new revision for the project.
4. Invoke the graph of the LangGraph API deployment again.
5. Delete the project.

---

This test assumes that there is a static integration environment available.
A valid LangGraph API GitHub repo must be added to the organization/tenant.
The following environment variables must be static and never changed/deleted:

1. HOST_BACKEND_URL
2. LANGCHAIN_API_KEY
3. ORGANIZATION_ID
4. TENANT_ID
5. HOST_INTEGRATION_ID

This test can be run against <PERSON><PERSON><PERSON>'s `prod`, `staging`, or `dev`
environments assuming the environemnt variables above are set correctly.

---

To run this test locally, create a `.env` file with the required
environment variables in the same directory as this file. Run pytest
from the directory host/tests/integration/k8s_operator/:

pytest test_deployment_k8s_operator.py -x -s --durations=5
"""

import functools
import logging
import os
import random
import string
import time
from datetime import datetime, timezone
from typing import Any, Callable, Optional

import pytest
import requests
from dotenv import load_dotenv

from host.tests.util import create_session_with_retries

load_dotenv()

# required environment variables
HOST_BACKEND_URL = os.getenv("HOST_BACKEND_URL")
LANGCHAIN_API_KEY = os.getenv("LANGCHAIN_API_KEY")
ORGANIZATION_ID = os.getenv("ORGANIZATION_ID")
TENANT_ID = os.getenv("TENANT_ID")
HOST_INTEGRATION_ID = os.getenv("HOST_INTEGRATION_ID")
HOST_GITHUB_REPO_URL = os.getenv("HOST_GITHUB_REPO_URL")
HOST_GITHUB_REPO_JS_URL = os.getenv("HOST_GITHUB_REPO_JS_URL")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")

# Default retry configurations
DEFAULT_MAX_RETRIES = 5
DEFAULT_INITIAL_BACKOFF = 10  # seconds
DEFAULT_MAX_BACKOFF = 180  # seconds
DEFAULT_BACKOFF_FACTOR = 2
DEFAULT_RETRY_STATUS_CODES = (404, 408, 429, 500, 502, 504)
MAX_WAIT_TIME = 1500  # 25 mins

session = create_session_with_retries()
logger = logging.getLogger(__name__)


def retry_with_exponential_backoff(
    max_retries: int = DEFAULT_MAX_RETRIES,
    initial_backoff: float = DEFAULT_INITIAL_BACKOFF,
    max_backoff: float = DEFAULT_MAX_BACKOFF,
    backoff_factor: float = DEFAULT_BACKOFF_FACTOR,
    retry_status_codes: tuple = DEFAULT_RETRY_STATUS_CODES,
) -> Callable:
    """
    Retry decorator with exponential backoff for API calls.

    Args:
        max_retries: Maximum number of retries before giving up
        initial_backoff: Initial backoff time in seconds
        max_backoff: Maximum backoff time in seconds
        backoff_factor: Factor to increase backoff with each retry
        retry_status_codes: HTTP status codes to retry on

    Returns:
        Decorated function with retry logic
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            backoff = initial_backoff
            for retry in range(max_retries + 1):
                try:
                    result = func(*args, **kwargs)
                    # For functions returning API responses
                    if (
                        isinstance(result, requests.Response)
                        and result.status_code in retry_status_codes
                    ):
                        raise Exception(
                            f"Request failed with status code {result.status_code}: {result.text}"
                        )
                    return result
                except Exception as e:
                    if retry == max_retries:
                        logger.error(f"Failed after {max_retries} retries: {str(e)}")
                        raise

                    # Calculate backoff with jitter
                    jitter = random.uniform(0.8, 1.2)
                    sleep_time = min(backoff * jitter, max_backoff)

                    logger.warning(
                        f"Attempt {retry + 1}/{max_retries} failed: {str(e)}. "
                        f"Retrying in {sleep_time:.2f} seconds..."
                    )

                    time.sleep(sleep_time)
                    backoff = min(backoff * backoff_factor, max_backoff)

        return wrapper

    return decorator


def get_headers() -> dict:
    """Return common headers for requests to host-backend API."""
    return {
        "X-Api-Key": LANGCHAIN_API_KEY,
        "X-Organization-Id": ORGANIZATION_ID,
        "X-Tenant-Id": TENANT_ID,
    }


@retry_with_exponential_backoff()
def create_project(github_repo_url: str, deployment_type: str) -> str:
    """Create project. Return project ID."""
    # create random project name
    random_str = "".join(random.choice(string.ascii_lowercase) for _ in range(4))
    random_num = random.randint(10000, 99999)
    project_name = f"integ-test-{random_str}-{random_num}"

    headers = get_headers()
    headers["Content-Type"] = "application/json"

    request_body = {
        "name": project_name,
        "lc_hosted": True,
        "repo_url": github_repo_url,
        "repo_path": "langgraph.json",
        "repo_commit": "main",
        "env_vars": [
            {
                "name": "OPENAI_API_KEY",
                "value": OPENAI_API_KEY,
                "type": "secret",
            },
            {
                "name": "ANTHROPIC_API_KEY",
                "value": ANTHROPIC_API_KEY,
                "type": "secret",
            },
            {
                "name": "TAVILY_API_KEY",
                "value": TAVILY_API_KEY,
                "type": "secret",
            },
        ],
        "host_integration_id": HOST_INTEGRATION_ID,
        "deployment_type": deployment_type,
        "shareable": False,
        "platform": None,
        "image_path": None,
        "build_on_push": False,
    }

    response = session.post(
        url=f"{HOST_BACKEND_URL}/v1/projects",
        headers=headers,
        json=request_body,
    )

    if response.status_code != 200:
        raise Exception(f"Failed to create project: {response.text}")

    project_id = response.json()["id"]
    print(f"Created project {project_name} ({project_id})")
    return project_id


@retry_with_exponential_backoff()
def get_project(project_id: str) -> dict:
    """Get project."""
    response = session.get(
        url=f"{HOST_BACKEND_URL}/v1/projects/{project_id}",
        headers=get_headers(),
    )

    if response.status_code != 200:
        raise Exception(f"Failed to get project ID {project_id}: {response.text}")

    return response.json()


@retry_with_exponential_backoff()
def list_revisions(project_id: str) -> list[dict]:
    """List revisions.

    Return list is sorted by created_at in descending order (latest first).
    """
    response = session.get(
        url=f"{HOST_BACKEND_URL}/v1/projects/{project_id}/revisions",
        headers=get_headers(),
    )

    if response.status_code != 200:
        raise Exception(
            f"Failed to list revisions for project ID {project_id}: {response.text}"
        )

    return response.json()


@retry_with_exponential_backoff()
def get_revision(
    project_id: str,
    revision_id: str,
) -> dict:
    """Get revision."""
    response = session.get(
        url=f"{HOST_BACKEND_URL}/v1/projects/{project_id}/revisions/{revision_id}",
        headers=get_headers(),
    )

    if response.status_code != 200:
        raise Exception(f"Failed to get revision ID {revision_id}: {response.text}")

    return response.json()


@retry_with_exponential_backoff()
def create_revision(project_id: str) -> None:
    """Create revision."""
    headers = get_headers()
    headers["Content-Type"] = "application/json"

    response = session.post(
        url=f"{HOST_BACKEND_URL}/v1/projects/{project_id}/revisions",
        headers=headers,
        json={
            "image_path": None,
            "repo_path": None,
            "env_vars": None,
            "shareable": None,
            "container_spec": None,
        },
    )

    if response.status_code != 200:
        raise Exception(f"Failed to create revision: {response.text}")

    print(f"Created revision for project ID {project_id}")


def wait_for_revision(project_id: str, revision_id: str) -> None:
    """Wait for revision status to be DEPLOYED.
    currently takes about 20 mins .. 10 build + 10 deploy"""
    start_time = time.time()
    revision, status = None, None
    while time.time() - start_time < MAX_WAIT_TIME:
        revision = get_revision(project_id, revision_id)
        status = revision["status"]
        if status == "DEPLOYED":
            break
        elif "FAILED" in status:
            raise Exception(f"Revision ID {revision_id} failed: {revision}")

        print(f"Waiting for revision ID {revision_id} to be DEPLOYED...")
        time.sleep(60)

    if status != "DEPLOYED":
        raise Exception(
            f"Timeout waiting for revision ID {revision_id} to be DEPLOYED: {revision}"
        )


@retry_with_exponential_backoff()
def redeploy_revision(project_id: str, revision_id: str) -> None:
    """(Re)deploy revision."""
    response = session.post(
        url=f"{HOST_BACKEND_URL}/v1/projects/{project_id}/revisions/{revision_id}/deploy",
        headers=get_headers(),
    )

    if response.status_code != 200:
        raise Exception(f"Failed redeploy revision ID {revision_id}: {response.text}")


@retry_with_exponential_backoff()
def list_deploy_logs(project_id: str, revision_id: Optional[str]) -> dict:
    """List revision logs."""
    headers = get_headers()
    headers["Content-Type"] = "application/json"

    if revision_id:
        url = f"{HOST_BACKEND_URL}/v1/projects/{project_id}/revisions/{revision_id}/deploy_logs"
    else:
        url = f"{HOST_BACKEND_URL}/v1/projects/{project_id}/deploy_logs"

    response = session.post(
        url=url,
        headers=headers,
        json={
            "start_time": None,  # start time will be set to the created_at time of the revision or project
            "end_time": datetime.now(timezone.utc).isoformat(),
            "order": "asc",
            "limit": 2,
            "offset": None,
        },
    )

    if response.status_code != 200:
        raise Exception(f"Failed to list deploy logs: {response.text}")

    # assert that there's at least 1 log entry
    resp_json = response.json()
    assert len(resp_json["logs"]) > 0

    return resp_json


@retry_with_exponential_backoff()
def delete_project(project_id: str) -> None:
    """Delete project."""
    response = session.delete(
        url=f"{HOST_BACKEND_URL}/v1/projects/{project_id}",
        headers=get_headers(),
    )

    if response.status_code != 200:
        raise Exception(f"Failed to delete project ID {project_id}: {response.text}")

    print(f"Project ID {project_id} deleted")


@retry_with_exponential_backoff()
def call_langgraph_api(project_url: str) -> None:
    """Call LangGraph API."""
    headers = get_headers()
    headers["Content-Type"] = "application/json"

    # get assistant ID
    response = requests.post(
        url=f"{project_url}/assistants/search",
        headers=headers,
        json={
            "limit": 1,
        },
    )

    if response.status_code != 200:
        raise Exception(f"Failed to search assistants: {response.text}")

    assistant = response.json()[0]
    assistant_id = assistant["assistant_id"]
    print(f"Invoking graph with assistant ID: {assistant_id}")

    # invoke graph
    response = requests.post(
        url=f"{project_url}/runs/wait",
        headers=headers,
        json={
            "assistant_id": assistant_id,
            "input": {
                "messages": [
                    {
                        "role": "human",
                        "content": "What's the weather in SF?",
                    }
                ]
            },
        },
    )

    if response.status_code != 200:
        raise Exception(f"Failed to call LangGraph API: {response.text}")

    print(f"LangGraph API response: {response.text}")


@pytest.mark.parametrize(
    "github_repo_url,deployment_type",
    [(HOST_GITHUB_REPO_URL, "prod"), (HOST_GITHUB_REPO_JS_URL, "dev")],
)
def test_deployment_k8s_operator(github_repo_url: str, deployment_type: str) -> None:
    # create project and get the latest revision
    project_id = create_project(github_repo_url, deployment_type)
    revisions = list_revisions(project_id)
    latest_revision = revisions[0]
    latest_revision_id = latest_revision["id"]

    # wait for latest revision to be DEPLOYED
    wait_for_revision(project_id, latest_revision_id)

    # Get project URL. It's only available after successfully creating a
    # revision.
    project = get_project(project_id)
    project_url = project["resource"]["url"]
    print(f"Project URL: {project_url}")

    # call the LangGraph API
    call_langgraph_api(project_url)

    # call the list logs API
    list_deploy_logs(project_id, latest_revision_id)

    # create a new revision for the project and get the revision
    create_revision(project_id)
    revisions = list_revisions(project_id)
    latest_revision = revisions[0]
    latest_revision_id = latest_revision["id"]

    # wait for latest revision to be DEPLOYED
    wait_for_revision(project_id, latest_revision_id)

    # call the LangGraph API again
    call_langgraph_api(project_url)

    # redeploy the revision
    redeploy_revision(project_id, latest_revision_id)

    # wait for latest revision to be DEPLOYED
    wait_for_revision(project_id, latest_revision_id)

    # delete the project
    delete_project(project_id)
