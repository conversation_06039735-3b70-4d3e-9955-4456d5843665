import time
from functools import partial
from typing import Any, As<PERSON><PERSON>enerator, <PERSON>wai<PERSON>, Callable
from uuid import UUID

import asyncpg
import pytest
import requests
from app.api.auth import AuthInfo
from app.config import settings
from app.tests.utils import (
    TEST_TENANT_ONE_UUID,
    TEST_TENANT_TWO_UUID,
    TEST_USER_ONE_UUID,
    TEST_USER_TWO_UUID,
    api_key_for_tenant,
    ensure_auth_info,
    jwt_for_tenant,
    queue_waiter,
)
from httpx import ASGITransport, AsyncClient
from lc_database.database import asyncpg_conn

from host.main import app

pytestmark = pytest.mark.anyio

TEST_HOST_INTEGRATION_ID_ONE_UUID = UUID("6d69c29c-937b-4acf-b2ab-1c17b847cbcf")
TEST_HOST_INTEGRATION_ID_TWO_UUID = UUID("651bc3cc-a3de-40a2-b372-42ada590abb9")


async def ensure_host_integration(
    db_asyncpg: asyncpg.Connection,
    host_integration_id: UUID,
    tenant_id: UUID,
) -> None:
    """Create a row in host_integration_namespace_ids for the given tenant ID."""
    await db_asyncpg.execute(
        """
        INSERT INTO host_integration_namespace_ids (id, namespace_id, provider, tenant_id)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (id)
        DO NOTHING
        """,
        host_integration_id,
        "123456790",
        "github",
        tenant_id,
    )


@pytest.fixture(scope="session", autouse=True)
def anyio_backend():
    return ("asyncio", {"use_uvloop": True})


@pytest.fixture()
async def db() -> AsyncGenerator[asyncpg.Connection, None]:
    async with asyncpg_conn() as conn:
        yield conn


@pytest.fixture()
async def http_public() -> AsyncGenerator:
    async with AsyncClient(transport=ASGITransport(app), base_url="http://test") as ac:
        yield ac


@pytest.fixture()
async def auth_tenant_one(db: asyncpg.Connection, anyio_backend: Any) -> AuthInfo:
    auth, _ = await ensure_auth_info(
        db_asyncpg=db,
        tenant_id=UUID(TEST_TENANT_ONE_UUID),
        tenant_handle="test-tenant-one",
        user_id=UUID(TEST_USER_ONE_UUID)
        if settings.AUTH_TYPE in ["supabase", "oauth"]
        else None,
        user_email="<EMAIL>"
        if settings.AUTH_TYPE in ["supabase", "oauth"]
        else None,
        user_full_name="Test User One",
        tenant_config=settings.SHARED_TENANT_DEFAULT_CONFIG,
        organization_is_personal=False,
        should_ensure_user=True,
    )

    await ensure_host_integration(
        db_asyncpg=db,
        host_integration_id=TEST_HOST_INTEGRATION_ID_ONE_UUID,
        tenant_id=UUID(TEST_TENANT_ONE_UUID),
    )

    return auth


@pytest.fixture()
async def auth_tenant_two(db: asyncpg.Connection, anyio_backend: Any) -> AuthInfo:
    auth, _ = await ensure_auth_info(
        db_asyncpg=db,
        tenant_id=UUID(TEST_TENANT_TWO_UUID),
        tenant_handle="test-tenant-two",
        user_id=UUID(TEST_USER_TWO_UUID)
        if settings.AUTH_TYPE in ["supabase", "oauth"]
        else None,
        user_email="<EMAIL>"
        if settings.AUTH_TYPE in ["supabase", "oauth"]
        else None,
        user_full_name="Test User Two",
        tenant_config=settings.SHARED_TENANT_DEFAULT_CONFIG,
        organization_is_personal=False,
        should_ensure_user=True,
    )

    await ensure_host_integration(
        db_asyncpg=db,
        host_integration_id=TEST_HOST_INTEGRATION_ID_TWO_UUID,
        tenant_id=UUID(TEST_TENANT_TWO_UUID),
    )

    return auth


@pytest.fixture()
async def headers_tenant_one(
    db: asyncpg.Connection, auth_tenant_one: AuthInfo
) -> dict[str, str]:
    if settings.AUTH_TYPE == "api_key":
        return {"x-api-key": await api_key_for_tenant(auth_tenant_one)}
    elif settings.AUTH_TYPE == "supabase":
        return {
            "Authorization": f"Bearer {await jwt_for_tenant(db, auth_tenant_one)}",
            "X-Tenant-Id": str(auth_tenant_one.tenant_id),
        }
    elif settings.AUTH_TYPE == "none":
        return {}
    else:
        raise NotImplementedError


@pytest.fixture()
async def headers_tenant_two(
    db: asyncpg.Connection, auth_tenant_two: AuthInfo
) -> dict[str, str]:
    if settings.AUTH_TYPE == "api_key":
        return {"x-api-key": await api_key_for_tenant(auth_tenant_two)}
    elif settings.AUTH_TYPE == "supabase":
        return {
            "Authorization": f"Bearer {await jwt_for_tenant(db, auth_tenant_two)}",
            "X-Tenant-Id": str(auth_tenant_two.tenant_id),
        }
    elif settings.AUTH_TYPE == "none":
        return {}
    else:
        raise NotImplementedError


@pytest.fixture()
async def http_tenant_one(headers_tenant_one: dict[str, str]) -> AsyncGenerator:
    async with AsyncClient(
        transport=ASGITransport(app), base_url="http://test", headers=headers_tenant_one
    ) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_two(headers_tenant_two: dict[str, str]) -> AsyncGenerator:
    async with AsyncClient(
        transport=ASGITransport(app), base_url="http://test", headers=headers_tenant_two
    ) as ac:
        yield ac


@pytest.fixture()
def wait_until_task_queue_empty(
    timeout: int = 60, n_empty_threshold: int = 3
) -> Callable[[], Awaitable[Any]]:
    return partial(queue_waiter, timeout=timeout, n_empty_threshold=n_empty_threshold)


@pytest.fixture(scope="function", autouse=False)
async def wait_for_service() -> None:
    # Check if servers are ready
    timeout = 120  # seconds
    start_time = time.time()
    backend_ready = False

    while time.time() - start_time < timeout:
        try:
            if not backend_ready:
                response = requests.get("http://localhost:8080/ok")
                if response.status_code == 200:
                    backend_ready = True
            if backend_ready:
                break
        except requests.ConnectionError:
            pass
        print("Waiting for servers to start...")
        time.sleep(0.5)


@pytest.fixture(scope="function", autouse=False)
async def clean_db(db: asyncpg.Connection) -> None:
    """Clean up LangSmith database after each test.

    If a test inserts data into the database, make sure the table is emptied
    (DELETE FROM table) in this function.

    This only applies to API tests. Unit and integration tests do not start a
    locally running database.
    """
    await db.execute("DELETE FROM host_revisions")
    await db.execute("DELETE FROM host_projects")
    await db.execute("DELETE FROM tracer_session")
    await db.execute("DELETE FROM taggings")
    await db.execute("DELETE FROM tag_values")
    await db.execute("DELETE FROM tag_keys")
    await db.execute("DELETE FROM service_accounts")
    await db.execute("DELETE FROM host_integration_namespace_ids")
    await db.execute("DELETE FROM api_keys")
    await db.execute("DELETE FROM remote_metrics")
