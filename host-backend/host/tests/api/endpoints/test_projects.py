from datetime import datetime, timezone
from uuid import UUID, uuid4

import asyncpg
import pytest
from app.api.auth import AuthInfo
from httpx import AsyncClient
from pytest_mock import Mocker<PERSON>ixture

from host.tests.any_value import AnyDateTime, AnyStr, AnyUUID
from host.tests.conftest import (
    TEST_HOST_INTEGRATION_ID_ONE_UUID,
    TEST_TENANT_ONE_UUID,
)


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_create_project_endpoint_k8s_github(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
    mocker: MockerFixture,
) -> None:
    """This test verifies the state of the project and revision metadata
    after calling the POST /v1/projects endpoint for a K8s/Knative deployment
    deployed from a GitHub repository.

    This test does not verify the GitHub integration or the cloud service
    integration (e.g. GCP).
    """
    # set up mocks
    mock_validate_project = mocker.patch(
        "host.models.projects.bootstrap.github.validate_project"
    )
    mock_validate_project.return_value = (
        "62283478da8a58f8492c36e33e07a3b4cd49d905",
        None,
    )

    # call POST /v1/projects
    res = await http_tenant_one.post(
        "/v1/projects",
        json={
            "repo_url": "https://github.com/langchain-ai/langgraph-example",
            "name": "ht-api-test-1",
            "env_vars": [],
            "host_integration_id": str(TEST_HOST_INTEGRATION_ID_ONE_UUID),
        },
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body == {
        "id": AnyStr(),
        "tenant_id": str(TEST_TENANT_ONE_UUID),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "name": "ht-api-test-1",
        "lc_hosted": True,
        "repo_url": "https://github.com/langchain-ai/langgraph-example",
        "repo_branch": "main",
        "tracer_session_id": AnyStr(),
        "api_key_id": AnyStr(),
        "build_on_push": False,
        "host_integration_id": str(TEST_HOST_INTEGRATION_ID_ONE_UUID),
        "metadata": {
            "platform": "k8s_operator",
            "deployment_type": "prod",
            "image_source": "github",
            "shareable": False,
            "region": "us-central1",
            "public": False,
            "database_platform": "gcp_cloud_sql",
            "k8s_cluster": "langgraph-cloud-test",
            "k8s_namespace": "2ebda79f-2946-4491-a9ad-d642f49e0815",
        },
        "resource": {
            "id": {
                "type": "services",
                "name": "ht-api-test-1-ad3461e19627570ba1f9553427209e92",
            },
            "url": None,
            "latest_revision": {
                "env_vars": [],
                "hosted_langserve_revision_id": AnyStr(),
                "id": {
                    "type": "revisions",
                    "name": "",
                },
            },
            "latest_active_revision": None,
        },
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "AWAITING_DATABASE",
        "custom_url": None,
        "remote_reconciled": False,
        "container_spec": {},
    }

    # verify control plane state
    service_account = await db.fetchrow(
        """
        SELECT *
        FROM service_accounts
        WHERE name = $1
        """,
        "Hosted LangServe: ht-api-test-1",
    )
    assert dict(service_account) == {
        "id": AnyUUID(),
        "name": "Hosted LangServe: ht-api-test-1",
        "organization_id": AnyUUID(),
        "default_workspace_id": None,
        "created_at": AnyDateTime(),
        "updated_at": AnyDateTime(),
    }

    service_account_identity = await db.fetchrow(
        """
        SELECT *
        FROM identities
        WHERE
            service_account_id = $1
            AND tenant_id = $2
        """,
        service_account["id"],
        TEST_TENANT_ONE_UUID,
    )
    assert dict(service_account_identity) == {
        "id": AnyUUID(),
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "user_id": None,
        "created_at": AnyDateTime(),
        "read_only": False,
        "organization_id": service_account["organization_id"],
        "role_id": AnyUUID(),
        "access_scope": "workspace",
        "parent_identity_id": AnyUUID(),
        "service_account_id": service_account["id"],
        "ls_user_id": None,
    }

    api_key = await db.fetchrow(
        """
        SELECT *
        FROM api_keys
        WHERE id = $1
        """,
        res_body["api_key_id"],
    )
    assert dict(api_key) == {
        "id": UUID(res_body["api_key_id"]),
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "api_key": AnyStr(),
        "created_at": AnyDateTime(),
        "short_key": AnyStr(),
        "read_only": False,
        "identity_id": service_account_identity["id"],
        "description": "Hosted LangServe: ht-api-test-1",
        "user_id": None,
        "service_account_id": service_account["id"],
        "organization_id": service_account["organization_id"],
        "ls_user_id": None,
        "last_used_at": None,
    }

    # TODO: Verify tracer_session table.

    host_project = await db.fetchrow(
        """
        SELECT *
        FROM host_projects
        WHERE id = $1
        """,
        res_body["id"],
    )
    assert dict(host_project) == {
        "id": UUID(res_body["id"]),
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "created_at": datetime.strptime(
            res_body["created_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "updated_at": datetime.strptime(
            res_body["updated_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "name": "ht-api-test-1",
        "readme_markdown": None,
        "repo_url": "https://github.com/langchain-ai/langgraph-example",
        "knative": {
            "type": "services",
            "name": "ht-api-test-1-ad3461e19627570ba1f9553427209e92",
        },
        "tracer_session_id": UUID(res_body["tracer_session_id"]),
        "api_key_id": UUID(res_body["api_key_id"]),
        "host_integration_id": TEST_HOST_INTEGRATION_ID_ONE_UUID,
        "metadata": {
            "deployment_type": "prod",
            "image_source": "github",
            "k8s_cluster": "langgraph-cloud-test",
            "k8s_namespace": str(TEST_TENANT_ONE_UUID),
            "platform": "k8s_operator",
            "region": "us-central1",
            "shareable": False,
            "database_platform": "gcp_cloud_sql",
            "public": False,
        },
        "lc_hosted": True,
        "repo_branch": "main",
        "build_on_push": False,
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "AWAITING_DATABASE",
        "custom_url": None,
        "remote_reconciled": False,
        "secrets": {
            "LANGCHAIN_API_KEY": AnyStr(),
        },
        "container_spec": {},
    }

    host_revisions = await db.fetch(
        """
        SELECT *
        FROM host_revisions
        WHERE project_id = $1
        """,
        res_body["id"],
    )
    assert len(host_revisions) == 1

    host_revision = host_revisions[0]
    assert dict(host_revision) == {
        "id": AnyUUID(),
        "project_id": UUID(res_body["id"]),
        "created_at": AnyDateTime(),
        "updated_at": AnyDateTime(),
        "repo_path": "langgraph.json",
        "repo_commit": "main",
        "knative": None,
        "status": "AWAITING_BUILD",
        "status_message": None,
        "completed_at": None,
        "gcp_build_name": None,
        "metadata": {
            "created_by": {
                "type": "user",
                "identity_id": AnyStr(),
            },
            "repo_commit_sha": "62283478da8a58f8492c36e33e07a3b4cd49d905",
        },
        "image_path": None,
        "container_spec": {},
    }


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_create_project_endpoint_aws(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
    mocker: MockerFixture,
) -> None:
    """This test verifies the state of the project and revision metadata
    after calling the POST /v1/projects endpoint for a BYOC/AWS deployment.

    This test does not verify the cloud service integration (e.g. AWS).
    """
    # set up mocks
    mocker.patch("host.models.projects._validate_aws_env")

    # call POST /v1/projects
    res = await http_tenant_one.post(
        "/v1/projects",
        json={
            "name": "ht-api-test-2",
            "lc_hosted": False,
            "env_vars": [],
            "platform": {
                "deployment_platform": "aws_ecs",
                "region": "us-west-2",
                "aws_account_id": "************",
            },
            "image_path": "************.dkr.ecr.us-west-2.amazonaws.com/andrew-test:ht-gripping-duck-3",
        },
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert (
        res_body
        == {
            "id": AnyStr(),
            "tenant_id": str(TEST_TENANT_ONE_UUID),
            "created_at": AnyStr(),
            "updated_at": AnyStr(),
            "name": "ht-api-test-2",
            "lc_hosted": False,
            "repo_url": None,
            "repo_branch": None,
            "tracer_session_id": AnyStr(),
            "api_key_id": AnyStr(),
            "build_on_push": False,
            "host_integration_id": None,
            "metadata": {
                "platform": "aws_ecs",
                "deployment_type": "prod",
                "image_source": "external_docker",
                "shareable": False,
                "aws_account_id": "************",
                "aws_external_id": AnyStr(),  # the org ID, but it gets recreated during each test
                "region": "us-west-2",
                "public": False,
                "database_platform": "aws_rds",
            },
            "resource": {
                "id": {
                    "type": "services",
                    "name": "ht-api-test-2-8ed76fdc43da5d16a3638924dbb59fbe",
                },
                "url": None,
                "latest_revision": {
                    "env_vars": [],
                    "hosted_langserve_revision_id": AnyStr(),
                    "id": {
                        "type": "revisions",
                        "name": "",
                    },
                },
                "latest_active_revision": None,
            },
            "description": None,
            "display_name": None,
            "input_json_schemas": None,
            "output_json_schemas": None,
            "tool_name": None,
            "example_input": None,
            "status": "AWAITING_DATABASE",
            "custom_url": None,
            "remote_reconciled": False,
            "container_spec": {},
        }
    )

    # verify control plane state
    service_account = await db.fetchrow(
        """
        SELECT *
        FROM service_accounts
        WHERE name = $1
        """,
        "Hosted LangServe: ht-api-test-2",
    )
    assert dict(service_account) == {
        "id": AnyUUID(),
        "name": "Hosted LangServe: ht-api-test-2",
        "organization_id": AnyUUID(),
        "default_workspace_id": None,
        "created_at": AnyDateTime(),
        "updated_at": AnyDateTime(),
    }

    service_account_identity = await db.fetchrow(
        """
        SELECT *
        FROM identities
        WHERE
            service_account_id = $1
            AND tenant_id = $2
        """,
        service_account["id"],
        TEST_TENANT_ONE_UUID,
    )
    assert dict(service_account_identity) == {
        "id": AnyUUID(),
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "user_id": None,
        "created_at": AnyDateTime(),
        "read_only": False,
        "organization_id": service_account["organization_id"],
        "role_id": AnyUUID(),
        "access_scope": "workspace",
        "parent_identity_id": AnyUUID(),
        "service_account_id": service_account["id"],
        "ls_user_id": None,
    }

    api_key = await db.fetchrow(
        """
        SELECT *
        FROM api_keys
        WHERE id = $1
        """,
        res_body["api_key_id"],
    )
    assert dict(api_key) == {
        "id": UUID(res_body["api_key_id"]),
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "api_key": AnyStr(),
        "created_at": AnyDateTime(),
        "short_key": AnyStr(),
        "read_only": False,
        "identity_id": service_account_identity["id"],
        "description": "Hosted LangServe: ht-api-test-2",
        "user_id": None,
        "service_account_id": service_account["id"],
        "organization_id": service_account["organization_id"],
        "ls_user_id": None,
        "last_used_at": None,
    }

    # TODO: Verify tracer_session table.

    host_project = await db.fetchrow(
        """
        SELECT *
        FROM host_projects
        WHERE id = $1
        """,
        res_body["id"],
    )
    assert dict(host_project) == {
        "id": UUID(res_body["id"]),
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "created_at": datetime.strptime(
            res_body["created_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "updated_at": datetime.strptime(
            res_body["updated_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "name": "ht-api-test-2",
        "readme_markdown": None,
        "repo_url": None,
        "knative": {
            "type": "services",
            "name": "ht-api-test-2-8ed76fdc43da5d16a3638924dbb59fbe",
        },
        "tracer_session_id": UUID(res_body["tracer_session_id"]),
        "api_key_id": UUID(res_body["api_key_id"]),
        "host_integration_id": None,
        "metadata": {
            "deployment_type": "prod",
            "image_source": "external_docker",
            "platform": "aws_ecs",
            "aws_account_id": "************",
            "aws_external_id": AnyStr(),
            "region": "us-west-2",
            "shareable": False,
            "database_platform": "aws_rds",
            "public": False,
        },
        "lc_hosted": False,
        "repo_branch": None,
        "build_on_push": False,
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "AWAITING_DATABASE",
        "custom_url": None,
        "remote_reconciled": False,
        "secrets": {
            "LANGCHAIN_API_KEY": AnyStr(),
        },
        "container_spec": {},
    }

    host_revisions = await db.fetch(
        """
        SELECT *
        FROM host_revisions
        WHERE project_id = $1
        """,
        res_body["id"],
    )
    assert len(host_revisions) == 1

    host_revision = host_revisions[0]
    assert dict(host_revision) == {
        "id": AnyUUID(),
        "project_id": UUID(res_body["id"]),
        "created_at": AnyDateTime(),
        "updated_at": AnyDateTime(),
        "repo_path": None,
        "repo_commit": None,
        "knative": None,
        "status": "AWAITING_DEPLOY",
        "status_message": None,
        "completed_at": None,
        "gcp_build_name": None,
        "metadata": {
            "created_by": {
                "type": "user",
                "identity_id": AnyStr(),
            },
        },
        "image_path": "************.dkr.ecr.us-west-2.amazonaws.com/andrew-test:ht-gripping-duck-3",
        "container_spec": {},
    }


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_patch_project_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the state of the project metadata after calling
    the PATCH /v1/projects/{project_id} endpoint for a project.
    """
    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-project-to-patch-1",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata, repo_branch, build_on_push)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-project-to-patch-1",
        {
            "type": "services",
            "name": "ht-project-to-patch-1-894a7380811150dca85c0bf0da377fd0",
        },
        tracer_session_id,
        {"platform": "k8s"},
        "main",
        False,
    )
    project_id = project_row["id"]

    # call PATCH /v1/projects/{project_id}
    res = await http_tenant_one.patch(
        f"/v1/projects/{project_id}",
        json={
            "repo_branch": "example-branch",
            "build_on_push": True,
            "custom_url": "https://example.com",
        },
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body == {
        "id": AnyStr(),
        "tenant_id": str(TEST_TENANT_ONE_UUID),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "name": "ht-project-to-patch-1",
        "lc_hosted": True,
        "repo_url": None,
        "repo_branch": "example-branch",  # verify this field was updated
        "tracer_session_id": str(tracer_session_id),
        "api_key_id": None,
        "build_on_push": True,  # verify this field was updated
        "host_integration_id": None,
        "metadata": {
            "platform": "k8s",
        },
        "resource": {
            "id": {
                "type": "services",
                "name": "ht-project-to-patch-1-894a7380811150dca85c0bf0da377fd0",
            },
            "url": None,
            "latest_revision": None,
            "latest_active_revision": None,
        },
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "READY",
        "custom_url": "https://example.com",
        "remote_reconciled": False,
        "container_spec": {},
    }

    # verify control plane state
    host_project = await db.fetchrow(
        """
        SELECT *
        FROM host_projects
        WHERE id = $1
        """,
        project_id,
    )
    assert dict(host_project) == {
        "id": project_id,
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "created_at": datetime.strptime(
            res_body["created_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "updated_at": datetime.strptime(
            res_body["updated_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "name": "ht-project-to-patch-1",
        "readme_markdown": None,
        "repo_url": None,
        "knative": {
            "type": "services",
            "name": "ht-project-to-patch-1-894a7380811150dca85c0bf0da377fd0",
        },
        "tracer_session_id": tracer_session_id,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "api_key_id": None,
        "host_integration_id": None,
        "metadata": {
            "platform": "k8s",
        },
        "lc_hosted": True,
        "repo_branch": "example-branch",  # verify this field was updated
        "build_on_push": True,  # verify this field was updated
        "description": None,
        "display_name": None,
        "status": "READY",
        "custom_url": "https://example.com",
        "remote_reconciled": False,
        "secrets": None,
        "container_spec": {},
    }


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_get_project_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the GET /v1/projects/{project_id} endpoint.

    Note: This test is slightly redundant because its implementation
    overlaps with other endpoints (e.g. POST /projects). Regardless,
    we'll include it for good measure.
    """
    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-project-to-get-1",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-project-to-get-1",
        {
            "type": "services",
            "name": "ht-project-to-get-1-5dd6de10188a52c6b5152ba2af6670cd",
        },
        tracer_session_id,
        {"platform": "k8s"},
    )
    project_id = project_row["id"]

    # call GET /v1/projects/{project_id}
    res = await http_tenant_one.get(
        f"/v1/projects/{project_id}",
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body == {
        "id": str(project_id),
        "tenant_id": str(TEST_TENANT_ONE_UUID),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "name": "ht-project-to-get-1",
        "lc_hosted": True,
        "repo_url": None,
        "repo_branch": None,
        "tracer_session_id": str(tracer_session_id),
        "api_key_id": None,
        "build_on_push": False,
        "host_integration_id": None,
        "metadata": {
            "platform": "k8s",
        },
        "resource": {
            "id": {
                "type": "services",
                "name": "ht-project-to-get-1-5dd6de10188a52c6b5152ba2af6670cd",
            },
            "url": None,
            "latest_revision": None,
            "latest_active_revision": None,
        },
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "READY",
        "custom_url": None,
        "remote_reconciled": False,
        "container_spec": {},
    }


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_list_projects_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the GET /v1/projects endpoint."""
    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-project-to-list-1",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id_1 = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-project-to-list-1",
        {
            "type": "services",
            "name": "ht-project-to-list-1-7e09da23742f547da1396bfb702fc97c",
        },
        tracer_session_id_1,
        {"platform": "k8s"},
    )
    project_id_1 = project_row["id"]

    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-project-to-list-2",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id_2 = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-project-to-list-2",
        {
            "type": "services",
            "name": "ht-project-to-list-2-b4f07ddfebc452a9b85e5b288e0fcdb9",
        },
        tracer_session_id_2,
        {"platform": "k8s"},
    )
    project_id_2 = project_row["id"]

    tag_key_row = await db.fetchrow(
        """
        INSERT INTO tag_keys (tenant_id, key)
        VALUES ($1, $2)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "list_projects_tag_key",
    )

    tag_value_row = await db.fetchrow(
        """
        INSERT INTO tag_values (tag_key_id, value)
        VALUES ($1, $2)
        RETURNING id
        """,
        tag_key_row["id"],
        "list_projects_tag_value",
    )
    tag_value_id = tag_value_row["id"]

    await db.fetchrow(
        """
        INSERT INTO taggings (tag_value_id, resource_type, resource_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        tag_value_id,
        "deployment",
        project_id_2,
    )

    # Test 1: call GET /v1/projects
    res = await http_tenant_one.get("/v1/projects")

    # verify API response
    assert res.status_code == 200, res.content

    headers = res.headers
    x_pagination_total = headers.get("X-Pagination-Total")
    assert x_pagination_total == "2"

    res_body = res.json()
    assert isinstance(res_body, list)
    assert len(res_body) == 2

    project_1 = res_body[0]
    project_2 = res_body[1]

    # projects are returned in updated_at order desc (most recent first)
    assert project_1 == {
        "id": str(project_id_2),
        "tenant_id": str(TEST_TENANT_ONE_UUID),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "name": "ht-project-to-list-2",
        "lc_hosted": True,
        "repo_url": None,
        "repo_branch": None,
        "tracer_session_id": str(tracer_session_id_2),
        "api_key_id": None,
        "build_on_push": False,
        "host_integration_id": None,
        "metadata": {
            "platform": "k8s",
        },
        "resource": {
            "id": {
                "type": "services",
                "name": "ht-project-to-list-2-b4f07ddfebc452a9b85e5b288e0fcdb9",
            },
            "url": None,
            "latest_revision": None,
            "latest_active_revision": None,
        },
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "READY",
        "custom_url": None,
        "remote_reconciled": False,
        "container_spec": {},
    }

    assert project_2 == {
        "id": str(project_id_1),
        "tenant_id": str(TEST_TENANT_ONE_UUID),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "name": "ht-project-to-list-1",
        "lc_hosted": True,
        "repo_url": None,
        "repo_branch": None,
        "tracer_session_id": str(tracer_session_id_1),
        "api_key_id": None,
        "build_on_push": False,
        "host_integration_id": None,
        "metadata": {
            "platform": "k8s",
        },
        "resource": {
            "id": {
                "type": "services",
                "name": "ht-project-to-list-1-7e09da23742f547da1396bfb702fc97c",
            },
            "url": None,
            "latest_revision": None,
            "latest_active_revision": None,
        },
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "READY",
        "custom_url": None,
        "remote_reconciled": False,
        "container_spec": {},
    }

    # Test 2: call GET /v1/projects?limit=1
    res = await http_tenant_one.get(
        "/v1/projects",
        params={"limit": 1},
    )

    # verify API response
    assert res.status_code == 200, res.content

    headers = res.headers
    x_pagination_total = headers.get("X-Pagination-Total")
    assert x_pagination_total == "2"

    res_body = res.json()
    assert isinstance(res_body, list)
    assert len(res_body) == 1
    # don't need to verify response body contents

    # Test 3: call GET /v1/projects?name_contains=ht-project-to-list-1
    res = await http_tenant_one.get(
        "/v1/projects",
        params={"name_contains": "ht-project-to-list-1"},
    )

    # verify API response
    assert res.status_code == 200, res.content

    headers = res.headers
    x_pagination_total = headers.get("X-Pagination-Total")
    assert x_pagination_total == "1"

    res_body = res.json()
    assert isinstance(res_body, list)
    assert len(res_body) == 1

    project_1 = res_body[0]
    # only need to verify the project name
    assert project_1["name"] == "ht-project-to-list-1"

    # Test 4: call GET /v1/projects?tag_value_id
    res = await http_tenant_one.get(
        "/v1/projects",
        params={"tag_value_id": tag_value_id},
    )

    # verify API response
    assert res.status_code == 200, res.content

    headers = res.headers
    x_pagination_total = headers.get("X-Pagination-Total")
    assert x_pagination_total == "1"

    res_body = res.json()
    assert isinstance(res_body, list)
    assert len(res_body) == 1

    project_1 = res_body[0]
    # only need to verify the project name
    assert project_1["name"] == "ht-project-to-list-2"


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_delete_project_endpoint(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the control plane state (e.g. host_projects and
    host_revisions tables) after calling the DELETE /v1/projects/{project_id}
    endpoint.

    This test does not verify the cloud service integration (e.g. GCP).
    """
    # create seed data
    service_account_row = await db.fetchrow(
        """
        INSERT INTO service_accounts (name, organization_id)
        VALUES ($1, $2)
        RETURNING id
        """,
        "ht-project-to-delete-1",
        auth_tenant_one.organization_id,
    )
    service_account_id = service_account_row["id"]

    service_account_identity_row = await db.fetchrow(
        """
        INSERT INTO identities (tenant_id, organization_id, access_scope, service_account_id)
        VALUES ($1, $2, $3, $4)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        auth_tenant_one.organization_id,
        "organization",
        service_account_id,
    )
    service_account_identity_id = service_account_identity_row["id"]

    api_key_row = await db.fetchrow(
        """
        INSERT into api_keys (id, tenant_id, api_key, short_key, identity_id, service_account_id)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
        """,
        uuid4(),
        UUID(TEST_TENANT_ONE_UUID),
        "api_key",
        "short_key",
        service_account_identity_id,
        service_account_id,
    )
    api_key_id = api_key_row["id"]

    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-project-to-delete-1",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, api_key_id, metadata)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-project-to-delete-1",
        {
            "type": "services",
            "name": "ht-project-to-delete-1-ad3461e19627570ba1f9553427209e92",
        },
        tracer_session_id,
        api_key_id,
        {"platform": "k8s"},
    )
    project_id = project_row["id"]

    await db.fetchrow(
        """
        INSERT INTO host_revisions (project_id, repo_path, repo_commit)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        project_id,
        "langgraph.json",
        "main",
    )

    # call DELETE /v1/projects/{project_id}
    res = await http_tenant_one.delete(
        f"/v1/projects/{project_id}",
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body == {
        "id": str(project_id),
        "tenant_id": str(TEST_TENANT_ONE_UUID),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "name": "ht-project-to-delete-1",
        "lc_hosted": True,
        "repo_url": None,
        "repo_branch": None,
        "tracer_session_id": str(tracer_session_id),
        "api_key_id": str(api_key_id),
        "build_on_push": False,
        "host_integration_id": None,
        "metadata": {
            "platform": "k8s",
        },
        "resource": None,
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "AWAITING_DELETE",  # verify that status is set to AWAITING_DELETE
        "custom_url": None,
        "remote_reconciled": False,
        "container_spec": {},
    }

    # verify control plane state
    project_row = await db.fetchrow(
        """
        SELECT * FROM host_projects
        WHERE id = $1
        """,
        project_id,
    )
    assert dict(project_row) == {
        "id": project_id,
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "created_at": datetime.strptime(
            res_body["created_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "updated_at": datetime.strptime(
            res_body["updated_at"], "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc),
        "name": "ht-project-to-delete-1",
        "readme_markdown": None,
        "repo_url": None,
        "knative": {
            "type": "services",
            "name": "ht-project-to-delete-1-ad3461e19627570ba1f9553427209e92",
        },
        "tracer_session_id": tracer_session_id,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "api_key_id": api_key_id,
        "host_integration_id": None,
        "metadata": {
            "platform": "k8s",
        },
        "lc_hosted": True,
        "repo_branch": None,
        "build_on_push": False,
        "description": None,
        "display_name": None,
        "status": "AWAITING_DELETE",  # verify that status is set to AWAITING_DELETE
        "custom_url": None,
        "remote_reconciled": False,
        "secrets": None,
        "container_spec": {},
    }


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_create_revision_endpoint_k8s_github(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
    mocker: MockerFixture,
) -> None:
    """This test verifies the state of the project and revision metadata
    after calling the POST /v1/projects/{project_id}/revisions endpoint for
    a K8s/Knative deployment deployed from a GitHub repository.

    This test does not verify the GitHub integration or the cloud service
    integration (e.g. GCP).
    """
    # set up mocks
    mock_validate_project = mocker.patch(
        "host.models.projects.bootstrap.github.validate_project"
    )
    mock_validate_project.return_value = (
        "62283478da8a58f8492c36e33e07a3b4cd49d905",
        None,
    )

    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-api-test-2",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, repo_url, knative, tracer_session_id, host_integration_id, metadata, repo_branch, build_on_push)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-api-test-2",
        "https://github.com/langchain-ai/langgraph-example",
        {
            "type": "services",
            "name": "ht-api-test-2-8ed76fdc43da5d16a3638924dbb59fbe",
        },
        tracer_session_id,
        TEST_HOST_INTEGRATION_ID_ONE_UUID,
        {"platform": "k8s"},
        "main",
        False,
    )
    project_id = project_row["id"]

    # call POST /v1/projects/{project_id}/revisions
    res = await http_tenant_one.post(
        f"/v1/projects/{project_id}/revisions",
        json={
            "image_path": None,
            "repo_path": "langgraph.json",
            "env_vars": [],
            "shareable": True,  # change shareable to True
            "container_spec": {
                # exclude min_scale
                "max_scale": 10,
                "cpu": 2,
                "memory_mb": 2048,
            },
        },
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body == {
        "id": str(project_id),
        "tenant_id": str(TEST_TENANT_ONE_UUID),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "name": "ht-api-test-2",
        "lc_hosted": True,
        "repo_url": "https://github.com/langchain-ai/langgraph-example",
        "repo_branch": "main",
        "tracer_session_id": str(tracer_session_id),
        "api_key_id": None,
        "build_on_push": False,
        "host_integration_id": str(TEST_HOST_INTEGRATION_ID_ONE_UUID),
        "metadata": {
            "platform": "k8s",
            "shareable": True,
        },
        "resource": {
            "id": {
                "type": "services",
                "name": "ht-api-test-2-8ed76fdc43da5d16a3638924dbb59fbe",
            },
            "url": None,
            "latest_revision": {
                "env_vars": [],
                "hosted_langserve_revision_id": AnyStr(),
                "id": {
                    "type": "revisions",
                    "name": "",
                },
            },
            "latest_active_revision": None,
        },
        "description": None,
        "display_name": None,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "status": "READY",
        "custom_url": None,
        "remote_reconciled": False,
        "container_spec": {
            "max_scale": 10,
            "cpu": 2,
            "memory_mb": 2048,
            "min_scale": None,
        },
    }

    # verify control plane state
    host_project = await db.fetchrow(
        """
        SELECT *
        FROM host_projects
        WHERE id = $1
        """,
        project_id,
    )
    assert dict(host_project) == {
        "id": project_id,
        "tenant_id": UUID(TEST_TENANT_ONE_UUID),
        "created_at": AnyDateTime(),
        "updated_at": AnyDateTime(),
        "name": "ht-api-test-2",
        "readme_markdown": None,
        "repo_url": "https://github.com/langchain-ai/langgraph-example",
        "knative": {
            "type": "services",
            "name": "ht-api-test-2-8ed76fdc43da5d16a3638924dbb59fbe",
        },
        "tracer_session_id": tracer_session_id,
        "input_json_schemas": None,
        "output_json_schemas": None,
        "tool_name": None,
        "example_input": None,
        "api_key_id": None,
        "host_integration_id": TEST_HOST_INTEGRATION_ID_ONE_UUID,
        "metadata": {
            "platform": "k8s",
            "shareable": True,
        },
        "lc_hosted": True,
        "repo_branch": "main",
        "build_on_push": False,
        "description": None,
        "display_name": None,
        "status": "READY",
        "custom_url": None,
        "remote_reconciled": False,
        "secrets": {},
        "container_spec": {
            "max_scale": 10,
            "cpu": 2,
            "memory_mb": 2048,
            "min_scale": None,
        },
    }

    host_revisions = await db.fetch(
        """
        SELECT *
        FROM host_revisions
        WHERE project_id = $1
        """,
        project_id,
    )
    assert len(host_revisions) == 1

    host_revision = host_revisions[0]
    assert dict(host_revision) == {
        "id": AnyUUID(),
        "project_id": project_id,
        "created_at": AnyDateTime(),
        "updated_at": AnyDateTime(),
        "repo_path": "langgraph.json",
        "repo_commit": "main",
        "knative": None,
        "status": "AWAITING_BUILD",
        "status_message": None,
        "completed_at": None,
        "gcp_build_name": None,
        "metadata": {
            "created_by": {
                "type": "user",
                "identity_id": AnyStr(),
            },
            "repo_commit_sha": "62283478da8a58f8492c36e33e07a3b4cd49d905",
        },
        "image_path": None,
        "container_spec": {},
    }

    # Create another revision and verify that the null (None) fields are used
    # from the previous revision.

    # But first, manually set the previous revision status to DEPLOYED so a
    # new revision can be created. Otherwise, the API will reject the request
    # (409 Conflict).
    await db.execute(
        """
        UPDATE host_revisions
        SET status = 'DEPLOYED'
        WHERE project_id = $1
        """,
        project_id,
    )

    res = await http_tenant_one.post(
        f"/v1/projects/{project_id}/revisions",
        json={
            "image_path": None,
            "repo_path": None,
            "env_vars": [],
            "shareable": True,
            "container_spec": None,
        },
    )
    assert res.status_code == 200, res.content

    host_revisions = await db.fetch(
        """
        SELECT *
        FROM host_revisions
        WHERE project_id = $1
        ORDER BY created_at DESC
        """,
        project_id,
    )
    assert len(host_revisions) == 2
    host_revision = host_revisions[0]
    assert host_revision["image_path"] is None
    assert host_revision["repo_path"] == "langgraph.json"
    # don't need to assert all of the columns


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_list_revisions_for_project_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the GET /v1/projects/{project_id}/revisions endpoint."""
    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-revision-to-list",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-revision-to-list",
        {
            "type": "services",
            "name": "ht-revision-to-list-ad3461e19627570ba1f9553427209e92",
        },
        tracer_session_id,
        {"platform": "k8s"},
    )
    project_id = project_row["id"]

    revision_row = await db.fetchrow(
        """
        INSERT INTO host_revisions (project_id, repo_path, repo_commit, knative)
        VALUES ($1, $2, $3, $4)
        RETURNING id
        """,
        project_id,
        "langgraph.json",
        "main",
        {
            "type": "revisions",
            "name": "ht-revision-to-list-ad3461e19627570ba1f9553427209e92-00001",
        },
    )
    revision_id_1 = revision_row["id"]

    revision_row = await db.fetchrow(
        """
        INSERT INTO host_revisions (project_id, repo_path, repo_commit, knative)
        VALUES ($1, $2, $3, $4)
        RETURNING id
        """,
        project_id,
        "langgraph.json",
        "main",
        {
            "type": "revisions",
            "name": "ht-revision-to-list-ad3461e19627570ba1f9553427209e92-00002",
        },
    )
    revision_id_2 = revision_row["id"]

    # Test 1: call GET /v1/projects/{project_id}/revisions
    res = await http_tenant_one.get(f"/v1/projects/{project_id}/revisions")

    # verify API response
    assert res.status_code == 200, res.content

    headers = res.headers
    x_pagination_total = headers.get("X-Pagination-Total")
    assert x_pagination_total == "2"

    res_body = res.json()
    assert isinstance(res_body, list)
    assert len(res_body) == 2

    revision_1 = res_body[0]
    revision_2 = res_body[1]

    # revisions are returned in created_at order desc (most recent first)
    assert revision_2 == {
        "id": str(revision_id_1),
        "project_id": str(project_id),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "repo_path": "langgraph.json",
        "repo_commit": "main",
        "status": "UNKNOWN",
        "status_message": None,
        "gcp_build_name": None,
        "metadata": {},
        "image_path": None,
        "resource": {
            "id": {
                "type": "revisions",
                "name": "ht-revision-to-list-ad3461e19627570ba1f9553427209e92-00001",
            },
            "hosted_langserve_revision_id": str(revision_id_1),
            "env_vars": None,
        },
        "container_spec": {},
    }

    assert revision_1 == {
        "id": str(revision_id_2),
        "project_id": str(project_id),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "repo_path": "langgraph.json",
        "repo_commit": "main",
        "status": "UNKNOWN",
        "status_message": None,
        "gcp_build_name": None,
        "metadata": {},
        "image_path": None,
        "resource": {
            "id": {
                "type": "revisions",
                "name": "ht-revision-to-list-ad3461e19627570ba1f9553427209e92-00002",
            },
            "hosted_langserve_revision_id": str(revision_id_2),
            "env_vars": None,
        },
        "container_spec": {},
    }

    # Test 2: call GET /v1/projects/{project_id}/revisions?limit=1
    res = await http_tenant_one.get(
        f"/v1/projects/{project_id}/revisions",
        params={"limit": 1},
    )

    # verify API response
    assert res.status_code == 200, res.content

    headers = res.headers
    x_pagination_total = headers.get("X-Pagination-Total")
    assert x_pagination_total == "2"

    res_body = res.json()
    assert isinstance(res_body, list)
    assert len(res_body) == 1
    # don't need to verify response body contents


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_get_revision_for_project_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the GET /v1/projects/{project_id}/revisions/{revision_id} endpoint.

    Note: This test is slightly redundant because its implementation
    overlaps with other endpoints (e.g. POST /v1/projects/{project_id}/revisions).
    Regardless, we'll include it for good measure.
    """
    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-revision-to-get",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-revision-to-get",
        {
            "type": "services",
            "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92",
        },
        tracer_session_id,
        {"platform": "k8s"},
    )
    project_id = project_row["id"]

    revision_row = await db.fetchrow(
        """
        INSERT INTO host_revisions (project_id, repo_path, repo_commit, knative)
        VALUES ($1, $2, $3, $4)
        RETURNING id
        """,
        project_id,
        "langgraph.json",
        "main",
        {
            "type": "revisions",
            "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92-00001",
        },
    )
    revision_id = revision_row["id"]

    # call GET /v1/projects/{project_id}/revisions/{revision_id}
    res = await http_tenant_one.get(
        f"/v1/projects/{project_id}/revisions/{revision_id}"
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body == {
        "id": str(revision_id),
        "project_id": str(project_id),
        "created_at": AnyStr(),
        "updated_at": AnyStr(),
        "repo_path": "langgraph.json",
        "repo_commit": "main",
        "status": "UNKNOWN",
        "status_message": None,
        "gcp_build_name": None,
        "metadata": {},
        "image_path": None,
        "resource": {
            "id": {
                "type": "revisions",
                "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92-00001",
            },
            "hosted_langserve_revision_id": AnyStr(),
            "env_vars": None,
        },
        "container_spec": {},
    }


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_deploy_revision_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the POST /v1/projects/{project_id}/revisions/{revision_id}/deploy
    endpoint.
    """
    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-revision-to-list",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-revision-to-get",
        {
            "type": "services",
            "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92",
        },
        tracer_session_id,
        {"platform": "k8s"},
    )
    project_id = project_row["id"]

    revision_row_1 = await db.fetchrow(
        """
        INSERT INTO host_revisions (project_id, repo_path, repo_commit, knative)
        VALUES ($1, $2, $3, $4)
        RETURNING id
        """,
        project_id,
        "langgraph.json",
        "main",
        {
            "type": "revisions",
            "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92-00001",
        },
    )
    revision_id_1 = revision_row_1["id"]

    revision_row_2 = await db.fetchrow(
        """
        INSERT INTO host_revisions (project_id, repo_path, repo_commit, knative, status)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        project_id,
        "langgraph.json",
        "main",
        {
            "type": "revisions",
            "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92-00002",
        },
        "DEPLOYING",
    )
    revision_id_2 = revision_row_2["id"]

    # call POST /v1/projects/{project_id}/revisions/{revision_id}/deploy
    res = await http_tenant_one.post(
        f"/v1/projects/{project_id}/revisions/{revision_id_1}/deploy"
    )

    # verify API response
    assert res.status_code == 400, res.content
    assert (
        res.json()["detail"] == "Revision must be the latest revision to be redeployed."
    )

    # call POST /v1/projects/{project_id}/revisions/{revision_id}/deploy
    res = await http_tenant_one.post(
        f"/v1/projects/{project_id}/revisions/{revision_id_2}/deploy"
    )

    # verify API response
    assert res.status_code == 400, res.content
    assert (
        res.json()["detail"] == "Revision must be in DEPLOYED state to be redeployed."
    )


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_interrupt_revision_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the POST /v1/projects/{project_id}/revisions/{revision_id}/interrupt
    endpoint.
    """
    # create seed data
    tracer_session_row = await db.fetchrow(
        """
        INSERT INTO tracer_session (id, name, tenant_id)
        VALUES ($1, $2, $3)
        RETURNING id
        """,
        uuid4(),
        "ht-revision-to-list",
        UUID(TEST_TENANT_ONE_UUID),
    )
    tracer_session_id = tracer_session_row["id"]

    project_row = await db.fetchrow(
        """
        INSERT INTO host_projects (tenant_id, name, knative, tracer_session_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
        """,
        UUID(TEST_TENANT_ONE_UUID),
        "ht-revision-to-get",
        {
            "type": "services",
            "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92",
        },
        tracer_session_id,
        {"platform": "k8s"},
    )
    project_id = project_row["id"]

    revision_row = await db.fetchrow(
        """
        INSERT INTO host_revisions (project_id, repo_path, repo_commit, knative)
        VALUES ($1, $2, $3, $4)
        RETURNING id
        """,
        project_id,
        "langgraph.json",
        "main",
        {
            "type": "revisions",
            "name": "ht-revision-to-get-ad3461e19627570ba1f9553427209e92-00001",
        },
    )
    revision_id = revision_row["id"]

    # call POST /v1/projects/{project_id}/revisions/{revision_id}/interrupt
    res = await http_tenant_one.post(
        f"/v1/projects/{project_id}/revisions/{revision_id}/interrupt"
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body is None

    # verify control plane state
    host_revision_row = await db.fetchrow(
        """
        SELECT status, status_message
        FROM host_revisions
        WHERE id = $1
        """,
        revision_id,
    )

    assert dict(host_revision_row) == {
        "status": "INTERRUPTED",
        "status_message": (
            "Revision interrupted by user. This status can change to a "
            "failed state if the revision was in progress when interrupted, "
            "or deployed if it was in the process of being deployed while "
            "interrupted."
        ),
    }


@pytest.mark.usefixtures("wait_for_service")
@pytest.mark.usefixtures("clean_db")
async def test_get_project_usage_endpoint(
    http_tenant_one: AsyncClient,
    db: asyncpg.Connection,
) -> None:
    """This test verifies the GET /v1/projects/{project_id}/usage endpoint."""
    # create seed data
    project_id = "00000000-0000-0000-0000-000000000001"
    await db.execute(
        """
        INSERT INTO remote_metrics (from_timestamp, to_timestamp, measures, tags)
        VALUES ($1, $2, $3, $4)
        """,
        datetime.fromisoformat("2025-05-14T00:00:00.000000+00:00"),
        datetime.fromisoformat("2025-05-14T00:05:00.000000+00:00"),
        {"langgraph.platform.runs": 1, "langgraph.platform.nodes": 1},
        {
            "langgraph.platform.project_id": project_id,
            "langgraph.platform.tenant_id": TEST_TENANT_ONE_UUID,
        },
    )
    await db.execute(
        """
        INSERT INTO remote_metrics (from_timestamp, to_timestamp, measures, tags)
        VALUES ($1, $2, $3, $4)
        """,
        datetime.fromisoformat("2025-05-14T00:00:00.000000+00:00"),
        datetime.fromisoformat("2025-05-14T00:05:00.000000+00:00"),
        {"langgraph.platform.runs": 2, "langgraph.platform.nodes": 10},
        {
            "langgraph.platform.project_id": project_id,
            "langgraph.platform.tenant_id": TEST_TENANT_ONE_UUID,
        },
    )
    await db.execute(
        """
        INSERT INTO remote_metrics (from_timestamp, to_timestamp, measures, tags)
        VALUES ($1, $2, $3, $4)
        """,
        datetime.fromisoformat("2025-05-14T00:06:00.000000+00:00"),
        datetime.fromisoformat("2025-05-14T00:11:00.000000+00:00"),
        {"langgraph.platform.runs": 0, "langgraph.platform.nodes": 0},
        {
            "langgraph.platform.project_id": project_id,
            "langgraph.platform.tenant_id": TEST_TENANT_ONE_UUID,
        },
    )
    await db.execute(
        """
        INSERT INTO remote_metrics (from_timestamp, to_timestamp, measures, tags)
        VALUES ($1, $2, $3, $4)
        """,
        datetime.fromisoformat("2025-05-14T00:06:00.000000+00:00"),
        datetime.fromisoformat("2025-05-14T00:12:00.000000+00:00"),
        {"langgraph.platform.runs": 99, "langgraph.platform.nodes": 99},
        {
            "langgraph.platform.project_id": project_id,
            "langgraph.platform.tenant_id": TEST_TENANT_ONE_UUID,
        },
    )

    # call GET /v1/projects/{project_id}/usage
    res = await http_tenant_one.get(
        f"/v1/projects/{project_id}/usage",
        params={
            "start_time": "2025-05-14T00:00:00.000000+00:00",
            "end_time": "2025-05-14T00:11:01.000000+00:00",
        },
    )

    # verify API response
    assert res.status_code == 200, res.content

    res_body = res.json()
    assert res_body == {
        "runs_executed": 3,
        "nodes_executed": 11,
        "standby_minutes": 11,
    }
