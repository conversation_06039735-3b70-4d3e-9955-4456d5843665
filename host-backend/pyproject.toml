[tool.poetry]
name = "host-backend"
version = "0.1.0"
description = ""
authors = []
packages = [{ include = "host" }]

[tool.poetry.dependencies]
python = "^3.11"
kubernetes_asyncio = "^28.2.0"
google-cloud-build = "^3.20.1"
google-auth = "^2.23.0"
lc-config = { path = "../lc_config", develop = true }
lc-database = {path = "../lc_database", develop = true}
lc-logging = { path = "../lc_logging", develop = true }
lc-metrics = { path = "../lc_metrics", develop = true }
langchainplus = { path = "../smith-backend", develop = true }
google-cloud-run = "^0.10.0"
minio = "^7.2.0"
google-cloud-secret-manager = "^2.16.4"
google-api-python-client = "^2.108.0"
starlette-cramjam = "^0.3.2"
google-cloud-storage = "^2.13.0"
pygithub = "^2.1.1"
tenacity = "8.2.3"
elasticsearch = "^8.14.0"
aioboto3 = ">=13.1.1"
google-cloud-artifact-registry = "^1.11.5"
aiocache = "^0.12.2"
pycurl = "^7.45.3"
google-crc32c = "1.5.0"
urllib3 = ">=1.26.19"
zstandard = "^0.23.0"
uhashring = "2.3"
pyyaml = "^6.0.2"
truststore = "^0.10.1"
httpcore = {git = "https://github.com/langchain-ai/httpcore"}
cryptography = ">=44.0.2"

[tool.poetry.group.dev.dependencies]
poethepoet = "^0.21.1"
ruff = "^0.11.2"
mypy = "^1.3.0"
pytest-mock = "^3.12.0"
types-requests = "^2.32.0.20240712"
types-pycurl = "^7.45.3.20240421"


[tool.poetry.group.test.dependencies]
pytest = "^7.4.0"
pytest-watch = "^4.2.0"
pytest-xdist = "^3.6.1"



[tool.poetry.group.typing.dependencies]
types-pyyaml = "^6.0.12.20250402"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
lint.select = [
  "E",  # pycodestyle
  "F",  # pyflakes
  "I",  # isort
]
lint.ignore = ["E501"]
target-version = "py311"

[tool.mypy]
ignore_missing_imports = "True"
follow_imports = "skip"
exclude = ["notebooks"]

[tool.poe.tasks]
run-singleton.shell = "AUTH_TYPE=none uvicorn host.main:app --reload --reload-dir=host --port 8300 --workers 1 --no-access-log" # none/singleton auth
run.shell = "AUTH_TYPE=supabase uvicorn host.main:app --reload --reload-dir=host --port 8300 --no-access-log" # web auth

_format-ruff-check = "ruff check --fix ."
_format-ruff-format = "ruff format ."
format = ["_format-ruff-check", "_format-ruff-format"]

_lint-mypy = "mypy ."
_lint-ruff-version="ruff version"
_lint-ruff-check = "ruff check ."
_lint-ruff-format = "ruff format --check ."
lint = ["_lint-ruff-version", "_lint-mypy", "_lint-ruff-check", "_lint-ruff-format"]

[tool.poe.tasks.pretest]
env = { LANGCHAIN_ENV = "local_test" }
shell = """
  chmod 0600 ./.pgpass.txt
  PGPASSFILE=./.pgpass.txt psql -w -h localhost -U postgres -c 'DROP DATABASE IF EXISTS langsmith_test;' -c 'CREATE DATABASE langsmith_test;'
  cd ../smith-backend
  LANGCHAIN_ENV=local_test poetry run alembic upgrade head
"""

[tool.poe.tasks.test]
env = { LANGCHAIN_ENV = "local_test" }
deps = ["pretest"]

shell = """
  set -e
  AUTH_TYPE=supabase SUPABASE_JWT_SECRET=super-secret poetry run pytest -vv --durations=5 ${test_pattern:+"-k $test_pattern"}
  AUTH_TYPE=none                                      poetry run pytest --durations=5 ${test_pattern:+"-k $test_pattern"}
"""

[[tool.poe.tasks.test.args]]
name = "test_pattern"
help = "A test name pattern to pass to pytest"
default = ""
options = ["-k"]

[tool.poe.tasks._watch-env]
env = { LANGCHAIN_ENV = "local_test" }
shell = "ptw --beforerun='poe pretest'"

[tool.poe.tasks.test-watch-api]
env = { AUTH_TYPE = "api_key", API_KEY_SALT = "super-secret-salt" }
sequence = ["_watch-env"]

[tool.poe.tasks.test-watch-web]
env = { AUTH_TYPE = "supabase", SUPABASE_JWT_SECRET = "super-secret" }
sequence = ["_watch-env"]

[tool.poe.tasks.test-watch-singleton]
env = { AUTH_TYPE = "none" }
sequence = ["_watch-env"]
