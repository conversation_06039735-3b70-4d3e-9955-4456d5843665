# Host Backend

This module contains the backend service responsible for CRUD operations for LangGraph Platform.

API docs: [LangGraph Control Plane API (Beta)](https://langchain-ai.github.io/langgraph/cloud/reference/api/api_ref_control_plane.html)
OpenAPI spec: [Link](https://github.com/langchain-ai/langgraph/blob/main/docs/docs/cloud/reference/api/openapi_control_plane.json)

## Developing locally

### Pre-requisites

Go through the installation outlined in the root [README](../README.md), and run the services including smith-backend and smith-frontend.

#### 0. Add secrets to your environment

In the root repo path, run

```commandline
cp -r secrets.example ./secrets
```

The `GITHUB_CLIENT_SECRET` is required (`Github Test App Client Secret` in Engineering vault in 1Password).

#### 1. Install Google Cloud SDK and Set Up Permissions

```commandline
brew install --cask google-cloud-sdk
```

Set the project to `langchain-test-387119`. You'll only need to do this once unless you change projects.

```commandline
gcloud config set project langchain-test-387119
```

Log into Google Cloud with this command (you may need to do this time to time):

```commandline
gcloud auth application-default login
```

The following permissions are required (ask for permissions in #it-requests):

| Permission | Resource |
| ---------- | -------- |
| secretmanager.versions.access | projects/langchain-test-387119/secrets/hosted-langserve-github-app-pem/versions/latest |
| run.services.setIamPolicy | projects/langchain-test-387119/locations/us-central1/services/* |

Ask an Owner of the [<EMAIL>](https://console.cloud.google.com/iam-admin/groups/037m2jsg28fgzsf?organizationId=758730753893&orgonly=true&supportedpurview=organizationId) Google Group to add you as an Owner.

#### 2. Install host-backend dependencies

```commandline
cd host-backend
poetry install
```

#### 3. Set `HOST_WORKER_TENANT_ID` and `HOST_WORKER_RECONCILIATION_CRON_ENABLED`

Set the `HOST_WORKER_TENANT_ID` environment variable in `.env.local_dev` to a tenant ID in your local database. Set `HOST_WORKER_RECONCILIATION_CRON_ENABLED=true` in `.env.local_dev` (revert this before committing changes).

#### 4. Run host-backend

From the host-backend, run:

```commandline
poetry run poe run-singleton
```

If running `smith-backend` and `smith-frontend` with authentication, run:

```commandline
poetry run poe run
```

## Kubernetes

### Knative

Coming soon... 

### Operations

#### Clusters

Connect to a cluster to view Knative services deployed to the cluster.

| Environment | Cluster | GCP Project ID | Region | Platform |
| ----------- | ------- | -------------- | ------ | -------- |
| `local` | [`langgraph-cloud-test`](https://console.cloud.google.com/kubernetes/clusters/details/us-central1/langgraph-cloud-test/) | `langchain-test-387119` | us-central1 | Knative (`k8s`) |
| `local` | [`langgraph-cloud-us-west1`](https://console.cloud.google.com/kubernetes/clusters/details/us-west1/langgraph-cloud-us-west1/) | `langchain-test-387119` | us-west1 | Vanilla K8s (`k8s_vanilla`) |
| `dev` | [`langgraph-cloud-dev`](https://console.cloud.google.com/kubernetes/clusters/details/us-central1/langgraph-cloud-dev/) | `langchain-dev` | us-central1 | Knative (`k8s`) |
| `dev` | [`langgraph-cloud-us-west1`](https://console.cloud.google.com/kubernetes/clusters/details/us-west1/langgraph-cloud-us-west1/) | `langchain-dev` | us-west1 | Vanilla K8s (`k8s_vanilla`) |
| `staging` | [`langgraph-cloud-staging`](https://console.cloud.google.com/kubernetes/clusters/details/us-central1/langgraph-cloud-staging/) | `langchain-staging` | us-central1 | Knative (`k8s`) |
| `prod` | [`langgraph-cloud-prod`](https://console.cloud.google.com/kubernetes/clusters/details/us-central1/langgraph-cloud-prod/) | `langchain-prod` | us-central1 |  Knative (`k8s`) |
| `prod` | [`langgraph-cloud-us-west1`](https://console.cloud.google.com/kubernetes/clusters/details/us-west1/langgraph-cloud-us-west1/) | `langchain-prod` | us-west1 | Vanilla K8s (`k8s_vanilla`) |
| `eu-prod` | [`langgraph-cloud-eu-prod`](https://console.cloud.google.com/kubernetes/clusters/details/europe-west4/langgraph-cloud-eu-prod/) | `langchain-prod` | europe-west-4 | Knative (`k8s`) |

##### Connect to a Cluster
1. Log in to `gcloud` CLI.

        gcloud auth login

1. Install `gke-gcloud-auth-plugin`.

        gcloud components install gke-gcloud-auth-plugin

1. Get cluster credentials. Example:

        gcloud container clusters get-credentials langgraph-cloud-test --project langchain-test-387119 --region us-central1

1. Display cluster contexts. Example:

        kubectl config get-contexts

1. Select cluster to connect to. Example:

        kubectl config use-context gke_langchain-test-387119_us-central1-c_langgraph-cloud-test

#### Knative Services

1. Display all Knative services.

         kubectl get services.serving.knative.dev -n default

1. Describe a Knative service.

         kubectl describe services.serving.knative.dev <service_name> -n default

1. Find K8s Deployment of the Knative service(s) by Project ID, Revision ID, Tenant ID, or Organization ID. After getting the name of the K8s deployment, navigate to the deployment in the GCP Console UI. This will be helpful for debugging user specific issues.

         # filtering by ls_project_id will return all Knative revisions for the Knative servicce
         kubectl get deployment -l ls_project_id=<project_id> -n default

         # filtering by ls_revision_id will return an individual Knative revision of a Knative service
         kubectl get deployment -l ls_revision_id=<revision_id> -n default

         # filtering by ls_tenant_id or ls_organization_id will return all Knative revisions for the tenant and organziation respectively
         kubectl get deployment -l ls_tenant_id=<tenant_id> -n default
         kubectl get deployment -l ls_organization_id=<org_id> -n default

1. Application logs for the K8s deployment can be found in the GCP Console UI.

1. Manually create a new revision for a Knative service by calling the `host-backend` API.

         python3 create_service_key.py
         JWT Token: <SERVICE_KEY>

         curl -XPOST https://api.host.langchain.com/projects/<PROJECT_ID>/revisions -H 'Content-Type: application/json' -H 'X-Service-Key: <SERVICE_KEY>' -H 'X-Tenant-Id: <TENANT_ID>' -H 'X-Organization-Id: <ORG_ID>' -d '{"image_path": null, "repo_path": "langgraph.json", "env_vars": [], "shareable": false, "container_spec": null}'

#### Postgres

Some Postgres instances (e.g. `k8s_vanilla` + `dev`) are deployed in K8s.

1. List all Postgres deployments in K8s (i.e. `StatefulSet` or `Pod`). All `StatefulSet` and `Pod` names are prefixed with `lg-`.

        kubectl get statefulsets | grep "lg-"
        kubectl get pods | grep "lg-"

1. Get a Postgres deployment by project ID.

        kubectl get statefulset -l ls_project_id=<project_id>

1. Log into Postgres. User name (`-U`) is `postgres` and database name (`-d`) is `postgres`.

        kubectl exec -it <pod-name> -- psql -U <username> -d <database-name>

1. Once logged into Postgres, list all tables and describe a table.

        postgres=# \d;
        postgres=# \d run;

1. Check available disk space. Disk volume is mounted to `/var/lib/postgresql/data`.

        kubectl exec -it <pod-name> -- df -h

1. Increase disk size. Get the `PersistantVolumeClaim` of the `StatefulSet`, then patch it.

        kubectl get pvc -l app=<statefulset-name>
        kubectl patch pvc <pvc-name> -p '{"spec": {"resources": {"requests": {"storage": "20Gi"}}}}' --type=merge

1. Restart the Postgres instance. Under rare circumstances, the Postgres instance may need to be restarted (e.g. deadlocks). This will cause downtime for the LangGraph Server deployment (i.e. the LangGraph Server will disconnect from Postgres and K8s will kill the container and restart the deployment).

        kubectl rollout restart statefulset/<statefulset-name>

1. In GCP, check `Quotas & System Limits` > `Compute Engine API` > `Persistent Disk SSD (GB)` quota. If this quota is exceeded, `PersistentVolume` for the Postgres `StatefulSet` cannot be created (i.e. deployment of the LangGraph Server will fail).

#### Secrets

1. Display all K8s secrets.

        kubectl get secrets -n default

1. Describe a secret. The K8s secret corresponding to the Knative service has the name: `<knative_service_name>-secrets`

        kubectl describe <secret_name> secrets -n default
   
1. Decode key value of secret.

        kubectl get secret <secret_name> -o jsonpath='{.data.<key_name>}' | base64 -d

1. Modify key value of secret.

        echo -n '<plain_text_secret_value>' | base64
        kubectl patch secret <secret_name> -p='{"data":{"<key_name>": "<base64_encoded_secret_value>"}}'

#### KEDA ScaledObject

These steps are for debugging issues with KEDA autoscaling.

1. Display all KEDA ScaledObjects.

        kubectl get scaledobjects -n default

1. Describe a KEDA ScaledObject. The name of the ScaledObject is the same as the Knative service name. Find the `Hpa Name` in the output.

        kubectl describe scaledobject <scaled_object_name> -n default

1. View events of the HPA resource (e.g. scaling events) and current metrics of the queries configured in the ScaledObject (e.g. Prometheus metrics).

        kubectl describe hpa <hpa_name> -n default

1. View logs of KEDA operator. Example:

        kubectl get pods -n keda
        kubectl logs keda-operator-647b44c8bb-jzjkv -n keda

#### Prometheus

Knative metrics are available in Prometheus.

1. Display Prometheus Server K8s service (not available in `local` environment).

        kubectl get service prometheus-server -n prometheus 

1. Set up port forwarding to access Prometheus UI from local machine.

        kubectl port-forward svc/prometheus-server -n prometheus 8080:80

1. Open Prometheus UI in browser. Navigate to http://localhost:8080/.

1. Available Knative service metrics: https://knative.dev/docs/serving/services/service-metrics/. Example query:
   
        revision_queue_depth{service_name="<knative_service_name>"}

#### Miscellaneous

1. Patch multiple deployments at once. Test out each part of the piped command before running the entire command. Example:

        kubectl get deployments -o name | grep <search_string> | awk -F '/' '{print $2}' | xargs -I {} kubectl patch deployment {} --patch '{"spec":{"template":{"spec":{"enableServiceLinks":false}}}}' --type=merge

## Testing Self-hosted `host-backend`

The following high-level steps are derived from [this guide](https://www.notion.so/Release-Test-Self-Hosted-and-Docker-Compose-0b71d0532fb445d5918d7a8ab9d1e653?pvs=4):

1. Make changes to `host-backend` code.
1. Follow Steps 1-3 from [this guide](https://www.notion.so/Release-Test-Self-Hosted-and-Docker-Compose-0b71d0532fb445d5918d7a8ab9d1e653?pvs=4#6f9f011342b44fcb91e129441320a470). Make note of the image tag from GCR/GAR.
1. Export access keys for AWS account `agola11` (************). Connect to the EKS cluster in AWS: `aws eks update-kubeconfig --name langsmith-eks --region us-west-2`
1. Create a new K8s namespace: `kubectl create namespace <namespace>`
1. Modify/update `self_hosted_deploy.sh` as needed (see file for details). Run the script: `./self_hosted_deploy.sh`
1. Modify/update `self_hosted_config.yaml` as needed (see file for details).
1. Clone the repo [`langchain-ai/helm`](https://github.com/langchain-ai/helm/). Make note of the full path to `helm/charts/langsmith`.
   1. Set the `VITE_HOST_ENABLED` to `"1"` for the `frontend` deployment.
1. Deploy via `helm`: `helm upgrade -i -f self_hosted_config.yaml -n <namespace> --create-namespace langsmith <FULL PATH TO LANGSMITH CHART> --debug`
1. Set port forwarding for LangSmith UI: `kubectl port-forward svc/langsmith-frontend 8080:80`. Navigate to http://localhost:8080/.
1. Delete the deployment: `helm uninstall -n <namespace> langsmith`

### Testing Self-hosted `langgraph-dataplane`

This section is under construction...

1. Follow the setup steps under `Testing Self-hosted host-backend`.
1. `helm upgrade -i -f self_hosted_listener_config.yaml -n <namespace> --create-namespace langgraph-dataplane <FULL PATH TO LANGRAPH_DATAPLANE CHART> --debug`

## Testing Deploy From Docker Image

In order to test this locally, you must make sure your account has the `Service Account OpenID Connect Identity Token Creator` role in order for it to be able to generate access tokens.

## Testing Deploy from AWS (BYOC)

### AWS Accounts

Navigate to the AWS console UI through the [AWS access portal](https://d-90679f3a37.awsapps.com/start/#/?tab=accounts).

| Account Name | Account ID | Notes |
| ----------- | ----------- | ----- |
| `agola11` | `************` | `host-backend` uses credentials from the `HostBackendRole*` to assume role of the `LangGraphCloudBYOCRole` in the dataplane accounts. |
| `dataplane-account` | `************` | For testing in `dev` and `local`. Resources are created in this account. |
| `dataplane-account-staging` | `************` | For testing in `staging`. Resources are created in this account. |
| `dataplane-account-prod` | `************` | For testing in `prod`. Resources are created in this account. |

### Authorize

Ask Mukil to invite you to the AWS IAM Identity Center. Then follow the `aws configure sso` instructions under the `agola11` account. **IMPORTANT**: Do not select `dataplane-account`, make sure to sign in as `agola11`.

### Enable Feature Flag

First, you need to enable the `langgraph_deploy_own_cloud_enabled` feature flag. Either update in the database with the below instructions 
or update in [Metronome local customers list](https://app.metronome.com/env-qa/customers) (search by organization ID)

- Go to Docker Desktop, select the `smith-backend` container, and then the `postgres` container within it.
- Run `psql -U postgres` to enter the postgres server
- Call `UPDATE organizations SET config = jsonb_set(config, '{langgraph_deploy_own_cloud_enabled}', 'true'::jsonb) WHERE id = '<YOUR_ORG_ID>';` to set the feature flag

### Build Image

High-level steps to build an image with the latest base LangGraph API image. This is only necessary when testing locally because building an "unlicensed" image is needed.

1. Create a deployment in GCP (i.e. non-BYOC workflow). An unlicensed build is automatically created. Find the build in GCP Container Registry.
1. Connect to GCP Container Registry and run `docker pull` to pull the image locally.
1. Tag the image with the required format for pushing to AWS Elastic Container Registry (ECR).
1. Connect to AWS ECR repository and run `docker push` to push the image to the remote repository.

### Deploy

Once the feature flag is set, when you click the "New Deployment" button you should see fields for the AWS region, account ID, and image path. You can of course fill it in with any valid values but here are some default values for testing in case you don't want to go through the effort of making your own image:

- `Region`: us-west-2
- `Account ID`: ************
- `Image Path`: ************.dkr.ecr.us-west-2.amazonaws.com/mukil-test:amd64

You should then be able to click deploy and see your deployment working!

## Testing Webhooks
To test GitHub webhooks locally, create the file `secrets/GITHUB_WEBHOOK_SECRET` with a dummy secret value. Then run the following commands:

```
SECRET="<dummy_secret>"

# Mock the payload coming from GitHub
# Reference: https://docs.github.com/en/webhooks/webhook-events-and-payloads
PAYLOAD='{"ref": "refs/heads/main", "created": false, "deleted": false, "installation": {"id": ********}, "repository": {"url": "https://github.com/andrewnguonly/langgraph-example"}}'

# Generate the HMAC SHA-256 signature
SIGNATURE=$(echo -n "$PAYLOAD" | openssl dgst -sha256 -hmac "$SECRET" | sed 's/^.* //')

# Specify the payload's corresponding event type (e.g. push)
curl -X POST http://127.0.0.1:8300/v1/integrations/github/webhook -H "Content-Type: application/json" -H "X-Hub-Signature-256: sha256=$SIGNATURE" -H "X-GitHub-Event: push" -H "X-GitHub-Delivery: 12345" -d "$PAYLOAD"
```

## Testing Email Notifications for Unused Projects

Email templates
1. [unused-host-project](https://account.postmarkapp.com/servers/********/templates/********/edit)
1. [deleted-host-project](https://account.postmarkapp.com/servers/********/templates/********/edit)

Set up
1. Set `ENABLE_UNUSED_PROJECT_NOTIFICATION_EMAILS=true` in `.env.local_dev`.
1. Get access to Postmark. Create file `secrets/POSTMARK_SERVER_TOKEN`. Get the production API token from Postmark or 1Password and save it to the file.
1. Create a new deployment.
1. In Postgres, set the `api_keys.last_used_at` column for the API key associated with the project to some date far in the past.

        update api_keys set last_used_at = '2025-01-11 00:15:45.193108' where id = '2e77628a-4c21-497f-9337-1cd113473b20';

1. In the next run of the `delete_unused_projects` cron job, the project will be marked as `UNUSED` and an email notification will be sent.
1. In the next run after that, the project will be deleted (i.e. `AWAITING_DELETE`) and an email notification will be sent.

Tear down
1. Remove the API token from `secrets/POSTMARK_SERVER_TOKEN`. This is a precautionary step to avoid inadvertently sending emails.
1. Set `ENABLE_UNUSED_PROJECT_NOTIFICATION_EMAILS=false` in `.env.local_dev`.

## Feature Flags

See the [Notion wiki](https://www.notion.so/LangSmith-Feature-Flags-and-Config-6b4cd2d65bc343748dcb6b0830a57da5) for additional details.

Only run the following SQL statements in non-production environments. For production, update these values in [Metronome](https://app.metronome.com/customers) (preferred) or the [Organizations Panel in Retool](https://langchaininc.retool.com/apps/ecb3f7c0-d5d6-11ee-b3b0-6f320284c1ce/Organizations%20Panel), which updates the DB directly.

### `can_use_langgraph_cloud`
Enable/disable LangGraph Platform for an organization. Currently, this flag is only relevant for organizations on non-paid LangSmith plans.

Set this in Metronome custom fields for the organization. Alternatively, set in the DB:

```sql
SELECT COUNT(*) from organizations WHERE (config->>'can_use_langgraph_cloud')::boolean IS TRUE;

UPDATE organizations SET config = jsonb_set(config, '{can_use_langgraph_cloud}', 'true'::jsonb) WHERE id = '<insert org ID here>';
```

### `max_langgraph_cloud_deployments`

Maximum number of LangGraph Platform deployments allowed per organization.

```sql
UPDATE organizations SET config = jsonb_set(config, '{max_langgraph_cloud_deployments}', '1'::jsonb) WHERE id = '<insert org ID here>';
```

### `max_free_langgraph_cloud_deployments`

Maximum number of free LangGraph Platform deployments allowed per organization.

```sql
UPDATE organizations SET config = jsonb_set(config, '{max_free_langgraph_cloud_deployments}', '1'::jsonb) WHERE id = '<insert org ID here>';
```

### `langgraph_deploy_own_cloud_enabled`

Enable/disable LangGraph Platform BYOC for an organization.

Set this in Metronome custom fields for the organization. Alternatively, set in the DB:

```sql
UPDATE organizations SET config = jsonb_set(config, '{langgraph_deploy_own_cloud_enabled}', 'true'::jsonb) WHERE id = '<insert org ID here>';
```

### `langgraph_remote_reconciler_enabled`

Enable/disable remote reconciliation for an organization. Remote reconcilation means that an organization has a remote instance of the reconciler deployed in their environment.

Set this in Metronome custom fields for the organization. Alternatively, set in the DB:

```sql
UPDATE organizations SET config = jsonb_set(config, '{langgraph_remote_reconciler_enabled}', 'true'::jsonb) WHERE id = '<insert org ID here>';
```

## GitHub Integration

| Environment | GitHub App Name |
| ----------- | --------------- |
| `local` | [`hosted-langserve-test`](https://github.com/apps/hosted-langserve-test) |
| `dev` | [`hosted-langserve-dev`](https://github.com/apps/hosted-langserve-dev) |
| `staging` | [`hosted-langserve-staging`](https://github.com/apps/hosted-langserve-staging) |
| `prod` | [`hosted-langserve`](https://github.com/apps/hosted-langserve) |
| `eu-prod` | [`hosted-langserve-eu`](https://github.com/apps/hosted-langserve-eu) |

**Secrets**
- `GITHUB_CLIENT_SECRET`: Create/revoke client secrets from the GitHub app setttings page. Store the secret in 1Password: `Github <env> App Client Secret`.
- `GITHUB_STATE_JWT_SECRET`: TBD (i.e. how do you rotate this?). Store the secret in 1Password: `Github <env> State JWT Secret`.
- `GITHUB_WEBHOOK_SECRET`: Set/remove webhook secret from the GitHub app setttings page. Store the secret in 1Password: `Github <env> App Client Secret`.

## Operations

### Manually Scale or Configure Resources for a Deployment

To manually scale or configure resources for a deployment, update the `container_spec` column in `host_revisions` for the latest revision of the project. Then, create a new revision for the project. The deployment process will read the `container_spec` column from the previous revision when creating the new revision.
```sql
update host_projects set container_spec = '{"min_scale": 2, "max_scale": 12, "cpu": 2, "memory_mb": 4096}' where id = '<project_id>';
```
`min_scale`, `max_scale`, `cpu`, and `memory_mb` keys can be set to `null` or omitted. If so, the default values will be used.

**WARNING**: ECS + Fargate only supports specific configurations for CPU and memory. See: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definition_parameters.html

### Create New Revision for a Deployment

This can be used to create a new revision for any deployment (i.e. any tenant).

1. Get the `X_SERVICE_AUTH_JWT_SECRET` from 1Password. Create a service key (`<SERVICE_KEY>`).

        X_SERVICE_AUTH_JWT_SECRET=<secret> python3 create_service_key.py

2. Get the project ID. Call API to create new revision.

        curl -XPOST https://api.host.langchain.com/v1/projects/{project_id}/revisions -H 'X-Service-Key: <SERVICE_KEY>' -H 'Content-Type: application/json' -d '{"image_path": null, "repo_path": null, "env_vars": null, "shareable": null, "container_spec": null}'
