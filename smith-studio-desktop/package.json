{"name": "langgraph-studio-desktop", "version": "0.0.37", "private": true, "type": "module", "main": "packages/main/dist/index.js", "productName": "LangGraph Studio", "repository": {"type": "git", "url": "https://github.com/langchain-ai/langgraph-studio"}, "build": {"appId": "com.langchain.langgraph.studio"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "scripts": {"build": "npm run build:main && npm run build:preload && npm run build:renderer", "build:main": "cd ./packages/main && vite build", "build:preload": "cd ./packages/preload && vite build", "build:renderer": "rm -rf ./packages/renderer/dist && cd ../smith-frontend && yarn build:graph && cp -R build/studio/ ../smith-studio-desktop/packages/renderer/dist && find ../smith-studio-desktop/packages/renderer/dist -depth -name '*.js.map' -delete", "compile": "cross-env MODE=production npm run build && electron-builder build --config electron-builder.yml", "compile:dev": "npm run compile --dir --config.asar=false", "release": "cross-env MODE=production npm run build && electron-builder build --config electron-builder.yml --publish=always", "test": "npm run test:main && npm run test:preload && npm run test:e2e", "test:e2e": "npm run build && vitest run", "test:main": "vitest run -r packages/main --passWithNoTests", "test:preload": "vitest run -r packages/preload --passWithNoTests", "test:watch:main": "vitest watch -r packages/main --passWithNoTests", "test:watch:preload": "vitest watch -r packages/preload --passWithNoTests", "dev": "node scripts/watch.js", "lint": "eslint . --ext js,mjs,cjs,ts,mts,cts,tsx", "typecheck:main": "tsc --noEmit -p packages/main/tsconfig.json", "typecheck:preload": "tsc --noEmit -p packages/preload/tsconfig.json", "typecheck": "npm run typecheck:main && npm run typecheck:preload", "postinstall": "patch-package && cross-env ELECTRON_RUN_AS_NODE=1 electron scripts/update-electron-vendors.js", "format": "prettier --write \"**/*.{js,mjs,cjs,ts,mts,cts,tsx,json}\"", "get-version": "node -p -e 'require(\"./package.json\").version'"}, "devDependencies": {"@types/node": "20.14.11", "@typescript-eslint/eslint-plugin": "7.17.0", "cross-env": "7.0.3", "electron": "31.2.1", "electron-builder": "24.13.3", "eslint": "9.7.0", "patch-package": "^8.0.0", "playwright": "1.45.2", "prettier": "^3.3.3", "typescript": "5.5.3", "unplugin-auto-expose": "0.3.0", "vite": "^6.3.5", "vitest": "^3.0.9"}, "dependencies": {"chokidar": "^3.6.0", "dedent": "^1.5.3", "electron-store": "^10.0.0", "electron-updater": "6.2.1", "execa": "^9.3.0", "get-port": "^7.1.0", "which": "^4.0.0", "yaml": "^2.4.5", "zod": "^3.23.8"}, "resolutions": {"vite": "^6.3.5"}}