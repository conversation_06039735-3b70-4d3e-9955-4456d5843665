name: 'Set PR Status'
description: 'Sets a status on a PR using GitHub CLI'
inputs:
  state:
    description: 'Status state (pending, success, failure)'
    required: true
  description:
    description: 'Status description'
    required: true
  context:
    description: 'Status context/title (displayed in GitHub UI)'
    required: false
    default: 'CI Status'
  token:
    description: 'GitHub token'
    required: true
    default: ${{ github.token }}
  sha:
    description: 'Specific SHA to set status on (defaults to github.sha if not provided)'
    required: false
    default: ${{ github.sha }}
  target_url:
    description: 'URL that will be linked to from the status (defaults to the commit checks page)'
    required: false

runs:
  using: 'composite'
  steps:
    - name: Set PR Status
      shell: bash
      run: |
        COMMIT_SHA="${{ inputs.sha }}"
        REPO="${{ github.repository }}"
        STATE="${{ inputs.state }}"
        DESCRIPTION="${{ inputs.description }}"
        CONTEXT="${{ inputs.context }}"
        
        # Use custom target URL if provided, otherwise default to commit checks page
        if [ -n "${{ inputs.target_url }}" ]; then
          TARGET_URL="${{ inputs.target_url }}"
        else
          TARGET_URL="https://github.com/$REPO/commit/$COMMIT_SHA/checks"
        fi
        
        echo "Setting status on SHA: $COMMIT_SHA"
        gh api \
          --method POST \
          -H "Accept: application/vnd.github+json" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          /repos/$REPO/statuses/$COMMIT_SHA \
          -f state="$STATE" \
          -f context="$CONTEXT" \
          -f description="$DESCRIPTION" \
          -f target_url="$TARGET_URL"
      env:
        GITHUB_TOKEN: ${{ inputs.token }}
