name: '1. Deploy: Dev'

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to dev? Set to false if you just want to build and push images.'
        type: choice
        required: true
        default: 'false'
        options:
          - 'true'
          - 'false'
      experimental:
        description: 'run experimental features not run by default in dev'
        type: choice
        required: false
        default: 'false'
        options:
          - 'true'
          - 'false'

env:
  GOLANG_MIGRATE_DEB_FILE: 'migrate_4.18.1_amd64.deb'

jobs:
  tag-and-get-version:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.get-version.outputs.image_tag }}
      git_sha: ${{ steps.get-version.outputs.git_sha }}
      version: ${{ steps.get-version.outputs.version }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install root dependencies
        run: |
          pip install poetry

      - name: Get semantic version from pyproject
        id: get-version
        working-directory: smith-backend
        run: |
          echo "version=$(poetry version -s)" >> $GITHUB_OUTPUT
          echo "git_sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "image_tag=$(poetry version -s)-$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

  backend-images-amd64:
    needs: ['tag-and-get-version']
    uses: ./.github/workflows/build_push_amd64_backend_images.yml
    secrets:
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
      gcr_credentials: ${{ secrets.GCR_JSON_KEY_DEV }}
    with:
      project: 'langchain-dev'
      tag: ${{ needs.tag-and-get-version.outputs.image_tag }}

  artifacts:
    needs: ['tag-and-get-version']
    uses: ./.github/workflows/build_shared_image_artifacts.yml
    secrets:
      datadog_api_key_us: ${{ secrets.DATADOG_API_KEY }}
      datadog_api_key_eu: ${{ secrets.DATADOG_API_KEY_EU }}
    with:
      tag: ${{ needs.tag-and-get-version.outputs.image_tag }}

  frontend-images-amd64:
    needs: ['artifacts', 'tag-and-get-version']
    uses: ./.github/workflows/build_push_amd64_frontend_images.yml
    secrets:
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
      gcr_credentials: ${{ secrets.GCR_JSON_KEY_DEV }}
    with:
      project: 'langchain-dev'
      tag: ${{ needs.tag-and-get-version.outputs.image_tag }}

  langgraph-debugger-images:
    needs: ['artifacts', 'tag-and-get-version']
    uses: ./.github/workflows/build_push_langgraph_debugger_images.yml
    secrets:
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
    with:
      tag: ${{ needs.tag-and-get-version.outputs.git_sha }}

  deploy:
    # Don't auto deploy for now
    if: inputs.deploy == 'true' || contains(github.ref, 'main')
    runs-on: ubuntu-latest-m

    # we only deploy amd64 images, so don't wait for the arm builds
    needs:
      ['tag-and-get-version', 'backend-images-amd64', 'frontend-images-amd64']

    # Prevent concurrent deploys to avoid conflicting DB migrations
    concurrency:
      group: ${{ github.workflow }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_DEV }}

      - uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ secrets.GOOGLE_CREDENTIALS }}
          instance: ${{ secrets.DEV_INSTANCE_CONNECTION_NAME }}

      - name: Run database migrations against Postgres
        run: |
          docker run --network host --env-file ./.env.migrations \
            -e POSTGRES_DATABASE_URI=${{ secrets.DEV_POSTGRES_DATABASE_URI }} \
            -e LANGCHAIN_ENV=development \
            gcr.io/langchain-dev/langsmith-backend:${{ needs.tag-and-get-version.outputs.image_tag }}-linux-amd64 \
            alembic upgrade head

      - name: Checkout deployments repo
        uses: actions/checkout@v4
        with:
          repository: langchain-ai/deployments
          path: deployments
          ref: main
          token: ${{ secrets.DEPLOYMENTS_PAT }}

      - name: Update Docker image in Terraform files (dev)
        run: |
          cd deployments/environments/gcp/dev
          # Modify the main.tf terraform file
          sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-and-get-version.outputs.image_tag}}-linux-amd64\"|" main.tf
          sed -i "s|tag: \"[^\"]*-linux-amd64|tag: \"${{ needs.tag-and-get-version.outputs.image_tag }}-linux-amd64|" kubernetes/langsmith_helm_values.yaml
          sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{ needs.tag-and-get-version.outputs.image_tag }}-linux-amd64\"|" kubernetes/langsmith_helm_values.yaml
          # Replace versions in all kustomize values files.
          # Update datadog tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i "s|tags.datadoghq.com/version: \"[^\"]*-linux-amd64|tags.datadoghq.com/version: \"${{ needs.tag-and-get-version.outputs.image_tag }}-linux-amd64|"
          # Update image tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-and-get-version.outputs.image_tag}}-linux-amd64\"|"

      - name: Create Pull Request
        id: create-pr
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          title: 'Update Dev Docker image to ${{ needs.tag-and-get-version.outputs.image_tag }}'
          commit-message: 'Update Docker image'
          base: 'main'
          branch: 'actions/update-dev-docker-image-${{ needs.tag-and-get-version.outputs.git_sha }}'
          body: 'Update Docker image to ${{ needs.tag-and-get-version.outputs.image_tag }}'
          path: deployments

      - name: Wait for PR to be available
        run: sleep 10  # Wait for 10 seconds

      # Use GH token to approve PR, must be different from the one used to create PR
      - name: Approve PR
        env:
          GH_TOKEN: ${{ secrets.DEPLOYMENTS_APPROVER_PAT }}
        working-directory: deployments
        run: |
          gh pr review ${{ steps.create-pr.outputs.pull-request-url }} --approve

      - name: Enable Pull Request Automerge
        uses: peter-evans/enable-pull-request-automerge@v3
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
          repository: langchain-ai/deployments
