name: 'CI: Go Tests'

on:
  push:
    branches: [main]
  pull_request:
  workflow_dispatch:

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time,
# so it's better to cancel pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHON_VERSION: '3.11'
  # Info at:
  # https://packagecloud.io/golang-migrate/migrate/packages/ubuntu/jammy/migrate_4.18.1_amd64.deb?distro_version_id=237
  GOLANG_MIGRATE_DEB_FILE: 'migrate_4.18.1_amd64.deb'

jobs:
  check-go-changes:
    permissions: write-all
    runs-on: ubuntu-latest
    outputs:
      changed: ${{ steps.check-changes.outputs.go == 'true' && !(steps.check-version-bump.outputs.version-bump == 'true') }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: check-changes
        with:
          list-files: json
          filters: |
            go:
              - '.github/workflows/ci_test_go.yaml'
              - 'smith-go/**'
      - name: Check if only a version bump pr
        id: check-version-bump
        run: |
          result=$(echo '${{ steps.check-changes.outputs.go_files }}' | jq '
            length == 2 and any(. == "smith-backend/app/__init__.py") and any(. == "smith-backend/pyproject.toml")
          ')
          echo $result
          echo "version-bump=$result" >> $GITHUB_ENV
          echo "version-bump=$result" >> $GITHUB_OUTPUT

  test-smith-go:
    needs: check-go-changes
    if: ${{ needs.check-go-changes.outputs.changed == 'true' }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-target: ["test", "test-redis-cluster", "test-redis-sharded"]
    env:
      LANGSMITH_LICENSE_KEY: ${{ secrets.LANGSMITH_LICENSE_KEY }}
    defaults:
      run:
        working-directory: smith-go
    services:
      db:
        image: postgres:14.7-alpine
        env:
          POSTGRES_PASSWORD: postgres
        ports: ['5432:5432']
        options: --health-cmd pg_isready --health-interval 1s --health-timeout 5s --health-retries 5
      redis:
        image: redis:7-alpine
        ports: ['6379:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5
      redis1:
        image: redis:7-alpine
        ports: ['6380:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5
      redis2:
        image: redis:7-alpine
        ports: ['6381:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5

      minio:
        image: minio/minio:edge-cicd
        env:
          MINIO_ROOT_USER: minioadmin1
          MINIO_ROOT_PASSWORD: minioadmin1
        ports:
          - '9002:9000'
          - '9003:9001'
        options: --name=minio
      # Azurite is used for local development with Azure Blob Storage
      # https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite
      azurite:
        image: mcr.microsoft.com/azure-storage/azurite
        env:
          AZURITE_ACCOUNTS: 'admin:password'
        ports:
          - '10000:10000'
    steps:
      - uses: actions/checkout@v4
      - name: Run Clickhouse
        working-directory: .
        run: |
          docker run -d --rm --name clickhouse-server --network host \
          --volume ${PWD}/smith-backend/clickhouse/users.xml:/etc/clickhouse-server/users.d/users.xml \
          --volume ${PWD}/smith-backend/clickhouse/u.xml:/etc/clickhouse-server/users.d/u.xml \
          --publish 8123:8123 \
          --publish 9000:9000 \
          clickhouse/clickhouse-server:24.2
      - name: Run Minio Setup
        run: |
          docker run --rm --network host --entrypoint /bin/sh minio/mc:latest -c "
          /usr/bin/mc alias remove local;
          /usr/bin/mc alias set --quiet --api s3v4 local http://127.0.0.1:9002 minioadmin1 minioadmin1;
          /usr/bin/mc mb --ignore-existing local/langsmith-images/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region-test;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data-test/;"
      - name: Run Azurite setup
        env:
          AZURE_STORAGE_CONNECTION_STRING: 'DefaultEndpointsProtocol=http;AccountName=admin;AccountKey=password;BlobEndpoint=http://127.0.0.1:10000/admin;'
        run: |
          docker run --rm --network host -e AZURE_STORAGE_CONNECTION_STRING=$AZURE_STORAGE_CONNECTION_STRING --entrypoint /bin/sh mcr.microsoft.com/azure-cli -c "
          az storage container create --name langsmith-images-test;
          az storage container create --name langsmith-images-single-region-test;"
      - name: Run Quickwit
        # Note we are relative to smith-go path here
        run: |
          docker run -d --rm --name quickwit --network host \
          --publish 7280:7280 \
          --volume ${PWD}/../smith-backend/quickwit:/quickwit-config \
          --entrypoint quickwit \
          quickwit/quickwit:edge \
          run
      - name: Set root suid on tar
        # Need this to restore migrate under /usr/bin
        run: sudo chown root /bin/tar && sudo chmod u+s /bin/tar
      - name: Restore golang-migrate cached dependencies
        uses: actions/cache@v3
        id: cached-go-migrate
        env:
          SEGMENT_DOWNLOAD_TIMEOUT_MIN: "3"
        with:
          path: /usr/bin/migrate
          key: langchainplus-${{ runner.os }}-${{ runner.arch }}-${{ env.GOLANG_MIGRATE_DEB_FILE }}
      - name: Install golang-migrate
        if: steps.cached-go-migrate.outputs.cache-hit != 'true'
        # Info at:
        # https://packagecloud.io/golang-migrate/migrate/packages/ubuntu/jammy/migrate_4.16.2_amd64.deb?distro_version_id=237
        #
        # Doing a direct `wget` and installation of the `deb` archive lets us avoid
        # an expensive `apt update` operation in which we refresh the entire index while ignoring
        # everything except the `golang-migrate` program in it.
        run: |
          wget -O migrate.deb "https://packagecloud.io/golang-migrate/migrate/packages/ubuntu/$(lsb_release -sc)/${{ env.GOLANG_MIGRATE_DEB_FILE }}/download.deb"
          sudo dpkg -i migrate.deb
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version-file: smith-go/go.mod
          cache-dependency-path: smith-go/go.sum
      - name: Go Tidy
        if: ${{ matrix.test-target == 'test' }}
        run: go mod tidy && git diff --exit-code
      - name: Go Mod
        if: ${{ matrix.test-target == 'test' }}
        run: go mod download
      - name: Go Mod Verify
        if: ${{ matrix.test-target == 'test' }}
        run: go mod verify
      - name: Go Format
        if: ${{ matrix.test-target == 'test' }}
        run: gofmt -s -w . && git diff -- ':!../go.work.sum' --exit-code
      - name: Go Vet
        if: ${{ matrix.test-target == 'test' }}
        run: go vet ./...
      - name: Go Nil Check
        if: ${{ matrix.test-target == 'test' }}
        run: |
          go install go.uber.org/nilaway/cmd/nilaway@latest
          nilaway -include-pkgs="langchain.com" ./...
      - name: Go Build
        run: go build -o /dev/null ./...
      # We keep this step as late as possible, so that we do as much other work as possible
      # while Clickhouse is starting up. This means we probably won't have to wait at all here.
      - name: Wait for Clickhouse
        shell: bash
        # Use the same kind of readiness check that Clickhouse itself uses.
        # https://github.com/ClickHouse/ClickHouse/blob/1d46ed75db43de34ebe5255d8aa0935017708f58/docker/server/entrypoint.sh#L129-L139
        run: |
          tries=30
          while ! wget --spider --no-check-certificate -T 1 -q 'http://127.0.0.1:8123/ping' 2>/dev/null; do
            if [ "$tries" -le "0" ]; then
                echo >&2 'ClickHouse init process failed.'
                exit 1
            fi
            tries=$(( tries-1 ))
            sleep 1
          done
      - name: Setup Clickhouse User
        working-directory: smith-backend
        run: ./clickhouse/setup_clickhouse.sh clickhouse-server
      - name: Install Poetry (for Postgres migrations)
        working-directory: .
        run: |
          pip install poetry
          mkdir secrets
      - name: Set up Python (for Postgres migrations)
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: poetry
          cache-dependency-path: 'smith-backend/poetry.lock'
      - name: Install  Python dependencies (for Postgres migrations)
        working-directory: smith-backend
        run: poetry install
      - name: Install redis
        if: ${{ matrix.test-target == 'test-redis-cluster' }}
        run: |
            sudo apt-get update
            sudo apt-get install -y redis
      - name: Setup Redis Cluster
        if: ${{ matrix.test-target == 'test-redis-cluster' }}
        run: |
            cd .. && cd smith-backend && make setup-test-redis-cluster
      - name: Go Test
        run: make ${{ matrix.test-target }}
      - name: Go Bench
        if: ${{ matrix.test-target == 'test' }}
        run: |
          go test -bench=. -benchmem ./storage/... -benchtime=100x > bench.txt
      - name: Store benchmark result
        if: ${{ matrix.test-target == 'test' && github.ref == 'refs/heads/main' }}
        uses: benchmark-action/github-action-benchmark@v1
        with:
          tool: 'go'
          output-file-path: smith-go/bench.txt
          github-token: ${{ secrets.GITHUB_TOKEN }}
          auto-push: true
          # Show alert with commit comment on detecting possible performance regression
          alert-threshold: '200%'
          comment-on-alert: true
          fail-on-alert: false
          # Save benchmark data to gh-pages branch
          gh-pages-branch: 'gh-pages'
          benchmark-data-dir-path: 'dev/bench'

  check-go-docs:
    needs: check-go-changes
    if: ${{ needs.check-go-changes.outputs.changed == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version-file: smith-go/go.mod
          cache-dependency-path: smith-go/go.sum
      - name: Install swaggo
        run: go install github.com/swaggo/swag/cmd/swag@latest
      - name: Check API docs
        working-directory: smith-go
        run: make check-api-docs

  results:
    if: ${{ always() }}
    runs-on: ubuntu-latest
    name: Go Final Results
    needs: [test-smith-go, check-go-docs]
    steps:
      - run: |
          result_smith_go="${{ needs.test-smith-go.result }}"
          result_go_docs="${{ needs.check-go-docs.result }}"
          if [[ ($result_smith_go == "success" || $result_smith_go == "skipped") &&
                ($result_go_docs == "success" || $result_go_docs == "skipped") ]]; then
            exit 0
          else
            exit 1
          fi
