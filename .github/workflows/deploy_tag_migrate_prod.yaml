name: '1. Deploy: Prod'

on:
  workflow_dispatch:

permissions: write-all

jobs:
  tag-prod:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.run-vars.outputs.image_tag }}
      git_sha: ${{ steps.run-vars.outputs.git_sha }}
      version: ${{ steps.run-vars.outputs.version }}
      create_tag: ${{ steps.check-tag.outputs.create_tag }}

    steps:
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_PROD }}

      - name: Login to Google Artifact Registry
        uses: docker/login-action@v2
        with:
          registry: us-docker.pkg.dev
          username: _json_key
          password: ${{ secrets.GOOGLE_CREDENTIALS }}

      - uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS }}'

      - name: Get Latest Successful Image Build
        id: run-vars
        run: |
          echo "image_tag=$(gcloud container images list-tags gcr.io/langchain-staging/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))')" >> $GITHUB_OUTPUT
          echo "version=$(gcloud container images list-tags gcr.io/langchain-staging/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))' | cut -d- -f1)" >> $GITHUB_OUTPUT
          echo "git_sha=$(gcloud container images list-tags gcr.io/langchain-staging/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))' | cut -d- -f2)" >> $GITHUB_OUTPUT

      - name: Tag Smith Backend
        run: |
          docker buildx imagetools create -t us-docker.pkg.dev/langchain-artifacts/langchain/langsmith-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-backend:latest -t gcr.io/langchain-prod/langchainpro-backend:${{steps.run-vars.outputs.image_tag}} gcr.io/langchain-staging/langsmith-backend:latest

      - name: Tag Hosted Langserve Backend
        run: |
          docker buildx imagetools create -t us-docker.pkg.dev/langchain-artifacts/langchain/hosted-langserve-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/hosted-langserve-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/hosted-langserve-backend:latest -t gcr.io/langchain-prod/langchain-host-backend:${{steps.run-vars.outputs.image_tag}} gcr.io/langchain-staging/hosted-langserve-backend:latest

      - name: Tag Smith Frontend
        run: |
          docker buildx imagetools create -t us-docker.pkg.dev/langchain-artifacts/langchain/langsmith-frontend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-frontend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-frontend:latest -t gcr.io/langchain-prod/langchainplus-frontend-dynamic:${{steps.run-vars.outputs.image_tag}} gcr.io/langchain-staging/langsmith-frontend:latest

      - name: Tag Smith Proxy
        run: |
          docker buildx imagetools create -t us-docker.pkg.dev/langchain-artifacts/langchain/langsmith-proxy:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-proxy:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-proxy:latest gcr.io/langchain-staging/langsmith-proxy:latest

      - name: Tag Go Backend
        run: |
          docker buildx imagetools create -t us-docker.pkg.dev/langchain-artifacts/langchain/langsmith-go-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-go-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-go-backend:latest gcr.io/langchain-staging/langsmith-go-backend:latest

      - name: Tag Ace Backend
        run: |
          docker buildx imagetools create -t us-docker.pkg.dev/langchain-artifacts/langchain/langsmith-ace-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-ace-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-ace-backend:latest gcr.io/langchain-staging/langsmith-ace-backend:latest

      - name: Tag Smith Playground
        run: |
          docker buildx imagetools create -t us-docker.pkg.dev/langchain-artifacts/langchain/langsmith-playground:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-playground-python:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-prod/langsmith-playground-python:latest gcr.io/langchain-staging/langsmith-playground-python:latest

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for Tag
        id: check-tag
        run: |
          TAG="${{ steps.run-vars.outputs.version }}"
          if git show-ref --tags --verify --quiet "refs/tags/${TAG}"; then
            echo "create_tag=false" >> $GITHUB_OUTPUT
          else
            echo "create_tag=true" >> $GITHUB_OUTPUT
          fi

      - name: Tag Sha
        if: steps.check-tag.outputs.create_tag == 'true'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git tag ${{ steps.run-vars.outputs.version }} ${{ steps.run-vars.outputs.version }}-${{ steps.run-vars.outputs.git_sha}}-rc
          git push origin ${{ steps.run-vars.outputs.version }}

  migrations-us:
    runs-on: ubuntu-latest-m

    # we only tag images on prod
    needs: ['tag-prod']

    # Prevent concurrent deploys to avoid conflicting DB migrations
    concurrency:
      group: ${{ github.workflow }}-us

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_PROD }}

      - name: CloudSQL Proxy US
        uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ secrets.GOOGLE_CREDENTIALS }}
          instance: ${{ secrets.prod_INSTANCE_CONNECTION_NAME }}

      - name: Run Database Migrations against Postgres US
        run: |
          docker run --network host --env-file ./.env.migrations \
          -e POSTGRES_DATABASE_URI=${{ secrets.PROD_POSTGRES_DATABASE_URI }}  \
          -e LANGCHAIN_ENV=production \
          gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-prod.outputs.image_tag }} \
          alembic upgrade head

      - name: Run migrations against Clickhouse US
        run: |
          docker run --network host gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-prod.outputs.image_tag }} /bin/bash -c \
            'PROCESSED_DIR=$(./scripts/process_templates.sh) && \
             migrate -source "file://${PROCESSED_DIR}" \
             -database "clickhouse://${{ secrets.PROD_CLICKHOUSE_URL }}?username=${{ secrets.PROD_CLICKHOUSE_USERNAME }}&password=${{ secrets.PROD_CLICKHOUSE_PASSWORD }}&database=default&x-multi-statement=true&secure=true&x-migrations-table-engine=MergeTree" up'

  migrations-eu:
    runs-on: ubuntu-latest-m

    # we only tag images on prod
    needs: ['tag-prod']

    # Prevent concurrent deploys to avoid conflicting DB migrations
    concurrency:
      group: ${{ github.workflow }}-eu

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_PROD }}

      - name: CloudSQL Proxy EU
        uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ secrets.GOOGLE_CREDENTIALS }}
          instance: ${{ secrets.eu_prod_INSTANCE_CONNECTION_NAME }}

      - name: Run Database Migrations against Postgres EU
        run: |
          docker run --network host --env-file ./.env.migrations \
          -e POSTGRES_DATABASE_URI=${{ secrets.EU_PROD_POSTGRES_DATABASE_URI }}  \
          -e LANGCHAIN_ENV=production \
          gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-prod.outputs.image_tag }} \
          alembic upgrade head

      - name: Run migrations against Clickhouse EU
        run: |
          docker run --network host gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-prod.outputs.image_tag }} /bin/bash -c \
            'PROCESSED_DIR=$(./scripts/process_templates.sh) && \
             migrate -source "file://${PROCESSED_DIR}" \
             -database "clickhouse://${{ secrets.EU_PROD_CLICKHOUSE_URL }}?username=${{ secrets.EU_PROD_CLICKHOUSE_USERNAME }}&password=${{ secrets.EU_PROD_CLICKHOUSE_PASSWORD }}&database=default&x-multi-statement=true&secure=true&x-migrations-table-engine=MergeTree" up'

  update-deployed-images:
    runs-on: ubuntu-latest-m

    # we only tag images after migrations
    needs: ['tag-prod', 'migrations-us', 'migrations-eu']
    steps:
      - name: Checkout deployments repo
        uses: actions/checkout@v4
        with:
          repository: langchain-ai/deployments
          path: deployments
          ref: main
          token: ${{ secrets.DEPLOYMENTS_PAT }}

      - name: Update Docker image in Terraform files (prod)
        working-directory: deployments/environments/gcp/prod
        run: |
          # Modify the terraform file
          sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-prod.outputs.image_tag}}\"|" main.tf
          sed -i "s|tag: \"[^\"]*-linux-amd64|tag: \"${{ needs.tag-prod.outputs.image_tag }}|" kubernetes/langsmith_helm_values.yaml
          sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{ needs.tag-prod.outputs.image_tag }}\"|" kubernetes/langsmith_helm_values.yaml
          # Update datadog tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i "s|tags.datadoghq.com/version: \"[^\"]*-linux-amd64|tags.datadoghq.com/version: \"${{ needs.tag-prod.outputs.image_tag }}|"
          # Update image tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-prod.outputs.image_tag}}\"|"

      - name: Update Docker image in Terraform files (eu-prod)
        working-directory: deployments/environments/gcp/eu-prod
        run: |
          # Modify the terraform file
          sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-prod.outputs.image_tag}}\"|" main.tf
          sed -i "s|tag: \"[^\"]*-linux-amd64|tag: \"${{ needs.tag-prod.outputs.image_tag }}|" kubernetes/langsmith_helm_values.yaml
          sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{ needs.tag-prod.outputs.image_tag }}\"|" kubernetes/langsmith_helm_values.yaml
          # Update datadog tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i "s|tags.datadoghq.com/version: \"[^\"]*-linux-amd64|tags.datadoghq.com/version: \"${{ needs.tag-prod.outputs.image_tag }}|"
          # Update image tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-prod.outputs.image_tag}}\"|"

      - name: Create Pull Request
        id: create-pr
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          title: 'Update Prod Docker image to ${{ needs.tag-prod.outputs.image_tag }}'
          commit-message: 'Update Prod Docker image'
          base: 'main'
          branch: 'actions/update-prod-docker-image-${{ needs.tag-prod.outputs.git_sha }}'
          body: 'Update Prod Docker image to ${{ needs.tag-prod.outputs.image_tag }}'
          path: deployments

      - name: Wait for PR to be available
        run: sleep 10  # Wait for 10 seconds

      # Use GH token to approve PR, must be different from the one used to create PR
      - name: Approve PR
        env:
          GH_TOKEN: ${{ secrets.DEPLOYMENTS_APPROVER_PAT }}
        working-directory: deployments
        run: |
          gh pr review ${{ steps.create-pr.outputs.pull-request-url }} --approve

      - name: Enable Pull Request Automerge
        uses: peter-evans/enable-pull-request-automerge@v3
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
          repository: langchain-ai/deployments

  create-release:
    needs: ['tag-prod']
    if: needs.tag-prod.outputs.create_tag == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install root dependencies
        env:
          POETRY_VIRTUALENVS_CREATE: false
        run: |
          pipx install poetry
          poetry self add poetry-bumpversion

      - name: Get last release
        id: get-last-release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo "last_version=$(gh release view --json tagName | jq -r '.tagName')" >> $GITHUB_OUTPUT

      - name: Create Release
        id: create_release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # Check if release already exists
          if gh release view ${{ needs.tag-prod.outputs.version }}; then
            echo "Release already exists"
            exit 0
          fi
          gh release create ${{ needs.tag-prod.outputs.version }} -t "Release ${{ needs.tag-prod.outputs.version }}" --generate-notes --latest --notes-start-tag ${{ steps.get-last-release.outputs.last_version }}

      - name: Bump version
        id: bump-version
        working-directory: smith-backend
        run: |
          make bump-version
          echo "new_version=$(poetry version -s)" >> $GITHUB_OUTPUT

      - name: Create Pull Request to bump version
        id: create-pr
        uses: peter-evans/create-pull-request@v6
        with:
          token:  ${{ secrets.DEPLOYMENTS_PAT }}
          title:  'ci: Update Semantic Version to ${{ steps.bump-version.outputs.new_version }}'
          commit-message: 'Update Semantic Version'
          base:   'main'
          branch: 'actions/update-semantic-version-${{ steps.bump-version.outputs.new_version }}'
          body: |
            ## Description
            
            Bump the semantic version to **${{ steps.bump-version.outputs.new_version }}**.
      
            ## Test Plan
            - [x] CI

      # Use GH token to approve PR, must be different from the one used to create PR
      - name: Approve PR
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh pr review ${{ steps.create-pr.outputs.pull-request-number }} --approve

      - name: Auto-merge PR
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh pr merge ${{ steps.create-pr.outputs.pull-request-number }} --squash --auto

  artifacts:
    needs: ['tag-prod']
    uses: ./.github/workflows/build_shared_image_artifacts.yml
    secrets:
      datadog_api_key_us: ${{ secrets.DATADOG_API_KEY }}
      datadog_api_key_eu: ${{ secrets.DATADOG_API_KEY_EU }}
    with:
      ref: ${{ needs.tag-prod.outputs.version }}

  backend-images-multiplatform:
    needs: ['tag-prod', 'create-release']
    uses: ./.github/workflows/build_merge_multiplatform_backend_images.yml
    secrets:
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
      gcr_credentials: ${{ secrets.GCR_JSON_KEY_PROD }}
    with:
      ref: ${{ needs.tag-prod.outputs.version }}
      tag: ${{ needs.tag-prod.outputs.version }}-${{ needs.tag-prod.outputs.git_sha }}

  frontend-images-multiplatform:
    needs: ['artifacts', 'tag-prod', 'create-release']
    uses: ./.github/workflows/build_merge_multiplatform_frontend_images.yml
    secrets:
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
      gcr_credentials: ${{ secrets.GCR_JSON_KEY_PROD }}
    with:
      ref: ${{ needs.tag-prod.outputs.version }}
      tag: ${{ needs.tag-prod.outputs.version }}-${{ needs.tag-prod.outputs.git_sha }}

  release-prod-images:
    needs:
      [
        'backend-images-multiplatform',
        'frontend-images-multiplatform',
        'tag-prod',
      ]
    uses: ./.github/workflows/release_publish_prod_images.yaml
    secrets:
      acr_password: ${{ secrets.ACR_PASSWORD }}
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
      gcr_credentials: ${{ secrets.GCR_JSON_KEY_PROD }}
    with:
      source-tag: ${{ needs.tag-prod.outputs.version }}-${{ needs.tag-prod.outputs.git_sha }}
      target-tag: ${{ needs.tag-prod.outputs.version }}

  notify-slack:
    needs: ['create-release']
    uses: ./.github/workflows/notify_slack.yaml
    secrets: inherit
    with:
      langsmith_env: 'production'
