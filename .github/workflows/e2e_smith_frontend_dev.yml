name: "CI: <PERSON> Playwright Tests - Dev"

on:
  schedule:
    - cron: '0 17-23 * * *'  # Runs every hour from 9 am to 3 pm PST (5 pm to 11 pm UTC)
    - cron: '0 0-3 * * *'    # Runs every hour from 4 pm to 7 pm PST (12 am to 3 am UTC next day)
  workflow_dispatch: # Allows manual triggering of the workflow

jobs:
  smith-frontend-e2e:
    timeout-minutes: 300
    runs-on: ubuntu-latest-l
    env:
      BASE_URL: https://dev.smith.langchain.com
      ENDPOINT_URL: https://dev.api.smith.langchain.com
      SUPABASE_SECRET_ROLE: ${{ secrets.DEV_SUPABASE_SECRET }}
      SUPABASE_URL: ${{ secrets.DEV_SUPABASE_URL }}
      EKS_TEST_ACCOUNT_PASSWORD: ${{ secrets.EKS_TEST_ACCOUNT_PASSWORD }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY_E2E }}
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY_E2E }}
      GROQ_API_KEY: ${{ secrets.GROQ_API_KEY_E2E }}
      VERTEX_AI_API_CREDS: ${{ secrets.VERTEX_AI_API_CREDS_E2E }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: yarn install --frozen-lockfile
        working-directory: smith-frontend-e2e
      - name: Install Playwright Browsers
        run: yarn run playwright install --with-deps
        working-directory: smith-frontend-e2e
      - name: Run Playwright tests and capture output
        run: |
          set -o pipefail
          yarn run playwright test | tee test-results.txt
        working-directory: smith-frontend-e2e
        timeout-minutes: 250
      - name: List files in the directory
        run: ls -la
        working-directory: smith-frontend-e2e
      - name: Parse Test Results
        if: failure()
        id: parse_test_results
        run: |
          # Find the line number of the last occurrence of "failed"
          last_failed_line=$(grep -n "failed" test-results.txt | tail -1 | cut -d: -f1)
          
          # Find the line number of the last occurrence of "flaky"
          last_flaky_line=$(grep -n "flaky" test-results.txt | tail -1 | cut -d: -f1)
          
          # Check if there are any failed tests
          if [ -z "$last_failed_line" ]; then
            echo "Something else went wrong. Please check the logs."
            echo "::set-output name=failing_tests::Something else went wrong. Please check the logs."
            exit 0
          fi
          
          # Check if "flaky" occurs after "failed", and set the last line for awk accordingly
          if [ -n "$last_flaky_line" ] && [ "$last_flaky_line" -gt "$last_failed_line" ]; then
            awk_last_line=$last_flaky_line
          else
            awk_last_line=$(wc -l < test-results.txt)
          fi
          
          # Extract failing test details using awk and remove U+2500 character
          FAILING_TESTS=$(awk "NR>${last_failed_line} && NR<${awk_last_line}" test-results.txt | grep "\[chromium\] ›" | sed 's/\xe2\x94\x80//g')
          
          # Count the number of failed tests
          NUM_FAILED_TESTS=$(echo "$FAILING_TESTS" | grep -c "\[chromium\] ›")
          
          # Print the number of failing tests and the tests themselves
          if [ -n "$FAILING_TESTS" ]; then
            echo "Number of failing tests: $NUM_FAILED_TESTS"
            echo "$FAILING_TESTS"
            ESCAPED_FAILING_TESTS=$(echo -e "Number of failing tests: $NUM_FAILED_TESTS\n$FAILING_TESTS" | sed ':a;N;$!ba;s/\n/\\n/g')
            echo "::set-output name=failing_tests::$ESCAPED_FAILING_TESTS"
          else
            echo "No failing tests detected."
            echo "::set-output name=failing_tests::"
          fi
        working-directory: smith-frontend-e2e
      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-report
          path: smith-frontend-e2e/playwright-report/
          retention-days: 7
      - name: Send custom JSON data to Slack workflow
        if: failure()
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "SLACK_MESSAGE": "There were e2e test failures in the most recent dev run!\n\n${{ steps.parse_test_results.outputs.failing_tests }}\n\nAccess the detailed report by downloading the artifacts from the run here: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      - name: Send custom JSON data to Slack workflow if success
        if: success()
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "SLACK_MESSAGE": "All e2e tests passed in dev! ✅"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
