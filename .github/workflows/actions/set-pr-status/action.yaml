name: 'Set PR Status'
description: 'Sets a status on a PR using GitHub CLI'
inputs:
  state:
    description: 'Status state (pending, success, failure)'
    required: true
  description:
    description: 'Status description'
    required: true
  token:
    description: 'GitHub token'
    required: true
    default: ${{ github.token }}

runs:
  using: 'composite'
  steps:
    - name: Set PR Status
      shell: bash
      run: |
        SHA="${{ github.sha }}"
        REPO="${{ github.repository }}"
        STATE="${{ inputs.state }}"
        DESCRIPTION="${{ inputs.description }}"
        TARGET_URL="https://github.com/$REPO/commit/$SHA/checks"
        
        gh api \
          --method POST \
          -H "Accept: application/vnd.github+json" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          /repos/$REPO/statuses/$SHA \
          -f state="$STATE" \
          -f description="$DESCRIPTION" \
          -f target_url="$TARGET_URL"
      env:
        GITHUB_TOKEN: ${{ inputs.token }}
