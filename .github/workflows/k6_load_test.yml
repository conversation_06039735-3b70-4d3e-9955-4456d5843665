name: K6 Load Test

on:
  push:
    branches:
      - sreenu/*
  workflow_dispatch:
    inputs:
      langchain_endpoint:
        description: 'LangChain API Endpoint (defaults to staging if not provided)'
        type: string
        default: 'https://beta.api.smith.langchain.com'
        required: false
      langchain_api_key:
        description: 'LangChain API Key (will use default if not provided)'
        required: false
        type: string
      test_type:
        description: 'Type of test to run'
        type: choice
        options:
          - run-by-id
          - run-by-trace
          - multipart
        default: 'run-by-id'
        required: true
      run_id:
        description: 'Run ID (required for run-by-id test)'
        default: '89323346-3373-4822-a016-55115160f4b3'
        required: false
      trace_id:
        description: 'Trace ID (required for run-by-trace test)'
        required: false
      session_id:
        description: 'Session ID (required for run-by-trace test)'
        required: false
      scale_level:
        description: 'Scale testing level (baseline, 4x, or 20x)'
        type: choice
        options:
          - baseline
          - 4x
          - 20x
        default: 'baseline'
        required: true
      duration_config:
          description: 'Duration config in format: rampUp,steady,rampDown (e.g., 2m,5m,1m)'
          default: '2m,5m,1m'
          required: false
      batch_size:
        description: 'Batch size for multipart test (number of child runs per parent)'
        type: string
        default: '5'
        required: false
      payload_size:
        description: 'Size of the payload in bytes for multipart test'
        type: string
        default: '25000'
        required: false

jobs:
  k6_load_test:
    name: K6 Load Test
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install k6
        run: |
          curl https://github.com/grafana/k6/releases/download/v0.45.0/k6-v0.45.0-linux-amd64.tar.gz -L | tar xvz --strip-components=1
          sudo cp k6 /usr/local/bin

      - name: Set Environment URL
        run: |
          echo "LANGCHAIN_ENDPOINT=https://beta.api.smith.langchain.com" >> $GITHUB_ENV

      - name: Parse Duration Config
        run: |
            # Set default values
            RAMP_UP="30s"
            STEADY="1m"
            RAMP_DOWN="30s"

            # Try to read from inputs if available
            if [ -n "${{ github.event.inputs.duration_config }}" ]; then
              IFS=',' read -r RAMP_UP STEADY RAMP_DOWN <<< "${{ github.event.inputs.duration_config }}"
            fi

            # Export to environment
            echo "RAMP_UP_DURATION=$RAMP_UP" >> $GITHUB_ENV
            echo "STEADY_DURATION=$STEADY" >> $GITHUB_ENV
            echo "RAMP_DOWN_DURATION=$RAMP_DOWN" >> $GITHUB_ENV

      - name: Validate Inputs
        run: |
          if [[ "${{ github.event.inputs.test_type }}" == "run-by-id" && -z "${{ github.event.inputs.run_id }}" ]]; then
            echo "Error: run_id is required for run-by-id test"
            exit 1
          fi
          if [[ "${{ github.event.inputs.test_type }}" == "run-by-trace" && -z "${{ github.event.inputs.trace_id }}" ]]; then
            echo "Error: trace_id is required for run-by-trace test"
            exit 1
          fi
          if [[ "${{ github.event.inputs.test_type }}" == "run-by-trace" && -z "${{ github.event.inputs.session_id }}" ]]; then
            echo "Error: session_id is required for run-by-trace test"
            exit 1
          fi

      - name: Run K6 Test
        continue-on-error: true
        env:
          LANGCHAIN_ENDPOINT: ${{ github.event.inputs.langchain_endpoint || 'https://beta.api.smith.langchain.com' }}
          LANGCHAIN_API_KEY: ${{ github.event.inputs.langchain_api_key || secrets.K6_LT_STAGING_API_KEY }}
          TEST_TYPE: ${{ github.event.inputs.test_type || 'run-by-id' }}
          TEST_RUN_ID: ${{ github.event.inputs.run_id || '89323346-3373-4822-a016-55115160f4b3' }}
          TEST_TRACE_ID: ${{ github.event.inputs.trace_id }}
          TEST_SESSION_ID: ${{ github.event.inputs.session_id }}
          SCALE_LEVEL: ${{ github.event.inputs.scale_level || 'baseline' }}
          BATCH_SIZE: ${{ github.event.inputs.batch_size }}
          DATA_SIZE: ${{ github.event.inputs.payload_size }}
          USE_ZSTD: 'true'
        run: |
          echo "Using test type: $TEST_TYPE"
          if [[ "$TEST_TYPE" == "run-by-id" ]]; then
            k6 run -e TEST_RUN_ID="$TEST_RUN_ID" -e LANGCHAIN_ENDPOINT="$LANGCHAIN_ENDPOINT" -e LANGCHAIN_API_KEY="$LANGCHAIN_API_KEY" -e SCALE_LEVEL="$SCALE_LEVEL" -e RAMP_UP_DURATION="$RAMP_UP_DURATION" -e STEADY_DURATION="$STEADY_DURATION" -e RAMP_DOWN_DURATION="$RAMP_DOWN_DURATION" smith-backend/scripts/k6/run-by-id-test.js --out json=results.json
          elif [[ "$TEST_TYPE" == "run-by-trace" ]]; then
            k6 run -e TEST_TRACE_ID="$TEST_TRACE_ID" -e TEST_SESSION_ID="$TEST_SESSION_ID" -e LANGCHAIN_ENDPOINT="$LANGCHAIN_ENDPOINT" -e LANGCHAIN_API_KEY="$LANGCHAIN_API_KEY" -e SCALE_LEVEL="$SCALE_LEVEL" -e RAMP_UP_DURATION="$RAMP_UP_DURATION" -e STEADY_DURATION="$STEADY_DURATION" -e RAMP_DOWN_DURATION="$RAMP_DOWN_DURATION" smith-backend/scripts/k6/run-by-trace.js --out json=results.json
          else
            k6 run -e LANGCHAIN_ENDPOINT="$LANGCHAIN_ENDPOINT" -e LANGCHAIN_API_KEY="$LANGCHAIN_API_KEY" -e SCALE_LEVEL="$SCALE_LEVEL" -e RAMP_UP_DURATION="$RAMP_UP_DURATION" -e STEADY_DURATION="$STEADY_DURATION" -e RAMP_DOWN_DURATION="$RAMP_DOWN_DURATION" -e BATCH_SIZE="$BATCH_SIZE" -e DATA_SIZE="$DATA_SIZE" -e USE_ZSTD="$USE_ZSTD" -e SESSION_NAME="k6-multipart-$SCALE_LEVEL-$(date +%Y%m%d-%H%M%S)" smith-backend/scripts/k6/multipart.js --out json=results.json
          fi

      - name: Process and Save Test Results
        run: |
            echo "# K6 Load Test Results - ${{ github.event.inputs.test_type }}" > test-report.md
            echo "## Test Configuration" >> test-report.md
            echo "- API Endpoint: https://beta.api.smith.langchain.com" >> test-report.md
            echo "- Test Type: ${{ github.event.inputs.test_type }}" >> test-report.md
            if [[ "${{ github.event.inputs.test_type }}" == "run-by-id" ]]; then
              echo "- Run ID: ${{ github.event.inputs.run_id }}" >> test-report.md
              LATENCY_METRIC="read_latency"
              REQUESTS_METRIC="successful_reads"
            else
              echo "- Trace ID: ${{ github.event.inputs.trace_id }}" >> test-report.md
              if [[ -n "${{ github.event.inputs.session_id }}" ]]; then
                echo "- Session ID: ${{ github.event.inputs.session_id }}" >> test-report.md
              fi
              LATENCY_METRIC="query_latency"
              REQUESTS_METRIC="successful_queries"
            fi
            echo "- Scale Level: ${{ github.event.inputs.scale_level }}" >> test-report.md
            echo "- Test Date: $(date)" >> test-report.md
            
            echo "## Results" >> test-report.md
            echo "\`\`\`" >> test-report.md
            jq -r --arg latency "$LATENCY_METRIC" --arg requests "$REQUESTS_METRIC" '.metrics | {
              "p50_latency": .[$latency].values.p50,
              "p95_latency": .[$latency].values.p95,
              "p99_latency": .[$latency].values.p99,
              "error_rate": .error_rate.values.rate,
              "total_requests": .[$requests].values.count,
              "timeout_errors": .timeout_errors.values.count,
              "server_errors": .server_errors.values.count,
              "client_errors": .client_errors.values.count
            }' results.json >> test-report.md
            echo "\`\`\`" >> test-report.md

      - name: Upload Test Results
        uses: actions/upload-artifact@v4
        with:
          name: k6-test-results
          path: |
            results.json
            test-report.md