name: '1. Deploy: Prod Hotfix'

on:
  workflow_dispatch:

permissions: write-all

jobs:
  tag-and-get-version:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.get-version.outputs.image_tag }}
      git_sha: ${{ steps.get-version.outputs.git_sha }}
      version: ${{ steps.get-version.outputs.version }}
      linux_image_tag: ${{ steps.get-version.outputs.linux_image_tag }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install root dependencies
        run: |
          pip install poetry

      - name: Get semantic version from pyproject
        id: get-version
        working-directory: smith-backend
        run: |
          echo "version=$(poetry version -s)" >> $GITHUB_OUTPUT
          echo "git_sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "image_tag=$(poetry version -s)-$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

          echo "linux_image_tag=$(poetry version -s)-$(git rev-parse --short HEAD)-linux-amd64" >> $GITHUB_OUTPUT

  backend-images-amd64:
    needs: ['tag-and-get-version']
    uses: ./.github/workflows/build_push_amd64_backend_images.yml
    secrets:
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
      gcr_credentials: ${{ secrets.GCR_JSON_KEY_PROD }}
    with:
      tag: ${{ needs.tag-and-get-version.outputs.image_tag }}
      project: 'langchain-prod'

  artifacts:
    needs: ['tag-and-get-version']
    uses: ./.github/workflows/build_shared_image_artifacts.yml
    secrets:
      datadog_api_key_us: ${{ secrets.DATADOG_API_KEY }}
      datadog_api_key_eu: ${{ secrets.DATADOG_API_KEY_EU }}

  frontend-images-amd64:
    needs: ['artifacts', 'tag-and-get-version']
    uses: ./.github/workflows/build_push_amd64_frontend_images.yml
    secrets:
      dockerhub_username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
      dockerhub_password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}
      gcr_credentials: ${{ secrets.GCR_JSON_KEY_PROD }}
    with:
      tag: ${{ needs.tag-and-get-version.outputs.image_tag }}
      project: 'langchain-prod'

  migrations-us:
    runs-on: ubuntu-latest-m

    # we only tag images on prod
    needs:
      ['tag-and-get-version', 'frontend-images-amd64', 'backend-images-amd64']

    # Prevent concurrent deploys to avoid conflicting DB migrations
    concurrency:
      group: ${{ github.workflow }}-us

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_PROD }}

      - name: CloudSQL Proxy US
        uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ secrets.GOOGLE_CREDENTIALS }}
          instance: ${{ secrets.prod_INSTANCE_CONNECTION_NAME }}

      - name: Run Database Migrations against Postgres US
        run: |
          docker run --network host --env-file ./.env.migrations \
          -e POSTGRES_DATABASE_URI=${{ secrets.PROD_POSTGRES_DATABASE_URI }}  \
          -e LANGCHAIN_ENV=production \
          gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-and-get-version.outputs.linux_image_tag }} \
          alembic upgrade head

      - name: Run migrations against Clickhouse US
        run: |
          docker run --network host gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-and-get-version.outputs.linux_image_tag }} /bin/bash -c \
            'PROCESSED_DIR=$(./scripts/process_templates.sh) && \
             migrate -source "file://${PROCESSED_DIR}" \
             -database "clickhouse://${{ secrets.PROD_CLICKHOUSE_URL }}?username=${{ secrets.PROD_CLICKHOUSE_USERNAME }}&password=${{ secrets.PROD_CLICKHOUSE_PASSWORD }}&database=default&x-multi-statement=true&secure=true&x-migrations-table-engine=MergeTree" up'

  migrations-eu:
    # Prevent concurrent deploys to avoid conflicting DB migrations
    concurrency:
      group: ${{ github.workflow }}-eu

    runs-on: ubuntu-latest-m

    # we only tag images on prod
    needs:
      ['tag-and-get-version', 'frontend-images-amd64', 'backend-images-amd64']

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_PROD }}

      - name: CloudSQL Proxy EU
        uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ secrets.GOOGLE_CREDENTIALS }}
          instance: ${{ secrets.eu_prod_INSTANCE_CONNECTION_NAME }}

      - name: Run Database Migrations against Postgres EU
        run: |
          docker run --network host --env-file ./.env.migrations \
          -e POSTGRES_DATABASE_URI=${{ secrets.EU_PROD_POSTGRES_DATABASE_URI }} \
          -e LANGCHAIN_ENV=production \
          gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-and-get-version.outputs.linux_image_tag }} \
          alembic upgrade head

      - name: Run migrations against Clickhouse EU
        run: |
          docker run --network host gcr.io/langchain-prod/langsmith-backend:${{ needs.tag-and-get-version.outputs.linux_image_tag }} /bin/bash -c \
            'PROCESSED_DIR=$(./scripts/process_templates.sh) && \
             migrate -source "file://${PROCESSED_DIR}" \
             -database "clickhouse://${{ secrets.EU_PROD_CLICKHOUSE_URL }}?username=${{ secrets.EU_PROD_CLICKHOUSE_USERNAME }}&password=${{ secrets.EU_PROD_CLICKHOUSE_PASSWORD }}&database=default&x-multi-statement=true&secure=true&x-migrations-table-engine=MergeTree" up'

  update-deployed-images:
    runs-on: ubuntu-latest-m

    needs: ['tag-and-get-version', 'migrations-us', 'migrations-eu']

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_PROD }}

      - name: Checkout deployments repo
        uses: actions/checkout@v4
        with:
          repository: langchain-ai/deployments
          path: deployments
          ref: main
          token: ${{ secrets.DEPLOYMENTS_PAT }}

      - name: Update Docker image in Terraform files (prod)
        working-directory: deployments/environments/gcp/prod
        run: |
          # Modify the terraform file
          sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-and-get-version.outputs.linux_image_tag}}\"|" main.tf
          sed -i "s|tag: \"[^\"]*\"|tag: \"${{ needs.tag-and-get-version.outputs.linux_image_tag}}\"|" kubernetes/langsmith_helm_values.yaml
          sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{ needs.tag-and-get-version.outputs.linux_image_tag }}\"|" kubernetes/langsmith_helm_values.yaml
          # Update datadog tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{ needs.tag-and-get-version.outputs.linux_image_tag }}\"|"
          # Update image tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-and-get-version.outputs.linux_image_tag}}\"|"

      - name: Update Docker image in Terraform files (eu-prod)
        working-directory: deployments/environments/gcp/eu-prod
        run: |
          # Modify the terraform file
          sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-and-get-version.outputs.linux_image_tag}}\"|" main.tf
          sed -i "s|tag: \"[^\"]*\"|tag: \"${{needs.tag-and-get-version.outputs.linux_image_tag}}\"|" kubernetes/langsmith_helm_values.yaml
          sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{ needs.tag-and-get-version.outputs.linux_image_tag}}\"|" kubernetes/langsmith_helm_values.yaml
          # Update datadog tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{needs.tag-and-get-version.outputs.linux_image_tag}}\"|"
          # Update image tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-and-get-version.outputs.linux_image_tag}}\"|"

      - name: Create Pull Request
        id: create-pr
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          title: 'Hotfix: Update Prod Docker image to ${{ needs.tag-and-get-version.outputs.linux_image_tag }}'
          commit-message: 'Update Prod Docker image'
          base: 'main'
          branch: 'actions/update-docker-image-${{ needs.tag-and-get-version.outputs.git_sha }}'
          body: 'Update Prod Docker image to ${{ needs.tag-and-get-version.outputs.linux_image_tag }}'
          path: deployments

      - name: Wait for PR to be available
        run: sleep 10  # Wait for 10 seconds

      # Use GH token to approve PR, must be different from the one used to create PR
      - name: Approve PR
        env:
          GH_TOKEN: ${{ secrets.DEPLOYMENTS_APPROVER_PAT }}
        working-directory: deployments
        run: |
          gh pr review ${{ steps.create-pr.outputs.pull-request-url }} --approve

      - name: Enable Pull Request Automerge
        uses: peter-evans/enable-pull-request-automerge@v3
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
          repository: langchain-ai/deployments
