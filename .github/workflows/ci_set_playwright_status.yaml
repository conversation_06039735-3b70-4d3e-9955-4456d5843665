name: 'CI: Check if <PERSON><PERSON> tests required'

on:
  pull_request:
  workflow_dispatch:

jobs:
  check-frontend-changes:
    permissions: write-all
    runs-on: ubuntu-latest
    outputs:
      changed: ${{ steps.check-changes.outputs.frontend }}

    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: check-changes
        with:
          filters: |
            frontend:
              - '.github/workflows/ci_test_frontend.yaml'
              - 'smith-frontend/**'
  
  check-backend-changes:
    permissions: write-all
    runs-on: ubuntu-latest
    outputs:
      changed: ${{ steps.check-changes.outputs.backend == 'true' && !(steps.check-version-bump.outputs.version-bump == 'true') }}
    
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: check-changes
        with:
          list-files: json
          filters: |
            backend:
              - '.github/workflows/ci_test_backend.yaml'
              - 'smith-backend/**'
              - 'smith-go/**'
              - 'smith-playground/**'
              - 'host-backend/**'
              - 'lc_config/**'
              - 'lc_database/**'
              - 'lc_logging/**'
              - 'lc_metrics/**'
              - 'test_data/**'
      - name: Check if only a version bump pr
        id: check-version-bump
        run: |
          result=$(echo '${{ steps.check-changes.outputs.backend_files }}' | jq '
            length == 2 and any(. == "smith-backend/app/__init__.py") and any(. == "smith-backend/pyproject.toml")
          ')
          echo $result
          echo "version-bump=$result" >> $GITHUB_ENV
          echo "version-bump=$result" >> $GITHUB_OUTPUT
  set_pending:
    needs: [check-frontend-changes, check-backend-changes]
    if: github.event_name == 'pull_request' && needs.check-frontend-changes.outputs.changed == 'true' && needs.check-backend-changes.outputs.changed == 'false'
    name: Register pending E2E tests state
    # This is the place where we define this job to only
    # run when the deployment state is still "pending".
    runs-on: ubuntu-latest

    steps:
      # This checks out the code of this repository.
      # We need this because this is where our action
      # lives.
      - uses: actions/checkout@v1

      - name: Set status to "pending".
        uses: ./.github/actions/set-pr-status
        with:
          # This is where we define the inputs
          # for this action.
          state: pending
          description: Waiting for E2E results
          token: ${{ github.token }}
