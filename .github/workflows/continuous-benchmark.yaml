name: Continuous Benchmarking

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHON_VERSION: '3.11'
  GOLANG_MIGRATE_DEB_FILE: 'migrate_4.18.1_amd64.deb'

jobs:
  check-go-changes:
    permissions: write-all
    runs-on: ubuntu-latest
    outputs:
      changed: ${{ steps.check-changes.outputs.go == 'true' }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: check-changes
        with:
          list-files: json
          filters: |
            go:
              - '.github/workflows/continuous-benchmark.yaml'
              - 'smith-go/**'

  benchmark:
    needs: check-go-changes
    if: ${{ needs.check-go-changes.outputs.changed == 'true' }}
    runs-on: ubuntu-latest
    env:
      LANGSMITH_LICENSE_KEY: ${{ secrets.LANGSMITH_LICENSE_KEY }}
    defaults:
      run:
        working-directory: smith-go
    services:
      db:
        image: postgres:14.7-alpine
        env:
          POSTGRES_PASSWORD: postgres
        ports: ['5432:5432']
        options: --health-cmd pg_isready --health-interval 1s --health-timeout 5s --health-retries 5
      redis:
        image: redis:7-alpine
        ports: ['6379:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5

      minio:
        image: minio/minio:edge-cicd
        env:
          MINIO_ROOT_USER: minioadmin1
          MINIO_ROOT_PASSWORD: minioadmin1
        ports:
          - '9002:9000'
          - '9003:9001'
        options: --name=minio
      # Azurite is used for local development with Azure Blob Storage
      azurite:
        image: mcr.microsoft.com/azure-storage/azurite
        env:
          AZURITE_ACCOUNTS: 'admin:password'
        ports:
          - '10000:10000'
    steps:
      - uses: actions/checkout@v4
        with:
          # Fetch full history for accurate benchmark comparison
          fetch-depth: 0
      - name: Run Clickhouse
        working-directory: .
        run: |
          docker run -d --rm --name clickhouse-server --network host \
          --volume ${PWD}/smith-backend/clickhouse/users.xml:/etc/clickhouse-server/users.d/users.xml \
          --volume ${PWD}/smith-backend/clickhouse/u.xml:/etc/clickhouse-server/users.d/u.xml \
          --publish 8123:8123 \
          --publish 9000:9000 \
          clickhouse/clickhouse-server:24.2
      - name: Run Minio Setup
        run: |
          docker run --rm --network host --entrypoint /bin/sh minio/mc:latest -c "
          /usr/bin/mc alias remove local;
          /usr/bin/mc alias set --quiet --api s3v4 local http://127.0.0.1:9002 minioadmin1 minioadmin1;
          /usr/bin/mc mb --ignore-existing local/langsmith-images/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region-test;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data-test/;"
      - name: Run Azurite setup
        env:
          AZURE_STORAGE_CONNECTION_STRING: 'DefaultEndpointsProtocol=http;AccountName=admin;AccountKey=password;BlobEndpoint=http://127.0.0.1:10000/admin;'
        run: |
          docker run --rm --network host -e AZURE_STORAGE_CONNECTION_STRING=$AZURE_STORAGE_CONNECTION_STRING --entrypoint /bin/sh mcr.microsoft.com/azure-cli -c "
          az storage container create --name langsmith-images-test;
          az storage container create --name langsmith-images-single-region-test;"
      - name: Set root suid on tar
        run: sudo chown root /bin/tar && sudo chmod u+s /bin/tar
      - name: Restore golang-migrate cached dependencies
        uses: actions/cache@v3
        id: cached-go-migrate
        env:
          SEGMENT_DOWNLOAD_TIMEOUT_MIN: "3"
        with:
          path: /usr/bin/migrate
          key: langchainplus-${{ runner.os }}-${{ runner.arch }}-${{ env.GOLANG_MIGRATE_DEB_FILE }}
      - name: Install golang-migrate
        if: steps.cached-go-migrate.outputs.cache-hit != 'true'
        run: |
          wget -O migrate.deb "https://packagecloud.io/golang-migrate/migrate/packages/ubuntu/$(lsb_release -sc)/${{ env.GOLANG_MIGRATE_DEB_FILE }}/download.deb"
          sudo dpkg -i migrate.deb
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version-file: smith-go/go.mod
          cache-dependency-path: smith-go/go.sum
      - name: Wait for Clickhouse
        shell: bash
        run: |
          tries=30
          while ! wget --spider --no-check-certificate -T 1 -q 'http://127.0.0.1:8123/ping' 2>/dev/null; do
            if [ "$tries" -le "0" ]; then
                echo >&2 'ClickHouse init process failed.'
                exit 1
            fi
            tries=$(( tries-1 ))
            sleep 1
          done
      - name: Setup Clickhouse User
        working-directory: smith-backend
        run: ./clickhouse/setup_clickhouse.sh clickhouse-server
      - name: Install Poetry (for Postgres migrations)
        working-directory: .
        run: |
          pip install poetry
          mkdir secrets
      - name: Set up Python (for Postgres migrations)
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: poetry
          cache-dependency-path: 'smith-backend/poetry.lock'
      - name: Install Python dependencies (for Postgres migrations)
        working-directory: smith-backend
        run: poetry install
      - name: Setup test environment
        run: make -C ../smith-backend tests-setup

      # Run benchmarks on current commit
      - name: Run benchmarks (current)
        run: |
          LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 \
          go test -bench=. -benchmem ./storage/... -benchtime=1x -count=6 | tee /tmp/current-bench.txt

      # Checkout main branch and run benchmarks for comparison
      - name: Stash benchmark results and checkout main
        if: github.ref != 'refs/heads/main'
        run: |
          # Move benchmark results to temp location to avoid git conflicts
          cp /tmp/current-bench.txt ./current-bench-backup.txt
          git stash push -m "benchmark-temp" || true
          git fetch origin main
          git checkout main

      - name: Run benchmarks (main)
        if: github.ref != 'refs/heads/main'
        run: |
          LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 \
          go test -bench=. -benchmem ./storage/... -benchtime=1x -count=6 | tee /tmp/main-bench.txt

      # Switch back to current branch
      - name: Checkout current branch and restore results
        if: github.ref != 'refs/heads/main'
        run: |
          git checkout ${{ github.sha }}
          git stash pop || true
          # Restore benchmark results from temp location
          cp ./current-bench-backup.txt /tmp/current-bench.txt
          rm -f ./current-bench-backup.txt

      # Compare benchmarks using benchstat
      - name: Install benchstat
        run: go install golang.org/x/perf/cmd/benchstat@latest

      - name: Compare benchmarks
        if: github.ref != 'refs/heads/main'
        run: |
          echo "## Benchmark Comparison" >> benchmark-comment.md
          echo "" >> benchmark-comment.md
          echo "Comparing current branch against main:" >> benchmark-comment.md
          echo "" >> benchmark-comment.md
          echo '```' >> benchmark-comment.md
          benchstat /tmp/main-bench.txt /tmp/current-bench.txt >> benchmark-comment.md || echo "No benchmarks found in main branch to compare against" >> benchmark-comment.md
          echo '```' >> benchmark-comment.md

      # Copy benchmark results to expected location for benchmark action
      - name: Prepare benchmark results
        run: |
          cp /tmp/current-bench.txt ./current-bench.txt

      # Store benchmark results for continuous tracking
      - name: Store benchmark result
        uses: benchmark-action/github-action-benchmark@v1
        with:
          tool: 'go'
          output-file-path: smith-go/current-bench.txt
          github-token: ${{ secrets.GITHUB_TOKEN }}
          auto-push: ${{ github.ref == 'refs/heads/main' }}
          # Show alert with commit comment on detecting possible performance regression
          alert-threshold: '150%'
          comment-on-alert: true
          fail-on-alert: false
          alert-comment-cc-users: '@angus-langchain'

      # Comment on PR with benchmark comparison
      - name: Comment PR with benchmark results
        if: github.event_name == 'pull_request' && github.ref != 'refs/heads/main'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            try {
              const comment = fs.readFileSync('smith-go/benchmark-comment.md', 'utf8');
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } catch (error) {
              console.log('No benchmark comparison file found or error reading it:', error);
            }
