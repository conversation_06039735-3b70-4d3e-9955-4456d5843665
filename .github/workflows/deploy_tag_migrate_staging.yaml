name: '1. Deploy: Staging'

on:
  workflow_dispatch:

permissions: write-all

jobs:
  tag-staging:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.run-vars.outputs.image_tag }}
      git_sha: ${{ steps.run-vars.outputs.git_sha }}
      version: ${{ steps.run-vars.outputs.version }}

    steps:
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_STAGING }}

      - uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS }}'

      - name: Get Latest Successful Image Build
        id: run-vars
        run: |
          echo "image_tag=$(gcloud container images list-tags gcr.io/langchain-dev/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))')" >> $GITHUB_ENV
          echo "image_tag=$(gcloud container images list-tags gcr.io/langchain-dev/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))')" >> $GITHUB_OUTPUT
          echo "git_sha=$(gcloud container images list-tags gcr.io/langchain-dev/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))' | cut -d- -f2)" >> $GITHUB_ENV
          echo "git_sha=$(gcloud container images list-tags gcr.io/langchain-dev/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))' | cut -d- -f2)" >> $GITHUB_OUTPUT
          echo "version=$(gcloud container images list-tags gcr.io/langchain-dev/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))' | cut -d- -f1)" >> $GITHUB_ENV
          echo "version=$(gcloud container images list-tags gcr.io/langchain-dev/langsmith-backend --filter "tags=latest" --format=json | jq -r '.[].tags[] | select(contains("linux"))' | cut -d- -f1)" >> $GITHUB_OUTPUT

      - name: Tag Smith Backend
        run: |
          docker buildx imagetools create -t gcr.io/langchain-staging/langsmith-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-staging/langsmith-backend:latest -t gcr.io/langchain-staging/langchainpro-backend:${{steps.run-vars.outputs.image_tag}} gcr.io/langchain-dev/langsmith-backend:latest

      - name: Tag Hosted Langserve Backend
        run: |
          docker buildx imagetools create -t gcr.io/langchain-staging/hosted-langserve-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-staging/hosted-langserve-backend:latest -t gcr.io/langchain-staging/langchain-host-backend:${{steps.run-vars.outputs.image_tag}} gcr.io/langchain-dev/hosted-langserve-backend:latest

      - name: Tag Smith Frontend
        run: |
          docker buildx imagetools create -t gcr.io/langchain-staging/langsmith-frontend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-staging/langsmith-frontend:latest -t gcr.io/langchain-staging/langchainplus-frontend-dynamic:${{steps.run-vars.outputs.image_tag}} gcr.io/langchain-dev/langsmith-frontend:latest

      - name: Tag Smith Proxy
        run: |
          docker buildx imagetools create -t gcr.io/langchain-staging/langsmith-proxy:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-staging/langsmith-proxy:latest gcr.io/langchain-dev/langsmith-proxy:latest

      - name: Tag Smith Go Backend
        run: |
          docker buildx imagetools create -t gcr.io/langchain-staging/langsmith-go-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-staging/langsmith-go-backend:latest gcr.io/langchain-dev/langsmith-go-backend:latest

      - name: Tag Smith Ace Backend
        run: |
          docker buildx imagetools create -t gcr.io/langchain-staging/langsmith-ace-backend:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-staging/langsmith-ace-backend:latest gcr.io/langchain-dev/langsmith-ace-backend:latest

      - name: Tag Smith Playground
        run: |
          docker buildx imagetools create -t gcr.io/langchain-staging/langsmith-playground-python:${{steps.run-vars.outputs.image_tag}} -t gcr.io/langchain-staging/langsmith-playground-python:latest gcr.io/langchain-dev/langsmith-playground-python:latest

  deploy:
    runs-on: ubuntu-latest-m

    # we only tag images on staging
    needs: ['tag-staging']

    # Prevent concurrent deploys to avoid conflicting DB migrations
    concurrency:
      group: ${{ github.workflow }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.LANGCHAIN_DOCKERHUB_USERNAME }}
          password: ${{ secrets.LANGCHAIN_DOCKERHUB_PASSWORD }}

      - name: Login to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCR_JSON_KEY_STAGING }}

      - uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ secrets.GOOGLE_CREDENTIALS }}
          instance: ${{ secrets.STAGING_INSTANCE_CONNECTION_NAME }}

      - name: Run Database Migrations against Postgres
        run: |
          docker run --network host --env-file ./.env.migrations \
          -e POSTGRES_DATABASE_URI=${{ secrets.STAGING_POSTGRES_DATABASE_URI }} \
          -e LANGCHAIN_ENV=staging \
          gcr.io/langchain-staging/langsmith-backend:${{ needs.tag-staging.outputs.image_tag }} \
          alembic upgrade head

      - name: Run migrations against Clickhouse
        run: |
          docker run --network host gcr.io/langchain-staging/langsmith-backend:${{ needs.tag-staging.outputs.image_tag }} /bin/bash -c \
            'PROCESSED_DIR=$(./scripts/process_templates.sh) && \
             migrate -source "file://${PROCESSED_DIR}" \
             -database "clickhouse://${{ secrets.STAGING_CLICKHOUSE_URL }}?username=${{ secrets.STAGING_CLICKHOUSE_USERNAME }}&password=${{ secrets.STAGING_CLICKHOUSE_PASSWORD }}&database=default&x-multi-statement=true&secure=true&x-migrations-table-engine=MergeTree" up'

      - name: Checkout deployments repo
        uses: actions/checkout@v4
        with:
          repository: langchain-ai/deployments
          path: deployments
          ref: main
          token: ${{ secrets.DEPLOYMENTS_PAT }}

      - name: Update Docker image in Terraform files (staging)
        run: |
          cd deployments/environments/gcp/staging
          # Modify the terraform file
          sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-staging.outputs.image_tag}}\"|" main.tf
          sed -i "s|tag: \"[^\"]*-linux-amd64|tag: \"${{ needs.tag-staging.outputs.image_tag }}|" kubernetes/langsmith_helm_values.yaml
          sed -i "s|tags.datadoghq.com/version: \"[^\"]*\"|tags.datadoghq.com/version: \"${{ needs.tag-staging.outputs.image_tag }}\"|" kubernetes/langsmith_helm_values.yaml
          # Update datadog tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i "s|tags.datadoghq.com/version: \"[^\"]*-linux-amd64|tags.datadoghq.com/version: \"${{ needs.tag-staging.outputs.image_tag }}|"
          # Update image tags
          find ./kubernetes/kustomize -type f -name "*.yaml" -print0 | xargs -0 sed -i -E "s|(gcr\.io/.*/.*):.*$|\1:${{needs.tag-staging.outputs.image_tag}}\"|"

      - name: Create Pull Request
        id: create-pr
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          title: 'Update Staging Docker image to ${{ needs.tag-staging.outputs.image_tag }}'
          commit-message: 'Update Staging Docker image'
          base: 'main'
          branch: 'actions/update-staging-docker-image-${{ needs.tag-staging.outputs.git_sha }}'
          body: 'Update Staging Docker image to ${{ needs.tag-staging.outputs.image_tag }}'
          path: deployments

      - name: Wait for PR to be available
        run: sleep 10  # Wait for 10 seconds

      # Use GH token to approve PR, must be different from the one used to create PR
      - name: Approve PR
        env:
          GH_TOKEN: ${{ secrets.DEPLOYMENTS_APPROVER_PAT }}
        working-directory: deployments
        run: |
          gh pr review ${{ steps.create-pr.outputs.pull-request-url }} --approve

      - name: Enable Pull Request Automerge
        uses: peter-evans/enable-pull-request-automerge@v3
        with:
          token: ${{ secrets.DEPLOYMENTS_PAT }}
          pull-request-number: ${{ steps.create-pr.outputs.pull-request-number }}
          repository: langchain-ai/deployments

  create-pre-release:
    needs: ['tag-staging']
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Tag Sha
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git tag ${{ needs.tag-staging.outputs.version }}-${{ needs.tag-staging.outputs.git_sha}}-rc ${{ needs.tag-staging.outputs.git_sha }}
          git push origin ${{ needs.tag-staging.outputs.version }}-${{ needs.tag-staging.outputs.git_sha}}-rc

      - name: Get last release
        id: get-last-release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo "last_version=$(gh release view --json tagName | jq -r '.tagName')" >> $GITHUB_OUTPUT

      - name: Create Pre-Release
        id: create_release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: gh release create ${{ needs.tag-staging.outputs.version }}-${{ needs.tag-staging.outputs.git_sha}}-rc -t "Pre-Release ${{ steps.run-vars.outputs.version }}" --generate-notes -p --notes-start-tag ${{ steps.get-last-release.outputs.last_version }} --verify-tag

  notify-slack:
    needs: ['create-pre-release']
    uses: ./.github/workflows/notify_slack.yaml
    secrets: inherit
    with:
      langsmith_env: 'staging'
