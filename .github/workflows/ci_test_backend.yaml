name: 'CI: Backend Tests'

on:
  push:
    branches: [main]
  pull_request:
  workflow_dispatch:

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time,
# so it's better to cancel pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHON_VERSION: '3.11'
  # Info at:
  # https://packagecloud.io/golang-migrate/migrate/packages/ubuntu/jammy/migrate_4.18.1_amd64.deb?distro_version_id=237
  GOLANG_MIGRATE_DEB_FILE: 'migrate_4.18.1_amd64.deb'

jobs:
  check-backend-changes:
    permissions: write-all
    runs-on: ubuntu-latest

    outputs:
      changed: ${{ steps.check-changes.outputs.backend == 'true' && !(steps.check-version-bump.outputs.version-bump == 'true') }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: check-changes
        with:
          list-files: json
          filters: |
            backend:
              - '.github/workflows/ci_test_backend.yaml'
              - 'smith-backend/**'
              - 'smith-go/**'
              - 'host-backend/**'
              - 'lc_config/**'
              - 'lc_database/**'
              - 'lc_logging/**'
              - 'lc_metrics/**'
              - 'test_data/**'
      - name: Check if only a version bump pr
        id: check-version-bump
        run: |
          result=$(echo '${{ steps.check-changes.outputs.backend_files }}' | jq '
            length == 2 and any(. == "smith-backend/app/__init__.py") and any(. == "smith-backend/pyproject.toml")
          ')
          echo $result
          echo "version-bump=$result" >> $GITHUB_ENV
          echo "version-bump=$result" >> $GITHUB_OUTPUT


  test-smith-backend:
    needs: check-backend-changes
    if: ${{ needs.check-backend-changes.outputs.changed == 'true' || github.event_name == 'workflow_dispatch' }}
    runs-on: ubuntu-latest-l
    strategy:
      matrix:
        # These tests mirror our cloud environments (PGBouncer, sharded mode, etc.).
        # Other tests are run in ci_test_backend_compatibility.yaml.
        # test-name: [ "auth-sharded-parallel", "auth-sharded-serial" ]
        test-name: [
          "auth-sharded-serial",
          "auth-quickwit",
          "auth-sharded-parallel",
          "auth-parallel-runs",
          "auth-parallel-redis-cluster",
          "auth-parallel-runs-s3",
          "auth-sharded-parallel-runs",
          "auth-parallel-s3"
        ]

    env:
      LANGSMITH_LICENSE_KEY: ${{ secrets.LANGSMITH_LICENSE_KEY }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

    services:
      langsmith-db-postgres:
        image: postgres:14.7-alpine
        env:
          POSTGRES_PASSWORD: postgres
        ports: ['5432:5432']
        options: --health-cmd pg_isready --health-interval 1s --health-timeout 5s --health-retries 5 --name langsmith-db-postgres
      redis:
        image: redis:7-alpine
        ports: ['6379:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5
      redis1:
        image: redis:7-alpine
        ports: ['6380:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5
      redis2:
        image: redis:7-alpine
        ports: ['6381:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5
      minio:
        image: minio/minio:edge-cicd
        env:
          MINIO_ROOT_USER: minioadmin1
          MINIO_ROOT_PASSWORD: minioadmin1
        ports:
          - '9002:9000'
          - '9003:9001'
        options: --name=minio
      elasticsearch:
        image: elasticsearch:8.14.3
        env:
          discovery.type: single-node
          xpack.security.enabled: false
        ports:
          - '9200:9200'
          - '9300:9300'
    steps:
      - uses: actions/checkout@v4
      - name: Install redis
        if: ${{ matrix.test-name == 'auth-parallel-redis-cluster' }}
        run: |
            sudo apt-get update
            sudo apt-get install -y redis

      - name: Install root dependencies
        run: |
          pip install poetry
          mkdir secrets

      # Prevent test_concurrent_invites_are_ordered flakiness
      - name: Increase max_connections and shared_buffers
        run: |
          docker exec -i langsmith-db-postgres bash << EOF
            sed -i -e 's/max_connections = 100/max_connections = 1000/' /var/lib/postgresql/data/postgresql.conf
            sed -i -e 's/shared_buffers = 128MB/shared_buffers = 2GB/' /var/lib/postgresql/data/postgresql.conf
          EOF
      - name: restart postgres
        run: docker restart --time 0 langsmith-db-postgres
      - name: Run Clickhouse
        run: |
          docker run -d --rm --name clickhouse-server --network host \
          --volume ${PWD}/smith-backend/clickhouse/users.xml:/etc/clickhouse-server/users.d/users.xml \
          --volume ${PWD}/smith-backend/clickhouse/u.xml:/etc/clickhouse-server/users.d/u.xml \
          --publish 8123:8123 \
          --publish 9000:9000 \
          clickhouse/clickhouse-server:24.5
      - name: Run Minio Setup
        run: |
          docker run --rm --network host --entrypoint /bin/sh minio/mc:latest -c "
          /usr/bin/mc config host rm local;
          /usr/bin/mc config host add --quiet --api s3v4 local http://127.0.0.1:9002 minioadmin1 minioadmin1;
          /usr/bin/mc mb --ignore-existing local/langsmith-images/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region-test;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data-test/;"
      - name: Run Quickwit
        run: |
          docker run -d --rm --name quickwit --network host \
          --publish 7280:7280 \
          --volume ${PWD}/smith-backend/quickwit:/quickwit-config \
          --entrypoint quickwit \
          quickwit/quickwit:edge \
          run
      - name: Install golang-migrate
        # Info at:
        # https://packagecloud.io/golang-migrate/migrate/packages/ubuntu/jammy/migrate_4.18.1_amd64.deb?distro_version_id=237
        #
        # Doing a direct `wget` and installation of the `deb` archive lets us avoid
        # an expensive `apt update` operation in which we refresh the entire index while ignoring
        # everything except the `golang-migrate` program in it.
        run: |
          wget -O migrate.deb "https://packagecloud.io/golang-migrate/migrate/packages/ubuntu/$(lsb_release -sc)/${{ env.GOLANG_MIGRATE_DEB_FILE }}/download.deb"
          sudo dpkg -i migrate.deb
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: poetry
          cache-dependency-path: 'smith-backend/poetry.lock'
      - name: Install dependencies
        run: |
          cd smith-backend
          poetry install
      # We keep this step as late as possible, so that we do as much other work as possible
      # while Clickhouse is starting up. This means we probably won't have to wait at all here.
      - name: Wait for Clickhouse
        shell: bash
        # Use the same kind of readiness check that Clickhouse itself uses.
        # https://github.com/ClickHouse/ClickHouse/blob/1d46ed75db43de34ebe5255d8aa0935017708f58/docker/server/entrypoint.sh#L129-L139
        run: |
          tries=30
          while ! wget --spider --no-check-certificate -T 1 -q 'http://127.0.0.1:8123/ping' 2>/dev/null; do
            if [ "$tries" -le "0" ]; then
                echo >&2 'ClickHouse init process failed.'
                exit 1
            fi
            tries=$(( tries-1 ))
            sleep 1
          done
      - name: Setup Clickhouse User
        run: |
          cd smith-backend
          ./clickhouse/setup_clickhouse.sh clickhouse-server

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version-file: smith-go/go.mod
          cache-dependency-path: smith-go/go.sum

      - name: Check Quickwit indexes
        run: |
          cd smith-backend
          make quickwit-diff-templates

      - name: Run tests
        env:
          GOOGLE_VERTEX_AI_WEB_CREDENTIALS: ${{ secrets.GOOGLE_VERTEX_AI_WEB_CREDENTIALS }}
          MINIO_CH_URL: http://127.0.0.1:9002
        run: |
          cd smith-backend
          make tests-${{ matrix.test-name }}

      - name: Combine logs into a single file, separated by file name
        if: ${{ failure() || steps.short-circuit.outputs.skip != 'true' }}
        continue-on-error: true
        run: |
          cd smith-backend
          shopt -s nullglob  # Ensure the loop doesn't run if no files match
          for file in logs/*.log; do
            echo "=== $file ===" >> logs/combined.log
            cat $file >> logs/combined.log
          done

      - name: Upload logs generated during tests
        if: ${{ failure() || steps.short-circuit.outputs.skip != 'true' }}
        continue-on-error: true
        uses: actions/upload-artifact@v4
        with:
          name: smith-backend-logs-${{ matrix.test-name }}
          path: smith-backend/logs/combined.log
          if-no-files-found: warn

  test-host-backend:
    needs: check-backend-changes
    if: ${{ needs.check-backend-changes.outputs.changed == 'true' || github.event_name == 'workflow_dispatch' }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-name: [ "unit", "api" ]

    services:
      langsmith-db-postgres:
        image: postgres:14.7-alpine
        env:
          POSTGRES_PASSWORD: postgres
        ports: ['5432:5432']
        options: --health-cmd pg_isready --health-interval 1s --health-timeout 5s --health-retries 5 --name langsmith-db-postgres
      redis:
        image: redis:7-alpine
        ports: ['6379:6379']
        options: --health-cmd "redis-cli ping" --health-interval 1s --health-timeout 5s --health-retries 5
      minio:
        image: minio/minio:edge-cicd
        env:
          MINIO_ROOT_USER: minioadmin1
          MINIO_ROOT_PASSWORD: minioadmin1
        ports:
          - '9002:9000'
          - '9003:9001'
        options: --name=minio

    steps:
      - uses: actions/checkout@v4
      - name: Install root dependencies
        run: |
          pip install poetry
          mkdir secrets
      # Prevent test_concurrent_invites_are_ordered flakiness
      - name: Increase max_connections and shared_buffers
        run: |
          docker exec -i langsmith-db-postgres bash << EOF
            sed -i -e 's/max_connections = 100/max_connections = 1000/' /var/lib/postgresql/data/postgresql.conf
            sed -i -e 's/shared_buffers = 128MB/shared_buffers = 2GB/' /var/lib/postgresql/data/postgresql.conf
          EOF
      - name: restart postgres
        run: docker restart --time 0 langsmith-db-postgres
      - name: Run Minio Setup
        if: ${{ steps.short-circuit.outputs.skip != 'true' }}
        run: |
          docker run --rm --network host --entrypoint /bin/sh minio/mc:latest -c "
          /usr/bin/mc config host rm local;
          /usr/bin/mc config host add --quiet --api s3v4 local http://127.0.0.1:9002 minioadmin1 minioadmin1;
          /usr/bin/mc mb --ignore-existing local/langsmith-images/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-images-single-region-test;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-manifests-test/;
          /usr/bin/mc mb --ignore-existing local/langsmith-run-data-test/;"
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Install dependencies
        run: |
          cd smith-backend
          poetry install --with test
      - name: Run tests
        run: |
          source smith-backend/.venv/bin/activate
          cd host-backend
          make tests-${{ matrix.test-name }}

  results:
      if: ${{ always() }}
      runs-on: ubuntu-latest
      name: Final Results
      needs: [test-smith-backend, test-host-backend]
      steps:
        - run: |
            result_smith_backend="${{ needs.test-smith-backend.result }}"
            result_host_backend="${{ needs.test-host-backend.result }}"
            if [[ ($result_smith_backend == "success" || $result_smith_backend == "skipped") ]]; then
              exit 0
            else
              exit 1
            fi
