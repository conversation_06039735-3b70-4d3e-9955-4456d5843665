name: Playwright E2E Tests - against Vercel preview deployment

on:
  repository_dispatch:
    types:
      - 'vercel.deployment.success'
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to test'
        required: true
        default: 'main'
      url:
        description: 'Vercel preview URL to test against'
        required: true
      sha:
        description: 'Commit SHA to test'
        required: true

jobs:
  run-e2es:
    if: github.event_name == 'repository_dispatch' || github.event_name == 'workflow_dispatch'
    timeout-minutes: 300
    runs-on: ubuntu-latest-l
    permissions:
      statuses: write
      contents: read
      pull-requests: write
      checks: write
      issues: read
    env:
        BASE_URL: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.url || github.event.inputs.url }}
        ENDPOINT_URL: https://dev.api.smith.langchain.com
        SUPABASE_SECRET_ROLE: ${{ secrets.DEV_SUPABASE_SECRET }}
        SUPABASE_URL: ${{ secrets.DEV_SUPABASE_URL }}
        EKS_TEST_ACCOUNT_PASSWORD: ${{ secrets.EKS_TEST_ACCOUNT_PASSWORD }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY_E2E }}
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY_E2E }}
        GROQ_API_KEY: ${{ secrets.GROQ_API_KEY_E2E }}
        VERTEX_AI_API_CREDS: ${{ secrets.VERTEX_AI_API_CREDS_E2E }}
    steps:
      - uses: actions/checkout@v4
        with:
            ref: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.git.sha || github.event.inputs.sha }}
      - name: Check current commit status
        id: check-status
        run: |
          # Clear any existing MERGE_SHA to ensure clean state
          echo "MERGE_SHA=" >> $GITHUB_ENV
          BRANCH_SHA="${{ github.event_name == 'repository_dispatch' && github.event.client_payload.git.sha || github.event.inputs.sha }}"
          REPO="${{ github.repository }}"
          
          echo "Checking commit status for SHA: $BRANCH_SHA in $REPO"
          
          # First try with the branch SHA
          echo "Checking statuses for branch SHA..."
          branch_statuses=$(gh api \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/$REPO/commits/$BRANCH_SHA/statuses?per_page=100")
            
          # Count E2E pending statuses on branch SHA
          branch_pending_e2e=$(echo "$branch_statuses" | jq '[.[] | select(.state == "pending" and .context == "E2E Tests")] | length')
          echo "Branch SHA pending E2E Tests count: $branch_pending_e2e"
          
          # If we didn't find any pending E2E statuses on the branch SHA,
          # try to find the associated PR and check its merge commit
          if [ "$branch_pending_e2e" -eq "0" ]; then
            echo "No pending E2E Tests found on branch SHA, checking if this is part of a PR..."
            
            # Try to find PRs associated with this commit
            prs=$(gh api \
              -H "Accept: application/vnd.github+json" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              "/repos/$REPO/commits/$BRANCH_SHA/pulls")
            
            # If we found PRs, check the merge commit SHA
            if [ "$(echo "$prs" | jq 'length')" -gt "0" ]; then
              MERGE_SHA=$(echo "$prs" | jq -r '.[0].merge_commit_sha')
              echo "Found PR with merge commit SHA: $MERGE_SHA"
              
              merge_statuses=$(gh api \
                -H "Accept: application/vnd.github+json" \
                -H "X-GitHub-Api-Version: 2022-11-28" \
                "/repos/$REPO/commits/$MERGE_SHA/statuses?per_page=100")
              
              # Count pending E2E statuses on merge SHA
              merge_pending_e2e=$(echo "$merge_statuses" | jq '[.[] | select(.state == "pending" and .context == "E2E Tests")] | length')
              echo "Merge SHA pending E2E Tests count: $merge_pending_e2e"
              
              # Use the merge SHA count if it's greater than 0
              if [ "$merge_pending_e2e" -gt "0" ]; then
                pending_e2e="$merge_pending_e2e"
                echo "Using pending count from merge SHA: $pending_e2e"
                # Save merge SHA for later
                echo "merge_sha=$MERGE_SHA" >> $GITHUB_OUTPUT
              else
                pending_e2e="$branch_pending_e2e"
                echo "Using pending count from branch SHA: $pending_e2e"
              fi
            else
              pending_e2e="$branch_pending_e2e"
              echo "No PRs found, using branch SHA pending count: $pending_e2e"
            fi
          else
            pending_e2e="$branch_pending_e2e"
            echo "Found pending E2E Tests on branch SHA: $pending_e2e"
          fi
          
          # Get overall status (use branch SHA for this)
          current_status=$(gh api \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/$REPO/commits/$BRANCH_SHA/status" | jq -r '.state')
          echo "Current status: $current_status"
          
          # Set outputs
          EOF=$(dd if=/dev/urandom bs=15 count=1 status=none | base64)
          echo "current_status<<$EOF" >> $GITHUB_OUTPUT
          echo "$current_status" >> $GITHUB_OUTPUT
          echo "$EOF" >> $GITHUB_OUTPUT
          
          echo "pending_exists<<$EOF" >> $GITHUB_OUTPUT
          echo "$pending_e2e" >> $GITHUB_OUTPUT
          echo "$EOF" >> $GITHUB_OUTPUT
        env:
          GITHUB_TOKEN: ${{ github.token }}
          
      - name: Skip tests if no pending status exists
        if: steps.check-status.outputs.pending_exists == '0' || github.event_name == 'workflow_dispatch'
        run: |
          echo "No pending E2E status found for this commit. Skipping tests to save resources."
          exit 0

      # Set pending status on PR
      - name: Set pending status on PR
        if: steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch'
        uses: ./.github/actions/set-pr-status
        with:
          state: 'pending'
          description: 'E2E Tests are running'
          context: 'E2E Tests'
          token: ${{ github.token }}
          sha: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.git.sha || github.event.inputs.sha }}
          target_url: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

      - uses: actions/setup-node@v3
        if: steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch'
        with:
            node-version: 18
            
      - name: Install dependencies
        if: steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch'
        run: yarn install --frozen-lockfile
        working-directory: smith-frontend-e2e
        
      - name: Install Playwright Browsers
        if: steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch'
        run: yarn run playwright install --with-deps
        working-directory: smith-frontend-e2e
        
      - name: Run Playwright tests and capture output
        if: steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch'
        run: |
          set -o pipefail
          yarn run playwright test | tee test-results.txt
        working-directory: smith-frontend-e2e
        timeout-minutes: 250
        
      - uses: actions/upload-artifact@v4
        if: failure() && (steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch')
        with:
          name: playwright-report
          path: smith-frontend-e2e/playwright-report/
          retention-days: 7
          
      # Set status on head SHA which is more stable
      - name: Set success status on head SHA
        if: success() && steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch'
        uses: ./.github/actions/set-pr-status
        with:
          state: 'success'
          description: 'All e2e tests passed'
          context: 'E2E Tests'
          token: ${{ github.token }}
          sha: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.git.sha || github.event.inputs.sha }}
          target_url: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
          
          
      # Set failure status on head SHA  
      - name: Set failure status on head SHA
        if: failure() && (steps.check-status.outputs.pending_exists != '0' || github.event_name == 'workflow_dispatch')
        uses: ./.github/actions/set-pr-status
        with:
          state: 'failure'
          description: 'Some tests failed. Click for details and artifacts.'
          context: 'E2E Tests'
          token: ${{ github.token }}
          sha: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.git.sha || github.event.inputs.sha }}
          target_url: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
