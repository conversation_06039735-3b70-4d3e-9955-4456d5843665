name: Playwright E2E Tests - against Vercel preview deployment

on:
  repository_dispatch:
    types:
      - 'vercel.deployment.success'

jobs:
  run-e2es:
    if: github.event_name == 'repository_dispatch'
    timeout-minutes: 300
    runs-on: ubuntu-latest
    env:
        BASE_URL: ${{ github.event.client_payload.url }}
        ENDPOINT_URL: https://dev.api.smith.langchain.com
        SUPABASE_SECRET_ROLE: ${{ secrets.DEV_SUPABASE_SECRET }}
        SUPABASE_URL: ${{ secrets.DEV_SUPABASE_URL }}
        EKS_TEST_ACCOUNT_PASSWORD: ${{ secrets.EKS_TEST_ACCOUNT_PASSWORD }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY_E2E }}
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY_E2E }}
        GROQ_API_KEY: ${{ secrets.GROQ_API_KEY_E2E }}
        VERTEX_AI_API_CREDS: ${{ secrets.VERTEX_AI_API_CREDS_E2E }}
    steps:
      - uses: actions/checkout@v4
        with:
            ref: ${{ github.event.client_payload.git.sha }}
      - name: Check current commit status
        id: check-status
        run: |
          SHA="${{ github.event.client_payload.git.sha }}"
          REPO="${{ github.repository }}"
          
          statuses=$(gh api \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            /repos/$REPO/commits/$SHA/status)
          
          current_status=$(echo "$statuses" | jq -r '.state')
          # Check if any pending status exists for this commit
          pending_exists=$(echo "$statuses" | jq -r '.statuses[] | select(.state == "pending" and .description == "Waiting for E2E results") | .state' | grep -c "pending" || echo "0")
          
          echo "current_status=$current_status" >> $GITHUB_OUTPUT
          echo "pending_exists=$pending_exists" >> $GITHUB_OUTPUT
          echo "Current status: $current_status"
          echo "Pending exists: $pending_exists"
        env:
          GITHUB_TOKEN: ${{ github.token }}
          
      - name: Skip tests if no pending status exists
        if: steps.check-status.outputs.pending_exists == '0'
        run: |
          echo "No pending E2E status found for this commit. Skipping tests to save resources."
          exit 0
          
      # All subsequent steps will only run if the workflow hasn't exited
      - uses: actions/setup-node@v3
        if: steps.check-status.outputs.pending_exists != '0'
        with:
            node-version: 18
            
      - name: Install dependencies
        if: steps.check-status.outputs.pending_exists != '0'
        run: yarn install --frozen-lockfile
        working-directory: smith-frontend-e2e
        
      - name: Install Playwright Browsers
        if: steps.check-status.outputs.pending_exists != '0'
        run: yarn run playwright install --with-deps
        working-directory: smith-frontend-e2e
        
      - name: Run Playwright tests and capture output
        if: steps.check-status.outputs.pending_exists != '0'
        run: |
          set -o pipefail
          yarn run playwright test | tee test-results.txt
        working-directory: smith-frontend-e2e
        timeout-minutes: 250
        
      - uses: actions/upload-artifact@v4
        if: failure() && steps.check-status.outputs.pending_exists != '0'
        with:
          name: playwright-report
          path: smith-frontend-e2e/playwright-report/
          retention-days: 7
          
      - name: Set success status
        if: success() && steps.check-status.outputs.pending_exists != '0'
        uses: ./.github/actions/set-pr-status
        with:
          state: 'success'
          description: 'All e2e tests passed'
              
      - name: Set failure status
        if: failure() && steps.check-status.outputs.pending_exists != '0'
        uses: ./.github/actions/set-pr-status
        with:
          state: 'failure'
          description: 'Some tests failed'
