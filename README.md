# LangSmith

Platform that extends LangChain to include tracing, feedback, etc.

Deployed versions:

- [Production](https://smith.langchain.com)
- [Development](https://dev.smith.langchain.com)

## Standards

- Which kind of "record" class to use? When you need to use dictionaries/records, prefer built-ins over 3rd party libraries. If you need a mutable record, a TypedDict or a dataclass is good option. If you need an immutable record, a Namedtuple is a good option. If you need runtime validation, and can accept the performance hit, use Pydantic.

## Developing locally

---

### Pre-requisites

#### 0. Install Python 3.11, poetry, psql, Node.js >=18, and Docker

**NOTE**: We have only tested the following instructions with python 3.11 on MacOS.

**NOTE**: Ensure that the docker daemon is running before starting the database services.

**NOTE**: Ensure that nothing else is running locally on langsmith ports by running `lsof -i :5432 -i :6379 -i :8123 -i :9000 -i :9002 -i :9003`

If you use Homebrew, you can install poetry with:

```commandline
brew install poetry
```

[Optional] psql is a terminal-based tool that allows you to interact with postgres. Install psql with:

```commandline
brew install libpq
```

**NOTE**: For installing psql, brew installs "keg-only", meaning that it isn't in the PATH by default. After installation, you'll see some instructions on how to add it to your PATH - make sure to follow these instructions.

You can enable poetry shell completions for bash with:

```commandline
poetry completions bash >> ~/.bash_completion
```

or for other shells by following
the steps [here](https://python-poetry.org/docs/#enable-tab-completion-for-bash-fish-or-zsh).

#### 0.1 Install Golang

We use Golang for our authentication service. You can install it with Homebrew:

```commandline
brew install go
```
#### 0.2 Install Docker Desktop

We use Docker Desktop for our local development environment. You can install it with Homebrew:

```commandline
brew install docker
```

#### 1. Create the root Python environment

From the repo root, run:

```commandline
poetry install --no-root
```

This will create a virtualenv containing the `poe` task runner we use to manage repo-wide tasks.

#### 2. Install frontend and backend dependencies

The repo uses a "monorepo" layout with multiple projects side by side.
Each project has its own dependencies (and for Python projects, its own virtualenv)
which will need to be installed.

To simplify the initial setup, we'll use a `poe` script that will create all of them at once.
From the repo root, run:

```commandline
poetry run poe install-deps
```

if the above doesn't work run each of the following manually
```commandline
cd ~/langchainplus/smith-backend
poetry install
cd ~/langchainplus/smith-playground
poetry install
cd ~/langchainplus/lc_config
poetry install
cd ~/langchainplus/lc_database
poetry install
cd ~/langchainplus/lc_logging
poetry install
cd ~/langchainplus/lc_metrics
poetry install
```

If you get an error installing `uvloop` you need to ensure poetry is pointing at python 3.11, as this may occur on newer versions. Try running `poetry env use python3.11`.
If you got error on yarn: command not found, make sure you have node and yarn installed. You can install them with `brew install node yarn`.
After this step, you'll have `.venv` directories in each Python project in this repo
that contain all the necessary dependencies. If you use an IDE with python analysis tools,
you can add `.venv/lib/python3.11/site-packages` to your `PYTHONPATH` for the appropriate projects.


Then install all frontend dependencies from repo root
```commandline
poetry run poe install-frontend-deps
```
You'll also have all the JS dependencies needed to run frontend code.

#### 3. Start Database Services

From the repo root, run :

```commandline
cd smith-backend
make start-db
```

This will run docker-compose with external service dependences including Postgres, Clickhouse, Minio, and Redis.

#### 4. Configure Clickhouse

We use Clickhouse as our analytical db and handle migrations with golang-migrate.
To install this tool, run:

```commandline
brew install golang-migrate
```

You need to run a one-time command to configure clickhouse correctly locally.

From the repo root
```commandline
cd smith-backend
make setup-local-clickhouse  # Ensure that clickhouse is running locally
```
#### 5. Add secrets to your environment

```commandline
cp -r secrets.example secrets
```

Then, fill in the files in `secrets` with the appropriate values, which you can retrieve from 1Password
- [metronome and stripe](https://start.1password.com/open/i?a=O6OO2FN6EZHNLCAYAIK5QR4TFY&v=p7dhmibvegyaohdnj7yjzznpte&i=gc3jpnbk7cluguurzujh3nmnyy&h=langchaininc.1password.com)
- [api key salt and supabase](https://start.1password.com/open/i?a=O6OO2FN6EZHNLCAYAIK5QR4TFY&v=p7dhmibvegyaohdnj7yjzznpte&i=loscwyurzjn2ghfyu7kthohdwq&h=langchaininc.1password.com)
- [github client dev](https://start.1password.com/open/i?a=O6OO2FN6EZHNLCAYAIK5QR4TFY&v=p7dhmibvegyaohdnj7yjzznpte&i=khzags4p3qplk5pg4zr7io6gki&h=langchaininc.1password.com)
- [github jwt dev](https://start.1password.com/open/i?a=O6OO2FN6EZHNLCAYAIK5QR4TFY&v=p7dhmibvegyaohdnj7yjzznpte&i=fiorhincaygqfwdfqlydvf3c4q&h=langchaininc.1password.com)
---

### Recommended quick start: Run all services with one command

From the repo root, run:

```commandline
poetry run poe run-auth
```

This command handles starting all backend services, the database, and the frontend in one step.

### Alternative: Run without authentication

#### 1. Start all backend services and database
**NOTE**: Before running, make sure you don't have any Postgres instances running locally.

LangSmith:

```commandline
cd smith-backend
make start
```

Playground:

```commandline
cd smith-playground
make start
```

#### 2. Start the frontend

```commandline
cd smith-frontend
yarn start
```

---

### Run with authentication backed by Supabase remote project

#### 1. Start all backend services and database

**NOTE**: Before running, make sure you don't have any Postgres instances running locally.

LangSmith:

```commandline
cd smith-backend
make start-auth
```

Playground

```commandline
cd smith-playground
make start
```

#### 2. Start the frontend

```commandline
cd smith-frontend
yarn start:auth
```

### [Optional] Pre-populate traces

To make it easier to get started, you can pre-populate LangSmith with some traces.
To do this, go to `smith-backend/scripts/populate_traces.py` and fill in your OpenAI API key and LangSmith API key. Then, run:

```commandline
cd smith-backend/scripts
python populate_traces.py
```

---

### Run locally in basic auth mode
1. `pushd smith-backend && make db-down && sleep 1 && make db-clean && sleep 1 && make db-up && popd` - note that this removes all data in your local installation!
1. `poetry run poe run-basic` (or `poetry run poe run-basic-quiet`)
1. Bootstrap the installation (create the single organization, first workspace, and initial admin user):

```commandline
cd smith-backend
set -a && source ../.env.local_basic && INITIAL_ORG_ADMIN_EMAIL=<EMAIL> poetry run python hooks/auth_bootstrap.py
```

You should now be able to login with the INITIAL_ORG_ADMIN_EMAIL and INITIAL_ORG_ADMIN_PASSWORD (password is in `.env.local_basic`).

---

### Run locally in Google OAuth mode
1. (skip this if testing upgrade from basic auth mode) `pushd smith-backend && make db-down && sleep 1 && make db-clean && sleep 1 && make db-up && popd` - note that this removes all data in your local installation!
1. Add a file `.env` with `OAUTH_CLIENT_SECRET=<secret from 1pass "OAUTH_GOOGLE_SECRET local dev">`
1. `set -a && source .env && poetry run poe run-google-oauth` (or `set -a && source .env && poetry run poe run-google-oauth-quiet`)


You should now be able to login via Google (not through Supabase, and only @langchain.dev emails should work).

If you need a second account, <NAME_EMAIL> email. login info is available in 1pass
---

### Run locally with Datadog enabled

This may be helpful when enabling new Datadog integrations/features or to troubleshoot Datadog-related issues.

1. [Download the agent for Mac](https://langchain-us.datadoghq.com/account/settings/agent/latest?platform=macos) and run it, changing configuration in `~/.datadog-agent/datadog.yaml` as needed.

1. [Create an API key](https://langchain-us.datadoghq.com/organization-settings/api-keys) and copy to `secrets/DATADOG_API_KEY`

1. Add the env vars below to `.env.local_dev` and/or others from Datadog docs, depending on what the goal is to test.
For example, [docs for ddtrace environment variables](https://ddtrace.readthedocs.io/en/stable/configuration.html).

Replace/remove depending on use case. In this scenario, the goal was to test tracing by sampling
a single endpoint in smith-go at 100%.
```
DATADOG_ENABLED=true
DATADOG_TRACE_ENABLED=true
DD_ENV=local
DD_SERVICE=test-smith-go
DD_TRACE_SAMPLING_RULES='[{"service": "test-smith-go", "resource": "GET /auth", "sample_rate": 1}]'
```

3. Run the desired service(s) as shown above and test as needed.


### Run locally with Metronome reporting enabled

Normally Metronome reporting is disabled in local dev mode. To enable, update these variables in `.env.local_dev` to enable and process more quickly:
```
FF_PROCESS_BILLING_TRANSACTIONS=true
FF_METRONOME_TRACE_REPORTING_ENABLED=true
TRANSACTION_PROCESSING_DELAY_SEC=10
METRONOME_TRACE_REPORTING_CRON="* * * * *"
```

### Testing

```commandline
cd smith-backend
make tests
```

#### Run Python tests

Replace the `TEST_PATH` to target specific tests to rerun on changes. You can include multiple paths to rerun multiple specific tests, separated by spaces.

run tests in auth mode
```commandline
TEST_PATH='app/tests/api/test_run_rules.py::test_transient_rule' make tests-auth
```

run tests in watch mode
```commandline
TEST_PATH='app/tests/api/test_run_rules.py::test_transient_rule' make tests-watch-auth
```

#### Output SAQ logs in parallel test modes

Due to a quirk with pytest-xdist, you must pass an additional parameter to pytest for SAQ subprocess logs to show up:

```commandline
LOG_LEVEL=INFO TEST_PATH='<optional-test-file-path> --log-cli-level=info' make tests-auth-parallel
```



#### MinIO: Connect locally

We use [MinIO](https://hub.docker.com/r/minio/minio) to simulate S3-compatible object storage locally.

1. Install the client: `brew install minio/stable/mc`
1. Add an alias for the local docker installation: `mc alias set local http://localhost:9002 minioadmin1 minioadmin1`
1. Check for files: `mc ls local/<bucket>/<prefix> --recursive > files.txt` e.g. `mc ls local/langsmith-images-test/ls_exports --recursive > files.txt`

#### ClickHouse: Connect locally

1. Install ClickHouse: https://clickhouse.com/docs/en/install#quick-install
1. (Optional) Move to somewhere on `PATH`: `sudo mv ~/clickhouse /usr/local/bin`
1. Connect (alternatively use `.env.local_dev`): `set -a && source .env.local_test && clickhouse client --host localhost --port 9000 --user $CLICKHOUSE_USER --password $CLICKHOUSE_PASSWORD --database $CLICKHOUSE_DB`.
**Note that this will set env vars in your current shell.** Alternatively replace the `$VARS` with the actual values and omit the `set -a && source <.env>`.

#### Azurite: Connect locally

We use [Azurite](https://github.com/azure/azurite) to simulate Azure Blob Storage locally.

1. Install Azure CLI: `brew install azure-cli`
1. Set the connection string: `export AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=http;AccountName=admin;AccountKey=password;BlobEndpoint=http://127.0.0.1:10000/admin;"`
1. List the blobs: `az storage blob list --container-name langsmith-images-test --output table`

### Linting and Formatting

backend (including shared utils, from repo root):

```commandline
source .venv/bin/activate
poe format
poe lint
```

frontend (run from `smith-frontend`):

```commandline
yarn format
yarn lint
```

### Enabling custom code evaluators

To test custom code evaluators, you must first install deno which you can do by running the following command, and then restarting the backend server.

```commandline
curl -fsSL https://deno.land/install.sh | sh
```

---

## Building and running with Docker

See the Makefile commands in the toplevel langchainplus directory:

```commandline
$ make build-all
$ docker-compose up
```

## Deploying

This is mostly handled by CI.

**DEV**

1. On every merge to main in `langchainplus`:

   1. The `backend` image is built and submitted to GCR (both dev and prod).
   2. Migrations against the dev Cloud SQL database are run via alembic.
   3. A pull request is issued to our deployments (terraform) repo to update the Google Cloud Run docker image tags.
   4. The frontend is built and deployed with Vercel.

2. After a pull request is merged in `langchainplus`, merge the PR in `deployments`, then run `terraform apply` on the latest `main` branch in `environments/dev` to deploy the new image to the dev environment.

**PROD**
The backend and frontend are deployed separately, so the process is a bit more involved. The backend is deployed first, then the frontend to avoid downtime.

1. Issue and merge a pull request from `main` to `prod-backend` in `langchainplus` to run migrations against the prod Cloud SQL database.
2. After the pull request is merged, run `terraform apply` on the latest `main` branch in `environments/prod` to deploy the new image to the prod environment.
3. Issue and merge a pull request from `prod-backend` to `prod` in `langchainplus` to deploy the new image to the prod frontend.

## Connect to development or production database via Cloud SQL Proxy

Postgres database is hosted on Google Cloud SQL. To connect to it, you need to use the Cloud SQL Proxy. Ensure you have access to the `langchain-dev` and/or `langchain-prod` projects on Google Cloud.

1. Install the `gcloud` CLI, instructions found [here](https://cloud.google.com/sdk/docs/install). You can also install it via Homebrew on Mac. (`brew install --cask google-cloud-sdk`).
2. Download the Cloud SQL Proxy binary from [here](https://cloud.google.com/sql/docs/postgres/sql-proxy#install).
3. Authenticate with `gcloud auth login` and `gcloud auth application-default login`.
4. Using the connection name of the SQL database, connect using `./cloud_sql_proxy [connection name]`.

   - DEV instance connection name: `langchain-dev:us-central1:langchainplus-db`
   - PROD instance connection name: `langchain-prod:us-central1:langchainplus-db`

5. Connect to the database using the credentials found in the 1Password vault (`DEV GCP Cloud SQL` for development for instance).

### Pre-requisites

1. Run Cloud SQL Proxy
2. Download the prod and dev env files from 1Password and load it as follows: `set -o allexport; source .env.local_waitlist_dev; set +o allexport`. NEVER CHECK IN THESE FILES.

### Exporting users

```bash
# Export emails of all non-whitelisted users to stdout
python3 smith-backend/scripts/waitlist.py export-users

# Export emails of first 100 users non-whitelisted
# users to stdout, ordered by date of creation
python3 smith-backend/scripts/waitlist.py export-users -n 100

# Export emails of users non-whitelisted users to stdout
# based on a email pattern
python3 smith-backend/scripts/waitlist.py export-users --email "%@gmail.com"

# Export emails of non-whitelisted users to a file
python3 smith-backend/scripts/waitlist.py export-users -o output.txt
```

### Importing users

```bash
# Mark users with the given emails as whitelisted
python3 smith-backend/scripts/waitlist.py import-users users.txt
```

### Whitelisting users (export and import directly)

```bash
# Mark first 100 users with @gmail.com suffix as whitelisted, dry run
python3 smith-backend/scripts/waitlist.py whitelist-users --dry -n 100 --email "%@gmail.com"
```

### Creating invite codes

```bash
# Create an invite code "test123"
python3 smith-backend/scripts/waitlist.py add-invite-code test123

# Delete an invite code "test123"
python3 smith-backend/scripts/waitlist.py delete-invite-code test123

# Delete all invite codes
python3 smith-backend/scripts/waitlist.py delete-all-invite-codes
```

## Troubleshooting

### I've got a blank page in development and 504 (Outdated Optimize Dep)

As a workaround, remove the `.vite` folder from `node_modules` and re-run the Vite development server.

```
rm -rf node_modules/.vite
```

### How do I see React components in the Chrome inspector?

Use the [React devtool extension](https://chrome.google.com/webstore/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)!
