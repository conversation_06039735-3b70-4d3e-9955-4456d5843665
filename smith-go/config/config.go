package config

import (
	"context"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"slices"
	"strconv"
	"strings"

	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"

	"github.com/Netflix/go-env"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/httplog/v2"
	"github.com/golang-jwt/jwt/v4"
	"github.com/joho/godotenv"
)

// Non-JSON list types must be present in environment, not just 'default' struct tag, if > 1 elements.
// Defaults don't work with go-env with comma separator https://github.com/Netflix/go-env/issues/32
type CommaSeparatedList struct {
	SplitList []string
}

func (s *CommaSeparatedList) UnmarshalEnvironmentValue(data string) error {
	s.SplitList = append(s.SplitList, strings.Split(data, ",")...)
	return nil
}

func (s CommaSeparatedList) MarshalEnvironmentValue() (string, error) {
	return strings.Join(s.SplitList, ","), nil
}

func (c CommaSeparatedList) Contains(s string) bool {
	for _, v := range c.SplitList {
		if v == s {
			return true
		}
	}
	return false
}

type List struct {
	SplitList []string
}

func (c *List) UnmarshalEnvironmentValue(data string) error {
	if err := json.Unmarshal([]byte(data), &c.SplitList); err != nil {
		return err
	}
	return nil
}

func (c List) MarshalEnvironmentValue() (string, error) {
	data, err := json.Marshal(c.SplitList)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func (c List) Contains(s string) bool {
	for _, v := range c.SplitList {
		if v == s {
			return true
		}
	}
	return false
}

type CtxKeyList struct {
	SplitList []CtxKey
}

func (c *CtxKeyList) UnmarshalEnvironmentValue(data string) error {
	if err := json.Unmarshal([]byte(data), &c.SplitList); err != nil {
		return err
	}
	return nil
}

func (c CtxKeyList) MarshalEnvironmentValue() (string, error) {
	data, err := json.Marshal(c.SplitList)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func (c CtxKeyList) Contains(s CtxKey) bool {
	for _, v := range c.SplitList {
		if v == s {
			return true
		}
	}
	return false
}

type SlogLevel slog.Level

func (s *SlogLevel) UnmarshalEnvironmentValue(data string) error {
	switch strings.ToLower(data) {
	case "debug":
		*s = SlogLevel(slog.LevelDebug)
	case "warn":
		*s = SlogLevel(slog.LevelWarn)
	case "error":
		*s = SlogLevel(slog.LevelError)
	case "info":
	default:
		*s = SlogLevel(slog.LevelInfo)
	}
	return nil
}

func (s SlogLevel) MarshalEnvironmentValue() (string, error) {
	return slog.Level(s).String(), nil
}

type TraceTierTTLDurationSecMap map[string]int64

func (s *TraceTierTTLDurationSecMap) UnmarshalEnvironmentValue(data string) error {
	trimmed := strings.Trim(data, `"`)
	if trimmed == "" {
		*s = TraceTierTTLDurationSecMap{
			"longlived":  60 * 60 * 24 * 400,
			"shortlived": 60 * 60 * 24 * 14,
		}
		return nil
	}
	if err := json.Unmarshal([]byte(data), s); err != nil {
		fmt.Println("Error unmarshalling TraceTierTTLDurationSecMap", err, data)
		return err
	}
	return nil
}

func (s TraceTierTTLDurationSecMap) MarshalEnvironmentValue() (string, error) {
	data, err := json.Marshal(s)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

type S3TraceTierPrefixMap map[string]string

func (s *S3TraceTierPrefixMap) UnmarshalEnvironmentValue(data string) error {
	trimmed := strings.Trim(data, `"`)
	if trimmed == "" {
		*s = S3TraceTierPrefixMap{
			"longlived":  "ttl_l",
			"shortlived": "ttl_s",
		}
		return nil
	}
	if err := json.Unmarshal([]byte(data), s); err != nil {
		fmt.Println("Error unmarshalling S3TraceTierPrefixMap", err, data)
		return err
	}
	return nil
}

func (s S3TraceTierPrefixMap) MarshalEnvironmentValue() (string, error) {
	data, err := json.Marshal(s)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

type JSONMap map[string]string

func (m *JSONMap) UnmarshalEnvironmentValue(data string) error {
	trimmed := strings.Trim(data, `"`)
	if trimmed == "" {
		*m = JSONMap{}
		return nil
	}
	if err := json.Unmarshal([]byte(data), m); err != nil {
		fmt.Println("Error unmarshalling JSONMap", err, data)
		return err
	}
	return nil
}

func (m JSONMap) MarshalEnvironmentValue() (string, error) {
	data, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

type Environment struct {
	LangchainEnv string

	ServiceName string `env:"SERVICE_NAME,default=platform-backend"` // Needed to differentiate between platform-backend and ingest-backend

	AuthType                         string             `env:"AUTH_TYPE,default=supabase"`
	AuditLogsEnabled                 bool               `env:"AUDIT_LOGS_ENABLED,default=false"`
	SingletonTenantId                string             `env:"SINGLETON_TENANT_ID"`
	PgDatabaseURI                    string             `env:"POSTGRES_DATABASE_URI"`
	PgbouncerDatabaseURI             string             `env:"PGBOUNCER_DATABASE_URI"`
	PgSchema                         string             `env:"POSTGRES_SCHEMA,default=public"`
	AsyncpgPoolMaxSize               int                `env:"ASYNCPG_POOL_MAX_SIZE,default=50"`
	AsyncPgPoolTimeoutSec            int                `env:"ASYNCPG_POOL_TIMEOUT_SEC,default=60"`
	ApiKeySalt                       string             `env:"API_KEY_SALT"`
	SupabaseJwtSecret                string             `env:"SUPABASE_JWT_SECRET"`
	BasicAuthEnabled                 bool               `env:"BASIC_AUTH_ENABLED,default=false"`
	BasicAuthJwtSecret               string             `env:"BASIC_AUTH_JWT_SECRET"`
	BasicAuthJwtExpirationSeconds    int                `env:"BASIC_AUTH_JWT_EXPIRATION_SECONDS,default=604800"`
	LangSmithUrl                     string             `env:"LANGSMITH_URL"`
	LangSmithLicenseKey              string             `env:"LANGSMITH_LICENSE_KEY"`
	SmithBackendEndpoint             string             `env:"SMITH_BACKEND_ENDPOINT"`
	PlatformBackendEndpoint          string             `env:"GO_ENDPOINT"`
	BeaconEndpoint                   string             `env:"BEACON_ENDPOINT"`
	LogLevel                         string             `env:"LOG_LEVEL,default=info"`
	SlogLevel                        SlogLevel          `env:"LOG_LEVEL,default=info"`
	DbLogLevel                       string             `env:"DB_LOG_LEVEL,default=error"`
	Port                             string             `env:"PORT,default=8080"`
	CORSAllowedOrigins               CommaSeparatedList `env:"CORS_ALLOWED_ORIGINS,default=*"`
	CORSAllowedOriginsRegex          string             `env:"CORS_ALLOWED_ORIGINS_REGEX"`
	XServiceAuthJwtSecret            string             `env:"X_SERVICE_AUTH_JWT_SECRET"`
	XServiceAuthJwtExpirationSeconds int                `env:"X_SERVICE_AUTH_JWT_EXPIRATION_SECONDS,default=3600"`
	AuthCacheTTLSec                  int                `env:"AUTH_CACHE_TTL_SEC,default=60"`
	RedisDatabaseURI                 string             `env:"REDIS_DATABASE_URI"`
	RedisCachingDatabaseURI          string             `env:"REDIS_CACHE_DATABASE_URI"`
	RedisShardURIs                   JSONMap            `env:"REDIS_SHARD_URIS"`
	RedisShardingEnabled             bool               `env:"REDIS_SHARDING_ENABLED,default=false"`
	RedisShardedWritesEnabled        bool               `env:"REDIS_SHARDED_WRITES_ENABLED,default=false"`
	RedisShardedQueuesEnabled        bool               `env:"REDIS_SHARDED_QUEUES_ENABLED,default=false"`
	RedisShardingOverrideWorkspaces  JSONMap            `env:"REDIS_SHARDING_OVERRIDE_WORKSPACES,default={}"`
	RedisShardingByTraceTenantIds    List               `env:"REDIS_SHARDING_BY_TRACE_TENANT_IDS,default=[]"`
	RedisMinVersion                  string             `env:"REDIS_MIN_VERSION,default=6.0.0"`
	RedisUseSmismember               bool               `env:"REDIS_USE_SMISMEMBER,default=false"`
	StripeSecretKey                  string             `env:"STRIPE_SECRET_KEY"`
	IsSelfHosted                     bool               `env:"IS_SELF_HOSTED,default=false"`
	RedisRunsExpirySeconds           int                `env:"REDIS_RUNS_EXPIRY_SECONDS,default=43200"`
	RedisTransactionRunsChunkSize    int                `env:"REDIS_TRANSACTION_RUNS_CHUNK_SIZE,default=40"` // ~25 Redis commands per run, so 40 * 25 = 1000 length pipeline

	// ClickHouse: https://clickhouse.com/docs/en/guides/sre/network-ports
	ClickHouseHost                    string `env:"CLICKHOUSE_HOST"`
	ClickHousePort                    int    `env:"CLICKHOUSE_NATIVE_PORT"`
	ClickHouseUser                    string `env:"CLICKHOUSE_USER"`
	ClickHousePassword                string `env:"CLICKHOUSE_PASSWORD"`
	ClickHouseDb                      string `env:"CLICKHOUSE_DB"`
	ClickHouseTLS                     bool   `env:"CLICKHOUSE_TLS,default=false"`
	ClickHouseMaxConnections          int    `env:"CLICKHOUSE_MAX_CONNECTIONS,default=100"`
	ClickHouseMaxKeepAliveConnections int    `env:"CLICKHOUSE_MAX_KEEPLIVE_CONNECTIONS,default=20"`
	ClickHouseKeepAliveExpiry         int    `env:"CLICKHOUSE_KEEPALIVE_EXPIRY,default=9"`
	ClickHouseConnectTimeout          int    `env:"CLICKHOUSE_CONNECT_TIMEOUT,default=5"`
	ClickHouseReadTimeout             int    `env:"CLICKHOUSE_READ_TIMEOUT,default=30"`

	// Audit logs (defaults are set dynamically if env var not present)
	AuditLogContextVars          CtxKeyList `env:"AUDIT_LOG_CONTEXT_VARS,default=[]"`
	SanitizedAuditLogContextVars CtxKeyList `env:"SANITIZED_AUDIT_LOG_CONTEXT_VARS,default=[]"`

	DatadogEnabled          bool               `env:"DATADOG_ENABLED,default=false"`
	DatadogS3Disabled       bool               `env:"DATADOG_S3_DISABLED,default=false"`
	DatadogProfilingEnabled bool               `env:"DATADOG_PROFILING_ENABLED,default=false"`
	DatadogIgnoreRoutes     CommaSeparatedList `env:"DATADOG_IGNORE_ROUTES,default=\"\""`
	HttpLogQuietRoutes      CommaSeparatedList `env:"HTTP_LOG_QUIET_ROUTES,default=\"\""`
	HttpLogQuietPeriodSec   int                `env:"HTTP_LOG_QUIET_PERIOD_SEC,default=60"`

	// OAuth settings
	OAuthIssuerUrl string `env:"OAUTH_ISSUER_URL"`

	// Provider-specific settings when not using PKCE
	OAuthClientId     string `env:"OAUTH_CLIENT_ID"`
	OAuthClientSecret string `env:"OAUTH_CLIENT_SECRET"`

	// 1 day by default. This is the max session duration without any refreshes.
	OAuthSessionMaxSec int `env:"OAUTH_SESSION_MAX_SEC,default=86400"`

	OAuthCallbackUrl         string
	OAuthCallbackUrlOverride string `env:"OAUTH_CALLBACK_URL_OVERRIDE"`
	OAuthRedirectUrl         string
	OauthScopes              CommaSeparatedList `env:"OAUTH_SCOPES,default=\"\""`
	OauthOverrideTokenExpiry bool               `env:"OAUTH_OVERRIDE_TOKEN_EXPIRY,default=false"`
	SupportedProviders       map[string]bool

	// Blob storage ingestion settings
	SpoolMinSizeKB int64 `env:"SPOOL_MIN_SIZE_KB,default=100"`
	SpoolLimitGB   int64 `env:"SPOOL_LIMIT_GB,default=10"`

	// Compression settings
	CompressMinSizeKB int64 `env:"COMPRESS_MIN_SIZE_KB,default=1"`

	// Storage settings
	AWSRegion                          string `env:"AWS_REGION,default=us-east-1"`
	S3StorageEnabled                   bool   `env:"FF_S3_STORAGE_ENABLED,default=false"`
	BlobStorageEnabled                 bool   `env:"FF_BLOB_STORAGE_ENABLED,default=false"`
	BlobStorageEngine                  string `env:"BLOB_STORAGE_ENGINE,default=S3"`
	S3AccessKey                        string `env:"S3_ACCESS_KEY"`
	S3AccessKeySecret                  string `env:"S3_ACCESS_KEY_SECRET"`
	S3ApiUrl                           string `env:"S3_API_URL"`
	S3BucketName                       string `env:"S3_BUCKET_NAME"`
	S3SingleRegionBucketName           string `env:"S3_SINGLE_REGION_BUCKET_NAME"`
	FFSingleRegionBucketEnabledTenants List   `env:"FF_SINGLE_REGION_BUCKET_ENABLED_TENANTS,default=[]"`
	S3Profile                          string `env:"S3_PROFILE"`
	AzureStorageAccountName            string `env:"AZURE_STORAGE_ACCOUNT_NAME"`
	AzureStorageAccountKey             string `env:"AZURE_STORAGE_ACCOUNT_KEY"`
	AzureStorageContainerName          string `env:"AZURE_STORAGE_CONTAINER_NAME"`
	AzureStorageConnectionString       string `env:"AZURE_STORAGE_CONNECTION_STRING"`
	AzureStorageSASToken               string `env:"AZURE_STORAGE_SAS_TOKEN"`
	AzureStorageServiceUrlOverride     string `env:"AZURE_STORAGE_SERVICE_URL_OVERRIDE"`

	BlobStorageFetchSemaphore int `env:"BLOB_STORAGE_FETCH_SEMAPHORE,default=100"`

	// Transport settings
	Http2MaxConcurrentStreams uint32 `env:"HTTP2_MAX_CONCURRENT_STREAMS,default=2500"`
	HttpIdleTimeoutSecs       int    `env:"HTTP_IDLE_TIMEOUT_SECS,default=60"`
	// Trace tiers
	FFTraceTiersEnabled        bool                       `env:"FF_TRACE_TIERS_ENABLED,default=false"`
	DefaultTraceTier           string                     `env:"DEFAULT_TRACE_TIER,default=shortlived"`
	TraceTierTtlDurationSecMap TraceTierTTLDurationSecMap `env:"TRACE_TIER_TTL_DURATION_SEC_MAP,default=\"\""`
	// Queue settings
	AdhocQueue     string `env:"ADHOC_QUEUE,default=adhoc"`
	IngestionQueue string `env:"INGESTION_QUEUE,default=default"`
	// Ingest project creation semaphore
	IngestProjectCreationSemaphore int `env:"INGEST_PROJECT_CREATION_SEMAPHORE,default=10"`
	// Rate limit settings
	RateLimitRejectExpirySec  int `env:"RATE_LIMIT_REJECT_EXPIRY_SEC,default=60"`
	UsageLimitRejectExpirySec int `env:"USER_DEFINED_USAGE_LIMIT_REJECT_EXPIRY_SEC,default=60"`
	// Used by Beacon to issue short-lived license JWTs
	LicensePrivateKeyBase64 string `env:"LICENSE_PRIVATE_KEY_BASE64"`
	LicenseKeyID            string `env:"LICENSE_KEY_ID"`
	LicensePrivateKey       *rsa.PrivateKey
	// Metronome settings
	MetronomeApiKey               string `env:"METRONOME_API_KEY"`
	MetronomeTimeoutSecs          int    `env:"METRONOME_TIMEOUT_SECS,default=30"`
	OrgMetronomeCacheExpirySec    int    `env:"ORG_METRONOME_CACHE_EXPIRY_SEC,default=60"`
	MetronomeConfigCacheExpirySec int    `env:"METRONOME_CONFIG_CACHE_EXPIRY_SEC,default=15"`
	MetronomeMiddlewareEnabled    bool   `env:"METRONOME_MIDDLEWARE_ENABLED,default=false"`
	// Payment settings
	PaymentEnabled bool `env:"FF_PAYMENT_ENABLED,default=false"`
	// Otel settings
	GenericOtelEnabled        bool `env:"GENERIC_OTEL_ENABLED,default=false"`
	GenericOtelEnabledTenants List `env:"GENERIC_OTEL_ENABLED_TENANTS,default=[]"`

	// Instance Flags
	FFOrgCreationDisabled             bool `env:"FF_ORG_CREATION_DISABLED,default=false"`
	FFPersonalOrgsDisabled            bool `env:"FF_PERSONAL_ORGS_DISABLED,default=false"`
	FFCHSearchEnabled                 bool `env:"FF_CH_SEARCH_ENABLED,default=true"`
	FFWorkspaceScopeOrgInvitesEnabled bool `env:"FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED,default=false"`
	InfoCacheMaxAgeSeconds            int  `env:"INFO_CACHE_MAX_AGE_SEC,default=60"`
	FFZstdCompressionEnabled          bool `env:"FF_ZSTD_COMPRESSION_ENABLED,default=true"`
	ExperimentalSearchEnabled         bool `env:"QUICKWIT_SEARCH_ENABLED,default=false"`
	FFV1ApiKeysEnabled                bool `env:"FF_V1_API_KEYS_ENABLED,default=false"`

	// Batch Ingest
	BatchIngestUseMultipartEndpoint   bool `env:"BATCH_INGEST_USE_MULTIPART_ENDPOINT,default=true"`
	BatchIngestScaleUpQsizeTrigger    int  `env:"BATCH_INGEST_SCALE_UP_QSIZE_TRIGGER,default=1000"`
	BatchIngestScaleUpNthreadsLimit   int  `env:"BATCH_INGEST_SCALE_UP_NTHREADS_LIMIT,default=16"`
	BatchIngestScaleDownNemptyTrigger int  `env:"BATCH_INGEST_SCALE_DOWN_NEMPTY_TRIGGER,default=4"`
	BatchIngestSizeLimit              int  `env:"BATCH_INGEST_SIZE_LIMIT,default=100"`
	BatchIngestSizeLimitBytes         int  `env:"BATCH_INGEST_SIZE_LIMIT_BYTES,default=20971520"`

	MinBlobStorageSizeKb          int64                `env:"MIN_BLOB_STORAGE_SIZE_KB,default=0"` // keep default at 0 for self hosted and hybrid
	MinMultipartBlobStorageSizeKb int64                `env:"MIN_MULTIPART_BLOB_STORAGE_SIZE_KB,default=2"`
	S3TraceTierPrefixMap          S3TraceTierPrefixMap `env:"S3_TRACE_TIER_PREFIX_MAP,default=\"\""`

	// Trace prefetch settings
	TokenStatsSemaphoreSize         int  `env:"TOKEN_STATS_SEMAPHORE_SIZE,default=100"`
	PrefetchKeyExpirationSec        int  `env:"PREFETCH_KEY_EXPIRATION_SEC,default=1800"`
	FFTracePrefetchEnabledTenantIds List `env:"TRACE_PREFETCH_ENABLED_TENANT_IDS,default=[]"`
	MinimumTraceAgeSeconds          int  `env:"MINIMUM_TRACE_AGE_SECONDS,default=15"`
	TokenStatsStartTimeBufferMs     int  `env:"TOKEN_STATS_START_TIME_BUFFER_MS,default=1000"`

	MaxTraceLimit int `env:"MAX_TRACE_LIMIT,default=25000"`

	// health check settings
	CPUHealthEnabled         bool    `env:"CPU_HEALTH_ENABLED,default=true"`
	CPUHealthIntervalSeconds int     `env:"CPU_HEALTH_INTERVAL_SECONDS,default=30"`
	CPUHealthThreshold       float64 `env:"CPU_HEALTH_THRESHOLD,default=1.75"`

	// Alerts settings
	AggregateAlertRuleDelayMinutes   int  `env:"AGGREGATE_ALERT_RULE_DELAY_MINUTES,default=2"` // wait time before expiring the minute key after it is out of the alert window
	RedisClusterDatabaseURIs         List `env:"REDIS_CLUSTER_DATABASE_URIS,default=[]"`
	RedisClusterEnabled              bool `env:"REDIS_CLUSTER_ENABLED,default=false"`
	RedisClusterAlertsEnabled        bool `env:"REDIS_CLUSTER_ALERTS_ENABLED,default=false"`
	RedisClusterDualWriteEnabled     bool `env:"REDIS_CLUSTER_DUAL_WRITE_ENABLED,default=false"`
	RedisShardDualWriteEnabled       bool `env:"REDIS_SHARD_DUAL_WRITE_ENABLED,default=false"`
	RedisShardDualWriteSkipTenantIds List `env:"REDIS_SHARD_DUAL_WRITE_SKIP_TENANT_IDS,default=[]"`
	MaxAlertRulesPerProject          int  `env:"MAX_ALERT_RULES_PER_PROJECT,default=10"`

	// Session lookup settings
	RedisSessionLookupEnabled bool `env:"REDIS_SESSION_LOOKUP_ENABLED,default=true"`

	// Redis cluster settings
	RedisClusterDiscoverIP              string `env:"REDIS_CLUSTER_DISCOVER_IP,default=\"\""`
	RedisClusterDiscoverPort            int    `env:"REDIS_CLUSTER_DISCOVER_PORT,default=6379"`
	RedisClusterIAMAuthEnabled          bool   `env:"REDIS_CLUSTER_IAM_AUTH_ENABLED,default=false"`
	RedisClusterIAMAuthTokenCacheBuffer int    `env:"REDIS_CLUSTER_IAM_AUTH_TOKEN_CACHE_BUFFER,default=5"`
	RedisClusterIngestionGlobalEnabled  bool   `env:"REDIS_CLUSTER_INGESTION_GLOBAL_ENABLED,default=false"`
	RedisClusterIngestionTenantIds      List   `env:"REDIS_CLUSTER_INGESTION_TENANT_IDS,default=[]"`

	// Multipart settings
	FFOrderingMultipartEnabledTenants List `env:"FF_ORDERING_MULTIPART_ENABLED_TENANTS,default=[]"`

	// Feedback Config settings
	FFUsePgForFeedbackConfigsFetchEnabledTenants List `env:"FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_TENANTS,default=[]"`
	FFUsePgForFeedbackConfigsFetchEnabledAll     bool `env:"FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_ALL,default=false"`
}

// Function to strip postgres/postgresql prefix
func stripPostgresPrefix(uri string) string {
	uri = strings.Replace(uri, "postgresql://", "", 1)
	uri = strings.Replace(uri, "postgres://", "", 1)
	return uri
}

var Env Environment

func LoadEnv() {
	// Load LANGCHAIN_ENV
	if read, ok := os.LookupEnv("LANGCHAIN_ENV"); ok {
		Env.LangchainEnv = read
	} else {
		Env.LangchainEnv = "local_dev"
	}

	// Load environment variables from .env file
	if err := godotenv.Load("../.env." + Env.LangchainEnv); err == nil {
		// pass
	} else if err := godotenv.Load("../../.env." + Env.LangchainEnv); err != nil {
		panic(err)
	}

	// Load all files in secrets directory and update config
	secretsDir := "../secrets"
	if _, err := os.Stat(secretsDir); err == nil {
		files, err := os.ReadDir(secretsDir)
		if err != nil {
			panic(err)
		}
		for _, file := range files {
			if file.IsDir() {
				continue
			}
			envVariables := map[string]string{}
			value, err := os.ReadFile(fmt.Sprintf("%s/%s", secretsDir, file.Name()))
			if err != nil {
				panic(err)
			}
			envVariables[file.Name()] = string(value)

			// Check if the env variable is already set
			currentEnv := map[string]bool{}
			rawEnv := os.Environ()
			for _, rawEnvLine := range rawEnv {
				splits := strings.Split(rawEnvLine, "=")
				if len(splits) > 0 {
					key := splits[0]
					currentEnv[key] = true
				}
			}
			for key, value := range envVariables {
				if !currentEnv[key] {
					_ = os.Setenv(key, strings.TrimSuffix(value, "\n"))
				}
			}

		}
	}

	// Populate Env
	if _, err := env.UnmarshalFromEnviron(&Env); err != nil {
		fmt.Printf("Error marshalling environment variables: %v\n", err)
		panic(err)
	}
	// Assign conditional defaults
	if Env.AuthType == "none" {
		Env.SingletonTenantId = "00000000-0000-0000-0000-000000000000"
	}

	// Assign dynamically-generated config
	Env.SupportedProviders = make(map[string]bool)
	Env.SupportedProviders = map[string]bool{
		"custom-oidc": true,
	}
	if (Env.LangchainEnv == "local_dev" || Env.LangchainEnv == "local_test") && Env.OAuthCallbackUrlOverride == "" {
		Env.OAuthCallbackUrl = Env.SmithBackendEndpoint + "/api/v1/oauth/custom-oidc/callback"
	} else if Env.OAuthCallbackUrlOverride == "" {
		Env.OAuthCallbackUrl = Env.LangSmithUrl + "/api/v1/oauth/custom-oidc/callback"
	} else if Env.OAuthCallbackUrlOverride != "" {
		Env.OAuthCallbackUrl = Env.OAuthCallbackUrlOverride
	}
	Env.OAuthRedirectUrl = Env.LangSmithUrl + "/oauth-callback"
	// Respect legacy FF_S3_STORAGE_ENABLED flag
	// TODO: Remove this in v0.9
	if Env.S3StorageEnabled && !Env.BlobStorageEnabled {
		Env.BlobStorageEnabled = true
	}

	// Decode license private key from base64 if provided
	if Env.LicensePrivateKeyBase64 != "" {
		data, err := base64.StdEncoding.DecodeString(Env.LicensePrivateKeyBase64)
		if err != nil {
			fmt.Println("Error parsing public key from base64")
		}
		privateKey, err := jwt.ParseRSAPrivateKeyFromPEM(data)
		if err != nil {
			fmt.Println("Private Key is not a valid RSA private key")
		}
		Env.LicensePrivateKey = privateKey
	}

	if Env.RedisCachingDatabaseURI == "" {
		Env.RedisCachingDatabaseURI = Env.RedisDatabaseURI
	}

	if Env.RedisMinVersion != "" {
		parts := strings.Split(Env.RedisMinVersion, ".")
		if len(parts) < 2 || len(parts) > 3 {
			fmt.Println("Invalid REDIS_MIN_VERSION format, must include major and minor in semver format")
		} else {
			major, err := strconv.Atoi(parts[0])
			if err != nil {
				fmt.Printf("Error parsing REDIS_MIN_VERSION major version: %v\n", err)
				return
			}
			minor, err := strconv.Atoi(parts[1])
			if err != nil {
				fmt.Printf("Error parsing REDIS_MIN_VERSION minor version: %v\n", err)
				return
			}
			Env.RedisUseSmismember = major > 7 || (major == 6 && minor > 2)
		}
	}

	if len(Env.AuditLogContextVars.SplitList) == 0 {
		Env.AuditLogContextVars.SplitList = append(Env.AuditLogContextVars.SplitList, AuditLogContextVars...)
	}
	if len(Env.SanitizedAuditLogContextVars.SplitList) == 0 {
		Env.SanitizedAuditLogContextVars.SplitList = append(Env.SanitizedAuditLogContextVars.SplitList, SanitizedAuditLogContextVars...)
	}
	Env.PgDatabaseURI = stripPostgresPrefix(Env.PgDatabaseURI)
	Env.PgbouncerDatabaseURI = stripPostgresPrefix(Env.PgbouncerDatabaseURI)
}

func init() {
	LoadEnv()
}

var headerToLogAttr = map[string]string{
	"X-Cloud-Trace-Context": "lb_trace_context",
}

type CtxKey string

const (
	LbTraceCtxKey  CtxKey = "lb_trace_context"
	UserIDCtxKey   CtxKey = "user_id"
	LsUserIDCtxKey CtxKey = "ls_user_id"
	OrgIDCtxKey    CtxKey = "organization_id"
	TenantIDCtxKey CtxKey = "tenant_id"
	// this is the shortend api key, not the full key, because sensitive
	ApiKeyCtxKey CtxKey = "api_key_short"
	// this defines the auditable operation
	OperationCtxKey CtxKey = "audit_operation_name"
)

var AuditLogContextVars = []CtxKey{
	UserIDCtxKey,
	LsUserIDCtxKey,
	OrgIDCtxKey,
	TenantIDCtxKey,
	ApiKeyCtxKey,
	OperationCtxKey,
}

var SanitizedAuditLogContextVars = []CtxKey{
	ApiKeyCtxKey,
	LbTraceCtxKey,
}

func LogField(ctx context.Context, msg string, attrs ...any) {
	// log to the logger in the context
	if entry, ok := ctx.Value(middleware.LogEntryCtxKey).(*httplog.RequestLoggerEntry); ok {
		entry.Logger.Log(ctx, slog.LevelInfo, msg, attrs...)
	}
}

func LogAndContextSetField(ctx context.Context, key string, value slog.Value) context.Context {
	// Modified from https://github.com/go-chi/httplog/blob/master/httplog.go#L365
	// to fix pointer assignment, fixed in https://github.com/go-chi/httplog/pull/48
	if entry, ok := ctx.Value(middleware.LogEntryCtxKey).(*httplog.RequestLoggerEntry); ok {
		*entry.Logger = *entry.Logger.With(slog.Attr{Key: key, Value: value})
		if slices.Contains(AuditLogContextVars, CtxKey(key)) {
			ctx = context.WithValue(ctx, CtxKey(key), value.String())
		}
	}

	// Set field on active datadog span if one exists
	if span, ok := tracer.SpanFromContext(ctx); ok {
		span.SetTag(key, value.String())
	}

	return ctx
}

func HeaderLogMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		for headerName, logAttr := range headerToLogAttr {
			if val := r.Header.Get(headerName); val != "" {
				LogAndContextSetField(ctx, logAttr, slog.StringValue(val))
				ctx = context.WithValue(ctx, LbTraceCtxKey, val)
			}
		}
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func HeaderSpanMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		span, ok := tracer.SpanFromContext(r.Context())
		for headerName, logAttr := range headerToLogAttr {
			if val := r.Header.Get(headerName); val != "" {
				if ok {
					span.SetTag(logAttr, val)
				}
			}
		}
		next.ServeHTTP(w, r)
	})
}
