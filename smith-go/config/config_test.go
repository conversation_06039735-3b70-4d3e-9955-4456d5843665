package config

import (
	"github.com/stretchr/testify/require"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestListUnmarshalEnvironmentValue(t *testing.T) {
	assert.Equal(t, []string{"email", "profile", "openid"}, Env.OauthScopes.SplitList)
	assert.Equal(t, []string{"/ok", "/health"}, Env.HttpLogQuietRoutes.SplitList)
	assert.Equal(t, []string{"/ok", "/health"}, Env.DatadogIgnoreRoutes.SplitList)
}

func TestStripPostgresURL(t *testing.T) {
	// 1. Give the process the values you want LoadEnv() to read.
	// t.Setenv was added in Go 1.17; it automatically cleans up.
	t.Setenv("POSTGRES_DATABASE_URI", "************************************/dbname")
	t.Setenv("PGBOUNCER_DATABASE_URI", "************************************/dbname")

	// 2. Re-load the configuration.
	LoadEnv() // returns a fresh struct, no globals needed

	// 3. Assert on the stripped result.
	// stripPostgresPrefix has collapsed “postgresql://” → “postgres://”.
	require.Equal(t, "user:password@host:5432/dbname", Env.PgDatabaseURI)
	require.Equal(t, "user:password@host:6432/dbname", Env.PgbouncerDatabaseURI)

	// Test postgres:// as well
	t.Setenv("POSTGRES_DATABASE_URI", "**********************************/dbname")
	t.Setenv("PGBOUNCER_DATABASE_URI", "**********************************/dbname")

	// 2. Re-load the configuration.
	LoadEnv() // returns a fresh struct, no globals needed

	// 3. Assert on the stripped result.
	// stripPostgresPrefix has collapsed “postgresql://” → “postgres://”.
	require.Equal(t, "user:password@host:5432/dbname", Env.PgDatabaseURI)
	require.Equal(t, "user:password@host:6432/dbname", Env.PgbouncerDatabaseURI)
}
