<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smith-Go Benchmark Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .benchmark-section {
            margin-bottom: 40px;
        }
        .benchmark-title {
            font-size: 1.5em;
            color: #444;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 30px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        .trend-up {
            color: #dc3545;
        }
        .trend-down {
            color: #28a745;
        }
        .trend-stable {
            color: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .last-updated {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Smith-Go Performance Benchmarks</h1>
        
        <div id="loading" class="loading">
            Loading benchmark data...
        </div>
        
        <div id="error" class="error" style="display: none;">
            Failed to load benchmark data. Please check if the benchmark results are available.
        </div>
        
        <div id="dashboard" style="display: none;">
            <!-- Storage Benchmarks -->
            <div class="benchmark-section">
                <h2 class="benchmark-title">Storage Uploader Performance</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="storage-time">-</div>
                        <div class="metric-label">Avg Time (ns/op)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="storage-memory">-</div>
                        <div class="metric-label">Memory (B/op)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="storage-allocs">-</div>
                        <div class="metric-label">Allocations</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="storageChart"></canvas>
                </div>
            </div>
            
            <!-- Compression Benchmarks -->
            <div class="benchmark-section">
                <h2 class="benchmark-title">Compression Performance</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="compression-time">-</div>
                        <div class="metric-label">Avg Time (ns/op)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="compression-memory">-</div>
                        <div class="metric-label">Memory (B/op)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="compression-ratio">-</div>
                        <div class="metric-label">Compression Ratio</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="compressionChart"></canvas>
                </div>
            </div>
            
            <!-- Ingestion Benchmarks -->
            <div class="benchmark-section">
                <h2 class="benchmark-title">Ingestion Performance</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="ingestion-throughput">-</div>
                        <div class="metric-label">Throughput (ops/sec)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="ingestion-latency">-</div>
                        <div class="metric-label">Latency (ms)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="ingestion-memory">-</div>
                        <div class="metric-label">Memory (B/op)</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="ingestionChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="last-updated" id="lastUpdated">
            Last updated: -
        </div>
    </div>

    <script>
        // Configuration
        const BENCHMARK_DATA_URL = './dev/bench/data.js';
        
        // Chart configurations
        const chartConfig = {
            type: 'line',
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day'
                        }
                    },
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Performance Trends'
                    }
                }
            }
        };
        
        // Initialize charts
        let storageChart, compressionChart, ingestionChart;
        
        function initCharts() {
            const storageCtx = document.getElementById('storageChart').getContext('2d');
            const compressionCtx = document.getElementById('compressionChart').getContext('2d');
            const ingestionCtx = document.getElementById('ingestionChart').getContext('2d');
            
            storageChart = new Chart(storageCtx, {
                ...chartConfig,
                data: {
                    datasets: [{
                        label: 'Upload Time (ns/op)',
                        borderColor: 'rgb(75, 192, 192)',
                        data: []
                    }]
                }
            });
            
            compressionChart = new Chart(compressionCtx, {
                ...chartConfig,
                data: {
                    datasets: [{
                        label: 'Compression Time (ns/op)',
                        borderColor: 'rgb(255, 99, 132)',
                        data: []
                    }]
                }
            });
            
            ingestionChart = new Chart(ingestionCtx, {
                ...chartConfig,
                data: {
                    datasets: [{
                        label: 'Ingestion Time (ns/op)',
                        borderColor: 'rgb(54, 162, 235)',
                        data: []
                    }]
                }
            });
        }
        
        function formatNumber(num) {
            if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
            if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
            if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
            return num.toFixed(0);
        }
        
        function updateMetrics(data) {
            // Update storage metrics
            if (data.storage) {
                document.getElementById('storage-time').textContent = formatNumber(data.storage.time);
                document.getElementById('storage-memory').textContent = formatNumber(data.storage.memory);
                document.getElementById('storage-allocs').textContent = formatNumber(data.storage.allocs);
            }
            
            // Update compression metrics
            if (data.compression) {
                document.getElementById('compression-time').textContent = formatNumber(data.compression.time);
                document.getElementById('compression-memory').textContent = formatNumber(data.compression.memory);
                document.getElementById('compression-ratio').textContent = data.compression.ratio + 'x';
            }
            
            // Update ingestion metrics
            if (data.ingestion) {
                document.getElementById('ingestion-throughput').textContent = formatNumber(data.ingestion.throughput);
                document.getElementById('ingestion-latency').textContent = formatNumber(data.ingestion.latency);
                document.getElementById('ingestion-memory').textContent = formatNumber(data.ingestion.memory);
            }
        }
        
        function updateCharts(data) {
            // Update storage chart
            if (data.storage && data.storage.history) {
                storageChart.data.datasets[0].data = data.storage.history.map(point => ({
                    x: point.date,
                    y: point.time
                }));
                storageChart.update();
            }
            
            // Update compression chart
            if (data.compression && data.compression.history) {
                compressionChart.data.datasets[0].data = data.compression.history.map(point => ({
                    x: point.date,
                    y: point.time
                }));
                compressionChart.update();
            }
            
            // Update ingestion chart
            if (data.ingestion && data.ingestion.history) {
                ingestionChart.data.datasets[0].data = data.ingestion.history.map(point => ({
                    x: point.date,
                    y: point.time
                }));
                ingestionChart.update();
            }
        }
        
        async function loadBenchmarkData() {
            try {
                // Try to load from GitHub Pages benchmark action data
                const response = await fetch(BENCHMARK_DATA_URL);
                if (!response.ok) {
                    throw new Error('Failed to fetch benchmark data');
                }
                
                const data = await response.json();
                
                // Hide loading, show dashboard
                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard').style.display = 'block';
                
                // Update metrics and charts
                updateMetrics(data);
                updateCharts(data);
                
                // Update last updated time
                document.getElementById('lastUpdated').textContent = 
                    `Last updated: ${new Date(data.lastUpdated || Date.now()).toLocaleString()}`;
                
            } catch (error) {
                console.error('Error loading benchmark data:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
            }
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadBenchmarkData();
            
            // Refresh data every 5 minutes
            setInterval(loadBenchmarkData, 5 * 60 * 1000);
        });
    </script>
</body>
</html>
