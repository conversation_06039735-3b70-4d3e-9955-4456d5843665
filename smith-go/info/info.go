package info

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/license"
)

type InfoHandler struct {
	redis redis.UniversalClient
}

func NewInfoHandler(redis redis.UniversalClient) *InfoHandler {
	return &InfoHandler{
		redis: redis,
	}
}

type BatchIngestConfig struct {
	UseMultipartEndpoint   bool `json:"use_multipart_endpoint"`
	ScaleUpQsizeTrigger    int  `json:"scale_up_qsize_trigger"`
	ScaleUpNthreadsLimit   int  `json:"scale_up_nthreads_limit"`
	ScaleDownNemptyTrigger int  `json:"scale_down_nempty_trigger"`
	SizeLimit              int  `json:"size_limit"`
	SizeLimitBytes         int  `json:"size_limit_bytes"`
}

// InfoGetResponse corresponds to the JSON response in <PERSON>'s schemas.InfoGetResponse
// This should only include instance-wide information, and is cached globally in cloud.
type InfoGetResponse struct {
	Version               string                 `json:"version"`
	LicenseExpirationTime *time.Time             `json:"license_expiration_time,omitempty"`
	InstanceFlags         map[string]interface{} `json:"instance_flags"`
	BatchIngestConfig     BatchIngestConfig      `json:"batch_ingest_config"`
}

func (h *InfoHandler) GetServerInfoHandler(w http.ResponseWriter, r *http.Request) {
	var expirationTimestampPointer *time.Time
	// Don't show license expiry in SaaS envs
	if strings.Contains(config.Env.LangchainEnv, "local") {
		licenseJWT, err := license.GetOrRefreshLicense(r.Context(), config.Env.LangSmithLicenseKey, h.redis)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		claims, err := license.GetSigningKeyAndDecodeLicenseJWT(licenseJWT)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		expirationTimestampFloat := claims["exp"].(float64)
		expirationTimestamp := time.Unix(int64(expirationTimestampFloat), 0).UTC()
		expirationTimestampPointer = &expirationTimestamp
	}
	traceTierDurationDays := make(map[string]int64)
	for k, v := range config.Env.TraceTierTtlDurationSecMap {
		traceTierDurationDays[k] = v / (60 * 60 * 24)
	}

	// Example feature flags (you'll need to set these from env or config)
	instanceFlags := map[string]interface{}{
		"generate_ai_query_enabled":          true,
		"payment_enabled":                    auth.IsPaymentEnabled(),
		"org_creation_disabled":              config.Env.FFOrgCreationDisabled,
		"personal_orgs_disabled":             config.Env.FFPersonalOrgsDisabled,
		"show_ttl_ui":                        config.Env.FFTraceTiersEnabled,
		"trace_tier_duration_days":           traceTierDurationDays,
		"search_enabled":                     config.Env.FFCHSearchEnabled,
		"workspace_scope_org_invites":        config.Env.FFWorkspaceScopeOrgInvitesEnabled,
		"s3_storage_enabled":                 config.Env.BlobStorageEnabled,
		"blob_storage_enabled":               config.Env.BlobStorageEnabled,
		"blob_storage_engine":                config.Env.BlobStorageEngine,
		"examples_multipart_enabled":         true,
		"dataset_examples_multipart_enabled": true,
		"zstd_compression_enabled":           true,
		"experimental_search_enabled":        config.Env.ExperimentalSearchEnabled,
		"playground_auth_bypass_enabled":     config.Env.PlaygroundBypassAuthEnabled,
	}

	batchIngestConfig := BatchIngestConfig{
		UseMultipartEndpoint:   config.Env.BatchIngestUseMultipartEndpoint,
		ScaleUpQsizeTrigger:    config.Env.BatchIngestScaleUpQsizeTrigger,
		ScaleUpNthreadsLimit:   config.Env.BatchIngestScaleUpNthreadsLimit,
		ScaleDownNemptyTrigger: config.Env.BatchIngestScaleDownNemptyTrigger,
		SizeLimit:              config.Env.BatchIngestSizeLimit,
		SizeLimitBytes:         config.Env.BatchIngestSizeLimitBytes,
	}

	resp := InfoGetResponse{
		Version:               license.Version,
		LicenseExpirationTime: expirationTimestampPointer,
		InstanceFlags:         instanceFlags,
		BatchIngestConfig:     batchIngestConfig,
	}

	w.Header().Set("Content-Type", "application/json")
	if config.Env.InfoCacheMaxAgeSeconds > 0 {
		w.Header().Set("Cache-Control", fmt.Sprintf("public, max-age=%d", config.Env.InfoCacheMaxAgeSeconds))
	}
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
}
