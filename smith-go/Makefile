.PHONY: tests-setup

test:
	$(MAKE) -C ../smith-backend tests-setup
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./... -vet=off -race -v -count=1 -p=1 -run=$(TEST)

test-redis-cluster:
	$(MAKE) -C ../smith-backend tests-setup
	set -a && . ../.env.local_redis_cluster && LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./... -vet=off -race -v -count=1 -p=1 -run=$(TEST)

test-redis-sharded:
	$(MAKE) -C ../smith-backend tests-setup
	set -a && . ../.env.local_test_sharded_redis && LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./... -vet=off -race -v -count=1 -p=1 -run=$(TEST)

test-dir:
	$(MAKE) -C ../smith-backend tests-setup
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./$(DIR) -vet=off -race -v -count=1 -p=1 -run=$(TEST)

test-watch:
	$(MAKE) -C ../smith-backend tests-setup
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 gow test ./... -vet=all -p=1 -run=$(TEST)

start:
	LANGCHAIN_ENV=local_dev AUTH_TYPE=none POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/postgres go run main.go

start-auth:
	LANGCHAIN_ENV=local_dev AUTH_TYPE=supabase POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/postgres go run main.go

start-auth-watch:
	LANGCHAIN_ENV=local_dev AUTH_TYPE=supabase POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/postgres gow run main.go

start-auth-test:
	LANGCHAIN_ENV=local_test AUTH_TYPE=supabase LOG_LEVEL=info go run main.go

start-auth-watch-test:
	LANGCHAIN_ENV=local_test AUTH_TYPE=supabase LOG_LEVEL=info gow run main.go

start-basic-auth:
	set -a && source ../.env.local_basic && LANGCHAIN_ENV=local_dev POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/postgres go run main.go

start-oauth:
	LANGCHAIN_ENV=local_dev AUTH_TYPE=oauth POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/postgres go run main.go

start-google-oauth:
	set -a && source ../.env.local_google_oauth && LANGCHAIN_ENV=local_dev AUTH_TYPE=mixed POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/postgres go run main.go

update-api-docs:
	swag init
	cp docs/swagger.json ../smith-backend/static/smith-go-swagger.json

check-api-docs:
	swag init
	@echo "Checking if API docs are up to date..."
	@diff -q docs/swagger.json ../smith-backend/static/smith-go-swagger.json || (echo "Error: API docs are out of sync. Please run 'make update-api-docs' to update them." && exit 1)
	@echo "Docs check passed..."

bench:
	$(MAKE) -C ../smith-backend tests-setup
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./... -vet=off -run=^$ -bench=. -benchmem

bench-dir:
	$(MAKE) -C ../smith-backend tests-setup
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./$(DIR) -vet=off -bench=. -benchmem -benchtime=100x

bench-storage:
	$(MAKE) -C ../smith-backend tests-setup
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./storage/... -vet=off -run=^$ -bench=. -benchmem -benchtime=10x -count=6

bench-storage-compare:
	$(MAKE) -C ../smith-backend tests-setup
	@echo "Running benchmarks on current branch..."
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./storage/... -vet=off -run=^$ -bench=. -benchmem -benchtime=10x -count=5 > current-bench.txt
	@echo "Switching to main branch..."
	git stash push -m "benchmark-temp" || true
	git checkout main
	@echo "Running benchmarks on main branch..."
	LANGCHAIN_ENV=local_test POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test REDIS_DATABASE_URI=redis://localhost:6379/1 go test ./storage/... -vet=off -run=^$ -bench=. -benchmem -benchtime=10x -count=5 > main-bench.txt
	@echo "Switching back to original branch..."
	git checkout -
	git stash pop || true
	@echo "Comparing benchmarks..."
	@command -v benchcmp >/dev/null 2>&1 || { echo "Installing benchcmp..."; go install golang.org/x/tools/cmd/benchcmp@latest; }
	@echo "=== Benchmark Comparison (main -> current) ==="
	@benchcmp main-bench.txt current-bench.txt || echo "No significant differences found"

bench-analysis:
	./scripts/benchmark-analysis.sh

bench-clean:
	./scripts/benchmark-analysis.sh --clean

bench-help:
	@echo "Available benchmark targets:"
	@echo "  bench                 - Run all benchmarks"
	@echo "  bench-dir DIR=<dir>   - Run benchmarks for specific directory"
	@echo "  bench-storage         - Run storage benchmarks with multiple iterations"
	@echo "  bench-storage-compare - Compare storage benchmarks against main branch"
	@echo "  bench-analysis        - Run advanced benchmark analysis"
	@echo "  bench-clean          - Clean up benchmark result files"
	@echo "  bench-help           - Show this help message"
	@echo ""
	@echo "For more options, use: ./scripts/benchmark-analysis.sh --help"
