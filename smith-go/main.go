package main

import (
	"context"
	"fmt"
	"os/signal"
	"regexp"
	"slices"
	"syscall"

	// https://github.com/go-chi/chi/issues/683 must use v5
	"log/slog"
	"net/http"
	"os"
	"time"

	chitrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/go-chi/chi.v5"

	"langchain.com/smith/alerts"
	"langchain.com/smith/feedback"
	"langchain.com/smith/health"
	"langchain.com/smith/info"
	"langchain.com/smith/license"
	"langchain.com/smith/otel"
	"langchain.com/smith/queue"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"

	"langchain.com/smith/beacon"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/DataDog/datadog-go/v5/statsd"
	"github.com/ggicci/httpin"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/go-chi/httplog/v2"
	"github.com/gorilla/sessions"
	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/openidConnect"
	"github.com/rbcervilla/redisstore/v9"
	"github.com/redis/go-redis/v9"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
	httptrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/net/http"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"gopkg.in/DataDog/dd-trace-go.v1/profiler"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/examples"
	"langchain.com/smith/marketplace"
	"langchain.com/smith/metronome"
	"langchain.com/smith/prompt_canvas"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/runs"
	"langchain.com/smith/storage"
)

const verifyInterval = 5 * time.Minute // Adjust as needed

func ExamplesDeprecationMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Warning", `199 - "This route is deprecated and will be removed in future API versions. Please use the /v1/platform/datasets/{dataset_id}/examples endpoint instead."`)
		next.ServeHTTP(w, r)
	})
}

func Handler(
	dbpool *database.AuditLoggedPool,
	cachingRedisPool redis.UniversalClient,
	routedRedisPools lsredis.RoutedRedisPools,
	storageClient storage.StorageClient,
	mcm *auth.MetronomeConfigMiddleware,
	chConn clickhouse.Conn,
	statsdClient *statsd.Client,
) http.Handler {
	// pick appropriate auth handler
	var ah auth.Handler
	if config.Env.AuthType == "none" {
		ah = auth.NewNone(dbpool, cachingRedisPool, config.Env.AuthCacheTTLSec)
	} else if config.Env.AuthType == "supabase" {
		ah = auth.NewSupabase(dbpool, cachingRedisPool, config.Env.AuthCacheTTLSec)
	} else if config.Env.AuthType == "oauth" {
		ah = auth.NewOAuth(dbpool, cachingRedisPool, config.Env.OAuthIssuerUrl, config.Env.AuthCacheTTLSec)
	} else if config.Env.AuthType == "mixed" && config.Env.BasicAuthEnabled {
		ah = auth.NewBasicAuth(dbpool, cachingRedisPool, config.Env.AuthCacheTTLSec)
	} else if config.Env.AuthType == "mixed" && config.Env.OAuthClientId != "" && config.Env.OAuthClientSecret != "" {
		p, err := openidConnect.NewNamed("custom", config.Env.OAuthClientId, config.Env.OAuthClientSecret, config.Env.OAuthCallbackUrl, config.Env.OAuthIssuerUrl+"/.well-known/openid-configuration", config.Env.OauthScopes.SplitList...)
		if err != nil {
			fmt.Printf("Error initializing openid connect provider: %v\n", err)
			panic("failed to initialize openid connect provider")
		}
		goth.UseProviders(p)
		store, err := redisstore.NewRedisStore(context.Background(), cachingRedisPool)
		if err != nil {
			panic(fmt.Sprintf("failed to create redis store for session state: %v", err))
		}
		store.Options(sessions.Options{
			Path:     "/",
			MaxAge:   3600, // 1 hour since this is only used for state verification
			HttpOnly: true,
			Secure:   true,
			SameSite: http.SameSiteLaxMode,
		})
		gothic.Store = store
		// maximum session length is 1 day. Must be refreshed before then
		ah = auth.NewOAuthSessioned(dbpool, cachingRedisPool, config.Env.AuthCacheTTLSec, min(config.Env.OAuthSessionMaxSec, 86400*7))
	} else {
		panic("unsupported auth type or auth configuration")
	}

	logger := httplog.NewLogger(config.Env.ServiceName, httplog.Options{
		JSON:           config.Env.DatadogEnabled,
		LogLevel:       slog.Level(config.Env.SlogLevel),
		Concise:        !config.Env.DatadogEnabled,
		RequestHeaders: true,
		HideRequestHeaders: []string{
			"x-api-key",
			"x-service-key",
		},
		Trace: &httplog.TraceOptions{
			HeaderTrace:   "x-datadog-trace-id",
			LogFieldTrace: "trace_id",
			LogFieldSpan:  "span_id",
		},
		ResponseHeaders:  true,
		MessageFieldName: "message",
		QuietDownRoutes:  config.Env.HttpLogQuietRoutes.SplitList,
		QuietDownPeriod:  time.Duration(config.Env.HttpLogQuietPeriodSec) * time.Second,
	})

	logger.Warn("quiet down routes", "routes", config.Env.HttpLogQuietRoutes.SplitList)
	logger.Warn("oauth scopes", "scopes", config.Env.OauthScopes.SplitList)

	rh := runs.NewRunObjectsHandler(storageClient)
	registryHandler := marketplace.NewRegistryEndpointHandler(dbpool)
	runsHandler := runs.NewRunHandler(dbpool, cachingRedisPool, routedRedisPools)
	batchHandler := runs.NewBatchRunHandler(dbpool, cachingRedisPool, routedRedisPools, storageClient)
	multipartHandler := runs.NewMultipartRunHandler(
		dbpool,
		cachingRedisPool,
		routedRedisPools,
		storageClient,
		chConn,
	)
	otelHandler := otel.NewOTELHandler(dbpool, cachingRedisPool, routedRedisPools, storageClient)
	prefetchHandler := runs.NewRunPrefetchHandler(cachingRedisPool, &chConn, statsdClient)
	alertsAggregationHandler := alerts.NewAlertsAggregationHandler(dbpool, routedRedisPools, cachingRedisPool)
	usageHandler := auth.NewUsageHandler()
	beaconHandler := beacon.NewBeaconHandler(dbpool)
	feedbackBatchHandler := feedback.NewBatchFeedbackHandler(dbpool, routedRedisPools, chConn)
	runsQueryHandler := runs.NewRunQueryHandler(&chConn)

	r := chi.NewRouter()

	// shared middleware
	if config.Env.DatadogEnabled {
		opts := chitrace.WithIgnoreRequest(
			func(r *http.Request) bool {
				return slices.Contains(config.Env.DatadogIgnoreRoutes.SplitList, r.URL.Path)
			},
		)
		r.Use(chitrace.Middleware(opts))
		r.Use(config.HeaderSpanMiddleware)
	}
	r.Use(middleware.Heartbeat("/ok"))
	r.Use(httplog.RequestLogger(logger))
	r.Use(config.HeaderLogMiddleware)
	r.Use(middleware.NoCache)
	corsOpts := cors.Options{
		AllowedMethods:   []string{"GET", "HEAD", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
		MaxAge:           86400,
	}
	if config.Env.CORSAllowedOriginsRegex != "" {
		regex := regexp.MustCompile(config.Env.CORSAllowedOriginsRegex)
		corsOpts.AllowOriginFunc = func(r *http.Request, origin string) bool {
			return regex.MatchString(origin)
		}
	} else {
		corsOpts.AllowedOrigins = config.Env.CORSAllowedOrigins.SplitList
	}
	r.Use(cors.Handler(corsOpts))

	r.Get("/health", health.HealthHandler)
	if config.Env.CPUHealthEnabled {
		health.StartCPUMonitoring()
	}

	// tenant-level authenticated routes
	r.Group(func(r chi.Router) {
		r.Use(ah.Middleware)
		r.Use(middleware.Timeout(60 * time.Second))

		if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
			r.Use(mcm.WithAllowNoPlans())
		}

		r.Get("/auth", auth.FetchAuth)
		r.Get("/auth/public", auth.FetchPublicAuth)

		eh := examples.FetchExamplesHandler{Pg: dbpool}
		r.With(
			lsredis.HandlerWithKey(cachingRedisPool, 60*time.Second, ah.CacheKey, "examples"),
			httpin.NewInput(examples.ListExamplesReq{}),
		).Get("/examples/latest", eh.ListExamples)
		r.Post("/registry/{projectID}/ratings", registryHandler.CreateMarketplaceRatings)
	})

	// org-level authenticated routes
	r.Group(func(r chi.Router) {
		r.Use(ah.OrgMiddleware)
		r.Use(middleware.Timeout(60 * time.Second))

		if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
			r.Use(mcm.WithAllowNoPlansOrg())
		}

		r.Get("/orgs/auth", auth.FetchOrgAuth)
	})

	// Unauthenticated group
	r.Group(func(r chi.Router) {
		// attach unauthenticated routes
		r.Use(middleware.Timeout(60 * time.Second))

		// langgraph platform metadata
		r.With(
			httpin.NewInput(beacon.SubmitMetadataRequest{}),
		).Post("/v1/metadata/submit", beaconHandler.SubmitMetadata)

		r.Head("/public/download", rh.DownloadObjectPublic)
		r.Get("/public/download", rh.DownloadObjectPublic)

		// Get tenants for current user
		r.Get("/current/tenants", ah.GetTenantlessAuth)
		r.Get("/auth/verify", ah.GetTenantlessAuth)

		// Basic auth login
		if basicAuthHandler, ok := ah.(*auth.HandlerBasicAuth); ok {
			r.Post("/token", basicAuthHandler.Login)
		}

		// OAuth
		if oauthHandlerSessioned, ok := ah.(*auth.HandlerOAuthSessioned); ok {
			r.Get("/oauth/{provider}", oauthHandlerSessioned.Auth)
			r.Get("/oauth/{provider}/logout", oauthHandlerSessioned.Logout)
			r.Get("/oauth/{provider}/callback", oauthHandlerSessioned.Callback)
			r.Get("/oauth/{provider}/current-user", oauthHandlerSessioned.CurrentUser)
		}

		// Marketplace registry search
		r.Get("/registry", registryHandler.Search)
		r.Get("/registry/{projectID}", registryHandler.Search)

		// Info
		infoHandler := info.NewInfoHandler(cachingRedisPool)
		r.Get("/info", infoHandler.GetServerInfoHandler)
	})

	// Marketplace gateway endpoints
	r.Group(func(r chi.Router) {
		r.Use(ah.OrgMiddleware)

		if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
			r.Use(mcm.OverrideOrgAuthConfig)
		}
		marketplaceHandler := marketplace.NewMarketplaceProxyEndpointHandler(dbpool)
		// TODO: Figure out sensible timeout
		r.Use(middleware.Timeout(600 * time.Second))
		r.Get("/marketplace/*", marketplaceHandler.ProxyRequest)
		r.Post("/marketplace/*", marketplaceHandler.ProxyRequest)
		r.Put("/marketplace/*", marketplaceHandler.ProxyRequest)
		r.Patch("/marketplace/*", marketplaceHandler.ProxyRequest)
	})

	// Internal group with long timeout
	r.Group(func(r chi.Router) {
		r.Use(middleware.Timeout(5 * time.Minute))
		r.Use(ah.XServiceKeyMiddleware)
		r.Post("/internal/upload", rh.UploadObject)
		r.With(
			httpin.NewInput(runs.DownloadObjectReq{}),
		).Head("/internal/download", rh.DownloadObjectInternal)
		r.With(
			httpin.NewInput(runs.DownloadObjectReq{}),
		).Get("/internal/download", rh.DownloadObjectInternal)
		r.With(
			httpin.NewInput(runs.CopyObjectRequest{}),
		).Post("/internal/copy", rh.CopyObjectInternal)
	})

	// Internal Auth group
	r.Group(func(r chi.Router) {
		r.Use(middleware.Timeout(60 * time.Second))
		r.Use(ah.XServiceKeyMiddleware)
		r.Get("/internal/auth", ah.FetchInternalAuth)
		r.Get("/internal/org-auth", ah.FetchInternalOrgAuth)
		r.Get("/internal/verify", func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})
	})

	r.Route("/v1/beacon", func(r chi.Router) {
		r.Use(middleware.Timeout(60 * time.Second))

		r.With(
			httpin.NewInput(beacon.VerifyRequest{}),
		).Post("/verify", beaconHandler.VerifyLicenseKey)
		r.With(httpin.NewInput(beacon.IngestTracesRequest{})).Post("/ingest-traces", beaconHandler.IngestTraces)
		r.With(httpin.NewInput(beacon.UsageRequest{})).Post("/usage", beaconHandler.GetUsage)
		r.With()
		r.With(ah.XServiceKeyMiddleware,
			httpin.NewInput(beacon.CreateLicenseRequest{}),
		).Post("/internal/create-license", beaconHandler.InternalCreateLicense)
		r.With(ah.XServiceKeyMiddleware,
			httpin.NewInput(beacon.CreateCustomerRequest{}),
		).Post("/internal/create-customer", beaconHandler.InternalCreateCustomer)
		// langgraph platform metadata
		r.With(
			httpin.NewInput(beacon.SubmitMetadataRequest{}),
		).Post("/metadata/submit", beaconHandler.SubmitMetadata)

	})

	r.Route("/v1/platform", func(r chi.Router) {
		r.Use(ah.Middleware)
		r.Use(middleware.Timeout(5 * time.Minute))
		r.Use(auth.VerifyAuthMiddleware)
		r.Use(auth.AuditLogContextMiddleware)

		if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
			r.Use(mcm.OverrideAuthConfig)
		}

		promptCanvasHandler := prompt_canvas.PromptCanvasHandler{Pg: dbpool}

		r.With(auth.RequirePermission(auth.PromptsRead)).Get("/prompt-canvas/quick-action", promptCanvasHandler.ListQuickActionsHandler)
		r.With(
			auth.RequirePermission(auth.PromptsRead),
			httpin.NewInput(prompt_canvas.CreateQuickActionRequest{}),
		).Post("/prompt-canvas/quick-action", promptCanvasHandler.CreateQuickActionHandler)
		r.With(
			auth.RequirePermission(auth.PromptsRead),
			httpin.NewInput(prompt_canvas.DeleteQuickActionRequest{}),
		).Delete("/prompt-canvas/quick-action", promptCanvasHandler.DeleteQuickActionHandler)
		r.With(
			auth.RequirePermission(auth.PromptsRead),
			httpin.NewInput(prompt_canvas.UpdateQuickActionRequest{}),
		).Patch("/prompt-canvas/quick-action", promptCanvasHandler.UpdateQuickActionHandler)

		examplesCrud := examples.NewExamplesCRUD(dbpool)
		emh := examples.MultipartExamplesHandler{
			Pg:             dbpool,
			StorageClient:  storageClient,
			ExamplesCRUD:   examplesCrud,
			ClickHousePool: chConn,
		}
		euh := examples.UploadExamplesHandler{
			Pg:           dbpool,
			ExamplesCRUD: examplesCrud,
		}
		r.With(auth.RequirePermission(auth.DatasetsUpdate)).Post("/datasets/{dataset_id}/examples/upload", euh.UploadExamplesFromJSONL)
		r.With(auth.RequirePermission(auth.DatasetsUpdate)).Post("/datasets/{dataset_id}/examples", emh.UploadDatasetExamples)
		r.With(auth.RequirePermission(auth.DatasetsUpdate)).Patch("/datasets/{dataset_id}/examples", emh.UpdateDatasetExamples)

		// Deprecated routes
		r.With(auth.RequirePermission(auth.DatasetsUpdate), ExamplesDeprecationMiddleware).Post("/examples/multipart", emh.UploadExamples)

		// alerts crud
		alertRulesCrud := alerts.NewAlertRulesCrudHandler(dbpool)
		r.With(auth.RequirePermission(auth.RunsRead)).Post("/alerts/{session_id}", alertRulesCrud.CreateAlertRuleHandler)
		r.With(auth.RequirePermission(auth.RunsRead)).Patch("/alerts/{session_id}/{alert_rule_id}", alertRulesCrud.UpdateAlertRuleHandler)
		r.With(auth.RequirePermission(auth.RunsRead)).Delete("/alerts/{session_id}/{alert_rule_id}", alertRulesCrud.DeleteAlertRuleHandler)
		r.With(auth.RequirePermission(auth.RunsRead)).Get("/alerts/{session_id}/{alert_rule_id}", alertRulesCrud.GetAlertRuleHandler)
		r.With(auth.RequirePermission(auth.RunsRead)).Get("/alerts/{session_id}", alertRulesCrud.ListAlertRulesBySessionIDHandler)
		r.With(auth.RequirePermission(auth.RunsRead)).Post("/alerts/{session_id}/test", alertsAggregationHandler.TestAlertAction)
	})

	r.Group(func(r chi.Router) {
		r.Use(ah.Middleware)
		r.Use(middleware.Timeout(60 * time.Second))
		r.Use(auth.VerifyAuthMiddleware)
		r.Post("/internal/aggregate-alerts", alertsAggregationHandler.AggregatedAlerts)
	})

	r.Group(func(r chi.Router) {
		r.Use(ah.OrgMiddleware)
		r.Use(middleware.Timeout(60 * time.Second))
		r.With(httpin.NewInput(metronome.UsageRequest{})).Get("/internal/org-usage", usageHandler.InternalGetUsage)
	})

	r.Route("/otel", func(r chi.Router) {
		r.Use(ah.Middleware)
		r.Use(middleware.Timeout(60 * time.Second))
		r.Use(auth.VerifyAuthMiddleware)

		if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
			r.Use(mcm.OverrideAuthConfig)
		}

		r.With(auth.RequirePermission(auth.RunsCreate)).Post("/v1/traces", otelHandler.PostTraces)
		r.With(auth.RequirePermission(auth.RunsCreate)).Post("/v1/metrics", otelHandler.PostMetrics)
	})

	// Runs group
	runsSubrouter := runsRouter(ah, mcm, runsHandler, batchHandler, multipartHandler)
	r.Mount("/runs", runsSubrouter)
	r.Mount("/v1/runs", runsSubrouter)
	r.Mount("/api/v1/runs", runsSubrouter)

	// prefetch traces
	r.Route("/traces", func(r chi.Router) {
		// auth and timeout middleware
		r.Use(ah.Middleware)
		r.Use(middleware.Timeout(60 * time.Second))
		r.Use(auth.VerifyAuthMiddleware)

		if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
			r.Use(mcm.OverrideAuthConfig)
		}

		r.With(auth.RequirePermission(auth.RunsRead)).Post("/prefetch", prefetchHandler.HandlePrefetchRuns)
		r.With(auth.RequirePermission(auth.RunsRead)).Get("/{trace_id}/runs", runsQueryHandler.SingleTraceQuery)
	})

	// feedback routes
	r.Route("/feedback", func(r chi.Router) {
		r.Use(ah.Middleware)
		r.Use(middleware.Timeout(60 * time.Second))
		r.Use(auth.VerifyAuthMiddleware)
		r.Use(auth.AuditLogContextMiddleware)

		if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
			r.Use(mcm.OverrideAuthConfig)
		}

		r.With(auth.RequirePermission(auth.FeedbackCreate)).Post("/batch", feedbackBatchHandler.IngestFeedbackBatch)
	})

	return r
}

func main() {
	var statsdClient *statsd.Client
	var statsdErr error
	if config.Env.DatadogEnabled {
		// initialize Datadog tracing
		tracer.Start()
		defer tracer.Stop()

		// Stop tracer on SIGTERM to avoid data loss
		// https://docs.datadoghq.com/tracing/trace_collection/library_config/go/
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGTERM)
		go func() {
			<-sigChan
			tracer.Stop()
		}()

		if os.Getenv("DD_DOGSTATSD_URL") != "" {
			statsdClient, statsdErr = statsd.New(os.Getenv("DD_DOGSTATSD_URL"))
			// initialize statsd client
			if statsdErr != nil {
				panic(statsdErr)
			}
			defer statsdClient.Close()
		}

	}

	// not nesting profiling in DatadogEnabled as we want to be able to enable them separately
	if config.Env.DatadogProfilingEnabled {
		// initialize Datadog continuous profiling
		err := profiler.Start(
			profiler.WithService(config.Env.ServiceName),
			profiler.WithSite(os.Getenv("DD_SITE")),
			// profiler.WithEnv(os.Getenv("DD_ENV")),
			// profiler.WithVersion(config.Env.Version), // TODO: Check that we get the DD_VERSION universal standard tag - set on the deployment
			// profiler.WithTags("<KEY1>:<VALUE1>", "<KEY2>:<VALUE2>"),
			profiler.WithProfileTypes(
				profiler.CPUProfile,
				profiler.HeapProfile,
				// The profiles below are disabled by default to keep overhead
				// low, but can be enabled as needed.
				// profiler.BlockProfile,
				// profiler.MutexProfile,
				// profiler.GoroutineProfile,
			),
		)
		if err != nil {
			panic(err)
		}
		defer profiler.Stop()
	}

	// initialize db connections
	dbpool := database.PgConnect()
	defer dbpool.Close()

	// initialize Redis Pools
	singleRedisPool := lsredis.SingleRedisConnect()
	defer func(redis redis.UniversalClient) {
		err := redis.Close()
		if err != nil {
			fmt.Printf("Error closing redis connections: %v\n", err)
		}
	}(singleRedisPool)

	var cachingRedisPool redis.UniversalClient
	if config.Env.RedisCachingDatabaseURI != "" {
		cachingRedisPool = lsredis.CachingRedisConnect()
		defer func(redis redis.UniversalClient) {
			err := redis.Close()
			if err != nil {
				fmt.Printf("Error closing redis connections: %v\n", err)
			}
		}(cachingRedisPool)

	} else {
		cachingRedisPool = singleRedisPool
	}

	var clusterRedisPool redis.UniversalClient
	if config.Env.RedisClusterEnabled {
		clusterRedisPool = lsredis.RedisClusterConnect()
		defer func(redis redis.UniversalClient) {
			err := redis.Close()
			if err != nil {
				fmt.Printf("Error closing redis connections: %v\n", err)
			}
		}(clusterRedisPool)
	}

	var shardedManager *lsredis.ShardedRedisManager
	if len(config.Env.RedisShardURIs) > 0 && config.Env.RedisShardingEnabled {
		shardedManager = lsredis.NewShardedManager()
		fmt.Println("Sharded Redis pools created")
	}

	var routedRedisPools = lsredis.RoutedRedisPools{
		SingleRedisPool:  &singleRedisPool,
		ClusterRedisPool: &clusterRedisPool,
		ShardManager:     shardedManager,
	}

	// initialize clickhouse client
	chConn, chError := database.ChConnect(true)
	if chError != nil {
		panic(chError)
	}
	defer func(ch clickhouse.Conn) {
		err := ch.Close()
		if err != nil {
			fmt.Printf("Error closing clickhouse connection: %v\n", err)
		}
	}(chConn)

	// initialize queue scripts
	queue.InitializeScripts(context.Background(), singleRedisPool)

	// initialize redis lock
	lsredis.InitRedisLock(singleRedisPool)

	// initialize metronome client
	metronome.NewMetronomeClient(cachingRedisPool)

	// initialize usage limits client
	usage_limits.NewUsageLimitsClient(dbpool, routedRedisPools, cachingRedisPool)

	// initialize feedback client
	feedback.NewFeedbackConfigClient(cachingRedisPool)

	// initialize tracer sessions client
	tracer_sessions.NewTracerSessionsClient(dbpool, cachingRedisPool)

	// Initialize the MetronomeConfigMiddleware under a feature flag
	var mcm *auth.MetronomeConfigMiddleware
	if config.Env.MetronomeMiddlewareEnabled {
		mcm = auth.NewMetronomeConfigMiddleware(
			dbpool,
			cachingRedisPool,
			singleRedisPool,
			config.Env.MetronomeConfigCacheExpirySec,
			false,
		)
	}

	// initialize storage client if enabled
	var storageClient storage.StorageClient
	var err error
	if config.Env.BlobStorageEnabled {
		if config.Env.BlobStorageEngine == "Azure" {
			storageClient, err = storage.NewAzureStorageClient(true, nil)
			if err != nil {
				panic(err)
			}
			err = storageClient.HealthCheck(context.Background(), &storage.HealthCheckInput{
				Buckets: storage.S3Buckets,
			})
			if err != nil {
				panic(err)
			}
		} else if config.Env.BlobStorageEngine == "S3" {
			storageClient, err = storage.NewS3StorageClient(true, nil)
			if err != nil {
				panic(err)
			}
			err = storageClient.HealthCheck(context.Background(), &storage.HealthCheckInput{
				Buckets: storage.S3Buckets,
			})
			if err != nil {
				panic(err)
			}
		} else {
			panic(fmt.Sprintf("unsupported BLOB_STORAGE_ENGINE: '%s'. must be either 'Azure' or 'S3'", config.Env.BlobStorageEngine))
		}
	}

	// initialize router
	router := Handler(
		dbpool,
		cachingRedisPool,
		routedRedisPools,
		storageClient,
		mcm,
		chConn,
		statsdClient,
	)

	if config.Env.DatadogEnabled {
		router = httptrace.WrapHandler(
			router,
			"go",
			"",
			httptrace.WithIgnoreRequest(func(r *http.Request) bool {
				return slices.Contains(config.Env.DatadogIgnoreRoutes.SplitList, r.URL.Path)
			}),
			httptrace.WithResourceNamer(func(r *http.Request) string {
				// TODO if we add any routes with path params, we need to update this
				return r.Method + " " + r.URL.Path
			}),
		)
	}
	// create h2c-capable handler
	dualHandler := h2c.NewHandler(router, &http2.Server{
		MaxConcurrentStreams: config.Env.Http2MaxConcurrentStreams,
		IdleTimeout:          time.Duration(config.Env.HttpIdleTimeoutSecs) * time.Second,
	})

	// Get the license key (from environment variable or configuration)
	licenseKey := os.Getenv("LANGSMITH_LICENSE_KEY")
	if licenseKey == "" {
		fmt.Println("LANGSMITH_LICENSE_KEY not set")
		os.Exit(1)
	}

	// Start the background license verification
	exitFunc := func(code int) {
		os.Exit(code)
	}
	ctx := context.Background()
	license.StartLicenseVerification(ctx, licenseKey, exitFunc, cachingRedisPool, verifyInterval)

	if config.Env.CPUHealthEnabled {
		defer health.StopCPUMonitoring()
	}

	// start server
	httplog.LogEntry(ctx).Info("Starting server", "port", config.Env.Port)
	server := &http.Server{Addr: fmt.Sprintf(":%v", config.Env.Port), Handler: dualHandler, IdleTimeout: time.Duration(config.Env.HttpIdleTimeoutSecs) * time.Second}
	err = server.ListenAndServe()
	if err != nil {
		panic(err)
	}
}

func runsRouter(
	ah auth.Handler,
	mcm *auth.MetronomeConfigMiddleware,
	runsHandler *runs.RunHandler,
	batchHandler *runs.BatchRunHandler,
	multipartHandler *runs.MultipartRunHandler,
) chi.Router {
	r := chi.NewRouter()
	r.Use(ah.Middleware)
	r.Use(middleware.Timeout(60 * time.Second))
	r.Use(auth.VerifyAuthMiddleware)
	r.Use(auth.AuditLogContextMiddleware)

	if config.Env.MetronomeMiddlewareEnabled && mcm != nil {
		r.Use(mcm.OverrideAuthConfig)
	}

	r.Group(func(r chi.Router) {
		r.With(auth.RequirePermission(auth.RunsCreate)).Post("/", runsHandler.CreateRun)
		r.With(auth.RequirePermission(auth.RunsCreate)).Patch("/{run_id}", runsHandler.UpdateRun)
		r.With(auth.RequirePermission(auth.RunsCreate)).Post("/batch", batchHandler.IngestRunsBatch)
		r.With(auth.RequirePermission(auth.RunsCreate)).Post("/multipart", multipartHandler.IngestRunsMultipart)
	})

	return r
}
