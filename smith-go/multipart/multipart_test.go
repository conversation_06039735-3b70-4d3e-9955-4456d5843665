package runs_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/textproto"
	"reflect"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/DataDog/zstd"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/goleak"
	"langchain.com/smith/database"
	"langchain.com/smith/feedback"

	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/runs"
	"langchain.com/smith/storage"
	"langchain.com/smith/testutil"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
)

var (
	testDbPool       *database.AuditLoggedPool
	stgClient        storage.StorageClient
	routedRedisPools *lsredis.RoutedRedisPools
	cachingRedisPool redis.UniversalClient
)

// parseS3Url parses an S3 URL and returns the base path, hash components, and offset range
func parseS3Url(s3Url string) (basePath string, hash1 string, hash2 string, uuid string, startOffset int64, endOffset int64, err error) {
	// Split the URL into path and offset parts
	parts := strings.Split(s3Url, "#")
	if len(parts) != 2 {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid S3 URL format: missing offset range")
	}

	// Parse the path components
	pathParts := strings.Split(parts[0], "/")
	if len(pathParts) < 5 {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid S3 URL format: insufficient path components")
	}

	// Extract hash components and UUID
	hash1 = pathParts[2]
	hash2 = pathParts[3]
	uuid = pathParts[4]

	// Parse the offset range
	offsetParts := strings.Split(parts[1], "-")
	if len(offsetParts) != 2 {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid S3 URL format: invalid offset range")
	}

	startOffset, err = strconv.ParseInt(offsetParts[0], 10, 64)
	if err != nil {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid start offset: %v", err)
	}

	endOffset, err = strconv.ParseInt(offsetParts[1], 10, 64)
	if err != nil {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid end offset: %v", err)
	}

	// Reconstruct the base path without the offset
	basePath = parts[0]

	return basePath, hash1, hash2, uuid, startOffset, endOffset, nil
}

func TestMain(m *testing.M) {
	testDbPool = database.PgConnect()

	routedRedisPools, cachingRedisPool = testutil.InitTestRedisClients(&testing.T{})
	defer testutil.CleanupTestRoutedRedisPools(&testing.T{}, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(&testing.T{}, cachingRedisPool, true)

	usage_limits.NewUsageLimitsClient(testDbPool, *routedRedisPools, cachingRedisPool)

	tracer_sessions.NewTracerSessionsClient(testDbPool, cachingRedisPool)

	var err error
	stgClient, err = storage.NewS3StorageClient(false, nil)
	if err != nil {
		panic(fmt.Sprintf("Failed to init S3 storage client: %v", err))
	}

	m.Run()

	testDbPool.Exec(context.Background(), `DELETE FROM tenants;`)
}

func setupTestTenant(t *testing.T) auth.AuthInfo {
	orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.NewString())
	handle := fmt.Sprintf("test-tenant-%s", uuid.NewString())
	tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Test Tenant", handle, &auth.TenantConfig{}, true)

	return auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}
}

func newMultipartRequest() (*bytes.Buffer, *multipart.Writer) {
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	return &buf, writer
}

func finalizeRequest(t *testing.T, buf *bytes.Buffer, w *multipart.Writer) *http.Request {
	err := w.Close()
	require.NoError(t, err, "Failed to close multipart writer")

	req := httptest.NewRequest("POST", "/", buf)
	require.NoError(t, err, "Failed to create HTTP request")

	req.Header.Set("Content-Type", "multipart/form-data; boundary="+w.Boundary())
	return req
}

func addJSONPart(t *testing.T, w *multipart.Writer, partName string, data []byte) {
	hdr := textproto.MIMEHeader{}
	hdr.Set("Content-Type", fmt.Sprintf("application/json; length=%d", len(data)))
	hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, partName))

	part, err := w.CreatePart(hdr)
	require.NoError(t, err, "Failed to create JSON part")

	_, err = part.Write(data)
	require.NoError(t, err, "Failed to write JSON part data")
}

func addAttachmentPart(t *testing.T, w *multipart.Writer, partName string, data []byte) {
	hdr := textproto.MIMEHeader{}
	hdr.Set("Content-Type", fmt.Sprintf("application/octet-stream; length=%d", len(data)))
	hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, partName))

	part, err := w.CreatePart(hdr)
	require.NoError(t, err, "Failed to create attachment part")

	_, err = part.Write(data)
	require.NoError(t, err, "Failed to write attachment part data")
}

func TestParseMultipartForm_SimplePostSuccess(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, err := usage_limits.GetUsageLimitsClient()
	assert.NoError(t, err)
	tracerClient, err := tracer_sessions.GetTracerSessionsClient()
	assert.NoError(t, err)

	runID := uuid.NewString()
	postPayload := []byte(fmt.Sprintf(`{
		"id": %q,
		"name": "run1",
		"session_name": "sess1",
		"run_type": "llm"
	}`, runID))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.NoError(t, err)
	assert.NotNil(t, res)

	assert.Len(t, res.Posts, 1)
	assert.Equal(t, runID, *res.Posts[0].ID)
	assert.Equal(t, "run1", *res.Posts[0].Name)
	assert.Equal(t, "sess1", *res.Posts[0].SessionName)
	assert.Empty(t, res.Patches)
	assert.Empty(t, res.Feedback)
}

func TestParseMultipartForm_SimplePatchSuccess(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	runID := uuid.NewString()
	patchPayload := []byte(fmt.Sprintf(`{"id": %q, "error": "something"}`, runID))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "patch."+runID, patchPayload)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.NoError(t, err)

	assert.Len(t, res.Patches, 1)
	assert.Equal(t, runID, *res.Patches[0].ID)
	assert.Equal(t, `"something"`, string(res.Extras[runID]["error"].Data))
	assert.Empty(t, res.Posts)
	assert.Empty(t, res.Feedback)
}

func TestParseMultipartForm_FeedbackSuccess(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	runID := uuid.New()
	traceID := uuid.New()
	fbPayload := []byte(fmt.Sprintf(`{"id":"%s","trace_id":"%s","key":"my_key"}`, runID, traceID))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "feedback."+runID.String(), fbPayload)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.NoError(t, err)
	assert.NotNil(t, res)

	assert.Len(t, res.Feedback, 1)
	assert.Equal(t, runID, *res.Feedback[0].ID)
	assert.Equal(t, traceID, *res.Feedback[0].TraceID)
	assert.Equal(t, "my_key", res.Feedback[0].Key)
	assert.Empty(t, res.Posts)
	assert.Empty(t, res.Patches)
}

func TestParseMultipartForm_AttachmentLargeDataSuccess(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	runID := uuid.NewString()
	postPayload := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"large-att-test"}`, runID))

	attachmentData := bytes.Repeat([]byte("X"), 10*1024) // 10KB of X

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	addAttachmentPart(t, w, "attachment."+runID+".huge_field", attachmentData)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.NoError(t, err)
	assert.NotNil(t, res)

	assert.Len(t, res.Posts, 1)
	assert.Contains(t, res.Extras, runID)

	extras := res.Extras[runID]
	attKey := "attachment.huge_field"
	attVal, ok := extras[attKey]
	assert.True(t, ok, "Expected large attachment info in extras")
	assert.NotEmpty(t, attVal.Data)

	require.NotNil(t, stgClient, "Storage client should not be nil")
	bucket := storage.GetBucket(string(attVal.Data))
	output, err := stgClient.GetObject(context.Background(), &storage.GetObjectInput{
		Bucket: bucket,
		Key:    string(attVal.Data),
	})
	require.NoError(t, err)

	bufOut := new(bytes.Buffer)
	_, err = bufOut.ReadFrom(output.Body)
	require.NoError(t, err)
	require.Equal(t, string(attachmentData), bufOut.String())
}

func TestParseMultipartForm_RepeatedPartNames(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	runID := uuid.NewString()
	json1 := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"first"}`, runID))
	json2 := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"second"}`, runID))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, json1)
	addJSONPart(t, w, "post."+runID, json2) // same name repeated
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.NoError(t, err)
	assert.NotNil(t, res)

	assert.Len(t, res.Posts, 1)
	assert.Equal(t, "first", *res.Posts[0].SessionName)
}

func TestParseMultipartForm_UnknownEventName(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	runID := uuid.NewString()
	payload := []byte(fmt.Sprintf(`{"id": %q}`, runID))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "xyz."+runID, payload) // unknown event name
	req := finalizeRequest(t, buf, w)

	_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.Error(t, err)
	assert.True(t, strings.Contains(err.Error(), "invalid part name"))
}

func TestParseMultipartForm_MissingRunID(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	payload := []byte(`{"id":"should-be-non-empty-run-id"}`)

	// Create a form part with name="post."
	buf, w := newMultipartRequest()

	hdr := textproto.MIMEHeader{}
	hdr.Set("Content-Type", fmt.Sprintf("application/json; length=%d", len(payload)))
	hdr.Set("Content-Disposition", `form-data; name="post."`)
	part, err := w.CreatePart(hdr)
	require.NoError(t, err)
	_, err = part.Write(payload)
	require.NoError(t, err)

	req := finalizeRequest(t, buf, w)
	_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)

	assert.Error(t, err)
	assert.True(t, strings.Contains(err.Error(), "invalid part name"))
}

func TestParseMultipartForm_MissingContentType(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	runID := uuid.NewString()

	// Build the request the "hard" way to omit content-type.
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	hdr := textproto.MIMEHeader{}
	hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
	part, err := writer.CreatePart(hdr)
	assert.NoError(t, err)
	_, err = part.Write([]byte(fmt.Sprintf(`{"id": %q}`, runID)))
	assert.NoError(t, err)

	require.NoError(t, writer.Close())
	req := httptest.NewRequest("POST", "/", &buf)

	req.Header.Set("Content-Type", "multipart/form-data; boundary="+writer.Boundary())

	_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.Error(t, err)
	assert.True(t, strings.Contains(err.Error(), "missing Content-Type"))
}

func TestParseMultipartForm_AttachmentBlobStorageDisabled(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	oldVal := config.Env.BlobStorageEnabled
	config.Env.BlobStorageEnabled = false
	defer func() { config.Env.BlobStorageEnabled = oldVal }()

	oldMaxSize := config.Env.MinBlobStorageSizeKb
	config.Env.MinBlobStorageSizeKb = 0
	defer func() { config.Env.MinBlobStorageSizeKb = oldMaxSize }()

	runID := uuid.NewString()
	postPayload := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"large-att-test"}`, runID))
	attachmentData := []byte("small data")

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	addAttachmentPart(t, w, "attachment."+runID+".field1", attachmentData)
	req := finalizeRequest(t, buf, w)

	_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "blob storage is not enabled")
}

func TestParseMultipartForm_BlobStorageDisabled(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	oldVal := config.Env.BlobStorageEnabled
	config.Env.BlobStorageEnabled = false
	defer func() { config.Env.BlobStorageEnabled = oldVal }()

	runID := uuid.NewString()
	postPayload := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"large-att-test"}`, runID))
	inputData := []byte("small data")

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	addJSONPart(t, w, "post."+runID+".inputs", inputData)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.NoError(t, err)
	assert.NotNil(t, res)
	assert.Contains(t, res.Extras, runID)
	assert.Contains(t, res.Extras[runID], "inputs")
	assert.Equal(t, len(inputData), len(res.Extras[runID]["inputs"].Data))
	assert.Equal(t, string(inputData), string(res.Extras[runID]["inputs"].Data))
}

func TestParseMultipartForm_AttachmentExceedsMaxFileSize(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	oversize := 51 * 1024 * 1024
	runID := uuid.NewString()
	postPayload := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"large-att-test"}`, runID))
	attachmentData := make([]byte, oversize)

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	addAttachmentPart(t, w, "attachment."+runID+".big_field", attachmentData)
	req := finalizeRequest(t, buf, w)

	_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "exceeds limit")
}

func TestParseMultipartForm_UnsupportedContentEncoding(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	runID := uuid.NewString()
	postPayload := []byte(fmt.Sprintf(`{"id": %q}`, runID))

	// Build a request that claims "encoding=gzip"
	buf, w := newMultipartRequest()

	hdr := textproto.MIMEHeader{}
	hdr.Set("Content-Type", fmt.Sprintf(`application/json; length=%d; encoding="gzip"`, len(postPayload)))
	hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
	part, err := w.CreatePart(hdr)
	require.NoError(t, err)
	_, err = part.Write(postPayload)
	require.NoError(t, err)

	req := finalizeRequest(t, buf, w)

	_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	assert.Error(t, err)
}

func TestParseMultipartForm_MissingLines(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	t.Run("NamePartsTooShort", func(t *testing.T) {
		// if len(nameParts) < 2
		buf, w := newMultipartRequest()
		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", "application/json; length=10")
		hdr.Set("Content-Disposition", `form-data; name="post"`)
		part, err := w.CreatePart(hdr)
		require.NoError(t, err)
		_, err = part.Write([]byte(`{"id": "ignored"}`))
		require.NoError(t, err)

		req := finalizeRequest(t, buf, w)
		_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "need at least event.run_id")
	})

	t.Run("InvalidContentTypeParseError", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()
		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", "application@@json; length=20") // invalid
		hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		part, err := w.CreatePart(hdr)
		require.NoError(t, err)
		part.Write([]byte(fmt.Sprintf(`{"id":%q}`, runID)))

		req := finalizeRequest(t, buf, w)
		_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid Content-Type")
	})

	t.Run("InvalidContentLengthHeader", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()
		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", "application/json")
		hdr.Set("Content-Length", "NotANumber") // invalid
		hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		part, err := w.CreatePart(hdr)
		require.NoError(t, err)
		part.Write([]byte(fmt.Sprintf(`{"id":%q}`, runID)))

		req := finalizeRequest(t, buf, w)
		_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid Content-Length or length parameter")
	})

	t.Run("MissingLengthParamIfNoContentLengthHeader", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()
		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", "application/json") // no length param
		hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		part, err := w.CreatePart(hdr)
		require.NoError(t, err)
		part.Write([]byte(fmt.Sprintf(`{"id":%q}`, runID)))

		req := finalizeRequest(t, buf, w)
		_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "missing both Content-Length header and length param")
	})

	t.Run("FeedbackRunIDMismatch", func(t *testing.T) {
		goodID := uuid.New()
		badID := uuid.New()

		buf, w := newMultipartRequest()
		fbPayload := []byte(fmt.Sprintf(`{"id":"%s","trace_id":"%s","key":"some_key"}`, badID, uuid.New()))
		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", fmt.Sprintf("application/json; length=%d", len(fbPayload)))
		hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="feedback.%s"`, goodID))
		part, err := w.CreatePart(hdr)
		require.NoError(t, err)
		part.Write(fbPayload)

		req := finalizeRequest(t, buf, w)
		_, err = runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "run id mismatch in feedback")
	})

	t.Run("FeedbackMissingTraceID", func(t *testing.T) {
		runID := uuid.NewString()

		buf, w := newMultipartRequest()
		fbPayload := []byte(fmt.Sprintf(`{"id":"%s","key":"some_key"}`, runID))
		addJSONPart(t, w, "feedback."+runID, fbPayload)
		req := finalizeRequest(t, buf, w)

		_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "feedback part missing trace_id")
	})

	t.Run("OutOfBandData_UnknownRun", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()
		oobData := []byte(`{"foo": "bar"}`)

		addJSONPart(t, w, "post."+runID+".inputs", oobData) // but the actual run doesn't exist
		req := finalizeRequest(t, buf, w)

		res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.NoError(t, err, "Should not be a fatal error; it's ignored")
		assert.Equal(t, 0, len(res.Extras))
		assert.Equal(t, 0, len(res.Posts))
		assert.Equal(t, 0, len(res.Patches))
		assert.Equal(t, 0, len(res.Feedback))
	})

	t.Run("OutOfBandData_InvalidField", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()

		// Main part
		postPayload := []byte(fmt.Sprintf(`{"id": %q, "name": "run1", "run_type":"llm", "session_name":"someSess"}`, runID))
		addJSONPart(t, w, "post."+runID, postPayload)

		// Invalid field
		oobData := []byte("some data")
		addJSONPart(t, w, "post."+runID+".foobar", oobData)
		req := finalizeRequest(t, buf, w)

		_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid part name")
	})

	t.Run("OutOfBandData_SmallDataPath", func(t *testing.T) {
		oldMin := config.Env.MinBlobStorageSizeKb
		config.Env.MinBlobStorageSizeKb = 5 // 5 KB threshold
		defer func() { config.Env.MinBlobStorageSizeKb = oldMin }()

		runID := uuid.NewString()
		buf, w := newMultipartRequest()

		// Main part
		postPayload := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"mySession"}`, runID))
		addJSONPart(t, w, "post."+runID, postPayload)

		// 1KB of data
		smallOob := bytes.Repeat([]byte("X"), 1024)
		addJSONPart(t, w, "post."+runID+".inputs", smallOob)
		req := finalizeRequest(t, buf, w)

		res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.NoError(t, err)
		require.NotNil(t, res)

		assert.Contains(t, res.Extras, runID)
		assert.Contains(t, res.Extras[runID], "inputs")
		assert.Equal(t, len(smallOob), len(res.Extras[runID]["inputs"].Data))
		assert.Equal(t, string(smallOob), string(res.Extras[runID]["inputs"].Data))
	})

	t.Run("OutOfBandData_LargeDataPath", func(t *testing.T) {
		oldMin := config.Env.MinBlobStorageSizeKb
		config.Env.MinBlobStorageSizeKb = 1 // 1 KB threshold
		defer func() { config.Env.MinBlobStorageSizeKb = oldMin }()

		runID := uuid.NewString()
		buf, w := newMultipartRequest()

		// Main post part
		postPayload := []byte(fmt.Sprintf(`{"id": %q, "name":"run1", "run_type":"llm", "session_name":"someSession"}`, runID))
		addJSONPart(t, w, "post."+runID, postPayload)

		// Out-of-band data bigger than 1KB
		bigOob := bytes.Repeat([]byte("Y"), 2000)
		addJSONPart(t, w, "post."+runID+".extra", bigOob)
		req := finalizeRequest(t, buf, w)

		res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.NoError(t, err)
		require.NotNil(t, res)

		assert.Contains(t, res.Extras, runID)
		assert.Contains(t, res.Extras[runID], "extra")

		basePath, _, _, _, startOffset, endOffset, err := parseS3Url(string(res.Extras[runID]["extra"].Data))
		require.NoError(t, err)

		output, err := stgClient.GetObject(context.Background(), &storage.GetObjectInput{
			Bucket: storage.GetBucket(string(res.Extras[runID]["extra"].Data)),
			Key:    basePath,
			Range: &storage.Range{
				Start: startOffset,
				End:   endOffset,
			},
		})
		require.NoError(t, err)

		getBuf := new(bytes.Buffer)
		_, err = getBuf.ReadFrom(output.Body)
		require.NoError(t, err)
		require.Equal(t, string(bigOob), getBuf.String())
	})

	t.Run("AttachmentMissingField", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()

		payload := []byte("some binary data")
		addAttachmentPart(t, w, "attachment."+runID, payload) // missing ".field"
		req := finalizeRequest(t, buf, w)

		_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "attachment missing field")
	})

	t.Run("AttachmentFieldSuffix", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()

		payload := []byte("some data")
		addAttachmentPart(t, w, "attachment."+runID+".some_s3_url", payload) // invalid suffix
		req := finalizeRequest(t, buf, w)

		_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid attachment part")
	})

	t.Run("AttachmentUnknownRun", func(t *testing.T) {
		runID := uuid.NewString()
		buf, w := newMultipartRequest()

		payload := []byte("some data")
		addAttachmentPart(t, w, "attachment."+runID+".some_field", payload) // run not declared
		req := finalizeRequest(t, buf, w)

		_, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.NoError(t, err, "Should merely warn & skip attachment")
	})
}

func TestParseMultipartForm_ZstdEncoded(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, err := usage_limits.GetUsageLimitsClient()
	require.NoError(t, err)
	tracerClient, err := tracer_sessions.GetTracerSessionsClient()
	require.NoError(t, err)

	runID := uuid.NewString()
	postPayload := []byte(fmt.Sprintf(`{
		"id": %q,
		"name": "zstd run",
		"session_name": "sess1",
		"run_type": "llm"
	}`, runID))

	// First, create a normal multipart body
	var originalBuf bytes.Buffer
	originalWriter := multipart.NewWriter(&originalBuf)

	hdr := textproto.MIMEHeader{}
	hdr.Set("Content-Type", fmt.Sprintf("application/json; length=%d", len(postPayload)))
	hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
	part, err := originalWriter.CreatePart(hdr)
	require.NoError(t, err)
	_, err = part.Write(postPayload)
	require.NoError(t, err)

	require.NoError(t, originalWriter.Close())

	// Compress the entire multipart body with zstd
	var compressedBody bytes.Buffer
	zw := zstd.NewWriter(&compressedBody)
	_, err = zw.Write(originalBuf.Bytes())
	require.NoError(t, err)
	require.NoError(t, zw.Close())

	// Build request with zstd encoding
	req := httptest.NewRequest("POST", "/", &compressedBody)
	req.Header.Set("Content-Encoding", "zstd")
	req.Header.Set("Content-Type", "multipart/form-data; boundary="+originalWriter.Boundary())

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	require.NoError(t, err)
	require.NotNil(t, res)

	assert.Len(t, res.Posts, 1)
	assert.Equal(t, runID, *res.Posts[0].ID)
	assert.Equal(t, "zstd run", *res.Posts[0].Name)
}

func TestParseMultipartForm_LargeOOBKeyInMainPayload(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	oldMin := config.Env.MinBlobStorageSizeKb
	config.Env.MinBlobStorageSizeKb = 1 // 1KB to force large data to S3
	defer func() { config.Env.MinBlobStorageSizeKb = oldMin }()

	runID := uuid.NewString()
	oobKey := "inputs"
	largeValue := strings.Repeat("X", 1025)

	postPayload := []byte(fmt.Sprintf(`{
		"id": %q,
		"name": "run-with-large-oob",
		"session_name": "test-session",
		"run_type": "llm",
		"%s": %q
	}`, runID, oobKey, largeValue))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	require.NoError(t, err)
	require.NotNil(t, res)

	extras := res.Extras[runID]
	require.NotNil(t, extras, "Extras should exist for run ID")
	oobEntry, exists := extras[oobKey]
	require.True(t, exists, "OOB key should be present in extras")
	assert.Empty(t, oobEntry.ContentType, "S3 key entry should have empty content type")

	s3Key := string(oobEntry.Data)
	basePath, _, _, _, startOffset, endOffset, err := parseS3Url(s3Key)
	require.NoError(t, err)

	// Download the data from S3 and verify it matches the original
	require.NotNil(t, stgClient, "Storage client should not be nil")
	output, err := stgClient.GetObject(ctx, &storage.GetObjectInput{
		Bucket: storage.GetBucket(s3Key),
		Key:    basePath,
		Range: &storage.Range{
			Start: startOffset,
			End:   endOffset,
		},
	})
	require.NoError(t, err)
	defer output.Body.Close()

	downloadedData, err := io.ReadAll(output.Body)
	require.NoError(t, err)

	expectedData, err := json.Marshal(largeValue)
	require.NoError(t, err)
	assert.Equal(t, expectedData, downloadedData, "Uploaded data should match original OOB value")
}

func TestParseMultipartForm_SmallOOBKeyInMainPayload(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	usageClient, _ := usage_limits.GetUsageLimitsClient()
	tracerClient, _ := tracer_sessions.GetTracerSessionsClient()

	oldMin := config.Env.MinBlobStorageSizeKb
	config.Env.MinBlobStorageSizeKb = 5 // 5KB threshold (small data remains inline)
	defer func() { config.Env.MinBlobStorageSizeKb = oldMin }()

	runID := uuid.NewString()
	oobKey := "inputs"
	smallValue := "small test value"

	postPayload := []byte(fmt.Sprintf(`{
		"id": %q,
		"name": "run-with-small-oob",
		"session_name": "test-session",
		"run_type": "llm",
		"%s": %q
	}`, runID, oobKey, smallValue))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	require.NoError(t, err)
	require.NotNil(t, res)

	extras := res.Extras[runID]
	require.NotNil(t, extras, "Extras should exist for run ID")

	oobEntry, exists := extras[oobKey]
	require.True(t, exists, "OOB key should be present in extras")
	assert.Equal(t, "application/json", oobEntry.ContentType, "Small OOB entry should have JSON content type")
	assert.Empty(t, oobEntry.Encoding, "Small OOB entry should have no encoding")

	var actualValue string
	err = json.Unmarshal(oobEntry.Data, &actualValue)
	require.NoError(t, err, "Should be able to unmarshal the stored value")
	assert.Equal(t, smallValue, actualValue, "Stored value should match original")
}

func jsonMustMarshal(t *testing.T, v interface{}) []byte {
	b, err := json.Marshal(v)
	require.NoError(t, err)
	return b
}

func TestParseMultipartForm_ContinuousUpload(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)

	oldMinSize := config.Env.MinBlobStorageSizeKb
	config.Env.MinBlobStorageSizeKb = 256
	defer func() { config.Env.MinBlobStorageSizeKb = oldMinSize }()

	usageClient, err := usage_limits.GetUsageLimitsClient()
	require.NoError(t, err)
	tracerClient, err := tracer_sessions.GetTracerSessionsClient()
	require.NoError(t, err)

	runID := uuid.NewString()

	mainOOBValue := "main oob payload"
	traceTier := tracer_sessions.ShortLived
	postPayload := []byte(fmt.Sprintf(`{
		"id": %q,
		"name": "continuous run",
		"session_name": "contSession",
		"inputs": %q,
		"run_type": "llm",
		"trace_tier": %q
	}`, runID, mainOOBValue, traceTier))

	multipartOOBValue := string(bytes.Repeat([]byte("A"), 300*1024))

	buf, w := newMultipartRequest()
	addJSONPart(t, w, "post."+runID, postPayload)
	addJSONPart(t, w, "post."+runID+".outputs", []byte(multipartOOBValue))
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	require.NoError(t, err)
	require.NotNil(t, res)

	extras, ok := res.Extras[runID]
	require.True(t, ok, "Expected extras for runID")

	tests := []struct {
		key      string
		expected string
		isJSON   bool
	}{
		{"inputs", mainOOBValue, true},
		{"outputs", multipartOOBValue, false},
	}

	for _, tt := range tests {
		extra, ok := extras[tt.key]
		require.True(t, ok, "Expected extra key %s", tt.key)

		if tt.key == "outputs" {
			// For outputs, we expect S3 storage with offsets
			parts := strings.Split(string(extra.Data), "#")
			require.Len(t, parts, 2, "Expected uploader key and offsets for key %s", tt.key)
			uploaderKey := parts[0]

			// Add check for TTL prefix
			require.True(t, strings.Contains(uploaderKey, "multipart/"),
				"Expected uploader key to have contain %s, got %s", "multipart/", uploaderKey)

			offsetRange := parts[1]
			offsetParts := strings.Split(offsetRange, "-")
			require.Len(t, offsetParts, 2, "Expected start and end offsets for key %s", tt.key)

			startOffset, err := strconv.ParseInt(offsetParts[0], 10, 64)
			require.NoError(t, err)
			endOffset, err := strconv.ParseInt(offsetParts[1], 10, 64)
			require.NoError(t, err)

			require.NotNil(t, stgClient, "Storage client should not be nil")
			obj, err := stgClient.GetObject(ctx, &storage.GetObjectInput{
				Bucket: storage.GetBucket(string(extra.Data)),
				Key:    uploaderKey,
				Range: &storage.Range{
					Start: startOffset,
					End:   endOffset,
				},
			})
			require.NoError(t, err)
			defer obj.Body.Close()

			uploadedBuf := new(bytes.Buffer)
			_, err = io.Copy(uploadedBuf, obj.Body)
			require.NoError(t, err)
			uploadedData := uploadedBuf.Bytes()

			require.LessOrEqual(t, int(endOffset), len(uploadedData), "end offset exceeds uploaded data length")

			assert.Equal(t, tt.expected, string(uploadedData), "Uploaded content for key %s does not match", tt.key)
		} else {
			// For inputs, we expect the value to be directly in Redis
			var expectedData string
			if tt.isJSON {
				expectedData = string(jsonMustMarshal(t, tt.expected))
			} else {
				expectedData = tt.expected
			}
			assert.Equal(t, expectedData, string(extra.Data), "Redis content for key %s does not match", tt.key)
		}
	}
}
func TestParseMultipartForm_PostAndPatchLongLived(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	usageClient, err := usage_limits.GetUsageLimitsClient()
	require.NoError(t, err)
	tracerClient, err := tracer_sessions.GetTracerSessionsClient()
	require.NoError(t, err)

	traceTier := tracer_sessions.LongLived // "longlived"
	sessionName := "LongLived Test Session"

	tsCreate := tracer_sessions.TracerSessionCreate{
		TracerSessionBase: tracer_sessions.TracerSessionBase{
			ID:        uuid.New(),
			StartTime: time.Now().UTC(),
			Name:      sessionName,
			TraceTier: &traceTier,
		},
	}

	sessionRecord, err := tracerClient.CreateTracerSession(ctx, authInfo, tsCreate, false)
	require.NoError(t, err)
	require.NotNil(t, sessionRecord)

	assert.Equal(t, traceTier, *sessionRecord.TraceTier)

	oldMinBlobSize := config.Env.MinBlobStorageSizeKb
	config.Env.MinBlobStorageSizeKb = 1
	defer func() { config.Env.MinBlobStorageSizeKb = oldMinBlobSize }()

	runID := uuid.NewString()

	// Build a post payload that includes the session details and a large "inputs" field.
	largeInput := strings.Repeat("X", 2000)
	postPayload := []byte(fmt.Sprintf(`{
		"id": %q,
		"name": "longlived post run",
		"run_type": "llm",
		"session_name": %q,
		"inputs": %q
	}`, runID, sessionName, largeInput))

	// Build a patch payload that omits session info and includes a large "error" field.
	largeError := strings.Repeat("Y", 2000)
	patchPayload := []byte(fmt.Sprintf(`{
		"id": %q,
		"name": "patch run",
		"run_type": "llm",
		"error": %q
	}`, runID, largeError))

	buf, w := newMultipartRequest()
	// Create the post part (with session info already present in the DB).
	addJSONPart(t, w, "post."+runID, postPayload)
	// Create the patch part (no session info; it should use the existing session).
	addJSONPart(t, w, "patch."+runID, patchPayload)
	req := finalizeRequest(t, buf, w)

	res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
	require.NoError(t, err)
	require.NotNil(t, res)

	// Ensure both the post and patch run objects are present.
	assert.Len(t, res.Posts, 1)
	assert.Len(t, res.Patches, 1)

	// Verify that the extras for the run include both the "inputs" (from the post) and "error" (from the patch) fields.
	extras, ok := res.Extras[runID]
	require.True(t, ok, "Expected extras for run ID %s", runID)
	inputExtra, ok := extras["inputs"]
	require.True(t, ok, "Expected 'inputs' extra in run extras")
	errorExtra, ok := extras["error"]
	require.True(t, ok, "Expected 'error' extra in run extras")

	// Check that both S3 keys start with the ttl_l prefix.
	inputKey := string(inputExtra.Data)
	errorKey := string(errorExtra.Data)
	assert.True(t, strings.Contains(inputKey, "ttl_l/multipart/"), "Input key should contain ttl_l/multipart/: got %s", inputKey)
	assert.True(t, strings.Contains(errorKey, "ttl_l/multipart/"), "Error key should contain ttl_l/multipart/: got %s", errorKey)
}

func TestParseMultipartForm_PatchRedisLookup(t *testing.T) {
	ctx := context.Background()
	authInfo := setupTestTenant(t)
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	usageClient, err := usage_limits.GetUsageLimitsClient()
	require.NoError(t, err)
	tracerClient, err := tracer_sessions.GetTracerSessionsClient()
	require.NoError(t, err)

	oldMinBlobSize := config.Env.MinBlobStorageSizeKb
	config.Env.MinBlobStorageSizeKb = 1
	defer func() { config.Env.MinBlobStorageSizeKb = oldMinBlobSize }()

	t.Run("LongLived session lookup", func(t *testing.T) {
		traceTier := tracer_sessions.LongLived
		sessionName := "Redis Lookup Test Session"

		tsCreate := tracer_sessions.TracerSessionCreate{
			TracerSessionBase: tracer_sessions.TracerSessionBase{
				ID:        uuid.New(),
				StartTime: time.Now().UTC(),
				Name:      sessionName,
				TraceTier: &traceTier,
			},
		}
		sessionRecord, err := tracerClient.CreateTracerSession(ctx, authInfo, tsCreate, false)
		require.NoError(t, err)
		require.NotNil(t, sessionRecord)
		assert.Equal(t, traceTier, *sessionRecord.TraceTier)

		runID := uuid.NewString()
		pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, runID)
		require.NotNil(t, routedRedisClient, "Redis pool should not be nil")
		sessionIDJson, err := json.Marshal(sessionRecord.ID.String())
		require.NoError(t, err)
		err = routedRedisClient.HSet(ctx, pendingKey, "session_id", string(sessionIDJson)).Err()
		require.NoError(t, err)
		defer routedRedisClient.Del(ctx, pendingKey)

		largeError := strings.Repeat("Y", 2000)
		patchPayload := []byte(fmt.Sprintf(`{
			"id": %q,
			"trace_id": %q,
			"name": "patch run",
			"run_type": "llm",
			"error": %q
		}`, runID, runID, largeError))

		buf, w := newMultipartRequest()
		addJSONPart(t, w, "patch."+runID, patchPayload)
		req := finalizeRequest(t, buf, w)

		res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.NoError(t, err)
		require.NotNil(t, res)

		assert.Len(t, res.Patches, 1)

		extras, ok := res.Extras[runID]
		require.True(t, ok, "Expected extras for run id %s", runID)
		errorExtra, ok := extras["error"]
		require.True(t, ok, "Expected 'error' extra in run extras")
		errorKey := string(errorExtra.Data)
		assert.True(t, strings.Contains(errorKey, "ttl_l/multipart/"), "Error key should contain ttl_l/multipart/ prefix, got: %s", errorKey)
	})

	t.Run("Invalid UUID in Redis", func(t *testing.T) {
		runID := uuid.NewString()
		pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, runID)
		require.NotNil(t, routedRedisClient, "Redis pool should not be nil")

		// Store an invalid UUID in Redis
		invalidUUID := "not-a-valid-uuid"
		sessionIDJson, err := json.Marshal(invalidUUID)
		require.NoError(t, err)
		err = routedRedisClient.HSet(ctx, pendingKey, "session_id", string(sessionIDJson)).Err()
		require.NoError(t, err)
		defer routedRedisClient.Del(ctx, pendingKey)

		largeError := strings.Repeat("Y", 2000)
		patchPayload := []byte(fmt.Sprintf(`{
			"id": %q,
			"trace_id": %q,
			"name": "patch run",
			"run_type": "llm",
			"error": %q
		}`, runID, runID, largeError))

		buf, w := newMultipartRequest()
		addJSONPart(t, w, "patch."+runID, patchPayload)
		req := finalizeRequest(t, buf, w)

		// The run should still be processed despite the invalid UUID
		res, err := runs.ParseMultipartForm(ctx, req, &authInfo, usageClient, tracerClient, routedRedisClient, stgClient)
		require.NoError(t, err)
		require.NotNil(t, res)

		assert.Len(t, res.Patches, 1)

		extras, ok := res.Extras[runID]
		require.True(t, ok, "Expected extras for run id %s", runID)
		errorExtra, ok := extras["error"]
		require.True(t, ok, "Expected 'error' extra in run extras")
		errorKey := string(errorExtra.Data)
		assert.True(t, strings.Contains(errorKey, "ttl_s/multipart/"), "Error key should contain ttl_s/multipart/ prefix, got: %s", errorKey)
	})
}

func TestIngestFullRunsMultipart(t *testing.T) {
	defer goleak.VerifyNone(t,
		goleak.IgnoreAnyFunction("github.com/cihub/seelog.(*asyncLoopLogger).processItem"),
		goleak.IgnoreAnyFunction("github.com/jackc/pgx/v5/pgxpool.(*Pool).backgroundHealthCheck"),
	)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	chConn, err := database.ChConnect(true)
	require.NoError(t, err)
	defer func(chConn clickhouse.Conn) {
		err := chConn.Close()
		if err != nil {
			t.Error(err)
		}
	}(chConn)

	feedback.NewFeedbackConfigClient(cachingRedisPool)
	feedbackConfigClient, err := feedback.GetFeedbackConfigClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get feedback client: %v", err))
	}

	// Create unique tenant handle
	tenantHandle := fmt.Sprintf("test-tenant-%s", uuid.New().String())
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", tenantHandle, &auth.TenantConfig{}, true)
	sessionID := testutil.TracerSessionSetup(t, dbpool, tenantID)

	// Create auth info for the test
	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	// Helper function to test run ingestion with different Redis configurations
	testRunIngestion := func(t *testing.T, configName string, setupConfig func()) {
		t.Run(fmt.Sprintf("successful run ingestion with %s", configName), func(t *testing.T) {

			routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
			defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
			defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

			baseHandler := runs.NewRunHandler(dbpool, cachingRedisPool, *routedRedisPools)
			handler := &runs.MultipartRunHandler{
				RunHandler:           *baseHandler,
				StorageClient:        nil,
				FeedbackConfigClient: feedbackConfigClient,
				ClickhouseConn:       chConn,
			}

			hllKey := usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(tenantID)

			ctx := context.Background()
			routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
			err := routedRedisClient.Del(ctx, hllKey).Err()
			assert.NoError(t, err)

			// Apply the test-specific configuration
			setupConfig()

			// Create a test run payload
			runID := uuid.NewString()
			dottedOrder := "20230505T051324571809Z" + runID
			runPayload := []byte(fmt.Sprintf(`{
				"id": %q,
				"trace_id": %q,
				"name": "test multipart run",
				"run_type": "llm",
				"dotted_order": %q,
				"session_name": "test_session"
			}`, runID, runID, dottedOrder))

			// Create a multipart request
			buf, mpWriter := newMultipartRequest()
			addJSONPart(t, mpWriter, "post."+runID, runPayload)
			req := finalizeRequest(t, buf, mpWriter)

			// Set up request with headers and auth context
			req.Header.Set("X-Tenant-Id", tenantID)
			req.Header.Set("X-Session-Id", sessionID)
			ctx = context.WithValue(req.Context(), auth.AuthCtxKey, &authInfo)
			req = req.WithContext(ctx)

			// Create a response recorder
			recorder := httptest.NewRecorder()
			testLogger(t)(http.HandlerFunc(handler.IngestRunsMultipart)).ServeHTTP(recorder, req)

			// Check the response
			require.Equal(t, http.StatusAccepted, recorder.Code, recorder.Body.String())

			// Verify response body
			var response map[string]string
			err = json.Unmarshal(recorder.Body.Bytes(), &response)
			require.NoError(t, err)
			assert.Equal(t, "Runs batch ingested", response["message"])

			// Verify that the run was stored in Redis
			ctx = context.Background()
			pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", tenantID, runID)

			// Get the Redis client
			routedRedisClient = routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
			queueRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationEnqueue)

			if config.Env.RedisShardingEnabled && config.Env.RedisShardingByTraceTenantIds.Contains(tenantID) {
				// if sharding by trace, use the traceID to route the run
				routedRedisClient = routedRedisPools.GetRoutedRedisClient(ctx, runID, lsredis.RedisOperationIngestion)
				queueRedisClient = routedRedisPools.GetRoutedRedisClient(ctx, runID, lsredis.RedisOperationEnqueue)
			}

			// Check if it's a *redis.Client
			_, isClient := routedRedisClient.(*redis.Client)
			_, isClusterClient := routedRedisClient.(*redis.ClusterClient)

			t.Logf("Redis client type: %s", reflect.TypeOf(routedRedisClient))

			if config.Env.RedisClusterEnabled && (config.Env.RedisClusterIngestionGlobalEnabled || config.Env.RedisClusterIngestionTenantIds.Contains(tenantID)) {
				assert.False(t, isClient, "Expected Redis client not to be of type *redis.Client")
				assert.True(t, isClusterClient, "Redis client should be of type *redis.ClusterClient")
				assert.NotEqual(t, routedRedisClient, queueRedisClient)
			} else if config.Env.RedisShardingEnabled {
				assert.True(t, isClient, "Expected Redis client to be of type *redis.Client")
				assert.False(t, isClusterClient, "Redis client should not be of type *redis.ClusterClient")
				assert.Equal(t, routedRedisClient, queueRedisClient)
			} else {
				assert.True(t, isClient, "Expected Redis client to be of type *redis.Client")
				assert.False(t, isClusterClient, "Redis client should not be of type *redis.ClusterClient")
				assert.Equal(t, routedRedisClient, queueRedisClient)
			}

			// Check the pending runs key
			exists, err := routedRedisClient.Exists(ctx, pendingKey).Result()
			require.NoError(t, err)
			assert.Equal(t, int64(1), exists, "Should have a pending run in Redis")

			// Check if the post field exists
			value, err := routedRedisClient.HGet(ctx, pendingKey, "post").Result()
			assert.NoError(t, err)
			assert.NotEmpty(t, value, "Post field should not be empty")

			// Check that job is queued
			queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
			jobKeys, err := queueRedisClient.LRange(ctx, queueKey, 0, -1).Result()
			assert.NoError(t, err)

			foundTrace := false
			for _, k := range jobKeys {
				jobData, _ := queueRedisClient.Get(ctx, k).Result()
				if strings.Contains(jobData, runID) {
					foundTrace = true
					break
				}
			}
			assert.True(t, foundTrace, "trace must be scheduled")

			// check usage limits
			hllKey = usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(tenantID)
			count, err := routedRedisClient.PFCount(ctx, hllKey).Result()
			assert.NoError(t, err)
			assert.Greater(t, count, int64(0), "trace must be in usage limits")
		})
	}

	// Save original configuration to restore after all tests
	originalRedisClusterIngestionGlobalEnabled := config.Env.RedisClusterIngestionGlobalEnabled
	originalRedisClusterIngestionTenantIds := config.Env.RedisClusterIngestionTenantIds
	originalRedisShardingByTraceTenantIds := config.Env.RedisShardingByTraceTenantIds
	originalRedisShardingEnabled := config.Env.RedisShardingEnabled
	configName := "single cluster"
	if config.Env.RedisShardingEnabled {
		configName = "Sharding enabled"
	} else if config.Env.RedisClusterEnabled && config.Env.RedisClusterIngestionGlobalEnabled {
		configName = "Redis cluster"
	}

	// Test with default configuration
	testRunIngestion(t, configName, func() {

	})

	// if sharding enabled, test with trace level sharding
	if config.Env.RedisShardingEnabled {
		config.Env.RedisShardingByTraceTenantIds = config.List{SplitList: []string{tenantID}}
		// Test with trace level configuration
		testRunIngestion(t, "sharding by trace", func() {

		})
	}

	// Restore original config after each test
	config.Env.RedisClusterIngestionGlobalEnabled = originalRedisClusterIngestionGlobalEnabled
	config.Env.RedisClusterIngestionTenantIds = originalRedisClusterIngestionTenantIds
	config.Env.RedisShardingByTraceTenantIds = originalRedisShardingByTraceTenantIds
	config.Env.RedisShardingEnabled = originalRedisShardingEnabled
	// if cluster enabled, test with cluster tenant enabled and disabled
	if config.Env.RedisClusterEnabled {
		// enable sharding also
		config.Env.RedisShardingEnabled = true
		config.Env.RedisClusterIngestionGlobalEnabled = false

		// Test with cluster configuration
		testRunIngestion(t, "Redis cluster with tenant disabled and sharding enabled", func() {

		})

		config.Env.RedisClusterIngestionTenantIds = config.List{SplitList: []string{tenantID}}
		// Test with cluster configuration
		testRunIngestion(t, "Redis cluster with tenant enabled", func() {

		})
	}

	// Restore original config after all tests complete
	config.Env.RedisClusterIngestionGlobalEnabled = originalRedisClusterIngestionGlobalEnabled
	config.Env.RedisClusterIngestionTenantIds = originalRedisClusterIngestionTenantIds
	config.Env.RedisShardingByTraceTenantIds = originalRedisShardingByTraceTenantIds
	config.Env.RedisShardingEnabled = originalRedisShardingEnabled
}

// Helper function for test logging
func testLogger(t *testing.T) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			next.ServeHTTP(w, r)
		})
	}
}
