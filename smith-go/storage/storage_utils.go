package storage

import (
	"strings"

	"langchain.com/smith/config"
)

var S3Buckets = []string{config.Env.S3BucketName, config.Env.S3SingleRegionBucketName}

var S3SingleRegionBuckets = []string{config.Env.S3SingleRegionBucketName}

func GetBucket(path string) string {
	for _, bucket := range S3SingleRegionBuckets {
		if bucket != "" && strings.Contains(path, bucket) {
			return bucket
		}
	}

	return config.Env.S3BucketName
}
