package storage_test

import (
	"bytes"
	"context"
	"fmt"
	"testing"

	"langchain.com/smith/storage"
)

func setupBenchmarkS3Client(t *testing.B) *storage.S3StorageClient {
	s3StorageClient, err := storage.NewS3StorageClient(false, nil)
	if err != nil {
		t.Fatalf("Failed to create S3 storage client: %v", err)
	}
	return s3StorageClient
}

func BenchmarkContinuousUploader_New(b *testing.B) {
	client := setupBenchmarkS3Client(b)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
		if err != nil {
			b.Fatalf("Failed to create uploader: %v", err)
		}
		uploader.Close()
	}
}

func BenchmarkContinuousUploader_NewWithOptions(b *testing.B) {
	client := setupBenchmarkS3Client(b)
	ctx := context.Background()

	options := []storage.Option{
		storage.WithPartSize(10 * 1024 * 1024), // 10MB
		storage.WithConcurrency(8),
		storage.WithSpoolLimitBytes(100 * 1024 * 1024), // 100MB
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName, options...)
		if err != nil {
			b.Fatalf("Failed to create uploader: %v", err)
		}
		uploader.Close()
	}
}

func BenchmarkContinuousUploader_UploadReader(b *testing.B) {
	client := setupBenchmarkS3Client(b)
	ctx := context.Background()

	sizes := []int{
		1 * 1024 * 1024,   // 1MB
		5 * 1024 * 1024,   // 5MB
		10 * 1024 * 1024,  // 10MB
		50 * 1024 * 1024,  // 50MB
		100 * 1024 * 1024, // 100MB
	}

	for _, size := range sizes {
		b.Run(fmt.Sprintf("Size_%dMB", size/1024/1024), func(b *testing.B) {
			data, err := generateRandomBytes(size)
			if err != nil {
				b.Fatalf("Failed to generate test data: %v", err)
			}

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
				if err != nil {
					b.Fatalf("Failed to create uploader: %v", err)
				}

				err = uploader.StartUpload(fmt.Sprintf("benchmark-%d-%d.dat", size, i))
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to start upload: %v", err)
				}

				reader := bytes.NewReader(data)
				_, _, err = uploader.UploadReader(reader)
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to upload data: %v", err)
				}

				result, err := uploader.Complete()
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to complete upload: %v", err)
				}

				uploader.Close()

				b.ReportMetric(float64(result.NumParts), "parts")
				if result.UploadStrategy == storage.UploadStrategyMultipart {
					b.ReportMetric(1, "multipart")
				} else {
					b.ReportMetric(1, "single")
				}
			}
		})
	}
}

func BenchmarkContinuousUploader_ChunkedUpload(b *testing.B) {
	client := setupBenchmarkS3Client(b)
	ctx := context.Background()

	totalSize := 50 * 1024 * 1024 // 50MB total
	chunkSizes := []int{
		1 * 1024 * 1024,  // 1MB chunks
		5 * 1024 * 1024,  // 5MB chunks
		10 * 1024 * 1024, // 10MB chunks
	}

	for _, chunkSize := range chunkSizes {
		b.Run(fmt.Sprintf("ChunkSize_%dMB", chunkSize/1024/1024), func(b *testing.B) {
			data, err := generateRandomBytes(totalSize)
			if err != nil {
				b.Fatalf("Failed to generate test data: %v", err)
			}

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				uploader, err := storage.NewContinuousUploader(ctx, client.S3, testBucketName)
				if err != nil {
					b.Fatalf("Failed to create uploader: %v", err)
				}

				err = uploader.StartUpload(fmt.Sprintf("benchmark-chunked-%d-%d.dat", chunkSize, i))
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to start upload: %v", err)
				}

				for offset := 0; offset < totalSize; offset += chunkSize {
					end := offset + chunkSize
					if end > totalSize {
						end = totalSize
					}

					chunk := data[offset:end]
					reader := bytes.NewReader(chunk)
					_, _, err = uploader.UploadReader(reader)
					if err != nil {
						uploader.Close()
						b.Fatalf("Failed to upload chunk: %v", err)
					}
				}

				result, err := uploader.Complete()
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to complete upload: %v", err)
				}

				uploader.Close()

				b.ReportMetric(float64(result.NumParts), "parts")
				if result.UploadStrategy == storage.UploadStrategyMultipart {
					b.ReportMetric(1, "multipart")
				} else {
					b.ReportMetric(1, "single")
				}
			}
		})
	}
}

func BenchmarkContinuousUploader_Concurrency(b *testing.B) {
	client := setupBenchmarkS3Client(b)
	ctx := context.Background()

	concurrencyLevels := []int{1, 2, 4, 8, 16}
	dataSize := 50 * 1024 * 1024 // 50MB

	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(b *testing.B) {
			data, err := generateRandomBytes(dataSize)
			if err != nil {
				b.Fatalf("Failed to generate test data: %v", err)
			}

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				uploader, err := storage.NewContinuousUploader(
					ctx,
					client.S3,
					testBucketName,
					storage.WithConcurrency(concurrency),
				)
				if err != nil {
					b.Fatalf("Failed to create uploader: %v", err)
				}

				err = uploader.StartUpload(fmt.Sprintf("benchmark-concurrency-%d-%d.dat", concurrency, i))
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to start upload: %v", err)
				}

				reader := bytes.NewReader(data)
				_, _, err = uploader.UploadReader(reader)
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to upload data: %v", err)
				}

				result, err := uploader.Complete()
				if err != nil {
					uploader.Close()
					b.Fatalf("Failed to complete upload: %v", err)
				}

				uploader.Close()

				b.ReportMetric(float64(result.NumParts), "parts")
				if result.UploadStrategy == storage.UploadStrategyMultipart {
					b.ReportMetric(1, "multipart")
				} else {
					b.ReportMetric(1, "single")
				}
			}
		})
	}
}
