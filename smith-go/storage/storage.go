package storage

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"os"
	"time"

	"langchain.com/smith/config"
)

var (
	ErrTempFileCreateFailed  = errors.New("failed to create temp file")
	ErrTempFileWriteFailed   = errors.New("failed to write to temp file")
	ErrRequestBodyReadFailed = errors.New("failed to read request body")
	ErrNotFound              = errors.New("object not found")
)

type UploadStrategy string

const (
	UploadStrategyDirect UploadStrategy = "direct"
	UploadStrategyMemory UploadStrategy = "memory"
	UploadStrategyDisk   UploadStrategy = "disk"
)

type StorageClient interface {
	HealthCheck(ctx context.Context, input *HealthCheckInput) error
	CopyObject(ctx context.Context, input *CopyObjectInput) error
	GetObject(ctx context.Context, input *GetObjectInput) (*GetObjectOutput, error)
	HeadObject(ctx context.Context, input *HeadObjectInput) (*HeadObjectOutput, error)
	UploadObject(ctx context.Context, input *UploadObjectInput) (UploadStrategy, error)
	UploadObjectAsync(ctx context.Context, input *UploadObjectInput) (*UploadAsyncResult, error)
}

type BlobStorageClientOptions struct {
	SpoolMinSizeBytes int64
	SpoolLimitBytes   int64
}

type Response struct {
	Message string `json:"message"`
}

type HealthCheckInput struct {
	Buckets []string
}

type CopyObjectInput struct {
	SourceBucket string
	SourceKey    string
	DestBucket   string
	DestKey      string
}

type Range struct {
	Start int64
	End   int64
}

type GetObjectInput struct {
	Bucket          string
	Key             string
	ContentEncoding string
	Range           *Range
	RawResponse     bool
}

type GetObjectOutput struct {
	Body            io.ReadCloser
	ContentType     *string
	ContentEncoding *string
	ContentLength   *int64
	LastModified    *time.Time
}

type HeadObjectInput struct {
	Bucket          string
	ContentEncoding string
	Key             string
}

type HeadObjectOutput struct {
	ContentType     *string
	ContentEncoding *string
	ContentLength   *int64
	LastModified    *time.Time
}

type UploadObjectInput struct {
	Bucket          string
	ContentEncoding string
	ContentLength   int64
	ContentType     string
	Key             string
	Reader          io.Reader
}

type UploadAsyncResult struct {
	Strategy UploadStrategy
	ErrChan  chan error
}

func (r *UploadAsyncResult) Wait() error {
	return <-r.ErrChan
}

// baseStorage provides common functionality for all storage implementations
type baseStorage struct {
	opts *BlobStorageClientOptions
}

func NewBaseStorage(opts *BlobStorageClientOptions) *baseStorage {
	if opts == nil {
		opts = &BlobStorageClientOptions{
			SpoolMinSizeBytes: config.Env.SpoolMinSizeKB * 1024,
			SpoolLimitBytes:   GetDefaultSpoolLimitBytes(),
		}
	}
	return &baseStorage{
		opts: opts,
	}
}

func (b *baseStorage) canSpoolToDisk(ctx context.Context, size int64) bool {
	return CanSpoolToDisk(ctx, size, b.opts.SpoolLimitBytes)
}

func (b *baseStorage) failIfDisabled() {
	if !config.Env.BlobStorageEnabled {
		panic("Blob storage is not enabled")
	}
}

func (b *baseStorage) prepareReader(ctx context.Context, input io.Reader, length int64, oplog *slog.Logger) (io.Reader, func(), UploadStrategy, error) {
	if length == 0 {
		oplog.Debug("Unknown content length, uploading directly")
		ctx = config.LogAndContextSetField(ctx, "uploadStrategy", slog.StringValue(string(UploadStrategyDirect)))
		return input, func() {}, UploadStrategyDirect, nil
	}
	if aboveLimit := length > b.opts.SpoolMinSizeBytes; aboveLimit && b.canSpoolToDisk(ctx, length) {
		// spool to disk
		f, err := os.CreateTemp("", "ls-ingest-part-")
		cleanup := func() {
			f.Close()
			os.Remove(f.Name())
			diskBytesUsed.Add(-length)
		}
		if err != nil {
			oplog.Warn("Failed to create temp file", "err", err)
			return nil, cleanup, UploadStrategyDisk, fmt.Errorf("%w: %w", ErrTempFileCreateFailed, err)
		}
		if _, err := io.Copy(f, input); err != nil {
			oplog.Warn("Failed to write to temp file", "err", err)
			return nil, cleanup, UploadStrategyDisk, fmt.Errorf("%w: %w", ErrTempFileWriteFailed, err)
		}
		f.Seek(0, 0)
		// upload from file
		ctx = config.LogAndContextSetField(ctx, "uploadStrategy", slog.StringValue(string(UploadStrategyDisk)))
		return f, cleanup, UploadStrategyDisk, nil
	} else if !aboveLimit {
		// spool to memory
		buf := make([]byte, length)
		if _, err := io.ReadFull(input, buf); err != nil {
			oplog.Warn("Failed to read request body", "err", err)
			return nil, func() {}, UploadStrategyMemory, fmt.Errorf("%w: %w", ErrRequestBodyReadFailed, err)
		}
		// upload from memory
		ctx = config.LogAndContextSetField(ctx, "uploadStrategy", slog.StringValue(string(UploadStrategyMemory)))
		return bytes.NewReader(buf), func() {}, UploadStrategyMemory, nil
	} else {
		// upload from reader directly
		oplog.Warn("Spool limit exceeded, uploading directly")
		ctx = config.LogAndContextSetField(ctx, "uploadStrategy", slog.StringValue(string(UploadStrategyDirect)))
		return input, func() {}, UploadStrategyDirect, nil
	}
}
