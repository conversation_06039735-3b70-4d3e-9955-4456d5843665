package runs_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/textproto"
	"strconv"
	"strings"
	"testing"

	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/feedback"
	"langchain.com/smith/storage"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/DataDog/zstd"
	"github.com/gofrs/uuid"
	"github.com/justinas/alice"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"langchain.com/smith/auth"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/runs"
	"langchain.com/smith/testutil"
)

const (
	apiKey       = "***************************************************"
	hashedApiKey = "223ccab5ebc19e3d3c55a2b9fbb5bebf6808893afff285317b88fb5889867efd76049cd4dc04407c037ab3b353a11501304c0042a2ba9b37ded9f193f19a5b65"
)

func setUpDatabase(t *testing.T, dbpool *database.AuditLoggedPool) string {
	userID, _ := uuid.NewV4()
	userIdStr := userID.String()
	orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, userIdStr)
	tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
	serviceAccountId := testutil.ServiceAccountSetup(t, dbpool, "test service account", orgId)
	orgIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, nil, "ORGANIZATION_ADMIN", "organization", serviceAccountId, nil)
	serviceAccountIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, &tenantId, "WORKSPACE_ADMIN", "workspace", serviceAccountId, &orgIdentityId)
	testutil.ServiceApiKeySetup(t, dbpool, hashedApiKey, tenantId, serviceAccountIdentityId, "hm", serviceAccountId, orgId)

	return tenantId
}

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "DELETE FROM organizations; DELETE FROM users; DELETE FROM api_keys;")
	assert.NoError(t, err)
}

// parseS3Url parses an S3 URL and returns the base path, hash components, and offset range
func parseS3Url(s3Url string) (basePath string, hash1 string, hash2 string, uuid string, startOffset int64, endOffset int64, err error) {
	// Split the URL into path and offset parts
	parts := strings.Split(s3Url, "#")
	if len(parts) != 2 {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid S3 URL format: missing offset range")
	}

	// Parse the path components
	pathParts := strings.Split(parts[0], "/")
	if len(pathParts) < 5 {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid S3 URL format: insufficient path components")
	}

	// Extract hash components and UUID
	hash1 = pathParts[2]
	hash2 = pathParts[3]
	uuid = pathParts[4]

	// Parse the offset range
	offsetParts := strings.Split(parts[1], "-")
	if len(offsetParts) != 2 {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid S3 URL format: invalid offset range")
	}

	startOffset, err = strconv.ParseInt(offsetParts[0], 10, 64)
	if err != nil {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid start offset: %v", err)
	}

	endOffset, err = strconv.ParseInt(offsetParts[1], 10, 64)
	if err != nil {
		return "", "", "", "", 0, 0, fmt.Errorf("invalid end offset: %v", err)
	}

	// Reconstruct the base path without the offset
	basePath = parts[0]

	return basePath, hash1, hash2, uuid, startOffset, endOffset, nil
}

func TestPostMultipartRuns(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// Set up DB and Redis
	dbpool := database.PgConnect()
	tenantID := setUpDatabase(t, dbpool)
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	chConn, err := database.ChConnect(true)
	require.NoError(t, err)

	defer func(chConn clickhouse.Conn) {
		err := chConn.Close()
		if err != nil {
			t.Error(err)
		}
	}(chConn)

	usageLimitsClient := usage_limits.NewUsageLimitsClient(dbpool, *routedRedisPools, cachingRedisPool)
	assert.NotNil(t, usageLimitsClient)
	tracerSessionsClient := tracer_sessions.NewTracerSessionsClient(dbpool, cachingRedisPool)
	assert.NotNil(t, tracerSessionsClient)
	feedbackClient := feedback.NewFeedbackClient(cachingRedisPool)
	assert.NotNil(t, feedbackClient)
	storageClient, err := storage.NewS3StorageClient(false, nil)
	require.NoError(t, err)

	ah := auth.NewBasicAuth(dbpool, cachingRedisPool, 0)

	handler := &runs.MultipartRunHandler{
		RunHandler: runs.RunHandler{
			Pg:                   dbpool,
			CachingRedisPool:     cachingRedisPool,
			RoutedRedisPools:     *routedRedisPools,
			UsageLimitsClient:    usageLimitsClient,
			TracerSessionsClient: tracerSessionsClient,
		},
		StorageClient:  storageClient,
		FeedbackClient: feedbackClient,
		ClickhouseConn: chConn,
	}

	doMultipartRequest := func(t *testing.T, body *bytes.Buffer, contentType string) *httptest.ResponseRecorder {
		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", body)
		req.Header.Set("Content-Type", contentType)
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)
		return wRec
	}

	t.Run("valid request - happy path", func(t *testing.T) {

		runID := uuid.Must(uuid.FromString("d6d06bc4-7537-4dff-a8e1-b8136ce96d70"))
		startTime := 1726671437361668
		endTime := "2024-09-17T18:17:17.361909Z"
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		traceID := runID.String()

		mainRun := struct {
			ID          string                 `json:"id"`
			TraceID     string                 `json:"trace_id"`
			Name        string                 `json:"name"`
			RunType     string                 `json:"run_type"`
			StartTime   int                    `json:"start_time"`
			EndTime     string                 `json:"end_time"`
			DottedOrder string                 `json:"dotted_order"`
			Extra       map[string]interface{} `json:"extra"`
			Error       string                 `json:"error"`
			Events      []map[string]string    `json:"events"`
			Serialized  map[string]string      `json:"serialized"`
		}{
			ID:          runID.String(),
			TraceID:     traceID,
			Name:        "test run",
			RunType:     "llm",
			StartTime:   startTime,
			EndTime:     endTime,
			DottedOrder: dottedOrder,
			Error:       "some error message",
			Events: []map[string]string{
				{"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
				{"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
			},
			Extra: map[string]interface{}{
				"foo": "bar",
				"metadata": map[string]string{
					"conversation_id": "112233",
				},
			},
			Serialized: map[string]string{
				"name": "AgentExecutor",
			},
		}
		mainRunJSON, err := json.Marshal(mainRun)
		require.NoError(t, err)

		patchRun := struct {
			ID          string            `json:"id"`
			TraceID     string            `json:"trace_id"`
			RunType     string            `json:"run_type"`
			StartTime   int               `json:"start_time"`
			DottedOrder string            `json:"dotted_order"`
			Outputs     map[string]string `json:"outputs"`
		}{
			ID:          runID.String(),
			TraceID:     traceID,
			RunType:     "llm",
			StartTime:   startTime,
			DottedOrder: dottedOrder,
			Outputs:     map[string]string{"patch_output": "some patch value"},
		}
		patchRunJSON, err := json.Marshal(patchRun)
		require.NoError(t, err)

		// Feedback
		feedbackPayload := struct {
			ID      string `json:"id"`
			TraceID string `json:"trace_id"`
			Key     string `json:"key"`
			Value   string `json:"value"`
			Score   int    `json:"score"`
			Comment string `json:"comment"`
			RunID   string `json:"run_id"`
		}{
			ID:      runID.String(),
			TraceID: traceID,
			Key:     "my_key",
			Value:   "my_value",
			Score:   10,
			Comment: "this worked great",
			RunID:   runID.String(),
		}
		feedbackJSON, err := json.Marshal(feedbackPayload)
		require.NoError(t, err)

		// Build the multipart body
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		parts := []struct {
			fieldName string
			content   []byte
			mimeType  string
		}{
			// Main run info
			{fmt.Sprintf("post.%s", runID), mainRunJSON, "application/json"},
			// Inputs
			{fmt.Sprintf("post.%s.inputs", runID), []byte(`{"prompt":"How many people live in Canada as of 2023?"}`), "application/json"},
			// Outputs
			{fmt.Sprintf("post.%s.outputs", runID), []byte(`{"initial_answer":"Roughly 39 million"}`), "application/json"},
			// Patch
			{fmt.Sprintf("patch.%s", runID), patchRunJSON, "application/json"},
			// Feedback
			{fmt.Sprintf("feedback.%s", runID), feedbackJSON, "application/json"},
		}

		for _, p := range parts {
			header := textproto.MIMEHeader{}
			header.Set("Content-Type", p.mimeType)
			header.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, p.fieldName))
			header.Set("Content-Length", fmt.Sprintf("%d", len(p.content)))
			part, err := writer.CreatePart(header)
			require.NoError(t, err)
			_, err = part.Write(p.content)
			require.NoError(t, err)
		}

		// Attachment
		attachment := "Hello Attachment"
		attachHeader := textproto.MIMEHeader{}
		attachHeader.Set("Content-Type", "application/octet-stream")
		attachHeader.Set("Content-Disposition", fmt.Sprintf(`form-data; name="attachment.%s.image"`, runID))
		attachHeader.Set("Content-Length", fmt.Sprintf("%d", len([]byte(attachment))))
		attachmentPart, err := writer.CreatePart(attachHeader)
		require.NoError(t, err)
		_, err = attachmentPart.Write([]byte(attachment))
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		// Send the request
		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)

		resp := wRec.Result()
		defer resp.Body.Close()

		require.Equal(t, http.StatusAccepted, resp.StatusCode, "Expected HTTP 202 Accepted")

		ctx := context.Background()

		queueingPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationEnqueue)

		queuedKey := "saq:default:queued"
		queuedVal, err := queueingPool.LRange(ctx, queuedKey, 0, -1).Result()
		require.NoError(t, err)
		require.NotEmpty(t, queuedVal, "Expected queued jobs")

		incompleteKey := "saq:default:incomplete"
		incompleteVal, err := queueingPool.ZRange(ctx, incompleteKey, 0, -1).Result()
		require.NoError(t, err)
		require.NotEmpty(t, incompleteVal, "Expected incomplete jobs")

		pendingRunKey := fmt.Sprintf("smith:runs:pending:%s:%s", tenantID, runID)
		extrasKey := pendingRunKey + ":extra"

		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		inputsVal, err := routedPool.HGet(ctx, extrasKey, "inputs").Result()
		require.NoError(t, err)
		assert.JSONEq(t,
			`{"prompt":"How many people live in Canada as of 2023?"}`,
			inputsVal,
			"inputs should match the JSON we sent",
		)

		outputsVal, err := routedPool.HGet(ctx, extrasKey, "outputs").Result()
		require.NoError(t, err)
		assert.JSONEq(t,
			`{"patch_output":"some patch value"}`,
			outputsVal,
			"Outputs should contain patch updates",
		)

		eventsVal, err := routedPool.HGet(ctx, extrasKey, "events").Result()
		require.NoError(t, err)
		var events []map[string]string
		require.NoError(t, json.Unmarshal([]byte(eventsVal), &events))
		require.Len(t, events, 2)
		assert.Equal(t, "start", events[0]["name"])
		assert.Equal(t, "end", events[1]["name"])

		extraVal, err := routedPool.HGet(ctx, extrasKey, "extra").Result()
		require.NoError(t, err)
		var extraMap map[string]interface{}
		require.NoError(t, json.Unmarshal([]byte(extraVal), &extraMap))
		assert.Equal(t, "bar", extraMap["foo"])
		metadata := extraMap["metadata"].(map[string]interface{})
		assert.Equal(t, "112233", metadata["conversation_id"])

		errorVal, err := routedPool.HGet(ctx, extrasKey, "error").Result()
		require.NoError(t, err)
		assert.Equal(t, `"some error message"`, errorVal)

		serializedVal, err := routedPool.HGet(ctx, extrasKey, "serialized").Result()
		require.NoError(t, err)
		var serializedMap map[string]string
		require.NoError(t, json.Unmarshal([]byte(serializedVal), &serializedMap))
		assert.Equal(t, "AgentExecutor", serializedMap["name"])

		fbKey := fmt.Sprintf("smith:runs:feedback:%s:%s", tenantID, runID)
		fbSet, err := routedPool.SMembers(ctx, fbKey).Result()
		require.NoError(t, err)
		require.Len(t, fbSet, 1)
		var fbPayload map[string]interface{}
		err = json.Unmarshal([]byte(fbSet[0]), &fbPayload)
		require.NoError(t, err)
		assert.Equal(t, "my_key", fbPayload["key"])
		assert.Equal(t, float64(10), fbPayload["score"])
		assert.Equal(t, "this worked great", fbPayload["comment"])

		attachmentURL, err := routedPool.HGet(ctx, extrasKey, "attachment.image_s3_url").Result()
		require.NoError(t, err)
		assert.Contains(t, attachmentURL, "ttl_s/attachments/", "Expected the attachment key to point to an S3 URL")

		outputObj, err := storageClient.GetObject(ctx, &storage.GetObjectInput{
			Bucket: storage.GetBucket(attachmentURL),
			Key:    attachmentURL,
		})
		require.NoError(t, err)

		buf := new(bytes.Buffer)
		_, err = buf.ReadFrom(outputObj.Body)
		require.NoError(t, err)
		assert.Equal(t, "Hello Attachment", buf.String())
	})

	t.Run("invalid JSON in main run payload", func(t *testing.T) {
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		invalidJSON := []byte(`{"id":"1234","trace_id":"some-trace-id","run_type":"llm"`)

		header := textproto.MIMEHeader{}
		header.Set("Content-Type", "application/json")
		header.Set("Content-Disposition", `form-data; name="post.1234"`)
		header.Set("Content-Length", fmt.Sprintf("%d", len(invalidJSON)))
		part, err := writer.CreatePart(header)
		require.NoError(t, err)
		_, err = part.Write(invalidJSON)
		require.NoError(t, err)

		// Close writer
		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())

		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code,
			"Expected 422 for invalid JSON, got %d", wRec.Code)
		respBody := wRec.Body.String()
		assert.Contains(t, respBody, "invalid JSON part", "Should mention invalid json in error message")
	})

	t.Run("session ID does not exist in DB", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4())
		ghostSessionID := uuid.Must(uuid.NewV4()).String()

		mainRun := map[string]interface{}{
			"id":           runID.String(),
			"name":         "test run",
			"trace_id":     uuid.Must(uuid.NewV4()).String(),
			"session_id":   ghostSessionID, // Nonexistent
			"run_type":     "llm",
			"start_time":   "2025-01-01T10:00:00Z",
			"dotted_order": "20250101T100000Z" + runID.String(),
		}
		mainRunJSON, err := json.Marshal(mainRun)
		require.NoError(t, err)

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		header := textproto.MIMEHeader{}
		header.Set("Content-Type", "application/json")
		header.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		header.Set("Content-Length", fmt.Sprintf("%d", len(mainRunJSON)))
		part, err := writer.CreatePart(header)
		require.NoError(t, err)
		_, err = part.Write(mainRunJSON)
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())

		assert.Equal(t, http.StatusNotFound, wRec.Code,
			"Expected 404 for nonexistent session ID, got %d", wRec.Code)
		respBody := wRec.Body.String()
		assert.Contains(t, respBody, "tracer session", "Should mention tracer session not found or similar")
	})

	t.Run("missing dotted_order", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4())

		mainRun := map[string]interface{}{
			"id":         runID.String(),
			"name":       "test run",
			"trace_id":   uuid.Must(uuid.NewV4()).String(),
			"run_type":   "llm",
			"start_time": "2025-01-01T10:00:00Z",
		}
		mainRunJSON, err := json.Marshal(mainRun)
		require.NoError(t, err)

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		header := textproto.MIMEHeader{}
		header.Set("Content-Type", "application/json")
		header.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		header.Set("Content-Length", fmt.Sprintf("%d", len(mainRunJSON)))
		part, err := writer.CreatePart(header)
		require.NoError(t, err)
		_, err = part.Write(mainRunJSON)
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())

		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code,
			"Should return 422 for missing dotted_order")
		respBody := wRec.Body.String()
		assert.Contains(t, respBody, "missing 'dotted_order'", "Should mention missing dotted_order")
	})

	t.Run("empty batch", func(t *testing.T) {
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)
		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "empty batch")
	})

	t.Run("invalid part name", func(t *testing.T) {
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		content := []byte(`{"foo":"bar"}`)
		header := textproto.MIMEHeader{}
		header.Set("Content-Type", "application/json")
		header.Set("Content-Disposition", `form-data; name="invalidpartname"`)
		header.Set("Content-Length", fmt.Sprintf("%d", len(content)))
		part, err := writer.CreatePart(header)
		require.NoError(t, err)
		_, err = part.Write(content)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "invalid part name")
	})

	t.Run("missing api key", func(t *testing.T) {
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		content := []byte(`{"id":"1111","trace_id":"2222","run_type":"llm","dotted_order":"test_order"}`)
		header := textproto.MIMEHeader{}
		header.Set("Content-Type", "application/json")
		header.Set("Content-Disposition", `form-data; name="post.1111"`)
		header.Set("Content-Length", fmt.Sprintf("%d", len(content)))
		part, err := writer.CreatePart(header)
		require.NoError(t, err)
		_, err = part.Write(content)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("X-Api-Key", "")

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)
		require.Equal(t, http.StatusUnauthorized, wRec.Code)
	})

	t.Run("duplicate run payload", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		traceID := runID
		postRun := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","trace_id":"%s","run_type":"llm","dotted_order":"%s"}`, runID, traceID, dottedOrder))

		firstBuf := &bytes.Buffer{}
		firstWriter := multipart.NewWriter(firstBuf)
		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", "application/json")
		hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		hdr.Set("Content-Length", fmt.Sprintf("%d", len(postRun)))
		p1, err := firstWriter.CreatePart(hdr)
		require.NoError(t, err)
		_, err = p1.Write(postRun)
		require.NoError(t, err)
		require.NoError(t, firstWriter.Close())
		firstResp := doMultipartRequest(t, firstBuf, firstWriter.FormDataContentType())
		require.Equal(t, http.StatusAccepted, firstResp.Code)

		secondBuf := &bytes.Buffer{}
		secondWriter := multipart.NewWriter(secondBuf)
		p2Hdr := textproto.MIMEHeader{}
		p2Hdr.Set("Content-Type", "application/json")
		p2Hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		p2Hdr.Set("Content-Length", fmt.Sprintf("%d", len(postRun)))
		p2, err := secondWriter.CreatePart(p2Hdr)
		require.NoError(t, err)
		_, err = p2.Write(postRun)
		require.NoError(t, err)
		require.NoError(t, secondWriter.Close())
		secondResp := doMultipartRequest(t, secondBuf, secondWriter.FormDataContentType())
		require.Equal(t, http.StatusConflict, secondResp.Code)
		assert.Contains(t, secondResp.Body.String(), "already received")
	})

	t.Run("attempt to upload large file beyond limit", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()

		traceID := uuid.Must(uuid.NewV4())
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		payload := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","trace_id":"%s","run_type":"chain","dotted_order":"%s"}`, runID, traceID, dottedOrder))
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		mainHdr := textproto.MIMEHeader{}
		mainHdr.Set("Content-Type", "application/json")
		mainHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		mainHdr.Set("Content-Length", fmt.Sprintf("%d", len(payload)))
		mainPart, err := writer.CreatePart(mainHdr)
		require.NoError(t, err)
		_, err = mainPart.Write(payload)
		require.NoError(t, err)

		largeData := bytes.Repeat([]byte("A"), 50*1024*1024+1)
		attachHdr := textproto.MIMEHeader{}
		attachHdr.Set("Content-Type", "application/octet-stream")
		attachHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="attachment.%s.largefile"`, runID))
		attachHdr.Set("Content-Length", fmt.Sprintf("%d", len(largeData)))
		attachPart, err := writer.CreatePart(attachHdr)
		require.NoError(t, err)
		_, err = attachPart.Write(largeData)
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "exceeds limit")
	})

	t.Run("multiple attachments", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()

		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		payload := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","trace_id":"%s","run_type":"chain","dotted_order":"%s"}`, runID, runID, dottedOrder))
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		mainHdr := textproto.MIMEHeader{}
		mainHdr.Set("Content-Type", "application/json")
		mainHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		mainHdr.Set("Content-Length", fmt.Sprintf("%d", len(payload)))
		mainPart, err := writer.CreatePart(mainHdr)
		require.NoError(t, err)
		_, err = mainPart.Write(payload)
		require.NoError(t, err)

		for i := 1; i <= 2; i++ {
			content := []byte(fmt.Sprintf("Attachment #%d", i))
			attachHdr := textproto.MIMEHeader{}
			attachHdr.Set("Content-Type", "application/octet-stream")
			attachHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="attachment.%s.file%d"`, runID, i))
			attachHdr.Set("Content-Length", fmt.Sprintf("%d", len(content)))
			part, err := writer.CreatePart(attachHdr)
			require.NoError(t, err)
			_, err = part.Write(content)
			require.NoError(t, err)
		}

		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusAccepted, wRec.Code)
	})

	t.Run("out-of-order inputs before main run payload", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		inputs := []byte(`{"prompt":"hello?"}`)
		inHdr := textproto.MIMEHeader{}
		inHdr.Set("Content-Type", "application/json")
		inHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.inputs"`, runID))
		inHdr.Set("Content-Length", fmt.Sprintf("%d", len(inputs)))
		inPart, err := writer.CreatePart(inHdr)
		require.NoError(t, err)
		_, err = inPart.Write(inputs)
		require.NoError(t, err)

		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		payload := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","trace_id":"%s","run_type":"chain","dotted_order":"%s"}`, runID, runID, dottedOrder))
		mainHdr := textproto.MIMEHeader{}
		mainHdr.Set("Content-Type", "application/json")
		mainHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		mainHdr.Set("Content-Length", fmt.Sprintf("%d", len(payload)))
		mainPart, err := writer.CreatePart(mainHdr)
		require.NoError(t, err)
		_, err = mainPart.Write(payload)
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusAccepted, wRec.Code)

		ctx := context.Background()
		extrasKey := fmt.Sprintf("smith:runs:pending:%s:%s:extra", tenantID, runID)

		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
		inputVal, err := routedPool.HGet(ctx, extrasKey, "inputs").Result()
		require.Error(t, err)
		assert.Equal(t, redis.Nil, err)
		assert.Empty(t, inputVal)
	})

	t.Run("patch referencing session that doesn't exist", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		sessionID := uuid.Must(uuid.NewV4()).String()
		traceID := uuid.Must(uuid.NewV4())
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", traceID)

		patchPayload := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","session_id":"%s","trace_id":"%s","run_type":"llm","dotted_order":"%s"}`, runID, sessionID, traceID, dottedOrder))
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		h := textproto.MIMEHeader{}
		h.Set("Content-Type", "application/json")
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="patch.%s"`, runID))
		h.Set("Content-Length", fmt.Sprintf("%d", len(patchPayload)))
		part, err := writer.CreatePart(h)
		require.NoError(t, err)
		_, err = part.Write(patchPayload)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusNotFound, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "tracer session")
	})

	t.Run("unsupported content encoding", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()

		traceID := uuid.Must(uuid.NewV4())
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", traceID)
		payload := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","trace_id":"%s","run_type":"chain","dotted_order":"%s"}`, runID, traceID, dottedOrder))
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		h := textproto.MIMEHeader{}
		h.Set("Content-Type", "application/json")
		h.Set("Content-Encoding", "br")
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		h.Set("Content-Length", fmt.Sprintf("%d", len(payload)))
		part, err := writer.CreatePart(h)
		require.NoError(t, err)
		_, err = part.Write(payload)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusUnsupportedMediaType, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "Unsupported Content-Encoding")
	})

	t.Run("invalid content type", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()

		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)

		payload := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","trace_id":"%s","run_type":"chain","dotted_order":"%s"}`, runID, runID, dottedOrder))
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		h := textproto.MIMEHeader{}
		h.Set("Content-Type", "aplication@@json")
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		h.Set("Content-Length", fmt.Sprintf("%d", len(payload)))
		part, err := writer.CreatePart(h)
		require.NoError(t, err)
		_, err = part.Write(payload)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
	})

	t.Run("only feedback", func(t *testing.T) {

		runID := uuid.Must(uuid.FromString("d6d06bc4-7537-4dff-a8e1-b8136ce96d70"))
		traceID := runID.String()

		// Feedback
		feedbackPayload := struct {
			ID      string `json:"id"`
			TraceID string `json:"trace_id"`
			Key     string `json:"key"`
			Value   string `json:"value"`
			Score   int    `json:"score"`
			Comment string `json:"comment"`
			RunID   string `json:"run_id"`
		}{
			ID:      runID.String(),
			TraceID: traceID,
			Key:     "my_key",
			Value:   "my_value",
			Score:   10,
			Comment: "this worked great",
			RunID:   runID.String(),
		}
		feedbackJSON, err := json.Marshal(feedbackPayload)
		require.NoError(t, err)

		// Build the multipart body
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		parts := []struct {
			fieldName string
			content   []byte
			mimeType  string
		}{
			// Feedback
			{fmt.Sprintf("feedback.%s", runID), feedbackJSON, "application/json"},
		}

		for _, p := range parts {
			header := textproto.MIMEHeader{}
			header.Set("Content-Type", p.mimeType)
			header.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, p.fieldName))
			header.Set("Content-Length", fmt.Sprintf("%d", len(p.content)))
			part, err := writer.CreatePart(header)
			require.NoError(t, err)
			_, err = part.Write(p.content)
			require.NoError(t, err)
		}

		require.NoError(t, writer.Close())

		// Send the request
		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)

		resp := wRec.Result()
		defer resp.Body.Close()

		require.Equal(t, http.StatusAccepted, resp.StatusCode, "Expected HTTP 202 Accepted")

		ctx := context.Background()

		fbKey := fmt.Sprintf("smith:runs:feedback:%s:%s", tenantID, runID)

		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
		fbSet, err := routedPool.SMembers(ctx, fbKey).Result()
		require.NoError(t, err)
		require.Len(t, fbSet, 1)
		var fbPayload map[string]interface{}
		err = json.Unmarshal([]byte(fbSet[0]), &fbPayload)
		require.NoError(t, err)
		assert.Equal(t, "my_key", fbPayload["key"])
		assert.Equal(t, float64(10), fbPayload["score"])
		assert.Equal(t, "this worked great", fbPayload["comment"])
	})

	t.Run("zstd compressed request", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		traceID := runID
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)

		var origBody bytes.Buffer
		writer := multipart.NewWriter(&origBody)

		payload := []byte(fmt.Sprintf(`{
        "id": "%s",
        "trace_id": "%s",
        "name": "zstd test",
        "run_type": "llm",
        "dotted_order": "%s"
    }`, runID, traceID, dottedOrder))

		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", "application/json")
		hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		hdr.Set("Content-Length", fmt.Sprintf("%d", len(payload)))
		part, err := writer.CreatePart(hdr)
		require.NoError(t, err)
		_, err = part.Write(payload)
		require.NoError(t, err)
		writer.Close()

		var compressedBody bytes.Buffer
		zw := zstd.NewWriter(&compressedBody)
		_, err = zw.Write(origBody.Bytes())
		require.NoError(t, err)
		require.NoError(t, zw.Close())

		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &compressedBody)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("Content-Encoding", "zstd")
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)

		resp := wRec.Result()
		defer resp.Body.Close()
		require.Equal(t, http.StatusAccepted, resp.StatusCode)

		ctx := context.Background()
		pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", tenantID, runID)

		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
		exists, err := routedPool.Exists(ctx, pendingKey).Result()
		require.NoError(t, err)
		require.Equal(t, int64(1), exists, "Run should be pending processing")
	})

	t.Run("missing trace id", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		payload := []byte(fmt.Sprintf(`{"id":"%s","name":"test run","run_type":"llm", "dotted_order":"20230505T051324571809Z%s"}`, runID, runID))
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		hdr := textproto.MIMEHeader{}
		hdr.Set("Content-Type", "application/json")
		hdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		hdr.Set("Content-Length", fmt.Sprintf("%d", len(payload)))
		part, err := writer.CreatePart(hdr)
		require.NoError(t, err)
		_, err = part.Write(payload)
		require.NoError(t, err)
		writer.Close()

		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)

		resp := wRec.Result()
		defer resp.Body.Close()
		require.Equal(t, http.StatusUnprocessableEntity, resp.StatusCode)
		assert.Contains(t, wRec.Body.String(), "trace_id cannot be nil")
	})

	t.Run("legacy user agent with large out-of-band data triggers in-memory read and S3 upload", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)

		mainPayload := []byte(fmt.Sprintf(
			`{"id":"%s","name":"large-data-run","trace_id":"%s","run_type":"llm","dotted_order":"%s"}`,
			runID, runID, dottedOrder,
		))

		// Create large data that exceeds config.Env.MinBlobStorageSizeKb * 1024 (triggering direct S3 path).
		// For safety, use ~300KB, which is bigger than common default minBlobStorageSizeKb (256).
		largeData := bytes.Repeat([]byte("X"), 300*1024)

		// Build the multipart body
		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		mainHdr := textproto.MIMEHeader{}
		mainHdr.Set("Content-Type", "application/json")
		mainHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		mainHdr.Set("Content-Length", fmt.Sprintf("%d", len(mainPayload)))
		mainPart, err := writer.CreatePart(mainHdr)
		require.NoError(t, err)
		_, err = mainPart.Write(mainPayload)
		require.NoError(t, err)

		inputsHdr := textproto.MIMEHeader{}
		inputsHdr.Set("Content-Type", "application/json")
		inputsHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.inputs"`, runID))
		inputsHdr.Set("Content-Length", fmt.Sprintf("%d", len(largeData)-1)) // Set wrong length
		inputsPart, err := writer.CreatePart(inputsHdr)
		require.NoError(t, err)
		_, err = inputsPart.Write(largeData)
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		// Make the request with a legacy langsmith-js User-Agent
		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("User-Agent", "langsmith-js/0.3.4") // legacy version
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)

		resp := wRec.Result()
		defer resp.Body.Close()

		require.Equal(t, http.StatusAccepted, resp.StatusCode)

		ctx := context.Background()
		extrasKey := fmt.Sprintf("smith:runs:pending:%s:%s:extra", tenantID, runID)

		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
		inputVal, err := routedPool.HGet(ctx, extrasKey, "inputs_s3_url").Result()
		require.NoError(t, err, "Failed to get out-of-band input from Redis extras")

		require.NotEmpty(t, inputVal, "Expected an S3 key for large out-of-band input")
		slog.Info("inputVal", "inputVal", inputVal)
		assert.True(t,
			strings.Contains(inputVal, "ttl_s/multipart/"),
			"S3 path in redis extras should contain ttl_s/multipart/",
		)

		basePath, _, _, _, startOffset, endOffset, err := parseS3Url(inputVal)
		if err != nil {
			t.Fatalf("Failed to parse S3 URL: %v", err)
		}

		obj, err := storageClient.GetObject(ctx, &storage.GetObjectInput{
			Bucket: storage.GetBucket(inputVal),
			Key:    basePath,
			Range: &storage.Range{
				Start: startOffset,
				End:   endOffset,
			},
		})
		require.NoError(t, err, "S3 GetObject should succeed for the large data")

		defer obj.Body.Close()
		buf := new(bytes.Buffer)
		_, err = buf.ReadFrom(obj.Body)
		require.NoError(t, err)
		assert.Equal(t, largeData, buf.Bytes(), "Fetched data from S3 should match what we uploaded")
	})

	t.Run("invalid session id format", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		payload := map[string]interface{}{
			"id":           runID,
			"name":         "test run with invalid session id format",
			"trace_id":     uuid.Must(uuid.NewV4()).String(),
			"session_id":   uuid.Must(uuid.NewV4()).String(),
			"session_name": "test session",
			"start_time":   "2025-01-01T10:00:00Z",
			"run_type":     "llm",
			"dotted_order": "20250101T100000Z" + runID,
		}
		payloadJSON, err := json.Marshal(payload)
		require.NoError(t, err)

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)
		partName := fmt.Sprintf("post.%s", runID)
		header := textproto.MIMEHeader{}
		header.Set("Content-Type", "application/json")
		header.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, partName))
		header.Set("Content-Length", fmt.Sprintf("%d", len(payloadJSON)))
		part, err := writer.CreatePart(header)
		require.NoError(t, err)
		_, err = part.Write(payloadJSON)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)

		require.Equal(t, http.StatusNotFound, wRec.Code, "Expected 404 for invalid session id format")
		assert.Contains(t, wRec.Body.String(), "tracer session", "Error message should mention tracer session not found")
	})

	t.Run("invalid start time", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		payload := map[string]interface{}{
			"id":           runID,
			"name":         "test run with invalid start time",
			"trace_id":     uuid.Must(uuid.NewV4()).String(),
			"session_name": "test session",
			"start_time":   "not-a-valid-timestamp",
			"run_type":     "llm",
			"dotted_order": "20250101T100000Z" + runID,
		}
		payloadJSON, err := json.Marshal(payload)
		require.NoError(t, err)

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)
		partName := fmt.Sprintf("post.%s", runID)
		header := textproto.MIMEHeader{}
		header.Set("Content-Type", "application/json")
		header.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, partName))
		header.Set("Content-Length", fmt.Sprintf("%d", len(payloadJSON)))
		part, err := writer.CreatePart(header)
		require.NoError(t, err)
		_, err = part.Write(payloadJSON)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsMultipart)
		handlerWithAuth.ServeHTTP(wRec, req)

		require.Equal(t, http.StatusUnprocessableEntity, wRec.Code, "Expected 422 for invalid start time")
		assert.Contains(t, wRec.Body.String(), "invalid timestamp format", "Error message should indicate invalid timestamp format")
	})
	t.Run("batch uploading: uploader triggered for subsequent parts", func(t *testing.T) {
		oldMinSize := config.Env.MinBlobStorageSizeKb
		config.Env.MinBlobStorageSizeKb = 10
		defer func() { config.Env.MinBlobStorageSizeKb = oldMinSize }()

		runID := uuid.Must(uuid.NewV4()).String()
		mainRunPayload := map[string]interface{}{
			"id":           runID,
			"name":         "batch uploader test run",
			"trace_id":     runID,
			"run_type":     "llm",
			"dotted_order": "20230505T051324571809Z" + runID,
			"start_time":   1620000000,
			"end_time":     "2024-09-17T18:17:17.361909Z",
		}
		mainPayloadJSON, err := json.Marshal(mainRunPayload)
		require.NoError(t, err)

		// Create a large payload (300KB) to trigger the uploader branch.
		largeData := bytes.Repeat([]byte("A"), 11*1024)
		// Also add a subsequent small payload that should be uploaded via uploader as well.
		mediumData := bytes.Repeat([]byte("B"), 4*1024)
		smallData := []byte("small inline data")

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		// Main run part.
		mainHdr := textproto.MIMEHeader{}
		mainHdr.Set("Content-Type", "application/json")
		mainHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		mainHdr.Set("Content-Length", fmt.Sprintf("%d", len(mainPayloadJSON)))
		mainPart, err := writer.CreatePart(mainHdr)
		require.NoError(t, err)
		_, err = mainPart.Write(mainPayloadJSON)
		require.NoError(t, err)

		// Out-of-band "inputs" field with large data to trigger switching.
		inputsHdr := textproto.MIMEHeader{}
		inputsHdr.Set("Content-Type", "application/json")
		inputsHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.inputs"`, runID))
		inputsHdr.Set("Content-Length", fmt.Sprintf("%d", len(largeData)))
		inputsPart, err := writer.CreatePart(inputsHdr)
		require.NoError(t, err)
		_, err = inputsPart.Write(largeData)
		require.NoError(t, err)

		// Out-of-band "outputs" field with small data; although under threshold,
		// it should use the uploader since the global flag is triggered.
		outputsHdr := textproto.MIMEHeader{}
		outputsHdr.Set("Content-Type", "application/json")
		outputsHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.outputs"`, runID))
		outputsHdr.Set("Content-Length", fmt.Sprintf("%d", len(mediumData)))
		outputsPart, err := writer.CreatePart(outputsHdr)
		require.NoError(t, err)
		_, err = outputsPart.Write(mediumData)
		require.NoError(t, err)

		// Out-of-band "error" field with small data; under regular threadhols and multipart threshold.
		// So should be inline.
		errorHdr := textproto.MIMEHeader{}
		errorHdr.Set("Content-Type", "application/json")
		errorHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.error"`, runID))
		errorHdr.Set("Content-Length", fmt.Sprintf("%d", len(smallData)))
		errorPart, err := writer.CreatePart(errorHdr)
		require.NoError(t, err)
		_, err = errorPart.Write(smallData)
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusAccepted, wRec.Code)

		ctx := context.Background()
		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
		extrasKey := fmt.Sprintf("smith:runs:pending:%s:%s:extra", tenantID, runID)
		inputsS3Val, err := routedPool.HGet(ctx, extrasKey, "inputs_s3_url").Result()
		require.NoError(t, err, "Failed to get out-of-band input from Redis extras")
		require.NotEmpty(t, inputsS3Val, "Expected an S3 key for large out-of-band input")

		outputsS3Val, err := routedPool.HGet(ctx, extrasKey, "outputs_s3_url").Result()
		require.NoError(t, err, "Failed to get out-of-band output from Redis extras")
		require.NotEmpty(t, outputsS3Val, "Expected an S3 key for out-of-band output")

		errorS3Val, err := routedPool.HGet(ctx, extrasKey, "error").Result()
		require.NoError(t, err, "Failed to get out-of-band error from Redis extras")
		require.NotEmpty(t, errorS3Val, "Expected error to be stored inline")

		assert.Contains(t, inputsS3Val, "ttl_s/multipart/", "S3 path for inputs should contain ttl_s/multipart/")
		assert.Contains(t, outputsS3Val, "ttl_s/multipart/", "S3 path for outputs should contain ttl_s/multipart/")
		assert.Equal(t, string(smallData), string(errorS3Val), "Error should be stored inline")

		basePath, hash1, hash2, uuid, startOffset, endOffset, err := parseS3Url(inputsS3Val)
		if err != nil {
			t.Fatalf("Failed to parse S3 URL: %v", err)
		}

		// Verify the parsed components
		assert.Contains(t, basePath, "ttl_s/multipart/", "Base path should contain ttl_s/multipart/")
		assert.NotEmpty(t, hash1, "Hash1 should not be empty")
		assert.NotEmpty(t, hash2, "Hash2 should not be empty")
		assert.NotEmpty(t, uuid, "UUID should not be empty")
		assert.GreaterOrEqual(t, endOffset, startOffset, "End offset should be greater than or equal to start offset")

		// Verify the uploaded inputs with HeadObject
		require.NoError(t, err, "Failed to verify uploaded inputs object")

		// Verify the specific chunk of inputs using the offsets
		getChunkObj, err := storageClient.GetObject(ctx, &storage.GetObjectInput{
			Bucket: storage.GetBucket(basePath),
			Key:    basePath,
			Range: &storage.Range{
				Start: startOffset,
				End:   endOffset,
			},
		})
		require.NoError(t, err, "Failed to get chunk from S3")
		defer getChunkObj.Body.Close()

		chunkData, err := io.ReadAll(getChunkObj.Body)
		require.NoError(t, err, "Failed to read chunk data")

		// Verify the chunk content matches the original data
		assert.Equal(t, largeData, chunkData, "Chunk content mismatch")

		_, _, _, _, startOffset, endOffset, err = parseS3Url(outputsS3Val)
		if err != nil {
			t.Fatalf("Failed to parse S3 URL: %v", err)
		}

		// Download the entire inputs object to verify content
		getObj, err := storageClient.GetObject(ctx, &storage.GetObjectInput{
			Bucket: storage.GetBucket(basePath),
			Key:    basePath,
			Range: &storage.Range{
				Start: startOffset,
				End:   endOffset,
			},
		})
		require.NoError(t, err, "Failed to get uploaded inputs object")
		defer getObj.Body.Close()

		downloadedData, err := io.ReadAll(getObj.Body)
		require.NoError(t, err, "Failed to read downloaded inputs object")

		assert.Equal(t, mediumData, downloadedData, "Downloaded inputs content mismatch")

	})

	t.Run("batch uploading: all parts inline when none exceeds threshold", func(t *testing.T) {
		oldMinSize := config.Env.MinBlobStorageSizeKb
		config.Env.MinBlobStorageSizeKb = 256
		defer func() { config.Env.MinBlobStorageSizeKb = oldMinSize }()

		runID := uuid.Must(uuid.NewV4()).String()
		mainRunPayload := map[string]interface{}{
			"id":           runID,
			"name":         "all inline test run",
			"trace_id":     runID,
			"run_type":     "llm",
			"dotted_order": "20230505T051324571809Z" + runID,
			"start_time":   1620000000,
			"end_time":     "2024-09-17T18:17:17.361909Z",
		}
		mainPayloadJSON, err := json.Marshal(mainRunPayload)
		require.NoError(t, err)

		// Create only small payloads, both well below the threshold.
		smallData1 := []byte("small data for inputs")
		smallData2 := []byte("small data for outputs")

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		// Main run part.
		mainHdr := textproto.MIMEHeader{}
		mainHdr.Set("Content-Type", "application/json")
		mainHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		mainHdr.Set("Content-Length", fmt.Sprintf("%d", len(mainPayloadJSON)))
		mainPart, err := writer.CreatePart(mainHdr)
		require.NoError(t, err)
		_, err = mainPart.Write(mainPayloadJSON)
		require.NoError(t, err)

		// Out-of-band "inputs" field with small inline data.
		inputsHdr := textproto.MIMEHeader{}
		inputsHdr.Set("Content-Type", "application/json")
		inputsHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.inputs"`, runID))
		inputsHdr.Set("Content-Length", fmt.Sprintf("%d", len(smallData1)))
		inputsPart, err := writer.CreatePart(inputsHdr)
		require.NoError(t, err)
		_, err = inputsPart.Write(smallData1)
		require.NoError(t, err)

		// Out-of-band "outputs" field with small inline data.
		outputsHdr := textproto.MIMEHeader{}
		outputsHdr.Set("Content-Type", "application/json")
		outputsHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.outputs"`, runID))
		outputsHdr.Set("Content-Length", fmt.Sprintf("%d", len(smallData2)))
		outputsPart, err := writer.CreatePart(outputsHdr)
		require.NoError(t, err)
		_, err = outputsPart.Write(smallData2)
		require.NoError(t, err)

		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())
		require.Equal(t, http.StatusAccepted, wRec.Code)

		ctx := context.Background()
		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
		extrasKey := fmt.Sprintf("smith:runs:pending:%s:%s:extra", tenantID, runID)
		inputsVal, err := routedPool.HGet(ctx, extrasKey, "inputs").Result()
		require.NoError(t, err)
		outputsVal, err := routedPool.HGet(ctx, extrasKey, "outputs").Result()
		require.NoError(t, err)

		// In the inline case, no uploader markers (e.g. no '#' characters) should be present.
		assert.NotContains(t, inputsVal, "#", "inputs field should be stored inline without uploader markers")
		assert.Equal(t, string(smallData1), string(inputsVal), "inputs field inline data should match")
		assert.NotContains(t, outputsVal, "#", "outputs field should be stored inline without uploader markers")
		assert.Equal(t, string(smallData2), string(outputsVal), "outputs field inline data should match")
	})

	t.Run("error before multipart upload completes", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		mainRunPayload := map[string]interface{}{
			"id":           runID,
			"name":         "error test run",
			"trace_id":     runID,
			"run_type":     "llm",
			"dotted_order": "20230505T051324571809Z" + runID,
			"start_time":   1620000000,
			"end_time":     "2024-09-17T18:17:17.361909Z",
		}
		mainPayloadJSON, err := json.Marshal(mainRunPayload)
		require.NoError(t, err)

		// Create large data to trigger multipart upload
		largeData := bytes.Repeat([]byte("A"), 300*1024)

		var bodyBuf bytes.Buffer
		writer := multipart.NewWriter(&bodyBuf)

		// Main run part
		mainHdr := textproto.MIMEHeader{}
		mainHdr.Set("Content-Type", "application/json")
		mainHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		mainHdr.Set("Content-Length", fmt.Sprintf("%d", len(mainPayloadJSON)))
		mainPart, err := writer.CreatePart(mainHdr)
		require.NoError(t, err)
		_, err = mainPart.Write(mainPayloadJSON)
		require.NoError(t, err)

		// Large data part that will trigger multipart upload
		inputsHdr := textproto.MIMEHeader{}
		inputsHdr.Set("Content-Type", "application/json")
		inputsHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.inputs"`, runID))
		inputsHdr.Set("Content-Length", fmt.Sprintf("%d", len(largeData)))
		inputsPart, err := writer.CreatePart(inputsHdr)
		require.NoError(t, err)
		_, err = inputsPart.Write(largeData)
		require.NoError(t, err)

		outputsHdr := textproto.MIMEHeader{}
		outputsJson := []byte(`{"outputs": "invalid"}`)
		outputsHdr.Set("Content-Type", "application/json")
		outputsHdr.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s.invalid"`, runID))
		outputsHdr.Set("Content-Length", fmt.Sprintf("%d", len(outputsJson)))
		outputsPart, err := writer.CreatePart(outputsHdr)
		require.NoError(t, err)
		_, err = outputsPart.Write(outputsJson)
		require.NoError(t, err)
		require.NoError(t, writer.Close())

		wRec := doMultipartRequest(t, &bodyBuf, writer.FormDataContentType())

		require.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
	})

	t.Run("test extra map ordering", func(t *testing.T) {
		oldTenants := config.Env.FFOrderingMultipartEnabledTenants
		config.Env.FFOrderingMultipartEnabledTenants = config.List{SplitList: []string{"*"}}
		defer func() { config.Env.FFOrderingMultipartEnabledTenants = oldTenants }()

		runID := uuid.Must(uuid.NewV4()).String()
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		mainJSON := `
			{
				"id": "` + runID + `",
				"name": "extra map ordering test run",
				"trace_id": "` + runID + `",
				"run_type": "llm",
				"dotted_order": "` + dottedOrder + `",
				"extra": {
					"beta": 1,
					"gamma": 3,
					"alpha": 2
				}
			}
		`

		mainJSONBytes := []byte(mainJSON)

		var bodyBuf bytes.Buffer
		w := multipart.NewWriter(&bodyBuf)

		h := textproto.MIMEHeader{}
		h.Set("Content-Type", "application/json")
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="post.%s"`, runID))
		h.Set("Content-Length", fmt.Sprintf("%d", len(mainJSON)))
		part, err := w.CreatePart(h)
		require.NoError(t, err)
		_, err = part.Write(mainJSONBytes)
		require.NoError(t, err)
		require.NoError(t, w.Close())

		req := httptest.NewRequest(http.MethodPost, "/v1/runs/multipart", &bodyBuf)
		req.Header.Set("Content-Type", w.FormDataContentType())
		req.Header.Set("X-Api-Key", apiKey)

		rec := httptest.NewRecorder()
		alice.New(testutil.TestLogger(t), ah.Middleware).
			ThenFunc(handler.IngestRunsMultipart).
			ServeHTTP(rec, req)

		require.Equal(t, http.StatusAccepted, rec.Code)

		ctx := context.Background()
		pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", tenantID, runID)
		extrasKey := pendingKey + ":extra"

		routedPool := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)
		raw, err := routedPool.HGet(ctx, extrasKey, "extra").Result()
		require.NoError(t, err)

		assert.Equal(t, `{"beta":1,"gamma":3,"alpha":2}`, raw)

		beta := strings.Index(raw, `"beta"`)
		gamma := strings.Index(raw, `"gamma"`)
		alpha := strings.Index(raw, `"alpha"`)
		require.NotEqual(t, -1, beta)
		require.NotEqual(t, -1, gamma)
		require.NotEqual(t, -1, alpha)
		assert.True(t, beta < gamma && gamma < alpha, "key order beta<gamma<alpha not preserved")
	})
}
