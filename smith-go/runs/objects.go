package runs

import (
	"context"
	"errors"
	"io"
	"log/slog"
	"mime"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"

	"langchain.com/smith/storage"

	"github.com/ggicci/httpin"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/jwtauth/v5"
	"github.com/go-chi/render"
	"langchain.com/smith/config"
)

type DownloadObjectReq struct {
	Path       string `in:"query=path;nonzero"`
	DownloadAs string `in:"query=download_as"`
	Start      *int   `in:"query=start"`
	End        *int   `in:"query=end"`
}

type Response struct {
	Message string `json:"message"`
}

type RunObjectsHandler struct {
	Client storage.StorageClient
	Jwt    *jwtauth.JWTAuth
}

type CopyObjectRequest struct {
	Payload CopyObjectPayload `in:"body"`
}

type CopyObjectPayload struct {
	SourceKey string `json:"source_key"`
	DestKey   string `json:"dest_key"`
}

func NewRunObjectsHandler(client storage.StorageClient) *RunObjectsHandler {
	return &RunObjectsHandler{
		Client: client,
		Jwt:    jwtauth.New("HS256", []byte(config.Env.ApiKeySalt), nil),
	}
}

func (h *RunObjectsHandler) DownloadObjectPublic(w http.ResponseWriter, r *http.Request) {
	tok, err := jwtauth.VerifyRequest(h.Jwt, r, jwtauth.TokenFromQuery)
	if err != nil {
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, Response{Message: "Unauthorized"})
		return
	}
	path, ok := tok.Get("path")
	if !ok {
		render.Status(r, http.StatusUnprocessableEntity)
		render.JSON(w, r, Response{Message: "Missing path claim"})
		return
	}
	pathStr, ok := path.(string)
	if !ok {
		render.Status(r, http.StatusUnprocessableEntity)
		render.JSON(w, r, Response{Message: "Invalid path claim"})
		return
	}

	// Check if path contains range information (#start-end)
	var start, end *int
	if strings.Contains(pathStr, "#") {
		parts := strings.Split(pathStr, "#")
		pathStr = parts[0]
		if len(parts) > 1 && strings.Contains(parts[1], "-") {
			rangeParts := strings.Split(parts[1], "-")
			if len(rangeParts) == 2 {
				startVal, startErr := strconv.Atoi(rangeParts[0])
				endVal, endErr := strconv.Atoi(rangeParts[1])
				if startErr == nil && endErr == nil {
					start = &startVal
					end = &endVal
				}
			} else {
				oplog := httplog.LogEntry(r.Context())
				oplog.Warn("Invalid range format", "range", parts[1])
				render.Status(r, http.StatusBadRequest)
				render.JSON(w, r, Response{Message: "Invalid range format, expected start-end"})
				return
			}
		} else {
			oplog := httplog.LogEntry(r.Context())
			oplog.Warn("Invalid range format", "range", parts[1])
			render.Status(r, http.StatusBadRequest)
			render.JSON(w, r, Response{Message: "Invalid range format, expected start-end"})
			return
		}
	}

	h.downloadPath(w, r, &DownloadObjectReq{
		Path:       pathStr,
		DownloadAs: r.URL.Query().Get("download_as"),
		Start:      start,
		End:        end,
	})
}

func (h *RunObjectsHandler) DownloadObjectInternal(w http.ResponseWriter, r *http.Request) {
	req := r.Context().Value(httpin.Input).(*DownloadObjectReq)
	h.downloadPath(w, r, req)
}

func (h *RunObjectsHandler) downloadPath(w http.ResponseWriter, r *http.Request, req *DownloadObjectReq) {
	oplog := httplog.LogEntry(r.Context())

	// validate object path
	parts := strings.Split(req.Path, "/")
	if len(parts) < 2 {
		render.Status(r, http.StatusUnprocessableEntity)
		render.JSON(w, r, Response{Message: "Invalid object path"})
		return
	}

	if r.Method == http.MethodHead {
		// head object, get metadata
		bucket := storage.GetBucket(req.Path)
		o, err := h.Client.HeadObject(r.Context(), &storage.HeadObjectInput{
			Bucket:          bucket,
			ContentEncoding: r.Header.Get("Content-Encoding"),
			Key:             req.Path,
		})
		if err != nil {
			// reply with 404 if object not found
			if errors.Is(err, storage.ErrNotFound) {
				render.Status(r, http.StatusNotFound)
				render.JSON(w, r, Response{Message: "Object not found"})
				return
			}
			// reply with 500 if any other error
			oplog.Warn("Failed to head object", "err", err)
			render.Status(r, http.StatusInternalServerError)
			render.JSON(w, r, Response{Message: "Failed to head object"})
			return
		}
		// reply with 200 if object exists
		if o.ContentType != nil {
			w.Header().Set("Content-Type", *o.ContentType)
		}
		if o.ContentEncoding != nil {
			w.Header().Set("Content-Encoding", *o.ContentEncoding)
		}
		if o.ContentLength != nil {
			w.Header().Set("Content-Length", strconv.FormatInt(*o.ContentLength, 10))
		}
		if o.LastModified != nil {
			// Include seconds since last modified for creating a distribution metric in Datadog from logs
			w.Header().Set("Last-Modified", o.LastModified.Format(http.TimeFormat))
			w.Header().Set("Last-Modified-Age-Seconds", strconv.FormatInt(int64(time.Since(*o.LastModified).Seconds()), 10))
		}
		w.WriteHeader(http.StatusOK)
		return
	}

	bucket := storage.GetBucket(req.Path)
	getObjectInput := &storage.GetObjectInput{
		Bucket:          bucket,
		Key:             req.Path,
		ContentEncoding: r.Header.Get("Accept-Encoding"),
	}
	if req.Start != nil && req.End != nil {
		getObjectInput.Range = &storage.Range{
			Start: int64(*req.Start),
			End:   int64(*req.End),
		}
	}
	// download object, streaming to client
	resp, err := h.Client.GetObject(r.Context(), getObjectInput)
	if err != nil {
		// reply with 499 if client closed connection
		if errors.Is(err, context.Canceled) {
			render.Status(r, 499)
			render.JSON(w, r, Response{Message: "Client closed connection"})
			return
		}
		// reply with 404 if object not found
		if errors.Is(err, storage.ErrNotFound) {
			render.Status(r, http.StatusNotFound)
			render.JSON(w, r, Response{Message: "Object not found"})
			return
		}
		// reply with 500 if any other error
		oplog.Warn("Failed to download object", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to download object"})
		return
	}
	defer resp.Body.Close()

	// stream object to client
	if resp.ContentType != nil {
		w.Header().Set("Content-Type", *resp.ContentType)
		w.Header().Set("X-Content-Type-Options", "nosniff")
	}
	if resp.ContentEncoding != nil {
		w.Header().Set("Content-Encoding", *resp.ContentEncoding)
	}
	if resp.ContentLength != nil {
		w.Header().Set("Content-Length", strconv.FormatInt(*resp.ContentLength, 10))
	}
	if resp.LastModified != nil {
		// Include seconds since last modified for creating a distribution metric in Datadog from logs
		w.Header().Set("Last-Modified", resp.LastModified.Format(http.TimeFormat))
		w.Header().Set("Last-Modified-Age-Seconds", strconv.FormatInt(int64(time.Since(*resp.LastModified).Seconds()), 10))
	}
	// add content-disposition, if we can determine a filename
	if req.DownloadAs == "" {
		w.Header().Set("Content-Disposition", "inline")
	} else {
		ext, err := mime.ExtensionsByType(*resp.ContentType)
		if err == nil && len(ext) > 0 {
			if idx := slices.IndexFunc(ext, func(e string) bool {
				return strings.HasSuffix(req.DownloadAs, e)
			}); idx != -1 {
				// remove extension from download_as
				req.DownloadAs = strings.TrimSuffix(req.DownloadAs, ext[idx])
			}
			w.Header().Set("Content-Disposition", "attachment; filename=\""+parts[len(parts)-1]+ext[0]+"\"; filename*=\""+req.DownloadAs+ext[0]+"\"")
		}
	}
	// the first write will send headers and 200 status
	if _, err := io.Copy(w, resp.Body); err != nil {
		oplog.Warn("Failed to write to response body", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to stream object"})
		return
	}
}

func (h *RunObjectsHandler) UploadObject(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())

	// validate headers
	if ctype := r.Header.Get("Content-Type"); ctype == "" {
		render.Status(r, http.StatusUnprocessableEntity)
		render.JSON(w, r, Response{Message: "Missing content-type header"})
		return
	}
	if r.ContentLength == 0 {
		render.Status(r, http.StatusUnprocessableEntity)
		render.JSON(w, r, Response{Message: "Empty request body"})
		return
	}
	if r.ContentLength == -1 {
		render.Status(r, http.StatusLengthRequired)
		render.JSON(w, r, Response{Message: "Missing content-length header"})
		return
	}
	if cenc := r.Header.Get("Content-Encoding"); cenc != "" && cenc != "gzip" {
		render.Status(r, http.StatusUnprocessableEntity)
		render.JSON(w, r, Response{Message: "Unsupported content-encoding header"})
		return
	}
	if key := r.Header.Get("X-Object-Key"); key == "" {
		render.Status(r, http.StatusUnprocessableEntity)
		render.JSON(w, r, Response{Message: "Missing object key header"})
		return
	}

	// decide buffering strategy
	bucket := storage.GetBucket(r.Header.Get("X-Object-Key"))
	_, err := h.Client.UploadObject(r.Context(), &storage.UploadObjectInput{
		Bucket:          bucket,
		ContentEncoding: r.Header.Get("Content-Encoding"),
		ContentLength:   r.ContentLength,
		ContentType:     r.Header.Get("Content-Type"),
		Key:             r.Header.Get("X-Object-Key"),
		Reader:          r.Body,
	})

	// respond
	if err != nil {
		var status int
		var message string
		switch {
		case errors.Is(err, storage.ErrTempFileCreateFailed),
			errors.Is(err, storage.ErrTempFileWriteFailed),
			errors.Is(err, storage.ErrRequestBodyReadFailed):
			status = http.StatusUnprocessableEntity
			message = "Failed to create temp file"

		// reply with 499 if client closed connection
		case errors.Is(err, context.Canceled):
			status = 499
			message = "Client closed connection"
		default:
			status = http.StatusInternalServerError
			message = "Failed to upload object"
			oplog.Warn("Failed to upload object", "err", err)
		}

		render.Status(r, status)
		render.JSON(w, r, Response{Message: message})
		return
	}

	render.NoContent(w, r)
}

func (h *RunObjectsHandler) CopyObjectInternal(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	req := ctx.Value(httpin.Input).(*CopyObjectRequest)

	if req.Payload.SourceKey != "" && req.Payload.DestKey != "" {
		ctx = config.LogAndContextSetField(ctx, "source", slog.StringValue(req.Payload.SourceKey))
		ctx = config.LogAndContextSetField(ctx, "dest", slog.StringValue(req.Payload.DestKey))
		sourceBucket := storage.GetBucket(req.Payload.SourceKey)
		destBucket := storage.GetBucket(req.Payload.DestKey)
		err := h.Client.CopyObject(ctx, &storage.CopyObjectInput{
			SourceBucket: sourceBucket,
			SourceKey:    req.Payload.SourceKey,
			DestBucket:   destBucket,
			DestKey:      req.Payload.DestKey,
		})
		if err != nil {
			oplog.Warn("Failed to copy object", "err", err)
			render.Status(r, http.StatusInternalServerError)
			render.JSON(w, r, Response{Message: "Failed to copy object"})
			return
		}
	} else {
		http.Error(w, "both source_key and dest_key must be provided", http.StatusBadRequest)
		return
	}

	w.WriteHeader(http.StatusOK)
}
