package runs

import (
	"bufio"
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"mime"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/DataDog/zstd"
	"github.com/go-chi/httplog/v2"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	orderedmap "github.com/wk8/go-ordered-map/v2"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/feedback"
	"langchain.com/smith/ingestion"
	"langchain.com/smith/storage"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
	"langchain.com/smith/util"
)

var (
	maxFileSize = int64(50 * 1024 * 1024) // 50 MB

	allowedOutOfBandKeys = map[string]struct{}{
		"inputs":     {},
		"outputs":    {},
		"events":     {},
		"error":      {},
		"extra":      {},
		"serialized": {},
	}
)

type sessionKey struct {
	sessionID   string
	sessionName string
}

type MultipartParseResult struct {
	Feedback []feedback.FeedbackCreateSchema
	Posts    []Run
	Patches  []Run
	Extras   map[string]map[string]ingestion.ExtraValue
}

var knownEvents = map[string]struct{}{
	"post":       {},
	"patch":      {},
	"attachment": {},
	"feedback":   {},
}

// Decide which s3 bucket to use.
func selectMultipartBucket(authInfo *auth.AuthInfo) string {
	if config.Env.FFSingleRegionBucketEnabledTenants.Contains(authInfo.TenantID) || config.Env.FFSingleRegionBucketEnabledTenants.Contains("*") {
		return config.Env.S3SingleRegionBucketName
	}
	return config.Env.S3BucketName
}

type IoType string

const (
	Attachments IoType = "attachments"
	Inputs      IoType = "inputs"
	Outputs     IoType = "outputs"
	Events      IoType = "events"
	Error       IoType = "error"
	Extra       IoType = "extra"
	Serialized  IoType = "serialized"
)

func ParseMultipartForm(
	ctx context.Context,
	r *http.Request,
	authInfo *auth.AuthInfo,
	usageLimitsClient *usage_limits.UsageLimitsClient,
	tracerSessionsClient *tracer_sessions.TracerSessionsClient,
	routedRedisPool redis.UniversalClient,
	stgClient storage.StorageClient,
) (*MultipartParseResult, error) {
	oplog := httplog.LogEntry(ctx)

	var nParts int
	var nBytes int64

	ctype := r.Header.Get("Content-Type")
	if !strings.HasPrefix(ctype, "multipart/form-data") {
		return nil, fmt.Errorf("%w: %s", errInvalidContentType, ctype)
	}
	cenc := r.Header.Get("Content-Encoding")
	if cenc != "" && cenc != "zstd" {
		return nil, fmt.Errorf("%w: %s", errUnsupportedContentEncoding, cenc)
	}

	var body io.Reader = r.Body
	if cenc == "zstd" {
		zr := zstd.NewReader(r.Body)
		defer zr.Close()
		body = zr
	}

	_, params, err := mime.ParseMediaType(ctype)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", errInvalidContentType, err)
	}
	boundary, ok := params["boundary"]
	if !ok {
		return nil, fmt.Errorf("%w: missing boundary in Content-Type", errInvalidContentType)
	}

	reader := multipart.NewReader(body, boundary)

	result := &MultipartParseResult{
		Feedback: make([]feedback.FeedbackCreateSchema, 0),
		Posts:    make([]Run, 0),
		Patches:  make([]Run, 0),
		Extras:   make(map[string]map[string]ingestion.ExtraValue),
	}

	runs := map[string]map[string]*Run{
		"post":  {},
		"patch": {},
	}

	var feedbackList []feedback.FeedbackCreateSchema

	didCheckLonglivedLimit := false

	sessionsByRun := make(map[string]*tracer_sessions.TracerSessionWithoutVirtualFields)
	sessionsByKey := make(map[sessionKey]*tracer_sessions.TracerSessionWithoutVirtualFields)

	uploadResults := make([]*storage.UploadAsyncResult, 0)

	sessionUploaders := make(map[string]*storage.ContinuousUploader)
	sessionUploaderKeys := make(map[string]string)

	seenParts := make(map[string]struct{})

	batchStarted := false

	for {
		part, err := reader.NextPart()
		if err == io.EOF {
			break // done
		}
		if err != nil {
			if errors.Is(err, bufio.ErrBufferFull) {
				return nil, errPartTooLarge
			}
			return nil, fmt.Errorf("%w: %s", errReadingMultipartData, err)
		}

		nParts++

		name := part.FormName()
		if _, ok := seenParts[name]; ok {
			// Read and discard the part's body so subsequent parts can parse
			io.Copy(io.Discard, part)
			continue
		}
		seenParts[name] = struct{}{}

		nameParts := strings.SplitN(name, ".", 3)
		if len(nameParts) < 2 {
			return nil, fmt.Errorf("%w for %s: (need at least event.run_id)", errInvalidPartName, name)
		}
		event := nameParts[0]
		runID := nameParts[1]
		var field string
		if len(nameParts) == 3 {
			field = nameParts[2]
		}

		if _, ok := knownEvents[event]; !ok {
			return nil, fmt.Errorf("%w for %s", errInvalidPartName, name)
		}

		if runID == "" {
			return nil, fmt.Errorf("%w for %s: missing run_id", errInvalidPartName, name)
		}

		ctypeHeader := part.Header.Get("Content-Type")
		if ctypeHeader == "" {
			return nil, fmt.Errorf("%w for %s: missing Content-Type", errMissingContentType, name)
		}
		// parse "application/json; length=123" or "application/octet-stream; length=123" etc.
		mediaType, ctypeParams, err := mime.ParseMediaType(ctypeHeader)
		if err != nil {
			return nil, fmt.Errorf("%w for %s: %w", errInvalidContentType, name, err)
		}

		partEncoding := part.Header.Get("Content-Encoding")
		if partEncoding == "" {
			// fallback to "encoding" param if present
			if encParam, ok := ctypeParams["encoding"]; ok {
				partEncoding = encParam
			}
		}
		if partEncoding != "" {
			return nil, errUnsupportedContentEncoding
		}

		// Try Content-Length header first
		contentLength := part.Header.Get("Content-Length")
		var size int64
		if contentLength != "" {
			size, err = strconv.ParseInt(contentLength, 10, 64)
			if err != nil || size < 1 {
				return nil, fmt.Errorf("%w for %s", errInvalidLengthParam, name)
			}
		} else {
			lengthStr, ok := ctypeParams["length"]
			if !ok {
				return nil, fmt.Errorf("%w for %s", errMissingLengthParam, name)
			}
			size, err = strconv.ParseInt(lengthStr, 10, 64)
			if err != nil || size < 1 {
				return nil, fmt.Errorf("%w for %s", errInvalidLengthParam, name)
			}
		}

		nBytes += size

		switch event {
		case "feedback":
			dataBytes, err := io.ReadAll(part)
			if err != nil {
				return nil, fmt.Errorf("%w for %s", errReadingMultipartData, name)
			}
			var fb feedback.FeedbackCreateSchema
			if err = json.Unmarshal(dataBytes, &fb); err != nil {
				return nil, fmt.Errorf("%w for %s: %w", errInvalidJsonPart, name, err)
			}
			if fb.ID.String() != runID {
				return nil, fmt.Errorf("%w: run id mismatch in feedback: got %s, expected %s", errInvalidFeedbackPart, fb.ID, runID)
			}
			if fb.TraceID == nil {
				return nil, fmt.Errorf("%w: feedback part missing trace_id (part %s)", errInvalidFeedbackPart, name)
			}
			feedbackList = append(feedbackList, fb)

		case "post", "patch":
			// If we have a subfield, it's out-of-band data
			// out-of-band data are the following keys: inputs, outputs, events, error, extra, serialized
			// they are stored in the "extras" field of the run
			if field != "" {
				runSession, runKnown := sessionsByRun[runID]
				runKnown = runKnown || result.Extras[runID] != nil
				if !runKnown {
					oplog.Warn("Ignoring out-of-band data for unknown run", "run_id", runID, "field", field)
					io.Copy(io.Discard, part)
					continue
				}
				if _, ok := allowedOutOfBandKeys[field]; !ok {
					return nil, fmt.Errorf("%w for %s: invalid field '%s'", errInvalidPartName, name, field)
				}
				store := fetchOrInitExtrasSlot(result.Extras, runID)

				if shouldUseBlobStorageBatching(stgClient, oplog) {
					if batchStarted && size > config.Env.MinMultipartBlobStorageSizeKb*1024 {
						cleanup, err := uploadUsingSessionUploader(ctx, part, runSession, field, store,
							sessionUploaders, sessionUploaderKeys, stgClient, authInfo, oplog)
						if cleanup != nil {
							defer cleanup()
						}
						if err != nil {
							return nil, err
						}
					} else {
						if size > config.Env.MinBlobStorageSizeKb*1024 {
							// Switch to batching.
							batchStarted = true
							cleanup, err := uploadUsingSessionUploader(ctx, part, runSession, field, store,
								sessionUploaders, sessionUploaderKeys, stgClient, authInfo, oplog)
							if cleanup != nil {
								defer cleanup()
							}
							if err != nil {
								return nil, err
							}
						} else {
							// Inline small data.
							dataBytes, err := io.ReadAll(part)
							if err != nil {
								return nil, fmt.Errorf("%w for %s: %w", errReadingMultipartData, name, err)
							}
							store[field] = ingestion.ExtraValue{
								ContentType: mediaType,
								Encoding:    partEncoding,
								Data:        dataBytes,
							}
						}
					}
				} else if size > config.Env.MinBlobStorageSizeKb*1024 && config.Env.BlobStorageEnabled {
					// For legacy langsmith-js versions, upload to S3 with unknown size.
					// We need to do this because the data is not correctly encoded as utf-8
					// in legacy versions.
					legacy, _ := isLegacyLangsmithJS(r.Header.Get("User-Agent"))
					if legacy {
						size = 0
					}

					// Generate a storage key and upload to S3.
					uploadResult, s3Key, err := generateStorageKeyAndUpload(
						ctx,
						stgClient,
						S3UploadParams{
							AuthInfo:     authInfo,
							RunID:        runID,
							RunSession:   runSession,
							Part:         part,
							Size:         size,
							Name:         name,
							Key:          IoType(field),
							MediaType:    mediaType,
							PartEncoding: partEncoding,
						},
					)
					if err != nil {
						return nil, err
					}
					uploadResults = append(uploadResults, uploadResult)

					store[field] = ingestion.ExtraValue{
						ContentType: "",
						Encoding:    "",
						Data:        []byte(s3Key),
					}
				} else {
					// spool in memory for smaller data
					dataBytes, err := io.ReadAll(part)
					if err != nil {
						return nil, fmt.Errorf("%w for %s: %w", errReadingMultipartData, name, err)
					}

					store[field] = ingestion.ExtraValue{
						ContentType: mediaType,
						Encoding:    partEncoding,
						Data:        dataBytes,
					}
				}
				continue
			}

			// If there's no subfield, this is the main run payload -> parse JSON
			dataBytes, err := io.ReadAll(part)
			if err != nil {
				return nil, fmt.Errorf("%w for %s: failed to read part data: %w", errReadingMultipartData, name, err)
			}

			var rawMapOrdered *orderedmap.OrderedMap[string, interface{}]
			if config.Env.FFOrderingMultipartEnabledTenants.
				Contains(authInfo.TenantID) || config.Env.FFOrderingMultipartEnabledTenants.Contains("*") {

				rawMapOrdered, err = util.UnmarshalOrdered(dataBytes)
				if err != nil {
					return nil, fmt.Errorf("%w for %s: could not unmarshal run: %w",
						errInvalidJsonPart, name, err)
				}

			} else {
				var tmp map[string]interface{}
				if err := json.Unmarshal(dataBytes, &tmp); err != nil {
					return nil, fmt.Errorf("%w for %s: could not unmarshal run: %w",
						errInvalidJsonPart, name, err)
				}
				rawMapOrdered = util.MapToOrdered(tmp)
			}

			var sessionID, sessionName, startTime *string
			if val, ok := rawMapOrdered.Get("session_id"); ok {
				if valStr, ok := val.(string); ok {
					sessionID = &valStr
				}
			}
			if val, ok := rawMapOrdered.Get("session_name"); ok {
				if valStr, ok := val.(string); ok {
					sessionName = &valStr
				}
			}
			if val, ok := rawMapOrdered.Get("start_time"); ok {
				switch v := val.(type) {
				case string:
					startTime = &v
				case int64:
					unixTime := time.UnixMilli(v).UTC().Format(time.RFC3339Nano)
					startTime = &unixTime
				}
			}

			if sessionID == nil && sessionName == nil && sessionsByRun[runID] == nil {
				// Attempt to fetch tracer session from Redis.
				tracerSession, err := lookupSessionFromRedis(
					ctx,
					name,
					runID,
					authInfo,
					routedRedisPool,
					tracerSessionsClient,
					startTime,
				)
				if err != nil {
					return nil, err
				}
				if tracerSession != nil {
					sessionsByRun[runID] = tracerSession
				}
			}

			sk := newSessionKey(sessionID, sessionName)
			session, ok := sessionsByKey[sk]
			if !ok {
				session, err = tracerSessionsClient.StartOrFetchTracerSession(
					ctx,
					*authInfo,
					sessionID,
					sessionName,
					startTime,
				)
				if err != nil {
					return nil, handleTracerSessionError(err, name)
				}
				sessionsByKey[sk] = session
			}
			if sessionsByRun[runID] == nil {
				sessionsByRun[runID] = session
			}

			store := fetchOrInitExtrasSlot(result.Extras, runID)
			for oobKey := range allowedOutOfBandKeys {
				if val, found := rawMapOrdered.Get(oobKey); found {
					rawMapOrdered.Delete(oobKey)
					valBytes, err := json.Marshal(val)
					if err != nil {
						return nil, fmt.Errorf("%w for %s: could not marshal field %s: %w", errInvalidJsonPart, name, oobKey, err)
					}

					runSession, ok := sessionsByRun[runID]
					if !ok {
						return nil, fmt.Errorf("%w for run %s when processing oobkey %s", errSessionNotFound, runID, oobKey)
					}

					if shouldUseBlobStorageBatching(stgClient, oplog) {
						if batchStarted && int64(len(valBytes)) > (config.Env.MinMultipartBlobStorageSizeKb*1024) {
							cleanup, err := uploadUsingSessionUploader(ctx, bytes.NewReader(valBytes), runSession, oobKey, store,
								sessionUploaders, sessionUploaderKeys, stgClient, authInfo, oplog)
							if cleanup != nil {
								defer cleanup()
							}
							if err != nil {
								return nil, err
							}
						} else {
							if int64(len(valBytes)) > (config.Env.MinBlobStorageSizeKb * 1024) {
								batchStarted = true
								cleanup, err := uploadUsingSessionUploader(ctx, bytes.NewReader(valBytes), runSession, oobKey, store,
									sessionUploaders, sessionUploaderKeys, stgClient, authInfo, oplog)
								if cleanup != nil {
									defer cleanup()
								}
								if err != nil {
									return nil, err
								}
							} else {
								store[oobKey] = ingestion.ExtraValue{
									ContentType: "application/json",
									Encoding:    "",
									Data:        valBytes,
								}
							}
						}
					} else if int64(len(valBytes)) > (config.Env.MinBlobStorageSizeKb*1024) && config.Env.BlobStorageEnabled {
						uploadResult, s3Key, err := generateStorageKeyAndUpload(
							ctx,
							stgClient,
							S3UploadParams{
								AuthInfo:     authInfo,
								RunID:        runID,
								RunSession:   runSession,
								Part:         bytes.NewReader(valBytes),
								Size:         int64(len(valBytes)),
								Name:         name,
								Key:          IoType(oobKey),
								MediaType:    "application/json",
								PartEncoding: "",
							},
						)
						if err != nil {
							return nil, err
						}
						uploadResults = append(uploadResults, uploadResult)

						store[oobKey] = ingestion.ExtraValue{
							ContentType: "",
							Encoding:    "",
							Data:        []byte(s3Key),
						}
					} else {
						store[oobKey] = ingestion.ExtraValue{
							ContentType: "application/json",
							Encoding:    "",
							Data:        valBytes,
						}
					}
				}
			}

			var runObj *Run
			if event == "patch" {
				runObj, err = ParseValidatePatchOrderedMap(rawMapOrdered)
			} else {
				runObj, err = ParseValidatePostOrderedMap(rawMapOrdered)
			}
			if err != nil {
				return nil, fmt.Errorf("%w for %s: %w", errParsingRun, name, err)
			}
			if runObj.ID == nil || *runObj.ID != runID {
				return nil, fmt.Errorf("%w for %s: run id mismatch: got %v, expected %s",
					errParsingRun, name, runObj.ID, runID)
			}

			if config.Env.FFTraceTiersEnabled && (sessionsByRun[runID].TraceTier != nil && *sessionsByRun[runID].TraceTier == tracer_sessions.LongLived ||
				runObj.ReferenceExampleID != nil && *runObj.ReferenceExampleID != "") && !didCheckLonglivedLimit {
				tenantExceededLonglivedLimits, limitExceededMessage, err := usageLimitsClient.CheckLonglivedUsageLimits(ctx, *authInfo)
				if err != nil {
					return nil, fmt.Errorf("%w for %s: %w", errCheckingUsageLimits, name, err)
				}
				if tenantExceededLonglivedLimits {
					return nil, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, limitExceededMessage)
				}
				didCheckLonglivedLimit = true
			}

			runs[event][runID] = runObj

		case "attachment":
			if !config.Env.BlobStorageEnabled {
				return nil, errBlobStorageNotEnabled
			}
			if field == "" {
				return nil, fmt.Errorf("%w for %s: attachment missing field", errInvalidAttachmentPart, name)
			}
			// disallow fields that end with these suffixes
			if strings.HasSuffix(field, "_s3_url") ||
				strings.HasSuffix(field, "_content_type") ||
				strings.HasSuffix(field, "_compression_method") {
				return nil, fmt.Errorf("%w for %s", errInvalidAttachmentPart, name)
			}
			if size > maxFileSize {
				return nil, fmt.Errorf("%w for attachment %s: file size exceeds limit", errInvalidLengthParam, name)
			}
			runSession, ok := sessionsByRun[runID]
			if !ok {
				oplog.Warn("Dropping attachment - unknown run_id", "run_id", runID, "name", name)
				continue
			}
			store := fetchOrInitExtrasSlot(result.Extras, runID)

			uploadResult, s3Key, err := generateStorageKeyAndUpload(
				ctx,
				stgClient,
				S3UploadParams{
					AuthInfo:     authInfo,
					RunID:        runID,
					RunSession:   runSession,
					Part:         part,
					Size:         size,
					Name:         name,
					Key:          Attachments,
					MediaType:    mediaType,
					PartEncoding: partEncoding,
				},
			)
			if err != nil {
				return nil, err
			}
			uploadResults = append(uploadResults, uploadResult)

			attachmentKey := fmt.Sprintf("attachment.%s", field)
			store[attachmentKey] = ingestion.ExtraValue{
				ContentType: "",
				Encoding:    "",
				Data:        []byte(s3Key),
			}

		default:
			return nil, fmt.Errorf("%w for %s", errInvalidPartName, name)
		}
	}

	config.LogAndContextSetField(ctx, "n_parts", slog.IntValue(nParts))
	config.LogAndContextSetField(ctx, "n_bytes", slog.Int64Value(nBytes))
	config.LogAndContextSetField(ctx, "n_uploads", slog.IntValue(len(uploadResults)))
	config.LogAndContextSetField(ctx, "n_runs", slog.IntValue(len(runs["post"])+len(runs["patch"])))
	config.LogAndContextSetField(ctx, "n_feedback", slog.IntValue(len(feedbackList)))

	for _, uploader := range sessionUploaders {
		if _, err := uploader.Complete(); err != nil {
			return nil, fmt.Errorf("%w: %w", errUploadingPart, err)
		}
	}

	// wait for all uploads to complete
	for _, uploadResult := range uploadResults {
		if uploadResult == nil {
			continue
		}
		if err := uploadResult.Wait(); err != nil {
			return nil, fmt.Errorf("%w: %w", errUploadingPart, err)
		}
	}

	for _, rmap := range runs["post"] {
		result.Posts = append(result.Posts, *rmap)
	}
	for _, rmap := range runs["patch"] {
		result.Patches = append(result.Patches, *rmap)
	}
	result.Feedback = feedbackList

	return result, nil
}

func isLegacyLangsmithJS(userAgent string) (bool, string) {
	const firstCorrectVersion = "0.3.5"
	if !strings.Contains(userAgent, "langsmith-js/") {
		return false, ""
	}
	version := strings.TrimPrefix(userAgent, "langsmith-js/")

	vParts := strings.Split(version, ".")
	fcParts := strings.Split(firstCorrectVersion, ".")

	for i := 0; i < len(vParts) && i < len(fcParts); i++ {
		v1, err1 := strconv.Atoi(vParts[i])
		v2, err2 := strconv.Atoi(fcParts[i])
		if err1 != nil || err2 != nil {
			return strings.Compare(version, firstCorrectVersion) < 0, version
		}
		if v1 < v2 {
			return true, version
		}
		if v1 > v2 {
			return false, version
		}
	}

	return len(vParts) < len(fcParts), version
}

func generateStorageKey(tenantID string, sessionID string, runID string, traceTier string, ioType IoType, bucket string) (string, error) {
	tenantIDBytes := []byte(tenantID)
	sessionIDBytes := []byte(sessionID)

	// generate SHA-256 hash for tenantID and sessionID
	hTenantID := sha256.Sum256(tenantIDBytes)
	hTenantIDHex := hex.EncodeToString(hTenantID[:])

	hSessionID := sha256.Sum256(sessionIDBytes)
	hSessionIDHex := hex.EncodeToString(hSessionID[:])

	objID := uuid.NewString()

	ttlPrefix := ""
	if traceTier != "" {
		ttlPrefix = config.Env.S3TraceTierPrefixMap[traceTier] + "/"
	}

	bucketPrefix := ""
	if bucket != "" && (config.Env.FFSingleRegionBucketEnabledTenants.Contains(tenantID) || config.Env.FFSingleRegionBucketEnabledTenants.Contains("*")) {
		bucketPrefix = bucket + "/"
	}

	s3Key := fmt.Sprintf("%s%s%s/%s/%s/%s/%s", bucketPrefix, ttlPrefix, ioType, hTenantIDHex, hSessionIDHex, runID, objID)

	return s3Key, nil
}

func fetchOrInitExtrasSlot(extras map[string]map[string]ingestion.ExtraValue, runID string) map[string]ingestion.ExtraValue {
	slot, ok := extras[runID]
	if !ok {
		slot = make(map[string]ingestion.ExtraValue)
		extras[runID] = slot
	}
	return slot
}

type S3UploadParams struct {
	AuthInfo     *auth.AuthInfo
	RunID        string
	RunSession   *tracer_sessions.TracerSessionWithoutVirtualFields
	Part         io.Reader
	Size         int64
	Name         string
	Key          IoType
	MediaType    string
	PartEncoding string
}

func generateStorageKeyAndUpload(
	ctx context.Context,
	stgClient storage.StorageClient,
	params S3UploadParams,
) (*storage.UploadAsyncResult, string, error) {
	bucket := selectMultipartBucket(params.AuthInfo)
	s3Key, err := generateStorageKey(
		params.AuthInfo.TenantID,
		params.RunSession.ID.String(),
		params.RunID,
		string(*params.RunSession.TraceTier),
		params.Key,
		bucket,
	)
	if err != nil {
		return nil, "", fmt.Errorf("%w for %s: %w", errGenerateStorageKeyFailure, params.Name, err)
	}

	putObj := &storage.UploadObjectInput{
		Bucket:          bucket,
		Key:             s3Key,
		Reader:          params.Part,
		ContentType:     params.MediaType,
		ContentLength:   params.Size,
		ContentEncoding: params.PartEncoding,
	}

	uploadResult, err := stgClient.UploadObjectAsync(ctx, putObj)
	if err != nil {
		return nil, "", fmt.Errorf("%w for %s: %w", errUploadingPart, params.Name, err)
	}

	return uploadResult, s3Key, nil
}

func isS3StorageClient(stgClient storage.StorageClient) bool {
	_, ok := stgClient.(*storage.S3StorageClient)
	return ok
}

func shouldUseBlobStorageBatching(stgClient storage.StorageClient, oplog *slog.Logger) bool {
	// Check if we're using an S3 client (batching not supported on Azure)
	if !config.Env.BlobStorageEnabled {
		return false
	}

	isS3Client := isS3StorageClient(stgClient)

	if !isS3Client {
		oplog.Warn("Blob storage batching is only supported for S3 storage clients")
		return false
	}

	return true
}

func createSessionUploader(ctx context.Context, stgClient storage.StorageClient, authInfo *auth.AuthInfo, session *tracer_sessions.TracerSessionWithoutVirtualFields, oplog *slog.Logger) (*storage.ContinuousUploader, string, error) {
	s3Client, ok := stgClient.(*storage.S3StorageClient)
	if !ok {
		// We shouldn't ever run into an issue here, but just in case
		oplog.Warn("Blob storage batching is only supported for S3")
		return nil, "", errAzureNotSupported
	}

	bucket := selectMultipartBucket(authInfo)

	// options for coninuous uploader
	var opts []storage.Option

	if config.Env.FFZstdCompressionEnabled {
		opts = append(opts, storage.WithCompression(zstd.DefaultCompression))
	}

	uploader, err := storage.NewContinuousUploader(ctx, s3Client.S3, bucket, opts...)
	if err != nil {
		return nil, "", fmt.Errorf("%w: new continuous uploader: %w", errUploadingPart, err)
	}
	traceTier := ""
	if session.TraceTier != nil {
		traceTier = string(*session.TraceTier)
	}
	uploaderKey := generateUploaderKey(authInfo.TenantID, session.ID.String(), traceTier, bucket)
	if err := uploader.StartUpload(uploaderKey); err != nil {
		return nil, "", fmt.Errorf("%w: start continuous uploader: %w", errUploadingPart, err)
	}

	return uploader, uploaderKey, nil
}

func generateUploaderKey(tenantID string, sessionID string, traceTier string, bucket string) string {
	tenantIDBytes := []byte(tenantID)
	sessionIDBytes := []byte(sessionID)

	// Generate SHA-256 hash for tenantID and sessionID
	hTenantID := sha256.Sum256(tenantIDBytes)
	hTenantIDHex := hex.EncodeToString(hTenantID[:])

	hSessionID := sha256.Sum256(sessionIDBytes)
	hSessionIDHex := hex.EncodeToString(hSessionID[:])

	objID := uuid.NewString()

	ttlPrefix := ""
	if traceTier != "" {
		ttlPrefix = config.Env.S3TraceTierPrefixMap[traceTier] + "/"
	}

	bucketPrefix := ""
	if bucket != "" && (config.Env.FFSingleRegionBucketEnabledTenants.Contains(tenantID) || config.Env.FFSingleRegionBucketEnabledTenants.Contains("*")) {
		bucketPrefix = bucket + "/"
	}

	return fmt.Sprintf("%s%smultipart/%s/%s/%s", bucketPrefix, ttlPrefix, hTenantIDHex, hSessionIDHex, objID)
}

func handleTracerSessionError(err error, name string) error {
	switch {
	case errors.Is(err, tracer_sessions.ErrSessionAlreadyExists):
		return fmt.Errorf("%w for %s: session already exists", errPayloadAlreadyReceived, name)
	case errors.Is(err, tracer_sessions.ErrReferenceDatasetNotFound),
		errors.Is(err, tracer_sessions.ErrDefaultDatasetNotFound),
		errors.Is(err, tracer_sessions.ErrTracerSessionNotFound):
		return fmt.Errorf("%w for %s: %v", errNotFound, name, err)
	case errors.Is(err, tracer_sessions.ErrSessionEndTimeBeforeRun):
		return fmt.Errorf("%w for %s: %v", errInvalidInput, name, err)
	case errors.Is(err, tracer_sessions.ErrInvalidTimestampFormat):
		return fmt.Errorf("%w for %s: %v", errParsingRun, name, err)
	default:
		return fmt.Errorf("error ensuring sessions for %s: %w", name, err)
	}
}

func lookupSessionFromRedis(
	ctx context.Context,
	name, runID string,
	authInfo *auth.AuthInfo,
	redisPool redis.UniversalClient,
	tracerSessionsClient *tracer_sessions.TracerSessionsClient,
	startTime *string,
) (*tracer_sessions.TracerSessionWithoutVirtualFields, error) {
	// If we get a patch request without session information, attempt to lookup the session in Redis
	if redisPool == nil || !config.Env.RedisSessionLookupEnabled {
		return nil, nil
	}

	pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, runID)
	sessionIDStr, err := redisPool.HGet(ctx, pendingKey, "session_id").Result()
	if err != nil && err != redis.Nil {
		return nil, nil
	}
	if sessionIDStr == "" {
		return nil, nil
	}

	var sessionIDVal string
	if err := json.Unmarshal([]byte(sessionIDStr), &sessionIDVal); err != nil {
		return nil, nil
	}

	uuidObj, _ := ParseUUID(&sessionIDVal, "session_id")
	if uuidObj == nil {
		return nil, nil
	}

	uuidStr := uuidObj.String()
	tracerSession, err := tracerSessionsClient.StartOrFetchTracerSession(ctx, *authInfo, &uuidStr, nil, startTime)
	if err != nil {
		return nil, handleTracerSessionError(err, name)
	}
	return tracerSession, nil
}

func newSessionKey(sessionID, sessionName *string) sessionKey {
	var sid, sname string
	if sessionID != nil {
		sid = *sessionID
	}
	if sessionName != nil {
		sname = *sessionName
	}
	return sessionKey{
		sessionID:   sid,
		sessionName: sname,
	}
}

func uploadUsingSessionUploader(
	ctx context.Context,
	reader io.Reader,
	runSession *tracer_sessions.TracerSessionWithoutVirtualFields,
	field string,
	store map[string]ingestion.ExtraValue,
	sessionUploaders map[string]*storage.ContinuousUploader,
	sessionUploaderKeys map[string]string,
	stgClient storage.StorageClient,
	authInfo *auth.AuthInfo,
	oplog *slog.Logger,
) (cleanup func(), err error) {
	sessID := runSession.ID.String()
	uploader, exists := sessionUploaders[sessID]
	if !exists {
		var uploaderKey string
		uploader, uploaderKey, err = createSessionUploader(ctx, stgClient, authInfo, runSession, oplog)
		if err != nil {
			return nil, err
		}
		sessionUploaders[sessID] = uploader
		sessionUploaderKeys[sessID] = uploaderKey
		cleanup = func() {
			uploader.Close()
		}
	}
	startOffset, endOffset, err := uploader.UploadReader(reader)
	if err != nil {
		return cleanup, fmt.Errorf("%w: %w", errUploadingPart, err)
	}
	store[field] = ingestion.ExtraValue{
		ContentType: "",
		Encoding:    "",
		Data:        []byte(fmt.Sprintf("%s#%d-%d", sessionUploaderKeys[sessID], startOffset, endOffset)),
	}
	return cleanup, nil
}
