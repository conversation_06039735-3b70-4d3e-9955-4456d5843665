package runs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/feedback"
	"langchain.com/smith/ingestion"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/storage"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
	"langchain.com/smith/util"
)

type RunHandler struct {
	Pg                   *database.AuditLoggedPool
	CachingRedisPool     redis.UniversalClient
	RoutedRedisPools     lsredis.RoutedRedisPools
	UsageLimitsClient    *usage_limits.UsageLimitsClient
	TracerSessionsClient *tracer_sessions.TracerSessionsClient
}

type BatchRunHandler struct {
	RunHandler
	StorageClient storage.StorageClient
}

type MultipartRunHandler struct {
	RunHandler
	StorageClient        storage.StorageClient
	FeedbackConfigClient *feedback.FeedbackConfigClient
	ClickhouseConn       clickhouse.Conn
}

func NewRunHandler(
	dbpool *database.AuditLoggedPool,
	cachingRedisPool redis.UniversalClient,
	routedRedisPools lsredis.RoutedRedisPools,
) *RunHandler {
	usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get usage limits client: %v", err))
	}
	tracerSessionsClient, err := tracer_sessions.GetTracerSessionsClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get tracer sessions client: %v", err))
	}
	return &RunHandler{
		Pg:                   dbpool,
		CachingRedisPool:     cachingRedisPool,
		RoutedRedisPools:     routedRedisPools,
		UsageLimitsClient:    usageLimitsClient,
		TracerSessionsClient: tracerSessionsClient,
	}
}

func NewBatchRunHandler(
	dbpool *database.AuditLoggedPool,
	cachingRedisPool redis.UniversalClient,
	routedRedisPools lsredis.RoutedRedisPools,
	storageClient storage.StorageClient,
) *BatchRunHandler {
	return &BatchRunHandler{
		RunHandler:    *NewRunHandler(dbpool, cachingRedisPool, routedRedisPools),
		StorageClient: storageClient,
	}
}

func NewMultipartRunHandler(
	dbpool *database.AuditLoggedPool,
	cachingRedisPool redis.UniversalClient,
	routedRedisPools lsredis.RoutedRedisPools,
	storageClient storage.StorageClient,
	clickhouseConn clickhouse.Conn,
) *MultipartRunHandler {
	baseHandler := NewRunHandler(dbpool, cachingRedisPool, routedRedisPools)

	feedbackConfigClient, err := feedback.GetFeedbackConfigClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get feedback client: %v", err))
	}

	return &MultipartRunHandler{
		RunHandler:           *baseHandler,
		StorageClient:        storageClient,
		FeedbackConfigClient: feedbackConfigClient,
		ClickhouseConn:       clickhouseConn,
	}
}

const (
	RunTypeTool      = "tool"
	RunTypeChain     = "chain"
	RunTypeLLM       = "llm"
	RunTypeRetriever = "retriever"
	RunTypeEmbedding = "embedding"
	RunTypePrompt    = "prompt"
	RunTypeParser    = "parser"
)

type Run struct {
	ID                 *string                  `json:"id"`
	TraceID            *string                  `json:"trace_id,omitempty"`
	Name               *string                  `json:"name,omitempty"`
	RunType            *string                  `json:"run_type,omitempty" validate:"oneof=tool chain llm retriever embedding prompt parser"`
	StartTime          *string                  `json:"start_time,omitempty"`
	EndTime            *string                  `json:"end_time,omitempty"`
	SessionID          *string                  `json:"session_id,omitempty"`
	SessionName        *string                  `json:"session_name,omitempty"`
	DottedOrder        *string                  `json:"dotted_order,omitempty"`
	Status             *string                  `json:"status,omitempty"`
	Inputs             map[string]interface{}   `json:"inputs,omitempty"`
	Outputs            map[string]interface{}   `json:"outputs,omitempty"`
	Extra              map[string]interface{}   `json:"extra,omitempty"`
	Error              *string                  `json:"error,omitempty"`
	Serialized         map[string]interface{}   `json:"serialized,omitempty,omitzero"`
	ParentRunID        *string                  `json:"parent_run_id,omitempty"`
	Events             []map[string]interface{} `json:"events,omitempty"`
	Tags               []string                 `json:"tags"`
	InputAttachments   map[string]interface{}   `json:"input_attachments,omitempty"`
	OutputAttachments  map[string]interface{}   `json:"output_attachments,omitempty"`
	ReferenceExampleID *string                  `json:"reference_example_id,omitempty"`
}

var (
	maxBytes = int64(214748365) // 200 MB set by gcp here https://github.com/langchain-ai/deployments/blob/main/modules/gcp/gke/main.tf#L479C64-L479C73
)

var (
	// 422 Unprocessable Entity
	errReadingMultipartData  = errors.New("error reading multipart data")
	errInvalidBatchJson      = errors.New("invalid batch JSON")
	errMissingContentType    = errors.New("missing Content-Type")
	errInvalidContentType    = errors.New("invalid Content-Type")
	errInvalidPartName       = errors.New("invalid part name")
	errInvalidAttachmentPart = errors.New("invalid attachment part")
	errMissingLengthParam    = errors.New("missing both Content-Length header and length param")
	errInvalidLengthParam    = errors.New("invalid Content-Length or length parameter")
	errParsingRun            = errors.New("error parsing run")
	errInvalidJsonPart       = errors.New("invalid JSON part")
	errEmptyBatch            = errors.New("empty batch")
	errPartTooLarge          = errors.New("part exceeds maximum size")
	errTraceLimitExceeded    = errors.New("trace limit exceeded")
	errInvalidFeedbackPart   = errors.New("invalid feedback part")

	// 400 Bad Request
	errInvalidDottedOrder    = errors.New("invalid 'dotted_order'")
	errInvalidFeedbackConfig = errors.New("invalid feedback config")
	errInvalidInput          = errors.New("invalid input")
	errInvalidSession        = errors.New("invalid session")

	// 403 Forbidden
	errUnauthorized = errors.New("unauthorized")

	// 404 Not Found
	errNotFound        = errors.New("not found")
	errSessionNotFound = errors.New("session not found")

	// 409 Conflict
	errPayloadAlreadyReceived = errors.New("payload already received")

	// 415 Unsupported Media Type
	errUnsupportedContentEncoding = errors.New("unsupported Content-Encoding")

	// 429 Too Many Requests
	errTenantExceededUsageLimits = errors.New("tenant exceeded usage limits")

	// 500 Internal Server Error
	errGenerateStorageKeyFailure = errors.New("failed to generate storage key")
	errCheckingUsageLimits       = errors.New("error checking usage limits")
	errFetchingFeedbackConfigs   = errors.New("failed to fetch feedback configs")
	// Specific to blob storage batching
	errAzureNotSupported = errors.New("azure not supported for blob storage batching")

	errUploadingPart = errors.New("error uploading part")

	// 501 Not Implemented
	errBlobStorageNotEnabled = errors.New("blob storage is not enabled")
)

func (h *RunHandler) handleTracerSessionError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error) {
	switch {
	case errors.Is(err, tracer_sessions.ErrSessionAlreadyExists):
		h.handleError(w, r, oplog, fmt.Errorf("%w: session already exists", errPayloadAlreadyReceived))
	case errors.Is(err, tracer_sessions.ErrReferenceDatasetNotFound),
		errors.Is(err, tracer_sessions.ErrDefaultDatasetNotFound),
		errors.Is(err, tracer_sessions.ErrTracerSessionNotFound):
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errNotFound, err))
	case errors.Is(err, tracer_sessions.ErrSessionEndTimeBeforeRun):
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidInput, err))
	case errors.Is(err, tracer_sessions.ErrInvalidTimestampFormat):
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
	default:
		h.handleError(w, r, oplog, fmt.Errorf("error ensuring sessions: %w", err))
	}
}

func (h *RunHandler) handleError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error) {
	var status int
	var message string

	switch {
	case errors.Is(err, errReadingMultipartData),
		errors.Is(err, errMissingContentType),
		errors.Is(err, errInvalidContentType),
		errors.Is(err, errInvalidPartName),
		errors.Is(err, errInvalidAttachmentPart),
		errors.Is(err, errMissingLengthParam),
		errors.Is(err, errInvalidLengthParam),
		errors.Is(err, errInvalidJsonPart),
		errors.Is(err, errEmptyBatch),
		errors.Is(err, errInvalidBatchJson),
		errors.Is(err, errPartTooLarge),
		errors.Is(err, errTraceLimitExceeded),
		errors.Is(err, errInvalidFeedbackPart),
		errors.Is(err, errParsingRun):
		status = http.StatusUnprocessableEntity
		message = "Unprocessable entity: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errInvalidDottedOrder),
		errors.Is(err, errInvalidFeedbackConfig),
		errors.Is(err, errInvalidSession),
		errors.Is(err, feedback.ErrInvalidFeedbackKey),
		errors.Is(err, errInvalidInput):
		status = http.StatusBadRequest
		message = "Bad request: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errNotFound),
		errors.Is(err, errSessionNotFound):
		status = http.StatusNotFound
		message = "Not found: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errPayloadAlreadyReceived):
		status = http.StatusConflict
		message = "Conflict: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errUnsupportedContentEncoding):
		status = http.StatusUnsupportedMediaType
		message = "Unsupported Content-Encoding: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errTenantExceededUsageLimits):
		status = http.StatusTooManyRequests
		message = "Too many requests: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, context.Canceled),
		errors.Is(err, io.EOF),
		errors.Is(err, io.ErrUnexpectedEOF),
		errors.Is(err, http.ErrHandlerTimeout),
		strings.Contains(err.Error(), "failed to read from underlying reader"):
		status = 499 // Client closed request
		message = "Client closed connection"
		oplog.Warn(message, "error", err)

	case errors.Is(err, errUnauthorized):
		status = http.StatusForbidden
		message = "Unauthorized: " + err.Error()
		oplog.Warn(message, "error", err)

	default:
		// if it's known transient => 503, else 500
		if util.IsRetriableError(err) {
			status = http.StatusServiceUnavailable
			message = "Service unavailable: " + err.Error()
			oplog.Warn(message, "error", err)
		} else {
			status = http.StatusInternalServerError
			referenceId := uuid.NewString()
			message = "Internal server error: reference ID " + referenceId
			oplog.Error(message, "err", err, "reference_id", referenceId)
		}
	}

	render.Status(r, status)
	render.JSON(w, r, map[string]string{"error": message})
}

type ErrorResponse struct {
	// Error message
	Error string `json:"error" example:"Invalid request: missing required fields"`

	// Optional error details as JSON string
	Details string `json:"details,omitempty" example:"{\"field\":\"dataset_id\",\"reason\":\"required\"}"`
}

// CreateRun godoc
// @Summary      Create a Run
// @Description  Queues a single run for ingestion. The request body must be a JSON-encoded run object that follows the Run schema.
// @Tags         runs
// @Accept       json
// @Produce      json
// @Param        run  body      runs.Run  true  "Run object"
// @Success      202  {object}  map[string]string{message=string} "Run created"
// @Failure      400  {object}  ErrorResponse
// @Failure      403  {object}  ErrorResponse
// @Failure      409  {object}  ErrorResponse
// @Failure      422  {object}  ErrorResponse
// @Failure      429  {object}  ErrorResponse
// @Router       /runs [post]
func (h *RunHandler) CreateRun(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	tenantExceededUsageLimits, limitExceededMessage, err := h.UsageLimitsClient.HasTenantExceededUsageLimits(ctx, *authInfo)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("failed to check usage limits: %w", err))
		return
	}
	if tenantExceededUsageLimits {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, limitExceededMessage))
		return
	}

	raw, err := io.ReadAll(r.Body)
	if err != nil {
		if err == io.EOF {
			h.handleError(w, r, oplog, fmt.Errorf("client disconnected: %w", err))
		} else {
			h.handleError(w, r, oplog, fmt.Errorf("failed to read request body: %w", err))
		}
		return
	}

	run, err := ParseValidatePost(raw)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
		return
	}

	var runID *uuid.UUID
	if run.ID == nil {
		id := uuid.New()
		runID = &id
	} else {
		var err error
		runID, err = ParseUUID(run.ID, "run_id")
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
			return
		}
	}
	if runID == nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: run_id is required", errInvalidInput))
		return
	}

	ctx = config.LogAndContextSetField(ctx, "run_ids", slog.AnyValue([]string{runID.String()}))

	traceID, err := ParseUUID(run.TraceID, "trace_id")
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
		return
	}

	parentRunID, err := ParseUUID(run.ParentRunID, "parent_run_id")
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
		return
	}

	sessionID, err := ParseUUID(run.SessionID, "session_id")
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
		return
	}

	sessionName := run.SessionName

	startTime := run.StartTime

	dottedOrder := run.DottedOrder
	if dottedOrder == nil {
		dottedOrder = util.StringPtr("")
	}

	err = ValidateDottedOrder(traceID, *dottedOrder, parentRunID, *runID)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidDottedOrder, err))
		return
	}

	processInline := (dottedOrder != nil && *dottedOrder != "") && (traceID != nil && *traceID != uuid.Nil)

	payload := ingestion.QueuePayload{
		RunID:         *runID,
		ParentID:      parentRunID,
		TraceID:       traceID,
		Value:         raw,
		ContentType:   "application/json",
		HashKey:       "post",
		ProcessInline: processInline,
		SessionID:     sessionID,
		SessionName:   sessionName,
		StartTime:     startTime,
		Extra:         nil,
		AutoUpgrade:   run.ReferenceExampleID != nil && *run.ReferenceExampleID != "",
	}

	err = ingestion.EnsureSessionsBeforeQueueRunPayload(
		r.Context(),
		h.Pg,
		h.UsageLimitsClient,
		h.TracerSessionsClient,
		*authInfo,
		[]ingestion.QueuePayload{payload},
		nil,
	)
	if err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeUsageLimitExceeded:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, err.Message))
				return
			}
		}
		h.handleTracerSessionError(w, r, oplog, err)
		return
	}

	traceIDs := map[uuid.UUID]struct{}{}
	if traceID != nil && *traceID != uuid.Nil {
		traceIDs[*traceID] = struct{}{}
	}

	routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	err = ingestion.QueueRunPayload(r.Context(), routedRedisClient, queueingRedisClient, *authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	if err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeInvalidInput:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidInput, err))
			case ingestion.CodeInternal:
				h.handleError(w, r, oplog, fmt.Errorf("internal server error: %w", err))
			case ingestion.CodeTraceLimitExceeded:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTraceLimitExceeded, err))
			case ingestion.CodeDuplicate:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errPayloadAlreadyReceived, err))
			default:
				h.handleError(w, r, oplog, fmt.Errorf("error queueing runs: %w", err))
			}
		default:
			h.handleError(w, r, oplog, fmt.Errorf("error queueing runs: %w", err))
		}
		return
	}
	if lsredis.IsDualWriteEnabledForTenant(authInfo.TenantID) {
		dualWriteRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationDualWrite)
		if err := ingestion.QueueRunPayload(ctx, dualWriteRedisClient, queueingRedisClient, *authInfo, traceIDs, []ingestion.QueuePayload{payload}, false, true); err != nil {
			// Log error but continue execution
			oplog := httplog.LogEntry(ctx)
			oplog.Error("Error in dual write to Redis", "error", err)
		}
	}

	response := map[string]string{"message": "Run created"}
	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, response)
}

// UpdateRun godoc
// @Summary      Update a Run
// @Description  Updates a run identified by its ID. The body should contain only the fields to be changed; unknown fields are ignored.
// @Tags         runs
// @Accept       json
// @Produce      json
// @Param        run_id  path      string    true  "Run ID"  format(uuid)
// @Param        run     body      runs.Run  true  "Run update"
// @Success      202  {object}  map[string]string{message=string} "Run updated"
// @Failure      400  {object}  ErrorResponse
// @Failure      403  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      409  {object}  ErrorResponse
// @Failure      422  {object}  ErrorResponse
// @Failure      429  {object}  ErrorResponse
// @Router       /runs/{run_id} [patch]
func (h *RunHandler) UpdateRun(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	tenantExceededUsageLimits, limitExceededMessage, err := h.UsageLimitsClient.HasTenantExceededUsageLimits(ctx, *authInfo)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("failed to check usage limits: %w", err))
		return
	}
	if tenantExceededUsageLimits {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, limitExceededMessage))
		return
	}

	raw, err := io.ReadAll(r.Body)
	if err != nil {
		if err == io.EOF {
			h.handleError(w, r, oplog, fmt.Errorf("client disconnected: %w", err))
		} else {
			h.handleError(w, r, oplog, fmt.Errorf("failed to read request body: %w", err))
		}
		return
	}

	run, err := ParseValidatePatch(raw)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
		return
	}

	runIDParam := chi.URLParam(r, "run_id")
	if runIDParam == "" {
		h.handleError(w, r, oplog, fmt.Errorf("%w: run_id path parameter missing", errInvalidInput))
		return
	}
	runIDParsed, err := uuid.Parse(runIDParam)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: invalid UUID for run_id: %v", errParsingRun, err))
		return
	}

	ctx = config.LogAndContextSetField(ctx, "run_ids", slog.AnyValue([]string{runIDParsed.String()}))

	traceID, err := ParseUUID(run.TraceID, "trace_id")
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
		return
	}

	parentRunID, err := ParseUUID(run.ParentRunID, "parent_run_id")
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
		return
	}

	dottedOrder := run.DottedOrder
	if dottedOrder == nil {
		dottedOrder = util.StringPtr("")
	}

	err = ValidateDottedOrder(traceID, *dottedOrder, parentRunID, runIDParsed)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidDottedOrder, err))
		return
	}

	processInline := (dottedOrder != nil && *dottedOrder != "") && (traceID != nil && *traceID != uuid.Nil)

	payload := ingestion.QueuePayload{
		RunID:         runIDParsed,
		ParentID:      parentRunID,
		TraceID:       traceID,
		Value:         raw,
		ContentType:   "application/json",
		HashKey:       "patch",
		ProcessInline: processInline,
		Extra:         nil,
		AutoUpgrade:   run.ReferenceExampleID != nil && *run.ReferenceExampleID != "",
	}

	err = ingestion.EnsureSessionsBeforeQueueRunPayload(
		r.Context(),
		h.Pg,
		h.UsageLimitsClient,
		h.TracerSessionsClient,
		*authInfo,
		[]ingestion.QueuePayload{payload},
		nil,
	)
	if err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeUsageLimitExceeded:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, err.Message))
				return
			}
		}
		h.handleTracerSessionError(w, r, oplog, err)
		return
	}

	traceIDs := map[uuid.UUID]struct{}{}
	if traceID != nil && *traceID != uuid.Nil {
		traceIDs[*traceID] = struct{}{}
	}
	routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	err = ingestion.QueueRunPayload(ctx, routedRedisClient, queueingRedisClient, *authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	if err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeInvalidInput:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidInput, err))
			case ingestion.CodeInternal:
				h.handleError(w, r, oplog, fmt.Errorf("internal server error: %w", err))
			case ingestion.CodeTraceLimitExceeded:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTraceLimitExceeded, err))
			case ingestion.CodeDuplicate:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errPayloadAlreadyReceived, err))
			default:
				h.handleError(w, r, oplog, fmt.Errorf("error queueing runs: %w", err))
			}
		}
		return
	}

	if lsredis.IsDualWriteEnabledForTenant(authInfo.TenantID) {
		dualWriteRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationDualWrite)
		if err := ingestion.QueueRunPayload(ctx, dualWriteRedisClient, queueingRedisClient, *authInfo, traceIDs, []ingestion.QueuePayload{payload}, false, true); err != nil {
			// Log error but continue execution
			oplog := httplog.LogEntry(ctx)
			oplog.Error("Error in dual write to Redis", "error", err)
		}
	}

	response := map[string]string{"message": "Run updated"}
	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, response)
}

// IngestRunsMultipart godoc
// @Summary      Ingest Runs (Multipart)
//
//	@Description  Ingests multiple runs, feedback objects, and binary attachments in a single `multipart/form-data` request.
//	@Description  **Part‑name pattern**: `<event>.<run_id>[.<field>]` where `event` ∈ {`post`, `patch`, `feedback`, `attachment`}.
//	@Description  * `post|patch.<run_id>` – JSON run payload.
//	@Description  * `post|patch.<run_id>.<field>` – out‑of‑band run data (`inputs`, `outputs`, `events`, `error`, `extra`, `serialized`).
//	@Description  * `feedback.<run_id>` – JSON feedback payload (must include `trace_id`).
//	@Description  * `attachment.<run_id>.<filename>` – arbitrary binary attachment stored in S3.
//	@Description  **Headers**: every part must set `Content-Type` **and** either a `Content-Length` header or `length` parameter. Per‑part `Content-Encoding` is **not** allowed; the top‑level request may be `Content-Encoding: zstd`.
//	@Description  **Best performance** for high‑volume ingestion.
//
// @Tags         runs
// @Accept       multipart/form-data
// @Produce      json
// @Param        post.{run_id}                    formData  string  false  "Run to create (JSON)"                                format(binary)
// @Param        patch.{run_id}                   formData  string  false  "Run to update (JSON)"                                format(binary)
// @Param        post.{run_id}.inputs             formData  string  false  "Large inputs object (JSON) stored out‑of‑band"       format(binary)
// @Param        patch.{run_id}.outputs           formData  string  false  "Large outputs object (JSON) stored out‑of‑band"      format(binary)
// @Param        feedback.{run_id}                formData  string  false  "Feedback object (JSON) – must include trace_id"      format(binary)
// @Param        attachment.{run_id}.{filename}   formData  file    false  "Binary attachment linked to run {run_id}"            format(binary)
// @Success      202  {object}  map[string]string
// @Failure      400  {object}  ErrorResponse
// @Failure      403  {object}  ErrorResponse
// @Failure      409  {object}  ErrorResponse
// @Failure      422  {object}  ErrorResponse
// @Failure      429  {object}  ErrorResponse
// @Router       /runs/multipart [post]
func (h *MultipartRunHandler) IngestRunsMultipart(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	tenantExceededMonthlyLimits, limitExceededMessage, err := h.UsageLimitsClient.HasTenantExceededMonthlyLimits(ctx, *authInfo)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("failed to check usage limits: %w", err))
		return
	}
	if tenantExceededMonthlyLimits {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, limitExceededMessage))
		return
	}
	// limit size of request body
	r.Body = http.MaxBytesReader(w, r.Body, maxBytes)

	routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	result, err := ParseMultipartForm(ctx, r, authInfo, h.UsageLimitsClient, h.TracerSessionsClient, routedRedisClient, h.StorageClient)
	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	post := result.Posts
	patch := result.Patches
	extras := result.Extras

	runs := append(post, patch...)

	if len(runs) == 0 && len(result.Feedback) == 0 {
		h.handleError(w, r, oplog, errEmptyBatch)
		return
	}

	for _, run := range runs {
		dottedOrderVal := run.DottedOrder
		if dottedOrderVal == nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: missing 'dotted_order'", errParsingRun))
			return
		}
		traceID, err := ParseUUID(run.TraceID, "trace_id")
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
			return
		}
		if traceID == nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: trace_id cannot be nil", errParsingRun))
			return
		}

		parentRunID, err := ParseUUID(run.ParentRunID, "parent_run_id")
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
			return
		}

		runID, err := ParseUUID(run.ID, "run_id")
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
			return
		}
		if runID == nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: run_id cannot be nil", errParsingRun))
			return
		}

		err = ValidateDottedOrder(traceID, *dottedOrderVal, parentRunID, *runID)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidDottedOrder, err))
			return
		}
	}

	runIDs := make([]string, len(runs))
	for i, run := range runs {
		runIDs[i] = *run.ID
	}
	ctx = config.LogAndContextSetField(r.Context(), "run_ids", slog.AnyValue(runIDs))

	runsBytes := dumpsEach(runs)

	queuePayloads := make([]ingestion.QueuePayload, 0, len(runs)+len(result.Feedback))

	if len(result.Feedback) > 0 {
		feedbackKeys := make([]string, 0)
		for _, f := range result.Feedback {
			feedbackKeys = append(feedbackKeys, f.Key)
		}

		fetchedConfigs, err := h.FeedbackConfigClient.FetchFeedbackConfigsCached(r.Context(), *authInfo, h.ClickhouseConn, h.Pg, feedbackKeys)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errFetchingFeedbackConfigs, err))
			return
		}

		feedbackConfigsMap := make(map[string]*feedback.FeedbackConfig, len(fetchedConfigs))
		for _, cfg := range fetchedConfigs {
			feedbackConfigsMap[cfg.Key] = cfg.FeedbackConfig
		}

		for _, f := range result.Feedback {
			storedConfig := feedbackConfigsMap[f.Key]

			defaultConfig := feedback.GetDefaultFeedbackConfig([]feedback.FeedbackCreateSchema{f})

			feedbackConfig, err := feedback.ResolveFeedbackConfig(
				storedConfig,
				f.FeedbackConfig,
				defaultConfig,
			)
			if err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidFeedbackConfig, err))
				return
			}

			if err := feedback.VerifyFeedbackConfig(feedback.Feedback{FeedbackConfig: &feedbackConfig, Score: f.Score}, feedbackConfig); err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidFeedbackConfig, err))
				return
			}
		}
	}

	traceIDs := map[uuid.UUID]struct{}{}

	for i, run := range runs {
		traceID, _ := ParseUUID(run.TraceID, "trace_id")
		parentRunID, _ := ParseUUID(run.ParentRunID, "parent_run_id")
		runID, err := ParseUUID(run.ID, "run_id")
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
			return
		}
		if runID == nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: run_id cannot be nil", errParsingRun))
			return
		}

		sessionID, _ := ParseUUID(run.SessionID, "session_id")

		hashKey := "post"
		if i >= len(post) {
			hashKey = "patch"
		}

		payload := ingestion.QueuePayload{
			RunID:         *runID,
			ParentID:      parentRunID,
			TraceID:       traceID,
			Value:         runsBytes[i],
			ContentType:   "application/json",
			HashKey:       hashKey,
			ProcessInline: true,
			SessionID:     sessionID,
			SessionName:   run.SessionName,
			StartTime:     run.StartTime,
			Extra:         extras[runID.String()],
			AutoUpgrade:   run.ReferenceExampleID != nil && *run.ReferenceExampleID != "",
		}
		queuePayloads = append(queuePayloads, payload)

		if traceID != nil {
			traceIDs[*traceID] = struct{}{}
		}
	}

	for _, f := range result.Feedback {
		feedbackJSON, err := json.Marshal(f)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("failed to marshal feedback: %w", err))
			return
		}

		payload := ingestion.QueuePayload{
			RunID:         *f.RunID,
			TraceID:       f.TraceID,
			ParentID:      nil,
			SetKey:        util.StringPtr("feedback"),
			ContentType:   "application/json",
			Value:         feedbackJSON,
			ProcessInline: true,
		}
		queuePayloads = append(queuePayloads, payload)

		if f.TraceID != nil {
			traceIDs[*f.TraceID] = struct{}{}
		}
	}

	if !lsredis.IsRedisClusterIngestionEnabledForTenant(authInfo.TenantID) && lsredis.IsRedisShardedWritesByTraceEnabledForTenant(authInfo.TenantID) {
		// In this case we are still sharding, use traceID to determine shard
		runsByTraceID := make(map[uuid.UUID][]ingestion.QueuePayload)
		for _, payload := range queuePayloads {
			if payload.TraceID != nil {
				runsByTraceID[*payload.TraceID] = append(runsByTraceID[*payload.TraceID], payload)
			}
		}
		for traceID, payloads := range runsByTraceID {
			// Set the traceID as the routing Key
			routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, traceID.String(), lsredis.RedisOperationIngestion)
			// Queue to the same redis shard as the traceID
			queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, traceID.String(), lsredis.RedisOperationEnqueue)

			err = ingestion.QueueRunPayload(r.Context(), routedRedisClient, queueingRedisClient, *authInfo, traceIDs, payloads, false, false)
			if err != nil {
				break
			}
		}
	} else {
		routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
		queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

		err = ingestion.QueueRunPayload(r.Context(), routedRedisClient, queueingRedisClient, *authInfo, traceIDs, queuePayloads, false, false)
	}

	if err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeInvalidInput:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidInput, err))
			case ingestion.CodeDuplicate:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errPayloadAlreadyReceived, err))
			case ingestion.CodeTraceLimitExceeded:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTraceLimitExceeded, err))
			default:
				h.handleError(w, r, oplog, fmt.Errorf("failed to queue runs: %w", err))
			}
		}
		return
	}

	// Dual write to Redis
	if lsredis.IsDualWriteEnabledForTenant(authInfo.TenantID) {
		queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
		dualWriteRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationDualWrite)
		if err := ingestion.QueueRunPayload(ctx, dualWriteRedisClient, queueingRedisClient, *authInfo, traceIDs, queuePayloads, false, true); err != nil {
			// Log error but continue execution
			oplog := httplog.LogEntry(ctx)
			oplog.Error("Error in dual write to Redis", "error", err)
		}
	}
	response := map[string]string{"message": "Runs batch ingested"}
	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, response)
}

// IngestRunsBatch godoc
// @Summary      Ingest Runs (Batch JSON)
// @Description  Ingests a batch of runs in a single JSON payload. The payload must have `post` and/or `patch` arrays containing run objects.
// @Description  Prefer this endpoint over single‑run ingestion when submitting hundreds of runs, but `/runs/multipart` offers better handling for very large fields and attachments.
// @Tags         runs
// @Accept       json
// @Produce      json
// @Param        body  body  object{post=[]runs.Run,patch=[]runs.Run}  true  "Batch payload with 'post' and 'patch' arrays"
// @Success      202  {object}  map[string]string{message=string} "Runs batch ingested"
// @Failure      400  {object}  ErrorResponse
// @Failure      403  {object}  ErrorResponse
// @Failure      409  {object}  ErrorResponse
// @Failure      422  {object}  ErrorResponse
// @Failure      429  {object}  ErrorResponse
// @Router       /runs/batch [post]
func (h *BatchRunHandler) IngestRunsBatch(w http.ResponseWriter, r *http.Request) {
	didCheckLonglivedLimit := false
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	exceeded, message, err := h.UsageLimitsClient.HasTenantExceededUsageLimits(ctx, *authInfo)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("failed to check usage limits: %w", err))
		return
	}
	if exceeded {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, message))
		return
	}

	raw, err := io.ReadAll(r.Body)
	if err != nil {
		if errors.Is(err, io.EOF) {
			h.handleError(w, r, oplog, context.Canceled)
		} else {
			h.handleError(w, r, oplog, fmt.Errorf("failed to read request body: %w", err))
		}
		return
	}

	type BatchPayload struct {
		Post  []json.RawMessage `json:"post"`
		Patch []json.RawMessage `json:"patch"`
	}

	extras := make(map[string]map[string]ingestion.ExtraValue)

	var batchPayload BatchPayload
	if err := json.Unmarshal(raw, &batchPayload); err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidBatchJson, err))
		return
	}

	var rawMap map[string]interface{}
	if err := json.Unmarshal(raw, &rawMap); err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidBatchJson, err))
		return
	}
	if err := ValidateBatchSchema(rawMap); err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidBatchJson, err))
		return
	}

	var runs []Run
	for _, rawRun := range batchPayload.Post {
		run, err := ParseValidatePost(rawRun)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
			return
		}
		runs = append(runs, *run)
	}
	for _, rawRun := range batchPayload.Patch {
		run, err := ParseValidatePatch(rawRun)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errParsingRun, err))
			return
		}
		runs = append(runs, *run)
	}

	if len(runs) == 0 {
		h.handleError(w, r, oplog, errEmptyBatch)
		return
	}

	postCount := len(batchPayload.Post)

	sessionsByKey := make(map[sessionKey]*tracer_sessions.TracerSessionWithoutVirtualFields)
	sessionsByRun := make(map[string]*tracer_sessions.TracerSessionWithoutVirtualFields)
	uploads := []*storage.UploadAsyncResult{}

	runIDs := make([]string, len(runs))
	for i, run := range runs {
		runIDs[i] = *run.ID
	}
	ctx = config.LogAndContextSetField(r.Context(), "run_ids", slog.AnyValue(runIDs))

	for i, runObj := range runs {
		if runObj.ID == nil || runObj.TraceID == nil || runObj.DottedOrder == nil {
			h.handleError(w, r, oplog, fmt.Errorf("invalid run: missing id/trace_id/dotted_order: %w", errParsingRun))
			return
		}
		runUUID, err := uuid.Parse(*runObj.ID)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: invalid run_id: %v", errParsingRun, err))
			return
		}
		traceUUID, err := uuid.Parse(*runObj.TraceID)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: invalid trace_id: %v", errParsingRun, err))
			return
		}
		var parentUUID *uuid.UUID
		if runObj.ParentRunID != nil {
			tmp, err := uuid.Parse(*runObj.ParentRunID)
			if err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("%w: invalid parent_run_id: %v", errParsingRun, err))
				return
			}
			parentUUID = &tmp
		}
		if err := ValidateDottedOrder(&traceUUID, *runObj.DottedOrder, parentUUID, runUUID); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidDottedOrder, err))
			return
		}

		sessKey := newSessionKey(runObj.SessionID, runObj.SessionName)
		sess, found := sessionsByKey[sessKey]
		if !found {
			sess, err = h.TracerSessionsClient.StartOrFetchTracerSession(
				r.Context(),
				*authInfo,
				runObj.SessionID,
				runObj.SessionName,
				runObj.StartTime,
			)
			if err != nil {
				h.handleTracerSessionError(w, r, oplog, err)
				return
			}
			sessionsByKey[sessKey] = sess
		}
		if sessionsByRun[*runObj.ID] == nil {
			sessionsByRun[*runObj.ID] = sess
		}

		session, ok := sessionsByRun[*runObj.ID]
		if !ok {
			oplog.Error("Failed to get session for run", "run_id", *runObj.ID)
			continue
		}

		if config.Env.FFTraceTiersEnabled && session.TraceTier != nil && *session.TraceTier == tracer_sessions.LongLived && !didCheckLonglivedLimit {
			tenantExceededLonglivedLimits, limitExceededMessage, err := h.UsageLimitsClient.CheckLonglivedUsageLimits(r.Context(), *authInfo)
			if err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: %w", errCheckingUsageLimits, *runObj.ID, err))
				return
			}
			if tenantExceededLonglivedLimits {
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, limitExceededMessage))
				return
			}
			didCheckLonglivedLimit = true
		}

		store := fetchOrInitExtrasSlot(extras, *runObj.ID)
		newRun, partialUploads, err := HandleLargeFields(
			r.Context(), runObj, store, authInfo, session, h.StorageClient,
		)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("failed uploading large fields for run %s: %v", *runObj.ID, err))
			return
		}
		runs[i] = newRun
		uploads = append(uploads, partialUploads...)
	}

	payloads := make([]ingestion.QueuePayload, len(runs))
	traceIDs := map[uuid.UUID]struct{}{}
	for i, runObj := range runs {
		runUUID, _ := uuid.Parse(*runObj.ID)
		traceUUID, _ := uuid.Parse(*runObj.TraceID)

		hashKey := "post"
		if i >= postCount {
			hashKey = "patch"
		}

		runBytes, _ := json.Marshal(runObj)

		sessionID, _ := ParseUUID(runObj.SessionID, "session_id")
		parentRunID, _ := ParseUUID(runObj.ParentRunID, "parent_run_id")

		payloads[i] = ingestion.QueuePayload{
			RunID:         runUUID,
			ParentID:      parentRunID,
			TraceID:       &traceUUID,
			Value:         runBytes,
			ContentType:   "application/json",
			HashKey:       hashKey,
			ProcessInline: true,
			SessionID:     sessionID,
			SessionName:   runObj.SessionName,
			StartTime:     runObj.StartTime,
			Extra:         extras[*runObj.ID],
			AutoUpgrade:   runObj.ReferenceExampleID != nil && *runObj.ReferenceExampleID != "",
		}
		traceIDs[traceUUID] = struct{}{}
	}

	for _, uploadResult := range uploads {
		if uploadResult == nil {
			continue
		}
		if err := uploadResult.Wait(); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %w", errUploadingPart, err))
			return
		}
	}

	routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	if err := ingestion.QueueRunPayload(
		r.Context(),
		routedRedisClient,
		queueingRedisClient,
		*authInfo,
		traceIDs,
		payloads,
		false,
		false,
	); err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeInvalidInput:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidInput, err))
			case ingestion.CodeDuplicate:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errPayloadAlreadyReceived, err))
			case ingestion.CodeTraceLimitExceeded:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTraceLimitExceeded, err))
			default:
				h.handleError(w, r, oplog, fmt.Errorf("failed to queue runs: %w", err))
			}
		}
		return
	}

	if lsredis.IsDualWriteEnabledForTenant(authInfo.TenantID) {
		dualWriteRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationDualWrite)
		if err := ingestion.QueueRunPayload(ctx, dualWriteRedisClient, queueingRedisClient, *authInfo, traceIDs, payloads, false, true); err != nil {
			// Log error but continue execution
			oplog := httplog.LogEntry(ctx)
			oplog.Error("Error in dual write to Redis", "error", err)
		}
	}

	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, map[string]string{"message": "Runs batch ingested"})
}

func HandleLargeFields(
	ctx context.Context,
	run Run,
	store map[string]ingestion.ExtraValue,
	authInfo *auth.AuthInfo,
	sessionObj *tracer_sessions.TracerSessionWithoutVirtualFields,
	stgClient storage.StorageClient,
) (Run, []*storage.UploadAsyncResult, error) {

	var uploads []*storage.UploadAsyncResult
	threshold := config.Env.MinBlobStorageSizeKb * 1024
	uploadJSON := func(fieldName string, val interface{}) error {
		data, err := json.Marshal(val)
		if err != nil {
			return err
		}
		if int64(len(data)) <= threshold || !config.Env.BlobStorageEnabled {
			// store inline
			store[fieldName] = ingestion.ExtraValue{
				ContentType: "application/json",
				Encoding:    "",
				Data:        data,
			}
			return nil
		}
		// spool to S3
		bucket := selectMultipartBucket(authInfo)
		key, err := generateStorageKey(
			authInfo.TenantID,
			sessionObj.ID.String(),
			*run.ID,
			string(*sessionObj.TraceTier),
			IoType(fieldName),
			bucket,
		)
		if err != nil {
			return err
		}

		inp := &storage.UploadObjectInput{
			Bucket:        bucket,
			Key:           key,
			Reader:        bytes.NewReader(data),
			ContentType:   "application/json",
			ContentLength: int64(len(data)),
		}
		uRes, err := stgClient.UploadObjectAsync(ctx, inp)
		if err != nil {
			return err
		}
		uploads = append(uploads, uRes)

		store[fieldName] = ingestion.ExtraValue{
			ContentType: "",
			Encoding:    "",
			Data:        []byte(key),
		}
		return nil
	}

	// for each known large field, if present => upload + nil out
	if run.Inputs != nil {
		if err := uploadJSON("inputs", run.Inputs); err != nil {
			return run, nil, err
		}
		run.Inputs = map[string]interface{}{}
	}
	if run.Outputs != nil {
		if err := uploadJSON("outputs", run.Outputs); err != nil {
			return run, nil, err
		}
		run.Outputs = map[string]interface{}{}
	}
	if len(run.Events) > 0 {
		if err := uploadJSON("events", run.Events); err != nil {
			return run, nil, err
		}
		run.Events = []map[string]interface{}{}
	}
	if run.Error != nil {
		if err := uploadJSON("error", *run.Error); err != nil {
			return run, nil, err
		}
		run.Error = nil
	}
	if len(run.Extra) > 0 {
		if err := uploadJSON("extra", run.Extra); err != nil {
			return run, nil, err
		}
		run.Extra = map[string]interface{}{}
	}
	if len(run.Serialized) > 0 {
		if err := uploadJSON("serialized", run.Serialized); err != nil {
			return run, nil, err
		}
		run.Serialized = map[string]interface{}{}
	}

	return run, uploads, nil
}

func dumpsEach(runs []Run) [][]byte {
	result := make([][]byte, len(runs))
	for i, run := range runs {
		json, _ := json.Marshal(run)
		result[i] = json
	}
	return result
}
