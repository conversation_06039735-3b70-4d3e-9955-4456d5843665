package beacon

import (
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"github.com/ggicci/httpin"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"langchain.com/smith/metronome"
)

type UsagePayload struct {
	License       string    `json:"license" validate:"required"`
	StartingOn    time.Time `json:"starting_on" validate:"required"`
	EndingBefore  time.Time `json:"ending_before" validate:"required"`
	OnCurrentPlan bool      `json:"on_current_plan"`
}

type UsageRequest struct {
	Payload *UsagePayload `in:"body"`
}

func (h *BeaconHandler) GetUsage(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())

	reqBody := r.Context().Value(httpin.Input).(*UsageRequest)
	req := reqBody.Payload
	err := h.validator.Struct(req)
	if err != nil {
		oplog.Warn("Invalid request", "error", err)
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	licenseInfo, err := verifyLicense(r.Context(), h.db, req.License, json.RawMessage(`{}`))
	if err != nil {
		var le *LicenseError
		if errors.As(err, &le) {
			oplog.Warn("Error verifying license", "error", le)
			http.Error(w, le.Message, le.StatusCode)
			return
		}
	}
	if licenseInfo == nil {
		oplog.Warn("Error verifying license", "error", err)
		http.Error(w, "Error verifying license", http.StatusInternalServerError)
		return
	}

	metronomeClient := metronome.GetMetronomeClient()

	if metronomeClient == nil {
		oplog.Error("Metronome client is nil")
		http.Error(w, "Error fetching usage", http.StatusInternalServerError)
		return
	}

	if licenseInfo.MetronomeCustomerId == nil {
		oplog.Error("Metronome customer id is nil", "customer_id", licenseInfo.CustomerID)
		http.Error(w, "Error fetching usage", http.StatusInternalServerError)
		return
	}

	// returning monthly usage for now, may adjust request params later to allow for more control
	response, err := metronome.GetMonthlyUsage(r.Context(), metronomeClient, *licenseInfo.MetronomeCustomerId, req.StartingOn, req.EndingBefore, req.OnCurrentPlan)
	if err != nil {
		oplog.Error("Error fetching from usage from metronome", "error", err)
		http.Error(w, "Error fetching usage", http.StatusInternalServerError)
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, response)
}
