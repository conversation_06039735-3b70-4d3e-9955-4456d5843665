package beacon

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"io"

	"github.com/ggicci/httpin"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/jwtauth/v5"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/license"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil/leak"
)

func TestBeaconHandler(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer dbpool.Close()

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	// Create a new chi router
	r := chi.NewRouter()

	// Mount the middleware and handlers
	r.Route("/v1/beacon", func(r chi.Router) {
		r.Use(middleware.Timeout(60 * time.Second))

		beaconHandler := NewBeaconHandler(dbpool)

		r.With(
			httpin.NewInput(VerifyRequest{}),
		).Post("/verify", beaconHandler.VerifyLicenseKey)
		r.With(httpin.NewInput(IngestTracesRequest{})).Post("/ingest-traces", beaconHandler.IngestTraces)
		r.With(ah.XServiceKeyMiddleware,
			httpin.NewInput(CreateLicenseRequest{}),
		).Post("/internal/create-license", beaconHandler.InternalCreateLicense)
		r.With(ah.XServiceKeyMiddleware,
			httpin.NewInput(CreateCustomerRequest{}),
		).Post("/internal/create-customer", beaconHandler.InternalCreateCustomer)
		r.With(httpin.NewInput(SubmitMetadataRequest{})).Post("/metadata/submit", beaconHandler.SubmitMetadata)
		r.With(httpin.NewInput(UsageRequest{})).Post("/usage", beaconHandler.GetUsage)
	})

	// Create a test server using httptest
	ts := httptest.NewServer(r)
	defer ts.Close()

	runVerifyTests(t, dbpool, ts)
	runIngestTracesTests(t, dbpool, ts)
	runCreateCustomerTests(t, dbpool, ts)
	runCreateLicenseTests(t, dbpool, ts)
	runSubmitMetadataTests(t, dbpool, ts)
	runUsageTests(t, dbpool, ts)
}

func runVerifyTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
	jwt := jwtauth.New(
		"HS256",
		[]byte(config.Env.XServiceAuthJwtSecret),
		nil,
	)

	claims := map[string]interface{}{
		"sub":       "unspecified",
		"tenant_id": expectedTenantId,
		"exp":       time.Now().Add(5 * time.Minute).Unix(),
	}

	_, xServiceToken, err := jwt.Encode(claims)
	if err != nil {
		t.Fatalf("Failed to encode JWT: %v", err)
	}

	// Step 1: Create a customer using the InternalCreateCustomer endpoint
	createCustomerRequest := map[string]interface{}{
		"customer_name": "Ingest Test Customer",
		"customer_config": map[string]interface{}{
			"lgp_enabled": true,
		},
	}

	reqBodyBytes, err := json.Marshal(createCustomerRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create customer request: %v", err)
	}

	// Create the request to the create-customer endpoint
	createCustomerReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createCustomerReq.Header.Set("Content-Type", "application/json")
	createCustomerReq.Header.Set("X-Service-Key", xServiceToken)

	client := &http.Client{}
	createCustomerResp, err := client.Do(createCustomerReq)
	if err != nil {
		t.Fatalf("Failed to make create customer request: %v", err)
	}
	defer createCustomerResp.Body.Close()

	if createCustomerResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createCustomerResp.StatusCode)
	}

	var createCustomerResponse map[string]interface{}
	err = json.NewDecoder(createCustomerResp.Body).Decode(&createCustomerResponse)
	if err != nil {
		t.Fatalf("Failed to decode create customer response: %v", err)
	}

	// Step 2: Create a license using the InternalCreateLicense endpoint
	createLicenseRequest := map[string]interface{}{
		"customer_id":      createCustomerResponse["customer_id"],
		"license_type":     "langsmith",
		"expiration_weeks": 4,
	}

	reqBodyBytes, err = json.Marshal(createLicenseRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create license request: %v", err)
	}

	// Create the request to the create-license endpoint
	createLicenseReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createLicenseReq.Header.Set("Content-Type", "application/json")
	createLicenseReq.Header.Set("X-Service-Key", xServiceToken)

	createLicenseResp, err := client.Do(createLicenseReq)
	if err != nil {
		t.Fatalf("Failed to make create license request: %v", err)
	}
	defer createLicenseResp.Body.Close()

	if createLicenseResp.StatusCode != http.StatusOK {
		// Print the response body for debugging
		bodyBytes, _ := ioutil.ReadAll(createLicenseResp.Body)
		fmt.Printf("Response body: %s\n", string(bodyBytes))
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createLicenseResp.StatusCode)
	}

	var createLicenseResponse map[string]string
	err = json.NewDecoder(createLicenseResp.Body).Decode(&createLicenseResponse)
	if err != nil {
		t.Fatalf("Failed to decode create license response: %v", err)
	}

	licenseKey, ok := createLicenseResponse["license_key"]
	if !ok {
		t.Fatalf("License key not found in create license response")
	}

	t.Run("missing license", func(t *testing.T) {
		reqBody := `{}`
		resp, err := http.Post(ts.URL+"/v1/beacon/verify", "application/json", strings.NewReader(reqBody))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Fatalf("Expected status %d, got %d", http.StatusUnauthorized, resp.StatusCode)
		}
	})

	t.Run("invalid license", func(t *testing.T) {
		nonPersistedKey, _, err := GenerateKeyAndHash("lcl")
		if err != nil {
			t.Fatalf("Failed to generate key and hash: %v", err)
		}
		reqBody := fmt.Sprintf(`{"license": "%s"}`, nonPersistedKey)
		resp, err := http.Post(ts.URL+"/v1/beacon/verify", "application/json", strings.NewReader(reqBody))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusForbidden {
			t.Fatalf("Expected status %d, got %d", http.StatusForbidden, resp.StatusCode)
		}
	})

	t.Run("valid license", func(t *testing.T) {
		reqBody := fmt.Sprintf(`{"license": "%s"}`, licenseKey)
		resp, err := http.Post(ts.URL+"/v1/beacon/verify", "application/json", strings.NewReader(reqBody))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Optionally, check the response body
		var responseData map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		token, ok := responseData["token"]
		if !ok {
			t.Fatalf("Expected 'token' in response")
		}
		// Token should be valid
		tokenString, ok := token.(string)
		if !ok {
			t.Fatalf("Expected 'token' to be a string")
		}
		// Define our claims structure or use jwt.MapClaims.
		licenseClaims, err := license.DecodeLicenseJWT(tokenString, license.GetSigningKey)

		// If you have a custom claim named 'foo'
		lgpEnabled, ok := licenseClaims["lgp_enabled"].(bool)
		if !ok || !lgpEnabled {
			t.Fatalf("Expected 'lgp_enabled' to be true")
		}

		exitChan := make(chan int)
		exitFunc := func(code int) {
			exitChan <- code
		}
		// Create a context with cancellation
		ctx, cancel := context.WithCancel(context.Background())

		redisPool := lsredis.SingleRedisConnect()
		defer func(redis redis.UniversalClient) {
			err := redis.Close()
			if err != nil {
				fmt.Printf("Error closing redis connections: %v\n", err)
			}
		}(redisPool)

		// Ensure the context is canceled when the test finishes
		license.StartLicenseVerification(ctx, tokenString, exitFunc, redisPool, 100*time.Millisecond)
		select {
		case code := <-exitChan:
			t.Fatalf("Invalid token: %d", code)
		case <-time.After(1 * time.Second):
			cancel()
		}
	})

	t.Run("valid license with metadata", func(t *testing.T) {
		// Send version as metadata
		reqBody := fmt.Sprintf(`{"license": "%s", "metadata": {"version": "1.0.0"}}`, licenseKey)
		resp, err := http.Post(ts.URL+"/v1/beacon/verify", "application/json", strings.NewReader(reqBody))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Optionally, check the response body
		var responseData map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		token, ok := responseData["token"]
		if !ok {
			t.Fatalf("Expected 'token' in response")
		}
		// Token should be valid
		tokenString, ok := token.(string)
		if !ok {
			t.Fatalf("Expected 'token' to be a string")
		}
		exitChan := make(chan int)
		exitFunc := func(code int) {
			exitChan <- code
		}
		// Create a context with cancellation
		ctx, cancel := context.WithCancel(context.Background())

		redisPool := lsredis.SingleRedisConnect()
		defer func(redis redis.UniversalClient) {
			err := redis.Close()
			if err != nil {
				fmt.Printf("Error closing redis connections: %v\n", err)
			}
		}(redisPool)

		// Ensure the context is canceled when the test finishes
		license.StartLicenseVerification(ctx, tokenString, exitFunc, redisPool, 100*time.Millisecond)
		select {
		case code := <-exitChan:
			t.Fatalf("Invalid token: %d", code)
		case <-time.After(1 * time.Second):
			cancel()
		}
	})

	t.Run("valid license with metronome_customer_id", func(t *testing.T) {
		// First, create a new customer with metronome_customer_id
		metronomeCustomerId := uuid.New().String()

		// Manually update the customer to add a metronome_customer_id
		_, err := dbpool.Exec(context.Background(), `
			UPDATE self_hosted_customers 
			SET metronome_customer_id = $1 
			WHERE id = $2
		`, metronomeCustomerId, createCustomerResponse["customer_id"])

		if err != nil {
			t.Fatalf("Failed to update customer with metronome_customer_id: %v", err)
		}

		// Call verifyLicense directly to test the functionality
		ctx := context.Background()
		// Empty metadata for this test
		var emptyMetadata json.RawMessage = []byte("{}")

		licenseInfo, err := verifyLicense(ctx, dbpool, licenseKey, emptyMetadata)
		if err != nil {
			t.Fatalf("Failed to verify license: %v", err)
		}

		// Verify the license info contains the metronome_customer_id
		if licenseInfo.MetronomeCustomerId == nil {
			t.Fatalf("Expected MetronomeCustomerId to be non-nil")
		}

		if *licenseInfo.MetronomeCustomerId != metronomeCustomerId {
			t.Fatalf("Expected MetronomeCustomerId to be %s, got %s",
				metronomeCustomerId, *licenseInfo.MetronomeCustomerId)
		}
	})
}

func runIngestTracesTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
	jwt := jwtauth.New(
		"HS256",
		[]byte(config.Env.XServiceAuthJwtSecret),
		nil,
	)

	claims := map[string]interface{}{
		"sub":       "unspecified",
		"tenant_id": expectedTenantId,
		"exp":       time.Now().Add(5 * time.Minute).Unix(),
	}

	_, xServiceToken, err := jwt.Encode(claims)
	if err != nil {
		t.Fatalf("Failed to encode JWT: %v", err)
	}

	// Step 1: Create a customer using the InternalCreateCustomer endpoint
	createCustomerRequest := map[string]interface{}{
		"customer_name": "Ingest Test Customer",
		"customer_config": map[string]interface{}{
			"lgp_enabled": true,
		},
	}

	reqBodyBytes, err := json.Marshal(createCustomerRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create customer request: %v", err)
	}

	// Create the request to the create-customer endpoint
	createCustomerReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createCustomerReq.Header.Set("Content-Type", "application/json")
	createCustomerReq.Header.Set("X-Service-Key", xServiceToken)

	client := &http.Client{}
	createCustomerResp, err := client.Do(createCustomerReq)
	if err != nil {
		t.Fatalf("Failed to make create customer request: %v", err)
	}
	defer createCustomerResp.Body.Close()

	if createCustomerResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createCustomerResp.StatusCode)
	}

	var createCustomerResponse map[string]interface{}
	err = json.NewDecoder(createCustomerResp.Body).Decode(&createCustomerResponse)
	if err != nil {
		t.Fatalf("Failed to decode create customer response: %v", err)
	}

	// Step 2: Create a license using the InternalCreateLicense endpoint
	createLicenseRequest := map[string]interface{}{
		"customer_id":      createCustomerResponse["customer_id"],
		"license_type":     "langsmith",
		"expiration_weeks": 4,
	}

	reqBodyBytes, err = json.Marshal(createLicenseRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create license request: %v", err)
	}

	// Create the request to the create-license endpoint
	createLicenseReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createLicenseReq.Header.Set("Content-Type", "application/json")
	createLicenseReq.Header.Set("X-Service-Key", xServiceToken)

	createLicenseResp, err := client.Do(createLicenseReq)
	if err != nil {
		t.Fatalf("Failed to make create license request: %v", err)
	}
	defer createLicenseResp.Body.Close()

	if createLicenseResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createLicenseResp.StatusCode)
	}

	var createLicenseResponse map[string]string
	err = json.NewDecoder(createLicenseResp.Body).Decode(&createLicenseResponse)
	if err != nil {
		t.Fatalf("Failed to decode create license response: %v", err)
	}

	licenseKey, ok := createLicenseResponse["license_key"]
	if !ok {
		t.Fatalf("License key not found in create license response")
	}

	t.Run("invalid license", func(t *testing.T) {
		// Prepare trace transactions
		traceTransactions := []map[string]interface{}{
			{
				"id":                       uuid.New().String(),
				"tenant_id":                uuid.New().String(),
				"session_id":               uuid.New().String(),
				"trace_count":              5,
				"start_insertion_time":     time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"end_insertion_time":       time.Now().Format(time.RFC3339),
				"start_interval_time":      time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
				"end_interval_time":        time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"status":                   "completed",
				"num_failed_send_attempts": 0,
				"transaction_type":         "type1",
				"organization_id":          uuid.New().String(),
			},
		}

		requestBody := map[string]interface{}{
			"license":            "foo",
			"trace_transactions": traceTransactions,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		resp, err := http.Post(ts.URL+"/v1/beacon/ingest-traces", "application/json", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Fatalf("Expected status %d, got %d", http.StatusUnauthorized, resp.StatusCode)
		}
	})

	t.Run("valid ingest traces", func(t *testing.T) {
		// Prepare trace transactions
		traceTransactions := []map[string]interface{}{
			{
				"id":                       uuid.New().String(),
				"tenant_id":                uuid.New().String(),
				"session_id":               uuid.New().String(),
				"trace_count":              5,
				"start_insertion_time":     time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"end_insertion_time":       time.Now().Format(time.RFC3339),
				"start_interval_time":      time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
				"end_interval_time":        time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"status":                   "completed",
				"num_failed_send_attempts": 0,
				"transaction_type":         "type1",
				"organization_id":          uuid.New().String(),
			},
		}

		requestBody := map[string]interface{}{
			"license":            licenseKey,
			"trace_transactions": traceTransactions,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		resp, err := http.Post(ts.URL+"/v1/beacon/ingest-traces", "application/json", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}
		response := map[string]interface{}{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}
		countStr, ok := response["inserted_count"]
		if !ok {
			t.Fatalf("Expected 'inserted_count' to be present in the response")
		}
		count, err := strconv.Atoi(countStr.(string))
		if err != nil {
			t.Fatalf("Error converting 'inserted_count' to int: %v", err)
		}
		if count != 1 {
			t.Fatalf("Expected 1 inserted count, got %d", count)
		}
	})

	t.Run("valid ingest many traces", func(t *testing.T) {
		// Prepare trace transactions
		traceTransactions := []map[string]interface{}{
			{
				"id":                       uuid.New().String(),
				"tenant_id":                uuid.New().String(),
				"session_id":               uuid.New().String(),
				"trace_count":              5,
				"start_insertion_time":     time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"end_insertion_time":       time.Now().Format(time.RFC3339),
				"start_interval_time":      time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
				"end_interval_time":        time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"status":                   "completed",
				"num_failed_send_attempts": 0,
				"transaction_type":         "type1",
				"organization_id":          uuid.New().String(),
			},
			{
				"id":                       uuid.New().String(),
				"tenant_id":                uuid.New().String(),
				"session_id":               uuid.New().String(),
				"trace_count":              5,
				"start_insertion_time":     time.Now().Add(time.Hour).Format(time.RFC3339),
				"end_insertion_time":       time.Now().Add(time.Hour).Format(time.RFC3339),
				"start_interval_time":      time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"end_interval_time":        time.Now().Add(time.Hour).Format(time.RFC3339),
				"status":                   "completed",
				"num_failed_send_attempts": 0,
				"transaction_type":         "type1",
				"organization_id":          uuid.New().String(),
			},
		}

		requestBody := map[string]interface{}{
			"license":            licenseKey,
			"trace_transactions": traceTransactions,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		resp, err := http.Post(ts.URL+"/v1/beacon/ingest-traces", "application/json", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}
		response := map[string]interface{}{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}
		countStr, ok := response["inserted_count"]
		if !ok {
			t.Fatalf("Expected 'inserted_count' to be present in the response")
		}
		count, err := strconv.Atoi(countStr.(string))
		if err != nil {
			t.Fatalf("Error converting 'inserted_count' to int: %v", err)
		}
		if count != 2 {
			t.Fatalf("Expected 2 inserted count, got %d", count)
		}
	})

	t.Run("valid ingest traces dummy org id", func(t *testing.T) {
		// Prepare trace transactions
		traceTransactions := []map[string]interface{}{
			{
				"id":                       uuid.New().String(),
				"tenant_id":                uuid.New().String(),
				"session_id":               uuid.New().String(),
				"trace_count":              5,
				"start_insertion_time":     time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"end_insertion_time":       time.Now().Format(time.RFC3339),
				"start_interval_time":      time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
				"end_interval_time":        time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"status":                   "completed",
				"num_failed_send_attempts": 0,
				"transaction_type":         "type1",
				// ********-0000-0000-0000-********0000 is a dummy org id
				"organization_id": "********-0000-0000-0000-********0000",
			},
			{
				"id":                       uuid.New().String(),
				"tenant_id":                uuid.New().String(),
				"session_id":               uuid.New().String(),
				"trace_count":              5,
				"start_insertion_time":     time.Now().Add(time.Hour).Format(time.RFC3339),
				"end_insertion_time":       time.Now().Add(time.Hour).Format(time.RFC3339),
				"start_interval_time":      time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
				"end_interval_time":        time.Now().Add(time.Hour).Format(time.RFC3339),
				"status":                   "completed",
				"num_failed_send_attempts": 0,
				"transaction_type":         "type1",
				"organization_id":          uuid.New().String(),
			},
		}

		requestBody := map[string]interface{}{
			"license":            licenseKey,
			"trace_transactions": traceTransactions,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		resp, err := http.Post(ts.URL+"/v1/beacon/ingest-traces", "application/json", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}
		response := map[string]interface{}{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}
		countStr, ok := response["inserted_count"]
		if !ok {
			t.Fatalf("Expected 'inserted_count' to be present in the response")
		}
		count, err := strconv.Atoi(countStr.(string))
		if err != nil {
			t.Fatalf("Error converting 'inserted_count' to int: %v", err)
		}
		if count != 2 {
			t.Fatalf("Expected 2 inserted count, got %d", count)
		}
	})
}

func runCreateLicenseTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
	jwt := jwtauth.New(
		"HS256",
		[]byte(config.Env.XServiceAuthJwtSecret),
		nil,
	)

	claims := map[string]interface{}{
		"sub":       "unspecified",
		"tenant_id": expectedTenantId,
		"exp":       time.Now().Add(5 * time.Minute).Unix(),
	}

	_, xServiceToken, err := jwt.Encode(claims)
	if err != nil {
		t.Fatalf("Failed to encode JWT: %v", err)
	}

	// Step 1: Create a customer using the InternalCreateCustomer endpoint
	createCustomerRequest := map[string]interface{}{
		"customer_name": "Ingest Test Customer",
		"customer_config": map[string]interface{}{
			"lgp_enabled": true,
		},
	}

	reqBodyBytes, err := json.Marshal(createCustomerRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create customer request: %v", err)
	}

	// Create the request to the create-customer endpoint
	createCustomerReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createCustomerReq.Header.Set("Content-Type", "application/json")
	createCustomerReq.Header.Set("X-Service-Key", xServiceToken)

	client := &http.Client{}
	createCustomerResp, err := client.Do(createCustomerReq)
	if err != nil {
		t.Fatalf("Failed to make create customer request: %v", err)
	}
	defer createCustomerResp.Body.Close()

	if createCustomerResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createCustomerResp.StatusCode)
	}

	var createCustomerResponse map[string]interface{}
	err = json.NewDecoder(createCustomerResp.Body).Decode(&createCustomerResponse)
	if err != nil {
		t.Fatalf("Failed to decode create customer response: %v", err)
	}
	customerId := createCustomerResponse["customer_id"]
	t.Run("missing x-service-key", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"customer_id":     customerId,
			"license_type":    "langsmith",
			"expiration_date": time.Now().AddDate(0, 0, 7),
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}
	})

	t.Run("missing fields fail", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"license_type":     "langsmith",
			"expiration_weeks": 4,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")

		// Add the X-Service-Key header required by the middleware
		req.Header.Set("X-Service-Key", xServiceToken)

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusBadRequest {
			t.Fatalf("Expected status %d, got %d", http.StatusBadRequest, resp.StatusCode)
		}
	})

	t.Run("missing customer fails", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"customer_id":      uuid.New(),
			"license_type":     "langsmith",
			"expiration_weeks": 4,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")

		// Add the X-Service-Key header required by the middleware
		req.Header.Set("X-Service-Key", xServiceToken)

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusNotFound {
			t.Fatalf("Expected status %d, got %d", http.StatusNotFound, resp.StatusCode)
		}
	})

	t.Run("create license", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"customer_id":      customerId,
			"license_type":     "langsmith",
			"expiration_weeks": 4,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")

		// Add the X-Service-Key header required by the middleware
		req.Header.Set("X-Service-Key", xServiceToken)

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Parse the response
		var responseData map[string]string
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		key, ok := responseData["license_key"]
		if !ok {
			t.Fatalf("Expected 'license_key' in response")
		}
		if key == "" {
			t.Fatalf("Expected 'license_key' to be non-empty")
		}
		if !strings.HasPrefix(key, "lcl_") {
			t.Fatalf("Expected 'license_key' to start with 'lcl_'")
		}
	})
	t.Run("create license with date", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"customer_id":     customerId,
			"license_type":    "langsmith",
			"expiration_date": time.Now().Add(4 * 7 * 24 * time.Hour).Format(time.RFC3339),
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")

		// Add the X-Service-Key header required by the middleware
		req.Header.Set("X-Service-Key", xServiceToken)

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Logf("Request body: %s", reqBodyBytes)
			//read resp body
			respBody, _ := ioutil.ReadAll(resp.Body)
			t.Logf("Response body: %s", respBody)
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Parse the response
		var responseData map[string]string
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		key, ok := responseData["license_key"]
		if !ok {
			t.Fatalf("Expected 'license_key' in response")
		}
		if key == "" {
			t.Fatalf("Expected 'license_key' to be non-empty")
		}
		if !strings.HasPrefix(key, "lcl_") {
			t.Fatalf("Expected 'license_key' to start with 'lcl_'")
		}
	})
	t.Run("create license offline", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"customer_id":     customerId,
			"license_type":    "langsmith",
			"expiration_date": time.Now().Add(4 * 7 * 24 * time.Hour).Format(time.RFC3339),
			"offline_mode":    true,
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")

		// Add the X-Service-Key header required by the middleware
		req.Header.Set("X-Service-Key", xServiceToken)

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Logf("Request body: %s", reqBodyBytes)
			//read resp body
			respBody, _ := ioutil.ReadAll(resp.Body)
			t.Logf("Response body: %s", respBody)
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Parse the response
		var responseData map[string]string
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		key, ok := responseData["license_key"]
		if !ok {
			t.Fatalf("Expected 'license_key' in response")
		}
		if key == "" {
			t.Fatalf("Expected 'license_key' to be non-empty")
		}
		if strings.HasPrefix(key, "lcl_") {
			t.Fatalf("Expected 'license_key' be a jwt")
		}
	})
}

func runCreateCustomerTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
	jwt := jwtauth.New(
		"HS256",
		[]byte(config.Env.XServiceAuthJwtSecret),
		nil,
	)

	claims := map[string]interface{}{
		"sub":       "unspecified",
		"tenant_id": expectedTenantId,
		"exp":       time.Now().Add(5 * time.Minute).Unix(),
	}

	_, xServiceToken, err := jwt.Encode(claims)
	if err != nil {
		t.Fatalf("Failed to encode JWT: %v", err)
	}

	t.Run("missing x-service-key", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"customer_name": "New Customer",
			"customer_config": map[string]interface{}{
				"lgp_enabled": true,
			},
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		// Do not set the X-Service-Key header to simulate missing authentication

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusUnauthorized {
			t.Fatalf("Expected status %d, got %d", http.StatusUnauthorized, resp.StatusCode)
		}
	})

	t.Run("missing fields fail", func(t *testing.T) {
		// Prepare the request body with missing required fields
		requestBody := map[string]interface{}{
			"customer_config": map[string]interface{}{
				"lgp_enabled": true,
			},
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Service-Key", xServiceToken)

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusBadRequest {
			t.Fatalf("Expected status %d, got %d", http.StatusBadRequest, resp.StatusCode)
		}
	})

	t.Run("create customer", func(t *testing.T) {
		// Prepare the request body
		requestBody := map[string]interface{}{
			"customer_name": "New Customer",
			"customer_config": map[string]interface{}{
				"lgp_enabled": true,
			},
		}

		reqBodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		// Create the request
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
		if err != nil {
			t.Fatalf("Failed to create request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Service-Key", xServiceToken)

		// Send the request
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Parse the response
		var responseData map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Verify that customer_id is present and valid uuid
		customerID, ok := responseData["customer_id"]
		if !ok {
			t.Fatalf("Expected 'customer_id' in response")
		}
		_, err = uuid.Parse(customerID.(string))
		if err != nil {
			t.Fatalf("Expected 'customer_id' to be a valid UUID: %v", err)
		}
	})
}

func runUsageTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	// First, create a customer with a metronome_customer_id
	expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
	jwt := jwtauth.New(
		"HS256",
		[]byte(config.Env.XServiceAuthJwtSecret),
		nil,
	)

	claims := map[string]any{
		"sub":       "unspecified",
		"tenant_id": expectedTenantId,
		"exp":       time.Now().Add(5 * time.Minute).Unix(),
	}

	_, xServiceToken, err := jwt.Encode(claims)
	if err != nil {
		t.Fatalf("Failed to encode JWT: %v", err)
	}

	// Create a customer
	createCustomerRequest := map[string]any{
		"customer_name": "Usage Test Customer",
		"customer_config": map[string]any{
			"lgp_enabled": true,
		},
	}

	reqBodyBytes, err := json.Marshal(createCustomerRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create customer request: %v", err)
	}

	createCustomerReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createCustomerReq.Header.Set("Content-Type", "application/json")
	createCustomerReq.Header.Set("X-Service-Key", xServiceToken)

	client := &http.Client{}
	createCustomerResp, err := client.Do(createCustomerReq)
	if err != nil {
		t.Fatalf("Failed to make create customer request: %v", err)
	}
	defer createCustomerResp.Body.Close()

	if createCustomerResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createCustomerResp.StatusCode)
	}

	var createCustomerResponse map[string]any
	err = json.NewDecoder(createCustomerResp.Body).Decode(&createCustomerResponse)
	if err != nil {
		t.Fatalf("Failed to decode create customer response: %v", err)
	}

	// Set metronome_customer_id for the customer
	metronomeCustomerId := uuid.New().String()
	_, err = dbpool.Exec(context.Background(), `
		UPDATE self_hosted_customers 
		SET metronome_customer_id = $1 
		WHERE id = $2
	`, metronomeCustomerId, createCustomerResponse["customer_id"])
	if err != nil {
		t.Fatalf("Failed to update customer with metronome_customer_id: %v", err)
	}

	// Create a license for the customer
	createLicenseRequest := map[string]any{
		"customer_id":      createCustomerResponse["customer_id"],
		"license_type":     "langsmith",
		"expiration_weeks": 4,
	}

	reqBodyBytes, err = json.Marshal(createLicenseRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create license request: %v", err)
	}

	createLicenseReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createLicenseReq.Header.Set("Content-Type", "application/json")
	createLicenseReq.Header.Set("X-Service-Key", xServiceToken)

	createLicenseResp, err := client.Do(createLicenseReq)
	if err != nil {
		t.Fatalf("Failed to make create license request: %v", err)
	}
	defer createLicenseResp.Body.Close()

	if createLicenseResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createLicenseResp.StatusCode)
	}

	var createLicenseResponse map[string]string
	err = json.NewDecoder(createLicenseResp.Body).Decode(&createLicenseResponse)
	if err != nil {
		t.Fatalf("Failed to decode create license response: %v", err)
	}

	licenseKey, ok := createLicenseResponse["license_key"]
	if !ok {
		t.Fatalf("License key not found in create license response")
	}

	// Define test cases for the usage endpoint
	tests := []struct {
		name       string
		payload    map[string]any
		wantStatus int
	}{
		{
			name: "missing license",
			payload: map[string]any{
				"starting_on":     time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
				"ending_before":   time.Now().Format(time.RFC3339),
				"on_current_plan": false,
			},
			wantStatus: http.StatusBadRequest,
		},
		{
			name: "missing starting_on",
			payload: map[string]any{
				"license":         licenseKey,
				"ending_before":   time.Now().Format(time.RFC3339),
				"on_current_plan": false,
			},
			wantStatus: http.StatusBadRequest,
		},
		{
			name: "missing ending_before",
			payload: map[string]any{
				"license":         licenseKey,
				"starting_on":     time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
				"on_current_plan": false,
			},
			wantStatus: http.StatusBadRequest,
		},
		{
			name: "invalid license",
			payload: map[string]any{
				"license":         "invalid_license_key",
				"starting_on":     time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
				"ending_before":   time.Now().Format(time.RFC3339),
				"on_current_plan": false,
			},
			wantStatus: http.StatusUnauthorized,
		},
	}

	// Run the test cases
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			reqBody, err := json.Marshal(tc.payload)
			if err != nil {
				t.Fatalf("Failed to marshal request: %v", err)
			}

			resp, err := http.Post(ts.URL+"/v1/beacon/usage", "application/json", bytes.NewReader(reqBody))
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}
			defer resp.Body.Close()

			// Read response body for debugging
			bodyBytes, _ := io.ReadAll(resp.Body)
			bodyString := string(bodyBytes)

			if resp.StatusCode != tc.wantStatus {
				t.Fatalf("Expected status %d, got %d. Response: %s", tc.wantStatus, resp.StatusCode, bodyString)
			}

			// For successful requests, verify we got a valid JSON response
			if tc.wantStatus == http.StatusOK {
				var responseData any
				err := json.Unmarshal(bodyBytes, &responseData)
				if err != nil {
					t.Fatalf("Failed to parse response as JSON: %v", err)
				}
			}
		})
	}
}

func runSubmitMetadataTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	ctx := context.Background()

	// 1) First, create a customer and license just like your ingest‑traces tests
	expectedTenantID := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
	jwt := jwtauth.New("HS256", []byte(config.Env.XServiceAuthJwtSecret), nil)
	claims := map[string]interface{}{
		"sub":       "unspecified",
		"tenant_id": expectedTenantID,
		"exp":       time.Now().Add(5 * time.Minute).Unix(),
	}
	_, svcToken, err := jwt.Encode(claims)
	if err != nil {
		t.Fatalf("jwt.Encode: %v", err)
	}

	// create-customer
	custReq := map[string]interface{}{
		"customer_name":   "MetaTestCustomer",
		"customer_config": map[string]bool{"lgp_enabled": true},
	}
	{
		body, _ := json.Marshal(custReq)
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(body))
		if err != nil {
			t.Fatalf("create-customer: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Service-Key", svcToken)
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			t.Fatalf("create-customer: %v", err)
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			t.Fatalf("create-customer got %d", resp.StatusCode)
		}
		var out map[string]interface{}
		json.NewDecoder(resp.Body).Decode(&out)
		custID, ok := out["customer_id"].(string)
		if !ok {
			t.Fatalf("no customer_id in response")
		}
		// override expectedTenantID to match what the endpoint returns
		expectedTenantID = custID
	}

	// create-license
	var licenseKey string
	{
		licReq := map[string]interface{}{
			"customer_id":      expectedTenantID,
			"license_type":     "langsmith",
			"expiration_weeks": 4,
		}
		body, _ := json.Marshal(licReq)
		req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(body))
		if err != nil {
			t.Fatalf("create-license: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Service-Key", svcToken)
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			t.Fatalf("create-license: %v", err)
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			t.Fatalf("create-license got %d", resp.StatusCode)
		}
		var out map[string]string
		json.NewDecoder(resp.Body).Decode(&out)
		lk, ok := out["license_key"]
		if !ok {
			t.Fatalf("no license_key in response")
		}
		licenseKey = lk
	}
	rawApiKey := seedV2ServiceKey(t, dbpool)

	// Now our table of subtests
	tests := []struct {
		name       string
		payload    map[string]interface{}
		wantStatus int
		verifyDB   func(t *testing.T)
	}{
		{
			name:       "missing params metadata",
			payload:    map[string]interface{}{},
			wantStatus: http.StatusBadRequest,
		},
		{
			name:       "invalid license metadata",
			payload:    map[string]interface{}{"license_key": "not-a-valid-key"},
			wantStatus: http.StatusForbidden,
		},
		{
			name: "valid license metadata",
			payload: map[string]interface{}{
				"license_key":    licenseKey,
				"from_timestamp": time.Now().Add(-time.Hour).Format(time.RFC3339),
				"to_timestamp":   time.Now().Format(time.RFC3339),
				"measures":       map[string]int{"foo": 1},
				"tags":           map[string]string{"bar": "baz"},
			},
			wantStatus: http.StatusNoContent,
			verifyDB: func(t *testing.T) {
				// verify one row in remote_metrics for this license
				var cnt int
				hashed := hashKey(licenseKey)
				err := dbpool.QueryRow(ctx,
					`SELECT COUNT(*) FROM remote_metrics WHERE license_key = $1`, hashed,
				).Scan(&cnt)
				if err != nil {
					t.Fatalf("db query: %v", err)
				}
				if cnt != 1 {
					t.Fatalf("expected 1 metric row, got %d", cnt)
				}
			},
		},
		{
			name:       "invalid api key metadata",
			payload:    map[string]interface{}{"api_key": "bogus"},
			wantStatus: http.StatusForbidden,
		},
		{
			name: "valid api key metadata",
			payload: map[string]interface{}{
				"api_key":        rawApiKey,
				"from_timestamp": time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
				"to_timestamp":   time.Now().Format(time.RFC3339),
				"measures":       map[string]int{"bar": 2},
				"tags":           map[string]string{"foo": "bar"},
			},
			wantStatus: http.StatusNoContent,
			verifyDB: func(t *testing.T) {
				// verify one row in remote_metrics for this api_key
				var cnt int
				// hashedApi is computed below
				err := dbpool.QueryRow(ctx,
					`SELECT COUNT(*) FROM remote_metrics WHERE api_key = $1`, auth.HashApiKey(rawApiKey),
				).Scan(&cnt)
				if err != nil {
					t.Fatalf("db query: %v", err)
				}
				if cnt != 1 {
					t.Fatalf("expected 1 metric row for api_key, got %d", cnt)
				}
			},
		},
	}

	client := &http.Client{}
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			body, _ := json.Marshal(tc.payload)
			req, err := http.NewRequest("POST", ts.URL+"/v1/beacon/metadata/submit", bytes.NewReader(body))
			if err != nil {
				t.Fatalf("failed to create request: %v", err)
			}
			resp, err := client.Do(req)
			if err != nil {
				t.Fatalf("request error: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != tc.wantStatus {
				// read resp body
				respBody, _ := ioutil.ReadAll(resp.Body)
				t.Logf("Response body: %s", respBody)
				t.Fatalf("expected status %d, got %d", tc.wantStatus, resp.StatusCode)
			}

			if tc.verifyDB != nil {
				tc.verifyDB(t)
			}
		})
	}
}

// seedV2ServiceKey will bootstrap everything in the DB and return
// the raw API key (e.g. "lsv2_sk_<uuid>") that your handler will accept.
func seedV2ServiceKey(t *testing.T, db *database.AuditLoggedPool) string {
	apiKey := "***************************************************"
	hashedApiKey := "223ccab5ebc19e3d3c55a2b9fbb5bebf6808893afff285317b88fb5889867efd76049cd4dc04407c037ab3b353a11501304c0042a2ba9b37ded9f193f19a5b65"
	_, err := db.Exec(
		context.Background(),
		`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

service_account as (
	insert into service_accounts (id, name, organization_id)
	select $8, 'test service account', id
    from org
    returning id
),
org_ident as (
	insert into identities (id, organization_id, role_id, service_account_id, access_scope)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'),  $8, 'organization'
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, service_account_id, parent_identity_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, service_account_id, organization_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4
from ident`,
		"********-0000-0000-0000-********0001",
		"test tenant",
		"{}",
		"********-0000-0000-0000-********0003",
		"test org",
		"********-0000-0000-0000-********0002",
		hashedApiKey,
		"********-0000-0000-0000-********0004",
		"********-0000-0000-0000-************")
	assert.NoError(t, err)
	return apiKey
}
