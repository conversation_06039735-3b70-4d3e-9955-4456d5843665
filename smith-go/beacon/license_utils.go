package beacon

import (
	"context"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
)

type selfHostedLicense struct {
	LicenseID string    `json:"license_id"`
	ExpiresAt time.Time `json:"expires_at"`
}

type selfHostedCustomer struct {
	CustomerID          string  `json:"customer_id"`
	CustomerName        string  `json:"customer_name"`
	Config              string  `json:"config"`
	MetronomeCustomerId *string `json:"metronome_customer_id"`
}

type selfHostedLicenseWithCustomer struct {
	selfHostedLicense
	selfHostedCustomer
}

// License type enum
type LicenseType string

const (
	LicenseTypeLangsmith LicenseType = "langsmith"
)

func generateChecksum(message string, length int) (string, error) {
	// Create a new SHA-512 hash object
	hash := sha512.New()
	// Write the message concatenated with the API_KEY_SALT to the hash
	_, err := hash.Write([]byte(message + config.Env.ApiKeySalt))
	if err != nil {
		return "", err
	}
	// Compute the hash and get the hexadecimal representation
	checksum := hex.EncodeToString(hash.Sum(nil))
	// Ensure the requested length does not exceed the checksum length
	if length > len(checksum) {
		length = len(checksum)
	}
	// Return the first 'length' characters of the checksum
	return checksum[:length], nil
}

func validateChecksum(key string) error {
	// Check if the api_key is long enough to slice safely
	if len(key) < 21 { // "lsv2" + 10 for checksum + 1 for separator
		return errors.New("API key is too short")
	}

	// Verify checksum
	secret := key[0 : len(key)-11]
	checksum := key[len(key)-10:]
	hash := sha512.New()
	hash.Write([]byte(secret + config.Env.ApiKeySalt))
	hashedSecret := hex.EncodeToString(hash.Sum(nil))
	if hashedSecret[0:10] != checksum {
		return errors.New("API key checksum does not match")
	}

	return nil
}

func hashKey(key string) string {
	hash := sha512.New()
	hash.Write([]byte(key + config.Env.ApiKeySalt))
	return hex.EncodeToString(hash.Sum(nil))
}

func GenerateKeyAndHash(prefix string) (string, string, error) {
	// Generate a new UUID4 and get its hex representation without dashes
	secretUUID := uuid.New()
	secretUUIDHex := strings.ReplaceAll(secretUUID.String(), "-", "")
	// Construct the secret
	secret := fmt.Sprintf("%s_%s", prefix, secretUUIDHex)

	// Generate checksum of the secret
	checksum, err := generateChecksum(secret, 10)
	if err != nil {
		return "", "", err
	}

	// Construct the full key and hash it
	fullKey := fmt.Sprintf("%s_%s", secret, checksum)
	hashedKey := hashKey(fullKey)

	return fullKey, hashedKey, nil
}

type LicenseError struct {
	StatusCode int
	Message    string
}

func (e *LicenseError) Error() string {
	return e.Message
}

func verifyLicense(ctx context.Context, db *database.AuditLoggedPool, license string, metadata json.RawMessage) (*selfHostedLicenseWithCustomer, error) {
	err := validateChecksum(license)
	if err != nil {
		return nil, &LicenseError{
			StatusCode: http.StatusUnauthorized,
			Message:    "Invalid license key",
		}
	}

	// Hash the provided license with salt
	hashedLicense := hashKey(license)

	// Check presence of license in the database and update metadata
	query := `
	WITH updated_license AS (
		UPDATE self_hosted_licenses
		SET last_verified_at = now(),
			metadata = COALESCE(metadata, '{}'::jsonb) || $2::jsonb
		WHERE hashed_license_key = $1
		RETURNING id, self_hosted_customer_id, expires_at, license_type
	)
	SELECT
		ul.id as license_id,
		ul.expires_at,
		ul.self_hosted_customer_id as customer_id,
		c.customer_name,
		c.config,
		c.metronome_customer_id
	FROM updated_license ul
	JOIN self_hosted_customers c ON ul.self_hosted_customer_id = c.id;
	`

	rows, err := db.Query(
		ctx,
		query,
		hashedLicense,
		metadata,
	)

	licenseInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[selfHostedLicenseWithCustomer])
	if err == pgx.ErrNoRows {
		return nil, &LicenseError{
			StatusCode: http.StatusForbidden,
			Message:    "License not found",
		}
	} else if err != nil {
		slog.Error("Error verifying license info", "error", err)
		return nil, &LicenseError{
			StatusCode: http.StatusInternalServerError,
			Message:    "Error fetching license info",
		}
	}

	// Check if license is expired
	if licenseInfo.ExpiresAt.Before(time.Now()) {
		return nil, &LicenseError{
			StatusCode: http.StatusForbidden,
			Message:    "Expired License Key",
		}
	}
	return licenseInfo, nil
}

// Validate license key and issue a short lived JWT token. This token will last for 7 days s.t errors validating/LangSmith
// going down will not affect the customer's ability to use the product.
// TODO: we should implement a JWKs endpoint that exposes public keys for a set of private keys. This way we can rotate keys
func (h *BeaconHandler) IssueLicenseJWT(licenseInfo *selfHostedLicenseWithCustomer) (string, error) {
	// Unmarshal the config into a map
	var configMap map[string]interface{}
	err := json.Unmarshal([]byte(licenseInfo.Config), &configMap)
	if err != nil {
		return "", err
	}

	expirationTime := licenseInfo.ExpiresAt
	claims := jwt.MapClaims{
		"sub": licenseInfo.CustomerID,
		"iat": time.Now().UTC().Unix(),
		"exp": expirationTime.Unix(),
		"aud": LicenseTypeLangsmith,
	}

	// Add configMap entries as claims
	for k, v := range configMap {
		claims[k] = v
	}

	// Create a new JWT token with the claims
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)

	// Set the Key ID (kid) in the JWT header
	token.Header["kid"] = config.Env.LicenseKeyID

	// Sign the token with the private key
	tokenString, err := token.SignedString(config.Env.LicensePrivateKey)

	if err != nil {
		return "", err
	}
	return tokenString, nil
}
