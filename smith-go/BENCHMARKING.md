# Continuous Benchmarking for Smith-Go

This document describes the continuous benchmarking setup for the smith-go project, following best practices for Go performance monitoring.

## Overview

We use continuous benchmarking to:
- Track performance regressions automatically
- Compare performance between branches
- Monitor long-term performance trends
- Alert on significant performance changes

## Quick Start

### Running Benchmarks Locally

```bash
# Run all benchmarks
make bench

# Run storage benchmarks only
make bench-storage

# Compare current branch against main
make bench-storage-compare

# Use the analysis script for advanced options
./scripts/benchmark-analysis.sh --help
```

### Benchmark Analysis Script

The `scripts/benchmark-analysis.sh` script provides advanced benchmarking capabilities:

```bash
# Full comparison against main branch
./scripts/benchmark-analysis.sh

# Run with custom parameters
./scripts/benchmark-analysis.sh -t 100x -c 3 -p "./ingestion/..."

# Compare against a different branch
./scripts/benchmark-analysis.sh -b develop

# Only run benchmarks on current branch
./scripts/benchmark-analysis.sh --current-only

# Clean up benchmark files
./scripts/benchmark-analysis.sh --clean
```

## Continuous Integration

### Automatic Benchmarking

Benchmarks run automatically on:
- **Push to main**: Full benchmarks with results stored in GitHub Pages
- **Pull Requests**: Benchmark comparison with main branch
- **Manual trigger**: Via GitHub Actions workflow dispatch

### Benchmark Workflows

1. **CI Test Go** (`.github/workflows/ci_test_go.yaml`):
   - Runs basic benchmarks as part of the test suite
   - Stores results for main branch commits

2. **Continuous Benchmark** (`.github/workflows/continuous-benchmark.yaml`):
   - Dedicated benchmarking workflow
   - Compares PR branches against main
   - Posts comparison results as PR comments
   - Alerts on performance regressions

## Configuration

### Benchmark Settings

Configuration is managed through:
- Makefile targets: Quick access to common benchmark commands
- GitHub Actions workflows: CI/CD benchmark execution
- Analysis script parameters: Advanced local benchmarking

### Key Parameters

```bash
# Benchmark execution (configured in Makefile and workflows)
BENCHTIME=10x          # Number of iterations
COUNT=5               # Number of benchmark runs
TIMEOUT=30m           # Maximum execution time

# Performance thresholds (configured in GitHub Actions)
ALERT_THRESHOLD=150%   # Performance regression threshold
```

## Benchmark Suites

### Storage Benchmarks (`./storage/...`)

Tests for the continuous uploader and storage components:
- `BenchmarkContinuousUploader_New`: Uploader creation performance
- `BenchmarkContinuousUploader_UploadReader`: Upload performance by size
- `BenchmarkContinuousUploader_ChunkedUpload`: Chunked upload performance
- `BenchmarkContinuousUploader_Concurrency`: Concurrency scaling

### Compression Benchmarks (`./compression/...`)

Performance tests for data compression:
- Compression ratio vs speed tradeoffs
- Different compression algorithms
- Size-based compression decisions

### Ingestion Benchmarks (`./ingestion/...`)

Performance tests for data ingestion pipeline:
- Queue processing performance
- Batch processing efficiency
- Redis transaction performance

## Results and Monitoring

### GitHub Pages Dashboard

Benchmark results are automatically published to GitHub Pages at:
`https://your-org.github.io/your-repo/dev/bench/`

The dashboard shows:
- Performance trends over time
- Comparison between branches
- Regression detection alerts
- Historical performance data

### Performance Alerts

Alerts are triggered when:
- Performance degrades beyond threshold (default: 150%)
- Memory usage increases significantly
- Allocation patterns change dramatically

Alerts are delivered via:
- GitHub PR comments
- Commit status checks
- Optional Slack/Discord notifications

## Best Practices

### Writing Benchmarks

1. **Use realistic data sizes**: Test with data similar to production
2. **Avoid setup in benchmark loops**: Use `b.ResetTimer()` appropriately
3. **Report custom metrics**: Use `b.ReportMetric()` for domain-specific metrics
4. **Test multiple scenarios**: Different sizes, concurrency levels, etc.

### Benchmark Stability

1. **Run multiple iterations**: Use `-count=5` or higher
2. **Use appropriate benchtime**: Balance accuracy vs CI time
3. **Control environment**: Consistent test environment in CI
4. **Warm up caches**: Consider cache warming for realistic results

### Interpreting Results

1. **Look for trends**: Single measurements can be noisy
2. **Consider statistical significance**: Use `benchstat` for analysis
3. **Understand variance**: High variance indicates unstable benchmarks
4. **Context matters**: Consider what changed between measurements

## Tools and Dependencies

### Required Tools

- `go test`: Built-in Go benchmarking
- `benchcmp`: Compare benchmark results
- `benchstat`: Statistical analysis of benchmarks
- `github-action-benchmark`: GitHub Actions integration

### Installation

```bash
# Install comparison tools
go install golang.org/x/tools/cmd/benchcmp@latest
go install golang.org/x/perf/cmd/benchstat@latest
```

## Troubleshooting

### Common Issues

1. **Noisy benchmarks**: Increase `-count` or `-benchtime`
2. **CI timeouts**: Reduce `-benchtime` for CI environments
3. **Memory issues**: Monitor test environment resources
4. **Inconsistent results**: Check for external factors affecting performance

### Debug Mode

Enable verbose output for debugging:

```bash
# Verbose benchmark output
go test -bench=. -benchmem -v ./storage/...

# With CPU profiling
go test -bench=. -benchmem -cpuprofile=cpu.prof ./storage/...

# With memory profiling  
go test -bench=. -benchmem -memprofile=mem.prof ./storage/...
```

## Contributing

When adding new benchmarks:

1. Follow naming convention: `BenchmarkComponentName_Operation`
2. Add appropriate test cases to cover different scenarios
3. Update benchmark configuration if needed
4. Document expected performance characteristics
5. Consider impact on CI execution time

## References

- [Go Benchmarking Guide](https://pkg.go.dev/testing#hdr-Benchmarks)
- [Continuous Benchmarking Blog Post](https://dev.to/vearutop/continuous-benchmarking-with-go-and-github-actions-41ok)
- [GitHub Action Benchmark](https://github.com/benchmark-action/github-action-benchmark)
- [benchstat Documentation](https://pkg.go.dev/golang.org/x/perf/cmd/benchstat)
