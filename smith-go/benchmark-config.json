{"name": "Smith-Go Continuous Benchmarks", "description": "Performance benchmarks for smith-go storage and ingestion components", "benchmarks": [{"name": "Storage Uploader Benchmarks", "package": "./storage/...", "patterns": ["BenchmarkContinuousUploader.*"], "metrics": ["ns/op", "B/op", "allocs/op"], "thresholds": {"time": 150, "memory": 150, "allocations": 200}}, {"name": "Compression Benchmarks", "package": "./compression/...", "patterns": ["BenchmarkCompress.*"], "metrics": ["ns/op", "B/op", "allocs/op"], "thresholds": {"time": 120, "memory": 130, "allocations": 150}}, {"name": "Ingestion Benchmarks", "package": "./ingestion/...", "patterns": ["BenchmarkQueue.*", "BenchmarkIngest.*"], "metrics": ["ns/op", "B/op", "allocs/op"], "thresholds": {"time": 150, "memory": 200, "allocations": 200}}], "environments": {"ci": {"benchtime": "10x", "count": 5, "timeout": "30m"}, "local": {"benchtime": "100x", "count": 3, "timeout": "10m"}, "nightly": {"benchtime": "1000x", "count": 10, "timeout": "2h"}}, "reporting": {"github_pages": {"enabled": true, "branch": "gh-pages", "path": "dev/bench"}, "alerts": {"enabled": true, "threshold": 150, "comment_on_pr": true, "fail_on_regression": false}, "notifications": {"slack": {"enabled": false, "webhook_url": ""}, "email": {"enabled": false, "recipients": []}}}, "retention": {"keep_results_days": 90, "max_results_per_branch": 100}, "comparison": {"base_branch": "main", "compare_branches": ["main", "develop"], "statistical_analysis": true}}