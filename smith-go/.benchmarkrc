# Benchmark configuration for smith-go
# This file contains default settings for benchmark runs

# Default benchmark parameters
BENCHTIME=10x
COUNT=5
TIMEOUT=30m

# Packages to benchmark
BENCHMARK_PACKAGES=(
    "./storage/..."
    "./ingestion/..."
    "./compression/..."
)

# Performance thresholds (percentage increase that triggers alerts)
ALERT_THRESHOLD_MEMORY=150
ALERT_THRESHOLD_TIME=150
ALERT_THRESHOLD_ALLOCS=200

# Benchmark environments
BENCHMARK_ENVS=(
    "local_test"
    "ci"
)

# Database configurations for benchmarks
POSTGRES_URI="postgres:postgres@localhost:5432/langsmith_test"
REDIS_URI="redis://localhost:6379/1"

# S3/Storage configurations for benchmarks
S3_BUCKET_NAME="langsmith-run-data-test"
S3_API_URL="http://127.0.0.1:9002"
S3_ACCESS_KEY="minioadmin1"
S3_ACCESS_KEY_SECRET="minioadmin1"

# Azure configurations for benchmarks
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=http;AccountName=admin;AccountKey=password;BlobEndpoint=http://127.0.0.1:10000/admin;"
AZURE_STORAGE_CONTAINER_NAME="langsmith-images-test"

# Benchmark output settings
OUTPUT_FORMAT="json"
SAVE_PROFILES=false
PROFILE_DIR="profiles"

# Continuous benchmarking settings
COMPARE_BRANCH="main"
AUTO_PUSH_RESULTS=true
GITHUB_PAGES_BRANCH="gh-pages"
BENCHMARK_DATA_DIR="dev/bench"

# Notification settings
SLACK_WEBHOOK_URL=""
DISCORD_WEBHOOK_URL=""
EMAIL_NOTIFICATIONS=false

# Advanced settings
CPU_PROFILE=false
MEM_PROFILE=false
BLOCK_PROFILE=false
MUTEX_PROFILE=false
TRACE=false

# Benchmark tags for categorization
BENCHMARK_TAGS=(
    "storage"
    "upload"
    "compression"
    "ingestion"
    "performance"
)

# Exclusion patterns (benchmarks to skip)
EXCLUDE_PATTERNS=(
    "*_slow"
    "*_integration"
)

# Include patterns (only run these benchmarks)
INCLUDE_PATTERNS=()

# Custom benchmark flags
CUSTOM_FLAGS=(
    "-benchmem"
    "-vet=off"
)

# Parallel execution settings
PARALLEL_PACKAGES=true
MAX_PARALLEL_JOBS=4

# Regression detection settings
REGRESSION_DETECTION=true
REGRESSION_THRESHOLD=120  # 20% increase
REGRESSION_WINDOW=10      # Compare against last 10 runs

# Benchmark result retention
KEEP_RESULTS_DAYS=30
ARCHIVE_OLD_RESULTS=true

# Development settings
DEV_MODE=false
VERBOSE_OUTPUT=false
DEBUG_BENCHMARKS=false
