package auth

import (
	"context"
	"crypto/sha512"
	"encoding/hex"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

const getTenantInfoForApiKeyQuery = `
WITH updated AS (
    UPDATE api_keys
    SET last_used_at = NOW()
    WHERE api_key = $1
    RETURNING ls_user_id
)
SELECT
    t.id as tenant_id,
    coalesce(t.tenant_handle, '') as tenant_handle,
	t.is_deleted as tenant_is_deleted,
    t.config as tenant_config,
	i.id,
	i.read_only,
	array(select permission from role_permissions rp where rp.role_id = i.role_id order by permission) as identity_permissions
FROM api_keys a
INNER JOIN identities i ON a.service_account_id = i.service_account_id
INNER JOIN tenants t ON i.tenant_id = t.id
WHERE i.access_scope = 'workspace'
AND a.api_key = $1 AND i.tenant_id = $2;`

const getOrgInfoForApiKeyQuery = `
SELECT
	a.organization_id as organization_id,
	o.is_personal,
	coalesce(o.metronome_customer_id, '') as metronome_customer_id,
	coalesce(o.stripe_customer_id, '') as stripe_customer_id,
	i.id as identity_id,
	i.read_only as identity_read_only,
	array(select permission from role_permissions rp where rp.role_id = i.role_id order by permission) as permissions,
	o.config,
	o.disabled as organization_disabled,
	o.public_sharing_disabled as public_sharing_disabled,
	o.sso_only as sso_only,
	'' as user_id,
	'' as ls_user_id,
	'' as email,
	'' as full_name,
	false as is_sso_user,
	a.tenant_id as default_tenant_id,
	'' as service_identity
FROM api_keys a
INNER JOIN identities i ON a.service_account_id = i.service_account_id AND i.organization_id = a.organization_id
INNER JOIN organizations o ON a.organization_id = o.id
WHERE i.access_scope = 'organization'
AND a.api_key = $1;`

const getTenantInfoForPATQuery = `
WITH updated AS (
    UPDATE api_keys
    SET last_used_at = NOW()
    WHERE api_key = $1
    RETURNING ls_user_id
)
SELECT
    t.id as tenant_id,
    coalesce(t.tenant_handle, '') as tenant_handle,
	t.is_deleted as tenant_is_deleted,
    t.config as tenant_config,
	i.id,
	i.read_only,
	array(select permission from role_permissions rp where rp.role_id = i.role_id order by permission) as identity_permissions
FROM api_keys a
INNER JOIN identities i ON a.ls_user_id = i.ls_user_id
INNER JOIN tenants t ON i.tenant_id = t.id
WHERE i.access_scope = 'workspace'
AND a.api_key = $1 AND i.tenant_id = $2;`

// TODO: detect SSO user?
const getOrgInfoForPATQuery = `
SELECT
	a.organization_id as organization_id,
	o.is_personal,
	coalesce(o.metronome_customer_id, '') as metronome_customer_id,
	coalesce(o.stripe_customer_id, '') as stripe_customer_id,
	i.id as identity_id,
	i.read_only as identity_read_only,
	array(select permission from role_permissions rp where rp.role_id = i.role_id order by permission) as permissions,
	o.config,
	o.disabled as organization_disabled,
	o.public_sharing_disabled as public_sharing_disabled,
	o.sso_only as sso_only,
	u.id as user_id,
	u.ls_user_id as ls_user_id,
	u.email as email,
	coalesce(u.full_name, '') as full_name,
	false as is_sso_user,
	a.tenant_id as default_tenant_id,
	'' as service_identity
FROM api_keys a
INNER JOIN identities i ON a.ls_user_id = i.ls_user_id AND i.organization_id = a.organization_id
INNER JOIN users u ON i.ls_user_id = u.ls_user_id
INNER JOIN organizations o ON a.organization_id = o.id
WHERE i.access_scope = 'organization'
AND a.api_key = $1;`

func NewApiKey(pg *database.AuditLoggedPool, redisClient redis.UniversalClient, cacheTTLSecs int) *HandlerApiKey {
	ttl := time.Duration(cacheTTLSecs) * time.Second
	cache := lsredis.NewCache[uint64, *AuthInfo](redisClient, "authInfo", ttl)
	orgCache := lsredis.NewCache[uint64, *OrgAuthInfo](redisClient, "orgAuthInfo", ttl)
	return &HandlerApiKey{Pg: pg, cache: cache, orgCache: orgCache}
}

type HandlerApiKey struct {
	Pg *database.AuditLoggedPool

	cache    *lsredis.Cache[uint64, *AuthInfo]
	orgCache *lsredis.Cache[uint64, *OrgAuthInfo]
}

func ValidateApiKey(apiKey string) error {
	// Old keys are prefixed with "ls__" and didn't have a checksum. Some self-hosted customers
	// may still be using these keys, so we need to allow them for now.
	if config.Env.FFV1ApiKeysEnabled {
		if strings.HasPrefix(apiKey, "ls__") {
			return nil
		}
	}
	// Check if the api_key is long enough to slice safely
	if len(apiKey) < 21 { // "lsv2" + 10 for checksum + 1 for separator
		return errors.New("API key is too short")
	}

	// Verify checksum
	secret := apiKey[0 : len(apiKey)-11]
	checksum := apiKey[len(apiKey)-10:]
	hash := sha512.New()
	hash.Write([]byte(secret + config.Env.ApiKeySalt))
	hashedSecret := hex.EncodeToString(hash.Sum(nil))
	if hashedSecret[0:10] != checksum {
		return errors.New("API key checksum does not match")
	}

	return nil
}

func HashApiKey(apiKey string) string {
	hash := sha512.New()
	hash.Write([]byte(apiKey + config.Env.ApiKeySalt))
	return hex.EncodeToString(hash.Sum(nil))
}

func ShortenApiKey(apiKey string) string {
	return apiKey[:9] + "..." + apiKey[len(apiKey)-4:]
}

func getQueriesForApiKey(apiKey string) (string, string) {
	if strings.HasPrefix(apiKey, "lsv2_pt") {
		return getTenantInfoForPATQuery, getOrgInfoForPATQuery
	}
	return getTenantInfoForApiKeyQuery, getOrgInfoForApiKeyQuery
}

func (h *HandlerApiKey) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(ctx)
		// get api key header
		apiKey := r.Header.Get("X-Api-Key")
		if apiKey == "" {
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		err := ValidateApiKey(apiKey)
		if err != nil {
			oplog.Info("Forbidden", "err", err.Error())
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		tenantAuthQuery, orgAuthQuery := getQueriesForApiKey(apiKey)

		tenantId := r.Header.Get("X-Tenant-ID")

		// verify tenant access
		auth, err := h.cache.GetFresh(
			ctx,
			lsredis.StringToHash(tenantAuthQuery, apiKey, tenantId),
			func() (ai *AuthInfo, err error) {
				// hash api key
				hashedApiKey := HashApiKey(apiKey)
				shortApiKey := ShortenApiKey(apiKey)
				ctx = config.LogAndContextSetField(ctx, "api_key_short", slog.StringValue(shortApiKey))

				// validate access by retrieving org info then tenant info in one transaction
				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
				if err != nil {
					return nil, err
				}

				defer func() {
					if err != nil {
						err = tx.Rollback(ctx)
						if err != nil {
							return
						}
						return
					}
					err = tx.Commit(ctx) // commit, set return error here
				}()

				// org info
				orgRows, _ := tx.Query(
					ctx,
					orgAuthQuery,
					hashedApiKey,
				)
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if errors.Is(err, pgx.ErrNoRows) {
					oplog.Info("No org info found for api key")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Info("pgx error collecting org auth info for api key", "err", err)
					return nil, err // unknown error, not cached
				}

				if tenantId == "" {
					tenantId = orgAuth.DefaultTenantID
				}
				ctx = config.LogAndContextSetField(ctx, "tenant_id", slog.StringValue(tenantId))

				rows, _ := h.Pg.Query(ctx, tenantAuthQuery, hashedApiKey, tenantId)
				tenantAuth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[TenantAuthInfo])
				if errors.Is(err, pgx.ErrNoRows) {
					oplog.Info("No tenant info found for api key")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Info("pgx error collecting auth info for api key", "err", err)
					return nil, err // unknown error, not cached
				}

				auth := &AuthInfo{
					OrganizationID:                  orgAuth.OrganizationID,
					OrganizationIsPersonal:          orgAuth.OrganizationIsPersonal,
					OrganizationMetronomeCustomerId: orgAuth.OrganizationMetronomeCustomerId,
					OrganizationStripeCustomerId:    orgAuth.OrganizationStripeCustomerId,
					OrganizationIdentityID:          orgAuth.IdentityID,
					OrganizationIdentityReadOnly:    orgAuth.IdentityReadOnly,
					OrganizationPermissions:         orgAuth.OrganizationPermissions,
					OrganizationConfig:              orgAuth.OrganizationConfig,
					OrganizationDisabled:            orgAuth.OrganizationDisabled,
					PublicSharingDisabled:           orgAuth.PublicSharingDisabled,
					SsoOnly:                         orgAuth.SsoOnly,
					UserID:                          orgAuth.UserID,
					LSUserID:                        orgAuth.LSUserID,
					UserEmail:                       orgAuth.UserEmail,
					UserFullName:                    orgAuth.UserFullName,
					TenantID:                        tenantAuth.TenantID,
					TenantHandle:                    tenantAuth.TenantHandle,
					TenantIsDeleted:                 tenantAuth.TenantIsDeleted,
					TenantConfig:                    tenantAuth.TenantConfig,
					TenantIdentityID:                tenantAuth.IdentityID,
					IdentityPermissions:             tenantAuth.IdentityPermissions,
					ServiceIdentity:                 "",
				}

				InitializeAndUnmarshalConfig(auth)

				return auth, nil
			},
		)
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerApiKey) CacheKey(r *http.Request) uint64 {
	return cacheKeyWithHeaders(r, "X-Api-Key")
}

func (h *HandlerApiKey) GetTenantlessAuth(w http.ResponseWriter, r *http.Request) {
	render.Status(r, http.StatusForbidden)
	render.JSON(w, r, map[string]string{"error": "Tenantless auth not supported for api key"})
}

func (h *HandlerApiKey) OrgMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(ctx)
		apiKey := r.Header.Get("X-Api-Key")
		if apiKey == "" {
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		err := ValidateApiKey(apiKey)
		if err != nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": fmt.Sprintf("Forbidden: %s", err.Error())})
			return
		}

		_, orgAuthQuery := getQueriesForApiKey(apiKey)

		// verify org access
		auth, err := h.orgCache.GetFresh(
			ctx,
			lsredis.StringToHash(orgAuthQuery, apiKey),
			func() (*OrgAuthInfo, error) {
				// hash api key
				hashedApiKey := HashApiKey(apiKey)
				shortApiKey := ShortenApiKey(apiKey)
				ctx = config.LogAndContextSetField(ctx, "api_key_short", slog.StringValue(shortApiKey))

				rows, _ := h.Pg.Query(ctx, orgAuthQuery, hashedApiKey)
				auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if errors.Is(err, pgx.ErrNoRows) {
					oplog.Info("No org info found for api key")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Info("pgx error collecting auth info for api key", "err", err)
					return nil, err // unknown error, not cached
				}

				InitializeAndUnmarshalOrgConfig(auth)

				return auth, nil
			},
		)
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, orgAuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerApiKey) XServiceKeyMiddleware(next http.Handler) http.Handler {
	// Forbidden, not supported
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		render.Status(r, http.StatusForbidden)
	})
}

func (h *HandlerApiKey) FetchInternalAuth(w http.ResponseWriter, r *http.Request) {
	// Forbidden, not supported
	render.Status(r, http.StatusForbidden)
}

func (h *HandlerApiKey) FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request) {
	// Forbidden, not supported
	render.Status(r, http.StatusForbidden)
}
