package auth_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"io"
	"langchain.com/smith/config"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	. "langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
)

func TestAuthHandlerApiKey_Middleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		assert.NoError(t, err)
	}(redisPool)
	ah := auth.NewApiKey(dbpool, redisPool, 0)

	runApiKeyMiddlewareTests(t, dbpool, ah, true)
}

func runApiKeyMiddlewareTests(t *testing.T, dbpool *database.AuditLoggedPool, ah auth.Handler, shouldRunOrgTests bool) {
	t.Run("missing header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid api key", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Api-Key", "hello")

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("invalid api key signature", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Api-Key", "lsv2_pt_645b74bb56da429f91f651aab2d878b6_foo")

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("present with v2 service key", func(t *testing.T) {
		apiKey := "***************************************************"
		hashedApiKey := "223ccab5ebc19e3d3c55a2b9fbb5bebf6808893afff285317b88fb5889867efd76049cd4dc04407c037ab3b353a11501304c0042a2ba9b37ded9f193f19a5b65"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

service_account as (
	insert into service_accounts (id, name, organization_id)
	select $8, 'test service account', id
    from org
    returning id
),
org_ident as (
	insert into identities (id, organization_id, role_id, service_account_id, access_scope)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'),  $8, 'organization'
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, service_account_id, parent_identity_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, service_account_id, organization_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4
from ident`,
			"********-0000-0000-0000-********0001",
			"test tenant",
			"{}",
			"********-0000-0000-0000-********0003",
			"test org",
			"********-0000-0000-0000-********0002",
			hashedApiKey,
			"********-0000-0000-0000-********0004",
			"********-0000-0000-0000-********0005")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Api-Key", apiKey)

		// accepts requests when tenant present
		called := false
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "********-0000-0000-0000-********0003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "********-0000-0000-0000-********0005", Valid: true},
				TenantID:                "********-0000-0000-0000-********0001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "********-0000-0000-0000-********0002",
				TenantIdentityReadOnly:  false,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, auth.GetAuthInfo(r))
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Api-Key", apiKey)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, authResponse))
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "********-0000-0000-0000-********0003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "********-0000-0000-0000-********0005", Valid: true},
			TenantID:                "********-0000-0000-0000-********0001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "********-0000-0000-0000-********0002",
			TenantIdentityReadOnly:  false,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations")

	t.Run("invalid v1 service key", func(t *testing.T) {
		apiKey := "ls__7b055d5177d048f2bf198fae7a4e7eb4"
		hashedApiKey := "d065aa2d0db6fc32f92b7c89f47b4c287338a21eaa4ead4724e377344965e2b4fe997bf26458f41d14ecb8476e73930cd9457cdb7e23ec692aa13988f662c62a"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

service_account as (
	insert into service_accounts (id, name, organization_id)
	select $8, 'test service account', id
    from org
    returning id
),
org_ident as (
	insert into identities (id, organization_id, role_id, service_account_id, access_scope)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'),  $8, 'organization'
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, service_account_id, parent_identity_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, service_account_id, organization_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4
from ident`,
			"********-0000-0000-0000-********0001",
			"test tenant",
			"{}",
			"********-0000-0000-0000-********0003",
			"test org",
			"********-0000-0000-0000-********0002",
			hashedApiKey,
			"********-0000-0000-0000-********0004",
			"********-0000-0000-0000-********0005")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Api-Key", apiKey)

		// accepts requests when tenant present
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations")

	t.Setenv("FF_V1_API_KEYS_ENABLED", "true")
	config.LoadEnv()

	t.Run("valid v1 service key with env var", func(t *testing.T) {
		apiKey := "ls__7b055d5177d048f2bf198fae7a4e7eb4"
		hashedApiKey := "d065aa2d0db6fc32f92b7c89f47b4c287338a21eaa4ead4724e377344965e2b4fe997bf26458f41d14ecb8476e73930cd9457cdb7e23ec692aa13988f662c62a"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

service_account as (
	insert into service_accounts (id, name, organization_id)
	select $8, 'test service account', id
    from org
    returning id
),
org_ident as (
	insert into identities (id, organization_id, role_id, service_account_id, access_scope)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'),  $8, 'organization'
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, service_account_id, parent_identity_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, service_account_id, organization_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4
from ident`,
			"********-0000-0000-0000-********0001",
			"test tenant",
			"{}",
			"********-0000-0000-0000-********0003",
			"test org",
			"********-0000-0000-0000-********0002",
			hashedApiKey,
			"********-0000-0000-0000-********0004",
			"********-0000-0000-0000-********0005")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Api-Key", apiKey)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations")

	t.Setenv("FF_V1_API_KEYS_ENABLED", "false")
	config.LoadEnv()

	t.Run("present v2 pat", func(t *testing.T) {
		apiKey := "***************************************************"
		hashedApiKey := "37110cec3212c718a08dd789296a69147af6ff3809b12749a8feb2661d0e20de364ce3a006cbfc2cf8d2cc76ff216b6307e91e99b760fe05c1705a6671318674"
		userId := "********-0000-0000-0000-********0006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

usr as (
	insert into users (id, email, full_name, ls_user_id) select $8, '<EMAIL>', 'hello', $8 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, provider_user_id)
	select u.ls_user_id, u.email, 'supabase:non-sso', u.id from usr u
),

org_ident as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, user_id, parent_identity_id, ls_user_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9, (select ls_user_id from usr)
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, user_id, organization_id, ls_user_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4, (select ls_user_id from usr)
from ident`,
			"********-0000-0000-0000-********0001",
			"test tenant",
			"{}",
			"********-0000-0000-0000-********0003",
			"test org",
			"********-0000-0000-0000-********0002",
			hashedApiKey,
			userId,
			"********-0000-0000-0000-********0005")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Api-Key", apiKey) // accepts requests when tenant present
		called := false
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "********-0000-0000-0000-********0003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "********-0000-0000-0000-********0005", Valid: true},
				TenantID:                "********-0000-0000-0000-********0001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "********-0000-0000-0000-********0002",
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				LSUserID:                userId,
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, auth.GetAuthInfo(r))
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Api-Key", apiKey)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, authResponse))
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "********-0000-0000-0000-********0003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "********-0000-0000-0000-********0005", Valid: true},
			TenantID:                "********-0000-0000-0000-********0001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "********-0000-0000-0000-********0002",
			TenantIdentityReadOnly:  false,
			UserID:                  userId,
			LSUserID:                userId,
			UserEmail:               "<EMAIL>",
			UserFullName:            "hello",
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_USER_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations")

	if shouldRunOrgTests {
		t.Run("org auth service key", func(t *testing.T) {
			apiKey := "***************************************************"
			hashedApiKey := "223ccab5ebc19e3d3c55a2b9fbb5bebf6808893afff285317b88fb5889867efd76049cd4dc04407c037ab3b353a11501304c0042a2ba9b37ded9f193f19a5b65"
			_, err := dbpool.Exec(
				context.Background(),
				`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

service_account as (
	insert into service_accounts (id, name, organization_id)
	select $8, 'test service account', id
    from org
    returning id
),
org_ident as (
	insert into identities (id, organization_id, role_id, service_account_id, access_scope)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'),  $8, 'organization'
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, service_account_id, parent_identity_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, service_account_id, organization_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4
from ident`,
				"********-0000-0000-0000-********0001",
				"test tenant",
				"{}",
				"********-0000-0000-0000-********0003",
				"test org",
				"********-0000-0000-0000-********0002",
				hashedApiKey,
				"********-0000-0000-0000-********0004",
				"********-0000-0000-0000-********0005")
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/orgs/auth", nil)
			r.Header.Set("X-Api-Key", apiKey)

			// accepts requests when tenant present
			called := false
			TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				called = true
				assert.Equal(t, &auth.OrgAuthInfo{
					OrganizationID:          "********-0000-0000-0000-********0003",
					OrganizationIsPersonal:  false,
					IdentityID:              sql.NullString{String: "********-0000-0000-0000-********0005", Valid: true},
					OrganizationPermissions: ORG_PERMISSIONS,
					OrganizationConfig:      testutil.GetDefaultOrgConfig(),
					DefaultTenantID:         "********-0000-0000-0000-********0001",
				}, auth.GetOrgAuthInfo(r))
			}))).ServeHTTP(w, r)
			assert.True(t, called)
			assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

			// returns json auth info
			rtr := chi.NewRouter()
			rtr.With(TestLogger(t)).With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
			srv := httptest.NewServer(rtr)
			defer srv.Close()

			req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
			assert.NoError(t, err)
			req.Header.Set("X-Api-Key", apiKey)
			res, err := srv.Client().Do(req)

			assert.NoError(t, err)
			defer res.Body.Close()
			assert.Equal(t, http.StatusOK, res.StatusCode)
			assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
			body, err := io.ReadAll(res.Body)
			assert.NoError(t, err)
			authResponse := &auth.OrgAuthInfo{}
			assert.NoError(t, json.Unmarshal(body, authResponse))
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:          "********-0000-0000-0000-********0003",
				OrganizationIsPersonal:  false,
				IdentityID:              sql.NullString{String: "********-0000-0000-0000-********0005", Valid: true},
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
				DefaultTenantID:         "********-0000-0000-0000-********0001",
			}, authResponse)
		})

		dbpool.Exec(context.Background(), "delete from organizations")

		t.Run("org auth with v2 pat", func(t *testing.T) {
			apiKey := "***************************************************"
			hashedApiKey := "37110cec3212c718a08dd789296a69147af6ff3809b12749a8feb2661d0e20de364ce3a006cbfc2cf8d2cc76ff216b6307e91e99b760fe05c1705a6671318674"
			userId := "********-0000-0000-0000-********0005"
			_, err := dbpool.Exec(
				context.Background(),
				`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

usr as (
	insert into users (id, email, full_name, ls_user_id) select $8, '<EMAIL>', 'hello', $8 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, provider_user_id)
	select u.ls_user_id, u.email, 'supabase:non-sso', u.id from usr u
),

org_ident as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, user_id, parent_identity_id, ls_user_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9, (select ls_user_id from usr)
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, user_id, organization_id, ls_user_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4, (select ls_user_id from usr)
from ident`,
				"********-0000-0000-0000-********0001",
				"test tenant",
				"{}",
				"********-0000-0000-0000-********0003",
				"test org",
				"********-0000-0000-0000-********0002",
				hashedApiKey,
				userId,
				"********-0000-0000-0000-********0006")
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/orgs/auth", nil)
			r.Header.Set("X-Api-Key", apiKey)

			// accepts requests when tenant present
			called := false
			TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				called = true
				assert.Equal(t, &auth.OrgAuthInfo{
					OrganizationID:          "********-0000-0000-0000-********0003",
					OrganizationIsPersonal:  false,
					IdentityID:              sql.NullString{String: "********-0000-0000-0000-********0006", Valid: true},
					OrganizationPermissions: ORG_USER_PERMISSIONS,
					OrganizationConfig:      testutil.GetDefaultOrgConfig(),
					UserID:                  userId,
					LSUserID:                userId,
					UserEmail:               "<EMAIL>",
					UserFullName:            "hello",
					DefaultTenantID:         "********-0000-0000-0000-********0001",
				}, auth.GetOrgAuthInfo(r))
			}))).ServeHTTP(w, r)
			assert.True(t, called)
			assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

			// returns json auth info
			rtr := chi.NewRouter()
			rtr.With(TestLogger(t)).With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
			srv := httptest.NewServer(rtr)
			defer srv.Close()

			req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
			assert.NoError(t, err)
			req.Header.Set("X-Api-Key", apiKey)
			res, err := srv.Client().Do(req)

			assert.NoError(t, err)
			defer res.Body.Close()
			assert.Equal(t, http.StatusOK, res.StatusCode)
			assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
			body, err := io.ReadAll(res.Body)
			assert.NoError(t, err)
			authResponse := &auth.OrgAuthInfo{}
			assert.NoError(t, json.Unmarshal(body, authResponse))
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:          "********-0000-0000-0000-********0003",
				OrganizationIsPersonal:  false,
				IdentityID:              sql.NullString{String: "********-0000-0000-0000-********0006", Valid: true},
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
				UserID:                  userId,
				LSUserID:                userId,
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				DefaultTenantID:         "********-0000-0000-0000-********0001",
			}, authResponse)
		})

		dbpool.Exec(context.Background(), "delete from organizations")

		t.Run("multiple orgs/identities with v2 pat", func(t *testing.T) {
			apiKey := "***************************************************"
			hashedApiKey := "37110cec3212c718a08dd789296a69147af6ff3809b12749a8feb2661d0e20de364ce3a006cbfc2cf8d2cc76ff216b6307e91e99b760fe05c1705a6671318674"
			userId := "********-0000-0000-0000-********0007"
			_, err := dbpool.Exec(
				context.Background(),
				`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

org2 as (
	insert into organizations (id, display_name, created_by_user_id) select $10, $11, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

ten2 as (
	insert into tenants (id, display_name, config, organization_id)
	select gen_random_uuid(), 'test tenant 2', $3, id
	from org2
	returning id
),

usr as (
	insert into users (id, email, full_name, ls_user_id) select $8, '<EMAIL>', 'hello', $8 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, provider_user_id)
	select u.ls_user_id, u.email, 'supabase:non-sso', u.id from usr u
),

org_ident as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten
	cross join org
	returning id
),

org_ident2 as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $10, org2.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten2
	cross join org2
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, user_id, parent_identity_id, ls_user_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9, (select ls_user_id from usr)
	from ten
	cross join org
	returning id, tenant_id
),

ident2 as (
	insert into identities (id, tenant_id, organization_id, role_id, user_id, parent_identity_id, ls_user_id)
	select gen_random_uuid(), ten2.id, org2.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $10, (select ls_user_id from usr)
	from ten2
	cross join org2
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, user_id, organization_id, ls_user_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4, (select ls_user_id from usr)
from ident`,
				"********-0000-0000-0000-********0001",
				"test tenant",
				"{}",
				"********-0000-0000-0000-********0003",
				"test org",
				"********-0000-0000-0000-********0002",
				hashedApiKey,
				userId,
				"********-0000-0000-0000-********0006",
				"********-0000-0000-0000-********0004",
				"test org 2")
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/orgs/auth", nil)
			r.Header.Set("X-Api-Key", apiKey)

			// accepts requests when tenant present
			called := false
			TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				called = true
				assert.Equal(t, &auth.OrgAuthInfo{
					OrganizationID:          "********-0000-0000-0000-********0003",
					OrganizationIsPersonal:  false,
					IdentityID:              sql.NullString{String: "********-0000-0000-0000-********0006", Valid: true},
					OrganizationPermissions: ORG_USER_PERMISSIONS,
					OrganizationConfig:      testutil.GetDefaultOrgConfig(),
					UserID:                  userId,
					LSUserID:                userId,
					UserEmail:               "<EMAIL>",
					UserFullName:            "hello",
					DefaultTenantID:         "********-0000-0000-0000-********0001",
				}, auth.GetOrgAuthInfo(r))
			}))).ServeHTTP(w, r)
			assert.True(t, called)
			assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

			// returns json auth info
			rtr := chi.NewRouter()
			rtr.With(TestLogger(t)).With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
			srv := httptest.NewServer(rtr)
			defer srv.Close()

			req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
			assert.NoError(t, err)
			req.Header.Set("X-Api-Key", apiKey)
			res, err := srv.Client().Do(req)

			assert.NoError(t, err)
			defer res.Body.Close()
			assert.Equal(t, http.StatusOK, res.StatusCode)
			assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
			body, err := io.ReadAll(res.Body)
			assert.NoError(t, err)
			authResponse := &auth.OrgAuthInfo{}
			assert.NoError(t, json.Unmarshal(body, authResponse))
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:          "********-0000-0000-0000-********0003",
				OrganizationIsPersonal:  false,
				IdentityID:              sql.NullString{String: "********-0000-0000-0000-********0006", Valid: true},
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
				UserID:                  userId,
				LSUserID:                userId,
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				DefaultTenantID:         "********-0000-0000-0000-********0001",
			}, authResponse)
		})

		dbpool.Exec(context.Background(), "delete from organizations")

		t.Run("X-Tenant-ID with v2 pat", func(t *testing.T) {
			apiKey := "***************************************************"
			hashedApiKey := "37110cec3212c718a08dd789296a69147af6ff3809b12749a8feb2661d0e20de364ce3a006cbfc2cf8d2cc76ff216b6307e91e99b760fe05c1705a6671318674"
			tenant2ID := "********-0000-0000-0000-********0007"
			tenant2IdentityID := "********-0000-0000-0000-********0008"
			userId := "********-0000-0000-0000-********0008"
			_, err := dbpool.Exec(
				context.Background(),
				`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

org2 as (
	insert into organizations (id, display_name, created_by_user_id) select $10, $11, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

ten2 as (
	insert into tenants (id, display_name, config, organization_id)
	select $12, 'test tenant 2', $3, id
	from org2
	returning id
),

usr as (
	insert into users (id, email, full_name, ls_user_id) select $8, '<EMAIL>', 'hello', $8 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, provider_user_id)
	select u.ls_user_id, u.email, 'supabase:non-sso', u.id from usr u
),

org_ident as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten
	cross join org
	returning id
),

org_ident2 as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $10, org2.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten2
	cross join org2
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, user_id, parent_identity_id, ls_user_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9, (select ls_user_id from usr)
	from ten
	cross join org
	returning id, tenant_id
),

ident2 as (
	insert into identities (id, tenant_id, organization_id, role_id, user_id, parent_identity_id, ls_user_id)
	select $13, ten2.id, org2.id, (select id from roles where name = 'WORKSPACE_VIEWER'), $8, $10, (select ls_user_id from usr)
	from ten2
	cross join org2
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, user_id, organization_id, ls_user_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4, (select ls_user_id from usr)
from ident`,
				"********-0000-0000-0000-********0001",
				"test tenant",
				"{}",
				"********-0000-0000-0000-********0003",
				"test org",
				"********-0000-0000-0000-********0002",
				hashedApiKey,
				userId,
				"********-0000-0000-0000-********0006",
				"********-0000-0000-0000-********0004",
				"test org 2",
				tenant2ID,
				tenant2IdentityID)
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/auth", nil)
			r.Header.Set("X-Api-Key", apiKey)
			r.Header.Set("X-Tenant-ID", tenant2ID)

			// accepts requests when tenant present
			called := false
			TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				called = true
				assert.Equal(t, &auth.AuthInfo{
					OrganizationID:          "********-0000-0000-0000-********0003",
					OrganizationIsPersonal:  false,
					OrganizationIdentityID:  sql.NullString{String: "********-0000-0000-0000-********0006", Valid: true},
					TenantID:                tenant2ID,
					TenantHandle:            "",
					TenantConfig:            testutil.GetDefaultTenantConfig(),
					TenantIdentityID:        tenant2IdentityID,
					TenantIdentityReadOnly:  false,
					UserID:                  userId,
					LSUserID:                userId,
					UserEmail:               "<EMAIL>",
					UserFullName:            "hello",
					IdentityPermissions:     READ_ONLY_PERMISSIONS,
					OrganizationPermissions: ORG_USER_PERMISSIONS,
					OrganizationConfig:      testutil.GetDefaultOrgConfig(),
				}, auth.GetAuthInfo(r))
			}))).ServeHTTP(w, r)
			assert.True(t, called)
			assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

			// returns json auth info
			rtr := chi.NewRouter()
			rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
			srv := httptest.NewServer(rtr)
			defer srv.Close()

			req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
			assert.NoError(t, err)
			req.Header.Set("X-Api-Key", apiKey)
			req.Header.Set("X-Tenant-ID", tenant2ID)
			res, err := srv.Client().Do(req)

			assert.NoError(t, err)
			defer res.Body.Close()
			assert.Equal(t, http.StatusOK, res.StatusCode)
			assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
			body, err := io.ReadAll(res.Body)
			assert.NoError(t, err)
			authResponse := &auth.AuthInfo{}
			assert.NoError(t, json.Unmarshal(body, authResponse))
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "********-0000-0000-0000-********0003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "********-0000-0000-0000-********0006", Valid: true},
				TenantID:                tenant2ID,
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenant2IdentityID,
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				LSUserID:                userId,
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     READ_ONLY_PERMISSIONS,
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authResponse)
		})

		dbpool.Exec(context.Background(), "delete from organizations")

		t.Run("No access to different tenant via X-Tenant-ID with v2 pat", func(t *testing.T) {
			apiKey := "***************************************************"
			hashedApiKey := "37110cec3212c718a08dd789296a69147af6ff3809b12749a8feb2661d0e20de364ce3a006cbfc2cf8d2cc76ff216b6307e91e99b760fe05c1705a6671318674"
			tenant2ID := "********-0000-0000-0000-********0007"
			_, err := dbpool.Exec(
				context.Background(),
				`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

org2 as (
	insert into organizations (id, display_name, created_by_user_id) select $10, $11, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

ten2 as (
	insert into tenants (id, display_name, config, organization_id)
	select $12, 'test tenant 2', $3, id
	from org2
	returning id
),

usr as (
	insert into users (id, email, full_name) select $8, '<EMAIL>', 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, provider_user_id)
	select u.ls_user_id, u.email, 'supabase:non-sso', u.id from usr u
),

org_ident as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten
	cross join org
	returning id
),

org_ident2 as (
	insert into identities (id, organization_id, role_id, user_id, access_scope, ls_user_id)
	select $10, org2.id, (select id from roles where name = 'ORGANIZATION_USER'),  $8, 'organization', (select ls_user_id from usr)
	from ten2
	cross join org2
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, user_id, parent_identity_id, ls_user_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9, (select ls_user_id from usr)
	from ten
	cross join org
	returning id, tenant_id
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, user_id, organization_id, ls_user_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4, (select ls_user_id from usr)
from ident`,
				"********-0000-0000-0000-********0001",
				"test tenant",
				"{}",
				"********-0000-0000-0000-********0003",
				"test org",
				"********-0000-0000-0000-********0002",
				hashedApiKey,
				"********-0000-0000-0000-********0009",
				"********-0000-0000-0000-********0006",
				"********-0000-0000-0000-********0004",
				"test org 2",
				tenant2ID)
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/auth", nil)
			r.Header.Set("X-Api-Key", apiKey)
			r.Header.Set("X-Tenant-ID", tenant2ID)

			// Should fail since no access to tenant2
			TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				assert.Fail(t, "should not be called")
			}))).ServeHTTP(w, r)
			assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
		})
	}

}
