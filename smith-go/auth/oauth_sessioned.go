package auth

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/markbates/goth"

	"github.com/gorilla/sessions"
	"github.com/markbates/goth/gothic"
	"github.com/rbcervilla/redisstore/v9"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/gofrs/uuid"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/database"
)

const UserSessionCookieName = "_ls_oauth_user"
const userInfoKey = "user-info"

var linkableOauthLoginMethods = map[string][]string{
	"custom-oidc": {"email"},
	"email":       {"custom-oidc"},
}

type SessionError struct {
	Message string
}

func (e *SessionError) Error() string {
	return e.Message
}

var sessionNotFoundError = &SessionError{Message: "session not found"}
var sessionExpiredError = &SessionError{Message: "session expired"}

type StoredOAuthUserInfo struct {
	Provider string `json:"provider"`
	Sub      string `json:"sub"`
	Email    string `json:"email"`
	Name     string `json:"name"`
	// This is the UUID5 of the sub
	UserID       uuid.UUID `json:"userId"`
	LSUserID     string    `json:"lsUserId"`
	FirstName    string    `json:"firstName"`
	LastName     string    `json:"lastName"`
	NickName     string    `json:"nickName"`
	Description  string    `json:"description"`
	AvatarURL    string    `json:"avatarUrl"`
	Location     string    `json:"location"`
	ExpiresAt    time.Time `json:"expiresAt"`
	AccessToken  string    `json:"accessToken"`
	RefreshToken string    `json:"refreshToken"`
}

func (info StoredOAuthUserInfo) ToBase64() (string, error) {
	// Make a copy so as not to alter the original in-place
	info.AccessToken = ""
	info.RefreshToken = ""

	jsonBytes, err := json.Marshal(info)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(jsonBytes), nil
}

func logFieldsFromUserSession(ctx context.Context, userInfo StoredOAuthUserInfo) context.Context {
	ctx = config.LogAndContextSetField(ctx, "sub", slog.StringValue(userInfo.Sub))
	ctx = config.LogAndContextSetField(ctx, "user_id", slog.StringValue(userInfo.UserID.String()))
	ctx = config.LogAndContextSetField(ctx, "ls_user_id", slog.StringValue(userInfo.LSUserID))
	ctx = config.LogAndContextSetField(ctx, "provider", slog.StringValue(userInfo.Provider))
	return ctx
}

type HandlerOAuthSessioned struct {
	Pg              *database.AuditLoggedPool
	ApiKey          *HandlerApiKey
	XServiceKeyAuth *HandlerXServiceKeyAuth

	cache    *lsredis.Cache[uint64, *AuthInfo]
	orgCache *lsredis.Cache[uint64, *OrgAuthInfo]

	sessionStore  *redisstore.RedisStore
	sessionMaxAge time.Duration
}

func NewOAuthSessioned(pg *database.AuditLoggedPool, redisClient redis.UniversalClient, cacheTTLSecs int, sessionTTLSecs int) *HandlerOAuthSessioned {
	ttl := time.Duration(cacheTTLSecs) * time.Second
	cache := lsredis.NewCache[uint64, *AuthInfo](redisClient, "authInfo", ttl)
	orgCache := lsredis.NewCache[uint64, *OrgAuthInfo](redisClient, "orgAuthInfo", ttl)
	sessionStore, err := redisstore.NewRedisStore(context.Background(), redisClient)
	if err != nil {
		panic(fmt.Sprintf("failed to create redis user session store: %v", err))
	}
	sessionStore.KeyPrefix("smith:user:session:")
	sessionStore.Options(sessions.Options{
		Path:     "/",
		MaxAge:   sessionTTLSecs,
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
	})

	return &HandlerOAuthSessioned{
		Pg:              pg,
		ApiKey:          &HandlerApiKey{pg, cache, orgCache},
		XServiceKeyAuth: NewHandlerXServiceKeyAuth(pg, cache, orgCache),
		cache:           cache,
		orgCache:        orgCache,
		sessionStore:    sessionStore,
		sessionMaxAge:   time.Duration(sessionTTLSecs) * time.Second,
	}
}

// Adds users and provider_users records if necessary.
// Returns resolved provider_users info, or error if provisioning failed.
// This allows linking between basic auth and oauth sessioned login methods
func maybeProvisionOauth(tx pgx.Tx, ctx context.Context, incomingUserInfo LinkableUserInfo, middlewareType string, oplog *slog.Logger) (*ProviderUserInfo, error) {
	providerUserRows, _ := tx.Query(
		ctx,
		ListProvidersForEmailOrUserIdQuery,
		incomingUserInfo.Email,
		incomingUserInfo.ProviderUserId,
	)
	providerInfos, err := pgx.CollectRows(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
	if err != nil && err != pgx.ErrNoRows {
		oplog.Error("pgx error collecting provider user info by email", "err", err)
		return nil, err
	}

	var providerInfoWithMatchingUserId *ProviderUserInfo
	var providerInfoWithMatchingEmail *ProviderUserInfo
	var providerInfoExactMatch *ProviderUserInfo
	for _, v := range providerInfos {
		if v.ProviderUserID.String == incomingUserInfo.ProviderUserId {
			providerInfoWithMatchingUserId = v
		}
		if v.Email.String == incomingUserInfo.Email {
			providerInfoWithMatchingEmail = v
		}
		if v.ProviderUserID.String == incomingUserInfo.ProviderUserId && v.Email.String == incomingUserInfo.Email && v.Provider.String == incomingUserInfo.Provider && v.FullName.String == incomingUserInfo.FullName && (v.SAMLProviderID.String == incomingUserInfo.SamlProviderId || (!v.SAMLProviderID.Valid && incomingUserInfo.SamlProviderId == nil)) {
			providerInfoExactMatch = v
		}
	}

	if len(providerInfos) == 0 {
		// if we have no existing login methods or user, create them
		oplog.Info("creating user and provider user info")
		providerUserRow, _ := tx.Query(
			ctx,
			SafeInsertUserAndProviderUserQuery,
			incomingUserInfo.ProviderUserId,
			incomingUserInfo.Email,
			incomingUserInfo.FullName,
			incomingUserInfo.Provider,
			incomingUserInfo.SamlProviderId,
			incomingUserInfo.ProviderUserId,
		)
		providerInfo, err := pgx.CollectExactlyOneRow(providerUserRow, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
		if err != nil {
			oplog.Error("error creating provider user info", "err", err)
			return nil, err
		}
		return providerInfo, nil
	} else if len(providerInfos) == 1 && providerInfos[0].Provider.String != incomingUserInfo.Provider && providerInfoWithMatchingEmail != nil && providerInfoWithMatchingUserId == nil {
		// we have an existing login method with the same email address but different provider,
		// so link the new login method if allowed
		if linkableProviders, ok := linkableOauthLoginMethods[providerInfoWithMatchingEmail.Provider.String]; !ok {
			errorMsg := fmt.Sprintf("unexpected existing login method %s, not linkable to %s", providerInfoWithMatchingEmail.Provider.String, incomingUserInfo.Provider)
			oplog.Error(errorMsg)
			return nil, errors.New(errorMsg)
		} else {
			var found bool
			for _, v := range linkableProviders {
				if v == incomingUserInfo.Provider {
					found = true
					break
				}
			}
			if !found {
				errorMsg := fmt.Sprintf("existing login method %s is not linkable to %s", providerInfoWithMatchingEmail.Provider.String, incomingUserInfo.Provider)
				oplog.Error(errorMsg)
				return nil, errors.New(errorMsg)
			}
		}

		linkProviderUsersQuery := linkProviderUsersQueryByProvider(incomingUserInfo.Provider)
		linkedProviderInfoRows, _ := tx.Query(
			ctx,
			linkProviderUsersQuery,
			incomingUserInfo.Email,
			incomingUserInfo.Provider,
			incomingUserInfo.SamlProviderId,
			incomingUserInfo.ProviderUserId,
			incomingUserInfo.FullName,
		)

		linkedProviderInfo, err := pgx.CollectExactlyOneRow(linkedProviderInfoRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
		if err != nil {
			oplog.Error("error linking provider user info", "err", err)
			return nil, err
		}
		oplog.Info("linked provider user info", "linked_provider_user_id", linkedProviderInfo.ProviderUserID.String, "linked_provider", linkedProviderInfo.Provider.String)
		return linkedProviderInfo, nil
	} else if providerInfoExactMatch != nil {
		// we already have a matching login method
		return providerInfoExactMatch, nil
	} else if (len(providerInfos) == 1 || len(providerInfos) == 2) && providerInfoWithMatchingUserId != nil && (providerInfoWithMatchingUserId.Email.String != incomingUserInfo.Email || providerInfoWithMatchingUserId.FullName.String != incomingUserInfo.FullName) {
		// we have 1 or 2 existing login method with the same user ID but different email address or full name,
		// so update those fields
		oplog.Info("updating provider user info", "old_email", providerInfoWithMatchingUserId.Email.String, "new_email", incomingUserInfo.Email, "old_name", providerInfoWithMatchingUserId.FullName.String, "new_name", incomingUserInfo.FullName)
		updatedProviderInfoRows, _ := tx.Query(
			ctx,
			updateLinkedUserInfoQuery,
			providerInfoWithMatchingUserId.LSUserID,
			incomingUserInfo.Email,
			incomingUserInfo.FullName,
		)
		updatedProviderInfos, err := pgx.CollectRows(updatedProviderInfoRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
		if err != nil {
			oplog.Error("error updating provider user info", "err", err)
			return nil, err
		}
		if len(updatedProviderInfos) == 0 {
			oplog.Error("no matching updated provider info found after updating user info")
			return nil, errors.New("no matching updated provider info found after updating user info")
		}
		// select the updated provider info with the matching user ID
		for _, v := range updatedProviderInfos {
			if v.ProviderUserID.String == incomingUserInfo.ProviderUserId {
				return v, nil
			}
		}
		oplog.Error("updated user info but did not find one with matching user ID")
		for _, info := range updatedProviderInfos {
			oplog.Error("updated provider info", "updated_id", info.ID, "ls_user_id", info.LSUserID, "updated_provider", info.Provider.String, "updated_provider_user_id", info.ProviderUserID.String, "updated_email", info.Email.String, "updated_full_name", info.FullName.String)
		}
		return nil, errors.New("unexpected state updating user info")
	} else if len(providerInfos) == 1 && providerInfoWithMatchingEmail != nil && providerInfoWithMatchingUserId == nil {
		// we have a single existing login method with the same email address but different user ID,
		// this should not happen, so return an error
		oplog.Error("email is already in use with a different user ID")
		return nil, errors.New("email is already in use with a different user ID")
	}

	oplog.Error("unexpected state provisioning user. see existing provider info list to compare to incoming info:")
	for _, info := range providerInfos {
		oplog.Error("existing provider info", "id", info.ID, "ls_user_id", info.LSUserID, "provider", info.Provider.String, "provider_user_id", info.ProviderUserID.String, "email", info.Email.String, "full_name", info.FullName.String)
	}

	return nil, errors.New("unexpected state provisioning user")
}

func storeUserSession(store *redisstore.RedisStore, value *StoredOAuthUserInfo, req *http.Request, res http.ResponseWriter, oplog *slog.Logger) error {
	encoded, err := json.Marshal(value)
	if err != nil {
		errorMsg := "Error encoding user information. Please check " + config.Env.ServiceName + " logs."
		oplog.Error(errorMsg, "err", err)
		return err
	}
	encodedB64 := base64.StdEncoding.EncodeToString(encoded)

	session, err := store.Get(req, UserSessionCookieName)
	if err != nil {
		return err
	}
	session.Values[userInfoKey] = encodedB64
	err = session.Save(req, res)
	if err != nil {
		return err
	}
	return nil
}

func userFromExistingSession(store *redisstore.RedisStore, req *http.Request, res http.ResponseWriter, oplog *slog.Logger) (StoredOAuthUserInfo, error) {
	sess, err := store.Get(req, UserSessionCookieName)
	if err != nil {
		return StoredOAuthUserInfo{}, err
	}

	encodedUserData, ok := sess.Values[userInfoKey].(string)
	if !ok {
		return StoredOAuthUserInfo{}, sessionNotFoundError
	}

	decoded, err := base64.StdEncoding.DecodeString(encodedUserData)
	if err != nil {
		return StoredOAuthUserInfo{}, err
	}

	var user StoredOAuthUserInfo
	if err := json.Unmarshal([]byte(decoded), &user); err != nil {
		return StoredOAuthUserInfo{}, err
	}

	if user.ExpiresAt.Before(time.Now()) {
		if user.RefreshToken != "" {
			oplog.Info("session expired for user, refreshing access token", "email", user.Email)
			// refresh the access token
			provider, err := goth.GetProvider(user.Provider)
			if err != nil {
				return StoredOAuthUserInfo{}, err
			}
			refreshedToken, err := provider.RefreshToken(user.RefreshToken)
			if err != nil {
				return StoredOAuthUserInfo{}, err
			}
			user.AccessToken = refreshedToken.AccessToken
			user.RefreshToken = refreshedToken.RefreshToken
			user.ExpiresAt = refreshedToken.Expiry
			err = storeUserSession(store, &user, req, res, oplog)
			if err != nil {
				return StoredOAuthUserInfo{}, err
			}
			return user, nil
		}

		return StoredOAuthUserInfo{}, sessionExpiredError
	}

	// Return the user object
	return user, nil
}

func (h *HandlerOAuthSessioned) CacheKey(r *http.Request) uint64 {
	return cacheKeyWithHeaders(r, "X-Tenant-Id", "X-Api-Key", "X-Service-Key")
}

func (h *HandlerOAuthSessioned) OrgMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(ctx)
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		organizationId := r.Header.Get("X-Organization-Id")
		if organizationId == "" {
			oplog.Warn("Missing organization ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		var auth *OrgAuthInfo
		if user, err := userFromExistingSession(h.sessionStore, r, w, oplog); err == nil {
			ctx = logFieldsFromUserSession(ctx, user)
			orgRows, _ := h.Pg.Query(
				ctx,
				getOrgInfoForProvisionedUserQuery,
				user.UserID.String(),
				user.LSUserID,
				user.Email,
				user.Name,
				organizationId,
				nil,
			)
			auth, err = pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
			if err != nil {
				oplog.Warn("error collecting org auth info", "err", err)
				render.Status(r, http.StatusForbidden)
				render.JSON(w, r, map[string]string{"error": "Forbidden"})
				return
			}
			InitializeAndUnmarshalOrgConfig(auth)
		} else {
			if errors.Is(err, sessionNotFoundError) {
				oplog.Warn("session not found for org ID", "organization_id", organizationId)
			} else if errors.Is(err, sessionExpiredError) {
				oplog.Warn("session expired for org ID", "organization_id", organizationId)
			} else {
				oplog.Error("Error completing org user auth", "err", err)
			}
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, orgAuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerOAuthSessioned) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(ctx)
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.Middleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.Middleware(next).ServeHTTP(w, r)
			return
		}

		// get tenant id header
		tenantId := r.Header.Get("X-Tenant-Id")
		if tenantId == "" {
			oplog.Warn("Missing tenant ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
		ctx = config.LogAndContextSetField(ctx, "tenant_id", slog.StringValue(tenantId))

		var auth *AuthInfo
		if user, err := userFromExistingSession(h.sessionStore, r, w, oplog); err == nil {
			ctx = logFieldsFromUserSession(ctx, user)
			tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
			if err != nil {
				oplog.Error("error beginning transaction", "err", err)
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			}
			defer func() {
				if err != nil {
					tx.Rollback(ctx)
					return
				}
				err = tx.Commit(ctx) // commit, set return error here
			}()

			// retrieve org ID from tenant ID if not provided
			organizationId := r.Header.Get("X-Organization-Id")
			var resolvedOrgId string
			if organizationId == "" {
				orgIdRow, _ := tx.Query(
					ctx,
					getOrgIdFromTenantIdQuery,
					tenantId,
				)
				if orgId, err := pgx.CollectExactlyOneRow(orgIdRow, pgx.RowTo[string]); err == nil {
					resolvedOrgId = orgId
				} else if resolvedOrgId == "" {
					oplog.Warn("error collecting org ID from tenant ID", "err", err)
					render.Status(r, http.StatusUnauthorized)
					render.JSON(w, r, map[string]string{"error": "Unauthorized"})
					return
				}
			} else {
				resolvedOrgId = organizationId
			}
			ctx = config.LogAndContextSetField(ctx, "organization_id", slog.StringValue(resolvedOrgId))

			// org info
			orgRows, _ := h.Pg.Query(
				ctx,
				getOrgInfoForProvisionedUserQuery,
				user.UserID.String(),
				user.LSUserID,
				user.Email,
				user.Name,
				resolvedOrgId,
				nil,
			)
			orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
			if err == pgx.ErrNoRows {
				oplog.Warn("No user found for org in workspace middleware")
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			} else if err != nil {
				oplog.Error("pgx error collecting org auth info in workspace middleware", "err", err)
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			}

			providerUserRows, _ := tx.Query(
				ctx,
				ListProvidersQuery,
				user.UserID,
				user.Email,
				user.Provider,
				nil,
			)
			providerInfo, err := pgx.CollectExactlyOneRow(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
			if err == pgx.ErrNoRows {
				oplog.Warn("No login method found for user")
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			} else if err != nil {
				oplog.Error("pgx error collecting provider user info in workspace middleware", "err", err)
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			}

			// tenant info
			rows, _ := tx.Query(
				ctx,
				getTenantInfoForUserQuery,
				providerInfo.LSUserID,
				tenantId,
			)
			tenantAuth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[TenantAuthInfo])
			if err == pgx.ErrNoRows {
				oplog.Warn("No identity found for user")
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			} else if err != nil {
				oplog.Error("pgx error collecting workspace auth info in workspace middleware", "err", err)
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			}

			auth = &AuthInfo{
				OrganizationID:                  orgAuth.OrganizationID,
				OrganizationIsPersonal:          orgAuth.OrganizationIsPersonal,
				OrganizationMetronomeCustomerId: orgAuth.OrganizationMetronomeCustomerId,
				OrganizationStripeCustomerId:    orgAuth.OrganizationStripeCustomerId,
				OrganizationIdentityID:          orgAuth.IdentityID,
				OrganizationIdentityReadOnly:    orgAuth.IdentityReadOnly,
				OrganizationPermissions:         orgAuth.OrganizationPermissions,
				OrganizationConfig:              orgAuth.OrganizationConfig,
				OrganizationDisabled:            orgAuth.OrganizationDisabled,
				PublicSharingDisabled:           orgAuth.PublicSharingDisabled,
				SsoOnly:                         orgAuth.SsoOnly,
				UserID:                          orgAuth.UserID,
				LSUserID:                        orgAuth.LSUserID,
				UserEmail:                       orgAuth.UserEmail,
				UserFullName:                    orgAuth.UserFullName,
				TenantID:                        tenantAuth.TenantID,
				TenantHandle:                    tenantAuth.TenantHandle,
				TenantIsDeleted:                 tenantAuth.TenantIsDeleted,
				TenantConfig:                    tenantAuth.TenantConfig,
				TenantIdentityID:                tenantAuth.IdentityID,
				TenantIdentityReadOnly:          tenantAuth.IdentityReadOnly,
				IdentityPermissions:             tenantAuth.IdentityPermissions,
			}

			InitializeAndUnmarshalConfig(auth)
		} else {
			if errors.Is(err, sessionNotFoundError) {
				oplog.Warn("session not found")
			} else if errors.Is(err, sessionExpiredError) {
				oplog.Warn("session expired")
			} else {
				oplog.Error("Error retrieving user session", "err", err)
			}
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerOAuthSessioned) GetTenantlessAuth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	if r.Header.Get("X-Api-Key") != "" {
		h.ApiKey.GetTenantlessAuth(w, r)
		return
	}

	var auth *AllTenantsAuthInfo
	if user, err := userFromExistingSession(h.sessionStore, r, w, oplog); err == nil {
		ctx = logFieldsFromUserSession(ctx, user)
		rows, _ := h.Pg.Query(
			ctx,
			listTenantsAndOrgsForProvisionedUserQuery,
			user.UserID.String(),
			user.LSUserID,
			user.Email,
			user.Name,
			user.Provider,
			nil,
		)
		auth, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[AllTenantsAuthInfo])
		if err != nil {
			oplog.Error("error collecting auth info in tenantless auth", "err", err)
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}
	} else {
		if errors.Is(err, sessionNotFoundError) {
			oplog.Warn("session not found for tenantless user")
		} else if errors.Is(err, sessionExpiredError) {
			oplog.Warn("session expired for tenantless user")
		} else {
			oplog.Error("error getting user session for tenantless user auth", "err", err)
		}
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func (h *HandlerOAuthSessioned) Callback(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	user, err := gothic.CompleteUserAuth(w, r)
	if err != nil {
		errorMsg := "Error completing user authentication"
		oplog.Error(errorMsg, "err", err)
		http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?error="+url.QueryEscape(errorMsg), http.StatusTemporaryRedirect)
		return
	}
	provider := chi.URLParam(r, "provider")
	if provider == "" {
		errorMsg := "Provider not found. Please contact support."
		oplog.Error(errorMsg)
		http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?error="+url.QueryEscape(errorMsg), http.StatusTemporaryRedirect)
		return
	}
	_, ok := config.Env.SupportedProviders[provider]
	if !ok {
		errorMsg := fmt.Sprintf("Provider '%s' not supported. Please contact support.", provider)
		oplog.Error(errorMsg)
		http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?error="+url.QueryEscape(errorMsg), http.StatusTemporaryRedirect)
	}

	// user.UserID may not be a UUID, so hash it
	userIdFromSub := uuid.NewV5(uuid.NamespaceOID, user.UserID)

	linkableUserInfo := LinkableUserInfo{
		Email:          user.Email,
		FullName:       user.Name,
		Provider:       provider,
		ProviderUserId: userIdFromSub.String(),
		SamlProviderId: nil,
	}
	ctx = logFieldsFromUserInfo(ctx, linkableUserInfo)
	tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
	if err != nil {
		oplog.Error("error starting transaction", "err", err)
		http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?error="+url.QueryEscape("error starting transaction"), http.StatusTemporaryRedirect)
		return
	}
	defer func() {
		if err != nil {
			tx.Rollback(ctx)
			return
		}
		err = tx.Commit(ctx) // commit, set return error here
	}()

	providerUserInfo, err := maybeProvisionOauth(tx, ctx, linkableUserInfo, "callback", oplog)
	if err != nil {
		oplog.Error("error provisioning user", "err", err)
		http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?error="+url.QueryEscape("error provisioning or linking user"), http.StatusTemporaryRedirect)
		return
	}

	// hack to avoid taking ID token expiry as expiry of the user session in cases where refresh tokens can't be used
	// see FetchUser logic: https://github.com/markbates/goth/blob/master/providers/openidConnect/openidConnect.go
	if config.Env.OauthOverrideTokenExpiry {
		user.ExpiresAt = time.Now().Add(h.sessionMaxAge)
	}

	userInfo := &StoredOAuthUserInfo{
		Provider:     provider,
		Sub:          user.UserID,
		Email:        user.Email,
		Name:         user.Name,
		UserID:       userIdFromSub,
		LSUserID:     providerUserInfo.LSUserID,
		FirstName:    user.FirstName,
		LastName:     user.LastName,
		NickName:     user.NickName,
		Description:  user.Description,
		AvatarURL:    user.AvatarURL,
		Location:     user.Location,
		ExpiresAt:    user.ExpiresAt,
		AccessToken:  user.AccessToken,
		RefreshToken: user.RefreshToken,
	}

	// store the user info
	// https://github.com/markbates/goth/issues/270#issuecomment-495662495
	err = storeUserSession(h.sessionStore, userInfo, r, w, oplog)
	if err != nil {
		errorMsg := "Error storing user information in session. Please contact support."
		oplog.Error(errorMsg, "err", err)
		http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?error="+url.QueryEscape(errorMsg), http.StatusTemporaryRedirect)
		return
	}
	// Hack: strip all _gothic_session cookies from Set-Cookie header to avoid auth error
	// https://github.com/markbates/goth/issues/548
	if setCookieHeader := w.Header().Values("Set-Cookie"); len(setCookieHeader) > 0 {
		var newCookies []string
		for _, cookie := range setCookieHeader {
			if !strings.Contains(cookie, "_gothic_session") {
				newCookies = append(newCookies, cookie)
			}
		}
		w.Header().Del("Set-Cookie")
		for _, cookie := range newCookies {
			w.Header().Add("Set-Cookie", cookie)
		}
	}
	encodedB64, err := userInfo.ToBase64()
	if err != nil {
		errorMsg := "Error encoding user information. Please check " + config.Env.ServiceName + " logs."
		oplog.Error(errorMsg, "err", err)
		http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?error="+url.QueryEscape(errorMsg), http.StatusTemporaryRedirect)
		return
	}
	http.Redirect(w, r, config.Env.OAuthRedirectUrl+"?user="+encodedB64, http.StatusTemporaryRedirect)
}

func LogoutUser(store *redisstore.RedisStore, res http.ResponseWriter, req *http.Request) error {
	session, err := store.Get(req, UserSessionCookieName)
	if err != nil {
		return err
	}
	session.Options.MaxAge = -1
	session.Values = make(map[interface{}]interface{})
	err = session.Save(req, res)
	if err != nil {
		return fmt.Errorf("could not delete user oauth session: %w", err)
	}
	return nil
}

func (h *HandlerOAuthSessioned) Logout(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())
	err := LogoutUser(h.sessionStore, w, r)
	if err != nil {
		oplog.Error("error removing user session", "err", err)
	}
	err = gothic.Logout(w, r)
	if err != nil {
		oplog.Error("error logging out", "err", err)
	}
	http.Redirect(w, r, config.Env.LangSmithUrl, http.StatusTemporaryRedirect)
}

func (h *HandlerOAuthSessioned) Auth(w http.ResponseWriter, r *http.Request) {
	gothic.BeginAuthHandler(w, r)
}

func (h *HandlerOAuthSessioned) XServiceKeyMiddleware(next http.Handler) http.Handler {
	return h.XServiceKeyAuth.XServiceKeyMiddleware(next)
}

func (h *HandlerOAuthSessioned) FetchInternalAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalAuth(w, r)
}

func (h *HandlerOAuthSessioned) FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalOrgAuth(w, r)
}

func (h *HandlerOAuthSessioned) CurrentUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	user, err := userFromExistingSession(h.sessionStore, r, w, oplog)
	if err != nil {
		oplog.Error("error getting user session", "err", err)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}
	ctx = logFieldsFromUserSession(ctx, user)
	encodedB64, err := user.ToBase64()
	if err != nil {
		oplog.Error("error encoding user information", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]string{"error": "Internal Server Error"})
		return
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, map[string]string{"user": encodedB64})
}
