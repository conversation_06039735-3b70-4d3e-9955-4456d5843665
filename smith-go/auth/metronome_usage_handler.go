package auth

import (
	"net/http"

	"github.com/ggicci/httpin"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"langchain.com/smith/metronome"
)

type UsageHandler struct{}

func NewUsageHandler() *UsageHandler {
	return &UsageHandler{}
}

func (u *UsageHandler) InternalGetUsage(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())
	req := r.Context().Value(httpin.Input).(*metronome.UsageRequest)
	authInfo := GetOrgAuthInfo(r)

	metronomeClient := metronome.GetMetronomeClient()

	if metronomeClient == nil {
		oplog.Error("Metronome client is nil")
		http.Error(w, "Error fetching usage", http.StatusInternalServerError)
		return
	}

	// returning monthly usage for now, may adjust request params later to allow for more control
	response, err := metronome.GetMonthlyUsage(r.Context(), metronomeClient, authInfo.OrganizationMetronomeCustomerId, req.StartingOn, req.EndingBefore, req.OnCurrentPlan)
	if err != nil {
		oplog.Error("Error fetching from usage from metronome", "error", err)
		http.Error(w, "Error fetching usage", http.StatusInternalServerError)
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, response)
}
