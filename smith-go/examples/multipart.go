package examples

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"mime"
	"mime/multipart"
	"net/http"
	"reflect"
	"strconv"
	"strings"

	"langchain.com/smith/util"

	"github.com/go-chi/chi/v5"
	"github.com/xeipuuv/gojsonschema"
	"golang.org/x/sync/errgroup"

	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/storage"
)

type MultipartExamplesHandler struct {
	Pg             *database.AuditLoggedPool
	StorageClient  storage.StorageClient
	ExamplesCRUD   *ExamplesCRUD
	ClickHousePool driver.Conn
}

type exampleValidateRequest struct {
	ID          string                 `json:"id,omitempty"`
	DatasetID   string                 `json:"dataset_id"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Inputs      json.RawMessage        `json:"inputs,omitempty"`
	Outputs     json.RawMessage        `json:"outputs,omitempty"`
	Split       *Split                 `json:"split,omitempty"`
	CreatedAt   *TimeUTC               `json:"created_at,omitempty"`
	SourceRunID string                 `json:"source_run_id,omitempty"`
}

type RunData struct {
	ID            string
	Inputs        map[string]interface{}
	Outputs       map[string]interface{}
	InputsBytes   []byte
	OutputsBytes  []byte
	Extras        map[string]interface{}
	S3Urls        map[string]interface{}
	InputsS3Urls  map[string]interface{}
	OutputsS3Urls map[string]interface{}
}

var (
	MaxBytes    = int64(1 * 1024 * 1024 * 1024) // 1 GB
	MaxFileSize = int64(50 * 1024 * 1024)       // 50 MB

	// 422 Unprocessable Entity
	errReadingMultipartData         = errors.New("error reading multipart data")
	errMissingContentDisposition    = errors.New("missing Content-Disposition header")
	errInvalidContentDisposition    = errors.New("invalid Content-Disposition header")
	errMissingContentType           = errors.New("missing Content-Type")
	errInvalidContentType           = errors.New("invalid Content-Type")
	errInvalidPartName              = errors.New("invalid part name")
	errInvalidAttachmentPart        = errors.New("invalid attachment part")
	errMissingLengthParam           = errors.New("missing length parameter")
	errInvalidLengthParam           = errors.New("invalid length parameter")
	errMissingDatasetID             = errors.New("missing dataset_id")
	errInvalidDatasetID             = errors.New("invalid dataset_id, must be a uuid")
	errInvalidAttachmentsOperations = errors.New("invalid attachments operations")
	errInvalidExampleCreate         = errors.New("invalid example create")
	errInvalidRunIO                 = errors.New("invalid run IO")
	errInvalidExampleSchema         = errors.New("invalid example schema")

	// 403 Forbidden
	errUnauthorized = errors.New("unauthorized")

	// 415 Unsupported Media Type
	errUnsupportedContentEncoding = errors.New("unsupported Content-Encoding")

	// 500 Internal Server Error
	errGenerateS3KeyFailure = errors.New("failed to generate S3 key")
	errRetrievingFile       = errors.New("error retrieving file")

	// 501 Not Implemented
	errBlobStorageNotEnabled = errors.New("blob storage is not enabled")
)

func (h *MultipartExamplesHandler) handleError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error) {
	var status int
	var message string

	switch {
	case errors.Is(err, errReadingMultipartData),
		errors.Is(err, errMissingContentDisposition), errors.Is(err, errInvalidContentDisposition),
		errors.Is(err, errMissingContentType), errors.Is(err, errInvalidContentType),
		errors.Is(err, errInvalidPartName),
		errors.Is(err, errInvalidJsonPart),
		errors.Is(err, errInvalidExampleSchema),
		errors.Is(err, errInvalidAttachmentPart),
		errors.Is(err, errMissingLengthParam),
		errors.Is(err, errInvalidLengthParam),
		errors.Is(err, errMissingDatasetID),
		errors.Is(err, errInvalidDataset),
		errors.Is(err, errInvalidAttachmentsOperations),
		errors.Is(err, errInvalidExampleCreate),
		errors.Is(err, errInvalidRunIO),
		errors.Is(err, errInvalidDatasetID),
		errors.Is(err, errArraysNotAllowed):
		status = http.StatusUnprocessableEntity
		message = "Unprocessable entity: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errDatasetNotFound):
		status = http.StatusNotFound
		message = "Dataset not found: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errExamplesNotFound):
		status = http.StatusNotFound
		message = "Examples not found: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errUnsupportedContentEncoding):
		status = http.StatusUnsupportedMediaType
		message = "Unsupported Content-Encoding: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, context.Canceled):
		status = 499 // Client closed request
		message = "Client closed connection"
		oplog.Warn(message, "error", err)

	case errors.Is(err, io.EOF):
		status = http.StatusInternalServerError
		message = "Unexpected end of data stream"
		oplog.Error(message, "error", err)

	case errors.Is(err, errExampleAlreadyExists):
		status = http.StatusConflict
		message = "Example already exists: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errDatasetExampleMismatch):
		status = http.StatusConflict
		message = "Dataset ID mismatch: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errUnauthorized):
		status = http.StatusForbidden
		message = "Unauthorized: " + err.Error()
		oplog.Warn(message, "error", err)

	default:
		// if it's known transient => 503, else 500
		if util.IsRetriableError(err) {
			status = http.StatusServiceUnavailable
			message = "Service unavailable: " + err.Error()
			oplog.Warn(message, "error", err)
		} else {
			status = http.StatusInternalServerError
			referenceId := uuid.NewString()
			message = "Internal server error: reference ID " + referenceId
			oplog.Error(message, "err", err, "reference_id", referenceId)
		}
	}

	render.Status(r, status)
	render.JSON(w, r, map[string]string{"error": message})
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	// Error message
	Error string `json:"error" example:"Invalid request: missing required fields"`

	// Optional error details as JSON string
	Details string `json:"details,omitempty" example:"{\"field\":\"dataset_id\",\"reason\":\"required\"}"`
}

// UploadDatasetExamples godoc
// @Summary      Upload Examples
// @Description  This endpoint allows clients to upload examples to a specified dataset by sending a multipart/form-data POST request.
// @Description  Each form part contains either JSON-encoded data or binary attachment files associated with an example.
//
// @Tags         examples
// @Accept       multipart/form-data
// @Produce      json
// @Param        dataset_id                       path      string                  true   "Dataset ID"    format(uuid)
// @Param        {example_id}                     formData  string                  true   "The Example info as JSON. Can have fields 'metadata', 'split', 'use_source_run_io', 'source_run_id', 'created_at', 'modified_at'"  format(binary)
// @Param        {example_id}.inputs              formData  string                  true   "The Example inputs as JSON" format(binary)
// @Param        {example_id}.outputs             formData  string                  false  "THe Example outputs as JSON" format(binary)
// @Param        {example_id}.attachments.{name}  formData  string                  false  "File attachment named {name}" format(binary)
// @Success      201  {object}  ExamplesCreatedResponse
// @Failure      400  {object}  ErrorResponse
// @Failure      403  {object}  ErrorResponse
// @Failure      409  {object}  ErrorResponse
// @Failure      422  {object}  ErrorResponse
// @Router       /v1/platform/datasets/{dataset_id}/examples [post]
func (h *MultipartExamplesHandler) UploadDatasetExamples(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	ctx = context.WithValue(ctx, config.OperationCtxKey, "create_examples")

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		h.handleError(w, r, oplog, errUnauthorized)
		return
	}

	// get datasetId as a path parameter
	datasetId := chi.URLParam(r, "dataset_id")
	if datasetId == "" {
		h.handleError(w, r, oplog, errMissingDatasetID)
		return
	}

	// check to see if datasetId is a valid UUID
	if err := uuid.Validate(datasetId); err != nil {
		h.handleError(w, r, oplog, errInvalidDatasetID)
		return
	}

	dsInfoForInsert, err := h.ExamplesCRUD.readDatasetInfoForUpsert(
		ctx,
		datasetId,
		authInfo.TenantID,
	)

	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	// limit size of request body
	r.Body = http.MaxBytesReader(w, r.Body, MaxBytes)

	// use reader to process body as a stream
	reader, err := r.MultipartReader()
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %s", errReadingMultipartData, err))
		return
	}

	examplesData := make(map[string]*exampleInfo)
	uploadResults := make([]*storage.UploadAsyncResult, 0)

	for {
		part, err := reader.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %s", errReadingMultipartData, err))
			return
		}

		contentDisposition := part.Header.Get("Content-Disposition")
		if contentDisposition == "" {
			h.handleError(w, r, oplog, errMissingContentDisposition)
			return
		}

		contentEncoding := part.Header.Get("Content-Encoding")
		if contentEncoding != "" {
			// TODO(agola11): support gzip
			h.handleError(w, r, oplog, errUnsupportedContentEncoding)
			return
		}

		// parse Content-Disposition to get name and filename
		_, cdParams, err := mime.ParseMediaType(contentDisposition)
		if err != nil {
			h.handleError(w, r, oplog, errInvalidContentDisposition)
			return
		}

		name := cdParams["name"]

		// name can be "{exampleID}", "{exampleID}.inputs", "{exampleID}.outputs", "{exampleID}.attachment.{attachment_name}"
		nameParts := strings.Split(name, ".")
		if len(nameParts) == 0 {
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}
		if nameParts[0] == "" {
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}

		exampleID := nameParts[0]
		_, err = uuid.Parse(exampleID)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w for %s: invalid example part identifier, must UUID", errInvalidPartName, exampleID))
			return
		}

		exData, exists := examplesData[exampleID]
		var partErr error

		switch {
		case len(nameParts) == 1:
			if !exists {
				exData = &exampleInfo{}
				exData.exampleCreateWithID = &exampleCreateWithID{
					id:            exampleID,
					exampleCreate: &exampleCreate{},
				}
				examplesData[exampleID] = exData
			} else {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part already seen", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processDatasetExampleCreatePart(part, exData, &dsInfoForInsert)
		case len(nameParts) == 2 && nameParts[1] == "inputs":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processInputsPart(part, exData, &dsInfoForInsert)
		case len(nameParts) == 2 && nameParts[1] == "outputs":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processOutputsPart(part, exData, &dsInfoForInsert)
		case len(nameParts) >= 3 && nameParts[1] == "attachment":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidAttachmentPart, part.FormName()))
				return
			}
			attachmentName := strings.Join(nameParts[2:], ".")
			partErr = h.processAttachmentPart(r, part, exData, attachmentName, authInfo, contentEncoding, &uploadResults)
		default:
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}

		if partErr != nil {
			h.handleError(w, r, oplog, partErr)
			return
		}
	}

	exampleToSourceRun := make(map[string]*string)
	for _, ex := range examplesData {
		// Build an item that includes fields required by the schema
		if ex.exampleCreateWithID.exampleCreate.UseSourceRunIO {
			if ex.exampleCreateWithID.exampleCreate.SourceRunID == nil {
				h.handleError(w, r, oplog, fmt.Errorf(
					"%w for example %s: UseSourceRunIO was set but SourceRunID is nil",
					errInvalidExampleCreate,
					ex.exampleCreateWithID.id,
				))
				return
			}
			exampleToSourceRun[ex.exampleCreateWithID.id] = ex.exampleCreateWithID.exampleCreate.SourceRunID
		}
	}

	if len(exampleToSourceRun) > 0 {
		runIDs := make([]string, 0, len(exampleToSourceRun))
		for _, sourceID := range exampleToSourceRun {
			runIDs = append(runIDs, *sourceID)
		}

		deserializeInputs := dsInfoForInsert.dataType == "chat" || dsInfoForInsert.dataType == "llm" || dsInfoForInsert.inputsSchemaDefinition != nil
		deserializeOutputs := dsInfoForInsert.dataType == "chat" || dsInfoForInsert.dataType == "llm" || dsInfoForInsert.outputsSchemaDefinition != nil
		runIOMap, err := fetchRunIOs(h.ClickHousePool, ctx, runIDs, authInfo.TenantID, deserializeInputs, deserializeOutputs)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("failed to fetch source run I/O: %w", err))
			return
		}

		// Overwrite Inputs/Outputs if UseSourceRunIO is true
		type exRunPair struct {
			ex  *exampleInfo
			run RunData
		}
		var pairs []exRunPair

		for _, ex := range examplesData {
			sr := ex.exampleCreateWithID.exampleCreate.SourceRunID
			if sr != nil && exampleToSourceRun[ex.exampleCreateWithID.id] != nil {
				if run, ok := runIOMap[*sr]; ok {
					pairs = append(pairs, exRunPair{ex: ex, run: run})
				}
			}
		}

		// Use an errgroup with concurrency limit:
		g, ctx := errgroup.WithContext(ctx)
		g.SetLimit(config.Env.BlobStorageFetchSemaphore)

		// Launch each populateExampleFromRun in its own goroutine
		for _, p := range pairs {
			pair := p // capture
			g.Go(func() error {
				// This runs in parallel, but limited by g.SetLimit
				return h.populateExampleFromRun(
					pair.ex,
					pair.run,
					w,
					r.WithContext(ctx), // use the errgroup ctx
					oplog,
					dsInfoForInsert,
					authInfo.TenantID,
					nil,
					deserializeInputs,
					deserializeOutputs,
				)
			})
		}

		// If anything fails, g.Wait() returns the error
		if err := g.Wait(); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("failed populating examples from runs: %w", err))
			return
		}
	}

	if dsInfoForInsert.hasTransformations {
		// Gather examples into a slice
		var validationPayload []exampleValidateRequest
		for _, ex := range examplesData {
			item := exampleValidateRequest{
				ID:        ex.exampleCreateWithID.id,
				DatasetID: ex.exampleCreateWithID.exampleCreate.DatasetID,
				Metadata:  ex.exampleCreateWithID.exampleCreate.Metadata,
				Split:     ex.exampleCreateWithID.exampleCreate.Split,
				CreatedAt: ex.exampleCreateWithID.exampleCreate.CreatedAt,
			}
			if len(ex.inputs) > 0 {
				raw, err := json.Marshal(ex.inputs)
				if err != nil {
					h.handleError(w, r, oplog, fmt.Errorf("error marshaling data for validation: %w", err))
					return
				}
				item.Inputs = json.RawMessage(raw)
			} else if ex.inputsBytes != nil {
				item.Inputs = ex.inputsBytes
			}

			if len(ex.outputs) > 0 {
				raw, err := json.Marshal(ex.outputs)
				if err != nil {
					h.handleError(w, r, oplog, fmt.Errorf("error marshaling data for validation: %w", err))
					return
				}
				item.Outputs = json.RawMessage(raw)
			} else if ex.outputsBytes != nil {
				item.Outputs = ex.outputsBytes
			}
			validationPayload = append(validationPayload, item)
		}
		// Marshal to JSON
		payload, err := json.Marshal(validationPayload)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("error marshaling data for validation: %w", err))
			return
		}

		// Construct the validation request
		validateURL := config.Env.SmithBackendEndpoint + "/examples/validate/bulk"
		req, err := http.NewRequestWithContext(ctx, http.MethodPost, validateURL, bytes.NewBuffer(payload))
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("building validation request: %w", err))
			return
		}
		req.Header = r.Header.Clone()
		req.Header.Set("Content-Type", "application/json")

		// Execute
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("sending validation request: %w", err))
			return
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			// For debugging, you can read the body
			respBody, _ := io.ReadAll(resp.Body)
			h.handleError(w, r, oplog, fmt.Errorf(
				"%w: validation endpoint returned %d: %s", errInvalidExampleSchema, resp.StatusCode, string(respBody),
			))
			return
		}
		var validatedExamples []exampleValidateRequest
		if err := json.NewDecoder(resp.Body).Decode(&validatedExamples); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("decoding validation response: %w", err))
			return
		}
		for _, ve := range validatedExamples {
			if ex, ok := examplesData[ve.ID]; ok {
				ex.inputsBytes = ve.Inputs
				ex.outputsBytes = ve.Outputs
				ex.inputs = nil
				ex.outputs = nil
				ex.exampleCreateWithID.exampleCreate.Metadata = ve.Metadata
			}
		}
	}

	// bulk insert examples
	insertedIds, err := h.ExamplesCRUD.bulkCreateExamples(ctx, examplesData, authInfo.TenantID)
	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	// wait for all uploads to complete
	for _, uploadResult := range uploadResults {
		if uploadResult == nil {
			continue
		}
		if err := uploadResult.Wait(); err != nil {
			h.handleError(w, r, oplog, err)
			return
		}
	}

	// prepare response
	response := ExamplesCreatedResponse{
		Count:      len(insertedIds),
		ExampleIDs: insertedIds,
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, response)
}

// UploadExamples is DEPRECATED, use UploadDatasetExamples instead
func (h *MultipartExamplesHandler) UploadExamples(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	ctx = context.WithValue(ctx, config.OperationCtxKey, "create_examples")

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		h.handleError(w, r, oplog, errUnauthorized)
		return
	}

	// limit size of request body
	r.Body = http.MaxBytesReader(w, r.Body, MaxBytes)

	// use reader to process body as a stream
	reader, err := r.MultipartReader()
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %s", errReadingMultipartData, err))
		return
	}

	examplesData := make(map[string]*exampleInfo)
	var dsInfo *datasetInfo

	uploadResults := make([]*storage.UploadAsyncResult, 0)

	for {
		part, err := reader.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %s", errReadingMultipartData, err))
			return
		}

		contentDisposition := part.Header.Get("Content-Disposition")
		if contentDisposition == "" {
			h.handleError(w, r, oplog, errMissingContentDisposition)
			return
		}

		contentEncoding := part.Header.Get("Content-Encoding")
		if contentEncoding != "" {
			// TODO(agola11): support gzip
			h.handleError(w, r, oplog, errUnsupportedContentEncoding)
			return
		}

		// parse Content-Disposition to get name and filename
		_, cdParams, err := mime.ParseMediaType(contentDisposition)
		if err != nil {
			h.handleError(w, r, oplog, errInvalidContentDisposition)
			return
		}

		name := cdParams["name"]

		// name can be "{exampleID}", "{exampleID}.inputs", "{exampleID}.outputs", "{exampleID}.attachment.{attachment_name}"
		nameParts := strings.Split(name, ".")
		if len(nameParts) == 0 {
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}
		if nameParts[0] == "" {
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}

		exampleID := nameParts[0]
		_, err = uuid.Parse(exampleID)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w for %s: invalid example part identifier, must UUID", errInvalidPartName, exampleID))
			return
		}

		exData, exists := examplesData[exampleID]
		var partErr error

		switch {
		case len(nameParts) == 1:
			if !exists {
				exData = &exampleInfo{}
				exData.exampleCreateWithID = &exampleCreateWithID{
					id:            exampleID,
					exampleCreate: &exampleCreate{},
				}
				examplesData[exampleID] = exData
			} else {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part already seen", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processExampleCreatePart(part, exData, &dsInfo, authInfo)
		case len(nameParts) == 2 && nameParts[1] == "inputs":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processInputsPart(part, exData, &dsInfo)
		case len(nameParts) == 2 && nameParts[1] == "outputs":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processOutputsPart(part, exData, &dsInfo)
		case len(nameParts) == 3 && nameParts[1] == "attachment":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidAttachmentPart, part.FormName()))
				return
			}
			attachmentName := strings.Join(nameParts[2:], ".")
			partErr = h.processAttachmentPart(r, part, exData, attachmentName, authInfo, contentEncoding, &uploadResults)
		default:
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}

		if partErr != nil {
			h.handleError(w, r, oplog, partErr)
			return
		}
	}

	// bulk insert examples
	insertedIds, err := h.ExamplesCRUD.bulkCreateExamples(ctx, examplesData, authInfo.TenantID)
	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	// wait for all uploads to complete
	for _, uploadResult := range uploadResults {
		if uploadResult == nil {
			continue
		}
		if err := uploadResult.Wait(); err != nil {
			h.handleError(w, r, oplog, err)
			return
		}
	}

	// prepare response
	response := ExamplesCreatedResponse{
		Count:      len(insertedIds),
		ExampleIDs: insertedIds,
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, response)
}

// UpdateDatasetExamples godoc
// @Summary      Update Examples
// @Description  This endpoint allows clients to update existing examples in a specified dataset by sending a multipart/form-data PATCH request.
// @Description  Each form part contains either JSON-encoded data or binary attachment files to update an example.
//
// @Tags         examples
// @Accept       multipart/form-data
// @Produce      json
// @Param        dataset_id                           path      string                  true   "Dataset ID"    format(uuid)
// @Param        {example_id}                         formData  string                  true   "The Example update info as JSON. Can have fields 'metadata', 'split'"  format(binary)  example:{"metadata":{"source":"updated-source","tags":["updated","example"]},"split":["test"]}
// @Param        {example_id}.inputs                  formData  string                  false  "The updated Example inputs as JSON" format(binary)  example:{"input":"What is the capital of France?"}
// @Param        {example_id}.outputs                 formData  string                  false  "The updated Example outputs as JSON" format(binary)  example:{"output":"Paris is the capital of France."}
// @Param        {example_id}.attachments_operations  formData  string                  false  "JSON describing attachment operations (retain, rename)" format(binary)  example:{"retain":["attachment.image1.jpg"],"rename":{"attachment.old_name.pdf":"attachment.new_name.pdf"}}
// @Param        {example_id}.attachment.{name}       formData  string                  false  "New file attachment named {name}" format(binary)
// @Success      201  {object}  ExamplesUpdatedResponse
// @Failure      400  {object}  ErrorResponse
// @Failure      403  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      409  {object}  ErrorResponse
// @Failure      422  {object}  ErrorResponse
// @Router       /v1/platform/datasets/{dataset_id}/examples [patch]
func (h *MultipartExamplesHandler) UpdateDatasetExamples(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	ctx = context.WithValue(ctx, config.OperationCtxKey, "update_examples")

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		h.handleError(w, r, oplog, errUnauthorized)
		return
	}

	// get datasetId as a path parameter
	datasetId := chi.URLParam(r, "dataset_id")
	if datasetId == "" {
		h.handleError(w, r, oplog, errMissingDatasetID)
		return
	}

	// check to see if datasetId is a valid UUID
	if err := uuid.Validate(datasetId); err != nil {
		h.handleError(w, r, oplog, errInvalidDatasetID)
		return
	}

	dsInfoForUpdate, err := h.ExamplesCRUD.readDatasetInfoForUpsert(
		ctx,
		datasetId,
		authInfo.TenantID,
	)

	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	// limit size of request body
	r.Body = http.MaxBytesReader(w, r.Body, MaxBytes)

	// use reader to process body as a stream
	reader, err := r.MultipartReader()
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %s", errReadingMultipartData, err))
		return
	}

	updateExamplesData := make(map[string]*updateExampleInfo)
	uploadResults := make([]*storage.UploadAsyncResult, 0)

	for {
		part, err := reader.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %s", errReadingMultipartData, err))
			return
		}

		contentDisposition := part.Header.Get("Content-Disposition")
		if contentDisposition == "" {
			h.handleError(w, r, oplog, errMissingContentDisposition)
			return
		}

		contentEncoding := part.Header.Get("Content-Encoding")
		if contentEncoding != "" {
			// TODO: support gzip
			h.handleError(w, r, oplog, errUnsupportedContentEncoding)
			return
		}

		// parse Content-Disposition to get name and filename
		_, cdParams, err := mime.ParseMediaType(contentDisposition)
		if err != nil {
			h.handleError(w, r, oplog, errInvalidContentDisposition)
			return
		}

		name := cdParams["name"]

		// name can be "{exampleID}", "{exampleID}.inputs", "{exampleID}.outputs", "{exampleID}.attachments_operations", "{exampleID}.attachment.{attachment_name}"
		nameParts := strings.Split(name, ".")
		if len(nameParts) == 0 {
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}
		if nameParts[0] == "" {
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}

		exampleID := nameParts[0]
		if _, err = uuid.Parse(exampleID); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w for %s: invalid example part identifier, must be UUID", errInvalidPartName, exampleID))
			return
		}

		updateData, exists := updateExamplesData[exampleID]
		var partErr error

		switch {
		case len(nameParts) == 1:
			if !exists {
				updateData = &updateExampleInfo{}
				updateData.exampleUpdateWithID = &exampleUpdateWithID{
					id:            exampleID,
					exampleUpdate: &exampleUpdate{},
				}
				updateData.attachmentsOperations = &attachmentsOperations{
					Retain: nil,
					Rename: nil,
				}
				updateData.attachmentUrls = nil
				updateExamplesData[exampleID] = updateData
			} else {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part already seen", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processDatasetExampleUpdatePart(part, updateData, &dsInfoForUpdate)
		case len(nameParts) == 2 && (nameParts[1] == "inputs" || nameParts[1] == "outputs"):
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processIOPart(part, updateData, nameParts[1], &dsInfoForUpdate)
		case len(nameParts) == 2 && nameParts[1] == "attachments_operations":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidJsonPart, part.FormName()))
				return
			}
			partErr = h.processAttachmentsOperationsPart(part, updateData)
		case len(nameParts) >= 3 && nameParts[1] == "attachment":
			if !exists {
				h.handleError(w, r, oplog, fmt.Errorf("%w for %s: example part not seen yet", errInvalidAttachmentPart, part.FormName()))
				return
			}
			attachmentName := strings.Join(nameParts[2:], ".")
			partErr = h.processAttachmentPart(r, part, updateData, attachmentName, authInfo, contentEncoding, &uploadResults)
		default:
			h.handleError(w, r, oplog, errInvalidPartName)
			return
		}

		if partErr != nil {
			h.handleError(w, r, oplog, partErr)
			return
		}
	}

	// apply transformations if present
	if dsInfoForUpdate.hasTransformations {
		// Gather examples into a slice
		var validationPayload []exampleValidateRequest
		for _, ex := range updateExamplesData {
			item := exampleValidateRequest{
				ID:        ex.exampleUpdateWithID.id,
				DatasetID: ex.exampleUpdateWithID.exampleUpdate.DatasetID,
				Metadata:  ex.exampleUpdateWithID.exampleUpdate.Metadata,
				Split:     ex.exampleUpdateWithID.exampleUpdate.Split,
			}
			if len(ex.inputs) > 0 {
				raw, err := json.Marshal(ex.inputs)
				if err != nil {
					h.handleError(w, r, oplog, fmt.Errorf("error marshaling data for validation: %w", err))
					return
				}
				item.Inputs = json.RawMessage(raw)
			} else if ex.inputsBytes != nil {
				item.Inputs = ex.inputsBytes
			}

			if len(ex.outputs) > 0 {
				raw, err := json.Marshal(ex.outputs)
				if err != nil {
					h.handleError(w, r, oplog, fmt.Errorf("error marshaling data for validation: %w", err))
					return
				}
				item.Outputs = json.RawMessage(raw)
			} else if ex.outputsBytes != nil {
				item.Outputs = ex.outputsBytes
			}
			if item.Outputs != nil || item.Inputs != nil {
				validationPayload = append(validationPayload, item)
			}
		}
		// Marshal to JSON
		if validationPayload != nil {
			payload, err := json.Marshal(validationPayload)
			if err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("error marshaling data for validation: %w", err))
				return
			}

			// Construct the validation request
			validateURL := config.Env.SmithBackendEndpoint + "/examples/validate/bulk"
			req, err := http.NewRequestWithContext(ctx, http.MethodPost, validateURL, bytes.NewBuffer(payload))
			if err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("building validation request: %w", err))
				return
			}
			req.Header = r.Header.Clone()
			req.Header.Set("Content-Type", "application/json")

			// Execute
			client := &http.Client{}
			resp, err := client.Do(req)
			if err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("sending validation request: %w", err))
				return
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				// For debugging, you can read the body
				respBody, _ := io.ReadAll(resp.Body)
				h.handleError(w, r, oplog, fmt.Errorf(
					"%w: validation endpoint returned %d: %s", errInvalidExampleSchema, resp.StatusCode, string(respBody),
				))
				return
			}
			var validatedExamples []exampleValidateRequest
			if err := json.NewDecoder(resp.Body).Decode(&validatedExamples); err != nil {
				h.handleError(w, r, oplog, fmt.Errorf("decoding validation response: %w", err))
				return
			}
			for _, ve := range validatedExamples {
				if ex, ok := updateExamplesData[ve.ID]; ok {
					ex.inputsBytes = ve.Inputs
					ex.outputsBytes = ve.Outputs
					ex.inputs = nil
					ex.outputs = nil
					ex.exampleUpdateWithID.exampleUpdate.Metadata = ve.Metadata
				}
			}
		}
	}

	// bulk update examples
	insertedIds, err := h.ExamplesCRUD.bulkUpdateExamples(ctx, updateExamplesData, authInfo.TenantID)
	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	// wait for all uploads to complete
	for _, uploadResult := range uploadResults {
		if uploadResult == nil {
			continue
		}
		if err := uploadResult.Wait(); err != nil {
			h.handleError(w, r, oplog, err)
			return
		}
	}

	// prepare response
	response := ExamplesUpdatedResponse{
		Count:      len(insertedIds),
		ExampleIDs: insertedIds,
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, response)
}

func (h *MultipartExamplesHandler) verifyJsonPart(part *multipart.Part) ([]byte, error) {
	if part.Header.Get("Content-Type") == "" {
		return nil, fmt.Errorf("%w for %s", errMissingContentType, part.FormName())
	}

	if part.Header.Get("Content-Type") != "application/json" {
		return nil, fmt.Errorf("%w for %s", errInvalidContentType, part.FormName())
	}

	jsonData, err := io.ReadAll(part)
	if err != nil {
		return nil, fmt.Errorf("%w for %s", errReadingMultipartData, part.FormName())
	}
	return jsonData, nil
}

func (h *MultipartExamplesHandler) processDatasetExampleCreatePart(
	part *multipart.Part,
	exData *exampleInfo,
	dsInfo **datasetInfo,
) error {
	jsonData, err := h.verifyJsonPart(part)
	if err != nil {
		return err
	}

	if dsInfo == nil || *dsInfo == nil {
		return fmt.Errorf("%w for %s: dataset information not found", errInvalidJsonPart, part.FormName())
	}
	localDSInfo := *dsInfo // Unwrap into a local var so the linter knows it's non-nil

	decoder := json.NewDecoder(bytes.NewReader(jsonData))
	decoder.DisallowUnknownFields()
	var datasetExampleCreate datasetExampleCreate
	if err := decoder.Decode(&datasetExampleCreate); err != nil {
		return fmt.Errorf("%w for %s: %s", errInvalidJsonPart, part.FormName(), err)
	}
	// For backwards compatibility with legacy /examples/bulk endpoint.
	if datasetExampleCreate.Split == nil || len(*datasetExampleCreate.Split) == 0 {
		s := Split{"base"}
		datasetExampleCreate.Split = &s
	}

	if datasetExampleCreate.Metadata == nil {
		datasetExampleCreate.Metadata = make(map[string]interface{})
	}

	datasetExampleCreate.Metadata["dataset_split"] = *datasetExampleCreate.Split

	exData.exampleCreateWithID.exampleCreate = &exampleCreate{
		DatasetID:      localDSInfo.id,
		Metadata:       datasetExampleCreate.Metadata,
		Split:          datasetExampleCreate.Split,
		CreatedAt:      datasetExampleCreate.CreatedAt,
		ModifiedAt:     datasetExampleCreate.ModifiedAt,
		SourceRunID:    datasetExampleCreate.SourceRunID,
		UseSourceRunIO: datasetExampleCreate.UseSourceRunIO,
	}

	return nil
}

func (h *MultipartExamplesHandler) processDatasetExampleUpdatePart(part *multipart.Part, exData *updateExampleInfo, dsInfo **datasetInfo) error {
	jsonData, err := h.verifyJsonPart(part)
	if err != nil {
		return err
	}

	if dsInfo == nil || *dsInfo == nil {
		return fmt.Errorf("%w for %s: dataset information not found", errInvalidJsonPart, part.FormName())
	}

	decoder := json.NewDecoder(bytes.NewReader(jsonData))
	decoder.DisallowUnknownFields()
	var datasetExampleUpdate datasetExampleUpdate
	if err := decoder.Decode(&datasetExampleUpdate); err != nil {
		return fmt.Errorf("%w for %s: %s", errInvalidJsonPart, part.FormName(), err)
	}
	exData.exampleUpdateWithID.exampleUpdate = &exampleUpdate{
		DatasetID:   (*dsInfo).id,
		Metadata:    datasetExampleUpdate.Metadata,
		Split:       datasetExampleUpdate.Split,
		SourceRunID: datasetExampleUpdate.SourceRunID,
	}

	return nil
}

func (h *MultipartExamplesHandler) processAttachmentsOperationsPart(part *multipart.Part, exData *updateExampleInfo) error {
	if config.Env.BlobStorageEnabled == false {
		return errBlobStorageNotEnabled
	}
	jsonData, err := h.verifyJsonPart(part)
	if err != nil {
		return err
	}

	decoder := json.NewDecoder(bytes.NewReader(jsonData))
	decoder.DisallowUnknownFields()
	operations := attachmentsOperations{
		Retain: make([]string, 0),
		Rename: make(map[string]string),
	}
	if err := decoder.Decode(&operations); err != nil {
		return fmt.Errorf("%w for %s: %s", errInvalidJsonPart, part.FormName(), err)
	}
	if len(operations.Retain) > 0 || len(operations.Rename) > 0 {
		retainKeys := make(map[string]struct{}, len(operations.Retain))
		for _, key := range operations.Retain {
			retainKeys[key] = struct{}{}
		}
		for old, new := range operations.Rename {
			if _, exists := retainKeys[old]; exists {
				return fmt.Errorf("%w for %s: %s appears in both retain and rename", errInvalidAttachmentsOperations, part.FormName(), old)
			}
			if _, exists := retainKeys[new]; exists {
				return fmt.Errorf("%w for %s: %s appears in both retain and rename", errInvalidAttachmentsOperations, part.FormName(), new)
			}
		}
	}
	exData.attachmentsOperations = &operations

	return nil
}

// This is only used for the examples/multipart endpoint which is deprecated
func (h *MultipartExamplesHandler) processExampleCreatePart(part *multipart.Part, exData *exampleInfo, dsInfo **datasetInfo, authInfo *auth.AuthInfo) error {
	jsonData, err := h.verifyJsonPart(part)
	if err != nil {
		return err
	}

	decoder := json.NewDecoder(bytes.NewReader(jsonData))
	decoder.DisallowUnknownFields()
	var exampleCreate exampleCreate
	if err := decoder.Decode(&exampleCreate); err != nil {
		return fmt.Errorf("%w for %s: %s", errInvalidJsonPart, part.FormName(), err)
	}

	// validate dataset_id
	if exampleCreate.DatasetID == "" {
		return fmt.Errorf("%w for %s", errMissingDatasetID, part.FormName())
	}

	exData.exampleCreateWithID.exampleCreate = &exampleCreate

	// validate example with existing dsInfo or AuthInfo
	if dsInfo == nil {
		// should never happen since we initialize dsInfo in the main function
		return fmt.Errorf("%w for %s: dataset information not found", errInvalidJsonPart, part.FormName())
	}
	if *dsInfo != nil {
		if exampleCreate.DatasetID != (*dsInfo).id {
			return fmt.Errorf("%w for %s: inconsistent dataset_ids in parts", errInvalidJsonPart, part.FormName())
		}
	} else {
		// issue a postgres query to get the dataset info (only done once per multipart request)
		if authInfo == nil {
			return errUnauthorized
		}

		createdDatasetInfo, err := h.ExamplesCRUD.readDatasetInfoForUpsert(
			context.Background(),
			exampleCreate.DatasetID,
			authInfo.TenantID,
		)
		if err != nil {
			return fmt.Errorf("error for %s - %w", part.FormName(), err)
		}
		if createdDatasetInfo == nil {
			return fmt.Errorf("error for %s: readDatasetInfoForUpsert returned nil", part.FormName())
		}
		if createdDatasetInfo.hasTransformations {
			return fmt.Errorf("%w: %s", errInvalidDataset, "dataset contains transformations")
		}
		*dsInfo = createdDatasetInfo
	}

	return nil
}

// process inputs and outputs
func (h *MultipartExamplesHandler) processIOPart(part *multipart.Part, exData ExampleInfoCommon, partType string, dsInfo **datasetInfo) error {
	jsonData, err := h.verifyJsonPart(part)
	if err != nil {
		return err
	}

	if dsInfo == nil || *dsInfo == nil {
		return fmt.Errorf("%w for %s: dataset information not found", errInvalidJsonPart, part.FormName())
	}

	switch partType {
	case "inputs":
		if exData.GetInputs() != nil || exData.GetInputsBytes() != nil {
			return fmt.Errorf("%w for %s: inputs already seen", errInvalidJsonPart, part.FormName())
		}
		if (*dsInfo).inputsSchemaDefinition == nil || (*dsInfo).hasTransformations {
			exData.SetInputsBytes(jsonData)
			return nil
		}
		jsonObj, err := h.ExamplesCRUD.validateAgainstSchema(jsonData, (*dsInfo).inputsSchemaDefinition)
		if err != nil {
			return fmt.Errorf("%w for %s: %s", errInvalidJsonPart, part.FormName(), err)
		}
		exData.SetInputs(jsonObj)
		exData.SetInputsBytes(jsonData)
	case "outputs":
		if exData.GetOutputs() != nil || exData.GetOutputsBytes() != nil {
			return fmt.Errorf("%w for %s: outputs already seen", errInvalidJsonPart, part.FormName())
		}
		if (*dsInfo).outputsSchemaDefinition == nil || (*dsInfo).hasTransformations {
			exData.SetOutputsBytes(jsonData)
			return nil
		}
		jsonObj, err := h.ExamplesCRUD.validateAgainstSchema(jsonData, (*dsInfo).outputsSchemaDefinition)
		if err != nil {
			return fmt.Errorf("%w for %s: %s", errInvalidJsonPart, part.FormName(), err)
		}
		exData.SetOutputs(jsonObj)
		exData.SetOutputsBytes(jsonData)
	default:
		return fmt.Errorf("%w for %s: unknown part type", errInvalidJsonPart, part.FormName())
	}

	return nil
}

// process inputs part
func (h *MultipartExamplesHandler) processInputsPart(part *multipart.Part, exData ExampleInfoCommon, dsInfo **datasetInfo) error {
	return h.processIOPart(part, exData, "inputs", dsInfo)
}

// process outputs part
func (h *MultipartExamplesHandler) processOutputsPart(part *multipart.Part, exData ExampleInfoCommon, dsInfo **datasetInfo) error {
	return h.processIOPart(part, exData, "outputs", dsInfo)
}

// process attachment part
func (h *MultipartExamplesHandler) processAttachmentPart(
	r *http.Request,
	part *multipart.Part,
	exData ExampleInfoCommon,
	attachmentName string,
	authInfo *auth.AuthInfo,
	contentEncoding string,
	uploadResults *[]*storage.UploadAsyncResult,
) error {
	datasetId := exData.GetDatasetID()
	id := exData.GetID()
	attachmentUrls := exData.GetAttachmentUrls()

	if config.Env.BlobStorageEnabled == false {
		return errBlobStorageNotEnabled
	}

	// verify attachmentName and check if it already exists
	if attachmentName == "" {
		return fmt.Errorf("%w: attachment name is empty", errInvalidAttachmentPart)
	}
	if _, exists := attachmentUrls[attachmentName]; exists {
		return fmt.Errorf("%w: attachment %s already exists", errInvalidAttachmentPart, attachmentName)
	}

	// check the content type
	ctypeHeader := part.Header.Get("Content-Type")
	if ctypeHeader == "" {
		return fmt.Errorf("%w for attachment %s", errMissingContentType, attachmentName)
	}
	// the header should be of the form "application/octet-stream; length=3"
	ctype, params, err := mime.ParseMediaType(ctypeHeader)
	if err != nil {
		return fmt.Errorf("%w for attachment %s", errInvalidContentType, attachmentName)
	}
	// check for the length parameter
	length, ok := params["length"]
	if !ok {
		return fmt.Errorf("%w for attachment %s", errMissingLengthParam, attachmentName)
	}
	// convert length to int64, check if it's valid
	lengthInt, err := strconv.ParseInt(length, 10, 64)
	if err != nil {
		return fmt.Errorf("%w for attachment %s", errInvalidLengthParam, attachmentName)
	}

	// check the content length
	if lengthInt > MaxFileSize {
		return fmt.Errorf("%w for attachment %s: file size exceeds limit", errInvalidLengthParam, attachmentName)
	}

	// generate S3 key
	s3Key, err := generateS3Key(authInfo.TenantID, datasetId, id, Attachments)
	if err != nil {
		return fmt.Errorf("%w: %s", errGenerateS3KeyFailure, err)
	}

	putObj := &storage.UploadObjectInput{
		Bucket:          config.Env.S3BucketName,
		Key:             s3Key,
		Reader:          part,
		ContentType:     ctype,
		ContentLength:   lengthInt,
		ContentEncoding: contentEncoding,
	}

	// upload the attachment to blob storage
	uploadResult, err := h.StorageClient.UploadObjectAsync(r.Context(), putObj)
	*uploadResults = append(*uploadResults, uploadResult)

	if err != nil {
		return err
	}

	// store the attachment URL
	attachmentKey := fmt.Sprintf("attachment.%s", attachmentName)
	if attachmentUrls == nil {
		attachmentUrls = make(map[string]AttachmentInfo)
		// Need to set if nil, because there is no memory allocation
		exData.SetAttachmentUrls(attachmentUrls)
	}
	// no need to use SetAttachmentUrls because it's a pointer
	attachmentUrls[attachmentKey] = AttachmentInfo{
		StorageURL: s3Key,
		MimeType:   ctype,
	}

	return nil
}

type IoType string

// TODO(agola11): support other types of IO
const (
	Attachments IoType = "attachments"
)

// generates an S3 key with hashed tenant and dataset IDs for obfuscation.
func generateS3Key(tenantID string, datasetID string, exampleID string, ioType IoType) (string, error) {
	tenantIDBytes := []byte(tenantID)
	datasetIDBytes := []byte(datasetID)

	// generate SHA-256 hash for tenantID
	hTenantID := sha256.Sum256(tenantIDBytes)
	hTenantIDHex := hex.EncodeToString(hTenantID[:])

	// generate SHA-256 hash for datasetID
	hDatasetID := sha256.Sum256(datasetIDBytes)
	hDatasetIDHex := hex.EncodeToString(hDatasetID[:])

	// generate a new UUID for objID
	objID := uuid.NewString()

	// construct the S3 key
	s3Key := fmt.Sprintf("%s/%s/%s/%s/%s", ioType, hTenantIDHex, hDatasetIDHex, exampleID, objID)

	return s3Key, nil
}

func fetchRunIOs(
	clickHousePool driver.Conn,
	ctx context.Context,
	runIDs []string,
	tenantID string,
	deserializeInputs bool,
	deserializeOutputs bool,
) (map[string]RunData, error) {

	// Early return if no run IDs are provided
	if len(runIDs) == 0 {
		return map[string]RunData{}, nil
	}

	runsSQL := `
		WITH filtered_runs_cte AS (
			SELECT
				runs.tenant_id     AS tenant_id,
				runs.session_id    AS session_id,
				runs.is_root       AS is_root,
				runs.start_time    AS start_time,
				runs.id            AS id
			FROM runs
			WHERE
				(
					(runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN 
					(
						SELECT tenant_id, is_root, session_id, start_time, id
						FROM runs_run_id_v2
						WHERE 
							id IN $2
							AND (tenant_id = $1)
					)
				)
				AND (
					(
						runs.trace_first_received_at IS NOT NULL
						AND runs.ttl_seconds IS NOT NULL
						AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)
					) = 0
				)
				AND (runs.tenant_id = $1)
			ORDER BY
				runs.start_time DESC,
				toString(runs.id) DESC
		)
		SELECT
			runs.id               AS id,
			runs.inputs           AS inputs,
			runs.outputs          AS outputs,
			runs.extra            AS extra,
			runs.s3_urls          AS s3_urls,
			runs.inputs_s3_urls   AS inputs_s3_urls,
			runs.outputs_s3_urls  AS outputs_s3_urls
		FROM runs
		WHERE
			(runs.tenant_id, runs.session_id, runs.is_root, runs.start_time, runs.id) IN 
			(
				SELECT
					tenant_id,
					session_id,
					is_root,
					start_time,
					id
				FROM filtered_runs_cte
			)
		ORDER BY
			runs.start_time DESC,
			toString(runs.id) DESC,
			coalesce(runs.modified_at, runs.end_time, runs.start_time) DESC
		LIMIT 1 BY runs.id
	`

	// Perform the query
	rows, err := (clickHousePool).Query(ctx, runsSQL, tenantID, runIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to query runs table for I/O: %w", err)
	}
	defer rows.Close()

	runIOMap := make(map[string]RunData)

	for rows.Next() {
		var (
			idStr            string
			rawInputs        string
			rawOutputs       string
			rawExtras        string
			rawS3Urls        string
			rawInputsS3Urls  string
			rawOutputsS3Urls string
		)
		if err := rows.Scan(&idStr, &rawInputs, &rawOutputs, &rawExtras, &rawS3Urls, &rawInputsS3Urls, &rawOutputsS3Urls); err != nil {
			return nil, fmt.Errorf("failed to scan run row: %w", err)
		}

		var extras, s3Urls, inputsS3Urls, outputsS3Urls, inputs, outputs map[string]interface{}

		inputsBytes := []byte(rawInputs)
		outputsBytes := []byte(rawOutputs)
		if deserializeInputs && rawInputs != "" {
			if err := json.Unmarshal(inputsBytes, &inputs); err != nil {
				return nil, fmt.Errorf("failed to unmarshal run inputs: %w", err)
			}
		}
		if deserializeOutputs && rawOutputs != "" {
			if err := json.Unmarshal(outputsBytes, &outputs); err != nil {
				return nil, fmt.Errorf("failed to unmarshal run outputs: %w", err)
			}
		}
		if rawExtras != "" {
			if err := json.Unmarshal([]byte(rawExtras), &extras); err != nil {
				return nil, fmt.Errorf("failed to unmarshal run extras: %w", err)
			}
		}
		if rawS3Urls != "" {
			if err := json.Unmarshal([]byte(rawS3Urls), &s3Urls); err != nil {
				return nil, fmt.Errorf("failed to unmarshal s3_urls: %w", err)
			}
		}
		if rawInputsS3Urls != "" {
			if err := json.Unmarshal([]byte(rawInputsS3Urls), &inputsS3Urls); err != nil {
				return nil, fmt.Errorf("failed to unmarshal inputs_s3_urls: %w", err)
			}
		}
		if rawOutputsS3Urls != "" {
			if err := json.Unmarshal([]byte(rawOutputsS3Urls), &outputsS3Urls); err != nil {
				return nil, fmt.Errorf("failed to unmarshal outputs_s3_urls: %w", err)
			}
		}

		run := RunData{
			ID:            idStr,
			Inputs:        inputs,
			Outputs:       outputs,
			Extras:        extras,
			S3Urls:        s3Urls,
			InputsS3Urls:  inputsS3Urls,
			OutputsS3Urls: outputsS3Urls,
		}
		if inputs == nil && len(inputsBytes) > 0 {
			run.InputsBytes = inputsBytes
		}
		if outputs == nil && len(outputsBytes) > 0 {
			run.OutputsBytes = outputsBytes
		}
		runIOMap[idStr] = run

	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("iteration error scanning runs: %w", err)
	}

	return runIOMap, nil
}

func (h *MultipartExamplesHandler) populateExampleFromRun(
	exData *exampleInfo,
	run RunData,
	w http.ResponseWriter,
	r *http.Request,
	oplog *slog.Logger,
	dsInfo *datasetInfo,
	tenantID string,
	correctionsDict map[string]map[string]interface{},
	deserializeInputs bool,
	deserializeOutputs bool,
) error {
	if run.Extras != nil {

		// If this example has no user-defined metadata yet, try copying from run.Extras["metadata"]
		_, hasUserMetadata := exData.exampleCreateWithID.exampleCreate.Metadata["metadata"]
		if !hasUserMetadata {
			if metaVal, metaOk := run.Extras["metadata"].(map[string]interface{}); metaOk {
				exData.exampleCreateWithID.exampleCreate.Metadata["metadata"] = metaVal
			} else {
				exData.exampleCreateWithID.exampleCreate.Metadata["metadata"] = map[string]interface{}{}
			}
		}
	}

	if run.S3Urls != nil {
		for k, v := range run.S3Urls {
			// We only consider attachments that start with "attachment."
			if !strings.HasPrefix(k, "attachment.") {
				continue
			}
			vMap, vIsMap := v.(map[string]interface{})
			if !vIsMap || vMap == nil {
				continue
			}
			runS3KeyRaw, hasRunS3Key := vMap["s3_url"]
			if !hasRunS3Key || runS3KeyRaw == nil {
				continue
			}
			runS3Key, isStr := runS3KeyRaw.(string)
			if !isStr || runS3Key == "" {
				continue
			}

			attachmentName := k[len("attachment."):]
			exampleIDStr := exData.exampleCreateWithID.id
			exampleS3Key := fmt.Sprintf("ten:%s/ds:%s/ex:%s/attachments/%s",
				tenantID, dsInfo.id, exampleIDStr, attachmentName)

			attachmentInfo, err := h.copyAttachment(runS3Key, exampleS3Key, r, oplog)
			if err != nil {
				return err
			}
			keyForMap := fmt.Sprintf("attachment.%s", attachmentName)
			if exData.attachmentUrls == nil {
				exData.attachmentUrls = make(map[string]AttachmentInfo)
			}
			exData.attachmentUrls[keyForMap] = *attachmentInfo
		}
	}

	if run.InputsS3Urls != nil {
		if s3URLRaw, hasRoot := run.InputsS3Urls["ROOT"]; hasRoot {
			if s3URLStr, isStr := s3URLRaw.(string); isStr && s3URLStr != "" {
				err := h.loadInputsS3Urls(s3URLStr, &run, w, r, oplog, deserializeInputs, dsInfo.inputsSchemaDefinition)
				if err != nil {
					return err
				}
			}
		}
	}
	exData.inputsBytes = run.InputsBytes

	runInputs, err := h.prepareRunInputs(dsInfo.dataType, run)
	if err != nil {
		return err
	}
	exData.inputs = runInputs

	var runOutputs map[string]interface{}
	if correctionsDict != nil && run.ID != "" {
		// If there's a corrected output for this run
		if corrected, found := correctionsDict[run.ID]; found {
			runOutputs = corrected
		}
	}
	if runOutputs == nil {
		if run.OutputsS3Urls != nil {
			if s3URLRaw, hasRoot := run.OutputsS3Urls["ROOT"]; hasRoot {
				if s3URLStr, isStr := s3URLRaw.(string); isStr && s3URLStr != "" {
					err := h.loadOutputsS3Urls(s3URLStr, &run, w, r, oplog, deserializeOutputs, dsInfo.outputsSchemaDefinition)
					if err != nil {
						return err
					}
				}
			}
		}
		if run.OutputsBytes != nil {
			exData.outputsBytes = run.OutputsBytes
		} else {
			runOutputs, err = h.prepareRunOutputs(dsInfo.dataType, run)
			if err != nil {
				return err
			}
			exData.outputs = runOutputs
		}
	}

	return nil
}

func (h *MultipartExamplesHandler) getAttachmentMetadata(ctx context.Context, s3URL string) (*AttachmentMeta, error) {
	// Attempt a HEAD request using the StorageClient
	out, err := h.StorageClient.HeadObject(ctx, &storage.HeadObjectInput{
		Bucket: config.Env.S3BucketName,
		Key:    s3URL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to HEAD object for %s: %w", s3URL, err)
	}

	// Default to application/octet-stream if content type is absent
	mimeType := "application/octet-stream"
	if out.ContentType != nil {
		mimeType = *out.ContentType
	}

	var length int64
	if out.ContentLength != nil {
		length = *out.ContentLength
	}

	// Return the metadata wrapped in the AttachmentMeta struct
	return &AttachmentMeta{
		MimeType:      mimeType,
		ContentLength: length,
	}, nil
}

type AttachmentMeta struct {
	MimeType      string
	ContentLength int64
}

//   - If dataType == "chat", we look for invocation_params inside run["extra"] or else run["inputs"];
//     we parse out tools or functions if present, store them alongside "messages" derived from those params.
//   - If dataType == "llm", we set "input" to inputs["prompt"] or the first item of inputs["prompts"].
//   - Otherwise, we just return run["inputs"] as-is.
func (h *MultipartExamplesHandler) prepareRunInputs(
	dataType string,
	run RunData,
) (map[string]interface{}, error) {
	switch dataType {
	case "chat":
		return GetChatInputs(run)
	case "llm":
		return GetLLMInputs(run)
	default:
		// For "kv" or all else, just return run.Inputs unchanged
		return run.Inputs, nil
	}
}

// Add these two helper functions somewhere in the file, e.g. near prepareRunInputs:

func GetChatInputs(run RunData) (map[string]interface{}, error) {
	// 1) "params" can come from run.Extras["invocation_params"] or else run.Inputs
	var params interface{}
	if run.Extras != nil {
		if p, ok := run.Extras["invocation_params"]; ok {
			params = p
		}
	}
	if params == nil {
		params = run.Inputs
	}

	// 2) Check if there are openai tools and/or functions
	rawTools := SafeGet(params, nil, "tools")
	var tools []interface{}
	if IsOpenAITools(rawTools) {
		tools = rawTools.([]interface{})
	}

	rawFunctions := SafeGet(params, nil, "functions")
	var functions []interface{}
	if IsOpenAIFunctions(rawFunctions) {
		functions = rawFunctions.([]interface{})
	}

	// 3) Build the user input messages array from run.Inputs["messages"]
	inputMessages := []interface{}{}
	if msgsRaw, ok := run.Inputs["messages"]; ok {
		if msgsSlice, ok2 := msgsRaw.([]interface{}); ok2 && len(msgsSlice) > 0 {
			for _, m := range msgsSlice {
				// convert each message to stored format
				storedMsg := GetMessageAsStoredMessage(m)
				inputMessages = append(inputMessages, storedMsg)
			}
		}
	}

	inputs := map[string]interface{}{
		"input": inputMessages, // could be empty if no messages
	}
	// 4) If we found any OpenAI tools or functions, attach them
	if len(tools) > 0 {
		var renderedTools []interface{}
		for _, t := range tools {
			if tMap, ok := t.(map[string]interface{}); ok {
				if fn, hasFn := tMap["function"]; hasFn {
					renderedTools = append(renderedTools, fn)
				}
			}
		}
		inputs["functions"] = renderedTools
	} else if len(functions) > 0 {
		inputs["functions"] = functions
	}

	return inputs, nil
}

func GetLLMInputs(run RunData) (map[string]interface{}, error) {
	// Matches _get_default_inputs for "llm"
	prompt, hasPrompt := run.Inputs["prompt"]
	if !hasPrompt {
		// Check prompts[0] if "prompt" is missing
		if promptsRaw, ok := run.Inputs["prompts"]; ok {
			if promptsSlice, ok2 := promptsRaw.([]interface{}); ok2 && len(promptsSlice) > 0 {
				prompt = promptsSlice[0]
			}
		}
	}
	return map[string]interface{}{
		"input": prompt,
	}, nil
}

func (h *MultipartExamplesHandler) loadInputsS3Urls(s3URLStr string, run *RunData, w http.ResponseWriter, r *http.Request, oplog *slog.Logger, deserialize bool, schema gojsonschema.JSONLoader) error {
	objOut, err := h.StorageClient.GetObject(r.Context(), &storage.GetObjectInput{
		Bucket: config.Env.S3BucketName,
		Key:    s3URLStr,
	})
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("could not fetch S3 inputs object %s: %w", s3URLStr, err))
		return err
	}
	defer objOut.Body.Close()
	bodyBytes, err := io.ReadAll(objOut.Body)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("error reading S3 object for inputs: %w", err))
		return err
	}
	if deserialize {
		var s3Inputs map[string]interface{}
		if err := json.Unmarshal(bodyBytes, &s3Inputs); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("error unmarshalling S3 inputs JSON: %w", err))
			return err
		}
		if len(s3Inputs) > 0 {
			run.Inputs = s3Inputs
		}
	} else {
		run.InputsBytes = bodyBytes
	}

	if schema != nil {
		loader := gojsonschema.NewGoLoader(run.Inputs)
		result, err := gojsonschema.Validate(schema, loader)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("error validating input JSON against schema: %w", err))
			return err
		}

		if !result.Valid() {
			var valErrors []string
			for _, desc := range result.Errors() {
				valErrors = append(valErrors, desc.String())
			}
			h.handleError(w, r, oplog, fmt.Errorf("%w: input schema validation errors: %v", errInvalidRunIO, valErrors))
			return errInvalidRunIO
		}
	}
	return nil
}

func (h *MultipartExamplesHandler) loadOutputsS3Urls(s3URLStr string, run *RunData, w http.ResponseWriter, r *http.Request, oplog *slog.Logger, deserialize bool, schema gojsonschema.JSONLoader) error {
	objOut, err := h.StorageClient.GetObject(r.Context(), &storage.GetObjectInput{
		Bucket: config.Env.S3BucketName,
		Key:    s3URLStr,
	})
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("could not fetch S3 outputs object %s: %w", s3URLStr, err))
		return err
	}
	defer objOut.Body.Close()
	bodyBytes, err := io.ReadAll(objOut.Body)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("error reading S3 object for outputs: %w", err))
		return err
	}
	if deserialize {
		var s3Outputs map[string]interface{}
		if err := json.Unmarshal(bodyBytes, &s3Outputs); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("error unmarshalling S3 outputs JSON: %w", err))
			return err
		}
		if len(s3Outputs) > 0 {
			run.Outputs = s3Outputs
		}
	} else {
		run.OutputsBytes = bodyBytes
	}

	if schema != nil {
		loader := gojsonschema.NewGoLoader(run.Outputs)
		result, err := gojsonschema.Validate(schema, loader)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("error validating output JSON against schema: %w", err))
			return err
		}

		if !result.Valid() {
			var valErrors []string
			for _, desc := range result.Errors() {
				valErrors = append(valErrors, desc.String())
			}
			h.handleError(w, r, oplog, fmt.Errorf("%w: output schema validation errors: %v", errInvalidRunIO, valErrors))
			return errInvalidRunIO
		}
	}
	return nil
}

func (h *MultipartExamplesHandler) copyAttachment(runS3Key string, exampleS3Key string, r *http.Request, oplog *slog.Logger) (*AttachmentInfo, error) {
	// Use the preconfigured S3 bucket name from config.Env, or change as needed:
	copyInput := &storage.CopyObjectInput{
		SourceBucket: config.Env.S3BucketName,
		SourceKey:    runS3Key,
		DestBucket:   config.Env.S3BucketName,
		DestKey:      exampleS3Key,
	}

	// Call our storage client's copy method
	if err := h.StorageClient.CopyObject(r.Context(), copyInput); err != nil {
		return nil, err
	}

	attachMeta, err := h.getAttachmentMetadata(r.Context(), runS3Key)
	if err != nil || attachMeta == nil {
		// Handle error or log a warning, and optionally set a default
		oplog.Warn("Warning: can't get metadata for %s: %v\n", runS3Key, err)
		// Either return here or proceed with default values
		return &AttachmentInfo{
			StorageURL: exampleS3Key,
			MimeType:   "application/octet-stream", // fallback or leave empty
		}, nil
	} else {
		return &AttachmentInfo{
			StorageURL: exampleS3Key,
			MimeType:   attachMeta.MimeType,
		}, nil
	}
}

func GetChatOutputs(run RunData) (map[string]interface{}, error) {
	// For chat, we look in run.Outputs["generations"] first:
	var firstGen interface{}
	if gens, ok := run.Outputs["generations"].([]interface{}); ok && len(gens) > 0 {
		firstGen = gens[0]
	}

	outputMessage := SafeGet(firstGen, nil, "message")
	stored := GetMessageAsStoredMessage(outputMessage)
	if stored != nil {
		return map[string]interface{}{
			"output": stored,
		}, nil
	}

	return map[string]interface{}{
		"output": map[string]interface{}{
			"type": "",
			"data": map[string]interface{}{
				"content": "",
			},
		},
	}, nil
}

func GetLLMOutputs(run RunData) (map[string]interface{}, error) {
	// For llm, we look in run.Outputs["generations"][0]["text"] or run.Outputs["generations"][0][0]["text"].
	if gens, ok := run.Outputs["generations"].([]interface{}); ok && len(gens) > 0 {
		// firstGenVal might itself be an array or a map
		firstGenVal := gens[0]

		// If firstGenVal is actually an array ([][]interface{} scenario),
		// pick its 0th element.
		if nestedArr, isArr := firstGenVal.([]interface{}); isArr && len(nestedArr) > 0 {
			firstGenVal = nestedArr[0]
		}

		// Now see if firstGenVal is a map so we can look for "text"
		if genMap, isMap := firstGenVal.(map[string]interface{}); isMap {
			if outputText, hasText := genMap["text"]; hasText && outputText != nil {
				return map[string]interface{}{
					"output": outputText,
				}, nil
			}
		}
	}

	return map[string]interface{}{"output": ""}, nil
}

func (h *MultipartExamplesHandler) prepareRunOutputs(
	dataType string,
	run RunData,
) (map[string]interface{}, error) {
	switch dataType {
	case "chat":
		return GetChatOutputs(run)
	case "llm":
		return GetLLMOutputs(run)
	}

	if len(run.Outputs) > 0 {
		return run.Outputs, nil
	}
	return map[string]interface{}{"output": ""}, nil
}

// SafeGet attempts to navigate
// nested map levels (or list indexes), returning defaultVal if anything doesn't exist.
// You provide the object (start), defaultVal, and a variadic list of path keys or indexes.
func SafeGet(
	start interface{},
	defaultVal interface{},
	path ...interface{},
) interface{} {
	current := start
	for _, key := range path {
		if current == nil {
			return defaultVal
		}

		switch c := current.(type) {
		case map[string]interface{}:
			strKey, isString := key.(string)
			if !isString {
				return defaultVal
			}
			val, found := c[strKey]
			if !found {
				return defaultVal
			}
			current = val
		case []interface{}:
			index, isIndex := key.(int)
			if !isIndex || index < 0 || index >= len(c) {
				return defaultVal
			}
			current = c[index]
		default:
			// If not a map or list, we can't continue
			return defaultVal
		}
	}
	return current
}

// IsOpenAITools checks if x is a list of items each recognized as an openai-style tool
func IsOpenAITools(x interface{}) bool {
	slice, ok := x.([]interface{})
	if !ok {
		return false
	}
	for _, item := range slice {
		if !IsOpenAITool(item) {
			return false
		}
	}
	return true
}

// IsOpenAIFunctions checks if x is a list of items each recognized as an openai-style function
func IsOpenAIFunctions(x interface{}) bool {
	slice, ok := x.([]interface{})
	if !ok {
		return false
	}
	for _, item := range slice {
		if !IsOpenAIFunction(item) {
			return false
		}
	}
	return true
}

// IsOpenAITool checks if a single item is shaped like a function-based tool: { "type": "function", "function": {...} }
func IsOpenAITool(x interface{}) bool {
	m, ok := x.(map[string]interface{})
	if !ok {
		return false
	}
	if t, hasType := m["type"]; !hasType || t != "function" {
		return false
	}
	if _, hasFn := m["function"]; !hasFn {
		return false
	}
	return IsOpenAIFunction(m["function"])
}

// IsOpenAIFunction checks if an item is an openai function { "name": string, "parameters": ... }
func IsOpenAIFunction(x interface{}) bool {
	m, ok := x.(map[string]interface{})
	if !ok {
		return false
	}
	name, hasName := m["name"]
	if !hasName || reflect.TypeOf(name).Kind() != reflect.String {
		return false
	}
	if _, hasParams := m["parameters"]; !hasParams {
		return false
	}
	return true
}

// GetMessageAsStoredMessage replicates _get_message_as_stored_message. We attempt to figure out
// the message’s type (user, system, ai, etc.) and data. If it’s invalid, we return nil.
func GetMessageAsStoredMessage(message interface{}) map[string]interface{} {
	msgType := GetMessageType(message)
	data := GetMessageFields(message)
	if msgType == "" || data == nil {
		return nil
	}
	return map[string]interface{}{
		"type": msgType,
		"data": data,
	}
}

// GetMessageType tries to figure out the "role" or "type" from the given message in various formats.
func GetMessageType(message interface{}) string {
	// If message is nil or not one of the types we expect, return empty string (like None in Python).
	if message == nil {
		return ""
	}

	var rawType string

	switch m := message.(type) {

	case map[string]interface{}:
		// 1) If "lc" is present, try extracting something like "foo" from the last element of message["id"]
		if _, hasLC := m["lc"]; hasLC {
			if idVal, hasID := m["id"]; hasID {
				if idArr, ok := idVal.([]interface{}); ok && len(idArr) > 0 {
					if last, ok2 := idArr[len(idArr)-1].(string); ok2 {
						// If last is e.g. "FooMessage", then split off "Message"
						parts := strings.SplitN(last, "Message", 2)
						rawType = parts[0] // parts[0] is everything before "Message"
					}
				}
			}

			// 2) Else if "type" is present and not nil
		} else if tVal, hasT := m["type"]; hasT && tVal != nil {
			if tStr, ok := tVal.(string); ok {
				rawType = tStr
			}

			// 3) Else if "role" is present and we also see content/tool_calls/function_call
		} else if roleVal, hasRole := m["role"]; hasRole {
			roleStr, ok := roleVal.(string)
			if !ok {
				// If "role" isn't even a string, bail out now
				break
			}
			_, hasContent := m["content"]
			_, hasToolCalls := m["tool_calls"]
			_, hasFuncCall := m["function_call"]

			if hasContent || hasToolCalls || hasFuncCall {
				// Use role -> "human", "ai", etc. (OpenAI to LangSmith role mapping)
				rawType = GetLangSmithRoleFromOpenAIRole(roleStr)
			} else {
				// fallback to the role value itself
				rawType = roleStr
			}
		}

	// 4) If it's a slice/tuple of length two, set the type = message[0] (lowercased).
	case []interface{}:
		if len(m) == 2 {
			if firstVal, ok := m[0].(string); ok {
				rawType = firstVal
			}
		}
	}

	// If no type was found or not a string, rawType remains "".

	// Convert to lowercase
	typeLower := strings.ToLower(rawType)

	// Strip trailing "messagechunk" if present
	if strings.HasSuffix(typeLower, "messagechunk") {
		typeLower = strings.TrimSuffix(typeLower, "messagechunk")
	}

	return typeLower
}

func GetMessageFields(message interface{}) map[string]interface{} {
	// First, mimic "if not message or not isinstance(message, (dict, tuple, list)): return None"
	if message == nil {
		return nil
	}

	switch m := message.(type) {

	// If message is a map/dict
	case map[string]interface{}:

		// If "lc" in message => "serialized message" => return message["kwargs"]
		if _, hasLC := m["lc"]; hasLC {
			if kwargsVal, hasKwargs := m["kwargs"]; hasKwargs {
				if kwargsMap, ok := kwargsVal.(map[string]interface{}); ok {
					return kwargsMap
				}
			}
			// If "lc" present but no valid "kwargs", just return nil
			return nil
		}

		// Else if "data" in message => "stored message" => return message["data"]
		if dataVal, hasData := m["data"]; hasData {
			if dataMap, ok := dataVal.(map[string]interface{}); ok {
				return dataMap
			}
			// If it's present but not a map, return nil
			return nil
		}

		// Else if "role" in message => "openai message"
		if _, hasRole := m["role"]; hasRole {
			// content = message["content"] or ""
			content := ""
			if cVal, hasContent := m["content"]; hasContent {
				if cStr, ok := cVal.(string); ok {
					content = cStr
				}
			}
			// additional_kwargs = everything except role, content
			additional := map[string]interface{}{}
			for k, v := range m {
				if k == "role" || k == "content" || k == "type" {
					continue
				}
				additional[k] = v
			}
			return map[string]interface{}{
				"content":           content,
				"additional_kwargs": additional,
			}
		}

		// Else => unknown message format, return as is
		return m

	// If message is a slice/tuple
	case []interface{}:
		// Possibly a (role, content) 2-element pair => treat second item as content
		if len(m) == 2 {
			if _, ok := m[0].(string); ok {
				if contentStr, ok2 := m[1].(string); ok2 {
					return map[string]interface{}{"content": contentStr}
				}
			}
		}
		return nil

	// If none of the above => return nil
	default:
		return nil
	}
}

func GetLangSmithRoleFromOpenAIRole(role string) string {
	switch role {
	case "user":
		return "human"
	case "assistant":
		return "ai"
	default:
		return role
	}
}

var acceptedMessageRoles = map[string]bool{
	"human":     true,
	"system":    true,
	"ai":        true,
	"user":      true,
	"assistant": true,
	"function":  true,
	"tool":      true,
	"chat":      true,
	"platform":  true,
	"developer": true,
}

// IsAcceptedRole checks if the string is in the allowed roles
func IsAcceptedRole(role string) bool {
	return acceptedMessageRoles[role]
}
