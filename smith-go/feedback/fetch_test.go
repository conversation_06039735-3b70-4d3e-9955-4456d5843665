package feedback_test

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/feedback"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	"langchain.com/smith/util"
)

var (
	testChPool    driver.Conn
	testDbPool    *database.AuditLoggedPool
	testRedisPool redis.UniversalClient
)

func TestMain(m *testing.M) {
	testDbPool = database.PgConnect()
	var err error
	testChPool, err = database.ChConnect(true)
	if err != nil {
		panic(err)
	}

	testRedisPool = lsredis.SingleRedisConnect()

	m.Run()

	if testChPool != nil {
		testChPool.Close()
	}
	testRedisPool.Close()
}

func DbCleanup(t *testing.T, conn driver.Conn) {
	err := conn.Exec(context.Background(), "ALTER TABLE feedback_configs DELETE WHERE 1=1")
	assert.NoError(t, err)

	_, err = testDbPool.Exec(context.Background(), `
        DELETE FROM tenants;
        DELETE FROM organizations;
        DELETE FROM users;
    `)
	assert.NoError(t, err)
}

func TestFetchFeedbackConfigs(t *testing.T) {
	if testChPool == nil {
		t.Fatal("ClickHouse connection is nil")
	}
	defer DbCleanup(t, testChPool)

	config.Env.FFUsePgForFeedbackConfigsFetchEnabledAll = false
	t.Cleanup(func() { config.Env.FFUsePgForFeedbackConfigsFetchEnabledAll = true })

	ctx := context.Background()
	client := feedback.NewFeedbackClient(testRedisPool)

	orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	testConfigs := []struct {
		key    string
		config feedback.FeedbackConfig
	}{
		{
			key: "test_continuous",
			config: feedback.FeedbackConfig{
				Type: feedback.FeedbackTypeContinuous,
				Min:  util.FloatPtr(0),
				Max:  util.FloatPtr(1),
			},
		},
		{
			key: "test_categorical",
			config: feedback.FeedbackConfig{
				Type: feedback.FeedbackTypeCategorical,
				Categories: []feedback.FeedbackCategory{
					{Value: 1, Label: util.StringPtr("Good")},
					{Value: 0, Label: util.StringPtr("Bad")},
				},
			},
		},
	}

	for _, tc := range testConfigs {
		// Marshal Go struct -> JSON string
		configJSON, marshalErr := json.Marshal(tc.config)
		assert.NoError(t, marshalErr)

		// Now insert JSON as a string
		err := testChPool.Exec(ctx, `
            INSERT INTO feedback_configs (
                tenant_id,
                feedback_key,
                feedback_config,
                modified_at
            ) VALUES (?, ?, ?, ?)`,
			uuid.MustParse(tenantID),
			tc.key,
			string(configJSON),
			time.Now(),
		)
		assert.NoError(t, err)
	}

	t.Run("fetch all configs", func(t *testing.T) {
		configs, err := client.FetchFeedbackConfigsCached(ctx, authInfo, testChPool, testDbPool, nil)
		assert.NoError(t, err)
		assert.Len(t, configs, len(testConfigs))

		foundCategorical := false
		foundContinuous := false
		for _, config := range configs {
			if config.Key == "test_categorical" {
				foundCategorical = true
			}
			if config.Key == "test_continuous" {
				foundContinuous = true
			}
		}
		assert.True(t, foundCategorical, "test_categorical config not found")
		assert.True(t, foundContinuous, "test_continuous config not found")
	})

	t.Run("fetch specific configs", func(t *testing.T) {
		configs, err := client.FetchFeedbackConfigsCached(ctx, authInfo, testChPool, testDbPool, []string{"test_continuous"})
		assert.NoError(t, err)
		assert.Len(t, configs, 1)
		assert.Equal(t, "test_continuous", configs[0].Key)
		assert.Equal(t, feedback.FeedbackTypeContinuous, configs[0].FeedbackConfig.Type)
	})

	t.Run("verify caching", func(t *testing.T) {
		// First call should cache the result
		configs1, err := client.FetchFeedbackConfigsCached(ctx, authInfo, testChPool, testDbPool, []string{"test_continuous"})
		assert.NoError(t, err)

		// Delete the config from DB
		err = testChPool.Exec(ctx, `
            ALTER TABLE feedback_configs
            DELETE WHERE tenant_id = ? AND feedback_key = ?`,
			uuid.MustParse(tenantID),
			"test_continuous",
		)
		assert.NoError(t, err)

		// Second call should return cached result
		configs2, err := client.FetchFeedbackConfigsCached(ctx, authInfo, testChPool, testDbPool, []string{"test_continuous"})
		assert.NoError(t, err)
		assert.Equal(t, configs1, configs2)
	})
}

func insertPGCfg(t *testing.T, pool *database.AuditLoggedPool, tenant uuid.UUID, key string, cfg feedback.FeedbackConfig) {
	j, err := json.Marshal(cfg)
	assert.NoError(t, err)
	_, err = pool.Exec(context.Background(),
		`INSERT INTO feedback_configs (tenant_id, feedback_key, feedback_config, modified_at)
		 VALUES ($1,$2,$3::jsonb,now())`, tenant, key, string(j))
	assert.NoError(t, err)
}

func TestFetchFeedbackConfigsPG(t *testing.T) {
	ctx := context.Background()

	pg := database.PgConnect()
	defer pg.Close()
	rdb := testRedisPool
	ch := testChPool
	client := feedback.NewFeedbackClient(rdb)

	config.Env.FFUsePgForFeedbackConfigsFetchEnabledAll = true
	t.Cleanup(func() { config.Env.FFUsePgForFeedbackConfigsFetchEnabledAll = false })

	orgID := testutil.OrgSetup(t, pg, "o", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, pg, orgID, "t", fmt.Sprintf("test-tenant-%s", uuid.New().String()), &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{TenantID: tenantID, TenantConfig: &auth.TenantConfig{}}

	cfg1 := feedback.FeedbackConfig{
		Type: feedback.FeedbackTypeContinuous,
		Min:  util.FloatPtr(0), Max: util.FloatPtr(1),
	}
	cfg2 := feedback.FeedbackConfig{
		Type: feedback.FeedbackTypeCategorical,
		Categories: []feedback.FeedbackCategory{
			{Value: 1, Label: util.StringPtr("yes")},
			{Value: 0, Label: util.StringPtr("no")},
		},
	}
	insertPGCfg(t, pg, uuid.MustParse(tenantID), "pg_k1", cfg1)
	insertPGCfg(t, pg, uuid.MustParse(tenantID), "pg_k2", cfg2)

	// -------- act & assert -----
	t.Run("all keys via PG", func(t *testing.T) {
		got, err := client.FetchFeedbackConfigsCached(ctx, authInfo, ch, pg, nil)
		assert.NoError(t, err)
		assert.Len(t, got, 2)
	})

	t.Run("subset filter", func(t *testing.T) {
		got, err := client.FetchFeedbackConfigsCached(ctx, authInfo, ch, pg, []string{"pg_k1"})
		assert.NoError(t, err)
		assert.Len(t, got, 1)
		assert.Equal(t, "pg_k1", got[0].Key)
	})

	t.Run("redis cache respected", func(t *testing.T) {
		first, err := client.FetchFeedbackConfigsCached(ctx, authInfo, ch, pg, []string{"pg_k2"})
		assert.NoError(t, err)
		assert.Len(t, first, 1)

		// delete underlying row
		_, err = pg.Exec(ctx, `DELETE FROM feedback_configs WHERE tenant_id=$1 AND feedback_key=$2`, tenantID, "pg_k2")
		assert.NoError(t, err)
		got, err := client.FetchFeedbackConfigsCached(ctx, authInfo, ch, pg, []string{"pg_k2"})
		assert.NoError(t, err)
		assert.Equal(t, first, got)
	})
}
