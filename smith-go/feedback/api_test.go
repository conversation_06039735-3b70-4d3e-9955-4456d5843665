package feedback_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/gofrs/uuid"
	"github.com/justinas/alice"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	"langchain.com/smith/feedback"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
)

const (
	apiKey       = "***************************************************"
	hashedApiKey = "223ccab5ebc19e3d3c55a2b9fbb5bebf6808893afff285317b88fb5889867efd76049cd4dc04407c037ab3b353a11501304c0042a2ba9b37ded9f193f19a5b65"
)

func setUpDatabase(t *testing.T, dbpool *database.AuditLoggedPool) string {
	userID, _ := uuid.NewV4()
	userIdStr := userID.String()
	orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, userIdStr)
	tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
	serviceAccountId := testutil.ServiceAccountSetup(t, dbpool, "test service account", orgId)
	orgIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, nil, "ORGANIZATION_ADMIN", "organization", serviceAccountId, nil)
	serviceAccountIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, &tenantId, "WORKSPACE_ADMIN", "workspace", serviceAccountId, &orgIdentityId)
	testutil.ServiceApiKeySetup(t, dbpool, hashedApiKey, tenantId, serviceAccountIdentityId, "hm", serviceAccountId, orgId)

	return tenantId
}

func PostgresCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "DELETE FROM organizations; DELETE FROM users; DELETE FROM api_keys;")
	assert.NoError(t, err)
}

func TestIngestFeedbackBatch(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	tenantID := setUpDatabase(t, dbpool)
	defer PostgresCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	usage_limits.NewUsageLimitsClient(dbpool, *routedRedisPools, cachingRedisPool)
	tracer_sessions.NewTracerSessionsClient(dbpool, cachingRedisPool)
	feedback.NewFeedbackConfigClient(cachingRedisPool)

	chConn, err := database.ChConnect(true)
	require.NoError(t, err)
	defer func(chConn clickhouse.Conn) {
		err := chConn.Close()
		if err != nil {
			t.Error(err)
		}
	}(chConn)

	bh := feedback.NewBatchFeedbackHandler(dbpool, *routedRedisPools, chConn)

	ah := auth.NewBasicAuth(dbpool, cachingRedisPool, 0)

	doBatchRequest := func(t *testing.T, payload []byte) *httptest.ResponseRecorder {
		req := httptest.NewRequest(http.MethodPost, "/feedback/batch", bytes.NewReader(payload))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(bh.IngestFeedbackBatch)
		handlerWithAuth.ServeHTTP(wRec, req)
		return wRec
	}

	ctx := context.Background()
	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

	t.Run("valid request – happy path", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4())
		traceID := uuid.Must(uuid.NewV4())

		fmt.Println("traceID", traceID)
		fmt.Println("runID", runID)

		fb := map[string]interface{}{
			"run_id":   runID.String(),
			"trace_id": traceID.String(),
			"key":      "accuracy",
			"score":    0.95,
			"feedback_config": map[string]interface{}{
				"type": "continuous",
				"min":  0,
				"max":  1,
			},
		}
		raw, err := json.Marshal([]interface{}{fb})
		require.NoError(t, err)

		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusAccepted, wRec.Code, "Expected HTTP 202 Accepted")

		feedbackKey := fmt.Sprintf("smith:runs:feedback:%s:%s", tenantID, runID)
		exists, err := routedRedisClient.Exists(ctx, feedbackKey).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists, "Should have a feedback payload in Redis")
		fbSet, err := routedRedisClient.SMembers(ctx, feedbackKey).Result()
		require.NoError(t, err)
		var fbPayload map[string]interface{}
		err = json.Unmarshal([]byte(fbSet[0]), &fbPayload)
		require.NoError(t, err)
		assert.Equal(t, fbPayload["run_id"], runID.String())
		assert.Equal(t, fbPayload["trace_id"], traceID.String())
		assert.Equal(t, fbPayload["key"], "accuracy")
		assert.Equal(t, fbPayload["score"], 0.95)
		assert.Equal(t, fbPayload["feedback_config"], map[string]interface{}{
			"type": "continuous",
			"min":  float64(0),
			"max":  float64(1),
		})
	})

	t.Run("empty batch", func(t *testing.T) {
		raw, _ := json.Marshal([]interface{}{})
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "empty batch")
	})

	t.Run("missing run_id", func(t *testing.T) {
		traceID := uuid.Must(uuid.NewV4())
		fb := map[string]interface{}{
			"trace_id": traceID.String(),
			"key":      "accuracy",
			"score":    0.8,
		}
		raw, _ := json.Marshal([]interface{}{fb})
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusBadRequest, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "missing run_id")
	})

	t.Run("missing trace_id", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4())
		fb := map[string]interface{}{
			"run_id": runID.String(),
			"key":    "accuracy",
			"score":  0.8,
		}
		raw, _ := json.Marshal([]interface{}{fb})
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusBadRequest, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "missing trace_id")
	})

	t.Run("invalid JSON", func(t *testing.T) {
		// Missing closing square bracket
		raw := []byte(`[{"run_id":"abc"}`)
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "invalid batch JSON")
	})

	t.Run("invalid feedback config", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4())
		traceID := uuid.Must(uuid.NewV4())
		// min > max should be rejected by Resolve/VerifyFeedbackConfig
		fb := map[string]interface{}{
			"run_id":   runID.String(),
			"trace_id": traceID.String(),
			"key":      "rating",
			"score":    0.5,
			"feedback_config": map[string]interface{}{
				"type": "continuous",
				"min":  1,
				"max":  3,
			},
		}
		raw, _ := json.Marshal([]interface{}{fb})
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusBadRequest, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "invalid feedback config")
	})
}
