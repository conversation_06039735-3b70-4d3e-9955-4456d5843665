package feedback

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/jackc/pgx/v5"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

type FeedbackClient struct {
	cache *lsredis.Cache[string, []Feedback]
}

func (c *FeedbackClient) FetchFeedbackConfigsCached(
	ctx context.Context,
	auth auth.AuthInfo,
	clickhouseConn clickhouse.Conn,
	db *database.AuditLoggedPool,
	feedbackKeys []string,
) ([]Feedback, error) {
	if len(feedbackKeys) > 0 {
		sort.Strings(feedbackKeys)
	}

	keysStr := strings.Join(feedbackKeys, ",")
	hasher := sha256.New()
	hasher.Write([]byte(keysStr))
	keysHash := hex.EncodeToString(hasher.Sum(nil))
	cacheKey := fmt.Sprintf("feedback_configs:%s:%s", auth.TenantID, keysHash)
	feedbackConfigs, err := c.cache.GetFresh(ctx, cacheKey, func() ([]Feedback, error) {
		return fetchFeedbackConfigs(ctx, auth, clickhouseConn, db, feedbackKeys)
	})
	if err != nil {
		return nil, err
	}
	return feedbackConfigs, nil
}

type pgRow struct {
	FeedbackKey    string          `db:"feedback_key"`
	FeedbackConfig json.RawMessage `db:"feedback_config"`
	ModifiedAt     time.Time       `db:"modified_at"`
}

func fetchFeedbackConfigsPG(
	ctx context.Context,
	authInfo auth.AuthInfo,
	db *database.AuditLoggedPool,
	feedbackKeys []string,
) ([]Feedback, error) {
	var (
		sql  string
		args []any
	)
	if len(feedbackKeys) == 0 {
		sql = `
			SELECT DISTINCT ON (feedback_key)
			       feedback_key,
			       feedback_config,
			       modified_at
			  FROM feedback_configs
			 WHERE tenant_id = $1
		  ORDER BY feedback_key, modified_at DESC`
		args = []any{authInfo.TenantID}
	} else {
		var err error
		norm := make([]string, len(feedbackKeys))
		for i, k := range feedbackKeys {
			norm[i], err = normalizeFeedbackKey(k)
			if err != nil {
				return nil, fmt.Errorf("normalise %q: %w", k, err)
			}
		}
		sql = `
			SELECT DISTINCT ON (feedback_key)
			       feedback_key,
			       feedback_config,
			       modified_at
			  FROM feedback_configs
			 WHERE tenant_id = $1
			   AND feedback_key = ANY($2::text[])
		  ORDER BY feedback_key, modified_at DESC`
		args = []any{authInfo.TenantID, norm}
	}

	rows, err := db.Query(ctx, sql, args...)
	if err != nil {
		return nil, fmt.Errorf("postgres query: %w", err)
	}
	defer rows.Close()

	pgRows, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[pgRow])
	if err != nil {
		return nil, fmt.Errorf("collect rows: %w", err)
	}

	results := make([]Feedback, 0, len(pgRows))
	for _, r := range pgRows {
		var cfg FeedbackConfig
		if err = json.Unmarshal(r.FeedbackConfig, &cfg); err != nil {
			var jsonStr string
			if err2 := json.Unmarshal(r.FeedbackConfig, &jsonStr); err2 == nil {
				if err = json.Unmarshal([]byte(jsonStr), &cfg); err != nil {
					return nil, fmt.Errorf("error unmarshalling double-encoded config for %q: %w", r.FeedbackKey, err)
				}
			} else {
				return nil, fmt.Errorf("error unmarshalling config for %q: %w", r.FeedbackKey, err)
			}
		}
		if err = ValidateFeedbackConfig(cfg); err != nil {
			return nil, fmt.Errorf("invalid config for %q: %w", r.FeedbackKey, err)
		}
		results = append(results, Feedback{
			Key:            r.FeedbackKey,
			FeedbackConfig: &cfg,
		})
	}

	return results, nil
}

func fetchFeedbackConfigs(
	ctx context.Context,
	auth auth.AuthInfo,
	clickhouseConn clickhouse.Conn,
	db *database.AuditLoggedPool,
	feedbackKeys []string,
) ([]Feedback, error) {
	if config.Env.FFUsePgForFeedbackConfigsFetchEnabledAll || config.Env.FFUsePgForFeedbackConfigsFetchEnabledTenants.Contains(auth.TenantID) {
		return fetchFeedbackConfigsPG(ctx, auth, db, feedbackKeys)
	}

	var err error
	var span tracer.Span
	if config.Env.DatadogEnabled {
		span, _ = tracer.StartSpanFromContext(ctx, "clickhouse.query", tracer.ResourceName("FetchFeedbackConfigs"))
		defer func() {
			span.Finish(tracer.WithError(err))
		}()
	}
	var query string
	var args []interface{}

	if len(feedbackKeys) == 0 {
		query = `
            -- fetch_feedback_configs
            --
            -- organization_id:` + auth.OrganizationID + `
            -- tenant_id:` + auth.TenantID + `
            -- user_id:` + auth.UserID + `
            -- ls_user_id:` + auth.LSUserID + `
            SELECT
                feedback_key,
                feedback_config,
                modified_at
            FROM (
                SELECT *
                FROM feedback_configs
                WHERE tenant_id = ?
                ORDER BY modified_at DESC
                LIMIT 1 BY feedback_key
                SETTINGS select_sequential_consistency = 1
            ) AS feedback_configs
            ORDER BY modified_at ASC
        `
		args = []interface{}{auth.TenantID}
	} else {
		normalizedKeys := make([]string, len(feedbackKeys))
		for i, key := range feedbackKeys {
			normalizedKeys[i], err = normalizeFeedbackKey(key)
			if err != nil {
				return nil, fmt.Errorf("failed to normalize feedback key: %w", err)
			}
		}

		query = `
            -- fetch_feedback_configs_by_keys
            --
            -- organization_id:` + auth.OrganizationID + `
            -- tenant_id:` + auth.TenantID + `
            -- user_id:` + auth.UserID + `
            -- ls_user_id:` + auth.LSUserID + `
            SELECT
                feedback_key,
                feedback_config,
                modified_at
            FROM feedback_configs FINAL
            WHERE tenant_id = ?
              AND feedback_key IN ?
            ORDER BY modified_at ASC
        `
		args = []interface{}{auth.TenantID, normalizedKeys}
	}

	if span != nil {
		span.SetTag("clickhouse.query", query)
	}

	rows, err := clickhouseConn.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("query execution failed: %w", err)
	}
	defer rows.Close()

	results := make([]Feedback, 0, len(feedbackKeys))
	for rows.Next() {
		var (
			feedbackKey    string
			feedbackCfgStr string
			modifiedAt     time.Time
		)

		if err := rows.Scan(&feedbackKey, &feedbackCfgStr, &modifiedAt); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		var feedbackConfig FeedbackConfig
		if err := json.Unmarshal([]byte(feedbackCfgStr), &feedbackConfig); err != nil {
			return nil, fmt.Errorf("invalid feedback config for key %q: %w", feedbackKey, err)
		}

		rowData := Feedback{
			Key:            feedbackKey,
			FeedbackConfig: &feedbackConfig,
		}

		mapped, err := mapFeedbackConfig(rowData)
		if err != nil {
			return nil, fmt.Errorf("failed to map feedback config: %w", err)
		}
		results = append(results, mapped)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("row iteration error: %w", err)
	}

	return results, nil
}

func mapFeedbackConfig(row Feedback) (Feedback, error) {
	feedbackKey := row.Key
	if feedbackKey == "" {
		return Feedback{}, fmt.Errorf("feedback_key must be a string")
	}
	normalizedKey, err := normalizeFeedbackKey(feedbackKey)
	if err != nil {
		return Feedback{}, err
	}

	configBytes, err := json.Marshal(row.FeedbackConfig)
	if err != nil {
		return Feedback{}, fmt.Errorf("invalid feedback config: %w", err)
	}

	var feedbackConfig FeedbackConfig
	if err := json.Unmarshal(configBytes, &feedbackConfig); err != nil {
		return Feedback{}, fmt.Errorf("invalid feedback config: %w", err)
	}

	if err := ValidateFeedbackConfig(feedbackConfig); err != nil {
		return Feedback{}, fmt.Errorf("invalid feedback config: %w", err)
	}

	return Feedback{
		Key:            normalizedKey,
		FeedbackConfig: &feedbackConfig,
	}, nil
}
