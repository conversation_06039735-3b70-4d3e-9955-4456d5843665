package feedback

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/google/uuid"

	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	"langchain.com/smith/ingestion"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
	"langchain.com/smith/util"
)

type BatchFeedbackHandler struct {
	RoutedRedisPools     lsredis.RoutedRedisPools
	UsageLimitsClient    *usage_limits.UsageLimitsClient
	TracerSessionsClient *tracer_sessions.TracerSessionsClient
	FeedbackConfigClient *FeedbackConfigClient
	Pg                   *database.AuditLoggedPool
	ClickhouseConn       clickhouse.Conn
}

func NewBatchFeedbackHandler(
	dbpool *database.AuditLoggedPool,
	routedRedisPools lsredis.RoutedRedisPools,
	clickhouseConn clickhouse.Conn,
) *BatchFeedbackHandler {
	usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get usage limits client: %v", err))
	}
	tracerSessionsClient, err := tracer_sessions.GetTracerSessionsClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get tracer sessions client: %v", err))
	}
	feedbackConfigClient, err := GetFeedbackConfigClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get feedback client: %v", err))
	}

	return &BatchFeedbackHandler{
		RoutedRedisPools:     routedRedisPools,
		UsageLimitsClient:    usageLimitsClient,
		TracerSessionsClient: tracerSessionsClient,
		FeedbackConfigClient: feedbackConfigClient,
		Pg:                   dbpool,
		ClickhouseConn:       clickhouseConn,
	}
}

var (
	// 422 Unprocessable Entity
	errInvalidBatchJson   = errors.New("invalid batch JSON")
	errEmptyBatch         = errors.New("empty batch")
	errTraceLimitExceeded = errors.New("trace limit exceeded")

	// 400 Bad Request
	errInvalidDottedOrder    = errors.New("invalid 'dotted_order'")
	errInvalidFeedbackConfig = errors.New("invalid feedback config")
	errInvalidInput          = errors.New("invalid input")
	errInvalidSession        = errors.New("invalid session")

	// 409 Conflict
	errPayloadAlreadyReceived = errors.New("payload already received")

	// 429 Too Many Requests
	errTenantExceededUsageLimits = errors.New("tenant exceeded usage limits")

	// 500 Internal Server Error
	errFetchingFeedbackConfigs = errors.New("failed to fetch feedback configs")
)

func (h *BatchFeedbackHandler) handleError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error) {
	var status int
	var message string

	switch {
	case errors.Is(err, errInvalidBatchJson),
		errors.Is(err, errEmptyBatch),
		errors.Is(err, errTraceLimitExceeded):
		status = http.StatusUnprocessableEntity
		message = "Unprocessable entity: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errInvalidDottedOrder),
		errors.Is(err, errInvalidFeedbackConfig),
		errors.Is(err, errInvalidSession),
		errors.Is(err, ErrInvalidFeedbackKey),
		errors.Is(err, errInvalidInput):
		status = http.StatusBadRequest
		message = "Bad request: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errPayloadAlreadyReceived):
		status = http.StatusConflict
		message = "Conflict: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errTenantExceededUsageLimits):
		status = http.StatusTooManyRequests
		message = "Too many requests: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, context.Canceled),
		errors.Is(err, http.ErrHandlerTimeout),
		strings.Contains(err.Error(), "failed to read from underlying reader"):
		status = 499 // Client closed request
		message = "Client closed connection"
		oplog.Warn(message, "error", err)

	default:
		// if it's known transient => 503, else 500
		if util.IsRetriableError(err) {
			status = http.StatusServiceUnavailable
			message = "Service unavailable: " + err.Error()
			oplog.Warn(message, "error", err)
		} else {
			status = http.StatusInternalServerError
			referenceId := uuid.NewString()
			message = "Internal server error: reference ID " + referenceId
			oplog.Error(message, "err", err, "reference_id", referenceId)
		}
	}

	render.Status(r, status)
	render.JSON(w, r, map[string]string{"error": message})
}

// IngestFeedbackBatch godoc
// @Summary      Ingest Feedback (Batch JSON)
// @Description  Ingests a batch of feedback objects in a single JSON array payload.
// @Tags         feedback
// @Accept       json
// @Produce      json
// @Param        body  body  []feedback.FeedbackCreateSchema  true  "Array of feedback objects"
// @Success      202  {object}  map[string]string{message=string} "Feedback batch ingested"
// @Failure      400  {object}  runs.ErrorResponse
// @Failure      403  {object}  runs.ErrorResponse
// @Failure      409  {object}  runs.ErrorResponse
// @Failure      422  {object}  runs.ErrorResponse
// @Failure      429  {object}  runs.ErrorResponse
// @Router       /feedback/batch [post]
func (h *BatchFeedbackHandler) IngestFeedbackBatch(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)

	tenantExceededLonglivedLimits, limitExceededMessage, err := h.UsageLimitsClient.CheckLonglivedUsageLimits(r.Context(), *authInfo)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("failed to check usage limits: %w", err))
		return
	}

	if tenantExceededLonglivedLimits {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTenantExceededUsageLimits, limitExceededMessage))
		return
	}

	rawBody, err := io.ReadAll(r.Body)
	if err != nil {
		if errors.Is(err, io.EOF) {
			h.handleError(w, r, oplog, context.Canceled)
		} else {
			h.handleError(w, r, oplog, fmt.Errorf("failed to read request body: %w", err))
		}
		return
	}

	var feedbackList []FeedbackCreateSchema
	if err := json.Unmarshal(rawBody, &feedbackList); err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidBatchJson, err))
		return
	}
	if len(feedbackList) == 0 {
		h.handleError(w, r, oplog, errEmptyBatch)
		return
	}

	traceIDs := make(map[uuid.UUID]struct{})
	payloads := make([]ingestion.QueuePayload, len(feedbackList))

	feedbackKeys := make([]string, 0)
	for _, f := range feedbackList {
		feedbackKeys = append(feedbackKeys, f.Key)
	}

	fetchedConfigs, err := h.FeedbackConfigClient.FetchFeedbackConfigsCached(r.Context(), *authInfo, h.ClickhouseConn, h.Pg, feedbackKeys)
	if err != nil {
		h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errFetchingFeedbackConfigs, err))
		return
	}

	feedbackConfigsMap := make(map[string]*FeedbackConfig, len(fetchedConfigs))
	for _, cfg := range fetchedConfigs {
		feedbackConfigsMap[cfg.Key] = cfg.FeedbackConfig
	}

	for i, fb := range feedbackList {
		if fb.RunID == nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: feedback[%d] missing run_id", errInvalidInput, i))
			return
		}
		if fb.TraceID == nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: feedback[%d] missing trace_id", errInvalidInput, i))
			return
		}

		storedConfig := feedbackConfigsMap[fb.Key]

		defaultConfig := GetDefaultFeedbackConfig([]FeedbackCreateSchema{fb})

		feedbackConfig, err := ResolveFeedbackConfig(
			storedConfig,
			fb.FeedbackConfig,
			defaultConfig,
		)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidFeedbackConfig, err))
			return
		}

		if err := VerifyFeedbackConfig(Feedback{FeedbackConfig: &feedbackConfig, Score: fb.Score}, feedbackConfig); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidFeedbackConfig, err))
			return
		}

		fbBytes, _ := json.Marshal(fb)

		payloads[i] = ingestion.QueuePayload{
			RunID:         *fb.RunID,
			TraceID:       fb.TraceID,
			ParentID:      nil,
			SetKey:        util.StringPtr("feedback"),
			ContentType:   "application/json",
			Value:         fbBytes,
			ProcessInline: true,
		}

		traceIDs[*fb.TraceID] = struct{}{}
	}

	routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	if err := ingestion.QueueRunPayload(ctx, routedRedisClient, queueingRedisClient, *authInfo, traceIDs, payloads, false, false); err != nil {
		switch ierr := err.(type) {
		case *ingestion.IngestionError:
			switch ierr.Code {
			case ingestion.CodeInvalidInput:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidInput, ierr))
			case ingestion.CodeDuplicate:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errPayloadAlreadyReceived, ierr))
			case ingestion.CodeTraceLimitExceeded:
				h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errTraceLimitExceeded, ierr))
			default:
				h.handleError(w, r, oplog, fmt.Errorf("failed to queue feedback: %w", err))
			}
		default:
			h.handleError(w, r, oplog, fmt.Errorf("failed to queue feedback: %w", err))
		}
		return
	}

	if lsredis.IsDualWriteEnabledForTenant(authInfo.TenantID) {
		dualWriteRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationDualWrite)
		if err := ingestion.QueueRunPayload(ctx, dualWriteRedisClient, queueingRedisClient, *authInfo, traceIDs, payloads, false, true); err != nil {
			oplog.Error("Error in dual write to Redis", "error", err)
		}
	}

	response := map[string]string{"message": "Feedback batch ingested"}
	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, response)
}
