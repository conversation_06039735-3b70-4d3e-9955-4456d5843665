#!/bin/bash

# Benchmark analysis script for continuous benchmarking
# Usage: ./scripts/benchmark-analysis.sh [options]

set -e

# Default values
BENCHTIME="10x"
COUNT="5"
PACKAGE="./storage/..."
OUTPUT_DIR="benchmark-results"
COMPARE_BRANCH="main"
CURRENT_BRANCH=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    -t, --benchtime TIME    Benchmark time (default: ${BENCHTIME})
    -c, --count COUNT       Number of benchmark runs (default: ${COUNT})
    -p, --package PACKAGE   Package to benchmark (default: ${PACKAGE})
    -o, --output DIR        Output directory (default: ${OUTPUT_DIR})
    -b, --branch BRANCH     Branch to compare against (default: ${COMPARE_BRANCH})
    --current-only          Only run benchmarks on current branch
    --compare-only          Only compare existing benchmark files
    --clean                 Clean up benchmark result files

Examples:
    $0                                          # Run full benchmark comparison
    $0 -t 100x -c 3                           # Run with different parameters
    $0 -p "./ingestion/..." -b develop        # Benchmark different package against develop
    $0 --current-only                          # Only benchmark current branch
    $0 --compare-only                          # Only compare existing results
    $0 --clean                                 # Clean up result files

EOF
}

# Function to clean up benchmark files
cleanup() {
    print_color $YELLOW "Cleaning up benchmark result files..."
    rm -f current-bench.txt main-bench.txt "${COMPARE_BRANCH}-bench.txt"
    rm -rf "${OUTPUT_DIR}"
    print_color $GREEN "Cleanup completed."
}

# Function to setup test environment
setup_test_env() {
    print_color $BLUE "Setting up test environment..."
    make -C ../smith-backend tests-setup
}

# Function to run benchmarks
run_benchmark() {
    local branch_name=$1
    local output_file=$2
    
    print_color $BLUE "Running benchmarks on branch: ${branch_name}"
    print_color $YELLOW "Output file: ${output_file}"
    
    LANGCHAIN_ENV=local_test \
    POSTGRES_DATABASE_URI=postgres:postgres@localhost:5432/langsmith_test \
    REDIS_DATABASE_URI=redis://localhost:6379/1 \
    go test ${PACKAGE} -vet=off -run=^$ -bench=. -benchmem -benchtime=${BENCHTIME} -count=${COUNT} | tee "${output_file}"
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "Benchmarks completed successfully for ${branch_name}"
    else
        print_color $RED "Benchmarks failed for ${branch_name}"
        return 1
    fi
}

# Function to compare benchmarks
compare_benchmarks() {
    local base_file=$1
    local current_file=$2
    
    print_color $BLUE "Comparing benchmarks..."
    
    # Install benchcmp if not available
    if ! command -v benchcmp &> /dev/null; then
        print_color $YELLOW "Installing benchcmp..."
        go install golang.org/x/tools/cmd/benchcmp@latest
    fi
    
    # Install benchstat if not available
    if ! command -v benchstat &> /dev/null; then
        print_color $YELLOW "Installing benchstat..."
        go install golang.org/x/perf/cmd/benchstat@latest
    fi
    
    echo ""
    print_color $GREEN "=== Benchmark Comparison (${COMPARE_BRANCH} -> current) ==="
    echo ""
    
    # Use benchcmp for simple comparison
    if benchcmp "${base_file}" "${current_file}"; then
        print_color $GREEN "Benchmark comparison completed"
    else
        print_color $YELLOW "Some differences found in benchmarks"
    fi
    
    echo ""
    print_color $GREEN "=== Statistical Analysis ==="
    echo ""
    
    # Use benchstat for statistical analysis
    benchstat "${base_file}" "${current_file}" || print_color $YELLOW "Statistical analysis completed with warnings"
}

# Function to save current branch
save_current_branch() {
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    print_color $BLUE "Current branch: ${CURRENT_BRANCH}"
}

# Function to restore branch
restore_branch() {
    if [ -n "${CURRENT_BRANCH}" ] && [ "${CURRENT_BRANCH}" != "HEAD" ]; then
        print_color $BLUE "Restoring branch: ${CURRENT_BRANCH}"
        git checkout "${CURRENT_BRANCH}"
        git stash pop 2>/dev/null || true
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -t|--benchtime)
            BENCHTIME="$2"
            shift 2
            ;;
        -c|--count)
            COUNT="$2"
            shift 2
            ;;
        -p|--package)
            PACKAGE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -b|--branch)
            COMPARE_BRANCH="$2"
            shift 2
            ;;
        --current-only)
            CURRENT_ONLY=true
            shift
            ;;
        --compare-only)
            COMPARE_ONLY=true
            shift
            ;;
        --clean)
            cleanup
            exit 0
            ;;
        *)
            print_color $RED "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_color $GREEN "Starting benchmark analysis..."
    print_color $BLUE "Configuration:"
    echo "  Benchtime: ${BENCHTIME}"
    echo "  Count: ${COUNT}"
    echo "  Package: ${PACKAGE}"
    echo "  Compare branch: ${COMPARE_BRANCH}"
    echo ""
    
    # Create output directory
    mkdir -p "${OUTPUT_DIR}"
    
    # Setup test environment
    setup_test_env
    
    # Save current branch
    save_current_branch
    
    # Set up trap to restore branch on exit
    trap restore_branch EXIT
    
    if [ "${COMPARE_ONLY}" = true ]; then
        # Only compare existing files
        if [ -f "${COMPARE_BRANCH}-bench.txt" ] && [ -f "current-bench.txt" ]; then
            compare_benchmarks "${COMPARE_BRANCH}-bench.txt" "current-bench.txt"
        else
            print_color $RED "Benchmark files not found for comparison"
            exit 1
        fi
    elif [ "${CURRENT_ONLY}" = true ]; then
        # Only run current branch benchmarks
        run_benchmark "${CURRENT_BRANCH}" "current-bench.txt"
    else
        # Full benchmark comparison
        
        # Run benchmarks on current branch
        run_benchmark "${CURRENT_BRANCH}" "current-bench.txt"
        
        # Stash changes and switch to compare branch
        git stash push -m "benchmark-temp-$(date +%s)" 2>/dev/null || true
        git checkout "${COMPARE_BRANCH}"
        
        # Run benchmarks on compare branch
        run_benchmark "${COMPARE_BRANCH}" "${COMPARE_BRANCH}-bench.txt"
        
        # Switch back to original branch
        git checkout "${CURRENT_BRANCH}"
        git stash pop 2>/dev/null || true
        
        # Compare benchmarks
        compare_benchmarks "${COMPARE_BRANCH}-bench.txt" "current-bench.txt"
        
        # Move results to output directory
        mv current-bench.txt "${OUTPUT_DIR}/"
        mv "${COMPARE_BRANCH}-bench.txt" "${OUTPUT_DIR}/"
    fi
    
    print_color $GREEN "Benchmark analysis completed!"
}

# Run main function
main
