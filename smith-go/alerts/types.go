package alerts

import (
	"context"
	"encoding/json"
	"log/slog"
	"time"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

// alert rule constants
const (
	ThresholdType = "threshold"
	ChangeType    = "change"
)

const (
	LatencyAttribute       = "latency"
	ErrorCountAttribute    = "error_count"
	FeedbackScoreAttribute = "feedback_score"
	RunLatencyAttribute    = "run_latency"
	RunCountAttribute      = "run_count"
)

const (
	AvgAggregation = "avg"
	SumAggregation = "sum"
	PctAggregation = "pct"
)

const (
	GreaterThanOrEqualToOperator = "gte"
	LessThanOrEqualToOperator    = "lte"
)

const (
	PagerDutyActionTarget = "pagerduty"
	WebhookActionTarget   = "webhook"
)

var AttributeMapping = map[string]string{
	LatencyAttribute:       "Latency",
	ErrorCountAttribute:    "Error Count",
	FeedbackScoreAttribute: "Feedback Score",
	RunLatencyAttribute:    "Run Latency",
	RunCountAttribute:      "Error Run Count",
}

// Base struct with common fields
type AlertRuleBase struct {
	ID                     string   `json:"id"`
	Name                   string   `json:"name" validate:"required"`
	Description            string   `json:"description" validate:"required"`
	Type                   string   `json:"type" validate:"required,oneof=threshold change"`
	Attribute              string   `json:"attribute" validate:"required,oneof=latency error_count feedback_score run_latency run_count"`
	Aggregation            string   `json:"aggregation" validate:"required,oneof=avg sum pct"`
	WindowMinutes          int      `json:"window_minutes" validate:"required,lte=15"` // max 15 minutes for alert rule
	Operator               string   `json:"operator" validate:"required,oneof=gte lte"`
	Threshold              *float64 `json:"threshold" validate:"omitempty,required_if=Type threshold"`
	ThresholdWindowMinutes *int     `json:"threshold_window_minutes" validate:"omitempty,required_if=Type change,lte=60"`
	ThresholdMultiplier    *float64 `json:"threshold_multiplier" validate:"omitempty,required_if=Type change"`
	Filter                 *string  `json:"filter"`
	DenominatorFilter      *string  `json:"denominator_filter"`
}

type AlertActionBase struct {
	ID          string          `json:"id"`
	AlertRuleID string          `json:"alert_rule_id"`
	Target      string          `json:"target" validate:"required,oneof=pagerduty webhook"`
	Config      json.RawMessage `json:"config" validate:"required" swaggertype:"object"`
}

type AlertRule struct {
	AlertRuleBase
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type AlertAction struct {
	AlertActionBase
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type AlertActionTarget string

const (
	AlertActionTargetPagerDuty AlertActionTarget = "pagerduty"
	AlertActionTargetWebhook   AlertActionTarget = "webhook"
)

type CreateAlertRuleRequest struct {
	Rule    AlertRuleBase     `json:"rule" validate:"required"`
	Actions []AlertActionBase `json:"actions" validate:"required,dive,min=1"`
}

type UpdateAlertRuleRequest struct {
	Rule    AlertRuleBase     `json:"rule" validate:"required"`
	Actions []AlertActionBase `json:"actions" validate:"required,dive,min=1"`
}

type DeleteAlertRuleRequest struct {
	ID string `json:"id"`
}

type AlertRuleResponse struct {
	Rule    AlertRule     `json:"rule"`
	Actions []AlertAction `json:"actions"`
}

type ErrorResponse struct {
	Error string `json:"error" example:"Invalid request: missing required fields"`
}

type ListAlertRulesResponse struct {
	SessionAlertRules []*AlertRuleResponse `json:"session_alert_rules"`
}

// alerts aggregation types
type AlertsAggregationHandler struct {
	dbpool           *database.AuditLoggedPool
	routedRedisPools lsredis.RoutedRedisPools
	cachingRedisPool redis.UniversalClient
	Oplog            *slog.Logger
	cache            *lsredis.Cache[string, *AlertRuleResponse]

	FirePagerDutyActionFn func(
		ctx context.Context,
		tenantID string,
		sessionID string,
		alertRule AlertRule,
		action AlertAction,
		triggeredMetricValue float64,
		triggeredThreshold float64,
		triggeredTime time.Time,
	) error
	FireWebhookActionFn func(
		ctx context.Context,
		tenantID string,
		sessionID string,
		alertRule AlertRule,
		action AlertAction,
		triggeredMetricValue float64,
		triggeredThreshold float64,
		triggeredTime time.Time,
	) error
}

type AggregatedAlertsRequest struct {
	SessionID   string `json:"session_id"`
	AlertRuleID string `json:"alert_rule_id"`
	MinuteKey   string `json:"minute_key"`
}

type AlertAggregation struct {
	Count            float64 `json:"count"`
	Sum              float64 `json:"sum"`
	DenominatorCount float64 `json:"denominator_count"`
	DenominatorSum   float64 `json:"denominator_sum"`
}

func (a *AlertAggregation) Add(b *AlertAggregation) {
	a.Count += b.Count
	a.Sum += b.Sum
	a.DenominatorCount += b.DenominatorCount
	a.DenominatorSum += b.DenominatorSum
}

type PagerDutyConfig struct {
	IntegrationKey string `json:"integration_key" validate:"required"`
	Severity       string `json:"severity" validate:"required,oneof=info warning error critical"`
}

type WebhookConfig struct {
	URL         string                 `json:"url" validate:"required"`
	Headers     map[string]string      `json:"headers" validate:"required"`
	Body        map[string]interface{} `json:"body" validate:"required"`
	ProjectName string                 `json:"project_name" validate:"required"`
}
