package alerts

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"

	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/util"
)

// error types for alerts handlers
var (
	errBadRequest                  = errors.New("bad request")
	errAlertRuleIDRequired         = errors.New("alert_rule_id is required")
	errSessionIDRequired           = errors.New("session_id is required")
	errAlertRuleIDDoesNotMatch     = errors.New("action alert rule uid does not match")
	errValidationFailed            = errors.New("request validation failed")
	errUnauthorized                = errors.New("unauthorized")
	errSessionNotFound             = errors.New("session / project not found")
	errServiceUnavailable          = errors.New("service unavailable")
	errInternalServerError         = errors.New("internal server error")
	errAlertRuleNotFound           = errors.New("alert rule not found")
	errNoAlertRulesFoundForSession = errors.New("no alert rules found for session")
	errAlertRuleLimitReached       = errors.New("alert rule limit reached for project")
)

// handle error for alerts handlers
func handleError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error, logString string) {
	var status int
	message := err.Error()
	logMessage := fmt.Sprintf("%s: %s", err.Error(), logString)

	switch {
	case errors.Is(err, errAlertRuleIDRequired),
		errors.Is(err, errBadRequest),
		errors.Is(err, errSessionIDRequired),
		errors.Is(err, errAlertRuleIDDoesNotMatch),
		errors.Is(err, errValidationFailed):
		status = http.StatusBadRequest
		oplog.Warn(logMessage)
		// propogate full error message in this case
		message = logMessage
	case errors.Is(err, errUnauthorized):
		oplog.Warn(logMessage)
		status = http.StatusForbidden
	case errors.Is(err, errAlertRuleNotFound),
		errors.Is(err, errSessionNotFound):
		status = http.StatusNotFound
		oplog.Warn(logMessage)
	case errors.Is(err, errAlertRuleLimitReached):
		status = http.StatusTooManyRequests
		oplog.Warn(logMessage)
	default:
		// if it's known transient => 503, else 500
		if util.IsRetriableError(err) {
			oplog.Warn(logMessage)
			status = http.StatusServiceUnavailable
			message = errServiceUnavailable.Error()
		} else {
			oplog.Error(logMessage)
			status = http.StatusInternalServerError
			message = errInternalServerError.Error()
		}
	}
	render.Status(r, status)
	render.JSON(w, r, ErrorResponse{Error: message})
}

// validate session is part of auth tenant or organization
func (h *AlertRulesCrudHandler) validateSessionID(ctx context.Context, authInfo *auth.AuthInfo, sessionID string) error {

	if authInfo == nil {
		return errors.New("unauthorized")
	}

	var query string
	var authID string

	if authInfo.TenantID != "" {
		query = `
			SELECT id 
			FROM tracer_session 
			WHERE id = $1
			AND tenant_id = $2
		`
		authID = authInfo.TenantID
	} else {
		query = `
			SELECT id 
			FROM tracer_session 
			WHERE id = $1
			AND tenant_id = ANY(
				SELECT id 
				FROM tenants 
				WHERE organization_id = $2
			)
		`
		authID = authInfo.OrganizationID
	}

	var id string
	err := h.DBPool.QueryRow(ctx, query, sessionID, authID).Scan(&id)
	if err == pgx.ErrNoRows {
		return errSessionNotFound
	}
	if err != nil {
		return err
	}

	if id == sessionID {
		return nil
	}

	return errors.New("unauthorized")
}

func GetPagerDutyConfig(actionConfigRaw json.RawMessage) (*PagerDutyConfig, error) {
	var integrationKey string
	var severity string

	// get the config string
	var configStr string
	err := json.Unmarshal(actionConfigRaw, &configStr)
	if err != nil {
		fmt.Println("Error getting config string:", err)
		return nil, err
	}

	// get the pagerduty config
	actionConfig := map[string]interface{}{}
	err = json.Unmarshal([]byte(configStr), &actionConfig)
	if err != nil {
		fmt.Println("Error unmarshalling PagerDuty config", err)
		return nil, err
	}

	// Check integration_key
	if val, ok := actionConfig["integration_key"]; ok {
		if integrationKey, ok = val.(string); !ok {
			return nil, fmt.Errorf("integration_key is not a string")
		}
	} else {
		return nil, fmt.Errorf("missing required field: integration_key")
	}

	// Check severity
	if val, ok := actionConfig["severity"]; ok {
		if severity, ok = val.(string); !ok {
			return nil, fmt.Errorf("severity is not a string")
		}
	} else {
		return nil, fmt.Errorf("missing required field: severity")
	}

	return &PagerDutyConfig{
		IntegrationKey: integrationKey,
		Severity:       severity,
	}, nil
}

func GetWebhookConfig(actionConfigRaw json.RawMessage) (*WebhookConfig, error) {
	var configStr string
	err := json.Unmarshal(actionConfigRaw, &configStr)
	if err != nil {
		fmt.Println("Error getting config string:", err)
		return nil, err
	}

	actionConfig := map[string]interface{}{}
	err = json.Unmarshal([]byte(configStr), &actionConfig)
	if err != nil {
		fmt.Println("Error unmarshalling PagerDuty config", err)
		return nil, err
	}

	var projectName string
	var webhookURL string
	var webhookHeaders map[string]string
	var webhookBody map[string]interface{}

	// Check project_name
	if val, ok := actionConfig["project_name"]; ok {
		if projectName, ok = val.(string); !ok {
			return nil, fmt.Errorf("project_name is not a string")
		}
	} else {
		return nil, fmt.Errorf("missing required field: project_name")
	}

	// Check webhook_url
	if val, ok := actionConfig["url"]; ok {
		if webhookURL, ok = val.(string); !ok {
			return nil, fmt.Errorf("url is not a string")
		}
		// Validate the URL
		parsedURL, err := url.Parse(webhookURL)
		if err != nil {
			return nil, fmt.Errorf("invalid URL format: %v", err)
		}
		// Check if URL has necessary components
		if parsedURL.Scheme == "" || parsedURL.Host == "" {
			return nil, fmt.Errorf("URL must have scheme and host")
		}
	} else {
		return nil, fmt.Errorf("missing required field: url")
	}

	// Check webhook_headers
	if val, ok := actionConfig["headers"]; ok {
		err = json.Unmarshal([]byte(val.(string)), &webhookHeaders)
		if err != nil {
			return nil, fmt.Errorf("headers is not a map of strings")
		}
	} else {
		return nil, fmt.Errorf("missing required field: headers")
	}

	// Check webhook_body
	if val, ok := actionConfig["body"]; ok {
		err = json.Unmarshal([]byte(val.(string)), &webhookBody)
		if err != nil {
			return nil, fmt.Errorf("body is not a map")
		}
	} else {
		return nil, fmt.Errorf("missing required field: body")
	}

	return &WebhookConfig{
		URL:         webhookURL,
		Headers:     webhookHeaders,
		Body:        webhookBody,
		ProjectName: projectName,
	}, nil
}

func BuildAlertRuleUrl(tenantID string, sessionID string, alertRuleID string) string {
	return fmt.Sprintf(
		"%s/o/%s/projects/p/%s?tab=3&peekAlert=%s",
		config.Env.LangSmithUrl,
		tenantID,
		sessionID,
		alertRuleID,
	)
}
