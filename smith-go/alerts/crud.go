package alerts

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
)

// Run prefetch handler
type AlertRulesCrudHandler struct {
	DBPool *database.AuditLoggedPool
	Oplog  *slog.Logger
}

func validateAlertFilter(ctx context.Context, r *http.Request, filter *string) error {
	if filter == nil || *filter == "" {
		return nil
	}

	// Construct the validation request
	filterBody := map[string]interface{}{
		"filter": *filter,
	}
	filterBodyBytes, err := json.Marshal(filterBody)
	if err != nil {
		return fmt.Errorf("marshalling filter body: %w", err)
	}
	validateURL := config.Env.SmithBackendEndpoint + "/internal/alert-filters/validate"
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		validateURL,
		bytes.NewBuffer(filterBodyBytes),
	)
	if err != nil {
		return fmt.Errorf("building validation request: %w", err)
	}
	req.Header = r.Header.Clone()
	req.Header.Set("Content-Type", "application/json")

	// Execute
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("sending validation request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// For debugging, you can read the body
		respBody, _ := io.ReadAll(resp.Body)
		return fmt.Errorf(
			"validation endpoint returned %d: %s", resp.StatusCode, string(respBody),
		)
	}

	return nil
}

// NewAlertRulesCrudHandler creates a new AlertRulesCrudHandler
func NewAlertRulesCrudHandler(dbpool *database.AuditLoggedPool) *AlertRulesCrudHandler {
	logger := slog.Default().With("component", "AlertRulesCrudHandler")
	return &AlertRulesCrudHandler{
		DBPool: dbpool,
		Oplog:  logger,
	}
}

// CreateAlertRule creates a new alert rule
func (h *AlertRulesCrudHandler) CreateAlertRule(ctx context.Context, tenantID string, sessionID string, alertRule *AlertRule, alertRuleActions *[]AlertAction) (err error) {

	tx, err := h.DBPool.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.ReadCommitted})
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			rollbackErr := tx.Rollback(ctx)
			if rollbackErr != nil {
				// Combine or log the rollback error, but keep the original error
				err = fmt.Errorf("original error: %w, rollback error: %v", err, rollbackErr)
			}
			return
		}

		commitErr := tx.Commit(ctx)
		if commitErr != nil {
			err = fmt.Errorf("commit error: %w", commitErr)
		}
	}()

	_, err = tx.Exec(ctx,
		`
		INSERT INTO alert_rules (
				id,
				name,
				description,
				session_id,
				tenant_id,
				type,
				attribute,
				aggregation,
				window_minutes,
				operator,
				threshold,
				threshold_window_minutes,
				threshold_multiplier,
				filter,
				denominator_filter,
				created_at,
				updated_at
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)`,
		alertRule.ID,
		alertRule.Name,
		alertRule.Description,
		sessionID,
		tenantID,
		alertRule.Type,
		alertRule.Attribute,
		alertRule.Aggregation,
		alertRule.WindowMinutes,
		alertRule.Operator,
		alertRule.Threshold,
		alertRule.ThresholdWindowMinutes,
		alertRule.ThresholdMultiplier,
		alertRule.Filter,
		alertRule.DenominatorFilter,
		alertRule.CreatedAt,
		alertRule.UpdatedAt,
	)
	if err != nil {
		return err
	}

	for _, action := range *alertRuleActions {
		_, err = tx.Exec(ctx, `
			INSERT INTO alert_actions (
				id,
				alert_rule_id,
				tenant_id,
				session_id,
				target,
				config,
				created_at,
				updated_at
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
			action.ID,
			alertRule.ID,
			tenantID,
			sessionID,
			action.Target,
			action.Config,
			action.CreatedAt,
			action.UpdatedAt)
		if err != nil {
			return err
		}
	}
	return err
}

// GetAlertRule gets an alert rule
func (h *AlertRulesCrudHandler) GetAlertRule(ctx context.Context, tenantID string, sessionID string, alertRuleID string) (*AlertRuleResponse, error) {
	// get the alert rule from the database

	rows, _ := h.DBPool.Query(ctx, `
		SELECT 
			id,
			name,
			description,
			type,
			attribute,
			aggregation,
			window_minutes,
			operator,
			threshold,
			threshold_window_minutes,
			threshold_multiplier,
			filter,
			denominator_filter,
			created_at,
			updated_at
		FROM alert_rules
		WHERE id = $1 AND session_id = $2 AND tenant_id = $3
	`, alertRuleID, sessionID, tenantID)

	alertRule, err := pgx.CollectOneRow(rows, pgx.RowToStructByPos[AlertRule])
	if err == pgx.ErrNoRows {
		return nil, errAlertRuleNotFound
	} else if err != nil {
		return nil, err
	}

	// get the alert rule actions from the database
	rows, _ = h.DBPool.Query(ctx, `
		SELECT
			id,
			alert_rule_id,
			target,
			config,
			created_at,
			updated_at
		FROM alert_actions
		WHERE alert_rule_id = $1 AND session_id = $2 AND tenant_id = $3
	`, alertRuleID, sessionID, tenantID)

	alertRuleActions, err := pgx.CollectRows(rows, pgx.RowToStructByPos[AlertAction])

	if err != nil {
		return nil, err
	}

	return &AlertRuleResponse{
		Rule:    alertRule,
		Actions: alertRuleActions,
	}, nil
}

// ListAlertRulesBySessionID lists all alert rules for a given session ID
func (h *AlertRulesCrudHandler) ListAlertRulesBySessionID(ctx context.Context, tenantID string, sessionID string) ([]*AlertRuleResponse, error) {

	rows, err := h.DBPool.Query(ctx, `
		SELECT
			id,
			name,
			description,
			type,
			attribute,
			aggregation,
			window_minutes,
			operator,
			threshold,
			threshold_window_minutes,
			threshold_multiplier,
			filter,
			denominator_filter,
			created_at,
			updated_at
		FROM alert_rules
		WHERE session_id = $1 AND tenant_id = $2
	`, sessionID, tenantID)

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	alertRules, err := pgx.CollectRows(rows, pgx.RowToStructByPos[AlertRule])

	if err == pgx.ErrNoRows {
		return nil, errNoAlertRulesFoundForSession
	}

	if err != nil {
		return nil, err
	}

	// mapping from alert rule to alert actions
	alertRuleActionsMap := make(map[string][]AlertAction)
	for _, alertRule := range alertRules {
		alertRuleActionsMap[alertRule.ID] = []AlertAction{}
	}

	alertRuleIDs := make([]string, len(alertRules))
	for i, alertRule := range alertRules {
		alertRuleIDs[i] = alertRule.ID
	}

	// get the alert actions from the database
	actionRows, err := h.DBPool.Query(ctx, `
		SELECT 
			id,
			alert_rule_id,
			target,
			config,
			created_at,
			updated_at
		FROM alert_actions
		WHERE alert_rule_id = ANY($1) AND session_id = $2 AND tenant_id = $3
	`, alertRuleIDs, sessionID, tenantID)
	if err != nil {
		return nil, err
	}
	defer actionRows.Close()

	alertRuleActions, err := pgx.CollectRows(actionRows, pgx.RowToStructByPos[AlertAction])
	if err != nil {
		return nil, err
	}

	for _, alertRuleAction := range alertRuleActions {
		alertRuleActionsMap[alertRuleAction.AlertRuleID] = append(alertRuleActionsMap[alertRuleAction.AlertRuleID], alertRuleAction)
	}

	// merge the alert actions into alert rules
	var alertRuleResponses []*AlertRuleResponse
	for _, alertRule := range alertRules {
		alertRuleResponses = append(alertRuleResponses, &AlertRuleResponse{
			Rule:    alertRule,
			Actions: alertRuleActionsMap[alertRule.ID],
		})
	}

	return alertRuleResponses, nil
}

// UpdateAlertRule updates an alert rule
func (h *AlertRulesCrudHandler) UpdateAlertRule(ctx context.Context, tenantID string, sessionID string, alertRuleID string, alertRule *AlertRuleBase, alertRuleActions *[]AlertActionBase) error {

	tx, err := h.DBPool.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.ReadCommitted})
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			rollbackErr := tx.Rollback(ctx)
			if rollbackErr != nil {
				// Combine or log the rollback error, but keep the original error
				err = fmt.Errorf("original error: %w, rollback error: %v", err, rollbackErr)
			}
			return
		}

		commitErr := tx.Commit(ctx)
		if commitErr != nil {
			err = fmt.Errorf("commit error: %w", commitErr)
		}
	}()

	now := time.Now().UTC()

	result, err := tx.Exec(ctx, `
		UPDATE alert_rules
		SET 
			name = $1,
			description = $2, 
			type = $3,
			attribute = $4,
			aggregation = $5,
			window_minutes = $6,
			operator = $7,
			threshold = $8,
			threshold_window_minutes = $9,
			threshold_multiplier = $10,
			filter = $11,
			denominator_filter = $12,
			updated_at = $13
		WHERE id = $14 AND session_id = $15 AND tenant_id = $16
	`,
		alertRule.Name,
		alertRule.Description,
		alertRule.Type,
		alertRule.Attribute,
		alertRule.Aggregation,
		alertRule.WindowMinutes,
		alertRule.Operator,
		alertRule.Threshold,
		alertRule.ThresholdWindowMinutes,
		alertRule.ThresholdMultiplier,
		alertRule.Filter,
		alertRule.DenominatorFilter,
		now,
		alertRuleID,
		sessionID,
		tenantID,
	)

	if err != nil {
		return err
	}

	if result.RowsAffected() == 0 {
		return errAlertRuleNotFound
	}

	for _, action := range *alertRuleActions {
		_, err = tx.Exec(ctx, `
			INSERT INTO alert_actions (
				id,
				alert_rule_id,
				tenant_id,
				session_id,
				target,
				config,
				created_at,
				updated_at
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
			ON CONFLICT (id) DO UPDATE SET
				target = $5,
				config = $6,
				updated_at = $7
			WHERE 
				alert_actions.id = $1 AND 
				alert_actions.alert_rule_id = $2 AND 
				alert_actions.tenant_id = $3 AND 
				alert_actions.session_id = $4
		`,
			action.ID,
			action.AlertRuleID,
			tenantID,
			sessionID,
			action.Target,
			action.Config,
			now,
			now,
		)
		if err != nil {
			return err
		}
	}

	return nil
}

// // DeleteAlertRule deletes an alert rule
func (h *AlertRulesCrudHandler) DeleteAlertRule(ctx context.Context, tenantID string, sessionID string, alertRuleID string) error {

	_, err := h.DBPool.Exec(ctx, `
		DELETE FROM alert_rules
		WHERE id = $1 AND session_id = $2 AND tenant_id = $3
	`, alertRuleID, sessionID, tenantID)

	if err != nil {
		return err
	}

	return nil
}

// CreateAlertRule godoc
// @Summary      Create an alert rule
// @Description  Creates a new alert rule. The request body must be a JSON-encoded alert rule object that follows the CreateAlertRuleRequest schema.
// @Tags         alert_rules
// @Accept       json
// @Produce      json
// @Param        X-API-Key  header    string  true  "LangSmith API Key"
// @Param        X-Tenant-ID  header    string  true  "Tenant ID"
// @Param        session_id  path      string  true  "Session ID"
// @Param        request     body      CreateAlertRuleRequest  true  "Alert rule request"
// @Success      201  {object}  AlertRuleResponse "Alert rule created"
// @Failure      400  {object}  ErrorResponse "Bad request"
// @Failure      403  {object}  ErrorResponse "Forbidden"
// @Failure      429  {object}  ErrorResponse "Alert Rule Limit Reached"
// @Failure      503  {object}  ErrorResponse "Service unavailable"
// @Failure      500  {object}  ErrorResponse "Internal server error"
// @Router       /v1/platform/alerts/{session_id} [post]
func (h *AlertRulesCrudHandler) CreateAlertRuleHandler(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()
	validate := validator.New()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	sessionID := chi.URLParam(r, "session_id")
	if sessionID == "" {
		handleError(w, r, oplog, errSessionIDRequired, "")
		return
	}

	err := h.validateSessionID(ctx, authInfo, sessionID)
	if err != nil {
		handleError(w, r, oplog, err, "")
		return
	}

	// validate the number of alert rules in session < limit
	alertRules, err := h.ListAlertRulesBySessionID(ctx, authInfo.TenantID, sessionID)
	if err != nil {
		handleError(w, r, oplog, err, "")
		return
	}

	if len(alertRules) >= config.Env.MaxAlertRulesPerProject {
		handleError(w, r, oplog, errAlertRuleLimitReached, "")
		return
	}

	var alertRuleRequest CreateAlertRuleRequest
	if err := json.NewDecoder(r.Body).Decode(&alertRuleRequest); err != nil {
		handleError(w, r, oplog, errValidationFailed, err.Error())
		return
	}

	// validate the alert rule request
	if err := validate.Struct(alertRuleRequest); err != nil {
		// Format validation errors
		validationErrors := err.(validator.ValidationErrors)
		errorMessages := make([]string, 0, len(validationErrors))
		for _, e := range validationErrors {
			errorMessages = append(errorMessages, fmt.Sprintf("%s: %s", e.Field(), e.Tag()))
		}
		render.Status(r, http.StatusBadRequest)
		handleError(w, r, oplog, errValidationFailed, fmt.Sprintf("%v", errorMessages))
		return
	}

	// validate any filters if they exist
	if alertRuleRequest.Rule.Filter != nil {
		if err := validateAlertFilter(ctx, r, alertRuleRequest.Rule.Filter); err != nil {
			handleError(w, r, oplog, errValidationFailed, err.Error())
			return
		}
	}
	if alertRuleRequest.Rule.DenominatorFilter != nil {
		if err := validateAlertFilter(ctx, r, alertRuleRequest.Rule.DenominatorFilter); err != nil {
			handleError(w, r, oplog, errValidationFailed, err.Error())
			return
		}
	}

	now := time.Now().UTC()

	// create the alert rule ID
	alertRuleRequest.Rule.ID = uuid.New().String()

	// add created and updated at to the alert rule
	alertRule := AlertRule{
		AlertRuleBase: alertRuleRequest.Rule,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// add created and updated at to the alert actions
	alertRuleActions := make([]AlertAction, len(alertRuleRequest.Actions))
	for i, action := range alertRuleRequest.Actions {

		// validate the alert action config
		if action.Target == "pagerduty" {
			_, err := GetPagerDutyConfig(action.Config)
			if err != nil {
				handleError(w, r, oplog, errValidationFailed, err.Error())
				return
			}
		} else if action.Target == "webhook" {
			_, err := GetWebhookConfig(action.Config)
			if err != nil {
				handleError(w, r, oplog, errValidationFailed, err.Error())
				return
			}
		}

		action.ID = uuid.New().String()
		action.AlertRuleID = alertRule.ID
		alertRuleActions[i] = AlertAction{
			AlertActionBase: action,
			CreatedAt:       now,
			UpdatedAt:       now,
		}
	}

	if err := h.CreateAlertRule(ctx, authInfo.TenantID, sessionID, &alertRule, &alertRuleActions); err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	oplog.Info(
		"Created alert rule",
		"session_id", sessionID,
		"tenant_id", authInfo.TenantID,
		"alert_rule_id", alertRule.ID,
	)

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, AlertRuleResponse{
		Rule:    alertRule,
		Actions: alertRuleActions,
	})
}

// GetAlertRuleHandler godoc
// @Summary      Get an alert rule
// @Description  Gets an alert rule.
// @Tags         alert_rules
// @Produce      json
// @Param        X-API-Key  header    string  true  "LangSmith API Key"
// @Param        X-Tenant-ID  header    string  true  "Tenant ID"
// @Param        session_id  path      string  true  "Session ID"
// @Param        alert_rule_id  path      string  true  "Alert rule ID"
// @Success      200  {object}  AlertRuleResponse "Alert rule"
// @Failure      400  {object}  ErrorResponse "Bad request"
// @Failure      403  {object}  ErrorResponse "Forbidden"
// @Failure      404  {object}  ErrorResponse "Not found"
// @Failure      503  {object}  ErrorResponse "Service unavailable"
// @Failure      500  {object}  ErrorResponse "Internal server error"
// @Router       /v1/platform/alerts/{session_id}/{alert_rule_id} [get]
func (h *AlertRulesCrudHandler) GetAlertRuleHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	sessionID := chi.URLParam(r, "session_id")
	if sessionID == "" {
		handleError(w, r, oplog, errSessionIDRequired, "")
		return
	}

	err := h.validateSessionID(ctx, authInfo, sessionID)
	if err != nil {
		handleError(w, r, oplog, err, "")
		return
	}

	alertRuleID := chi.URLParam(r, "alert_rule_id")
	if alertRuleID == "" {
		handleError(w, r, oplog, errAlertRuleIDRequired, "")
		return
	}

	alertRuleAndActions, err := h.GetAlertRule(ctx, authInfo.TenantID, sessionID, alertRuleID)
	if err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, alertRuleAndActions)
}

// ListAlertRulesBySessionIDHandler lists all alert rules for a given session ID
func (h *AlertRulesCrudHandler) ListAlertRulesBySessionIDHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	sessionID := chi.URLParam(r, "session_id")
	if sessionID == "" {
		handleError(w, r, oplog, errSessionIDRequired, "")
		return
	}

	err := h.validateSessionID(ctx, authInfo, sessionID)
	if err != nil {
		handleError(w, r, oplog, err, "accessing invalid session")
		return
	}

	alertRulesAndActions, err := h.ListAlertRulesBySessionID(ctx, authInfo.TenantID, sessionID)
	if err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	oplog.Info(
		"Alert rules for session",
		"session_id", sessionID,
		"tenant_id", authInfo.TenantID,
		"n_alert_rules", len(alertRulesAndActions),
	)

	render.Status(r, http.StatusOK)
	render.JSON(w, r, ListAlertRulesResponse{
		SessionAlertRules: alertRulesAndActions,
	})
}

// UpdateAlertRuleHandler godoc
// @Summary      Update an alert rule
// @Description  Updates an alert rule.
// @Tags         alert_rules
// @Produce      json
// @Param        X-API-Key  header    string  true  "LangSmith API Key"
// @Param        X-Tenant-ID  header    string  true  "Tenant ID"
// @Param        session_id  path      string  true  "Session ID"
// @Param        alert_rule_id  path      string  true  "Alert rule ID"
// @Param        request     body      UpdateAlertRuleRequest  true  "Alert rule request"
// @Success      200  {object}  map[string]string{message=string} "Alert rule updated"
// @Failure      400  {object}  ErrorResponse "Bad request"
// @Failure      403  {object}  ErrorResponse "Forbidden"
// @Failure      404  {object}  ErrorResponse "Not found"
// @Failure      503  {object}  ErrorResponse "Service unavailable"
// @Failure      500  {object}  ErrorResponse "Internal server error"
// @Router       /v1/platform/alerts/{session_id}/{alert_rule_id} [patch]
func (h *AlertRulesCrudHandler) UpdateAlertRuleHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	validate := validator.New()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	sessionID := chi.URLParam(r, "session_id")
	if sessionID == "" {
		handleError(w, r, oplog, errSessionIDRequired, "")
		return
	}

	alertRuleID := chi.URLParam(r, "alert_rule_id")
	if alertRuleID == "" {
		handleError(w, r, oplog, errAlertRuleIDRequired, "")
		return
	}

	err := h.validateSessionID(ctx, authInfo, sessionID)
	if err != nil {
		handleError(w, r, oplog, err, "")
		return
	}

	var alertRuleRequest UpdateAlertRuleRequest
	if err := json.NewDecoder(r.Body).Decode(&alertRuleRequest); err != nil {
		handleError(w, r, oplog, errValidationFailed, err.Error())
		return
	}

	if alertRuleID != alertRuleRequest.Rule.ID {
		handleError(w, r, oplog, errAlertRuleIDDoesNotMatch, "")
		return
	}

	// validate the alert rule request
	if err := validate.Struct(alertRuleRequest); err != nil {
		// Format validation errors
		validationErrors := err.(validator.ValidationErrors)
		errorMessages := make([]string, 0, len(validationErrors))
		for _, e := range validationErrors {
			errorMessages = append(errorMessages, fmt.Sprintf("%s: %s", e.Field(), e.Tag()))
		}
		handleError(w, r, oplog, errValidationFailed, fmt.Sprintf("%v", errorMessages))
		return
	}

	// validate any filters if they exist
	if alertRuleRequest.Rule.Filter != nil {
		if err := validateAlertFilter(ctx, r, alertRuleRequest.Rule.Filter); err != nil {
			handleError(w, r, oplog, errValidationFailed, err.Error())
			return
		}
	}
	if alertRuleRequest.Rule.DenominatorFilter != nil {
		if err := validateAlertFilter(ctx, r, alertRuleRequest.Rule.DenominatorFilter); err != nil {
			handleError(w, r, oplog, errValidationFailed, err.Error())
			return
		}
	}

	for i := range alertRuleRequest.Actions {

		// validate the alert action config
		if alertRuleRequest.Actions[i].Target == "pagerduty" {
			_, err := GetPagerDutyConfig(alertRuleRequest.Actions[i].Config)
			if err != nil {
				handleError(w, r, oplog, errValidationFailed, err.Error())
				return
			}
		} else if alertRuleRequest.Actions[i].Target == "webhook" {
			_, err := GetWebhookConfig(alertRuleRequest.Actions[i].Config)
			if err != nil {
				handleError(w, r, oplog, errValidationFailed, err.Error())
				return
			}
		}

		// if the action ID is not set, generate a new one
		if alertRuleRequest.Actions[i].ID == "" {
			alertRuleRequest.Actions[i].ID = uuid.New().String()
		}

		// if the action alert rule ID is not set, set it to the alert rule ID
		if alertRuleRequest.Actions[i].AlertRuleID == "" {
			alertRuleRequest.Actions[i].AlertRuleID = alertRuleRequest.Rule.ID
		}
	}

	if err := h.UpdateAlertRule(ctx, authInfo.TenantID, sessionID, alertRuleRequest.Rule.ID, &alertRuleRequest.Rule, &alertRuleRequest.Actions); err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, map[string]string{"message": "alert rule updated"})
}

// DeleteAlertRuleHandler godoc
// @Summary      Delete an alert rule
// @Description  Deletes an alert rule
// @Tags         alert_rules
// @Produce      json
// @Param        X-API-Key  header    string  true  "LangSmith API Key"
// @Param        X-Tenant-ID  header    string  true  "Tenant ID"
// @Param        session_id  path      string  true  "Session ID"
// @Param        alert_rule_id  path      string  true  "Alert rule ID"
// @Success      200  {object}  map[string]string{message=string} "Alert rule deleted"
// @Failure      400  {object}  ErrorResponse "Bad request"
// @Failure      403  {object}  ErrorResponse "Forbidden"
// @Failure      404  {object}  ErrorResponse "Not found"
// @Failure      503  {object}  ErrorResponse "Service unavailable"
// @Failure      500  {object}  ErrorResponse "Internal server error"
// @Router       /v1/platform/alerts/{session_id}/{alert_rule_id} [delete]
func (h *AlertRulesCrudHandler) DeleteAlertRuleHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	sessionID := chi.URLParam(r, "session_id")
	if sessionID == "" {
		handleError(w, r, oplog, errSessionIDRequired, "")
		return
	}

	alertRuleID := chi.URLParam(r, "alert_rule_id")
	if alertRuleID == "" {
		handleError(w, r, oplog, errAlertRuleIDRequired, "")
		return
	}

	err := h.DeleteAlertRule(ctx, authInfo.TenantID, sessionID, alertRuleID)
	if err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	oplog.Info(
		"Deleted alert rule",
		"session_id", sessionID,
		"tenant_id", authInfo.TenantID,
		"alert_rule_id", alertRuleID,
	)

	render.Status(r, http.StatusOK)
	render.JSON(w, r, map[string]string{"message": "alert rule deleted"})
}
