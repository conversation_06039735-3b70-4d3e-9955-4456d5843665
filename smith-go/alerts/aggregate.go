package alerts

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

const oneMinuteSeconds = 60

// helper to calculate the aggregations for the alert rule
func calculateAggregations(values []string, denominatorValues []string) (*AlertAggregation, error) {
	aggregations := AlertAggregation{}

	// Process values
	var floatValues []float64
	for _, val := range values {
		if val != "" {
			floatVal, err := strconv.ParseFloat(val, 64)
			if err != nil {
				return nil, err
			}
			floatValues = append(floatValues, floatVal)
		}
	}

	// Process denominator values
	denominatorFloatValues := []float64{}
	for _, val := range denominatorValues {
		if val != "" {
			floatVal, err := strconv.ParseFloat(val, 64)
			if err != nil {
				return nil, err
			}
			denominatorFloatValues = append(denominatorFloatValues, floatVal)
		}
	}

	// Calculate aggregations
	count := len(floatValues)
	sum := 0.0
	// TODO: Add more aggregations here
	if count > 0 {
		for _, val := range floatValues {
			sum += val
		}
	}
	denominatorCount := 0
	denominatorSum := 0.0
	if len(denominatorFloatValues) > 0 {
		denominatorCount = len(denominatorFloatValues)
		for _, val := range denominatorFloatValues {
			denominatorSum += val
		}
	}
	aggregations.Count = float64(count)
	aggregations.Sum = sum
	aggregations.DenominatorCount = float64(denominatorCount)
	aggregations.DenominatorSum = denominatorSum
	return &aggregations, nil
}

// get the aggregation value for the alert rule window
func getAggregationValue(aggregation *AlertAggregation, aggregationType string) float64 {
	switch aggregationType {
	case AvgAggregation:
		if aggregation.Count == 0 {
			return 0
		}
		return aggregation.Sum / aggregation.Count
	case SumAggregation:
		return aggregation.Sum
	case PctAggregation:
		if aggregation.DenominatorSum == 0 {
			return 0
		}
		return (aggregation.Sum / aggregation.DenominatorSum) * 100
	}
	return 0
}

// NewAlertsAggregationHandler creates a new AlertsAggregationHandler
func NewAlertsAggregationHandler(dbpool *database.AuditLoggedPool, routedRedisPools lsredis.RoutedRedisPools, cachingRedisPool redis.UniversalClient) *AlertsAggregationHandler {
	return &AlertsAggregationHandler{
		dbpool:           dbpool,
		routedRedisPools: routedRedisPools,
		cachingRedisPool: cachingRedisPool,
		cache:            lsredis.NewCache[string, *AlertRuleResponse](cachingRedisPool, "smith:alerts", 10*time.Minute),

		// Define PagerDuty action implementation
		FirePagerDutyActionFn: func(
			ctx context.Context,
			tenantID string,
			sessionID string,
			alertRule AlertRule,
			action AlertAction,
			triggeredMetricValue float64,
			triggeredThreshold float64,
			triggeredTime time.Time,
		) error {

			// get the pager duty config
			pagerDutyConfig, err := GetPagerDutyConfig(action.Config)
			if err != nil {
				return err
			}

			// Implement PagerDuty logic here
			pagerDutyRequestBody := map[string]interface{}{
				"routing_key":  pagerDutyConfig.IntegrationKey,
				"event_action": "trigger",
				"payload": map[string]interface{}{
					"summary":  alertRule.Name,
					"severity": pagerDutyConfig.Severity,
					"source":   "LangSmith",
					"custom_details": map[string]interface{}{
						"Description":         alertRule.Description,
						"Triggered Attribute": AttributeMapping[alertRule.Attribute],
						"Triggered Value":     triggeredMetricValue,
						"Alert Threshold":     triggeredThreshold,
					},
					"timestamp": triggeredTime.Format(time.RFC3339),
				},
				"dedup_key": fmt.Sprintf("langsmith-%s-%s", alertRule.ID, sessionID),
				"links": []map[string]string{
					// TODO: add a link with the time filter applied
					{
						"text": "View In LangSmith",
						"href": BuildAlertRuleUrl(tenantID, sessionID, alertRule.ID),
					},
					// TODO: add a link to the dashboard
				},
			}

			jsonData, err := json.Marshal(pagerDutyRequestBody)
			if err != nil {
				return err
			}

			resp, err := http.Post(
				"https://events.pagerduty.com/v2/enqueue",
				"application/json",
				bytes.NewBuffer(jsonData),
			)
			if err != nil {
				return err
			}
			if resp.StatusCode != 202 {
				return fmt.Errorf("failed to fire PagerDuty action: %s", resp.Status)
			}
			defer resp.Body.Close()
			return nil
		},

		// Define Webhook action implementation
		FireWebhookActionFn: func(
			ctx context.Context,
			tenantID string,
			sessionID string,
			alertRule AlertRule,
			action AlertAction,
			triggeredMetricValue float64,
			triggeredThreshold float64,
			triggeredTime time.Time,
		) error {
			// get the config string
			var configStr string
			err := json.Unmarshal(action.Config, &configStr)
			if err != nil {
				return fmt.Errorf("error unmarshalling webhook config: %w", err)
			}

			// get the webhook config
			actionConfig := map[string]interface{}{}
			err = json.Unmarshal([]byte(configStr), &actionConfig)
			if err != nil {
				return err
			}

			// extract values from the config
			webhookConfig, err := GetWebhookConfig(action.Config)
			if err != nil {
				return err
			}

			// Implement webhook logic here
			webhookRequestBody := map[string]interface{}{
				"project_name":           webhookConfig.ProjectName,
				"alert_rule_id":          alertRule.ID,
				"alert_rule_name":        alertRule.Name,
				"alert_rule_description": alertRule.Description,
				"alert_rule_type":        alertRule.Type,
				"alert_rule_attribute":   alertRule.Attribute,
				"triggered_metric_value": triggeredMetricValue,
				"triggered_threshold":    triggeredThreshold,
				"timestamp":              triggeredTime.Format(time.RFC3339),
				"alert_rule_url":         BuildAlertRuleUrl(tenantID, sessionID, alertRule.ID),
			}
			// merge the webhook body with the alert rule attributes``
			for k, v := range webhookConfig.Body {
				webhookRequestBody[k] = v
			}
			jsonData, err := json.Marshal(webhookRequestBody)
			if err != nil {
				return fmt.Errorf("error marshalling webhook request body: %w", err)
			}
			// Create a new request with body
			req, err := http.NewRequest("POST", webhookConfig.URL, bytes.NewBuffer(jsonData))
			if err != nil {
				return fmt.Errorf("error creating request: %w", err)
			}
			// Add custom headers from webhookHeaders
			for key, value := range webhookConfig.Headers {
				req.Header.Set(key, value)
			}
			// Execute the request
			client := &http.Client{}
			resp, err := client.Do(req)
			if err != nil {
				return fmt.Errorf("error sending webhook: %w", err)
			}
			if resp.StatusCode >= 400 {
				return fmt.Errorf("failed to fire webhook action: %s", resp.Status)
			}
			// close the response body
			defer resp.Body.Close()
			return nil
		},
	}
}

// get the alert rule and actions from the database
func (h *AlertsAggregationHandler) getAlertRuleAndActions(ctx context.Context, tenantID string, sessionID string, alertRuleID string) (*AlertRuleResponse, error) {

	rows, _ := h.dbpool.Query(ctx, `
		SELECT 
			id,
			name,
			description,
			type,
			attribute,
			aggregation,
			window_minutes,
			operator,
			threshold,
			threshold_window_minutes,
			threshold_multiplier,
			filter,
			denominator_filter,
			created_at,
			updated_at
		FROM alert_rules
		WHERE id = $1 AND session_id = $2 AND tenant_id = $3
	`, alertRuleID, sessionID, tenantID)

	alertRule, err := pgx.CollectOneRow(rows, pgx.RowToStructByPos[AlertRule])
	if err == pgx.ErrNoRows {
		return nil, errAlertRuleNotFound
	} else if err != nil {
		return nil, err
	}

	// get the alert rule actions from the database
	rows, _ = h.dbpool.Query(ctx, `
		SELECT
			id,
			alert_rule_id,
			target,
			config,
			created_at,
			updated_at
		FROM alert_actions
		WHERE alert_rule_id = $1 AND session_id = $2 AND tenant_id = $3
	`, alertRuleID, sessionID, tenantID)

	alertRuleActions, err := pgx.CollectRows(rows, pgx.RowToStructByPos[AlertAction])

	if err != nil {
		return nil, err
	}

	return &AlertRuleResponse{
		Rule:    alertRule,
		Actions: alertRuleActions,
	}, nil
}

// cache wrapped version of getAlertRuleAndActions
func (h *AlertsAggregationHandler) getAlertRuleAndActionsCached(ctx context.Context, tenantID string, sessionID string, alertRuleID string) (*AlertRuleResponse, error) {
	cacheKey := fmt.Sprintf("rule:%s:%s:%s", tenantID, sessionID, alertRuleID)
	alertRuleAndActions, err := h.cache.GetFresh(ctx, cacheKey, func() (*AlertRuleResponse, error) {
		return h.getAlertRuleAndActions(ctx, tenantID, sessionID, alertRuleID)
	})
	if err != nil {
		return nil, err
	}
	return alertRuleAndActions, nil
}

// aggregates the minute's worth of data for the alert rule into aggregations such as sum, count, etc.
func (h *AlertsAggregationHandler) aggregateAlertMinuteKey(ctx context.Context, tenantID string, alertRuleAndActions *AlertRuleResponse, minuteKey string) error {
	valuesKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleAndActions.Rule.ID, minuteKey)

	redisClient := h.routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

	// fetch the values from redis
	values, err := redisClient.LRange(ctx, valuesKey, 0, -1).Result()
	if err != nil && err != redis.Nil {
		return err
	}

	// fetch the denominator values from redis
	denominatorValues := []string{}
	denominatorValuesKey := fmt.Sprintf("smith:alerts:metrics:%s:%s:denominator", alertRuleAndActions.Rule.ID, minuteKey)
	if alertRuleAndActions.Rule.Aggregation == PctAggregation {
		denominatorValues, err = redisClient.LRange(ctx, denominatorValuesKey, 0, -1).Result()
		if err != nil && err != redis.Nil {
			return err
		}
	}

	// calculate the aggregations
	aggregations, err := calculateAggregations(values, denominatorValues)
	if err != nil {
		return err
	}

	// convert the aggregations to a map
	var mapping map[string]interface{}
	jsonBytes, _ := json.Marshal(aggregations)
	json.Unmarshal(jsonBytes, &mapping)

	// set the aggregations in redis
	aggregationsKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleAndActions.Rule.ID, minuteKey)
	for k, v := range mapping {
		// use HINCRBYFLOAT in order to update any existing aggregation state for the minute key
		_, err = redisClient.HIncrByFloat(ctx, aggregationsKey, k, v.(float64)).Result()
		if err != nil {
			return err
		}
	}

	// remove the aggregated metrics values from redis (except during tests)
	if len(values) > 0 && config.Env.LangchainEnv != "local_test" {
		err = redisClient.LTrim(ctx, valuesKey, int64(len(values)), -1).Err()
		if err != nil {
			return err
		}
	}
	if len(denominatorValues) > 0 && config.Env.LangchainEnv != "local_test" {
		err = redisClient.LTrim(ctx, denominatorValuesKey, int64(len(denominatorValues)), -1).Err()
		if err != nil {
			return err
		}
	}

	// Set expiration for the aggregations for that minute
	windowMinutes := alertRuleAndActions.Rule.WindowMinutes
	if alertRuleAndActions.Rule.Type == ChangeType {
		windowMinutes = *alertRuleAndActions.Rule.ThresholdWindowMinutes + alertRuleAndActions.Rule.WindowMinutes
	}
	expirationSeconds := (windowMinutes + config.Env.AggregateAlertRuleDelayMinutes) * oneMinuteSeconds
	_, err = redisClient.Expire(ctx, aggregationsKey, time.Duration(expirationSeconds)*time.Second).Result()
	if err != nil {
		return err
	}

	return nil
}

// checks if the alert rule should fire based on the latest minute aggregation
func (h *AlertsAggregationHandler) checkAlertRule(ctx context.Context, tenantID string, minuteKey string, alertRuleAndActions *AlertRuleResponse) (bool, float64, float64, error) {
	oplog := httplog.LogEntry(ctx)
	endMinute, err := time.Parse("2006-01-02-15-04", minuteKey)
	if err != nil {
		return false, 0, 0, err
	}

	if config.Env.LangchainEnv != "local_test" && alertRuleAndActions.Rule.Type == ChangeType && endMinute.Sub(alertRuleAndActions.Rule.UpdatedAt) < time.Duration(alertRuleAndActions.Rule.WindowMinutes+(*alertRuleAndActions.Rule.ThresholdWindowMinutes))*time.Minute {
		// change alert is warming up (not enough data yet)
		oplog.Debug(
			"Change alert is warming up",
			"alert_rule_id", alertRuleAndActions.Rule.ID,
			"end_minute", endMinute,
			"window_minutes", alertRuleAndActions.Rule.WindowMinutes,
			"threshold_window_minutes", *alertRuleAndActions.Rule.ThresholdWindowMinutes,
		)
		return false, 0, 0, nil
	}

	redisClient := h.routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)
	pipeline := redisClient.Pipeline()

	startMinute := endMinute.Add(-time.Duration(alertRuleAndActions.Rule.WindowMinutes) * time.Minute)
	for minute := endMinute; minute.After(startMinute); minute = minute.Add(-time.Minute) {
		minuteKey := minute.Format("2006-01-02-15-04")
		pipeline.HGetAll(ctx, fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleAndActions.Rule.ID, minuteKey))
	}
	results, err := pipeline.Exec(ctx)
	if err != nil {
		return false, 0, 0, err
	}

	// calculate the aggregation for the alert rule window
	windowAggregation := AlertAggregation{}
	for _, result := range results[:min(len(results), alertRuleAndActions.Rule.WindowMinutes)] {
		if result.Err() != nil {
			return false, 0, 0, result.Err()
		}

		// parse the aggregation from the result of the redis command
		minuteAggregation := AlertAggregation{}
		minuteAggregationMap := map[string]float64{}
		stringMap, err := result.(*redis.MapStringStringCmd).Result()
		if err != nil {
			return false, 0, 0, err
		}
		for k, v := range stringMap {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}
		jsonBytes, _ := json.Marshal(minuteAggregationMap)
		json.Unmarshal(jsonBytes, &minuteAggregation)

		// aggregate the minute aggregation into the window aggregation
		windowAggregation.Add(&minuteAggregation)
	}

	val := getAggregationValue(&windowAggregation, alertRuleAndActions.Rule.Aggregation)

	if alertRuleAndActions.Rule.Type == ThresholdType {
		if alertRuleAndActions.Rule.Operator == GreaterThanOrEqualToOperator {
			if val >= *alertRuleAndActions.Rule.Threshold {
				return true, val, *alertRuleAndActions.Rule.Threshold, nil
			}
		} else if alertRuleAndActions.Rule.Operator == LessThanOrEqualToOperator {
			if val <= *alertRuleAndActions.Rule.Threshold {
				return true, val, *alertRuleAndActions.Rule.Threshold, nil
			}
		}
	} else if alertRuleAndActions.Rule.Type == ChangeType {
		// calculate the change threshold - which is the
		oplog.Debug(
			"Calculating change threshold",
			"alert_rule_id", alertRuleAndActions.Rule.ID,
			"end_minute", endMinute,
			"threshold_window_minutes", *alertRuleAndActions.Rule.ThresholdWindowMinutes,
			"window_minutes", alertRuleAndActions.Rule.WindowMinutes,
		)
		endChangeThresholdMinute := endMinute.Add(-time.Duration(*alertRuleAndActions.Rule.ThresholdWindowMinutes) * time.Minute)
		startChangeThresholdMinute := endChangeThresholdMinute.Add(-time.Duration(alertRuleAndActions.Rule.WindowMinutes) * time.Minute)

		for minute := endChangeThresholdMinute; minute.After(startChangeThresholdMinute); minute = minute.Add(-time.Minute) {
			minuteKey := minute.Format("2006-01-02-15-04")
			pipeline.HGetAll(ctx, fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleAndActions.Rule.ID, minuteKey))
		}

		results, err := pipeline.Exec(ctx)
		if err != nil {
			return false, 0, 0, err
		}

		// check if there at least one result
		numEmptyResults := 0
		for _, result := range results {
			mapResult, err := result.(*redis.MapStringStringCmd).Result()
			if err != nil {
				return false, 0, 0, err
			}
			if len(mapResult) == 0 {
				numEmptyResults++
			}
		}

		// if there are no results, we cannot calculate the change threshold so skip the alert
		if numEmptyResults == len(results) {
			oplog.Info("No results found for the alert rule window yet")
			return false, 0, 0, nil
		}

		// calculate the aggregation for the alert rule window
		changeThresholdAggregation := AlertAggregation{}
		for _, result := range results[:min(len(results), alertRuleAndActions.Rule.WindowMinutes)] {
			if result.Err() != nil && result.Err() != redis.Nil {
				return false, 0, 0, result.Err()
			} else if result.Err() == redis.Nil {
				continue
			}
			// parse the aggregation from the result of the redis command
			minuteAggregation := AlertAggregation{}
			minuteAggregationMap := map[string]float64{}
			stringMap, err := result.(*redis.MapStringStringCmd).Result()
			if err != nil {
				return false, 0, 0, err
			}
			for k, v := range stringMap {
				floatVal, _ := strconv.ParseFloat(v, 64)
				minuteAggregationMap[k] = floatVal
			}
			jsonBytes, _ := json.Marshal(minuteAggregationMap)
			json.Unmarshal(jsonBytes, &minuteAggregation)
			// aggregate the minute aggregation into the window aggregation
			changeThresholdAggregation.Add(&minuteAggregation)
		}
		changeThreshold := *alertRuleAndActions.Rule.ThresholdMultiplier * getAggregationValue(&changeThresholdAggregation, alertRuleAndActions.Rule.Aggregation)
		oplog.Debug(
			"Change Threshold",
			"alert_rule_id", alertRuleAndActions.Rule.ID,
			"change_threshold", changeThreshold,
			"val", val,
		)
		if alertRuleAndActions.Rule.Operator == GreaterThanOrEqualToOperator {
			if val >= changeThreshold {

				return true, val, changeThreshold, nil
			}
		} else if alertRuleAndActions.Rule.Operator == LessThanOrEqualToOperator {
			if val <= changeThreshold {
				return true, val, changeThreshold, nil
			}
		}
	}
	return false, 0, 0, nil
}

// fires the actions for the alert rule
func (h *AlertsAggregationHandler) fireActions(ctx context.Context, tenantID string, sessionID string, alertRuleAndActions *AlertRuleResponse, triggeredMetricValue float64, triggeredThreshold float64, triggeredTime time.Time) error {
	oplog := httplog.LogEntry(ctx)
	for _, action := range alertRuleAndActions.Actions {
		var err error
		switch action.Target {
		case PagerDutyActionTarget:
			err = h.FirePagerDutyActionFn(ctx, tenantID, sessionID, alertRuleAndActions.Rule, action, triggeredMetricValue, triggeredThreshold, triggeredTime)
			if err != nil {
				return err
			}
			oplog.Info(
				"Fired PagerDuty action",
				"for the alert rule", alertRuleAndActions.Rule.ID,
				"triggered_metric_value", triggeredMetricValue,
				"triggered_threshold", triggeredThreshold,
				"triggered_time", triggeredTime,
			)
		case WebhookActionTarget:
			err = h.FireWebhookActionFn(ctx, tenantID, sessionID, alertRuleAndActions.Rule, action, triggeredMetricValue, triggeredThreshold, triggeredTime)
			if err != nil {
				return err
			}
			oplog.Info(
				"Fired Webhook action",
				"for the alert rule", alertRuleAndActions.Rule.ID,
				"triggered_metric_value", triggeredMetricValue,
				"triggered_threshold", triggeredThreshold,
				"triggered_time", triggeredTime,
			)
		}
		if err != nil {
			oplog.Error("Error firing action", "action", action.ID, "for the alert rule", alertRuleAndActions.Rule.ID, "error", err.Error())
			continue
		}
	}
	return nil
}

// handles the request to aggregate the alerts for a given minute key and checks if the alert rule should fire based on the aggregation for that minute key
func (h *AlertsAggregationHandler) AggregatedAlerts(w http.ResponseWriter, r *http.Request) {
	// Get tenant id header
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	tenantID := r.Header.Get("X-Tenant-ID")
	if tenantID == "" {
		handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	var request AggregatedAlertsRequest
	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		handleError(w, r, oplog, errBadRequest, "")
		return
	}

	sessionID := request.SessionID
	alertRuleID := request.AlertRuleID
	minuteKey := request.MinuteKey

	oplog.Info(
		"Aggregating alerts for",
		"tenant_id", tenantID,
		"session_id", sessionID,
		"alert_rule_id", alertRuleID,
		"minute_key", minuteKey,
	)

	alertRuleAndActions, err := h.getAlertRuleAndActionsCached(ctx, tenantID, sessionID, alertRuleID)
	if err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	startTime := time.Now()
	err = h.aggregateAlertMinuteKey(ctx, tenantID, alertRuleAndActions, minuteKey)
	oplog.Info("Aggregate alert minute key", "duration", time.Since(startTime).Milliseconds())
	if err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	startTime = time.Now()
	fire, triggeredMetricValue, triggeredThreshold, err := h.checkAlertRule(ctx, tenantID, minuteKey, alertRuleAndActions)
	oplog.Info("Check alert rule", "duration", time.Since(startTime).Milliseconds())
	if err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	if fire {
		// check the dedup key to see if the alert rule has already fired in the window containing the current minute
		shouldDedup := false
		cacheKey := fmt.Sprintf("smith:alerts:dedup:%s:%s", sessionID, alertRuleAndActions.Rule.ID)
		dedup, err := h.cachingRedisPool.Get(ctx, cacheKey).Result()
		if err != nil && err != redis.Nil {
			handleError(w, r, oplog, err, err.Error())
			return
		}
		if dedup == "true" {
			shouldDedup = true
		}
		// return early if the alert rule should be deduped
		if shouldDedup {
			oplog.Debug(
				"Alert rule should be deduped, skipping fire",
				"alert_rule_id", alertRuleAndActions.Rule.ID,
				"session_id", sessionID,
				"minute_key", minuteKey,
			)
			render.Status(r, http.StatusOK)
			render.JSON(w, r, map[string]string{"message": "Alerts aggregated successfully"})
			return
		}

		// fire the actions for the alert rule
		triggeredTime, err := time.Parse("2006-01-02-15-04", minuteKey)
		if err != nil {
			handleError(w, r, oplog, err, err.Error())
			return
		}
		err = h.fireActions(
			ctx,
			tenantID,
			sessionID,
			alertRuleAndActions,
			triggeredMetricValue,
			triggeredThreshold,
			triggeredTime,
		)
		if err != nil {
			handleError(w, r, oplog, err, err.Error())
			return
		}

		// set the dedup key to true to prevent the alert rule from firing again in the window containing the current minute
		h.cachingRedisPool.Set(ctx, cacheKey, "true", time.Duration(alertRuleAndActions.Rule.WindowMinutes)*time.Minute)
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, map[string]string{"message": "Alerts aggregated successfully"})
}

// TestAlertAction godoc
// @Summary      Test an alert action to determine if configuration is valid
// @Description  Tests an alert action which will fire a notification to all configured recipients if the configuration is valid.
// @Tags         alert_rules
// @Produce      json
// @Param        X-API-Key  header    string  true  "LangSmith API Key"
// @Param        X-Tenant-ID  header    string  true  "Tenant ID"
// @Param        session_id  path      string  true  "Session ID"
// @Param        alert_rule_id  path      string  true  "Alert rule ID"
// @Success      200  {object}  map[string]string{message=string} "Alert action fired successfully"
// @Failure      400  {object}  ErrorResponse "Bad request"
// @Failure      403  {object}  ErrorResponse "Forbidden"
// @Failure      503  {object}  ErrorResponse "Service unavailable"
// @Failure      500  {object}  ErrorResponse "Internal server error"
// @Router       /v1/platform/alerts/{session_id}/test [post]
func (h *AlertsAggregationHandler) TestAlertAction(w http.ResponseWriter, r *http.Request) {
	// Get tenant id header
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	tenantID := r.Header.Get("X-Tenant-ID")
	if tenantID == "" {
		handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	sessionID := chi.URLParam(r, "session_id")
	if sessionID == "" {
		handleError(w, r, oplog, errSessionIDRequired, "")
		return
	}

	var request CreateAlertRuleRequest
	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		handleError(w, r, oplog, errBadRequest, "")
		return
	}

	// set up the test alert rule and action
	alertRuleAndActions := AlertRuleResponse{
		Rule: AlertRule{
			AlertRuleBase: AlertRuleBase{
				ID:            "00000000-0000-0000-0000-000000000000",
				Name:          "[Test] " + request.Rule.Name,
				Description:   "[Test] " + request.Rule.Description,
				Type:          request.Rule.Type,
				Attribute:     request.Rule.Attribute,
				Aggregation:   request.Rule.Aggregation,
				WindowMinutes: request.Rule.WindowMinutes,
				Operator:      request.Rule.Operator,
				Threshold:     request.Rule.Threshold,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Actions: []AlertAction{},
	}

	for _, action := range request.Actions {

		// validate the alert action config
		if action.Target == "pagerduty" {
			_, err := GetPagerDutyConfig(action.Config)
			if err != nil {
				handleError(w, r, oplog, errValidationFailed, err.Error())
				return
			}
		} else if action.Target == "webhook" {
			_, err := GetWebhookConfig(action.Config)
			if err != nil {
				handleError(w, r, oplog, errValidationFailed, err.Error())
				return
			}
		}

		alertRuleAndActions.Actions = append(alertRuleAndActions.Actions, AlertAction{
			AlertActionBase: AlertActionBase{
				ID:          "00000000-0000-0000-0000-000000000000",
				AlertRuleID: "00000000-0000-0000-0000-000000000000",
				Target:      action.Target,
				Config:      action.Config,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		})
	}

	// run fire actions
	err = h.fireActions(
		ctx,
		tenantID,
		sessionID,
		&alertRuleAndActions,
		0,
		0,
		time.Now(),
	)
	if err != nil {
		handleError(w, r, oplog, err, err.Error())
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, map[string]string{"message": "Alert action fired successfully"})
}
