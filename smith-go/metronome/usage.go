package metronome

import (
	"context"
	"encoding/json"
	"fmt"
	"maps"
	"slices"
	"time"
)

const TenantGroupKey = "tenant_id"

type BillableMetricId = string
type MonthKey = string

type CustomerUsage struct {
	CustomerId         string             `json:"customer_id"`
	BillableMetricId   BillableMetricId   `json:"billable_metric_id"`
	BillableMetricName string             `json:"billable_metric_name"`
	StartTimestamp     time.Time          `json:"start_timestamp"`
	EndTimestamp       time.Time          `json:"end_timestamp"`
	Value              *float64           `json:"value"`
	Groups             map[string]float64 `json:"groups"`
}

type CustomerUsageResponse struct {
	Data     []CustomerUsage `json:"data"`
	NextPage *string         `json:"next_page"`
}

type CustomerUsageGroupBy struct {
	Id      BillableMetricId `json:"id"`
	GroupBy *GroupBy         `json:"group_by,omitempty"`
}

type GroupBy struct {
	Key string `json:"key"`
}

type BillableMetric struct {
	BillableMetricId BillableMetricId `json:"id"`
	Name             string           `json:"name"`
	GroupKeys        [][]string       `json:"group_keys"`
}

type GetBillableMetricsResponse struct {
	Data []BillableMetric `json:"data"`
}

type UsageRequest struct {
	StartingOn    time.Time `in:"query=starting_on"`
	EndingBefore  time.Time `in:"query=ending_before"`
	OnCurrentPlan bool      `in:"query=on_current_plan"`
}

func GetUsage(ctx context.Context, metronomeClient *MetronomeClient, metronomeCustomerId string, startingOn time.Time, endingBefore time.Time, billableMetrics []CustomerUsageGroupBy, nextPage *string) (*CustomerUsageResponse, error) {
	// avoid missing usage that has been submitted for the current day
	endingBefore = endingBefore.AddDate(0, 0, 1)

	// metronome only accepts dates in UTC at midnight, so we must truncate dates here
	payload := map[string]any{
		"window_size":      "day",
		"starting_on":      time.Date(startingOn.Year(), startingOn.Month(), startingOn.Day(), 0, 0, 0, 0, startingOn.Location()).Format(time.RFC3339),
		"ending_before":    time.Date(endingBefore.Year(), endingBefore.Month(), endingBefore.Day(), 0, 0, 0, 0, endingBefore.Location()).Format(time.RFC3339),
		"customer_ids":     []string{metronomeCustomerId},
		"billable_metrics": billableMetrics,
	}

	endpoint := "usage"
	if nextPage != nil && *nextPage != "" {
		endpoint = fmt.Sprintf("usage?next_page=%s", *nextPage)
	}

	usageDict, err := metronomeClient.client.Post(ctx, endpoint, payload)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch usage details from metronome: %w", err)
	}
	var response CustomerUsageResponse

	jsonBytes, err := json.Marshal(usageDict)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal usage details from metronome to JSON: %w", err)
	}

	err = json.Unmarshal(jsonBytes, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal usage details from metronome JSON to struct: %w", err)
	}
	return &response, nil
}

func GetBillableMetrics(ctx context.Context, metronomeClient *MetronomeClient, metronomeCustomerId string, onCurrentPlan bool) (*GetBillableMetricsResponse, error) {

	result, err := metronomeClient.client.Get(ctx, fmt.Sprintf("customers/%s/billable-metrics", metronomeCustomerId), map[string]string{"on_current_plan": fmt.Sprintf("%t", onCurrentPlan)})
	if err != nil {
		return nil, fmt.Errorf("failed to fetch billable metrics from metronome: %w", err)
	}

	var response GetBillableMetricsResponse

	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal billable metrics from metronome to JSON: %w", err)
	}

	err = json.Unmarshal(jsonBytes, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal billable metrics from metronome JSON to struct: %w", err)
	}

	return &response, nil
}

func GetMonthlyUsage(ctx context.Context, metronomeClient *MetronomeClient, metronomeCustomerId string, startingOn time.Time, endingBefore time.Time, onCurrentPlan bool) ([]CustomerUsage, error) {
	// before querying metronome for usage, we query metronome to get a list of available billable metrics and corresponding group keys for the customer
	// we send this data when we eventually call GetUsage
	billableMetricsResponse, err := GetBillableMetrics(ctx, metronomeClient, metronomeCustomerId, onCurrentPlan)
	if err != nil {
		return nil, err
	}
	groupByPayload := getGroupByPayload(billableMetricsResponse.Data)
	monthlyUsage := make([]CustomerUsage, 0)

	usageMap := make(map[MonthKey]map[BillableMetricId]*CustomerUsage)

	nextPage := (*string)(nil)
	hasMorePages := true

	for hasMorePages {
		customerUsageResponse, err := GetUsage(ctx, metronomeClient, metronomeCustomerId, startingOn, endingBefore, groupByPayload, nextPage)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch usage details from metronome: %w", err)
		}

		aggregateUsageByMonth(customerUsageResponse.Data, usageMap)

		if customerUsageResponse.NextPage == nil {
			hasMorePages = false
		} else {
			nextPage = customerUsageResponse.NextPage
		}
	}

	for _, billableMetricToUsage := range usageMap {
		for _, usageAggregate := range billableMetricToUsage {
			monthlyUsage = append(monthlyUsage, *usageAggregate)
		}
	}

	return monthlyUsage, nil
}

// when we query metronome usage, we have to pass a list of billable_metric_ids and their corresponding group keys if we want to group by a certain field
// in our case, we want to group by tenant_id for all billable metrics that attach a tenant_id when we submit usage
// this helper function returns the final list of billable_metric_ids and the corresponding tenant_id group key if available
func getGroupByPayload(billableMetrics []BillableMetric) []CustomerUsageGroupBy {
	billableMetricsIdsWithTenantGroupKey := getBillableMetricIdsWithTenantGroupKey(billableMetrics)
	groupByPayload := make([]CustomerUsageGroupBy, 0)
	for _, billableMetric := range billableMetrics {
		if slices.Contains(billableMetricsIdsWithTenantGroupKey, billableMetric.BillableMetricId) {
			groupByPayload = append(groupByPayload, CustomerUsageGroupBy{Id: billableMetric.BillableMetricId, GroupBy: &GroupBy{Key: TenantGroupKey}})
		} else {
			groupByPayload = append(groupByPayload, CustomerUsageGroupBy{Id: billableMetric.BillableMetricId})
		}
	}

	return groupByPayload
}

// not all billable metrics can be grouped by tenant_id, and metronome will return an error if we attempt to group by tenant_id to an unsupported billable metric
// this helper function finds the list of billable metric ids where we can group by tenant_id
func getBillableMetricIdsWithTenantGroupKey(billableMetrics []BillableMetric) []BillableMetricId {
	billableMetricIdsWithTenantGroupKey := make([]BillableMetricId, 0)
	for _, billableMetric := range billableMetrics {
		if len(billableMetric.GroupKeys) > 0 {
			for _, groupKey := range billableMetric.GroupKeys {
				if len(groupKey) == 1 && groupKey[0] == TenantGroupKey {
					billableMetricIdsWithTenantGroupKey = append(billableMetricIdsWithTenantGroupKey, billableMetric.BillableMetricId)
				}
			}
		}
	}
	return billableMetricIdsWithTenantGroupKey
}

func aggregateUsageByMonth(usage []CustomerUsage, usageMap map[MonthKey]map[BillableMetricId]*CustomerUsage) {
	for _, u := range usage {
		month := time.Date(u.StartTimestamp.Year(), u.StartTimestamp.Month(), 1, 0, 0, 0, 0, time.UTC)
		nextMonth := month.AddDate(0, 1, 0)
		monthKey := month.Format("2006-01")

		if _, exists := usageMap[monthKey]; !exists {
			usageMap[monthKey] = make(map[BillableMetricId]*CustomerUsage)
		}

		existingUsage, exists := usageMap[monthKey][u.BillableMetricId]
		if !exists {
			newUsage := &CustomerUsage{
				CustomerId:         u.CustomerId,
				BillableMetricId:   u.BillableMetricId,
				BillableMetricName: u.BillableMetricName,
				StartTimestamp:     month,
				EndTimestamp:       nextMonth,
			}

			initializeValue(newUsage, u.Value)
			initializeGroups(newUsage, u.Groups)

			usageMap[monthKey][u.BillableMetricId] = newUsage
		} else {
			updateValue(existingUsage, u.Value)
			updateGroups(existingUsage, u.Groups)
		}
	}
}

// below are all helpers for aggregating usage by month
func initializeValue(target *CustomerUsage, source *float64) {
	if source != nil {
		newValue := new(float64)
		*newValue = *source
		target.Value = newValue
	}
}

func updateValue(target *CustomerUsage, source *float64) {
	if source != nil {
		if target.Value == nil {
			initializeValue(target, source)
		} else {
			*target.Value += *source
		}
	}
}

func initializeGroups(target *CustomerUsage, source map[string]float64) {
	if source != nil && len(source) > 0 {
		target.Groups = make(map[string]float64)
		maps.Copy(target.Groups, source)
	}
}

func updateGroups(target *CustomerUsage, source map[string]float64) {
	if source != nil && len(source) > 0 {
		if target.Groups == nil {
			initializeGroups(target, source)
		} else {
			for k, v := range source {
				_, ok := target.Groups[k]
				if ok {
					target.Groups[k] += v
				} else {
					target.Groups[k] = v
				}
			}
		}
	}
}
