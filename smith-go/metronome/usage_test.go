package metronome

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGetBillableMetricsIdsWithTenantGroupKey(t *testing.T) {
	tests := []struct {
		name            string
		billableMetrics []BillableMetric
		expected        []BillableMetricId
	}{
		{
			name: "No metrics with tenant group key",
			billableMetrics: []BillableMetric{
				{
					BillableMetricId: "metric-1",
					Name:             "Metric 1",
					GroupKeys:        [][]string{},
				},
				{
					BillableMetricId: "metric-2",
					Name:             "Metric 2",
					GroupKeys:        [][]string{{"other_key"}},
				},
			},
			expected: []BillableMetricId{},
		},
		{
			name: "One metric with tenant group key",
			billableMetrics: []BillableMetric{
				{
					BillableMetricId: "metric-1",
					Name:             "Metric 1",
					GroupKeys:        [][]string{{"tenant_id"}},
				},
				{
					BillableMetricId: "metric-2",
					Name:             "Metric 2",
					GroupKeys:        [][]string{{"other_key"}},
				},
			},
			expected: []BillableMetricId{"metric-1"},
		},
		{
			name: "Multiple metrics with tenant group key",
			billableMetrics: []BillableMetric{
				{
					BillableMetricId: "metric-1",
					Name:             "Metric 1",
					GroupKeys:        [][]string{{"tenant_id"}},
				},
				{
					BillableMetricId: "metric-2",
					Name:             "Metric 2",
					GroupKeys:        [][]string{{"tenant_id"}},
				},
				{
					BillableMetricId: "metric-3",
					Name:             "Metric 3",
					GroupKeys:        [][]string{{"other_key"}},
				},
			},
			expected: []BillableMetricId{"metric-1", "metric-2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getBillableMetricIdsWithTenantGroupKey(tt.billableMetrics)
			assert.ElementsMatch(t, tt.expected, result)
		})
	}
}

func TestGetGroupByPayload(t *testing.T) {
	tests := []struct {
		name            string
		billableMetrics []BillableMetric
		expected        []CustomerUsageGroupBy
	}{
		{
			name: "No metrics with tenant group key",
			billableMetrics: []BillableMetric{
				{
					BillableMetricId: "metric-1",
					Name:             "Metric 1",
					GroupKeys:        [][]string{},
				},
				{
					BillableMetricId: "metric-2",
					Name:             "Metric 2",
					GroupKeys:        [][]string{{"other_key"}},
				},
			},
			expected: []CustomerUsageGroupBy{
				{Id: "metric-1"},
				{Id: "metric-2"},
			},
		},
		{
			name: "One metric with tenant group key",
			billableMetrics: []BillableMetric{
				{
					BillableMetricId: "metric-1",
					Name:             "Metric 1",
					GroupKeys:        [][]string{{"tenant_id"}},
				},
				{
					BillableMetricId: "metric-2",
					Name:             "Metric 2",
					GroupKeys:        [][]string{{"other_key"}},
				},
			},
			expected: []CustomerUsageGroupBy{
				{Id: "metric-1", GroupBy: &GroupBy{Key: "tenant_id"}},
				{Id: "metric-2"},
			},
		},
		{
			name: "Multiple metrics with tenant group key",
			billableMetrics: []BillableMetric{
				{
					BillableMetricId: "metric-1",
					Name:             "Metric 1",
					GroupKeys:        [][]string{{"tenant_id"}},
				},
				{
					BillableMetricId: "metric-2",
					Name:             "Metric 2",
					GroupKeys:        [][]string{{"tenant_id"}},
				},
				{
					BillableMetricId: "metric-3",
					Name:             "Metric 3",
					GroupKeys:        [][]string{{"other_key"}},
				},
			},
			expected: []CustomerUsageGroupBy{
				{Id: "metric-1", GroupBy: &GroupBy{Key: "tenant_id"}},
				{Id: "metric-2", GroupBy: &GroupBy{Key: "tenant_id"}},
				{Id: "metric-3"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getGroupByPayload(tt.billableMetrics)

			// Check that the lengths match
			assert.Equal(t, len(tt.expected), len(result))

			// Create maps to make comparison easier
			expectedMap := make(map[string]*GroupBy)
			for _, item := range tt.expected {
				expectedMap[item.Id] = item.GroupBy
			}

			resultMap := make(map[string]*GroupBy)
			for _, item := range result {
				resultMap[item.Id] = item.GroupBy
			}

			// Compare the maps
			for id, expectedGroupBy := range expectedMap {
				resultGroupBy, exists := resultMap[id]
				assert.True(t, exists, "Expected metric ID %s not found in result", id)

				if expectedGroupBy == nil {
					assert.Nil(t, resultGroupBy)
				} else {
					assert.NotNil(t, resultGroupBy)
					assert.Equal(t, expectedGroupBy.Key, resultGroupBy.Key)
				}
			}
		})
	}
}

// When we do not have a group key for the metric, usage will be grouped by org instead and stored on the value field
func TestAggregateUsageByMonthValue(t *testing.T) {
	customerId := "d7abd0cd-4ae9-4db7-8676-e986a4ebd8dc"
	metricId1 := "metric-1"
	metricId2 := "metric-2"

	jan1 := time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)
	jan15 := time.Date(2021, 1, 15, 0, 0, 0, 0, time.UTC)
	feb1 := time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC)

	value1 := float64(100)
	value2 := float64(200)
	value3 := float64(300)

	tests := []struct {
		name     string
		usages   [][]CustomerUsage // Multiple slices to simulate pagination
		expected map[MonthKey]map[BillableMetricId]*CustomerUsage
	}{
		{
			name: "Basic aggregation with values",
			usages: [][]CustomerUsage{
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              &value1,
						Groups:             nil,
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan15,
						EndTimestamp:       jan15.AddDate(0, 0, 1),
						Value:              &value2,
						Groups:             nil,
					},
				},
			},
			expected: map[MonthKey]map[BillableMetricId]*CustomerUsage{
				"2021-01": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              floatPtr(value1 + value2),
						Groups:             nil,
					},
				},
			},
		},
		{
			name: "Multiple months with values",
			usages: [][]CustomerUsage{
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              &value1,
						Groups:             nil,
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     feb1,
						EndTimestamp:       feb1.AddDate(0, 0, 1),
						Value:              &value2,
						Groups:             nil,
					},
				},
			},
			expected: map[MonthKey]map[BillableMetricId]*CustomerUsage{
				"2021-01": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              floatPtr(value1),
						Groups:             nil,
					},
				},
				"2021-02": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 3, 1, 0, 0, 0, 0, time.UTC),
						Value:              floatPtr(value2),
						Groups:             nil,
					},
				},
			},
		},
		{
			name: "Nil values",
			usages: [][]CustomerUsage{
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              &value1,
						Groups:             nil,
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan15,
						EndTimestamp:       jan15.AddDate(0, 0, 1),
						Value:              nil,
						Groups:             nil,
					},
				},
			},
			expected: map[MonthKey]map[BillableMetricId]*CustomerUsage{
				"2021-01": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              floatPtr(value1),
						Groups:             nil,
					},
				},
			},
		},
		{
			name: "Pagination simulation with values",
			usages: [][]CustomerUsage{
				// First page
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              &value1,
						Groups:             nil,
					},
				},
				// Second page
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan15,
						EndTimestamp:       jan15.AddDate(0, 0, 1),
						Value:              &value2,
						Groups:             nil,
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     feb1,
						EndTimestamp:       feb1.AddDate(0, 0, 1),
						Value:              &value3,
						Groups:             nil,
					},
				},
			},
			expected: map[MonthKey]map[BillableMetricId]*CustomerUsage{
				"2021-01": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              floatPtr(value1 + value2),
						Groups:             nil,
					},
				},
				"2021-02": {
					metricId2: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 3, 1, 0, 0, 0, 0, time.UTC),
						Value:              floatPtr(value3),
						Groups:             nil,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usageMap := make(map[MonthKey]map[BillableMetricId]*CustomerUsage)

			for _, usages := range tt.usages {
				aggregateUsageByMonth(usages, usageMap)
			}

			assert.Equal(t, len(tt.expected), len(usageMap), "Number of months should match")

			for month, expectedMetrics := range tt.expected {
				actualMetrics, exists := usageMap[month]
				assert.True(t, exists, "Month %s should exist in results", month)

				assert.Equal(t, len(expectedMetrics), len(actualMetrics), "Number of metrics for month %s should match", month)

				for metricId, expectedUsage := range expectedMetrics {
					actualUsage, exists := actualMetrics[metricId]
					assert.True(t, exists, "Metric %s should exist for month %s", metricId, month)

					if expectedUsage.Value == nil {
						assert.Nil(t, actualUsage.Value, "Value should be nil for month %s, metric %s", month, metricId)
					} else {
						assert.NotNil(t, actualUsage.Value, "Value should not be nil for month %s, metric %s", month, metricId)
						assert.Equal(t, *expectedUsage.Value, *actualUsage.Value, "Value should match for month %s, metric %s", month, metricId)
					}

					assert.Equal(t, expectedUsage.CustomerId, actualUsage.CustomerId, "CustomerId should match")
					assert.Equal(t, expectedUsage.BillableMetricId, actualUsage.BillableMetricId, "BillableMetricId should match")
					assert.Equal(t, expectedUsage.BillableMetricName, actualUsage.BillableMetricName, "BillableMetricName should match")
					assert.Equal(t, expectedUsage.StartTimestamp.Format(time.RFC3339), actualUsage.StartTimestamp.Format(time.RFC3339), "StartTimestamp should match")
					assert.Equal(t, expectedUsage.EndTimestamp.Format(time.RFC3339), actualUsage.EndTimestamp.Format(time.RFC3339), "EndTimestamp should match")
				}
			}
		})
	}
}

// When we use a group by key, usage will be stored in the Groups field
func TestAggregateUsageByMonthGroups(t *testing.T) {
	customerId := "d7abd0cd-4ae9-4db7-8676-e986a4ebd8dc"
	metricId1 := "metric-1"
	metricId2 := "metric-2"
	metricId3 := "metric-3"

	jan1 := time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)
	jan15 := time.Date(2021, 1, 15, 0, 0, 0, 0, time.UTC)
	feb1 := time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC)
	mar1 := time.Date(2021, 3, 1, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name     string
		usages   [][]CustomerUsage // Multiple slices to simulate pagination
		expected map[MonthKey]map[BillableMetricId]*CustomerUsage
	}{
		{
			name: "Basic groups aggregation",
			usages: [][]CustomerUsage{
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 50,
							"tenant_id:456": 50,
						},
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan15,
						EndTimestamp:       jan15.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 100,
							"tenant_id:789": 100,
						},
					},
				},
			},
			expected: map[MonthKey]map[BillableMetricId]*CustomerUsage{
				"2021-01": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 150, // 50 + 100
							"tenant_id:456": 50,
							"tenant_id:789": 100,
						},
					},
				},
			},
		},
		{
			name: "Multiple metrics with groups",
			usages: [][]CustomerUsage{
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 50,
							"tenant_id:456": 75,
						},
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 25,
							"tenant_id:789": 30,
						},
					},
				},
			},
			expected: map[MonthKey]map[BillableMetricId]*CustomerUsage{
				"2021-01": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 50,
							"tenant_id:456": 75,
						},
					},
					metricId2: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 25,
							"tenant_id:789": 30,
						},
					},
				},
			},
		},
		{
			name: "Comprehensive groups test - multiple metrics, months, and groups",
			usages: [][]CustomerUsage{
				// First page - January data
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan1,
						EndTimestamp:       jan1.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 50,
							"tenant_id:456": 75,
						},
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     jan15,
						EndTimestamp:       jan15.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 25,
							"tenant_id:789": 30,
						},
					},
				},
				// Second page - More January and February data
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     jan15,
						EndTimestamp:       jan15.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 100,
							"tenant_id:456": 25,
							"tenant_id:789": 50,
						},
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     feb1,
						EndTimestamp:       feb1.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 75,
							"tenant_id:456": 40,
						},
					},
				},
				// Third page - March data with new metric
				{
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId3,
						BillableMetricName: "Metric 3",
						StartTimestamp:     mar1,
						EndTimestamp:       mar1.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 60,
							"tenant_id:456": 70,
							"tenant_id:789": 80,
						},
					},
					{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     mar1,
						EndTimestamp:       mar1.AddDate(0, 0, 1),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 90,
							"tenant_id:999": 100,
						},
					},
				},
			},
			expected: map[MonthKey]map[BillableMetricId]*CustomerUsage{
				"2021-01": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 150, // 50 + 100
							"tenant_id:456": 100, // 75 + 25
							"tenant_id:789": 50,
						},
					},
					metricId2: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 25,
							"tenant_id:789": 30,
						},
					},
				},
				"2021-02": {
					metricId2: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId2,
						BillableMetricName: "Metric 2",
						StartTimestamp:     time.Date(2021, 2, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 3, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 75,
							"tenant_id:456": 40,
						},
					},
				},
				"2021-03": {
					metricId1: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId1,
						BillableMetricName: "Metric 1",
						StartTimestamp:     time.Date(2021, 3, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 4, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 90,
							"tenant_id:999": 100,
						},
					},
					metricId3: &CustomerUsage{
						CustomerId:         customerId,
						BillableMetricId:   metricId3,
						BillableMetricName: "Metric 3",
						StartTimestamp:     time.Date(2021, 3, 1, 0, 0, 0, 0, time.UTC),
						EndTimestamp:       time.Date(2021, 4, 1, 0, 0, 0, 0, time.UTC),
						Value:              nil,
						Groups: map[string]float64{
							"tenant_id:123": 60,
							"tenant_id:456": 70,
							"tenant_id:789": 80,
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usageMap := make(map[MonthKey]map[BillableMetricId]*CustomerUsage)

			for _, usages := range tt.usages {
				aggregateUsageByMonth(usages, usageMap)
			}

			assert.Equal(t, len(tt.expected), len(usageMap), "Number of months should match")

			for month, expectedMetrics := range tt.expected {
				actualMetrics, exists := usageMap[month]
				assert.True(t, exists, "Month %s should exist in results", month)

				assert.Equal(t, len(expectedMetrics), len(actualMetrics), "Number of metrics for month %s should match", month)

				for metricId, expectedUsage := range expectedMetrics {
					actualUsage, exists := actualMetrics[metricId]
					assert.True(t, exists, "Metric %s should exist for month %s", metricId, month)

					if expectedUsage.Groups == nil {
						assert.Nil(t, actualUsage.Groups, "Groups should be nil for month %s, metric %s", month, metricId)
					} else {
						assert.NotNil(t, actualUsage.Groups, "Groups should not be nil for month %s, metric %s", month, metricId)
						assert.Equal(t, len(expectedUsage.Groups), len(actualUsage.Groups), "Groups length should match for month %s, metric %s", month, metricId)

						for key, expectedValue := range expectedUsage.Groups {
							actualValue, exists := actualUsage.Groups[key]
							assert.True(t, exists, "Group key %s should exist for month %s, metric %s", key, month, metricId)
							assert.Equal(t, expectedValue, actualValue, "Group value for key %s should match for month %s, metric %s", key, month, metricId)
						}
					}

					assert.Equal(t, expectedUsage.CustomerId, actualUsage.CustomerId, "CustomerId should match")
					assert.Equal(t, expectedUsage.BillableMetricId, actualUsage.BillableMetricId, "BillableMetricId should match")
					assert.Equal(t, expectedUsage.BillableMetricName, actualUsage.BillableMetricName, "BillableMetricName should match")
					assert.Equal(t, expectedUsage.StartTimestamp.Format(time.RFC3339), actualUsage.StartTimestamp.Format(time.RFC3339), "StartTimestamp should match")
					assert.Equal(t, expectedUsage.EndTimestamp.Format(time.RFC3339), actualUsage.EndTimestamp.Format(time.RFC3339), "EndTimestamp should match")
				}
			}
		})
	}
}

func floatPtr(f float64) *float64 {
	return &f
}
