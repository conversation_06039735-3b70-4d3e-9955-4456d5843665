package runs_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gofrs/uuid"
	"github.com/justinas/alice"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/runs"
	"langchain.com/smith/storage"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
)

const (
	apiKey       = "***************************************************"
	hashedApiKey = "223ccab5ebc19e3d3c55a2b9fbb5bebf6808893afff285317b88fb5889867efd76049cd4dc04407c037ab3b353a11501304c0042a2ba9b37ded9f193f19a5b65"
)

func setUpDatabase(t *testing.T, dbpool *database.AuditLoggedPool) string {
	userID, _ := uuid.NewV4()
	userIdStr := userID.String()
	orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, userIdStr)
	tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
	serviceAccountId := testutil.ServiceAccountSetup(t, dbpool, "test service account", orgId)
	orgIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, nil, "ORGANIZATION_ADMIN", "organization", serviceAccountId, nil)
	serviceAccountIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, &tenantId, "WORKSPACE_ADMIN", "workspace", serviceAccountId, &orgIdentityId)
	testutil.ServiceApiKeySetup(t, dbpool, hashedApiKey, tenantId, serviceAccountIdentityId, "hm", serviceAccountId, orgId)

	return tenantId
}

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "DELETE FROM organizations; DELETE FROM users; DELETE FROM api_keys;")
	assert.NoError(t, err)
}

func TestIngestRunsBatch(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	// Set up DB and Redis
	dbpool := database.PgConnect()
	tenantID := setUpDatabase(t, dbpool)
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	usageLimitsClient := usage_limits.NewUsageLimitsClient(dbpool, *routedRedisPools, cachingRedisPool)
	tracerSessionsClient := tracer_sessions.NewTracerSessionsClient(dbpool, cachingRedisPool)
	stgClient, err := storage.NewS3StorageClient(false, nil)
	require.NoError(t, err)
	ah := auth.NewBasicAuth(dbpool, cachingRedisPool, 0)
	ctx := context.Background()

	handler := &runs.BatchRunHandler{
		RunHandler: runs.RunHandler{
			Pg:                   dbpool,
			CachingRedisPool:     cachingRedisPool,
			RoutedRedisPools:     *routedRedisPools,
			UsageLimitsClient:    usageLimitsClient,
			TracerSessionsClient: tracerSessionsClient,
		},
		StorageClient: stgClient,
	}

	doBatchRequest := func(t *testing.T, payload []byte) *httptest.ResponseRecorder {
		req := httptest.NewRequest(http.MethodPost, "/v1/runs/batch", bytes.NewReader(payload))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Api-Key", apiKey)

		wRec := httptest.NewRecorder()
		handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.IngestRunsBatch)
		handlerWithAuth.ServeHTTP(wRec, req)
		return wRec
	}

	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

	t.Run("valid request - happy path", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		traceID := runID
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		runObj := map[string]interface{}{
			"id":           runID,
			"trace_id":     traceID,
			"name":         "batch run",
			"run_type":     "llm",
			"dotted_order": dottedOrder,
			"start_time":   "2025-01-01T10:00:00Z",
		}
		batchBody := map[string]interface{}{
			"post":  []interface{}{runObj},
			"patch": []interface{}{},
		}
		raw, err := json.Marshal(batchBody)
		require.NoError(t, err)

		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusAccepted, wRec.Code, "Expected HTTP 202 Accepted")

		ctx := context.Background()
		pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", tenantID, runID)
		exists, err := routedRedisClient.Exists(ctx, pendingKey).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists, "Should have a pending run in Redis")
	})

	t.Run("valid request - hex uuid", func(t *testing.T) {
		runID := "d6d06bc47537dfffa8e1b8136ce96d70"
		traceID := runID
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		runObj := map[string]interface{}{
			"id":           runID,
			"trace_id":     traceID,
			"name":         "batch run",
			"run_type":     "llm",
			"dotted_order": dottedOrder,
			"start_time":   "2025-01-01T10:00:00Z",
		}
		batchBody := map[string]interface{}{
			"post":  []interface{}{runObj},
			"patch": []interface{}{},
		}
		raw, err := json.Marshal(batchBody)
		require.NoError(t, err)

		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusAccepted, wRec.Code, "Expected HTTP 202 Accepted")

		ctx := context.Background()
		runIDUUID, err := uuid.FromString(runID)
		require.NoError(t, err)
		pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", tenantID, runIDUUID)
		exists, err := routedRedisClient.Exists(ctx, pendingKey).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists, "Should have a pending run in Redis")
	})

	t.Run("empty batch", func(t *testing.T) {
		// empty 'post' and 'patch'
		batchBody := map[string]interface{}{
			"post":  []interface{}{},
			"patch": []interface{}{},
		}
		raw, _ := json.Marshal(batchBody)
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "empty batch")
	})

	t.Run("missing dotted_order", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		// no dotted_order here
		runObj := map[string]interface{}{
			"id":       runID,
			"trace_id": runID,
			"run_type": "llm",
		}
		batchBody := map[string]interface{}{
			"post":  []interface{}{runObj},
			"patch": []interface{}{},
		}
		raw, _ := json.Marshal(batchBody)
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "dotted_order")
	})

	t.Run("missing trace_id", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		runObj := map[string]interface{}{
			"id":           runID,
			"dotted_order": fmt.Sprintf("20230505T051324571809Z%s", runID),
		}
		batchBody := map[string]interface{}{
			"post": []interface{}{runObj},
		}
		raw, _ := json.Marshal(batchBody)
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "trace_id is required")
	})

	t.Run("invalid JSON", func(t *testing.T) {
		// missing closing brace
		raw := []byte(`{"post":[{"id":"abc","trace_id":"xyz","dotted_order":"some_order"}`)
		wRec := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusUnprocessableEntity, wRec.Code)
		assert.Contains(t, wRec.Body.String(), "invalid batch JSON")
	})

	t.Run("duplicate run payload", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)
		runObj := map[string]interface{}{
			"id":           runID,
			"name":         "duplicate run",
			"trace_id":     runID,
			"run_type":     "llm",
			"dotted_order": dottedOrder,
			"start_time":   "2025-01-01T10:00:00Z",
		}
		batchBody := map[string]interface{}{"post": []interface{}{runObj}}
		raw, _ := json.Marshal(batchBody)
		// First time is OK
		firstResp := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusAccepted, firstResp.Code)

		// Second time should conflict
		secondResp := doBatchRequest(t, raw)
		assert.Equal(t, http.StatusConflict, secondResp.Code)
		assert.Contains(t, secondResp.Body.String(), "already received")
	})
	t.Run("large fields get uploaded", func(t *testing.T) {
		runID := uuid.Must(uuid.NewV4()).String()
		traceID := runID
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)

		largeString := strings.Repeat("A", int(config.Env.MinBlobStorageSizeKb)*1024+1000)

		runObj := map[string]interface{}{
			"id":           runID,
			"name":         "large fields",
			"trace_id":     traceID,
			"run_type":     "llm",
			"dotted_order": dottedOrder,
			"start_time":   "2025-01-01T10:00:00Z",
			"inputs":       map[string]string{"prompt": largeString},
			"outputs":      map[string]string{"answer": largeString},
		}
		batchBody := map[string]interface{}{
			"post":  []interface{}{runObj},
			"patch": []interface{}{},
		}
		bodyBytes, err := json.Marshal(batchBody)
		require.NoError(t, err)

		wRec := doBatchRequest(t, bodyBytes)
		require.Equal(t, http.StatusAccepted, wRec.Code)

		// Check Redis for placeholders
		ctx := context.Background()
		extrasKey := fmt.Sprintf("smith:runs:pending:%s:%s:extra", tenantID, runID)

		// 'inputs' in Redis should be just a storage key, not the original data
		inputsVal, err := routedRedisClient.HGet(ctx, extrasKey, "inputs_s3_url").Result()
		require.NoError(t, err)
		require.NotContains(t, inputsVal, largeString, "Should not store massive data inline")

		// Attempt to retrieve it from S3
		stgKey := strings.Trim(string(inputsVal), `"`) // the code typically stores the key as JSON
		bucket := storage.GetBucket(stgKey)
		obj, err := stgClient.GetObject(ctx, &storage.GetObjectInput{
			Bucket: bucket,
			Key:    stgKey,
		})
		require.NoError(t, err)
		defer obj.Body.Close()

		// The S3 object should contain the original large JSON
		var s3Data map[string]string
		err = json.NewDecoder(obj.Body).Decode(&s3Data)
		require.NoError(t, err)
		require.Equal(t, largeString, s3Data["prompt"])

		// Same check for 'outputs'
		outputsVal, err := routedRedisClient.HGet(ctx, extrasKey, "outputs_s3_url").Result()
		require.NoError(t, err)
		require.NotContains(t, outputsVal, largeString)

		stgKey = strings.Trim(string(outputsVal), `"`)
		obj2, err := stgClient.GetObject(ctx, &storage.GetObjectInput{
			Bucket: bucket,
			Key:    stgKey,
		})
		require.NoError(t, err)
		defer obj2.Body.Close()

		var outData map[string]string
		err = json.NewDecoder(obj2.Body).Decode(&outData)
		require.NoError(t, err)
		require.Equal(t, largeString, outData["answer"])
	})

	t.Run("large fields - blob storage disabled", func(t *testing.T) {
		oldVal := config.Env.BlobStorageEnabled
		config.Env.BlobStorageEnabled = false
		defer func() { config.Env.BlobStorageEnabled = oldVal }()

		oldMaxSize := config.Env.MinBlobStorageSizeKb
		config.Env.MinBlobStorageSizeKb = 0
		defer func() { config.Env.MinBlobStorageSizeKb = oldMaxSize }()

		runID := uuid.Must(uuid.NewV4()).String()
		traceID := runID
		dottedOrder := fmt.Sprintf("20230505T051324571809Z%s", runID)

		largeString := strings.Repeat("A", 1000)

		runObj := map[string]interface{}{
			"id":           runID,
			"name":         "large fields",
			"trace_id":     traceID,
			"run_type":     "llm",
			"dotted_order": dottedOrder,
			"start_time":   "2025-01-01T10:00:00Z",
			"inputs":       map[string]string{"prompt": largeString},
			"outputs":      map[string]string{"answer": largeString},
		}
		batchBody := map[string]interface{}{
			"post":  []interface{}{runObj},
			"patch": []interface{}{},
		}
		bodyBytes, err := json.Marshal(batchBody)
		require.NoError(t, err)

		wRec := doBatchRequest(t, bodyBytes)
		require.Equal(t, http.StatusAccepted, wRec.Code)

		// Check Redis for placeholders
		ctx := context.Background()
		extrasKey := fmt.Sprintf("smith:runs:pending:%s:%s:extra", tenantID, runID)

		inputsVal, err := routedRedisClient.HGet(ctx, extrasKey, "inputs").Result()
		require.NoError(t, err)
		var inputs map[string]string
		err = json.Unmarshal([]byte(inputsVal), &inputs)
		require.NoError(t, err)
		require.Equal(t, inputs["prompt"], largeString)

		outputsVal, err := routedRedisClient.HGet(ctx, extrasKey, "outputs").Result()
		require.NoError(t, err)
		var outputs map[string]string
		err = json.Unmarshal([]byte(outputsVal), &outputs)
		require.NoError(t, err)
		require.Equal(t, outputs["answer"], largeString)
	})
}
