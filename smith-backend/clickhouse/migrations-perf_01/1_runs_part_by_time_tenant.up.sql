CREATE TABLE IF NOT EXISTS runs{{TABLE_SUFFIX}} {{ON_CLUSTER}}
(
  id UUID,
  tenant_id UUID,
  name String,
  start_time DateTime64(6, 'UTC') COMMENT 'Start time of the run with microsecond precision',
  end_time Nullable(DateTime64(6, 'UTC')) COMMENT 'End time of the run with microsecond precision',
  extra String DEFAULT '{}' COMMENT 'JSON dict of extra metadata',
  error Nullable(String),
  is_root Bool,
  run_type LowCardinality(String),
  inputs String DEFAULT '{}' COMMENT 'JSON dict of inputs',
  outputs String DEFAULT '{}' COMMENT 'JSON dict of outputs',
  session_id UUID,
  parent_run_id Nullable(UUID),
  reference_example_id Nullable(UUID),
  reference_dataset_id Nullable(UUID),
  events String DEFAULT '[]' COMMENT 'JSON list of events',
  tags Array(String),
  manifest_id Nullable(UUID),
  status LowCardinality(String),
  trace_id UUID,
  dotted_order String,
  prompt_tokens UInt32 DEFAULT 0,
  completion_tokens UInt32 DEFAULT 0,
  total_tokens UInt32 DEFAULT 0,
  first_token_time Nullable(DateTime64(6, 'UTC')) COMMENT 'Time of first token with microsecond precision',
  modified_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'Time of last modification with microsecond precision',
  is_deleted UInt8 COMMENT 'Whether the run is deleted',
  inputs_s3_urls String DEFAULT '{}' COMMENT 'JSON dict of S3 URLs',
  outputs_s3_urls String DEFAULT '{}' COMMENT 'JSON dict of S3 URLs',
  received_at Nullable(DateTime64(6, 'UTC')) COMMENT 'Time of receiving the run with microsecond precision',
  tenant_tier UInt8 DEFAULT 0 COMMENT 'Tier of the tenant',
  manifest_s3_id Nullable(UUID),
  inserted_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC'),
  prompt_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of prompt tokens',
  completion_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of completion tokens',
  total_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of all tokens',
  input_tokens String DEFAULT '',
  output_tokens String DEFAULT '',
  price_model_id Nullable(UUID) COMMENT 'ID of the price map used to compute cost',
  input_size UInt32 DEFAULT 0 COMMENT 'the size in bytes of the original input',
  output_size UInt32 DEFAULT 0 COMMENT 'the size in bytes of the original output',
  trace_tier Nullable(String) DEFAULT NULL COMMENT 'Tier of the trace',
  ttl_seconds Nullable(UInt64) DEFAULT NULL COMMENT 'Time to live in seconds',
  trace_first_received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time when the parent run was first received',
  trace_upgrade Bool DEFAULT false COMMENT 'If trace was upgraded',
  s3_urls String DEFAULT '{}' COMMENT 'JSON dict of S3 URLs',
  inputs_kv Array(Tuple(String, String)),
  outputs_kv Array(Tuple(String, String)),
  inputs_preview Nullable(String) DEFAULT NULL COMMENT 'String preview of inputs',
  outputs_preview Nullable(String) DEFAULT NULL COMMENT 'String preview of outputs',
  thread_id Nullable(String) DEFAULT NULL COMMENT 'thread_id for the run',
  error_tokens String DEFAULT '',
  manifest String DEFAULT '{}' COMMENT 'JSON dict of manifest',
  INDEX idx_cursor_minmax concat(toString(start_time), toString(id)) TYPE minmax GRANULARITY 4,
  INDEX sk_mm_end_time end_time TYPE minmax GRANULARITY 1,
  INDEX sk_mm_inserted_at inserted_at TYPE minmax GRANULARITY 1,
  INDEX session_id_bloom session_id TYPE bloom_filter(0.01) GRANULARITY 4
)
ENGINE = {{REPLACING_ENGINE(modified_at, is_deleted)}}
PARTITION BY (toYYYYMM(toMonday(start_time)), cityHash64(tenant_id) % 50)
ORDER BY (tenant_id, session_id, start_time, id) -- NOTE: Not using is_root
SETTINGS
  allow_nullable_key = 1,
  index_granularity = 8192,
  ttl_only_drop_parts = 1;
