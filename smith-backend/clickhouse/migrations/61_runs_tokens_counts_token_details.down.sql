DROP VIEW IF EXISTS runs_token_counts_mv_v3 {{ON_CLUSTER}};

CREATE MATERIALIZED VIEW IF NOT EXISTS runs_token_counts_mv_v2 {{ON_CLUSTER}} TO runs_token_counts AS
SELECT
    toUUID(dest[2]) as id,
    tenant_id,
    session_id,
    toUUID(dest[2]) = trace_id as is_root,
    runs.id as source_id,
    toDateTime64(substring(dest[1], 1, 4) || '-' || substring(dest[1], 5, 2) || '-' || substring(dest[1], 7, 2) || ' ' || substring(dest[1], 10, 2) || ':' || substring(dest[1], 12, 2) || ':' || substring(dest[1], 14, 2) || '.' || substring(dest[1], 16, 6), 6, 'UTC') as start_time,
    prompt_tokens,
    completion_tokens,
    total_tokens,
    first_token_time,
    prompt_cost,
    completion_cost,
    total_cost,
    modified_at,
    is_deleted,
    trace_tier,
    ttl_seconds as trace_ttl_seconds,
    trace_first_received_at
from runs
array join arrayMap(x -> splitByChar('Z', x), splitByChar('.', dotted_order)) as dest
where run_type = 'llm';

ALTER TABLE runs_token_counts
DROP COLUMN IF EXISTS "prompt_token_details",
DROP COLUMN IF EXISTS "completion_token_details",
DROP COLUMN IF EXISTS "prompt_cost_details",
DROP COLUMN IF EXISTS "completion_cost_details";