# Use the official lightweight Python image.
# https://hub.docker.com/_/python
FROM python:3.11-slim AS builder
ARG ARCH="arm64"

RUN apt-get update && apt-get install -y curl gcc libffi-dev g++ wget perl
RUN curl https://sh.rustup.rs -sSf | bash -s -- -y

ENV PATH="/root/.cargo/bin:${PATH}"

# Add golang-migrate binary
RUN wget "https://go.dev/dl/go1.22.5.linux-${ARCH}.tar.gz"
RUN tar -C /usr/local -xzf "go1.22.5.linux-${ARCH}.tar.gz"
ENV PATH $PATH:/usr/local/go/bin
ENV GOBIN /usr/local/go/bin
RUN go install -tags 'clickhouse' github.com/golang-migrate/migrate/v4/cmd/migrate@v4.18.1

# Note: this is run by bake from the root of the repo, not this folder

WORKDIR /code/smith-backend

ENV POETRY_VERSION 1.5.1

COPY ./smith-backend/pyproject.toml ./smith-backend/poetry.lock* ./

# Install poetry, do a global install of only our dependencies, and compile them to *.pyc.
#
# Doing this step separately and this early in the Dockerfile improves layer caching:
# most of the time, the app code changes but its dependencies do not. By separating the work
# we do for dependencies, we get to cache and reuse that Docker layer across runs.
#
# It's important to run these in the same `RUN` step, since each `RUN` step creates its own layer
# and ideally we only want to have a single layer for all this.
RUN pip3 install poetry=="$POETRY_VERSION" && \
    poetry config virtualenvs.create false && \
    poetry install --without dev,test --no-interaction --no-ansi --no-root --no-directory && \
    python -c "import compileall; compileall.compile_path(maxlevels=1000, legacy=True)"

WORKDIR /code

# copy all env files
COPY ./.env* ./
RUN rm ./.env.local_dev* ./.env.local_test && \
    mkdir secrets

# copy all utils - this should match `develop` deps in pyproject.toml
COPY ./lc_config ./lc_config
COPY ./lc_database ./lc_database
COPY ./lc_logging ./lc_logging
COPY ./lc_metrics ./lc_metrics
COPY ./host-backend ./host-backend

WORKDIR /code/smith-backend

COPY ./smith-backend/app ./app
COPY ./smith-backend/hooks ./hooks
COPY ./smith-backend/static ./static
COPY ./smith-backend/alembic ./alembic
COPY ./smith-backend/clickhouse ./clickhouse
COPY ./smith-backend/quickwit/runs-index*.yaml ./quickwit/
COPY ./smith-backend/alembic.ini \
    ./smith-backend/queue_healthcheck.sh \
    ./smith-backend/queue_supervisord.conf \
    ./smith-backend/pyproject.toml \
    ./smith-backend/poetry.lock* \
    ./
COPY ./smith-backend/scripts/wait_for_clickhouse_and_migrate.sh ./scripts/wait_for_clickhouse_and_migrate.sh
COPY ./smith-backend/scripts/process_templates.sh ./scripts/process_templates.sh
COPY ./smith-backend/scripts/test_e2e_trace.py ./scripts/test_e2e_trace.py


# Finish installing our project, then remove poetry since we no longer need it.
# All the dependencies should have already been installed.
RUN poetry install --without test,dev,lint,typing --no-interaction --no-ansi && \
    pip uninstall poetry -y

# Compile all installed code, then:
# - remove .py files in the code directory, except alembic migrations which alembic needs as .py
# - remove .py files in `site-packages`
#
# `find` does not allow combining `-prune` and `-delete`, so we have to `xargs rm` instead.
RUN python -c "import compileall; compileall.compile_path(maxlevels=1000, legacy=True)" && \
    find /code -type d -path /code/smith-backend/alembic -prune -o -type f -name '*.py' -print0 | xargs -0 rm && \
    find /usr/local/lib/python3.11/site-packages -not -path '/usr/local/lib/python3.11/site-packages/ddtrace/*' -type f -name '*.py' -delete

FROM python:3.11-slim

RUN apt-get update && apt-get upgrade -y && apt-get install -y jq yq curl perl

# Enable Python stack traces on segfaults https://stackoverflow.com/a/29246977
ENV PYTHONFAULTHANDLER=1

# Ensure Python does not buffer output, which is recommended when running inside a container.
ENV PYTHONUNBUFFERED True

ENV LOG_LEVEL info

WORKDIR /code/smith-backend

# Get the files we need from the previous build:
# - our source code
# - our dependencies
# - the uvicorn, alembic, supervisord, rq binaries
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /code /code
COPY --from=builder /usr/local/bin/uvicorn /usr/local/bin/uvicorn
COPY --from=builder /usr/local/bin/alembic /usr/local/bin/alembic
COPY --from=builder /usr/local/bin/supervisord /usr/local/bin/supervisord
COPY --from=builder /usr/local/bin/rq /usr/local/bin/rq
COPY --from=builder /usr/local/bin/saq /usr/local/bin/saq
COPY --from=builder /usr/local/bin/ddtrace-run /usr/local/bin/ddtrace-run
COPY --from=builder /usr/local/go/bin/migrate /usr/local/bin/migrate

# Remove pip to avoid vulnerability
RUN pip uninstall pip -y
# Remove setuptools from site-packages to avoid vulnerability
RUN find /usr/local/lib/python3.11/site-packages -type d -name 'setuptools*' -print0 | xargs -0 rm -r

# Cache the tiktoken encoding file
ENV TIKTOKEN_CACHE_DIR=/tmp
RUN python -c "import tiktoken; [tiktoken.encoding_for_model(model_name) for model_name in tiktoken.model.MODEL_TO_ENCODING.keys()]"

CMD exec uvicorn app.main:app --host 0.0.0.0 --port "$PORT" --log-level "$LOG_LEVEL" --loop uvloop --http httptools --no-access-log
