.PHONY: format tests start start-auth start-db db-down db-logs start-saq-worker start-saq-worker-log-level-error start-no-auth start-api-auth start-web-auth start-api-auth-dev start-web-auth-dev lint-fix tests-setup tests-watch-auth-none tests-watch-auth-supabase tests-watch-auth-api-key bump-version tests-smith-go.PID tests-smith-go-sharded.PID tests-smith-go-s3.PID tests-smith-go-s3-sr.PID tests-smith-go-kill

# Commands to run the project locally with
# - local, ephemeral db
# - no auth

bump-version:
	poetry version patch

start-db:
	docker compose \
		$$([ "$$RUN_AZURE" = "true" ] && echo "--profile azure") \
		$$([ "$$RUN_ELASTIC" = "true" ] && echo "--profile elastic") \
		$$([ "$$RUN_SHARDED_REDIS" = "true" ] && echo "--profile sharded-redis") \
		$$([ "$$RUN_PG_15" = "true" ] && echo "--profile postgres-15") \
		$$([ "$$RUN_PG_15" != "true" ] && [ "$$RUN_PGAUDIT" != "true" ] && echo "--profile postgres-14") \
		$$([ "$$RUN_PGAUDIT" = "true" ] && echo "--profile pgaudit") \
		$$([ "$$RUN_KAFKA" = "true" ] && echo "--profile kafka") \
		$$([ "$$RUN_KAFKA_CONNECT" = "true" ] && echo "--profile kafka-connect") \
		$$([ "$$RUN_QUICKWIT" = "true" ] && echo "--profile quickwit") \
		-f docker-compose-db-local.yml up -d --wait
	docker run --rm --network host --entrypoint /bin/sh minio/mc:latest -c " \
		/usr/bin/mc config host rm local; \
		/usr/bin/mc config host add --quiet --api s3v4 local http://127.0.0.1:9002 minioadmin1 minioadmin1; \
		/usr/bin/mc mb --ignore-existing local/langsmith-images/; \
		/usr/bin/mc mb --ignore-existing local/langsmith-images-single-region/; \
		/usr/bin/mc mb --ignore-existing local/langsmith-run-manifests/; \
		/usr/bin/mc mb --ignore-existing local/langsmith-run-data/; \
		/usr/bin/mc mb --ignore-existing local/langsmith-images-test/; \
		/usr/bin/mc mb --ignore-existing local/langsmith-images-single-region-test; \
		/usr/bin/mc mb --ignore-existing local/langsmith-run-data-test/; \
		/usr/bin/mc mb --ignore-existing local/langsmith-run-manifests-test/;"
	if [ "$$RUN_AZURE" = "true" ]; then \
		docker run --rm --network host -e AZURE_STORAGE_CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=admin;AccountKey=password;BlobEndpoint=http://127.0.0.1:10000/admin;' --entrypoint /bin/sh mcr.microsoft.com/azure-cli -c " \
			az storage container create --name langsmith-images-test; \
			az storage container create --name langsmith-images-single-region-test;"; \
	fi

db-up: start-db setup-local-clickhouse

db-down:
	docker compose --profile "*" -f docker-compose-db-local.yml down

db-clean:
	rm -rf ./.data

db-logs:
	docker compose \
		$$([ "$$RUN_AZURE" = "true" ] && echo "--profile azure") \
		$$([ "$$RUN_ELASTIC" = "true" ] && echo "--profile elastic") \
		$$([ "$$RUN_SHARDED_REDIS" = "true" ] && echo "--profile sharded-redis") \
		$$([ "$$RUN_PG_15" = "true" ] && echo "--profile postgres-15") \
		$$([ "$$RUN_KAFKA" = "true" ] && echo "--profile kafka") \
		$$([ "$$RUN_KAFKA_CONNECT" = "true" ] && echo "--profile kafka-connect") \
		$$([ "$$RUN_PGBOUNCER" = "true" ] && echo "--profile pgbouncer") \
		-f docker-compose-db-local.yml logs -f --tail=0

setup-local-clickhouse:
	chmod +x ./clickhouse/setup_clickhouse.sh
	./clickhouse/setup_clickhouse.sh clickhouse-server

start-saq-worker:
	poetry run saq app.workers.queues.single_queue_worker.settings --quiet

start-saq-worker-auth:
	AUTH_TYPE=supabase poetry run saq app.workers.queues.single_queue_worker.settings --quiet

start-saq-worker-log-level-error:
	LOG_LEVEL=ERROR poetry run saq app.workers.queues.single_queue_worker.settings --quiet

start-saq-worker-auth-log-level-error:
	AUTH_TYPE=supabase LOG_LEVEL=ERROR poetry run saq app.workers.queues.single_queue_worker.settings --quiet

start-saq-worker-shard:
	poetry run saq app.workers.queues.single_queue_worker.sharded_settings --quiet

start-saq-worker-shard-log-level-error:
	LOG_LEVEL=ERROR poetry run saq app.workers.queues.single_queue_worker.sharded_settings --quiet

start-no-auth:
	poetry run uvicorn app.main:app --reload --reload-exclude './data/**' --reload-dir=app --reload-include 'app/**/*.sql' --reload-include 'app/**/*.json' --log-level debug --no-access-log --port 1984 --loop uvloop --http httptools

start-web-auth:
	AUTH_TYPE=supabase poetry run uvicorn app.main:app --reload --reload-exclude './data/**' --reload-dir=app --reload-include 'app/**/*.sql' --reload-include 'app/**/*.json' --no-access-log --port 1984 --loop uvloop --http httptools

start-web-basic-auth:
	set -a && . ../.env.local_basic && poetry run uvicorn app.main:app --reload --reload-exclude './data/**' --reload-dir=app --reload-include 'app/**/*.sql' --reload-include 'app/**/*.json' --reload-include ../lc_config/lc_config --reload-include ../lc_logging/lc_logging --reload-include ../lc_database/lc_database --reload-include ../lc_metrics/lc_metrics --no-access-log --port 1984 --loop uvloop --http httptools

start-web-oauth:
	AUTH_TYPE=oauth poetry run uvicorn app.main:app --reload --reload-exclude './data/**' --reload-dir=app --reload-include 'app/**/*.sql' --reload-include 'app/**/*.json' --no-access-log --port 1984 --loop uvloop --http httptools

start-web-google-oauth:
	set -a && . ../.env.local_google_oauth && poetry run uvicorn app.main:app --reload --reload-exclude './data/**' --reload-dir=app --reload-include 'app/**/*.sql' --reload-include 'app/**/*.json' --no-access-log --port 1984 --loop uvloop --http httptools

start: start-db migrate-local-clickhouse-up
	AUTH_TYPE=none make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgGreen.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start" \
		"make start-saq-worker" \
		"make start-no-auth"

start-quiet: start-db migrate-local-clickhouse-up
	AUTH_TYPE=none make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgGreen.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start" \
		"make start-saq-worker-log-level-error" \
		"make start-no-auth"

start-auth: start-db migrate-local-clickhouse-up
	AUTH_TYPE=supabase make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,ace,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-auth" \
		"cd ../smith-ace && deno run --unstable-worker-options --allow-all src/main.ts" \
		"make start-saq-worker-auth" \
		"make start-web-auth"

start-auth-sharded:
	RUN_SHARDED_REDIS=true make start-db
	make migrate-local-clickhouse-up
	AUTH_TYPE=supabase make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,ace,saq0,saq1,saq2,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"RUN_SHARDED_REDIS=true make db-logs" \
		"cd ../smith-go && set -a && . ../.env.local_dev_sharded_redis && make start-auth" \
		"cd ../smith-ace && deno run --unstable-worker-options --allow-all src/main.ts" \
		"set -a && . ../.env.local_dev_sharded_redis && SAQ_REDIS_NODE_NAME=node-0 make start-saq-worker-shard" \
		"set -a && . ../.env.local_dev_sharded_redis && SAQ_REDIS_NODE_NAME=node-1 make start-saq-worker-shard" \
		"set -a && . ../.env.local_dev_sharded_redis && SAQ_REDIS_NODE_NAME=node-2 make start-saq-worker-shard" \
		"set -a && . ../.env.local_dev_sharded_redis && make start-web-auth"


start-auth-redis-cluster: setup-test-redis-cluster
	RUN_SHARDED_REDIS=true make start-db
	make migrate-local-clickhouse-up
	AUTH_TYPE=supabase make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,ace,saq0,saq1,saq2,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"RUN_SHARDED_REDIS=true make db-logs" \
		"cd ../smith-go && set -a && . ../.env.local_redis_cluster && make start-auth" \
		"cd ../smith-ace && deno run --unstable-worker-options --allow-all src/main.ts" \
		"set -a && . ../.env.local_redis_cluster && SAQ_REDIS_NODE_NAME=node-0 make start-saq-worker-shard" \
		"set -a && . ../.env.local_redis_cluster && make start-web-auth"


start-auth-sharded-no-saq:
	RUN_SHARDED_REDIS=true make start-db
	make migrate-local-clickhouse-up
	AUTH_TYPE=supabase make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,ace,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"RUN_SHARDED_REDIS=true make db-logs" \
		"cd ../smith-go && make start-auth" \
		"cd ../smith-ace && deno run --unstable-worker-options --allow-all src/main.ts" \
		"set -a && . ../.env.local_dev_sharded_redis && make start-web-auth"

start-auth-quiet: start-db migrate-local-clickhouse-up
	AUTH_TYPE=supabase make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,ace,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-auth" \
		"cd ../smith-ace && deno run --unstable-worker-options --allow-all src/main.ts" \
		"make start-saq-worker-auth-log-level-error" \
		"make start-web-auth"

start-auth-sharded-quiet:
	RUN_SHARDED_REDIS=true make start-db
	make migrate-local-clickhouse-up
	AUTH_TYPE=supabase make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,ace,saq0,saq1,saq2,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"RUN_SHARDED_REDIS=true make db-logs" \
		"cd ../smith-go && make start-auth" \
		"cd ../smith-ace && deno run --unstable-worker-options --allow-all src/main.ts" \
		"set -a && . ../.env.local_dev_sharded_redis && SAQ_REDIS_NODE_NAME=node-0 make start-saq-worker-shard-log-level-error" \
		"set -a && . ../.env.local_dev_sharded_redis && SAQ_REDIS_NODE_NAME=node-1 make start-saq-worker-shard-log-level-error" \
		"set -a && . ../.env.local_dev_sharded_redis && SAQ_REDIS_NODE_NAME=node-2 make start-saq-worker-shard-log-level-error" \
		"set -a && . ../.env.local_dev_sharded_redis && make start-web-auth"

start-oauth: start-db migrate-local-clickhouse-up
	AUTH_TYPE=oauth make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-oauth" \
		"make start-saq-worker" \
		"make start-web-oauth"

start-oauth-quiet: start-db migrate-local-clickhouse-up
	AUTH_TYPE=oauth make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-oauth" \
		"make start-saq-worker-log-level-error" \
		"make start-web-oauth"

start-google-oauth: start-db migrate-local-clickhouse-up
	AUTH_TYPE=mixed make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-google-oauth" \
		"make start-saq-worker" \
		"make start-web-google-oauth"

start-google-oauth-quiet: start-db migrate-local-clickhouse-up
	AUTH_TYPE=mixed make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-google-oauth" \
		"make start-saq-worker-log-level-error" \
		"make start-web-google-oauth"

start-basic-auth: start-db migrate-local-clickhouse-up
	set -a && . ../.env.local_basic && make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-basic-auth" \
		"make start-saq-worker" \
		"make start-web-basic-auth"

start-basic-auth-quiet: start-db migrate-local-clickhouse-up
	set -a && . ../.env.local_basic && make migrate-local-postgres-up
	npx --yes concurrently -n "db,go,saq,app" -c "bgBlue.bold,bgCyan.bold,bgMagenta.bold" \
		"make db-logs" \
		"cd ../smith-go && make start-basic-auth" \
		"make start-saq-worker-log-level-error" \
		"make start-web-basic-auth"

# This command requires two additional ENV vars
# - POSTGRES_PASSWORD: get it from 1Password
start-api-auth-dev:
	 GCP_PROJECT_ID=langchain-dev LANGCHAIN_ENV=development.api POSTGRES_UNIX_SOCKET=~/cloudsql/langchain-dev:us-central1:langchainplus-db make start

# This command requires two additional ENV vars
# - POSTGRES_PASSWORD: get it from 1Password
# - SUPABASE_JWT_SECRET: get it from Supabase
start-web-auth-dev:
	 GCP_PROJECT_ID=langchain-dev LANGCHAIN_ENV=development.web POSTGRES_UNIX_SOCKET=~/cloudsql/langchain-dev:us-central1:langchainplus-db poetry run uvicorn app.main:app --reload --port 1984

format:
	poetry run poe format

lint:
	poetry run poe lint

PYTHON_FILES=.
lint_diff: PYTHON_FILES=$(shell git diff --name-only --diff-filter=d master | grep -E '\.py$$')

lint_diff:
	poetry run mypy $(PYTHON_FILES)
	poetry run black $(PYTHON_FILES) --check
	poetry run ruff .

lint-fix:
	poetry run poe format


setup-test-alembic:
	LANGCHAIN_ENV=local_test poetry run alembic upgrade head

setup-test-postgres-db:
	echo "Creating a temporary postgres database for tests"
	chmod 0600 ./.pgpass.txt
	PGPASSFILE=./.pgpass.txt psql -w -h localhost -U postgres -c 'CREATE DATABASE langsmith_test;' || true
	PGPASSFILE=./.pgpass.txt psql -w -h localhost -U postgres -d langsmith_test -c 'DROP SCHEMA IF EXISTS public CASCADE;' -c 'DROP SCHEMA IF EXISTS faker CASCADE;' -c 'CREATE SCHEMA public;'

setup-test-postgres: setup-test-postgres-db setup-test-alembic

seed-test-clickhouse:
	echo "Creating a temporary clickhouse database for tests"
	docker exec -i clickhouse-server clickhouse-client -q "DROP DATABASE IF EXISTS langsmith_test"
	docker exec -i clickhouse-server clickhouse-client -q "DROP ROW POLICY IF EXISTS delete_mask ON langsmith_test.feedbacks_rmt;"
	docker exec -i clickhouse-server clickhouse-client -q "CREATE DATABASE IF NOT EXISTS langsmith_test;"
	./scripts/migrate_clickhouse.sh langsmith_test up

seed-perf-experiment-clickhouse-local-up:
	EXPERIMENT=perf_01; \
	docker exec -i clickhouse-server clickhouse-client -q "CREATE DATABASE IF NOT EXISTS $${EXPERIMENT};"; \
	TEMPLATE_DIR=./clickhouse/migrations-$${EXPERIMENT} TABLE_SUFFIX=_$${EXPERIMENT} ./scripts/migrate_clickhouse.sh $${EXPERIMENT} up

seed-perf-experiment-clickhouse-local-down:
	EXPERIMENT=perf_01; \
	TEMPLATE_DIR=./clickhouse/migrations-$${EXPERIMENT} TABLE_SUFFIX=_$${EXPERIMENT} ./scripts/migrate_clickhouse.sh $${EXPERIMENT} down

clean-test-redis:
	LANGCHAIN_ENV=local_test poetry run python -c 'from lc_database.redis import _flush_test_aredis_pool; _flush_test_aredis_pool();'

clean-test-sharded-redis:
	set -a && . ../.env.local_test_sharded_redis && LANGCHAIN_ENV=local_test poetry run python -c 'from lc_database.redis import _flush_test_aredis_sharded_pools; _flush_test_aredis_sharded_pools();'

clean-test-redis-cluster:
	set -a && . ../.env.local_redis_cluster && LANGCHAIN_ENV=local_test poetry run python -c 'from lc_database.redis import _flush_test_aredis_cluster_pools; _flush_test_aredis_cluster_pools();'

quickwit-render-templates:
	poetry run ./quickwit/render-templates.sh

quickwit-diff-templates: quickwit-render-templates
	git diff --exit-code ./quickwit

define reset-quickwit-test-index
	@INDEX="$(1)_test"; \
	echo "Resetting $$INDEX"; \
	OUTPUT=$$(docker exec quickwit quickwit index delete --index $$INDEX --yes 2>&1); \
	EXIT_CODE=$$?; \
	if echo "$$OUTPUT" | grep -q "not found"; then \
		echo "Index $$INDEX not found, creating..."; \
	elif [ $$EXIT_CODE -ne 0 ]; then \
		echo "Error deleting $$INDEX: $$OUTPUT"; exit 2; \
	fi; \
	docker exec quickwit sh -c "sed 's/index_id: $(1)/index_id: $$INDEX/' \
		/quickwit-config/$(2) > /tmp/$$INDEX.yaml && \
		quickwit index create --index-config /tmp/$$INDEX.yaml"
endef

setup-test-quickwit: quickwit-render-templates
	$(call reset-quickwit-test-index,runs,runs-index-default.yaml)
	$(call reset-quickwit-test-index,runs-long-ttl,runs-index-long-ttl.yaml)


setup-test-redis-cluster:
	@echo "Setting up Redis Cluster for tests..."
	@echo "Checking if Redis Cluster is already running..."
	@if redis-cli -p 30001 ping > /dev/null 2>&1; then \
		CLUSTER_STATUS=$$(redis-cli -p 30001 cluster info | grep cluster_state | cut -d ":" -f2 | tr -d '[:space:]'); \
		if [ "$$CLUSTER_STATUS" = "ok" ]; then \
			echo "Redis Cluster is already running and in OK state. Skipping setup."; \
			exit 0; \
		else \
			echo "Redis Cluster is running but not in OK state. Will recreate."; \
		fi; \
	else \
		echo "Redis Cluster is not running. Will set up."; \
	fi
	@mkdir -p ./.data/redis-cluster
	@cd ./.data/redis-cluster && ../../redis-cluster/create-cluster start
	@echo "Waiting for Redis Cluster to start..."
	@for i in {1..15}; do \
		if redis-cli -p 30001 ping > /dev/null 2>&1; then \
			echo "Redis Cluster is responsive after $$i seconds"; \
			break; \
		fi; \
		echo "Waiting for Redis Cluster to be responsive ($$i/15 seconds)..."; \
		sleep 1; \
		if [ $$i -eq 15 ]; then \
			echo "Maximum wait time reached (15 seconds)"; \
		fi; \
	done
	@CLUSTER_STATUS=$$(redis-cli -p 30001 cluster info | grep cluster_state | cut -d ":" -f2 | tr -d '[:space:]'); \
	if [ "$$CLUSTER_STATUS" != "ok" ]; then \
		echo "Cluster not ready, creating cluster..."; \
		cd ./.data/redis-cluster && ../../redis-cluster/create-cluster create -f; \
	else \
		echo "Redis Cluster is already running and ready"; \
	fi

stop-test-redis-cluster:
	@echo "Stopping Redis Cluster..."
	@cd ./.data/redis-cluster && ../../redis-cluster/create-cluster stop
	@echo "Checking if Redis Cluster has stopped..."
	@if redis-cli -p 30001 ping > /dev/null 2>&1; then \
		echo "Warning: Redis Cluster is still responsive after stop command"; \
	else \
		echo "Redis Cluster has been stopped successfully"; \
	fi

tests-setup: setup-local-clickhouse setup-test-postgres clean-test-redis seed-test-clickhouse

tests-setup-sharded-redis: setup-local-clickhouse setup-test-postgres clean-test-sharded-redis seed-test-clickhouse

tests-setup-redis-cluster: setup-local-clickhouse setup-test-postgres clean-test-redis-cluster seed-test-clickhouse setup-test-redis-cluster

tests-smith-go.PID:
	LANGCHAIN_ENV=local_test LOG_LEVEL=debug go run ../smith-go/main.go > logs/smith-go.log 2>&1 & echo $$! > $@

tests-smith-go-sharded.PID:
	set -a && . ../.env.local_test_sharded_redis && LANGCHAIN_ENV=local_test LOG_LEVEL=debug go run ../smith-go/main.go > logs/smith-go.log 2>&1 & echo $$! > $@

tests-smith-go-s3.PID:
	set -a && . ../.env.local_test_s3_runs && LANGCHAIN_ENV=local_test LOG_LEVEL=debug go run ../smith-go/main.go > logs/smith-go.log 2>&1 & echo $$! > $@

tests-smith-go-s3-sr.PID:
	set -a && . ../.env.local_test_s3 && LANGCHAIN_ENV=local_test LOG_LEVEL=debug go run ../smith-go/main.go > logs/smith-go.log 2>&1 & echo $$! > $@

tests-smith-go-kill:
	lsof -i :8080 | grep LISTEN | awk '{print $$2}' | xargs -r kill || true
	[ -f tests-smith-go.PID ] && rm tests-smith-go.PID || true

# Supabase Auth tests
tests-auth: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH)

tests-auth-update: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 --snapshot-update $(TEST_PATH)

tests-auth-parallel: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -n auto -s --durations=5 --maxfail=1000 $(TEST_PATH) -k 'not serial'

tests-auth-serial: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH) -k 'serial'

# Supabase Auth tests with sharded Redis
tests-auth-sharded: tests-smith-go-kill tests-setup-sharded-redis
	AUTH_TYPE=supabase make tests-smith-go-sharded.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_test_sharded_redis && LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH)

tests-auth-sharded-parallel: tests-smith-go-kill tests-setup-sharded-redis
	AUTH_TYPE=supabase make tests-smith-go-sharded.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_test_sharded_redis && LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -n auto -s --durations=5 --maxfail=1 $(TEST_PATH) -k 'not serial and not runs_only'

tests-auth-parallel-runs: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -n auto -s --durations=5 --maxfail=1000 'app/tests/api/test_runs.py' -k 'not serial'

tests-auth-parallel-runs-s3: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go-s3.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_test_s3_runs && LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -n auto -s --durations=5 --maxfail=1000 'app/tests/api/test_runs.py' -k 'not serial'

tests-auth-sharded-serial: tests-smith-go-kill tests-setup-sharded-redis
	AUTH_TYPE=supabase make tests-smith-go-sharded.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_test_sharded_redis && LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH) -k 'serial'

tests-auth-sharded-parallel-runs: tests-smith-go-kill tests-setup-sharded-redis
	AUTH_TYPE=supabase make tests-smith-go-sharded.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_test_sharded_redis && LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -n auto -s --durations=5 --maxfail=1000 'app/tests/api/test_runs.py' -k 'not serial'

# Supabase Auth tests with separate workers
tests-auth-separate: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; RUN_RULES_QUEUE=rules INGESTION_QUEUE=ingestion SEPARATE_QUEUES_WITH_SINGLE_WORKER='["rules", "ingestion"]' LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH)

tests-auth-separate-parallel: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; RUN_RULES_QUEUE=rules INGESTION_QUEUE=ingestion SEPARATE_QUEUES_WITH_SINGLE_WORKER='["rules", "ingestion"]' LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -n auto -s --durations=5 $(TEST_PATH) -k 'not serial'

tests-auth-separate-serial: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; RUN_RULES_QUEUE=rules INGESTION_QUEUE=ingestion SEPARATE_QUEUES_WITH_SINGLE_WORKER='["rules", "ingestion"]' LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH) -k 'serial'

# Api Key Auth tests
tests-auth-with-api-key: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --api-key --durations=5 $(TEST_PATH)

tests-auth-with-api-key-parallel: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -n auto -s --api-key --durations=5 $(TEST_PATH) -k 'not serial'

tests-auth-with-api-key-serial: tests-smith-go-kill tests-setup
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --api-key --durations=5 $(TEST_PATH) -k 'serial'

# Basic Auth Tests
tests-auth-basic: tests-smith-go-kill tests-setup
	set -a && . ../.env.local_basic && make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_basic; LANGCHAIN_ENV=local_test poetry run pytest -vv --log-cli-level=info --capture=tee-sys -o log_cli=true -s --durations=5 'app/tests/test_basic_auth.py' -k 'serial'

# Auth tests with PGBouncer
tests-auth-pgbouncer: tests-smith-go-kill tests-setup
	set -a && . ../.env.local_pgbouncer && AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_pgbouncer; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH)

tests-auth-pgbouncer-parallel: tests-smith-go-kill tests-setup
	set -a && . ../.env.local_pgbouncer && AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_pgbouncer; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -n auto -s --durations=5 $(TEST_PATH) -k 'not serial'

tests-auth-pgbouncer-serial: tests-smith-go-kill tests-setup
	set -a && . ../.env.local_pgbouncer && AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_pgbouncer; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH) -k 'serial'

tests-auth-quickwit: tests-smith-go-kill tests-setup setup-test-quickwit
	AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_quickwit; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -vv --log-cli-level=info -s --durations=5 $(or $(TEST_PATH), "app/tests/api/test_runs_quickwit.py")

tests-auth-parallel-redis-cluster: tests-smith-go-kill tests-setup-redis-cluster
	set -a && . ../.env.local_redis_cluster && AUTH_TYPE=supabase make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_redis_cluster; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -n auto -s --durations=5 --maxfail=1000 $(or $(TEST_PATH), "app/tests/test_alerts.py" "app/tests/api/test_runs.py")  -k 'not serial'

tests-auth-parallel-s3: tests-smith-go-kill tests-setup
	set -a && . ../.env.local_test_s3 && AUTH_TYPE=supabase make tests-smith-go-s3-sr.PID
	trap 'make tests-smith-go-kill' EXIT; set -a && . ../.env.local_test_s3; LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run pytest -n auto -s --durations=5 --maxfail=1000 $(or $(TEST_PATH), "app/tests/api/test_runs.py") -k 'not serial'

# None Auth Tests
tests-auth-none: tests-smith-go-kill tests-setup
	AUTH_TYPE=none make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=none poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH)

tests-auth-none-parallel: tests-smith-go-kill tests-setup
	AUTH_TYPE=none make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=none poetry run pytest -vv --log-cli-level=info -x -n auto -s --durations=5 $(TEST_PATH) -k 'not serial'

tests-auth-none-serial: tests-smith-go-kill tests-setup
	AUTH_TYPE=none make tests-smith-go.PID
	trap 'make tests-smith-go-kill' EXIT; LANGCHAIN_ENV=local_test AUTH_TYPE=none poetry run pytest -vv --log-cli-level=info -s --durations=5 $(TEST_PATH) -k 'serial'

tests-watch-auth: tests-setup tests-smith-go-kill
	AUTH_TYPE=supabase make tests-smith-go.PID
	LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run ptw app --runner ./ptw_runner.sh

tests-watch-auth-go-outside: tests-setup
	LANGCHAIN_ENV=local_test AUTH_TYPE=supabase poetry run ptw app --runner ./ptw_runner.sh

migrate-local-clickhouse-up:
	./scripts/migrate_clickhouse.sh default up

migrate-local-clickhouse-down:
	./scripts/migrate_clickhouse.sh default down
migrate-local-postgres-up:
	LANGCHAIN_ENV=local_dev poetry run alembic upgrade head

migrate-local-postgres-down:
	LANGCHAIN_ENV=local_dev poetry run alembic downgrade head-1

create-dev-license:
	poetry run python ./scripts/generate_license.py --customer-name dev --expiration-weeks 52
