# Use the official lightweight Python Alpine image.
# https://hub.docker.com/_/python
FROM python:3.11-alpine AS builder
ARG ARCH="arm64"

# Install necessary packages using apk
RUN apk update && apk upgrade && apk add --no-cache bash gcc libffi-dev g++ make openssl-dev rust cargo go build-base python3-dev geos geos-dev git curl-dev perl

# Build kafka
RUN apk add --no-cache build-base bash git zlib-dev openssl-dev && \
    git clone https://github.com/confluentinc/librdkafka.git && \
    cd librdkafka && \
    ./configure && \
    make && \
    make install
# Set GOBIN to ensure Go binaries are installed to /usr/bin
ENV GOBIN=/usr/bin

# Install Go dependencies
RUN go install -tags 'clickhouse' github.com/golang-migrate/migrate/v4/cmd/migrate@v4.18.1

WORKDIR /code/smith-backend

ENV POETRY_VERSION=1.5.1

# Copy poetry configuration files
COPY ./smith-backend/pyproject.toml ./smith-backend/poetry.lock* ./

# Install Poetry and project dependencies
RUN pip3 install poetry=="$POETRY_VERSION" && \
    poetry config virtualenvs.create false && \
    poetry install --without dev,test --no-interaction --no-ansi --no-root --no-directory && \
    python -c "import compileall; compileall.compile_path(maxlevels=1000, legacy=True)"

WORKDIR /code

# Copy environment files
COPY ./.env* ./
RUN rm ./.env.local_dev* ./.env.local_test && \
    mkdir secrets

# Copy utility directories
COPY ./lc_config ./lc_config
COPY ./lc_database ./lc_database
COPY ./lc_logging ./lc_logging
COPY ./lc_metrics ./lc_metrics
COPY ./host-backend ./host-backend

WORKDIR /code/smith-backend

# Copy application code and scripts
COPY ./smith-backend/app ./app
COPY ./smith-backend/hooks ./hooks
COPY ./smith-backend/static ./static
COPY ./smith-backend/alembic ./alembic
COPY ./smith-backend/clickhouse ./clickhouse
COPY ./smith-backend/quickwit/runs-index*.yaml ./quickwit/
COPY ./smith-backend/alembic.ini \
    ./smith-backend/queue_healthcheck.sh \
    ./smith-backend/queue_supervisord.conf \
    ./smith-backend/pyproject.toml \
    ./smith-backend/poetry.lock* \
    ./
COPY ./smith-backend/scripts/wait_for_clickhouse_and_migrate.sh ./scripts/wait_for_clickhouse_and_migrate.sh
COPY ./smith-backend/scripts/process_templates.sh ./scripts/process_templates.sh
COPY ./smith-backend/scripts/test_e2e_trace.py ./scripts/test_e2e_trace.py

# Install project and remove Poetry
RUN poetry install --without test,dev,lint,typing --no-interaction --no-ansi && \
    pip uninstall poetry -y

# Compile Python files and remove source files
RUN python -c "import compileall; compileall.compile_path(maxlevels=1000, legacy=True)" && \
    find /code -type d -path /code/smith-backend/alembic -prune -o -type f -name '*.py' -print0 | xargs -0 rm && \
    find /usr/local/lib/python3.11/site-packages -not -path '/usr/local/lib/python3.11/site-packages/ddtrace/*' -type f -name '*.py' -delete

FROM python:3.11-alpine

# Update packages and install required utilities
RUN apk update && apk upgrade && apk add --no-cache jq yq curl curl-dev openssl libgcc bash libstdc++ perl

# Install Kafka
RUN apk add --no-cache build-base bash git zlib-dev openssl-dev && \
    git clone https://github.com/edenhill/librdkafka.git && \
    cd librdkafka && \
    ./configure && \
    make && \
    make install

# Set environment variables
ENV PYTHONFAULTHANDLER=1
ENV PYTHONUNBUFFERED=True
ENV LOG_LEVEL=info

WORKDIR /code/smith-backend

# Copy necessary files from the builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /code /code
COPY --from=builder /usr/local/bin/uvicorn /usr/local/bin/uvicorn
COPY --from=builder /usr/local/bin/alembic /usr/local/bin/alembic
COPY --from=builder /usr/local/bin/supervisord /usr/local/bin/supervisord
COPY --from=builder /usr/local/bin/rq /usr/local/bin/rq
COPY --from=builder /usr/local/bin/saq /usr/local/bin/saq
COPY --from=builder /usr/local/bin/ddtrace-run /usr/local/bin/ddtrace-run
COPY --from=builder /usr/bin/migrate /usr/local/bin/migrate

# Remove pip and setuptools for security
RUN pip uninstall pip -y && \
    find /usr/local/lib/python3.11/site-packages -type d -name 'setuptools*' -print0 | xargs -0 rm -r

# Cache the tiktoken encoding file
ENV TIKTOKEN_CACHE_DIR=/tmp
RUN python -c "import tiktoken; [tiktoken.encoding_for_model(model_name) for model_name in tiktoken.model.MODEL_TO_ENCODING.keys()]"

# Start the application
CMD uvicorn app.main:app --host 0.0.0.0 --port $PORT --log-level $LOG_LEVEL --loop uvloop --http httptools --no-access-log
