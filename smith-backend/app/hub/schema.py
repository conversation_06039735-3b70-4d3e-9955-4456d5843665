import datetime
import re
from typing import Any, Dict, List, Literal, Optional
from uuid import UUID

from fastapi import HTTPException, Query
from pydantic import BaseModel, Field, field_validator


class RepoExampleResponse(BaseModel):
    """Response model for example runs"""

    id: UUID
    start_time: datetime.datetime | None = None
    inputs: Dict[str, Any] | None = None
    outputs: Dict[str, Any] | None = None
    session_id: UUID


class CommitManifestResponse(BaseModel):
    """Response model for get_commit_manifest."""

    commit_hash: str
    manifest: Dict
    examples: list[RepoExampleResponse] | None = None


class Commit(BaseModel):
    """All database fields for commits."""

    id: UUID
    manifest: Dict
    repo_id: UUID
    parent_id: Optional[UUID] = None
    commit_hash: str
    created_at: datetime.datetime
    updated_at: datetime.datetime
    example_run_ids: list[UUID]


class CommitWithLookups(Commit):
    """All database fields for commits, plus helpful computed fields and user info
    for private prompts."""

    num_downloads: int
    num_views: int
    parent_commit_hash: Optional[str] = None
    full_name: Optional[str] = None


class Repo(BaseModel):
    """All database fields for repos."""

    repo_handle: str
    description: Optional[str] = None
    readme: Optional[str] = None
    id: UUID
    tenant_id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
    is_public: bool
    is_archived: bool
    tags: list[str]
    original_repo_id: Optional[UUID] = None
    upstream_repo_id: Optional[UUID] = None


class RepoBaseInfo(Repo):
    """All database fields for repos plus some extra important information"""

    owner: Optional[str]
    full_name: str


class RepoWithLookups(RepoBaseInfo):
    """All database fields for repos, plus helpful computed fields."""

    num_likes: int
    num_downloads: int
    num_views: int
    liked_by_auth_user: Optional[bool] = None
    last_commit_hash: Optional[str] = None
    num_commits: int
    original_repo_full_name: Optional[str] = None
    upstream_repo_full_name: Optional[str] = None
    latest_commit_manifest: Optional[CommitManifestResponse] = None


class ListReposResponse(BaseModel):
    repos: List[RepoWithLookups]
    total: int


class GetRepoResponse(BaseModel):
    repo: RepoWithLookups


class CreateRepoResponse(BaseModel):
    repo: RepoWithLookups


class TagCount(BaseModel):
    tag: str
    count: int


class ListTagsResponse(BaseModel):
    tags: List[TagCount]


class ReposFilterQueryParams(BaseModel):
    tenant_handle: Optional[str] = None
    tenant_id: Optional[UUID] = None
    query: Optional[str] = None
    has_commits: Optional[bool] = None
    tags: Optional[list[str]] = Field(Query(None))
    is_archived: Optional[Literal["true", "allow", "false"]] = None
    is_public: Optional[Literal["true", "false"]] = None
    upstream_repo_owner: Optional[str] = None
    upstream_repo_handle: Optional[str] = None
    # This represents resource_tags, not to be confused with the tags field
    tag_value_id: Optional[list[UUID]] = Field(
        Query(None), description="Tag value IDs of resource tags to filter by"
    )


class ReposSortQueryParams(BaseModel):
    sort_field: Optional[str] = None
    sort_direction: Literal["asc"] | Literal["desc"] | None = None


handle_re = re.compile(r"^[a-z][a-z0-9-_]*$")


# validate that repo_handle is only alphanumeric and hyphen and underscore
def repo_handle_validator(v):
    if not handle_re.match(v):
        raise HTTPException(
            400,
            "handle must be lowercase alphanumeric, "
            "hyphen, or underscore, starting with a-z",
        )


class ForkRepoRequest(BaseModel):
    """Fields to fork a repo"""

    repo_handle: str
    readme: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[list[str]] = None
    is_public: Optional[bool] = None

    @field_validator("repo_handle")
    @classmethod
    def check_handle(cls, v: str):
        repo_handle_validator(v)
        return v


class CreateRepoRequest(BaseModel):
    """Fields to create a repo"""

    repo_handle: str
    description: Optional[str] = None
    readme: Optional[str] = None
    is_public: bool
    tags: Optional[list[str]] = None

    @field_validator("repo_handle")
    @classmethod
    def check_handle(cls, v: str):
        repo_handle_validator(v)
        return v


class ListCommitsResponse(BaseModel):
    commits: List[CommitWithLookups]
    total: int


class CreateRepoCommitRequest(BaseModel):
    manifest: Dict
    parent_commit: Optional[str] = None
    example_run_ids: list[UUID] | None = None
    skip_webhooks: bool | list[UUID] = False


class CreateRepoCommitResponse(BaseModel):
    commit: CommitWithLookups


class UpdateRepoRequest(BaseModel):
    """Fields to update a repo"""

    # repo_handle: Optional[str] disallow repo renaming to start
    description: Optional[str] = None
    readme: Optional[str] = None
    tags: Optional[list[str]] = None
    is_public: Optional[bool] = None
    is_archived: Optional[bool] = None


class RepoTag(BaseModel):
    """Fields for a prompt tag"""

    id: UUID
    repo_id: UUID
    commit_id: UUID
    commit_hash: str
    tag_name: str
    created_at: datetime.datetime
    updated_at: datetime.datetime

    @field_validator("tag_name")
    def tag_name_validator(cls, v):
        if not re.match(r"^[a-zA-Z0-9-_]+$", v):
            raise HTTPException(
                400,
                "Tag name must only contain letters, numbers, hyphens and underscores",
            )
        return v


class RepoTagRequest(BaseModel):
    """Fields to create a prompt tag"""

    tag_name: str
    commit_id: UUID


class RepoUpdateTagRequest(BaseModel):
    """Fields to update a prompt tag"""

    commit_id: UUID
