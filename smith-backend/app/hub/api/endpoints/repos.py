"""Endpoints for repos."""

from typing import Optional

from fastapi import Depends, HTTPException
from lc_database import api_router, database
from lc_logging.audit_logs import audit_operation_name

from app import schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.hub import schema
from app.hub.api import pagination
from app.hub.crud import repos
from app.hub.utils import get_owner_from_passed_handle
from app.prompt_optimization import optimize as optimize_job

router = api_router.TrailingSlashRouter()


@router.get("/")
@audit_operation_name("read_repos")
async def list_repos(
    pagination_params: pagination.PaginationQueryParams = Depends(),
    filter_params: schema.ReposFilterQueryParams = Depends(),
    sort_params: schema.ReposSortQueryParams = Depends(),
    with_latest_manifest: bool = False,
    auth: Optional[deps.AuthInfo] = Depends(
        deps.Authorize(Permissions.PROMPTS_READ, allow_public=True)
    ),
) -> schema.ListReposResponse:
    """Get all repos."""
    async with database.asyncpg_conn() as db:
        if auth is not None:
            repos_lst, total = await repos.get_repos(
                db,
                auth,
                pagination_params=pagination_params,
                filter_params=filter_params,
                sort_params=sort_params,
                with_latest_manifest=with_latest_manifest,
            )
            return schema.ListReposResponse(repos=repos_lst, total=total)
        else:
            # ignore tag_value_id in public mode, because no concept
            # workspace-scoped resource tags
            filter_params.tag_value_id = None
            repos_lst, total = await repos.get_repos_public(
                db,
                pagination_params=pagination_params,
                filter_params=filter_params,
                sort_params=sort_params,
            )
            return schema.ListReposResponse(repos=repos_lst, total=total)


@router.get("/{owner}/{repo}")
@audit_operation_name("read_repo")
async def get_repo(
    owner: str,
    repo: str,
    auth: Optional[deps.AuthInfo] = Depends(
        deps.Authorize(Permissions.PROMPTS_READ, allow_public=True)
    ),
) -> schema.GetRepoResponse:
    """Get a repo."""
    effective_owner: Optional[str] = get_owner_from_passed_handle(owner)

    async with database.asyncpg_conn() as db:
        if auth is not None:
            repo_obj = await repos.get_repo_by_full_name(
                db, auth, repo, effective_owner
            )
            return schema.GetRepoResponse(repo=repo_obj)
        else:
            if effective_owner is None:
                raise HTTPException(status_code=400, detail="No prompt owner specified")
            repo_obj = await repos.get_repo_by_full_name_public(
                db, effective_owner, repo
            )
            return schema.GetRepoResponse(repo=repo_obj)


@router.post("/{owner}/{repo}/fork")
@audit_operation_name("create_repo_fork")
async def fork_repo(
    owner: str,
    repo: str,
    body: schema.ForkRepoRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_CREATE)),
) -> schema.GetRepoResponse:
    """Fork a repo."""
    effective_owner: Optional[str] = get_owner_from_passed_handle(owner)

    repo_obj = await repos.fork_repo(auth, repo, body, effective_owner)
    return schema.GetRepoResponse(repo=repo_obj)


@router.post("/")
@audit_operation_name("create_repo")
async def create_repo(
    repo: schema.CreateRepoRequest,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(Permissions.PROMPTS_CREATE, require_handle=False)
    ),
) -> schema.CreateRepoResponse:
    """Create a repo."""
    async with database.asyncpg_conn() as db, db.transaction():
        new_repo = await repos.create_repo(db, auth, repo)
        return schema.CreateRepoResponse(repo=new_repo)


@router.patch("/{owner}/{repo}")
@audit_operation_name("update_repo")
async def update_repo(
    owner: str,
    repo: str,
    repo_update: schema.UpdateRepoRequest,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(Permissions.PROMPTS_UPDATE, require_handle=False)
    ),
) -> schema.CreateRepoResponse:
    """Update a repo."""
    effective_owner: Optional[str] = get_owner_from_passed_handle(owner)
    async with database.asyncpg_conn() as db, db.transaction():
        updated_repo = await repos.update_repo(
            db, auth, repo, repo_update, effective_owner
        )
        return schema.CreateRepoResponse(repo=updated_repo)


@router.get("/tags")
async def list_repo_tags(
    pagination_params: pagination.PaginationQueryParams = Depends(),
    filter_params: schema.ReposFilterQueryParams = Depends(),
    auth: Optional[deps.AuthInfo] = Depends(
        deps.Authorize(Permissions.PROMPTS_READ, allow_public=True)
    ),
) -> schema.ListTagsResponse:
    """Get all repo tags."""
    async with database.asyncpg_conn() as db:
        if auth is not None:
            tags = await repos.get_all_repo_tags(
                db,
                auth,
                filter_params=filter_params,
                pagination_params=pagination_params,
            )
            return schema.ListTagsResponse(tags=tags)
        else:
            tags = await repos.get_all_repo_tags_public(
                db,
                filter_params=filter_params,
                pagination_params=pagination_params,
            )
            return schema.ListTagsResponse(tags=tags)


@router.delete("/{owner}/{repo}")
@audit_operation_name("delete_repo")
async def delete_repo(
    owner: str,
    repo: str,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_DELETE)),
) -> None:
    """Delete a repo."""
    effective_owner: Optional[str] = get_owner_from_passed_handle(owner)

    async with database.asyncpg_conn() as db:
        await repos.delete_repo(db, auth, repo, effective_owner)


@router.post("/optimize-job")
@audit_operation_name("create_optimized_prompt_job")
async def optimize_prompt_job(
    body: schemas.OptimizePromptJobRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_UPDATE)),
) -> schemas.OptimizePromptResponse:
    """Optimize prompt"""
    return await optimize_job.optimize(request=body, auth=auth)
