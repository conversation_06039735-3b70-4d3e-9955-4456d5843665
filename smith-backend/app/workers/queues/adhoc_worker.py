# ruff: noqa: E402
import structlog
from lc_config.settings import shared_settings
from lc_database.queue.connection import AsyncRedisRetry, get_sharded_settings
from lc_database.redis import create_async_queue
from lc_database.s3_client import zero_out_or_delete_s3_objects
from saq import CronJob

from app.workers.jobs.adhoc import (
    add_metronome_customers_for_self_hosted_customer_cron,
    aggregate_and_check_alert_rule_worker,
    autoscale_clickhouse_nodes,
    background_cleanup_runs_trace_id,
    background_delete_stored_assets,
    background_delete_tracer_sessions,
    cron_record_seat_heartbeats,
    cron_schedule_apply_rules,
    cron_schedule_bulk_exports,
    cron_schedule_onboarding_emails,
    cron_sync_org_charts,
    disable_clickhouse_merge_setting_job,
    enable_clickhouse_merge_setting_job,
    execute_playground_rules,
    fetch_and_execute_examples,
    process_all_traces_trace_transactions,
    process_batched_trace_deletes,
    process_batched_tracer_session_deletes,
    process_longlived_traces_trace_transactions,
    prompt_optimization,
    record_seat_heartbeats,
    record_seat_heartbeats_batch,
    report_metronome_seat_usage,
    report_nodes_usage,
    report_trace_usage,
    retry_metronome_seat_errors,
    retry_nodes_reporting_errors,
    retry_trace_reporting_errors,
    setup_self_hosted_customer,
    sync_served_dataset_cron,
    update_metronome_cache_for_org,
)
from app.workers.utils import shutdown_callback, startup_callback

logger = structlog.getLogger(__name__)

# ========================================== Adhoc Queue ============================================
# This queue is for any crons or general background jobs that don't necessitate their own queue.
# If you think the jobs will have a high volume, consider creating a separate queue for them.
# ===================================================================================================


cron_jobs = []

if shared_settings.RUN_RULES_CRON:
    # run the cron in the adhoc queue so scheduling is not interferred with the apply_run_rules jobs
    cron_jobs.append(
        CronJob(cron_schedule_apply_rules, shared_settings.RUN_RULES_CRON, timeout=200),
    )

if shared_settings.FF_PROCESS_BILLING_TRANSACTIONS:
    all_traces_job = CronJob(
        process_all_traces_trace_transactions,
        shared_settings.TRANSACTION_PROCESSING_CRON,
        timeout=shared_settings.TRANSACTION_PROCESSING_JOB_TIMEOUT_SEC,
    )
    longlived_traces_job = CronJob(
        process_longlived_traces_trace_transactions,
        shared_settings.TRANSACTION_PROCESSING_CRON,
        timeout=shared_settings.TRANSACTION_PROCESSING_JOB_TIMEOUT_SEC,
    )

    cron_jobs.append(all_traces_job)
    cron_jobs.append(longlived_traces_job)

if (
    shared_settings.FF_METRONOME_TRACE_REPORTING_ENABLED
    or shared_settings.PHONE_HOME_ENABLED
):
    reporting_job = CronJob(
        report_trace_usage,
        shared_settings.METRONOME_TRACE_REPORTING_CRON,
        timeout=shared_settings.METRONOME_TRACE_REPORTING_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(reporting_job)

    error_retry_job = CronJob(
        retry_trace_reporting_errors,
        shared_settings.METRONOME_TRACE_ERROR_RETRY_CRON,
        timeout=shared_settings.METRONOME_TRACE_ERROR_RETRY_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(error_retry_job)

if shared_settings.FF_METRONOME_NODES_REPORTING_ENABLED:
    nodes_reporting_job = CronJob(
        report_nodes_usage,
        shared_settings.METRONOME_NODES_REPORTING_CRON,
        timeout=shared_settings.METRONOME_NODES_REPORTING_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(nodes_reporting_job)

    nodes_error_retry_job = CronJob(
        retry_nodes_reporting_errors,
        shared_settings.METRONOME_NODES_ERROR_RETRY_CRON,
        timeout=shared_settings.METRONOME_NODES_ERROR_RETRY_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(nodes_error_retry_job)


if shared_settings.FF_METRONOME_SEAT_REPORTING_ENABLED:
    seat_reporting_job = CronJob(
        report_metronome_seat_usage,
        shared_settings.METRONOME_SEAT_REPORTING_CRON,
        timeout=shared_settings.METRONOME_SEAT_REPORTING_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(seat_reporting_job)

    seat_error_retry_job = CronJob(
        retry_metronome_seat_errors,
        shared_settings.METRONOME_SEAT_ERROR_RETRY_CRON,
        timeout=shared_settings.METRONOME_SEAT_ERROR_RETRY_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(seat_error_retry_job)

    seat_heartbeat_job = CronJob(
        cron_record_seat_heartbeats,
        shared_settings.METRONOME_SEAT_HEARTBEAT_CRON,
        timeout=shared_settings.METRONOME_SEAT_HEARTBEAT_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(seat_heartbeat_job)

if shared_settings.SYNC_SERVED_DATASETS_CRON_ENABLED:
    sync_served_dataset_job = CronJob(
        sync_served_dataset_cron,
        shared_settings.SYNC_SERVED_DATASETS_CRON,
        timeout=shared_settings.SYNC_SERVED_DATASETS_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(sync_served_dataset_job)

if shared_settings.ENABLE_SELF_HOSTED_BILLING_CUSTOMER_CRON:
    sh_customer_create_cron = CronJob(
        add_metronome_customers_for_self_hosted_customer_cron,
        shared_settings.SELF_HOSTED_BILLING_CUSTOMER_CRON,
        timeout=shared_settings.SELF_HOSTED_BILLING_CUSTOMER_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(sh_customer_create_cron)

if shared_settings.ENABLE_BULK_EXPORT_CRON:
    # keep the cron scheduler in the adhoc queue since the export queue is heavyweight and may miss crons
    bulk_export_cron = CronJob(
        cron_schedule_bulk_exports,
        shared_settings.BULK_EXPORT_CRON,
        timeout=shared_settings.BULK_EXPORT_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(bulk_export_cron)

if shared_settings.ENABLE_ONBOARDING_EMAILS:
    onboarding_emails_cron = CronJob(
        cron_schedule_onboarding_emails,
        shared_settings.ONBOARDING_EMAILS_CRON,
        timeout=shared_settings.ONBOARDING_EMAILS_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(onboarding_emails_cron)
if shared_settings.ENABLE_CLICKHOUSE_MERGE_SETTINGS_CRON:
    heavier_merge_enable = CronJob(
        enable_clickhouse_merge_setting_job,
        shared_settings.CLICKHOUSE_MERGE_SETTINGS_ON_CRON,
        timeout=shared_settings.CLICKHOUSE_MERGE_CRON_TIMEOUT_SEC,
    )
    cron_jobs.append(heavier_merge_enable)

    heavier_merge_disable = CronJob(
        disable_clickhouse_merge_setting_job,
        shared_settings.CLICKHOUSE_MERGE_SETTINGS_OFF_CRON,
        timeout=shared_settings.CLICKHOUSE_MERGE_CRON_TIMEOUT_SEC,
    )
    cron_jobs.append(heavier_merge_disable)

if shared_settings.ENABLE_ORG_CHARTS_CRON:
    org_charts_cron = CronJob(
        cron_sync_org_charts,
        shared_settings.ORG_CHARTS_CRON,
        timeout=shared_settings.ORG_CHARTS_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(org_charts_cron)


if shared_settings.FF_TRACER_SESSION_DELETE_BATCHING:
    session_delete_cron = CronJob(
        process_batched_tracer_session_deletes,
        shared_settings.TRACER_SESSION_BATCH_DELETE_CRON,
        timeout=shared_settings.TRACER_SESSION_DELETE_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(session_delete_cron)

if shared_settings.ENABLE_RUN_DELETE_CRON:
    run_delete_cron = CronJob(
        process_batched_trace_deletes,
        shared_settings.RUN_DELETE_CRON,
        timeout=shared_settings.RUN_DELETE_JOB_TIMEOUT_SEC,
    )
    cron_jobs.append(run_delete_cron)

# Only schedule the autoscaler if any cluster is enabled
if any(c.get("enabled") for c in shared_settings.CLICKHOUSE_CLUSTERS.values()):
    cron_jobs.append(
        CronJob(
            autoscale_clickhouse_nodes,
            shared_settings.AUTOSCALE_CLICKHOUSE_CRON,
            timeout=shared_settings.AUTOSCALE_CLICKHOUSE_TIMEOUT_SEC,
        )
    )

settings = {
    "queue": create_async_queue(
        shared_settings.ADHOC_QUEUE,
        AsyncRedisRetry.from_url(shared_settings.REDIS_DATABASE_URI),
    ),
    "functions": [
        background_delete_tracer_sessions,
        background_delete_stored_assets,
        record_seat_heartbeats,
        record_seat_heartbeats_batch,
        update_metronome_cache_for_org,
        process_all_traces_trace_transactions,
        process_longlived_traces_trace_transactions,
        setup_self_hosted_customer,
        fetch_and_execute_examples,
        execute_playground_rules,
        enable_clickhouse_merge_setting_job,
        disable_clickhouse_merge_setting_job,
        prompt_optimization,
        process_batched_tracer_session_deletes,
        aggregate_and_check_alert_rule_worker,
        process_batched_trace_deletes,
        background_cleanup_runs_trace_id,
        zero_out_or_delete_s3_objects,
        autoscale_clickhouse_nodes,
    ],
    "startup": startup_callback,
    "shutdown": shutdown_callback,
    "concurrency": shared_settings.MAX_ASYNC_JOBS_PER_WORKER,
    "cron_jobs": cron_jobs,
}


def sharded_settings() -> dict:
    return get_sharded_settings(shared_settings.ADHOC_QUEUE, settings)
