from base64 import urlsafe_b64encode
from hashlib import sha256
from typing import Optional

import asyncpg
import orj<PERSON>
from cryptography.fernet import Fernet
from fastapi import HTTPException
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.config import settings
from app.retry import retry_asyncpg

fernet = Fernet(urlsafe_b64encode(sha256(settings.API_KEY_SALT.encode()).digest()[:32]))


async def _get_secrets_dict(
    auth: AuthInfo,
    db: asyncpg.Connection,
    *,
    for_update: bool = False,
) -> dict[str, str]:
    row = await db.fetchrow(
        """
        SELECT secrets
        FROM tenants
        WHERE id = $1
        """
        if not for_update
        else """
        SELECT secrets
        FROM tenants
        WHERE id = $1
        FOR UPDATE
        """,
        auth.tenant_id,
    )

    if row is None:
        raise HTTPException(status_code=404, detail="Tenant not found")

    encrypted = row["secrets"]

    return orjson.loads(fernet.decrypt(encrypted)) if encrypted else {}


@retry_asyncpg
async def list_secrets(
    auth: AuthInfo,
    db: Optional[asyncpg.Connection] = None,
) -> list[schemas.Secret]:
    """List all secrets."""
    if db is None:
        async with asyncpg_conn() as db:
            decrypted = await _get_secrets_dict(auth, db)
    else:
        decrypted = await _get_secrets_dict(auth, db)
    return [schemas.Secret(key=key, value=value) for key, value in decrypted.items()]


async def list_secrets_as_dict(
    auth: AuthInfo,
    db: Optional[asyncpg.Connection] = None,
) -> dict[str, str]:
    secrets = await list_secrets(auth, db)
    return {s.key: s.value for s in secrets}


async def list_secrets_keys(
    auth: AuthInfo,
) -> list[schemas.SecretKey]:
    secrets = await list_secrets(auth)
    return [schemas.SecretKey(key=s.key) for s in secrets]


@retry_asyncpg
async def upsert_secrets(
    auth: AuthInfo,
    secrets: list[schemas.SecretUpsert],
) -> None:
    """Upsert secrets."""

    async with asyncpg_conn() as db, db.transaction():
        decrypted = await _get_secrets_dict(auth, db, for_update=True)

        for secret in secrets:
            if secret.value is None:
                decrypted.pop(secret.key, None)
            else:
                decrypted[secret.key] = secret.value

        await db.execute(
            """
            UPDATE tenants
            SET secrets = $1
            WHERE id = $2
            """,
            fernet.encrypt(orjson.dumps(decrypted)),
            auth.tenant_id,
        )
