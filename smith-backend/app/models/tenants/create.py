import asyncio
import json
import logging
import uuid
from collections import defaultdict
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, cast
from uuid import UUID, uuid4

import asyncpg
from fastapi import BackgroundTasks, HTTPException
from lc_config.tenant_config import OrganizationConfig, TenantConfig

from app import config, models, schemas
from app.api.auth.schemas import (
    AuthInfo,
    OrganizationRoles,
    TenantlessAuthInfo,
)
from app.models import ttl_settings
from app.models.constants import CH_INSERT_TIME
from app.models.organizations.payment import create_external_billing_customers
from app.models.tenants.tags import create_default_tag_keys

logger = logging.getLogger(__name__)


async def _org_has_reached_max_workspaces(
    db: asyncpg.Connection, organization_id: uuid.UUID, org_is_personal: bool
) -> bool:
    """Check in an authless context if an organization has reached its maximum number of workspaces."""
    # This import is here to avoid circular imports
    from app.models.organizations.config import get_org_config_cached
    from app.models.organizations.payment import (
        config_has_reached_max_workspaces,
    )

    org_result = await db.fetchrow(
        "SELECT metronome_customer_id, config FROM organizations WHERE id = $1",
        organization_id,
    )
    resolved_org_config, _ = await get_org_config_cached(
        OrganizationConfig.model_validate(org_result["config"]),
        str(organization_id),
        org_is_personal,
        org_result["metronome_customer_id"],
    )
    return await config_has_reached_max_workspaces(
        db, resolved_org_config, organization_id
    )


async def create_org_within_txn(
    db: asyncpg.Connection,
    display_name: str,
    org_config: OrganizationConfig,
    created_by_user_id: UUID,
    org_is_personal: bool = False,
) -> dict:
    org_config_values = org_config.model_dump()

    # Personal orgs are limited to 1 workspace
    if org_is_personal:
        org_config_values["max_workspaces"] = 1

    org = await db.fetchrow(
        "INSERT INTO organizations (display_name, config, is_personal, created_by_user_id, disabled) VALUES ($1, $2, $3, $4, $5) RETURNING *",
        display_name,
        org_config_values,
        org_is_personal,
        created_by_user_id,
        org_is_personal and config.settings.FF_PERSONAL_ORGS_DISABLED,
    )
    await ttl_settings.crud.upsert_ttl_settings_within_txn(
        db,
        org["id"],
        None,
        schemas.TraceTier(config.settings.DEFAULT_TRACE_TIER),
        False,
    )

    return dict(org)


async def create_tenant(
    db: asyncpg.Connection,
    auth: TenantlessAuthInfo,
    tenant: schemas.TenantCreate,
    background_tasks: Optional[BackgroundTasks] = None,
    org_config: OrganizationConfig | None = None,
    force_create_customers: bool = False,
) -> schemas.Tenant:
    """Create a tenant."""
    tenant_values = tenant.model_dump()

    if config.settings.SINGLETON_TENANT_ID is not None:
        tenant_values["id"] = cast(UUID, config.settings.SINGLETON_TENANT_ID)
        tenant_values["tenant_handle"] = "default"

    if not tenant_values.get("config"):
        tenant_values["config"] = (
            config.settings.SHARED_TENANT_DEFAULT_CONFIG.model_dump()
        )

    if not tenant_values.get("is_personal"):
        tenant_values["is_personal"] = False

    if not org_config:
        org_config = (
            config.settings.PERSONAL_ORG_DEFAULT_CONFIG
            if tenant_values["is_personal"]
            else config.settings.SHARED_ORG_DEFAULT_CONFIG
        )

    # Personal org is not allowed if basic auth is enabled
    org_is_personal = (
        False if config.settings.BASIC_AUTH_ENABLED else tenant_values["is_personal"]
    )

    # Create an organization in two cases:
    # 1. If the tenant is not part of an existing org and basic auth is enabled and there is no existing org
    # 2. If the tenant is not part of an existing org, the user is a non-SSO user, and basic auth is not enabled
    org_created = False
    provided_org_id = tenant_values.get("organization_id") is not None
    async with db.transaction():
        # Check if the organization has reached its maximum number of workspaces only if adding to an existing organization
        if provided_org_id and await _org_has_reached_max_workspaces(
            db, tenant_values.get("organization_id"), org_is_personal
        ):
            raise HTTPException(
                status_code=400,
                detail="This organization has reached its maximum number of workspaces.",
            )
        if not provided_org_id and config.settings.BASIC_AUTH_ENABLED:
            existing_org_id = await db.fetchval("SELECT id FROM organizations")
            if existing_org_id and not provided_org_id:
                raise HTTPException(
                    status_code=400,
                    detail="No additional organizations can be created in basic auth mode.",
                )
            elif existing_org_id:
                logger.info(
                    f"Using existing org, as only a single org is allowed for basic auth: {existing_org_id}"
                )
                organization_id = existing_org_id
            else:
                # Create the single org for basic auth
                organization = await create_org_within_txn(
                    db,
                    tenant_values["display_name"],
                    org_config,
                    cast(UUID, auth.user_id),
                    org_is_personal=org_is_personal,
                )
                organization_id = organization["id"]
                org_created = True
        elif not provided_org_id:
            if auth.is_sso_user:
                raise HTTPException(
                    status_code=400,
                    detail="SSO users cannot create new organizations.",
                )
            # Create a new org for non basic auth
            organization = await create_org_within_txn(
                db,
                tenant_values["display_name"],
                org_config,
                cast(UUID, auth.user_id),
                org_is_personal=org_is_personal,
            )
            organization_id = organization["id"]
            org_created = True
        else:
            organization_id = tenant_values["organization_id"]

        # Insert the tenant into the database
        query = """
        INSERT INTO tenants (id, created_at, display_name, config, tenant_handle, organization_id)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (id)
        DO NOTHING
        RETURNING *;
        """

        # Execute the insertion query and get the newly inserted tenant record
        tenant_record = await db.fetchrow(
            query,
            tenant_values.get("id"),
            tenant_values.get("created_at", datetime.now()),
            config.settings.DEFAULT_WORKSPACE_NAME
            if org_created and org_config
            else tenant_values["display_name"],
            tenant_values["config"],
            tenant_values.get("tenant_handle"),
            organization_id,
        )

        if tenant_record is None:
            raise HTTPException(
                status_code=409,
                detail="A tenant with this ID already exists.",
            )

        tenant_record = dict(tenant_record)

        workspace_identity_id = None

        # Insert workspace and organization identities if user is provided
        if auth.user_id is not None and auth.ls_user_id is not None:
            identity_query = """
            INSERT INTO identities (user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
            VALUES ($1, $2, $3, (SELECT id FROM roles WHERE name like $4), $5, $6, $7)
            RETURNING id;
            """

            # If we created a new org, create an org-level identity for the user.
            if org_created:
                org_identity_id = await db.fetchval(
                    identity_query,
                    auth.user_id,
                    None,
                    tenant_record["organization_id"],
                    OrganizationRoles.ADMIN.value,
                    schemas.AccessScope.organization,
                    None,
                    auth.ls_user_id,
                )
                if not org_identity_id:
                    logger.error(
                        f"Failed to create org identity for {auth.user_id=} in org {tenant_record['organization_id']}"
                    )
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to create organization identity",
                    )
            else:
                org_identity_id = await db.fetchval(
                    "select id from identities where ls_user_id = $1 and organization_id = $2 and access_scope = 'organization'",
                    auth.ls_user_id,
                    tenant_record["organization_id"],
                )
            if not org_identity_id:
                logger.error(
                    f"Failed to find org identity for {auth.user_id=} {auth.ls_user_id=} in org {tenant_record['organization_id']}"
                )
                raise HTTPException(
                    status_code=500,
                    detail="Failed to create organization identity",
                )
            workspace_identity_id = await db.fetchval(
                identity_query,
                auth.user_id,
                tenant_record["id"],
                tenant_record["organization_id"],
                "WORKSPACE_ADMIN",
                schemas.AccessScope.workspace,
                org_identity_id,
                auth.ls_user_id,
            )
            if not workspace_identity_id:
                logger.error(
                    f"Failed to create workspace identity for {auth.user_id=} in tenant {tenant_record['id']} {tenant_record['organization_id']}"
                )
                raise HTTPException(
                    status_code=500,
                    detail="Failed to create workspace identity",
                )

        # Create default tag keys for the workspace
        tag_key_ids = await create_default_tag_keys(db, tenant_record["id"])

        if not tag_key_ids:
            logger.error(
                f"Failed to create default tag keys for tenant {tenant_record['id']}"
            )

    # If we created a new org, create the customers
    if org_created or force_create_customers:
        auth_info = AuthInfo(
            user_id=auth.user_id,
            ls_user_id=auth.ls_user_id,
            user_email=auth.user_email,
            user_full_name=auth.user_full_name,
            tenant_handle=tenant_record.get("tenant_handle"),
            tenant_id=tenant_record["id"],
            organization_id=organization_id,
            tenant_config=tenant_record["config"],
            organization_is_personal=org_is_personal,
            # this is unused for creating external billing customers and may be None
            identity_id=workspace_identity_id,
        )
        try:
            await create_external_billing_customers(auth_info)
        except Exception as e:
            logger.exception(
                f"Failed to create metronome/stripe customer on org creation. {e}"
            )

    tenant_record["is_personal"] = tenant_values["is_personal"]
    if tenant_values["is_personal"]:
        if background_tasks:
            background_tasks.add_task(create_example_runs, auth, tenant_record)
        else:
            await create_example_runs(auth, tenant_record)

    return schemas.Tenant(**tenant_record)


# starting at the root, visit direct children and assign dotted order based on the parent's dotted order
def assign_dotted_order(
    run_id: UUID, parent_dotted_order: str | None, dict_ids_to_runs: dict
):
    run = dict_ids_to_runs[run_id]
    formatted_start_time = run["updated_start_time"].strftime("%Y%m%dT%H%M%S%fZ")
    del run["updated_start_time"]
    curr_dotted_order = f"{formatted_start_time}{str(run_id.hex)}"
    run["dotted_order"] = (
        f"{parent_dotted_order}.{curr_dotted_order}"
        if parent_dotted_order
        else f"{curr_dotted_order}"
    )
    for id in run["direct_child_run_ids"]:
        assign_dotted_order(id, run["dotted_order"], dict_ids_to_runs)


async def create_example_run(
    auth: TenantlessAuthInfo, dict_list: dict, name: str, tenant_record: dict
):
    map_uuids_to_new_uuids = {}
    trace_id = uuid4()
    for run in dict_list:
        if run["parent_run_id"] is None:
            map_uuids_to_new_uuids[run["id"]] = trace_id

    runs_payload = []
    dict_ids_to_runs = {}
    tokens_dict: dict = defaultdict(
        lambda: models.runs.ingest.TokenTracker(
            prompt_tokens=None,
            completion_tokens=None,
            total_tokens=None,
            first_token_time=None,
            prompt_cost=None,
            completion_cost=None,
            total_cost=None,
        )
    )

    for run in dict_list:
        if run["id"] not in map_uuids_to_new_uuids:
            map_uuids_to_new_uuids[run["id"]] = uuid4()
        if (
            run["parent_run_id"] is not None
            and run["parent_run_id"] not in map_uuids_to_new_uuids
        ):
            map_uuids_to_new_uuids[run["parent_run_id"]] = uuid4()
        for id in run["child_run_ids"] if run["child_run_ids"] is not None else []:
            if id not in map_uuids_to_new_uuids:
                map_uuids_to_new_uuids[id] = uuid4()
        for id in run["parent_run_ids"] if run["parent_run_ids"] is not None else []:
            if id not in map_uuids_to_new_uuids:
                map_uuids_to_new_uuids[id] = uuid4()
        for id in (
            run["direct_child_run_ids"]
            if run["direct_child_run_ids"] is not None
            else []
        ):
            if id not in map_uuids_to_new_uuids:
                map_uuids_to_new_uuids[id] = uuid4()
        parent_run_id = (
            map_uuids_to_new_uuids[run["parent_run_id"]]
            if run["parent_run_id"] is not None
            else None
        )
        new_run_id = map_uuids_to_new_uuids[run["id"]]
        run_dict = dict(run)
        run_dict["child_run_ids"] = [
            map_uuids_to_new_uuids[id]
            for id in (run["child_run_ids"] if run["child_run_ids"] is not None else [])
        ]
        run_dict["parent_run_ids"] = [
            map_uuids_to_new_uuids[id]
            for id in (
                run["parent_run_ids"] if run["parent_run_ids"] is not None else []
            )
        ]
        run_dict["direct_child_run_ids"] = [
            map_uuids_to_new_uuids[id]
            for id in (
                run["direct_child_run_ids"]
                if run["direct_child_run_ids"] is not None
                else []
            )
        ]
        run_dict["session_id"] = None
        run_dict["id"] = new_run_id
        run_dict["parent_run_id"] = parent_run_id
        current_date = datetime.now()
        format_string = "%Y-%m-%dT%H:%M:%S.%f"
        updated_end_time = datetime.strptime(
            run_dict["end_time"], format_string
        ).replace(
            year=current_date.year, month=current_date.month, day=current_date.day
        )
        updated_start_time = datetime.strptime(
            run_dict["start_time"], format_string
        ).replace(
            year=current_date.year, month=current_date.month, day=current_date.day
        )
        run_dict["end_time"] = datetime.strftime(updated_end_time, CH_INSERT_TIME)
        run_dict["start_time"] = datetime.strftime(updated_start_time, CH_INSERT_TIME)
        run_dict["modified_at"] = run_dict["end_time"]
        run_dict["first_token_time"] = None
        run_dict["updated_start_time"] = updated_start_time
        run_dict["extra"] = {}
        run_dict["manifest_id"] = None
        run_dict["session_name"] = "default"
        if run_dict["parent_run_id"] is None:
            updated_start_time_formatted = updated_start_time.strftime(
                "%Y%m%dT%H%M%S%fZ"
            )
            run_dict["dotted_order"] = f"{updated_start_time_formatted}Z{trace_id.hex}"
            run_dict["name"] = name
        runs_payload.append(run_dict)
        dict_ids_to_runs[run_dict["id"]] = run_dict
        run_dict["tags"] = ["this-is-a-tag"]
        run_dict["extra"] = {
            "metadata": {
                "custom-metadata-key": "custom-metadata-value",
                "revision_id": "my-revision-id",
            }
        }
        tokens_dict[run_dict["id"]] = models.runs.ingest.TokenTracker(
            prompt_tokens=run_dict["prompt_tokens"],
            completion_tokens=run_dict["completion_tokens"],
            total_tokens=run_dict["total_tokens"],
            first_token_time=run_dict["first_token_time"],
            prompt_cost=None,
            completion_cost=None,
            total_cost=None,
        )

    assign_dotted_order(trace_id, None, dict_ids_to_runs)

    await models.runs.ingest.upsert_runs(
        [
            models.runs.ingest.RunInsert(
                payload=curr_run,
                trace_id=str(trace_id.hex),
                dotted_order=curr_run["dotted_order"],
                received_at=datetime.now(timezone.utc).isoformat(),
                modified_at=curr_run["start_time"],
                hash_key="",
                should_insert=True,
                done=False,
            )
            for curr_run in runs_payload
        ],
        tokens_dict,
        AuthInfo(
            tenant_id=cast(UUID, tenant_record["id"]),
            tenant_config=TenantConfig(**tenant_record["config"]),
            organization_is_personal=tenant_record["is_personal"],
            user_id=auth.user_id,
            ls_user_id=auth.ls_user_id,
            tenant_handle=tenant_record["tenant_handle"],
        ),
        asyncio.Semaphore(),
    )


async def create_example_runs(auth: TenantlessAuthInfo, tenant_record: dict):
    with open(Path(__file__).parent / "interesting_run.json") as interesting_run_json:
        interesting_run = json.load(interesting_run_json)
    dict_list = interesting_run["runs"]

    await create_example_run(auth, dict_list, "Sample Agent Trace", tenant_record)
