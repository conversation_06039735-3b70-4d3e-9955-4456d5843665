import copy
import datetime
from base64 import urlsafe_b64encode
from hashlib import sha256
from typing import Dict, List, Optional, cast
from uuid import UUID, uuid4

import asyncpg
import or<PERSON><PERSON>
import structlog
from cryptography.fernet import Fernet
from fastapi import HTTPException
from lc_database.database import asyncpg_conn, kwargs_to_pgpos

from app import config, schemas
from app.api.auth.schemas import AuthInfo
from app.models.bulk_exports.export import validate_bulk_export_destination_connection
from app.retry import retry_asyncpg

logger = structlog.get_logger(__name__)

fernet = Fernet(
    urlsafe_b64encode(sha256(config.settings.API_KEY_SALT.encode()).digest()[:32])
)

BULK_EXPORT_CANCELLABLE_STATUSES = [
    schemas.BulkExportStatus.CREATED,
    schemas.BulkExportStatus.RUNNING,
]

# These statuses should propagate from the bulk export to the bulk export runs.
# 'Completed' propagates from the runs to the export.
BULK_EXPORT_PROPAGATE_STATUSES = {
    schemas.BulkExportStatus.CANCELLED: schemas.BulkExportRunStatus.CANCELLED,
    schemas.BulkExportStatus.FAILED: schemas.BulkExportRunStatus.FAILED,
    schemas.BulkExportStatus.TIMEDOUT: schemas.BulkExportRunStatus.TIMEDOUT,
}

BULK_EXPORT_ALLOWED_STATUS_CHANGES: Dict[
    schemas.BulkExportStatus, List[schemas.BulkExportStatus]
] = {
    schemas.BulkExportStatus.CANCELLED: [],
    schemas.BulkExportStatus.COMPLETED: [],
    schemas.BulkExportStatus.CREATED: [
        schemas.BulkExportStatus.CANCELLED,
        schemas.BulkExportStatus.FAILED,
        schemas.BulkExportStatus.RUNNING,
        schemas.BulkExportStatus.COMPLETED,
    ],
    schemas.BulkExportStatus.FAILED: [],
    schemas.BulkExportStatus.TIMEDOUT: [],
    schemas.BulkExportStatus.RUNNING: [
        schemas.BulkExportStatus.COMPLETED,
        schemas.BulkExportStatus.FAILED,
        schemas.BulkExportStatus.TIMEDOUT,
        schemas.BulkExportStatus.CANCELLED,
    ],
}

BULK_EXPORT_RUN_ALLOWED_STATUS_CHANGES: Dict[
    schemas.BulkExportRunStatus, List[schemas.BulkExportRunStatus]
] = {
    schemas.BulkExportRunStatus.CANCELLED: [],
    schemas.BulkExportRunStatus.COMPLETED: [],
    schemas.BulkExportRunStatus.CREATED: [
        schemas.BulkExportRunStatus.CANCELLED,
        schemas.BulkExportRunStatus.FAILED,
        schemas.BulkExportRunStatus.RUNNING,
    ],
    schemas.BulkExportRunStatus.FAILED: [],
    schemas.BulkExportRunStatus.TIMEDOUT: [],
    schemas.BulkExportRunStatus.RUNNING: [
        schemas.BulkExportRunStatus.COMPLETED,
        schemas.BulkExportRunStatus.FAILED,
        schemas.BulkExportRunStatus.TIMEDOUT,
        schemas.BulkExportRunStatus.CANCELLED,
    ],
}


def validate_bulk_exports_allowed(auth: AuthInfo) -> None:
    if not auth.tenant_config.organization_config.can_use_bulk_export:
        raise HTTPException(
            status_code=403,
            detail="Bulk exports are not enabled for this organization.",
        )


def _validate_export_status_transition(
    current_status: schemas.BulkExportStatus,
    new_status: schemas.BulkExportStatus,
) -> None:
    if new_status not in BULK_EXPORT_ALLOWED_STATUS_CHANGES.get(current_status, []):
        raise HTTPException(
            status_code=400,
            detail=f"Invalid bulk export status change from {current_status.value} to {new_status.value}",
        )


def _validate_export_run_status_transition(
    current_status: schemas.BulkExportRunStatus,
    new_status: Optional[schemas.BulkExportRunStatus],
) -> None:
    if (
        new_status is not None
        and new_status
        not in BULK_EXPORT_RUN_ALLOWED_STATUS_CHANGES.get(current_status, [])
    ):
        raise HTTPException(
            status_code=400,
            detail=f"Invalid bulk export run status change from {current_status.value} to {new_status.value}",
        )


def _record_with_decoded_fields(record: dict, *keys: str) -> dict:
    mutable_record = dict(record)
    for key in keys:
        if mutable_record.get(key) is not None:
            mutable_record[key] = orjson.loads(mutable_record[key])
    return mutable_record


def _safe_bulk_export_destination(
    record: dict, payload_keys: Optional[list[str]] = None
) -> schemas.BulkExportDestination:
    """This strips actual credentials from the record and returns an object safe for API responses."""
    mutable_record = copy.deepcopy(record)
    mutable_record = _record_with_decoded_fields(mutable_record, "config")

    if payload_keys:
        mutable_record["credentials_keys"] = payload_keys
    else:
        if "encrypted_credentials" not in mutable_record:
            raise HTTPException(
                status_code=400,
                detail="Failed to construct bulk export destination data",
            )
        decrypted_credentials: dict = orjson.loads(
            fernet.decrypt(mutable_record["encrypted_credentials"])
        )
        keys = decrypted_credentials.keys()
        mutable_record["credentials_keys"] = list(keys)
    if "encrypted_credentials" in mutable_record:
        del mutable_record["encrypted_credentials"]
    return schemas.BulkExportDestination(**mutable_record)


@retry_asyncpg
async def create_bulk_export_destination(
    auth: AuthInfo, payload: schemas.BulkExportDestinationCreate
) -> schemas.BulkExportDestination:
    """Create a bulk export destination."""
    validate_bulk_exports_allowed(auth)
    await validate_bulk_export_destination_connection(
        payload.config.model_dump(), payload.credentials.model_dump()
    )
    async with asyncpg_conn() as db:
        query = """
        INSERT INTO bulk_export_destinations (id, tenant_id, destination_type, display_name, config, encrypted_credentials)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
        """
        record = await db.fetchrow(
            query,
            uuid4(),
            auth.tenant_id,
            payload.destination_type.value,
            payload.display_name,
            payload.config.model_dump(),
            fernet.encrypt(orjson.dumps(payload.credentials.model_dump())),
        )
        if not record:
            raise HTTPException(
                status_code=500, detail="Failed to create bulk export destination"
            )

    return _safe_bulk_export_destination(
        dict(record), payload_keys=list(payload.credentials.model_dump().keys())
    )


@retry_asyncpg
async def get_bulk_export_destination(
    auth: AuthInfo, destination_id: UUID
) -> schemas.BulkExportDestination:
    """Retrieve a sanitized bulk export destination by ID."""
    validate_bulk_exports_allowed(auth)
    async with asyncpg_conn() as db:
        query = """
        SELECT * FROM bulk_export_destinations
        WHERE tenant_id = $1 AND id = $2
        """
        record = await db.fetchrow(query, auth.tenant_id, destination_id)
        if not record:
            raise HTTPException(
                status_code=404, detail="Bulk export destination not found"
            )

    return _safe_bulk_export_destination(dict(record))


@retry_asyncpg
async def get_bulk_export_destination_decrypted(
    auth: AuthInfo, destination_id: UUID
) -> schemas.BulkExportDestinationInternal:
    """FOR INTERNAL USE ONLY: retrieve a bulk export destination by ID including decrypted credentials."""
    validate_bulk_exports_allowed(auth)
    async with asyncpg_conn() as db:
        query = """
        SELECT * FROM bulk_export_destinations
        WHERE tenant_id = $1 AND id = $2
        """
        record = await db.fetchrow(query, auth.tenant_id, destination_id)
        if not record:
            raise HTTPException(
                status_code=404, detail="Bulk export destination not found"
            )
        mutable_record = dict(record)
        decrypted_credentials: dict = orjson.loads(
            fernet.decrypt(mutable_record["encrypted_credentials"])
        )
        mutable_record["decrypted_credentials"] = decrypted_credentials
        mutable_record = _record_with_decoded_fields(mutable_record, "config")

    return schemas.BulkExportDestinationInternal(**mutable_record)


@retry_asyncpg
async def list_bulk_export_destinations(
    auth: AuthInfo,
) -> list[schemas.BulkExportDestination]:
    """Retrieve all bulk export destinations for a workspace."""
    validate_bulk_exports_allowed(auth)
    async with asyncpg_conn() as db:
        query = """
        SELECT * FROM bulk_export_destinations
        WHERE tenant_id = $1
        """
        records = await db.fetch(query, auth.tenant_id)
        if not records:
            return []

    return [_safe_bulk_export_destination(dict(record)) for record in records]


@retry_asyncpg
async def delete_bulk_export_destination(
    auth: AuthInfo,
    destination_id: UUID,
) -> None:
    """Delete the given bulk export destination by ID."""
    validate_bulk_exports_allowed(auth)
    async with asyncpg_conn() as db:
        query = """
        DELETE FROM bulk_export_destinations
        WHERE id = $1 and tenant_id = $2
        RETURNING id
        """
        record = await db.fetchrow(query, destination_id, auth.tenant_id)
        if not record:
            raise HTTPException(
                status_code=404, detail="Bulk export destination not found"
            )

    return


@retry_asyncpg
async def create_bulk_export(
    auth: AuthInfo, payload: schemas.BulkExportCreate
) -> schemas.BulkExport:
    """Create a bulk export."""
    validate_bulk_exports_allowed(auth)

    async with asyncpg_conn() as db, db.transaction():
        query = """
        WITH destination AS (
            SELECT id, tenant_id FROM bulk_export_destinations
            WHERE id = $3 AND tenant_id = $2
        ),
        project AS (
            SELECT id, tenant_id FROM tracer_session
            WHERE id = $5 AND tenant_id = $2
        )
        INSERT INTO bulk_exports (id, tenant_id, bulk_export_destination_id, status, session_id, start_time, end_time, format, compression)
        SELECT $1, $2, d.id, $4, p.id, $6, $7, $8, $9
        FROM destination d JOIN project p ON d.tenant_id = p.tenant_id
        RETURNING *
        """

        record = await db.fetchrow(
            query,
            uuid4(),
            auth.tenant_id,
            payload.bulk_export_destination_id,
            schemas.BulkExportStatus.CREATED.value,
            payload.session_id,
            payload.start_time,
            payload.end_time,
            payload.format.value,
            payload.compression.value,
        )
        if not record:
            logger.warning(
                "Creating bulk export failed due to session_id, destination_id, or workspace not found",
                session_id=payload.session_id,
                destination_id=payload.bulk_export_destination_id,
                workspace_id=auth.tenant_id,
            )
            raise HTTPException(
                status_code=400,
                detail="session_id, destination_id, or workspace not found",
            )

    return schemas.BulkExport(**record)


async def get_bulk_export_in_txn(
    db: asyncpg.Connection, auth: AuthInfo, export_id: UUID
) -> schemas.BulkExport:
    query = """
        SELECT * FROM bulk_exports
        WHERE id = $1 and tenant_id = $2
        """
    record = await db.fetchrow(query, export_id, auth.tenant_id)
    if not record:
        raise HTTPException(status_code=404, detail="Bulk export not found")

    return schemas.BulkExport(**record)


@retry_asyncpg
async def get_bulk_export(auth: AuthInfo, export_id: UUID) -> schemas.BulkExport:
    """Retrieve a bulk export by ID."""
    validate_bulk_exports_allowed(auth)

    async with asyncpg_conn() as db:
        return await get_bulk_export_in_txn(db, auth, export_id)


@retry_asyncpg
async def list_bulk_exports(auth: AuthInfo) -> list[schemas.BulkExport]:
    """Retrieve all bulk exports for a workspace."""
    validate_bulk_exports_allowed(auth)

    async with asyncpg_conn() as db:
        query = """
        SELECT * FROM bulk_exports
        WHERE tenant_id = $1
        """
        records = await db.fetch(query, auth.tenant_id)
        if not records:
            return []

    return [schemas.BulkExport(**record) for record in records]


async def list_bulk_exports_for_statuses(
    db: asyncpg.Connection, statuses: List[schemas.BulkExportStatus]
) -> list[schemas.BulkExport]:
    """Retrieve all bulk exports for a workspace."""
    query = """
    SELECT * FROM bulk_exports
    WHERE status = ANY($1)
    order by status desc, created_at asc
    """
    records = await db.fetch(query, [status.value for status in statuses])
    if not records:
        return []

    return [schemas.BulkExport(**record) for record in records]


@retry_asyncpg
async def cancel_bulk_export(
    auth: AuthInfo, export_id: UUID, payload: schemas.BulkExportUpdate
) -> schemas.BulkExport:
    """Cancel a bulk export. Note that the bulk export hearbeat job will complete the cancellation process."""
    validate_bulk_exports_allowed(auth)
    if payload.status != schemas.BulkExportStatus.CANCELLED:
        raise HTTPException(
            status_code=400,
            detail=f"Only {schemas.BulkExportStatus.CANCELLED.value} status is allowed for bulk export update.",
        )

    async with asyncpg_conn() as db, db.transaction():
        current_status = await db.fetchval(
            "SELECT status FROM bulk_exports WHERE id = $1 AND tenant_id = $2",
            export_id,
            auth.tenant_id,
        )
        if not current_status:
            raise HTTPException(status_code=404, detail="Bulk export not found")

        _validate_export_status_transition(current_status, payload.status)

        # Cancel bulk export
        query = """
        UPDATE bulk_exports
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND tenant_id = $3
        AND status = any($4)
        RETURNING *
        """
        record = await db.fetchrow(
            query,
            payload.status.value,
            export_id,
            auth.tenant_id,
            [status.value for status in BULK_EXPORT_CANCELLABLE_STATUSES],
        )
        if not record:
            raise HTTPException(
                status_code=404, detail="Bulk export not found or not cancellable"
            )

        # Cancel pending bulk export runs.
        # Currently-running jobs will separately detect cancellation and update state.
        bulk_export_runs = await list_bulk_export_runs_in_txn(db, auth, export_id)
        pending_runs = [
            run
            for run in bulk_export_runs
            if run.status == schemas.BulkExportRunStatus.CREATED
        ]
        if pending_runs:
            update_batch_payload = schemas.BulkExportRunUpdateBatch(
                bulk_export_id=export_id,
                updates=[
                    schemas.BulkExportRunUpdateBatchItem(
                        id=run.id,
                        status=schemas.BulkExportRunStatus.CANCELLED,
                    )
                    for run in pending_runs
                ],
            )
            await update_bulk_export_runs_batch_in_txn(db, auth, update_batch_payload)

    return schemas.BulkExport(**record)


@retry_asyncpg
async def update_bulk_export_internal(
    auth: AuthInfo, export_id: UUID, payload: schemas.BulkExportUpdateInternal
) -> schemas.BulkExport:
    """Update a bulk export."""
    validate_bulk_exports_allowed(auth)

    async with asyncpg_conn() as db, db.transaction():
        current_status = await db.fetchval(
            "SELECT status FROM bulk_exports WHERE id = $1 AND tenant_id = $2 FOR UPDATE",
            export_id,
            auth.tenant_id,
        )
        if not current_status:
            raise HTTPException(status_code=404, detail="Bulk export not found")

        updates = []
        sql_kwargs: Dict[str, str | datetime.datetime | dict | UUID | List | None] = {
            "id": export_id,
            "tenant_id": auth.tenant_id,
        }

        if "status" in payload.model_fields_set:
            new_status = cast(schemas.BulkExportStatus, payload.status)
            _validate_export_status_transition(current_status, new_status)
            updates.append("status = $status")
            sql_kwargs["status"] = new_status

        if "errors" in payload.model_fields_set:
            updates.append("errors = $errors")
            sql_kwargs["errors"] = payload.errors

        if "metadata" in payload.model_fields_set:
            updates.append("metadata = $metadata")
            sql_kwargs["metadata"] = payload.metadata

        if "finished_at" in payload.model_fields_set:
            updates.append("finished_at = $finished_at")
            sql_kwargs["finished_at"] = payload.finished_at

        updates.append("updated_at = NOW()")
        updates_str = ", ".join(updates)
        sql_template = f"""
            UPDATE bulk_exports
            SET {updates_str}
            WHERE id = $id
            AND tenant_id = $tenant_id
            RETURNING *
        """
        sql = kwargs_to_pgpos(sql_template, sql_kwargs)

        record = await db.fetchrow(
            sql.sql,
            *sql.args,
        )
        if not record:
            raise HTTPException(
                status_code=404,
                detail="Bulk export not found or invalid status transition",
            )

    return schemas.BulkExport(**record)


@retry_asyncpg
async def create_bulk_export_runs_batch(
    auth: AuthInfo, payload: schemas.BulkExportRunCreateBatch
) -> List[schemas.BulkExportRun]:
    """Create a bulk export run."""
    validate_bulk_exports_allowed(auth)

    async with asyncpg_conn() as db, db.transaction():
        # Check that bulk export exists
        await get_bulk_export_in_txn(db, auth, payload.bulk_export_id)
        query = """
        WITH data AS (
            SELECT UNNEST($3::json[]) AS metadata
        )
        INSERT INTO bulk_export_runs (bulk_export_id, status, metadata)
        SELECT 
            $1,
            $2,
            metadata
        FROM data
        RETURNING *
        """
        records = await db.fetch(
            query,
            payload.bulk_export_id,
            schemas.BulkExportRunStatus.CREATED.value,
            [create.metadata.model_dump() for create in payload.creates],
        )
        if not records:
            raise HTTPException(
                status_code=500, detail="Failed to create bulk export run"
            )

    mutable_records = [
        _record_with_decoded_fields(record, "metadata", "errors") for record in records
    ]
    return [schemas.BulkExportRun(**record) for record in mutable_records]


async def update_bulk_export_runs_batch_in_txn(
    db: asyncpg.Connection, auth: AuthInfo, payload: schemas.BulkExportRunUpdateBatch
) -> List[schemas.BulkExportRun]:
    """Update bulk export runs."""
    validate_bulk_exports_allowed(auth)

    # Check that bulk export exists for this tenant
    bulk_export = await get_bulk_export_in_txn(db, auth, payload.bulk_export_id)
    assert auth.exists(bulk_export)

    # Validate status transitions
    existing_runs = await list_bulk_export_runs_in_txn(db, auth, payload.bulk_export_id)
    existing_runs_dict: Dict[UUID, schemas.BulkExportRun] = {
        run.id: run for run in existing_runs
    }
    for update in payload.updates:
        current_status = existing_runs_dict[update.id].status
        _validate_export_run_status_transition(current_status, update.status)

    statuses: List[schemas.BulkExportRunStatus | None] = [
        update.status if update.status else None for update in payload.updates
    ]
    sql_kwargs: Dict[str, str | datetime.datetime | dict | UUID | List | None] = {
        "bulk_export_id": payload.bulk_export_id,
        "tenant_id": auth.tenant_id,
        "run_ids": [update.id for update in payload.updates],
        "status_updates": [status.value if status else None for status in statuses],
        "errors_updates": [update.errors for update in payload.updates],
        "metadata_updates": [update.metadata for update in payload.updates],
        "finished_at_updates": [update.finished_at for update in payload.updates],
        "retry_number_updates": [update.retry_number for update in payload.updates],
    }

    sql_template = """
    update bulk_export_runs ber
        set status = coalesce(updates.status, ber.status),
        errors = coalesce(updates.errors, ber.errors),
        metadata = coalesce(updates.metadata, ber.metadata),
        finished_at = coalesce(updates.finished_at, ber.finished_at),
        retry_number = coalesce(updates.retry_number, ber.retry_number),
        updated_at = now()
    from (
        select
            unnest($run_ids::uuid[]) as run_id,
            unnest($status_updates::varchar[]) as status,
            unnest($errors_updates::json[]) as errors,
            unnest($metadata_updates::json[]) as metadata,
            unnest($finished_at_updates::timestamptz[]) as finished_at,
            unnest($retry_number_updates::int[]) as retry_number
    ) as updates
    where ber.id = updates.run_id
        and ber.bulk_export_id = $bulk_export_id
        and (select tenant_id from bulk_exports where id = $bulk_export_id) = $tenant_id
    returning ber.*
    """
    sql = kwargs_to_pgpos(sql_template, sql_kwargs)

    records = await db.fetch(
        sql.sql,
        *sql.args,
    )

    if not records:
        raise HTTPException(status_code=500, detail="Failed to bulk update export runs")

    if len(records) != len(payload.updates):
        logger.error(
            f"Expected to update {len(payload.updates)} records, but updated {len(records)}"
        )
        raise HTTPException(
            status_code=400,
            detail="Failed to bulk update some export runs - ensure all status transitions are valid",
        )

    mutable_records = [
        _record_with_decoded_fields(record, "metadata", "errors") for record in records
    ]
    return [schemas.BulkExportRun(**record) for record in mutable_records]


@retry_asyncpg
async def update_bulk_export_runs_batch(
    auth: AuthInfo, payload: schemas.BulkExportRunUpdateBatch
) -> List[schemas.BulkExportRun]:
    """Update bulk export runs."""
    validate_bulk_exports_allowed(auth)
    logger.info(f"Updating with payload: {payload}")

    async with asyncpg_conn() as db, db.transaction():
        return await update_bulk_export_runs_batch_in_txn(db, auth, payload)


@retry_asyncpg
async def update_bulk_export_run_progress(
    auth: AuthInfo,
    export_id: UUID,
    run_id: UUID,
    progress: schemas.BulkExportRunProgress,
) -> schemas.BulkExportRun:
    """Update a bulk export run's progress."""
    validate_bulk_exports_allowed(auth)

    async with asyncpg_conn() as db:
        query = """
        UPDATE bulk_export_runs
        SET metadata = jsonb_set(metadata::jsonb, '{result}', $3::jsonb)::json
        WHERE bulk_export_id = $1 AND id = $2 
        RETURNING *
        """
        record = await db.fetchrow(query, export_id, run_id, progress.model_dump())
        if not record:
            raise HTTPException(status_code=404, detail="Bulk export or run not found")

    mutable_record = _record_with_decoded_fields(record, "metadata")
    return schemas.BulkExportRun(**mutable_record)


@retry_asyncpg
async def get_bulk_export_run(
    auth: AuthInfo, export_id: UUID, export_run_id: UUID, lock=False
) -> schemas.BulkExportRun:
    """Retrieve a bulk export run by ID."""
    validate_bulk_exports_allowed(auth)
    lock_clause = "FOR UPDATE" if lock else ""

    async with asyncpg_conn() as db:
        query = f"""
        SELECT ber.* FROM bulk_export_runs ber
        JOIN bulk_exports be ON ber.bulk_export_id = be.id
        WHERE ber.id = $1 AND ber.bulk_export_id = $2 AND be.tenant_id = $3
        {lock_clause}
        """
        record = await db.fetchrow(query, export_run_id, export_id, auth.tenant_id)
        if not record:
            raise HTTPException(status_code=404, detail="Bulk export run not found")

    mutable_record = _record_with_decoded_fields(record, "metadata", "errors")
    return schemas.BulkExportRun(**mutable_record)


async def list_bulk_export_runs_in_txn(
    db: asyncpg.Connection, auth: AuthInfo, export_id: UUID
) -> List[schemas.BulkExportRun]:
    query = """
    SELECT ber.* FROM bulk_export_runs ber
    JOIN bulk_exports be ON ber.bulk_export_id = be.id
    WHERE ber.bulk_export_id = $1 AND be.tenant_id = $2
    order by (metadata->>'start_time')::timestamp asc
    """
    records = await db.fetch(query, export_id, auth.tenant_id)
    if not records:
        return []

    mutable_records = [
        _record_with_decoded_fields(record, "metadata", "errors") for record in records
    ]
    return [schemas.BulkExportRun(**record) for record in mutable_records]


@retry_asyncpg
async def list_bulk_export_runs(
    auth: AuthInfo, export_id: UUID
) -> List[schemas.BulkExportRun]:
    """Retrieve a bulk export run by ID."""
    validate_bulk_exports_allowed(auth)

    async with asyncpg_conn() as db:
        return await list_bulk_export_runs_in_txn(db, auth, export_id)
