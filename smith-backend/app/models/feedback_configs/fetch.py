from uuid import UUID

import asyncpg
import or<PERSON><PERSON>
from aiochclient import Record as ClickhouseRecord
from asyncpg import Record as PostgresRecord
from fastapi import HTTPException
from lc_database import clickhouse
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth.schemas import AuthInfo, FeedbackTokenInfo
from app.memoize import redis_cache
from app.models.feedback.utils import normalize_feedback_key
from app.models.feedback_configs.ingest import (
    is_feedback_configs_write_and_fetch_enabled,
)
from app.retry import retry_asyncpg, retry_clickhouse_read


@retry_clickhouse_read
async def _fetch_feedback_configs_clickhouse(
    auth: AuthInfo | FeedbackTokenInfo,
    feedback_keys: list[str] | None = None,
    read_after_write: bool = False,
) -> list[dict]:
    async with clickhouse.clickhouse_client(
        clickhouse.ClickhouseClient.USER_QUERIES
    ) as ch:
        if feedback_keys is None:
            rows = await ch.fetch(
                "fetch_feedback_configs",
                """
                SELECT * FROM (
                    SELECT *
                    FROM feedback_configs
                    WHERE tenant_id = {tenant_id}
                    ORDER BY modified_at DESC
                    LIMIT 1 BY feedback_key
                    SETTINGS select_sequential_consistency = 1
                ) as feedback_configs
                ORDER BY modified_at ASC
                """,
                params={"tenant_id": UUID(auth.tenant_id.hex)},
            )
        else:
            rows = await ch.fetch(
                "fetch_feedback_configs_by_keys",
                f"""
                SELECT * FROM (
                    SELECT *
                    FROM feedback_configs {"FINAL" if not read_after_write else ""}
                    WHERE tenant_id = {{tenant_id}}
                    AND feedback_key IN {{feedback_keys}}
                    ORDER BY modified_at DESC
                    LIMIT 1 BY feedback_key
                    {
                    "SETTINGS select_sequential_consistency = 1"
                    if read_after_write
                    else ""
                }
                ) as feedback_configs
                ORDER BY modified_at ASC
                """,
                params={
                    "tenant_id": UUID(auth.tenant_id.hex),
                    "feedback_keys": [
                        normalize_feedback_key(key) for key in feedback_keys
                    ],
                },
            )

        return rows


@retry_asyncpg
async def _fetch_feedback_configs_postgres(
    auth: AuthInfo | FeedbackTokenInfo,
    feedback_keys: list[str] | None,
) -> list[asyncpg.Record]:
    async with asyncpg_conn() as conn:
        if feedback_keys is None:
            sql = """
                SELECT DISTINCT ON (feedback_key)
                    tenant_id,
                    feedback_key,
                    feedback_config,
                    modified_at,
                    COALESCE(is_lower_score_better, FALSE) AS is_lower_score_better
                  FROM feedback_configs
                 WHERE tenant_id = $1
              ORDER BY feedback_key, modified_at DESC
            """
            rows = await conn.fetch(sql, auth.tenant_id)
        else:
            sql = """
                SELECT DISTINCT ON (feedback_key)
                    tenant_id,
                    feedback_key,
                    feedback_config,
                    modified_at,
                    COALESCE(is_lower_score_better, FALSE) AS is_lower_score_better
                  FROM feedback_configs
                 WHERE tenant_id = $1
                   AND feedback_key = ANY($2::text[])
              ORDER BY feedback_key, modified_at DESC
            """
            norm_keys = [normalize_feedback_key(k) for k in feedback_keys]
            rows = await conn.fetch(sql, auth.tenant_id, norm_keys)

    return rows


async def fetch_feedback_configs(
    auth: AuthInfo | FeedbackTokenInfo,
    feedback_keys: list[str] | None = None,
    read_after_write: bool = False,
) -> list[dict]:
    if is_feedback_configs_write_and_fetch_enabled(auth):
        rows = await _fetch_feedback_configs_postgres(auth, feedback_keys)
    else:
        rows = await _fetch_feedback_configs_clickhouse(
            auth, feedback_keys, read_after_write
        )

    return [map_feedback_config(row) for row in rows]


@redis_cache(ttl=120, cache_empty=False)
async def fetch_feedback_configs_cached(
    auth: AuthInfo | FeedbackTokenInfo, feedback_keys: list[str] | None = None
) -> list[dict]:
    return await fetch_feedback_configs(
        auth, sorted(feedback_keys) if feedback_keys else feedback_keys
    )


def map_feedback_config(row: ClickhouseRecord | PostgresRecord) -> dict:
    try:
        feedback_config = row["feedback_config"]
        if isinstance(feedback_config, str):
            # From clickhouse: JSON string that needs parsing
            parsed_config = orjson.loads(feedback_config)
        else:
            # From postgres: already a dict/object
            parsed_config = feedback_config
        return {
            **row,
            "feedback_key": normalize_feedback_key(row["feedback_key"]),
            "feedback_config": schemas.FeedbackConfig.model_validate(parsed_config),
        }
    except Exception as e:
        # This should never happen, but if it does, we want to know about it
        raise HTTPException(
            status_code=400,
            detail=f"Invalid feedback config: {repr(e)}",
        )
