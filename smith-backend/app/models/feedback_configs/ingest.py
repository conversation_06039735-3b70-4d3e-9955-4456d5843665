from datetime import datetime, timezone
from typing import Any
from uuid import UUID

import or<PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from lc_config.settings import shared_settings as settings
from lc_database import clickhouse
from lc_database.clickhouse import ClickhouseClient
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth import AuthInfo
from app.models.feedback.utils import normalize_feedback_key
from app.retry import retry_clickhouse


async def create_feedback_config(
    auth: AuthInfo, payload: schemas.CreateFeedbackConfigSchema
) -> schemas.FeedbackConfigSchema:
    (config,) = await create_feedback_configs(auth, [payload])
    return config


@retry_clickhouse
async def create_feedback_configs(
    auth: AuthInfo, payloads: list[schemas.CreateFeedbackConfigSchema]
) -> list[schemas.FeedbackConfigSchema]:
    async with clickhouse.clickhouse_client(ClickhouseClient.INGESTION) as ch:
        rows = await ch.fetch(
            "fetch_feedback_configs",
            """
            SELECT * FROM feedback_configs
            WHERE tenant_id = {tenant_id} AND feedback_key IN {feedback_keys}
            """,
            params={
                "tenant_id": UUID(auth.tenant_id.hex),
                "feedback_keys": [
                    normalize_feedback_key(payload.feedback_key) for payload in payloads
                ],
            },
        )

        modified_at = datetime.now(timezone.utc)
        to_insert: list[list[Any]] = []

        for payload in payloads:
            feedback_config_str = orjson.dumps(
                payload.feedback_config.model_dump()
            ).decode("utf-8")

            if row := next(
                (
                    row
                    for row in rows
                    if row["feedback_key"]
                    == normalize_feedback_key(payload.feedback_key)
                ),
                None,
            ):
                if row["feedback_config"] != feedback_config_str:
                    raise HTTPException(
                        status_code=400, detail="Feedback config already exists"
                    )
            else:
                to_insert.append(
                    [
                        UUID(auth.tenant_id.hex),
                        normalize_feedback_key(payload.feedback_key),
                        feedback_config_str,
                        modified_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                        payload.is_lower_score_better
                        if payload.is_lower_score_better is not None
                        else False,
                    ]
                )

        insert_query = """
        INSERT INTO feedback_configs
        (tenant_id, feedback_key, feedback_config, modified_at, is_lower_score_better)
        SETTINGS async_insert=0 VALUES
        """

        if is_feedback_configs_write_and_fetch_enabled(auth):
            insert_query = """
            INSERT INTO feedback_configs
            (tenant_id, feedback_key, feedback_config, modified_at, is_lower_score_better)
            VALUES
            """

        skip_ch_write = is_postgres_only_ingest_skip_ch_enabled(auth)

        if not skip_ch_write:
            await clickhouse.multi_execute_single(
                clickhouse.ExecuteRequest(
                    "insert_feedback_config", insert_query, args=to_insert
                ),
            )

        if is_feedback_configs_produce_enabled(auth):
            pg_inserts = [
                tuple(row[:3] + [row[4]]) for row in to_insert
            ]  # Skip modified_at
            await _insert_feedback_configs_to_postgres(pg_inserts)

        return [
            schemas.FeedbackConfigSchema(
                feedback_key=normalize_feedback_key(payload.feedback_key),
                feedback_config=payload.feedback_config,
                tenant_id=auth.tenant_id,
                modified_at=modified_at,
                is_lower_score_better=payload.is_lower_score_better
                if payload.is_lower_score_better is not None
                else False,
            )
            for payload in payloads
        ]


async def _insert_feedback_configs_to_postgres(config_inserts: list[tuple[Any, ...]]):
    if not config_inserts:
        return

    COLUMNS = (
        "tenant_id",
        "feedback_key",
        "feedback_config",
        "is_lower_score_better",
    )
    values_sql = ",\n".join(
        "(" + ",".join(f"${i}" for i in range(idx, idx + len(COLUMNS))) + ")"
        for idx in range(1, len(config_inserts) * len(COLUMNS) + 1, len(COLUMNS))
    )

    sql = f"""
    INSERT INTO feedback_configs ({", ".join(COLUMNS)})
    VALUES {values_sql}
    ON CONFLICT (tenant_id, feedback_key) DO UPDATE
      SET feedback_config        = EXCLUDED.feedback_config,
          is_lower_score_better = EXCLUDED.is_lower_score_better,
          modified_at           = NOW();
    """

    flat_params = [v for row in config_inserts for v in row]
    async with asyncpg_conn() as conn, conn.transaction():
        await conn.execute(sql, *flat_params)


def is_feedback_configs_write_and_fetch_enabled(auth: AuthInfo) -> bool:
    return is_feedback_configs_produce_enabled(auth) and (
        auth.tenant_id in settings.FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_TENANTS
        or settings.FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_ALL
    )


def is_feedback_configs_produce_enabled(auth: AuthInfo) -> bool:
    return (
        auth.tenant_id in settings.FF_USE_PG_FOR_FEEDBACK_CONFIGS_UPSERT_ENABLED_TENANTS
        or settings.FF_USE_PG_FOR_FEEDBACK_CONFIGS_UPSERT_ENABLED_ALL
    )


def is_postgres_only_ingest_skip_ch_enabled(auth: AuthInfo) -> bool:
    return (
        is_feedback_configs_produce_enabled(auth)
        and settings.FEEDBACK_CONFIG_SKIP_INGEST_CH_WRITE
    )
