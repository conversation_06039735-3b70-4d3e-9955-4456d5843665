import logging
import math
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, cast
from uuid import UUID

import orj<PERSON>
from aiochclient import Record
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.models.constants import CH_INSERT_TIME
from app.models.runs.utils import process_feedback_value_stats

logger = logging.getLogger(__name__)

token_counts_pre_agg = """
SELECT
    id,
    session_id,
    sum(total_tokens) as total_tokens,
    sum(prompt_tokens) as prompt_tokens,
    sum(completion_tokens) as completion_tokens,
    sum(total_cost) as total_cost,
    sum(prompt_cost) as prompt_cost,
    sum(completion_cost) as completion_cost,
    min(start_time) as min_start_time,
    min(first_token_time) as first_token_time
FROM runs_token_counts FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time} AND runs_token_counts.total_tokens < 4000000000
GROUP BY session_id, id"""

runs_stats_projection = """
    uniq(id) as run_count,
    max(start_time) as last_run_start_time,
    arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
    if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
    """

runs_stats_projection_agg = """,
    countMerge(run_count) as run_count,
    maxMerge(last_run_start_time) as last_run_start_time,
    arrayMap(x -> x / 1000, [quantileMerge(0.5)(latency_ptiles), quantileMerge(0.99)(latency_ptiles)]) as latency_ptiles,
    if(run_count = 0, 0, countMerge(error_run_count) / run_count) as error_rate
"""

runs_avg_latency_projection = """
    uniq(id) as run_count,
    if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
"""


runs_facets_projection = """,
    topK(20)(name) as top_10_name,
    topK(5)(status) as top_10_status,
    topK(10)(run_type) as top_10_run_type,
    topKArray(20)(tags) as top_10_tags"""

runs_facets_projection_agg = """,
    topKMerge(20)(topk_run_names) as top_10_name,
    topKMerge(5)(topk_run_status) as top_10_status,
    topKMerge(10)(topk_run_types) as top_10_run_type,
    topKMerge(20)(topk_run_tags) as top_10_tags"""

token_count_stats_projection = """
    uniq(id) as token_run_count,
    arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
    if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
    if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
    if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
    if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
    if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
    if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
    if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
    """

feedback_stats_projection = """
    mapKeys(uniqMap(map(key, id))) as feedback_keys,
    mapValues(avgMap(map(key, COALESCE(
        CASE
            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
            ELSE NULL
        END,
        score
    )))) as feedback_avgs,
    mapValues(uniqMap(map(key, id))) as feedback_counts,
    mapValues(stddevPopMap(map(key, COALESCE(
        CASE
            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
            ELSE NULL
        END,
        score
    )))) as feedback_stdevs,
    mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
    mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
    mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors"""

session_feedback_stats_projection = """
    mapKeys(uniqMap(map(key, id))) as session_feedback_keys,
    mapValues(avgMap(map(key, score))) as session_feedback_avgs,
    mapValues(stddevPopMap(map(key, score))) as session_feedback_stdevs,
    mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as session_feedback_errors,
    mapValues(uniqMap(map(key, id))) as session_feedback_counts"""

feedback_facets_projection = """,
    topK(20)(key) as top_10_feedback_key,
    topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
    topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
    topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source"""

metadata_facets_projection = """
    topK(20)(key) as top_10_metadata_key,
    topK(20)(key || ' == ' || value) as top_10_metadata_key_value"""

metadata_facets_projection_agg = """
    topKMerge(20)(topk_metadata_keys) as top_10_metadata_key,
    topKMerge(20)(topk_metadata_key_values) as top_10_metadata_key_value"""


input_kv_facets_projection = """
    topK(20)(key) as top_10_input_key,
    topK(20)(key || ' == ' || value) as top_10_input_key_value"""

input_kv_facets_projection_agg = """
    topKMerge(20)(topk_input_keys) as top_10_input_key,
    topKMerge(20)(topk_input_key_values) as top_10_input_key_value"""


output_kv_facets_projection = """
    topK(20)(key) as top_10_output_key,
    topK(20)(key || ' == ' || value) as top_10_output_key_value"""

output_kv_facets_projection_agg = """
    topKMerge(20)(topk_output_keys) as top_10_output_key,
    topKMerge(20)(topk_output_key_values) as top_10_output_key_value"""


token_count_stats_projection_agg = """
    countMerge(llm_run_count) as token_run_count,
    arrayMap(x -> x / 1000, [quantileMerge(0.5)(first_token_ptiles), quantileMerge(0.99)(first_token_ptiles)]) as first_token_ptiles,
    sumMerge(total_tokens) as total_tokens,
    sumMerge(prompt_tokens) as prompt_tokens,
    sumMerge(completion_tokens) as completion_tokens,
    sumMerge(total_cost) as total_cost,
    sumMerge(prompt_cost) as prompt_cost,
    sumMerge(completion_cost) as completion_cost,
    countMerge(streaming_run_count) / token_run_count as streaming_rate,
    quantileMerge(0.5)(median_tokens) as median_tokens
"""


sql_stats_for_session = (
    """
WITH

run_stats as (
SELECT session_id,"""
    + runs_stats_projection
    + """
FROM runs FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
GROUP BY session_id
),

feedback_stats as (
    SELECT session_id,"""
    + feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id != '00000000-0000-0000-0000-000000000000' AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
    GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
SELECT session_id,"""
    + token_count_stats_projection
    + """
FROM token_counts
GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)

sql_stats_w_facets_for_session = (
    """
WITH

run_stats as (
SELECT session_id,"""
    + runs_stats_projection
    + runs_facets_projection
    + """
FROM runs
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
GROUP BY session_id
),

feedback_stats as (
SELECT session_id,"""
    + feedback_stats_projection
    + feedback_facets_projection
    + """
FROM feedbacks_rmt FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND run_id != '00000000-0000-0000-0000-000000000000' AND session_id IN {session_ids} AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

metadata_stats as (
select session_id,"""
    + metadata_facets_projection
    + """
from runs_metadata_kv
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
group by session_id
),

input_kv_stats as (
select session_id,"""
    + input_kv_facets_projection
    + """
from runs_inputs_kv
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
group by session_id
),

output_kv_stats as (
select session_id,"""
    + output_kv_facets_projection
    + """
from runs_outputs_kv
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
group by session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
    SELECT session_id,"""
    + token_count_stats_projection
    + """
    FROM token_counts
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    metadata_stats.* EXCEPT (session_id),
    input_kv_stats.* EXCEPT (session_id),
    output_kv_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN metadata_stats ON run_stats.session_id = metadata_stats.session_id
LEFT JOIN input_kv_stats ON run_stats.session_id = input_kv_stats.session_id
LEFT JOIN output_kv_stats ON run_stats.session_id = output_kv_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)

sql_stats_for_session_agg = (
    """
WITH

run_stats as (
SELECT session_id """
    + runs_stats_projection_agg
    + """
FROM runs_sessions_agg_hourly
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
GROUP BY session_id
),

feedback_stats as (
    SELECT session_id,"""
    + feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id != '00000000-0000-0000-0000-000000000000' AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
    GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

token_count_stats as (
    SELECT session_id,
        countMerge(llm_run_count) as token_run_count,
        arrayMap(x -> x / 1000, [quantileMerge(0.5)(first_token_ptiles), quantileMerge(0.99)(first_token_ptiles)]) as first_token_ptiles,
        sumMerge(total_tokens) as total_tokens,
        sumMerge(prompt_tokens) as prompt_tokens,
        sumMerge(completion_tokens) as completion_tokens,
        sumMerge(total_cost) as total_cost,
        sumMerge(prompt_cost) as prompt_cost,
        sumMerge(completion_cost) as completion_cost,
        countMerge(streaming_run_count) / token_run_count as streaming_rate
    FROM runs_sessions_agg_hourly
    WHERE tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)


# use topk aggregated merge tree for computation
sql_stats_w_facets_for_session_agg = (
    """
WITH

run_stats as (
SELECT session_id"""
    + runs_stats_projection_agg
    + runs_facets_projection_agg
    + """
FROM runs_topk_hourly_agg
JOIN runs_sessions_agg_hourly ON runs_topk_hourly_agg.session_id = runs_sessions_agg_hourly.session_id
WHERE is_root = true AND runs_topk_hourly_agg.tenant_id = {tenant_id} AND runs_topk_hourly_agg.session_id IN {session_ids} AND runs_topk_hourly_agg.hour >= toStartOfHour(toDateTime64({min_start_time}, 6))

GROUP BY session_id
),

feedback_stats as (
SELECT session_id,"""
    + feedback_stats_projection
    + feedback_facets_projection
    + """
FROM feedbacks_rmt FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND run_id != '00000000-0000-0000-0000-000000000000' AND session_id IN {session_ids} AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

metadata_stats as (
select session_id,"""
    + metadata_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

input_kv_stats as (
select session_id,"""
    + input_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

output_kv_stats as (
select session_id,"""
    + output_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
    SELECT session_id,
        countMerge(llm_run_count) as token_run_count,
        arrayMap(x -> x / 1000, [quantileMerge(0.5)(first_token_ptiles), quantileMerge(0.99)(first_token_ptiles)]) as first_token_ptiles,
        sumMerge(total_tokens) as total_tokens,
        sumMerge(prompt_tokens) as prompt_tokens,
        sumMerge(completion_tokens) as completion_tokens,
        sumMerge(total_cost) as total_cost,
        sumMerge(prompt_cost) as prompt_cost,
        sumMerge(completion_cost) as completion_cost,
        countMerge(streaming_run_count) / token_run_count as streaming_rate
    FROM runs_sessions_agg_hourly
    WHERE tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    metadata_stats.* EXCEPT (session_id),
    input_kv_stats.* EXCEPT (session_id),
    output_kv_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN metadata_stats ON run_stats.session_id = metadata_stats.session_id
LEFT JOIN input_kv_stats ON run_stats.session_id = input_kv_stats.session_id
LEFT JOIN output_kv_stats ON run_stats.session_id = output_kv_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)


sql_stats_w_facets_for_session_without_sessions_agg = (
    """
WITH

run_stats as (
SELECT session_id,"""
    + runs_stats_projection
    + runs_facets_projection
    + """
FROM runs
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
GROUP BY session_id
),

feedback_stats as (
SELECT session_id,"""
    + feedback_stats_projection
    + feedback_facets_projection
    + """
FROM feedbacks_rmt FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND run_id != '00000000-0000-0000-0000-000000000000' AND session_id IN {session_ids} AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

metadata_stats as (
select session_id,"""
    + metadata_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

input_kv_stats as (
select session_id,"""
    + input_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

output_kv_stats as (
select session_id,"""
    + output_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
    SELECT session_id,"""
    + token_count_stats_projection
    + """
    FROM token_counts
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    metadata_stats.* EXCEPT (session_id),
    input_kv_stats.* EXCEPT (session_id),
    output_kv_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN metadata_stats ON run_stats.session_id = metadata_stats.session_id
LEFT JOIN input_kv_stats ON run_stats.session_id = input_kv_stats.session_id
LEFT JOIN output_kv_stats ON run_stats.session_id = output_kv_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)


def _construct_kv_stats(
    stats: dict, key_name: str, value_name: str, top_key: str, top_value: str
):
    key_info = [
        {
            "key": key_name,
            "value": key,
            "query": f'eq({key_name}, "{key}")',
        }
        for key in stats.get(top_key, [])
        if key != ""
    ]
    key_values = stats.get(top_value, [])
    key_value_info = []

    for key_value in cast(list[str], key_values):
        if len(key_value.split(" == ")) != 2:
            logger.warning(f"Invalid {key_name} value: {key_value}, skipping")
            continue
        key, value = key_value.split(" == ")
        if key == "" or value == "":
            continue
        if value.startswith("{") or value.startswith("["):
            value = f"'{value}'"
        key_value_info.append(
            {
                "key": f"{key_name}_value",
                "value": key_value,
                "query": f"and(eq({key_name}, '{key}'), eq({value_name}, {value}))",
            }
        )
        if not next((True for info in key_info if info["value"] == key), False):
            key_info.append(
                {
                    "key": key_name,
                    "value": key,
                    "query": f'eq({key_name}, "{key}")',
                }
            )
    return key_info, key_value_info


def map_stats(
    stats: Record,
    with_facets=False,
    max_values: int | None = None,
    bucketed_stats: dict[datetime, Any] | None = None,
) -> dict[str, Any]:
    stats = stats or defaultdict(lambda: None)
    if len(stats.get("latency_ptiles", [None, None])) != 2:
        latency_p50, latency_p99 = [None, None]
    else:
        latency_p50, latency_p99 = stats.get("latency_ptiles", [None, None])
        latency_p50 = _handle_nan(latency_p50)
        latency_p99 = _handle_nan(latency_p99)
    latency_avg = _handle_nan(stats.get("latency_avg"))
    first_token_ptiles = stats.get("first_token_ptiles", [None, None])
    first_token_ptiles = first_token_ptiles if first_token_ptiles else [None, None]
    first_token_p50, first_token_p99 = first_token_ptiles
    if first_token_p50 is not None and math.isnan(first_token_p50):
        first_token_p50 = None
    if first_token_p99 is not None and math.isnan(first_token_p99):
        first_token_p99 = None
    (
        feedback_key_score_info,
        feedback_key_value_info,
        feedback_key_info,
        metadata_key_value_info,
        metadata_key_info,
    ) = [], [], [], [], []
    feedback_values_sorted = process_feedback_value_stats(
        stats, max_values, bucketed_stats
    )
    if with_facets:
        feedback_key_info = [
            {
                "key": "feedback_key",
                "value": feedback_key,
                "query": f'eq(feedback_key, "{feedback_key}")',
            }
            for feedback_key in stats.get("top_10_feedback_key", [])
        ]
        feedback_key_scores = stats.get("top_10_feedback_key_score", [])
        for feedback_key_score in feedback_key_scores:
            if len(feedback_key_score.split(" == ")) != 2:
                # This shouldn't be possible unless the score is an empty string
                logger.warning(
                    f"Invalid feedback key score: {feedback_key_score}, skipping"
                )
                continue
            feedback_key, feedback_score = feedback_key_score.split(" == ")
            feedback_key_score_info.append(
                {
                    "key": "feedback_key_score",
                    "value": feedback_key_score,
                    "query": f'and(eq(feedback_key, "{feedback_key}"), eq(feedback_score, "{feedback_score}"))',
                }
            )
            if not next(
                (True for info in feedback_key_info if info["value"] == feedback_key),
                False,
            ):
                feedback_key_info.append(
                    {
                        "key": "feedback_key",
                        "value": feedback_key,
                        "query": f'eq(feedback_key, "{feedback_key}")',
                    }
                )
        feedback_key_values = stats.get("top_10_feedback_key_value", [])
        for feedback_key_value in feedback_key_values:
            if len(feedback_key_value.split(" == ")) != 2:
                # This shouldn't be possible unless the score is an empty string
                logger.warning(
                    f"Invalid feedback key value: {feedback_key_value}, skipping"
                )
                continue
            feedback_key, feedback_value = feedback_key_value.split(" == ")
            feedback_value = orjson.loads(feedback_value)
            feedback_key_value_info.append(
                {
                    "key": "feedback_value",
                    "value": feedback_key_value,
                    "query": f'and(eq(feedback_key, "{feedback_key}"), eq(feedback_value, "{feedback_value}"))',
                }
            )
            if not next(
                (True for info in feedback_key_info if info["value"] == feedback_key),
                False,
            ):
                feedback_key_info.append(
                    {
                        "key": "feedback_key",
                        "value": feedback_key,
                        "query": f'eq(feedback_key, "{feedback_key}")',
                    }
                )

        metadata_key_info, metadata_key_value_info = _construct_kv_stats(
            stats,
            "metadata_key",
            "metadata_value",
            "top_10_metadata_key",
            "top_10_metadata_key_value",
        )
        input_key_info, input_key_value_info = _construct_kv_stats(
            stats,
            "input_key",
            "input_value",
            "top_10_input_key",
            "top_10_input_key_value",
        )
        output_key_info, output_key_value_info = _construct_kv_stats(
            stats,
            "output_key",
            "output_value",
            "top_10_output_key",
            "top_10_output_key_value",
        )

    retval = dict(
        last_run_start_time=stats.get("last_run_start_time"),
        run_count=stats.get("run_count", 0),
        total_tokens=stats.get("total_tokens", 0),
        prompt_tokens=stats.get("prompt_tokens"),
        error_rate=stats.get("error_rate", 0),
        streaming_rate=stats.get("streaming_rate", 0),
        completion_tokens=stats.get("completion_tokens", 0),
        total_cost=stats.get("total_cost", 0),
        prompt_cost=stats.get("prompt_cost", 0),
        completion_cost=stats.get("completion_cost", 0),
        latency_p50=timedelta(seconds=latency_p50).total_seconds()
        if latency_p50 is not None
        else None,
        latency_p99=timedelta(seconds=latency_p99).total_seconds()
        if latency_p99 is not None
        else None,
        latency_avg=timedelta(seconds=latency_avg).total_seconds()
        if latency_avg is not None
        else None,
        first_token_p50=timedelta(seconds=first_token_p50).total_seconds()
        if first_token_p50 is not None
        else None,
        first_token_p99=timedelta(seconds=first_token_p99).total_seconds()
        if first_token_p99 is not None
        else None,
        feedback_stats={
            key: dict(n=n, avg=avg, stdev=stdev, errors=errors, values=values)
            for key, n, avg, stdev, errors, values in zip(
                stats.get("feedback_keys", []) or [],
                stats.get("feedback_counts", []) or [],
                stats.get("feedback_avgs", []) or [],
                stats.get("feedback_stdevs", []) or [],
                stats.get("feedback_errors", []) or [],
                feedback_values_sorted,
            )
        },
        run_facets=[
            *[
                {
                    "key": "name",
                    "value": name,
                    "query": f'eq(name, "{name}")',
                }
                for name in stats.get("top_10_name", [])
                if name != ""
            ],
            *[
                {
                    "key": "status",
                    "value": status,
                    "query": f'eq(status, "{status}")',
                }
                for status in stats.get("top_10_status", [])
                if status != ""
            ],
            *[
                {
                    "key": "run_type",
                    "value": run_type,
                    "query": f'eq(run_type, "{run_type}")',
                }
                for run_type in stats.get("top_10_run_type", [])
                if run_type != ""
            ],
            *[
                {
                    "key": "tag",
                    "value": tag,
                    "query": f'eq(tag, "{tag}")',
                }
                for tag in stats.get("top_10_tags", [])
                if tag != ""
            ],
            *feedback_key_info,
            *feedback_key_score_info,
            *feedback_key_value_info,
            *[
                {
                    "key": "feedback_source",
                    "value": feedback_source,
                    "query": f'eq(feedback_source, "{feedback_source}")',
                }
                for feedback_source in stats.get("top_10_feedback_source", [])
                if feedback_source != ""
            ],
            *metadata_key_info,
            *metadata_key_value_info,
            *input_key_info,
            *input_key_value_info,
            *output_key_info,
            *output_key_value_info,
        ]
        if with_facets
        else [],
    )
    if "median_tokens" in stats:
        median_tokens = _handle_nan(stats["median_tokens"])
        if median_tokens is not None:
            median_tokens = int(median_tokens)
        retval["median_tokens"] = median_tokens
    if "tokens_ptiles" in stats:
        median_tokens, tokens_p99 = stats["tokens_ptiles"] or [0, 0]
        median_tokens = _handle_nan(median_tokens)
        tokens_p99 = _handle_nan(tokens_p99)
        if median_tokens is not None:
            median_tokens = int(median_tokens)
        if tokens_p99 is not None:
            tokens_p99 = int(tokens_p99)
        retval.update({"median_tokens": median_tokens, "tokens_p99": tokens_p99})
    if "completion_tokens_ptiles" in stats:
        completion_tokens_p50, completion_tokens_p99 = stats[
            "completion_tokens_ptiles"
        ] or [0, 0]
        completion_tokens_p50 = _handle_nan(completion_tokens_p50)
        completion_tokens_p99 = _handle_nan(completion_tokens_p99)
        if completion_tokens_p50 is not None:
            completion_tokens_p50 = int(completion_tokens_p50)
        if completion_tokens_p99 is not None:
            completion_tokens_p99 = int(completion_tokens_p99)
        retval.update(
            {
                "completion_tokens_p50": completion_tokens_p50,
                "completion_tokens_p99": completion_tokens_p99,
            }
        )
    if "prompt_tokens_ptiles" in stats:
        prompt_tokens_p50, prompt_tokens_p99 = stats["prompt_tokens_ptiles"] or [
            0,
            0,
        ]
        prompt_tokens_p50 = _handle_nan(prompt_tokens_p50)
        prompt_tokens_p99 = _handle_nan(prompt_tokens_p99)
        if prompt_tokens_p50 is not None:
            prompt_tokens_p50 = int(prompt_tokens_p50)
        if prompt_tokens_p99 is not None:
            prompt_tokens_p99 = int(prompt_tokens_p99)
        retval.update(
            {
                "prompt_tokens_p50": prompt_tokens_p50,
                "prompt_tokens_p99": prompt_tokens_p99,
            }
        )
    if "cost_ptiles" in stats:
        cost_p50, cost_p99 = stats["cost_ptiles"] or [0.0, 0.0]
        cost_p50 = _handle_nan(cost_p50)
        cost_p99 = _handle_nan(cost_p99)
        retval.update({"cost_p50": cost_p50, "cost_p99": cost_p99})

    if stats.get("session_feedback_keys") is not None:
        retval["session_feedback_stats"] = {
            key: dict(n=n, avg=avg, stdev=stdev, errors=errors)
            for key, n, avg, stdev, errors in zip(
                stats["session_feedback_keys"] or [],
                stats["session_feedback_counts"] or [],
                stats["session_feedback_avgs"] or [],
                stats["session_feedback_stdevs"] or [],
                stats["session_feedback_errors"] or [],
            )
        }
    return retval


async def get_tracer_session_metadata_top_k(
    auth: AuthInfo,
    session_id: UUID,
    query_params: schemas.QueryParamsForTracerSessionMetadataSchema,
) -> Dict[str, List[str]]:
    """Given a session, a number K, and (optionally) a list of metadata keys, return the top K values for each key."""

    # if no default start time is provided, use the current time minus 30 days because that is the highest setting on the monitor page
    default_start_time = datetime.now() - timedelta(days=30)

    query = f"""
    SELECT
        key,
        topK({{k}})(value) as top_k_metadata_array
    FROM runs_metadata_kv
    WHERE session_id = {{session_id}}
        AND tenant_id = {{tenant_id}}
        AND start_time >= {{start_time}}
        {"AND is_root = true" if query_params.root_runs_only else ""}
    {"AND key IN {metadata_keys}" if query_params.metadata_keys else ""}
    GROUP BY key
    """
    params = {
        "k": query_params.k,
        "session_id": session_id,
        "metadata_keys": query_params.metadata_keys,
        "tenant_id": auth.tenant_id,
        "start_time": (query_params.start_time or default_start_time).strftime(
            CH_INSERT_TIME
        ),
    }
    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        rows = await ch.fetch(
            "fetch_top_k_metadata_for_tracer_session", query, params=params
        )
    return {
        row["key"]: row["top_k_metadata_array"]
        if isinstance(row["top_k_metadata_array"], list)
        else [row["top_k_metadata_array"]]
        for row in rows
        if row and row["top_k_metadata_array"]
    }


def _handle_nan(x: int | float | None) -> int | float | None:
    if x is None:
        return x
    elif math.isnan(x):
        return None
    else:
        return x
