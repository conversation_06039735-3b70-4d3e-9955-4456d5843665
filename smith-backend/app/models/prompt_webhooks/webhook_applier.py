"""Webhook applier for prompt commits."""

import asyncio
import logging
from dataclasses import asdict
from typing import Optional
from uuid import UUID

from app.api.auth.schemas import AuthInfo
from app.models.runs.rules_webhook import webhook_client
from app.schemas import PromptWebhookCore, PromptWebhookPayload

logger = logging.getLogger(__name__)


def get_relevant_webhooks(
    *,
    webhooks: list[PromptWebhookCore],
    prompt_id: str,
    ignore_webhook_ids: Optional[list[UUID]] = None,
) -> list[PromptWebhookCore]:
    """Filter webhooks that should be triggered for a given prompt.

    Args:
        webhooks: List of all webhooks to filter
        prompt_id: ID of the prompt to check against
        ignore_webhook_ids: List of webhook IDs to ignore

    Returns:
        List of webhooks that should be triggered for this prompt
    """
    return [
        webhook
        for webhook in webhooks
        if (
            # Check if webhook has COMMIT trigger
            "commit" in webhook.triggers
            and
            # Check if prompt is included (if include_prompts is set)
            (not webhook.include_prompts or prompt_id in webhook.include_prompts)
            and
            # Check if prompt is not excluded
            (not webhook.exclude_prompts or prompt_id not in webhook.exclude_prompts)
            and
            # Check if webhook is not in ignore_webhook_ids
            (not ignore_webhook_ids or webhook.id not in ignore_webhook_ids)
        )
    ]


async def apply_prompt_webhooks(
    *,
    auth: AuthInfo,
    payload: PromptWebhookPayload,
    ignore_webhook_ids: Optional[list[UUID]] = None,
    webhooks: Optional[list[PromptWebhookCore]] = None,
) -> None:
    """Apply webhooks for a prompt commit.

    Args:
        auth: The authentication info
        payload: The payload of the prompt commit
        ignore_webhook_ids: The webhooks to ignore
        webhooks: The specific webhooks to apply (optional, used for the test endpoint, will fetch if not provided)
    """
    if webhooks is not None:
        all_webhooks: list[PromptWebhookCore] = webhooks
    else:
        from app.models.prompt_webhooks import crud as prompt_webhooks_crud

        all_webhooks = await prompt_webhooks_crud.list_prompt_webhooks(
            auth, limit=None, offset=0
        )

    relevant_webhooks = get_relevant_webhooks(
        webhooks=all_webhooks,
        prompt_id=payload.prompt_id,
        ignore_webhook_ids=ignore_webhook_ids,
    )

    if not relevant_webhooks:
        return

    # Send webhooks in parallel
    results = await asyncio.gather(
        *(
            webhook_client().post(
                str(webhook.url),
                json=asdict(payload),
                headers=webhook.headers or {},
            )
            for webhook in relevant_webhooks
        ),
        return_exceptions=True,
    )
    # Log any errors
    errors = [repr(result) for result in results if isinstance(result, Exception)]
    if errors:
        logger.warning(
            "Error sending prompt webhooks", extra={"errors": errors}, exc_info=True
        )
