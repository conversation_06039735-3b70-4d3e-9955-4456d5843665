"""CRUD operations for prompt webhooks."""

import asyncio
from typing import List
from uuid import UUID, uuid4

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Response
from lc_config.settings import shared_settings as settings
from lc_database.database import asyncpg_conn
from starlette.status import HTTP_204_NO_CONTENT

from app import schemas
from app.api.auth import AuthInfo
from app.models.prompt_webhooks.webhook_applier import apply_prompt_webhooks
from app.models.utils.encryption import (
    decrypt_dict_values,
    decrypt_string,
    encrypt_dict_values,
    encrypt_string,
)
from app.retry import retry_asyncpg


@retry_asyncpg
async def list_prompt_webhooks(
    auth: AuthInfo, limit: int = 100, offset: int = 0
) -> List[schemas.PromptWebhook]:
    """List all prompt webhooks for the current tenant.

    Args:
        auth: AuthInfo object containing tenant information
        limit: Maximum number of webhooks to return (default: 100)
        offset: Number of webhooks to skip (default: 0)

    Returns:
        List of prompt webhooks
    """
    async with asyncpg_conn() as db:
        rows = await db.fetch(
            """
            SELECT * FROM prompt_webhooks
            WHERE tenant_id = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
            """,
            auth.tenant_id,
            limit,
            offset,
        )

        sem = asyncio.Semaphore(settings.MAX_CONCURRENCY_PROMPT_WEBHOOKS)

        async def _build(row):
            async with sem:
                data = dict(row)
                data["headers"] = await decrypt_dict_values(data["headers"])
                data["url"] = await decrypt_string(data["url"])
                return schemas.PromptWebhook(**data)

        return await asyncio.gather(*[_build(r) for r in rows])


@retry_asyncpg
async def get_prompt_webhook(auth: AuthInfo, webhook_id: UUID) -> schemas.PromptWebhook:
    """Get a specific prompt webhook."""
    async with asyncpg_conn() as db:
        row = await db.fetchrow(
            """
            SELECT * FROM prompt_webhooks
            WHERE tenant_id = $1 AND id = $2
            """,
            auth.tenant_id,
            webhook_id,
        )
        if not row:
            raise HTTPException(status_code=404, detail="Webhook not found")
        webhook = dict(row)
        webhook["headers"] = await decrypt_dict_values(webhook["headers"])
        webhook["url"] = await decrypt_string(webhook["url"])
        return schemas.PromptWebhook(**webhook)


@retry_asyncpg
async def create_prompt_webhook(
    auth: AuthInfo, webhook: schemas.PromptWebhookCreate
) -> schemas.PromptWebhook:
    """Create a new prompt webhook."""
    async with asyncpg_conn() as db:
        # Check if the tenant has reached the maximum number of prompt webhooks
        if (
            await db.fetchval(
                "select count(*) from prompt_webhooks where tenant_id = $1",
                auth.tenant_id,
            )
            >= auth.tenant_config.organization_config.max_prompt_webhooks
        ):
            raise HTTPException(
                status_code=400,
                detail="Maximum number of prompt webhooks exceeded, <NAME_EMAIL> to increase limit",
            )

        encrypted_headers = await encrypt_dict_values(webhook.headers)
        encrypted_url = await encrypt_string(str(webhook.url))

        webhook_id = webhook.id if webhook.id is not None else uuid4()

        query = """
            INSERT INTO prompt_webhooks (
                id, tenant_id, include_prompts, exclude_prompts, url, headers, triggers
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        """
        params = [
            webhook_id,
            auth.tenant_id,
            webhook.include_prompts,
            webhook.exclude_prompts,
            encrypted_url,
            encrypted_headers,
            webhook.triggers,
        ]

        row = await db.fetchrow(query, *params)
        created_webhook = dict(row)
        created_webhook["headers"] = await decrypt_dict_values(
            created_webhook["headers"]
        )
        created_webhook["url"] = await decrypt_string(created_webhook["url"])
        return schemas.PromptWebhook(**created_webhook)


@retry_asyncpg
async def update_prompt_webhook(
    auth: AuthInfo, webhook_id: UUID, webhook: schemas.PromptWebhookUpdate
) -> schemas.PromptWebhook:
    """Update a specific prompt webhook."""
    async with asyncpg_conn() as db:
        # Get existing webhook to merge with updates
        existing = await db.fetchrow(
            """
            SELECT * FROM prompt_webhooks
            WHERE tenant_id = $1 AND id = $2
            """,
            auth.tenant_id,
            webhook_id,
        )
        if not existing:
            raise HTTPException(status_code=404, detail="Webhook not found")

        # Decrypt existing headers before potentially reusing them
        existing_headers = await decrypt_dict_values(existing["headers"])

        # Replace headers with updates
        headers = webhook.headers if webhook.headers is not None else existing_headers
        encrypted_headers = await encrypt_dict_values(headers)

        # Encrypt URL if provided
        url = (
            await encrypt_string(str(webhook.url)) if webhook.url is not None else None
        )

        # Update webhook
        row = await db.fetchrow(
            """
            UPDATE prompt_webhooks
            SET
                include_prompts = COALESCE($3, include_prompts),
                exclude_prompts = COALESCE($4, exclude_prompts),
                url = COALESCE($5, url),
                headers = $6,
                triggers = COALESCE($7, triggers),
                updated_at = NOW()
            WHERE tenant_id = $1 AND id = $2
            RETURNING *
            """,
            auth.tenant_id,
            webhook_id,
            webhook.include_prompts,
            webhook.exclude_prompts,
            url,
            encrypted_headers,
            webhook.triggers,
        )
        updated_webhook = dict(row)
        updated_webhook["headers"] = await decrypt_dict_values(
            updated_webhook["headers"]
        )
        updated_webhook["url"] = await decrypt_string(updated_webhook["url"])
        return schemas.PromptWebhook(**updated_webhook)


@retry_asyncpg
async def delete_prompt_webhook(auth: AuthInfo, webhook_id: UUID) -> Response:
    """Delete a specific prompt webhook."""
    async with asyncpg_conn() as db:
        result = await db.execute(
            """
            DELETE FROM prompt_webhooks
            WHERE tenant_id = $1 AND id = $2
            """,
            auth.tenant_id,
            webhook_id,
        )
        if result == "DELETE 0":
            raise HTTPException(status_code=404, detail="Webhook not found")
        else:
            return Response(status_code=HTTP_204_NO_CONTENT)


@retry_asyncpg
async def test_prompt_webhook(
    auth: AuthInfo,
    webhook: schemas.PromptWebhookTest,
) -> None:
    """Test a specific prompt webhook."""
    await apply_prompt_webhooks(
        auth=auth,
        payload=webhook.payload,
        webhooks=[webhook.webhook],
    )
