"""Translate filter directives to something that can execute the operation it
describes, eg. SQL.

eg. Comparison(comparator=Comparator.EQ, attribute="name", value="foo")
    -> "name = 'foo'"

Currently implemented:
- Abstract SQL
- Clickhouse SQL
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from collections import defaultdict
from datetime import datetime
from typing import (
    Any,
    Callable,
    Dict,
    List,
    Literal,
    NamedTuple,
    Tuple,
    cast,
)
from uuid import UUID, uuid4

import orjson
from langchain.chains.query_constructor.ir import FilterDirective, Operation
from pydantic import BaseModel, Field

from app.models.feedback.utils import normalize_feedback_key
from app.models.query_lang.parse import Comparator, Comparison, Operator
from app.models.query_lang.visitor import Visitor


class BaseAttributeInfo(BaseModel, ABC):
    """Information about a data source attribute."""

    name: str | None
    description: str | None = None
    type: str | None = None

    @abstractmethod
    def translate(self, comparison: Comparison, visitor: Visitor) -> Any:
        """Translate a Comparison."""


def identity(x: Any) -> Any:
    return x


PUSHDOWN_JOIN: Literal["__PUSHDOWN_JOIN__"] = "__PUSHDOWN_JOIN__"
PUSHDOWN_SUBQUERY: Literal["__PUSHDOWN_SUBQUERY__"] = "__PUSHDOWN_SUBQUERY__"


class SqlResult(NamedTuple):
    sqls: dict[
        tuple[str, str]
        | Literal["__PUSHDOWN_JOIN__"]
        | Literal["__PUSHDOWN_SUBQUERY__"]
        | None,
        str,
    ]
    params: dict[str, Any]


subquery_skip_tables = [
    "runs_metadata_kv",
    "runs_tags",
    "runs_inputs_kv",
    "runs_outputs_kv",
]


def comparison_sql(
    comparison: Comparison, expression: str, param_name: str, ignore_negative: bool
) -> str:
    if comparison.comparator == Comparator.NEQ and ignore_negative:
        sql = f"{expression} = {param_name}"
    elif comparison.comparator == Comparator.NOTLIKE and ignore_negative:
        sql = f"{expression} LIKE {param_name}"
    elif comparison.comparator == Comparator.EQ:
        sql = f"{expression} = {param_name}"
    elif comparison.comparator == Comparator.NEQ:
        sql = f"{expression} != {param_name}"
    elif comparison.comparator == Comparator.GT:
        sql = f"{expression} > {param_name}"
    elif comparison.comparator == Comparator.GTE:
        sql = f"{expression} >= {param_name}"
    elif comparison.comparator == Comparator.LT:
        sql = f"{expression} < {param_name}"
    elif comparison.comparator == Comparator.LTE:
        sql = f"{expression} <= {param_name}"
    elif comparison.comparator == Comparator.LIKE:
        sql = f"{expression} LIKE {param_name}"
    elif comparison.comparator == Comparator.NOTLIKE:
        sql = f"{expression} NOT LIKE {param_name}"
    elif comparison.comparator == Comparator.IN:
        sql = f"{expression} IN {param_name}"
    elif comparison.comparator == Comparator.EXISTS:
        if comparison.value:
            sql = f"{expression} IS NOT NULL"
        else:
            sql = f"{expression} IS NULL"
    else:
        raise ValueError(f"Comparison {comparison} not accepted.")
    return sql


class SqlAttributeInfo(BaseAttributeInfo):
    name: str

    sql_value: Callable[[Any], Any] = identity

    sql_expression: str | None = None

    sql_join_table: str | None = None

    sql_join_method: Literal["join", "subquery"] = "join"

    sql_join_key: str | None = None

    sql_subquery_key: str | None = None

    support_multi: bool = False

    paired_attribute: bool = False

    # if true, use subquery instead of join
    use_subquery: bool = False

    subquery_skip_expr: str | None = None

    def translate(self, comparison: Comparison, visitor: SqlVisitor) -> SqlResult:
        key: tuple[str, str] | None = None
        if (
            self.sql_join_table
            and self.use_subquery
            and self.sql_expression is not None
        ):
            join_alias = visitor.sql_join_alias(self)
            potential_subqueries = {
                (k[0], k[1]): k
                for k in visitor.sql_subquery_tables.keys()
                if not visitor.sql_subquery_tables[k]["use_not_in"]
            }
            separate_paired_attribute = (
                self.sql_join_table,
                join_alias,
            ) not in potential_subqueries
            # if the paired attribute needs a separate subquery,
            # then re-compute the join alias to use a new subquery
            key = (
                self.sql_join_table,
                join_alias
                if (not self.paired_attribute or not separate_paired_attribute)
                else visitor.sql_join_alias(self),
            )
        elif self.sql_join_table and self.sql_join_method == "join":
            key = (self.sql_join_table, visitor.sql_join_alias(self))

        param_name = f"{self.name}_{visitor.param_suffix}_{comparison.comparator.value}"
        if key:
            param_name = f"{key[1]}_{param_name}"
        elif self.sql_join_table and self.sql_join_method == "subquery":
            param_name = f"{visitor.sql_join_alias(self)}_{param_name}"
        if comparison.multi or self.support_multi:
            param_name += f"_{str(uuid4())[:5]}"
        try:
            params = (
                {}
                if comparison.comparator == Comparator.EXISTS
                else {param_name: [self.sql_value(value) for value in comparison.value]}
                if comparison.comparator == Comparator.IN
                else {param_name: self.sql_value(comparison.value)}
            )
        except ValueError:
            raise ValueError(
                f"Incorrect type for value {comparison.value}, attribute {comparison.attribute} for comparator {comparison.comparator}."
            )

        if (
            self.use_subquery
            and isinstance(key, tuple)
            and key not in visitor.sql_subquery_tables
        ):
            visitor.sql_subquery_tables[key] = {
                "table": self.sql_join_table,
                "use_not_in": comparison.comparator == Comparator.NEQ
                or comparison.comparator == Comparator.NOTLIKE,
                "subquery_skip_expr": self.subquery_skip_expr.replace(
                    "{var}", visitor.sql_param(param_name)
                )
                if (
                    self.subquery_skip_expr is not None
                    and comparison.comparator in [Comparator.EQ, Comparator.NEQ]
                )
                else None,
            }

        expression = (
            self.name
            if self.sql_join_method == "subquery"
            else self.sql_expression or self.name
        )

        ignore_negative = (
            visitor.sql_subquery_tables[key]["use_not_in"]
            if key in visitor.sql_subquery_tables and isinstance(key, tuple)
            else False
        )
        sql = comparison_sql(
            comparison, expression, visitor.sql_param(param_name), ignore_negative
        )
        if self.sql_join_table and self.sql_join_method == "subquery":
            out_projection = ", ".join(
                [a.sql_expression or a.name for a in visitor.join_keys]
            )
            in_projection = ", ".join([a.name for a in visitor.join_keys])
            sql = f"""({out_projection}) IN (
    SELECT {in_projection}
    FROM {self.sql_join_table}
    WHERE {sql} $PUSHDOWN
)"""

        if self.sql_join_key:
            pushdown_join_sql = comparison_sql(
                comparison,
                self.sql_join_key,
                visitor.sql_param(param_name),
                ignore_negative,
            )
            # tables that we "join" to by subquery method by definition
            # have the join keys column names matching those on root table
            # so we maintain a separate pushdown list for each join method
            pushdown_subquery_sql = comparison_sql(
                comparison,
                self.name or self.sql_join_key,
                visitor.sql_param(param_name),
                ignore_negative,
            )
            return SqlResult(
                {
                    key: sql,
                    PUSHDOWN_JOIN: pushdown_join_sql,
                    PUSHDOWN_SUBQUERY: pushdown_subquery_sql,
                },
                params,
            )
        else:
            return SqlResult({key: sql}, params)


class AltAttributeInfo(BaseAttributeInfo):
    alt: Callable[[Comparison], FilterDirective]

    def translate(self, comparison: Comparison, visitor: Visitor) -> SqlResult:
        try:
            return self.alt(comparison).accept(visitor)
        except AssertionError:
            raise ValueError(f"Comparison {comparison} not accepted.")


def _param_unique_suffix() -> str:
    return str(uuid4())[:5]


class PostgresAttributeInfo(BaseAttributeInfo):
    sql_value: Callable[[Any], Any] = identity
    sql_expression: str | None = None

    def translate(self, comparison: Comparison, visitor: SqlVisitor) -> SqlResult:
        """Currently, only supports HAS and EXISTS comparisons."""
        params = {}
        expression = self.sql_expression or self.name
        if comparison.comparator == Comparator.HAS:
            param_name = f"{self.name}_{_param_unique_suffix()}_has"
            sql = f"{expression} @> {visitor.sql_param(param_name)}"
            try:
                params[param_name] = orjson.loads(comparison.value)
            except orjson.JSONDecodeError:
                raise ValueError(
                    f"Cannot decode JSON for HAS comparison: {comparison.value}"
                )
        elif comparison.comparator == Comparator.EXISTS:
            param_name = f"{self.name}_{_param_unique_suffix()}_exists"
            sql = f"{expression} ? {visitor.sql_param(param_name)}"
            params[param_name] = comparison.value
        elif comparison.comparator == Comparator.NEQ:
            param_name = f"{self.name}_{_param_unique_suffix()}_neq"
            sql = f"{expression} != {visitor.sql_param(param_name)}"
            params[param_name] = comparison.value
        else:
            raise ValueError(f"Comparison {comparison} not accepted.")
        return SqlResult({None: sql}, params)


class ExampleAttributeInfoPg(PostgresAttributeInfo):
    """Attribute information for examples table in Postgres."""


class TracerSessionAttributeInfoPg(PostgresAttributeInfo):
    """Attribute information for tracer session table in Postgres."""


class SqlVisitor(Visitor):
    attributes: list[BaseAttributeInfo]

    main_table: str

    main_table_suffix: str = ""

    param_suffix: str = ""

    sql_join_projection_columns: dict[str, list[str]] = Field(default_factory=dict)

    sql_subquery_tables: dict[tuple[str, str | None], dict] = {}

    sql_subquery_skip_enabled: bool = False

    @abstractmethod
    def sql_param(self, name: str) -> str: ...

    @abstractmethod
    def sql_join_str(
        self, table: str, alias: str, pushdown: str, where: str
    ) -> str: ...

    @abstractmethod
    def sql_subquery_str(
        self, table: str, pushdown: str, where: str, use_not_in: bool
    ) -> str: ...

    @abstractmethod
    def sql_runs_times_cte_str(self, table: str, pushdown: str, where: str) -> str: ...

    joins: defaultdict[str, int] = Field(default_factory=lambda: defaultdict(int))

    depth: int = 0
    depth_counts: defaultdict[int, int] = Field(
        default_factory=lambda: defaultdict(int)
    )
    seen: defaultdict[str, int] = Field(default_factory=lambda: defaultdict(int))

    @property
    def join_keys(self) -> list[SqlAttributeInfo]:
        return [
            a
            for a in self.attributes
            if isinstance(a, SqlAttributeInfo) and a.sql_join_key is not None
        ]

    @property
    def subquery_keys(self) -> list[SqlAttributeInfo]:
        return [
            a
            for a in self.attributes
            if isinstance(a, SqlAttributeInfo) and a.sql_subquery_key is not None
        ]

    def sql_join_alias(self, attr: SqlAttributeInfo) -> str:
        """In order to support multiple operations on the same joined attribute,
        we need to join to the same table multiple times, but with different aliases.
        This routine generates a unique alias for each join, keyed by the attribute
        name and operation depth."""
        assert attr.sql_join_table is not None
        key = f"{attr.sql_join_table}_{self.depth}_{self.depth_counts[self.depth]}"
        seen_key = f"{key}_{attr.name}"
        self.seen[seen_key] += 1
        return f"{key}_{self.seen[seen_key]}"

    def visit_comparison(self, comparison: Comparison) -> SqlResult:
        try:
            info = next(a for a in self.attributes if a.name == comparison.attribute)
        except StopIteration:
            raise ValueError(f"Attribute {comparison.attribute} not accepted.")

        return info.translate(comparison, self)

    def sql_min_max_time_filtered_runs_cte(
        self,
        sqls: dict[
            tuple[str, str]
            | Literal["__PUSHDOWN_JOIN__"]
            | Literal["__PUSHDOWN_SUBQUERY__"]
            | None,
            str,
        ],
        join_pushdown: str,
    ) -> str:
        has_negative_query = any(
            k in self.sql_subquery_tables and self.sql_subquery_tables[k]["use_not_in"]
            for k, _ in sqls.items()
            if k is not None and k != PUSHDOWN_JOIN and k != PUSHDOWN_SUBQUERY
        )

        intersected_runs_times_cte = " INTERSECT ".join(
            self.sql_runs_times_cte_str(
                k[0],
                join_pushdown,
                v,
            )
            for k, v in sqls.items()
            if k is not None and k != PUSHDOWN_JOIN and k != PUSHDOWN_SUBQUERY
        )

        min_max_start_times_cte = (
            (
                """
            WITH runs_and_times as (
                """
                + intersected_runs_times_cte
                + """
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,"""
            )
            if (intersected_runs_times_cte and not has_negative_query)
            else ""
        )

        return min_max_start_times_cte

    def visit_operation(
        self, operation: Operation
    ) -> SqlResult | tuple[str, dict, str, str, str, str]:
        sqls: defaultdict[
            tuple[str, str]
            | Literal["__PUSHDOWN_JOIN__"]
            | Literal["__PUSHDOWN_SUBQUERY__"]
            | None,
            set[str],
        ] = defaultdict(set)
        params: dict[str, Any] = {}
        self.depth += 1
        self.depth_counts[self.depth] += 1
        for arg in operation.arguments:
            # visit each argument
            arg_sqls, arg_params = arg.accept(self)
            # validate no conflicting parameters
            for param_key, param_value in arg_params.items():
                if param_key in params:
                    if isinstance(param_value, list) and isinstance(
                        params[param_key], list
                    ):
                        if all(pv in params[param_key] for pv in param_value):
                            # new is subset of existing
                            params[param_key] = param_value
                        elif all(pv in param_value for pv in params[param_key]):
                            # existing is subset of new
                            continue
                        else:
                            raise ValueError(
                                f"Parameter {param_key} has conflicting values "
                                f"{param_value} and {params[param_key]}."
                            )
                    elif param_value != params[param_key]:
                        if param_key.endswith("_gte"):
                            params[param_key] = max(param_value, params[param_key])
                        elif param_key.endswith("_lte"):
                            params[param_key] = min(param_value, params[param_key])
                        elif param_key.endswith("_gt"):
                            params[param_key] = max(param_value, params[param_key])
                        elif param_key.endswith("_lt"):
                            params[param_key] = min(param_value, params[param_key])
                        else:
                            raise ValueError(
                                f"Parameter {param_key} has conflicting values "
                                f"{param_value} and {params[param_key]}."
                            )
            # merge with existing
            params.update(arg_params)
            for key, sql in arg_sqls.items():
                if (
                    key in self.sql_subquery_tables
                    and isinstance(key, tuple)
                    and key[0] in subquery_skip_tables
                ):
                    # Check if we can skip the subquery
                    self.sql_subquery_tables[key]["can_skip_subquery"] = (
                        self.sql_subquery_tables[key].get("can_skip_subquery", True)
                        and "value" not in sql
                    )

                sqls[key].add(sql)
        self.depth -= 1
        if operation.operator == Operator.AND:
            result = SqlResult(
                {
                    key: " AND ".join(f"({sql})" for sql in sorted(sqls_for_key))
                    for key, sqls_for_key in sqls.items()
                },
                params,
            )
        elif operation.operator == Operator.OR:
            if len(sqls) > 1:
                raise ValueError(
                    f"OR operator cannot be used across different tables: {operation}"
                )
            result = SqlResult(
                {
                    key: " OR ".join(f"({sql})" for sql in sorted(sqls_for_key))
                    for key, sqls_for_key in sqls.items()
                },
                params,
            )
        elif operation.operator == Operator.NOT:
            result = SqlResult(
                {
                    key: "NOT ("
                    + " OR ".join(f"({sql})" for sql in sorted(sqls_for_key))
                    + ")"
                    for key, sqls_for_key in sqls.items()
                },
                params,
            )
        else:
            raise ValueError(f"Operator {operation.operator} not accepted.")

        if self.depth == 0:
            if None not in result.sqls:
                raise ValueError(
                    f"Top-level operation must have at least one filter on the main table: {operation}"
                )
            join_pushdown = result.sqls.get(PUSHDOWN_JOIN, "")

            joins = ""
            subquery_filters = ""

            filtered_keys = [
                k
                for k in result.sqls.keys()
                if isinstance(k, tuple) and k in self.sql_subquery_tables
            ]
            can_use_subquery_instead_of_join = bool(
                filtered_keys and all(filtered_keys)
            )

            # check if we have a column that can use subquery filter instead of inner join
            if can_use_subquery_instead_of_join:
                subquery_filters = " AND ".join(
                    self.sql_subquery_str(
                        k[0],
                        join_pushdown,
                        v,
                        self.sql_subquery_tables[k]["use_not_in"]
                        if isinstance(k, tuple) and k in self.sql_subquery_tables
                        else False,
                    )
                    for k, v in result.sqls.items()
                    if k is not None
                    and k != PUSHDOWN_JOIN
                    and k != PUSHDOWN_SUBQUERY
                    and (
                        k not in self.sql_subquery_tables
                        or not (
                            self.sql_subquery_skip_enabled
                            and self.sql_subquery_tables[k].get(
                                "can_skip_subquery", False
                            )
                            and self.sql_subquery_tables[k].get("subquery_skip_expr")
                        )
                    )
                )
            else:
                joins = "\n".join(
                    self.sql_join_str(k[0], k[1], join_pushdown, v)
                    for k, v in result.sqls.items()
                    if k is not None and k != PUSHDOWN_JOIN and k != PUSHDOWN_SUBQUERY
                )

            subquery_pushdown = result.sqls.get(PUSHDOWN_SUBQUERY, "")

            min_max_start_times_cte = self.sql_min_max_time_filtered_runs_cte(
                result.sqls,
                join_pushdown,
            )
            runs_table_filter = result.sqls[None].replace(
                "$PUSHDOWN", f"AND {subquery_pushdown}" if subquery_pushdown else ""
            )

            for k, _ in result.sqls.items():
                if (
                    k in self.sql_subquery_tables
                    and isinstance(k, tuple)
                    and self.sql_subquery_skip_enabled
                    and self.sql_subquery_tables[k].get("can_skip_subquery")
                    and self.sql_subquery_tables[k].get("subquery_skip_expr")
                ):
                    runs_table_filter += f" AND {'NOT' if self.sql_subquery_tables[k]['use_not_in'] else ''} {self.sql_subquery_tables[k]['subquery_skip_expr'].replace('var', k[1])}"

            return (
                "\n".join(
                    filter(
                        None,
                        [
                            f"FROM {self.main_table}{f' {self.main_table_suffix}' if self.main_table_suffix else ''}",
                            joins,
                            f"WHERE {runs_table_filter}",
                            (" AND " + subquery_filters) if subquery_filters else "",
                        ],
                    )
                ),
                result.params,
                join_pushdown,
                subquery_pushdown,
                min_max_start_times_cte,
                runs_table_filter,
            )
        else:
            return result


class SqlVisitorClickhouse(SqlVisitor):
    def sql_param(self, name: str) -> str:
        return "{" + name + "}"

    def sql_join_str(self, table: str, alias: str, pushdown: str, where: str) -> str:
        join_cond = " AND ".join(
            f"{alias}.{a.sql_join_key} = {a.sql_expression or a.name}"
            for a in self.join_keys
        )
        projection_cols = cast(
            list[str], [a.sql_join_key for a in self.join_keys]
        ) + self.sql_join_projection_columns.get(table, [])
        projection = ", ".join(projection_cols)

        return f"""INNER JOIN (
    SELECT DISTINCT {projection}
    FROM {table}
    WHERE {pushdown}
    AND {where}
) AS {alias} ON {join_cond}"""

    def sql_subquery_str(
        self, table: str, pushdown: str, where: str, use_not_in: bool
    ) -> str:
        runs_cols = cast(list[str], [a.sql_expression for a in self.subquery_keys])
        run_cols_str = ", ".join(runs_cols)

        projection_cols = cast(
            list[str], [a.sql_subquery_key for a in self.subquery_keys]
        ) + self.sql_join_projection_columns.get(table, [])
        projection = ", ".join(projection_cols)

        return f"""({run_cols_str}) {("NOT IN" if use_not_in else "IN")} (
            SELECT {projection}
            FROM {table}
            WHERE {pushdown}
            AND {where}
        )"""

    def sql_runs_times_cte_str(self, table: str, pushdown: str, where: str) -> str:
        return f"""
                    SELECT
                        run_id,
                        start_time
                    FROM
                        {table}
                    WHERE {pushdown}
                    AND {where}
        """


class SqlVisitorPostgres(SqlVisitorClickhouse):
    """Subject to change, only difference from CH is the parameter syntax."""

    def sql_param(self, name: str) -> str:
        return f"${name}"


def convert_datetime(x: str | datetime) -> str:
    if isinstance(x, datetime):
        return x.strftime("%Y-%m-%d %H:%M:%S.%f")
    elif x == "today":
        return (
            datetime.now()
            .replace(hour=0, minute=0, second=0, microsecond=0)
            .strftime("%Y-%m-%d %H:%M:%S.%f")
        )
    else:
        return datetime.fromisoformat(x).strftime("%Y-%m-%d %H:%M:%S.%f")


def convert_interval(x: str | float) -> float:
    if isinstance(x, str):
        return float(x.strip("s"))
    else:
        return x


def convert_lower(x: str) -> str:
    return x.lower()


def convert_uuid(x: str | UUID) -> str:
    if isinstance(x, UUID):
        return str(x)

    if isinstance(x, str):
        return str(UUID(x))


def convert_primary_key_attribute_value(x: Any) -> Any:
    if isinstance(x, UUID) or isinstance(x, str):
        return str(x)
    elif (isinstance(x, list) or isinstance(x, set)) and all(
        (isinstance(v, UUID) or isinstance(v, str)) for v in x
    ):
        return [str(v) for v in x]
    elif isinstance(x, datetime):
        return convert_datetime(x)
    else:
        return x


##############################################
# Translate session feedback operation to CH #
##############################################
def translate_session_feedback_operation_to_ch_query(
    operation: FilterDirective, tenant_id: UUID
) -> Tuple[str, Dict[str, Any]]:
    """
    Translates a top-level AND operation:
    Operation(
        operator=Operator.AND,
        arguments=(
            Operation(
                operator=Operator.AND,
                arguments=(
                    Comparison(Comparator.EQ, "feedback_key", "hello"),
                    Comparison(Comparator.LTE, "avg_feedback_score", 0.5),
                ),
            ),
            Operation(
                operator=Operator.AND,
                arguments=(
                    Comparison(Comparator.EQ, "feedback_key", "world"),
                    Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                ),
            ),
        ),
    ),

    into a SQL query:
    SELECT session_id FROM (
        SELECT
            tenant_id,
            session_id,
            avgIf(score, key = 'hello') AS avg_hello,
            avgIf(score, key = 'world') AS avg_world
        FROM
            feedbacks_rmt FINAL
        WHERE
            tenant_id = 'tenant_id'
            AND key IN ('hello', 'world')
        GROUP BY tenant_id, session_id
        HAVING
            avg_hello <= 0.5 AND avg_hello IS NOT NULL
            AND avg_world >= 0.5 AND avg_world IS NOT NULL
    )
    """
    if not isinstance(operation, Operation):
        raise ValueError("Top-level operation must be an Operation")
    if operation.operator != Operator.AND:
        raise ValueError("Top-level operation must be AND")
    if not isinstance(operation.arguments[0], Operation):
        operation = Operation(operator=Operator.AND, arguments=[operation])

    conditions_list = []

    for sub_op in operation.arguments:
        if not isinstance(sub_op, Operation) or sub_op.operator != Operator.AND:
            raise ValueError("Sub-operations must be AND operations")

        feedback_key = None
        avg_score_conditions = []

        if len(sub_op.arguments) != 2:
            raise ValueError(
                "Sub-operations must contain two Comparisons, one for feedback_key and one for avg_feedback_score"
            )

        for comp in sub_op.arguments:
            if isinstance(comp, Comparison):
                if comp.attribute == "feedback_key":
                    if comp.comparator != Comparator.EQ:
                        raise ValueError(
                            "feedback_key comparison must use EQ comparator"
                        )
                    feedback_key = comp.value
                elif comp.attribute == "avg_feedback_score":
                    operator_symbol = _map_comparator(comp.comparator)
                    avg_score_conditions.append((operator_symbol, comp.value))
                else:
                    raise ValueError(
                        "Sub-operations must contain feedback_key and avg_feedback_score comparisons"
                    )
            else:
                raise ValueError("Sub-operations must contain Comparisons")

        if not feedback_key:
            raise ValueError(
                "Each sub-operation must contain a feedback_key comparison"
            )
        if not avg_score_conditions:
            raise ValueError(
                "Each sub-operation must contain avg_feedback_score comparisons"
            )

        conditions_list.append(
            {
                "feedback_key": normalize_feedback_key(feedback_key),
                "conditions": avg_score_conditions,
            }
        )

    return _generate_session_by_feedback_ch_query(conditions_list, tenant_id)


def _map_comparator(comparator: Comparator) -> str:
    comparator_map = {
        Comparator.EQ: "=",
        Comparator.NEQ: "!=",
        Comparator.GT: ">",
        Comparator.GTE: ">=",
        Comparator.LT: "<",
        Comparator.LTE: "<=",
    }
    operator_symbol = comparator_map.get(comparator)
    if operator_symbol is None:
        raise ValueError(f"Unsupported comparator: {comparator}")
    return operator_symbol


def _generate_session_by_feedback_ch_query(
    conditions_list: List[Dict[str, Any]], tenant_id: UUID
) -> Tuple[str, Dict[str, Any]]:
    params = {}

    if len(conditions_list) == 0:
        raise ValueError("No conditions provided")

    feedback_keys = [entry["feedback_key"] for entry in conditions_list]
    key_aliases = {key: f"avg_feedback_{i}" for i, key in enumerate(feedback_keys)}

    avg_ifs = []
    for key, alias in key_aliases.items():
        param_name = f"feedback_key_{_param_unique_suffix()}_eq"
        avg_ifs.append("avgIf(score, key = {" + param_name + "}) AS " + alias)
        params[param_name] = key

    select_clauses = [
        "tenant_id",
        "session_id",
    ] + avg_ifs

    in_param_name = f"feedback_keys_{_param_unique_suffix()}_in"
    tenant_id_param_name = "tenant_id_eq"
    where_clauses = [
        "tenant_id = {" + tenant_id_param_name + "}",
        "key IN {" + in_param_name + "}",
    ]
    params[in_param_name] = [key for key in feedback_keys]
    params[tenant_id_param_name] = tenant_id

    group_by_clause = "GROUP BY tenant_id, session_id"

    having_conditions = []
    for entry in conditions_list:
        key = entry["feedback_key"]
        alias = key_aliases[key]
        for operator, value in entry["conditions"]:
            score_param_name = f"{alias}_{_param_unique_suffix()}_score"
            condition = f"{alias} {operator} " + "{" + score_param_name + "}"
            having_conditions.append(condition)
            params[score_param_name] = value
        # Ensure the avgIf result is not NULL (session has feedback for this key)
        having_conditions.append(f"{alias} IS NOT NULL")

    # Build final query
    query = f"""
SELECT session_id FROM (
    SELECT
        {", ".join(select_clauses)}
    FROM
        feedbacks_rmt FINAL
    WHERE
        {" AND ".join(where_clauses)}
    {group_by_clause}
    HAVING
        {" AND ".join(having_conditions)}
)
"""
    return query.strip(), params
