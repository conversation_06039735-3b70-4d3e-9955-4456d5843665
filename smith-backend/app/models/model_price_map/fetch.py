import uuid

from lc_database import database

from app import schemas
from app.retry import retry_asyncpg


@retry_asyncpg
async def fetch_model_price_map(
    tenant_id: uuid.UUID,
) -> list[schemas.ModelPriceMapSchema]:
    async with database.asyncpg_conn() as db:
        rows = await db.fetch(
            """
            WITH
            unioned as (
                SELECT *
                FROM (
                    SELECT id, name, start_time, match_path, match_pattern, prompt_cost, completion_cost, prompt_cost_details, completion_cost_details, tenant_id, provider, priority_order
                    FROM model_price_map
                    WHERE tenant_id = $1
                    ORDER BY priority_order DESC
                ) as tenant_prices
                UNION ALL
                SELECT * FROM (
                    SELECT id, name, start_time, match_path, match_pattern, prompt_cost, completion_cost, prompt_cost_details, completion_cost_details, tenant_id, provider, -1000 + priority_order
                    FROM model_price_map
                    WHERE tenant_id is NULL
                ) AS global_prices
            )
            SELECT *
            FROM unioned
            ORDER BY priority_order DESC
            """,
            tenant_id,
        )

        return [schemas.ModelPriceMapSchema(**row) for row in rows]
