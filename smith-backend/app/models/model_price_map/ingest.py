import uuid

import asyncpg
from fastapi import HTTPException
from lc_database import database

from app import schemas
from app.retry import retry_asyncpg


@retry_asyncpg
async def create_model_price_map(
    payload: schemas.ModelPriceMapCreateSchema,
    tenant_id: uuid.UUID,
) -> schemas.ModelPriceMapSchema:
    try:
        async with database.asyncpg_conn() as db:
            row = await db.fetchrow(
                """
                INSERT INTO model_price_map (tenant_id, name, start_time, match_path, match_pattern, prompt_cost, completion_cost, prompt_cost_details, completion_cost_details, provider, priority_order)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NULLIF($10, ''), (SELECT COALESCE(MAX(priority_order), 0) + 1 FROM model_price_map WHERE tenant_id = $1))
                RETURNING *
            """,
                tenant_id,
                payload.name,
                payload.start_time,
                payload.match_path,
                payload.match_pattern,
                payload.prompt_cost,
                payload.completion_cost,
                {k: str(v) for k, v in payload.prompt_cost_details.items()}
                if payload.prompt_cost_details is not None
                else None,
                {k: str(v) for k, v in payload.completion_cost_details.items()}
                if payload.completion_cost_details is not None
                else None,
                payload.provider.strip() if payload.provider else None,
            )

            return schemas.ModelPriceMapSchema(**row)
    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(
            status_code=409,
            detail="An entry with same match conditions already exists.",
        )


@retry_asyncpg
async def update_model_price_map(
    id: uuid.UUID,
    payload: schemas.ModelPriceMapUpdateSchema,
    tenant_id: uuid.UUID,
) -> schemas.ModelPriceMapSchema:
    try:
        async with database.asyncpg_conn() as db:
            row = await db.fetchrow(
                """
                UPDATE model_price_map
                SET name = $3, start_time = $4, match_path = $5, match_pattern = $6, prompt_cost = $7, completion_cost = $8, prompt_cost_details = $9, completion_cost_details = $10, provider = NULLIF($11, '')
                WHERE tenant_id = $1 and id = $2
                RETURNING *
                """,
                tenant_id,
                id,
                payload.name,
                payload.start_time,
                payload.match_path,
                payload.match_pattern,
                payload.prompt_cost,
                payload.completion_cost,
                {k: str(v) for k, v in payload.prompt_cost_details.items()}
                if payload.prompt_cost_details is not None
                else None,
                {k: str(v) for k, v in payload.completion_cost_details.items()}
                if payload.completion_cost_details is not None
                else None,
                payload.provider.strip() if payload.provider else None,
            )

            return schemas.ModelPriceMapSchema(**row)
    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(
            status_code=409,
            detail="An entry with same match conditions already exists.",
        )
