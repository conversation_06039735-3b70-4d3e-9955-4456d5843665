import datetime
import uuid
from typing import Any

import structlog
from lc_config.settings import shared_settings
from lc_database.database import asyncpg_conn

from app.models.billing.usagereporting.transactions import (
    MetronomeTransaction,
    MetronomeTransactionReporter,
    UsageReportingStatus,
)
from app.schemas import (
    NodesTransactionWithOrg as NodesTransactionWithOrgModel,
)
from app.schemas import TraceTransactionSource

logger = structlog.getLogger(__name__)


class MinuteAccumulator:
    __slots__ = ("_seen",)

    def __init__(self) -> None:
        self._seen: set[int] = set()

    @staticmethod
    def _bucket(ts: datetime.datetime) -> int:
        return int(ts.replace(second=0, microsecond=0).timestamp() // 60)

    def add_span(self, start: datetime.datetime, end: datetime.datetime) -> int:
        s = self._bucket(start)
        e = self._bucket(end)
        if s >= e:
            return 0
        before = len(self._seen)
        self._seen.update(range(s, e))
        return len(self._seen) - before


class NodesTransactionWithOrg(NodesTransactionWithOrgModel, MetronomeTransaction):
    def get_properties(self) -> dict[str, Any]:
        nodes_count = self.nodes_count
        standby_minutes = self.deduped_standby_minutes

        if self.self_hosted_customer_id:
            # self_hosted_customer_id is from Self-Hosted Control Plane deployments
            plan = "self_hosted"
        elif self.remote_reconciled:
            # remote_reconciled is from Self-Hosted Data Plane deployments
            plan = "self_hosted_data_plane"
        elif self.project_id and self.api_key:
            plan = "cloud"
        elif self.api_key:
            plan = "self_hosted_lite"
        else:
            plan = "unknown"

        if plan == "cloud":
            if self.deployment_type == "dev_free":
                # metrics for dev_free deloyments should be zero
                nodes_count = 0
                standby_minutes = 0
            else:
                # Charge normally for all other deployment types.
                #
                # TODO: After August 14, 2025, this else-block can be removed.
                #
                # Projects created before May 14, 2025 at 00:00 UTC will have 3 months
                # of free usage. On August 14, 2025 at 00:00 UTC (roughly 3 months later),
                # these projects will begin to be charged.
                may_14_2025 = datetime.datetime(
                    2025, 5, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                )
                aug_14_2025 = datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                )
                todays_date = datetime.datetime.now(datetime.timezone.utc).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                # Explicity set nodes_count and standby_minutes, even though there's
                # probably a way to optimize this.
                if todays_date >= aug_14_2025:
                    # all projects will be charged after August 14, 2025
                    if (
                        self.project_created_at
                        and self.project_created_at < may_14_2025
                        and self.start_interval_time < aug_14_2025
                    ):
                        # Edge case: The start interval time is before Aug 14, 2025, but
                        # the reporting cron job runs after Aug 14, 2025. In this case,
                        # we won't charge for usage, even if the end interval time is after
                        # Aug 14, 2025.
                        #
                        # This only applies for projects created before May 14, 2025.
                        nodes_count = 0
                        standby_minutes = 0
                    else:
                        nodes_count = self.nodes_count
                        standby_minutes = self.deduped_standby_minutes
                else:
                    if self.project_created_at:
                        if self.project_created_at < may_14_2025:
                            # projects created before May 14, 2025 will be free for 3 months
                            nodes_count = 0
                            standby_minutes = 0
                        else:
                            nodes_count = self.nodes_count
                            standby_minutes = self.deduped_standby_minutes
                    else:
                        # for whatever reason, we don't know when the project was created
                        # (e.g. self-hosted deployments)
                        nodes_count = self.nodes_count
                        standby_minutes = self.deduped_standby_minutes

        # property values must be string, boolean, or number
        properties: dict[str, Any] = {
            "count": nodes_count,
            "tenant_id": str(self.tenant_id) if self.tenant_id else "self-hosted",
            "project_id": str(self.project_id) if self.project_id else "",
            "standby_minutes": standby_minutes,
            "deployment_type": self.deployment_type,
            "plan": plan,
        }
        return properties

    def get_org_id(self) -> uuid.UUID | None:
        return self.organization_id

    def get_transaction_id(self) -> uuid.UUID:
        return self.id

    def get_status(self) -> UsageReportingStatus:
        return UsageReportingStatus(self.status)

    def get_num_failed_send_attempts(self) -> int:
        return self.num_failed_send_attempts

    def get_event_timestamp(self) -> datetime.datetime:
        return self.start_interval_time

    def get_event_type(self) -> str:
        if self.transaction_type == "nodes_executed":
            return "nodes_executed"
        raise ValueError(f"Unknown transaction type: {self.transaction_type}")

    def get_self_hosted_customer_id(self) -> uuid.UUID | None:
        return self.self_hosted_customer_id

    def to_json(self) -> dict:
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id),
            "project_id": str(self.project_id),
            "project_created_at": (
                self.project_created_at.isoformat() if self.project_created_at else None
            ),
            "api_key": self.api_key,
            "remote_reconciled": self.remote_reconciled,
            "nodes_count": self.nodes_count,
            "deployment_type": self.deployment_type,
            "start_insertion_time": self.start_insertion_time.isoformat(),
            "end_insertion_time": self.end_insertion_time.isoformat(),
            "start_interval_time": self.start_interval_time.isoformat(),
            "end_interval_time": self.end_interval_time.isoformat(),
            "status": self.status,
            "num_failed_send_attempts": self.num_failed_send_attempts,
            "transaction_type": self.transaction_type,
            "organization_id": str(self.organization_id),
            "source": self.source,
            "self_hosted_customer_id": (
                str(self.self_hosted_customer_id)
                if self.self_hosted_customer_id
                else None
            ),
        }

    @classmethod
    def get_status_colname(cls) -> str:
        return "reported_status"

    @classmethod
    def get_failed_attempts_colname(cls) -> str:
        return "num_failed_metronome_send_attempts"

    @classmethod
    def get_table_name(cls) -> str:
        return "remote_metrics"

    @classmethod
    def get_transaction_id_colname(cls) -> str:
        return "id"


class NodesTransactionReporter(MetronomeTransactionReporter[NodesTransactionWithOrg]):
    # _acc is used to deduplicate standby minutes for a project ID.
    # We don't bill for replicas, but we DO aggregate node/run executions
    # So we report for all and deduplicate overlapping intervals of reported
    # standby minutes.
    _acc: dict[uuid.UUID, MinuteAccumulator] = {}

    @classmethod
    def _acc_for(cls, pid: uuid.UUID) -> MinuteAccumulator:
        return cls._acc.setdefault(pid, MinuteAccumulator())

    @classmethod
    def clear_dedup_keys(cls):
        cls._acc.clear()

    @classmethod
    def _get_initial_status(cls) -> UsageReportingStatus:
        return UsageReportingStatus.PENDING

    @classmethod
    async def _fetch_reportable_transactions(
        cls,
        status: UsageReportingStatus,
        before_time: datetime.datetime | None = None,
    ) -> tuple[list[NodesTransactionWithOrg], bool]:
        async with asyncpg_conn() as db:
            # Filter to past March 1st 2025 - otherwise metronome will throw 400s
            # because it was before we made the change to the plans
            #
            # Update: Starting on April 23, 2025, we will begin reporting usage
            # where langgraph.platform.nodes==0 in order to track hourly compute
            # usage.

            safety_window = (
                before_time - datetime.timedelta(minutes=30) if before_time else None
            )

            rows = await db.fetch(
                f"""
                SELECT
                    r.id as transaction_id,
                    r.api_key as api_key,
                    r.measures,
                    r.from_timestamp as start_interval_time,
                    r.to_timestamp as end_interval_time,
                    r.received_at as start_insertion_time,
                    r.received_at as end_insertion_time,
                    r.reported_status,
                    r.num_failed_metronome_send_attempts,
                    r.self_hosted_customer_id,
                    r.tenant_id,
                    t.organization_id,
                    r.tags->>'langgraph.platform.project_id' as project_id,
                    hp.created_at as project_created_at,
                    hp.metadata->>'deployment_type' as deployment_type,
                    hp.remote_reconciled as remote_reconciled
                FROM
                    remote_metrics r
                LEFT JOIN tenants t ON t.id = r.tenant_id
                LEFT JOIN host_projects hp ON hp.id = uuid(COALESCE(NULLIF(r.tags->>'langgraph.platform.project_id', ''), '00000000-0000-0000-0000-000000000000'))
                WHERE reported_status = '{status.value}'
                AND r.from_timestamp >= '2025-04-23'
                AND (r.tenant_id is NOT NULL or r.self_hosted_customer_id IS NOT NULL)
                {f"AND r.from_timestamp < '{safety_window.isoformat()}'" if safety_window else ""}
                ORDER BY start_interval_time
                LIMIT {shared_settings.NODES_REPORTING_LIMIT}
                """
            )

            out: list[NodesTransactionWithOrg] = []
            for row in rows:
                project_id = (
                    uuid.UUID(row["project_id"]) if row.get("project_id") else None
                )
                if project_id:
                    deduped = cls._acc_for(project_id).add_span(
                        row["start_interval_time"], row["end_interval_time"]
                    )
                else:
                    deduped = 0

                out.append(
                    NodesTransactionWithOrg(
                        id=row["transaction_id"],
                        tenant_id=row.get("tenant_id"),
                        project_id=project_id,
                        project_created_at=(
                            row.get("project_created_at")
                            if row.get("project_created_at")
                            else None
                        ),
                        api_key=row["api_key"] if row.get("api_key") else None,
                        remote_reconciled=row["remote_reconciled"]
                        if row.get("remote_reconciled")
                        else False,
                        nodes_count=dict(row["measures"]).get(
                            "langgraph.platform.nodes", 0
                        ),
                        deduped_standby_minutes=deduped,
                        deployment_type=(
                            row["deployment_type"]
                            if row.get("deployment_type")
                            else "prod"
                        ),
                        start_insertion_time=row["start_insertion_time"],
                        end_insertion_time=row["end_insertion_time"],
                        start_interval_time=row["start_interval_time"],
                        end_interval_time=row["end_interval_time"],
                        status=row["reported_status"],
                        num_failed_send_attempts=row[
                            "num_failed_metronome_send_attempts"
                        ],
                        transaction_type="nodes_executed",
                        organization_id=row.get("organization_id"),
                        source=TraceTransactionSource.local.value,
                        self_hosted_customer_id=row.get("self_hosted_customer_id"),
                    )
                )

            return (
                out,
                len(rows) == shared_settings.NODES_REPORTING_LIMIT and len(rows) > 0,
            )
