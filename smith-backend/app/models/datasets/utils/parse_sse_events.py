# A helper function to parse SSE events from raw bytes
import orjson
import structlog

logger = structlog.get_logger(__name__)


def parse_sse_events(data: bytes) -> list[dict]:
    parsed_runs = []
    if b"data:" in data and b'"end_time":' in data:
        chunks = data.split(b"\n\n")
        for chunk in chunks:
            decoded_chunk = chunk.decode("utf-8", errors="ignore").strip()
            if not decoded_chunk or "data:" not in decoded_chunk:
                continue
            try:
                # Extract the JSON after "data: "
                json_str = decoded_chunk.split("data:")[1].strip()
                if json_str and json_str != "[DONE]":
                    # Parse the JSON data
                    run_data = orjson.loads(json_str)
                    if isinstance(run_data, dict) and "patch" in run_data:
                        # The format used by our jsonpatch_sse_stream
                        patches = run_data.get("patch", [])
                        for patch in patches:
                            if (
                                patch.get("op") == "replace"
                                and "value" in patch
                                and "end_time" in patch["value"]
                                and patch["value"]["end_time"]
                            ):
                                parsed_runs.append(patch["value"])
            except orjson.JSONDecodeError as e:
                logger.warning(
                    "Error parsing SSE event.",
                    error=e,
                    data_bytes=data,
                    chunks=chunks,
                    decoded_chunk=decoded_chunk,
                    json_str=json_str,
                )
    return parsed_runs
