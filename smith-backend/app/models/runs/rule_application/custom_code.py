import uuid
from collections import defaultdict
from typing import Any

import httpx
from aiochclient import ChClientError
from fastapi import HTTPException
from langchain_core.runnables import RunnableConfig, <PERSON><PERSON>bleLambda
from lc_config.settings import shared_settings
from lc_database.database import asyncpg_conn
from lc_database.tracer import LocalTracer

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.models.custom_code.execute import execute_custom_code
from app.models.feedback.ingest import FeedbackInsert, upsert_feedback
from app.models.runs.rule_application.apply import (
    NULL_UUID,
    RuleApplicationUsageLimitError,
    RuleApplier,
    logger,
)
from app.models.runs.rule_application.rule_application_utils import (
    is_clickhouse_error,
)
from app.models.runs.rule_application.usage_limit import (
    ALL_TRACES_USAGE_LIMIT_ERROR,
    handle_usage_limit_error,
)
from app.models.runs.utils import parse_feedback


class CustomCodeRuleApplier(RuleApplier):
    @classmethod
    async def _apply(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> dict[str, dict[str, schemas.RuleLogActionResponse]]:
        response: dict[str, dict[str, schemas.RuleLogActionResponse]] = defaultdict(
            dict
        )
        if not rule.code_evaluators:
            raise ValueError("Rule must have code evaluators set to apply")

        evaluator_errors: dict[str, list[str]] = {str(run["id"]): [] for run in sampled}
        evaluator_feedbacks_ids: dict[str, list[uuid.UUID]] = {
            str(run["id"]): [] for run in sampled
        }

        if example_ids := [
            run["reference_example_id"]
            for run in sampled
            if run.get("reference_example_id")
        ]:
            async with asyncpg_conn() as db:
                examples = await db.fetch(
                    "select id, inputs, outputs, metadata from examples_tagged where id = any($1) and tag = 'latest'",
                    example_ids,
                )
            example_dict = {
                uuid.UUID(example["id"].hex): {
                    "id": str(example["id"]),
                    "inputs": example["inputs"],
                    "outputs": example["outputs"],
                    "metadata": example["metadata"],
                }
                for example in examples
            }
        else:
            example_dict = {}
        tracer = LocalTracer(project_name="evaluators")
        feedback_inserts: list[FeedbackInsert] = []

        for evaluator in rule.code_evaluators:
            run_metadata: list[RunnableConfig] = [
                {
                    "callbacks": [tracer],
                    "metadata": {
                        "run_id": str(run["id"]),
                        "rule_id": str(rule.id),
                        "source_project_id": str(rule.session_id),
                    },
                    "run_id": uuid.uuid4(),
                    "max_concurrency": shared_settings.ASYNC_WORKER_SEMAPHORE,
                }
                for run in sampled
            ]
            outputs = await execute_custom_code(
                {
                    "code": evaluator.code,
                    "args": [
                        [run, example_dict.get(run["reference_example_id"])]
                        if run.get("reference_example_id")
                        else [run]
                        for run in sampled
                    ],
                }
            )
            for run, results, metadata in zip(sampled, outputs, run_metadata):
                # The following function is just for tracing purposes - allows us to trace individual evaluator results instead of batched
                def execute_custom_evaluator(
                    invocation_params: dict[str, Any],
                ):
                    return results

                runnable = RunnableLambda(execute_custom_evaluator)
                runnable.invoke(
                    {
                        "code": evaluator.code,
                        "run": run,
                        "example": example_dict.get(run["reference_example_id"])
                        if run.get("reference_example_id")
                        else None,
                    },
                    metadata,
                )

                if not isinstance(results, dict):
                    await logger.awarn(
                        "Code execution returned invalid payload for rule %s. payload had type %s: %s",
                        rule.id,
                        type(results),
                        results,
                        exc_info=True,
                    )
                if results is None:
                    evaluator_errors[str(run["id"])].append(str("No score received"))
                    continue
                if results.get("status") != "success":
                    if results.get("stacktrace"):
                        await logger.awarn(
                            "Evaluator %s produced an error for run %s. Trace: %s",
                            evaluator,
                            run["id"],
                            results.get("stacktrace"),
                            exc_info=results,
                        )
                    else:
                        await logger.awarn(
                            "Evaluator %s produced an error for run %s. Status: %s. Results: %s",
                            evaluator,
                            run["id"],
                            results.get("status"),
                            str(results),
                            exc_info=results,
                        )
                    evaluator_errors[str(run["id"])].append(str(results))
                    continue
                else:
                    keys_scores = results.get("result")
                    if not keys_scores:
                        await logger.awarn(
                            "Evaluator %s produced a null result %s. Results: %s",
                            evaluator,
                            run["id"],
                            str(results),
                        )
                        evaluator_errors[str(run["id"])].append("No score received")
                        continue
                    elif (
                        not isinstance(keys_scores, dict)
                        and not (
                            isinstance(keys_scores, list)
                            and all(
                                isinstance(item, (str, int, float, bool))
                                or (
                                    isinstance(item, dict)
                                    and all(isinstance(k, str) for k in item.keys())
                                )
                                for item in keys_scores
                            )
                        )
                        and not isinstance(keys_scores, (str, int, float, bool))
                    ):
                        await logger.awarn(
                            "Evaluator %s produced an invalid result for run %s. Results: %s",
                            evaluator,
                            run["id"],
                            str(results),
                        )
                        evaluator_errors[str(run["id"])].append(
                            f"Invalid return type. Return type must be a dict. Received {type(keys_scores)}: {keys_scores}"
                        )
                        continue
                    else:
                        if not isinstance(keys_scores, dict):
                            keys_scores = {rule.display_name: keys_scores}
                        elif (
                            len(keys_scores) == 2
                            and ("score" in keys_scores or "value" in keys_scores)
                            and "comment" in keys_scores
                        ) or (
                            len(keys_scores) == 1
                            and ("score" in keys_scores or "value" in keys_scores)
                        ):
                            keys_scores["key"] = rule.display_name
                        await parse_feedback(
                            keys_scores,
                            run,
                            rule.id,
                            metadata["run_id"],
                            feedback_inserts,
                            evaluator_feedbacks_ids,
                        )
        # persist evaluator runs
        # TODO: move this out of the database transaction
        try:
            await tracer.upsert_runs(rule.tenant_id)
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                if start_time:
                    await handle_usage_limit_error(
                        ALL_TRACES_USAGE_LIMIT_ERROR,
                        rule,
                        start_time,
                        end_time,
                    )
                raise RuleApplicationUsageLimitError("Total trace usage limit exceeded")
            else:
                await logger.aexception("Error upserting runs", exc_info=e)

            response[str(NULL_UUID)] |= {
                "evaluators": schemas.RuleLogActionResponse(
                    outcome=schemas.RuleLogActionOutcome.error,
                )
            }
        except Exception as e:
            await logger.aexception(
                "Evaluator produced invalid runs for rule %s",
                rule.id,
                exc_info=e,
            )

            response[str(NULL_UUID)] |= {
                "evaluators": schemas.RuleLogActionResponse(
                    outcome=schemas.RuleLogActionOutcome.error,
                )
            }

        # persist feedback
        try:
            await upsert_feedback(
                feedback_inserts,
                auth,
                throw_on_invalid=False,
                skip_trace_upgrade=True,
                tenant_config=auth.tenant_config,
            )

            response[str(NULL_UUID)] |= {
                "evaluators": schemas.RuleLogActionResponse(
                    outcome=schemas.RuleLogActionOutcome.error
                    if any(errors for errors in evaluator_errors.values())
                    else schemas.RuleLogActionOutcome.success
                )
            }

            for run_id, feedback_ids in evaluator_feedbacks_ids.items():
                payload: dict[str, Any] = {"feedback_ids": feedback_ids}
                if evaluator_errors.get(run_id):
                    payload["error"] = ", ".join(
                        str(e) for e in evaluator_errors[run_id]
                    )

                response[run_id] |= {
                    "evaluators": schemas.RuleLogActionResponse(
                        outcome=schemas.RuleLogActionOutcome.success
                        if not payload.get("error")
                        else schemas.RuleLogActionOutcome.error,
                        payload=payload,
                    )
                }

        except HTTPException:
            await logger.awarn(
                "Evaluator produced invalid feedback for rule %s",
                rule.id,
                exc_info=True,
            )
            response[str(NULL_UUID)] |= {
                "evaluators": schemas.RuleLogActionResponse(
                    outcome=schemas.RuleLogActionOutcome.error
                )
            }
        except (ExceptionGroup, ChClientError) as exc:
            if is_clickhouse_error(exc):
                await logger.awarn(
                    "Failed to persist feedback for rule %s",
                    rule.id,
                    exc_info=True,
                )
                response[str(NULL_UUID)] |= {
                    "evaluators": schemas.RuleLogActionResponse(
                        outcome=schemas.RuleLogActionOutcome.error
                    )
                }

        return response
