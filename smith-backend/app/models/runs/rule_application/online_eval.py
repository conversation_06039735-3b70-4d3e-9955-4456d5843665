import asyncio
import re
import uuid
from collections import defaultdict
from typing import Any, Dict, List, Optional, Union, cast

from aiochclient import ChClientError
from fastapi import HTTPException
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.config import settings
from app.models.feedback.ingest import FeedbackInsert, upsert_feedback
from app.models.runs.rule_application.apply import (
    NULL_UUID,
    RuleApplicationUsageLimitError,
    RuleApplier,
    logger,
)
from app.models.runs.rule_application.rule_application_utils import (
    is_clickhouse_error,
)
from app.models.runs.rule_application.usage_limit import (
    ALL_TRACES_USAGE_LIMIT_ERROR,
    handle_usage_limit_error,
)
from app.models.runs.rules import (
    EXAMPLES_FEW_SHOT_KEY,
    batch_invoke_evaluator,
    get_chain_from_evaluator,
)
from app.models.runs.usage_limits import has_tenant_exceeded_usage_limits
from app.models.runs.utils import parse_feedback
from app.models.tenants.secrets import list_secrets_as_dict


async def get_default_score_for_schema(schema: Union[dict[str, Any], None]) -> Any:
    if not isinstance(schema, dict) or not schema:
        await logger.awarn(
            "The schema for this evaluator was not well defined, errors will not be surfaced."
        )
        return {}

    schema_type = schema.get("type")
    if not schema_type:
        await logger.awarn(
            "The schema for this evaluator was not well defined, errors will not be surfaced."
        )
        return {}

    def is_type(type_def: Optional[str | List[str]], type_to_check: str):
        if type_def is None:
            return False
        elif isinstance(type_def, str):
            return type_def == type_to_check
        else:
            return type_to_check in type_def

    if is_type(schema_type, "object"):
        result: Dict[str, Any] = {}
        if "properties" in schema:
            for key, prop_schema in schema["properties"].items():
                result[key] = await get_default_score_for_schema(prop_schema)
        return result

    if is_type(schema_type, "array"):
        return [await get_default_score_for_schema(schema.get("items"))]

    if is_type(schema_type, "string"):
        return ""

    if is_type(schema_type, "number") or is_type(schema_type, "integer"):
        return 0

    if is_type(schema_type, "boolean"):
        return False

    if is_type(schema_type, "null"):
        return None

    # Default case
    await logger.awarn(
        "The schema for this evaluator was not well defined, errors will not be surfaced."
    )
    return {}


def _maybe_flatten_dict(obj: Any, evaluator_version: int) -> Any:
    if isinstance(obj, dict) and len(obj) == 1 and evaluator_version < 2:
        return next(iter(obj.values()))

    return obj


class OnlineEvaluatorRuleApplier(RuleApplier):
    @classmethod
    async def _filter_samples(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> list[dict[str, Any]]:
        try:
            await has_tenant_exceeded_usage_limits(auth)
        except HTTPException as e:
            if e.status_code == 429:
                await handle_usage_limit_error(
                    ALL_TRACES_USAGE_LIMIT_ERROR,
                    rule,
                    start_time,
                    end_time,
                )
                raise RuleApplicationUsageLimitError("Total trace usage limit exceeded")

        return sampled

    @classmethod
    async def _apply(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> dict[str, dict[str, schemas.RuleLogActionResponse]]:
        response: dict[str, dict[str, schemas.RuleLogActionResponse]] = defaultdict(
            dict
        )
        if not rule.evaluators:
            raise ValueError("Rule must have evaluators set to apply")

        async with asyncpg_conn() as db:
            evaluator_errors: dict[str, list[str]] = {
                str(run["id"]): [] for run in sampled
            }
            evaluator_feedbacks_ids: dict[str, list[uuid.UUID]] = {
                str(run["id"]): [] for run in sampled
            }

            if example_ids := [
                run["reference_example_id"]
                for run in sampled
                if run.get("reference_example_id")
            ]:
                examples = await db.fetch(
                    f"select id, outputs{', inputs' if rule.evaluator_version > 1 else ''} from examples_tagged where id = any($1) and tag = 'latest'",
                    example_ids,
                )
                example_outputs_dict = {
                    uuid.UUID(example["id"].hex): _maybe_flatten_dict(
                        example["outputs"], rule.evaluator_version
                    )
                    for example in examples
                }
                example_inputs_dict = (
                    {
                        uuid.UUID(example["id"].hex): example["inputs"]
                        for example in examples
                    }
                    if rule.evaluator_version > 1
                    else {}
                )
            else:
                example_outputs_dict = {}
                example_inputs_dict = {}
            secrets = await list_secrets_as_dict(auth, db)
            feedback_inserts: list[FeedbackInsert] = []

            # Fetch few-shot examples from dataset
            correction_dicts = []
            if rule.use_corrections_dataset is True:
                num_examples = (
                    rule.num_few_shot_examples
                    if rule.num_few_shot_examples is not None
                    else 5
                )
                corrections = await db.fetch(
                    """
                    select id, outputs, inputs
                    from examples_tagged
                    where dataset_id = $1 and tag = 'latest' and EXISTS (
                        SELECT 1 FROM dataset
                        WHERE dataset.id = examples_tagged.dataset_id AND dataset.tenant_id = $2
                    )
                    order by RANDOM()
                    limit $3
                    """,
                    rule.corrections_dataset_id,
                    rule.tenant_id,
                    num_examples,
                )
                try:
                    if (
                        rule.evaluators[0].structured
                        and rule.evaluators[0].structured.tool_schema
                    ):
                        for k, v in list(
                            rule.evaluators[0]
                            .structured.tool_schema.get("properties", {})
                            .items()
                        ):
                            if v.get("type") == "boolean":
                                for example in corrections:
                                    for key, value in example["outputs"].items():
                                        if key == k:
                                            if (
                                                value == 0
                                                or value == "0"
                                                or value == "0.0"
                                            ):
                                                example["outputs"][key] = "false"
                                            elif (
                                                value == 1
                                                or value == "1"
                                                or value == "1.0"
                                            ):
                                                example["outputs"][key] = "true"
                except Exception as e:
                    await logger.awarn(
                        f"Error converting boolean values for rule {rule.id}\n{e}",
                        exc_info=True,
                    )

                correction_dicts = [
                    {
                        # These corrections dicts will be traced in the new llm as
                        # judge runs, and any corrections to those llm as judge runs
                        # will include all of the values here. If we don't take out
                        # the few shot examples here then they will grow recursively
                        # each time a correction is made on llm as judge runs that
                        # pulled in the latest few shot examples.
                        **{
                            k: v
                            for k, v in example["inputs"].items()
                            if k != EXAMPLES_FEW_SHOT_KEY
                        },
                        **example["outputs"],
                    }
                    for example in corrections
                ]

            if rule.evaluators[0].structured.tool_schema:
                default_scores = await get_default_score_for_schema(
                    rule.evaluators[0].structured.tool_schema
                )
            else:
                await logger.awarn(
                    "The schema for this evaluator was not well defined, errors will not be surfaced."
                )
                default_scores = {}
            eval_chains: list[dict | list[dict]] = []
            for evaluator in rule.evaluators:
                try:
                    eval_chains.append(
                        await get_chain_from_evaluator(evaluator, auth, db)
                    )
                except HTTPException as e:
                    await logger.awarn(
                        f"Error loading evaluator chain for rule {rule.id} with error {e.detail}",
                        exc_info=True,
                    )
                    eval_chains.append(e)
        for evaluator, chain in zip(rule.evaluators, eval_chains):
            run_metadata: list[Dict] = [
                {
                    "metadata": {
                        "run_id": str(run["id"]),
                        "rule_id": str(rule.id),
                        "source_project_id": str(rule.session_id),
                        "source_run_id": str(run["id"]),
                        "source_rule_id": str(rule.id),
                    },
                    "run_id": uuid.uuid4(),
                }
                for run in sampled
            ]
            if isinstance(chain, HTTPException):
                scores = [{"error": chain.detail}] * len(sampled)
            else:
                inputs = [
                    {
                        EXAMPLES_FEW_SHOT_KEY: correction_dicts,
                        **_generate_prompt_inputs_from_mapping(
                            evaluator.structured.variable_mapping,
                            cast(
                                dict,
                                example_inputs_dict.get(
                                    uuid.UUID(run["reference_example_id"])
                                    if isinstance(run["reference_example_id"], str)
                                    else run["reference_example_id"]
                                )
                                if rule.evaluator_version > 1
                                and rule.dataset_id is not None
                                else run["inputs"],
                            ),
                            run["outputs"],
                            example_outputs_dict.get(
                                uuid.UUID(run["reference_example_id"])
                                if isinstance(run["reference_example_id"], str)
                                else run["reference_example_id"]
                            ),
                        ),
                    }
                    if evaluator.structured.variable_mapping
                    else {
                        "input": example_inputs_dict.get(run["reference_example_id"])
                        if rule.evaluator_version > 1 and rule.dataset_id is not None
                        else _maybe_flatten_dict(run["inputs"], rule.evaluator_version),
                        "output": _maybe_flatten_dict(
                            run["outputs"], rule.evaluator_version
                        ),
                        "reference": example_outputs_dict.get(
                            uuid.UUID(run["reference_example_id"])
                            if isinstance(run["reference_example_id"], str)
                            else run["reference_example_id"]
                        ),
                        "reference_output": example_outputs_dict.get(
                            uuid.UUID(run["reference_example_id"])
                            if isinstance(run["reference_example_id"], str)
                            else run["reference_example_id"]
                        ),
                        EXAMPLES_FEW_SHOT_KEY: correction_dicts,
                    }
                    for run in sampled
                ]

                def chunks(lst, n):
                    """Yield successive n-sized chunks from lst."""
                    for i in range(0, len(lst), n):
                        yield lst[i : i + n]

                async def limited_batch_invoke_evaluator(
                    chain, batch, run_metadata_batch, semaphore
                ):
                    async with semaphore:
                        try:
                            return await batch_invoke_evaluator(
                                chain, auth, batch, run_metadata_batch, secrets
                            )
                        except HTTPException as e:
                            await logger.awarn(
                                f"Error invoking evaluator for rule {rule.id} with error {e.detail}",
                                exc_info=True,
                            )
                            return [{"error": e.detail}] * len(sampled)

                async def process_batches():
                    batch_size = settings.ONLINE_EVALS_BATCH_SIZE
                    max_concurrent_batches = settings.ONLINE_EVALS_MAX_CONCURRENCY
                    semaphore = asyncio.Semaphore(max_concurrent_batches)

                    batches = list(chunks(inputs, batch_size))
                    run_metadata_batches = list(chunks(run_metadata, batch_size))
                    tasks = [
                        limited_batch_invoke_evaluator(
                            chain, zipped[0], zipped[1], semaphore
                        )
                        for zipped in zip(batches, run_metadata_batches)
                    ]
                    results = await asyncio.gather(*tasks)
                    return results

                try:
                    results = await process_batches()
                    scores = [score for result in results for score in result]
                except HTTPException as e:
                    await logger.awarn(
                        f"Error invoking evaluator for rule {rule.id} with error {e.detail}",
                        exc_info=True,
                    )
                    scores = [{"error": e.detail}] * len(sampled)

            for run, keys_scores, metadata in zip(sampled, scores, run_metadata):
                if not keys_scores:
                    evaluator_errors[str(run["id"])].append(str("No score received"))
                    await parse_feedback(
                        default_scores,
                        run,
                        rule.id,
                        metadata["run_id"],
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                        error="No score received",
                    )
                    continue
                if keys_scores.get("error"):
                    if settings.DETAILED_ONLINE_EVAL_ERROR_LOGS:
                        await logger.awarn(
                            "Evaluator %s produced an error for run.\nevaluator: %s \nrun: %s\nchain: %s",
                            evaluator,
                            run["id"],
                            str(run),
                            str(chain),
                            exc_info=keys_scores["error"],
                        )
                    else:
                        await logger.awarn(
                            "Evaluator %s produced an error for run %s",
                            evaluator,
                            run["id"],
                            exc_info=keys_scores["error"],
                        )

                    await parse_feedback(
                        default_scores,
                        run,
                        rule.id,
                        metadata["run_id"],
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                        error=repr(keys_scores["error"]),
                    )
                    evaluator_errors[str(run["id"])].append(str(keys_scores))
                    continue
                if keys_scores.get("outputs"):
                    await parse_feedback(
                        keys_scores["outputs"],
                        run,
                        rule.id,
                        metadata["run_id"],
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                    )
                else:
                    await logger.awarn(
                        "Evaluator produced invalid feedback for rule %s. feedback object had type %s: %s",
                        rule.id,
                        type(keys_scores),
                        keys_scores,
                        exc_info=True,
                    )
                    await parse_feedback(
                        default_scores,
                        run,
                        rule.id,
                        metadata["run_id"],
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                        error="Evaluator produced invalid feedback",
                    )
        # persist feedback
        try:
            await upsert_feedback(
                feedback_inserts,
                auth,
                throw_on_invalid=False,
                skip_trace_upgrade=True,
                tenant_config=auth.tenant_config,
            )
            response[str(NULL_UUID)] |= {
                "evaluators": schemas.RuleLogActionResponse(
                    outcome=schemas.RuleLogActionOutcome.error
                    if any(errors for errors in evaluator_errors.values())
                    else schemas.RuleLogActionOutcome.success
                )
            }

            for run_id, feedback_ids in evaluator_feedbacks_ids.items():
                payload: dict[str, Any] = {"feedback_ids": feedback_ids}
                if evaluator_errors.get(run_id):
                    payload["error"] = ", ".join(
                        str(e) for e in evaluator_errors[run_id]
                    )

                response[run_id] |= {
                    "evaluators": schemas.RuleLogActionResponse(
                        outcome=schemas.RuleLogActionOutcome.success
                        if not payload.get("error")
                        else schemas.RuleLogActionOutcome.error,
                        payload=payload,
                    )
                }

        except HTTPException:
            await logger.awarn(
                "Evaluator produced invalid feedback for rule %s",
                rule.id,
                exc_info=True,
            )
            response[str(NULL_UUID)] |= {
                "evaluators": schemas.RuleLogActionResponse(
                    outcome=schemas.RuleLogActionOutcome.error
                )
            }
        except (ExceptionGroup, ChClientError) as exc:
            if is_clickhouse_error(exc):
                await logger.awarn(
                    "Failed to persist feedback for rule %s",
                    rule.id,
                    exc_info=True,
                )
                response[str(NULL_UUID)] |= {
                    "evaluators": schemas.RuleLogActionResponse(
                        outcome=schemas.RuleLogActionOutcome.error
                    )
                }

        return response


def split_path(path_string: str) -> list[str]:
    """
    Splits a string of the form 'foo.bar[0].baz' into ['foo', 'bar', '0', 'baz']

    Args:
        path_string (str): String in dot notation with optional array indices in brackets

    Returns:
        list: Components of the path
    """
    # Pattern to match either:
    # - A sequence of characters followed by a dot or end of string
    # - A sequence of characters followed by a bracketed number
    pattern = r"([^.\[\]]+)(?:\[(\d+)\])?\.?"

    components = []
    for match in re.finditer(pattern, path_string):
        components.append(match.group(1))
        # bracketed element
        if match.group(2):
            components.append(match.group(2))

    return components


# Given a variable mapping, input, output, and reference, generate a dictionary of prompt inputs
# The FE passes 'input' as the accessor to run['inputs'] and 'output' as the accessor to run['outputs']
def _generate_prompt_inputs_from_mapping(
    variable_mapping: dict,
    input: dict,
    output: dict,
    reference: Optional[dict] = None,
):
    def get_nested_value(obj, path):
        if obj is None or path is None:
            return obj

        for key in path:
            if obj is None:
                return None
            if key in obj and isinstance(obj, dict):
                obj = obj[key]
            elif isinstance(obj, list):
                try:
                    obj = obj[int(key)]
                except (ValueError, IndexError):
                    return None
            else:
                return None
        return obj

    prompt_inputs = {}
    for key in variable_mapping:
        accessor = variable_mapping[key]
        run_parts = split_path(accessor)
        if run_parts[0] == "input":
            value = get_nested_value(input, run_parts[1:])
        elif run_parts[0] == "output":
            value = get_nested_value(output, run_parts[1:])
        # Support all of reference, reference_output, and referenceOutput for backwards compat
        elif run_parts[0] in ["reference", "reference_output", "referenceOutput"]:
            value = get_nested_value(reference, run_parts[1:])
        else:
            value = None

        if value is not None:
            prompt_inputs[key] = value
    return prompt_inputs
