import asyncio
from typing import Any, cast

import structlog
from aiochclient import Record
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

from app import schemas
from app.api.auth import AuthInfo
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    Operation,
    Operator,
)
from app.models.query_lang.translate import SqlVisitorClickhouse
from app.models.runs.attrs import RUN_ATTRIBUTES
from app.models.runs.group import (
    GROUP_BY_METHODS,
    _get_group_sql_params,
    _get_run_filter_group_sql_params,
)
from app.models.runs.stats import token_count_projection
from app.models.tracer_sessions.stats import (
    feedback_facets_projection,
    feedback_stats_projection,
    map_stats,
    metadata_facets_projection,
    runs_facets_projection,
    runs_stats_projection,
    token_count_stats_projection,
)
from app.retry import retry_clickhouse_read

logger = structlog.get_logger(__name__)


@retry_clickhouse_read
async def group_stats(
    auth: AuthInfo,
    query_params: schemas.RunGroupRequest,
) -> dict[str, Any]:
    (
        fg_projection,
        fg_sql_from_join_where,
        fg_sql_params,
        _,
        _,
    ) = _get_run_filter_group_sql_params(auth, query_params, group_key_param=None)

    group_by = next(x for x in GROUP_BY_METHODS if x.name == query_params.group_by)

    group_sql_from_join_where, group_sql_params = _get_group_sql_params(query_params)

    where = Operation(
        operator=Operator.AND,
        arguments=[
            Comparison(C.EQ, "tenant_id", auth.tenant_id),
            Comparison(C.EQ, "session_id", query_params.session_id),
            *(
                [Comparison(C.GTE, "start_time", query_params.start_time)]
                if query_params.start_time
                else []
            ),
            Comparison(C.EQ, "is_root", True),
        ],
    )

    (
        sql_from_join_where,
        sql_params,
        join_pushdown,
        subquery_pushdown,
        _,
        _,
    ) = where.accept(SqlVisitorClickhouse(attributes=RUN_ATTRIBUTES, main_table="runs"))

    # build projection
    projection = ", ".join(
        [
            f"runs.{col} AS {col}"
            for col in [
                "tenant_id",
                "session_id",
                "is_root",
                "id",
                "start_time",
                "end_time",
                "total_tokens",
                "prompt_tokens",
                "completion_tokens",
                "prompt_cost",
                "completion_cost",
                "total_cost",
                "name",
                "status",
                "run_type",
                "tags",
                "error",
                "first_token_time",
            ]
        ]
        + [f"{group_by.sql_expr} as group_key"]
    )

    common_query_cte = f"""
WITH

group_filtered_runs AS (
    SELECT {fg_projection} {fg_sql_from_join_where}
    SETTINGS
        optimize_read_in_order = 1,
        function_json_value_return_type_allow_nullable = 1
),

groups AS (
    SELECT
        group_filtered_runs.group_key as group_key,
        group_filtered_runs.session_id as session_id,
        uniq(group_filtered_runs.id) as count,
        min(start_time) as min_start_time,
        max(start_time) as max_start_time
    FROM group_filtered_runs
    GROUP BY group_filtered_runs.group_key, group_filtered_runs.session_id
),

filtered_groups AS (
    SELECT DISTINCT group_key
    {group_sql_from_join_where}
),

filtered_runs AS (
    SELECT {projection}
    {sql_from_join_where} AND group_key IN filtered_groups
    SETTINGS multiple_joins_try_to_keep_original_names = 1,
        optimize_read_in_order = 1,
        function_json_value_return_type_allow_nullable = 1
),

unique_run_keys AS (
    SELECT DISTINCT tenant_id, session_id, is_root, start_time, id
    FROM filtered_runs
),
        """

    group_count_stats_query = (
        common_query_cte
        + """
group_count_stats AS (
    SELECT uniq(group_key) as group_count
    FROM filtered_groups
)

SELECT * FROM group_count_stats
        """
    )

    run_stats_query = (
        common_query_cte
        + """
run_stats AS (
    SELECT"""
        + runs_stats_projection
        + runs_facets_projection
        + """
    FROM filtered_runs
)

SELECT * FROM run_stats
        """
    )

    feedback_stats_query = (
        common_query_cte
        + """
feedback_stats as (
    SELECT"""
        + feedback_stats_projection
        + feedback_facets_projection
        + """
    FROM (
        SELECT * FROM feedbacks_rmt FINAL WHERE """
        + join_pushdown
        + """
    ) AS feedbacks
    INNER JOIN unique_run_keys
        ON feedbacks.tenant_id = unique_run_keys.tenant_id
        AND feedbacks.session_id = unique_run_keys.session_id
        AND feedbacks.is_root = unique_run_keys.is_root
        AND feedbacks.start_time = unique_run_keys.start_time
        AND feedbacks.run_id = unique_run_keys.id
)

SELECT * FROM feedback_stats
        """
    )

    metadata_stats_query = (
        common_query_cte
        + """
metadata_stats as (
    SELECT"""
        + metadata_facets_projection
        + """
    FROM (
        SELECT * FROM runs_metadata_kv WHERE """
        + join_pushdown
        + """
    ) AS runs_metadata_kv
    INNER JOIN unique_run_keys
        ON runs_metadata_kv.tenant_id = unique_run_keys.tenant_id
        AND runs_metadata_kv.session_id = unique_run_keys.session_id
        AND runs_metadata_kv.is_root = unique_run_keys.is_root
        AND runs_metadata_kv.start_time = unique_run_keys.start_time
        AND runs_metadata_kv.run_id = unique_run_keys.id
)

SELECT * FROM metadata_stats
        """
    )

    token_count_stats_query = (
        common_query_cte
        + """
token_counts as (
    SELECT"""
        + token_count_projection
        + """
    FROM unique_run_keys
    LEFT JOIN (
        SELECT * FROM runs_token_counts FINAL
        WHERE runs_token_counts.total_tokens < 4000000000 AND"""
        + subquery_pushdown
        + """
    ) AS runs_token_counts
        ON runs_token_counts.tenant_id = unique_run_keys.tenant_id
        AND runs_token_counts.session_id = unique_run_keys.session_id
        AND runs_token_counts.is_root = unique_run_keys.is_root
        AND runs_token_counts.start_time = unique_run_keys.start_time
        AND runs_token_counts.id = unique_run_keys.id"""
        + """
    GROUP BY id
),

token_count_stats as (
    SELECT"""
        + token_count_stats_projection
        + """,
        if(token_run_count = 0, 0, toUInt32(median(token_counts.total_tokens))) as median_tokens
    FROM token_counts
)

SELECT * FROM token_count_stats
        """
    )

    params = {**sql_params, **fg_sql_params, **group_sql_params}

    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        results = await asyncio.gather(
            ch.fetchrow(
                "fetch_group_count_stats",
                group_count_stats_query,
                params=params,
                with_timeout=True,
            ),
            ch.fetchrow(
                "fetch_group_run_stats",
                run_stats_query,
                params=params,
                with_timeout=True,
            ),
            ch.fetchrow(
                "fetch_feedback_stats",
                feedback_stats_query,
                params=params,
                with_timeout=True,
            ),
            ch.fetchrow(
                "fetch_group_metadata_stats",
                metadata_stats_query,
                params=params,
                with_timeout=True,
            ),
            ch.fetchrow(
                "fetch_group_token_count_stats",
                token_count_stats_query,
                params=params,
                with_timeout=True,
            ),
            return_exceptions=True,
        )

        combined_stats: dict = {}

        # raise exception if all errors
        if all(isinstance(result, Exception) for result in results):
            raise ExceptionGroup(
                "All stats group fetching operations failed",
                cast(list[Exception], results),
            )

        for result in results:
            if isinstance(result, Exception):
                logger.error("Error fetching stats", exc_info=result)
            else:
                combined_stats.update(result)  # type: ignore

    return map_group_stats(combined_stats)


def map_group_stats(stats: Record):
    run_stats = map_stats(stats, True)
    group_count = stats.get("group_count", 0)
    return {**run_stats, "group_count": group_count}
