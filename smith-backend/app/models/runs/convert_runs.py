from contextlib import asynccontextmanager
from typing import (
    Any,
    AsyncGenerator,
    Optional,
)
from urllib.parse import urlparse
from uuid import UUID

import httpx
import structlog
from lc_config.settings import shared_settings as settings

from app.api.auth import BaseAuthInfo
from app.models.runs.tokens import TokenUsage

logger = structlog.getLogger(__name__)


def _extract_unified_run_tokens(outputs: Any | None) -> TokenUsage | None:
    if not outputs or not isinstance(outputs, dict):
        return None

    token_and_cost_fields = [
        "input_tokens",
        "output_tokens",
        "total_tokens",
        "input_token_details",
        "output_token_details",
        "input_cost",
        "output_cost",
        "total_cost",
        "input_cost_details",
        "output_cost_details",
    ]
    if not any(x in outputs for x in token_and_cost_fields):
        return None

    if not (
        isinstance(outputs.get("input_tokens", 0), int)
        and isinstance(outputs.get("output_tokens", 0), int)
        and isinstance(outputs.get("total_tokens", 0), int)
        and isinstance(outputs.get("input_cost", 0.0), (int, float))
        and isinstance(outputs.get("output_cost", 0.0), (int, float))
        and isinstance(outputs.get("total_cost", 0.0), (int, float))
    ):
        return None

    if outputs.get("input_token_details") and _dict_type_check(
        outputs["input_token_details"], key_type=str, val_type=int
    ):
        prompt_token_details = outputs["input_token_details"]
    else:
        prompt_token_details = None

    if outputs.get("output_token_details") and _dict_type_check(
        outputs["output_token_details"], key_type=str, val_type=int
    ):
        completion_token_details = outputs["output_token_details"]
    else:
        completion_token_details = None

    if outputs.get("input_cost_details") and _dict_type_check(
        outputs["input_cost_details"], key_type=str, val_type=(int, float)
    ):
        prompt_cost_details = outputs["input_cost_details"]
    else:
        prompt_cost_details = None

    if outputs.get("output_cost_details") and _dict_type_check(
        outputs["output_cost_details"], key_type=str, val_type=(int, float)
    ):
        completion_cost_details = outputs["output_cost_details"]
    else:
        completion_cost_details = None

    prompt_tokens = (
        outputs.get("input_tokens")
        if outputs.get("input_tokens") is not None
        else sum((prompt_token_details or {}).values())
        if prompt_token_details is not None
        else None
    )
    completion_tokens = (
        outputs.get("output_tokens")
        if outputs.get("output_tokens") is not None
        else sum((completion_token_details or {}).values())
        if completion_token_details is not None
        else None
    )

    prompt_cost = (
        outputs.get("input_cost")
        if outputs.get("input_cost") is not None
        else sum((prompt_cost_details or {}).values())
        if prompt_cost_details is not None
        else None
    )
    completion_cost = (
        outputs.get("output_cost")
        if outputs.get("output_cost") is not None
        else sum((completion_cost_details or {}).values())
        if completion_cost_details is not None
        else None
    )

    total_tokens = (
        outputs.get("total_tokens")
        if outputs.get("total_tokens") is not None
        else (prompt_tokens or 0) + (completion_tokens or 0)
        if (prompt_tokens is not None or completion_tokens is not None)
        else None
    )
    total_cost = (
        outputs.get("total_cost")
        if outputs.get("total_cost") is not None
        else (prompt_cost or 0) + (completion_cost or 0)
        if (completion_cost is not None or prompt_cost is not None)
        else None
    )

    usage = TokenUsage(
        prompt_tokens=prompt_tokens,
        completion_tokens=completion_tokens,
        total_tokens=total_tokens,
        prompt_token_details=prompt_token_details,
        completion_token_details=completion_token_details,
        prompt_cost=prompt_cost,
        completion_cost=completion_cost,
        total_cost=total_cost,
        prompt_cost_details=prompt_cost_details,
        completion_cost_details=completion_cost_details,
    )  # type: ignore[call-arg]
    return usage


def get_unified_run_tokens(outputs: dict | None):
    if not outputs:
        return None

    # search in non-generations lists
    if output := _extract_unified_run_tokens(outputs.get("usage_metadata")):
        return output

    # find if direct kwarg in outputs
    keys = outputs.keys()
    for key in keys:
        haystack = outputs[key]
        if not haystack or not isinstance(haystack, dict):
            continue

        if output := _extract_unified_run_tokens(haystack.get("usage_metadata")):
            return output

        if (
            haystack.get("lc") == 1
            and "kwargs" in haystack
            and isinstance(haystack["kwargs"], dict)
            and (
                output := _extract_unified_run_tokens(
                    haystack["kwargs"].get("usage_metadata")
                )
            )
        ):
            return output

    # find in generations
    # List[Generation] || List[ChatGeneration] || List[List[Generation]] || List[List[ChatGeneration]]
    generations = outputs.get("generations") or []
    if not isinstance(generations, list):
        return None
    if generations and not isinstance(generations[0], list):
        generations = [generations]

    for generation in [x for xs in generations for x in xs]:
        if (
            isinstance(generation, dict)
            and "message" in generation
            and isinstance(generation["message"], dict)
            and "kwargs" in generation["message"]
            and isinstance(generation["message"]["kwargs"], dict)
            and (
                output := _extract_unified_run_tokens(
                    generation["message"]["kwargs"].get("usage_metadata")
                )
            )
        ):
            return output
    return None


def get_run_tokens(
    outputs: dict | None, metadata: dict | None, recursive: bool = True
) -> Optional[TokenUsage]:
    check_outputs = outputs and isinstance(outputs, dict)
    check_metadata = metadata and isinstance(metadata, dict)
    if not check_outputs and not check_metadata:
        return None
    elif check_outputs and (unified := get_unified_run_tokens(outputs)):
        return unified
    elif check_metadata and (unified := get_unified_run_tokens(metadata)):
        return unified
    # linter gets confused if we just use check_outputs here.
    elif not outputs or not isinstance(outputs, dict):
        return None
    elif (
        "llm_output" in outputs
        and isinstance(outputs.get("llm_output"), dict)
        and isinstance(outputs["llm_output"].get("token_usage"), dict)
    ):
        token_usage: dict = outputs["llm_output"]["token_usage"]
        return TokenUsage(
            prompt_tokens=token_usage.get("prompt_tokens"),
            completion_tokens=token_usage.get("completion_tokens"),
        )  # type: ignore[call-arg]
    elif (
        "llmOutput" in outputs
        and isinstance(outputs.get("llmOutput"), dict)
        and isinstance(outputs["llmOutput"].get("tokenUsage"), dict)
    ):
        token_usage = outputs["llmOutput"]["tokenUsage"]
        return TokenUsage(
            prompt_tokens=token_usage.get("promptTokens"),
            completion_tokens=token_usage.get("completionTokens"),
        )  # type: ignore[call-arg]
    elif (
        "usage" in outputs
        and isinstance(outputs.get("usage"), dict)
        and isinstance(outputs["usage"].get("prompt_tokens"), int)
        and isinstance(outputs["usage"].get("completion_tokens"), int)
    ):
        # This is OpenAI's response format
        return TokenUsage(
            prompt_tokens=outputs["usage"]["prompt_tokens"],
            completion_tokens=outputs["usage"]["completion_tokens"],
        )  # type: ignore[call-arg]
    elif recursive and len(outputs) == 1:
        # If there is only one output, try to get the tokens from that
        return get_run_tokens(list(outputs.values())[0], metadata=None, recursive=False)
    else:
        return None


### Handling MultiModal Values in Chat Messages


async def download_image(
    httpx_session: httpx.AsyncClient, image_url: str
) -> tuple[bytes, str] | None:
    try:
        async with httpx_session.stream(
            "GET", image_url, follow_redirects=True
        ) as response:
            if response.status_code != 200:
                return None

            content_type = response.headers.get("Content-Type")
            if not content_type or not content_type.startswith("image/"):
                return None  # Return None if not an image, without downloading the full content
            # If it's an image, read the full content
            content = await response.aread()
            return content, content_type

    except Exception as e:
        await logger.awarning(f"Error downloading image from {image_url}: {repr(e)}")
        return None


def is_valid_url(url: str) -> bool:
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


# NOTE: modifies inputs and outputs in place
async def process_mm_run_io(
    _: BaseAuthInfo,
    __: UUID,
    ___: dict | None,
    ____: dict | None,
    _____: httpx.AsyncClient | None = None,
):
    """Process multimodal values in a run's inputs and outputs."""
    return {
        "input_attachments": {},
        "output_attachments": {},
    }
    # Unused code. Keeping around in case we need to re-enable this feature.
    # async with mm_run_io_httpx_session(httpx_session) as httpx_session:
    #     input_attachments: Dict[Any, Any] = {}
    #     output_attachments: Dict[Any, Any] = {}
    #     if inputs:
    #         await replace_values(
    #             httpx_session, auth, session_id, inputs, input_attachments
    #         )
    #     if outputs:
    #         await replace_values(
    #             httpx_session, auth, session_id, outputs, output_attachments
    #         )
    #     return {
    #         "input_attachments": input_attachments,
    #         "output_attachments": output_attachments,
    #     }


@asynccontextmanager
async def mm_run_io_httpx_session(
    httpx_session: httpx.AsyncClient | None,
) -> AsyncGenerator[httpx.AsyncClient | None, None]:
    if httpx_session is not None:
        yield httpx_session
    elif not settings.FF_S3_URL_STORAGE_ENABLED:
        yield None
    else:
        async with httpx.AsyncClient(
            transport=httpx.AsyncHTTPTransport(
                retries=5,
                limits=httpx.Limits(
                    max_connections=100,
                    max_keepalive_connections=20,
                    keepalive_expiry=60.0,
                ),
            ),
            timeout=httpx.Timeout(
                connect=5.0,
                read=15.0,
                write=15.0,
                pool=30.0,
            ),
        ) as httpx_session:
            yield httpx_session


def _dict_type_check(
    val: Any, key_type: type | tuple[type, ...], val_type: type | tuple[type, ...]
):
    return isinstance(val, dict) and all(
        isinstance(k, key_type) and isinstance(v, val_type) for k, v in val.items()
    )
