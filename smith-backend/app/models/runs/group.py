import asyncio
import datetime
import math
from typing import Any, AsyncIterator, Callable, cast

import structlog
from lc_config.settings import shared_settings as settings
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.s3_client import get_run_data
from pydantic import BaseModel

from app import schemas
from app.api.auth import AuthInfo
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    FilterDirective,
    Operation,
    Operator,
)
from app.models.query_lang.translate import (
    BaseAttributeInfo,
    SqlAttributeInfo,
    SqlVisitorClickhouse,
    convert_datetime,
)
from app.models.runs.fetch_ch import (
    RUN_ATTRIBUTES,
    get_run_attributes,
    parse_as_filter_directive,
)
from app.models.runs.ingest import ERROR_S3_KEY, ROOT_S3_KEY
from app.models.runs.preview import preview_inputs, preview_outputs
from app.models.runs.utils import process_feedback_value_stats
from app.retry import retry_clickhouse_read
from app.utils import arun_in_executor, completed_future, gated_coro, load_json

logger = structlog.get_logger(__name__)


class GroupBy(BaseModel):
    name: str

    run_sql_projection: list[str]

    sql_expr: str

    filter_expr: Callable[[str], str]


GROUP_BY_METHODS = [
    GroupBy(
        name="conversation",
        run_sql_projection=[],
        sql_expr="thread_id",
        # TODO: Convert this to thread_id after some time
        filter_expr=lambda group_key: f'eq(thread_id, "{group_key}")',
    )
]


GROUP_ATTRIBUTES: list[BaseAttributeInfo] = [
    SqlAttributeInfo(
        name="max_start_time",
        sql_expression="groups.max_start_time",
        sql_value=convert_datetime,
    ),
    SqlAttributeInfo(
        name="run_count",
        sql_expression="groups.count",
    ),
]

GROUP_ATTR_NAMES = {attr.name for attr in GROUP_ATTRIBUTES}


def _parse_group_filter_directive(directive: FilterDirective) -> FilterDirective | None:
    if isinstance(directive, Comparison):
        if directive.attribute is None and directive.comparator != C.SEARCH:
            return directive

        return directive if directive.attribute in GROUP_ATTR_NAMES else None

    elif isinstance(directive, Operation):
        filtered_arguments = [
            _parse_group_filter_directive(arg) for arg in directive.arguments
        ]
        arguments = [arg for arg in filtered_arguments if arg is not None]

        if len(arguments) == 0:
            return None
        return Operation(directive.operator, arguments)

    return None


async def _get_run_input_preview(run: dict, s3: dict | None) -> dict | None:
    if run.get("inputs_preview") is not None:
        return run["inputs_preview"]
    return await arun_in_executor(preview_inputs, s3 or run["inputs"])


async def _get_run_output_preview(run: dict, s3: dict | None) -> dict | None:
    if run.get("outputs_preview") is not None:
        return run["outputs_preview"]
    return await arun_in_executor(preview_outputs, s3 or run["outputs"])


def _parse_run_filter_directive(directive: FilterDirective) -> FilterDirective | None:
    if isinstance(directive, Comparison):
        if directive.attribute is None:
            return directive

        return directive if directive.attribute not in GROUP_ATTR_NAMES else None

    elif isinstance(directive, Operation):
        filtered_arguments = [
            _parse_run_filter_directive(arg) for arg in directive.arguments
        ]
        arguments = [arg for arg in filtered_arguments if arg is not None]

        if len(arguments) == 0:
            return None
        return Operation(directive.operator, arguments)

    return None


def _get_run_filter_any_sql(
    auth: AuthInfo, params: schemas.RunGroupRequest
) -> tuple[str | None, dict[str, Any]]:
    if not params.filter or len(params.filter) == 0:
        return None, {}

    where = Operation(
        operator=Operator.AND,
        arguments=[
            Comparison(C.EQ, "tenant_id", auth.tenant_id),
            Comparison(C.EQ, "session_id", params.session_id),
            Comparison(C.EQ, "is_trace_expired", False),
            *(
                [Comparison(C.GTE, "start_time", params.start_time)]
                if params.start_time is not None
                else []
            ),
            parse_as_filter_directive(params.filter),
        ],
    )

    run_where = _parse_run_filter_directive(where)
    if run_where is None:
        return None, {}

    group_by = next(x for x in GROUP_BY_METHODS if x.name == params.group_by)

    (
        sql_from_join_where,
        sql_params,
        _,
        _,
        _,
        _,
    ) = run_where.accept(
        SqlVisitorClickhouse(
            attributes=get_run_attributes(run_where.arguments),
            main_table="runs",
            param_suffix="group_run_filter",
        )
    )

    sql_query = f"""
        SELECT DISTINCT {group_by.sql_expr} as group_key
        {sql_from_join_where} AND group_key is not NULL AND group_key != ''
        SETTINGS
            optimize_read_in_order = 1,
            function_json_value_return_type_allow_nullable = true
    """

    return sql_query, sql_params


def _get_run_filter_group_sql_params(
    auth: AuthInfo,
    params: schemas.RunGroupRequest,
    group_key_param: list[str] | None = None,
) -> tuple[str, str, Any, Any, Any]:
    where = Operation(
        operator=Operator.AND,
        arguments=[
            Comparison(C.EQ, "tenant_id", auth.tenant_id),
            Comparison(C.EQ, "session_id", params.session_id),
            Comparison(C.EQ, "is_root", True),
            Comparison(C.EQ, "is_trace_expired", False),
        ]
        + (
            [Comparison(C.GTE, "start_time", params.start_time)]
            if params.start_time
            else []
        ),
    )

    (
        sql_from_join_where,
        sql_params,
        join_pushdown,
        subquery_pushdown,
        _,
        _,
    ) = where.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES, main_table="runs", param_suffix="group_filter"
        )
    )

    group_by = next(x for x in GROUP_BY_METHODS if x.name == params.group_by)

    sql_projection = ", ".join(
        list(
            f"runs.{col} AS {col}"
            for col in set(
                ["tenant_id", "session_id", "is_root", "start_time", "end_time", "id"]
                + group_by.run_sql_projection
            )
        )
        + [f"{group_by.sql_expr} as group_key"]
    )

    any_filter_cte, any_filter_sql_params = _get_run_filter_any_sql(auth, params)
    if group_key_param:
        sql_from_join_where += " AND group_key in {group_key}"
        sql_params["group_key"] = group_key_param
    elif any_filter_cte:
        sql_from_join_where += f" AND group_key in ({any_filter_cte})"
        sql_params |= any_filter_sql_params
    else:
        sql_from_join_where += " AND group_key is not NULL AND group_key != ''"

    return (
        sql_projection,
        sql_from_join_where,
        sql_params,
        join_pushdown,
        subquery_pushdown,
    )


def _get_run_filter_group_token_stats_sql_params(
    auth: AuthInfo,
    params: schemas.RunGroupRequest,
    group_key_param: list[str] | None = None,
) -> tuple[str, Any, Any]:
    where = Operation(
        operator=Operator.AND,
        arguments=[
            Comparison(C.EQ, "tenant_id", auth.tenant_id),
            Comparison(C.EQ, "session_id", params.session_id),
            Comparison(C.EQ, "is_trace_expired", False),
        ]
        + (
            [Comparison(C.GTE, "start_time", params.start_time)]
            if params.start_time
            else []
        ),
    )

    (
        sql_from_join_where,
        sql_params,
        _,
        _,
        _,
        _,
    ) = where.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES, main_table="runs", param_suffix="group_filter"
        )
    )

    group_by = next(x for x in GROUP_BY_METHODS if x.name == params.group_by)

    sql_projection = ", ".join(
        list(
            f"runs.{col} AS {col}"
            for col in set(
                ["tenant_id", "session_id", "is_root", "start_time", "id"]
                + group_by.run_sql_projection
            )
        )
        + [f"{group_by.sql_expr} as group_key"]
        + [
            "argMax(if(total_tokens < 4000000000, total_tokens, 0), modified_at) AS total_tokens_max",
            "argMax(if(total_tokens < 4000000000, total_cost, 0), modified_at) AS total_cost_max",
        ]
    )

    any_filter_cte, any_filter_sql_params = _get_run_filter_any_sql(auth, params)
    if group_key_param:
        sql_from_join_where += " AND group_key in {group_key}"
        sql_params["group_key"] = group_key_param
    elif any_filter_cte:
        sql_from_join_where += f" AND group_key in ({any_filter_cte})"
        sql_params |= any_filter_sql_params
    else:
        sql_from_join_where += " AND group_key is not NULL AND group_key != ''"

    return (sql_projection, sql_from_join_where, sql_params)


def _get_group_sql_params(params: schemas.RunGroupRequest):
    group_where = Operation(operator=Operator.AND, arguments=[])
    if params.start_time:
        group_where.arguments.append(
            Comparison(C.GTE, "max_start_time", params.start_time)
        )
    if params.end_time:
        group_where.arguments.append(
            Comparison(C.LTE, "max_start_time", params.end_time)
        )
    if params.filter:
        group_where.arguments.append(parse_as_filter_directive(params.filter))

    group_where = _parse_group_filter_directive(group_where)

    if group_where:
        group_sql_from_join_where, group_sql_params, _, _, _, _ = group_where.accept(
            SqlVisitorClickhouse(attributes=GROUP_ATTRIBUTES, main_table="groups")
        )
        return group_sql_from_join_where, group_sql_params

    return "FROM groups", {}


@retry_clickhouse_read
async def _get_grouped_runs(
    auth: AuthInfo, params: schemas.RunGroupRequest
) -> tuple[list[dict], list[dict]]:
    (
        _,
        sql_from_join_where,
        sql_params,
        _,
        _,
    ) = _get_run_filter_group_sql_params(auth, params, group_key_param=None)

    group_sql_from_join_where, group_sql_params = _get_group_sql_params(params)

    group_by = next(x for x in GROUP_BY_METHODS if x.name == params.group_by)

    # gather sql where and params for group runs. We need to join with runs to get across all times for that thread.
    group_runs_where = Operation(
        operator=Operator.AND,
        arguments=[
            Comparison(C.EQ, "tenant_id", auth.tenant_id),
            Comparison(C.EQ, "session_id", params.session_id),
            Comparison(C.EQ, "is_root", True),
            Comparison(C.EQ, "is_trace_expired", False),
        ],
    )

    (
        sql_group_runs_where,
        sql_group_runs_params,
        _,
        _,
        _,
        _,
    ) = group_runs_where.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
            param_suffix="group_runs_filter",
        )
    )

    sql_query = f"""
        WITH filtered_runs AS (
            SELECT DISTINCT {group_by.sql_expr} as group_key
            {sql_from_join_where}
            SETTINGS
                optimize_read_in_order = 1,
                function_json_value_return_type_allow_nullable = true
        ),
        groups as (
            SELECT
                {group_by.sql_expr} as group_key,
                runs.session_id as session_id,
                uniq(runs.trace_id) as count,
                arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(greatest(0, date_diff('ms', start_time, end_time, 'UTC')))) as latency_ptiles,
                min(start_time) as min_start_time,
                max(start_time) as max_start_time,
                argMin(runs.id, start_time) as first_run_id,
                argMax(runs.id, start_time) as last_run_id,
                argMin(runs.is_root, start_time) as first_run_is_root,
                argMax(runs.is_root, start_time) as last_run_is_root
            {sql_group_runs_where}
            AND group_key IN (SELECT group_key FROM filtered_runs)
            GROUP BY group_key, runs.session_id
            ORDER BY max_start_time DESC
            SETTINGS
                optimize_read_in_order = 1,
                function_json_value_return_type_allow_nullable = true
        )
        SELECT groups.*
        {group_sql_from_join_where}
        LIMIT {{limit}}
        OFFSET {{offset}}
        SETTINGS max_execution_time={settings.CLICKHOUSE_FE_TIMEOUT}, max_threads={settings.CLICKHOUSE_GROUPS_MAX_THREADS}
    """

    async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
        raw_groups = await ch.fetch(
            "group_runs",
            sql_query,
            params={
                **sql_params,
                **group_sql_params,
                **sql_group_runs_params,
                "limit": params.limit + 1,
                "offset": params.offset,
            },
        )

        return [
            {
                "group_key": raw_group["group_key"],
                "filter": group_by.filter_expr(raw_group["group_key"]),
                "count": raw_group["count"],
                "min_start_time": raw_group["min_start_time"],
                "max_start_time": raw_group["max_start_time"],
                "first_run_id": raw_group["first_run_id"],
                "last_run_id": raw_group["last_run_id"],
            }
            for raw_group in raw_groups
        ], raw_groups


async def _get_grouped_runs_stats(
    raw_groups: list[dict], auth: AuthInfo, params: schemas.RunGroupRequest
) -> dict[str, Any]:
    # This function returns stats for grouped runs.
    # Note that if any queries fail it will return empty for that stat.
    semaphore = asyncio.Semaphore(settings.BLOB_STORAGE_FETCH_SEMAPHORE)

    (
        projection,
        sql_from_join_where,
        sql_params,
        join_pushdown,
        subquery_pushdown,
    ) = _get_run_filter_group_sql_params(
        auth, params, group_key_param=[group["group_key"] for group in raw_groups]
    )

    feedback_stats_sql_query = f"""
        WITH filtered_runs AS (
            SELECT {projection} {sql_from_join_where}
            ORDER BY runs.start_time DESC, id DESC
            SETTINGS
                optimize_read_in_order = 1,
                function_json_value_return_type_allow_nullable = true
        )
        SELECT
            group_key,
            mapKeys(uniqMap(map(key, feedbacks.id))) as feedback_keys,
            mapValues(avgMap(map(key, COALESCE(
                CASE
                    WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
                    ELSE NULL
                END,
                score
            )))) as feedback_avgs,
            mapValues(stddevPopMap(map(key, COALESCE(
                CASE
                    WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
                    ELSE NULL
                END,
                score
            )))) as feedback_stdevs,
            mapValues(uniqMap(map(key, feedbacks.id))) as feedback_counts,
            mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
            mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts
        FROM (
            SELECT DISTINCT tenant_id, session_id, is_root, start_time, id, group_key
            FROM filtered_runs
        ) as unique_run_keys
        INNER JOIN (
            SELECT * FROM feedbacks_rmt FINAL WHERE {join_pushdown}
        ) AS feedbacks
            ON feedbacks.tenant_id = unique_run_keys.tenant_id
            AND feedbacks.session_id = unique_run_keys.session_id
            AND feedbacks.is_root = unique_run_keys.is_root
            AND feedbacks.start_time = unique_run_keys.start_time
            AND feedbacks.run_id = unique_run_keys.id
        GROUP BY group_key
        SETTINGS max_execution_time={settings.CLICKHOUSE_FE_TIMEOUT}, max_threads={settings.CLICKHOUSE_GROUPS_MAX_THREADS}
    """

    (
        sql_token_stats_projection,
        sql_token_stats_from_join_where,
        sql_token_stats_params,
    ) = _get_run_filter_group_token_stats_sql_params(
        auth, params, group_key_param=[group["group_key"] for group in raw_groups]
    )

    token_counts_sql_query = f"""
        WITH filtered_runs AS (
            SELECT {sql_token_stats_projection}
            {sql_token_stats_from_join_where}
            GROUP BY (tenant_id, session_id, is_root, start_time, id, group_key)
        )
        SELECT
            group_key,
            sum(total_tokens_max) as total_tokens,
            sum(total_cost_max) as total_cost
        FROM filtered_runs
        GROUP BY group_key
        SETTINGS
            max_execution_time={settings.CLICKHOUSE_FE_TIMEOUT},
            max_threads={settings.CLICKHOUSE_GROUPS_MAX_THREADS},
            optimize_read_in_order = 1,
            function_json_value_return_type_allow_nullable = 1
    """

    if not raw_groups:
        return {}

    # forms a set of the first and last run of each thread
    runs_from_groups = set(
        [
            (
                auth.tenant_id,
                group["session_id"],
                group["first_run_is_root"],
                group["min_start_time"],
                group["first_run_id"],
            )
            for group in raw_groups
        ]
        + [
            (
                auth.tenant_id,
                group["session_id"],
                group["last_run_is_root"],
                group["max_start_time"],
                group["last_run_id"],
            )
            for group in raw_groups
        ]
    )

    # gets the inputs, outputs and errors for the first and last run of each thread
    io_sql_where_terms = [
        [
            pair
            for pair in zip(
                [
                    "tenant_id",
                    "session_id",
                    "is_root",
                    "start_time",
                    "id",
                ],
                [
                    "tenant_id",
                    "session_id",
                    f"is_root_{idx}",
                    f"start_time_{idx}",
                    f"run_id_{idx}",
                ],
                values,
            )
        ]
        for idx, values in enumerate(runs_from_groups)
    ]

    io_sql_where_clause = " OR ".join(
        f"({' AND '.join(f'{name} = {{{arg}}}' for name, arg, _ in clause)})"
        for clause in io_sql_where_terms
    )

    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        results = await asyncio.gather(
            ch.fetch(
                "group_runs_feedback_stats",
                feedback_stats_sql_query,
                params=sql_params,
            ),
            ch.fetch(
                "group_runs_token_counts",
                token_counts_sql_query,
                params=sql_token_stats_params,
            ),
            ch.fetch(
                "group_runs_io",
                f"""
                    SELECT
                        id,
                        argMax(runs.inputs, modified_at) as inputs,
                        argMax(runs.inputs_preview, modified_at) as inputs_preview,
                        argMax(runs.inputs_s3_urls, modified_at) as inputs_s3_urls,
                        argMax(runs.outputs, modified_at) as outputs,
                        argMax(runs.outputs_preview, modified_at) as outputs_preview,
                        argMax(runs.outputs_s3_urls, modified_at) as outputs_s3_urls,
                        argMax(runs.error, modified_at) as error,
                        argMax(runs.s3_urls, modified_at) as s3_urls
                    FROM runs
                    PREWHERE {io_sql_where_clause}
                    GROUP BY (tenant_id, session_id, is_root, start_time, id)
                    SETTINGS max_execution_time={settings.CLICKHOUSE_FE_TIMEOUT}, max_threads={settings.CLICKHOUSE_GROUPS_MAX_THREADS}
                """,
                params={
                    name: val for item in io_sql_where_terms for _, name, val in item
                },
            ),
            return_exceptions=True,
        )

        # raise exception if all errors
        if all(isinstance(result, Exception) for result in results):
            raise ExceptionGroup(
                "Failed fetching all group stats",
                cast(list[Exception], results),
            )

        # log errors if partial
        for result in results:
            if isinstance(result, Exception):
                await logger.awarn(
                    "Partial failure in grouped stats query", exc_info=result
                )

        (feedback_stats, token_counts, io_runs) = results

        s3_input_urls = (
            [
                (load_json(run.get("inputs_s3_urls")) or {}).get(ROOT_S3_KEY)
                if run.get("inputs_preview") is None
                else None
                for run in cast(list[dict], io_runs)
            ]
            if not isinstance(io_runs, Exception)
            else []
        )

        s3_output_urls = (
            [
                (load_json(run.get("outputs_s3_urls")) or {}).get(ROOT_S3_KEY)
                if run.get("outputs_preview") is None
                else None
                for run in cast(list[dict], io_runs)
            ]
            if not isinstance(io_runs, Exception)
            else []
        )

        s3_error_urls = (
            [
                (load_json(run.get("s3_urls")) or {}).get(ERROR_S3_KEY)
                if run.get("error") is None
                else None
                for run in cast(list[dict], io_runs)
            ]
            if not isinstance(io_runs, Exception)
            else []
        )

        (s3_inputs, s3_outputs, s3_errors) = await asyncio.gather(
            asyncio.gather(
                *(
                    [
                        gated_coro(get_run_data(s3_path), semaphore)
                        if s3_path
                        else completed_future(None)
                        for s3_path in s3_input_urls
                    ]
                )
                if settings.FF_BLOB_STORAGE_ENABLED and s3_input_urls
                else [],
            ),
            asyncio.gather(
                *(
                    [
                        gated_coro(get_run_data(s3_path), semaphore)
                        if s3_path
                        else completed_future(None)
                        for s3_path in s3_output_urls
                    ]
                )
                if settings.FF_BLOB_STORAGE_ENABLED and s3_output_urls
                else [],
            ),
            asyncio.gather(
                *(
                    [
                        gated_coro(get_run_data(s3_path), semaphore)
                        if s3_path
                        else completed_future(None)
                        for s3_path in s3_error_urls
                    ]
                )
                if settings.FF_BLOB_STORAGE_ENABLED and s3_error_urls
                else [],
            ),
        )

        (inputs_map_gather, outputs_map_gather) = await asyncio.gather(
            asyncio.gather(
                *[
                    _get_run_input_preview(run, s3)
                    for run, s3 in zip(cast(list[dict], io_runs), s3_inputs)
                ]
            )
            if not isinstance(io_runs, Exception)
            else completed_future([]),
            asyncio.gather(
                *[
                    _get_run_output_preview(run, s3)
                    for run, s3 in zip(cast(list[dict], io_runs), s3_outputs)
                ]
            )
            if not isinstance(io_runs, Exception)
            else completed_future([]),
        )

        inputs_map = (
            {
                run["id"]: result
                for run, result in zip(cast(list[dict], io_runs), inputs_map_gather)
            }
            if not isinstance(io_runs, Exception)
            else {}
        )

        outputs_map = (
            {
                run["id"]: result
                for run, result in zip(cast(list[dict], io_runs), outputs_map_gather)
            }
            if not isinstance(io_runs, Exception)
            else {}
        )

        errors_map = (
            {
                run["id"]: run["error"] or s3
                for run, s3 in zip(cast(list[dict], io_runs), s3_errors)
            }
            if not isinstance(io_runs, Exception)
            else {}
        )

        feedback_stats = (
            {
                row["group_key"]: {
                    key: dict(n=n, avg=avg, stdev=stdev, values=values)
                    for key, n, avg, stdev, values in zip(
                        row["feedback_keys"] or [],
                        row["feedback_counts"] or [],
                        row["feedback_avgs"] or [],
                        row["feedback_stdevs"] or [],
                        process_feedback_value_stats(row),
                    )
                }
                for row in cast(list[dict], feedback_stats)
            }
            if not isinstance(feedback_stats, Exception)
            else {}
        )

        token_counts = (
            {
                row["group_key"]: {
                    "total_tokens": row["total_tokens"],
                    "total_cost": row["total_cost"],
                }
                for row in cast(list[dict], token_counts)
            }
            if not isinstance(token_counts, Exception)
            else {}
        )

        return {
            raw_group["group_key"]: {
                "feedback_stats": feedback_stats.get(raw_group["group_key"], None),
                **(token_counts.get(raw_group["group_key"], None) or {}),
                "latency_p50": datetime.timedelta(
                    seconds=raw_group["latency_ptiles"][0]
                ).total_seconds()
                if raw_group["latency_ptiles"][0] is not None
                and not math.isnan(raw_group["latency_ptiles"][0])
                else None,
                "latency_p99": datetime.timedelta(
                    seconds=raw_group["latency_ptiles"][1]
                ).total_seconds()
                if raw_group["latency_ptiles"][1] is not None
                and not math.isnan(raw_group["latency_ptiles"][1])
                else None,
                "first_inputs": inputs_map.get(raw_group["first_run_id"])
                if raw_group["first_run_id"]
                else None,
                "last_outputs": outputs_map.get(raw_group["last_run_id"])
                if raw_group["last_run_id"]
                else None,
                "last_error": errors_map.get(raw_group["last_run_id"])
                if raw_group["last_run_id"]
                else None,
            }
            for raw_group in raw_groups
        }


async def stream_group_runs(
    auth: AuthInfo, params: schemas.RunGroupRequest
) -> AsyncIterator[list[dict]]:
    groups, raw_groups = await _get_grouped_runs(auth, params)
    total = len(raw_groups) + params.offset
    yield [{"op": "add", "path": "", "value": {"groups": groups, "total": total}}]

    stats = await _get_grouped_runs_stats(raw_groups, auth, params)
    yield [
        {"op": "add", "path": f"/groups/{i}/{k}", "value": v}
        for i, group in enumerate(groups)
        for k, v in stats[group["group_key"]].items()
    ]


async def group_runs(
    auth: AuthInfo, params: schemas.RunGroupRequest
) -> schemas.RunGroupResponse:
    groups, raw_groups = await _get_grouped_runs(auth, params)
    total = len(raw_groups) + params.offset

    stats = await _get_grouped_runs_stats(raw_groups, auth, params)

    return schemas.RunGroupResponse(
        groups=[{**group, **stats[group["group_key"]]} for group in groups],
        total=total,
    )
