import asyncio
import decimal
from typing import Any, Awaitable, Callable

import httpx
import orj<PERSON>
from ddtrace import tracer
from ddtrace.constants import SPAN_KIND
from ddtrace.ext import Span<PERSON>ind
from httpx._types import HeaderTypes, QueryParamTypes
from lc_config.settings import shared_settings
from tenacity import (
    retry,
    retry_if_exception,
    stop_after_attempt,
    wait_exponential_jitter,
)


def is_retriable_error(exception: Exception) -> bool:
    if isinstance(exception, httpx.TransportError):
        return True
    if isinstance(exception, httpx.HTTPStatusError):
        if exception.response.status_code > 499:
            return True

    return False


retry_httpx = retry(
    reraise=True,
    retry=retry_if_exception(is_retriable_error),
    wait=wait_exponential_jitter(),
    stop=stop_after_attempt(3),
)


class JsonHttpClient:
    """HTTPX client for JSON requests, with retries."""

    def __init__(self, client: httpx.AsyncClient, timeout: int) -> None:
        """Initialize the auth client."""
        self.client = client
        self.timeout = timeout

    async def _post(
        self,
        path: str,
        json: dict,
        *,
        params: QueryParamTypes | None = None,
        headers: HeaderTypes | None = None,
    ) -> None:
        def convert_decimals(obj):
            if isinstance(obj, dict):
                return {k: convert_decimals(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_decimals(item) for item in obj]
            elif isinstance(obj, decimal.Decimal):
                return float(obj)
            else:
                return obj

        # Convert any Decimal values to float for JSON serialization
        json_safe = convert_decimals(json)

        try:
            res = await asyncio.wait_for(
                self.client.post(
                    path,
                    content=orjson.dumps(json_safe),
                    params=params,
                    headers={
                        "Content-Type": "application/json",
                        **(headers or {}),
                    },
                ),
                # httpx timeout controls are additive for each operation
                # (connect, read, write), so we need an asyncio timeout instead
                self.timeout,
            )
            # Raise for retriable errors
            res.raise_for_status()
        finally:
            # We don't need the response body, so we close the response
            try:
                await res.aclose()
            except UnboundLocalError:
                pass

    @retry_httpx
    async def post(
        self,
        path: str,
        json: dict,
        *,
        params: QueryParamTypes | None = None,
        headers: HeaderTypes | None = None,
    ) -> None:
        if not shared_settings.DATADOG_ENABLED:
            return await self._post(path, json, params=params, headers=headers)

        with tracer.trace(
            "run_rules.webhook", service="run_rules", resource="POST: " + path
        ) as span:
            span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
            return await self._post(path, json, params=params, headers=headers)


def webhook_client() -> JsonHttpClient:
    """Create the auth http client."""
    if shared_settings.LANGCHAIN_ENV == "local_test":

        async def webhook_destination_asgi_app(
            scope: dict[str, Any],
            receive: Callable[[], Awaitable[dict[str, Any]]],
            send: Callable[[dict[str, Any]], Awaitable[None]],
        ) -> None:
            assert scope["type"] == "http", f"Expected http, got {scope['type']}"
            assert scope["path"] == "/webhook", (
                f"Expected /webhook, got {scope['path']}"
            )
            message = await receive()
            assert message["type"] == "http.request", (
                f"Expected http.request, got {message['type']}"
            )
            body = orjson.loads(message["body"])
            assert isinstance(body["rule_id"], str), (
                f"Expected body['rule_id'] to be a string, got {body['rule_id']}"
            )
            assert isinstance(body["start_time"], str), (
                f"Expected body['start_time'] to be a string, got {body['start_time']}"
            )
            assert isinstance(body["end_time"], str), (
                f"Expected body['end_time'] to be a string, got {body['end_time']}"
            )
            assert isinstance(body["runs"], list), (
                f"Expected body['runs'] to be a list, got {body['runs']}"
            )
            assert all(isinstance(run, dict) for run in body["runs"]), (
                "Expected all runs to be dictionaries"
            )
            assert all(isinstance(run["id"], str) for run in body["runs"]), (
                "Expected all runs to have an 'id' key"
            )
            assert body["runs"], "has runs"
            assert all(run.get("feedback_stats") for run in body["runs"]), (
                "runs have feedback stats"
            )
            await send(
                {
                    "type": "http.response.start",
                    "status": 200,
                    "headers": [[b"content-type", b"application/json"]],
                }
            )
            await send({"type": "http.response.body"})

        # set webhook client for tests
        return JsonHttpClient(
            client=httpx.AsyncClient(
                transport=httpx.ASGITransport(webhook_destination_asgi_app)
            ),
            timeout=5,
        )

    global _client
    try:
        return _client
    except NameError:
        _client = JsonHttpClient(
            client=httpx.AsyncClient(
                transport=httpx.AsyncHTTPTransport(
                    retries=2,  # this applies only to ConnectError, ConnectTimeout
                    limits=httpx.Limits(
                        max_keepalive_connections=10, keepalive_expiry=60.0
                    ),
                ),
            ),
            timeout=5,
        )
        return _client


_client: JsonHttpClient
