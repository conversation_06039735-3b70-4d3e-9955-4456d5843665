from typing import Any, Literal

import orjson

from app.config import settings
from app.models.feedback.utils import normalize_feedback_key, normalize_feedback_value
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    FilterDirective,
    Operation,
    Operator,
)
from app.models.query_lang.translate import (
    AltAttributeInfo,
    BaseAttributeInfo,
    SqlAttributeInfo,
    convert_datetime,
    convert_interval,
    convert_lower,
    convert_uuid,
)
from app.models.runs.tokenize import tokenize_string


def convert_metadata_value(value: Any) -> str:
    return orjson.dumps(value).decode("utf-8")


def convert_io_value(value: Any) -> str:
    return orjson.dumps(value).decode("utf-8").lower()


def translate_metadata(comp: Comparison) -> FilterDirective:
    assert comp.comparator == C.HAS
    value = orjson.loads(comp.value)
    assert isinstance(value, dict)
    ops = [
        Operation(
            operator=Operator.AND,
            arguments=[
                Comparison(C.EQ, "metadata_key", key),
                Comparison(C.EQ, "metadata_value", value),
            ],
        )
        for key, value in value.items()
    ]
    if len(ops) == 1:
        return ops[0]
    else:
        return Operation(operator=Operator.AND, arguments=ops)


def translate_tags(comp: Comparison) -> FilterDirective:
    assert comp.comparator == C.HAS
    return Comparison(C.EQ, "tag", comp.value)


def _token_search(search: str, fields: list[str]) -> list[FilterDirective]:
    # if multiple keywords search for all of them
    tokens = tokenize_string(search)
    token_searches = []
    for field in fields:
        token_searches.append(
            Operation(
                operator=Operator.AND,
                arguments=[
                    Comparison(C.LIKE, field, f"%{token}%", multi=True)
                    for token in tokens
                ],
            )
        )
    return token_searches


def _negative_token_search(search: str, fields: list[str]) -> list[FilterDirective]:
    # if multiple keywords search for all of them
    tokens = tokenize_string(search)
    token_searches = []
    for field in fields:
        token_searches.append(
            Operation(
                operator=Operator.OR,
                arguments=[
                    Comparison(C.NOTLIKE, field, f"%{token}%", multi=True)
                    for token in tokens
                ],
            )
        )
    return token_searches


def translate_search(comp: Comparison) -> FilterDirective:
    assert comp.comparator == C.SEARCH
    assert isinstance(comp.value, str)
    if not settings.FF_BLOB_STORAGE_ENABLED:
        return Operation(
            operator=Operator.OR,
            arguments=[
                Comparison(
                    C.LIKE, "lower_inputs", f"%{comp.value.lower()}%", multi=True
                ),
                Comparison(
                    C.LIKE, "lower_outputs", f"%{comp.value.lower()}%", multi=True
                ),
            ],
        )
    else:
        # search both raw inputs, outputs and tokens as well since data could be in either
        return Operation(
            operator=Operator.OR,
            arguments=[
                *_token_search(comp.value.lower(), ["input_tokens", "output_tokens"]),
            ],
        )


def translate_input_search(comp: Comparison) -> FilterDirective:
    assert comp.comparator == C.LIKE or comp.comparator == C.NOTLIKE
    if not settings.FF_BLOB_STORAGE_ENABLED:
        if comp.comparator == C.LIKE:
            return Comparison(
                C.LIKE, "lower_inputs", f"%{comp.value.lower()}%", multi=True
            )
        else:
            return Comparison(
                C.NOTLIKE, "lower_inputs", f"%{comp.value.lower()}%", multi=True
            )

    if comp.comparator == C.LIKE:
        return Operation(
            operator=Operator.OR,
            arguments=[
                *_token_search(comp.value.lower(), ["input_tokens"]),
            ],
        )
    else:
        return Operation(
            operator=Operator.AND,
            arguments=[
                *_negative_token_search(comp.value.lower(), ["input_tokens"]),
            ],
        )


def translate_output_search(comp: Comparison) -> FilterDirective:
    assert comp.comparator == C.LIKE or comp.comparator == C.NOTLIKE
    if not settings.FF_BLOB_STORAGE_ENABLED:
        if comp.comparator == C.LIKE:
            return Comparison(
                C.LIKE, "lower_outputs", f"%{comp.value.lower()}%", multi=True
            )
        else:
            return Comparison(
                C.NOTLIKE, "lower_outputs", f"%{comp.value.lower()}%", multi=True
            )

    if comp.comparator == C.LIKE:
        return Operation(
            operator=Operator.OR,
            arguments=[
                *_token_search(comp.value.lower(), ["output_tokens"]),
            ],
        )
    else:
        return Operation(
            operator=Operator.AND,
            arguments=[
                *_negative_token_search(comp.value.lower(), ["output_tokens"]),
            ],
        )


def translate_error_search(comp: Comparison) -> FilterDirective:
    assert comp.comparator == C.LIKE or comp.comparator == C.NOTLIKE
    if not settings.FF_BLOB_STORAGE_ENABLED:
        if comp.comparator == C.LIKE:
            return Comparison(
                C.LIKE, "lower_error", f"%{comp.value.lower()}%", multi=True
            )
        else:
            return Comparison(
                C.NOTLIKE, "lower_error", f"%{comp.value.lower()}%", multi=True
            )

    if comp.comparator == C.LIKE:
        return Operation(
            operator=Operator.OR,
            arguments=[
                *_token_search(comp.value.lower(), ["error_tokens"]),
            ],
        )
    else:
        return Operation(
            operator=Operator.AND,
            arguments=[
                *_negative_token_search(comp.value.lower(), ["error_tokens"]),
            ],
        )


def _get_attributes_direct(
    table: Literal["runs"] | Literal["runs_history"],
) -> list[BaseAttributeInfo]:
    return [
        SqlAttributeInfo(
            name="id",
            type="uuid",
            sql_value=convert_uuid,
            description="The unique identifier for the run.",
            sql_expression=f"{table}.id",
            sql_join_key="run_id",
        ),
        SqlAttributeInfo(
            name="reference_example_id",
            sql_value=convert_uuid,
        ),
        SqlAttributeInfo(
            name="run_type",
            type="string",
        ),
    ]


RUN_ATTRIBUTES_DIRECT = _get_attributes_direct("runs")
RUN_HISTORY_ATTRIBUTES_DIRECT = _get_attributes_direct("runs_history")


def _get_shared_run_attributes(
    table: Literal["runs"] | Literal["runs_history"],
) -> list[BaseAttributeInfo]:
    return [
        SqlAttributeInfo(
            name="tenant_id",
            sql_value=convert_uuid,
            sql_expression=f"{table}.tenant_id",
            sql_join_key="tenant_id",
            sql_subquery_key="tenant_id",
        ),
        SqlAttributeInfo(
            name="is_root",
            sql_expression=f"{table}.is_root",
            sql_join_key="is_root",
            sql_subquery_key="is_root",
        ),
        SqlAttributeInfo(
            name="session_id",
            sql_value=convert_uuid,
            sql_expression=f"{table}.session_id",
            sql_join_key="session_id",
            sql_subquery_key="session_id",
        ),
        SqlAttributeInfo(
            name="reference_example_id",
            sql_value=convert_uuid,
            sql_join_table="runs_reference_example_id",
            sql_join_method="subquery",
        ),
        SqlAttributeInfo(name="reference_dataset_id", sql_value=convert_uuid),
        SqlAttributeInfo(name="status"),
        SqlAttributeInfo(name="parent_run_id", sql_value=convert_uuid),
        SqlAttributeInfo(name="dotted_order"),
        SqlAttributeInfo(
            name="trace_id",
            sql_value=convert_uuid,
            sql_join_table="runs_trace_id",
            sql_join_method="subquery",
        ),
        SqlAttributeInfo(
            name="run_type",
            type="string",
            description="One of 'tool', 'chain', 'llm', 'retriever', 'embedding', 'prompt', 'parser'",
            sql_expression=f"{table}.run_type",
            support_multi=True,
        ),
        SqlAttributeInfo(
            name="start_time",
            type="iso datetime string",
            description="The start time of the run.",
            sql_expression=f"{table}.start_time",
            sql_value=convert_datetime,
            sql_join_key="start_time",
            sql_subquery_key="start_time",
        ),
        SqlAttributeInfo(
            name="end_time",
            sql_expression=f"{table}.end_time",
            sql_value=convert_datetime,
        ),
        SqlAttributeInfo(
            name="name",
            type="string",
            description="The name of the run.",
            support_multi=True,
        ),
        SqlAttributeInfo(
            name="id",
            type="uuid",
            sql_value=convert_uuid,
            description="The unique identifier for the run.",
            sql_expression=f"{table}.id",
            sql_join_table="runs_run_id_v2",
            sql_join_method="subquery",
            sql_join_key="run_id",
            sql_subquery_key="run_id",
        ),
        SqlAttributeInfo(
            name="latency",
            type="postgres interval",
            description="The latency or duration of the run.",
            sql_expression="date_diff('ms', start_time, end_time, 'UTC')/1000",
            sql_value=convert_interval,
        ),
        SqlAttributeInfo(
            name="feedback_key",
            type="string",
            description="The feedback key of the run (eg. thumbs_up, correctness, etc).",
            sql_expression="feedbacks_rmt.key",
            sql_join_table="feedbacks_rmt",
            sql_value=normalize_feedback_key,
        ),
        SqlAttributeInfo(
            name="feedback_score",
            type="numeric",
            description="The feedback score of the run (eg. 1, 0, 75, etc).",
            sql_expression="feedbacks_rmt.score",
            sql_join_table="feedbacks_rmt",
            sql_value=float,
        ),
        SqlAttributeInfo(
            name="feedback_value",
            type="string",
            description="The feedback value of the run (eg. 'correct', 'incorrect', etc).",
            sql_expression="feedbacks_rmt.value",
            sql_join_table="feedbacks_rmt",
            sql_value=normalize_feedback_value,
        ),
        SqlAttributeInfo(
            name="feedback_source",
            type="string",
            description="The source of the provided feedback (eg. app, api, etc).",
            sql_expression="JSONExtract(feedbacks_rmt.feedback_source, 'type', 'String')",
            sql_join_table="feedbacks_rmt",
        ),
        SqlAttributeInfo(
            name="feedback_error",
            type="boolean",
            description="Indicates whether there was an error in the feedback.",
            sql_expression="JSONExtract(feedbacks_rmt.extra.error, 'type', 'Boolean')",
            sql_join_table="feedbacks_rmt",
        ),
        SqlAttributeInfo(
            name="tag",
            type="string",
            description="The tag of the run.",
            sql_expression="runs_tags.tag",
            sql_join_table="runs_tags",
            use_subquery=True,
        ),
        AltAttributeInfo(name="tags", alt=translate_tags),
        SqlAttributeInfo(
            name="metadata_key",
            type="string",
            description="The metadata key of the run.",
            sql_expression="runs_metadata_kv.key",
            sql_join_table="runs_metadata_kv",
            use_subquery=True,
        ),
        SqlAttributeInfo(
            name="metadata_value",
            type="json primitive",
            description="The metadata value of the run.",
            sql_expression="runs_metadata_kv.value",
            sql_join_table="runs_metadata_kv",
            sql_value=convert_metadata_value,
            use_subquery=True,
            paired_attribute=True,
        ),
        AltAttributeInfo(name="metadata", alt=translate_metadata),
        SqlAttributeInfo(
            name="cursor",
            sql_expression=f"concat(toString({table}.start_time), toString({table}.id))",
        ),
        SqlAttributeInfo(
            name="cursor_inserted_at",
            sql_expression=f"concat(toString({table}.inserted_at), toString({table}.id))",
        ),
        SqlAttributeInfo(
            name="cursor_modified_at",
            sql_expression=f"concat(toString({table}.modified_at), toString({table}.id))",
        ),
        SqlAttributeInfo(
            name="dataset_version",
            sql_expression=f"JSONExtractString({table}.extra, 'metadata', 'dataset_version')",
        ),
    ]


RUN_HISTORY_ATTRIBUTES = _get_shared_run_attributes("runs_history")

"""
Attributes with description/type are available for self-query generator.
All attributes are available for filtering through query lang expression.
NOTE: updates made here should also propagate to app.models.query_lang.ai_query

This is a subset of all attributes available in the query language. If
you have an attribute that you would like to filter on inside of our
application, but do not want to expose to the ai query language 
(ex: whether a trace has hit its TTL expiration), then add to 
RUN_ATTRIBUTES_DO_NOT_AI_QUERY instead.
"""
RUN_ATTRIBUTES_SAFE_AI_QUERY = _get_shared_run_attributes("runs") + [
    AltAttributeInfo(
        name="inputs",
        description="The inputs of the run, lowercased.",
        type="string",
        alt=translate_input_search,
    ),
    AltAttributeInfo(
        name="outputs",
        description="The outputs of the run, lowercased.",
        type="string",
        alt=translate_output_search,
    ),
    AltAttributeInfo(
        name="error",
        description="The error of the run, lowercased.",
        type="string",
        alt=translate_error_search,
    ),
    AltAttributeInfo(name=None, alt=translate_search),
    SqlAttributeInfo(
        name="lower_inputs",
        type="string",
        description="The inputs of the run, lowercased.",
        sql_expression="lower(runs.inputs)",
        sql_value=convert_lower,
    ),
    SqlAttributeInfo(
        name="lower_outputs",
        type="string",
        description="The outputs of the run, lowercased.",
        sql_expression="lower(runs.outputs)",
        sql_value=convert_lower,
    ),
    SqlAttributeInfo(
        name="lower_error",
        type="string",
        description="The errors of the run, lowercased.",
        sql_expression="lower(runs.error)",
        sql_value=convert_lower,
    ),
    SqlAttributeInfo(
        name="input_tokens",
        type="string",
        description="The input tokens of the run.",
    ),
    SqlAttributeInfo(
        name="output_tokens",
        type="string",
        description="The output tokens of the run.",
    ),
    SqlAttributeInfo(
        name="error_tokens",
        type="string",
        description="The error tokens of the run.",
    ),
    SqlAttributeInfo(
        name="input_key",
        type="string",
        description="The input key of the run.",
        sql_expression="runs_inputs_kv.key",
        sql_join_table="runs_inputs_kv",
        use_subquery=True,
    ),
    SqlAttributeInfo(
        name="input_value",
        type="json primitive",
        description="The input value of the run.",
        sql_expression="runs_inputs_kv.value",
        sql_join_table="runs_inputs_kv",
        sql_value=convert_io_value,
        use_subquery=True,
        paired_attribute=True,
    ),
    SqlAttributeInfo(
        name="output_key",
        type="string",
        description="The output key of the run.",
        sql_expression="runs_outputs_kv.key",
        sql_join_table="runs_outputs_kv",
        use_subquery=True,
    ),
    SqlAttributeInfo(
        name="output_value",
        type="json primitive",
        description="The output value of the run.",
        sql_expression="runs_outputs_kv.value",
        sql_join_table="runs_outputs_kv",
        sql_value=convert_io_value,
        use_subquery=True,
        paired_attribute=True,
    ),
    SqlAttributeInfo(
        name="thread_id",
        type="string",
        description="The thread_id of the run.",
    ),
]

RUN_ATTRIBUTES_DO_NOT_AI_QUERY = [
    SqlAttributeInfo(
        name="is_trace_expired",
        type="boolean",
        description="Whether the trace is expired",
        sql_expression="(runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds))",
    ),
]

RUN_ATTRIBUTES = RUN_ATTRIBUTES_SAFE_AI_QUERY + RUN_ATTRIBUTES_DO_NOT_AI_QUERY


JOIN_ATTR_NAMES = {
    attr.name
    for attr in RUN_ATTRIBUTES
    if isinstance(attr, SqlAttributeInfo) and attr.sql_join_key
}
