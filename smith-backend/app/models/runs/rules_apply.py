import asyncio
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from random import random
from typing import Any, List, Optional, Type, cast
from uuid import UUID

import asyncpg
import structlog
from aiochclient import ChClientError
from fastapi import HTTPException
from lc_config.settings import shared_settings
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_conn
from saq import Queue
from typing_extensions import TypedDict

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.api.auth.verify import internal_auth_request
from app.memoize import redis_cache
from app.models.constants import CH_INSERT_TIME
from app.models.query_lang.parse import Comparison, Operation, parse_as_filter_directive
from app.models.query_lang.translate import convert_datetime
from app.models.runs import usage_limits
from app.models.runs.fetch_ch import fetch_runs
from app.models.runs.rule_application import (
    add_to_annotation_queue,
    add_to_dataset,
    alert,
    apply,
    custom_code,
    online_eval,
    usage_limit,
    webhook,
)
from app.models.runs.rules import (
    validate_and_decrypt_rule,
)
from app.models.runs.upgrade import TraceTierUpgradeReason, upgrade_trace_tier
from app.retry import retry_clickhouse

NULL_UUID = UUID("00000000-0000-0000-0000-000000000000")
logger = structlog.getLogger(__name__)

CH_NON_TRANSIENT_ERRORS = {"Code: 467"}


class RunRulesQueryCursors(TypedDict):
    next_run_cursor: Optional[str]
    next_feedback_cursor: Optional[str]


async def _enqueue_one(
    semaphore: asyncio.Semaphore,
    start_time: datetime,
    end_time: datetime,
    rule_dict: dict,
    query_cursors: RunRulesQueryCursors | None,
    queue: Queue,
) -> None:
    scheduled: float = 0
    if shared_settings.RUN_RULES_SPREAD_SEC:
        # spread it out over time period
        delay_seconds = rule_dict["id"].int % shared_settings.RUN_RULES_SPREAD_SEC
        scheduled = (
            datetime.now(timezone.utc) + timedelta(seconds=delay_seconds)
        ).timestamp()

    await queue.enqueue(
        "apply_run_rule",
        start_time=start_time,
        end_time=end_time,
        rule_dict=rule_dict,
        query_cursors=query_cursors,
        scheduled=scheduled,
        retries=0,
        timeout=settings.RUN_RULES_APPLY_TIMEOUT_SEC,
    )
    semaphore.release()


@redis_cache(shared_settings.RUN_RULES_AUTH_CACHE_TTL_SEC)
async def _get_auth_for_tenant_cached(tenant_id: UUID) -> AuthInfo:
    return await internal_auth_request(tenant_id)


@retry_clickhouse
async def _get_latest_run_timestamps(
    cutoff_time: datetime,
) -> dict[tuple[UUID, UUID | None, UUID | None], datetime]:
    if not settings.RUN_RULES_FILTERING_ENABLED:
        return {}

    async with clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW) as ch:
        session_query = """
            SELECT
                tenant_id,
                session_id,
                MAX(max_time) AS max_time
            FROM
            (
                SELECT
                    tenant_id,
                    session_id,
                    MAX(inserted_at) AS max_time
                FROM runs
                WHERE inserted_at >= toDateTime64({start}, 6)
                GROUP BY tenant_id, session_id

                UNION ALL

                SELECT
                    tenant_id,
                    session_id,
                    MAX(modified_at) AS max_time
                FROM feedbacks_rmt
                WHERE modified_at >= toDateTime64({start}, 6)
                GROUP BY tenant_id, session_id
            )
            GROUP BY tenant_id, session_id
            SETTINGS max_memory_usage = {max_memory}
        """

        dataset_query = """
            SELECT
                tenant_id,
                reference_dataset_id,
                MAX(inserted_at) AS max_time
            FROM runs
            WHERE inserted_at >= toDateTime64({start}, 6) AND reference_dataset_id IS NOT NULL
            GROUP BY tenant_id, reference_dataset_id
            SETTINGS max_memory_usage = {max_memory}
        """

        session_rows, dataset_rows = await asyncio.gather(
            ch.fetch(
                "fetch_latest_run_times_for_rules_by_session",
                session_query,
                params={
                    "start": (cutoff_time).strftime(CH_INSERT_TIME),
                    "max_memory": settings.RUN_RULES_FILTERING_MAX_MEMORY,
                },
            ),
            ch.fetch(
                "fetch_latest_run_times_for_rules_by_dataset",
                dataset_query,
                params={
                    "start": (cutoff_time).strftime(CH_INSERT_TIME),
                    "max_memory": settings.RUN_RULES_FILTERING_MAX_MEMORY,
                },
            ),
        )

        results: dict[tuple[UUID, UUID | None, UUID | None], datetime] = {
            (row["tenant_id"], row["session_id"], None): row["max_time"].replace(
                tzinfo=timezone.utc
            )
            for row in session_rows
        }
        results.update(
            {
                (row["tenant_id"], None, row["reference_dataset_id"]): row[
                    "max_time"
                ].replace(tzinfo=timezone.utc)
                for row in dataset_rows
            }
        )
        return results


async def cron_schedule_apply_rules(rule_ids: Optional[List[str]] = None) -> None:
    async with (
        asyncpg_conn() as db,
        db.transaction(),
        redis.async_queue(shared_settings.RUN_RULES_QUEUE) as queue,
    ):
        cursor = db.cursor(
            """
            SELECT
                run_rules.*,
                ra.last_applied_at,
                ra.query_cursors
            FROM run_rules
            INNER JOIN
                tenants ON tenants.id = run_rules.tenant_id
            INNER JOIN
                organizations ON tenants.organization_id = organizations.id
            LEFT JOIN LATERAL (
                SELECT
                    ra.end_time AS last_applied_at,
                    ra.query_cursors
                FROM
                    run_rules_applications ra
                WHERE
                    ra.rule_id = run_rules.id
                    AND ra.run_id = '00000000-0000-0000-0000-000000000000'
                    AND ra.committed = true
                ORDER BY
                    ra.end_time DESC
                LIMIT 1
            ) ra ON TRUE
            WHERE NOT tenants.is_deleted
                AND NOT organizations.disabled
                AND (
                    -- we filter out disabled auto evaluator rules downstream
                    -- because you can opt into disabled ones
                    run_rules.is_enabled
                    OR run_rules.dataset_id IS NOT NULL
                )
                AND (
                    run_rules.add_to_annotation_queue_id IS NOT NULL
                    OR run_rules.add_to_dataset_id IS NOT NULL
                    OR run_rules.evaluators IS NOT NULL
                    OR run_rules.extend_only
                )
                AND (
                    run_rules.id = ANY($1::uuid[])
                    OR $1 is null
                )

            ORDER BY
                run_rules.id;
            """,
            rule_ids,
        )
        tasks: list[asyncio.Task] = []
        semaphore = asyncio.Semaphore(shared_settings.RUN_RULES_SCHEDULER_SEMAPHORE)
        now = datetime.now(timezone.utc)

        run_query_cutoff = datetime.now(timezone.utc) - timedelta(
            minutes=settings.RUN_RULES_FILTERING_TIMERANGE_MINUTES
        )
        latest_run_times = await _get_latest_run_timestamps(run_query_cutoff)

        skipped_rules = []
        async for row in cursor:
            rule = dict(row)
            if rule["last_applied_at"]:
                start_time = rule["last_applied_at"]
            elif rule["backfill_from"]:
                start_time = rule["backfill_from"]
            else:
                start_time = rule["created_at"]

            # use cursor based search with larger limits if backfill or catching up on date
            apply_cursor = rule.get("backfill_from") or (
                datetime.fromisoformat(start_time.isoformat())
                < datetime.now(timezone.utc)
                - timedelta(seconds=settings.RUN_RULES_CURSOR_CATCHUP_SEC)
            )
            end_time = min(
                now,
                start_time
                + timedelta(
                    minutes=shared_settings.RUN_RULES_MAX_MINUTES_TO_PROCESS
                    if not apply_cursor
                    else shared_settings.RUN_RULES_MAX_BACKFILL_MINUTES_TO_PROCESS
                ),
            )
            trigger_rule = True

            # Check if we can skip any rules that don't have fresh runs or feedback
            if (
                settings.RUN_RULES_FILTERING_ENABLED
                and rule.get("last_applied_at")
                and not (
                    rule.get("query_cursors")
                    and (
                        rule["query_cursors"].get("next_run_cursor")
                        or rule["query_cursors"].get("next_feedback_cursor")
                    )
                )
            ):
                last_applied_at_utc = rule["last_applied_at"].replace(
                    tzinfo=timezone.utc
                )
                if last_applied_at_utc > run_query_cutoff:
                    last_run_time = latest_run_times.get(
                        (
                            rule["tenant_id"],
                            rule.get("session_id"),
                            rule.get("dataset_id"),
                        )
                    )
                    if not last_run_time or last_applied_at_utc > last_run_time:
                        logger.info(
                            "Skipping rule due to no new runs",
                            rule_id=rule["id"],
                            tenant_id=rule["tenant_id"],
                            session_id=rule.get("session_id"),
                            dataset_id=rule.get("dataset_id"),
                            last_run_time=last_run_time,
                            last_applied_at=rule["last_applied_at"],
                        )
                        trigger_rule = False
                else:
                    # in this case the rule hasn't run in a while and we don't have info on applicable runs for it, schedule it
                    logger.info(
                        "Rule application last run beyond query cutoff, scheduling run rule",
                        rule_id=rule["id"],
                        tenant_id=rule["tenant_id"],
                        session_id=rule.get("session_id"),
                        dataset_id=rule.get("dataset_id"),
                        last_applied_at=rule["last_applied_at"],
                    )

            if trigger_rule:
                await semaphore.acquire()
                tasks.append(
                    asyncio.create_task(
                        _enqueue_one(
                            semaphore,
                            start_time,
                            end_time,
                            rule,
                            row["query_cursors"],
                            queue,
                        )
                    )
                )
            else:
                # insert empty run_rules_applications to mark rule as processed
                skipped_rules.append(
                    {
                        "id": rule["id"],
                        "start_time": start_time,
                    }
                )
        vals = await asyncio.gather(*tasks, return_exceptions=True)
        for val in vals:
            if isinstance(val, BaseException):
                await logger.aerror("Error scheduling rule", exc_info=val)

        if skipped_rules:
            # insert markers for skipped rules
            updated_cursors = RunRulesQueryCursors(
                next_feedback_cursor=None, next_run_cursor=None
            )
            await db.fetch(
                """
                insert into run_rules_applications (rule_id, run_id, start_time, end_time, query_cursors, committed)
                select * from unnest($1::uuid[], $2::uuid[], $3::timestamptz[], $4::timestamptz[], $5::jsonb[], $6::boolean[])
                on conflict do nothing
                returning run_id::text""",
                [rule["id"] for rule in skipped_rules],
                [NULL_UUID for _ in skipped_rules],
                [rule["start_time"] for rule in skipped_rules],
                [now for _ in skipped_rules],
                [updated_cursors for _ in skipped_rules],
                [True for _ in skipped_rules],
            )


async def apply_run_rule(
    start_time: str,
    end_time: str,
    rule_dict: dict,
    query_cursors: RunRulesQueryCursors | None = None,
) -> None:
    rule = await validate_and_decrypt_rule(rule_dict)
    async with redis.renewable_lock(
        f"apply_run_rule:{rule.id}",
        ttl=settings.RUN_RULES_LOCK_TIMEOUT_SEC,
        renewal_interval=settings.RUN_RULES_LOCK_RENEWAL_SEC,
    ) as lock:
        if not lock:
            await logger.ainfo(
                "Skipping rule due to lock - rule %s for tenant %s from %s to %s",
                rule.id,
                rule.tenant_id,
                start_time,
                end_time,
            )
            return

        await apply_run_rule_without_lock(
            start_time, end_time, rule_dict, query_cursors
        )


async def apply_run_rule_without_lock(
    start_time: str,
    end_time: str,
    rule_dict: dict,
    query_cursors: RunRulesQueryCursors | None = None,
) -> None:
    rule = await validate_and_decrypt_rule(rule_dict)

    window_past_rule_creation_time = (
        rule.transient and datetime.fromisoformat(end_time) > rule.created_at
    )

    await logger.ainfo(
        "Applying rule %s for tenant %s from %s to %s",
        rule.id,
        rule.tenant_id,
        start_time,
        end_time,
        rule_dict={
            k: "..." if v and k in ["code_evaluators", "evaluators", "webhooks"] else v
            for k, v in rule_dict.items()
        },
    )

    try:
        auth = await _get_auth_for_tenant_cached(rule.tenant_id)
    except Exception as e:
        await logger.aerror(
            "Failed to get tenant auth for rule %s",
            rule.id,
            exc_info=e,
            rule_id=str(rule.id),
            tenant_id=str(rule.tenant_id),
            start_time=start_time,
            end_time=end_time,
        )
        return

    # Fail the run rules job if you have hit your trace limit, since we you _may_
    # upgrade traces within the run rules code
    try:
        await usage_limits.check_longlived_usage_limits(auth)
    except HTTPException as e:
        if e.status_code == 429:
            await usage_limit.handle_usage_limit_error(
                usage_limit.LONGLIVED_USAGE_LIMIT_ERROR, rule, start_time, end_time
            )
            return

    # find relevant sessions
    if rule.session_id:
        session = [rule.session_id]
    elif rule.dataset_id:
        async with asyncpg_conn() as db:
            has_evaluators = rule.evaluators or rule.code_evaluators
            if has_evaluators:
                # only filter to sessions if we have evaluators - otherwise they are passed with the filter
                params = [
                    auth.tenant_id,
                    rule.dataset_id,
                    datetime.fromisoformat(end_time),
                    rule.is_enabled,
                    str(rule.id),
                ]
                rows = await db.fetch(
                    """
                    SELECT id
                    FROM tracer_session
                    WHERE
                        tenant_id = $1
                        AND reference_dataset_id = $2
                        AND (
                            start_time AT TIME ZONE 'UTC' + interval '1 day' > $3
                        )
                        AND (
                            CASE
                                WHEN extra->'evaluator_info'->'selected_rules' IS NOT NULL THEN
                                    EXISTS (
                                        SELECT 1
                                        FROM jsonb_array_elements_text(extra->'evaluator_info'->'selected_rules') as selected_rules
                                        WHERE selected_rules = $5
                                    )
                                ELSE
                                    $4 = true
                            END
                        )
                    """
                    + (  # We execute playground evals separately (if FF is on), so we need to filter out experiments that are playground evals here
                        """
                        AND (
                            CASE
                                WHEN extra->'metadata'->'__ls_runner' IS NOT NULL THEN
                                    extra->'metadata'->'__ls_runner' <> '"langsmith_ui"'
                                ELSE
                                    true
                            END
                        )"""
                        if auth.tenant_config.organization_config.enable_run_playground_evals_immediately
                        else ""
                    ),
                    *params,
                )
                session = [UUID(row["id"].hex) for row in rows]
            else:
                # If there are no evaluators set, we should be passing the session within the filters
                session_ids = None
                session = None
                filter_dir = parse_as_filter_directive(rule.filter or "")
                if isinstance(filter_dir, Operation):
                    for arg in filter_dir.arguments:
                        if (
                            isinstance(arg, Comparison)
                            and arg.attribute == "session_id"
                        ):
                            if isinstance(arg.value, UUID):
                                session_ids = [arg.value]
                            elif isinstance(arg.value, list):
                                session_ids = arg.value
                if session_ids:
                    rows = await db.fetch(
                        """
                        SELECT id
                        FROM tracer_session
                        WHERE id = any($1::uuid[])
                        AND tenant_id = $2
                        """,
                        session_ids,
                        auth.tenant_id,
                    )
                    session = [UUID(row["id"].hex) for row in rows]

    updated_cursors = RunRulesQueryCursors(
        next_feedback_cursor=None, next_run_cursor=None
    )

    # fetch matching runs
    if session:
        # Update the time filters to match cursor specific times
        if query_cursors and query_cursors.get("next_run_cursor"):
            query_runs_start_time = _extract_date_from_cursor(
                cast(str, query_cursors["next_run_cursor"])
            )
        else:
            query_runs_start_time = start_time

        if query_cursors and query_cursors.get("next_feedback_cursor"):
            query_feedbacks_start_time = _extract_date_from_cursor(
                cast(str, query_cursors["next_feedback_cursor"])
            )
        else:
            query_feedbacks_start_time = start_time

        # Adjust start times to include a lookback period from the start_time
        query_feedbacks_start_time = convert_datetime(
            datetime.fromisoformat(query_feedbacks_start_time)
            - timedelta(seconds=settings.RUN_RULES_QUERY_LOOKBACK_SEC)
        )
        query_runs_start_time = convert_datetime(
            datetime.fromisoformat(query_runs_start_time)
            - timedelta(seconds=settings.RUN_RULES_QUERY_LOOKBACK_SEC)
        )

        # use cursor based search with larger limits if backfill or catching up on date
        apply_cursor = (
            rule.backfill_from
            or (
                datetime.fromisoformat(start_time)
                < datetime.now(timezone.utc)
                - timedelta(seconds=settings.RUN_RULES_CURSOR_CATCHUP_SEC)
            )
            or (
                query_cursors
                and (
                    query_cursors.get("next_run_cursor")
                    or query_cursors.get("next_feedback_cursor")
                )
            )
        )

        select = (
            [
                schemas.RunSelect.inputs,
                schemas.RunSelect.outputs,
                schemas.RunSelect.extra,
                schemas.RunSelect.reference_example_id,
                schemas.RunSelect.trace_tier,
                schemas.RunSelect.error,
            ]
            if rule.evaluators
            or rule.add_to_dataset_id
            or rule.webhooks
            or rule.code_evaluators
            else [schemas.RunSelect.trace_tier]
        )
        if rule.webhooks:
            select.append(schemas.RunSelect.feedback_stats)
            select.append(schemas.RunSelect.total_tokens)
            select.append(schemas.RunSelect.prompt_tokens)
            select.append(schemas.RunSelect.completion_tokens)
            select.append(schemas.RunSelect.total_cost)
            select.append(schemas.RunSelect.prompt_cost)
            select.append(schemas.RunSelect.completion_cost)

        runs_for_window = None
        runs_w_feedback_for_window = None

        min_start_time = datetime.fromisoformat(query_runs_start_time) - timedelta(
            hours=settings.RUN_RULES_START_TIME_HOUR_DELTA
        )
        max_start_time = datetime.fromisoformat(end_time) + timedelta(
            hours=settings.RUN_RULES_START_TIME_HOUR_DELTA
        )
        clickhouse_client_type = ClickhouseClient.from_string(
            settings.RUN_RULES_TARGET_CLICKHOUSE_CLIENT
        )
        try:
            runs_for_window, runs_w_feedback_for_window = await asyncio.gather(
                fetch_runs(
                    auth,
                    schemas.BodyParamsForRunSchema.model_construct(
                        session=session,
                        filter=rule.filter,
                        trace_filter=rule.trace_filter,
                        tree_filter=rule.tree_filter,
                        select=select,
                        cursor=query_cursors.get("next_run_cursor")
                        if query_cursors
                        else None,
                        limit=shared_settings.RUN_RULES_QUERY_LIMIT
                        if apply_cursor
                        else 0,
                        order=None,
                    ),
                    additional_sql_where=f"""AND inserted_at >= '{convert_datetime(query_runs_start_time)}'
        AND inserted_at <= '{convert_datetime(end_time)}' AND status <> 'pending' AND start_time >= '{convert_datetime(min_start_time)}' AND start_time <= '{convert_datetime(max_start_time)}'""",
                    cursor_field="inserted_at",
                    skip_expensive_enabled=False,
                    include_timeout=True,
                    query_timeout_when_enabled=settings.RUN_RULES_QUERY_TIMEOUT_SEC,
                    clickhouse_client_type=clickhouse_client_type,
                    query_name_postfix="_rules",
                ),
                fetch_runs(
                    auth,
                    schemas.BodyParamsForRunSchema.model_construct(
                        session=session,
                        filter=rule.filter,
                        trace_filter=rule.trace_filter,
                        tree_filter=rule.tree_filter,
                        select=select,
                        cursor=query_cursors.get("next_feedback_cursor")
                        if query_cursors
                        else None,
                        limit=shared_settings.RUN_RULES_QUERY_LIMIT
                        if apply_cursor
                        else 0,
                        order=None,
                    ),
                    additional_sql_where="""AND status <> 'pending' AND (runs.tenant_id, runs.session_id, runs.is_root, runs.start_time, runs.id) IN (
            SELECT tenant_id, session_id, is_root, start_time, id
            FROM runs_trace_id
            WHERE (tenant_id, session_id, trace_id) IN (
                SELECT tenant_id, session_id, trace_id
                FROM feedbacks_rmt """
                    + (" FINAL " if apply_cursor else " ")
                    + f"""
                WHERE tenant_id = '{rule.tenant_id}'
                AND session_id in ({", ".join(f"'{str(s)}'" for s in session)})
                AND modified_at >= '{convert_datetime(query_feedbacks_start_time)}'
                AND modified_at <= '{convert_datetime(end_time)}'
            )
        )""",
                    cursor_field="modified_at",
                    skip_expensive_enabled=False,
                    include_timeout=True,
                    query_timeout_when_enabled=settings.RUN_RULES_QUERY_TIMEOUT_SEC,
                    clickhouse_client_type=clickhouse_client_type,
                    query_name_postfix="_rules",
                ),
            )
        except ChClientError as e:
            if any(error in str(e) for error in CH_NON_TRANSIENT_ERRORS):
                await _write_rule_application_error(
                    rule,
                    "Filter expression is invalid: {}".format(rule.filter),
                    start_time,
                    end_time,
                    updated_cursors,
                )
                return
            else:
                await _write_rule_application_error(
                    rule,
                    "Unknown error has occurred while fetching runs for rule",
                    start_time,
                    end_time,
                    updated_cursors,
                    set_committed=False,
                )
                raise e
        except HTTPException as e:
            if e.status_code == 400:
                logger.error("Error fetching runs for rule", exc_info=e)
                # Record the error in run_rules_applications
                await _write_rule_application_error(
                    rule,
                    e.detail,
                    start_time,
                    end_time,
                    updated_cursors,
                )
                return
            else:
                raise e

        result: list[dict[str, Any]] = []
        ids_seen: set[UUID] = set()
        for each_result in runs_for_window, runs_w_feedback_for_window:
            for run in each_result["runs"]:
                if run["id"] not in ids_seen:
                    ids_seen.add(run["id"])
                    result.append(run)

        # Save cursors for the next run and potentially change the end_time
        # as well to match the cursor end date.
        if apply_cursor:
            original_end_time = end_time
            if runs_for_window["cursors"].get("next"):
                # Update cursors and pull back end time
                updated_cursors["next_run_cursor"] = runs_for_window["cursors"]["next"]
                end_time = (
                    datetime.fromisoformat(
                        _extract_date_from_cursor(runs_for_window["cursors"]["next"])
                    )
                    .replace(tzinfo=timezone.utc)
                    .isoformat()
                )

            if runs_w_feedback_for_window["cursors"].get("next"):
                # Update cursors and pull back end time
                updated_cursors["next_feedback_cursor"] = runs_w_feedback_for_window[
                    "cursors"
                ]["next"]
                end_time = min(
                    datetime.fromisoformat(end_time),
                    datetime.fromisoformat(
                        _extract_date_from_cursor(
                            runs_w_feedback_for_window["cursors"]["next"]
                        )
                    ).replace(tzinfo=timezone.utc),
                ).isoformat()

            if end_time != original_end_time:
                await logger.ainfo(
                    "Query limit reached, reducing end time from {} to {} start_time={} returned records ({},{})".format(
                        original_end_time,
                        end_time,
                        start_time,
                        len(runs_for_window["runs"]),
                        len(runs_w_feedback_for_window["runs"]),
                    )
                )
                if end_time == start_time:
                    # In practicality, this should not be hit given we return a large set of records.
                    # However, in this case, we can slightly bump end time, which is used for rule log display.
                    # The query would still use the cursors for pagination so records wouldn't be missed.
                    await logger.awarn("End time reached start time, updated time.")
                    end_time = (
                        datetime.fromisoformat(end_time) + timedelta(microseconds=1)
                    ).isoformat()

                if start_time > end_time:
                    # Handle situation where cursor and times are offset
                    await logger.awarn("Start time larger than end time, updated time.")
                    end_time = (
                        datetime.fromisoformat(start_time) + timedelta(microseconds=1)
                    ).isoformat()
    else:
        result = []

    await logger.ainfo(
        "Found %s matching runs",
        len(result),
        num_found=len(result),
        rule_id=str(rule.id),
    )
    # sample runs according to the rule's sampling rate
    sampled = [run for run in result if random() < rule.sampling_rate]
    await logger.ainfo(
        "Sampled %s runs",
        len(sampled),
        rule_id=str(rule.id),
        num_sampled=len(sampled),
        run_ids=[str(run["id"]) for run in sampled][:200],
    )
    start_time_dt = datetime.fromisoformat(start_time)
    end_time_dt = datetime.fromisoformat(end_time)

    appliers: list[Type[apply.RuleApplier]] = []

    if rule.add_to_annotation_queue_id:
        appliers.append(add_to_annotation_queue.AddToAnnotationQueueRuleApplier)

    # send to dataset
    if rule.add_to_dataset_id:
        appliers.append(add_to_dataset.AddToDatasetRuleApplier)

    # send webhooks
    if rule.webhooks:
        appliers.append(webhook.WebhookRuleApplier)

    # apply evaluators
    if rule.evaluators:
        appliers.append(online_eval.OnlineEvaluatorRuleApplier)

    # apply code evaluators
    if rule.code_evaluators:
        appliers.append(custom_code.CustomCodeRuleApplier)

    if rule.extend_only:
        logger.info(
            "Extending traces for rule",
            rule_id=rule.id,
            run_ids=[str(run["id"]) for run in sampled],
        )

    # send alerts
    if rule.alerts:
        appliers.append(alert.AlertRuleApplier)

    for applier in appliers:
        try:
            sampled = await applier.filter_samples(
                rule, auth, sampled, start_time, end_time
            )
        except apply.RuleApplicationExpectedError as e:
            await logger.awarn(
                "Rule application failed during pre-filtering",
                exc_info=e,
                rule_id=str(rule.id),
                tenant_id=str(rule.tenant_id),
                start_time=start_time,
                end_time=end_time,
            )
            return

    should_delete_rule = (
        window_past_rule_creation_time
        and not updated_cursors.get("next_run_cursor")
        and not updated_cursors.get("next_feedback_cursor")
    )

    async with asyncpg_conn() as db, db.transaction():
        # No need to save logs if we are about to delete the rule
        if not should_delete_rule:
            # Fetch any existing NULL_UUID rule application to check for retries
            retries = await db.fetchval(
                """
                UPDATE run_rules_applications
                SET retries = retries + 1
                WHERE rule_id = $1 AND run_id = $2 AND start_time = $3
                RETURNING retries
                """,
                rule.id,
                NULL_UUID,
                start_time_dt,
            )
            # Check if there are retries and log them
            if retries is not None:
                await logger.awarn(
                    "Rule application being retried",
                    rule_id=str(rule.id),
                    tenant_id=str(rule.tenant_id),
                    start_time=start_time,
                    end_time=end_time,
                    retries=retries,
                )
                if retries >= settings.RUN_RULES_MAX_RETRIES:
                    await logger.awarn(
                        "Rule application retries limit reached",
                        rule_id=str(rule.id),
                        tenant_id=str(rule.tenant_id),
                    )
                    await _write_rule_application_error(
                        rule,
                        f"Rule application retries limit reached: {retries}",
                        start_time,
                        end_time,
                        updated_cursors,
                        is_update=True,
                        existing_txn_opt=db,
                    )
                    return

            # Clean up any uncommitted rules, which could have happened if a task was killed
            # This is needed for deduplication below otherwise tasks will not get rerun on failure
            deleted = await db.fetchval(
                """
                with deleted as (
                    DELETE FROM run_rules_applications
                    where rule_id = $1 AND NOT committed AND run_id != '00000000000000000000000000000000'
                    returning *
                )
                select count(*) from deleted
                """,
                rule.id,
            )
            if deleted:
                logger.warning(
                    "Deleted uncommitted runs",
                    delete_count=deleted,
                    rule_id=str(rule.id),
                    start_time=start_time,
                    end_time=end_time,
                    run_ids=[str(run["id"]) for run in sampled],
                )

            inserted = await db.fetch(
                """
                insert into run_rules_applications (rule_id, run_id, start_time, end_time, query_cursors, committed, retries)
                select * from unnest($1::uuid[], $2::uuid[], $3::timestamptz[], $4::timestamptz[], $5::jsonb[], $6::boolean[], $7::integer[])
                on conflict do nothing
                returning run_id::text""",
                [rule.id for _ in sampled] + [rule.id],
                [run["id"] for run in sampled] + [NULL_UUID],
                [start_time_dt for _ in sampled] + [start_time_dt],
                [end_time_dt for _ in sampled] + [end_time_dt],
                [updated_cursors for _ in sampled] + [updated_cursors],
                [False for _ in sampled] + [False],
                [0 for _ in sampled] + [0],
            )

            # dedupe previously actioned runs
            sampled = [
                run
                for run in sampled
                if str(run["id"]) in [row["run_id"] for row in inserted]
            ]
            await logger.ainfo(
                "After deduping, %s runs remain",
                len(sampled),
                rule_id=str(rule.id),
                run_ids=[str(run["id"]) for run in sampled][:200],
            )
            # exit early if empty
            if not sampled:
                await db.execute(
                    """
                                update run_rules_applications
                                set committed = TRUE
                                where rule_id = $1 AND run_id = $2 AND start_time = $3
                                """,
                    rule.id,
                    NULL_UUID,
                    start_time_dt,
                )
                return

    updated_val: dict[str, dict[str, schemas.RuleLogActionResponse]] = defaultdict(dict)

    for applier in appliers:
        try:
            response = await applier.apply(
                rule,
                auth,
                sampled,
                start_time,
                end_time,
            )
            for id, action in response.items():
                updated_val[id] |= action
        except apply.RuleApplicationExpectedError as e:
            await logger.awarn(
                "Rule application failed",
                exc_info=e,
                rule_id=str(rule.id),
                tenant_id=str(rule.tenant_id),
                start_time=start_time,
                end_time=end_time,
            )
            return

    outcomes = [
        [updated_val[str(run["id"])].get(key, None) for run in sampled]
        + [updated_val[str(NULL_UUID)].get(key, None)]
        for key in usage_limit.RULE_TYPE_COLS
    ]
    serializable_outcomes = [
        [outcome.model_dump() if outcome else None for outcome in outcomes_for_type]
        for outcomes_for_type in outcomes
    ]

    # update run_rules_applications
    async with asyncpg_conn() as db:
        await db.fetch(
            """
            update run_rules_applications
            set
                add_to_annotation_queue = query.add_to_annotation_queue,
                add_to_dataset = query.add_to_dataset,
                evaluators = query.evaluators,
                alerts = query.alerts,
                webhooks = query.webhooks,
                committed = true
            from (
                select *
                from unnest(
                    $1::uuid[], $2::uuid[], $3::timestamptz[],
                    $4::jsonb[], $5::jsonb[], $6::jsonb[], $7::jsonb[], $8::jsonb[]
                )
                as t(
                    rule_id, run_id, start_time,
                    add_to_annotation_queue, add_to_dataset, evaluators, alerts, webhooks
                )
            ) AS query
            where
                run_rules_applications.rule_id = query.rule_id AND
                run_rules_applications.run_id = query.run_id AND
                run_rules_applications.start_time = query.start_time""",
            [rule.id for _ in sampled] + [rule.id],
            [run["id"] for run in sampled] + [NULL_UUID],
            [start_time_dt for _ in sampled] + [start_time_dt],
            *serializable_outcomes,
        )

    if shared_settings.FF_TRACE_TIERS_ENABLED:
        # Group sampled runs by session_id
        runs_by_session = defaultdict(list)
        for run in sampled:
            runs_by_session[run["session_id"]].append(run)

        # Call upgrade_trace_tier for each session and list of runs
        for session_id, runs in runs_by_session.items():
            trace_ids = list(
                set(
                    [
                        run["trace_id"]
                        for run in runs
                        if run.get("trace_tier") != schemas.TraceTier.longlived.value
                    ]
                )
            )
            if trace_ids:
                await upgrade_trace_tier(
                    auth,
                    UUID(session_id.hex),
                    trace_ids,
                    reason=TraceTierUpgradeReason.run_rule,
                )

    if should_delete_rule:
        async with asyncpg_conn() as db:
            # Delete transient rules after they have been fully processed
            await db.fetch(
                """
                DELETE FROM run_rules
                WHERE id = $1
                """,
                rule.id,
            )


def _extract_date_from_cursor(cursor: str) -> str:
    return cursor.split("'")[1][:26]


async def _write_rule_application_error(
    rule: schemas.RunRulesSchema,
    error_payload: str,
    start_time: str,
    end_time: str,
    updated_cursors: RunRulesQueryCursors | None = None,
    is_update: bool = False,
    existing_txn_opt: Optional[asyncpg.Connection] = None,
    set_committed: bool = True,
) -> None:
    # Determine which columns should receive the error message
    cols_for_rule = usage_limit._get_cols_for_rule(rule)
    cols_to_insert_list = [
        "rule_id",
        "run_id",
        "start_time",
        "end_time",
        "query_cursors",
        "committed",
    ] + cols_for_rule

    error_response = schemas.RuleLogActionResponse(
        outcome=schemas.RuleLogActionOutcome.error,
        payload={"error": error_payload},
    )

    values_to_insert = [
        rule.id,
        NULL_UUID,
        datetime.fromisoformat(start_time),
        datetime.fromisoformat(end_time),
        updated_cursors,
        set_committed,
    ] + [error_response.model_dump() for _ in cols_for_rule]
    insert_replacement_keys = ", ".join(
        f"${i}" for i in range(1, len(values_to_insert) + 1)
    )
    insert_cols = ", ".join(cols_to_insert_list)

    async with usage_limit._maybe_in_txn(existing_txn_opt) as db:
        if is_update:
            await db.execute(
                f"""
                UPDATE run_rules_applications
                SET {", ".join(f"{col} = ${i + 4}" for i, col in enumerate(cols_for_rule))}, query_cursors = ${len(cols_for_rule) + 4}, committed = ${len(cols_for_rule) + 5}
                WHERE rule_id = $1 AND run_id = $2 AND start_time = $3
                """,
                rule.id,
                NULL_UUID,
                datetime.fromisoformat(start_time),
                *[error_response.model_dump() for _ in cols_for_rule],
                updated_cursors,
                set_committed,
            )
        else:
            await db.execute(
                f"""
                INSERT INTO run_rules_applications ({insert_cols})
                VALUES ({insert_replacement_keys})
                ON CONFLICT (rule_id, run_id, start_time)"""
                + (
                    f"""
                DO UPDATE SET {", ".join(f"{col} = excluded.{col}" for col in cols_for_rule)}, query_cursors = excluded.query_cursors"""
                    if cols_for_rule
                    else "DO NOTHING"
                ),
                *values_to_insert,
            )
