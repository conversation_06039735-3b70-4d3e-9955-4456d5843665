import asyncio
import itertools
import operator
import string
from pathlib import Path
from typing import Callable, Sequence, TypeVar

import orjson
from fastapi import HTTPException
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_pool

from app import schemas
from app.api.auth import AuthInfo
from app.models.runs.monitor_blocks.row_predicates import (
    llm_per_trace,
    status_rates,
    streaming_rate,
)

T = TypeVar("T")


def partition(pred: Callable[[T], bool], seq: Sequence[T]) -> tuple[list[T], list[T]]:
    """Partition entries into true entries and false entries.

    partition(is_even, range(10)) --> 0 2 4 6 8  and  1 3 5 7 9
    """
    return list(filter(pred, seq)), list(itertools.filterfalse(pred, seq))


def get_timedelta_name(interval: schemas.TimedeltaInput) -> str:
    name = ""
    if interval.days > 0:
        name += f"{interval.days}d"
    if interval.hours > 0:
        name += f"{interval.hours}h"
    if interval.minutes > 0:
        name += f"{interval.minutes}min"
    return name


def get_timedelta_unit(interval: schemas.TimedeltaInput) -> str:
    unit = ""
    count = 0
    if interval.days > 0:
        count += 1
        unit = "day"
    if interval.hours > 0:
        count += 1
        unit = "hour"
    if interval.minutes > 0:
        count += 1
        unit = "minute"
    assert count == 1, "interval must be a single unit"
    return unit


def get_timedelta_value(interval: schemas.TimedeltaInput) -> int:
    value = 0
    count = 0
    if interval.days > 0:
        count += 1
        value = interval.days
    if interval.hours > 0:
        count += 1
        value = interval.hours
    if interval.minutes > 0:
        count += 1
        value = interval.minutes
    assert count == 1, "interval must be a single unit"
    return value


def get_template_vars(params: schemas.MonitorRequest) -> dict[str, str]:
    is_grouped_by_metadata = any(group.metadata is not None for group in params.groups)
    is_grouped_by_tag = any(group.tag is not None for group in params.groups)
    return schemas.MonitorBlockTemplateVars(
        interval=get_timedelta_name(params.interval),
        stride=get_timedelta_name(params.stride),
        group_name="Metadata"
        if is_grouped_by_metadata
        else "Tag"
        if is_grouped_by_tag
        else "Project",
    ).model_dump()


class ChartTemplate(string.Template):
    delimiter = "$$"


def chart_from_file(path: str) -> ChartTemplate:
    with open(Path(__file__).parent / "monitor_blocks" / path) as f:
        return ChartTemplate(f.read())


def sql_from_file(path: str) -> str:
    with open(Path(__file__).parent / "monitor_blocks" / path) as f:
        return f.read()


CH_QUERIES_DICT: dict[str, str] = {
    "fetch_monitor_trace": sql_from_file("ch_trace.sql"),
    "fetch_monitor_llm": sql_from_file("ch_llm.sql"),
    "fetch_monitor_feedback": sql_from_file("ch_feedback.sql"),
}


def get_ch_queries(auth: AuthInfo) -> list[tuple[str, str]]:
    return [
        ("fetch_monitor_trace", CH_QUERIES_DICT["fetch_monitor_trace"]),
        ("fetch_monitor_llm", CH_QUERIES_DICT["fetch_monitor_llm"]),
        ("fetch_monitor_feedback", CH_QUERIES_DICT["fetch_monitor_feedback"]),
    ]


# It is expected that all charts in the same section have the same marks (e.g. Success, Pending, Error)
BLOCKS: list[schemas.MonitorBlockSpec] = [
    schemas.MonitorBlockSpec(
        section="Volume",
        title="Trace Count",
        columns=["group", "ts", "count_success", "count_pending", "count_error"],
        chart_spec=chart_from_file("chart_trace_count.json"),
        chart_spec_grouped=chart_from_file("chart_trace_count_grouped.json"),
        click_target="trace",
        query_idx=0,
        toggleable_marks={
            "Success": [0],
            "Pending": [1],
            "Error": [2],
            "Interrupted": [3],
        },
        toggleable_marks_grouped={
            "Success": [0, 1],
            "Pending": [2, 3],
            "Error": [4, 5],
            "Interrupted": [6, 7],
        },
    ),
    schemas.MonitorBlockSpec(
        section="Volume",
        title="LLM Call Count",
        columns=["group", "ts", "count_success", "count_pending", "count_error"],
        chart_spec=chart_from_file("chart_llm_count.json"),
        chart_spec_grouped=chart_from_file("chart_llm_count_grouped.json"),
        click_target="llm",
        query_idx=1,
        toggleable_marks={
            "Success": [0],
            "Pending": [1],
            "Error": [2],
            "Interrupted": [3],
        },
        toggleable_marks_grouped={
            "Success": [0, 1],
            "Pending": [2, 3],
            "Error": [4, 5],
            "Interrupted": [6, 7],
        },
    ),
    schemas.MonitorDerivedBlockSpec(
        section="Volume",
        title="Trace Success Rates",
        columns=["group", "ts", "rate_success", "rate_pending", "rate_error"],
        query_idx=[0],
        row_predicate=status_rates,
        chart_spec=chart_from_file("chart_trace_rates.json"),
        chart_spec_grouped=chart_from_file("chart_trace_rates_grouped.json"),
        click_target="trace",
        toggleable_marks={
            "Success": [0],
            "Pending": [1],
            "Error": [2],
            "Interrupted": [3],
        },
        toggleable_marks_grouped={
            "Success": [0, 1],
            "Pending": [2, 3],
            "Error": [4, 5],
            "Interrupted": [6, 7],
        },
    ),
    schemas.MonitorDerivedBlockSpec(
        section="Volume",
        title="LLM Call Success Rates",
        columns=["group", "ts", "rate_success", "rate_pending", "rate_error"],
        query_idx=[1],
        row_predicate=status_rates,
        chart_spec=chart_from_file("chart_llm_rates.json"),
        chart_spec_grouped=chart_from_file("chart_llm_rates_grouped.json"),
        click_target="llm",
        toggleable_marks={
            "Success": [0],
            "Pending": [1],
            "Error": [2],
            "Interrupted": [3],
        },
        toggleable_marks_grouped={
            "Success": [0, 1],
            "Pending": [2, 3],
            "Error": [4, 5],
            "Interrupted": [6, 7],
        },
    ),
    schemas.MonitorBlockSpec(
        section="Latency",
        title="Trace Latency (s)",
        columns=["group", "ts", "p50_latency", "p95_latency"],
        chart_spec=chart_from_file("chart_trace_latency.json"),
        chart_spec_grouped=chart_from_file("chart_trace_latency_grouped.json"),
        query_idx=0,
        click_target="trace",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorBlockSpec(
        section="Latency",
        title="LLM Latency (s)",
        columns=["group", "ts", "p50_latency", "p95_latency"],
        chart_spec=chart_from_file("chart_llm_latency.json"),
        chart_spec_grouped=chart_from_file("chart_llm_latency_grouped.json"),
        query_idx=1,
        click_target="llm",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorDerivedBlockSpec(
        section="Latency",
        title="LLM Calls per Trace",
        columns=["group", "ts", "count_success"],
        query_idx=[0, 1],
        row_predicate=llm_per_trace,
        chart_spec=chart_from_file("chart_llm_per_trace.json"),
        chart_spec_grouped=chart_from_file("chart_llm_per_trace_grouped.json"),
        click_target="llm",
    ),
    schemas.MonitorBlockSpec(
        section="Latency",
        title="Tokens / sec",
        columns=["group", "ts", "p50_tokens_per_sec", "p95_tokens_per_sec"],
        chart_spec=chart_from_file("chart_tokens_per_sec.json"),
        chart_spec_grouped=chart_from_file("chart_tokens_per_sec_grouped.json"),
        query_idx=1,
        click_target="trace",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorBlockSpec(
        section="Feedback",
        title="Avg. Score",
        columns=["group", "ts", "feedback_key", "count", "avg_score"],
        split_by_column_idx=2,
        chart_spec=chart_from_file("chart_trace_feedback.json"),
        chart_spec_grouped=chart_from_file("chart_trace_feedback_grouped.json"),
        click_target="feedback",
        query_idx=2,
    ),
    schemas.MonitorBlockSpec(
        section="Tokens",
        title="Total Tokens",
        columns=["group", "ts", "total_tokens"],
        chart_spec=chart_from_file("chart_trace_tokens.json"),
        chart_spec_grouped=chart_from_file("chart_trace_tokens_grouped.json"),
        query_idx=0,
        click_target="trace",
    ),
    schemas.MonitorBlockSpec(
        section="Tokens",
        title="Tokens per Trace",
        columns=["group", "ts", "p50_tokens", "p95_tokens"],
        chart_spec=chart_from_file("chart_tokens_per_trace.json"),
        chart_spec_grouped=chart_from_file("chart_tokens_per_trace_grouped.json"),
        query_idx=0,
        click_target="trace",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorBlockSpec(
        section="Tokens",
        title="Tokens per LLM Call",
        columns=["group", "ts", "p50_tokens", "p95_tokens"],
        chart_spec=chart_from_file("chart_tokens_per_llm.json"),
        chart_spec_grouped=chart_from_file("chart_tokens_per_llm_grouped.json"),
        query_idx=1,
        click_target="llm",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorBlockSpec(
        section="Cost",
        title="Total Cost",
        columns=["group", "ts", "total_cost"],
        chart_spec=chart_from_file("chart_trace_cost.json"),
        chart_spec_grouped=chart_from_file("chart_trace_cost_grouped.json"),
        query_idx=0,
        click_target="trace",
    ),
    schemas.MonitorBlockSpec(
        section="Cost",
        title="Cost per Trace",
        columns=["group", "ts", "p50_cost", "p95_cost"],
        chart_spec=chart_from_file("chart_cost_per_trace.json"),
        chart_spec_grouped=chart_from_file("chart_cost_per_trace_grouped.json"),
        query_idx=0,
        click_target="trace",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorBlockSpec(
        section="Cost",
        title="Cost per LLM Call",
        columns=["group", "ts", "p50_cost", "p95_cost"],
        chart_spec=chart_from_file("chart_cost_per_llm.json"),
        chart_spec_grouped=chart_from_file("chart_cost_per_llm_grouped.json"),
        query_idx=1,
        click_target="llm",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorDerivedBlockSpec(
        section="Streaming",
        title="% Traces w/ Streaming",
        columns=["group", "ts", "rate_streaming"],
        chart_spec=chart_from_file("chart_trace_streaming.json"),
        chart_spec_grouped=chart_from_file("chart_trace_streaming_grouped.json"),
        row_predicate=streaming_rate,
        query_idx=[0],
        click_target="trace",
    ),
    schemas.MonitorDerivedBlockSpec(
        section="Streaming",
        title="% LLM Calls w/ Streaming",
        columns=["group", "ts", "rate_streaming"],
        chart_spec=chart_from_file("chart_llm_streaming.json"),
        chart_spec_grouped=chart_from_file("chart_llm_streaming_grouped.json"),
        row_predicate=streaming_rate,
        query_idx=[1],
        click_target="llm",
    ),
    schemas.MonitorBlockSpec(
        section="Streaming",
        title="Trace Time-to-First-Token (ms)",
        columns=["group", "ts", "p50_ttft_ms", "p95_ttft_ms"],
        chart_spec=chart_from_file("chart_trace_ttft.json"),
        chart_spec_grouped=chart_from_file("chart_trace_ttft_grouped.json"),
        query_idx=0,
        click_target="trace",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
    schemas.MonitorBlockSpec(
        section="Streaming",
        title="LLM Time-to-First-Token (ms)",
        columns=["group", "ts", "p50_ttft_ms", "p95_ttft_ms"],
        chart_spec=chart_from_file("chart_llm_ttft.json"),
        chart_spec_grouped=chart_from_file("chart_llm_ttft_grouped.json"),
        query_idx=1,
        click_target="llm",
        toggleable_marks={"P50": [0], "P95": [1]},
        toggleable_marks_grouped={"P50": [0, 1], "P95": [2, 3]},
    ),
]


def group_key(group: schemas.MonitorGroupSpec) -> str:
    if group.tag is not None:
        return "{tag}"
    elif group.metadata is not None:
        return "{metadata_key} || '=' || {metadata_value}"
    else:
        return "{tracer_session_id}"


async def get_query_ch(
    auth: AuthInfo,
    group_names_map: dict[str, str],
    params: schemas.MonitorRequest,
    query_name: str,
    query_str: str,
) -> list[dict]:
    join_clause = """
join (
    select * from {runs_table}
    where
    tenant_id = {{tenant_id}}
    and session_id = {{tracer_session_id}}
    {has_tag_or_metadata_condition}
    and is_root = true
    and start_time > timestamp_sub({interval_unit}, {{interval_value}}, now())
) as {runs_table} on feedbacks_rmt.run_id = {runs_table}.id
"""
    interval_unit = get_timedelta_unit(params.interval)

    runs_table = "runs_history"
    query_name += "_runs_history"

    metadata_subquery_condition = """
    and id in (
        select run_id as id
        from runs_metadata_kv
        where tenant_id = {{tenant_id}}
        and session_id = {{tracer_session_id}}
        and start_time > timestamp_sub({interval_unit}, {{interval_value}}, now())
        and runs_metadata_kv.key = {{metadata_key}}
        and runs_metadata_kv.value = {{metadata_value}}
    )
    """

    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        rows_per_group = await asyncio.gather(
            *(
                ch.fetch(
                    query_name,
                    query_str.format(
                        interval_unit=interval_unit,
                        stride_unit=get_timedelta_unit(params.stride),
                        group_key=group_key(group),
                        join_clause=join_clause.format(
                            interval_unit=interval_unit,
                            has_tag_or_metadata_condition="and has(tags, {tag})"
                            if group.tag
                            else metadata_subquery_condition.format(
                                interval_unit=interval_unit,
                            )
                            if group.metadata
                            else "",
                            runs_table=runs_table,
                        )
                        if group.tag or group.metadata
                        else "",
                        has_tag_or_metadata_condition="and has(tags, {tag})"
                        if group.tag
                        else metadata_subquery_condition.format(
                            interval_unit=interval_unit,
                        )
                        if group.metadata
                        else "",
                        runs_table=runs_table,
                    ),
                    params={
                        "tracer_session_id": group.session,
                        "tag": group.tag,
                        "metadata_key": group.metadata.key if group.metadata else "",
                        "metadata_value": group.metadata.value
                        if group.metadata
                        else "",
                        "tenant_id": auth.tenant_id,
                        "interval_value": get_timedelta_value(params.interval),
                        "stride_value": get_timedelta_value(params.stride),
                        "timezone": params.timezone,
                    },
                    with_timeout=True,
                )
                for group in params.groups
            )
        )

    result = []
    for rows in rows_per_group:
        group_id = next(
            (row["group_id"] for row in rows if row["group_id"] != ""), None
        )
        if group_id is not None:
            for row in rows:
                result.append(
                    {
                        **row,
                        "group": group_names_map[group_id],
                    }
                )

    return result


def get_blocks(
    queries: list[list[dict]],
    template_vars: dict[str, str],
    block_spec: schemas.MonitorBlockSpec | schemas.MonitorDerivedBlockSpec,
    multiple_groups: bool,
) -> list[schemas.MonitorBlock]:
    if isinstance(block_spec, schemas.MonitorBlockSpec):
        rows = [
            [row[column] for column in block_spec.columns]
            for row in queries[block_spec.query_idx]
        ]
    elif isinstance(block_spec, schemas.MonitorDerivedBlockSpec):
        args = [queries[idx] for idx in block_spec.query_idx]
        unique_groups = set(row["group"] for rows in args for row in rows)
        rows = []
        for group in unique_groups:
            unique_ts = set(
                row["ts"] for rows in args for row in rows if row["group"] == group
            )
            args_by_ts = [
                {row["ts"]: row for row in rows if row["group"] == group}
                for rows in args
            ]
            group_rows = [
                block_spec.row_predicate(*[rows.get(ts, {}) for rows in args_by_ts])
                for ts in unique_ts
            ]
            rows.extend(group_rows)

    rows = [row for row in rows if any(val is not None for val in row[2:])]
    if multiple_groups and block_spec.chart_spec_grouped is not None:
        chart_spec = block_spec.chart_spec_grouped
        toggleable_marks = block_spec.toggleable_marks_grouped
    else:
        chart_spec = block_spec.chart_spec
        toggleable_marks = block_spec.toggleable_marks
    chart_spec = orjson.loads(chart_spec.substitute(template_vars))

    if block_spec.split_by_column_idx is not None:
        split_idx: int = block_spec.split_by_column_idx
        null_rows, defined_rows = partition(lambda row: row[split_idx] is None, rows)

        if not defined_rows:
            return []

        key = operator.itemgetter(split_idx)

        return [
            schemas.MonitorBlock(
                section=block_spec.section,
                title=block_spec.title,
                subtitle=group_title,
                chart_spec=chart_spec,
                click_target=block_spec.click_target,
                columns=block_spec.columns,
                rows=null_rows + list(group_rows),
                toggleable_marks=toggleable_marks,
            )
            for group_title, group_rows in itertools.groupby(
                sorted(defined_rows, key=key), key=key
            )
        ]
    else:
        return [
            schemas.MonitorBlock(
                section=block_spec.section,
                title=block_spec.title,
                chart_spec=chart_spec,
                click_target=block_spec.click_target,
                columns=block_spec.columns,
                rows=rows,
                toggleable_marks=toggleable_marks,
            )
        ]


async def get_monitor_blocks(
    auth: AuthInfo, params: schemas.MonitorRequest
) -> schemas.MonitorResponse:
    session_ids = set(group.session for group in params.groups)
    async with asyncpg_pool() as pool:
        session_names = await pool.fetch(
            "select id, name from tracer_session where id = any($1::uuid[]) and tenant_id = $2",
            session_ids,
            auth.tenant_id,
        )
        if len(session_names) != len(session_ids):
            raise HTTPException(status_code=404, detail="Tracer session not found")
        session_names_map = {
            group_name["id"]: group_name["name"] for group_name in session_names
        }
        group_names_map = {}
        for group in params.groups:
            if group.tag:
                group_names_map[f"{group.tag}"] = f"{group.tag}"
            elif group.metadata:
                group_names_map[f"{group.metadata.key}={group.metadata.value}"] = (
                    f"{group.metadata.key} = {group.metadata.value}"
                )
            else:
                group_names_map[str(group.session)] = session_names_map[group.session]
        template_vars = get_template_vars(params)
        queries = await asyncio.gather(
            *(
                get_query_ch(auth, group_names_map, params, query_name, query_str)
                for (query_name, query_str) in get_ch_queries(auth)
            )
        )
        blocks = [
            block
            for spec in BLOCKS
            for block in get_blocks(
                queries, template_vars, spec, len(params.groups) > 1
            )
        ]
        return schemas.MonitorResponse(blocks=blocks)
