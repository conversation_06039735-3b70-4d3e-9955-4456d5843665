import asyncio
import datetime
import logging
import re
from typing import Any, Dict, List, Literal
from uuid import UUID, uuid4

import asyncpg
from aiohttp import ClientSession
from asyncpg import ForeignKeyViolationError
from fastapi import HTTPException
from httpx import HTTPStatusError
from langchain_core.load import dumpd, load
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.prompts.structured import StructuredPrompt
from langchain_core.runnables import RunnableConfig
from langchain_core.runnables.utils import Input
from lc_config.service_communication_settings import ServiceName
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.custom_code import custom_code_client
from lc_database.database import asyncpg_conn, asyncpg_pool
from lc_database.service_client import (
    get_internal_tenant_scoped_service_client,
    get_service_client,
)

from app import crud, schemas
from app.api.auth.schemas import AuthInfo
from app.hub.crud.commits import get_commit_manifest
from app.hub.utils import parse_owner_repo_commit
from app.models.custom_code.constants import CUSTOM_CODE_EXECUTION_FUNC_NAME
from app.models.feedback.fetch import fetch_feedback
from app.models.query_lang.parse import parse_as_filter_directive
from app.models.query_lang.translate import convert_datetime
from app.models.runs.constants import NEW_RULE_EVALUATOR_VERSION
from app.models.runs.fetch_ch import fetch_runs
from app.models.runs.utils import verify_rule_valid_for_corrections_and_get_feedback_key
from app.models.tenants.secrets import list_secrets_as_dict
from app.models.tracer_sessions.create import (
    create_tracer_session_within_existing_transaction,
)
from app.models.utils.encryption import (
    decrypt_dict_values,
    decrypt_string,
    encrypt_dict_values,
    encrypt_string,
)
from app.retry import retry_asyncpg
from app.utils import base_exception_mapper, gated_coro

logger = logging.getLogger(__name__)

EXAMPLES_FEW_SHOT_KEY = "examples_few_shot"


async def decrypt_webhooks(webhooks: list[schemas.RunRulesWebhookSchema]):
    for webhook in webhooks:
        if webhook.headers:
            webhook.headers = await decrypt_dict_values(webhook.headers)

        if webhook.url and not webhook.url.startswith("http"):
            decrypted_url = await decrypt_string(webhook.url)
            if decrypted_url is not None:
                webhook.url = decrypted_url


async def encrypt_webhooks(webhooks: list[schemas.RunRulesWebhookSchema]):
    for webhook in webhooks:
        if webhook.headers:
            webhook.headers = await encrypt_dict_values(webhook.headers)

        if webhook.url:
            encrypted_url = await encrypt_string(webhook.url)
            if encrypted_url is not None:
                webhook.url = encrypted_url


async def validate_and_decrypt_rule(rule_dict: dict) -> schemas.RunRulesSchema:
    rule = schemas.RunRulesSchema.model_validate(rule_dict)
    if rule.webhooks:
        await decrypt_webhooks(rule.webhooks)
    return rule


def verify_python_function_with_name_and_args(code_string, function_name, num_args):
    pattern = re.compile(rf"def {function_name}\((.*?)\)(\s*->\s*[\w\[\],\s]*)?:")

    match = pattern.search(code_string)
    if match:
        args = match.group(1).split(",")
        if len(args) != num_args:
            raise HTTPException(
                status_code=400,
                detail=f"Function should take exactly {num_args} positional arguments.",
            )
        return True
    raise HTTPException(
        status_code=400,
        detail=f"Code evaluator does not contain function with name {function_name}",
    )


async def validate_code_evaluators(
    auth: AuthInfo,
    code_evaluators: list[schemas.CodeEvaluatorTopLevel],
    has_dataset: bool = False,
) -> None:
    async with custom_code_client() as client:
        for code_eval in code_evaluators:
            response = await client.post(
                "/validate",
                json={
                    "code": code_eval.code,
                    "function": CUSTOM_CODE_EXECUTION_FUNC_NAME,
                },
            )
            try:
                response.raise_for_status()
            except HTTPStatusError as e:
                stack_trace = e.response.json().get("stacktrace")
                raise HTTPException(
                    status_code=e.response.status_code,
                    detail=f"Code evaluator failed validation: {stack_trace or e.response.text}",
                )

            verify_python_function_with_name_and_args(
                code_eval.code, CUSTOM_CODE_EXECUTION_FUNC_NAME, 2 if has_dataset else 1
            )


@retry_asyncpg
async def create_rule_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    rule: schemas.RunRulesCreateSchema,
) -> schemas.RunRulesSchema:
    if rule.filter:
        parse_as_filter_directive(rule.filter)
    if rule.trace_filter:
        parse_as_filter_directive(rule.trace_filter)
    if rule.tree_filter:
        parse_as_filter_directive(rule.tree_filter)
    if rule.evaluators or rule.code_evaluators:
        if rule.session_id:
            session_name = await db.fetchval(
                """
                select name
                from tracer_session
                where tenant_id = $1 and id = $2""",
                auth.tenant_id,
                rule.session_id,
            )

            if session_name == "evaluators":
                raise HTTPException(
                    status_code=400,
                    detail="The 'evaluators' project is reserved, and you cannot create a run rule with evaluators on it.",
                )

        if rule.evaluators:
            secrets = await list_secrets_as_dict(auth)
            for evaluator in rule.evaluators:
                # will raise http exception if invalid
                await validate_evaluator(evaluator, auth, db, secrets)
        elif rule.code_evaluators:
            await validate_code_evaluators(
                auth, rule.code_evaluators, bool(rule.dataset_id)
            )

    if (
        await db.fetchval(
            "select count(*) from run_rules where tenant_id = $1 and parent_rule_id is NULL",
            auth.tenant_id,
        )
        >= auth.tenant_config.max_run_rules
        and not settings.IS_SELF_HOSTED
    ):
        raise HTTPException(
            status_code=400,
            detail="Maximum number of run rules exceeded, <NAME_EMAIL> to increase limit",
        )

    rule_id = uuid4()
    corrections_dataset_id = None
    correction_feedback_key = verify_rule_valid_for_corrections_and_get_feedback_key(
        rule
    )
    if correction_feedback_key is None and rule.use_corrections_dataset:
        raise HTTPException(
            status_code=400, detail="Invalid rule for corrections dataset"
        )
    if correction_feedback_key is not None:
        corrections_dataset_id = (
            await create_and_setup_corrections_dataset_with_existing_transaction(
                db,
                auth,
                rule_id,
                rule,
            )
        )

    if rule.backfill_from:
        if rule.session_id:
            session = [rule.session_id]
        elif rule.dataset_id:
            rows = await db.fetch(
                """
                select id
                from tracer_session
                where tenant_id = $1 and reference_dataset_id = $2
                and (end_time is null or end_time + interval '1 day' > $3)""",
                auth.tenant_id,
                rule.dataset_id,
                datetime.datetime.now(datetime.timezone.utc),
            )
            session = [UUID(row["id"].hex) for row in rows]

        async with clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW) as ch:
            rows = await ch.fetch(
                "fetch_rule_backfill_count",
                """
                SELECT count(1) FROM runs
                WHERE tenant_id = {tenant_id} AND session_id IN({session}) AND inserted_at >= {backfill_from}""",
                params={
                    "tenant_id": auth.tenant_id,
                    "session": session,
                    "backfill_from": convert_datetime(rule.backfill_from),
                },
            )
        row_count = rows[0][0]

        row_limit = (
            settings.RUN_RULES_BACKFILL_FOR_EXTENDED_RETENTION_PROJECT_LIMIT
            if rule.extend_only
            else settings.RUN_RULES_BACKFILL_PROJECT_LIMIT
        )
        if row_count > row_limit:
            raise HTTPException(
                status_code=400,
                detail=f"Backfill from date records exceed {row_limit} records",
            )

    try:
        if rule.webhooks:
            await encrypt_webhooks(rule.webhooks)

        if rule.session_id:
            row = await db.fetchrow(
                """insert into run_rules (
                    session_id,
                    tenant_id,
                    filter,
                    trace_filter,
                    tree_filter,
                    sampling_rate,
                    add_to_annotation_queue_id,
                    add_to_dataset_id,
                    evaluators,
                    code_evaluators,
                    alerts,
                    display_name,
                    backfill_from,
                    webhooks,
                    add_to_dataset_prefer_correction,
                    id,
                    corrections_dataset_id, 
                    extend_only,
                    use_corrections_dataset,
                    num_few_shot_examples,
                    evaluator_version,
                    transient
                )
                select id, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
                from tracer_session
                where tenant_id = $1 and id = $11
                returning *""",
                auth.tenant_id,
                rule.filter,
                rule.trace_filter,
                rule.tree_filter,
                rule.sampling_rate,
                rule.add_to_annotation_queue_id,
                rule.add_to_dataset_id,
                (
                    [
                        evaluator.model_dump(exclude_none=True, by_alias=True)
                        for evaluator in rule.evaluators
                    ]
                    if rule.evaluators
                    else []
                ),
                (
                    [
                        code_eval.model_dump(exclude_none=True, by_alias=True)
                        for code_eval in rule.code_evaluators
                    ]
                    if rule.code_evaluators
                    else []
                ),
                [alert.model_dump() for alert in rule.alerts] if rule.alerts else [],
                rule.session_id,
                rule.display_name,
                rule.backfill_from,
                [webhook.model_dump() for webhook in rule.webhooks]
                if rule.webhooks
                else [],
                rule.add_to_dataset_prefer_correction,
                rule_id,
                corrections_dataset_id,
                rule.extend_only,
                rule.use_corrections_dataset,
                rule.num_few_shot_examples,
                NEW_RULE_EVALUATOR_VERSION,
                rule.transient,
            )
        elif rule.dataset_id:
            row = await db.fetchrow(
                """insert into run_rules (
                    dataset_id,
                    tenant_id,
                    filter,
                    trace_filter,
                    tree_filter,
                    sampling_rate,
                    add_to_annotation_queue_id,
                    add_to_dataset_id,
                    evaluators,
                    code_evaluators,
                    alerts,
                    display_name,
                    backfill_from,
                    webhooks,
                    add_to_dataset_prefer_correction,
                    id,
                    corrections_dataset_id, 
                    extend_only,
                    use_corrections_dataset,
                    num_few_shot_examples,
                    evaluator_version,
                    transient
                )
                select id, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
                from dataset
                where tenant_id = $1 and id = $11
                returning *""",
                auth.tenant_id,
                rule.filter,
                rule.trace_filter,
                rule.tree_filter,
                rule.sampling_rate,
                rule.add_to_annotation_queue_id,
                rule.add_to_dataset_id,
                (
                    [
                        evaluator.model_dump(exclude_none=True, by_alias=True)
                        for evaluator in rule.evaluators
                    ]
                    if rule.evaluators
                    else []
                ),
                (
                    [
                        code_eval.model_dump(exclude_none=True, by_alias=True)
                        for code_eval in rule.code_evaluators
                    ]
                    if rule.code_evaluators
                    else []
                ),
                [alert.model_dump() for alert in rule.alerts] if rule.alerts else [],
                rule.dataset_id,
                rule.display_name,
                rule.backfill_from,
                [webhook.model_dump() for webhook in rule.webhooks]
                if rule.webhooks
                else [],
                rule.add_to_dataset_prefer_correction,
                rule_id,
                corrections_dataset_id,
                rule.extend_only,
                rule.use_corrections_dataset,
                rule.num_few_shot_examples,
                NEW_RULE_EVALUATOR_VERSION,
                rule.transient,
            )
        if corrections_dataset_id is not None and correction_feedback_key is not None:
            await upsert_sub_rule_with_existing_transaction(
                db,
                auth,
                rule_id,
                corrections_dataset_id,
                correction_feedback_key,
                rule.display_name,
            )
    except ForeignKeyViolationError:
        if rule.add_to_annotation_queue_id:
            raise HTTPException(status_code=400, detail="Annotation queue not found")
        elif rule.add_to_dataset_id:
            raise HTTPException(status_code=400, detail="Dataset not found")
        else:
            raise

    if not row:
        raise HTTPException(status_code=404, detail="Session/dataset not found")

    return await validate_and_decrypt_rule(row)


@retry_asyncpg
async def create_rule(
    auth: AuthInfo,
    rule: schemas.RunRulesCreateSchema,
) -> schemas.RunRulesSchema:
    """Create a new run rule."""
    async with asyncpg_conn() as db, db.transaction():
        return await create_rule_with_existing_transaction(db, auth, rule)


async def create_and_setup_corrections_dataset_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    rule_id: UUID,
    rule: schemas.RunRulesCreateSchema,
) -> UUID:
    correction_feedback_key = verify_rule_valid_for_corrections_and_get_feedback_key(
        rule
    )
    evaluators_project_id = await db.fetchval(
        """
        select id
        from tracer_session
        where tenant_id = $1 and name = 'evaluators'""",
        auth.tenant_id,
    )
    corrections_dataset = await crud.create_dataset_with_existing_transaction(
        db,
        auth,
        schemas.DatasetCreate(
            name=f"ds-corrections-{rule.display_name}-{correction_feedback_key}-{rule_id}",
            description=f"Corrections dataset for run rule {rule.display_name}",
            extra={
                "rule_id": str(rule_id),
                "feedback_key": correction_feedback_key,
            },
            # Each corrected example will have a few shot examples key in the inputs.
            # Ideally we'd remove this key completely (TODO) but we don't support
            # removing a single field today. Instead we just empty out all of the few
            # shot examples.
            inputs_schema_definition={
                "type": "object",
                "title": "dataset_input_schema",
                "properties": {
                    EXAMPLES_FEW_SHOT_KEY: {
                        "type": "array",
                        "items": {"type": "object"},
                    }
                },
                "required": [],
            },
            transformations=[
                schemas.DatasetTransformation(
                    path=["inputs", EXAMPLES_FEW_SHOT_KEY, "*"],
                    transformation_type=schemas.DatasetTransformationType.remove_extra_fields,
                )
            ],
        ),
    )
    corrections_dataset_id = corrections_dataset.id

    if not evaluators_project_id:
        await create_tracer_session_within_existing_transaction(
            db,
            auth,
            schemas.TracerSessionCreate(
                name="evaluators",
            ),
        )

    return corrections_dataset_id


async def upsert_sub_rule_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    parent_rule_id: UUID,
    corrections_dataset_id: UUID,
    correction_feedback_key: str,
    parent_display_name: str,
):
    evaluator_project_rule_filter = f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, '{correction_feedback_key}'))"
    await db.fetchrow(
        """
        with existing_rule as (
            select id
            from run_rules
            where parent_rule_id = $7
        ),
        update_rule as (
            update run_rules
            set
                session_id = tracer_session.id,
                tenant_id = $1,
                filter = $2,
                sampling_rate = $3,
                add_to_dataset_id = $4,
                display_name = $5,
                add_to_dataset_prefer_correction = $6
            from tracer_session
            where run_rules.parent_rule_id = $7
              and tracer_session.tenant_id = $1
              and run_rules.tenant_id = $1
              and tracer_session.name = 'evaluators'
            returning run_rules.*
        )
        insert into run_rules (
            session_id,
            tenant_id,
            filter,
            sampling_rate,
            add_to_dataset_id,
            display_name,
            add_to_dataset_prefer_correction,
            parent_rule_id
        )
        select id, $1, $2, $3, $4, $5, $6, $7
        from tracer_session
        where tenant_id = $1
          and name = 'evaluators'
          and not exists (select 1 from existing_rule)
        returning *
        """,
        auth.tenant_id,
        evaluator_project_rule_filter,
        1,
        corrections_dataset_id,
        f"Corrections for run rule {parent_display_name}",
        True,
        parent_rule_id,
    )


@retry_asyncpg
async def update_rule(
    auth: AuthInfo,
    rule_id: UUID,
    rule: schemas.RunRulesCreateSchema,
) -> schemas.RunRulesSchema:
    if rule.filter:
        parse_as_filter_directive(rule.filter)
    if rule.trace_filter:
        parse_as_filter_directive(rule.trace_filter)
    if rule.tree_filter:
        parse_as_filter_directive(rule.tree_filter)

    async with asyncpg_conn() as db, db.transaction():
        if rule.evaluators:
            secrets = await list_secrets_as_dict(auth)
            for evaluator in rule.evaluators:
                # will raise http exception if invalid
                await validate_evaluator(evaluator, auth, db, secrets)
        elif rule.code_evaluators:
            await validate_code_evaluators(
                auth, rule.code_evaluators, bool(rule.dataset_id)
            )

        corrections_dataset_id = None
        corrections_feedback_tag = (
            verify_rule_valid_for_corrections_and_get_feedback_key(rule)
        )
        if corrections_feedback_tag is None and rule.use_corrections_dataset:
            raise HTTPException(
                status_code=400, detail="Invalid rule for corrections dataset"
            )
        if corrections_feedback_tag is not None:
            # Need to check if a corrections dataset exists with the correct rule id and feedback tag, and create one if it doesn't.
            corrections_dataset_id = await db.fetchval(
                """
                SELECT id
                FROM dataset
                WHERE tenant_id = $1
                AND extra @> jsonb_build_object('rule_id', $2::text, 'feedback_key', $3::text);
                """,
                auth.tenant_id,
                str(rule_id),
                corrections_feedback_tag,
            )
            if corrections_dataset_id is None:
                corrections_dataset_id = await create_and_setup_corrections_dataset_with_existing_transaction(
                    db,
                    auth,
                    rule_id,
                    rule,
                )
            await upsert_sub_rule_with_existing_transaction(
                db,
                auth,
                rule_id,
                corrections_dataset_id,
                corrections_feedback_tag or "",
                rule.display_name,
            )
        else:
            await db.fetch(
                """
                DELETE FROM run_rules
                WHERE tenant_id = $1 and parent_rule_id = $2;""",
                auth.tenant_id,
                rule_id,
            )
        try:
            if rule.webhooks:
                await encrypt_webhooks(rule.webhooks)

            if rule.session_id:
                row = await db.fetchrow(
                    """update run_rules set
                    session_id = tracer_session.id,
                    is_enabled = $3, 
                    dataset_id = null,
                    filter = $5,
                    trace_filter = $6,
                    tree_filter = $7,
                    sampling_rate = $8,
                    add_to_annotation_queue_id = $9,
                    add_to_dataset_id = $10,
                    evaluators = $11,
                    alerts = $12,
                    display_name = $13,
                    webhooks = $14,
                    add_to_dataset_prefer_correction = $15,
                    updated_at = now(),
                    corrections_dataset_id = $16, 
                    extend_only = $17,
                    use_corrections_dataset = $18,
                    num_few_shot_examples = $19,
                    code_evaluators = $20,
                    evaluator_version = $21
                from tracer_session
                where tracer_session.tenant_id = $1 and run_rules.tenant_id = $1 and run_rules.id = $2 and tracer_session.id = $4
                returning *""",
                    auth.tenant_id,
                    rule_id,
                    rule.is_enabled,
                    rule.session_id,
                    rule.filter,
                    rule.trace_filter,
                    rule.tree_filter,
                    rule.sampling_rate,
                    rule.add_to_annotation_queue_id,
                    rule.add_to_dataset_id,
                    (
                        [
                            evaluator.model_dump(exclude_none=True, by_alias=True)
                            for evaluator in rule.evaluators
                        ]
                        if rule.evaluators
                        else []
                    ),
                    [alert.model_dump() for alert in rule.alerts]
                    if rule.alerts
                    else [],
                    rule.display_name,
                    [webhook.model_dump() for webhook in rule.webhooks]
                    if rule.webhooks
                    else [],
                    rule.add_to_dataset_prefer_correction,
                    corrections_dataset_id,
                    rule.extend_only,
                    rule.use_corrections_dataset,
                    rule.num_few_shot_examples,
                    (
                        [
                            code_eval.model_dump(exclude_none=True, by_alias=True)
                            for code_eval in rule.code_evaluators
                        ]
                        if rule.code_evaluators
                        else []
                    ),
                    rule.evaluator_version or NEW_RULE_EVALUATOR_VERSION,
                )
            elif rule.dataset_id:
                row = await db.fetchrow(
                    """update run_rules set
                    session_id = null,
                    is_enabled = $3, 
                    dataset_id = dataset.id,                    
                    filter = $5,
                    trace_filter = $6,
                    tree_filter = $7,
                    sampling_rate = $8,
                    add_to_annotation_queue_id = $9,
                    add_to_dataset_id = $10,
                    evaluators = $11,
                    alerts = $12,
                    display_name = $13,
                    webhooks = $14,
                    add_to_dataset_prefer_correction = $15,
                    updated_at = now(),
                    corrections_dataset_id = $16, 
                    extend_only = $17,
                    use_corrections_dataset = $18,
                    num_few_shot_examples = $19,
                    code_evaluators = $20,
                    evaluator_version = $21
                from dataset
                where dataset.tenant_id = $1 and run_rules.tenant_id = $1 and run_rules.id = $2 and dataset.id = $4
                returning *""",
                    auth.tenant_id,
                    rule_id,
                    rule.is_enabled,
                    rule.dataset_id,
                    rule.filter,
                    rule.trace_filter,
                    rule.tree_filter,
                    rule.sampling_rate,
                    rule.add_to_annotation_queue_id,
                    rule.add_to_dataset_id,
                    (
                        [
                            evaluator.model_dump(exclude_none=True, by_alias=True)
                            for evaluator in rule.evaluators
                        ]
                        if rule.evaluators
                        else []
                    ),
                    [alert.model_dump() for alert in rule.alerts]
                    if rule.alerts
                    else [],
                    rule.display_name,
                    [webhook.model_dump() for webhook in rule.webhooks]
                    if rule.webhooks
                    else [],
                    rule.add_to_dataset_prefer_correction,
                    corrections_dataset_id,
                    rule.extend_only,
                    rule.use_corrections_dataset,
                    rule.num_few_shot_examples,
                    (
                        [
                            code_eval.model_dump(exclude_none=True, by_alias=True)
                            for code_eval in rule.code_evaluators
                        ]
                        if rule.code_evaluators
                        else []
                    ),
                    rule.evaluator_version or NEW_RULE_EVALUATOR_VERSION,
                )
        except ForeignKeyViolationError:
            if rule.add_to_annotation_queue_id:
                raise HTTPException(
                    status_code=400, detail="Annotation queue not found"
                )
            elif rule.add_to_dataset_id:
                raise HTTPException(status_code=400, detail="Dataset not found")
            else:
                raise

    if not row:
        raise HTTPException(status_code=404, detail="Rule not found")

    return await validate_and_decrypt_rule(row)


@retry_asyncpg
async def delete_rule(
    auth: AuthInfo,
    rule_id: str,
) -> None:
    async with asyncpg_pool() as db:
        row = await db.fetchrow(
            """delete from run_rules
            where tenant_id = $1 and id = $2
            returning id""",
            auth.tenant_id,
            rule_id,
        )

    if not row:
        raise HTTPException(status_code=404, detail="Rule not found")


@retry_asyncpg
async def trigger_rules_by_dataset(
    auth: AuthInfo,
    dataset_id: UUID,
    enforce_user_id: bool = True,
) -> None:
    async with asyncpg_pool() as db:
        row = await db.fetch(
            """
            select id from run_rules where tenant_id=$1 and dataset_id=$2 limit $3
            """,
            auth.tenant_id,
            dataset_id,
            settings.RUN_RULES_TRIGGER_LIMIT,
        )
        coroutines = [
            trigger_rule(auth, rule["id"], enforce_user_id=enforce_user_id)
            for rule in row
        ]
        semaphore = asyncio.Semaphore(settings.RULES_TRIGGER_SEMAPHORE)
        await asyncio.gather(*(gated_coro(coro, semaphore) for coro in coroutines))


@retry_asyncpg
async def trigger_rule(
    auth: AuthInfo,
    rule_id: UUID,
    enforce_user_id: bool = True,
) -> None:
    async with (
        asyncpg_pool() as db,
        redis.async_queue(settings.RUN_RULES_QUEUE) as queue,
    ):
        row = await db.fetchrow(
            """
            with

            last_applied as (
                select rule_id, max(end_time) as last_applied_at
                from run_rules_applications
                where run_id = '00000000-0000-0000-0000-000000000000'
                and committed = true
                group by rule_id
            )

            select run_rules.*, last_applied_at
            from run_rules
            inner join tenants
                on tenants.id = run_rules.tenant_id
            inner join organizations
                on tenants.organization_id = organizations.id
            left join last_applied
                on last_applied.rule_id = run_rules.id
            where not tenants.is_deleted and not organizations.disabled and (
                add_to_annotation_queue_id is not null
                or add_to_dataset_id is not null
                or evaluators is not null
            )
            and run_rules.id = $1 and run_rules.tenant_id = $2
            """,
            rule_id,
            auth.tenant_id,
        )
        if not row:
            raise HTTPException(
                status_code=404,
                detail="Rule not found or workspace/org is deleted/disabled",
            )

        rule = dict(row)
        if rule["last_applied_at"]:
            start_time = rule["last_applied_at"]
        elif rule["backfill_from"]:
            start_time = rule["backfill_from"]
        else:
            start_time = rule["created_at"]

        await queue.enqueue(
            "apply_run_rule",
            start_time=start_time,
            end_time=datetime.datetime.now(datetime.timezone.utc),
            rule_dict=rule,
            **redis.DEFAULT_JOB_KWARGS,
        )


@retry_asyncpg
async def list_rules(
    auth: AuthInfo,
    dataset_id: UUID | None = None,
    session_id: UUID | None = None,
    type: Literal["session", "dataset"] | None = None,
    name_contains: str | None = None,
    id: list[UUID] | None = None,
) -> list[schemas.RunRulesSchema]:
    """List all run rules."""

    where_clause = ["run_rules.tenant_id = $1"]
    params: list[Any] = [auth.tenant_id]

    if dataset_id:
        where_clause.append("run_rules.dataset_id = $2")
        params.append(dataset_id)
    elif session_id:
        where_clause.append("run_rules.session_id = $2")
        params.append(session_id)
    elif name_contains:
        where_clause.append("run_rules.display_name ILIKE $2")
        params.append(f"%{name_contains}%")
    elif id:
        where_clause.append("run_rules.id = ANY($2)")
        params.append(id)
    if type == "session":
        where_clause.append("run_rules.session_id is not null")
    elif type == "dataset":
        where_clause.append("run_rules.dataset_id is not null")

    where_sql = " and ".join(where_clause)

    async with asyncpg_conn(exception_mapper=base_exception_mapper) as db:
        rows = await db.fetch(
            f"""
            select
                run_rules.*,
                tracer_session.name as session_name,
                dataset.name as dataset_name,
                add_dataset.name as add_to_dataset_name,
                annotation_queues.name as add_to_annotation_queue_name
            from run_rules
            left join tracer_session ON tracer_session.id = run_rules.session_id
            left join dataset on dataset.id = run_rules.dataset_id
            left join dataset as add_dataset ON add_dataset.id = run_rules.add_to_dataset_id
            left join annotation_queues ON annotation_queues.id = run_rules.add_to_annotation_queue_id
            where {where_sql}
            order by run_rules.updated_at desc""",
            *params,
        )

    return await asyncio.gather(*[validate_and_decrypt_rule({**row}) for row in rows])


async def get_last_application_for_rule(
    auth: AuthInfo,
    rule_id: UUID,
) -> schemas.RuleLogSchema:
    async with asyncpg_pool() as db:
        last_applied = await db.fetchrow(
            """
            WITH rule_application as (
                select *
                from run_rules_applications
                where rule_id = $1
                    AND application_time IS NOT NULL
                    AND committed = true
                ORDER BY application_time DESC
                LIMIT 1
            )
            SELECT rule_application.*
            FROM rule_application
                INNER JOIN run_rules ON run_rules.id = rule_application.rule_id
            WHERE run_rules.tenant_id = $2
            """,
            rule_id,
            auth.tenant_id,
        )

    if not last_applied:
        raise HTTPException(
            status_code=404, detail="No rule applications found not found"
        )

    return schemas.RuleLogSchema(**last_applied)


@retry_asyncpg
async def list_rule_logs(
    auth: AuthInfo,
    rule_id: str,
    limit: int,
    offset: int,
    start_time: datetime.datetime,
    end_time: datetime.datetime,
) -> tuple[list[schemas.RuleLogSchema], int]:
    async with asyncpg_pool() as db:
        query = """
            SELECT run_rules_applications.*
            FROM run_rules_applications
            INNER JOIN run_rules ON run_rules.id = run_rules_applications.rule_id
            WHERE run_rules.tenant_id = $1
            AND rule_id = $2
            AND committed = true
        """
        params = [auth.tenant_id, rule_id]

        idx = 3
        if start_time is not None:
            query += f" AND start_time >= ${str(idx)}"
            params.append(start_time)
            idx += 1
        if end_time is not None:
            query += f" AND start_time <= ${str(idx)}"
            params.append(end_time)
            idx += 1

        query += f"""
            ORDER BY start_time DESC, run_id ASC
            LIMIT ${str(idx)} OFFSET ${str(idx + 1)}
        """
        params.extend([limit + 1, offset])
        rows = await db.fetch(query, *params)

    run_ids = set(
        str(row["run_id"])
        for row in rows
        if row["run_id"] != "00000000-0000-0000-0000-000000000000"
    )

    runs_query = (
        await fetch_runs(
            auth,
            schemas.BodyParamsForRunSchema.model_construct(
                select=[
                    schemas.RunSelect.run_type,
                    schemas.RunSelect.session_id,
                ],
                id=run_ids,
            ),
        )
        if run_ids
        else {}
    )

    run_metadata = {run["id"]: run for run in runs_query.get("runs", [])}

    feedback_ids = [
        ((row["evaluators"] or {}).get("payload", {}) or {}).get("feedback_ids", None)
        for row in rows
        if row["run_id"] != "00000000-0000-0000-0000-000000000000" and row["evaluators"]
    ]

    feedback_ids = [id for ids in feedback_ids if ids for id in ids]
    feedback_query = await fetch_feedback(feedback_ids, auth) if feedback_ids else []
    feedback_metadata = {str(feedback["id"]): feedback for feedback in feedback_query}

    mutable_rows = [{**row} for row in rows]

    for row in mutable_rows:
        row_feedback_ids: list[str] | None = (
            (row["evaluators"] or {}).get("payload", {}) or {}
        ).get("feedback_ids", None)
        if row_feedback_ids:
            row["evaluators"]["payload"] |= {
                "feedback_metadata": {
                    id: feedback_metadata.get(id) for id in row_feedback_ids
                }
            }

    return [
        schemas.RuleLogSchema(
            **row,
            run_name=run_metadata.get(row["run_id"], {}).get("name"),
            run_type=run_metadata.get(row["run_id"], {}).get("run_type"),
            run_session_id=run_metadata.get(row["run_id"], {}).get("session_id"),
        )
        for row in mutable_rows
    ], len(rows)


async def send_rules_alert(
    run_rule: schemas.RunRulesSchema,
    alert_config: schemas.RunRulesAlertSchema,
    runs: list[dict],
) -> None:
    if alert_config.type == schemas.RunRulesAlertType.pagerduty:
        event_link = (
            "https://smith.langchain.com/"
            if not settings.LANGCHAIN_ENDPOINT
            or "api.smith.langchain.com" in settings.LANGCHAIN_ENDPOINT
            else settings.LANGCHAIN_ENDPOINT
        )
        async with ClientSession() as session:
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
            }
            response = await session.post(
                "https://events.pagerduty.com/v2/enqueue",
                headers=headers,
                json={
                    "payload": {
                        "summary": alert_config.summary or "Run rule alert triggered",
                        "timestamp": datetime.datetime.now().strftime(
                            "%Y-%m-%dT%H:%M:%S.%f+0000"
                        ),
                        "severity": alert_config.severity,
                        "source": f"Project: {run_rule.session_id}",
                        "component": f"Rule: {run_rule.id}",
                        "custom_details": {
                            "# of runs alerted": str(len(runs)),
                        },
                    },
                    "routing_key": alert_config.routing_key,
                    "event_action": "trigger",
                    "client": "LangSmith",
                    "client_url": event_link,
                },
            )
            logger.info(
                "Alert sent to pagerduty response={}".format(await response.text())
            )

    else:
        raise Exception("Unknown alert type")


def chat_to_structured(prompt, schema, **structured_output_kwargs):
    return StructuredPrompt(
        schema=schema,
        **prompt.to_json()["kwargs"],
        structured_output_kwargs=structured_output_kwargs,
    )


async def get_chain_from_evaluator(
    evaluator: schemas.EvaluatorTopLevel,
    auth: AuthInfo,
    db: asyncpg.Connection,
) -> dict:
    chain: Dict[str, Any] = {
        "lc": 1,
        "type": "constructor",
        "id": ["langchain", "schema", "runnable", "RunnableSequence"],
        "kwargs": {},
    }
    # load prompt, build chain
    if evaluator.structured.prompt:
        prompt = ChatPromptTemplate.from_messages(
            messages=evaluator.structured.prompt,
            template_format=evaluator.structured.template_format or "mustache",
        )
        schema = {
            "name": "eval",
            "description": "Submit your evaluation for this run.",
            "parameters": evaluator.structured.tool_schema,
        }
        structured_prompt = chat_to_structured(prompt, schema)
        chain["kwargs"]["first"] = dumpd(structured_prompt)
    elif evaluator.structured.hub_ref:
        owner, repo, commit = parse_owner_repo_commit(evaluator.structured.hub_ref)
        if commit == "latest":
            commit = None
        manifest = await get_commit_manifest(
            db, auth, owner, repo, commit, get_examples=False, is_view=False
        )
        try:
            prompt = load(manifest.manifest)
        except Exception as e:
            # Failed to load chain, raise HTTPException
            logger.error("Failed to load prompt", exc_info=e)
            raise HTTPException(status_code=400, detail=str(e))
        if not isinstance(prompt, StructuredPrompt):
            raise HTTPException(status_code=400, detail="Expected structured prompt")

        # TODO: remove this once langchain_openai supports optional "title" and "description" fields
        # https://github.com/langchain-ai/langchain/pull/19656
        if isinstance(prompt.schema_, dict):
            prompt.schema_.setdefault("title", "extract")
            prompt.schema_.setdefault("description", "")

        chain["kwargs"]["first"] = dumpd(prompt)
    else:
        raise HTTPException(status_code=400, detail="Invalid evaluator")

    chain["kwargs"]["last"] = evaluator.structured.model

    return chain


async def validate_evaluator(
    evaluator: schemas.EvaluatorTopLevel,
    auth: AuthInfo,
    db: asyncpg.Connection,
    secrets: dict[str, str] | None = None,
) -> None:
    chain = await get_chain_from_evaluator(evaluator, auth, db)

    body = {
        "manifest": chain,
        "secrets": secrets,
        "options": {},
    }
    try:
        async with get_service_client(
            ServiceName.PLAYGROUND,
        ) as playground_client:
            response = await playground_client.post(
                "/internal/playground/validate",
                json=body,
            )
        response.raise_for_status()
        return response.json()
    except HTTPStatusError as e:
        logger.error(f"Failed to validate evaluator: {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail="Evaluator failed validation",
        )


async def batch_invoke_evaluator(
    chain: dict,
    auth: AuthInfo,
    inputs: List[Input],
    configs: List[RunnableConfig],
    secrets: dict[str, str] | None = None,
) -> List[dict]:
    for config in configs:
        config["run_id"] = str(config["run_id"])
    body = {
        "manifest": chain,
        "secrets": secrets,
        "options": configs,
        "input": inputs,
        "project_name": "evaluators",
    }

    # Call batch in playground service
    try:
        async with get_internal_tenant_scoped_service_client(
            ServiceName.PLAYGROUND, auth.tenant_id
        ) as playground_client:
            response = await playground_client.post(
                "/playground/batch", json=body, headers=auth.to_headers()
            )
        response.raise_for_status()
        return response.json()
    except HTTPStatusError as e:
        logger.error(f"Failed to batch invoke evaluator: {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Evaluator failed to batch invoke {e.response.text}",
        )
