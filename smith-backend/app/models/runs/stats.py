import asyncio
import functools
import hashlib
from copy import deepcopy
from datetime import datetime
from functools import partial
from typing import Any, Callable, Literal, Mapping, cast
from uuid import UUID

import orjson
import structlog
from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_config.utils import arun_in_executor
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.queue.serializer import _option

from app import schemas
from app.api.auth import AuthInfo, ShareDatasetInfo, ShareRunInfo
from app.api.auth.schemas import OrgAuthInfo
from app.memoize import execute_with_cache, orjson_default
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    Operation,
    Operator,
    parse_as_filter_directive,
)
from app.models.query_lang.translate import SqlVisitorClickhouse
from app.models.runs.attrs import JOIN_ATTR_NAMES, RUN_ATTRIBUTES
from app.models.runs.constants import LANGCHAIN_GENERIC_RUN_NAMES
from app.models.runs.fetch_ch import _all_attrs, process_query_params
from app.models.runs.monitor import get_timedelta_unit, get_timedelta_value
from app.models.tracer_sessions.stats import (
    feedback_facets_projection,
    feedback_stats_projection,
    input_kv_facets_projection,
    input_kv_facets_projection_agg,
    map_stats,
    metadata_facets_projection,
    metadata_facets_projection_agg,
    output_kv_facets_projection,
    output_kv_facets_projection_agg,
    runs_avg_latency_projection,
    runs_facets_projection,
    runs_facets_projection_agg,
    runs_stats_projection,
    runs_stats_projection_agg,
    token_count_stats_projection,
    token_count_stats_projection_agg,
)
from app.retry import retry_clickhouse_read

logger = structlog.get_logger(__name__)

GROUP_BY_TOP_K = 5
RUNS_PK_COLUMNS = [
    "tenant_id",
    "session_id",
    "is_root",
    "start_time",
    "run_id",
    "id",
    "is_trace_expired",
]

RUNS_COLUMNS = [
    "tenant_id",
    "session_id",
    "is_root",
    "start_time",
    "run_id",
    "id",
    "is_trace_expired",
    "run_type",
    "status",
    "name",
]

token_count_projection = """
    id,
    sum(runs_token_counts.total_tokens) as total_tokens,
    sum(runs_token_counts.prompt_tokens) as prompt_tokens,
    sum(runs_token_counts.completion_tokens) as completion_tokens,
    sum(runs_token_counts.total_cost) as total_cost,
    sum(runs_token_counts.prompt_cost) as prompt_cost,
    sum(runs_token_counts.completion_cost) as completion_cost,
    min(runs_token_counts.start_time) as min_start_time,
    min(runs_token_counts.first_token_time) as first_token_time
"""

exclude_expired_traces = " AND ((trace_first_received_at IS NOT NULL AND trace_ttl_seconds IS NOT NULL AND trace_first_received_at < now() - toIntervalSecond(trace_ttl_seconds)) = 0)"

in_filtered_runs_query = " AND run_id IN (SELECT id FROM filtered_runs)"

min_max_time_in_filtered_runs_query = """
AND start_time <= global_max_start_time
AND start_time >= global_min_start_time
AND (run_id) IN (SELECT id FROM filtered_runs)"""

bucketed_ts_select_custom_column = """
    toTimeZone(
        toDateTime(
            toUInt32(
                toStartOfInterval(
                    toDateTime(toUnixTimestamp({{table_name}}.{col}) - toUnixTimestamp(toDateTime({{{{start_time}}}}))),
                    INTERVAL {{{{stride_value}}}} {{stride_unit}}
                )
            )
            + toDateTime({{{{start_time}}}}
        )
    ), {{{{timezone}}}})
     AS ts,"""
bucketed_ts_select = bucketed_ts_select_custom_column.format(col="start_time")

bucketed_ts_group_by = """
GROUP BY ts
ORDER BY ts asc WITH FILL FROM toDateTime({{start_time}}) TO COALESCE(toDateTime({{end_time}}), now()) step interval {{stride_value}} {bucketed_stride_unit}"""


def _build_in_filtered_runs_query(params: dict) -> str:
    """
    Build the in_filtered_runs_query based on the params.
    """
    proj_cols = ["tenant_id", "session_id", "is_root", "start_time", "run_id"]
    include_proj_cols = [True] * len(proj_cols)
    # Attempt to pull out primary columns from IN tuple
    for key in params:
        if "__" in key and key.split("__")[0] in proj_cols:
            include_proj_cols[proj_cols.index(key.split("__")[0])] = False
    proj_cols_str = ", ".join(
        [col for col, include in zip(proj_cols, include_proj_cols) if include]
    )
    in_tuple_str = ", ".join(
        [
            col if col != "run_id" else "id"
            for col, include in zip(proj_cols, include_proj_cols)
            if include
        ]
    )
    return f" AND ({proj_cols_str}) IN (SELECT {in_tuple_str} FROM filtered_runs)"


def _is_primary_key_filters_only(params: dict) -> bool:
    """
    Check if the query has only primary key filters.
    """
    for key in params:
        if (
            "__" in key
            and key.split("__")[0] not in RUNS_PK_COLUMNS
            and not key.startswith("group_by")
        ):
            return False
    return True


def _is_run_filters_only(params: dict) -> bool:
    """
    Check if the query has only run filters.
    """
    for key in params:
        if (
            "__" in key
            and key.split("__")[0] not in RUNS_COLUMNS
            and not key.startswith("group_by")
        ):
            return False
    return True


async def fetch_runs_stats(
    runs_filtered_query: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    can_skip_topk_facets: bool = False,
    **kwargs: Any,
) -> dict[str, Any] | list[dict[str, Any]]:  # dummy params to match other functions
    return await fetch_runs_stats_for_projection(
        projection_type="stats_facets",
        runs_filtered_query=runs_filtered_query,
        params=params,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        group_by=group_by,
        can_skip_topk_facets=can_skip_topk_facets,
        **kwargs,
    )


async def fetch_runs_stats_for_projection(
    projection_type: Literal["stats", "facets", "stats_facets"],
    runs_filtered_query: str,
    params: dict,
    join_pushdown: str,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    can_skip_topk_facets: bool = False,
    can_skip_filtered_runs_cte: bool = False,
    **kwargs: Any,
) -> dict[str, Any] | list[dict[str, Any]]:  # dummy params to match other functions
    # First use params to determine if any non-primary key filters in the query

    query, params = _runs_stats_for_projection_query(
        projection_type=projection_type,
        runs_filtered_query=runs_filtered_query,
        params=params,
        join_pushdown=join_pushdown,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        group_by=group_by,
        can_skip_topk_facets=can_skip_topk_facets,
        can_skip_filtered_runs_cte=can_skip_filtered_runs_cte,
        **kwargs,
    )
    return await _fetch_stats(
        query,
        params,
        name="run_" + projection_type,
        bucketed_ts=bucketed_ts,
        group_by=group_by,
    )


def _runs_stats_for_projection_query(
    projection_type: Literal["stats", "facets", "stats_facets"],
    runs_filtered_query: str,
    params: dict,
    join_pushdown: str,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    can_skip_topk_facets: bool = False,
    can_skip_filtered_runs_cte: bool = False,
    runs_table_name: str = "runs",
    runs_filtered_where: str = "",
    **kwargs: Any,
) -> tuple[str, dict[str, Any]]:  # dummy params to match other functions
    # get the primary key where clause and the filtered runs where clause
    runs_filtered_query = runs_filtered_query if not can_skip_filtered_runs_cte else ""
    table_name = runs_table_name if can_skip_filtered_runs_cte else "filtered_runs"

    # parse the bucketed ts and group by
    select, group_by_clause, parsed_group_by = _parse_bucketed_ts_and_group_by(
        table_name,
        bucketed_ts,
        bucketed_stride_unit,
        group_by,
        join_pushdown=join_pushdown,
        in_filtered_runs=_build_in_filtered_runs_query(params),
        runs_table_name=table_name,
        can_skip_filtered_runs_cte=can_skip_filtered_runs_cte,
        runs_filtered_where=runs_filtered_where,
    )
    if projection_type in ("stats", "stats_facets"):
        select += runs_stats_projection
    if projection_type == "facets":
        select += runs_facets_projection[2:]
    if projection_type == "stats_facets":
        select += runs_facets_projection

    use_top_k_agg_merge_tree = params["use_topk_agg_merge_tree"] or can_skip_topk_facets
    if parsed_group_by:
        params = {**params, **parsed_group_by["params"]}
        # Case when the group by is on the run columns - name, run_type
        if parsed_group_by["table_name"] == table_name:
            query = f"""
                WITH
                {runs_filtered_query.replace(f"{runs_table_name} FINAL", runs_table_name).replace("WITH", "")}
                {_top_k_groups_cte(parsed_group_by).strip().rstrip(",")}

                SELECT {select}
                FROM (
                    SELECT DISTINCT
                        start_time,
                        id,
                        end_time,
                        status,
                        name,
                        run_type,
                        tags
                    FROM {table_name}
                    WHERE 
                        {parsed_group_by["table_name"]}.{parsed_group_by["col"]} IN (SELECT group_name from top_k_groups)
                        AND {runs_filtered_where} 
                ) as {table_name}
                {group_by_clause}
            """
        # Case when the group by is on the metadata_value, tag columns
        else:
            runs_filtered_query_cte = runs_filtered_query.replace(
                f"{runs_table_name} FINAL", runs_table_name
            )
            topk_runs_with_group_name_cte_str = _top_k_groups_rows_cte(parsed_group_by)
            query = f"""
                WITH
                    {runs_filtered_query_cte.replace("WITH", "")}
                    {topk_runs_with_group_name_cte_str}
                SELECT {select}
                FROM (
                    SELECT DISTINCT
                        {table_name}.start_time, 
                        {table_name}.id,
                        {table_name}.end_time,
                        {table_name}.status, 
                        {table_name}.name, 
                        {table_name}.run_type, 
                        {table_name}.tags, 
                        topk_runs_with_group_name.{parsed_group_by["col"]} as group_name
                    FROM {table_name}
                    JOIN topk_runs_with_group_name ON {_join_on(table_name, "topk_runs_with_group_name", first_id_col="id")}
                    {"WHERE " + runs_filtered_where if can_skip_filtered_runs_cte else ""}
                ) as {table_name}
                {group_by_clause}
            """
    elif (
        _top_k_agg_merge_tree_enabled(str(params.get("tenant_id__eq")))
        and use_top_k_agg_merge_tree
        and projection_type == "facets"
    ):
        query = (
            """
            WITH run_stats AS (
                SELECT"""
            + runs_facets_projection_agg[2:]
            + """
                    FROM runs_topk_hourly_agg
                    WHERE """
            + (
                """is_root = {is_root__eq} AND """
                if params.get("is_root__eq") is not None
                else ""
            )
            + (
                """hour >= toStartOfHour(toDateTime64({start_time__gte}, 6)) AND """
                if params.get("start_time__gte") is not None
                else ""
            )
            + (
                """hour <= dateAdd(HOUR, 1, toStartOfHour(toDateTime64({start_time__lte}, 6))) AND """
                if params.get("start_time__lte") is not None
                else ""
            )
            + """session_id in {session_id__in} AND tenant_id = {tenant_id__eq}
            )
            SELECT run_stats.* FROM run_stats
            """
        )
    elif (
        projection_type == "stats"
        and params["use_session_agg_merge_tree"]
        and _session_agg_enabled(params.get("tenant_id__eq", ""))
    ):
        query = (
            """
                WITH run_stats AS (
                    SELECT"""
            + runs_stats_projection_agg[2:]
            + """
            FROM runs_sessions_agg_hourly
            WHERE """
            + (
                """is_root = {is_root__eq} AND """
                if params.get("is_root__eq") is not None
                else ""
            )
            + (
                """reference_dataset_id = {reference_dataset_id__eq} AND """
                if params.get("reference_dataset_id__eq")
                else ""
            )
            + (
                """hour >= toStartOfHour(toDateTime64({start_time__gte}, 6)) AND """
                if params.get("start_time__gte") is not None
                else ""
            )
            + (
                """hour <= dateAdd(HOUR, 1, toStartOfHour(toDateTime64({start_time__lte}, 6))) AND """
                if params.get("start_time__lte") is not None
                else ""
            )
            + """session_id in {session_id__in} AND tenant_id = {tenant_id__eq}
            )

            SELECT run_stats.* FROM run_stats
            """
        )
    elif can_skip_filtered_runs_cte:
        query = f"""
            SELECT 
                {select}
            FROM {runs_table_name} as filtered_runs
            {"PREWHERE " + runs_filtered_where.replace("WHERE", "") if runs_filtered_where else ""}
            {group_by_clause}
        """
    else:
        query = f"""
            {runs_filtered_query.replace(f"{runs_table_name} FINAL", runs_table_name)}

            run_stats AS (
                SELECT 
                    {select}
                FROM filtered_runs
                {group_by_clause}
            )

            SELECT run_stats.* FROM run_stats
        """

    return query, params


async def fetch_runs_stats_latency_avg(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    params: dict,
    join_pushdown: str,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    runs_filtered_where: str = "",
    **kwargs: Any,
) -> dict[str, Any] | list[dict[str, Any]]:
    query, params = _runs_stats_latency_avg_query(
        runs_filtered_query,
        params=params,
        join_pushdown=join_pushdown,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        group_by=group_by,
        runs_filtered_where=runs_filtered_where,
        **kwargs,
    )
    return await _fetch_stats(
        query,
        params,
        name="run_stats_latency_avg",
        group_by=group_by,
        bucketed_ts=bucketed_ts,
        can_skip_join_to_runs=can_skip_join_to_runs,
    )


def _runs_stats_latency_avg_query(
    runs_filtered_query: str,
    *,
    params: dict,
    join_pushdown: str,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    runs_filtered_where: str = "",
    runs_table_name: str = "runs",
    can_skip_filtered_runs_cte: bool = False,
    **kwargs: Any,
) -> tuple[str, dict[str, Any]]:
    runs_filtered_query = runs_filtered_query if not can_skip_filtered_runs_cte else ""
    table_name = runs_table_name if can_skip_filtered_runs_cte else "filtered_runs"
    select, group_by_clause, parsed_group_by = _parse_bucketed_ts_and_group_by(
        table_name,
        bucketed_ts,
        bucketed_stride_unit,
        group_by,
        join_pushdown=join_pushdown,
        in_filtered_runs=_build_in_filtered_runs_query(params),
        runs_table_name=table_name,
        can_skip_filtered_runs_cte=can_skip_filtered_runs_cte,
        runs_filtered_where=runs_filtered_where,
    )
    select += runs_avg_latency_projection
    if parsed_group_by:
        params = {**params, **parsed_group_by["params"]}
        if parsed_group_by["table_name"] == table_name:
            query = f"""
                {runs_filtered_query or "WITH"}

                {_top_k_groups_cte(parsed_group_by)}

                run_stats AS (
                    SELECT {select}
                    FROM {table_name} {"FINAL" if can_skip_filtered_runs_cte else ""}
                    WHERE
                        {parsed_group_by["col"]} IN (SELECT group_name FROM top_k_groups)
                        {"AND " + runs_filtered_where if can_skip_filtered_runs_cte else ""}
                    {group_by_clause}
                )

                SELECT run_stats.* FROM run_stats
            """
        else:
            group_by_cte = "filtered_" + parsed_group_by["table_name"]
            query = f"""
                {runs_filtered_query or "WITH"}

                {_top_k_groups_cte(parsed_group_by)}

                run_stats AS (
                    SELECT {select}
                    FROM (
                        SELECT 
                            start_time, 
                            id, 
                            end_time, 
                            {group_by_cte}.{parsed_group_by["col"]} as group_name
                        FROM {table_name} {"FINAL" if can_skip_filtered_runs_cte else ""}
                        JOIN (
                            SELECT 
                                tenant_id,
                                session_id,
                                is_root,
                                start_time,
                                run_id,
                                {parsed_group_by["col"]}
                            FROM {parsed_group_by["table_name"]} FINAL
                            WHERE
                                {parsed_group_by["col"]} IN (SELECT group_name FROM top_k_groups)
                                {parsed_group_by["where"]}
                        ) as {group_by_cte} ON {_join_on(table_name, group_by_cte, first_id_col="id")}
                        {"WHERE " + runs_filtered_where if can_skip_filtered_runs_cte else ""}
                    ) as {table_name}
                    {group_by_clause}
                ) 

                SELECT run_stats.* FROM run_stats
                """
    elif not can_skip_filtered_runs_cte:
        query = f"""
        {runs_filtered_query}

        runs_stats AS(
            SELECT {select}
            FROM filtered_runs
            {group_by_clause}
        )

        SELECT run_stats.* FROM run_stats
        """
    else:
        query = f"""
        WITH run_stats AS (
            SELECT {select}
            FROM {runs_table_name} FINAL
            {"PREWHERE " + runs_filtered_where.replace("WHERE", "") if runs_filtered_where else ""}
            {group_by_clause}
        )
        SELECT run_stats.* FROM run_stats
        """
    return query, params


async def fetch_feedback_stats(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    runs_table_name: str = "runs",
    min_max_time_filtered_query: str = "",
    skip_facets: bool = False,
    runs_filtered_where: str = "",
    **kwargs: Any,
) -> dict[str, Any] | list[dict[str, Any]]:
    query, params = _feedback_stats_query(
        can_skip_join_to_runs,
        runs_filtered_query,
        join_pushdown,
        params=params,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        group_by=group_by,
        runs_table_name=runs_table_name,
        min_max_time_filtered_query=min_max_time_filtered_query,
        skip_facets=skip_facets,
        runs_filtered_where=runs_filtered_where,
        **kwargs,
    )
    return await _fetch_stats(
        query,
        params,
        name="feedback_stats",
        bucketed_ts=bucketed_ts,
        group_by=group_by,
        can_skip_join_to_runs=can_skip_join_to_runs,
    )


def _feedback_stats_query(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    runs_table_name: str = "runs",
    min_max_time_filtered_query: str = "",
    skip_facets: bool = False,
    runs_filtered_where: str = "",
    **kwargs: Any,
) -> tuple[str, dict[str, Any]]:
    if can_skip_join_to_runs:
        filter_runs = ""
        in_filtered_runs = ""
    elif settings.FF_USE_MIN_MAX_TIME_FILTERED_QUERY and min_max_time_filtered_query:
        filter_runs = min_max_time_filtered_query.replace(
            f"{runs_table_name} FINAL", runs_table_name
        )
        in_filtered_runs = min_max_time_in_filtered_runs_query
    else:
        if "is_root__eq" in params:
            filter_runs = runs_filtered_query.replace(
                f"{runs_table_name} FINAL", runs_table_name
            )

        else:
            filter_runs = runs_filtered_query.replace(
                f"{runs_table_name} FINAL", runs_table_name
            )
        in_filtered_runs = _build_in_filtered_runs_query(params)

    select, group_by_clause, parsed_group_by = _parse_bucketed_ts_and_group_by(
        "feedbacks_rmt",
        bucketed_ts,
        bucketed_stride_unit,
        group_by,
        in_filtered_runs=in_filtered_runs,
        runs_table_name=runs_table_name,
        join_pushdown=join_pushdown,
        runs_filtered_where=runs_filtered_where,
    )
    select += feedback_stats_projection
    if not skip_facets:
        select += feedback_facets_projection

    if parsed_group_by:
        group_by_cte = "filtered_" + parsed_group_by["table_name"]
        group_by_run_id = (
            "run_id" if parsed_group_by["table_name"] != runs_table_name else "id"
        )
        params = {**params, **parsed_group_by["params"]}
        if filter_runs and parsed_group_by["table_name"] == runs_table_name:
            query = f"""
        WITH filtered_runs AS (
            SELECT DISTINCT
                tenant_id,
                session_id,
                is_root,
                start_time,
                id,
                {parsed_group_by["col"]} as group_name
            FROM {runs_table_name} 
            WHERE
                {runs_filtered_where}
                AND {parsed_group_by["col"]} IN (
                    SELECT arrayJoin(topK({GROUP_BY_TOP_K})({parsed_group_by["col"]})) AS top_groups
                    FROM {runs_table_name}
                    WHERE {_remove_prefix(parsed_group_by["where"], "AND")}
                )
        ),

        feedback_stats AS (
            SELECT {select.replace("feedbacks_rmt.group_name", "group_name")}
            FROM filtered_runs
            LEFT JOIN (
                SELECT 
                    tenant_id,
                    session_id,
                    is_root,
                    start_time,
                    run_id,
                    id, 
                    key, 
                    score, 
                    value, 
                    correction, 
                    extra, 
                    feedback_source
                FROM feedbacks_rmt
                WHERE 
                    {join_pushdown}
            ) as feedbacks_rmt ON
                {_join_on("feedbacks_rmt", "filtered_runs", second_id_col="id")}
            {group_by_clause}
        )

        SELECT feedback_stats.* FROM feedback_stats
        """
        elif filter_runs:
            top_k_select = (
                _top_k_groups_cte(parsed_group_by)
                .replace("top_k_groups AS ", "")
                .strip()
                .strip(",")
            )
            query = f"""
        {filter_runs}

        {group_by_cte} AS (
            SELECT DISTINCT
                tenant_id,
                session_id,
                is_root,
                start_time,
                run_id,
                {parsed_group_by["col"]}
            FROM {parsed_group_by["table_name"]}
            WHERE 
                {parsed_group_by["col"]} IN {top_k_select}
                {parsed_group_by["where"]}
        ),

        feedback_stats AS (
            SELECT {select}
            FROM (
                SELECT 
                    feedbacks_rmt.id, 
                    feedbacks_rmt.key, 
                    feedbacks_rmt.score, 
                    feedbacks_rmt.value, 
                    feedbacks_rmt.correction, 
                    feedbacks_rmt.extra, 
                    feedbacks_rmt.feedback_source,
                    feedbacks_rmt.start_time,
                    {group_by_cte}.{parsed_group_by["col"]} as group_name
                FROM feedbacks_rmt FINAL
                JOIN {group_by_cte} ON {_join_on("feedbacks_rmt", group_by_cte)}
                WHERE {join_pushdown}
            ) as feedbacks_rmt
            {group_by_clause}
        )

        SELECT feedback_stats.* FROM feedback_stats"""
        else:
            query = f"""
        WITH {_top_k_groups_cte(parsed_group_by)}

        feedback_stats AS (
            SELECT {select}
            FROM (
                SELECT 
                    feedbacks_rmt.id, 
                    feedbacks_rmt.key, 
                    feedbacks_rmt.score, 
                    feedbacks_rmt.value, 
                    feedbacks_rmt.correction, 
                    feedbacks_rmt.extra, 
                    feedbacks_rmt.feedback_source,
                    feedbacks_rmt.start_time,
                    {group_by_cte}.{parsed_group_by["col"]} as group_name
                FROM feedbacks_rmt FINAL
                JOIN (
                    SELECT DISTINCT
                        tenant_id,
                        session_id,
                        is_root,
                        start_time,
                        {group_by_run_id},
                        {parsed_group_by["col"]}
                    FROM {parsed_group_by["table_name"]}
                    WHERE
                        {parsed_group_by["col"]} IN (SELECT group_name FROM top_k_groups)
                        {parsed_group_by["where"]}
                ) as {group_by_cte} ON {_join_on("feedbacks_rmt", group_by_cte, second_id_col=group_by_run_id)}
                WHERE {join_pushdown}
            ) as feedbacks_rmt
            {group_by_clause}
        )

        SELECT feedback_stats.* FROM feedback_stats"""
    elif can_skip_join_to_runs:
        query = (
            """
        WITH feedback_stats as (
            SELECT"""
            + select
            + """
            FROM feedbacks_rmt FINAL 
            WHERE """
            + join_pushdown
            + group_by_clause
            + """
        )
        SELECT feedback_stats.* FROM feedback_stats
        """
        )
    else:
        query = (
            filter_runs
            + """
        feedback_stats AS (
            SELECT"""
            + select
            + """
            FROM (
                SELECT 
                    feedbacks_rmt.id, 
                    feedbacks_rmt.key, 
                    feedbacks_rmt.score, 
                    feedbacks_rmt.value, 
                    feedbacks_rmt.correction, 
                    feedbacks_rmt.extra, 
                    feedbacks_rmt.feedback_source,
                    feedbacks_rmt.start_time,
                FROM feedbacks_rmt FINAL 
                WHERE """
            + join_pushdown
            + in_filtered_runs
            + """
            ) AS feedbacks_rmt
            """
            + group_by_clause
            + """
        )

        SELECT feedback_stats.* FROM feedback_stats
        """
        )

    return query, params


async def fetch_metadata_stats(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    runs_table_name: str = "runs",
    can_skip_topk_facets: bool = False,
    runs_filtered_where: str = "",
    **kwargs,
) -> dict[str, Any] | list[dict[str, Any]]:
    query, params = _metadata_stats_query(
        can_skip_join_to_runs,
        runs_filtered_query,
        join_pushdown,
        params=params,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        group_by=group_by,
        runs_table_name=runs_table_name,
        can_skip_topk_facets=can_skip_topk_facets,
        runs_filtered_where=runs_filtered_where,
        **kwargs,
    )

    return await _fetch_stats(
        query,
        params,
        name="metadata_stats",
        bucketed_ts=bucketed_ts,
        group_by=group_by,
        can_skip_join_to_runs=can_skip_join_to_runs,
    )


def _metadata_stats_query(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsGroupBy | None = None,
    runs_table_name: str = "runs",
    can_skip_topk_facets: bool = False,
    runs_filtered_where: str = "",
    **kwargs,
) -> tuple[str, dict[str, Any]]:
    if can_skip_join_to_runs:
        filter_runs = ""
        in_filtered_runs = ""
        filtered_runs_table_name = runs_table_name
    else:
        filter_runs = runs_filtered_query.replace(
            f"{runs_table_name} FINAL", runs_table_name
        )
        in_filtered_runs = in_filtered_runs_query
        filtered_runs_table_name = "filtered_runs"

    select, group_by_clause, parsed_group_by = _parse_bucketed_ts_and_group_by(
        "runs_metadata_kv",
        bucketed_ts,
        bucketed_stride_unit,
        group_by,
        in_filtered_runs=in_filtered_runs,
        runs_table_name=filtered_runs_table_name,
        join_pushdown=join_pushdown,
        runs_filtered_where=runs_filtered_where,
    )
    select += metadata_facets_projection

    tenant_id = str(params.get("tenant_id__eq"))
    use_top_k_agg_merge_tree = params["use_topk_agg_merge_tree"] or can_skip_topk_facets
    if parsed_group_by:
        params = {**params, **parsed_group_by["params"]}

        if parsed_group_by["table_name"] == "runs_metadata_kv":
            query = f"""
    {(filter_runs or "WITH")}

    {_top_k_groups_cte(parsed_group_by)}

    metadata_stats AS (
        SELECT {select}
        FROM runs_metadata_kv
        WHERE
            {join_pushdown}
            AND {parsed_group_by["col"]} IN (SELECT group_name from top_k_groups)
            {parsed_group_by["where"]}
        {group_by_clause}
    )

    SELECT metadata_stats.* FROM metadata_stats"""

        else:
            group_by_cte = "filtered_" + parsed_group_by["table_name"]
            group_by_run_id = (
                "run_id"
                if parsed_group_by["table_name"] != filtered_runs_table_name
                else "id"
            )
            query = f"""
    {(filter_runs or "WITH")}

    {_top_k_groups_cte(parsed_group_by)}

    metadata_stats AS (
        SELECT {select}
        FROM (
            SELECT 
                runs_metadata_kv.start_time, 
                runs_metadata_kv.key, 
                runs_metadata_kv.value, 
                {group_by_cte}.{parsed_group_by["col"]} as group_name 
            FROM runs_metadata_kv 
            JOIN (
                SELECT 
                    tenant_id,
                    session_id,
                    is_root,
                    start_time,
                    {group_by_run_id},
                    {parsed_group_by["col"]}
                FROM {parsed_group_by["table_name"]}
                WHERE
                    {parsed_group_by["col"]} IN (SELECT group_name FROM top_k_groups)
                    {parsed_group_by["where"]}
            ) as {group_by_cte} ON {_join_on("runs_metadata_kv", group_by_cte, second_id_col=group_by_run_id)}
            WHERE {join_pushdown}
        ) AS runs_metadata_kv
        {group_by_clause}
    )

    SELECT metadata_stats.* FROM metadata_stats"""
    elif _top_k_agg_merge_tree_enabled(tenant_id) and use_top_k_agg_merge_tree:
        if params.get("start_time__gte") is not None:
            join_pushdown = join_pushdown.replace("start_time >= ", "hour >= ")
            join_pushdown = join_pushdown.replace(
                "{start_time__gte}", "toStartOfHour(toDateTime64({start_time__gte}, 6))"
            )
        if params.get("start_time__lte") is not None:
            join_pushdown = join_pushdown.replace("start_time <= ", "hour <= ")
            join_pushdown = join_pushdown.replace(
                "{start_time__lte}",
                "dateAdd(HOUR, 1, toStartOfHour(toDateTime64({start_time__lte}, 6)))",
            )
        query = (
            """
    WITH metadata_stats as ( 
        SELECT"""
            + metadata_facets_projection_agg
            + """
        FROM runs_topk_hourly_agg WHERE """
            + join_pushdown
            + """ 
    ) 

    SELECT metadata_stats.* FROM metadata_stats"""
        )
    elif can_skip_join_to_runs:
        query = (
            """
    WITH metadata_stats as (
        SELECT"""
            + select
            + """
        FROM runs_metadata_kv WHERE """
            + join_pushdown
            + exclude_expired_traces
            + group_by_clause
            + """
    )
    
    SELECT metadata_stats.* FROM metadata_stats
    """
        )
    else:
        query = (
            runs_filtered_query.replace(f"{runs_table_name} FINAL", runs_table_name)
            + """
    metadata_stats AS (
        SELECT"""
            + select
            + """
        FROM runs_metadata_kv
        WHERE """
            + join_pushdown
            + in_filtered_runs
            + group_by_clause
            + """
    )

    SELECT metadata_stats.* FROM metadata_stats
    """
        )
    return query, params


def _io_kv_stats_query(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    can_skip_topk_facets: bool = False,
    group_by: schemas.RunStatsGroupBy | None = None,
    io: Literal["input", "output"] = "input",
    runs_filtered_where: str = "",
    runs_table_name: str = "runs",
    **kwargs: Any,
) -> tuple[str, dict[str, Any]]:
    if can_skip_join_to_runs:
        filter_runs = ""
        in_filtered_runs = ""
        filtered_runs_table_name = runs_table_name
    else:
        filter_runs = runs_filtered_query.replace(
            f"{runs_table_name} FINAL", runs_table_name
        )
        in_filtered_runs = in_filtered_runs_query
        filtered_runs_table_name = "filtered_runs"

    select, group_by_clause, parsed_group_by = _parse_bucketed_ts_and_group_by(
        f"runs_{io}s_kv",
        bucketed_ts,
        bucketed_stride_unit,
        group_by,
        in_filtered_runs=in_filtered_runs,
        runs_table_name=filtered_runs_table_name,
        join_pushdown=join_pushdown,
        runs_filtered_where=runs_filtered_where,
    )
    select += (
        input_kv_facets_projection if io == "input" else output_kv_facets_projection
    )

    tenant_id = str(params.get("tenant_id__eq"))
    use_top_k_agg_merge_tree = params["use_topk_agg_merge_tree"] or can_skip_topk_facets
    if parsed_group_by:
        params = {**params, **parsed_group_by["params"]}
        if parsed_group_by["table_name"] == f"runs_{io}s_kv":
            query = f"""
    {(filter_runs or "WITH")}

    {_top_k_groups_cte(parsed_group_by)}

    {io}_kv_stats AS (
        SELECT {select}
        FROM runs_{io}s_kv
        WHERE
            {join_pushdown}
            AND {parsed_group_by["col"]} IN (SELECT group_name from top_k_groups)
            {parsed_group_by["where"]}
        {group_by_clause}
    )

    SELECT {io}_kv_stats.* FROM {io}_kv_stats"""

        else:
            group_by_cte = "filtered_" + parsed_group_by["table_name"]
            group_by_run_id = (
                "run_id"
                if parsed_group_by["table_name"] != filtered_runs_table_name
                else "id"
            )
            query = f"""
    {(filter_runs or "WITH")}

    {_top_k_groups_cte(parsed_group_by)}

    {io}_kv_stats AS (
        SELECT {select}
        FROM (
            SELECT 
                runs_{io}s_kv.start_time, 
                runs_{io}s_kv.key, 
                runs_{io}s_kv.value, 
                {group_by_cte}.{parsed_group_by["col"]} as group_name 
            FROM runs_{io}s_kv 
            JOIN (
                SELECT 
                    tenant_id,
                    session_id,
                    is_root,
                    start_time,
                    {group_by_run_id},
                    {parsed_group_by["col"]}
                FROM {parsed_group_by["table_name"]}
                WHERE
                    {parsed_group_by["col"]} IN (SELECT group_name FROM top_k_groups)
                    {parsed_group_by["where"]}
            ) as {group_by_cte} ON {_join_on(f"runs_{io}s_kv", group_by_cte, second_id_col=group_by_run_id)}
            WHERE {join_pushdown}
        ) AS runs_{io}s_kv
        {group_by_clause}
    )

    SELECT {io}_kv_stats.* FROM {io}_kv_stats"""

    elif _top_k_agg_merge_tree_enabled(tenant_id) and use_top_k_agg_merge_tree:
        if params.get("start_time__gte") is not None:
            join_pushdown = join_pushdown.replace("start_time >= ", "hour >= ")
            join_pushdown = join_pushdown.replace(
                "{start_time__gte}", "toStartOfHour(toDateTime64({start_time__gte}, 6))"
            )
        if params.get("start_time__lte") is not None:
            join_pushdown = join_pushdown.replace("start_time <= ", "hour <= ")
            join_pushdown = join_pushdown.replace(
                "{start_time__lte}",
                "dateAdd(HOUR, 1, toStartOfHour(toDateTime64({start_time__lte}, 6)))",
            )
        projection_agg = (
            input_kv_facets_projection_agg
            if io == "input"
            else output_kv_facets_projection_agg
        )
        query = (
            f"""
            WITH {io}_kv_stats as ( 
                SELECT"""
            + projection_agg
            + """ 
                FROM runs_topk_hourly_agg 
                WHERE """
            + join_pushdown
            + f""" ) 
            
            SELECT {io}_kv_stats.* FROM {io}_kv_stats"""
        )
    elif can_skip_join_to_runs:
        query = (
            f"""
    WITH {io}_kv_stats as (
        SELECT"""
            + select
            + f"""
        FROM runs_{io}s_kv WHERE """
            + join_pushdown
            + exclude_expired_traces
            + group_by_clause
            + f"""
    )
    SELECT {io}_kv_stats.* FROM {io}_kv_stats
    """
        )
    else:
        query = (
            runs_filtered_query.replace(f"{runs_table_name} FINAL", runs_table_name)
            + f"""
    {io}_kv_stats AS (
        SELECT"""
            + select
            + f"""
        FROM runs_{io}s_kv
        WHERE """
            + join_pushdown
            + in_filtered_runs_query
            + group_by_clause
            + f"""
    )

    SELECT {io}_kv_stats.* FROM {io}_kv_stats
    """
        )

    return query, params


async def fetch_input_kv_stats(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    can_skip_topk_facets: bool = False,
    group_by: schemas.RunStatsGroupBy | None = None,
    **kwargs: Any,
) -> dict[str, Any] | list[dict[Any, Any]]:
    query, params = _io_kv_stats_query(
        can_skip_join_to_runs,
        runs_filtered_query,
        join_pushdown,
        params=params,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        can_skip_topk_facets=can_skip_topk_facets,
        group_by=group_by,
        io="input",
        **kwargs,
    )
    return await _fetch_stats(
        query,
        params,
        name="input_kv_stats",
        bucketed_ts=bucketed_ts,
        group_by=group_by,
        can_skip_join_to_runs=can_skip_join_to_runs,
    )


async def fetch_output_kv_stats(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    can_skip_topk_facets: bool = False,
    group_by: schemas.RunStatsGroupBy | None = None,
    **kwargs: Any,
) -> dict[str, Any] | list[dict[Any, Any]]:
    query, params = _io_kv_stats_query(
        can_skip_join_to_runs,
        runs_filtered_query,
        join_pushdown,
        params=params,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        can_skip_topk_facets=can_skip_topk_facets,
        group_by=group_by,
        io="output",
        **kwargs,
    )
    return await _fetch_stats(
        query,
        params,
        name="output_kv_stats",
        bucketed_ts=bucketed_ts,
        group_by=group_by,
        can_skip_join_to_runs=can_skip_join_to_runs,
    )


async def fetch_token_count_stats(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    tokens_pushdown: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsQueryParams | None = None,
    min_max_time_filtered_query: str = "",
    runs_table_name: str = "runs",
    **kwargs: Any,
) -> dict[str, Any] | list[dict]:
    query, params = _token_count_stats_query(
        can_skip_join_to_runs,
        runs_filtered_query,
        tokens_pushdown,
        join_pushdown,
        params=params,
        bucketed_ts=bucketed_ts,
        bucketed_stride_unit=bucketed_stride_unit,
        group_by=group_by,
        min_max_time_filtered_query=min_max_time_filtered_query,
        runs_table_name=runs_table_name,
        **kwargs,
    )
    return await _fetch_stats(
        query,
        params,
        name="token_count_stats",
        bucketed_ts=bucketed_ts,
        group_by=group_by,
        can_skip_join_to_runs=can_skip_join_to_runs,
    )


def _token_count_stats_query(
    can_skip_join_to_runs: bool,
    runs_filtered_query: str,
    tokens_pushdown: str,
    join_pushdown: str,
    params: dict,
    bucketed_ts: bool = False,
    bucketed_stride_unit: str = "day",
    group_by: schemas.RunStatsQueryParams | None = None,
    min_max_time_filtered_query: str = "",
    runs_table_name: str = "runs",
    runs_filtered_where: str = "",
    **kwargs: Any,
) -> tuple[str, dict[str, Any]]:
    if can_skip_join_to_runs:
        filter_runs = ""
        in_filtered_runs = ""
    elif settings.FF_USE_MIN_MAX_TIME_FILTERED_QUERY and min_max_time_filtered_query:
        filter_runs = min_max_time_filtered_query
        in_filtered_runs = min_max_time_in_filtered_runs_query
    else:
        if "is_root__eq" in params:
            in_filtered_runs = " AND run_id IN (SELECT id FROM filtered_runs)"
        else:
            in_filtered_runs = (
                " AND (is_root, run_id) in (SELECT is_root, id from filtered_runs)"
            )
        filter_runs = runs_filtered_query.replace(
            f"{runs_table_name} FINAL", runs_table_name
        ).replace("SELECT", "SELECT DISTINCT", 1)
    select, group_by_clause, parsed_group_by = _parse_bucketed_ts_and_group_by(
        "token_counts",
        bucketed_ts,
        bucketed_stride_unit,
        group_by,
        ts_col="min_start_time",
        in_filtered_runs=in_filtered_runs,
        runs_table_name=runs_table_name,
        join_pushdown=join_pushdown,
        runs_filtered_where=runs_filtered_where,
    )
    select += token_count_stats_projection
    select += """,
    arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
    arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
    arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
    quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
    """
    if parsed_group_by:
        params = {**params, **parsed_group_by["params"]}
        # grouping by run name or run type
        if parsed_group_by["table_name"] == runs_table_name:
            query = f"""
    WITH top_k_runs AS (
        SELECT DISTINCT
            tenant_id,
            session_id,
            is_root,
            start_time,
            id,
            {parsed_group_by["col"]} as group_name
        FROM {runs_table_name}
        WHERE 
            {runs_filtered_where}
            AND {parsed_group_by["col"]} IN (
                SELECT 
                    arrayJoin(topK({GROUP_BY_TOP_K})({parsed_group_by["col"]})) as group_name
                FROM {runs_table_name}
                WHERE
                    {_remove_prefix(parsed_group_by["where"], "AND")}
            )
    ),

    token_counts AS (
        SELECT 
            {token_count_projection},
            top_k_runs.group_name
        FROM top_k_runs
        LEFT JOIN (
            SELECT 
                tenant_id,
                session_id,
                is_root,
                start_time,
                id,
                total_tokens,
                prompt_tokens,
                completion_tokens,
                total_cost,
                prompt_cost,
                completion_cost,
                first_token_time 
            FROM runs_token_counts FINAL
            WHERE 
                runs_token_counts.total_tokens < 4000000000 AND
                {tokens_pushdown}
        ) as runs_token_counts ON
            {_join_on("runs_token_counts", "top_k_runs", first_id_col="id", second_id_col="id")}
        WHERE 
            {join_pushdown}
        GROUP BY id, group_name
    ),

    token_count_stats AS (
        SELECT 
            {select}
        from token_counts
        {group_by_clause}
    )

    SELECT token_count_stats.* FROM token_count_stats
    """
        # grouping by something not on the runs table
        else:
            group_by_cte = "filtered_" + parsed_group_by["table_name"]
            top_k_select = (
                _top_k_groups_cte(parsed_group_by)
                .replace("top_k_groups AS", "")
                .strip()
                .strip(",")
            )
            query = f"""
        {filter_runs or "WITH"}

        {group_by_cte} AS (
            SELECT DISTINCT
                tenant_id,
                session_id,
                is_root,
                start_time,
                run_id,
                {parsed_group_by["col"]}
            FROM {parsed_group_by["table_name"]}
            WHERE
                {parsed_group_by["col"]} in {top_k_select}
                {parsed_group_by["where"]}
        ),

        token_counts AS (
            SELECT 
                {group_by_cte}.{parsed_group_by["col"]} as group_name,
                {token_count_projection}
            FROM {group_by_cte}
            LEFT JOIN (
                SELECT
                    tenant_id,
                    session_id,
                    is_root,
                    start_time,
                    id,
                    total_tokens,
                    prompt_tokens,
                    completion_tokens,
                    total_cost,
                    prompt_cost,
                    completion_cost,
                    first_token_time
                FROM runs_token_counts FINAL
                WHERE
                    runs_token_counts.total_tokens < 4000000000
                    AND {tokens_pushdown}
            ) as runs_token_counts ON
                {_join_on(group_by_cte, "runs_token_counts", second_id_col="id")}
            GROUP BY id, group_name
        ),

        token_count_stats AS (
            SELECT 
                {select}
            FROM token_counts
            {group_by_clause}
        )

        SELECT token_count_stats.* FROM token_count_stats
        """
    elif (
        can_skip_join_to_runs
        and params["use_session_agg_merge_tree"]
        and _session_agg_enabled(params.get("tenant_id__eq", ""))
    ):
        query = (
            """
            WITH token_count_stats as (
                SELECT """
            + token_count_stats_projection_agg
            + """
                FROM runs_sessions_agg_hourly
                WHERE """
            + (
                """ hour >= toStartOfHour(toDateTime64({start_time__gte}, 6)) AND """
                if params.get("start_time__gte") is not None
                else ""
            )
            + (
                """ hour <= dateAdd(HOUR, 1, toStartOfHour(toDateTime64({start_time__lte}, 6))) AND """
                if params.get("start_time__lte") is not None
                else ""
            )
            + """session_id in {session_id__in} AND tenant_id = {tenant_id__eq}
            )
            SELECT token_count_stats.* FROM token_count_stats
        """
        )
    elif can_skip_join_to_runs:
        query = (
            """
    WITH token_counts as (
        SELECT"""
            + token_count_projection
            + """
        FROM runs_token_counts FINAL
        WHERE runs_token_counts.total_tokens < 4000000000 AND"""
            + tokens_pushdown
            + exclude_expired_traces
            + """
        GROUP BY id
    ),

    token_count_stats as (
        SELECT"""
            + select
            + """
        FROM token_counts """
            + group_by_clause
            + """
    )
   
    SELECT token_count_stats.* FROM token_count_stats
    """
        )
    else:
        if settings.FF_USE_MIN_MAX_TIME_FILTERED_QUERY and min_max_time_filtered_query:
            join_on = "runs_token_counts.id = filtered_runs.id"
        else:
            join_on = _join_on(
                "runs_token_counts",
                "filtered_runs",
                first_id_col="id",
                second_id_col="id",
            )
        query = (
            (filter_runs or "WITH")
            + """
    token_counts AS (
        SELECT"""
            + token_count_projection
            + """
        FROM filtered_runs
        LEFT JOIN (
            SELECT 
                tenant_id,
                session_id,
                is_root,
                start_time,
                id,
                total_tokens,
                prompt_tokens,
                completion_tokens,
                total_cost,
                prompt_cost,
                completion_cost,
                first_token_time
            FROM runs_token_counts FINAL
            WHERE runs_token_counts.total_tokens < 4000000000 AND """
            + tokens_pushdown
            + f"""
            {in_filtered_runs.replace("run_id", "id")}
        ) AS runs_token_counts ON 
            {join_on}
        GROUP BY id
    ),

    token_count_stats AS (
        SELECT"""
            + select
            + """
        FROM token_counts """
            + group_by_clause
            + """
    )

    SELECT token_count_stats.* FROM token_count_stats
    """
        )

    return query, params


metrics_to_functions: dict[schemas.RunStatsSelect, list[Callable[..., Any]]] = {
    schemas.RunStatsSelect.run_count: [
        partial(fetch_runs_stats_for_projection, "stats")
    ],
    schemas.RunStatsSelect.latency_p50: [
        partial(fetch_runs_stats_for_projection, "stats")
    ],
    schemas.RunStatsSelect.latency_p99: [
        partial(fetch_runs_stats_for_projection, "stats")
    ],
    schemas.RunStatsSelect.first_token_p50: [fetch_token_count_stats],
    schemas.RunStatsSelect.first_token_p99: [fetch_token_count_stats],
    schemas.RunStatsSelect.total_tokens: [fetch_token_count_stats],
    schemas.RunStatsSelect.prompt_tokens: [fetch_token_count_stats],
    schemas.RunStatsSelect.completion_tokens: [fetch_token_count_stats],
    schemas.RunStatsSelect.median_tokens: [fetch_token_count_stats],
    schemas.RunStatsSelect.completion_tokens_p50: [fetch_token_count_stats],
    schemas.RunStatsSelect.prompt_tokens_p50: [fetch_token_count_stats],
    schemas.RunStatsSelect.tokens_p99: [fetch_token_count_stats],
    schemas.RunStatsSelect.completion_tokens_p99: [fetch_token_count_stats],
    schemas.RunStatsSelect.prompt_tokens_p99: [fetch_token_count_stats],
    schemas.RunStatsSelect.cost_p50: [fetch_token_count_stats],
    schemas.RunStatsSelect.cost_p99: [fetch_token_count_stats],
    schemas.RunStatsSelect.last_run_start_time: [
        partial(fetch_runs_stats_for_projection, "stats")
    ],
    schemas.RunStatsSelect.feedback_stats: [
        partial(fetch_feedback_stats, skip_facets=True)
    ],
    schemas.RunStatsSelect.run_facets: [
        fetch_metadata_stats,
        fetch_runs_stats,
        fetch_feedback_stats,
        fetch_input_kv_stats,
        fetch_output_kv_stats,
    ],
    schemas.RunStatsSelect.error_rate: [
        partial(fetch_runs_stats_for_projection, "stats")
    ],
    schemas.RunStatsSelect.streaming_rate: [fetch_token_count_stats],
    schemas.RunStatsSelect.total_cost: [fetch_token_count_stats],
    schemas.RunStatsSelect.prompt_cost: [fetch_token_count_stats],
    schemas.RunStatsSelect.completion_cost: [fetch_token_count_stats],
    schemas.RunStatsSelect.latency_avg: [fetch_runs_stats_latency_avg],
}

_orjson_option = _option | orjson.OPT_SORT_KEYS


async def cached_stats(
    max_time_range_minutes: int | None,
    auth: AuthInfo | OrgAuthInfo | ShareRunInfo | ShareDatasetInfo,
    func: Callable[..., Any],
    *args: Any,
    **kwargs: Any,
) -> Any:
    """
    Util to add caching to stats functions.
    Also rounds down the start_time to the nearest bucket to allow caching over time.
    """
    kwargs["params"] = deepcopy(kwargs["params"]) if kwargs.get("params") else None
    if "params" in kwargs and "start_time__gte" in kwargs["params"]:
        start_time_str = kwargs["params"]["start_time__gte"]
        try:
            start_time = datetime.fromisoformat(start_time_str)
            if max_time_range_minutes is not None:
                end_time = kwargs["params"].get("start_time__lte")
                end_time = (
                    datetime.fromisoformat(end_time) if end_time else datetime.now()
                )
                if (
                    end_time - start_time
                ).total_seconds() / 60 > max_time_range_minutes:
                    await logger.ainfo(
                        f"Returning empty stats for func due to larger period: {start_time} to {end_time}"
                    )
                    return []
            # Replace start_time with rounded start_time down to hour.
            # This is normalizing the start_time otherwise we can't cache a moving start_time.
            bucketed_start_time = start_time.replace(minute=0, second=0, microsecond=0)
            kwargs["params"]["start_time__gte"] = bucketed_start_time.isoformat()
        except ValueError:
            await logger.aerror(f"Invalid start_time__gte: {start_time_str}")
    else:
        # if time range max return none since this is unbounded
        if max_time_range_minutes is not None:
            await logger.ainfo(
                "Returning empty stats for func due to unbounded time period"
            )
            return []

    key_prefix = (
        "smith:cache:",
        func.__module__,
        func.__name__,
        auth.tenant_id
        if isinstance(auth, AuthInfo)
        else auth.organization_id
        if isinstance(auth, OrgAuthInfo)
        else auth,
    )
    search_args_hash = await arun_in_executor(
        orjson.dumps,
        (args, kwargs),
        option=_orjson_option,
        default=orjson_default,
    )
    hashed_content = hashlib.sha256(search_args_hash).hexdigest()

    key = await arun_in_executor(
        orjson.dumps,
        key_prefix + (hashed_content,),
        option=_orjson_option,
        default=orjson_default,
    )
    return await execute_with_cache(
        key=key,
        func=func,
        args=args,
        kwargs=kwargs,
        ttl=settings.STATS_CACHE_TTL_SEC,
    )


def get_min_max_time_filtered_runs_cte(
    min_max_start_times_cte: str, projection: str, runs_table_filter: str
) -> str:
    return min_max_start_times_cte + (
        f"""
    filtered_runs AS (
    SELECT {projection}
    FROM runs FINAL
    WHERE
    {runs_table_filter}
    AND (runs.id) IN (
        select
            run_id
        from
            runs_and_times
    )
    and runs.start_time <= global_max_start_time 
    and runs.start_time >= global_min_start_time
    SETTINGS multiple_joins_try_to_keep_original_names = 1  
),
    """
        if min_max_start_times_cte
        else ""
    )


async def _fetch_kwargs(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    query_params: schemas.RunStatsQueryParams,
    bucket_info: schemas.CustomChartsRequestBase | None = None,
    tenant_id: UUID | None = None,
) -> dict[str, Any]:
    processed_query_params = await process_query_params(
        auth, query_params, tenant_id=tenant_id
    )

    # Check if we can skip the join making query more efficient.
    # Don't count trace expiration as it's calculated in the stats query
    can_skip_join_to_runs = (
        _all_attrs(processed_query_params.where.arguments) - {"is_trace_expired"}
    ).issubset(JOIN_ATTR_NAMES)

    data_source_type = (
        query_params.data_source_type or schemas.RunsFilterDataSourceTypeEnum.current
    )
    runs_table_name = data_source_type.get_table_name()

    # translate the query to clickhouse sql
    try:
        (
            sql_from_join_where,
            params,
            join_pushdown,
            subquery_pushdown,
            min_max_start_times_cte,
            runs_table_filter,
        ) = processed_query_params.where.accept(
            SqlVisitorClickhouse(
                attributes=processed_query_params.run_attributes,
                main_table=runs_table_name,
                main_table_suffix="FINAL",
            )
        )

        if "tenant_id" not in join_pushdown or "tenant_id" not in subquery_pushdown:
            # extra check to ensure tenant_id present
            can_skip_join_to_runs = False

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e

    if query_params.trace_filter:
        can_skip_join_to_runs = False

        where_trace = Operation(
            operator=Operator.AND, arguments=[Comparison(C.EQ, "is_root", True)]
        )
        # copy tenant_id and session_id filters from the main query
        for arg in processed_query_params.where.arguments:
            if isinstance(arg, Comparison) and arg.attribute in (
                "tenant_id",
                "session_id",
                "start_time",
            ):
                where_trace.arguments.append(arg)

        # add the trace filter expression, and translate to clickhouse sql
        where_trace.arguments.append(
            parse_as_filter_directive(query_params.trace_filter)
        )
        try:
            trace_sql_from_join_where, trace_params, _, _, _, _ = where_trace.accept(
                SqlVisitorClickhouse(
                    attributes=RUN_ATTRIBUTES,
                    main_table=runs_table_name,
                    param_suffix="_trace_filter",
                )
            )
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e)) from e

        # apply the trace filter using a subquery via runs_trace_id
        params = {**params, **trace_params}
        sql_from_join_where = f"""{sql_from_join_where}
AND (tenant_id, session_id, is_root, start_time, id) IN (
    SELECT tenant_id, session_id, is_root, start_time, id
    FROM runs_trace_id
    WHERE (tenant_id, session_id, trace_id) IN (
        SELECT tenant_id, session_id, id
        {trace_sql_from_join_where}
    )
)"""

    if query_params.tree_filter:
        can_skip_join_to_runs = False

        where_child = Operation(operator=Operator.AND, arguments=[])
        # copy tenant_id and session_id filters from the main query
        for arg in processed_query_params.where.arguments:
            if isinstance(arg, Comparison) and arg.attribute in (
                "tenant_id",
                "session_id",
                "start_time",
            ):
                where_child.arguments.append(arg)

        # add the child filter expression, and translate to clickhouse sql
        where_child.arguments.append(
            parse_as_filter_directive(query_params.tree_filter)
        )
        try:
            child_sql_from_join_where, child_params, _, _, _, _ = where_child.accept(
                SqlVisitorClickhouse(
                    attributes=RUN_ATTRIBUTES,
                    main_table=runs_table_name,
                    param_suffix="_child_filter",
                )
            )
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e)) from e

        # apply the child filter using a subquery via runs_trace_id
        params = {**params, **child_params}
        sql_from_join_where = f"""{sql_from_join_where}
AND trace_id IN (
SELECT trace_id
{child_sql_from_join_where}
)"""

    tokens_pushdown = subquery_pushdown
    # Only count source run tokens unless is_root__eq=True.
    if not params.get("is_root__eq"):
        tokens_pushdown += " AND id = source_id"

    params["use_topk_agg_merge_tree"] = (
        processed_query_params.can_use_topk_agg_merge_tree
    )
    params["use_session_agg_merge_tree"] = (
        processed_query_params.can_use_session_agg_merge_tree
    )
    # build projection
    projection = ", ".join(
        f"{runs_table_name}.{col} AS {col}"
        for col in [
            "tenant_id",
            "session_id",
            "is_root",
            "id",
            "start_time",
            "end_time",
            "total_tokens",
            "prompt_tokens",
            "completion_tokens",
            "prompt_cost",
            "completion_cost",
            "total_cost",
            "name",
            "status",
            "run_type",
            "tags",
            "first_token_time",
        ]
    )

    bucketed_ts = bucket_info is not None
    runs_filtered_query = f"""
WITH

filtered_runs AS (
    SELECT {projection}
    {sql_from_join_where}
    ORDER BY {runs_table_name}.start_time DESC, id DESC
    SETTINGS multiple_joins_try_to_keep_original_names = 1,
        optimize_read_in_order = 1
),

"""

    min_max_time_filtered_query = get_min_max_time_filtered_runs_cte(
        min_max_start_times_cte,
        projection,
        runs_table_filter,
    )

    if bucket_info:
        params["stride_value"] = get_timedelta_value(bucket_info.stride)
        params["timezone"] = bucket_info.timezone
        params["start_time"] = (
            query_params.start_time.strftime("%Y-%m-%d %H:%M:%S")
            if query_params.start_time
            else "now()"
        )  # Should never be None
        params["end_time"] = (
            query_params.end_time.strftime("%Y-%m-%d %H:%M:%S")
            if query_params.end_time
            else "now()"
        )

    can_skip_filtered_runs_cte = _is_primary_key_filters_only(params) or (
        _is_run_filters_only(params)
        and (
            query_params.group_by is None
            or query_params.group_by.attribute in ("name", "run_type")
        )
    )

    return {
        "can_skip_join_to_runs": can_skip_join_to_runs,
        "runs_filtered_query": runs_filtered_query,
        "primary_key_where_clause": processed_query_params.where_primary_key_str,
        "tokens_pushdown": tokens_pushdown,
        "join_pushdown": join_pushdown,
        "params": {**params, **processed_query_params.where_primary_key_params},
        "bucketed_ts": bucketed_ts,
        "bucketed_stride_unit": get_timedelta_unit(bucket_info.stride)
        if bucket_info
        else "day",
        "group_by": query_params.group_by,
        "runs_table_name": runs_table_name,
        "min_max_time_filtered_query": min_max_time_filtered_query,
        "can_skip_topk_facets": (
            auth.tenant_config.organization_config.tenant_skip_topk_facets
            if isinstance(auth, AuthInfo)
            else False
        ),
        "runs_filtered_where": sql_from_join_where[
            sql_from_join_where.find("WHERE") + 5 :
        ],
        "can_skip_filtered_runs_cte": can_skip_filtered_runs_cte,
    }


@retry_clickhouse_read
async def run_stats(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    query_params: schemas.RunStatsQueryParams,
    select: list[schemas.RunStatsSelect] | None = None,
    bucket_info: schemas.CustomChartsRequestBase | None = None,
    max_values: int | None = None,
    tenant_id: UUID | None = None,
) -> dict[str, Any] | dict[datetime, Any]:
    if (
        query_params.group_by
        and not settings.FF_RUN_STATS_GROUP_BY_ENABLED_ALL
        and str(auth.tenant_id) not in settings.FF_RUN_STATS_GROUP_BY_ENABLED_TENANTS
    ):
        raise HTTPException(status_code=400, detail="'group_by' not enabled.")
    fetch_kwargs = await _fetch_kwargs(
        auth,
        query_params,
        bucket_info=bucket_info,
        tenant_id=tenant_id,
    )

    fetch_functions = []
    if select:
        for s in select:
            fetch_functions.extend(metrics_to_functions[s])
    else:
        fetch_functions = [
            partial(fetch_runs_stats_for_projection, "stats"),
            partial(
                cached_stats, None, auth, fetch_runs_stats_for_projection, "facets"
            ),
            fetch_feedback_stats,
            partial(
                cached_stats,
                settings.STATS_MAX_RANGE_MINUTES,
                auth,
                fetch_metadata_stats,
            ),
            partial(
                cached_stats,
                settings.STATS_MAX_RANGE_MINUTES,
                auth,
                fetch_input_kv_stats,
            ),
            partial(
                cached_stats,
                settings.STATS_MAX_RANGE_MINUTES,
                auth,
                fetch_output_kv_stats,
            ),
            fetch_token_count_stats,
        ]

    results = await asyncio.gather(
        *[fetch_fn(**fetch_kwargs) for fetch_fn in fetch_functions],
        return_exceptions=True,
    )

    # raise exception if all errors
    if all(isinstance(result, BaseException) for result in results):
        raise ExceptionGroup(
            "All stats fetching operations failed",
            cast(list[Exception], results),
        )
    if results and isinstance(results[0], Mapping):
        results = [results]

    grouped_stats: dict = {}
    for result in results:
        if isinstance(result, BaseException):
            logger.error("Error fetching stats", exc_info=result)
            continue
        if not isinstance(result, list):
            logger.error(
                "Unexpected result: not of type Exception or list", result=result
            )
            continue
        for row in result:
            if isinstance(row, BaseException):
                logger.error("Error fetching stats", exc_info=row)
                continue
            row_dict = dict(row)
            key = (row_dict.pop("group_name", None), row_dict.pop("ts", None))
            if key not in grouped_stats:
                grouped_stats[key] = {}
            grouped_stats[key].update(row_dict)
    mapped_stats = {}
    for group, stats in grouped_stats.items():
        mapped = map_stats(
            stats, with_facets=True, max_values=max_values, bucketed_stats=grouped_stats
        )
        mapped_stats[group] = {s.value: mapped[s] for s in select} if select else mapped

    # no group by, no ts buckets:
    if list(mapped_stats.keys()) == [(None, None)]:
        return mapped_stats[(None, None)]

    ret_stats: dict = {}
    for group, stats in mapped_stats.items():
        keys = [k for k in group if k is not None]
        v = ret_stats
        for k in keys[:-1]:
            v[k] = v.get(k, {})
            v = v[k]
        v[keys[-1]] = stats

    return ret_stats


def _clickhouse_stats_settings(
    tenant_id: UUID | None,
) -> tuple[ClickhouseClient, float, int]:
    if tenant_id and str(tenant_id) in settings.TENANT_EXTENDED_CLICKHOUSE_RESOURCES:
        ch_cluster = ClickhouseClient.IN_APP_STATS_SLOW
        stats_timeout = settings.EXTENDED_CLICKHOUSE_STATS_TIMEOUT
        stats_max_threads = settings.EXTENDED_CLICKHOUSE_STATS_MAX_THREADS
    else:
        ch_cluster = ClickhouseClient.IN_APP_STATS
        stats_timeout = settings.CLICKHOUSE_STATS_TIMEOUT
        stats_max_threads = settings.CLICKHOUSE_STATS_MAX_THREADS

    return ch_cluster, stats_timeout, stats_max_threads


def _parse_group_by(
    group_by: schemas.RunStatsGroupBy,
    in_filtered_runs: str,
    join_pushdown: str,
    runs_table_name: str = "runs",
    runs_filtered_where: str = "",
) -> dict:
    join_pushdown = (
        (" AND " + join_pushdown)
        if join_pushdown and not join_pushdown.strip().startswith("AND")
        else join_pushdown
    )
    if group_by.attribute == "name":
        table_name = runs_table_name
        col = "name"
        # Add pushdown if we're not using a filtered runs CTE.
        conditions = []
        if runs_table_name in ("runs", "runs_history"):
            conditions.append(runs_filtered_where.replace("WHERE", ""))
        conditions.append(_filter_generic_run_names_where())
        where = "AND " + " AND ".join(conditions)
        params: dict = {}
    elif group_by.attribute == "run_type":
        table_name = runs_table_name
        col = "run_type"
        where = (
            "AND " + runs_filtered_where.replace("WHERE", "")
            if runs_table_name in ("runs", "runs_history")
            else ""
        )
        params = {}
    elif group_by.attribute == "tag":
        table_name = "runs_tags"
        col = "tag"
        where = join_pushdown + in_filtered_runs
        params = {}
    elif group_by.attribute == "metadata":
        table_name = "runs_metadata_kv"
        col = "value"
        where = (
            "AND key = {group_by_metadata_key__eq} " + join_pushdown + in_filtered_runs
        )
        params = {"group_by_metadata_key__eq": group_by.path}
    else:
        raise ValueError(...)
    return {"table_name": table_name, "col": col, "where": where, "params": params}


def _parse_bucketed_ts_and_group_by(
    table_name: str,
    bucketed_ts: bool,
    bucketed_stride_unit: str,
    group_by: schemas.RunStatsGroupBy | None,
    in_filtered_runs: str,
    runs_table_name: str,
    join_pushdown: str,
    *,
    runs_filtered_where: str = "",
    can_skip_filtered_runs_cte: bool = False,
    ts_col: str | None = None,
) -> tuple[str, str, dict | None]:
    if group_by:
        parsed_group_by = _parse_group_by(
            group_by,
            join_pushdown=join_pushdown,
            runs_table_name=runs_table_name,
            in_filtered_runs=in_filtered_runs if not can_skip_filtered_runs_cte else "",
            runs_filtered_where=runs_filtered_where,
        )
    else:
        parsed_group_by = None
    select = ""
    if bucketed_ts:
        if ts_col:
            select = bucketed_ts_select_custom_column.format(col=ts_col).format(
                table_name=table_name, stride_unit=bucketed_stride_unit
            )
        else:
            select = bucketed_ts_select.format(
                table_name=table_name, stride_unit=bucketed_stride_unit
            )
    if parsed_group_by:
        if parsed_group_by["table_name"] == table_name:
            col = parsed_group_by["col"]
        else:
            col = "group_name"
        select += f"""
{table_name}.{col} as group_name,"""

    if bucketed_ts and parsed_group_by:
        group_by_clause = f"""
GROUP BY ts, group_name
ORDER BY group_name, ts ASC
WITH FILL FROM toDateTime({{start_time}})
         TO   COALESCE(toDateTime({{end_time}}), now())
         STEP INTERVAL {{stride_value}} {bucketed_stride_unit}
"""
    elif bucketed_ts:
        group_by_clause = bucketed_ts_group_by.format(
            bucketed_stride_unit=bucketed_stride_unit
        )
    elif parsed_group_by:
        group_by_clause = """
GROUP BY group_name
"""
    else:
        group_by_clause = ""
    return select, group_by_clause, parsed_group_by


def _join_on(
    first: str, second: str, first_id_col: str = "run_id", second_id_col: str = "run_id"
) -> str:
    return f"""
{first}.tenant_id = {second}.tenant_id AND
{first}.session_id = {second}.session_id AND
{first}.is_root = {second}.is_root AND
{first}.start_time = {second}.start_time AND
{first}.{first_id_col} = {second}.{second_id_col}"""


async def _fetch_stats(
    query: str,
    params: dict,
    *,
    name: str,
    bucketed_ts: bool,
    group_by: schemas.RunStatsGroupBy | None = None,
    can_skip_join_to_runs: bool = False,
) -> dict | list[dict]:
    ch_cluster, stats_timeout, stats_max_threads = _clickhouse_stats_settings(
        params.get("tenant_id__eq", None)
    )
    async with clickhouse_client(ch_cluster) as ch:
        name = f"fetch_{name}"
        if bucketed_ts:
            name += "_bucketed"
        if group_by:
            name += "_grouped"
        if can_skip_join_to_runs:
            name += "_simple"
        fetch = ch.fetch if bucketed_ts or group_by else ch.fetchrow
        result = await fetch(
            name,
            query,
            params=params,
            with_timeout=True,
            query_timeout_when_enabled=stats_timeout,
            max_threads=stats_max_threads,
        )
        if isinstance(result, list):
            return [dict(row) for row in result]
        else:
            return dict(result)


def _top_k_groups_cte(parsed_group_by: dict) -> str:
    trimmed_where = parsed_group_by["where"].lstrip(" ")
    if trimmed_where.startswith("AND "):
        trimmed_where = trimmed_where[len("AND ") :]
    operator = (
        "PREWHERE"
        if not parsed_group_by["table_name"].startswith("filtered_")
        else "WHERE"
    )
    top_k_where = (operator + "\n" + trimmed_where) if trimmed_where else ""
    return f"""
        top_k_groups AS (
            SELECT 
                arrayJoin(topK({GROUP_BY_TOP_K})({parsed_group_by["col"]})) AS group_name
            FROM {parsed_group_by["table_name"]}
            {top_k_where}
        ),
        """


def _top_k_groups_rows_cte(parsed_group_by: dict) -> str:
    top_k_groups_cte_str = _top_k_groups_cte(parsed_group_by)
    trimmed_where = parsed_group_by["where"].lstrip(" ")
    if trimmed_where.startswith("AND "):
        trimmed_where = trimmed_where[len("AND ") :]
    return f"""
        {top_k_groups_cte_str}
        topk_runs_with_group_name AS (
            SELECT
                tenant_id,
                session_id,
                is_root,
                start_time,
                run_id,
                {parsed_group_by["col"]}
            FROM {parsed_group_by["table_name"]}
            WHERE
                {trimmed_where}
                AND {parsed_group_by["col"]} IN (SELECT group_name FROM top_k_groups)
        )
    """


def _top_k_agg_merge_tree_enabled(tenant_id: str) -> bool:
    return (
        settings.FF_USE_TOPK_AGGREGATED_MERGE_TREE
        or tenant_id in settings.TOPK_AGGREGATED_MERGE_TREE_PROD_TENANT_IDS
    )


def _session_agg_enabled(tenant_id: str) -> bool:
    return (
        settings.STATS_USE_SESSION_AGGREGATION
        or tenant_id in settings.STATS_AGGREGATION_TENANT_IDS
    )


def _remove_prefix(s: str, prefix: str) -> str:
    if s.strip().startswith(prefix):
        return s.strip()[len(prefix) :]
    return s


@functools.lru_cache()
def _filter_generic_run_names_where() -> str:
    names_to_skip = ", ".join(f"'{x}'" for x in LANGCHAIN_GENERIC_RUN_NAMES)
    return f"(name NOT IN [{names_to_skip}]) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')"
