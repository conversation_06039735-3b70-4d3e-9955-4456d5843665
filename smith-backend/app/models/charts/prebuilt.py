"""
Definitions for prebuilt, session-scoped charts
"""


def get_tracing_project_prebuilt_dashboard() -> dict:
    return {
        "sub_sections": [
            {
                "title": "Traces",
                "description": "Trace data over time",
                "index": 0,
                "id": "sub-section-traces",
                "charts": [
                    {
                        "id": "chart-trace-count",
                        "title": "Trace Count",
                        "description": "Total number of traces over time",
                        "index": 0,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Success",
                                "filters": {
                                    "filter": 'and(eq(is_root, true), eq(status, "success"))',
                                },
                                "metric": "run_count",
                                "id": "series-success-count",
                            },
                            {
                                "name": "Error",
                                "filters": {
                                    "filter": 'and(eq(is_root, true), eq(status, "error"))',
                                },
                                "metric": "run_count",
                                "id": "series-error-count",
                            },
                        ],
                    },
                    {
                        "id": "chart-trace-latency",
                        "title": "Trace Latency",
                        "description": "Trace latency percentiles over time",
                        "index": 1,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "P50",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "latency_p50",
                                "id": "series-p50-latency",
                            },
                            {
                                "name": "P99",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "latency_p99",
                                "id": "series-p99-latency",
                            },
                        ],
                    },
                    {
                        "id": "chart-error-rate-per-run",
                        "title": "Trace Error Rate",
                        "description": "Percent of traces that errored over time",
                        "index": 2,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Traces",
                                "metric": "error_rate",
                                "filters": {"filter": "eq(is_root, true)"},
                                "id": "series-error-rate-per-run",
                            }
                        ],
                    },
                ],
            },
            {
                "title": "LLM Calls",
                "description": "LLM call metrics over time",
                "index": 1,
                "id": "sub-section-latency",
                "charts": [
                    {
                        "id": "chart-llm-call-count",
                        "title": "LLM Count",
                        "description": "Number of LLM calls over time",
                        "index": 1,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Success",
                                "filters": {
                                    "filter": 'and(eq(run_type, "llm"), eq(status, "success"))',
                                },
                                "metric": "run_count",
                                "id": "series-llm-call-success-count",
                            },
                            {
                                "name": "Error",
                                "filters": {
                                    "filter": 'and(eq(run_type, "llm"), eq(status, "error"))',
                                },
                                "metric": "run_count",
                                "id": "series-llm-call-error-count",
                            },
                        ],
                    },
                    {
                        "id": "chart-llm-call-latency",
                        "title": "LLM Latency",
                        "description": "LLM call latency percentiles over time",
                        "index": 1,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "P50",
                                "filters": {
                                    "filter": 'eq(run_type, "llm")',
                                },
                                "metric": "latency_p50",
                                "id": "series-llm-p50-latency",
                            },
                            {
                                "name": "P99",
                                "filters": {
                                    "filter": 'eq(run_type, "llm")',
                                },
                                "metric": "latency_p99",
                                "id": "series-llm-p99-latency",
                            },
                        ],
                    },
                ],
            },
            {
                "title": "Cost & Tokens",
                "description": "Cost and token usage metrics over time",
                "index": 2,
                "id": "sub-section-cost",
                "charts": [
                    {
                        "id": "chart-total-cost",
                        "title": "Total Cost",
                        "description": "Total cost over time",
                        "index": 0,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Total",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "total_cost",
                                "id": "series-total-cost",
                            }
                        ],
                    },
                    {
                        "id": "chart-cost-per-trace",
                        "title": "Cost per Trace",
                        "description": "Median cost per trace",
                        "index": 1,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "P50",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "cost_p50",
                                "id": "series-p50-cost",
                            },
                            {
                                "name": "P99",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "cost_p99",
                                "id": "series-p99-cost",
                            },
                        ],
                    },
                    {
                        "id": "chart-completion-token-count",
                        "title": "Output Tokens",
                        "description": "Total output tokens over time",
                        "index": 4,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Output",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "completion_tokens",
                                "id": "series-completion-token-count",
                            }
                        ],
                    },
                    {
                        "id": "chart-completion-tokens-per-trace",
                        "title": "Output Tokens per Trace",
                        "description": "Output tokens used per trace over time",
                        "index": 5,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "P50",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "completion_tokens_p50",
                                "id": "series-median-completion-tokens-per-trace",
                            },
                            {
                                "name": "P99",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "completion_tokens_p99",
                                "id": "series-p99-completion-tokens-per-trace",
                            },
                        ],
                    },
                    {
                        "id": "chart-input-token-count",
                        "title": "Input Tokens",
                        "description": "Total input tokens over time",
                        "index": 6,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Input",
                                "metric": "prompt_tokens",
                                "id": "series-prompt-token-count",
                            }
                        ],
                    },
                    {
                        "id": "chart-input-tokens-per-trace",
                        "title": "Input Tokens per Trace",
                        "description": "Input tokens used per trace over time",
                        "index": 7,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "P50",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "prompt_tokens_p50",
                                "id": "series-median-input-tokens-per-trace",
                            },
                            {
                                "name": "P99",
                                "filters": {
                                    "filter": "eq(is_root, true)",
                                },
                                "metric": "prompt_tokens_p99",
                                "id": "series-p99-input-tokens-per-trace",
                            },
                        ],
                    },
                ],
            },
            {
                "title": "Tools",
                "description": "Tool calling metrics over time",
                "index": 3,
                "id": "sub-section-tools",
                "charts": [
                    {
                        "id": "chart-tools-run-count",
                        "title": "Run Count by Tool",
                        "description": "Tool run counts over time",
                        "index": 0,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Run Count",
                                "filters": {"filter": 'eq(run_type, "tool")'},
                                "metric": "run_count",
                                "group_by": {"attribute": "name"},
                                "id": "chart-tools-run-count-series-run-count",
                            },
                        ],
                    },
                    {
                        "id": "chart-tools-latency",
                        "title": "Median Latency by Tool",
                        "description": "Median tool latency over time",
                        "index": 1,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "P50",
                                "filters": {"filter": 'eq(run_type, "tool")'},
                                "metric": "latency_p50",
                                "group_by": {"attribute": "name"},
                                "id": "chart-tools-latency-series-p50-latency",
                            },
                        ],
                    },
                    {
                        "id": "chart-tools-error-rate",
                        "title": "Error Rate by Tool",
                        "description": "Tool error rate over time",
                        "index": 2,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Error Rate",
                                "filters": {"filter": 'eq(run_type, "tool")'},
                                "metric": "error_rate",
                                "group_by": {"attribute": "name"},
                                "id": "chart-tool-error-rate-series-error-rate",
                            },
                        ],
                    },
                ],
            },
            {
                "title": "Run Types",
                "description": "Run metrics over time",
                "index": 4,
                "id": "sub-section-runs",
                "charts": [
                    {
                        "id": "chart-runs-run-count",
                        "title": "Run Count by Name, depth=1",
                        "description": "Run counts by name over time. Filtered to runs that occur at depth=1 within a trace, where depth=0 is the root run.",
                        "index": 0,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Run Count",
                                "metric": "run_count",
                                "group_by": {"attribute": "name"},
                                "id": "chart-runs-run-count-series-run-count",
                                "filters": {
                                    "filter": "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 1))"
                                },
                            },
                        ],
                    },
                    {
                        "id": "chart-runs-latency",
                        "title": "Median Latency by Run Name, depth=1",
                        "description": "Median run latency over time. Filtered to runs that occur at depth=1 within a trace, where depth=0 is the root run.",
                        "index": 1,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "P50",
                                "metric": "latency_p50",
                                "group_by": {"attribute": "name"},
                                "id": "chart-runs-latency-series-p50-latency",
                                "filters": {
                                    "filter": "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 1))"
                                },
                            },
                        ],
                    },
                    {
                        "id": "chart-runs-error-rate",
                        "title": "Error Rate by Run Name, depth=1",
                        "description": "Run error rate over time. Filtered to runs that occur at depth=1 within a trace, where depth=0 is the root run.",
                        "index": 2,
                        "chart_type": "line",
                        "series": [
                            {
                                "name": "Error Rate",
                                "metric": "error_rate",
                                "group_by": {"attribute": "name"},
                                "id": "chart-runs-error-rate-series-error-rate",
                                "filters": {
                                    "filter": "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 1))"
                                },
                            },
                        ],
                    },
                ],
            },
        ]
    }
