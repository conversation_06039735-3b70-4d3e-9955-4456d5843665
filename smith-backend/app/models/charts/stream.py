import asyncio
import copy
import uuid
from datetime import datetime, timezone
from typing import Any, Async<PERSON><PERSON><PERSON>, cast

import structlog
from fastapi import HTT<PERSON><PERSON>xception
from lc_config.settings import shared_settings as settings
from lc_database.database import asyncpg_pool

from app import schemas
from app.api.auth.schemas import AuthInfo, OrgAuthInfo

logger = structlog.get_logger(__name__)


async def stream_prebuilt_dashboard_for_session(
    session_id: uuid.UUID,
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo,
) -> AsyncIterator[list[dict]]:
    from app.models.charts.fetch import (
        _group_by_to_filters,
        _prebuilt_sub_sections,
        combine_series_filters,
    )

    async with asyncpg_pool() as db:
        session_name = await db.fetchrow(
            """
            SELECT name from tracer_session
            WHERE id=$1
            AND tenant_id=$2
            AND reference_dataset_id IS NULL
            """,
            session_id,
            auth.tenant_id,
        )
        if session_name is None:
            raise HTTPException(
                status_code=404,
                detail="Project doesn't exist or is an experiment.",
            )
        session_name = session_name["name"]

    sub_sections = await _prebuilt_sub_sections(
        auth, request, session_id, use_cache=True
    )

    charts: list = [c for s in sub_sections for c in s["charts"]]
    chart_id_to_chart = {chart["id"]: chart for chart in charts}

    sub_section_chart_index = {}  # {chart_id: (sub_section_idx, chart_idx_in_sub)}

    # Build the full sub_sections inline for the top-level dashboard
    formatted_sub_sections = []
    for ss_idx, sub in enumerate(sub_sections):
        filtered_charts: list[dict] = []
        for chart in sub["charts"]:
            chart["data"] = []
            sub_section_chart_index[chart["id"]] = (ss_idx, len(filtered_charts))
            filtered_charts.append(chart)

        formatted_sub_sections.append(
            {
                "title": sub["title"],
                "description": sub.get("description"),
                "index": sub["index"],
                "id": sub["id"],
                "charts": filtered_charts,
            }
        )

    all_series_obj: list[schemas.CustomChartSeries] = []
    series_id_to_chart_id = {}
    for chart in charts:
        new_series = []
        for s in chart["series"]:
            if s.get("group_by"):
                s["group_by"]["set_by"] = "series"
            elif request.group_by:
                s["group_by"] = {**request.group_by.model_dump(), "set_by": "section"}
            else:
                pass
            s["filters"] = combine_series_filters(
                s.get("filters"), chart.get("common_filters")
            )
            all_series_obj.append(schemas.CustomChartSeries(**s))
            series_id_to_chart_id[s["id"]] = chart["id"]
            # If group by is applied, we'll dynamically generate the series based on
            # the returned groups.
            if not s.get("group_by"):
                new_series.append(s)
        chart["series"] = new_series

    # Single yield with complete dashboard + sub_sections in one op
    yield [
        {
            "op": "add",
            "path": "",
            "value": {
                "title": f"Prebuilt Dashboard for {session_name}",
                "description": f"Prebuilt Dashboard for {session_name}",
                "session_id": session_id,
                "id": session_id,
                "charts": [],
                "sub_sections": copy.deepcopy(formatted_sub_sections),
            },
        }
    ]

    async for ops in stream_chart_data_for_series(
        auth, all_series_obj, request, [], schemas.AccessScope.workspace, use_cache=True
    ):
        for op in ops:
            datapoint = schemas.CustomChartsDataPoint(**op["value"])
            chart_id = series_id_to_chart_id[datapoint.series_id]
            chart = chart_id_to_chart[chart_id]
            ss_idx, _ = sub_section_chart_index[chart_id]

            # If group_by: dynamically add the series to the chart
            if datapoint.group is not None:
                new_sid = datapoint.series_id + ":" + datapoint.group
                if new_sid not in [s["id"] for s in chart["series"]]:
                    orig_series = next(
                        s for s in all_series_obj if str(s.id) == datapoint.series_id
                    )
                    group_series = orig_series.model_dump()
                    group_series["id"] = new_sid
                    group_series["filters"] = combine_series_filters(
                        group_series["filters"],
                        _group_by_to_filters(
                            cast(schemas.RunStatsGroupBy, orig_series.group_by),
                            datapoint.group,
                        ),
                    )
                    chart["series"].append(group_series)

                    _, chart_idx = sub_section_chart_index[chart["id"]]
                    yield [
                        {
                            "op": "add",
                            "path": f"/sub_sections/{ss_idx}/charts/{chart_idx}/series/-",
                            "value": group_series,
                        }
                    ]
                datapoint.series_id = new_sid

            # Yield the datapoint
            ss_idx, chart_idx = sub_section_chart_index[chart_id]
            yield [
                {
                    "op": "add",
                    "path": f"/sub_sections/{ss_idx}/charts/{chart_idx}/data/-",
                    "value": datapoint,
                }
            ]


async def stream_chart_data_for_series(
    auth: AuthInfo | OrgAuthInfo,
    series: list[schemas.CustomChartSeries],
    request: schemas.CustomChartsSectionRequest,
    common_filters: list[schemas.CustomChartSeriesFilters | None],
    access_scope: schemas.AccessScope,
    use_cache: bool = False,
) -> AsyncIterator[list[dict]]:
    """
    Stream chart data for multiple series, yielding JSON patch updates as they become available.
    Each update adds a new data point to the result list.
    """
    from app.models.charts.fetch import chart_metric_to_stats_select

    # In an organization context, fetch historical stats to include deleted traces
    data_source_type = (
        schemas.RunsFilterDataSourceTypeEnum.historical
        if access_scope == schemas.AccessScope.organization
        else schemas.RunsFilterDataSourceTypeEnum.current
    )
    # chop the microseconds to avoid an extra bucket at the end
    start_time = request.start_time.replace(microsecond=0)
    end_time = (request.end_time or datetime.now(timezone.utc)).replace(microsecond=0)

    # Create a semaphore to limit concurrent operations
    semaphore = asyncio.Semaphore(settings.CHARTS_STATS_SEMAPHORE)

    # Process each series one at a time to stream results
    common_filters_ = common_filters or (None for _ in series)
    tasks = [
        asyncio.create_task(
            _fetch_stats_include_error(
                s,
                cf,
                request,
                auth,
                use_cache,
                semaphore,
                data_source_type,
                start_time,
                end_time,
            )
        )
        for s, cf in zip(series, common_filters_)
    ]

    for task in asyncio.as_completed(tasks):
        s, result = await task
        if isinstance(result, Exception):
            # Log error but continue processing other series
            logger.error(
                "Error fetching stats for series",
                series=s.model_dump(),
                exc_info=result,
            )
            continue
        sid = str(s.id)
        if not s.group_by:
            result = {None: result}
        for group, bucket in result.items():
            if group:
                group = group.strip('"').strip("'")
            for ts, stats in bucket.items():
                select = chart_metric_to_stats_select(s.metric).value
                val = stats[select]
                if select == schemas.RunStatsSelect.feedback_stats:
                    feedback_val = val.get(s.feedback_key) or {
                        "n": 0,
                        "avg": None,
                        "values": {},
                    }
                    val = {s.feedback_key: feedback_val}

                dp = {
                    "series_id": sid,
                    "timestamp": ts.isoformat() if isinstance(ts, datetime) else ts,
                    "value": val,
                    "group": group,
                }

                yield [{"op": "add", "path": "/-", "value": dp}]


async def _fetch_stats_include_error(
    s: schemas.CustomChartSeries, *args: Any, **kwargs: Any
) -> tuple[schemas.CustomChartSeries, dict | Exception]:
    from app.models.charts.fetch import fetch_stats

    try:
        return s, (await fetch_stats(s, *args, **kwargs))
    except Exception as e:
        return s, e
