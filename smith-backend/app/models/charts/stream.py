import asyncio
import copy
import uuid
from collections import defaultdict
from datetime import datetime, timezone
from typing import Any, Async<PERSON><PERSON><PERSON>, cast

import structlog
from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_database.database import asyncpg_pool

from app import schemas
from app.api.auth.schemas import AuthInfo, OrgAuthInfo
from app.models.charts.cached_fetch import (
    cached_fetch_top_k_feedback_key_types,
    fetch_top_k_feedback_key_types_for_prebuilt_dashboard,
)
from app.models.charts.prebuilt import get_tracing_project_prebuilt_dashboard

logger = structlog.get_logger(__name__)


async def stream_prebuilt_dashboard_for_session(
    session_id: uuid.UUID,
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo,
) -> AsyncIterator[list[dict]]:
    async with asyncpg_pool() as db:
        session_name = await db.fetchrow(
            """
            SELECT name from tracer_session
            WHERE id=$1
            AND tenant_id=$2
            AND reference_dataset_id IS NULL
            """,
            session_id,
            auth.tenant_id,
        )
        if session_name is None:
            raise HTTPException(
                status_code=404,
                detail="Project doesn't exist or is an experiment.",
            )
        session_name = session_name["name"]

    sub_sections = await _prebuilt_sub_sections(
        auth, request, session_id, use_cache=True
    )
    dashboard_definition = {
        "title": session_name,
        "description": f"Prebuilt dashboard for tracing project: {session_name}",
        "session_id": session_id,
        "id": session_id,
        "charts": [],
        "sub_sections": sub_sections,
    }

    async for op in _stream_dashboard(request, auth, dashboard_definition):
        yield op


async def _stream_dashboard(
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo,
    dashboard_definition: dict,
) -> AsyncIterator[list[dict]]:
    from app.models.charts.fetch import combine_series_filters

    sub_sections = dashboard_definition.get("sub_sections", [])
    charts: list = dashboard_definition.get("charts", []) or [
        c for s in sub_sections for c in s["charts"]
    ]

    chart_id_to_chart = {chart["id"]: chart for chart in charts}
    chart_id_to_path = {}
    for i, sub_section in enumerate(sub_sections):
        for j, chart in enumerate(sub_section["charts"]):
            chart_id_to_path[chart["id"]] = f"/sub_sections/{i}/charts/{j}"
    for i, chart in enumerate(charts):
        chart["data"] = []
        if chart["id"] not in chart_id_to_path:
            chart_id_to_path[chart["id"]] = f"/charts/{i}"

    all_series_obj: list[schemas.CustomChartSeries] = []
    series_id_to_chart_id = {}
    expected_queries_per_chart = defaultdict(list)
    for chart in charts:
        static_series = []
        for s in chart["series"]:
            if s.get("group_by"):
                s["group_by"]["set_by"] = "series"
            elif request.group_by:
                s["group_by"] = {**request.group_by.model_dump(), "set_by": "section"}
            else:
                pass
            s["filters"] = combine_series_filters(
                s.get("filters"), chart.get("common_filters")
            )
            all_series_obj.append(schemas.CustomChartSeries(**s))
            series_id_to_chart_id[s["id"]] = chart["id"]
            expected_queries_per_chart[chart["id"]].append(s["id"])
            # If group by is applied or the metric is feedback values,
            # we'll dynamically generate the series based on the returned
            # groups/categories.
            if not s.get("group_by") and not s.get("metric") == "feedback_values":
                static_series.append(s)
        chart["series"] = static_series

    # Yield dashboard skeleton
    yield [
        {
            "op": "add",
            "path": "",
            "value": {
                **dashboard_definition,
                **{
                    "sub_sections": copy.deepcopy(sub_sections),
                    "charts": copy.deepcopy(charts if not sub_sections else []),
                },
            },
        }
    ]

    series_id_to_series_obj = {s.id: s for s in all_series_obj}
    series_order = [s.id for s in all_series_obj]
    completed_series_queries = set()
    async for series_data in stream_chart_data_for_series(
        auth, all_series_obj, request, [], schemas.AccessScope.workspace, use_cache=True
    ):
        # All the datapoints have the same series_id
        series_id = series_data[0].series_id
        completed_series_queries.add(series_id)
        chart_id = series_id_to_chart_id[series_id]
        chart = chart_id_to_chart[chart_id]
        chart_path = chart_id_to_path[chart_id]

        series_data = _unpack_feedback_datapoints(
            series_data,
            chart_id_to_chart,
            series_id_to_chart_id,
            series_id_to_series_obj,
        )

        # If group_by: dynamically add the series to the chart
        group_to_series_id: dict = {}
        for sid, group in set(
            (dp.series_id, dp.group) for dp in series_data if dp.group is not None
        ):
            new_sid: Any = sid + ":" + group
            orig_series_obj = series_id_to_series_obj[sid]
            group_to_series_id[(sid, group)] = new_sid
            if new_sid not in [s["id"] for s in chart["series"]]:
                group_series = orig_series_obj.model_dump()
                group_series["id"] = new_sid
                group_series["filters"] = combine_series_filters(
                    group_series["filters"],
                    _group_by_to_filters(
                        cast(schemas.RunStatsGroupBy, orig_series_obj.group_by),
                        group,
                    ),
                )
                chart["series"].append(group_series)
        chart["series"] = sorted(
            chart["series"],
            key=lambda s: (
                series_order.index(s["id"].split(":")[0]),
                *s["id"].split(":")[1:],
            ),
        )

        for datapoint in series_data:
            if new_sid := group_to_series_id.get(
                (datapoint.series_id, datapoint.group)
            ):
                datapoint.series_id = new_sid

        chart["data"].extend(series_data)

        # Only yield chart data once we've collected all series in the chart
        if all(
            sid in completed_series_queries
            for sid in expected_queries_per_chart[chart["id"]]
        ):
            ops = []
            # Update series if there are any dynamic series.
            if any(":" in s["id"] for s in chart["series"]):
                ops.append(
                    {
                        "op": "replace",
                        "path": f"{chart_path}/series",
                        "value": chart["series"],
                    }
                )
            ops.append(
                {"op": "replace", "path": f"{chart_path}/data", "value": chart["data"]}
            )
            yield ops


async def stream_chart_data_for_series(
    auth: AuthInfo | OrgAuthInfo,
    series: list[schemas.CustomChartSeries],
    request: schemas.CustomChartsSectionRequest,
    common_filters: list[schemas.CustomChartSeriesFilters | None],
    access_scope: schemas.AccessScope,
    use_cache: bool = False,
) -> AsyncIterator[list[schemas.CustomChartsDataPoint]]:
    """Yield datapoints for a series as they become available."""
    from app.models.charts.fetch import chart_metric_to_stats_select

    # chop the microseconds to avoid an extra bucket at the end
    if request.start_time is None:
        raise HTTPException(
            status_code=404,
            detail="start_time must be set.",
        )
    start_time = request.start_time.replace(microsecond=0)
    end_time = (request.end_time or datetime.now(timezone.utc)).replace(microsecond=0)

    # Create a semaphore to limit concurrent operations
    semaphore = asyncio.Semaphore(settings.CHARTS_STATS_SEMAPHORE)

    # Process each series one at a time to stream results
    common_filters_ = common_filters or (None for _ in series)
    tasks = [
        asyncio.create_task(
            _fetch_stats_include_error(
                s,
                cf,
                request,
                auth,
                use_cache,
                semaphore,
                schemas.RunsFilterDataSourceTypeEnum.historical,
                start_time,
                end_time,
            )
        )
        for s, cf in zip(series, common_filters_)
    ]

    for task in asyncio.as_completed(tasks):
        s, result = await task
        if isinstance(result, Exception):
            # Log error but continue processing other series
            logger.error(
                "Error fetching stats for series",
                series=s.model_dump(),
                exc_info=result,
            )
            continue
        # Same series id for each datapoint per yield
        sid = str(s.id)
        series_data = []
        if not s.group_by:
            result = {None: result}
        for group, bucket in result.items():
            if group:
                group = group.strip('"').strip("'")
            for ts, stats in bucket.items():
                select = chart_metric_to_stats_select(s.metric).value
                val = stats[select]
                if select == schemas.RunStatsSelect.feedback_stats:
                    feedback_val = val.get(s.feedback_key) or {
                        "n": 0,
                        "avg": None,
                        "values": {},
                    }
                    val = {s.feedback_key: feedback_val}

                dp = schemas.CustomChartsDataPoint(
                    series_id=sid,
                    timestamp=ts,
                    value=val,
                    group=group,
                )

                series_data.append(dp)

        yield series_data


async def _fetch_stats_include_error(
    s: schemas.CustomChartSeries, *args: Any, **kwargs: Any
) -> tuple[schemas.CustomChartSeries, dict | Exception]:
    from app.models.charts.fetch import fetch_stats

    try:
        return s, (await fetch_stats(s, *args, **kwargs))
    except Exception as e:
        return s, e


async def _prebuilt_sub_sections(
    auth: AuthInfo | OrgAuthInfo,
    request: schemas.CustomChartsRequestBase,
    session_id: uuid.UUID,
    use_cache: bool = False,
) -> list[dict]:
    # Fetch the top k feedback keys
    if request.start_time is None:
        raise HTTPException(
            status_code=404,
            detail="start_time must be set.",
        )
    if use_cache:
        rounded_start = request.start_time.replace(second=0, microsecond=0)
        rounded_end = (
            request.end_time.replace(second=0, microsecond=0)
            if request.end_time
            else None
        )
        feedback_key_types = await cached_fetch_top_k_feedback_key_types(
            auth.tenant_id, rounded_start, rounded_end, session_id, auth=auth
        )
    else:
        feedback_key_types = (
            await fetch_top_k_feedback_key_types_for_prebuilt_dashboard(
                auth, request.start_time, request.end_time, session_id
            )
        )

    # Add a chart and series for each feedback key
    feedback_charts = []
    for i, feedback_key_type in enumerate(feedback_key_types):
        feedback_key = feedback_key_type["key"]
        feedback_type = feedback_key_type["feedback_type"]
        metric = (
            "feedback_score_avg" if feedback_type == "numerical" else "feedback_values"
        )
        description = (
            f"{feedback_key} counts over time"
            if feedback_type == "categorical"
            else f"Average {feedback_key} score over time"
        )

        chart = {
            "id": f"chart-{feedback_key}",
            "title": f"{feedback_key}",
            "description": description,
            "chart_type": "line",
            "index": i,
            "series": [
                {
                    "id": f"series-{feedback_key}",
                    "name": "Avg",
                    "metric": metric,
                    "feedback_key": feedback_key,
                }
            ],
        }
        feedback_charts.append(chart)

    prebuilt_dashboard = get_tracing_project_prebuilt_dashboard()
    sub_sections: list[dict] = prebuilt_dashboard["sub_sections"] + [
        {
            "title": "Feedback Scores",
            "description": "Feedback scores over time",
            "index": len(prebuilt_dashboard["sub_sections"]),
            "id": "feedback",
            "charts": feedback_charts,
        }
    ]
    for sub_section in sub_sections:
        for chart in sub_section["charts"]:
            chart["common_filters"] = {"session": [session_id]}
    return sub_sections


def _unpack_feedback_datapoints(
    all_series_data: list[schemas.CustomChartsDataPoint],
    chart_id_to_chart: dict,
    series_id_to_chart_id: dict,
    series_id_to_series_obj: dict,
) -> list[schemas.CustomChartsDataPoint]:
    from app.models.charts.fetch import combine_series_filters

    categorical_feedback_keys_to_categories = defaultdict(set)
    for datapoint in all_series_data:
        chart = chart_id_to_chart[series_id_to_chart_id[datapoint.series_id]]
        if isinstance(datapoint.value, dict) and (
            categories := list(list(datapoint.value.values())[0]["values"].keys())
        ):
            # should only be one top level key which represents the feedback key
            feedback_key = list(datapoint.value.keys())[0]
            categorical_feedback_keys_to_categories[feedback_key].update(categories)
            for category in categories:
                new_sid = datapoint.series_id + ":" + category
                chart["series"] = [
                    s for s in chart["series"] if s["id"] != datapoint.series_id
                ]
                if new_sid not in [s["id"] for s in chart["series"]]:
                    orig_series = series_id_to_series_obj[datapoint.series_id]
                    category_series = orig_series.model_dump()
                    category_series["name"] = category
                    category_series["id"] = new_sid
                    category_series["metric"] = "run_count"
                    category_series.pop("feedback_key")
                    category_series["filters"] = combine_series_filters(
                        category_series["filters"],
                        {
                            "filter": f'and(eq(feedback_key, "{feedback_key}"), eq(feedback_value, "{category}"))'
                        },
                    )
                    if not category_series.get("group_by"):
                        chart["series"].append(category_series)
                    series_id_to_series_obj[new_sid] = schemas.CustomChartSeries(
                        **category_series
                    )
    updated_datapoints = []
    for datapoint in all_series_data:
        chart = chart_id_to_chart[series_id_to_chart_id[datapoint.series_id]]
        if isinstance(datapoint.value, dict):
            # should only be one top level key which represents the feedback key
            feedback_key = list(datapoint.value.keys())[0]
            if category_set := categorical_feedback_keys_to_categories.get(
                feedback_key
            ):
                counts = {
                    **{c: 0 for c in category_set},
                    **datapoint.value[feedback_key]["values"],
                }
                for category, count in counts.items():
                    new_sid = datapoint.series_id + ":" + category
                    series_id_to_chart_id[new_sid] = chart["id"]
                    new_datapoint = datapoint.model_copy(
                        update={"value": count, "series_id": new_sid}
                    )
                    updated_datapoints.append(new_datapoint)
            else:
                datapoint.value = datapoint.value[feedback_key]["avg"]
                updated_datapoints.append(datapoint)
        else:
            updated_datapoints.append(datapoint)
    return updated_datapoints


def _group_by_to_filters(
    group_by: schemas.RunStatsGroupBy, group: str
) -> dict[str, str]:
    new_filters = {}
    if group_by.attribute == "name":
        new_filters["filter"] = f'eq(name, "{group}")'
    elif group_by.attribute == "run_type":
        new_filters["filter"] = f'eq(run_type, "{group}")'
    elif group_by.attribute == "tag":
        new_filters["filter"] = f'eq(tag, "{group}")'
    elif group_by.attribute == "metadata":
        new_filters["filter"] = (
            f'and(eq(metadata_key, "{group_by.path}"), eq(metadata_value, "{group}"))'
        )
    else:
        raise ValueError(...)
    return new_filters
