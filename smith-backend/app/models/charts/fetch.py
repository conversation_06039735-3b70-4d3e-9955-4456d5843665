import asyncio
import uuid
from datetime import datetime
from typing import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, cast

import jsonpatch
import structlog
from fastapi import HTTPException
from lc_database.database import asyncpg_pool, kwargs_to_pgpos
from pydantic import BaseModel

from app import config, schemas
from app.api.auth.schemas import AuthInfo, OrgAuthInfo
from app.memoize import redis_cache
from app.models.charts.cached_fetch import (
    cached_fetch_top_k_feedback_key_types,
    fetch_top_k_feedback_key_types_for_prebuilt_dashboard,
)
from app.models.charts.constants import MAX_SERIES_PER_FETCH
from app.models.charts.prebuilt import get_tracing_project_prebuilt_dashboard
from app.models.charts.stream import stream_chart_data_for_series
from app.models.charts.utils.validate import validate_chart_filters
from app.utils import gated_coro

logger = structlog.get_logger(__name__)


async def fetch_prebuilt_dashboard_for_session(
    session_id: uuid.UUID,
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo,
) -> schemas.CustomChartsSection:
    from app.models.charts.stream import stream_prebuilt_dashboard_for_session

    result = jsonpatch.apply_patch(
        {},
        [
            op
            async for ops in stream_prebuilt_dashboard_for_session(
                session_id, request, auth
            )
            for op in ops
        ],
    )

    return schemas.CustomChartsSection(**result)


async def fetch_custom_charts_sections(
    query_params: schemas.CustomChartsSectionsRequest,
    auth: AuthInfo | OrgAuthInfo,
    access_scope: schemas.AccessScope,
) -> Tuple[list[schemas.CustomChartsSectionResponse], int]:
    """Get all sections for the tenant, including the chart count for each section."""
    tenant_id = (
        auth.tenant_id if access_scope == schemas.AccessScope.workspace else None
    )
    tag_where_clause = (
        """
        AND s.id IN (
            SELECT t.resource_id
            FROM taggings t
            JOIN tag_values tv ON t.tag_value_id = tv.id
            JOIN tag_keys tk ON tv.tag_key_id = tk.id
            WHERE t.resource_type = 'dashboard'
            AND t.tag_value_id = ANY($tag_value_id::uuid[])
            AND tk.tenant_id = $tenant_id
            GROUP BY t.resource_id
        )
        """
        if query_params.tag_value_id and access_scope == schemas.AccessScope.workspace
        else ""
    )

    # Validate and sanitize sort_by parameter
    valid_sort_fields = {"modified_at", "created_at"}
    sort_by = (
        query_params.sort_by
        if query_params.sort_by in valid_sort_fields
        else "modified_at"
    )
    sort_order = "DESC" if query_params.sort_by_desc else "ASC"

    # Build the WHERE clauses based on access scope
    chart_scope_where = (
        "tenant_id = $tenant_id"
        if access_scope == schemas.AccessScope.workspace
        else "organization_id = $organization_id AND access_scope = 'organization'"
    )

    section_scope_where = (
        "s.tenant_id = $tenant_id"
        if access_scope == schemas.AccessScope.workspace
        else "s.organization_id = $organization_id AND s.access_scope = 'organization'"
    )

    scoped_kwargs = (
        {"tenant_id": tenant_id}
        if access_scope == schemas.AccessScope.workspace
        else {"organization_id": auth.organization_id}
    )

    query = f"""
            WITH section_chart_counts AS (
                SELECT
                    section_id,
                    COUNT(*) as chart_count
                FROM
                    custom_chart
                WHERE {chart_scope_where}
                GROUP BY
                    section_id
            )
            SELECT
                s.id,
                s.session_id,
                s.title,
                s.description,
                s.index,
                s.modified_at,
                s.created_at,
                COALESCE(scc.chart_count, 0) AS chart_count
            FROM
                custom_chart_section s
            LEFT JOIN
                section_chart_counts scc ON s.id = scc.section_id
            WHERE
                {section_scope_where}
                {tag_where_clause}
                {"AND s.id = ANY($ids::uuid[])" if query_params.ids else ""}
                {"AND s.title ILIKE $title_contains" if query_params.title_contains else ""}
            ORDER BY
                {sort_by} {sort_order}, s.id ASC
            LIMIT $limit OFFSET $offset;
        """

    query, params = kwargs_to_pgpos(
        query,
        {
            "access_scope": access_scope,
            "title_contains": f"%{query_params.title_contains}%"
            if query_params.title_contains
            else None,
            "limit": query_params.limit + 1,
            "offset": query_params.offset,
            "ids": query_params.ids,
            "tag_value_id": query_params.tag_value_id,
            **scoped_kwargs,
        },
    )

    async with asyncpg_pool() as db:
        sections = await db.fetch(
            query,
            *params,
        )

    return (
        [
            schemas.CustomChartsSectionResponse(**section)
            for section in sections[: query_params.limit]
        ],
        query_params.offset + len(sections),
    )


async def fetch_single_custom_chart_info(
    auth: AuthInfo | OrgAuthInfo,
    chart_id: uuid.UUID | str,
    access_scope: schemas.AccessScope,
) -> dict:
    """Get info for a chart, without the data"""
    tenant_id = (
        auth.tenant_id if access_scope == schemas.AccessScope.workspace else None
    )
    async with asyncpg_pool() as db:
        query = """
            SELECT
                c.id,
                c.title,
                c.description,
                c.chart_type,
                c.metadata,
                c.index,
                c.common_filters,
                (
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            'id', l.id,
                            'name', l.name,
                            'filters', l.filters,
                            'metric', l.metric,
                            'feedback_key', l.feedback_key,
                            'workspace_id', l.workspace_id,
                            'group_by', l.group_by
                        )
                    )
                    FROM custom_chart_series l
                    WHERE l.custom_chart_id = c.id
                ) AS series
            FROM
                custom_chart c
            WHERE
                c.id = $1 AND 
                CASE
                    WHEN $4 = 'workspace' THEN tenant_id = $2
                    ELSE organization_id = $3 AND access_scope = 'organization'
                END
        """
        row = await db.fetchrow(
            query, chart_id, tenant_id, auth.organization_id, access_scope
        )

    if not row:
        raise HTTPException(status_code=404, detail="Chart not found")

    return row


async def fetch_single_custom_chart(
    chart_id: uuid.UUID,
    charts_request: schemas.CustomChartsRequestInternal,
    auth: AuthInfo | OrgAuthInfo,
) -> schemas.SingleCustomChartResponse:
    """Get a single custom chart for the tenant."""
    row = await fetch_single_custom_chart_info(
        auth, chart_id, charts_request.access_scope
    )

    # Prepare the series and mapping
    all_series = [schemas.CustomChartSeries(**s) for s in (row["series"] or [])]
    all_common_filters = [
        schemas.CustomChartSeriesFilters(**row["common_filters"])
        if row.get("common_filters")
        else None
        for _ in all_series
    ]

    chart_data = await get_chart_data_for_series(
        auth,
        all_series,
        charts_request,
        all_common_filters,
        charts_request.access_scope,
    )

    return schemas.SingleCustomChartResponse(
        id=row["id"],
        title=row["title"],
        description=row["description"],
        chart_type=row["chart_type"],
        metadata=row["metadata"],
        index=row["index"],
        data=chart_data,
        series=all_series,
        common_filters=row["common_filters"],
    )


@redis_cache(ttl=config.settings.CHARTS_CACHE_TTL_SEC)
async def fetch_single_custom_chart_cached(
    chart_id: uuid.UUID,
    charts_request: schemas.CustomChartsRequestInternal,
    auth: AuthInfo | OrgAuthInfo,
) -> schemas.SingleCustomChartResponse:
    return await fetch_single_custom_chart(chart_id, charts_request, auth)


async def fetch_single_custom_chart_section(
    section_id: uuid.UUID,
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo | OrgAuthInfo,
    access_scope: schemas.AccessScope,
    include_chart_data: bool = True,
) -> schemas.CustomChartsSection:
    """Get a single section by ID including all of the charts including the data of the chart in that section."""
    tenant_id = (
        auth.tenant_id if access_scope == schemas.AccessScope.workspace else None
    )

    # Build the WHERE clauses based on access scope
    chart_scope_where = (
        "c.tenant_id = $tenant_id"
        if access_scope == schemas.AccessScope.workspace
        else "c.organization_id = $organization_id AND c.access_scope = 'organization'"
    )

    section_scope_where = (
        "s.tenant_id = $tenant_id"
        if access_scope == schemas.AccessScope.workspace
        else "s.organization_id = $organization_id AND s.access_scope = 'organization'"
    )
    scoped_kwargs = (
        {"tenant_id": tenant_id}
        if access_scope == schemas.AccessScope.workspace
        else {"organization_id": auth.organization_id}
    )

    async with asyncpg_pool() as db:
        query = f"""
            SELECT
                s.id AS id,
                s.title AS title,
                s.description AS description,
                s.index AS index,
                jsonb_agg(
                    jsonb_build_object(
                        'id', c.id,
                        'title', c.title,
                        'description', c.description,
                        'chart_type', c.chart_type,
                        'metadata', c.metadata,
                        'index', c.index,
                        'common_filters', c.common_filters,
                        'series', (
                            SELECT jsonb_agg(
                                jsonb_build_object(
                                    'id', l.id,
                                    'name', l.name,
                                    'filters', l.filters,
                                    'metric', l.metric,
                                    'feedback_key', l.feedback_key,
                                    'workspace_id', l.workspace_id,
                                    'group_by', l.group_by
                                )
                            )
                            FROM custom_chart_series l
                            WHERE l.custom_chart_id = c.id
                        )
                    )
                ) FILTER (WHERE c.id IS NOT NULL) AS charts
            FROM
                custom_chart_section s
            LEFT JOIN
                custom_chart c ON s.id = c.section_id AND {chart_scope_where}
            WHERE
                s.id = $section_id AND {section_scope_where}
            GROUP BY
                s.id;
        """

        query, params = kwargs_to_pgpos(
            query,
            {
                "section_id": section_id,
                "access_scope": access_scope,
                **scoped_kwargs,
            },
        )

        section = await db.fetchrow(query, *params)

    if not section:
        raise HTTPException(status_code=404, detail="Section not found")

    section = dict(section)

    # Process the charts and fetch their data
    charts = section.pop("charts") or []
    for chart in charts:
        chart["data"] = []

    # Optionally return early without querying for data
    if not include_chart_data:
        return schemas.CustomChartsSection(**section, charts=charts)

    all_series_obj: list[schemas.CustomChartSeries] = []
    series_id_to_chart_id = {}
    for chart in charts:
        new_series = []
        for s in chart["series"]:
            if s.get("group_by"):
                s["group_by"]["set_by"] = "series"
            elif request.group_by:
                s["group_by"] = {**request.group_by.model_dump(), "set_by": "section"}
            else:
                pass
            s["filters"] = combine_series_filters(
                s.get("filters"), chart.get("common_filters")
            )
            all_series_obj.append(schemas.CustomChartSeries(**s))
            series_id_to_chart_id[s["id"]] = chart["id"]
            # If group by is applied, we'll dynamically generate the series based on
            # the returned groups.
            if not s.get("group_by"):
                new_series.append(s)
        chart["series"] = new_series

    # Fetch chart data using the provided request parameters
    all_series_data = await get_chart_data_for_series(
        auth, all_series_obj, request, [], access_scope
    )
    chart_id_to_chart = {chart["id"]: chart for chart in charts}
    for datapoint in all_series_data:
        chart = chart_id_to_chart[series_id_to_chart_id[datapoint.series_id]]
        if datapoint.group is not None:
            new_sid = datapoint.series_id + ":" + datapoint.group
            if new_sid not in [s["id"] for s in chart["series"]]:
                orig_series = next(
                    s for s in all_series_obj if str(s.id) == datapoint.series_id
                )
                group_series = orig_series.model_dump()
                group_series["id"] = new_sid
                group_series["filters"] = combine_series_filters(
                    group_series["filters"],
                    _group_by_to_filters(
                        cast(schemas.RunStatsGroupBy, orig_series.group_by),
                        datapoint.group,
                    ),
                )
                chart["series"].append(group_series)
            datapoint.series_id = new_sid
        chart["data"].append(datapoint)

    return schemas.CustomChartsSection(**section, charts=charts)


async def fetch_chart_preview(
    charts_request: schemas.CustomChartPreviewRequestInternal,
    auth: AuthInfo | OrgAuthInfo,
) -> schemas.SingleCustomChartResponseBase:
    # Prepare the series and mapping
    all_series = charts_request.chart.series
    await validate_chart_filters(
        auth,
        [s.filters for s in charts_request.chart.series],
        charts_request.chart.common_filters,
        charts_request.access_scope,
    )

    chart_data = await get_chart_data_for_series(
        auth,
        all_series,
        charts_request.bucket_info,
        [charts_request.chart.common_filters for _ in all_series],
        charts_request.access_scope,
    )

    return schemas.SingleCustomChartResponseBase(
        data=chart_data,
    )


@redis_cache(ttl=config.settings.CHARTS_CACHE_TTL_SEC)
async def fetch_chart_preview_cached(
    charts_request: schemas.CustomChartPreviewRequestInternal,
    auth: AuthInfo | OrgAuthInfo,
) -> schemas.SingleCustomChartResponseBase:
    return await fetch_chart_preview(charts_request, auth)


async def fetch_custom_charts(
    charts_request: schemas.CustomChartsRequestInternal,
    auth: AuthInfo | OrgAuthInfo,
) -> Tuple[schemas.CustomChartsResponse, int]:
    """Get all charts for the tenant."""
    tenant_id = (
        auth.tenant_id
        if charts_request.access_scope == schemas.AccessScope.workspace
        else None
    )
    tag_where_clause = (
        """
                AND s.id IN (
                    SELECT t.resource_id
                    FROM taggings t
                    JOIN tag_values tv ON t.tag_value_id = tv.id
                    JOIN tag_keys tk ON tv.tag_key_id = tk.id
                    WHERE t.resource_type = 'dashboard'
                    AND t.tag_value_id = ANY($tag_value_id::uuid[])
                    AND tk.tenant_id = $tenant_id
                    GROUP BY t.resource_id
                    HAVING COUNT(*) = (
                        SELECT COUNT(*) FROM unnest($tag_value_id::uuid[])
                    )
                )
                """
        if charts_request.tag_value_id
        and charts_request.access_scope == schemas.AccessScope.workspace
        else ""
    )

    async with asyncpg_pool() as db:
        query = f"""
            SELECT
                s.id,
                s.title,
                s.description,
                s.index,
                COALESCE(
                    jsonb_agg(
                        CASE
                            WHEN c.id IS NOT NULL THEN
                                jsonb_build_object(
                                    'id', c.id,
                                    'title', c.title,
                                    'description', c.description,
                                    'chart_type', c.chart_type,
                                    'metadata', c.metadata,
                                    'index', c.index,
                                    'common_filters', c.common_filters,
                                    'series', (
                                        SELECT jsonb_agg(
                                            jsonb_build_object(
                                                'id', l.id,
                                                'name', l.name,
                                                'filters', l.filters,
                                                'metric', l.metric,
                                                'feedback_key', l.feedback_key,
                                                'workspace_id', l.workspace_id,
                                                'group_by', l.group_by
                                            )
                                        )
                                        FROM custom_chart_series l
                                        WHERE l.custom_chart_id = c.id
                                    )
                                )
                        END
                    ) FILTER (WHERE c.id IS NOT NULL),
                    '[]'::jsonb
                ) AS charts
            FROM
                custom_chart_section s
            LEFT JOIN
                custom_chart c ON s.id = c.section_id
            WHERE
                CASE
                    WHEN $access_scope = 'workspace' THEN s.tenant_id = $tenant_id
                    ELSE s.organization_id = $organization_id AND s.access_scope = 'organization'
                END
                AND s.index > $index
                {tag_where_clause}
            GROUP BY
                s.id
            ORDER BY
                s.index ASC;
        """
        params: dict[str, Any] = {
            "tenant_id": tenant_id,
            "organization_id": auth.organization_id,
            "access_scope": charts_request.access_scope,
            "index": charts_request.after_index
            if charts_request.after_index is not None
            else -1,
        }
        if charts_request.tag_value_id:
            params["tag_value_id"] = charts_request.tag_value_id

        sql = kwargs_to_pgpos(query, params)
        rows = await db.fetch(
            sql.sql,
            *sql.args,
        )

    # Get a list of all the charts
    charts = []
    total_num_series = 0
    highest_overall_section_index = max([row["index"] for row in rows]) if rows else -1
    highest_section_index_to_return: int | None = None
    for row in rows:
        total_num_series += len([c["series"] for c in row["charts"]])
        charts.extend(row["charts"])
        if total_num_series > MAX_SERIES_PER_FETCH:
            highest_section_index_to_return = row["index"]
            break

    rows = [
        row
        for row in rows
        if (
            highest_section_index_to_return is None
            or row["index"] < highest_section_index_to_return
        )
    ]

    # Get a list of all the series and a mapping of which series belong to which chart
    all_series: list[schemas.CustomChartSeries] = []
    all_common_filters: list[schemas.CustomChartSeriesFilters | None] = []
    chart_series_mapping = {}  # series_id -> chart_id

    for chart in charts:
        common_filters = (
            schemas.CustomChartSeriesFilters(**chart["common_filters"])
            if chart.get("common_filters")
            else None
        )
        for series in chart["series"]:
            series_obj = schemas.CustomChartSeries(**series)
            all_series.append(series_obj)
            all_common_filters.append(common_filters)
            chart_series_mapping[series["id"]] = chart["id"]

    chart_data = await get_chart_data_for_series(
        auth,
        all_series,
        charts_request,
        all_common_filters,
        charts_request.access_scope,
    )

    for chart in charts:
        chart["data"] = []
        for result in chart_data:
            # Add all the stats for that series to the data of the chart
            if chart_series_mapping[result.series_id] == chart["id"]:
                chart["data"].append(result.model_dump())
    return (
        schemas.CustomChartsResponse(
            sections=[schemas.CustomChartsSection(**row) for row in rows]
        ),
        highest_overall_section_index,
    )


@redis_cache(ttl=config.settings.CHARTS_CACHE_TTL_SEC)
async def fetch_custom_charts_cached(
    charts_request: schemas.CustomChartsRequestInternal,
    auth: AuthInfo | OrgAuthInfo,
) -> Tuple[schemas.CustomChartsResponse, int]:
    return await fetch_custom_charts(charts_request, auth)


def combine_series_filters(
    filter_1: schemas.CustomChartSeriesFilters | dict | None,
    filter_2: schemas.CustomChartSeriesFilters | dict | None,
) -> dict:
    filter_1 = filter_1.model_dump() if isinstance(filter_1, BaseModel) else filter_1
    filter_2 = filter_2.model_dump() if isinstance(filter_2, BaseModel) else filter_2
    if filter_1 and filter_2:
        combined: dict = {}
        for key in ("filter", "trace_filter", "tree_filter"):
            combined[key] = combine_filters(filter_1.get(key), filter_2.get(key))
        combined["session"] = combine_sessions(
            filter_1.get("session"), filter_2.get("session")
        )
        return combined
    return cast(dict, filter_1 or filter_2 or {})


def combine_filters(filter_1: str | None, filter_2: str | None) -> str | None:
    if filter_1 and filter_2:
        return f"and({filter_1}, {filter_2})"
    else:
        return filter_1 or filter_2


def combine_sessions(
    session_1: list[uuid.UUID] | None, session_2: list[uuid.UUID] | None
) -> list[uuid.UUID]:
    if session_1 and session_2:
        ret_val = list(set(session_1) & set(session_2))
        if len(ret_val) == 0:
            raise HTTPException(
                status_code=500,
                detail="Session filters must have at least one session in common",
            )
    else:
        ret_val = session_1 or session_2 or []
    return ret_val


async def get_chart_data_for_series(
    auth: AuthInfo | OrgAuthInfo,
    series: list[schemas.CustomChartSeries],
    request: schemas.CustomChartsSectionRequest,
    common_filters: list[schemas.CustomChartSeriesFilters | None],
    access_scope: schemas.AccessScope,
    use_cache: bool = False,
) -> list[schemas.CustomChartsDataPoint]:
    result = jsonpatch.apply_patch(
        [],  # Start with an empty list
        [
            op
            async for ops in stream_chart_data_for_series(
                auth, series, request, common_filters, access_scope, use_cache
            )
            for op in ops
        ],
    )

    return [schemas.CustomChartsDataPoint(**item) for item in result]


def chart_metric_to_stats_select(
    metric: schemas.CustomChartMetric,
) -> schemas.RunStatsSelect:
    if metric in (
        schemas.CustomChartMetric.feedback,
        schemas.CustomChartMetric.feedback_score_avg,
        schemas.CustomChartMetric.feedback_values,
    ):
        return schemas.RunStatsSelect.feedback_stats
    return schemas.RunStatsSelect(metric)


def _group_by_to_filters(
    group_by: schemas.RunStatsGroupBy, group: str
) -> dict[str, str]:
    new_filters = {}
    if group_by.attribute == "name":
        new_filters["filter"] = f'eq(name, "{group}")'
    elif group_by.attribute == "run_type":
        new_filters["filter"] = f'eq(run_type, "{group}")'
    elif group_by.attribute == "tag":
        new_filters["filter"] = f'eq(tag, "{group}")'
    elif group_by.attribute == "metadata":
        new_filters["filter"] = (
            f'and(eq(metadata_key, "{group_by.path}"), eq(metadata_value, "{group}"))'
        )
    else:
        raise ValueError(...)
    return new_filters


async def _prebuilt_sub_sections(
    auth: AuthInfo | OrgAuthInfo,
    request: schemas.CustomChartsRequestBase,
    session_id: uuid.UUID,
    use_cache: bool = False,
) -> list[dict]:
    # Fetch the top k feedback keys
    if use_cache:
        rounded_start = request.start_time.replace(second=0, microsecond=0)
        rounded_end = (
            request.end_time.replace(second=0, microsecond=0)
            if request.end_time
            else None
        )
        feedback_key_types = await cached_fetch_top_k_feedback_key_types(
            auth.tenant_id, rounded_start, rounded_end, session_id, auth=auth
        )
    else:
        feedback_key_types = (
            await fetch_top_k_feedback_key_types_for_prebuilt_dashboard(
                auth, request.start_time, request.end_time, session_id
            )
        )

    # Add a chart and series for each feedback key
    feedback_charts = []
    for i, feedback_key_type in enumerate(feedback_key_types):
        feedback_key = feedback_key_type["key"]
        feedback_type = feedback_key_type["feedback_type"]
        metric = (
            "feedback_score_avg" if feedback_type == "numerical" else "feedback_values"
        )
        chart = {
            "id": f"chart-{feedback_key}",
            "title": f"{feedback_key}",
            "description": f"Average {feedback_key} over time",
            "chart_type": "line",
            "index": i,
            "series": [
                {
                    "id": f"series-{feedback_key}",
                    "name": "Avg",
                    "metric": metric,
                    "feedback_key": feedback_key,
                }
            ],
        }
        feedback_charts.append(chart)

    prebuilt_dashboard = get_tracing_project_prebuilt_dashboard()
    sub_sections: list[dict] = prebuilt_dashboard["sub_sections"] + [
        {
            "title": "Feedback Scores",
            "description": "Feedback scores over time",
            "index": len(prebuilt_dashboard["sub_sections"]),
            "id": "feedback",
            "charts": feedback_charts,
        }
    ]
    for sub_section in sub_sections:
        for chart in sub_section["charts"]:
            chart["common_filters"] = {"session": [session_id]}
    return sub_sections


async def fetch_stats(
    s: schemas.CustomChartSeries,
    cf: schemas.CustomChartSeriesFilters | None,
    request: schemas.CustomChartsSectionRequest,
    auth: AuthInfo | OrgAuthInfo,
    use_cache: bool,
    semaphore: asyncio.Semaphore,
    data_source_type: Literal[
        schemas.RunsFilterDataSourceTypeEnum.historical,
        schemas.RunsFilterDataSourceTypeEnum.current,
    ],
    start_time: datetime,
    end_time: datetime,
) -> dict:
    from app.models.charts.cached_fetch import (
        cached_run_stats,
        normalize_stats_query_params,
    )
    from app.models.runs.stats import run_stats

    stats_query_params = schemas.RunStatsQueryParams(
        start_time=start_time,
        end_time=end_time,
        data_source_type=data_source_type,
        group_by=s.group_by,
        **combine_series_filters(s.filters, cf),
    )

    if use_cache:
        normalized_params = normalize_stats_query_params(stats_query_params)
        request.start_time = normalized_params.start_time or request.start_time
        request.end_time = normalized_params.end_time

        result = await gated_coro(
            cached_run_stats(
                s.workspace_id,
                normalized_params,
                select=[chart_metric_to_stats_select(s.metric)],
                bucket_info=request,
                max_values=5,
                auth=auth,
            ),
            semaphore,
        )
    else:
        result = await gated_coro(
            run_stats(
                auth,
                stats_query_params,
                select=[chart_metric_to_stats_select(s.metric)],
                bucket_info=request,
                max_values=5,
                tenant_id=s.workspace_id,
            ),
            semaphore,
        )

    return result
