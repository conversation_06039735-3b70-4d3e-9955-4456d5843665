from datetime import datetime, timedelta, timezone

from fastapi import H<PERSON>P<PERSON>xception
from lc_database.database import asyncpg_conn, kwargs_to_pgpos

from app import schemas
from app.api.auth.schemas import AuthInfo, OrgAuthInfo
from app.models.charts.constants import MAX_CHARTS_PER_SECTION
from app.models.charts.stream import _prebuilt_sub_sections
from app.models.charts.utils.validate import (
    validate_chart_filters,
    validate_chart_series_group_by,
    validate_or_fetch_or_create_chart_section,
)
from app.models.shared.utils import validate_workspaces_in_org


async def create_custom_chart(
    auth: AuthInfo | OrgAuthInfo,
    chart: schemas.CustomChartCreateInternal,
) -> schemas.CustomChartResponse:
    """Create a new chart."""
    tenant_id = (
        auth.tenant_id if chart.access_scope == schemas.AccessScope.workspace else None
    )
    await validate_chart_filters(
        auth,
        [s.filters for s in chart.series],
        chart.common_filters,
        chart.access_scope,
    )
    await validate_workspaces_in_org(
        auth, [s.workspace_id for s in chart.series if s.workspace_id]
    )
    await validate_chart_series_group_by([s.group_by for s in chart.series])

    async with asyncpg_conn() as db, db.transaction():
        # First check if the section exists
        section_id = chart.section_id
        section_id = await validate_or_fetch_or_create_chart_section(
            auth, section_id, db, chart.access_scope
        )

        existing_charts_info = await db.fetchrow(
            """
            SELECT MAX(index)+1 as highest, COUNT(*) as count
            FROM custom_chart
            WHERE section_id = $1 AND
            CASE
                WHEN $4 = 'workspace' THEN tenant_id = $2
                ELSE organization_id = $3 AND access_scope = 'organization'
            END
            """,
            section_id,
            tenant_id,
            auth.organization_id,
            chart.access_scope,
        )
        highest_existing_index = existing_charts_info["highest"]
        chart_count = existing_charts_info["count"]

        if chart_count >= MAX_CHARTS_PER_SECTION:
            raise HTTPException(
                status_code=400,
                detail="Maximum number of charts per section reached",
            )

        if (
            chart.index is None
            or highest_existing_index is None
            or chart.index >= highest_existing_index
        ):
            # Get the latest index in the current section:
            chart.index = highest_existing_index
            if highest_existing_index is None:
                chart.index = 0
        else:
            await db.execute(
                """
                UPDATE custom_chart
                SET index = index + 1
                WHERE section_id = $1 AND index >= $2 AND
                CASE
                    WHEN $5 = 'workspace' THEN tenant_id = $3
                    ELSE organization_id = $4 AND access_scope = 'organization'
                END
                """,
                section_id,
                chart.index,
                tenant_id,
                auth.organization_id,
                chart.access_scope,
            )

        new_chart = await db.fetchrow(
            """
            INSERT INTO custom_chart (
                title, description, tenant_id, chart_type, index, metadata, section_id, common_filters, organization_id, access_scope
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
            """,
            chart.title,
            chart.description,
            tenant_id,
            chart.chart_type,
            chart.index,
            chart.metadata,
            section_id,
            chart.common_filters.model_dump() if chart.common_filters else None,
            auth.organization_id,
            chart.access_scope,
        )

        sql_kwargs = {
            "names": [line.name for line in chart.series],
            "custom_chart_id": new_chart["id"],
            "filters": [
                line.filters.model_dump() if line.filters else None
                for line in chart.series
            ],
            "metrics": [line.metric for line in chart.series],
            "feedback_keys": [line.feedback_key for line in chart.series],
            "workspace_ids": [line.workspace_id for line in chart.series],
            "group_bys": [
                line.group_by.model_dump() if line.group_by else None
                for line in chart.series
            ],
        }

        query = """
        WITH data AS (
            SELECT UNNEST($names::text[]) AS name,
            UNNEST($filters::jsonb[]) AS filters,
            UNNEST($metrics::varchar[]) AS metric,
            UNNEST($feedback_keys::text[]) AS feedback_key,
            UNNEST($workspace_ids::uuid[]) AS workspace_id,
            UNNEST($group_bys::jsonb[]) AS group_by
        )
        INSERT INTO custom_chart_series (
            name, custom_chart_id, filters, metric, feedback_key, workspace_id, group_by
        )
        SELECT
            name,
            $custom_chart_id,
            filters,
            metric,
            feedback_key,
            workspace_id,
            group_by
        FROM data
        RETURNING *
        """

        sql = kwargs_to_pgpos(query, sql_kwargs)

        lines = await db.fetch(
            sql.sql,
            *sql.args,
        )

        chart_series = [
            schemas.CustomChartSeries(
                id=line["id"],
                name=line["name"],
                filters=line["filters"],
                metric=line["metric"],
                feedback_key=line["feedback_key"],
                workspace_id=line["workspace_id"],
                group_by=line["group_by"],
            )
            for line in lines
        ]

        return schemas.CustomChartResponse(
            id=new_chart["id"],
            title=new_chart["title"],
            description=new_chart["description"],
            chart_type=new_chart["chart_type"],
            index=new_chart["index"],
            metadata=new_chart["metadata"],
            section_id=new_chart["section_id"],
            series=chart_series,
        )


async def clone_chart_section(
    auth: AuthInfo | OrgAuthInfo,
    sections_clone_request: schemas.CustomChartsSectionsCloneRequest,
) -> schemas.CustomChartsSectionResponse:
    """Clone a dashboard.

    If a section_id is provided, clone that section into a new one with the same name but with "Copy of " prefix.
    If a session_id is provided, get the prebuilt dashboard for that session and create a new section with all the charts
    from the sub_sections (not the sub_sections themselves), setting the correct session_id.
    """
    async with asyncpg_conn() as db, db.transaction():
        if sections_clone_request.section_id:
            # Use a single query with CTEs to clone the section, charts, and series
            result = await db.fetchrow(
                """
            WITH 
            -- Clone the section
            new_section AS (
                INSERT INTO custom_chart_section (
                    title, description, tenant_id, index, organization_id, access_scope
                )
                SELECT 
                    'Copy of ' || title, description, tenant_id, index, organization_id, access_scope
                FROM 
                    custom_chart_section
                WHERE 
                    id = $1 AND tenant_id = $2
                RETURNING *
            ),
            -- Clone all charts at once
            new_charts AS (
                WITH source_charts AS (
                    SELECT id AS original_id, title, description, tenant_id, chart_type, 
                            index, metadata, common_filters, organization_id, access_scope
                    FROM custom_chart
                    WHERE section_id = $1 AND tenant_id = $2
                )
                INSERT INTO custom_chart (
                    title, description, tenant_id, chart_type, index, metadata,
                    section_id, common_filters, organization_id, access_scope
                )
                SELECT 
                    sc.title, sc.description, sc.tenant_id, sc.chart_type, sc.index, sc.metadata,
                    (SELECT id FROM new_section), sc.common_filters, sc.organization_id, sc.access_scope
                FROM source_charts sc
                RETURNING *, (SELECT original_id FROM source_charts 
                                WHERE title = title AND index = index LIMIT 1) AS original_id
            ),
            -- Map old chart IDs to new chart IDs for series reference
            chart_id_mapping AS (
                SELECT 
                    c.id as old_id, 
                    nc.id as new_id
                FROM 
                    custom_chart c
                JOIN 
                    new_charts nc ON c.index = nc.index AND c.title = nc.title
                WHERE 
                    c.section_id = $1 AND c.tenant_id = $2
            ),
            -- Clone all series at once
            new_series AS (
                INSERT INTO custom_chart_series (
                    name, custom_chart_id, filters, metric, feedback_key, workspace_id, group_by
                )
                SELECT 
                    s.name, m.new_id, s.filters, s.metric, s.feedback_key, s.workspace_id, s.group_by
                FROM 
                    custom_chart_series s
                JOIN 
                    chart_id_mapping m ON s.custom_chart_id = m.old_id
                RETURNING 1
            ),
            -- Count the charts in the new section
            chart_count AS (
                SELECT COUNT(*) AS count
                FROM new_charts
            )
            -- Return the new section with chart count
            SELECT 
                ns.*, 
                (SELECT count FROM chart_count) AS chart_count
            FROM 
                new_section ns
            """,
                sections_clone_request.section_id,
                auth.tenant_id,
            )

            if not result:
                raise HTTPException(
                    status_code=404,
                    detail="Section not found",
                )

            chart_count = result["chart_count"]

            return schemas.CustomChartsSectionResponse(
                id=result["id"],
                title=result["title"],
                description=result["description"],
                index=result["index"],
                chart_count=chart_count,
                created_at=result["created_at"],
                modified_at=result["modified_at"],
            )

        elif sections_clone_request.session_id:
            session_name = await db.fetchrow(
                """
                SELECT name from tracer_session
                WHERE id=$1
                AND tenant_id=$2
                AND reference_dataset_id IS NULL
                """,
                sections_clone_request.session_id,
                auth.tenant_id,
            )
            if session_name is None:
                raise HTTPException(
                    status_code=404,
                    detail="Project doesn't exist or is an experiment.",
                )
            session_name = session_name["name"]

            clone_request = schemas.CustomChartsRequestBase(
                start_time=datetime.now(timezone.utc) - timedelta(days=7)
            )
            sub_sections = await _prebuilt_sub_sections(
                auth, clone_request, sections_clone_request.session_id, use_cache=True
            )
            prebuilt_dashboard = {
                "title": session_name,
                "description": f"Copy of prebuilt dashboard for tracing project: {session_name}",
                "session_id": sections_clone_request.session_id,
                "id": sections_clone_request.session_id,
                "charts": [],
                "sub_sections": sub_sections,
            }

            # Create a new section for this session
            new_section = await db.fetchrow(
                """
                INSERT INTO custom_chart_section (
                    title, description, index, session_id, tenant_id
                )
                VALUES ($1, $2, $3, $4, $5)
                RETURNING *
                """,
                f"Copy of {session_name}",
                f"Copy of prebuilt dashboard for tracing project: {session_name}",
                0,
                sections_clone_request.session_id,
                auth.tenant_id,
            )

            # Prepare data for charts and series
            charts_to_create = []
            chart_index = 0

            # Extract all charts from the template and prepare data
            for sub_section in prebuilt_dashboard["sub_sections"]:
                for chart in sub_section["charts"]:
                    # Store chart data with series
                    chart_data = {
                        "title": chart["title"],
                        "description": chart.get("description"),
                        "chart_type": chart["chart_type"],
                        "index": chart_index,
                        "metadata": chart.get("metadata"),
                        "section_id": new_section["id"],
                        "common_filters": chart.get("common_filters"),
                        "series": [],
                    }

                    # Process series for this chart
                    for series in chart["series"]:
                        # Update the session filter if it exists
                        filters = series.get("filters", {})
                        if isinstance(filters, dict) and "session" not in filters:
                            # Add the session ID to the filters if not already present
                            filters["session"] = [sections_clone_request.session_id]

                        # Add series data
                        chart_data["series"].append(
                            {
                                "name": series["name"],
                                "filters": filters,
                                "metric": series["metric"],
                                "feedback_key": series.get("feedback_key"),
                                "workspace_id": series.get("workspace_id"),
                                "group_by": series.get("group_by"),
                            }
                        )

                    charts_to_create.append(chart_data)
                    chart_index += 1

            # Insert charts and their series
            chart_inserts = []
            all_series_records = []

            for chart_data in charts_to_create:
                # Add chart data to batch
                chart_inserts.append(
                    (
                        chart_data["title"],
                        chart_data["description"],
                        chart_data["chart_type"],
                        chart_data["index"],
                        chart_data["metadata"],
                        chart_data["section_id"],
                        chart_data["common_filters"],
                        auth.tenant_id,
                    )
                )

            # Bulk insert all charts and get their IDs
            # This assumes db.fetch_all returns rows with column names accessible as dict keys
            new_charts = await db.fetch(
                """
                INSERT INTO custom_chart (
                    title, description, chart_type, index, metadata,
                    section_id, common_filters, tenant_id
                )
                SELECT * FROM UNNEST($1::text[], $2::text[], $3::chart_type[], $4::integer[], 
                            $5::jsonb[], $6::uuid[], $7::jsonb[], $8::uuid[])
                RETURNING *
                """,
                [c[0] for c in chart_inserts],  # title
                [c[1] for c in chart_inserts],  # description
                [c[2] for c in chart_inserts],  # chart_type
                [c[3] for c in chart_inserts],  # index
                [c[4] for c in chart_inserts],  # metadata
                [c[5] for c in chart_inserts],  # section_id
                [c[6] for c in chart_inserts],  # common_filters
                [c[7] for c in chart_inserts],  # tenant_id
            )

            # Prepare all series data for batch insertion
            for i, chart_data in enumerate(charts_to_create):
                if not chart_data["series"]:
                    continue

                chart_id = new_charts[i]["id"]

                for series in chart_data["series"]:
                    all_series_records.append(
                        (
                            series["name"],
                            chart_id,
                            series["filters"],
                            series["metric"],
                            series["feedback_key"],
                            series["workspace_id"],
                            series["group_by"],
                        )
                    )

            # Batch insert all series if any exist
            if all_series_records:
                await db.executemany(
                    """
                    INSERT INTO custom_chart_series
                    (name, custom_chart_id, filters, metric, feedback_key, workspace_id, group_by)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    """,
                    all_series_records,
                )
            # Set chart count
            chart_count = len(charts_to_create)

            return schemas.CustomChartsSectionResponse(
                id=new_section["id"],
                title=new_section["title"],
                description=new_section["description"],
                index=new_section["index"],
                chart_count=chart_count,
                created_at=new_section["created_at"],
                modified_at=new_section["modified_at"],
                session_id=sections_clone_request.session_id,
            )

        else:
            # This should not happen due to validation in the request schema
            raise HTTPException(
                status_code=422, detail="Must set one of section_id or session_id"
            )


async def create_custom_chart_section(
    auth: AuthInfo | OrgAuthInfo,
    section: schemas.CustomChartsSectionCreateInternal,
) -> schemas.CustomChartsSectionResponse:
    """Create a section to group/order charts."""
    tenant_id = (
        auth.tenant_id
        if section.access_scope == schemas.AccessScope.workspace
        else None
    )

    async with asyncpg_conn() as db, db.transaction():
        highest_existing_index = await db.fetchval(
            """
            SELECT MAX(index)+1
            FROM custom_chart_section
            WHERE (
                CASE
                    WHEN $3 = 'workspace' THEN tenant_id = $1
                    ELSE organization_id = $2 AND access_scope = 'organization'
                END
            )
            """,
            tenant_id,
            auth.organization_id,
            section.access_scope,
        )
        if highest_existing_index is None:
            section.index = 0
        elif section.index is None or section.index > highest_existing_index:
            section.index = highest_existing_index
        await db.execute(
            """
            UPDATE custom_chart_section
            SET index = index + 1
            WHERE (
                CASE
                    WHEN $4 = 'workspace' THEN tenant_id = $2
                    ELSE organization_id = $3 AND access_scope = 'organization'
                END
            ) AND index >= $1
            """,
            section.index,
            tenant_id,
            auth.organization_id,
            section.access_scope,
        )

        new_section = await db.fetchrow(
            """
            INSERT INTO custom_chart_section (
                title, description, tenant_id, index, organization_id, access_scope
            )
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *
            """,
            section.title,
            section.description,
            tenant_id,
            section.index,
            auth.organization_id,
            section.access_scope,
        )

        return schemas.CustomChartsSectionResponse(
            id=new_section["id"],
            title=new_section["title"],
            description=new_section["description"],
            index=new_section["index"],
            chart_count=0,
            access_scope=new_section["access_scope"],
        )
