"""Encryption utilities for the application."""

import asyncio
import base64
from typing import Any, Dict, Optional

from lc_config.utils import arun_in_executor

from app.models.tenants.secrets import fernet


def _encrypt_b64(value: str) -> str:
    """Encrypt a string value using <PERSON>rnet and encode as base64."""
    return base64.b64encode(fernet.encrypt(value.encode("utf-8"))).decode("utf-8")


def _decrypt_b64(value: str) -> str:
    """Decrypt a base64 encoded Fernet encrypted string."""
    return fernet.decrypt(base64.b64decode(value + "=" * (-len(value) % 4))).decode(
        "utf-8"
    )


async def encrypt_dict_values(d: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Encrypt all string values in a dictionary."""
    if not d:
        return d
    encrypted_values = await asyncio.gather(
        *[arun_in_executor(_encrypt_b64, str(value)) for value in d.values()]
    )
    return dict(zip(d.keys(), encrypted_values))


async def decrypt_dict_values(d: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Decrypt all string values in a dictionary."""
    if not d:
        return d
    decrypted_values = await asyncio.gather(
        *[arun_in_executor(_decrypt_b64, value) for value in d.values()]
    )
    return dict(zip(d.keys(), decrypted_values))


async def encrypt_string(value: Optional[str]) -> Optional[str]:
    """Encrypt a string value."""
    if not value:
        return value
    return _encrypt_b64(value)


async def decrypt_string(value: Optional[str]) -> Optional[str]:
    """Decrypt a string value."""
    if not value:
        return value
    return _decrypt_b64(value)
