"""Endpoints for Tracer Sessions."""

from datetime import datetime
from typing import Annotated, Dict, List, Sequence
from uuid import UUID

import asyncpg
from fastapi import (
    Depends,
    Header,
    HTTPException,
    Query,
    Response,
)
from lc_config.settings import shared_settings as settings
from lc_database import api_router, database
from lc_logging.audit_logs import audit_operation_name
from sse_starlette import EventSourceResponse
from starlette.status import HTTP_202_ACCEPTED, HTTP_204_NO_CONTENT

from app import crud, models, schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.stream import jsonpatch_sse_stream

router = api_router.TrailingSlashRouter()


@router.post("/{session_id}/dashboard", response_model=schemas.CustomChartsSection)
async def get_tracing_project_prebuilt_dashboard(
    session_id: UUID,
    request: schemas.CustomChartsSectionRequest,
    accept: Annotated[str | None, Header()] = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_READ)),
) -> schemas.CustomChartsSection:
    """Get a prebuilt dashboard for a tracing project."""
    if not (
        str(auth.tenant_id) in settings.FF_RUN_STATS_GROUP_BY_ENABLED_TENANTS
        or settings.FF_RUN_STATS_GROUP_BY_ENABLED_ALL
    ):
        raise HTTPException(
            status_code=501,
            detail="This tenant does not have the proper permissions to use this endpoint.",
        )

    if accept == "text/event-stream":
        return EventSourceResponse(
            jsonpatch_sse_stream(
                models.charts.stream.stream_prebuilt_dashboard_for_session(
                    session_id, request, auth
                )
            )
        )

    return await models.charts.fetch.fetch_prebuilt_dashboard_for_session(
        session_id, request, auth
    )


@router.get("/{session_id}", response_model=schemas.TracerSession)
@audit_operation_name("read_tracer_session")
async def read_tracer_session(
    session_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_READ)),
    accept: Annotated[str | None, Header()] = None,
    include_stats: bool = False,
) -> schemas.TracerSession:
    """Get a specific session."""
    query_params = schemas.QueryParamsForSingleTracerSessionSchema(
        include_stats=include_stats
    )
    if accept == "text/event-stream":
        return EventSourceResponse(
            jsonpatch_sse_stream(
                models.tracer_sessions.stream.stream_session(
                    auth,
                    session_id,
                    query_params,
                )
            )
        )

    async with database.asyncpg_conn() as db:
        session = await crud.get_tracer_session_asyncpg(
            db,
            auth,
            session_id,
            query_params,
        )
        return session


@router.get("", response_model=List[schemas.TracerSession])
@audit_operation_name("read_tracer_sessions")
async def read_tracer_sessions(
    response: Response,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_READ)),
    accept: Annotated[str | None, Header()] = None,
    reference_free: bool | None = None,
    reference_dataset: Annotated[list[UUID] | None, Query()] = None,
    id: Annotated[list[UUID] | None, Query()] = None,
    name: str | None = None,
    name_contains: str | None = None,
    dataset_version: str | None = None,
    sort_by: schemas.SessionSortableColumns = schemas.SessionSortableColumns.START_TIME,
    sort_by_desc: bool = True,
    metadata: str | None = None,
    sort_by_feedback_key: str | None = None,
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    tag_value_id: Annotated[list[UUID] | None, Query()] = None,
    facets: bool = False,
    filter: str | None = None,
    include_stats: bool = True,
    use_approx_stats: bool = False,
) -> Sequence[schemas.TracerSession]:
    """Get all sessions."""
    query_params = schemas.FilterQueryParamsForTracerSessionSchema(
        reference_free=reference_free,
        reference_dataset=reference_dataset,
        id=id,
        name=name,
        name_contains=name_contains,
        metadata=metadata,
        dataset_version=dataset_version,
        sort_by=sort_by,
        sort_by_desc=sort_by_desc,
        sort_by_feedback_key=sort_by_feedback_key,
        offset=offset,
        limit=limit,
        facets=facets,
        tag_value_id=tag_value_id,
        filter=filter,
        include_stats=include_stats,
        use_approx_stats=use_approx_stats,
    )

    # temporarily reset this value to 1 to check if the query is cacheable
    query_params.limit = 1

    TOP_K = 10

    is_cacheable = (
        query_params
        == schemas.FilterQueryParamsForTracerSessionSchema(
            offset=0,
            limit=1,
            sort_by=schemas.SessionSortableColumns.LAST_RUN_START_TIME,
            sort_by_desc=True,
            reference_free=True,
        )
        and limit <= TOP_K
    )

    query_params.limit = limit

    if is_cacheable:
        rows, total = await crud.get_cached_top_tracer_sessions(
            auth.tenant_id, TOP_K, auth=auth, query_params=query_params
        )
        rows = rows[:limit]

        async def _a_iter_rows():
            yield [
                {
                    "op": "add",
                    "path": "",
                    "value": {
                        "rows": [row.dict() for row in rows],
                        "total": total,
                    },
                }
            ]

        if accept == "text/event-stream":
            return EventSourceResponse(
                jsonpatch_sse_stream(
                    _a_iter_rows(),
                )
            )
        else:
            response.headers["X-Pagination-Total"] = str(total)
            return rows

    if accept == "text/event-stream":
        return EventSourceResponse(
            jsonpatch_sse_stream(
                models.tracer_sessions.stream.stream_sessions(auth, query_params)
            )
        )

    rows, total = await crud.get_tracer_sessions(auth, query_params)
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.get(
    "/{session_id}/metadata", response_model=schemas.TracerSessionMetadataResponse
)
@audit_operation_name("read_tracer_session_runs_metadata")
async def read_tracer_sessions_runs_metadata(
    session_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_READ)),
    metadata_keys: Annotated[list[str] | None, Query()] = None,
    start_time: datetime | None = None,
    k: Annotated[int, Query(ge=1)] = 10,
    root_runs_only: bool = False,
) -> Dict[str, List[str]]:
    """Given a session, a number K, and (optionally) a list of metadata keys, return the top K values for each key."""
    return await models.tracer_sessions.stats.get_tracer_session_metadata_top_k(
        auth,
        session_id,
        schemas.QueryParamsForTracerSessionMetadataSchema(
            metadata_keys=metadata_keys,
            start_time=start_time,
            k=k,
            root_runs_only=root_runs_only,
        ),
    )


@router.post("", response_model=schemas.TracerSessionWithoutVirtualFields)
@audit_operation_name("create_tracer_session")
async def create_tracer_session(
    tracer_session: schemas.TracerSessionCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_CREATE)),
    upsert: bool = False,
) -> schemas.TracerSessionWithoutVirtualFields:
    """Create a new session."""

    try:
        return await crud.create_tracer_session(auth, tracer_session, upsert=upsert)
    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(
            status_code=409,
            detail="A session with that id already exists for this tenant.",
        )


@router.patch("/{session_id}", response_model=schemas.TracerSessionWithoutVirtualFields)
@audit_operation_name("update_tracer_session")
async def update_tracer_session(
    session_id: UUID,
    tracer_session: schemas.TracerSessionUpdate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_UPDATE)),
) -> schemas.TracerSessionWithoutVirtualFields:
    """Create a new session."""

    try:
        return await crud.update_tracer_session(auth, session_id, tracer_session)
    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(
            status_code=409,
            detail="A session with that name already exists for this workspace.",
        )


@router.delete("/{session_id}")
@audit_operation_name("delete_tracer_session")
async def delete_tracer_session(
    session_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_DELETE)),
) -> None:
    """Delete a specific session."""
    await crud.delete_tracer_sessions(auth, [session_id])
    return Response(status_code=HTTP_202_ACCEPTED)


@router.delete("")
@audit_operation_name("delete_tracer_sessions")
async def delete_tracer_sessions(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_DELETE)),
    session_ids: List[UUID] = Query(...),
) -> None:
    """Delete a specific session."""
    await crud.delete_tracer_sessions(auth, session_ids)
    return Response(status_code=HTTP_202_ACCEPTED)


@router.get("/{session_id}/views", response_model=List[schemas.FilterView])
async def read_filter_views(
    session_id: UUID,
    type: schemas.FilterViewType | None = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_READ)),
) -> List[schemas.FilterView]:
    """Get all filter views for a session."""
    return await crud.get_filter_views(auth, session_id, type)


@router.get("/{session_id}/views/{view_id}", response_model=schemas.FilterView)
async def read_filter_view(
    session_id: UUID,
    view_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_READ)),
) -> schemas.FilterView:
    """Get a specific filter view."""
    return await crud.get_filter_view(auth, session_id, view_id)


@router.post("/{session_id}/views", response_model=schemas.FilterView)
async def create_filter_view(
    session_id: UUID,
    filter_view: schemas.FilterViewCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_CREATE)),
) -> schemas.FilterView:
    """Create a new filter view."""
    return await crud.create_filter_view(auth, session_id, filter_view)


@router.patch("/{session_id}/views/{view_id}", response_model=schemas.FilterView)
async def update_filter_view(
    session_id: UUID,
    view_id: UUID,
    filter_view: schemas.FilterViewUpdate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_UPDATE)),
) -> schemas.FilterView:
    """Update a filter view."""
    return await crud.update_filter_view(auth, session_id, view_id, filter_view)


@router.delete("/{session_id}/views/{view_id}")
async def delete_filter_view(
    session_id: UUID,
    view_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_DELETE)),
) -> None:
    """Delete a specific filter view."""
    await crud.delete_filter_view(auth, session_id, view_id)
    return Response(status_code=HTTP_204_NO_CONTENT)
