"""Endpoints for organizations."""

import datetime
from typing import Annotated, List, Optional, Sequence
from uuid import UUID

import asyncpg
import structlog
from fastapi import Depends, Query, Response
from fastapi.responses import ORJSONResponse
from lc_database import api_router
from lc_database.supabase import supabase_client

from app import models, schemas
from app.api import deps
from app.api.auth.schemas import OrganizationPermissions, ServiceIdentity
from app.api.auth.utils import check_saml_sso_enabled
from app.models import ttl_settings
from app.models.identities import crud as identities_crud

logger = structlog.get_logger(__name__)

router = api_router.TrailingSlashRouter()


async def _check_saml_sso_params(
    db: asyncpg.Connection,
    auth: deps.OrgAuthInfo,
    payload: schemas.SSOSettingsCreate | schemas.SSOSettingsUpdate,
):
    if payload.default_workspace_ids:
        workspace_ids = await db.fetch(
            "SELECT id FROM tenants WHERE organization_id = $1", auth.organization_id
        )
        workspace_ids = [row["id"] for row in workspace_ids]
        if not all(
            workspace_id in workspace_ids
            for workspace_id in payload.default_workspace_ids
        ):
            raise deps.HTTPException(
                status_code=400,
                detail="Invalid default_workspace_ids.",
            )
    if payload.default_workspace_role_id:
        role = await db.fetchval(
            "SELECT id FROM roles WHERE id = $1 AND access_scope = 'workspace' AND (organization_id IS NULL OR organization_id = $2)",
            payload.default_workspace_role_id,
            auth.organization_id,
        )
        if not role:
            raise deps.HTTPException(
                status_code=400,
                detail="Invalid default_workspace_role_id.",
            )


@router.get("", response_model=List[schemas.OrganizationPGSchemaSlim])
async def list_organizations(
    skip_create: bool = False,
    auth: deps.TenantlessAuthInfo = Depends(deps.TenantlessAuthorize()),
) -> Sequence[schemas.OrganizationPGSchemaSlim]:
    """Get all orgs visible to this auth"""
    return await models.organizations.list.list_organizations(
        auth, skip_create=skip_create
    )


@router.post("", response_model=schemas.OrganizationPGSchemaSlim)
async def create_organization(
    schema: schemas.OrganizationCreate,
    auth: deps.TenantlessAuthInfo = Depends(deps.TenantlessAuthorize()),
) -> schemas.OrganizationPGSchemaSlim:
    return await models.organizations.create.create_organization(auth, schema)


@router.post("/current/setup", response_model=schemas.StripeSetupIntentResponse)
async def create_customers_and_get_stripe_setup_intent(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> schemas.StripeSetupIntentResponse:
    return await models.organizations.payment.create_payment_setup_intent(auth)


@router.get("/current", response_model=schemas.Organization)
async def get_organization_info(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ, allow_disabled=True
        )
    ),
) -> schemas.Organization:
    return await models.organizations.payment.get_organization_info(auth)


@router.get("/current/info", response_model=schemas.OrganizationInfo)
async def get_current_organization_info(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ,
            allow_disabled=True,
            allowed_services=[ServiceIdentity.UNSPECIFIED],
        )
    ),
) -> schemas.OrganizationInfo:
    return await models.organizations.payment.get_organization_info_slim(auth)


@router.patch("/current/info", response_model=schemas.OrganizationInfo)
async def update_current_organization_info(
    update_data: schemas.OrganizationUpdate,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
            allow_disabled=False,
            allowed_services=[ServiceIdentity.UNSPECIFIED],
        )
    ),
) -> schemas.OrganizationInfo:
    return await models.organizations.update.update_organization_info(auth, update_data)


@router.get("/current/billing", response_model=schemas.OrganizationBillingInfo)
async def get_organization_billing_info(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ, allow_disabled=True
        )
    ),
) -> schemas.OrganizationBillingInfo:
    return await models.organizations.payment.get_organization_billing_info(auth)


@router.get("/current/dashboard", response_model=schemas.OrganizationDashboardSchema)
async def get_dashboard(
    type: schemas.OrganizationDashboardType,
    color_scheme: Optional[schemas.OrganizationDashboardColorScheme],
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ, allow_disabled=True
        )
    ),
) -> schemas.OrganizationDashboardSchema:
    color = color_scheme or schemas.OrganizationDashboardColorScheme.light
    return await models.organizations.dashboards.get_embeddable_dashboard(
        auth, type, color
    )


@router.post("/current/payment-method")
async def on_payment_method_created(
    payment_method: schemas.StripePaymentInformation,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> None:
    await models.organizations.payment.on_payment_method_created(auth, payment_method)
    return ORJSONResponse(
        {"message": "Updated default payment method and billing info"}, status_code=202
    )


@router.get("/current/business-info")
async def get_company_info(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> schemas.StripeBusinessInfo:
    return await models.organizations.payment.get_company_info(auth)


@router.post("/current/business-info")
async def set_company_info(
    business_info: schemas.StripeBusinessInfo,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> None:
    await models.organizations.payment.set_company_info(auth, business_info)
    return ORJSONResponse({"message": "Business information updated"}, status_code=202)


@router.post("/current/plan")
async def change_payment_plan(
    schema: schemas.ChangePaymentPlanSchema,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> None:
    await models.organizations.payment.switch_payment_plan(auth, schema)
    return ORJSONResponse({"message": "Updated payment plan"}, status_code=202)


@router.get("/current/roles", response_model=list[schemas.Role])
async def list_organization_roles(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ, allow_disabled=True
        )
    ),
) -> list[schemas.Role]:
    return await models.organizations.roles.list_roles(auth)


@router.post("/current/roles", response_model=schemas.Role)
async def create_organization_roles(
    schema: schemas.CreateRoleRequest,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> schemas.Role:
    return await models.organizations.roles.create_role(auth, schema)


@router.delete("/current/roles/{role_id}", response_model=schemas.Role)
async def delete_organization_roles(
    role_id: UUID,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> schemas.Role:
    return await models.organizations.roles.delete_role(auth, role_id)


@router.patch("/current/roles/{role_id}", response_model=schemas.Role)
async def update_organization_roles(
    role_id: UUID,
    schema: schemas.UpdateRoleRequest,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> schemas.Role:
    return await models.organizations.roles.update_role(auth, role_id, schema)


@router.get("/permissions", response_model=List[schemas.PermissionResponse])
async def list_permissions(
    _: deps.TenantlessAuthInfo = Depends(deps.TenantlessAuthorize()),
) -> List[schemas.PermissionResponse]:
    return await models.organizations.roles.list_permissions()


@router.get("/pending", response_model=List[schemas.OrganizationPGSchemaSlim])
async def list_pending_organization_invites(
    auth: deps.TenantlessAuthInfo = Depends(deps.TenantlessAuthorize()),
) -> Sequence[schemas.OrganizationPGSchemaSlim]:
    """Get all pending orgs visible to this auth"""
    return await identities_crud.list_pending_organization_invites(auth)


@router.get("/current/members", response_model=schemas.OrganizationMembers)
async def get_current_org_members(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ,
            allow_disabled=True,
        )
    ),
) -> schemas.OrganizationMembers:
    return await identities_crud.get_org_members(auth)


@router.get("/current/members/active", response_model=List[schemas.OrgMemberIdentity])
async def get_current_active_org_members(
    response: Response,
    params: Annotated[schemas.ListMembersQueryParams, Query()],
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ,
            allow_disabled=True,
        )
    ),
) -> List[schemas.OrgMemberIdentity]:
    members, total = await identities_crud.get_active_org_members(auth, params)
    response.headers["X-Pagination-Total"] = str(total)
    return members


@router.get("/current/members/pending", response_model=List[schemas.OrgPendingIdentity])
async def get_current_pending_org_members(
    response: Response,
    params: Annotated[schemas.ListPendingMembersQueryParams, Query()],
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ,
            allow_disabled=True,
        )
    ),
) -> List[schemas.OrgPendingIdentity]:
    members, total = await identities_crud.get_pending_org_members(auth, params)
    response.headers["X-Pagination-Total"] = str(total)
    return members


@router.post("/current/members", response_model=schemas.PendingIdentity)
async def add_member_to_current_org(
    payload: schemas.PendingIdentityCreate,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
            allow_disabled=True,
        )
    ),
) -> schemas.PendingIdentity:
    internal_payload = schemas.PendingIdentityCreateInternal(
        **payload.model_dump(), access_scope=schemas.AccessScope.organization
    )
    return await identities_crud.invite_user_to_org(auth, internal_payload)


@router.post("/current/members/batch", response_model=List[schemas.PendingIdentity])
async def add_members_to_current_org_batch(
    payloads: List[schemas.PendingIdentityCreate],
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
            allow_disabled=True,
        )
    ),
) -> List[schemas.PendingIdentity]:
    """Batch invite up to 500 users to the current org."""
    internal_payloads = [
        schemas.PendingIdentityCreateInternal(
            **p.model_dump(), access_scope=schemas.AccessScope.organization
        )
        for p in payloads
    ]
    return await identities_crud.invite_users_to_org(auth, internal_payloads)


@router.post(
    "/current/members/basic/batch", response_model=List[schemas.UserWithPassword]
)
async def add_basic_auth_members_to_current_org(
    payloads: List[schemas.BasicAuthMemberCreate],
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
            allow_disabled=True,
        )
    ),
) -> List[schemas.UserWithPassword]:
    """Batch add up to 500 users to the org and specified workspaces in basic auth mode."""
    return await identities_crud.add_basic_auth_users(auth, payloads)


@router.delete("/current/members/{identity_id}/pending")
async def delete_current_org_pending_member(
    identity_id: UUID,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
            allow_disabled=True,
        )
    ),
) -> None:
    """When an admin deletes a pending member invite."""
    return await identities_crud.delete_pending_identity_for_current_org(
        auth, identity_id
    )


@router.delete("/pending/{organization_id}")
async def delete_pending_organization_invite(
    organization_id: UUID,
    auth: deps.TenantlessAuthInfo = Depends(deps.TenantlessAuthorize()),
) -> None:
    return await identities_crud.delete_pending_org_identity_for_current_user(
        auth, organization_id
    )


@router.post("/pending/{organization_id}/claim", response_model=schemas.Identity)
async def claim_pending_organization_invite(
    organization_id: UUID,
    auth: deps.TenantlessAuthInfo = Depends(deps.TenantlessAuthorize()),
) -> schemas.Identity:
    return await identities_crud.claim_pending_org_identity(auth, organization_id)


@router.delete("/current/members/{identity_id}")
async def remove_member_from_current_org(
    identity_id: UUID,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
            allow_disabled=True,
        )
    ),
):
    """Remove a user from the current organization."""
    await identities_crud.delete_identity_for_current_org(auth, identity_id)
    return ORJSONResponse({"message": "User deleted"})


@router.patch("/members/basic")
async def update_current_user(
    payload: schemas.BasicAuthUserPatch,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ, allow_disabled=True
        )
    ),
):
    """Update a user's full_name/password (basic auth only)"""
    return await identities_crud.patch_basic_auth_current_user(auth, payload)


@router.patch("/current/members/{identity_id}")
async def update_current_org_member(
    identity_id: UUID,
    payload: schemas.OrgIdentityPatch,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
):
    """This is used for updating a user's role (all auth modes) or full_name/password (basic auth)"""
    payload_internal = schemas.OrgIdentityPatchInternal(
        **payload.model_dump(), access_scope=schemas.AccessScope.organization
    )
    return await identities_crud.patch_organization_user(
        auth, identity_id, payload_internal
    )


@router.put("/ttl-settings", response_model=ttl_settings.crud.TTLSettings)
async def upsert_ttl_settings(
    ttl_settings_req: ttl_settings.crud.UpsertTTLSettingsRequest,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE, allow_disabled=True
        )
    ),
) -> ttl_settings.crud.TTLSettings:
    return await ttl_settings.crud.upsert_ttl_settings(auth, ttl_settings_req)


@router.get("/ttl-settings", response_model=List[ttl_settings.crud.TTLSettings])
async def list_ttl_settings(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ, allow_disabled=True
        )
    ),
) -> List[ttl_settings.crud.TTLSettings]:
    """List out the configured TTL settings for a given org (org-level and tenant-level)."""
    return await ttl_settings.crud.list_ttl_settings_for_org(auth)


@router.post(
    "/current/sso-settings", response_model=schemas.SSOProvider, status_code=201
)
async def create_sso_settings(
    payload: schemas.SSOSettingsCreate,
    db: asyncpg.Connection = Depends(deps.get_asyncpg_db),
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
        )
    ),
):
    """Create SSO provider settings for the current organization."""
    check_saml_sso_enabled(auth)
    async with supabase_client() as client:
        existing_sso_settings_for_info = (
            await models.organizations.sso.existing_sso_settings(
                db,
                metadata_xml=payload.metadata_xml,
                metadata_url=payload.metadata_url,
            )
        )
        if existing_sso_settings_for_info:
            logger.warning(
                f"Attempt to create duplicate SSO settings: {existing_sso_settings_for_info=}",
                payload=payload,
            )
            raise deps.HTTPException(
                status_code=409,
                detail="Cannot create duplicate SSO provider settings",
            )
        existing_sso_settings_for_org = (
            await models.organizations.sso.get_sso_providers(db, auth)
        )
        if existing_sso_settings_for_org:
            logger.warning(
                f"Attempt to create additional SSO settings: {existing_sso_settings_for_org=}",
                payload=payload,
            )
            raise deps.HTTPException(
                status_code=409,
                detail="Cannot create multiple SSO provider settings for an organization.",
            )
        await _check_saml_sso_params(db, auth, payload)
        response = await client.create_sso_provider(
            metadata_xml=payload.metadata_xml,
            metadata_url=payload.metadata_url,
            attribute_mapping=payload.attribute_mapping,
        )
        logger.info(
            "Created SSO provider in Supabase", payload=payload, response=response
        )
        provider = await models.organizations.sso.create_sso_provider(
            db,
            auth,
            schemas.SSOProviderCreateInternal(
                provider_id=response["id"],
                metadata_url=payload.metadata_url if payload.metadata_url else None,
                metadata_xml=payload.metadata_xml if payload.metadata_xml else None,
                default_workspace_role_id=payload.default_workspace_role_id,
                default_workspace_ids=payload.default_workspace_ids,
            ),
        )
        await models.organizations.sso.set_sso_login_slug_if_empty(db, auth)

    return provider


@router.patch("/current/sso-settings/{id}", response_model=schemas.SSOProvider)
async def update_sso_settings(
    id: UUID,
    payload: schemas.SSOSettingsUpdate,
    db: asyncpg.Connection = Depends(deps.get_asyncpg_db),
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
        )
    ),
):
    """Update SSO provider settings defaults for the current organization."""
    check_saml_sso_enabled(auth)
    await _check_saml_sso_params(db, auth, payload)
    async with supabase_client() as client:
        providers = await models.organizations.sso.get_sso_providers(db, auth)
        provider = next((p for p in providers if p.id == id), None)
        if not provider:
            raise deps.HTTPException(
                status_code=404,
                detail=f"SSO provider not found for this organization with ID {id}.",
            )
        if payload.metadata_url or payload.metadata_xml:
            response = await client.update_sso_provider(
                provider_id=provider.provider_id,
                metadata_url=payload.metadata_url if payload.metadata_url else None,
                metadata_xml=payload.metadata_xml if payload.metadata_xml else None,
            )
            logger.info("Updated SSO provider in Supabase", response=response)
        return await models.organizations.sso.update_sso_provider(
            db,
            auth,
            id,
            payload,
        )


@router.patch("/current/login-methods", response_model=dict)
async def update_allowed_login_methods(
    payload: schemas.AllowedLoginMethodsUpdate,
    db: asyncpg.Connection = Depends(deps.get_asyncpg_db),
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
        )
    ),
):
    """Update allowed login methods for the current organization."""
    check_saml_sso_enabled(auth)
    if not auth.is_sso_user and payload.sso_only:
        raise deps.HTTPException(
            status_code=400,
            detail="Must be logged in via SSO to set organization to SSO only.",
        )
    await models.organizations.sso.update_allowed_login_methods(
        db,
        auth,
        payload,
    )
    return ORJSONResponse({"message": "Updated allowed login methods"}, status_code=202)


@router.get("/current/billing/usage", response_model=List[schemas.OrgUsage])
async def get_org_usage(
    starting_on: datetime.datetime,
    ending_before: datetime.datetime,
    on_current_plan: bool = True,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_READ)
    ),
) -> List[schemas.OrgUsage]:
    return await models.organizations.usage.fetch_org_usage(
        starting_on=starting_on,
        ending_before=ending_before,
        on_current_plan=on_current_plan,
        auth=auth,
    )


@router.get(
    "/current/user/login-methods", response_model=List[schemas.ProviderUserSlim]
)
async def get_current_user_login_methods(
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ,
            allow_disabled=True,
            require_user=True,
        )
    ),
) -> List[schemas.ProviderUserSlim]:
    """Get login methods for the current user."""
    provider_users = await models.identities.users.get_provider_users(auth.ls_user_id)
    return [schemas.ProviderUserSlim(**p.model_dump()) for p in provider_users]


@router.get("/current/sso-settings", response_model=List[schemas.SSOProvider])
async def get_current_sso_settings(
    db: asyncpg.Connection = Depends(deps.get_asyncpg_db),
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_READ, allow_disabled=True
        )
    ),
) -> List[schemas.SSOProvider]:
    """Get SSO provider settings for the current organization."""
    check_saml_sso_enabled(auth)
    sso_providers = await models.organizations.sso.get_sso_providers(db, auth)
    return sso_providers


@router.delete("/current/sso-settings/{id}", response_model=schemas.SSOProvider)
async def delete_sso_settings(
    id: UUID,
    db: asyncpg.Connection = Depends(deps.get_asyncpg_db),
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
        )
    ),
):
    """Delete SSO provider settings for the current organization."""
    check_saml_sso_enabled(auth)
    if auth.is_sso_user:
        raise deps.HTTPException(
            status_code=400,
            detail="SSO users cannot delete SSO provider settings. Use a different login method to delete.",
        )
    async with supabase_client() as client:
        providers = await models.organizations.sso.get_sso_providers(db, auth)
        provider = next((p for p in providers if p.id == id), None)
        if not provider:
            raise deps.HTTPException(
                status_code=404,
                detail=f"SSO provider not found for this organization with ID {id}.",
            )
        response = await client.delete_sso_provider(
            provider_id=provider.provider_id,
        )
        logger.info("Deleted SSO provider in Supabase", response=response)
        await models.organizations.sso.delete_sso_provider(db, auth, provider.id)
    return provider


# Inbound credit purchase setup


@router.post("/current/stripe_checkout_session")
async def create_stripe_checkout_sessions_endpoint(
    payload: schemas.StripeCheckoutSessionsCreate,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
        )
    ),
):
    """Kick off a Stripe checkout session flow."""
    return await models.organizations.payment.create_checkout_session(
        auth.organization_id,
        auth.user_email,
        payload.amount_cents,
        payload.success_path,
    )


# TODO: Handle Stripe webhook events instead of relying solely on client redirect
@router.post("/current/confirm_checkout_session_completion")
async def confirm_checkout_session_completion_endpoint(
    payload: schemas.StripeCheckoutSessionsConfirm,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
        )
    ),
):
    """Complete a Stripe checkout session flow."""
    return await models.organizations.payment.confirm_checkout_session_completion(
        auth.organization_id, payload.stripe_session_id
    )


# Outbound transfer setup


@router.post("/current/stripe_account_links")
async def create_stripe_account_links_endpoint(
    payload: schemas.StripeAccountLinksCreate,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(
            OrganizationPermissions.ORGANIZATION_MANAGE,
        )
    ),
):
    """Kick off a Stripe account link flow."""
    connected_account_id = (
        await models.organizations.payment.find_or_create_stripe_connected_account(
            auth.organization_id
        )
    )
    return await models.organizations.payment.create_stripe_account_link(
        connected_account_id, payload.success_path
    )
