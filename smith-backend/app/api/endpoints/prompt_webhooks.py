"""Endpoints for prompt webhooks."""

from typing import List
from uuid import UUID

from fastapi import Depends, Response
from lc_database import api_router
from lc_logging.audit_logs import audit_operation_name

from app import schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.models.prompt_webhooks import crud as prompt_webhooks_crud

router = api_router.TrailingSlashRouter()


@router.get("", response_model=List[schemas.PromptWebhook])
@audit_operation_name("read_prompt_webhooks")
async def list_prompt_webhooks(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_READ)),
) -> List[schemas.PromptWebhook]:
    """List all prompt webhooks for the current tenant."""
    return await prompt_webhooks_crud.list_prompt_webhooks(auth)


@router.get("/{webhook_id}", response_model=schemas.PromptWebhook)
@audit_operation_name("read_prompt_webhook")
async def get_prompt_webhook(
    webhook_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_READ)),
) -> schemas.PromptWebhook:
    """Get a specific prompt webhook."""
    return await prompt_webhooks_crud.get_prompt_webhook(auth, webhook_id)


@router.post("", response_model=schemas.PromptWebhook)
@audit_operation_name("create_prompt_webhook")
async def create_prompt_webhook(
    webhook: schemas.PromptWebhookCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_CREATE)),
) -> schemas.PromptWebhook:
    """Create a new prompt webhook."""
    return await prompt_webhooks_crud.create_prompt_webhook(auth, webhook)


@router.patch("/{webhook_id}", response_model=schemas.PromptWebhook)
@audit_operation_name("update_prompt_webhook")
async def update_prompt_webhook(
    webhook_id: UUID,
    webhook: schemas.PromptWebhookUpdate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_UPDATE)),
) -> schemas.PromptWebhook:
    """Update a specific prompt webhook."""
    return await prompt_webhooks_crud.update_prompt_webhook(auth, webhook_id, webhook)


@router.delete("/{webhook_id}")
@audit_operation_name("delete_prompt_webhook")
async def delete_prompt_webhook(
    webhook_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_DELETE)),
) -> Response:
    """Delete a specific prompt webhook."""
    return await prompt_webhooks_crud.delete_prompt_webhook(auth, webhook_id)


@router.post("/test")
@audit_operation_name("test_prompt_webhook")
async def test_prompt_webhook(
    webhook: schemas.PromptWebhookTest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROMPTS_READ)),
) -> dict[str, str]:
    """Test a specific prompt webhook."""
    await prompt_webhooks_crud.test_prompt_webhook(auth, webhook)
    return {"message": "Test notification sent successfully"}
