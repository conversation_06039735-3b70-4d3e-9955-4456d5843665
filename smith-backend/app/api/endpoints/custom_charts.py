"""Endpoints for custom workspace-scoped monitoring charts."""

import logging
import uuid

from fastapi import Depends, HTTPException, Response
from lc_database import api_router

from app import models, schemas
from app.api import deps
from app.api.auth.schemas import OrganizationPermissions, Permissions

router = api_router.TrailingSlashRouter()
org_router = api_router.TrailingSlashRouter()
logger = logging.getLogger(__name__)


# NAMING:
# Wherever the term 'section' is used, it refers to a dashboard.
# A custom_chart belongs to a section, and a section belongs to a workspace.
# You have resource tags on sections not on charts.

# Workspace-scoped charts


def validate_org_charts_allowed(auth: deps.OrgAuthInfo) -> None:
    if not auth.org_config.enable_org_usage_charts:
        raise HTTPException(
            status_code=403,
            detail="Organization charts are not enabled for this organization.",
        )


@router.post("/section/clone", response_model=schemas.CustomChartsSectionResponse)
async def clone_section(
    sections_clone_request: schemas.CustomChartsSectionsCloneRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_READ)),
) -> schemas.CustomChartsSectionResponse:
    """Clone a dashboard."""
    return await models.charts.create.clone_chart_section(auth, sections_clone_request)


@router.get("/section", response_model=list[schemas.CustomChartsSectionResponse])
async def read_sections(
    response: Response,
    sections_request: schemas.CustomChartsSectionsRequest = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_READ)),
) -> list[schemas.CustomChartsSectionResponse]:
    """Get all sections for the tenant."""
    sections, total = await models.charts.fetch.fetch_custom_charts_sections(
        sections_request, auth, schemas.AccessScope.workspace
    )
    response.headers["X-Pagination-Total"] = str(total)
    return sections


@router.post("", response_model=schemas.CustomChartsResponse)
async def read_charts(
    response: Response,
    charts_request: schemas.CustomChartsRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_READ)),
) -> schemas.CustomChartsResponse:
    """Get all charts for the tenant."""
    payload = schemas.CustomChartsRequestInternal(
        **charts_request.model_dump(), access_scope=schemas.AccessScope.workspace
    )
    custom_charts, total = await models.charts.fetch.fetch_custom_charts_cached(
        payload, auth
    )
    response.headers["X-Pagination-Total"] = str(total)
    return custom_charts


@router.post("/preview", response_model=schemas.SingleCustomChartResponseBase)
async def read_chart_preview(
    preview_request: schemas.CustomChartPreviewRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_READ)),
) -> schemas.SingleCustomChartResponseBase:
    """Get a preview for a chart without actually creating it."""
    payload = schemas.CustomChartPreviewRequestInternal(
        **preview_request.model_dump(), access_scope=schemas.AccessScope.workspace
    )
    return await models.charts.fetch.fetch_chart_preview_cached(payload, auth)


@router.post("/create", response_model=schemas.CustomChartResponse)
async def create_chart(
    chart: schemas.CustomChartCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_CREATE)),
) -> schemas.CustomChartResponse:
    """Create a new chart."""
    payload = schemas.CustomChartCreateInternal(
        **chart.model_dump(), access_scope=schemas.AccessScope.workspace
    )
    return await models.charts.create.create_custom_chart(auth, payload)


@router.post("/section", response_model=schemas.CustomChartsSectionResponse)
async def create_section(
    section: schemas.CustomChartsSectionCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_CREATE)),
) -> schemas.CustomChartsSectionResponse:
    """Create a new section."""
    payload = schemas.CustomChartsSectionCreateInternal(
        **section.model_dump(), access_scope=schemas.AccessScope.workspace
    )
    return await models.charts.create.create_custom_chart_section(auth, payload)


@router.post("/{chart_id}", response_model=schemas.SingleCustomChartResponse)
async def read_single_chart(
    chart_id: uuid.UUID,
    charts_request: schemas.CustomChartsRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_READ)),
) -> schemas.SingleCustomChartResponse:
    """Get a single chart by ID."""
    payload = schemas.CustomChartsRequestInternal(
        **charts_request.model_dump(), access_scope=schemas.AccessScope.workspace
    )
    return await models.charts.fetch.fetch_single_custom_chart_cached(
        chart_id, payload, auth
    )


@router.post("/section/{section_id}", response_model=schemas.CustomChartsSection)
async def read_single_section(
    section_id: uuid.UUID,
    request: schemas.CustomChartsSectionRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_READ)),
) -> schemas.CustomChartsSection:
    """Get a single section by ID."""

    return await models.charts.fetch.fetch_single_custom_chart_section(
        section_id, request, auth, schemas.AccessScope.workspace
    )


@router.patch("/{chart_id}", response_model=schemas.CustomChartResponse)
async def update_chart(
    chart: schemas.CustomChartUpdate,
    chart_id: uuid.UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_UPDATE)),
) -> schemas.CustomChartResponse:
    """Update a chart."""
    payload = schemas.CustomChartUpdateInternal(
        **chart.model_dump(exclude_defaults=True),
        access_scope=schemas.AccessScope.workspace,
    )
    return await models.charts.update.update_custom_chart(auth, chart_id, payload)


@router.patch(
    "/section/{section_id}", response_model=schemas.CustomChartsSectionResponse
)
async def update_section(
    section: schemas.CustomChartsSectionUpdate,
    section_id: uuid.UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_UPDATE)),
) -> schemas.CustomChartsSectionResponse:
    """Update a section."""
    payload = schemas.CustomChartsSectionUpdateInternal(
        **section.model_dump(exclude_defaults=True),
        access_scope=schemas.AccessScope.workspace,
    )
    return await models.charts.update.update_custom_chart_section(
        auth, section_id, payload
    )


@router.delete("/section/{section_id}")
async def delete_section(
    section_id: uuid.UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_DELETE)),
) -> None:
    """Delete a section."""

    return await models.charts.delete.delete_custom_chart_section(
        auth, section_id, schemas.AccessScope.workspace
    )


@router.delete("/{chart_id}")
async def delete_chart(
    chart_id: uuid.UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.CHARTS_DELETE)),
) -> None:
    """Delete a chart."""

    return await models.charts.delete.delete_custom_chart(
        auth, chart_id, schemas.AccessScope.workspace
    )


# Org-scoped charts


@org_router.get("/section", response_model=list[schemas.CustomChartsSectionResponse])
async def org_read_sections(
    response: Response,
    sections_request: schemas.CustomChartsSectionsRequest = Depends(),
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_READ)
    ),
) -> list[schemas.CustomChartsSectionResponse]:
    """Get all sections for the tenant."""
    validate_org_charts_allowed(auth)
    sections, total = await models.charts.fetch.fetch_custom_charts_sections(
        sections_request, auth, schemas.AccessScope.organization
    )
    response.headers["X-Pagination-Total"] = str(total)
    return sections


@org_router.post("", response_model=schemas.CustomChartsResponse)
async def org_read_charts(
    response: Response,
    charts_request: schemas.CustomChartsRequest,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_READ)
    ),
) -> schemas.CustomChartsResponse:
    """Get all charts for the tenant."""
    validate_org_charts_allowed(auth)
    payload = schemas.CustomChartsRequestInternal(
        **charts_request.model_dump(), access_scope=schemas.AccessScope.organization
    )
    custom_charts, total = await models.charts.fetch.fetch_custom_charts_cached(
        payload, auth
    )
    response.headers["X-Pagination-Total"] = str(total)
    return custom_charts


@org_router.post("/preview", response_model=schemas.SingleCustomChartResponseBase)
async def org_read_chart_preview(
    preview_request: schemas.CustomChartPreviewRequest,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_READ)
    ),
) -> schemas.SingleCustomChartResponseBase:
    """Get a preview for a chart without actually creating it."""
    validate_org_charts_allowed(auth)
    payload = schemas.CustomChartPreviewRequestInternal(
        **preview_request.model_dump(), access_scope=schemas.AccessScope.organization
    )
    return await models.charts.fetch.fetch_chart_preview_cached(payload, auth)


@org_router.post("/create", response_model=schemas.CustomChartResponse)
async def org_create_chart(
    chart: schemas.CustomChartCreate,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_MANAGE)
    ),
) -> schemas.CustomChartResponse:
    """Create a new chart."""
    validate_org_charts_allowed(auth)
    payload = schemas.CustomChartCreateInternal(
        **chart.model_dump(), access_scope=schemas.AccessScope.organization
    )
    return await models.charts.create.create_custom_chart(auth, payload)


@org_router.post("/section", response_model=schemas.CustomChartsSectionResponse)
async def org_create_section(
    section: schemas.CustomChartsSectionCreate,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_MANAGE)
    ),
) -> schemas.CustomChartsSectionResponse:
    """Create a new section."""
    validate_org_charts_allowed(auth)
    payload = schemas.CustomChartsSectionCreateInternal(
        **section.model_dump(), access_scope=schemas.AccessScope.organization
    )
    return await models.charts.create.create_custom_chart_section(auth, payload)


@org_router.post("/{chart_id}", response_model=schemas.SingleCustomChartResponse)
async def org_read_single_chart(
    chart_id: uuid.UUID,
    charts_request: schemas.CustomChartsRequest,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_READ)
    ),
) -> schemas.SingleCustomChartResponse:
    """Get a single chart by ID."""
    validate_org_charts_allowed(auth)
    payload = schemas.CustomChartsRequestInternal(
        **charts_request.model_dump(), access_scope=schemas.AccessScope.organization
    )
    return await models.charts.fetch.fetch_single_custom_chart_cached(
        chart_id, payload, auth
    )


@org_router.post("/section/{section_id}", response_model=schemas.CustomChartsSection)
async def org_read_single_section(
    section_id: uuid.UUID,
    request: schemas.CustomChartsRequestBase,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_READ)
    ),
) -> schemas.CustomChartsSection:
    """Get a single section by ID."""
    validate_org_charts_allowed(auth)
    request_ = schemas.CustomChartsSectionRequest(**request.model_dump())
    return await models.charts.fetch.fetch_single_custom_chart_section(
        section_id, request_, auth, schemas.AccessScope.organization
    )


@org_router.patch("/{chart_id}", response_model=schemas.CustomChartResponse)
async def org_update_chart(
    chart: schemas.CustomChartUpdate,
    chart_id: uuid.UUID,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_MANAGE)
    ),
) -> schemas.CustomChartResponse:
    """Update a chart."""
    validate_org_charts_allowed(auth)
    payload = schemas.CustomChartUpdateInternal(
        **chart.model_dump(exclude_defaults=True),
        access_scope=schemas.AccessScope.organization,
    )
    return await models.charts.update.update_custom_chart(auth, chart_id, payload)


@org_router.patch(
    "/section/{section_id}", response_model=schemas.CustomChartsSectionResponse
)
async def org_update_section(
    section: schemas.CustomChartsSectionUpdate,
    section_id: uuid.UUID,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_MANAGE)
    ),
) -> schemas.CustomChartsSectionResponse:
    """Update a section."""
    validate_org_charts_allowed(auth)
    payload = schemas.CustomChartsSectionUpdateInternal(
        **section.model_dump(exclude_defaults=True),
        access_scope=schemas.AccessScope.organization,
    )
    return await models.charts.update.update_custom_chart_section(
        auth, section_id, payload
    )


@org_router.delete("/section/{section_id}")
async def org_delete_section(
    section_id: uuid.UUID,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_MANAGE)
    ),
) -> None:
    """Delete a section."""
    validate_org_charts_allowed(auth)
    return await models.charts.delete.delete_custom_chart_section(
        auth, section_id, schemas.AccessScope.organization
    )


@org_router.delete("/{chart_id}")
async def org_delete_chart(
    chart_id: uuid.UUID,
    auth: deps.OrgAuthInfo = Depends(
        deps.OrgAuthorize(OrganizationPermissions.ORGANIZATION_MANAGE)
    ),
) -> None:
    """Delete a chart."""
    validate_org_charts_allowed(auth)
    return await models.charts.delete.delete_custom_chart(
        auth, chart_id, schemas.AccessScope.organization
    )
