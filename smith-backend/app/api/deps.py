"""Dependencies for endpoints."""

from typing import Annotated, Async<PERSON>enerator, Optional
from uuid import UUID

import asyncpg
import orjson
from fastapi import Depends, HTTPException, Path, Request, status
from fastapi.security import (
    APIKeyHeader,
    HTTPAuthorizationCredentials,
    HTTPBasic,
    HTTPBasicCredentials,
    HTTPBearer,
)
from lc_database import redis
from lc_database.curl import platform_request
from lc_database.database import asyncpg_conn

from app import models, schemas
from app.api.auth import (
    AuthInfo,
    AuthInfoRequiredHandle,
    FeedbackTokenInfo,
    OrgAuthInfo,
    ShareDatasetInfo,
    ShareRunInfo,
    TenantlessAuthInfo,
)
from app.api.auth.schemas import (
    OrganizationPermissions,
    Permissions,
    ServiceIdentity,
    UnverifiedAuthInfo,
)
from app.api.auth.verify import (
    verify_auth_info,
    verify_org_auth_info,
    verify_tenantless_auth_info,
)
from app.config import settings
from app.retry import retry_asyncpg


async def get_asyncpg_db() -> AsyncGenerator[asyncpg.Connection, None]:
    async with asyncpg_conn() as conn:
        yield conn


# this is a hack to prevent fastapi from outputting "args" and "kwargs" in the
# spec due to Depends(None). Depends(lambda: None) works though.
def _noop_scheme():
    return None


def get_cookies(request: Request) -> str:
    return request.headers.get("cookie")


CookiesType = Annotated[Optional[str], Depends(get_cookies)]

ApiKeyType = Annotated[
    Optional[str],
    Depends(
        APIKeyHeader(name="X-API-Key", scheme_name="API Key", auto_error=False)
        if settings.AUTH_TYPE in ["supabase", "oauth", "mixed"]
        else _noop_scheme
    ),
]

AuthorizationType = Annotated[
    Optional[HTTPAuthorizationCredentials],
    Depends(
        HTTPBearer(
            scheme_name="Bearer Auth",
            auto_error=False,
            description="Bearer tokens are used to authenticate from the UI. Must also specify x-tenant-id or "
            "x-organization-id (for org scoped apis).",
        )
        if settings.AUTH_TYPE == "supabase"
        or settings.AUTH_TYPE == "oauth"
        or settings.BASIC_AUTH_ENABLED
        else _noop_scheme
    ),
]

BasicAuthType = Annotated[
    Optional[HTTPBasicCredentials],
    Depends(
        HTTPBasic(scheme_name="Basic Auth", auto_error=False)
        if settings.BASIC_AUTH_ENABLED
        else _noop_scheme
    ),
]


class APIKeyUUIDHeader(APIKeyHeader):
    """API key header that accepts UUIDs."""

    async def __call__(self, request: Request) -> Optional[UUID]:
        """Validate the API key."""
        api_key = await super().__call__(request)
        if api_key is None:
            return None
        try:
            val = UUID(api_key)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="UUID header value was not a valid UUID",
            )
        return val


OrganizationIdType = Annotated[
    Optional[UUID],
    Depends(
        APIKeyUUIDHeader(
            name="X-Organization-Id", scheme_name="Organization ID", auto_error=False
        )
        if settings.AUTH_TYPE in ["supabase", "oauth", "mixed"]
        else _noop_scheme
    ),
]

TenantIdType = Annotated[
    Optional[UUID],
    Depends(
        APIKeyUUIDHeader(name="X-Tenant-Id", scheme_name="Tenant ID", auto_error=False)
        if settings.AUTH_TYPE in ["supabase", "oauth", "mixed"]
        else _noop_scheme
    ),
]


class OptionalHeader:
    def __init__(self, name: str):
        self.name = name

    async def __call__(self, request: Request) -> Optional[str]:
        api_key = request.headers.get(self.name)
        if not api_key:
            return None
        return api_key


class TenantlessAuthorize:
    async def __call__(
        self,
        authorization: AuthorizationType,
        cookies: Optional[CookiesType] = Depends(get_cookies),
    ) -> TenantlessAuthInfo:
        return await verify_tenantless_auth_info(
            UnverifiedAuthInfo(
                x_api_key=None,
                x_tenant_id=None,
                authorization=authorization,
                cookies=cookies,
            )
        )


class Authorize:
    def __init__(
        self,
        permission: list[Permissions] | Permissions | None,
        allow_disabled: bool = False,
        allow_public: bool = False,
        require_handle: bool = False,
        require_user: bool = False,
        org_permission: OrganizationPermissions | None = None,
        allowed_services: list[ServiceIdentity] | None = None,
    ) -> None:
        """
        Args:
            permission: Workspace permission required for this endpoint
            allow_disabled: Allow disabled workspaces to access this endpoint
            allow_public: Ignore authorization result and allow access
            require_handle: Require tenant_handle to exist for this endpoint
            require_user: Require user ID to be set on the auth info
            org_permission: Optional additional org permission required for this endpoint
        """
        if allowed_services is None:
            allowed_services = []

        # Always allow the unspecified service identity for internal auth.
        if ServiceIdentity.UNSPECIFIED not in allowed_services:
            allowed_services.append(ServiceIdentity.UNSPECIFIED)
        self.permission = permission
        self.allow_disabled = allow_disabled
        self.allow_public = allow_public
        self.require_handle = require_handle
        self.require_user = require_user
        self.org_permission = org_permission
        self.allowed_services = allowed_services

        if self.permission is None and not self.allowed_services:
            raise AssertionError(
                "Either permission or allowed_services must be set for Authorize"
            )

    async def __call__(
        self,
        x_api_key: ApiKeyType,
        x_tenant_id: TenantIdType,
        authorization: AuthorizationType,
        x_service_key: Optional[str] = Depends(OptionalHeader("X-Service-Key")),
        x_user_id: Optional[str] = Depends(OptionalHeader("X-User-Id")),
        cookies: Optional[CookiesType] = Depends(get_cookies),
    ) -> Optional[AuthInfo]:
        try:
            auth_info = await verify_auth_info(
                info=UnverifiedAuthInfo(
                    x_api_key=x_api_key,
                    x_tenant_id=x_tenant_id,
                    authorization=authorization,
                    cookies=cookies,
                    x_service_key=x_service_key,
                    x_user_id=x_user_id,
                ),
                permission=[perm.value for perm in self.permission]
                if isinstance(self.permission, list)
                else self.permission.value
                if self.permission
                else None,
                org_permission=self.org_permission.value
                if self.org_permission
                else None,
                allow_disabled=self.allow_disabled,
                allowed_services=self.allowed_services,
            )
        except Exception as e:
            if self.allow_public:
                # Raise error if an api key is specified and
                if x_api_key and x_api_key.startswith("ls__"):
                    raise e
                return None
            else:
                raise e
        if self.require_user:
            if auth_info.user_id is None or auth_info.ls_user_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User ID required for this endpoint. Cannot use a service account.",
                )
        if self.require_handle:
            if auth_info.tenant_handle is None:
                async with asyncpg_conn() as db:
                    if tenant_handle := await db.fetchval(
                        "SELECT tenant_handle FROM tenants WHERE id = $1",
                        auth_info.tenant_id,
                    ):
                        auth_info.tenant_handle = tenant_handle

                if auth_info.tenant_handle is None:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Tenant handle required for this endpoint",
                    )
            return AuthInfoRequiredHandle(
                user_id=auth_info.user_id,
                ls_user_id=auth_info.ls_user_id,
                user_email=auth_info.user_email,
                user_full_name=auth_info.user_full_name,
                tenant_handle=auth_info.tenant_handle,
                tenant_id=auth_info.tenant_id,
                tenant_config=auth_info.tenant_config,
                identity_id=auth_info.identity_id,
                identity_permissions=auth_info.identity_permissions,
                organization_is_personal=auth_info.organization_is_personal,
                unverified=auth_info.unverified,
            )
        return auth_info


class OrgAuthorize:
    def __init__(
        self,
        permission: OrganizationPermissions | None,
        allow_disabled: bool = False,
        require_user: bool = False,
        allowed_services: list[ServiceIdentity] | None = None,
    ) -> None:
        self.permission = permission
        self.allow_disabled = allow_disabled
        self.require_user = require_user
        self.allowed_services = allowed_services if allowed_services is not None else []

    async def __call__(
        self,
        x_api_key: ApiKeyType,
        x_organization_id: OrganizationIdType,
        authorization: AuthorizationType,
        x_service_key: Optional[str] = Depends(OptionalHeader("X-Service-Key")),
        x_user_id: Optional[str] = Depends(OptionalHeader("X-User-Id")),
        cookies: Optional[CookiesType] = Depends(get_cookies),
    ) -> Optional[OrgAuthInfo]:
        auth_info = await verify_org_auth_info(
            UnverifiedAuthInfo(
                x_api_key=x_api_key,
                x_organization_id=x_organization_id,
                authorization=authorization,
                x_service_key=x_service_key,
                x_user_id=x_user_id,
                cookies=cookies,
            ),
            permission=self.permission.value if self.permission else None,
            allow_disabled=self.allow_disabled,
            allowed_services=self.allowed_services,
        )
        if self.require_user:
            if auth_info.user_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User ID required for this endpoint. Cannot use a service account.",
                )
        return auth_info


async def x_service_authorize(
    x_service_key: Optional[str] = Depends(OptionalHeader("X-Service-Key")),
):
    """Dependency to authorize requests using X-Service-Key"""
    if not x_service_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Service key required for this endpoint",
        )

    headers = {"X-Service-Key": x_service_key}
    await platform_request("GET", "/internal/verify", headers=headers)


@retry_asyncpg
async def get_share_run_info(share_token: UUID = Path()) -> ShareRunInfo:
    async with asyncpg_conn() as db:
        row = await db.fetchrow(
            "select tenant_id, run_id from share_keys where share_token = $1",
            share_token,
        )

    if row is None:
        raise HTTPException(
            status_code=404,
            detail="No run found associated with the share token",
        )

    run = await models.runs.fetch_ch.fetch_single_run(
        ShareRunInfo(
            run_id=row["run_id"], tenant_id=row["tenant_id"], child_run_ids=None
        ),
        UUID(row["run_id"].hex),
        [schemas.RunSelect.child_run_ids],
    )

    if run is None:
        raise HTTPException(
            status_code=404,
            detail="No run found associated with the share token",
        )

    return ShareRunInfo(
        run_id=run["id"],
        tenant_id=run["tenant_id"],
        child_run_ids=run.get("child_run_ids"),
        session_id=run["session_id"],
    )


@retry_asyncpg
async def get_share_dataset_info(share_token: UUID = Path()) -> ShareDatasetInfo:
    async with asyncpg_conn() as db:
        query = models.datasets.sql.get_datasets_by_share_token_sql
        dataset_record = await db.fetchrow(query, share_token)

    if dataset_record is None:
        raise HTTPException(
            status_code=404,
            detail="No dataset found associated with the share token",
        )
    return ShareDatasetInfo(
        dataset_id=dataset_record["id"], tenant_id=dataset_record["tenant_id"]
    )


@retry_asyncpg
async def get_feedback_token_info(token: UUID = Path()) -> FeedbackTokenInfo:
    async with asyncpg_conn() as db:
        row = await db.fetchrow(
            """SELECT tenant_id, run_id, feedback_key
            FROM feedback_ingest_tokens
            WHERE id = $1 AND expires_at > now()""",
            token,
        )
        if not row:
            raise HTTPException(
                status_code=404,
                detail="Token not found or expired",
            )
    async with redis.aredis_routed_pool(
        str(row["tenant_id"]), redis.RedisOperation.READ
    ) as aredis:
        session_id, trace_id, start_time = await aredis.hmget(
            f"smith:runs:pending:{row['tenant_id']}:{row['run_id']}",
            "session_id",
            "trace_id",
            "start_time",
        )
        session_id = orjson.loads(session_id) if session_id else None
        trace_id = orjson.loads(trace_id) if trace_id else None
        start_time = orjson.loads(start_time) if start_time else None
    return FeedbackTokenInfo(
        run_id=row["run_id"],
        feedback_key=row["feedback_key"],
        tenant_id=row["tenant_id"],
        session_id=session_id,
        start_time=start_time,
        trace_id=trace_id,
    )
