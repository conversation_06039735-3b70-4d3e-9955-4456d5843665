"""Initialize routers."""

import structlog
from lc_database import api_router as router
from lc_database import redis
from lc_database.curl import platform_request
from lc_database.database import asyncpg_conn

from app.api.endpoints import (
    admin_panel,
    annotation_queues,
    api_keys,
    auth,
    bulk_exports,
    custom_charts,
    custom_code,
    datasets,
    examples,
    feedback,
    feedback_configs,
    info,
    internal,
    model_price_map,
    orgs,
    playground_settings,
    prompt_webhooks,
    prompts,
    public,
    runs,
    service_accounts,
    tenants,
    tracer_sessions,
    ttl_settings,
    usage_limits,
    workspaces,
)
from app.config import settings

logger = structlog.get_logger(__name__)

api_router = router.TrailingSlashRouter()
api_router.include_router(
    tracer_sessions.router, prefix="/sessions", tags=["tracer-sessions"]
)
api_router.include_router(orgs.router, prefix="/orgs", tags=["orgs"])
api_router.include_router(auth.router, tags=["auth"])
api_router.include_router(api_keys.router, prefix="/api-key", tags=["api-key"])
api_router.include_router(examples.router, prefix="/examples", tags=["examples"])
api_router.include_router(datasets.router, prefix="/datasets", tags=["datasets"])
api_router.include_router(runs.router, prefix="/runs", tags=["run"])
api_router.include_router(feedback.router, prefix="/feedback", tags=["feedback"])
api_router.include_router(public.router, prefix="/public", tags=["public"])
api_router.include_router(
    annotation_queues.router, prefix="/annotation-queues", tags=["annotation-queues"]
)
api_router.include_router(custom_code.router, prefix="/ace", tags=["ace"])
api_router.include_router(
    bulk_exports.router, prefix="/bulk-exports", tags=["bulk-exports"]
)
api_router.include_router(tenants.router, prefix="/tenants", tags=["tenant"])
api_router.include_router(info.router, prefix="/info", tags=["info"])
api_router.include_router(
    feedback_configs.router, prefix="/feedback-configs", tags=["feedback-configs"]
)
api_router.include_router(
    model_price_map.router, prefix="/model-price-map", tags=["model-price-map"]
)
api_router.include_router(
    usage_limits.router, prefix="/usage-limits", tags=["usage-limits"]
)
api_router.include_router(
    ttl_settings.router, prefix="/ttl-settings", tags=["ttl-settings"]
)
api_router.include_router(prompts.router, prefix="/prompts", tags=["prompts"])
api_router.include_router(
    prompt_webhooks.router, prefix="/prompt-webhooks", tags=["prompt-webhooks"]
)
api_router.include_router(workspaces.router, prefix="/workspaces", tags=["workspaces"])
api_router.include_router(
    admin_panel.router,
    prefix="/admin-panel",
    tags=["admin-panel"],
    include_in_schema=False,
)

api_router.include_router(
    playground_settings.router,
    prefix="/playground-settings",
    tags=["playground-settings"],
)

api_router.include_router(
    internal.router,
    prefix="/internal",
    tags=["internal"],
    include_in_schema=False,
)

api_router.include_router(
    service_accounts.router,
    prefix="/service-accounts",
    tags=["service-accounts"],
)

api_router.include_router(
    custom_charts.router,
    prefix="/charts",
    tags=["charts"],
)

api_router.include_router(
    custom_charts.org_router,
    prefix="/org-charts",
    tags=["charts"],
)


@api_router.get("/ok")
async def ok():
    return {"ok": True}


@api_router.get("/health", include_in_schema=False)
async def health():
    # Check if go-client is healthy
    if "platform-backend" not in settings.SKIP_HEALTH_SERVICES:
        if settings.LOG_HEALTH_CHECKS:
            await logger.ainfo("Hitting platform-backend")
        await platform_request("GET", "/ok")
    if "postgres" not in settings.SKIP_HEALTH_SERVICES:
        if settings.LOG_HEALTH_CHECKS:
            await logger.ainfo("Hitting postgres")
        # Check if we can connect to pg
        async with asyncpg_conn() as db:
            await db.execute("SELECT 1")

    if "redis" not in settings.SKIP_HEALTH_SERVICES:
        if settings.REDIS_SHARD_URIS and len(settings.REDIS_SHARD_URIS) > 1:
            if settings.LOG_HEALTH_CHECKS:
                await logger.ainfo("Hitting redis shards")
            async with redis.aredis_sharded_pools() as sharded_pools:
                for node in sharded_pools.nodes.values():
                    node["instance"].ping()
        else:
            # Don't duplicate pings - in sharded Redis, we'll ping the original, which is one of the shards
            if settings.LOG_HEALTH_CHECKS:
                await logger.ainfo("Hitting redis")
            # Check if we can connect to redis
            async with redis.aredis_pool() as aredis:
                await aredis.ping()
        return {"ok": True}


@api_router.get("/health/platform", include_in_schema=False)
async def health_platform():
    # Check if go-client is healthy
    await platform_request("GET", "/ok")
    return {"ok": True}


@api_router.get("/health/postgres", include_in_schema=False)
async def health_postgres():
    # Check if we can connect to pg
    async with asyncpg_conn() as db:
        await db.execute("SELECT 1")
    return {"ok": True}


@api_router.get("/health/redis", include_in_schema=False)
async def health_redis():
    # Check if we can connect to redis
    if settings.REDIS_SHARD_URIS and len(settings.REDIS_SHARD_URIS) > 1:
        async with redis.aredis_sharded_pools() as sharded_pools:
            for node in sharded_pools.nodes.values():
                node["instance"].ping()
    else:
        # Don't duplicate pings - in sharded Redis, we'll ping the original, which is one of the shards
        async with redis.aredis_pool() as aredis:
            await aredis.ping()
    return {"ok": True}
