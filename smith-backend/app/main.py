"""Initialize the app."""

import asyncio
import json
import logging
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Any, AsyncItera<PERSON>, Dict

import orjson
import uvloop
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.openapi.utils import get_openapi
from lc_config import settings, tracing
from lc_database.exceptions import is_retriable_exception
from lc_logging.cors import CustomCORSMiddleware
from lc_metrics.metrics_middleware import PrometheusMiddleware, metrics
from pydantic.v1 import ValidationError
from starlette import status
from starlette.requests import Request
from starlette.responses import JSONResponse
from starlette_context.middleware import RawContextMiddleware

from app.cache import CacheControl, CacheControlMiddleware
from app.models.runs.validate import (
    SCHEMA_BATCH_STR,
    SCHEMA_PATCH_STR,
    SCHEMA_POST_STR,
)

tracing.maybe_setup_datadog()

import structlog  # noqa: E402
from lc_logging import initialize  # noqa: E402

initialize.init_logging(settings.shared_settings)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
structured_logger = structlog.getLogger(__name__)

asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

from fastapi import FastAPI, HTTPException  # noqa: E402
from fastapi.exception_handlers import http_exception_handler  # noqa: E402
from fastapi.responses import ORJSONResponse  # noqa: E402
from lc_database.clickhouse import (  # noqa: E402
    ClickhouseClient,
    close_clickhouse_client,
    initialize_clickhouse_client,
)
from lc_database.database import (  # noqa: E402
    close_global_asyncpg_pool,
    create_global_asyncpg_pool,
)
from lc_database.elasticsearch import (  # noqa: E402
    close as close_elastic_client,
)
from lc_database.redis import (  # noqa: E402
    close_global_aredis_pools,
    close_global_async_queues,
    close_global_cluster_aredis_pools,
    create_global_aredis_sharded_pools,
    redis_pool,
)
from lc_database.s3_client import close_global_s3_client  # noqa: E402
from lc_logging.exception_logger import ExceptionLoggingMiddleware  # noqa: E402

from app.api.api import api_router  # noqa: E402
from app.compress import CompressionMiddleware  # noqa: E402
from app.config import settings  # noqa: E402
from app.hub.api.api import api_router as hub_api_router  # noqa: E402


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator[None]:
    await startup_event()
    try:
        yield
    finally:
        await shutdown_event()


app = FastAPI(
    title=settings.PROJECT_NAME,
    default_response_class=ORJSONResponse,
    servers=[
        {"url": settings.LANGCHAIN_ENDPOINT, "description": "LangSmith API endpoint."}
    ]
    if settings.LANGCHAIN_ENDPOINT
    else None,
    docs_url=f"{settings.DOCS_PREFIX}/docs",
    redoc_url=f"{settings.DOCS_PREFIX}/redoc",
    openapi_url=f"{settings.DOCS_PREFIX}/openapi.json",
    lifespan=lifespan,
)


######################
# Exception Handling #
######################
@app.exception_handler(ValidationError)
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(
    request: Request, exc: RequestValidationError | ValidationError
):
    # Get the original 'detail' list of errors
    details = exc.errors()
    modified_details = []
    # Replace 'msg' with 'message' for each error
    for error in details:
        # Combine location and message into a single string
        loc_str = [str(loc) for loc in error.get("loc", [])]
        modified_details.append(f"{'.'.join(loc_str)}: {error.get('msg')}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": modified_details}),
    )


@app.exception_handler(ExceptionGroup)
async def exception_group_handler(request: Request, exc: ExceptionGroup):
    if len(exc.exceptions) > 0 and all(
        isinstance(e, HTTPException) for e in exc.exceptions
    ):
        return await http_exception_handler(request, exc.exceptions[0])
    logger.error(
        "Exception in request",
        exc_info=exc.exceptions[0] if len(exc.exceptions) == 1 else exc,
    )
    return JSONResponse(status_code=500, content={"detail": "Internal server error"})


##############
# Middleware #
##############
# Note: Middleware is loaded back to front in FastAPI
# so add_context is run first, then catch_exceptions, ...


if settings.MIDDLEWARE_ENABLED:
    app.add_middleware(CompressionMiddleware, minimum_size=24000)  # 24KB
    # Has to run before Exception logging middleware so that exceptions are added to metrics.
    if settings.METRICS_ENABLED:
        app.add_middleware(PrometheusMiddleware, app_name="backend")
    if settings.METRICS_ENDPOINT_ENABLED:
        app.add_route("/metrics", metrics)
    app.add_middleware(
        ExceptionLoggingMiddleware,
        logger=structured_logger,
        is_retriable_exception=is_retriable_exception,
        exclude_paths=settings.EXCEPTION_LOGGER_EXCLUDE_PATHS,
    )
    app.add_middleware(RawContextMiddleware)
    app.add_middleware(
        CacheControlMiddleware,
        cache_control=CacheControl("no-cache"),
        options_cache_control=CacheControl("public", max_age=600),
    )
    # Set all CORS enabled origins.
    # This is needed in addition to nginx unit for error responses
    app.add_middleware(
        CustomCORSMiddleware,
        allow_origins=settings.CORS_ALLOWED_ORIGINS.split(",")
        if not settings.CORS_ALLOWED_ORIGINS_REGEX
        else (),
        allow_origin_regex=settings.CORS_ALLOWED_ORIGINS_REGEX,
        cors_always_allow_paths_regex=settings.CORS_ALWAYS_ALLOW_PATHS_REGEX,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["*"],
    )


app.include_router(api_router, include_in_schema=False)
app.include_router(api_router, prefix="/api/v1")
app.include_router(hub_api_router, include_in_schema=False)
app.include_router(hub_api_router, prefix="/api/v1")


################
# OpenAPI Spec #
################
def _add_schema_ref(
    openapi_schema: Dict[str, Any],
    path: str,
    http_method: str,
    schema_name: str,
    schema: str,
):
    try:
        openapi_schema["paths"][path][http_method]["requestBody"] = {
            "required": True,
            "content": {
                "application/json": {
                    "schema": {"$ref": f"#/components/schemas/{schema_name}"}
                }
            },
        }
        openapi_schema["components"]["schemas"][schema_name] = orjson.loads(schema)
    except Exception:
        logger.exception(f"Could not set schema for {http_method} {path} {schema_name}")


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="LangSmith",
        version="0.1.0",
        routes=app.routes,
    )

    # set schema refs for POST, PATCH, and batch ingest since we have custom body handling
    # for these endpoints that makes Pydantic usage difficult
    _add_schema_ref(
        openapi_schema, "/api/v1/runs", "post", "CreateRunRequest", SCHEMA_POST_STR
    )
    _add_schema_ref(
        openapi_schema,
        "/api/v1/runs/batch",
        "post",
        "BatchIngestRunsRequest",
        SCHEMA_BATCH_STR,
    )
    _add_schema_ref(
        openapi_schema,
        "/api/v1/runs/{run_id}",
        "patch",
        "UpdateRunRequest",
        SCHEMA_PATCH_STR,
    )

    # merge with go endpoints
    with open(Path(__file__).parents[1] / "static" / "smith-go-swagger.json", "r") as f:
        smith_go_swagger = json.load(f)
    smith_go_openapi_v3 = _swagger_to_openapi_v3(smith_go_swagger)
    openapi_schema["paths"] = {
        **openapi_schema["paths"],
        **smith_go_openapi_v3["paths"],
    }
    openapi_schema["definitions"] = {
        **openapi_schema.get("definitions", {}),
        **smith_go_openapi_v3["definitions"],
    }
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


async def startup_event() -> None:
    """Initialize the database."""
    structured_logger.info("Starting up")
    structured_logger.info("Connecting to Redis")
    redis_pool.ping()
    structured_logger.info("Connecting to Postgres")
    await create_global_asyncpg_pool()
    structured_logger.info("Connecting to Clickhouse")
    await initialize_clickhouse_client()
    if settings.FF_CLICKHOUSE_USE_MULTISERVICE and settings.FF_CH_IN_APP_ANALYTICS:
        structured_logger.info("Initializing in-app analytics clickhouse client")
        await initialize_clickhouse_client(ClickhouseClient.USER_QUERIES)
    if settings.FF_CLICKHOUSE_USE_MULTISERVICE and settings.FF_CH_INTERNAL_ANALYTICS:
        structured_logger.info("Initializing internal analytics clickhouse client")
        await initialize_clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW)
    if settings.FF_CLICKHOUSE_USE_MULTISERVICE and settings.FF_CH_IN_APP_STATS:
        structured_logger.info("Initializing in-app stats clickhouse client")
        await initialize_clickhouse_client(ClickhouseClient.USER_ANALYTICS)
    if settings.REDIS_SHARD_URIS:
        structured_logger.info("Creating sharded Redis pools")
        create_global_aredis_sharded_pools()
    structured_logger.info("Startup complete")


async def shutdown_event() -> None:
    try:
        await close_global_asyncpg_pool()
    except Exception as e:
        structured_logger.exception(f"Error closing asyncpg pool {e}")
    try:
        await close_global_aredis_pools()
    except Exception as e:
        structured_logger.exception(f"Error closing aredis {e}")
    try:
        redis_pool.close()
    except Exception as e:
        structured_logger.exception(f"Error closing redis {e}")
    try:
        await close_global_async_queues()
    except Exception as e:
        structured_logger.exception(f"Error closing async queue {e}")
    try:
        await close_clickhouse_client()
    except Exception as e:
        structured_logger.exception(f"Error closing httpx client for clickhouse {e}")
    try:
        await close_global_cluster_aredis_pools()
    except Exception as e:
        structured_logger.exception(f"Error closing cluster aredis {e}")
    try:
        await close_global_s3_client()
    except Exception as e:
        structured_logger.exception(f"Error closing s3 client {e}")
    try:
        await close_elastic_client()
    except Exception as e:
        structured_logger.exception(f"Error closing elastic client {e}")
    if settings.FF_CLICKHOUSE_USE_MULTISERVICE and settings.FF_CH_IN_APP_ANALYTICS:
        try:
            await close_clickhouse_client(ClickhouseClient.USER_QUERIES)
        except Exception as e:
            structured_logger.exception(
                f"Error closing in-app analytics clickhouse client {e}"
            )
    if settings.FF_CLICKHOUSE_USE_MULTISERVICE and settings.FF_CH_INTERNAL_ANALYTICS:
        try:
            await close_clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW)
        except Exception as e:
            structured_logger.exception(
                f"Error closing internal analytics clickhouse client {e}"
            )
    if settings.FF_CLICKHOUSE_USE_MULTISERVICE and settings.FF_CH_IN_APP_STATS:
        try:
            await close_clickhouse_client(ClickhouseClient.USER_ANALYTICS)
        except Exception as e:
            structured_logger.exception(
                f"Error closing in-app stats clickhouse client {e}"
            )


def _swagger_to_openapi_v3(swagger: dict) -> dict:
    for path in swagger["paths"].values():
        for method in path.values():
            for response in method["responses"].values():
                schema = response.pop("schema")
                response["content"] = {"application/json": {"schema": schema}}
            multipart_properties: dict = {}
            required: list = []
            request_body: dict = {
                "required": True,
                "content": {
                    "multipart/form-data": {
                        "schema": {
                            "type": "object",
                            "properties": multipart_properties,
                            "required": required,
                        }
                    }
                },
            }
            for param in method["parameters"]:
                if param["in"] == "formData":
                    multipart_properties[param["name"]] = {"type": param["type"]}
                    if param.get("format"):
                        multipart_properties[param["name"]]["format"] = param["format"]
                    if param.get("description"):
                        multipart_properties[param["name"]]["description"] = param[
                            "description"
                        ]
                    if param.get("required"):
                        required.append(param["name"])
            method["parameters"] = [
                p for p in method["parameters"] if p["in"] != "formData"
            ]
            if request_body["content"]["multipart/form-data"]["schema"]["properties"]:
                method["requestBody"] = request_body
    return swagger


if __name__ == "__main__":
    import uvicorn

    # Set the environment to local_test in debugger mode so that we can skip
    # db migrations
    settings.LANGCHAIN_ENV = "local_test"
    # We still allow uvicorn locally for testing purposes
    uvicorn.run(app, host="0.0.0.0", port=1984, http="httptools", loop="uvloop")
