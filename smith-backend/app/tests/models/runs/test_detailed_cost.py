from decimal import Decimal

from app.models.runs.ingest import _detailed_cost


def test_detailed_cost():
    """Comprehensive test function for detailed_cost covering all edge cases."""
    # Test 1: Normal case - exact match (all tokens accounted for)
    total_tokens = 175
    token_breakdown = {"input": 100, "output": 50, "cache": 25}
    price_breakdown = {
        "input": Decimal("0.005"),
        "output": Decimal("0.015"),
        "cache": Decimal("0.001"),
    }
    default_price = Decimal("0.01")

    total_cost, cost_breakdown = _detailed_cost(
        total_tokens, token_breakdown, price_breakdown, default_price
    )
    expected_total = Decimal("0.500") + Decimal("0.750") + Decimal("0.025")  # 1.275
    expected_breakdown = {
        "input": Decimal("0.500"),
        "output": Decimal("0.750"),
        "cache": Decimal("0.025"),
    }

    assert total_cost == expected_total, (
        f"Test 1 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        f"Test 1 failed: expected {expected_breakdown}, got {cost_breakdown}"
    )

    # Test 2: Excess total tokens (unaccounted tokens charged at default price)
    total_tokens = 200  # 25 more than accounted
    total_cost, cost_breakdown = _detailed_cost(
        total_tokens, token_breakdown, price_breakdown, default_price
    )
    expected_total = Decimal("1.275") + Decimal("25") * Decimal("0.01")  # 1.525

    assert total_cost == expected_total, (
        f"Test 2 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        "Test 2 failed: breakdown should be same as test 1"
    )

    # Test 3: Deficit total tokens (negative unaccounted tokens)
    total_tokens = 150  # 25 less than accounted
    total_cost, cost_breakdown = _detailed_cost(
        total_tokens, token_breakdown, price_breakdown, default_price
    )
    expected_total = Decimal("1.275") - Decimal("25") * Decimal("0.01")  # 1.025

    assert total_cost == expected_total, (
        f"Test 3 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        "Test 3 failed: breakdown should be same as test 1"
    )

    # Test 4: Empty token breakdown
    total_tokens = 100
    empty_token_breakdown = {}
    total_cost, cost_breakdown = _detailed_cost(
        total_tokens, empty_token_breakdown, price_breakdown, default_price
    )
    expected_total = Decimal("100") * default_price
    expected_breakdown = None

    assert total_cost == expected_total, (
        f"Test 4 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        f"Test 4 failed: expected {expected_breakdown}, got {cost_breakdown}"
    )

    # Test 5: Empty price breakdown
    total_tokens = 175
    empty_price_breakdown = {}
    total_cost, cost_breakdown = _detailed_cost(
        total_tokens, token_breakdown, empty_price_breakdown, default_price
    )
    expected_total = Decimal("175") * default_price
    expected_breakdown = None

    assert total_cost == expected_total, (
        f"Test 5 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        f"Test 5 failed: expected {expected_breakdown}, got {cost_breakdown}"
    )

    # Test 6: Both breakdowns empty
    empty_token_breakdown = {}
    empty_price_breakdown = {}
    total_cost, cost_breakdown = _detailed_cost(
        total_tokens, empty_token_breakdown, empty_price_breakdown, default_price
    )
    expected_total = Decimal("175") * default_price
    expected_breakdown = None

    assert total_cost == expected_total, (
        f"Test 6 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        f"Test 6 failed: expected {expected_breakdown}, got {cost_breakdown}"
    )

    # Test 7: Partial key overlap
    partial_token_breakdown = {
        "input": 100,
        "output": 50,
        "special": 30,
    }  # 'special' not in price_breakdown
    partial_price_breakdown = {
        "input": Decimal("0.005"),
        "output": Decimal("0.015"),
        "cache": Decimal("0.001"),
    }  # 'cache' not in token_breakdown
    total_tokens = 200

    total_cost, cost_breakdown = _detailed_cost(
        total_tokens, partial_token_breakdown, partial_price_breakdown, default_price
    )
    expected_total = (
        Decimal("0.500")
        + Decimal("0.750")
        + (Decimal("200") - Decimal("150")) * default_price
    )  # 1.75
    expected_breakdown = {"input": Decimal("0.500"), "output": Decimal("0.750")}

    assert total_cost == expected_total, (
        f"Test 7 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        f"Test 7 failed: expected {expected_breakdown}, got {cost_breakdown}"
    )

    # Test 8: No key overlap
    no_overlap_token_breakdown = {"special1": 50, "special2": 25}
    no_overlap_price_breakdown = {"input": Decimal("0.005"), "output": Decimal("0.015")}
    total_tokens = 100

    total_cost, cost_breakdown = _detailed_cost(
        total_tokens,
        no_overlap_token_breakdown,
        no_overlap_price_breakdown,
        default_price,
    )
    expected_total = Decimal("100") * default_price
    expected_breakdown = None

    assert total_cost == expected_total, (
        f"Test 8 failed: expected {expected_total}, got {total_cost}"
    )
    assert cost_breakdown == expected_breakdown, (
        f"Test 8 failed: expected {expected_breakdown}, got {cost_breakdown}"
    )
