# serializer version: 1
# name: test_stats_query[group_by_metadata-query_params2-feedback]
  '''
  WITH 
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ),
          
  
          feedback_stats AS (
              SELECT 
  feedbacks_rmt.group_name as group_name,
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM (
                  SELECT 
                      feedbacks_rmt.id, 
                      feedbacks_rmt.key, 
                      feedbacks_rmt.score, 
                      feedbacks_rmt.value, 
                      feedbacks_rmt.correction, 
                      feedbacks_rmt.extra, 
                      feedbacks_rmt.feedback_source,
                      feedbacks_rmt.start_time,
                      filtered_runs_metadata_kv.value as group_name
                  FROM feedbacks_rmt FINAL
                  JOIN (
                      SELECT DISTINCT
                          tenant_id,
                          session_id,
                          is_root,
                          start_time,
                          run_id,
                          value
                      FROM runs_metadata_kv
                      WHERE
                          value IN (SELECT group_name FROM top_k_groups)
                          AND key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
                  ) as filtered_runs_metadata_kv ON 
  feedbacks_rmt.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  feedbacks_rmt.session_id = filtered_runs_metadata_kv.session_id AND
  feedbacks_rmt.is_root = filtered_runs_metadata_kv.is_root AND
  feedbacks_rmt.start_time = filtered_runs_metadata_kv.start_time AND
  feedbacks_rmt.run_id = filtered_runs_metadata_kv.run_id
                  WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as feedbacks_rmt
              
  GROUP BY group_name
  
          )
  
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[group_by_metadata-query_params2-input]
  '''
  WITH
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ),
          
  
      input_kv_stats AS (
          SELECT 
  runs_inputs_kv.group_name as group_name,
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM (
              SELECT 
                  runs_inputs_kv.start_time, 
                  runs_inputs_kv.key, 
                  runs_inputs_kv.value, 
                  filtered_runs_metadata_kv.value as group_name 
              FROM runs_inputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      value
                  FROM runs_metadata_kv
                  WHERE
                      value IN (SELECT group_name FROM top_k_groups)
                      AND key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as filtered_runs_metadata_kv ON 
  runs_inputs_kv.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  runs_inputs_kv.session_id = filtered_runs_metadata_kv.session_id AND
  runs_inputs_kv.is_root = filtered_runs_metadata_kv.is_root AND
  runs_inputs_kv.start_time = filtered_runs_metadata_kv.start_time AND
  runs_inputs_kv.run_id = filtered_runs_metadata_kv.run_id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_inputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[group_by_metadata-query_params2-metadata]
  '''
  WITH
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ),
          
  
      metadata_stats AS (
          SELECT 
  runs_metadata_kv.value as group_name,
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM runs_metadata_kv
          WHERE
              (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              AND value IN (SELECT group_name from top_k_groups)
              AND key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          
  GROUP BY group_name
  
      )
  
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[group_by_metadata-query_params2-output]
  '''
  WITH
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ),
          
  
      output_kv_stats AS (
          SELECT 
  runs_outputs_kv.group_name as group_name,
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM (
              SELECT 
                  runs_outputs_kv.start_time, 
                  runs_outputs_kv.key, 
                  runs_outputs_kv.value, 
                  filtered_runs_metadata_kv.value as group_name 
              FROM runs_outputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      value
                  FROM runs_metadata_kv
                  WHERE
                      value IN (SELECT group_name FROM top_k_groups)
                      AND key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as filtered_runs_metadata_kv ON 
  runs_outputs_kv.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  runs_outputs_kv.session_id = filtered_runs_metadata_kv.session_id AND
  runs_outputs_kv.is_root = filtered_runs_metadata_kv.is_root AND
  runs_outputs_kv.start_time = filtered_runs_metadata_kv.start_time AND
  runs_outputs_kv.run_id = filtered_runs_metadata_kv.run_id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_outputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[group_by_metadata-query_params2-run_latency]
  '''
  WITH
  
                  
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ),
          
  
                  run_stats AS (
                      SELECT 
  runs.group_name as group_name,
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
                      FROM (
                          SELECT 
                              start_time, 
                              id, 
                              end_time, 
                              filtered_runs_metadata_kv.value as group_name
                          FROM runs FINAL
                          JOIN (
                              SELECT 
                                  tenant_id,
                                  session_id,
                                  is_root,
                                  start_time,
                                  run_id,
                                  value
                              FROM runs_metadata_kv FINAL
                              WHERE
                                  value IN (SELECT group_name FROM top_k_groups)
                                  AND key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
                          ) as filtered_runs_metadata_kv ON 
  runs.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  runs.session_id = filtered_runs_metadata_kv.session_id AND
  runs.is_root = filtered_runs_metadata_kv.is_root AND
  runs.start_time = filtered_runs_metadata_kv.start_time AND
  runs.id = filtered_runs_metadata_kv.run_id
                          WHERE  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
                      ) as runs
                      
  GROUP BY group_name
  
                  ) 
  
                  SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[group_by_metadata-query_params2-run_stats]
  '''
  WITH
                      
                      
          
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ),
          
          topk_runs_with_group_name AS (
              SELECT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  value
              FROM runs_metadata_kv
              WHERE
                  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
                  AND value IN (SELECT group_name FROM top_k_groups)
          )
      
                  SELECT 
  runs.group_name as group_name,
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
                  FROM (
                      SELECT DISTINCT
                          runs.start_time, 
                          runs.id,
                          runs.end_time,
                          runs.status, 
                          runs.name, 
                          runs.run_type, 
                          runs.tags, 
                          topk_runs_with_group_name.value as group_name
                      FROM runs
                      JOIN topk_runs_with_group_name ON 
  runs.tenant_id = topk_runs_with_group_name.tenant_id AND
  runs.session_id = topk_runs_with_group_name.session_id AND
  runs.is_root = topk_runs_with_group_name.is_root AND
  runs.start_time = topk_runs_with_group_name.start_time AND
  runs.id = topk_runs_with_group_name.run_id
                      WHERE  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
                  ) as runs
                  
  GROUP BY group_name
  '''
# ---
# name: test_stats_query[group_by_metadata-query_params2-token_counts]
  '''
  WITH
  
          filtered_runs_metadata_kv AS (
              SELECT DISTINCT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  value
              FROM runs_metadata_kv
              WHERE
                  value in (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          )
                  AND key = {group_by_metadata_key__eq}  AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ),
  
          token_counts AS (
              SELECT 
                  filtered_runs_metadata_kv.value as group_name,
                  
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  
              FROM filtered_runs_metadata_kv
              LEFT JOIN (
                  SELECT
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      total_tokens,
                      prompt_tokens,
                      completion_tokens,
                      total_cost,
                      prompt_cost,
                      completion_cost,
                      first_token_time
                  FROM runs_token_counts FINAL
                  WHERE
                      runs_token_counts.total_tokens < 4000000000
                      AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND id = source_id
              ) as runs_token_counts ON
                  
  filtered_runs_metadata_kv.tenant_id = runs_token_counts.tenant_id AND
  filtered_runs_metadata_kv.session_id = runs_token_counts.session_id AND
  filtered_runs_metadata_kv.is_root = runs_token_counts.is_root AND
  filtered_runs_metadata_kv.start_time = runs_token_counts.start_time AND
  filtered_runs_metadata_kv.run_id = runs_token_counts.id
              GROUP BY id, group_name
          ),
  
          token_count_stats AS (
              SELECT 
                  
  token_counts.group_name as group_name,
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
              FROM token_counts
              
  GROUP BY group_name
  
          )
  
          SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
# name: test_stats_query[group_by_name-query_params5-feedback]
  '''
  WITH 
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
          feedback_stats AS (
              SELECT 
  feedbacks_rmt.group_name as group_name,
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM (
                  SELECT 
                      feedbacks_rmt.id, 
                      feedbacks_rmt.key, 
                      feedbacks_rmt.score, 
                      feedbacks_rmt.value, 
                      feedbacks_rmt.correction, 
                      feedbacks_rmt.extra, 
                      feedbacks_rmt.feedback_source,
                      feedbacks_rmt.start_time,
                      filtered_runs.name as group_name
                  FROM feedbacks_rmt FINAL
                  JOIN (
                      SELECT DISTINCT
                          tenant_id,
                          session_id,
                          is_root,
                          start_time,
                          id,
                          name
                      FROM runs
                      WHERE
                          name IN (SELECT group_name FROM top_k_groups)
                          AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
                  ) as filtered_runs ON 
  feedbacks_rmt.tenant_id = filtered_runs.tenant_id AND
  feedbacks_rmt.session_id = filtered_runs.session_id AND
  feedbacks_rmt.is_root = filtered_runs.is_root AND
  feedbacks_rmt.start_time = filtered_runs.start_time AND
  feedbacks_rmt.run_id = filtered_runs.id
                  WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as feedbacks_rmt
              
  GROUP BY group_name
  
          )
  
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[group_by_name-query_params5-input]
  '''
  WITH
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      input_kv_stats AS (
          SELECT 
  runs_inputs_kv.group_name as group_name,
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM (
              SELECT 
                  runs_inputs_kv.start_time, 
                  runs_inputs_kv.key, 
                  runs_inputs_kv.value, 
                  filtered_runs.name as group_name 
              FROM runs_inputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_runs ON 
  runs_inputs_kv.tenant_id = filtered_runs.tenant_id AND
  runs_inputs_kv.session_id = filtered_runs.session_id AND
  runs_inputs_kv.is_root = filtered_runs.is_root AND
  runs_inputs_kv.start_time = filtered_runs.start_time AND
  runs_inputs_kv.run_id = filtered_runs.id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_inputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[group_by_name-query_params5-metadata]
  '''
  WITH
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      metadata_stats AS (
          SELECT 
  runs_metadata_kv.group_name as group_name,
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM (
              SELECT 
                  runs_metadata_kv.start_time, 
                  runs_metadata_kv.key, 
                  runs_metadata_kv.value, 
                  filtered_runs.name as group_name 
              FROM runs_metadata_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_runs ON 
  runs_metadata_kv.tenant_id = filtered_runs.tenant_id AND
  runs_metadata_kv.session_id = filtered_runs.session_id AND
  runs_metadata_kv.is_root = filtered_runs.is_root AND
  runs_metadata_kv.start_time = filtered_runs.start_time AND
  runs_metadata_kv.run_id = filtered_runs.id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_metadata_kv
          
  GROUP BY group_name
  
      )
  
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[group_by_name-query_params5-output]
  '''
  WITH
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      output_kv_stats AS (
          SELECT 
  runs_outputs_kv.group_name as group_name,
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM (
              SELECT 
                  runs_outputs_kv.start_time, 
                  runs_outputs_kv.key, 
                  runs_outputs_kv.value, 
                  filtered_runs.name as group_name 
              FROM runs_outputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_runs ON 
  runs_outputs_kv.tenant_id = filtered_runs.tenant_id AND
  runs_outputs_kv.session_id = filtered_runs.session_id AND
  runs_outputs_kv.is_root = filtered_runs.is_root AND
  runs_outputs_kv.start_time = filtered_runs.start_time AND
  runs_outputs_kv.run_id = filtered_runs.id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_outputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[group_by_name-query_params5-run_latency]
  '''
  WITH
  
                  
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
                  run_stats AS (
                      SELECT 
  runs.name as group_name,
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
                      FROM runs FINAL
                      WHERE
                          name IN (SELECT group_name FROM top_k_groups)
                          AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
                      
  GROUP BY group_name
  
                  )
  
                  SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[group_by_name-query_params5-run_stats]
  '''
  WITH
                  
                  top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          )
  
                  SELECT 
  runs.name as group_name,
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
                  FROM (
                      SELECT DISTINCT
                          start_time,
                          id,
                          end_time,
                          status,
                          name,
                          run_type,
                          tags
                      FROM runs
                      WHERE 
                          runs.name IN (SELECT group_name from top_k_groups)
                          AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) 
                  ) as runs
                  
  GROUP BY group_name
  '''
# ---
# name: test_stats_query[group_by_name-query_params5-token_counts]
  '''
  WITH top_k_runs AS (
          SELECT DISTINCT
              tenant_id,
              session_id,
              is_root,
              start_time,
              id,
              name as group_name
          FROM runs
          WHERE 
               ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
              AND name IN (
                  SELECT 
                      arrayJoin(topK(5)(name)) as group_name
                  FROM runs
                  WHERE
                        ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              )
      ),
  
      token_counts AS (
          SELECT 
              
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  ,
              top_k_runs.group_name
          FROM top_k_runs
          LEFT JOIN (
              SELECT 
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  id,
                  total_tokens,
                  prompt_tokens,
                  completion_tokens,
                  total_cost,
                  prompt_cost,
                  completion_cost,
                  first_token_time 
              FROM runs_token_counts FINAL
              WHERE 
                  runs_token_counts.total_tokens < 4000000000 AND
                  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND id = source_id
          ) as runs_token_counts ON
              
  runs_token_counts.tenant_id = top_k_runs.tenant_id AND
  runs_token_counts.session_id = top_k_runs.session_id AND
  runs_token_counts.is_root = top_k_runs.is_root AND
  runs_token_counts.start_time = top_k_runs.start_time AND
  runs_token_counts.id = top_k_runs.id
          WHERE 
              (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          GROUP BY id, group_name
      ),
  
      token_count_stats AS (
          SELECT 
              
  token_counts.group_name as group_name,
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
          from token_counts
          
  GROUP BY group_name
  
      )
  
      SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
# name: test_stats_query[is_root-query_params0-feedback]
  '''
  WITH feedback_stats as (
              SELECT
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM feedbacks_rmt FINAL 
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          )
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[is_root-query_params0-input]
  '''
  WITH input_kv_stats as (
          SELECT
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM runs_inputs_kv WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND ((trace_first_received_at IS NOT NULL AND trace_ttl_seconds IS NOT NULL AND trace_first_received_at < now() - toIntervalSecond(trace_ttl_seconds)) = 0)
      )
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[is_root-query_params0-metadata]
  '''
  WITH metadata_stats as (
          SELECT
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM runs_metadata_kv WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND ((trace_first_received_at IS NOT NULL AND trace_ttl_seconds IS NOT NULL AND trace_first_received_at < now() - toIntervalSecond(trace_ttl_seconds)) = 0)
      )
      
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[is_root-query_params0-output]
  '''
  WITH output_kv_stats as (
          SELECT
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM runs_outputs_kv WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND ((trace_first_received_at IS NOT NULL AND trace_ttl_seconds IS NOT NULL AND trace_first_received_at < now() - toIntervalSecond(trace_ttl_seconds)) = 0)
      )
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[is_root-query_params0-run_latency]
  '''
  WITH run_stats AS (
              SELECT 
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
              FROM runs FINAL
              PREWHERE  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
              
          )
          SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[is_root-query_params0-run_stats]
  '''
  SELECT 
                  
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
              FROM runs as filtered_runs
              PREWHERE  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
  '''
# ---
# name: test_stats_query[is_root-query_params0-token_counts]
  '''
  WITH token_counts as (
          SELECT
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  
          FROM runs_token_counts FINAL
          WHERE runs_token_counts.total_tokens < 4000000000 AND(is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND ((trace_first_received_at IS NOT NULL AND trace_ttl_seconds IS NOT NULL AND trace_first_received_at < now() - toIntervalSecond(trace_ttl_seconds)) = 0)
          GROUP BY id
      ),
  
      token_count_stats as (
          SELECT
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
          FROM token_counts 
      )
     
      SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain-query_params1-feedback]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
          feedback_stats AS (
              SELECT
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM (
                  SELECT 
                      feedbacks_rmt.id, 
                      feedbacks_rmt.key, 
                      feedbacks_rmt.score, 
                      feedbacks_rmt.value, 
                      feedbacks_rmt.correction, 
                      feedbacks_rmt.extra, 
                      feedbacks_rmt.feedback_source,
                      feedbacks_rmt.start_time,
                  FROM feedbacks_rmt FINAL 
                  WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (run_id) IN (SELECT id FROM filtered_runs)
              ) AS feedbacks_rmt
              
          )
  
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain-query_params1-input]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
      input_kv_stats AS (
          SELECT
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM runs_inputs_kv
          WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
      )
  
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain-query_params1-metadata]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
      metadata_stats AS (
          SELECT
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM runs_metadata_kv
          WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
      )
  
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain-query_params1-output]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
      output_kv_stats AS (
          SELECT
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM runs_outputs_kv
          WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
      )
  
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain-query_params1-run_latency]
  '''
  WITH run_stats AS (
              SELECT 
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
              FROM runs FINAL
              PREWHERE  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
              
          )
          SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain-query_params1-run_stats]
  '''
  SELECT 
                  
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
              FROM runs as filtered_runs
              PREWHERE  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
  '''
# ---
# name: test_stats_query[is_root_run_type_chain-query_params1-token_counts]
  '''
  WITH
  
  filtered_runs AS (
      SELECT DISTINCT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
      token_counts AS (
          SELECT
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  
          FROM filtered_runs
          LEFT JOIN (
              SELECT 
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  id,
                  total_tokens,
                  prompt_tokens,
                  completion_tokens,
                  total_cost,
                  prompt_cost,
                  completion_cost,
                  first_token_time
              FROM runs_token_counts FINAL
              WHERE runs_token_counts.total_tokens < 4000000000 AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
               AND id IN (SELECT id FROM filtered_runs)
          ) AS runs_token_counts ON 
              
  runs_token_counts.tenant_id = filtered_runs.tenant_id AND
  runs_token_counts.session_id = filtered_runs.session_id AND
  runs_token_counts.is_root = filtered_runs.is_root AND
  runs_token_counts.start_time = filtered_runs.start_time AND
  runs_token_counts.id = filtered_runs.id
          GROUP BY id
      ),
  
      token_count_stats AS (
          SELECT
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
          FROM token_counts 
      )
  
      SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_metadata-query_params4-feedback]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
          filtered_runs_metadata_kv AS (
              SELECT DISTINCT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  value
              FROM runs_metadata_kv
              WHERE 
                  value IN (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (run_id) IN (SELECT id FROM filtered_runs)
          )
                  AND key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (run_id) IN (SELECT id FROM filtered_runs)
          ),
  
          feedback_stats AS (
              SELECT 
  feedbacks_rmt.group_name as group_name,
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM (
                  SELECT 
                      feedbacks_rmt.id, 
                      feedbacks_rmt.key, 
                      feedbacks_rmt.score, 
                      feedbacks_rmt.value, 
                      feedbacks_rmt.correction, 
                      feedbacks_rmt.extra, 
                      feedbacks_rmt.feedback_source,
                      feedbacks_rmt.start_time,
                      filtered_runs_metadata_kv.value as group_name
                  FROM feedbacks_rmt FINAL
                  JOIN filtered_runs_metadata_kv ON 
  feedbacks_rmt.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  feedbacks_rmt.session_id = filtered_runs_metadata_kv.session_id AND
  feedbacks_rmt.is_root = filtered_runs_metadata_kv.is_root AND
  feedbacks_rmt.start_time = filtered_runs_metadata_kv.start_time AND
  feedbacks_rmt.run_id = filtered_runs_metadata_kv.run_id
                  WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as feedbacks_rmt
              
  GROUP BY group_name
  
          )
  
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_metadata-query_params4-input]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          ),
          
  
      input_kv_stats AS (
          SELECT 
  runs_inputs_kv.group_name as group_name,
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM (
              SELECT 
                  runs_inputs_kv.start_time, 
                  runs_inputs_kv.key, 
                  runs_inputs_kv.value, 
                  filtered_runs_metadata_kv.value as group_name 
              FROM runs_inputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      value
                  FROM runs_metadata_kv
                  WHERE
                      value IN (SELECT group_name FROM top_k_groups)
                      AND key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
              ) as filtered_runs_metadata_kv ON 
  runs_inputs_kv.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  runs_inputs_kv.session_id = filtered_runs_metadata_kv.session_id AND
  runs_inputs_kv.is_root = filtered_runs_metadata_kv.is_root AND
  runs_inputs_kv.start_time = filtered_runs_metadata_kv.start_time AND
  runs_inputs_kv.run_id = filtered_runs_metadata_kv.run_id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_inputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_metadata-query_params4-metadata]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          ),
          
  
      metadata_stats AS (
          SELECT 
  runs_metadata_kv.value as group_name,
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM runs_metadata_kv
          WHERE
              (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              AND value IN (SELECT group_name from top_k_groups)
              AND key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          
  GROUP BY group_name
  
      )
  
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_metadata-query_params4-output]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          ),
          
  
      output_kv_stats AS (
          SELECT 
  runs_outputs_kv.group_name as group_name,
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM (
              SELECT 
                  runs_outputs_kv.start_time, 
                  runs_outputs_kv.key, 
                  runs_outputs_kv.value, 
                  filtered_runs_metadata_kv.value as group_name 
              FROM runs_outputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      value
                  FROM runs_metadata_kv
                  WHERE
                      value IN (SELECT group_name FROM top_k_groups)
                      AND key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
              ) as filtered_runs_metadata_kv ON 
  runs_outputs_kv.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  runs_outputs_kv.session_id = filtered_runs_metadata_kv.session_id AND
  runs_outputs_kv.is_root = filtered_runs_metadata_kv.is_root AND
  runs_outputs_kv.start_time = filtered_runs_metadata_kv.start_time AND
  runs_outputs_kv.run_id = filtered_runs_metadata_kv.run_id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_outputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_metadata-query_params4-run_latency]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs FINAL
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
                  
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (run_id) IN (SELECT id FROM filtered_runs)
          ),
          
  
                  run_stats AS (
                      SELECT 
  filtered_runs.group_name as group_name,
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
                      FROM (
                          SELECT 
                              start_time, 
                              id, 
                              end_time, 
                              filtered_runs_metadata_kv.value as group_name
                          FROM filtered_runs 
                          JOIN (
                              SELECT 
                                  tenant_id,
                                  session_id,
                                  is_root,
                                  start_time,
                                  run_id,
                                  value
                              FROM runs_metadata_kv FINAL
                              WHERE
                                  value IN (SELECT group_name FROM top_k_groups)
                                  AND key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (run_id) IN (SELECT id FROM filtered_runs)
                          ) as filtered_runs_metadata_kv ON 
  filtered_runs.tenant_id = filtered_runs_metadata_kv.tenant_id AND
  filtered_runs.session_id = filtered_runs_metadata_kv.session_id AND
  filtered_runs.is_root = filtered_runs_metadata_kv.is_root AND
  filtered_runs.start_time = filtered_runs_metadata_kv.start_time AND
  filtered_runs.id = filtered_runs_metadata_kv.run_id
                          
                      ) as filtered_runs
                      
  GROUP BY group_name
  
                  ) 
  
                  SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_metadata-query_params4-run_stats]
  '''
  WITH
                      
  
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
                      
          
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (run_id) IN (SELECT id FROM filtered_runs)
          ),
          
          topk_runs_with_group_name AS (
              SELECT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  value
              FROM runs_metadata_kv
              WHERE
                  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (run_id) IN (SELECT id FROM filtered_runs)
                  AND value IN (SELECT group_name FROM top_k_groups)
          )
      
                  SELECT 
  filtered_runs.group_name as group_name,
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
                  FROM (
                      SELECT DISTINCT
                          filtered_runs.start_time, 
                          filtered_runs.id,
                          filtered_runs.end_time,
                          filtered_runs.status, 
                          filtered_runs.name, 
                          filtered_runs.run_type, 
                          filtered_runs.tags, 
                          topk_runs_with_group_name.value as group_name
                      FROM filtered_runs
                      JOIN topk_runs_with_group_name ON 
  filtered_runs.tenant_id = topk_runs_with_group_name.tenant_id AND
  filtered_runs.session_id = topk_runs_with_group_name.session_id AND
  filtered_runs.is_root = topk_runs_with_group_name.is_root AND
  filtered_runs.start_time = topk_runs_with_group_name.start_time AND
  filtered_runs.id = topk_runs_with_group_name.run_id
                      
                  ) as filtered_runs
                  
  GROUP BY group_name
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_metadata-query_params4-token_counts]
  '''
  WITH
  
  filtered_runs AS (
      SELECT DISTINCT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
          filtered_runs_metadata_kv AS (
              SELECT DISTINCT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  value
              FROM runs_metadata_kv
              WHERE
                  value in (
              SELECT 
                  arrayJoin(topK(5)(value)) AS group_name
              FROM runs_metadata_kv
              PREWHERE
  key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          )
                  AND key = {group_by_metadata_key__eq}  AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          ),
  
          token_counts AS (
              SELECT 
                  filtered_runs_metadata_kv.value as group_name,
                  
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  
              FROM filtered_runs_metadata_kv
              LEFT JOIN (
                  SELECT
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      total_tokens,
                      prompt_tokens,
                      completion_tokens,
                      total_cost,
                      prompt_cost,
                      completion_cost,
                      first_token_time
                  FROM runs_token_counts FINAL
                  WHERE
                      runs_token_counts.total_tokens < 4000000000
                      AND (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as runs_token_counts ON
                  
  filtered_runs_metadata_kv.tenant_id = runs_token_counts.tenant_id AND
  filtered_runs_metadata_kv.session_id = runs_token_counts.session_id AND
  filtered_runs_metadata_kv.is_root = runs_token_counts.is_root AND
  filtered_runs_metadata_kv.start_time = runs_token_counts.start_time AND
  filtered_runs_metadata_kv.run_id = runs_token_counts.id
              GROUP BY id, group_name
          ),
  
          token_count_stats AS (
              SELECT 
                  
  token_counts.group_name as group_name,
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
              FROM token_counts
              
  GROUP BY group_name
  
          )
  
          SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name-query_params6-feedback]
  '''
  WITH filtered_runs AS (
              SELECT DISTINCT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  id,
                  name as group_name
              FROM runs 
              WHERE
                   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
                  AND name IN (
                      SELECT arrayJoin(topK(5)(name)) AS top_groups
                      FROM runs
                      WHERE   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
                  )
          ),
  
          feedback_stats AS (
              SELECT 
  group_name as group_name,
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM filtered_runs
              LEFT JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      id, 
                      key, 
                      score, 
                      value, 
                      correction, 
                      extra, 
                      feedback_source
                  FROM feedbacks_rmt
                  WHERE 
                      (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as feedbacks_rmt ON
                  
  feedbacks_rmt.tenant_id = filtered_runs.tenant_id AND
  feedbacks_rmt.session_id = filtered_runs.session_id AND
  feedbacks_rmt.is_root = filtered_runs.is_root AND
  feedbacks_rmt.start_time = filtered_runs.start_time AND
  feedbacks_rmt.run_id = filtered_runs.id
              
  GROUP BY group_name
  
          )
  
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name-query_params6-input]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM filtered_runs
              WHERE
  (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      input_kv_stats AS (
          SELECT 
  runs_inputs_kv.group_name as group_name,
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM (
              SELECT 
                  runs_inputs_kv.start_time, 
                  runs_inputs_kv.key, 
                  runs_inputs_kv.value, 
                  filtered_filtered_runs.name as group_name 
              FROM runs_inputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM filtered_runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_filtered_runs ON 
  runs_inputs_kv.tenant_id = filtered_filtered_runs.tenant_id AND
  runs_inputs_kv.session_id = filtered_filtered_runs.session_id AND
  runs_inputs_kv.is_root = filtered_filtered_runs.is_root AND
  runs_inputs_kv.start_time = filtered_filtered_runs.start_time AND
  runs_inputs_kv.run_id = filtered_filtered_runs.id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_inputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name-query_params6-metadata]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM filtered_runs
              WHERE
  (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      metadata_stats AS (
          SELECT 
  runs_metadata_kv.group_name as group_name,
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM (
              SELECT 
                  runs_metadata_kv.start_time, 
                  runs_metadata_kv.key, 
                  runs_metadata_kv.value, 
                  filtered_filtered_runs.name as group_name 
              FROM runs_metadata_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM filtered_runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_filtered_runs ON 
  runs_metadata_kv.tenant_id = filtered_filtered_runs.tenant_id AND
  runs_metadata_kv.session_id = filtered_filtered_runs.session_id AND
  runs_metadata_kv.is_root = filtered_filtered_runs.is_root AND
  runs_metadata_kv.start_time = filtered_filtered_runs.start_time AND
  runs_metadata_kv.run_id = filtered_filtered_runs.id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_metadata_kv
          
  GROUP BY group_name
  
      )
  
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name-query_params6-output]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM filtered_runs
              WHERE
  (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      output_kv_stats AS (
          SELECT 
  runs_outputs_kv.group_name as group_name,
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM (
              SELECT 
                  runs_outputs_kv.start_time, 
                  runs_outputs_kv.key, 
                  runs_outputs_kv.value, 
                  filtered_filtered_runs.name as group_name 
              FROM runs_outputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM filtered_runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_filtered_runs ON 
  runs_outputs_kv.tenant_id = filtered_filtered_runs.tenant_id AND
  runs_outputs_kv.session_id = filtered_filtered_runs.session_id AND
  runs_outputs_kv.is_root = filtered_filtered_runs.is_root AND
  runs_outputs_kv.start_time = filtered_filtered_runs.start_time AND
  runs_outputs_kv.run_id = filtered_filtered_runs.id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_outputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name-query_params6-run_latency]
  '''
  WITH
  
                  
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
                  run_stats AS (
                      SELECT 
  runs.name as group_name,
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
                      FROM runs FINAL
                      WHERE
                          name IN (SELECT group_name FROM top_k_groups)
                          AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
                      
  GROUP BY group_name
  
                  )
  
                  SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name-query_params6-run_stats]
  '''
  WITH
                  
                  top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          )
  
                  SELECT 
  runs.name as group_name,
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
                  FROM (
                      SELECT DISTINCT
                          start_time,
                          id,
                          end_time,
                          status,
                          name,
                          run_type,
                          tags
                      FROM runs
                      WHERE 
                          runs.name IN (SELECT group_name from top_k_groups)
                          AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) 
                  ) as runs
                  
  GROUP BY group_name
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name-query_params6-token_counts]
  '''
  WITH top_k_runs AS (
          SELECT DISTINCT
              tenant_id,
              session_id,
              is_root,
              start_time,
              id,
              name as group_name
          FROM runs
          WHERE 
               ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
              AND name IN (
                  SELECT 
                      arrayJoin(topK(5)(name)) as group_name
                  FROM runs
                  WHERE
                        ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              )
      ),
  
      token_counts AS (
          SELECT 
              
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  ,
              top_k_runs.group_name
          FROM top_k_runs
          LEFT JOIN (
              SELECT 
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  id,
                  total_tokens,
                  prompt_tokens,
                  completion_tokens,
                  total_cost,
                  prompt_cost,
                  completion_cost,
                  first_token_time 
              FROM runs_token_counts FINAL
              WHERE 
                  runs_token_counts.total_tokens < 4000000000 AND
                  (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) as runs_token_counts ON
              
  runs_token_counts.tenant_id = top_k_runs.tenant_id AND
  runs_token_counts.session_id = top_k_runs.session_id AND
  runs_token_counts.is_root = top_k_runs.is_root AND
  runs_token_counts.start_time = top_k_runs.start_time AND
  runs_token_counts.id = top_k_runs.id
          WHERE 
              (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          GROUP BY id, group_name
      ),
  
      token_count_stats AS (
          SELECT 
              
  token_counts.group_name as group_name,
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
          from token_counts
          
  GROUP BY group_name
  
      )
  
      SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name_bucketed-query_params7-feedback]
  '''
  WITH filtered_runs AS (
              SELECT DISTINCT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  id,
                  name as group_name
              FROM runs 
              WHERE
                   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
                  AND name IN (
                      SELECT arrayJoin(topK(5)(name)) AS top_groups
                      FROM runs
                      WHERE   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
                  )
          ),
  
          feedback_stats AS (
              SELECT 
      toTimeZone(
          toDateTime(
              toUInt32(
                  toStartOfInterval(
                      toDateTime(toUnixTimestamp(feedbacks_rmt.start_time) - toUnixTimestamp(toDateTime({start_time}))),
                      INTERVAL {stride_value} hour
                  )
              )
              + toDateTime({start_time}
          )
      ), {timezone})
       AS ts,
  group_name as group_name,
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM filtered_runs
              LEFT JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      id, 
                      key, 
                      score, 
                      value, 
                      correction, 
                      extra, 
                      feedback_source
                  FROM feedbacks_rmt
                  WHERE 
                      (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as feedbacks_rmt ON
                  
  feedbacks_rmt.tenant_id = filtered_runs.tenant_id AND
  feedbacks_rmt.session_id = filtered_runs.session_id AND
  feedbacks_rmt.is_root = filtered_runs.is_root AND
  feedbacks_rmt.start_time = filtered_runs.start_time AND
  feedbacks_rmt.run_id = filtered_runs.id
              
  GROUP BY ts, group_name
  ORDER BY group_name, ts ASC
  WITH FILL FROM toDateTime({start_time})
           TO   COALESCE(toDateTime({end_time}), now())
           STEP INTERVAL {stride_value} hour
  
          )
  
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name_bucketed-query_params7-input]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM filtered_runs
              WHERE
  (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      input_kv_stats AS (
          SELECT 
      toTimeZone(
          toDateTime(
              toUInt32(
                  toStartOfInterval(
                      toDateTime(toUnixTimestamp(runs_inputs_kv.start_time) - toUnixTimestamp(toDateTime({start_time}))),
                      INTERVAL {stride_value} hour
                  )
              )
              + toDateTime({start_time}
          )
      ), {timezone})
       AS ts,
  runs_inputs_kv.group_name as group_name,
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM (
              SELECT 
                  runs_inputs_kv.start_time, 
                  runs_inputs_kv.key, 
                  runs_inputs_kv.value, 
                  filtered_filtered_runs.name as group_name 
              FROM runs_inputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM filtered_runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_filtered_runs ON 
  runs_inputs_kv.tenant_id = filtered_filtered_runs.tenant_id AND
  runs_inputs_kv.session_id = filtered_filtered_runs.session_id AND
  runs_inputs_kv.is_root = filtered_filtered_runs.is_root AND
  runs_inputs_kv.start_time = filtered_filtered_runs.start_time AND
  runs_inputs_kv.run_id = filtered_filtered_runs.id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_inputs_kv
          
  GROUP BY ts, group_name
  ORDER BY group_name, ts ASC
  WITH FILL FROM toDateTime({start_time})
           TO   COALESCE(toDateTime({end_time}), now())
           STEP INTERVAL {stride_value} hour
  
      )
  
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name_bucketed-query_params7-metadata]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM filtered_runs
              WHERE
  (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      metadata_stats AS (
          SELECT 
      toTimeZone(
          toDateTime(
              toUInt32(
                  toStartOfInterval(
                      toDateTime(toUnixTimestamp(runs_metadata_kv.start_time) - toUnixTimestamp(toDateTime({start_time}))),
                      INTERVAL {stride_value} hour
                  )
              )
              + toDateTime({start_time}
          )
      ), {timezone})
       AS ts,
  runs_metadata_kv.group_name as group_name,
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM (
              SELECT 
                  runs_metadata_kv.start_time, 
                  runs_metadata_kv.key, 
                  runs_metadata_kv.value, 
                  filtered_filtered_runs.name as group_name 
              FROM runs_metadata_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM filtered_runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_filtered_runs ON 
  runs_metadata_kv.tenant_id = filtered_filtered_runs.tenant_id AND
  runs_metadata_kv.session_id = filtered_filtered_runs.session_id AND
  runs_metadata_kv.is_root = filtered_filtered_runs.is_root AND
  runs_metadata_kv.start_time = filtered_filtered_runs.start_time AND
  runs_metadata_kv.run_id = filtered_filtered_runs.id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_metadata_kv
          
  GROUP BY ts, group_name
  ORDER BY group_name, ts ASC
  WITH FILL FROM toDateTime({start_time})
           TO   COALESCE(toDateTime({end_time}), now())
           STEP INTERVAL {stride_value} hour
  
      )
  
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name_bucketed-query_params7-output]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM filtered_runs
              WHERE
  (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
      output_kv_stats AS (
          SELECT 
      toTimeZone(
          toDateTime(
              toUInt32(
                  toStartOfInterval(
                      toDateTime(toUnixTimestamp(runs_outputs_kv.start_time) - toUnixTimestamp(toDateTime({start_time}))),
                      INTERVAL {stride_value} hour
                  )
              )
              + toDateTime({start_time}
          )
      ), {timezone})
       AS ts,
  runs_outputs_kv.group_name as group_name,
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM (
              SELECT 
                  runs_outputs_kv.start_time, 
                  runs_outputs_kv.key, 
                  runs_outputs_kv.value, 
                  filtered_filtered_runs.name as group_name 
              FROM runs_outputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      name
                  FROM filtered_runs
                  WHERE
                      name IN (SELECT group_name FROM top_k_groups)
                      AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              ) as filtered_filtered_runs ON 
  runs_outputs_kv.tenant_id = filtered_filtered_runs.tenant_id AND
  runs_outputs_kv.session_id = filtered_filtered_runs.session_id AND
  runs_outputs_kv.is_root = filtered_filtered_runs.is_root AND
  runs_outputs_kv.start_time = filtered_filtered_runs.start_time AND
  runs_outputs_kv.run_id = filtered_filtered_runs.id
              WHERE (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_outputs_kv
          
  GROUP BY ts, group_name
  ORDER BY group_name, ts ASC
  WITH FILL FROM toDateTime({start_time})
           TO   COALESCE(toDateTime({end_time}), now())
           STEP INTERVAL {stride_value} hour
  
      )
  
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name_bucketed-query_params7-run_latency]
  '''
  WITH
  
                  
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          ),
          
  
                  run_stats AS (
                      SELECT 
      toTimeZone(
          toDateTime(
              toUInt32(
                  toStartOfInterval(
                      toDateTime(toUnixTimestamp(runs.start_time) - toUnixTimestamp(toDateTime({start_time}))),
                      INTERVAL {stride_value} hour
                  )
              )
              + toDateTime({start_time}
          )
      ), {timezone})
       AS ts,
  runs.name as group_name,
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
                      FROM runs FINAL
                      WHERE
                          name IN (SELECT group_name FROM top_k_groups)
                          AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
                      
  GROUP BY ts, group_name
  ORDER BY group_name, ts ASC
  WITH FILL FROM toDateTime({start_time})
           TO   COALESCE(toDateTime({end_time}), now())
           STEP INTERVAL {stride_value} hour
  
                  )
  
                  SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name_bucketed-query_params7-run_stats]
  '''
  WITH
                  
                  top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(name)) AS group_name
              FROM runs
              PREWHERE
   ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
          )
  
                  SELECT 
      toTimeZone(
          toDateTime(
              toUInt32(
                  toStartOfInterval(
                      toDateTime(toUnixTimestamp(runs.start_time) - toUnixTimestamp(toDateTime({start_time}))),
                      INTERVAL {stride_value} hour
                  )
              )
              + toDateTime({start_time}
          )
      ), {timezone})
       AS ts,
  runs.name as group_name,
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
                  FROM (
                      SELECT DISTINCT
                          start_time,
                          id,
                          end_time,
                          status,
                          name,
                          run_type,
                          tags
                      FROM runs
                      WHERE 
                          runs.name IN (SELECT group_name from top_k_groups)
                          AND  ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) 
                  ) as runs
                  
  GROUP BY ts, group_name
  ORDER BY group_name, ts ASC
  WITH FILL FROM toDateTime({start_time})
           TO   COALESCE(toDateTime({end_time}), now())
           STEP INTERVAL {stride_value} hour
  '''
# ---
# name: test_stats_query[is_root_run_type_chain_group_by_name_bucketed-query_params7-token_counts]
  '''
  WITH top_k_runs AS (
          SELECT DISTINCT
              tenant_id,
              session_id,
              is_root,
              start_time,
              id,
              name as group_name
          FROM runs
          WHERE 
               ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
              AND name IN (
                  SELECT 
                      arrayJoin(topK(5)(name)) as group_name
                  FROM runs
                  WHERE
                        ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq}) AND (name NOT IN ['RunnableAssign', 'RunnableBinding', 'RunnableBranch', 'RunnableEach', 'RunnableLambda', 'RunnableMap', 'RunnablePassthrough', 'RunnablePick', 'RunnableRetry', 'RunnableSequence', 'RunnableWithFallbacks', 'LangGraph', '__start__', '_write', 'ChannelWrite', 'ChannelWrite<...>', 'ChannelRead', 'ChannelRead<...>', 'ChannelInvoke', 'ChannelInvoke<...>', 'ChannelBatch', 'ChannelBatch<...>', 'Pregel']) AND NOT startsWith(name, '_') AND NOT startsWith(name, 'ChannelWrite')
              )
      ),
  
      token_counts AS (
          SELECT 
              
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  ,
              top_k_runs.group_name
          FROM top_k_runs
          LEFT JOIN (
              SELECT 
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  id,
                  total_tokens,
                  prompt_tokens,
                  completion_tokens,
                  total_cost,
                  prompt_cost,
                  completion_cost,
                  first_token_time 
              FROM runs_token_counts FINAL
              WHERE 
                  runs_token_counts.total_tokens < 4000000000 AND
                  (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) as runs_token_counts ON
              
  runs_token_counts.tenant_id = top_k_runs.tenant_id AND
  runs_token_counts.session_id = top_k_runs.session_id AND
  runs_token_counts.is_root = top_k_runs.is_root AND
  runs_token_counts.start_time = top_k_runs.start_time AND
  runs_token_counts.id = top_k_runs.id
          WHERE 
              (is_root = {is_root__eq}) AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          GROUP BY id, group_name
      ),
  
      token_count_stats AS (
          SELECT 
              
      toTimeZone(
          toDateTime(
              toUInt32(
                  toStartOfInterval(
                      toDateTime(toUnixTimestamp(token_counts.min_start_time) - toUnixTimestamp(toDateTime({start_time}))),
                      INTERVAL {stride_value} hour
                  )
              )
              + toDateTime({start_time}
          )
      ), {timezone})
       AS ts,
  token_counts.group_name as group_name,
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
          from token_counts
          
  GROUP BY ts, group_name
  ORDER BY group_name, ts ASC
  WITH FILL FROM toDateTime({start_time})
           TO   COALESCE(toDateTime({end_time}), now())
           STEP INTERVAL {stride_value} hour
  
      )
  
      SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
# name: test_stats_query[run_type_llm_group_by_tag-query_params3-feedback]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
          filtered_runs_tags AS (
              SELECT DISTINCT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  tag
              FROM runs_tags
              WHERE 
                  tag IN (
              SELECT 
                  arrayJoin(topK(5)(tag)) AS group_name
              FROM runs_tags
              PREWHERE
  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) IN (SELECT is_root, id FROM filtered_runs)
          )
                   AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) IN (SELECT is_root, id FROM filtered_runs)
          ),
  
          feedback_stats AS (
              SELECT 
  feedbacks_rmt.group_name as group_name,
      mapKeys(uniqMap(map(key, id))) as feedback_keys,
      mapValues(avgMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_avgs,
      mapValues(uniqMap(map(key, id))) as feedback_counts,
      mapValues(stddevPopMap(map(key, COALESCE(
          CASE
              WHEN JSONHas(correction, 'score') THEN JSONExtract(correction, 'score', 'Int32')
              ELSE NULL
          END,
          score
      )))) as feedback_stdevs,
      mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
      mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
      mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
      topK(20)(key) as top_10_feedback_key,
      topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
      topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
      topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source
              FROM (
                  SELECT 
                      feedbacks_rmt.id, 
                      feedbacks_rmt.key, 
                      feedbacks_rmt.score, 
                      feedbacks_rmt.value, 
                      feedbacks_rmt.correction, 
                      feedbacks_rmt.extra, 
                      feedbacks_rmt.feedback_source,
                      feedbacks_rmt.start_time,
                      filtered_runs_tags.tag as group_name
                  FROM feedbacks_rmt FINAL
                  JOIN filtered_runs_tags ON 
  feedbacks_rmt.tenant_id = filtered_runs_tags.tenant_id AND
  feedbacks_rmt.session_id = filtered_runs_tags.session_id AND
  feedbacks_rmt.is_root = filtered_runs_tags.is_root AND
  feedbacks_rmt.start_time = filtered_runs_tags.start_time AND
  feedbacks_rmt.run_id = filtered_runs_tags.run_id
                  WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
              ) as feedbacks_rmt
              
  GROUP BY group_name
  
          )
  
          SELECT feedback_stats.* FROM feedback_stats
  '''
# ---
# name: test_stats_query[run_type_llm_group_by_tag-query_params3-input]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(tag)) AS group_name
              FROM runs_tags
              PREWHERE
  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          ),
          
  
      input_kv_stats AS (
          SELECT 
  runs_inputs_kv.group_name as group_name,
      topK(20)(key) as top_10_input_key,
      topK(20)(key || ' == ' || value) as top_10_input_key_value
          FROM (
              SELECT 
                  runs_inputs_kv.start_time, 
                  runs_inputs_kv.key, 
                  runs_inputs_kv.value, 
                  filtered_runs_tags.tag as group_name 
              FROM runs_inputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      tag
                  FROM runs_tags
                  WHERE
                      tag IN (SELECT group_name FROM top_k_groups)
                       AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
              ) as filtered_runs_tags ON 
  runs_inputs_kv.tenant_id = filtered_runs_tags.tenant_id AND
  runs_inputs_kv.session_id = filtered_runs_tags.session_id AND
  runs_inputs_kv.is_root = filtered_runs_tags.is_root AND
  runs_inputs_kv.start_time = filtered_runs_tags.start_time AND
  runs_inputs_kv.run_id = filtered_runs_tags.run_id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_inputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT input_kv_stats.* FROM input_kv_stats
  '''
# ---
# name: test_stats_query[run_type_llm_group_by_tag-query_params3-metadata]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(tag)) AS group_name
              FROM runs_tags
              PREWHERE
  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          ),
          
  
      metadata_stats AS (
          SELECT 
  runs_metadata_kv.group_name as group_name,
      topK(20)(key) as top_10_metadata_key,
      topK(20)(key || ' == ' || value) as top_10_metadata_key_value
          FROM (
              SELECT 
                  runs_metadata_kv.start_time, 
                  runs_metadata_kv.key, 
                  runs_metadata_kv.value, 
                  filtered_runs_tags.tag as group_name 
              FROM runs_metadata_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      tag
                  FROM runs_tags
                  WHERE
                      tag IN (SELECT group_name FROM top_k_groups)
                       AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
              ) as filtered_runs_tags ON 
  runs_metadata_kv.tenant_id = filtered_runs_tags.tenant_id AND
  runs_metadata_kv.session_id = filtered_runs_tags.session_id AND
  runs_metadata_kv.is_root = filtered_runs_tags.is_root AND
  runs_metadata_kv.start_time = filtered_runs_tags.start_time AND
  runs_metadata_kv.run_id = filtered_runs_tags.run_id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_metadata_kv
          
  GROUP BY group_name
  
      )
  
      SELECT metadata_stats.* FROM metadata_stats
  '''
# ---
# name: test_stats_query[run_type_llm_group_by_tag-query_params3-output]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
      
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(tag)) AS group_name
              FROM runs_tags
              PREWHERE
  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
          ),
          
  
      output_kv_stats AS (
          SELECT 
  runs_outputs_kv.group_name as group_name,
      topK(20)(key) as top_10_output_key,
      topK(20)(key || ' == ' || value) as top_10_output_key_value
          FROM (
              SELECT 
                  runs_outputs_kv.start_time, 
                  runs_outputs_kv.key, 
                  runs_outputs_kv.value, 
                  filtered_runs_tags.tag as group_name 
              FROM runs_outputs_kv 
              JOIN (
                  SELECT 
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      run_id,
                      tag
                  FROM runs_tags
                  WHERE
                      tag IN (SELECT group_name FROM top_k_groups)
                       AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND run_id IN (SELECT id FROM filtered_runs)
              ) as filtered_runs_tags ON 
  runs_outputs_kv.tenant_id = filtered_runs_tags.tenant_id AND
  runs_outputs_kv.session_id = filtered_runs_tags.session_id AND
  runs_outputs_kv.is_root = filtered_runs_tags.is_root AND
  runs_outputs_kv.start_time = filtered_runs_tags.start_time AND
  runs_outputs_kv.run_id = filtered_runs_tags.run_id
              WHERE (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq})
          ) AS runs_outputs_kv
          
  GROUP BY group_name
  
      )
  
      SELECT output_kv_stats.* FROM output_kv_stats
  '''
# ---
# name: test_stats_query[run_type_llm_group_by_tag-query_params3-run_latency]
  '''
  WITH
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs FINAL
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
                  
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(tag)) AS group_name
              FROM runs_tags
              PREWHERE
  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) IN (SELECT is_root, id FROM filtered_runs)
          ),
          
  
                  run_stats AS (
                      SELECT 
  filtered_runs.group_name as group_name,
      uniq(id) as run_count,
      if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
  
                      FROM (
                          SELECT 
                              start_time, 
                              id, 
                              end_time, 
                              filtered_runs_tags.tag as group_name
                          FROM filtered_runs 
                          JOIN (
                              SELECT 
                                  tenant_id,
                                  session_id,
                                  is_root,
                                  start_time,
                                  run_id,
                                  tag
                              FROM runs_tags FINAL
                              WHERE
                                  tag IN (SELECT group_name FROM top_k_groups)
                                   AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) IN (SELECT is_root, id FROM filtered_runs)
                          ) as filtered_runs_tags ON 
  filtered_runs.tenant_id = filtered_runs_tags.tenant_id AND
  filtered_runs.session_id = filtered_runs_tags.session_id AND
  filtered_runs.is_root = filtered_runs_tags.is_root AND
  filtered_runs.start_time = filtered_runs_tags.start_time AND
  filtered_runs.id = filtered_runs_tags.run_id
                          
                      ) as filtered_runs
                      
  GROUP BY group_name
  
                  ) 
  
                  SELECT run_stats.* FROM run_stats
  '''
# ---
# name: test_stats_query[run_type_llm_group_by_tag-query_params3-run_stats]
  '''
  WITH
                      
  
  
  filtered_runs AS (
      SELECT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
                      
          
          top_k_groups AS (
              SELECT 
                  arrayJoin(topK(5)(tag)) AS group_name
              FROM runs_tags
              PREWHERE
  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) IN (SELECT is_root, id FROM filtered_runs)
          ),
          
          topk_runs_with_group_name AS (
              SELECT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  tag
              FROM runs_tags
              WHERE
                  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) IN (SELECT is_root, id FROM filtered_runs)
                  AND tag IN (SELECT group_name FROM top_k_groups)
          )
      
                  SELECT 
  filtered_runs.group_name as group_name,
      uniq(id) as run_count,
      max(start_time) as last_run_start_time,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
      if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
      ,
      topK(20)(name) as top_10_name,
      topK(5)(status) as top_10_status,
      topK(10)(run_type) as top_10_run_type,
      topKArray(20)(tags) as top_10_tags
                  FROM (
                      SELECT DISTINCT
                          filtered_runs.start_time, 
                          filtered_runs.id,
                          filtered_runs.end_time,
                          filtered_runs.status, 
                          filtered_runs.name, 
                          filtered_runs.run_type, 
                          filtered_runs.tags, 
                          topk_runs_with_group_name.tag as group_name
                      FROM filtered_runs
                      JOIN topk_runs_with_group_name ON 
  filtered_runs.tenant_id = topk_runs_with_group_name.tenant_id AND
  filtered_runs.session_id = topk_runs_with_group_name.session_id AND
  filtered_runs.is_root = topk_runs_with_group_name.is_root AND
  filtered_runs.start_time = topk_runs_with_group_name.start_time AND
  filtered_runs.id = topk_runs_with_group_name.run_id
                      
                  ) as filtered_runs
                  
  GROUP BY group_name
  '''
# ---
# name: test_stats_query[run_type_llm_group_by_tag-query_params3-token_counts]
  '''
  WITH
  
  filtered_runs AS (
      SELECT DISTINCT runs.tenant_id AS tenant_id, runs.session_id AS session_id, runs.is_root AS is_root, runs.id AS id, runs.start_time AS start_time, runs.end_time AS end_time, runs.total_tokens AS total_tokens, runs.prompt_tokens AS prompt_tokens, runs.completion_tokens AS completion_tokens, runs.prompt_cost AS prompt_cost, runs.completion_cost AS completion_cost, runs.total_cost AS total_cost, runs.name AS name, runs.status AS status, runs.run_type AS run_type, runs.tags AS tags, runs.first_token_time AS first_token_time
      FROM runs
  WHERE ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = {is_trace_expired__eq}) AND (runs.run_type = {run_type__eq_01234}) AND (runs.session_id IN {session_id__in}) AND (runs.start_time <= {start_time__lte}) AND (runs.start_time >= {start_time__gte}) AND (runs.tenant_id = {tenant_id__eq})
      ORDER BY runs.start_time DESC, id DESC
      SETTINGS multiple_joins_try_to_keep_original_names = 1,
          optimize_read_in_order = 1
  ),
  
  
  
          filtered_runs_tags AS (
              SELECT DISTINCT
                  tenant_id,
                  session_id,
                  is_root,
                  start_time,
                  run_id,
                  tag
              FROM runs_tags
              WHERE
                  tag in (
              SELECT 
                  arrayJoin(topK(5)(tag)) AS group_name
              FROM runs_tags
              PREWHERE
  (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) in (SELECT is_root, id from filtered_runs)
          )
                   AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND (is_root, run_id) in (SELECT is_root, id from filtered_runs)
          ),
  
          token_counts AS (
              SELECT 
                  filtered_runs_tags.tag as group_name,
                  
      id,
      sum(runs_token_counts.total_tokens) as total_tokens,
      sum(runs_token_counts.prompt_tokens) as prompt_tokens,
      sum(runs_token_counts.completion_tokens) as completion_tokens,
      sum(runs_token_counts.total_cost) as total_cost,
      sum(runs_token_counts.prompt_cost) as prompt_cost,
      sum(runs_token_counts.completion_cost) as completion_cost,
      min(runs_token_counts.start_time) as min_start_time,
      min(runs_token_counts.first_token_time) as first_token_time
  
              FROM filtered_runs_tags
              LEFT JOIN (
                  SELECT
                      tenant_id,
                      session_id,
                      is_root,
                      start_time,
                      id,
                      total_tokens,
                      prompt_tokens,
                      completion_tokens,
                      total_cost,
                      prompt_cost,
                      completion_cost,
                      first_token_time
                  FROM runs_token_counts FINAL
                  WHERE
                      runs_token_counts.total_tokens < 4000000000
                      AND (session_id IN {session_id__in}) AND (start_time <= {start_time__lte}) AND (start_time >= {start_time__gte}) AND (tenant_id = {tenant_id__eq}) AND id = source_id
              ) as runs_token_counts ON
                  
  filtered_runs_tags.tenant_id = runs_token_counts.tenant_id AND
  filtered_runs_tags.session_id = runs_token_counts.session_id AND
  filtered_runs_tags.is_root = runs_token_counts.is_root AND
  filtered_runs_tags.start_time = runs_token_counts.start_time AND
  filtered_runs_tags.run_id = runs_token_counts.id
              GROUP BY id, group_name
          ),
  
          token_count_stats AS (
              SELECT 
                  
  token_counts.group_name as group_name,
      uniq(id) as token_run_count,
      arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
      if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
      if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
      if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
      if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
      if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
      if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
      ,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.total_tokens)) as tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.completion_tokens)) as completion_tokens_ptiles,
      arrayMap(x -> if(isFinite(x), toUInt32(x), 0), quantiles(0.5, 0.99)(token_counts.prompt_tokens)) as prompt_tokens_ptiles,
      quantiles(0.5, 0.99)(token_counts.total_cost) as cost_ptiles
      
              FROM token_counts
              
  GROUP BY group_name
  
          )
  
          SELECT token_count_stats.* FROM token_count_stats
  '''
# ---
