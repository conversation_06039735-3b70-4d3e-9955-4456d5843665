from unittest.mock import patch
from uuid import UUID

import pytest
from fastapi import HTTPException

from app.models.examples.attrs import get_example_attributes
from app.models.query_lang.parse import (
    Comparator,
    Comparison,
    Operation,
    Operator,
    parse_and_split_filters_tracer_sessions,
    parse_as_filter_directive,
    parse_as_filter_directive_examples,
    parse_as_filter_directive_tracer_sessions,
)
from app.models.query_lang.translate import (
    SqlVisitorClickhouse,
    SqlVisitorPostgres,
    translate_session_feedback_operation_to_ch_query,
)
from app.models.runs.attrs import RUN_ATTRIBUTES
from app.models.tracer_sessions.attrs import get_tracer_session_attributes

# Mock UUIDs for testing
TEST_UUIDS = ["id_{}".format(i) for i in range(10000)]


def test_parse() -> None:
    query_str = (
        'and(eq("is_root", true), gte("start_time", "2020-12-01 00:00:00.000000"))'
    )

    assert parse_as_filter_directive(query_str) == Operation(
        operator=Operator.AND,
        arguments=(
            Comparison(Comparator.EQ, "is_root", True),
            Comparison(Comparator.GTE, "start_time", "2020-12-01 00:00:00.000000"),
        ),
    )


def test_parse_escaped_quotes() -> None:
    query_str = 'and(eq(is_root, true), search("\\"blahblah\\""))'
    assert parse_as_filter_directive(query_str) == Operation(
        operator=Operator.AND,
        arguments=(
            Comparison(Comparator.EQ, "is_root", True),
            Comparison(Comparator.SEARCH, None, '\\"blahblah\\"'),  # type: ignore[arg-type]
        ),
    )


def test_parse_invalid_vals() -> None:
    query_str = 'eq(trace_id, "foo")'

    with pytest.raises(ValueError):
        parse_as_filter_directive(query_str).accept(
            SqlVisitorClickhouse(
                attributes=RUN_ATTRIBUTES,
                main_table="runs",
            )
        )

    query_str = 'eq("feedback_score", "foo")'
    with pytest.raises(ValueError):
        parse_as_filter_directive(query_str).accept(
            SqlVisitorClickhouse(
                attributes=RUN_ATTRIBUTES,
                main_table="runs",
            )
        )


@patch("app.models.query_lang.translate.uuid4", side_effect=TEST_UUIDS)
def test_translate_sql_ch(_) -> None:
    query_str = (
        'and(eq("is_root", true), gte("start_time", "2020-12-01 00:00:00.000000"))'
    )

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq}) AND (runs.start_time >= {start_time__gte})""",
        {"is_root__eq": True, "start_time__gte": "2020-12-01 00:00:00.000000"},
        "(is_root = {is_root__eq}) AND (start_time >= {start_time__gte})",
        "(is_root = {is_root__eq}) AND (start_time >= {start_time__gte})",
        "",
        "(runs.is_root = {is_root__eq}) AND (runs.start_time >= {start_time__gte})",
    )

    query_str = 'and(eq("is_root", true), eq("feedback_key", "hello"))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
INNER JOIN (
    SELECT DISTINCT tenant_id, is_root, session_id, start_time, run_id
    FROM feedbacks_rmt
    WHERE (is_root = {is_root__eq})
    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_1_feedback_key__eq})
) AS feedbacks_rmt_1_1_1 ON feedbacks_rmt_1_1_1.tenant_id = runs.tenant_id AND feedbacks_rmt_1_1_1.is_root = runs.is_root AND feedbacks_rmt_1_1_1.session_id = runs.session_id AND feedbacks_rmt_1_1_1.start_time = runs.start_time AND feedbacks_rmt_1_1_1.run_id = runs.id
WHERE (runs.is_root = {is_root__eq})""",
        {"is_root__eq": True, "feedbacks_rmt_1_1_1_feedback_key__eq": "hello"},
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        """
            WITH runs_and_times as (
                
                    SELECT
                        run_id,
                        start_time
                    FROM
                        feedbacks_rmt
                    WHERE (is_root = {is_root__eq})
                    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_1_feedback_key__eq})
        
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,""",
        "(runs.is_root = {is_root__eq})",
    )

    query_str = 'and(eq("is_root", true), eq("feedback_key", "hello"), eq(feedback_key, "world"))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
INNER JOIN (
    SELECT DISTINCT tenant_id, is_root, session_id, start_time, run_id
    FROM feedbacks_rmt
    WHERE (is_root = {is_root__eq})
    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_1_feedback_key__eq})
) AS feedbacks_rmt_1_1_1 ON feedbacks_rmt_1_1_1.tenant_id = runs.tenant_id AND feedbacks_rmt_1_1_1.is_root = runs.is_root AND feedbacks_rmt_1_1_1.session_id = runs.session_id AND feedbacks_rmt_1_1_1.start_time = runs.start_time AND feedbacks_rmt_1_1_1.run_id = runs.id
INNER JOIN (
    SELECT DISTINCT tenant_id, is_root, session_id, start_time, run_id
    FROM feedbacks_rmt
    WHERE (is_root = {is_root__eq})
    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_2_feedback_key__eq})
) AS feedbacks_rmt_1_1_2 ON feedbacks_rmt_1_1_2.tenant_id = runs.tenant_id AND feedbacks_rmt_1_1_2.is_root = runs.is_root AND feedbacks_rmt_1_1_2.session_id = runs.session_id AND feedbacks_rmt_1_1_2.start_time = runs.start_time AND feedbacks_rmt_1_1_2.run_id = runs.id
WHERE (runs.is_root = {is_root__eq})""",
        {
            "is_root__eq": True,
            "feedbacks_rmt_1_1_1_feedback_key__eq": "hello",
            "feedbacks_rmt_1_1_2_feedback_key__eq": "world",
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        """
            WITH runs_and_times as (
                
                    SELECT
                        run_id,
                        start_time
                    FROM
                        feedbacks_rmt
                    WHERE (is_root = {is_root__eq})
                    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_1_feedback_key__eq})
         INTERSECT 
                    SELECT
                        run_id,
                        start_time
                    FROM
                        feedbacks_rmt
                    WHERE (is_root = {is_root__eq})
                    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_2_feedback_key__eq})
        
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,""",
        "(runs.is_root = {is_root__eq})",
    )
    query_str = 'and(eq("is_root", true), eq(feedback_key, "hi"), and(eq(feedback_key, "correctness"), eq(feedback_score, "1")))'

    assert parse_as_filter_directive(query_str) == Operation(
        operator=Operator.AND,
        arguments=(
            Comparison(Comparator.EQ, "is_root", True),
            Comparison(Comparator.EQ, "feedback_key", "hi"),
            Operation(
                operator=Operator.AND,
                arguments=(
                    Comparison(Comparator.EQ, "feedback_key", "correctness"),
                    Comparison(Comparator.EQ, "feedback_score", "1"),
                ),
            ),
        ),
    )

    filter_directive = parse_as_filter_directive(query_str)
    assert filter_directive.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
INNER JOIN (
    SELECT DISTINCT tenant_id, is_root, session_id, start_time, run_id
    FROM feedbacks_rmt
    WHERE (is_root = {is_root__eq})
    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_1_feedback_key__eq})
) AS feedbacks_rmt_1_1_1 ON feedbacks_rmt_1_1_1.tenant_id = runs.tenant_id AND feedbacks_rmt_1_1_1.is_root = runs.is_root AND feedbacks_rmt_1_1_1.session_id = runs.session_id AND feedbacks_rmt_1_1_1.start_time = runs.start_time AND feedbacks_rmt_1_1_1.run_id = runs.id
INNER JOIN (
    SELECT DISTINCT tenant_id, is_root, session_id, start_time, run_id
    FROM feedbacks_rmt
    WHERE (is_root = {is_root__eq})
    AND ((feedbacks_rmt.key = {feedbacks_rmt_2_1_1_feedback_key__eq}) AND (feedbacks_rmt.score = {feedbacks_rmt_2_1_1_feedback_score__eq}))
) AS feedbacks_rmt_2_1_1 ON feedbacks_rmt_2_1_1.tenant_id = runs.tenant_id AND feedbacks_rmt_2_1_1.is_root = runs.is_root AND feedbacks_rmt_2_1_1.session_id = runs.session_id AND feedbacks_rmt_2_1_1.start_time = runs.start_time AND feedbacks_rmt_2_1_1.run_id = runs.id
WHERE (runs.is_root = {is_root__eq})""",
        {
            "is_root__eq": True,
            "feedbacks_rmt_1_1_1_feedback_key__eq": "hi",
            "feedbacks_rmt_2_1_1_feedback_key__eq": "correctness",
            "feedbacks_rmt_2_1_1_feedback_score__eq": 1.0,
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        """
            WITH runs_and_times as (
                
                    SELECT
                        run_id,
                        start_time
                    FROM
                        feedbacks_rmt
                    WHERE (is_root = {is_root__eq})
                    AND (feedbacks_rmt.key = {feedbacks_rmt_1_1_1_feedback_key__eq})
         INTERSECT 
                    SELECT
                        run_id,
                        start_time
                    FROM
                        feedbacks_rmt
                    WHERE (is_root = {is_root__eq})
                    AND ((feedbacks_rmt.key = {feedbacks_rmt_2_1_1_feedback_key__eq}) AND (feedbacks_rmt.score = {feedbacks_rmt_2_1_1_feedback_score__eq}))
        
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,""",
        "(runs.is_root = {is_root__eq})",
    )

    query = Operation(
        operator=Operator.AND,
        arguments=(
            Comparison(Comparator.EQ, "is_root", True),
            Comparison(Comparator.IN, "id", ["94d69d4c-6280-4a40-ae8f-607695391769"]),
            Comparison(Comparator.EQ, "end_time", "2023-01-01"),
        ),
    )

    assert query.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE ((runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
    SELECT tenant_id, is_root, session_id, start_time, id
    FROM runs_run_id_v2
    WHERE id IN {runs_run_id_v2_1_1_1_id__in} AND (id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})
)) AND (runs.end_time = {end_time__eq}) AND (runs.is_root = {is_root__eq})""",
        {
            "is_root__eq": True,
            "end_time__eq": "2023-01-01 00:00:00.000000",
            "runs_run_id_v2_1_1_1_id__in": ["94d69d4c-6280-4a40-ae8f-607695391769"],
        },
        "(is_root = {is_root__eq}) AND (run_id IN {runs_run_id_v2_1_1_1_id__in})",
        "(id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})",
        "",
        "((runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (\n    SELECT tenant_id, is_root, session_id, start_time, id\n    FROM runs_run_id_v2\n    WHERE id IN {runs_run_id_v2_1_1_1_id__in} AND (id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})\n)) AND (runs.end_time = {end_time__eq}) AND (runs.is_root = {is_root__eq})",
    )

    query = Operation(
        operator=Operator.AND,
        arguments=(
            Comparison(Comparator.EQ, "is_root", True),
            Comparison(Comparator.IN, "id", ["94d69d4c-6280-4a40-ae8f-607695391769"]),
            Comparison(Comparator.EQ, "run_type", "llm"),
        ),
    )

    assert query.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE ((runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
    SELECT tenant_id, is_root, session_id, start_time, id
    FROM runs_run_id_v2
    WHERE id IN {runs_run_id_v2_1_1_1_id__in} AND (id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})
)) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_0})""",
        {
            "is_root__eq": True,
            "run_type__eq_id_0": "llm",
            "runs_run_id_v2_1_1_1_id__in": ["94d69d4c-6280-4a40-ae8f-607695391769"],
        },
        "(is_root = {is_root__eq}) AND (run_id IN {runs_run_id_v2_1_1_1_id__in})",
        "(id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})",
        "",
        "((runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (\n    SELECT tenant_id, is_root, session_id, start_time, id\n    FROM runs_run_id_v2\n    WHERE id IN {runs_run_id_v2_1_1_1_id__in} AND (id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})\n)) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_0})",
    )

    assert query.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
            main_table_suffix="FINAL",
        )
    ) == (
        """FROM runs FINAL
WHERE ((runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
    SELECT tenant_id, is_root, session_id, start_time, id
    FROM runs_run_id_v2
    WHERE id IN {runs_run_id_v2_1_1_1_id__in} AND (id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})
)) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_1})""",
        {
            "is_root__eq": True,
            "run_type__eq_id_1": "llm",
            "runs_run_id_v2_1_1_1_id__in": ["94d69d4c-6280-4a40-ae8f-607695391769"],
        },
        "(is_root = {is_root__eq}) AND (run_id IN {runs_run_id_v2_1_1_1_id__in})",
        "(id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})",
        "",
        "((runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (\n    SELECT tenant_id, is_root, session_id, start_time, id\n    FROM runs_run_id_v2\n    WHERE id IN {runs_run_id_v2_1_1_1_id__in} AND (id IN {runs_run_id_v2_1_1_1_id__in}) AND (is_root = {is_root__eq})\n)) AND (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_1})",
    )

    query = Operation(
        operator=Operator.AND,
        arguments=(
            Comparison(Comparator.EQ, "is_root", True),
            Comparison(Comparator.EQ, "run_type", "llm"),
        ),
    )

    assert query.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_2})""",
        {
            "is_root__eq": True,
            "run_type__eq_id_2": "llm",
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_2})",
    )

    assert query.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
            main_table_suffix="FINAL",
        )
    ) == (
        """FROM runs FINAL
WHERE (runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_3})""",
        {
            "is_root__eq": True,
            "run_type__eq_id_3": "llm",
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq}) AND (runs.run_type = {run_type__eq_id_3})",
    )

    query_str = 'and(and(eq("is_root", true), gte("start_time", "2020-12-01 00:00:00.000000")), eq(metadata_key, "user") , eq(metadata_key, "phone"))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        (
            """FROM runs
WHERE ((runs.is_root = {is_root__eq}) AND (runs.start_time >= {start_time__gte}))
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE ((is_root = {is_root__eq}) AND (start_time >= {start_time__gte}))
            AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_1_metadata_key__eq})
        ) AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE ((is_root = {is_root__eq}) AND (start_time >= {start_time__gte}))
            AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_2_metadata_key__eq})
        )""",
            {
                "is_root__eq": True,
                "start_time__gte": "2020-12-01 00:00:00.000000",
                "runs_metadata_kv_1_1_1_metadata_key__eq": "user",
                "runs_metadata_kv_1_1_2_metadata_key__eq": "phone",
            },
            "((is_root = {is_root__eq}) AND (start_time >= {start_time__gte}))",
            "((is_root = {is_root__eq}) AND (start_time >= {start_time__gte}))",
            """
            WITH runs_and_times as (
                
                    SELECT
                        run_id,
                        start_time
                    FROM
                        runs_metadata_kv
                    WHERE ((is_root = {is_root__eq}) AND (start_time >= {start_time__gte}))
                    AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_1_metadata_key__eq})
         INTERSECT 
                    SELECT
                        run_id,
                        start_time
                    FROM
                        runs_metadata_kv
                    WHERE ((is_root = {is_root__eq}) AND (start_time >= {start_time__gte}))
                    AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_2_metadata_key__eq})
        
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,""",
            "((runs.is_root = {is_root__eq}) AND (runs.start_time >= {start_time__gte}))",
        )
    )

    query_str = 'and(eq(is_root, true), and(eq(input_key, "messages.role"), eq(input_value, "user")))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_inputs_kv
            WHERE (is_root = {is_root__eq})
            AND ((runs_inputs_kv.key = {runs_inputs_kv_2_1_1_input_key__eq}) AND (runs_inputs_kv.value = {runs_inputs_kv_2_1_1_input_value__eq}))
        )""",
        {
            "is_root__eq": True,
            "runs_inputs_kv_2_1_1_input_key__eq": "messages.role",
            "runs_inputs_kv_2_1_1_input_value__eq": '"user"',
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        """
            WITH runs_and_times as (
                
                    SELECT
                        run_id,
                        start_time
                    FROM
                        runs_inputs_kv
                    WHERE (is_root = {is_root__eq})
                    AND ((runs_inputs_kv.key = {runs_inputs_kv_2_1_1_input_key__eq}) AND (runs_inputs_kv.value = {runs_inputs_kv_2_1_1_input_value__eq}))
        
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,""",
        "(runs.is_root = {is_root__eq})",
    )

    query_str = 'and(eq(is_root, true), and(eq(metadata_key, "user"), eq(metadata_value, "phone")))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        (
            """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE (is_root = {is_root__eq})
            AND ((runs_metadata_kv.key = {runs_metadata_kv_2_1_1_metadata_key__eq}) AND (runs_metadata_kv.value = {runs_metadata_kv_2_1_1_metadata_value__eq}))
        )""",
            {
                "is_root__eq": True,
                "runs_metadata_kv_2_1_1_metadata_key__eq": "user",
                "runs_metadata_kv_2_1_1_metadata_value__eq": '"phone"',
            },
            "(is_root = {is_root__eq})",
            "(is_root = {is_root__eq})",
            """
            WITH runs_and_times as (
                
                    SELECT
                        run_id,
                        start_time
                    FROM
                        runs_metadata_kv
                    WHERE (is_root = {is_root__eq})
                    AND ((runs_metadata_kv.key = {runs_metadata_kv_2_1_1_metadata_key__eq}) AND (runs_metadata_kv.value = {runs_metadata_kv_2_1_1_metadata_value__eq}))
        
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,""",
            "(runs.is_root = {is_root__eq})",
        )
    )

    query_str = 'and(eq(is_root, true), neq(input_key, "messages.role"))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) NOT IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_inputs_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_inputs_kv.key = {runs_inputs_kv_1_1_1_input_key__neq})
        )""",
        {
            "is_root__eq": True,
            "runs_inputs_kv_1_1_1_input_key__neq": "messages.role",
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq})",
    )

    query_str = 'and(eq(is_root, true), eq(output_key, "messages.role"), neq(output_value, "user"))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_outputs_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_outputs_kv.key = {runs_outputs_kv_1_1_1_output_key__eq}) AND (runs_outputs_kv.value != {runs_outputs_kv_1_1_1_output_value__neq})
        )""",
        {
            "is_root__eq": True,
            "runs_outputs_kv_1_1_1_output_key__eq": "messages.role",
            "runs_outputs_kv_1_1_1_output_value__neq": '"user"',
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        """
            WITH runs_and_times as (
                
                    SELECT
                        run_id,
                        start_time
                    FROM
                        runs_outputs_kv
                    WHERE (is_root = {is_root__eq})
                    AND (runs_outputs_kv.key = {runs_outputs_kv_1_1_1_output_key__eq}) AND (runs_outputs_kv.value != {runs_outputs_kv_1_1_1_output_value__neq})
        
            ),
            (
                SELECT
                    min(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_min_start_time,
            (
                SELECT
                    max(start_time) as start_time
                FROM
                    runs_and_times
            ) as global_max_start_time,""",
        "(runs.is_root = {is_root__eq})",
    )

    query_str = 'and(eq(is_root, true), notlike(input_key, "%message%"))'

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) NOT IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_inputs_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_inputs_kv.key LIKE {runs_inputs_kv_1_1_1_input_key__notlike})
        )""",
        {
            "is_root__eq": True,
            "runs_inputs_kv_1_1_1_input_key__notlike": "%message%",
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq})",
    )

    query_str = (
        'and(eq(is_root, true), eq(metadata_key, "foo"), neq(metadata_key, "bar"))'
    )

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_1_metadata_key__eq})
        ) AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) NOT IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_2_metadata_key__neq})
        )""",
        {
            "is_root__eq": True,
            "runs_metadata_kv_1_1_1_metadata_key__eq": "foo",
            "runs_metadata_kv_1_1_2_metadata_key__neq": "bar",
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq})",
    )

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
            sql_subquery_skip_enabled=True,
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq}) AND  simpleJSONHas(runs.extra, {runs_metadata_kv_1_1_1_metadata_key__eq}) = 1 AND NOT simpleJSONHas(runs.extra, {runs_metadata_kv_1_1_2_metadata_key__neq}) = 1""",
        {
            "is_root__eq": True,
            "runs_metadata_kv_1_1_1_metadata_key__eq": "foo",
            "runs_metadata_kv_1_1_2_metadata_key__neq": "bar",
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq}) AND  simpleJSONHas(runs.extra, {runs_metadata_kv_1_1_1_metadata_key__eq}) = 1 AND NOT simpleJSONHas(runs.extra, {runs_metadata_kv_1_1_2_metadata_key__neq}) = 1",
    )

    query_str = (
        'and(eq(is_root, true), neq(metadata_key, "foo"), eq(metadata_value, "bar"))'
    )

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) NOT IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_1_metadata_key__neq})
        ) AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_metadata_kv.value = {runs_metadata_kv_1_1_2_metadata_value__eq})
        )""",
        {
            "is_root__eq": True,
            "runs_metadata_kv_1_1_1_metadata_key__neq": "foo",
            "runs_metadata_kv_1_1_2_metadata_value__eq": '"bar"',
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq})",
    )

    query_str = (
        'and(eq(is_root, true), neq(metadata_key, "foo"), neq(metadata_value, "bar"))'
    )

    assert parse_as_filter_directive(query_str).accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    ) == (
        """FROM runs
WHERE (runs.is_root = {is_root__eq})
 AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) NOT IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_metadata_kv.key = {runs_metadata_kv_1_1_1_metadata_key__neq})
        ) AND (runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) NOT IN (
            SELECT tenant_id, is_root, session_id, start_time, run_id
            FROM runs_metadata_kv
            WHERE (is_root = {is_root__eq})
            AND (runs_metadata_kv.value = {runs_metadata_kv_1_1_2_metadata_value__neq})
        )""",
        {
            "is_root__eq": True,
            "runs_metadata_kv_1_1_1_metadata_key__neq": "foo",
            "runs_metadata_kv_1_1_2_metadata_value__neq": '"bar"',
        },
        "(is_root = {is_root__eq})",
        "(is_root = {is_root__eq})",
        "",
        "(runs.is_root = {is_root__eq})",
    )


def test_not_like() -> None:
    with patch("app.models.query_lang.translate.uuid4", side_effect=TEST_UUIDS):
        query_str = 'and(notlike(inputs, "foo"), notlike(outputs, "bar"))'

        parsed_query = parse_as_filter_directive(query_str)
        assert parsed_query == Operation(
            operator=Operator.AND,
            arguments=(
                Comparison(Comparator.NOTLIKE, "inputs", "foo"),
                Comparison(Comparator.NOTLIKE, "outputs", "bar"),
            ),
        )

        sql_where = parsed_query.accept(
            SqlVisitorClickhouse(
                attributes=RUN_ATTRIBUTES,
                main_table="runs",
            )
        )
        assert sql_where == (
            """FROM runs
WHERE (((input_tokens NOT LIKE {input_tokens__notlike_id_0}))) AND (((output_tokens NOT LIKE {output_tokens__notlike_id_1})))""",
            {
                "input_tokens__notlike_id_0": "%foo%",
                "output_tokens__notlike_id_1": "%bar%",
            },
            "",
            "",
            "",
            "(((input_tokens NOT LIKE {input_tokens__notlike_id_0}))) AND (((output_tokens NOT LIKE {output_tokens__notlike_id_1})))",
        )


@pytest.mark.parametrize(
    "query_str, expected_query, expected_params, expect_error",
    [
        (
            'and(has("metadata", \'{"tenant_id": "tenant-1"}\'))',
            "FROM examples_log\nWHERE (examples_log.metadata @> $metadata_id_0_has)",
            {"metadata_id_0_has": {"tenant_id": "tenant-1"}},
            None,
        ),
        (
            'and(exists("metadata", "tenant_id"))',
            "FROM examples_log\nWHERE (examples_log.metadata ? $metadata_id_0_exists)",
            {"metadata_id_0_exists": "tenant_id"},
            None,
        ),
        (
            'and(has("metadata", \'{"tenant_id": "tenant-1"}\'), exists("metadata", "tenant_id"))',
            "FROM examples_log\nWHERE (examples_log.metadata ? $metadata_id_1_exists) AND (examples_log.metadata @> $metadata_id_0_has)",
            {
                "metadata_id_1_exists": "tenant_id",
                "metadata_id_0_has": {"tenant_id": "tenant-1"},
            },
            None,
        ),
        (
            'not(has("metadata", \'{"tenant_id": "tenant-1"}\'))',
            "FROM examples_log\nWHERE NOT ((examples_log.metadata @> $metadata_id_0_has))",
            {"metadata_id_0_has": {"tenant_id": "tenant-1"}},
            None,
        ),
        (
            'not(exists("metadata", "tenant_id"))',
            "FROM examples_log\nWHERE NOT ((examples_log.metadata ? $metadata_id_0_exists))",
            {"metadata_id_0_exists": "tenant_id"},
            None,
        ),
        (
            'not(has("metadata", \'{"tenant_id": "tenant-1"}\'), exists("metadata", "tenant_id"))',
            "FROM examples_log\nWHERE NOT ((examples_log.metadata ? $metadata_id_1_exists) OR (examples_log.metadata @> $metadata_id_0_has))",
            {
                "metadata_id_1_exists": "tenant_id",
                "metadata_id_0_has": {"tenant_id": "tenant-1"},
            },
            None,
        ),
        (
            'and(not(has("metadata", \'{"tenant_id": "tenant-1"}\')), not(exists("metadata", "tenant_id")))',
            "FROM examples_log\nWHERE (NOT ((examples_log.metadata ? $metadata_id_1_exists))) AND (NOT ((examples_log.metadata @> $metadata_id_0_has)))",
            {
                "metadata_id_1_exists": "tenant_id",
                "metadata_id_0_has": {"tenant_id": "tenant-1"},
            },
            None,
        ),
        (
            'and(not(exists("metadata", "foo")), not(exists("metadata", "baz")))',
            "FROM examples_log\nWHERE (NOT ((examples_log.metadata ? $metadata_id_0_exists))) AND (NOT ((examples_log.metadata ? $metadata_id_1_exists)))",
            {
                "metadata_id_0_exists": "foo",
                "metadata_id_1_exists": "baz",
            },
            None,
        ),
        (
            'and(not(exists("metadata", "foo")), not(exists("metadata", "foo")))',
            "FROM examples_log\nWHERE (NOT ((examples_log.metadata ? $metadata_id_0_exists))) AND (NOT ((examples_log.metadata ? $metadata_id_1_exists)))",
            {
                "metadata_id_0_exists": "foo",
                "metadata_id_1_exists": "foo",
            },
            None,
        ),
        (
            "invalid(query)",
            None,
            None,
            HTTPException,
        ),
        (
            'and(eq("is_root", true), gte("start_time", "2020-12-01 00:00:00.000000"))',
            None,
            None,
            HTTPException,  # only HAS and EXISTS are allowed
        ),
        (
            'or(has("metadata", \'{"tenant_id": "tenant-1"}\'), exists("metadata", "tenant_id"))',
            None,
            None,
            HTTPException,  # only AND / NOT are allowed
        ),
    ],
)
def test_translate_examples(query_str, expected_query, expected_params, expect_error):
    with patch("app.models.query_lang.translate.uuid4", side_effect=TEST_UUIDS):
        if expect_error:
            with pytest.raises(expect_error):
                parse_as_filter_directive_examples(query_str).accept(
                    SqlVisitorPostgres(
                        attributes=get_example_attributes("examples_log"),
                        main_table="examples_log",
                    )
                )
        else:
            query, params, _, _, _, _ = parse_as_filter_directive_examples(
                query_str
            ).accept(
                SqlVisitorPostgres(
                    attributes=get_example_attributes("examples_log"),
                    main_table="examples_log",
                )
            )
            assert query == expected_query
            assert params == expected_params


@pytest.mark.parametrize(
    "query_str, expected_filter_directive, expected_split_filter_directive, expected_pg_sql, expected_pg_params, expected_ch_sql, expected_ch_params, expect_error",
    [
        (
            'and(has("metadata", \'{"foo": "bar"}\'))',
            Operation(
                operator=Operator.AND,
                arguments=(Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),),
            ),
            (
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                    ),
                ),
                None,
            ),
            "FROM tracer_session\nWHERE (tracer_session.extra->'metadata' @> $metadata_id_0_has)",
            {"metadata_id_0_has": {"foo": "bar"}},
            None,
            None,
            None,
        ),
        (
            'and(exists("metadata", "foo"))',
            Operation(
                operator=Operator.AND,
                arguments=(Comparison(Comparator.EXISTS, "metadata", "foo"),),
            ),
            (
                Operation(
                    operator=Operator.AND,
                    arguments=(Comparison(Comparator.EXISTS, "metadata", "foo"),),
                ),
                None,
            ),
            "FROM tracer_session\nWHERE (tracer_session.extra->'metadata' ? $metadata_id_0_exists)",
            {"metadata_id_0_exists": "foo"},
            None,
            None,
            None,
        ),
        (
            'and(has("metadata", \'{"foo": "bar"}\'), exists("metadata", "foo"))',
            Operation(
                operator=Operator.AND,
                arguments=(
                    Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                    Comparison(Comparator.EXISTS, "metadata", "foo"),
                ),
            ),
            (
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                        Comparison(Comparator.EXISTS, "metadata", "foo"),
                    ),
                ),
                None,
            ),
            "FROM tracer_session\nWHERE (tracer_session.extra->'metadata' ? $metadata_id_1_exists) AND (tracer_session.extra->'metadata' @> $metadata_id_0_has)",
            {"metadata_id_0_has": {"foo": "bar"}, "metadata_id_1_exists": "foo"},
            None,
            None,
            None,
        ),
        (
            'not(has("metadata", \'{"foo": "bar"}\'))',
            Operation(
                operator=Operator.NOT,
                arguments=(Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),),
            ),
            (
                Operation(
                    operator=Operator.NOT,
                    arguments=(
                        Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                    ),
                ),
                None,
            ),
            "FROM tracer_session\nWHERE NOT ((tracer_session.extra->'metadata' @> $metadata_id_0_has))",
            {"metadata_id_0_has": {"foo": "bar"}},
            None,
            None,
            None,
        ),
        (
            'not(exists("metadata", "foo"), exists("metadata", "bar"), has("metadata", \'{"foo": "bar"}\'))',
            Operation(
                operator=Operator.NOT,
                arguments=(
                    Comparison(Comparator.EXISTS, "metadata", "foo"),
                    Comparison(Comparator.EXISTS, "metadata", "bar"),
                    Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                ),
            ),
            (
                Operation(
                    operator=Operator.NOT,
                    arguments=(
                        Comparison(Comparator.EXISTS, "metadata", "foo"),
                        Comparison(Comparator.EXISTS, "metadata", "bar"),
                        Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                    ),
                ),
                None,
            ),
            "FROM tracer_session\nWHERE NOT ((tracer_session.extra->'metadata' ? $metadata_id_0_exists) OR (tracer_session.extra->'metadata' ? $metadata_id_1_exists) OR (tracer_session.extra->'metadata' @> $metadata_id_2_has))",
            {
                "metadata_id_0_exists": "foo",
                "metadata_id_1_exists": "bar",
                "metadata_id_2_has": {"foo": "bar"},
            },
            None,
            None,
            None,
        ),
        (
            'not(exists("metadata", "foo"), exists("metadata", "bar"), has("metadata", \'{"foo": "bar"}\'), and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.5)))',
            None,
            None,
            None,
            None,
            None,
            None,
            HTTPException,
        ),
        (
            'and(not(has("metadata", \'{"foo": "bar"}\')), exists("metadata", "foo"), and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.5)))',
            Operation(
                operator=Operator.AND,
                arguments=(
                    Operation(
                        operator=Operator.NOT,
                        arguments=(
                            Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                        ),
                    ),
                    Comparison(Comparator.EXISTS, "metadata", "foo"),
                    Operation(
                        operator=Operator.AND,
                        arguments=(
                            Comparison(Comparator.EQ, "feedback_key", "hello"),
                            Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                        ),
                    ),
                ),
            ),
            (
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Operation(
                            operator=Operator.NOT,
                            arguments=(
                                Comparison(
                                    Comparator.HAS, "metadata", '{"foo": "bar"}'
                                ),
                            ),
                        ),
                        Comparison(Comparator.EXISTS, "metadata", "foo"),
                    ),
                ),
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.EQ, "feedback_key", "hello"),
                        Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                    ),
                ),
            ),
            "FROM tracer_session\nWHERE (NOT ((tracer_session.extra->'metadata' @> $metadata_id_0_has))) AND (tracer_session.extra->'metadata' ? $metadata_id_1_exists)",
            {"metadata_id_0_has": {"foo": "bar"}, "metadata_id_1_exists": "foo"},
            """SELECT session_id FROM (
    SELECT
        tenant_id, session_id, avgIf(score, key = {feedback_key_id_2_eq}) AS avg_feedback_0
    FROM
        feedbacks_rmt FINAL
    WHERE
        tenant_id = {tenant_id_eq} AND key IN {feedback_keys_id_3_in}
    GROUP BY tenant_id, session_id
    HAVING
        avg_feedback_0 >= {avg_feedback_0_id_4_score} AND avg_feedback_0 IS NOT NULL
)""",
            {
                "feedback_key_id_2_eq": "hello",
                "feedback_keys_id_3_in": ["hello"],
                "avg_feedback_0_id_4_score": 0.5,
                "tenant_id_eq": UUID("10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8"),
            },
            None,
        ),
        (
            'and(has("metadata", \'{"foo": "bar"}\'), and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.5)))',
            Operation(
                operator=Operator.AND,
                arguments=(
                    Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                    Operation(
                        operator=Operator.AND,
                        arguments=(
                            Comparison(Comparator.EQ, "feedback_key", "hello"),
                            Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                        ),
                    ),
                ),
            ),
            (
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                    ),
                ),
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.EQ, "feedback_key", "hello"),
                        Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                    ),
                ),
            ),
            "FROM tracer_session\nWHERE (tracer_session.extra->'metadata' @> $metadata_id_0_has)",
            {"metadata_id_0_has": {"foo": "bar"}},
            """SELECT session_id FROM (
    SELECT
        tenant_id, session_id, avgIf(score, key = {feedback_key_id_1_eq}) AS avg_feedback_0
    FROM
        feedbacks_rmt FINAL
    WHERE
        tenant_id = {tenant_id_eq} AND key IN {feedback_keys_id_2_in}
    GROUP BY tenant_id, session_id
    HAVING
        avg_feedback_0 >= {avg_feedback_0_id_3_score} AND avg_feedback_0 IS NOT NULL
)""",
            {
                "feedback_key_id_1_eq": "hello",
                "feedback_keys_id_2_in": ["hello"],
                "avg_feedback_0_id_3_score": 0.5,
                "tenant_id_eq": UUID("10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8"),
            },
            None,
        ),
        (
            'and(not(exists("metadata", "foo")), and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.5)))',
            Operation(
                operator=Operator.AND,
                arguments=(
                    Operation(
                        operator=Operator.NOT,
                        arguments=(Comparison(Comparator.EXISTS, "metadata", "foo"),),
                    ),
                    Operation(
                        operator=Operator.AND,
                        arguments=(
                            Comparison(Comparator.EQ, "feedback_key", "hello"),
                            Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                        ),
                    ),
                ),
            ),
            (
                Operation(
                    operator=Operator.NOT,
                    arguments=(Comparison(Comparator.EXISTS, "metadata", "foo"),),
                ),
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.EQ, "feedback_key", "hello"),
                        Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                    ),
                ),
            ),
            "FROM tracer_session\nWHERE NOT ((tracer_session.extra->'metadata' ? $metadata_id_0_exists))",
            {"metadata_id_0_exists": "foo"},
            """SELECT session_id FROM (
    SELECT
        tenant_id, session_id, avgIf(score, key = {feedback_key_id_1_eq}) AS avg_feedback_0
    FROM
        feedbacks_rmt FINAL
    WHERE
        tenant_id = {tenant_id_eq} AND key IN {feedback_keys_id_2_in}
    GROUP BY tenant_id, session_id
    HAVING
        avg_feedback_0 >= {avg_feedback_0_id_3_score} AND avg_feedback_0 IS NOT NULL
)""",
            {
                "feedback_key_id_1_eq": "hello",
                "feedback_keys_id_2_in": ["hello"],
                "avg_feedback_0_id_3_score": 0.5,
                "tenant_id_eq": UUID("10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8"),
            },
            None,
        ),
        (
            'and(and(eq("feedback_key", "hello"), lte("avg_feedback_score", 0.5)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.5)))',
            Operation(
                operator=Operator.AND,
                arguments=(
                    Operation(
                        operator=Operator.AND,
                        arguments=(
                            Comparison(Comparator.EQ, "feedback_key", "hello"),
                            Comparison(Comparator.LTE, "avg_feedback_score", 0.5),
                        ),
                    ),
                    Operation(
                        operator=Operator.AND,
                        arguments=(
                            Comparison(Comparator.EQ, "feedback_key", "world"),
                            Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                        ),
                    ),
                ),
            ),
            (
                None,
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Operation(
                            operator=Operator.AND,
                            arguments=(
                                Comparison(Comparator.EQ, "feedback_key", "hello"),
                                Comparison(Comparator.LTE, "avg_feedback_score", 0.5),
                            ),
                        ),
                        Operation(
                            operator=Operator.AND,
                            arguments=(
                                Comparison(Comparator.EQ, "feedback_key", "world"),
                                Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                            ),
                        ),
                    ),
                ),
            ),
            None,
            None,
            """SELECT session_id FROM (
    SELECT
        tenant_id, session_id, avgIf(score, key = {feedback_key_id_0_eq}) AS avg_feedback_0, avgIf(score, key = {feedback_key_id_1_eq}) AS avg_feedback_1
    FROM
        feedbacks_rmt FINAL
    WHERE
        tenant_id = {tenant_id_eq} AND key IN {feedback_keys_id_2_in}
    GROUP BY tenant_id, session_id
    HAVING
        avg_feedback_0 <= {avg_feedback_0_id_3_score} AND avg_feedback_0 IS NOT NULL AND avg_feedback_1 >= {avg_feedback_1_id_4_score} AND avg_feedback_1 IS NOT NULL
)""",
            {
                "avg_feedback_0_id_3_score": 0.5,
                "avg_feedback_1_id_4_score": 0.5,
                "feedback_key_id_0_eq": "hello",
                "feedback_key_id_1_eq": "world",
                "feedback_keys_id_2_in": ["hello", "world"],
                "tenant_id_eq": UUID("10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8"),
            },
            None,
        ),
        (
            'and(has("metadata", \'{"foo": "bar"}\'), and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.5), not(exists("metadata", "foo"))))',
            Operation(
                operator=Operator.AND,
                arguments=(
                    Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                    Operation(
                        operator=Operator.AND,
                        arguments=(
                            Comparison(Comparator.EQ, "feedback_key", "hello"),
                            Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                            Operation(
                                operator=Operator.NOT,
                                arguments=(
                                    Comparison(Comparator.EXISTS, "metadata", "foo"),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            (
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.HAS, "metadata", '{"foo": "bar"}'),
                        Operation(
                            operator=Operator.NOT,
                            arguments=(
                                Comparison(Comparator.EXISTS, "metadata", "foo"),
                            ),
                        ),
                    ),
                ),
                Operation(
                    operator=Operator.AND,
                    arguments=(
                        Comparison(Comparator.EQ, "feedback_key", "hello"),
                        Comparison(Comparator.GTE, "avg_feedback_score", 0.5),
                    ),
                ),
            ),
            "FROM tracer_session\nWHERE (NOT ((tracer_session.extra->'metadata' ? $metadata_id_1_exists))) AND (tracer_session.extra->'metadata' @> $metadata_id_0_has)",
            {"metadata_id_0_has": {"foo": "bar"}, "metadata_id_1_exists": "foo"},
            """SELECT session_id FROM (
    SELECT
        tenant_id, session_id, avgIf(score, key = {feedback_key_id_2_eq}) AS avg_feedback_0
    FROM
        feedbacks_rmt FINAL
    WHERE
        tenant_id = {tenant_id_eq} AND key IN {feedback_keys_id_3_in}
    GROUP BY tenant_id, session_id
    HAVING
        avg_feedback_0 >= {avg_feedback_0_id_4_score} AND avg_feedback_0 IS NOT NULL
)""",
            {
                "feedback_key_id_2_eq": "hello",
                "feedback_keys_id_3_in": ["hello"],
                "avg_feedback_0_id_4_score": 0.5,
                "tenant_id_eq": UUID("10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8"),
            },
            None,
        ),
    ],
)
def test_translate_tracer_sessions(
    query_str,
    expected_filter_directive,
    expected_split_filter_directive,
    expected_pg_sql,
    expected_pg_params,
    expected_ch_sql,
    expected_ch_params,
    expect_error,
):
    tenant_id = UUID("10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8")
    with patch("app.models.query_lang.translate.uuid4", side_effect=TEST_UUIDS):
        if expect_error:
            with pytest.raises(expect_error):
                parse_as_filter_directive_tracer_sessions(query_str)
                parse_and_split_filters_tracer_sessions(query_str)
        else:
            assert (
                parse_as_filter_directive_tracer_sessions(query_str)
                == expected_filter_directive
            )
            filters = parse_and_split_filters_tracer_sessions(query_str)
            assert filters == expected_split_filter_directive
            pg_filter, ch_filter = filters
            if pg_filter:
                query, params, _, _, _, _ = pg_filter.accept(
                    SqlVisitorPostgres(
                        attributes=get_tracer_session_attributes("tracer_session"),
                        main_table="tracer_session",
                    )
                )
                assert query == expected_pg_sql
                assert params == expected_pg_params
            if ch_filter:
                query, params = translate_session_feedback_operation_to_ch_query(
                    ch_filter, tenant_id
                )
                assert query == expected_ch_sql
                assert params == expected_ch_params
