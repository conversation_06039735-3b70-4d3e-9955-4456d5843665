import datetime
from typing import Any, Awaitable, Callable
from unittest.mock import Mock, patch
from uuid import uuid4

import aiochclient
import asyncpg
import or<PERSON><PERSON>
import pytest
from fastapi import HTTPException

import app.models.bulk_exports.crud as exports_crud
from app import config, crud, schemas
from app.config import settings
from app.models.bulk_exports.jobs import (
    cron_schedule_bulk_exports,
    orchestrate_bulk_export,
    run_bulk_export_partition,
)
from app.tests.conftest import get_minio_server_url
from app.tests.utils import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    fresh_tenant_client,
)

pytestmark = pytest.mark.serial

TEST_DESTINATION = schemas.BulkExportDestinationCreate(
    destination_type=schemas.BulkExportDestinationType.S3,
    display_name="Test Destination",
    config=schemas.BulkExportDestinationS3Config(
        endpoint_url=settings.S3_API_URL,
        bucket_name="test-bucket",
        region="us-east-1",
        prefix="prefix/path",
    ),
    credentials=schemas.BulkExportDestinationS3Credentials(
        access_key_id="access", secret_access_key="my-secret"
    ),
)


def _bulk_export_from(
    destination: schemas.BulkExportDestination,
    project: schemas.TracerSessionWithoutVirtualFields,
) -> schemas.BulkExportCreate:
    return schemas.BulkExportCreate(
        bulk_export_destination_id=destination.id,
        session_id=project.id,
        start_time=datetime.datetime.now(tz=datetime.UTC) - datetime.timedelta(days=14),
        end_time=datetime.datetime.now(tz=datetime.UTC) - datetime.timedelta(hours=1),
    )


def _bulk_export_run_from(
    export: schemas.BulkExport,
) -> schemas.BulkExportRunCreateBatch:
    return schemas.BulkExportRunCreateBatch(
        bulk_export_id=export.id,
        creates=[
            schemas.BulkExportRunCreateBatchItem(
                metadata=schemas.BulkExportRunMetadata(
                    prefix="partition/a",
                    start_time=export.start_time,
                    end_time=export.end_time,
                )
            ),
            schemas.BulkExportRunCreateBatchItem(
                metadata=schemas.BulkExportRunMetadata(
                    prefix="partition/b",
                    start_time=export.start_time,
                    end_time=export.end_time,
                )
            ),
        ],
    )


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_bulk_export_create(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination = await exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        assert destination.tenant_id == auth.tenant_id
        assert destination.destination_type == schemas.BulkExportDestinationType.S3
        assert destination.display_name == TEST_DESTINATION.display_name
        assert destination.config == TEST_DESTINATION.config
        assert set(destination.credentials_keys) == set(
            TEST_DESTINATION.credentials.model_dump().keys()
        )

        export_payload = _bulk_export_from(destination, project)
        export = await exports_crud.create_bulk_export(auth, export_payload)
        assert export.bulk_export_destination_id == destination.id
        assert export.session_id == project.id
        assert export.start_time == export_payload.start_time
        assert export.end_time == export_payload.end_time
        assert export.format == schemas.BulkExportFormat.PARQUET
        assert export.compression == schemas.BulkExportCompression.GZIP
        assert export.tenant_id == auth.tenant_id
        assert export.status == schemas.BulkExportStatus.CREATED
        assert export.finished_at is None

        run_create_batch_payload = _bulk_export_run_from(export)
        export_runs = await exports_crud.create_bulk_export_runs_batch(
            auth, run_create_batch_payload
        )
        assert len(export_runs) == len(run_create_batch_payload.creates)
        for run, run_payload in zip(export_runs, run_create_batch_payload.creates):
            assert run.bulk_export_id == export.id
            assert run.status == schemas.BulkExportRunStatus.CREATED
            assert run.retry_number == 0
            assert run.errors is None
            assert run.finished_at is None
            assert run.metadata.model_dump() == run_payload.metadata.model_dump()


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_bulk_export_create_invalid_params(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    """Verify that missing or another tenant's destination or project fails"""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        other_ws_project = await crud.create_tracer_session(
            auth, schemas.TracerSessionCreate()
        )
        other_ws_destination = await exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )

    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination = await exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        missing_project = project.model_copy(update={"id": uuid4()})
        missing_destination = destination.model_copy(update={"id": uuid4()})

        combinations = [
            (other_ws_destination, other_ws_project),
            (destination, other_ws_project),
            (other_ws_destination, project),
            (missing_destination, missing_project),
            (destination, missing_project),
            (missing_destination, project),
        ]
        for destination, project in combinations:
            export_payload = _bulk_export_from(destination, project)
            with pytest.raises(HTTPException) as e:
                await exports_crud.create_bulk_export(auth, export_payload)
            assert e.value.status_code == 400


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_bulk_export_status_propagation(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination = await exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )

        export_payload = _bulk_export_from(destination, project)
        export = await exports_crud.create_bulk_export(auth, export_payload)

        run_create_batch_payload = schemas.BulkExportRunCreateBatch(
            bulk_export_id=export.id,
            creates=[
                schemas.BulkExportRunCreateBatchItem(
                    metadata=schemas.BulkExportRunMetadata(
                        prefix=f"partition/{partition}",
                        start_time=export.start_time,
                        end_time=export.end_time,
                    )
                )
                for partition in ["a", "b", "c"]
            ],
        )
        export_runs = await exports_crud.create_bulk_export_runs_batch(
            auth, run_create_batch_payload
        )
        runa, runb, runc = export_runs
        # update one run status to Failed, and leave one Created
        run_update_payload = schemas.BulkExportRunUpdateBatch(
            bulk_export_id=export.id,
            updates=[
                schemas.BulkExportRunUpdateBatchItem(
                    id=runb.id,
                    status=schemas.BulkExportRunStatus.FAILED,
                )
            ],
        )
        updated_runs = await exports_crud.update_bulk_export_runs_batch(
            auth, run_update_payload
        )
        assert len(updated_runs) == len(run_update_payload.updates)

        # update the progress of one run
        progress = schemas.BulkExportRunProgress(
            rows_written=100,
            exported_files=["file1", "file2"],
            export_path="path/to/files",
            latest_cursor="cursor",
        )
        run = await exports_crud.update_bulk_export_run_progress(
            auth,
            export.id,
            runa.id,
            progress,
        )
        assert run.metadata.result == progress

        # Cancel the bulk export, check results
        update_payload = schemas.BulkExportUpdate(
            status=schemas.BulkExportStatus.CANCELLED
        )
        export_updated = await exports_crud.cancel_bulk_export(
            auth, export.id, update_payload
        )
        assert export_updated.status == schemas.BulkExportStatus.CANCELLED
        export_runs = await exports_crud.list_bulk_export_runs(auth, export.id)
        runa_updated = next(run for run in export_runs if run.id == runa.id)
        runb_updated = next(run for run in export_runs if run.id == runb.id)
        runc_updated = next(run for run in export_runs if run.id == runc.id)
        assert runa_updated.status == schemas.BulkExportRunStatus.CANCELLED
        assert runb_updated.status == schemas.BulkExportRunStatus.FAILED
        assert runc_updated.status == schemas.BulkExportRunStatus.CANCELLED

        # running the export partition job for run A should short-circuit and update to cancelled
        # after manually overriding the status (to simulate concurrent execution)
        await db_asyncpg.execute(
            "UPDATE bulk_export_runs SET status = $1 WHERE id = $2",
            schemas.BulkExportRunStatus.CREATED,
            runa.id,
        )
        await run_bulk_export_partition(auth.tenant_id, export.id, runa.id)
        runa_cancelled = await exports_crud.get_bulk_export_run(
            auth, export.id, runa.id
        )
        assert runa_cancelled.status == schemas.BulkExportRunStatus.CANCELLED

        # similar for failed & timedout statuses
        await db_asyncpg.execute(
            "UPDATE bulk_export_runs SET status = $1 WHERE id = $2",
            schemas.BulkExportRunStatus.CREATED,
            runa.id,
        )
        await db_asyncpg.execute(
            "UPDATE bulk_exports SET status = $1 WHERE id = $2",
            schemas.BulkExportStatus.FAILED,
            export.id,
        )
        await run_bulk_export_partition(auth.tenant_id, export.id, runa.id)
        runa_failed = await exports_crud.get_bulk_export_run(auth, export.id, runa.id)
        assert runa_failed.status == schemas.BulkExportRunStatus.FAILED
        await db_asyncpg.execute(
            "UPDATE bulk_export_runs SET status = $1 WHERE id = $2",
            schemas.BulkExportRunStatus.CREATED,
            runa.id,
        )
        await db_asyncpg.execute(
            "UPDATE bulk_exports SET status = $1 WHERE id = $2",
            schemas.BulkExportStatus.TIMEDOUT,
            export.id,
        )
        await run_bulk_export_partition(auth.tenant_id, export.id, runa.id)
        runa_failed = await exports_crud.get_bulk_export_run(auth, export.id, runa.id)
        assert runa_failed.status == schemas.BulkExportRunStatus.TIMEDOUT


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_bulk_export_read(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination_created = await exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        destinations = await exports_crud.list_bulk_export_destinations(auth)
        assert len(destinations) == 1
        assert destinations[0].model_dump() == destination_created.model_dump()
        destination = await exports_crud.get_bulk_export_destination(
            auth, destination_created.id
        )
        assert destination.model_dump() == destination_created.model_dump()
        assert "encrypted_credentials" not in destination.model_dump()

        export_payload = _bulk_export_from(destination, project)
        export_created = await exports_crud.create_bulk_export(auth, export_payload)
        exports = await exports_crud.list_bulk_exports(auth)
        assert len(exports) == 1
        assert exports[0].model_dump() == export_created.model_dump()
        export = await exports_crud.get_bulk_export(auth, export_created.id)
        assert export.model_dump() == export_created.model_dump()

        run_create_batch_payload = _bulk_export_run_from(export)
        export_runs_created = await exports_crud.create_bulk_export_runs_batch(
            auth, run_create_batch_payload
        )
        export_runs = await exports_crud.list_bulk_export_runs(auth, export_created.id)
        assert len(export_runs) == len(export_runs_created)
        for run, run_created in zip(export_runs, export_runs_created):
            assert run.model_dump() == run_created.model_dump()
            run = await exports_crud.get_bulk_export_run(
                auth, export_created.id, run_created.id
            )
            assert run.model_dump() == run_created.model_dump()


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_bulk_export_endpoints(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth
        client = authed_client.client
        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())

        # Create a Bulk Export Destination
        resp = await client.post(
            "/bulk-exports/destinations", json=TEST_DESTINATION.model_dump()
        )
        assert resp.status_code == 200, resp.text
        destination_created = schemas.BulkExportDestination(**resp.json())

        # List Bulk Export Destinations
        resp = await client.get("/bulk-exports/destinations")
        assert resp.status_code == 200, resp.text
        destinations = [schemas.BulkExportDestination(**dest) for dest in resp.json()]
        assert len(destinations) == 1
        assert destinations[0].model_dump() == destination_created.model_dump()

        # Get a Single Bulk Export Destination by ID
        resp = await client.get(f"/bulk-exports/destinations/{destination_created.id}")
        assert resp.status_code == 200, resp.text
        destination = schemas.BulkExportDestination(**resp.json())
        assert destination.model_dump() == destination_created.model_dump()
        assert "encrypted_credentials" not in destination.model_dump()

        # Create a Bulk Export
        export_payload = _bulk_export_from(destination, project)
        resp = await client.post("/bulk-exports", data=export_payload.model_dump_json())
        assert resp.status_code == 200, resp.text
        export_created = schemas.BulkExport(**resp.json())

        # List Bulk Exports
        resp = await client.get("/bulk-exports")
        assert resp.status_code == 200, resp.text
        exports = [schemas.BulkExport(**exp) for exp in resp.json()]
        assert len(exports) == 1
        assert exports[0].model_dump() == export_created.model_dump()

        # Get a Single Bulk Export by ID
        resp = await client.get(f"/bulk-exports/{export_created.id}")
        assert resp.status_code == 200, resp.text
        export = schemas.BulkExport(**resp.json())
        assert export.model_dump() == export_created.model_dump()

        # Create Bulk Export Runs Batch (cannot via API)
        run_create_batch_payload = _bulk_export_run_from(export)
        export_runs_created = await exports_crud.create_bulk_export_runs_batch(
            auth, run_create_batch_payload
        )

        # List Bulk Export Runs for a Specific Export
        resp = await client.get(f"/bulk-exports/{export_created.id}/runs")
        assert resp.status_code == 200, resp.text
        export_runs = [schemas.BulkExportRun(**run) for run in resp.json()]
        assert len(export_runs) == len(export_runs_created)
        for run, run_created in zip(export_runs, export_runs_created):
            assert run.model_dump() == run_created.model_dump()

        # Get a Single Bulk Export Run by ID
        for run_created in export_runs_created:
            resp = await client.get(
                f"/bulk-exports/{export_created.id}/runs/{run_created.id}"
            )
            assert resp.status_code == 200, resp.text
            run = schemas.BulkExportRun(**resp.json())
            assert run.model_dump() == run_created.model_dump()

        # Cancel a Bulk Export
        resp = await client.patch(
            f"/bulk-exports/{export_created.id}",
            json=schemas.BulkExportUpdate(
                status=schemas.BulkExportStatus.CANCELLED
            ).model_dump(),
        )
        assert resp.status_code == 200, resp.text


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_bulk_export_update(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        # Set up a bulk export with runs
        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination_created = await exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        export_payload = _bulk_export_from(destination_created, project)
        export_created = await exports_crud.create_bulk_export(auth, export_payload)
        run_create_batch_payload = _bulk_export_run_from(export_created)
        export_runs_created = await exports_crud.create_bulk_export_runs_batch(
            auth, run_create_batch_payload
        )

        # Set run statuses to running, then completed
        update_batch_payload = schemas.BulkExportRunUpdateBatch(
            bulk_export_id=export_created.id,
            updates=[
                schemas.BulkExportRunUpdateBatchItem(
                    id=run.id,
                    status=schemas.BulkExportRunStatus.RUNNING,
                )
                for run in export_runs_created
            ],
        )
        updated_runs = await exports_crud.update_bulk_export_runs_batch(
            auth, update_batch_payload
        )
        assert len(updated_runs) == len(export_runs_created)
        for updated_run in updated_runs:
            assert updated_run.status == schemas.BulkExportRunStatus.RUNNING
            assert updated_run.finished_at is None
        update_batch_payload = schemas.BulkExportRunUpdateBatch(
            bulk_export_id=export_created.id,
            updates=[
                schemas.BulkExportRunUpdateBatchItem(
                    id=run.id,
                    status=schemas.BulkExportRunStatus.COMPLETED,
                    finished_at=datetime.datetime.now(tz=datetime.UTC),
                )
                for run in export_runs_created
            ],
        )
        updated_runs = await exports_crud.update_bulk_export_runs_batch(
            auth, update_batch_payload
        )
        assert len(updated_runs) == len(export_runs_created)
        for updated_run in updated_runs:
            assert updated_run.status == schemas.BulkExportRunStatus.COMPLETED
            assert updated_run.finished_at is not None

        # Cancel the bulk export
        update_payload = schemas.BulkExportUpdate(
            status=schemas.BulkExportStatus.CANCELLED
        )
        export_updated = await exports_crud.cancel_bulk_export(
            auth, export_created.id, update_payload
        )
        assert export_updated.status == schemas.BulkExportStatus.CANCELLED


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_bulk_export_destination_delete(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination_created = await exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        export_payload = _bulk_export_from(destination_created, project)
        export_created = await exports_crud.create_bulk_export(auth, export_payload)
        run_create_batch_payload = _bulk_export_run_from(export_created)
        export_runs_created = await exports_crud.create_bulk_export_runs_batch(
            auth, run_create_batch_payload
        )
        assert len(export_runs_created) > 0

        # Delete the destination
        result = await exports_crud.delete_bulk_export_destination(
            auth, destination_created.id
        )
        assert result is None
        with pytest.raises(HTTPException) as exc_info:
            await exports_crud.get_bulk_export_destination(auth, destination_created.id)
        assert exc_info.value.status_code == 404
        with pytest.raises(HTTPException) as exc_info:
            await exports_crud.get_bulk_export(auth, export_created.id)
        assert exc_info.value.status_code == 404

        runs = await exports_crud.list_bulk_export_runs(auth, export_created.id)
        assert len(runs) == 0

        # should not raise an error
        await run_bulk_export_partition(
            auth.tenant_id, export_created.id, export_runs_created[0].id
        )


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_export_run(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
) -> None:
    """Test that a run can be exported via the export job."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth
        client = authed_client.client

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        project_id = project.id
        run_id = uuid4()
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ]
        serialized = {"name": "AgentExecutor"}
        error = "an error"

        run_data = {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "metadata": {"conversation_id": "112233"}},
            "error": error,
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(project_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "tags": ["tag1", "tag2"],
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }
        response = await client.post("/runs/batch", json={"post": [run_data]})
        assert response.status_code == 202
        await wait_until_task_queue_empty(timeout=10)  # type: ignore[call-arg]

        # Check that the run was created for the correct session
        run = await crud.get_run(auth, run_id)
        assert run.session_id == project_id

        # construct data export
        export_start_time = datetime.datetime.fromisoformat(
            "2023-05-05T05:13:24.571809Z"
        ) - datetime.timedelta(minutes=5)
        export_end_time = datetime.datetime.fromisoformat(
            "2023-05-05T05:13:32.022361Z"
        ) + datetime.timedelta(minutes=5)

        s3_config = schemas.BulkExportDestinationS3Config(
            bucket_name=settings.S3_BUCKET_NAME,
            prefix=str(uuid4()),
            endpoint_url=settings.S3_API_URL,
            region="us-east-1",
        )
        creds = schemas.BulkExportDestinationS3Credentials(
            access_key_id=settings.S3_ACCESS_KEY,
            secret_access_key=settings.S3_ACCESS_KEY_SECRET,
        )
        destination_created = await exports_crud.create_bulk_export_destination(
            auth,
            schemas.BulkExportDestinationCreate(
                display_name="Test Destination",
                config=s3_config,
                credentials=creds,
            ),
        )
        export_payload = schemas.BulkExportCreate(
            bulk_export_destination_id=destination_created.id,
            session_id=project_id,
            start_time=export_start_time,
            end_time=export_end_time,
        )
        export = await exports_crud.create_bulk_export(auth, export_payload)

        await cron_schedule_bulk_exports()

        await wait_until_task_queue_empty(timeout=15)  # type: ignore[call-arg]

        # Check that the run was exported
        exported_run_rows = await ch_client.fetch(
            "select * from s3({path}, {key}, {secret}, 'Parquet')",
            params={
                "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{s3_config.prefix}/**",
                "key": settings.S3_ACCESS_KEY,
                "secret": settings.S3_ACCESS_KEY_SECRET,
            },
        )

        exported_runs = [dict(row) for row in exported_run_rows]
        assert len(exported_runs) == 1
        new_run = exported_runs[0]

        # Check that the exported run matches the original run
        assert new_run["id"] == str(run.id)
        assert new_run["tenant_id"] == str(auth.tenant_id)
        assert new_run["name"] == run.name
        assert new_run["start_time"] == run.start_time
        assert new_run["end_time"] == run.end_time
        assert orjson.loads(new_run["extra"]) == run.extra
        assert new_run["error"] == run.error
        assert new_run["is_root"]
        assert new_run["run_type"] == run.run_type
        assert orjson.loads(new_run["inputs"]) == run.inputs
        assert orjson.loads(new_run["outputs"]) == run.outputs
        assert new_run["session_id"] == str(run.session_id)
        assert new_run["parent_run_id"] == (
            str(run.parent_run_id) if run.parent_run_id else None
        )
        if run.reference_example_id:
            assert new_run["reference_example_id"] == (
                str(run.reference_example_id) if run.reference_example_id else None
            )
        assert orjson.loads(new_run["events"]) == run.events
        assert orjson.loads(new_run["tags"]) == run.tags
        assert new_run["status"] == run.status
        assert new_run["trace_id"] == str(run.trace_id)
        assert new_run["dotted_order"] == run.dotted_order
        assert new_run["prompt_tokens"] == run.prompt_tokens
        assert new_run["completion_tokens"] == run.completion_tokens
        assert new_run["total_tokens"] == run.total_tokens
        assert new_run["first_token_time"] == run.first_token_time
        assert (
            orjson.loads(new_run["feedback_stats"])
            if new_run["feedback_stats"]
            else None
        ) == run.feedback_stats
        assert new_run["total_cost"] == run.total_cost
        assert new_run["prompt_cost"] == run.prompt_cost
        assert new_run["completion_cost"] == run.completion_cost
        assert new_run["trace_tier"] == run.trace_tier
        assert (
            orjson.loads(new_run["parent_run_ids"])
            if new_run["parent_run_ids"]
            else None
        ) == run.parent_run_ids

        # Check for completed status
        bulk_export_runs = await exports_crud.list_bulk_export_runs(auth, export.id)
        assert len(bulk_export_runs) == 1
        bulk_export_run = bulk_export_runs[0]
        assert all(
            run.status == schemas.BulkExportRunStatus.COMPLETED
            for run in bulk_export_runs
        )
        await cron_schedule_bulk_exports()
        await wait_until_task_queue_empty(timeout=10)  # type: ignore[call-arg]
        bulk_export = await exports_crud.get_bulk_export(auth, export.id)
        assert bulk_export.status == schemas.BulkExportStatus.COMPLETED

        # Check that rerunning from Running status does NOT duplicate the data
        # because we pick up where we left off
        await db_asyncpg.execute(
            "UPDATE bulk_export_runs SET status = $1 WHERE id = $2",
            schemas.BulkExportRunStatus.RUNNING,
            bulk_export_run.id,
        )
        await run_bulk_export_partition(auth.tenant_id, export.id, bulk_export_run.id)
        exported_run_rows = await ch_client.fetch(
            "select * from s3({path}, {key}, {secret}, 'Parquet')",
            params={
                "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{s3_config.prefix}/**",
                "key": settings.S3_ACCESS_KEY,
                "secret": settings.S3_ACCESS_KEY_SECRET,
            },
        )

        exported_runs = [dict(row) for row in exported_run_rows]
        assert len(exported_runs) == 1
        assert exported_runs[0] == new_run


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_export_zero_partitions(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
) -> None:
    """Test that a bulk export completes successfully if there are zero runs."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        project_id = project.id
        start_time = "2023-05-05T05:13:24.571809"

        # construct data export
        start_time_tz = datetime.datetime.fromisoformat(start_time + "Z")
        export_start_time = start_time_tz - datetime.timedelta(minutes=5)
        export_end_time = start_time_tz + datetime.timedelta(minutes=5)

        s3_config = schemas.BulkExportDestinationS3Config(
            bucket_name=settings.S3_BUCKET_NAME,
            prefix=str(uuid4()),
            endpoint_url=settings.S3_API_URL,
        )
        creds = schemas.BulkExportDestinationS3Credentials(
            access_key_id=settings.S3_ACCESS_KEY,
            secret_access_key=settings.S3_ACCESS_KEY_SECRET,
        )
        destination_created = await exports_crud.create_bulk_export_destination(
            auth,
            schemas.BulkExportDestinationCreate(
                display_name="Test Destination",
                config=s3_config,
                credentials=creds,
            ),
        )
        export_payload = schemas.BulkExportCreate(
            bulk_export_destination_id=destination_created.id,
            session_id=project_id,
            start_time=export_start_time,
            end_time=export_end_time,
        )
        export = await exports_crud.create_bulk_export(auth, export_payload)
        await cron_schedule_bulk_exports()
        await wait_until_task_queue_empty(timeout=10)  # type: ignore[call-arg]

        # Check bulk export status
        bulk_export = await exports_crud.get_bulk_export(auth, export.id)
        assert bulk_export.status == schemas.BulkExportStatus.COMPLETED


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_export_runs_parallel(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
) -> None:
    """Test that runs can be exported in parallel partitions via the export job."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth
        client = authed_client.client

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        project_id = project.id
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        serialized = {"name": "AgentExecutor"}
        error = "an error"
        start_time = "2023-05-05T05:13:24.571809"
        start_time_dt = datetime.datetime.fromisoformat(start_time)
        num_runs = 7

        run_ids = [uuid4() for _ in range(num_runs)]

        run_data = [
            {
                "name": "LLM",
                "start_time": (
                    start_time_dt + datetime.timedelta(days=day)
                ).isoformat(),
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar", "metadata": {"conversation_id": "112233"}},
                "error": error,
                "execution_order": 1,
                "serialized": serialized,
                "inputs": inputs,
                "outputs": outputs,
                "events": [],
                "session_id": str(project_id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
                "tags": ["tag1", "tag2"],
                "dotted_order": f"20230505T05132022361Z{run_id}",
                "trace_id": str(run_id),
            }
            for day, run_id in enumerate(run_ids)
        ]
        response = await client.post("/runs/batch", json={"post": run_data})
        assert response.status_code == 202
        await wait_until_task_queue_empty(timeout=10)  # type: ignore[call-arg]

        # construct data export
        start_time_tz = datetime.datetime.fromisoformat(start_time + "Z")
        export_start_time = start_time_tz - datetime.timedelta(minutes=5)
        export_end_time = start_time_tz + datetime.timedelta(days=8)

        s3_config = schemas.BulkExportDestinationS3Config(
            bucket_name=settings.S3_BUCKET_NAME,
            prefix=str(uuid4()),
            endpoint_url=settings.S3_API_URL,
        )
        creds = schemas.BulkExportDestinationS3Credentials(
            access_key_id=settings.S3_ACCESS_KEY,
            secret_access_key=settings.S3_ACCESS_KEY_SECRET,
        )
        destination_created = await exports_crud.create_bulk_export_destination(
            auth,
            schemas.BulkExportDestinationCreate(
                display_name="Test Destination",
                config=s3_config,
                credentials=creds,
            ),
        )
        export_payload = schemas.BulkExportCreate(
            bulk_export_destination_id=destination_created.id,
            session_id=project_id,
            start_time=export_start_time,
            end_time=export_end_time,
        )
        export = await exports_crud.create_bulk_export(auth, export_payload)

        old_setting = settings.BULK_EXPORT_PARTITION_TARGET_SIZE
        settings.BULK_EXPORT_PARTITION_TARGET_SIZE = 1
        await cron_schedule_bulk_exports()
        await wait_until_task_queue_empty(timeout=10)  # type: ignore[call-arg]
        settings.BULK_EXPORT_PARTITION_TARGET_SIZE = old_setting

        # Check that the runs were exported
        exported_run_rows = await ch_client.fetch(
            "select * from s3({path}, {key}, {secret}, 'Parquet')",
            params={
                "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{s3_config.prefix}/**",
                "key": settings.S3_ACCESS_KEY,
                "secret": settings.S3_ACCESS_KEY_SECRET,
            },
        )
        exported_runs = [dict(row) for row in exported_run_rows]
        assert len(exported_runs) == num_runs

        # Check that there was 1 partition per day
        bulk_export_runs = await exports_crud.list_bulk_export_runs(auth, export.id)
        assert len(bulk_export_runs) == num_runs
        assert all(
            run.status == schemas.BulkExportRunStatus.COMPLETED
            for run in bulk_export_runs
        )
        await cron_schedule_bulk_exports()
        await wait_until_task_queue_empty(timeout=10)  # type: ignore[call-arg]
        bulk_export = await exports_crud.get_bulk_export(auth, export.id)
        assert bulk_export.status == schemas.BulkExportStatus.COMPLETED


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_export_timeout(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        project_id = project.id
        start_time = "2023-05-05T05:13:24.571809"

        # construct data export
        start_time_tz = datetime.datetime.fromisoformat(start_time + "Z")
        export_start_time = start_time_tz - datetime.timedelta(minutes=5)
        export_end_time = start_time_tz + datetime.timedelta(minutes=5)

        s3_config = schemas.BulkExportDestinationS3Config(
            bucket_name=settings.S3_BUCKET_NAME,
            prefix=str(uuid4()),
            endpoint_url=settings.S3_API_URL,
        )
        creds = schemas.BulkExportDestinationS3Credentials(
            access_key_id=settings.S3_ACCESS_KEY,
            secret_access_key=settings.S3_ACCESS_KEY_SECRET,
        )
        destination_created = await exports_crud.create_bulk_export_destination(
            auth,
            schemas.BulkExportDestinationCreate(
                display_name="Test Destination",
                config=s3_config,
                credentials=creds,
            ),
        )
        export_payload = schemas.BulkExportCreate(
            bulk_export_destination_id=destination_created.id,
            session_id=project_id,
            start_time=export_start_time,
            end_time=export_end_time,
        )
        export = await exports_crud.create_bulk_export(auth, export_payload)
        await exports_crud.update_bulk_export_internal(
            auth,
            export.id,
            schemas.BulkExportUpdateInternal(status=schemas.BulkExportStatus.RUNNING),
        )
        # create a fake run to avoid short-circuiting
        await exports_crud.create_bulk_export_runs_batch(
            auth,
            _bulk_export_run_from(export),
        )

        await orchestrate_bulk_export(auth.tenant_id, export.id, override_timeout=0)
        await wait_until_task_queue_empty(timeout=10)  # type: ignore[call-arg]

        # Check bulk export status
        bulk_export = await exports_crud.get_bulk_export(auth, export.id)
        assert bulk_export.status == schemas.BulkExportStatus.TIMEDOUT


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@pytest.mark.parametrize(
    "s3_config,creds,expected_errors",
    [
        (
            schemas.BulkExportDestinationS3Config(
                bucket_name=settings.S3_BUCKET_NAME,
                prefix=str(uuid4()),
                endpoint_url=settings.S3_API_URL,
                region="us-east-1",
            ),
            schemas.BulkExportDestinationS3Credentials(
                access_key_id="invalid",
                secret_access_key="invalid",
            ),
            {"retry_0": "The blob store credentials provided are not valid"},
        ),
        (
            schemas.BulkExportDestinationS3Config(
                bucket_name="invalid_bucket",
                prefix=str(uuid4()),
                endpoint_url=settings.S3_API_URL,
                region="us-east-1",
            ),
            schemas.BulkExportDestinationS3Credentials(
                access_key_id=settings.S3_ACCESS_KEY,
                secret_access_key=settings.S3_ACCESS_KEY_SECRET,
            ),
            {"retry_0": "The specified blob store bucket is not valid"},
        ),
        (
            schemas.BulkExportDestinationS3Config(
                bucket_name=settings.S3_BUCKET_NAME,
                prefix=str(uuid4()),
                endpoint_url="gs://backfill_bucket/",
                region="us-east-1",
            ),
            schemas.BulkExportDestinationS3Credentials(
                access_key_id=settings.S3_ACCESS_KEY,
                secret_access_key=settings.S3_ACCESS_KEY_SECRET,
            ),
            {"retry_0": "The endpoint_url provided is invalid"},
        ),
    ],
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_export_errors(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
    s3_config: schemas.BulkExportDestinationS3Config,
    creds: schemas.BulkExportDestinationS3Credentials,
    expected_errors: dict,
) -> None:
    """Test that a run can be exported via the export job with different error types."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth
        client = authed_client.client

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        project_id = project.id
        run_id = uuid4()
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ]
        serialized = {"name": "AgentExecutor"}
        error = "an error"

        run_data = {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "metadata": {"conversation_id": "112233"}},
            "error": error,
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(project_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "tags": ["tag1", "tag2"],
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }
        response = await client.post("/runs/batch", json={"post": [run_data]})
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        # Check that the run was created for the correct session
        run = await crud.get_run(auth, run_id)
        assert run.session_id == project_id

        # construct data export
        export_start_time = datetime.datetime.fromisoformat(
            "2023-05-05T05:13:24.571809Z"
        ) - datetime.timedelta(minutes=5)
        export_end_time = datetime.datetime.fromisoformat(
            "2023-05-05T05:13:32.022361Z"
        ) + datetime.timedelta(minutes=5)

        destination_created = await exports_crud.create_bulk_export_destination(
            auth,
            schemas.BulkExportDestinationCreate(
                display_name="Test Destination",
                config=s3_config,
                credentials=creds,
            ),
        )
        export_payload = schemas.BulkExportCreate(
            bulk_export_destination_id=destination_created.id,
            session_id=project_id,
            start_time=export_start_time,
            end_time=export_end_time,
        )
        export = await exports_crud.create_bulk_export(auth, export_payload)

        with patch("app.models.bulk_exports.jobs.settings.BULK_EXPORT_JOB_RETRIES", 0):
            await orchestrate_bulk_export(export.tenant_id, export.id)

            await wait_until_task_queue_empty()

            await orchestrate_bulk_export(export.tenant_id, export.id)

            # Check bulk export status
            bulk_export = await exports_crud.get_bulk_export(auth, export.id)
            assert bulk_export.status == schemas.BulkExportStatus.FAILED

            runs = await exports_crud.list_bulk_export_runs(auth, export.id)
            assert len(runs) == 1
            run = runs[0]
            assert run.retry_number == 1
            assert run.errors == expected_errors


@pytest.mark.asyncio
async def test_bulk_export_fails_with_missing_destination(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
):
    """Test that a bulk export fails when the destination is missing."""

    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        project_id = project.id

        # construct data export with a non-existent destination ID
        export_start_time = datetime.datetime.fromisoformat(
            "2023-05-05T05:13:24.571809Z"
        ) - datetime.timedelta(minutes=5)
        export_end_time = datetime.datetime.fromisoformat(
            "2023-05-05T05:13:32.022361Z"
        ) + datetime.timedelta(minutes=5)

        s3_config = schemas.BulkExportDestinationS3Config(
            bucket_name=settings.S3_BUCKET_NAME,
            prefix=str(uuid4()),
            endpoint_url=settings.S3_API_URL,
        )
        creds = schemas.BulkExportDestinationS3Credentials(
            access_key_id=settings.S3_ACCESS_KEY,
            secret_access_key=settings.S3_ACCESS_KEY_SECRET,
        )
        destination_created = await exports_crud.create_bulk_export_destination(
            auth,
            schemas.BulkExportDestinationCreate(
                display_name="Test Destination",
                config=s3_config,
                credentials=creds,
            ),
        )

        export_payload = schemas.BulkExportCreate(
            bulk_export_destination_id=destination_created.id,
            session_id=project_id,
            start_time=export_start_time,
            end_time=export_end_time,
        )
        export = await exports_crud.create_bulk_export(auth, export_payload)

        async with fresh_tenant_client(
            db_asyncpg,
            use_api_key,
            tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
        ) as authed_client:
            other_auth = authed_client.auth
            different_tenant_id = other_auth.tenant_id

            # Update the destination to belong to a different tenant in the database
            query = """
            UPDATE bulk_export_destinations
            SET tenant_id = $1
            WHERE id = $2
            """
            await db_asyncpg.execute(query, different_tenant_id, destination_created.id)

            with patch(
                "app.models.bulk_exports.jobs.settings.BULK_EXPORT_JOB_RETRIES", 0
            ):
                await orchestrate_bulk_export(export.tenant_id, export.id)
                await wait_until_task_queue_empty()

                # Check bulk export status
                bulk_export = await exports_crud.get_bulk_export(auth, export.id)
                assert bulk_export.status == schemas.BulkExportStatus.FAILED
