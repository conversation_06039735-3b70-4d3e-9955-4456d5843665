import asyncio
import inspect
import logging
import random
import string
import time
import uuid
import zlib
from collections import defaultdict
from contextlib import AsyncExitStack, asynccontextmanager
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Any, AsyncGenerator, Awaitable, Callable, Dict, Optional, Sequence
from unittest.mock import patch
from uuid import UUID, uuid4

import aiochclient
import asyncpg
import fastapi
import orj<PERSON>
import pytest
from cryptography.hazmat.primitives.asymmetric import rsa
from httpx import ASGITransport, AsyncClient
from httpx._types import FileTypes
from jwt import encode
from lc_config.settings import shared_settings
from lc_config.tenant_config import OrganizationConfig, TenantConfig
from lc_database import redis
from lc_database.curl import platform_request

from app import config, crud, models, schemas
from app.api.auth import AuthInfo, TenantlessAuthInfo
from app.api.auth.schemas import OrganizationRoles, OrgAuthInfo, UnverifiedAuthInfo
from app.main import app
from app.models.api_keys.crud import create_api_key, create_personal_access_token
from app.models.feedback.fetch import fetch_feedback
from app.models.identities.crud import add_basic_auth_users
from app.models.tenants.create import create_tenant
from app.tests.ensure import DecodedUserInfo, ensure_user

logger = logging.getLogger(__name__)

TEST_TENANT_ONE_UUID = (
    "2ebda79f-2946-4491-a9ad-d642f49e0815"
    if config.settings.AUTH_TYPE != "none"
    else str(config.settings.SINGLETON_TENANT_ID)
)
assert TEST_TENANT_ONE_UUID is not None
TEST_TENANT_TWO_UUID = "9aa8cd37-35b4-4151-b80a-f78f5bd49f55"
TEST_TENANT_LOW_PAYLOAD_SIZE_LIMIT_UUID = "be3c9933-ba62-48bf-a167-99c0a989d20d"
TEST_TENANT_LOW_TOTAL_REQUESTS_LIMIT_UUID = "c4b0d3a0-0b0a-4b0a-9b0a-0b0a0b0a0b0a"
TEST_TENANT_LOW_UNIQUE_TRACES_LIMIT_UUID = "1ff3a839-7678-45b2-91e6-83c474f3ed81"
TEST_TENANT_LOW_EVENTS_INGESTED_PER_MIN_UUID = "0c1062ea-f03b-439f-9fb0-6c4bdebbd0db"
TEST_TENANT_CUSTOM_USAGE_LIMIT_UUID = "b0aa97b3-4cb0-4583-88d8-4ec0c7769e88"

TEST_USER_LOW_PAY = "<EMAIL>"
TEST_USER_LOW_PAYLOAD_SIZE_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_LOW_PAY)
)
TEST_USER_LOW_TOTAL_REQUESTS = "<EMAIL>"
TEST_USER_LOW_TOTAL_REQUESTS_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_LOW_TOTAL_REQUESTS)
)
TEST_SECOND_USER_LOW_TOTAL_REQUESTS = "<EMAIL>"
TEST_SECOND_USER_LOW_TOTAL_REQUESTS_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_SECOND_USER_LOW_TOTAL_REQUESTS)
)
TEST_SECOND_USER_LOW_PAYLOAD_SIZE_LIMIT = "<EMAIL>"
TEST_SECOND_USER_LOW_PAYLOAD_SIZE_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_SECOND_USER_LOW_PAYLOAD_SIZE_LIMIT)
)
TEST_USER_LOW_UNIQUE_TRACES_LIMIT = "<EMAIL>"
TEST_USER_LOW_UNIQUE_TRACES_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_LOW_UNIQUE_TRACES_LIMIT)
)
TEST_SECOND_USER_LOW_UNIQUE_TRACES_LIMIT = "<EMAIL>"
TEST_SECOND_USER_LOW_UNIQUE_TRACES_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_SECOND_USER_LOW_UNIQUE_TRACES_LIMIT)
)
TEST_USER_CUSTOM_USAGE_LIMIT = "<EMAIL>"
TEST_USER_CUSTOM_USAGE_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_CUSTOM_USAGE_LIMIT)
)
TEST_USER_LOW_EVENTS_INGESTED_PER_MINUTE_LIMIT = "<EMAIL>"
TEST_USER_LOW_EVENTS_INGESTED_PER_MINUTE_LIMIT_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_LOW_EVENTS_INGESTED_PER_MINUTE_LIMIT)
)
TEST_USER_ONE_EMAIL = (
    "<EMAIL>"
    if config.settings.AUTH_TYPE != "none"
    else "<EMAIL>"
)
TEST_USER_ONE_UUID = (
    str(uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_ONE_EMAIL))
    if config.settings.AUTH_TYPE != "none"
    else str(config.settings.SINGLETON_TENANT_ID)
)  # a2a4bd89-d48f-56cf-849d-d3a58ae39dd3
TEST_USER_TWO_EMAIL = "<EMAIL>"
TEST_USER_TWO_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_TWO_EMAIL)
)  # c9f0ad3b-5f56-56e3-b30c-470eaf00c87f
TEST_USER_THREE_EMAIL = "<EMAIL>"
TEST_USER_THREE_UUID = str(uuid.uuid5(uuid.NAMESPACE_OID, TEST_USER_THREE_EMAIL))
TEST_TENANT_ONE_USER_TWO_EMAIL = "<EMAIL>"
TEST_TENANT_ONE_USER_TWO_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_TENANT_ONE_USER_TWO_EMAIL)
)
TEST_TENANT_ONE_USER_THREE_EMAIL = "<EMAIL>"
TEST_TENANT_ONE_USER_THREE_UUID = str(
    uuid.uuid5(uuid.NAMESPACE_OID, TEST_TENANT_ONE_USER_THREE_EMAIL)
)
TEST_SINGLETON_TENANT_DATASET_UUID = "825e2f7e-ff65-4cc3-b951-c673483c782a"
TEST_TENANT_ONE_DATASET_UUID = "4871202a-a817-4c3f-b750-0b8fe6a7e464"
TEST_TENANT_ONE_CHAT_DATASET_UUID = "b7522b92-0d6e-477c-86b6-c0153f24e665"
TEST_TENANT_ONE_LLM_DATASET_UUID = "9CE261A1-A09B-4692-9058-3DBD626578DF"
TEST_TENANT_TWO_DATASET_UUID = "b7622b92-0d6e-477c-86b6-c0153f24e665"
TEST_DISABLED_TENANT_UUID = "f8569dbf-5f93-4727-ad58-c88332f4f34f"
TEST_DISABLED_USER_EMAIL = "<EMAIL>"
TEST_DISABLED_USER_UUID = str(uuid.uuid5(uuid.NAMESPACE_OID, TEST_DISABLED_USER_EMAIL))
ALGORITHM = "RS256"
PUBLIC_KEY_ID = "sample-key-id"
RSA_PRIVATE_KEY = rsa.generate_private_key(public_exponent=65537, key_size=2048)
RSA_PUBLIC_KEY = RSA_PRIVATE_KEY.public_key()
TEST_PASSWORD = "LangSmithTest123!"

SHARED_ORG_DEFAULT_CONFIG_RBAC_ENABLED = (
    config.settings.SHARED_ORG_DEFAULT_CONFIG.model_copy(
        update={
            "can_use_rbac": True,
            "can_add_seats": True,
            "can_use_bulk_export": True,
            "max_identities": 30,
            "max_langgraph_cloud_deployments": 10,
        },
        deep=True,
    )
)

SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE = (
    config.settings.SHARED_TENANT_DEFAULT_CONFIG.model_copy(
        update={
            "can_use_rbac": True,
            "can_add_seats": True,
            "max_identities": 30,
            "organization_config": SHARED_ORG_DEFAULT_CONFIG_RBAC_ENABLED,
        },
        deep=True,
    )
)
SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES = (
    config.settings.SHARED_TENANT_DEFAULT_CONFIG.model_copy(
        update={
            "can_use_rbac": True,
            "can_add_seats": True,
            "max_identities": 30,
            "organization_config": SHARED_ORG_DEFAULT_CONFIG_RBAC_ENABLED.model_copy(
                update={"max_workspaces": -1}
            ),
        },
        deep=True,
    )
)


async def ensure_tenant(
    asyncpg_db: asyncpg.Connection,
    auth: AuthInfo,
    display_name: Optional[str] = None,
    organization_id: Optional[UUID] = None,
    org_config: OrganizationConfig = SHARED_ORG_DEFAULT_CONFIG_RBAC_ENABLED,
) -> schemas.Tenant:
    """Return the session ID for a given tenant"""
    try:
        tenant = await crud.get_tenant(auth)
    except Exception:
        # Handle race condition where tenant is created by another test
        try:
            tenant = await create_tenant(
                asyncpg_db,
                TenantlessAuthInfo(
                    available_tenants=[auth.tenant_id],
                    user_id=auth.user_id,
                    ls_user_id=auth.ls_user_id,
                ),
                schemas.TenantCreatePrivileged(
                    id=auth.tenant_id,
                    display_name=display_name or auth.tenant_handle or "test",
                    config=auth.tenant_config,
                    is_personal=auth.organization_is_personal,
                    tenant_handle=auth.tenant_handle,
                    organization_id=organization_id,
                ),
                org_config=org_config,
            )
        except fastapi.exceptions.HTTPException as e:
            if e.status_code == 409:
                logger.info(e)
                tenant = await crud.get_tenant(auth)
            else:
                raise
        except asyncpg.exceptions.UniqueViolationError as e:
            logger.info(e)
            tenant = await crud.get_tenant(auth)
    return tenant


async def ensure_identity(
    db_asyncpg: asyncpg.Connection, auth: AuthInfo, read_only: bool = False
) -> UUID | None:
    """Ensure identity exists for a given tenant"""
    tenant = await ensure_tenant(db_asyncpg, auth)
    if auth.user_id is None or auth.ls_user_id is None:
        logger.info("No user_id or ls_user_id, skipping identity creation")
        return None
    org_identity_id = await db_asyncpg.fetchval(
        """insert into identities (user_id, organization_id, read_only, role_id, access_scope, ls_user_id)
        values ($1, $2, $3, (SELECT id from roles where name = $4), 'organization', $5)
        on conflict (ls_user_id, organization_id) WHERE access_scope = 'organization'
        do update set ls_user_id = $5
        returning id""",
        auth.user_id,
        tenant.organization_id,
        read_only,
        OrganizationRoles.USER.value if read_only else OrganizationRoles.ADMIN.value,
        auth.ls_user_id,
    )
    return await db_asyncpg.fetchval(
        """insert into identities (user_id, tenant_id, organization_id, read_only, role_id, parent_identity_id, ls_user_id)
        values ($1, $2, $3, $4, (SELECT id from roles where name = $5), $6, $7)
        on conflict (ls_user_id, tenant_id) WHERE access_scope = 'workspace'
        do update set ls_user_id = $7
        returning id""",
        auth.user_id,
        auth.tenant_id,
        tenant.organization_id,
        read_only,
        "WORKSPACE_VIEWER" if read_only else "WORKSPACE_ADMIN",
        org_identity_id,
        auth.ls_user_id,
    )


async def ensure_basic_auth_user(auth: AuthInfo) -> schemas.UserWithPassword:
    payload = schemas.BasicAuthMemberCreate(
        user_id=auth.user_id,
        email=auth.user_email,
        full_name=auth.user_full_name,
        password=TEST_PASSWORD,
    )
    return (await add_basic_auth_users(auth, [payload]))[0]


async def api_key_for_tenant(auth: AuthInfo, read_only: bool = False) -> str:
    """Return the API key for a given tenant"""
    key = await create_api_key(auth, schemas.APIKeyCreateRequest(read_only=read_only))
    return key.key


async def pat_for_tenant(auth: AuthInfo, read_only: bool = False) -> str:
    """Return a personal access token for the given tenant"""
    key = await create_personal_access_token(
        auth, schemas.APIKeyCreateRequest(read_only=read_only)
    )
    return key.key


def _assert_settings():
    if config.settings.AUTH_TYPE == "supabase":
        assert config.settings.SUPABASE_JWT_SECRET
    elif config.settings.BASIC_AUTH_ENABLED:
        assert config.settings.BASIC_AUTH_JWT_SECRET


def jwt_for_authinfo(auth: AuthInfo | TenantlessAuthInfo) -> str:
    """Return the JWT for a given tenant"""
    return jwt_for_user(auth.user_id, auth.user_email, auth.user_full_name)


def jwt_for_user(
    user_id: UUID | None,
    user_email: str | None,
    user_full_name: str | None = None,
    app_metadata: dict | None = None,
) -> str:
    """Return the JWT for a given tenant"""
    _assert_settings()
    assert user_id
    user_metadata = {} if user_full_name is None else {"full_name": user_full_name}
    if config.settings.AUTH_TYPE == "supabase":
        return encode(
            {
                "sub": str(user_id),
                "aud": "authenticated",
                "email": user_email,
                "user_metadata": user_metadata,
                **({"app_metadata": app_metadata} if app_metadata else {}),
            },
            config.settings.SUPABASE_JWT_SECRET,
            algorithm="HS256",
        )
    elif config.settings.BASIC_AUTH_ENABLED:
        return encode(
            {
                "sub": str(user_id),
                "aud": "authenticated",
                "email": user_email,
                "full_name": user_full_name,
            },
            config.settings.BASIC_AUTH_JWT_SECRET,
            algorithm="HS256",
        )
    else:
        assert RSA_PRIVATE_KEY
        return encode(
            {
                "aud": config.settings.OAUTH_CLIENT_ID,
                "email": user_email,
                "name": user_full_name,
            },
            RSA_PRIVATE_KEY,
            algorithm=ALGORITHM,
        )


async def jwt_for_tenant(
    db_asyncpg: asyncpg.Connection, auth: AuthInfo, read_only: bool = False
) -> str:
    """Return the JWT for a given tenant"""
    await ensure_identity(db_asyncpg, auth, read_only=read_only)
    return jwt_for_authinfo(auth)


async def tracer_session_id_for_tenant(
    db_asyncpg: asyncpg.Connection, auth: AuthInfo, name: str = "default"
) -> UUID:
    """Return the session ID for a given tenant"""
    await ensure_tenant(db_asyncpg, auth)
    session = await crud.create_tracer_session(
        auth,
        schemas.TracerSessionCreate(name=name, description="test"),
        upsert=True,
    )

    return session.id


async def dataset_id_for_tenant(
    db_asyncpg: asyncpg.Connection,
    auth: AuthInfo,
    data_type: schemas.DataType = schemas.DataType.kv,
) -> UUID:
    """Return the dataset ID for a given tenant"""
    tenant = await ensure_tenant(db_asyncpg, auth)
    ds_id_map = {
        (
            schemas.DataType.kv,
            config.settings.SINGLETON_TENANT_ID,
        ): TEST_SINGLETON_TENANT_DATASET_UUID,
        (schemas.DataType.kv, UUID(TEST_TENANT_ONE_UUID)): TEST_TENANT_ONE_DATASET_UUID,
        (schemas.DataType.kv, UUID(TEST_TENANT_TWO_UUID)): TEST_TENANT_TWO_DATASET_UUID,
        (
            schemas.DataType.chat,
            UUID(TEST_TENANT_ONE_UUID),
        ): TEST_TENANT_ONE_CHAT_DATASET_UUID,
        (
            schemas.DataType.llm,
            UUID(TEST_TENANT_ONE_UUID),
        ): TEST_TENANT_ONE_LLM_DATASET_UUID,
    }
    if (data_type, tenant.id) in ds_id_map:
        dataset_id = ds_id_map[(data_type, tenant.id)]
    else:
        raise NotImplementedError
    try:
        dataset = await crud.get_dataset(auth, UUID(dataset_id))
    except Exception:
        dataset = await crud.create_dataset(
            auth,
            schemas.DatasetCreate(
                id=UUID(dataset_id),
                name=random_lower_string(),
                description="test",
                data_type=data_type,
            ),
        )
    return dataset.id


def random_lower_string() -> str:
    """Generate a random lower string."""
    return "".join(random.choices(string.ascii_lowercase, k=32))


#
async def queue_waiter(
    timeout: int,
    n_empty_threshold: int,
    debug: bool = False,
    sleep_length_sec: float = 0.01,
    extra: str = "",
) -> None:
    start_time = time.time()
    seen_empty: list[bool] = []
    async with AsyncExitStack() as stack:
        separate_queues = [
            await stack.enter_async_context(redis.async_queue(name))
            for name in shared_settings.SEPARATE_QUEUES_WITH_SINGLE_WORKER
        ]
        if shared_settings.REDIS_SHARD_URIS:
            single_queues = [
                await stack.enter_async_context(
                    redis.async_sharded_queue_ctx_for_node(
                        shared_settings.SINGLE_WORKER_QUEUE, nodename
                    )
                )
                for nodename in shared_settings.REDIS_SHARD_URIS
            ]
        else:
            single_queues = [
                await stack.enter_async_context(
                    redis.async_queue(shared_settings.SINGLE_WORKER_QUEUE)
                )
            ]

        while True:
            for queue in separate_queues + single_queues:
                info = await queue.info(jobs=debug)

                # jobs that are waiting their scheduled time
                scheduled_count = info["scheduled"]

                # jobs that are queued
                queued_count = info["queued"]

                # jobs in progress
                in_progress_count = info["active"]

                empty = not (scheduled_count or queued_count or in_progress_count)
                if debug:
                    logger.info(
                        f"""
                    [{extra}] Waited for: {time.time() - start_time} for {queue.redis}

                    seen_empty: {seen_empty}
                    empty: {empty}

                    Last Redis Check State:
                        scheduled_count: {scheduled_count}
                        queued_count: {queued_count}
                        in_progress_count: {in_progress_count}
                    """
                    )
                if len(seen_empty) >= n_empty_threshold and empty:
                    logger.info(
                        f"[{extra}] Queue is empty, returning. Time spent: {time.time() - start_time:.2f}s"
                    )
                    return
                if empty:
                    seen_empty.append(empty)
                else:
                    seen_empty = []
                time.sleep(sleep_length_sec)
                if time.time() - start_time > timeout:
                    jobs_str = f" Jobs: {info['jobs']}" if debug else ""
                    raise TimeoutError(
                        f"[{extra}] Timed out waiting for task queue to empty.{jobs_str}"
                    )


async def ensure_auth_info(
    db_asyncpg: asyncpg.Connection,
    tenant_id: UUID,
    tenant_handle: str | None,
    user_id: UUID | None,
    user_email: str | None,
    user_full_name: str | None,
    tenant_config: TenantConfig,
    identity_permissions: list[str] = ["projects:create"],
    read_only: bool = False,
    organization_is_personal: bool = False,
    org_id: UUID | None = None,
    should_ensure_user: bool = False,
    user_provider: str | None = None,
    user_saml_provider_id: str | None = None,
    org_config: OrganizationConfig = SHARED_ORG_DEFAULT_CONFIG_RBAC_ENABLED,
) -> tuple[AuthInfo, schemas.Tenant]:
    auth = AuthInfo(
        tenant_id=tenant_id,
        tenant_handle=tenant_handle,
        tenant_config=tenant_config,
        organization_is_personal=organization_is_personal,
        user_id=user_id,
        user_email=user_email,
        user_full_name=user_full_name,
        identity_permissions=identity_permissions,
        organization_id=org_id,
        org_config=org_config,
    )
    # user must be created before tenant/org in order for ls_user_id to be populated on the identity
    if should_ensure_user and user_id is not None:
        assert user_email is not None
        user = await ensure_user(
            DecodedUserInfo(
                sub="blahblah",
                id=str(user_id),
                email=user_email,
                full_name=user_full_name or "",
                provider=user_provider,
                saml_provider_id=user_saml_provider_id,
            )
        )
        auth.ls_user_id = user["ls_user_id"]
    tenant = await ensure_tenant(
        db_asyncpg,
        auth,
        organization_id=org_id,
        org_config=org_config,
    )
    auth.organization_id = tenant.organization_id
    identity_id = await ensure_identity(db_asyncpg, auth, read_only=read_only)
    auth.identity_id = identity_id
    return auth, tenant


def include_user_info():
    return (
        config.settings.AUTH_TYPE in ["supabase", "oauth"]
        or config.settings.BASIC_AUTH_ENABLED
    )


async def setup_headers_for_auth(
    db_asyncpg: asyncpg.Connection,
    auth: AuthInfo,
    use_api_key: bool,
    set_org_id: bool = True,
    read_only: bool = False,
    use_pat: bool = False,
) -> dict[str, str]:
    if use_api_key:
        if use_pat:
            return {"x-api-key": await pat_for_tenant(auth)}
        return {"x-api-key": await api_key_for_tenant(auth, read_only=read_only)}
    elif include_user_info():
        return {
            "Authorization": f"Bearer {await jwt_for_tenant(db_asyncpg, auth, read_only=read_only)}",
            "X-Tenant-Id": str(auth.tenant_id),
            **({"X-Organization-Id": str(auth.organization_id)} if set_org_id else {}),
        }
    elif config.settings.AUTH_TYPE == "none":
        return {}
    else:
        raise NotImplementedError


@asynccontextmanager
async def aclient_for_headers(
    headers: dict[str, str] | None = None,
) -> AsyncGenerator[AsyncClient, None]:
    async with AsyncClient(
        transport=ASGITransport(app),
        base_url="http://test",
        headers=headers,
    ) as ac:
        yield ac


@dataclass
class FreshTenantClient:
    client: AsyncClient
    auth: AuthInfo
    org_auth: OrgAuthInfo
    read_only_client: AsyncClient | None = None


@asynccontextmanager
async def fresh_tenant_client(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    set_org_id: bool = True,
    organization_is_personal: bool = False,
    org_id: UUID | None = None,
    user_id: UUID | None = None,
    set_tenant_handle: bool = True,
    include_read_only: bool = False,
    tenant_config: TenantConfig = SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE
    if config.settings.BASIC_AUTH_ENABLED
    else config.settings.SHARED_TENANT_DEFAULT_CONFIG,
    use_pat: bool = False,
    user_provider: str | None = None,
    user_saml_provider_id: str | None = None,
    org_config: OrganizationConfig = SHARED_ORG_DEFAULT_CONFIG_RBAC_ENABLED,
    org_config_updates: dict | None = None,
) -> AsyncGenerator[FreshTenantClient, None]:
    """
    Creates a brand-new tenant and returns a client to it.

    In basic auth this is in the same org as all other tenants. In all
    other modes this is in a new org.
    """
    if user_id is None:
        email = f"{uuid.uuid4().hex[:10]}@langchain.dev"
        user_id = uuid.uuid5(uuid.NAMESPACE_OID, email)
        user_full_name = "Benedict Cumberbatch"
    else:
        user = await db_asyncpg.fetchrow(
            "select email, full_name from users where id = $1", user_id
        )
        email = user["email"]
        user_full_name = user["full_name"]

    if org_config_updates:
        org_config = org_config.model_copy(update=org_config_updates)

    auth, _ = await ensure_auth_info(
        db_asyncpg,
        tenant_id=uuid.uuid4(),
        tenant_handle=uuid.uuid4().hex if set_tenant_handle else None,
        user_id=user_id if include_user_info() else None,
        user_email=email if include_user_info() else None,
        user_full_name=user_full_name if include_user_info() else None,
        tenant_config=tenant_config,
        organization_is_personal=organization_is_personal,
        org_id=org_id,
        should_ensure_user=True,
        read_only=False,
        user_provider=user_provider,
        user_saml_provider_id=user_saml_provider_id,
        org_config=org_config,
    )
    org_auth = org_auth_from_auth(auth, org_config)
    if config.settings.BASIC_AUTH_ENABLED and not auth.password:
        user = await ensure_basic_auth_user(auth)
        auth.password = user.password

    headers = await setup_headers_for_auth(
        db_asyncpg, auth, use_api_key, set_org_id=set_org_id, use_pat=use_pat
    )
    if use_api_key:
        auth.unverified = UnverifiedAuthInfo(
            x_api_key=headers["x-api-key"],
        )
    async with aclient_for_headers(headers) as ac:
        if not organization_is_personal and include_read_only:
            read_only_user_email = f"{uuid.uuid4().hex[:10]}-<EMAIL>"
            read_only_user_id = uuid.uuid5(uuid.NAMESPACE_OID, read_only_user_email)
            read_only_auth, _ = await ensure_auth_info(
                db_asyncpg,
                tenant_id=auth.tenant_id,
                tenant_handle=auth.tenant_handle,
                tenant_config=auth.tenant_config,
                organization_is_personal=organization_is_personal,
                user_id=read_only_user_id if include_user_info() else None,
                user_email=read_only_user_email if include_user_info() else None,
                should_ensure_user=True,
                read_only=True,
                user_full_name="Benedict Cumberbatch Reader"
                if include_user_info()
                else None,
                org_id=auth.organization_id,
            )
            read_only_headers = await setup_headers_for_auth(
                db_asyncpg,
                read_only_auth,
                use_api_key,
                set_org_id=set_org_id,
                read_only=True,
            )
            if use_api_key:
                auth.unverified = UnverifiedAuthInfo(
                    x_api_key=read_only_headers["x-api-key"],
                )
            async with aclient_for_headers(read_only_headers) as read_only_ac:
                yield FreshTenantClient(
                    client=ac,
                    auth=auth,
                    org_auth=org_auth,
                    read_only_client=read_only_ac,
                )
        else:
            yield FreshTenantClient(client=ac, auth=auth, org_auth=org_auth)


async def clear_redis_config_cache() -> None:
    """
    Clear the org/tenant config cache in Redis.
    NOTE: This is a dangerous/slow operation and should not be used outside of tests.
    """
    async with redis.aredis_pool() as aredis:
        keys = await aredis.keys("*get_org_tenant_config_cached*")
        for key in keys:
            await aredis.delete(key)
        keys = await aredis.keys("*get_org_config_cached*")
        for key in keys:
            await aredis.delete(key)


def org_auth_from_auth(auth: AuthInfo, org_config: OrganizationConfig) -> OrgAuthInfo:
    assert auth.organization_id is not None
    return OrgAuthInfo(
        user_id=auth.user_id,
        user_email=auth.user_email,
        user_full_name=auth.user_full_name,
        organization_id=auth.organization_id,
        org_config=org_config,
        organization_is_personal=auth.organization_is_personal,
        identity_id=auth.identity_id,
        ls_user_id=auth.ls_user_id,
    )


async def create_session(client: AsyncClient, name: str | None = None) -> str:
    session_id = str(uuid4())
    response = await client.post(
        "/sessions",
        json={"name": name or random_lower_string(), "id": session_id},
    )
    assert response.status_code == 200
    return session_id


async def create_dataset(
    client: AsyncClient, name: str | None = None, data_type: str | None = None
) -> str:
    response = await client.post(
        "/datasets",
        json={
            "name": name or random_lower_string(),
            "data_type": data_type or "kv",
        },
    )
    assert response.status_code == 200
    return response.json()["id"]


async def create_chat_preset_dataset(name: str, client: AsyncClient) -> dict:
    resp = await client.post(
        "/datasets",
        json={
            "name": "Chat Transform Dataset " + str(uuid4()),
            "description": "A dataset for testing chat transformations",
            "data_type": "kv",
            "inputs_schema_definition": {
                "type": "object",
                "properties": {
                    "messages": {
                        "type": "array",
                        "items": {
                            "$ref": f"{config.settings.LANGCHAIN_ENDPOINT}/public/schemas/v1/message.json"
                        },
                    },
                    "tools": {
                        "type": "array",
                        "items": {
                            "$ref": f"{config.settings.LANGCHAIN_ENDPOINT}/public/schemas/v1/tooldef.json"
                        },
                    },
                },
                "required": ["messages"],
            },
            "outputs_schema_definition": {
                "type": "object",
                "properties": {
                    "message": {
                        "$ref": f"{config.settings.LANGCHAIN_ENDPOINT}/public/schemas/v1/message.json"
                    },
                },
                "required": ["message"],
            },
            "transformations": [
                {
                    "path": ["inputs"],
                    "transformation_type": "remove_extra_fields",
                },
                {
                    "path": ["inputs", "messages"],
                    "transformation_type": "convert_to_openai_message",
                },
                {
                    "path": ["inputs", "tools"],
                    "transformation_type": "convert_to_openai_tool",
                },
                {
                    "path": ["outputs"],
                    "transformation_type": "remove_extra_fields",
                },
                {
                    "path": ["outputs", "message"],
                    "transformation_type": "convert_to_openai_message",
                },
            ],
        },
    )
    assert resp.status_code == 200, resp.json()
    return resp.json()


def chat_run_1_payload(session_id, chat_run_id):
    return {
        "name": "ChatOpenAI",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {
            "invocation_params": {
                "model": "gpt-4o",
                "model_name": "gpt-4o",
                "stream": False,
                "n": 1,
                "temperature": 0.0,
                "_type": "openai-chat",
                "stop": None,
                "tools": [
                    {
                        "type": "function",
                        "function": {
                            "name": "eval",
                            "description": "Submit your evaluation for this run.",
                            "parameters": {
                                "type": "object",
                                "required": ["about_langchain"],
                                "properties": {
                                    "about_langchain": {
                                        "type": "boolean",
                                        "description": "Is the user input about LangChain? Or is it about other arbitrary information?",
                                    }
                                },
                            },
                        },
                    }
                ],
                "tool_choice": {"type": "function", "function": {"name": "eval"}},
            },
            "options": {"stop": None},
            "batch_size": 1,
            "metadata": {
                "run_id": "*****",
                "rule_id": "*****",
                "source_project_id": "*****",
                "ls_provider": "openai",
                "ls_model_name": "gpt-4o",
                "ls_model_type": "chat",
                "ls_temperature": 0.0,
                "system_fingerprint": "*******",
            },
        },
        "inputs": {
            "messages": [
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "You are assessing whether a user's input is about LangChain. LangChain is a developer framework for building LLM applications. If the inputs seem to be about a developer framework (or LLMs) then it's likely about LangChain. \n\nHere is the data:\n[BEGIN DATA]\n***\n[User Query]: [{'id': '0.09573682871622125', 'type': 'human', 'content': \"I've asked exact question!\\nHow from this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1.gz\\nby using os.path.split()\\nI can make this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1\"}]\n***\n[END DATA]",
                        "type": "human",
                    },
                }
            ]
        },
        "outputs": {
            "llm_output": {
                "model_name": "gpt-4o",
                "system_fingerprint": "*******",
            },
            "run": None,
            "generations": [
                {
                    "text": "",
                    "generation_info": {"finish_reason": "stop", "logprobs": None},
                    "type": "ChatGeneration",
                    "message": {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "AIMessage"],
                        "kwargs": {
                            "content": "",
                            "additional_kwargs": {
                                "tool_calls": [
                                    {
                                        "id": "*******",
                                        "function": {
                                            "arguments": '{"about_langchain":False}',
                                            "name": "eval",
                                        },
                                        "type": "function",
                                    }
                                ]
                            },
                            "response_metadata": {
                                "token_usage": {
                                    "completion_tokens": 7,
                                    "prompt_tokens": 298,
                                    "total_tokens": 305,
                                },
                                "model_name": "gpt-4o",
                                "system_fingerprint": "*******",
                                "finish_reason": "stop",
                                "logprobs": None,
                            },
                            "type": "ai",
                            "id": "run-*******",
                            "tool_calls": [
                                {
                                    "name": "eval",
                                    "args": {"about_langchain": False},
                                    "id": "*******",
                                }
                            ],
                            "usage_metadata": {
                                "input_tokens": 298,
                                "output_tokens": 7,
                                "total_tokens": 305,
                            },
                            "invalid_tool_calls": [],
                        },
                    },
                }
            ],
        },
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "llm",
        "id": str(chat_run_id),
        "trace_id": str(chat_run_id),
        "dotted_order": datetime.utcnow().strftime("%Y%m%dT%H%M%S%fZ")
        + str(chat_run_id),
    }


async def create_test_run(
    auth: AuthInfo,
    run_id: UUID,
) -> UUID:
    """Create a run."""
    start_time = datetime.utcnow()
    payload = dict(
        id=str(run_id),
        name=random_lower_string(),
        start_time=start_time.isoformat(),
        run_type="llm",
        end_time=(start_time + timedelta(seconds=1)).isoformat(),
        extra={},
        error=None,
        serialized={},
        inputs={},
        outputs={},
        parent_run_id=None,
        reference_example_id=None,
        session_name=None,
        session_id=None,
        events=None,
        tags=None,
    )
    await models.runs.ingest.upsert_runs(
        [
            models.runs.ingest.RunInsert(
                payload=payload,
                trace_id=str(run_id),
                dotted_order=f"{start_time.isoformat(timespec='microseconds').replace(':', '').replace('.', '').replace('-', '')}Z{run_id.hex}",
                received_at=datetime.now(timezone.utc).isoformat(),
                modified_at=datetime.now(timezone.utc).isoformat(),
                hash_key="",
                should_insert=True,
                done=False,
            )
        ],
        defaultdict(
            lambda: models.runs.ingest.TokenTracker(
                prompt_tokens=None,
                completion_tokens=None,
                total_tokens=None,
                first_token_time=None,
                prompt_cost=None,
                completion_cost=None,
                total_cost=None,
            )
        ),
        auth,
        asyncio.Semaphore(),
    )

    return run_id


async def create_test_runs(
    auth: AuthInfo,
    run_ids: list[UUID],
) -> list[UUID]:
    """Create multiple runs."""
    start_time = datetime.utcnow()
    payloads = [
        dict(
            id=str(run_id),
            name=random_lower_string(),
            start_time=start_time.isoformat(),
            run_type="llm",
            end_time=(start_time + timedelta(seconds=1)).isoformat(),
            extra={},
            error=None,
            serialized={},
            inputs={},
            outputs={},
            parent_run_id=None,
            reference_example_id=None,
            session_name=None,
            session_id=None,
            events=None,
            tags=None,
        )
        for run_id in run_ids
    ]
    await models.runs.ingest.upsert_runs(
        [
            models.runs.ingest.RunInsert(
                payload=payload,
                trace_id=str(run_id),
                dotted_order=f"{start_time.isoformat(timespec='microseconds').replace(':', '').replace('.', '').replace('-', '')}Z{run_id.hex}",
                received_at=datetime.now(timezone.utc).isoformat(),
                modified_at=datetime.now(timezone.utc).isoformat(),
                hash_key="",
                should_insert=True,
                done=False,
            )
            for run_id, payload in zip(run_ids, payloads)
        ],
        defaultdict(
            lambda: models.runs.ingest.TokenTracker(
                prompt_tokens=None,
                completion_tokens=None,
                total_tokens=None,
                first_token_time=None,
                prompt_cost=None,
                completion_cost=None,
                total_cost=None,
            )
        ),
        auth,
        asyncio.Semaphore(),
    )

    return run_ids


def caller_filename_func() -> str:
    # returns '<caller_filename>:<caller_function>:<caller_lineno>'
    stack = inspect.stack()
    caller_frame = stack[1]
    # chop relative path from app/tests prefix of filename, including app/tests
    caller_filename = f"app/tests/{caller_frame.filename.split('app/tests/')[1]}"
    return f"{caller_filename}::{caller_frame.function}:{caller_frame.lineno}"


async def wait_for_condition(
    condition: Callable[..., Awaitable[bool]],
    timeout: float,
    sleep_interval: float,
    *args,
    **kwargs,
) -> bool:
    start_time = time.time()
    while not await condition(*args, **kwargs):
        if time.time() - start_time > timeout:
            raise TimeoutError(
                f"condition {condition.__name__} with {args=} and {kwargs=} not met after {timeout} seconds"
            )
        await asyncio.sleep(sleep_interval)
    return True


async def runs_exist(auth: AuthInfo, *run_ids: UUID, debug: bool = False) -> bool:
    for run_id in run_ids:
        try:
            await crud.get_run(auth, run_id)
            if debug:
                logger.info(f"run {run_id} exists")
        except fastapi.HTTPException as e:
            if e.status_code == 404:
                if debug:
                    logger.info(f"run {run_id} does not exist")
                return False
            else:
                raise
    return True


async def feedbacks_exist(
    auth: AuthInfo, *feedback_ids: UUID, debug: bool = False
) -> bool:
    for feedback_id in feedback_ids:
        try:
            await fetch_feedback(feedback_id, auth)
            if debug:
                logger.info(f"feedback {feedback_id} exists")
        except fastapi.HTTPException as e:
            if e.status_code == 404:
                if debug:
                    logger.info(f"feedback {feedback_id} does not exist")
                return False
            else:
                raise
    return True


async def runs_do_not_exist(
    auth: AuthInfo, *run_ids: UUID, debug: bool = False
) -> bool:
    for run_id in run_ids:
        try:
            run = await crud.get_run(auth, run_id)
            if debug:
                logger.info(f"run {run.name} {run_id} exists")
            return False
        except fastapi.HTTPException as e:
            if e.status_code == 404:
                if debug:
                    logger.info(f"run {run_id} does not exist")
                return True
            else:
                raise
    return True


async def runs_are_ended(auth: AuthInfo, *run_ids: UUID, debug: bool = False) -> bool:
    for run_id in run_ids:
        try:
            run = await crud.get_run(auth, run_id)
            if debug:
                logger.info(f"run {run.name} {run_id} exists and {run.end_time=}")
            if run.end_time is None:
                return False
        except fastapi.HTTPException as e:
            if e.status_code == 404:
                if debug:
                    logger.info(f"run {run_id} does not exist")
                return False
            else:
                raise
    return True


async def clickhouse_query_returns_rows(
    ch_client: aiochclient.ChClient,
    query: str,
) -> bool:
    result = await ch_client.fetch(query)
    return len(result) > 0


async def wait_for_runs_to_exist(
    auth: AuthInfo,
    *run_ids: UUID,
    timeout: float = 10,
    sleep_interval: float = 0.05,
    debug: bool = False,
) -> bool:
    return await wait_for_condition(
        runs_exist, timeout, sleep_interval, auth, *run_ids, debug=debug
    )


async def wait_for_runs_to_not_exist(
    auth: AuthInfo,
    *run_ids: UUID,
    timeout: float = 10,
    sleep_interval: float = 0.05,
    debug: bool = False,
) -> bool:
    return await wait_for_condition(
        runs_do_not_exist, timeout, sleep_interval, auth, *run_ids, debug=debug
    )


async def wait_for_runs_to_end(
    auth: AuthInfo,
    *run_ids: UUID,
    timeout: float = 10,
    sleep_interval: float = 0.05,
    debug: bool = False,
) -> bool:
    return await wait_for_condition(
        runs_are_ended,
        timeout,
        sleep_interval,
        auth,
        *run_ids,
        debug=debug,
    )


def build_multipart_body(
    files: list[tuple[str, tuple[Optional[str], bytes, str, Optional[Dict[str, str]]]]],
) -> tuple[bytes, str]:
    """
    Build multipart/form-data body manually, returning (body_bytes, boundary).
    Each element in `files` is expected to be:
        (field_name, (filename|None, content_bytes, content_type, optional_extra_headers))
    """
    boundary = f"----multipart-go-{uuid.uuid4()}"
    lines = []

    for field_name, (filename, content, content_type, extra_headers) in files:
        lines.append(f"--{boundary}")

        # Build Content-Disposition
        disposition = f'form-data; name="{field_name}"'
        if filename is not None:
            disposition += f'; filename="{filename}"'
        lines.append(f"Content-Disposition: {disposition}")

        # Content-Type
        lines.append(f"Content-Type: {content_type}")

        # Any extra headers, e.g. Content-Encoding or custom length.
        if extra_headers:
            for k, v in extra_headers.items():
                lines.append(f"{k}: {v}")

        # End of headers, add a blank line
        lines.append("")
        # Add the actual file content (need to decode to ISO-8859-1 if it's binary).
        lines.append(content)  # type: ignore[arg-type]

    # Close the multipart
    lines.append(f"--{boundary}--")
    lines.append("")  # final newline

    body_bytes = b""
    for line in lines:
        if isinstance(line, str):
            body_bytes += line.encode("utf-8") + b"\r\n"
        else:
            body_bytes += line + b"\r\n"

    return body_bytes, boundary


async def wait_for_feedbacks_to_exist(
    auth: AuthInfo,
    *feedback_ids: UUID,
    timeout: float = 10,
    sleep_interval: float = 0.05,
    debug: bool = False,
) -> bool:
    return await wait_for_condition(
        feedbacks_exist, timeout, sleep_interval, auth, *feedback_ids, debug=debug
    )


async def wait_for_clickhouse_to_have_rows(
    ch_client: aiochclient.ChClient,
    query: str,
) -> None:
    await wait_for_condition(clickhouse_query_returns_rows, 10, 0.05, ch_client, query)


def dummy_post_run_payload(
    run_id: UUID,
    start_time: datetime,
    end_time: datetime,
) -> dict[str, Any]:
    dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
    return {
        "name": "AgentExecutor",
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "extra": {"foo": "bar"},
        "error": None,
        "dotted_order": dotted_order,
        "id": str(run_id),
        "trace_id": str(run_id),
        "run_type": "chain",
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"input": "foo"},
        "outputs": None,
    }


async def post_runs(
    endpoint: str,
    http_client: AsyncClient,
    post: Sequence[dict[str, Any]] = (),
    patch_runs: Sequence[dict[str, Any]] = (),
    attachments: Optional[dict[tuple[str, str], tuple[str, bytes]]] = None,
    feedback: Sequence[dict[str, Any]] = (),
    status_code: int = 202,
) -> None:
    if endpoint.startswith("/runs/batch"):
        if attachments:
            pytest.skip("Attachments not supported for batch endpoint")
        if "|s3" in endpoint:
            with (
                patch("app.config.settings.MIN_BLOB_STORAGE_SIZE_KB", new=0),
                patch("app.config.settings.SPOOL_MIN_SIZE_KB", new=0),
            ):
                response = await http_client.post(
                    "/runs/batch",
                    json={"post": post, "patch": patch_runs},
                )
                assert response.status_code == status_code, response.text
        elif endpoint.startswith("/runs/batch|go"):
            forward_headers = dict(http_client.headers or {})
            body = orjson.dumps({"post": post, "patch": patch_runs})

            response = await platform_request(
                "POST",
                "/runs/batch",
                headers=forward_headers,
                body=body,
                raise_error=False,
            )
            assert response.code == status_code, response.body
        else:
            response = await http_client.post(
                "/runs/batch",
                json={"post": post, "patch": patch_runs},
            )
            assert response.status_code == status_code, response.text
    elif endpoint.startswith("/runs/multipart") and "|go" not in endpoint:
        if not config.settings.FF_BLOB_STORAGE_ENABLED:
            pytest.skip("S3 storage not enabled")
        files: list[tuple[str, FileTypes]] = []
        for event, run in [
            *(("post", run) for run in post),
            *(("patch", run) for run in patch_runs),
        ]:
            fields = [
                ("inputs", run.pop("inputs", None)),
                ("outputs", run.pop("outputs", None)),
                ("events", run.pop("events", None)),
            ]
            runb = orjson.dumps(run)
            headers = {"Content-Length": str(len(runb))}
            if "|gzip" in endpoint:
                # Use zlib.MAX_WBITS | 16 for gzip encoding
                runb = zlib.compress(runb, wbits=zlib.MAX_WBITS | 16)
                headers["Content-Encoding"] = "gzip"
                headers["Content-Length"] = str(len(runb))
            files.append(
                (
                    f"{event}.{run['id']}",
                    (None, runb, "application/json", headers),
                ),
            )
            for field, value in fields:
                if value is None:
                    continue
                if isinstance(value, bytes):
                    valueb = value
                else:
                    valueb = orjson.dumps(value)
                if "|gzip" in endpoint:
                    valueb = zlib.compress(valueb, wbits=zlib.MAX_WBITS | 16)
                    files.append(
                        (
                            f"{event}.{run['id']}.{field}",
                            (
                                None,
                                valueb,
                                f"application/json; length={len(valueb)}; encoding=gzip",
                            ),
                        ),
                    )
                else:
                    files.append(
                        (
                            f"{event}.{run['id']}.{field}",
                            (None, valueb, f"application/json; length={len(valueb)}"),
                        ),
                    )
        if attachments:
            for (run_id, key), (ctype, data) in attachments.items():
                if "|gzip" in endpoint:
                    data = zlib.compress(data, wbits=zlib.MAX_WBITS | 16)
                    ctype = f"{ctype}; encoding=gzip"
                files.append(
                    (
                        f"attachment.{run_id}.{key}",
                        (None, data, f"{ctype}; length={len(data)}"),
                    )
                )
        if feedback:
            for f in feedback:
                feedbackb = orjson.dumps(f)
                if "|gzip" in endpoint:
                    feedbackb = zlib.compress(feedbackb, wbits=zlib.MAX_WBITS | 16)
                    files.append(
                        (
                            f"feedback.{f['id']}",
                            (
                                None,
                                feedbackb,
                                f"application/json; length={len(feedbackb)}; encoding=gzip",
                            ),
                        ),
                    )
                else:
                    files.append(
                        (
                            f"feedback.{f['id']}",
                            (
                                None,
                                feedbackb,
                                f"application/json; length={len(feedbackb)}",
                            ),
                        ),
                    )
        if "|s3" in endpoint:
            with (
                patch("app.config.settings.MIN_BLOB_STORAGE_SIZE_KB", new=0),
                patch("app.config.settings.SPOOL_MIN_SIZE_KB", new=0),
            ):
                response = await http_client.post("/runs/multipart", files=files)
        else:
            response = await http_client.post("/runs/multipart", files=files)
        assert response.status_code == status_code, response.text
    elif endpoint.startswith("/runs/multipart|go"):
        # -- /runs/multipart|go logic --
        if not config.settings.FF_BLOB_STORAGE_ENABLED:
            pytest.skip("S3 storage not enabled")

        # Build the same 'files' list but store per-file headers for multipart
        final_files: list[
            tuple[str, tuple[Optional[str], bytes, str, Optional[dict[str, str]]]]
        ] = []

        # Populate final_files from post and patch_runs
        for event, run in [
            *(("post", run) for run in post),
            *(("patch", run) for run in patch_runs),
        ]:
            fields = [
                ("inputs", run.pop("inputs", None)),
                ("outputs", run.pop("outputs", None)),
                ("events", run.pop("events", None)),
            ]
            runb = orjson.dumps(run)
            headers_ = {"Content-Length": str(len(runb))}
            if "|gzip" in endpoint:
                runb = zlib.compress(runb, wbits=zlib.MAX_WBITS | 16)
                headers_["Content-Encoding"] = "gzip"
                headers_["Content-Length"] = str(len(runb))
            final_files.append(
                (
                    f"{event}.{run['id']}",
                    (None, runb, "application/json", headers_),
                )
            )
            for field, value in fields:
                if value is None:
                    continue
                if isinstance(value, bytes):
                    valueb = value
                else:
                    valueb = orjson.dumps(value)
                headers_ = {"Content-Length": str(len(valueb))}
                if "|gzip" in endpoint:
                    valueb = zlib.compress(valueb, wbits=zlib.MAX_WBITS | 16)
                    headers_["Content-Encoding"] = "gzip"
                    headers_["Content-Length"] = str(len(valueb))
                final_files.append(
                    (
                        f"{event}.{run['id']}.{field}",
                        (None, valueb, "application/json", headers_),
                    )
                )
        if attachments:
            for (run_id, key), (ctype, data) in attachments.items():
                headers_ = {"Content-Length": str(len(data))}
                if "|gzip" in endpoint:
                    data = zlib.compress(data, wbits=zlib.MAX_WBITS | 16)
                    headers_["Content-Encoding"] = "gzip"
                    headers_["Content-Length"] = str(len(data))
                final_files.append(
                    (
                        f"attachment.{run_id}.{key}",
                        (None, data, ctype, headers_),
                    )
                )
        if feedback:
            for fb in feedback:
                feedbackb = orjson.dumps(fb)
                headers_ = {"Content-Length": str(len(feedbackb))}
                if "|gzip" in endpoint:
                    feedbackb = zlib.compress(feedbackb, wbits=zlib.MAX_WBITS | 16)
                    headers_["Content-Encoding"] = "gzip"
                    headers_["Content-Length"] = str(len(feedbackb))
                final_files.append(
                    (
                        f"feedback.{fb['id']}",
                        (None, feedbackb, "application/json", headers_),
                    )
                )

        body, boundary = build_multipart_body(final_files)

        # Forward any existing http_client headers (e.g. Authorization)
        forward_headers = dict(http_client.headers or {})
        forward_headers["Content-Type"] = f"multipart/form-data; boundary={boundary}"

        response = await platform_request(
            "POST",
            "/runs/multipart",
            headers=forward_headers,
            body=body,
            raise_error=False,
        )

        # Perform the same status code check
        assert response.code == status_code, response.body
    else:
        raise NotImplementedError
