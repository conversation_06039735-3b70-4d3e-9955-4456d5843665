"""Test model_price_map CRUD endpoints."""

from typing import Any
from uuid import uuid4

import asyncpg
import pytest

from app.tests.utils import fresh_tenant_client

pytestmark = pytest.mark.anyio


@pytest.fixture
async def sample_price_map_data() -> dict[str, Any]:
    """Sample model price map data for testing."""
    return {
        "name": "OpenAI GPT-4",
        "match_pattern": "gpt-4*",
        "prompt_cost": "0.03",
        "completion_cost": 0.06,
        "provider": "openai",
    }


@pytest.fixture
async def sample_price_map_with_details() -> dict[str, Any]:
    """Sample model price map data with cost details."""
    return {
        "name": "OpenAI GPT-4 with Details",
        "match_pattern": "gpt-4-detailed*",
        "prompt_cost": 0.03,
        "completion_cost": "0.06",
        "prompt_cost_details": {
            "input_tokens": "0.03",
            "cached_tokens": 0.015,
        },
        "completion_cost_details": {
            "output_tokens": "0.06",
            "reasoning_tokens": 0.12,
        },
        "provider": "openai",
    }


def sanitize(val):
    if isinstance(val, dict):
        return {k: sanitize(v) for k, v in val.items()}
    return float(val)


async def test_create_basic_price_map(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test creating a basic model price map with required fields only."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        response = await client.post("/model-price-map/", json=sample_price_map_data)

        assert response.status_code == 200
        result = response.json()

        assert result["name"] == sample_price_map_data["name"]
        assert result["match_pattern"] == sample_price_map_data["match_pattern"]
        assert result["prompt_cost"] == sanitize(sample_price_map_data["prompt_cost"])
        assert result["completion_cost"] == sanitize(
            sample_price_map_data["completion_cost"]
        )
        assert result["provider"] == sample_price_map_data["provider"]
        assert result["prompt_cost_details"] is None
        assert result["completion_cost_details"] is None
        assert "id" in result
        assert result["tenant_id"] == str(authed_client.auth.tenant_id)


async def test_create_price_map_with_cost_details(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_with_details: dict[str, Any],
) -> None:
    """Test creating a model price map with cost details."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        response = await client.post(
            "/model-price-map/", json=sample_price_map_with_details
        )

        assert response.status_code == 200
        result = response.json()
        assert result["name"] == sample_price_map_with_details["name"]
        assert result["prompt_cost_details"] == sanitize(
            sample_price_map_with_details["prompt_cost_details"]
        )
        assert result["completion_cost_details"] == sanitize(
            sample_price_map_with_details["completion_cost_details"]
        )


async def test_create_price_map_empty_cost_details(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test creating a model price map with explicitly null cost details."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        data = {
            **sample_price_map_data,
            "prompt_cost_details": None,
            "completion_cost_details": None,
        }
        response = await client.post("/model-price-map/", json=data)

        assert response.status_code == 200
        result = response.json()
        assert result["prompt_cost_details"] is None
        assert result["completion_cost_details"] is None


async def test_create_price_map_missing_required_fields(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test creating a model price map with missing required fields."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Missing required fields
        incomplete_data = {"name": "Test Model"}
        response = await client.post("/model-price-map/", json=incomplete_data)

        assert response.status_code == 422


async def test_create_price_map_invalid_data_types(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test creating a model price map with invalid data types."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Invalid prompt_cost type
        invalid_data = {
            **sample_price_map_data,
            "prompt_cost": "not-a-number",
        }
        response = await client.post("/model-price-map/", json=invalid_data)

        assert response.status_code == 422


async def test_create_duplicate_price_map(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test creating duplicate model price maps with same match conditions."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create first price map
        response1 = await client.post("/model-price-map/", json=sample_price_map_data)
        assert response1.status_code == 200

        # Try to create duplicate
        response2 = await client.post("/model-price-map/", json=sample_price_map_data)
        assert response2.status_code == 409


async def test_create_price_map_priority_order(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test that priority_order is auto-assigned correctly."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create first price map
        data1 = {**sample_price_map_data, "match_pattern": "gpt-4-first*"}
        response1 = await client.post("/model-price-map/", json=data1)
        assert response1.status_code == 200

        # Create second price map
        data2 = {**sample_price_map_data, "match_pattern": "gpt-4-second*"}
        response2 = await client.post("/model-price-map/", json=data2)
        assert response2.status_code == 200

        # Second should have higher priority order
        row1 = await db_asyncpg.fetchrow(
            """
            SELECT * FROM model_price_map
            WHERE match_pattern LIKE 'gpt-4-first*'
            """,
        )

        row2 = await db_asyncpg.fetchrow(
            """
            SELECT * FROM model_price_map
            WHERE match_pattern LIKE 'gpt-4-second*'
            """,
        )
        assert row1["priority_order"] < row2["priority_order"]


async def test_fetch_empty_list(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test fetching empty list when no entries exist."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        response = await client.get("/model-price-map/")

        assert response.status_code == 200
        result = response.json()
        # Should return at least global entries, but test tenant entries specifically
        tenant_entries = [
            entry
            for entry in result
            if entry.get("tenant_id") == str(authed_client.auth.tenant_id)
        ]
        assert len(tenant_entries) == 0


async def test_fetch_single_entry_with_new_fields(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_with_details: dict[str, Any],
) -> None:
    """Test fetching a single entry with new cost detail fields."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create entry
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_with_details
        )
        assert create_response.status_code == 200
        created_entry = create_response.json()

        # Fetch entries
        response = await client.get("/model-price-map/")
        assert response.status_code == 200
        result = response.json()

        # Find our created entry
        our_entry = next(
            (entry for entry in result if entry["id"] == created_entry["id"]), None
        )
        assert our_entry is not None
        assert our_entry["prompt_cost_details"] == sanitize(
            sample_price_map_with_details["prompt_cost_details"]
        )
        assert our_entry["completion_cost_details"] == sanitize(
            sample_price_map_with_details["completion_cost_details"]
        )


async def test_tenant_isolation(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test that tenants can't see each other's entries."""
    # Create entry in first tenant
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client1:
        response1 = await client1.client.post(
            "/model-price-map/", json=sample_price_map_data
        )
        assert response1.status_code == 200
        entry1_id = response1.json()["id"]

    # Check from second tenant
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client2:
        response2 = await client2.client.get("/model-price-map/")
        assert response2.status_code == 200
        result = response2.json()

        # Should not see first tenant's entry
        entry_ids = [entry["id"] for entry in result]
        assert entry1_id not in entry_ids


async def test_update_with_cost_details(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test updating existing entry with new cost_details."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create entry
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_data
        )
        assert create_response.status_code == 200
        entry_id = create_response.json()["id"]

        # Update with cost details
        update_data = {
            **sample_price_map_data,
            "prompt_cost_details": {"input_tokens": "0.025"},
            "completion_cost_details": {"output_tokens": "0.05"},
        }
        response = await client.put(f"/model-price-map/{entry_id}", json=update_data)

        assert response.status_code == 200
        result = response.json()
        assert result["prompt_cost_details"] == sanitize(
            update_data["prompt_cost_details"]
        )
        assert result["completion_cost_details"] == sanitize(
            update_data["completion_cost_details"]
        )


async def test_update_cost_details_null_to_populated(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test updating cost_details from null to populated."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create entry with null cost details
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_data
        )
        assert create_response.status_code == 200
        entry_id = create_response.json()["id"]

        # Update to populated cost details
        update_data = {
            **sample_price_map_data,
            "prompt_cost_details": {"tokens": "0.03"},
            "completion_cost_details": {"tokens": "0.06"},
        }
        response = await client.put(f"/model-price-map/{entry_id}", json=update_data)

        assert response.status_code == 200
        result = response.json()
        assert result["prompt_cost_details"] == sanitize(
            update_data["prompt_cost_details"]
        )
        assert result["completion_cost_details"] == sanitize(
            update_data["completion_cost_details"]
        )


async def test_update_cost_details_populated_to_null(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_with_details: dict[str, Any],
) -> None:
    """Test updating cost_details from populated to null."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create entry with cost details
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_with_details
        )
        assert create_response.status_code == 200
        entry_id = create_response.json()["id"]

        # Update to null cost details
        update_data = {
            **sample_price_map_with_details,
            "prompt_cost_details": None,
            "completion_cost_details": None,
        }
        response = await client.put(f"/model-price-map/{entry_id}", json=update_data)

        assert response.status_code == 200
        result = response.json()
        assert result["prompt_cost_details"] is None
        assert result["completion_cost_details"] is None


async def test_update_all_fields(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test updating all fields including legacy ones."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create entry
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_data
        )
        assert create_response.status_code == 200
        entry_id = create_response.json()["id"]

        # Update all fields
        update_data = {
            "name": "Updated Model Name",
            "match_pattern": "updated-pattern*",
            "prompt_cost": "0.05",
            "completion_cost": "0.10",
            "prompt_cost_details": {"updated": "0.05"},
            "completion_cost_details": {"updated": "0.10"},
            "provider": "updated-provider",
        }
        response = await client.put(f"/model-price-map/{entry_id}", json=update_data)

        assert response.status_code == 200
        result = response.json()
        assert result["name"] == update_data["name"]
        assert result["match_pattern"] == update_data["match_pattern"]
        assert result["prompt_cost"] == sanitize(update_data["prompt_cost"])
        assert result["completion_cost"] == sanitize(update_data["completion_cost"])
        assert result["prompt_cost_details"] == sanitize(
            update_data["prompt_cost_details"]
        )
        assert result["completion_cost_details"] == sanitize(
            update_data["completion_cost_details"]
        )
        assert result["provider"] == update_data["provider"]


async def test_update_non_existent_entry(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test updating non-existent entry."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        fake_id = str(uuid4())
        response = await client.put(
            f"/model-price-map/{fake_id}", json=sample_price_map_data
        )

        assert response.status_code == 404 or response.status_code == 500


async def test_update_entry_different_tenant(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test updating entry from different tenant."""
    # Create entry in first tenant
    entry_id = None
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client1:
        response1 = await client1.client.post(
            "/model-price-map/", json=sample_price_map_data
        )
        assert response1.status_code == 200
        entry_id = response1.json()["id"]

    # Try to update from second tenant
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client2:
        response2 = await client2.client.put(
            f"/model-price-map/{entry_id}", json=sample_price_map_data
        )
        # Should fail as entry doesn't exist for this tenant
        assert response2.status_code == 404 or response2.status_code == 500


async def test_update_invalid_data(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test updating with invalid data."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create entry
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_data
        )
        assert create_response.status_code == 200
        entry_id = create_response.json()["id"]

        # Update with invalid data
        invalid_data = {
            **sample_price_map_data,
            "prompt_cost": "invalid-number",
        }
        response = await client.put(f"/model-price-map/{entry_id}", json=invalid_data)

        assert response.status_code == 422


async def test_delete_existing_entry(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_data: dict[str, Any],
) -> None:
    """Test deleting existing entry successfully."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Create entry
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_data
        )
        assert create_response.status_code == 200
        entry_id = create_response.json()["id"]

        # Delete entry
        response = await client.delete(f"/model-price-map/{entry_id}")
        assert response.status_code == 200
        assert "message" in response.json()

        # Verify entry is removed
        get_response = await client.get("/model-price-map/")
        assert get_response.status_code == 200
        result = get_response.json()
        entry_ids = [entry["id"] for entry in result]
        assert entry_id not in entry_ids


async def test_delete_non_existent_entry(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test deleting non-existent entry."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        fake_id = str(uuid4())
        response = await client.delete(f"/model-price-map/{fake_id}")

        # Should succeed even if entry doesn't exist (idempotent)
        assert response.status_code == 200


async def test_full_crud_lifecycle_with_new_fields(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    sample_price_map_with_details: dict[str, Any],
) -> None:
    """Test full CRUD lifecycle with new cost detail fields."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # CREATE
        create_response = await client.post(
            "/model-price-map/", json=sample_price_map_with_details
        )
        assert create_response.status_code == 200
        created_entry = create_response.json()
        entry_id = created_entry["id"]

        # READ
        get_response = await client.get("/model-price-map/")
        assert get_response.status_code == 200
        entries = get_response.json()
        our_entry = next((e for e in entries if e["id"] == entry_id), None)
        assert our_entry is not None
        assert our_entry["prompt_cost_details"] == sanitize(
            sample_price_map_with_details["prompt_cost_details"]
        )

        # UPDATE
        updated_details = {"updated_tokens": "0.04"}
        update_data = {
            **sample_price_map_with_details,
            "prompt_cost_details": updated_details,
        }
        update_response = await client.put(
            f"/model-price-map/{entry_id}", json=update_data
        )
        assert update_response.status_code == 200
        updated_entry = update_response.json()
        assert updated_entry["prompt_cost_details"] == sanitize(updated_details)

        # DELETE
        delete_response = await client.delete(f"/model-price-map/{entry_id}")
        assert delete_response.status_code == 200

        # Verify deletion
        final_get_response = await client.get("/model-price-map/")
        assert final_get_response.status_code == 200
        final_entries = final_get_response.json()
        final_entry_ids = [e["id"] for e in final_entries]
        assert entry_id not in final_entry_ids
