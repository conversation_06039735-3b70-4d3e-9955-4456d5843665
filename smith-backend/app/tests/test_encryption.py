"""Unit tests for encryption utilities."""

import pytest

from app.models.utils.encryption import (
    _decrypt_b64,
    _encrypt_b64,
    decrypt_dict_values,
    decrypt_string,
    encrypt_dict_values,
    encrypt_string,
)


def test_encrypt_decrypt_b64():
    """Test basic encryption and decryption of strings."""
    test_string = "test_string"
    encrypted = _encrypt_b64(test_string)
    decrypted = _decrypt_b64(encrypted)
    assert decrypted == test_string
    assert encrypted != test_string  # Ensure it's actually encrypted


def test_encrypt_decrypt_b64_with_special_chars():
    """Test encryption and decryption with special characters."""
    test_string = "test!@#$%^&*()_+string"
    encrypted = _encrypt_b64(test_string)
    decrypted = _decrypt_b64(encrypted)
    assert decrypted == test_string


def test_encrypt_decrypt_b64_with_unicode():
    """Test encryption and decryption with unicode characters."""
    test_string = "test_unicode_测试_文字"
    encrypted = _encrypt_b64(test_string)
    decrypted = _decrypt_b64(encrypted)
    assert decrypted == test_string


@pytest.mark.asyncio
async def test_encrypt_decrypt_dict_values():
    """Test encryption and decryption of dictionary values."""
    test_dict = {
        "key1": "value1",
        "key2": "value2",
        "key3": 123,  # Non-string value
    }
    encrypted = await encrypt_dict_values(test_dict)
    decrypted = await decrypt_dict_values(encrypted)

    assert decrypted["key1"] == "value1"
    assert decrypted["key2"] == "value2"
    assert decrypted["key3"] == "123"  # Non-string values are converted to strings
    assert encrypted["key1"] != "value1"  # Ensure values are encrypted


@pytest.mark.asyncio
async def test_encrypt_decrypt_dict_values_with_none():
    """Test encryption and decryption with None dictionary."""
    assert await encrypt_dict_values(None) is None
    assert await decrypt_dict_values(None) is None


@pytest.mark.asyncio
async def test_encrypt_decrypt_dict_values_with_empty():
    """Test encryption and decryption with empty dictionary."""
    test_dict = {}
    encrypted = await encrypt_dict_values(test_dict)
    decrypted = await decrypt_dict_values(encrypted)
    assert decrypted == {}


@pytest.mark.asyncio
async def test_encrypt_decrypt_string():
    """Test encryption and decryption of string values."""
    test_string = "test_string"
    encrypted = await encrypt_string(test_string)
    decrypted = await decrypt_string(encrypted)
    assert decrypted == test_string
    assert encrypted != test_string  # Ensure it's actually encrypted


@pytest.mark.asyncio
async def test_encrypt_decrypt_string_with_none():
    """Test encryption and decryption with None string."""
    assert await encrypt_string(None) is None
    assert await decrypt_string(None) is None


@pytest.mark.asyncio
async def test_encrypt_decrypt_string_with_empty():
    """Test encryption and decryption with empty string."""
    test_string = ""
    encrypted = await encrypt_string(test_string)
    decrypted = await decrypt_string(encrypted)
    assert decrypted == ""


@pytest.mark.asyncio
async def test_encrypt_decrypt_string_with_special_chars():
    """Test encryption and decryption with special characters."""
    test_string = "test!@#$%^&*()_+string"
    encrypted = await encrypt_string(test_string)
    decrypted = await decrypt_string(encrypted)
    assert decrypted == test_string


@pytest.mark.asyncio
async def test_encrypt_decrypt_string_with_unicode():
    """Test encryption and decryption with unicode characters."""
    test_string = "test_unicode_测试_文字"
    encrypted = await encrypt_string(test_string)
    decrypted = await decrypt_string(encrypted)
    assert decrypted == test_string


@pytest.mark.asyncio
async def test_encrypt_decrypt_dict_values_with_complex_values():
    """Test encryption and decryption with complex dictionary values."""
    test_dict = {
        "key1": "value1",
        "key2": "value2",
        "key3": 123,
        "key4": True,
        "key5": None,
        "key6": "test!@#$%^&*()_+string",
        "key7": "test_unicode_测试_文字",
    }
    encrypted = await encrypt_dict_values(test_dict)
    decrypted = await decrypt_dict_values(encrypted)

    assert decrypted["key1"] == "value1"
    assert decrypted["key2"] == "value2"
    assert decrypted["key3"] == "123"
    assert decrypted["key4"] == "True"
    assert decrypted["key5"] == "None"
    assert decrypted["key6"] == "test!@#$%^&*()_+string"
    assert decrypted["key7"] == "test_unicode_测试_文字"

    # Ensure all values are encrypted
    for value in encrypted.values():
        assert value != "value1"  # None of the values should be in plain text
