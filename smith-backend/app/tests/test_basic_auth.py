import uuid
from dataclasses import dataclass
from typing import AsyncGenerator, cast

import pytest
from asyncpg import Connection
from fastapi import HTT<PERSON><PERSON>xception
from httpx import AsyncClient
from lc_config import constants
from lc_database.database import asyncpg_conn

from app import config, schemas
from app.api.auth.basic_auth import basic_auth_header, hashed_passwords_match
from app.api.auth.schemas import (
    AuthInfo,
    OrganizationRoles,
    OrgAuthInfo,
    TenantlessAuthInfo,
)
from app.api.endpoints.orgs import create_organization
from app.crud import create_tenant
from app.models.api_keys import crud as api_keys_crud
from app.models.identities import crud as identities_crud
from app.models.identities import seat_txn
from app.models.identities.users import get_provider_users, get_user
from app.models.organizations.roles import list_roles
from app.models.tenants.list import list_tenants
from app.models.workspaces.crud import create_workspace
from app.tests.models.billing.test_transactions import _list_seat_events
from app.tests.models.identities.test_crud import (
    _NUM_SEAT_TYPES,
    _assert_reporting_status,
    _assert_valid_transaction_chain_for_org,
)
from app.tests.utils import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
)
from hooks import auth_bootstrap

pytestmark = [
    pytest.mark.skipif(
        not config.settings.BASIC_AUTH_ENABLED, reason="basic auth specific tests"
    ),
    pytest.mark.serial,
]


ADMIN_USER_EMAIL = "<EMAIL>"
ADMIN_USER_PASSWORD = "LangSmithTest123!"


async def clear_db():
    async with asyncpg_conn() as conn:
        await conn.execute("DELETE FROM organizations")
        await conn.execute("DELETE FROM users")
        await conn.execute("DELETE FROM seat_change_billing_events")


@pytest.fixture(autouse=True)
async def test_wrapper():
    await clear_db()

    config.settings.INITIAL_ORG_ADMIN_EMAIL = ADMIN_USER_EMAIL
    config.settings.INITIAL_ORG_ADMIN_PASSWORD = ADMIN_USER_PASSWORD
    await auth_bootstrap.main()

    yield

    # avoid affecting other test files after final test run
    # e.g. if there are conflicting user email addresses
    # await clear_db()


@dataclass
class AuthedUser:
    ws_auth: AuthInfo
    org_auth: OrgAuthInfo
    user: schemas.UserWithPassword


@pytest.fixture()
async def admin_user() -> AsyncGenerator[AuthedUser, None]:
    async with asyncpg_conn() as conn:
        organization_id = await conn.fetchval("SELECT id FROM organizations LIMIT 1")
        assert organization_id is not None
        workspace_id = await conn.fetchval("SELECT id FROM tenants LIMIT 1")
        assert workspace_id is not None
        user_record = await conn.fetchrow(
            "SELECT * FROM users WHERE email = $1", ADMIN_USER_EMAIL
        )
        assert user_record is not None

    ws_auth = AuthInfo(
        tenant_id=workspace_id,
        organization_id=organization_id,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES.model_copy(
            deep=True
        ),
        user_id=user_record["id"],
        ls_user_id=user_record["ls_user_id"],
        user_email=user_record["email"],
    )
    org_auth = OrgAuthInfo(
        organization_id=organization_id,
        org_config=ws_auth.tenant_config.organization_config,
        user_id=ws_auth.user_id,
        ls_user_id=ws_auth.ls_user_id,
        user_email=ws_auth.user_email,
        user_full_name=ws_auth.user_full_name,
    )
    user_record = dict(user_record)
    user_record["password"] = ADMIN_USER_PASSWORD
    user = schemas.UserWithPassword(**user_record)

    yield AuthedUser(ws_auth=ws_auth, org_auth=org_auth, user=user)


@pytest.fixture()
async def fresh_workspace_user(
    admin_user: AuthedUser,
) -> AsyncGenerator[AuthedUser, None]:
    """Create a random workspace with a basic auth user as an Org User and Workspace Admin."""
    user_id = uuid.uuid4()
    user_email = f"basicauthuser+{user_id}@langchain.dev"
    workspace_id = uuid.uuid4()
    workspace = await create_workspace(
        admin_user.org_auth,
        schemas.WorkspaceCreate(
            id=workspace_id,
            display_name="Test Fresh",
        ),
    )
    ws_auth = AuthInfo(
        tenant_id=workspace_id,
        organization_id=admin_user.org_auth.organization_id,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES.model_copy(
            deep=True
        ),
        user_id=user_id,
        user_email=user_email,
    )
    org_auth = OrgAuthInfo(
        organization_id=workspace.organization_id,
        org_config=ws_auth.tenant_config.organization_config,
        user_id=ws_auth.user_id,
        user_email=ws_auth.user_email,
        user_full_name=ws_auth.user_full_name,
    )
    roles = await list_roles(admin_user.org_auth)
    org_user_role_id = next(
        x.id for x in roles if x.name == OrganizationRoles.USER.value
    )
    workspace_admin_role_id = next(x.id for x in roles if x.name == "WORKSPACE_ADMIN")
    user = await identities_crud.add_basic_auth_user(
        admin_user.org_auth,
        schemas.BasicAuthMemberCreate(
            email=user_email,
            user_id=user_id,
            role_id=org_user_role_id,
            workspace_ids=[workspace_id],
            workspace_role_id=workspace_admin_role_id,
        ),
    )
    ws_auth.ls_user_id = user.ls_user_id
    org_auth.ls_user_id = user.ls_user_id
    yield AuthedUser(ws_auth=ws_auth, org_auth=org_auth, user=user)


async def _login_and_verify_org_access(
    db: Connection,
    client: AsyncClient,
    email: str,
    password: str,
    organization_id: uuid.UUID,
) -> str:
    response = await client.post(
        "/login",
        headers={"Authorization": basic_auth_header(email, password)},
    )
    assert response.status_code == 200
    assert "access_token" in response.json()
    token = response.json()["access_token"]

    headers = {
        "Authorization": f"Bearer {token}",
        "X-Organization-Id": str(organization_id),
    }

    response = await client.get("/orgs/current/info", headers=headers)
    assert response.status_code == 200

    user = await get_user(email)
    assert user.ls_user_id is not None
    provider_users = await get_provider_users(user.ls_user_id)
    assert len(provider_users) == 1
    provider_user = provider_users[0]
    assert provider_user.provider == schemas.AuthProvider.email
    assert provider_user.ls_user_id == user.ls_user_id
    assert provider_user.saml_provider_id is None
    assert provider_user.provider_user_id is None
    assert provider_user.email == email
    assert provider_user.hashed_password is not None
    assert hashed_passwords_match(provider_user.hashed_password, password)

    return token


async def test_auth_bootstrap(
    db_asyncpg: Connection,
    http_no_auth: AsyncClient,
) -> None:
    """Test that expected org, tenant, user, and identity are created during auth bootstrap."""
    orgs = await db_asyncpg.fetch("SELECT * FROM organizations")
    assert len(orgs) == 1
    org_id = orgs[0]["id"]
    workspaces = await db_asyncpg.fetch("SELECT * FROM tenants")
    assert len(workspaces) == 1
    workspace_id = workspaces[0]["id"]
    users = await db_asyncpg.fetch("SELECT * FROM users")
    assert len(users) == 1
    identities = await db_asyncpg.fetch("SELECT * FROM identities")
    assert len(identities) == 2, (
        "User should have a both a workspace and an org identity."
    )
    token = await _login_and_verify_org_access(
        db_asyncpg,
        http_no_auth,
        ADMIN_USER_EMAIL,
        ADMIN_USER_PASSWORD,
        org_id,
    )

    authed_headers = {
        "X-Organization-Id": str(org_id),
        "X-Tenant-Id": str(workspace_id),
        "Authorization": f"Bearer {token}",
    }

    response = await http_no_auth.get(
        "/workspaces/current/members", headers=authed_headers
    )
    assert response.status_code == 200


async def test_auth_bootstrap_multiple(
    db_asyncpg: Connection,
) -> None:
    """Test that running auth bootstrap again is a no-op"""
    await auth_bootstrap.main()

    orgs = await db_asyncpg.fetch("SELECT * FROM organizations")
    assert len(orgs) == 1
    workspaces = await db_asyncpg.fetch("SELECT * FROM tenants")
    assert len(workspaces) == 1
    users = await db_asyncpg.fetch("SELECT * FROM users")
    assert len(users) == 1
    identities = await db_asyncpg.fetch("SELECT * FROM identities")
    assert len(identities) == 2, (
        "User should have a both a workspace and an org identity."
    )


async def test_auth_bootstrap_migrate_from_none_auth(
    http_no_auth: AsyncClient,
) -> None:
    """Test migration from none auth to basic auth"""
    await clear_db()
    prev_settings = config.settings.model_copy()

    config.settings.AUTH_TYPE = "none"
    config.settings.SINGLETON_TENANT_ID = constants.STATIC_SINGLETON_TENANT_ID
    config.settings.BASIC_AUTH_ENABLED = False

    await list_tenants(
        TenantlessAuthInfo(
            user_id=constants.STATIC_SINGLETON_TENANT_ID,
            available_tenants=[],
            available_organizations=[],
        ),
    )

    config.settings.AUTH_TYPE = prev_settings.AUTH_TYPE
    config.settings.SINGLETON_TENANT_ID = prev_settings.SINGLETON_TENANT_ID
    config.settings.BASIC_AUTH_ENABLED = prev_settings.BASIC_AUTH_ENABLED

    await auth_bootstrap.main()

    async with asyncpg_conn() as db:
        orgs = await db.fetch("SELECT * FROM organizations")
        assert len(orgs) == 1
        org_id = orgs[0]["id"]
        assert orgs[0]["is_personal"] is False
        workspaces = await db.fetch("SELECT * FROM tenants")
        assert len(workspaces) == 1
        workspace_id = workspaces[0]["id"]
        users = await db.fetch("SELECT * FROM users")
        assert len(users) == 1
        identities = await db.fetch("SELECT * FROM identities")
        assert len(identities) == 2, (
            "User should have a both a workspace and an org identity."
        )

        token = await _login_and_verify_org_access(
            db,
            http_no_auth,
            ADMIN_USER_EMAIL,
            ADMIN_USER_PASSWORD,
            org_id,
        )

    authed_headers = {
        "X-Organization-Id": str(org_id),
        "X-Tenant-Id": str(workspace_id),
        "Authorization": f"Bearer {token}",
    }

    response = await http_no_auth.get(
        "/workspaces/current/members", headers=authed_headers
    )
    assert response.status_code == 200

    # subsequent run should be a no-op
    await auth_bootstrap.main()

    async with asyncpg_conn() as db:
        orgs = await db.fetch("SELECT * FROM organizations")
        assert len(orgs) == 1
        org_id = orgs[0]["id"]
        assert orgs[0]["is_personal"] is False
        workspaces = await db.fetch("SELECT * FROM tenants")
        assert len(workspaces) == 1
        workspace_id = workspaces[0]["id"]
        users = await db.fetch("SELECT * FROM users")
        assert len(users) == 1
        identities = await db.fetch("SELECT * FROM identities")
        assert len(identities) == 2, (
            "User should have a both a workspace and an org identity."
        )

        token = await _login_and_verify_org_access(
            db,
            http_no_auth,
            ADMIN_USER_EMAIL,
            ADMIN_USER_PASSWORD,
            org_id,
        )

    authed_headers = {
        "X-Organization-Id": str(org_id),
        "X-Tenant-Id": str(workspace_id),
        "Authorization": f"Bearer {token}",
    }

    response = await http_no_auth.get(
        "/workspaces/current/members", headers=authed_headers
    )
    assert response.status_code == 200


async def test_single_org_constraint(
    db_asyncpg: Connection,
    admin_user: AuthedUser,
) -> None:
    """Test that another org is not created when listing tenants or creating a tenant"""
    initial_workspaces = await db_asyncpg.fetchval("SELECT COUNT(*) FROM tenants")
    orgless_auth = TenantlessAuthInfo(
        user_id=admin_user.user.id,
        ls_user_id=admin_user.user.ls_user_id,
        user_email=admin_user.user.email,
        available_organizations=[],
        available_tenants=[],
    )
    await list_tenants(orgless_auth)
    num_workspaces = await db_asyncpg.fetchval("SELECT COUNT(*) FROM tenants")
    assert num_workspaces == initial_workspaces
    num_orgs = await db_asyncpg.fetchval("SELECT COUNT(*) FROM organizations")
    assert num_orgs == 1

    with pytest.raises(HTTPException) as exc:
        await create_tenant(
            orgless_auth,
            schemas.TenantCreate(
                display_name="Test Tenant",
            ),
        )
    assert exc.value.status_code == 400

    await create_tenant(
        orgless_auth,
        schemas.TenantCreate(
            display_name="Test Tenant",
            organization_id=admin_user.org_auth.organization_id,
        ),
    )

    num_workspaces = await db_asyncpg.fetchval("SELECT COUNT(*) FROM tenants")
    assert num_workspaces == initial_workspaces + 1
    num_orgs = await db_asyncpg.fetchval("SELECT COUNT(*) FROM organizations")
    assert num_orgs == 1

    await create_workspace(
        admin_user.org_auth,
        schemas.WorkspaceCreate(
            display_name="Test Workspace",
        ),
    )

    num_workspaces = await db_asyncpg.fetchval("SELECT COUNT(*) FROM tenants")
    assert num_workspaces == initial_workspaces + 2
    num_orgs = await db_asyncpg.fetchval("SELECT COUNT(*) FROM organizations")
    assert num_orgs == 1


async def test_patch_user_by_admin(
    db_asyncpg: Connection,
    admin_user: AuthedUser,
    fresh_workspace_user: AuthedUser,
) -> None:
    """Test that user full_name and password can be updated by an admin."""
    new_name = "New Name"
    new_password = "LangChainTest444!"

    identity_id = await db_asyncpg.fetchval(
        "SELECT id FROM identities WHERE user_id = $1 AND access_scope = 'organization'",
        fresh_workspace_user.user.id,
    )
    user_prev = await get_user(fresh_workspace_user.user.email)
    await identities_crud.patch_organization_user(
        admin_user.org_auth,
        identity_id,
        schemas.OrgIdentityPatchInternal(
            full_name=new_name,
            password=new_password,
        ),
    )

    user = await get_user(fresh_workspace_user.user.email)

    assert user is not None, "User should exist"
    assert user.full_name == new_name, "User should have updated full name"
    assert hashed_passwords_match(user.hashed_password, new_password), (
        "User should have updated password"
    )
    assert user_prev.updated_at != user.updated_at, "User should have updated timestamp"
    provider_users = await get_provider_users(user.ls_user_id)
    assert len(provider_users) == 1
    provider_user = provider_users[0]
    assert provider_user.provider == schemas.AuthProvider.email
    assert provider_user.ls_user_id == user.ls_user_id
    assert provider_user.saml_provider_id is None
    assert provider_user.provider_user_id is None
    assert provider_user.email == fresh_workspace_user.user.email
    assert provider_user.full_name == new_name
    assert provider_user.hashed_password is not None
    assert hashed_passwords_match(provider_user.hashed_password, new_password)

    newer_password = "LangChainTest555!"

    await identities_crud.patch_organization_user(
        admin_user.org_auth,
        identity_id,
        schemas.OrgIdentityPatchInternal(
            password=newer_password,
        ),
    )

    user = await get_user(fresh_workspace_user.user.email)
    assert user.full_name == new_name, "User should still have updated full name"
    assert hashed_passwords_match(user.hashed_password, newer_password), (
        "User should have updated password"
    )

    await identities_crud.patch_organization_user(
        admin_user.org_auth,
        identity_id,
        schemas.OrgIdentityPatchInternal(
            full_name="",
        ),
    )

    user = await get_user(fresh_workspace_user.user.email)
    assert user.full_name == "", "User should have empty full name"
    assert hashed_passwords_match(user.hashed_password, newer_password), (
        "User should have updated password"
    )
    provider_users = await get_provider_users(user.ls_user_id)
    assert len(provider_users) == 1
    provider_user = provider_users[0]
    assert provider_user.provider == schemas.AuthProvider.email
    assert provider_user.ls_user_id == user.ls_user_id
    assert provider_user.saml_provider_id is None
    assert provider_user.provider_user_id is None
    assert provider_user.email == fresh_workspace_user.user.email
    assert provider_user.full_name == ""
    assert provider_user.hashed_password is not None
    assert hashed_passwords_match(provider_user.hashed_password, newer_password)


async def test_patch_user_by_current_user(
    fresh_workspace_user: AuthedUser,
) -> None:
    """Test that user full_name and password can be updated by the current user."""
    new_name = "New Name"
    new_password = "LangChainTest444!"

    user_prev = await get_user(fresh_workspace_user.user.email)
    await identities_crud.patch_basic_auth_current_user(
        fresh_workspace_user.org_auth,
        schemas.BasicAuthUserPatch(
            full_name=new_name,
            password=new_password,
        ),
    )

    user = await get_user(fresh_workspace_user.user.email)

    assert user is not None, "User should exist"
    assert user.full_name == new_name, "User should have updated full name"
    assert hashed_passwords_match(user.hashed_password, new_password), (
        "User should have updated password"
    )
    assert user_prev.updated_at != user.updated_at, "User should have updated timestamp"
    provider_users = await get_provider_users(user.ls_user_id)
    assert len(provider_users) == 1
    provider_user = provider_users[0]
    assert provider_user.provider == schemas.AuthProvider.email
    assert provider_user.ls_user_id == user.ls_user_id
    assert provider_user.saml_provider_id is None
    assert provider_user.provider_user_id is None
    assert provider_user.email == fresh_workspace_user.user.email
    assert provider_user.full_name == new_name
    assert provider_user.hashed_password is not None
    assert hashed_passwords_match(provider_user.hashed_password, new_password)

    newer_password = "LangChainTest555!"

    await identities_crud.patch_basic_auth_current_user(
        fresh_workspace_user.org_auth,
        schemas.BasicAuthUserPatch(
            password=newer_password,
        ),
    )

    user = await get_user(fresh_workspace_user.user.email)
    assert user.full_name == new_name, "User should still have updated full name"
    assert hashed_passwords_match(user.hashed_password, newer_password), (
        "User should have updated password"
    )
    provider_users = await get_provider_users(user.ls_user_id)
    assert len(provider_users) == 1
    provider_user = provider_users[0]
    assert provider_user.provider == schemas.AuthProvider.email
    assert provider_user.ls_user_id == user.ls_user_id
    assert provider_user.saml_provider_id is None
    assert provider_user.provider_user_id is None
    assert provider_user.email == fresh_workspace_user.user.email
    assert provider_user.full_name == new_name
    assert provider_user.hashed_password is not None
    assert hashed_passwords_match(provider_user.hashed_password, newer_password)

    await identities_crud.patch_basic_auth_current_user(
        fresh_workspace_user.org_auth,
        schemas.BasicAuthUserPatch(
            full_name="",
        ),
    )

    user = await get_user(fresh_workspace_user.user.email)
    assert user.full_name == "", "User should have empty full name"
    assert hashed_passwords_match(user.hashed_password, newer_password), (
        "User should have updated password"
    )
    provider_users = await get_provider_users(user.ls_user_id)
    assert len(provider_users) == 1
    provider_user = provider_users[0]
    assert provider_user.provider == schemas.AuthProvider.email
    assert provider_user.ls_user_id == user.ls_user_id
    assert provider_user.saml_provider_id is None
    assert provider_user.provider_user_id is None
    assert provider_user.email == fresh_workspace_user.user.email
    assert provider_user.full_name == ""
    assert provider_user.hashed_password is not None
    assert hashed_passwords_match(provider_user.hashed_password, newer_password)


async def test_add_users_batch(
    db_asyncpg: Connection,
    http_no_auth: AsyncClient,
    admin_user: AuthedUser,
) -> None:
    await db_asyncpg.execute("DELETE FROM seat_change_billing_events")
    roles = await list_roles(admin_user.org_auth)
    org_user_role_id = next(
        x.id for x in roles if x.name == OrganizationRoles.USER.value
    )
    org_admin_role_id = next(
        x.id for x in roles if x.name == OrganizationRoles.ADMIN.value
    )
    workspace_admin_role_id = next(x.id for x in roles if x.name == "WORKSPACE_ADMIN")
    workspace_viewer_role_id = next(x.id for x in roles if x.name == "WORKSPACE_VIEWER")

    # Create another workspace in the organization
    workspace2 = await create_workspace(
        admin_user.org_auth, schemas.WorkspaceCreate(display_name="Another One")
    )
    ws2_auth = AuthInfo(
        tenant_id=workspace2.id,
        organization_id=admin_user.org_auth.organization_id,
        user_id=admin_user.user.id,
        ls_user_id=admin_user.user.ls_user_id,
        tenant_config=admin_user.ws_auth.tenant_config,
    )

    payloads = [
        schemas.BasicAuthMemberCreate(
            email="<EMAIL>",
            role_id=org_admin_role_id,
        ),
        schemas.BasicAuthMemberCreate(
            email="<EMAIL>",
        ),
        schemas.BasicAuthMemberCreate(
            email="<EMAIL>",
            role_id=org_user_role_id,
        ),
        schemas.BasicAuthMemberCreate(
            email="<EMAIL>",
            role_id=org_user_role_id,
            workspace_ids=[admin_user.ws_auth.tenant_id],
            workspace_role_id=workspace_admin_role_id,
        ),
        schemas.BasicAuthMemberCreate(
            email="<EMAIL>",
            role_id=org_user_role_id,
            workspace_ids=[admin_user.ws_auth.tenant_id],
            workspace_role_id=workspace_viewer_role_id,
        ),
        schemas.BasicAuthMemberCreate(
            email="<EMAIL>",
            role_id=org_user_role_id,
            workspace_ids=[workspace2.id],
            workspace_role_id=workspace_viewer_role_id,
        ),
        # one to test case
        schemas.BasicAuthMemberCreate(
            email="<EMAIL>",
            role_id=org_user_role_id,
            workspace_ids=[admin_user.ws_auth.tenant_id, workspace2.id],
            workspace_role_id=workspace_viewer_role_id,
        ),
    ]

    users = await identities_crud.add_basic_auth_users(
        admin_user.org_auth,
        payloads,
    )

    seat_events = await _list_seat_events()
    assert len(seat_events) == _NUM_SEAT_TYPES

    _assert_valid_transaction_chain_for_org(
        seat_events, [seat_txn.SeatChangeOperation.ADD_TO_ORGANIZATION]
    )
    _assert_reporting_status(seat_events)

    assert seat_events[0].seat_type == OrganizationRoles.ADMIN.value
    assert seat_events[0].seats_before == 1
    assert seat_events[0].seats_after == 2
    assert seat_events[0].pending_seats_before == 0
    assert seat_events[0].pending_seats_after == 0

    assert seat_events[1].seat_type == OrganizationRoles.USER.value
    assert seat_events[1].seats_before == 0
    assert seat_events[1].seats_after == 6
    assert seat_events[1].pending_seats_before == 0
    assert seat_events[1].pending_seats_after == 0

    org_members = await identities_crud.get_org_members(admin_user.org_auth)
    assert len(org_members.members) == 1 + len(payloads)
    assert len(org_members.pending) == 0
    org_admin_emails = {admin_user.user.email, "<EMAIL>"}
    org_user_emails = set(
        [f"testuser+{i}@langchain.dev" for i in range(1, len(payloads))]
    )
    assert (
        set([x.email for x in org_members.members if x.role_id == org_admin_role_id])
        == org_admin_emails
    )
    assert (
        set([x.email for x in org_members.members if x.role_id == org_user_role_id])
        == org_user_emails
    )

    workspace1_members = await identities_crud.get_tenant_members(admin_user.ws_auth)
    assert len(workspace1_members.members) == 5
    assert len(workspace1_members.pending) == 0
    assert set([x.email for x in workspace1_members.members]) == org_admin_emails | {
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    }
    workspace1_admins = set(
        [
            x.email
            for x in workspace1_members.members
            if x.role_id == workspace_admin_role_id
        ]
    )
    workspace1_admins == org_admin_emails | {"<EMAIL>"}
    workspace1_viewers = set(
        [
            x.email
            for x in workspace1_members.members
            if x.role_id == workspace_viewer_role_id
        ]
    )
    assert workspace1_viewers == {
        "<EMAIL>",
        "<EMAIL>",
    }

    workspace2_members = await identities_crud.get_tenant_members(ws2_auth)
    assert len(workspace2_members.members) == 4
    assert len(workspace2_members.pending) == 0
    assert set([x.email for x in workspace2_members.members]) == org_admin_emails | {
        "<EMAIL>",
        "<EMAIL>",
    }
    workspace2_admins = set(
        [
            x.email
            for x in workspace2_members.members
            if x.role_id == workspace_admin_role_id
        ]
    )
    assert workspace2_admins == org_admin_emails
    workspace2_viewers = set(
        [
            x.email
            for x in workspace2_members.members
            if x.role_id == workspace_viewer_role_id
        ]
    )
    assert workspace2_viewers == {
        "<EMAIL>",
        "<EMAIL>",
    }

    for user in users:
        await _login_and_verify_org_access(
            db_asyncpg,
            http_no_auth,
            user.email,
            user.password,
            admin_user.org_auth.organization_id,
        )


async def test_add_and_delete_user(
    db_asyncpg: Connection,
    http_no_auth: AsyncClient,
    admin_user: AuthedUser,
) -> None:
    email = "<EMAIL>"
    roles = await list_roles(admin_user.org_auth)
    org_admin_role_id = next(
        x.id for x in roles if x.name == OrganizationRoles.ADMIN.value
    )
    user = await identities_crud.add_basic_auth_user(
        admin_user.org_auth,
        schemas.BasicAuthMemberCreate(
            email=email,
            role_id=org_admin_role_id,
        ),
    )

    await db_asyncpg.execute("DELETE FROM seat_change_billing_events")

    identity_id = await db_asyncpg.fetchval(
        "SELECT id FROM identities WHERE user_id = $1 AND access_scope = 'organization'",
        user.id,
    )
    token = await _login_and_verify_org_access(
        db_asyncpg,
        http_no_auth,
        user.email,
        user.password,
        admin_user.org_auth.organization_id,
    )
    await identities_crud.delete_identity_for_current_org(
        admin_user.org_auth, identity_id
    )

    seat_events = await _list_seat_events()
    assert len(seat_events) == _NUM_SEAT_TYPES

    _assert_valid_transaction_chain_for_org(
        seat_events, [seat_txn.SeatChangeOperation.REMOVE_FROM_ORGANIZATION]
    )
    _assert_reporting_status(seat_events)

    assert seat_events[0].seat_type == OrganizationRoles.ADMIN.value
    assert seat_events[0].seats_before == 2
    assert seat_events[0].seats_after == 1
    assert seat_events[0].pending_seats_before == 0
    assert seat_events[0].pending_seats_after == 0

    assert seat_events[1].seat_type == OrganizationRoles.USER.value
    assert seat_events[1].seats_before == 0
    assert seat_events[1].seats_after == 0
    assert seat_events[1].pending_seats_before == 0
    assert seat_events[1].pending_seats_after == 0

    org_members = await identities_crud.get_org_members(admin_user.org_auth)
    assert len(org_members.members) == 1
    user_after_delete = await get_user(user.email)
    assert user_after_delete is None

    # verify that login fails and user does not have access to the org
    response = await http_no_auth.post(
        "/login",
        headers={"Authorization": basic_auth_header(user.email, user.password)},
    )
    assert response.status_code == 401
    headers = {
        "Authorization": f"Bearer {token}",
        "X-Organization-Id": str(admin_user.org_auth.organization_id),
    }
    response = await http_no_auth.get("/orgs/current/info", headers=headers)
    assert response.status_code == 403

    # re-add the user
    user = await identities_crud.add_basic_auth_user(
        admin_user.org_auth,
        schemas.BasicAuthMemberCreate(
            email=email,
            role_id=org_admin_role_id,
        ),
    )
    org_members = await identities_crud.get_org_members(admin_user.org_auth)
    assert len(org_members.members) == 2
    assert email in [x.email for x in org_members.members]
    assert await get_user(user.email) is not None
    await _login_and_verify_org_access(
        db_asyncpg,
        http_no_auth,
        user.email,
        user.password,
        admin_user.org_auth.organization_id,
    )


async def test_api_keys(
    db_asyncpg: Connection,
    http_no_auth: AsyncClient,
    fresh_workspace_user: AuthedUser,
) -> None:
    token = await _login_and_verify_org_access(
        db_asyncpg,
        http_no_auth,
        fresh_workspace_user.user.email,
        cast(str, fresh_workspace_user.user.password),
        fresh_workspace_user.org_auth.organization_id,
    )
    base_headers = {
        "X-Tenant-Id": str(fresh_workspace_user.ws_auth.tenant_id),
        "X-Organization-Id": str(fresh_workspace_user.ws_auth.organization_id),
    }
    user_headers = {
        **base_headers,
        "Authorization": f"Bearer {token}",
    }
    response = await http_no_auth.post(
        "/api-key", json={"description": "test"}, headers=user_headers
    )
    assert response.status_code == 200
    api_key = schemas.APIKeyCreateResponse(**response.json())

    api_keys = await api_keys_crud.list_api_keys(fresh_workspace_user.ws_auth)
    assert api_key.id in [x["id"] for x in api_keys]

    api_key_headers = {
        **base_headers,
        "X-Api-Key": api_key.key,
    }
    response = await http_no_auth.post(
        "/api-key", json={"description": "test"}, headers=api_key_headers
    )
    assert response.status_code == 200

    # Test the same for a PAT, which must be created by a user
    response = await http_no_auth.post(
        "/api-key/current", json={"description": "test"}, headers=user_headers
    )
    assert response.status_code == 200, response.text
    api_key = schemas.APIKeyCreateResponse(**response.json())

    api_keys = await api_keys_crud.list_personal_access_tokens(
        fresh_workspace_user.ws_auth
    )
    assert api_key.id in [x["id"] for x in api_keys]

    api_key_headers = {
        **base_headers,
        "X-Api-Key": api_key.key,
    }
    response = await http_no_auth.post(
        "/api-key", json={"description": "test"}, headers=api_key_headers
    )
    assert response.status_code == 200


async def test_cannot_add_users_duplicate_emails(
    admin_user: AuthedUser,
) -> None:
    with pytest.raises(HTTPException) as exc:
        await identities_crud.add_basic_auth_users(
            admin_user.org_auth,
            [
                schemas.BasicAuthMemberCreate(
                    email="<EMAIL>",
                ),
                schemas.BasicAuthMemberCreate(
                    email="<EMAIL>",
                ),
            ],
        )

    assert exc.value.status_code == 400


async def test_cannot_add_user_existing_email(
    admin_user: AuthedUser,
) -> None:
    with pytest.raises(HTTPException) as exc:
        await identities_crud.add_basic_auth_users(
            admin_user.org_auth,
            [
                schemas.BasicAuthMemberCreate(
                    email=admin_user.user.email,
                ),
            ],
        )

    assert exc.value.status_code == 409


async def test_cannot_add_users_if_can_add_seats_is_false(
    admin_user: AuthedUser,
) -> None:
    admin_user.org_auth.org_config.can_add_seats = False
    with pytest.raises(HTTPException) as exc:
        await identities_crud.add_basic_auth_users(
            admin_user.org_auth,
            [
                schemas.BasicAuthMemberCreate(
                    email="<EMAIL>",
                ),
            ],
        )

    assert exc.value.status_code == 400


async def test_cannot_add_users_past_max_identities(
    admin_user: AuthedUser,
) -> None:
    admin_user.org_auth.org_config.max_identities = 4

    payloads = [
        schemas.BasicAuthMemberCreate(
            email=f"basicauthuser+{i}@langchain.dev",
        )
        for i in range(admin_user.org_auth.org_config.max_identities)
    ]

    with pytest.raises(HTTPException) as exc:
        await identities_crud.add_basic_auth_users(admin_user.org_auth, payloads)

    assert exc.value.status_code == 400
    assert "maximum number of members: 5/4" in exc.value.detail


async def test_cannot_insert_duplicate_email(
    admin_user: AuthedUser,
    fresh_workspace_user: AuthedUser,
) -> None:
    with pytest.raises(HTTPException) as exc:
        await identities_crud.add_basic_auth_user(
            admin_user.org_auth,
            schemas.BasicAuthMemberCreate(
                email=fresh_workspace_user.user.email,
            ),
        )

    assert exc.value.status_code == 409


@pytest.mark.parametrize("is_personal", [True, False])
async def test_cannot_create_org(
    is_personal: bool,
    admin_user: AuthedUser,
) -> None:
    with pytest.raises(HTTPException) as exc:
        await create_organization(
            TenantlessAuthInfo(
                user_id=admin_user.user.id,
                ls_user_id=admin_user.user.ls_user_id,
                user_email=admin_user.user.email,
                available_organizations=[],
                available_tenants=[],
            ),
            schemas.OrganizationCreate(
                display_name="Test",
                is_personal=is_personal,
            ),
        )

    assert exc.value.status_code == 400
    assert "basic auth" in exc.value.detail
