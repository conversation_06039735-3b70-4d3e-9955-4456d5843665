# serializer version: 1
# name: test_fetch_section[group_by_metadata]
  dict({
    'charts': list([
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T12:49:00',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T15:49:00',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T17:49:00',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T12:49:00',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T14:49:00',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T17:49:00',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T11:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T14:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T16:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T11:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T13:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T16:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T18:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T10:49:00',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T13:49:00',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T15:49:00',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T18:49:00',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 3,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:0',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:1',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:2',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:3',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:4',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Trace Count',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T11:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0.75,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T13:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T14:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0.5,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T16:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0.25,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T18:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T19:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T11:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0.8,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T13:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0.55,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T15:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T16:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0.3,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T18:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0.05,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0.85,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T12:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T13:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0.6,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T15:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0.35,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T17:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T18:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0.1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0.9,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T12:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0.65,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T14:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T15:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0.4,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T17:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0.15,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T19:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0.95,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T11:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T12:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0.7,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T14:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0.45,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T16:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T17:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0.2,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T19:49:00',
            'value': None,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 1,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:0',
            'metric': 'feedback_score_avg',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:1',
            'metric': 'feedback_score_avg',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:2',
            'metric': 'feedback_score_avg',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:3',
            'metric': 'feedback_score_avg',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:4',
            'metric': 'feedback_score_avg',
            'name': 'avg score',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feedback: correctness',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T11:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T12:49:00',
            'value': 60.0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T13:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T14:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T15:49:00',
            'value': 10.0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T16:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T17:49:00',
            'value': 5.0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T18:49:00',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T19:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T11:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T12:49:00',
            'value': 16.0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T13:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T14:49:00',
            'value': 11.0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T15:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T16:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T17:49:00',
            'value': 6.0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T18:49:00',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T11:49:00',
            'value': 17.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T12:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T13:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T14:49:00',
            'value': 12.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T15:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T16:49:00',
            'value': 7.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T17:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T18:49:00',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T19:49:00',
            'value': 2.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T10:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T11:49:00',
            'value': 18.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T12:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T13:49:00',
            'value': 26.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T14:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T15:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T16:49:00',
            'value': 8.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T17:49:00',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T18:49:00',
            'value': 3.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T19:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T10:49:00',
            'value': 19.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T11:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T12:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T13:49:00',
            'value': 28.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T14:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T15:49:00',
            'value': 36.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T16:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T17:49:00',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T18:49:00',
            'value': 4.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T19:49:00',
            'value': None,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 0,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:0',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:1',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:2',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:3',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:4',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Avg Latency',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:blue:0',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T15:49:00',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:green:0',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T17:49:00',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:red:0',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T12:49:00',
            'value': 2,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:yellow:0',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T12:49:00',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:blue:1',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T17:49:00',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:green:1',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:red:1',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T14:49:00',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:yellow:1',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T14:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:blue:2',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:green:2',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T11:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:red:2',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T16:49:00',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:yellow:2',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T16:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:blue:3',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T11:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:green:3',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T13:49:00',
            'value': 2,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:red:3',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T18:49:00',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:yellow:3',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T18:49:00',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:blue:4',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T13:49:00',
            'value': 2,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:green:4',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T15:49:00',
            'value': 2,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:red:4',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T10:49:00',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:yellow:4',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 2,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "blue"))), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:blue:0',
            'metric': 'run_count',
            'name': 'blue',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "blue"))), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:blue:1',
            'metric': 'run_count',
            'name': 'blue',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "blue"))), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:blue:2',
            'metric': 'run_count',
            'name': 'blue',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "blue"))), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:blue:3',
            'metric': 'run_count',
            'name': 'blue',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "blue"))), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:blue:4',
            'metric': 'run_count',
            'name': 'blue',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "green"))), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:green:0',
            'metric': 'run_count',
            'name': 'green',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "green"))), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:green:1',
            'metric': 'run_count',
            'name': 'green',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "green"))), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:green:2',
            'metric': 'run_count',
            'name': 'green',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "green"))), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:green:3',
            'metric': 'run_count',
            'name': 'green',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "green"))), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:green:4',
            'metric': 'run_count',
            'name': 'green',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "red"))), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:red:0',
            'metric': 'run_count',
            'name': 'red',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "red"))), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:red:1',
            'metric': 'run_count',
            'name': 'red',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "red"))), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:red:2',
            'metric': 'run_count',
            'name': 'red',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "red"))), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:red:3',
            'metric': 'run_count',
            'name': 'red',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "red"))), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:red:4',
            'metric': 'run_count',
            'name': 'red',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "yellow"))), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:yellow:0',
            'metric': 'run_count',
            'name': 'yellow',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "yellow"))), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:yellow:1',
            'metric': 'run_count',
            'name': 'yellow',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "yellow"))), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:yellow:2',
            'metric': 'run_count',
            'name': 'yellow',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "yellow"))), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:yellow:3',
            'metric': 'run_count',
            'name': 'yellow',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "yellow"))), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:yellow:4',
            'metric': 'run_count',
            'name': 'yellow',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feedback: color',
      }),
    ]),
    'description': 'description_placeholder',
    'id': 'uuid_placeholder',
    'index': 0,
    'session_id': None,
    'sub_sections': list([
    ]),
    'title': 'test',
  })
# ---
# name: test_fetch_section[no_group_by]
  dict({
    'charts': list([
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0.95,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0.875,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0.775,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0.675,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0.575,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0.475,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0.375,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0.275,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0.175,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0.075,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 1,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'eq(is_root, true)',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder',
            'metric': 'feedback_score_avg',
            'name': 'avg score',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feedback: correctness',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T10:49:00',
            'value': 19.0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T11:49:00',
            'value': 17.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T12:49:00',
            'value': 23.0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T13:49:00',
            'value': 13.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T14:49:00',
            'value': 11.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T15:49:00',
            'value': 14.0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T16:49:00',
            'value': 7.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T17:49:00',
            'value': 5.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T18:49:00',
            'value': 3.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1.5,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 0,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'eq(is_root, true)',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Avg Latency',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T10:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T11:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T12:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T13:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T14:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T15:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T16:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T17:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T18:49:00',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T19:49:00',
            'value': 2,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 3,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'eq(is_root, true)',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Trace Count',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T12:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T14:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T16:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T18:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:blue',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T11:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T13:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T15:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T17:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:green',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T10:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T11:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T12:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T13:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T14:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T15:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T16:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T17:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T18:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:red',
            'timestamp': '2025-04-16T19:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T10:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T11:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T12:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T13:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T14:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T15:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T16:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T17:49:00',
            'value': 0,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T18:49:00',
            'value': 1,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder:yellow',
            'timestamp': '2025-04-16T19:49:00',
            'value': 0,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 2,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "blue")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder:blue',
            'metric': 'run_count',
            'name': 'blue',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "green")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder:green',
            'metric': 'run_count',
            'name': 'green',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "red")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder:red',
            'metric': 'run_count',
            'name': 'red',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(feedback_key, "color"), eq(feedback_value, "yellow")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder:yellow',
            'metric': 'run_count',
            'name': 'yellow',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feedback: color',
      }),
    ]),
    'description': 'description_placeholder',
    'id': 'uuid_placeholder',
    'index': 0,
    'session_id': None,
    'sub_sections': list([
    ]),
    'title': 'test',
  })
# ---
