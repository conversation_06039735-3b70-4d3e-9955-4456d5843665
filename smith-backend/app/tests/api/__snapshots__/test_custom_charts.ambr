# serializer version: 1
# name: test_fetch_section[group_by_metadata]
  dict({
    'charts': list([
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T10:49:34',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T11:49:34',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T12:49:34',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T13:49:34',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T14:49:34',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T15:49:34',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T16:49:34',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T17:49:34',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T18:49:34',
            'value': 0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T19:49:34',
            'value': 1,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T20:49:34',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T10:49:34',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T11:49:34',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T12:49:34',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T13:49:34',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T14:49:34',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T15:49:34',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T16:49:34',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T17:49:34',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T18:49:34',
            'value': 0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T19:49:34',
            'value': 1,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T20:49:34',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T10:49:34',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T11:49:34',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T12:49:34',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T13:49:34',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T14:49:34',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T15:49:34',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T16:49:34',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T17:49:34',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T18:49:34',
            'value': 1,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T19:49:34',
            'value': 0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T20:49:34',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T10:49:34',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T11:49:34',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T12:49:34',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T13:49:34',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T14:49:34',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T15:49:34',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T16:49:34',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T17:49:34',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T18:49:34',
            'value': 1,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T19:49:34',
            'value': 0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T20:49:34',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T10:49:34',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T11:49:34',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T12:49:34',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T13:49:34',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T14:49:34',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T15:49:34',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T16:49:34',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T17:49:34',
            'value': 1,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T18:49:34',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T19:49:34',
            'value': 0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T20:49:34',
            'value': 0,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 3,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:0',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:1',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:2',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:3',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:4',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Trace Count',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T10:49:34',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T11:49:34',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T12:49:34',
            'value': 15.0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T13:49:34',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T14:49:34',
            'value': 10.0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T15:49:34',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T16:49:34',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T17:49:34',
            'value': 5.0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T18:49:34',
            'value': None,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T19:49:34',
            'value': 0.0,
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T20:49:34',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T10:49:34',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T11:49:34',
            'value': 16.0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T12:49:34',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T13:49:34',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T14:49:34',
            'value': 11.0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T15:49:34',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T16:49:34',
            'value': 6.0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T17:49:34',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T18:49:34',
            'value': None,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T19:49:34',
            'value': 1.0,
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T20:49:34',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T10:49:34',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T11:49:34',
            'value': 17.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T12:49:34',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T13:49:34',
            'value': 12.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T14:49:34',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T15:49:34',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T16:49:34',
            'value': 7.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T17:49:34',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T18:49:34',
            'value': 2.0,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T19:49:34',
            'value': None,
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T20:49:34',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T10:49:34',
            'value': 18.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T11:49:34',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T12:49:34',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T13:49:34',
            'value': 13.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T14:49:34',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T15:49:34',
            'value': 8.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T16:49:34',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T17:49:34',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T18:49:34',
            'value': 3.0,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T19:49:34',
            'value': None,
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T20:49:34',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T10:49:34',
            'value': 19.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T11:49:34',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T12:49:34',
            'value': 14.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T13:49:34',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T14:49:34',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T15:49:34',
            'value': 9.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T16:49:34',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T17:49:34',
            'value': 4.0,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T18:49:34',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T19:49:34',
            'value': None,
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T20:49:34',
            'value': None,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 0,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:0',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:1',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:2',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:3',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:4',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Avg Latency',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'green': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'green': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'green': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'green': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'green': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 1,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 2,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': 'color',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:0',
            'metric': 'feedback_values',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'color',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:1',
            'metric': 'feedback_values',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'color',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:2',
            'metric': 'feedback_values',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'color',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:3',
            'metric': 'feedback_values',
            'name': 'run count',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'color',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:4',
            'metric': 'feedback_values',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feeback: color',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.75,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.5,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.25,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.0,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '0',
            'series_id': 'uuid_placeholder:0',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.8,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.55,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.3,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.05,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '1',
            'series_id': 'uuid_placeholder:1',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.85,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.6,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.35,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.1,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '2',
            'series_id': 'uuid_placeholder:2',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.9,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.65,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.4,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.15,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '3',
            'series_id': 'uuid_placeholder:3',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.95,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.7,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.45,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.2,
                'errors': 0,
                'n': 1,
                'stdev': 0.0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': '4',
            'series_id': 'uuid_placeholder:4',
            'timestamp': '2025-04-16T20:49:34',
            'value': dict({
              'correctness': dict({
                'avg': None,
                'n': 0,
                'values': dict({
                }),
              }),
            }),
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 1,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "0")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:0',
            'metric': 'feedback_values',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "1")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:1',
            'metric': 'feedback_values',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "2")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:2',
            'metric': 'feedback_values',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "3")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:3',
            'metric': 'feedback_values',
            'name': 'avg score',
            'workspace_id': None,
          }),
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "4")))',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': dict({
              'attribute': 'metadata',
              'path': 'foo',
              'set_by': 'section',
            }),
            'id': 'uuid_placeholder:4',
            'metric': 'feedback_values',
            'name': 'avg score',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feeback: correctness',
      }),
    ]),
    'description': 'description_placeholder',
    'id': 'uuid_placeholder',
    'index': 0,
    'session_id': None,
    'sub_sections': None,
    'title': 'test',
  })
# ---
# name: test_fetch_section[no_group_by]
  dict({
    'charts': list([
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T10:49:34',
            'value': 18.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T11:49:34',
            'value': 16.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T12:49:34',
            'value': 14.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T13:49:34',
            'value': 12.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T14:49:34',
            'value': 10.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T15:49:34',
            'value': 8.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T16:49:34',
            'value': 6.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T17:49:34',
            'value': 4.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T18:49:34',
            'value': 2.5,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T19:49:34',
            'value': 0.5,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 0,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'eq(is_root, true)',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder',
            'metric': 'latency_avg',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Avg Latency',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T10:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T11:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T12:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T13:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T14:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T15:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T16:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T17:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T18:49:34',
            'value': 2,
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T19:49:34',
            'value': 2,
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 3,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': None,
            'filters': dict({
              'filter': 'eq(is_root, true)',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder',
            'metric': 'run_count',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Trace Count',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'green': 1,
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'green': 1,
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'green': 1,
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'green': 1,
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                  'red': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'green': 1,
                  'yellow': 1,
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'color': dict({
                'avg': None,
                'errors': 0,
                'n': 2,
                'stdev': None,
                'values': dict({
                  'blue': 1,
                  'red': 1,
                }),
              }),
            }),
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 2,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': 'color',
            'filters': dict({
              'filter': 'eq(is_root, true)',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder',
            'metric': 'feedback_values',
            'name': 'run count',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feeback: color',
      }),
      dict({
        'chart_type': 'line',
        'common_filters': None,
        'data': list([
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T10:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.925,
                'errors': 0,
                'n': 2,
                'stdev': 0.024999999999997514,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T11:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.825,
                'errors': 0,
                'n': 2,
                'stdev': 0.025000000000001955,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T12:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.725,
                'errors': 0,
                'n': 2,
                'stdev': 0.024999999999999734,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T13:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.625,
                'errors': 0,
                'n': 2,
                'stdev': 0.024999999999999734,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T14:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.525,
                'errors': 0,
                'n': 2,
                'stdev': 0.024999999999999734,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T15:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.425,
                'errors': 0,
                'n': 2,
                'stdev': 0.024999999999999734,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T16:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.325,
                'errors': 0,
                'n': 2,
                'stdev': 0.02500000000000029,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T17:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.225,
                'errors': 0,
                'n': 2,
                'stdev': 0.025000000000000012,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T18:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.125,
                'errors': 0,
                'n': 2,
                'stdev': 0.025000000000000012,
                'values': dict({
                }),
              }),
            }),
          }),
          dict({
            'group': None,
            'series_id': 'uuid_placeholder',
            'timestamp': '2025-04-16T19:49:34',
            'value': dict({
              'correctness': dict({
                'avg': 0.025,
                'errors': 0,
                'n': 2,
                'stdev': 0.025,
                'values': dict({
                }),
              }),
            }),
          }),
        ]),
        'description': 'description_placeholder',
        'id': 'uuid_placeholder',
        'index': 1,
        'metadata': None,
        'series': list([
          dict({
            'feedback_key': 'correctness',
            'filters': dict({
              'filter': 'eq(is_root, true)',
              'session': list([
                'uuid_placeholder',
              ]),
              'trace_filter': None,
              'tree_filter': None,
            }),
            'group_by': None,
            'id': 'uuid_placeholder',
            'metric': 'feedback_values',
            'name': 'avg score',
            'workspace_id': None,
          }),
        ]),
        'title': 'Feeback: correctness',
      }),
    ]),
    'description': 'description_placeholder',
    'id': 'uuid_placeholder',
    'index': 0,
    'session_id': None,
    'sub_sections': None,
    'title': 'test',
  })
# ---
