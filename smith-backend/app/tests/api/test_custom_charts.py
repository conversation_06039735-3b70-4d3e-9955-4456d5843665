"""Test correct functionality of custom charts endpoints."""

import asyncio
from datetime import datetime, timedelta, timezone
from typing import Any, Awaitable, Callable
from uuid import UUID, uuid4

import asyncpg
import jsonpatch
import orjson
import pytest
from httpx import AsyncClient
from httpx_sse import aconnect_sse
from syrupy import SnapshotAssertion

from app import config, models, schemas
from app.api.auth.schemas import AuthInfo, OrgAuthInfo
from app.models.charts import constants as chart_constants
from app.models.charts.jobs import sync_org_charts
from app.models.runs.monitor import get_timedelta_unit, get_timedelta_value
from app.tests.conftest import create_workspaces
from app.tests.utils import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
    aclient_for_headers,
    fresh_tenant_client,
    post_runs,
    random_lower_string,
    setup_headers_for_auth,
)

ORG_SCOPED_TO_ENDPOINT_PREFIX = {
    True: "/org-charts",
    False: "/charts",
}

# Add this at the module level
_chart_request_cache: dict[tuple[str, str, str], Any] = {}
_chart_request_headers: dict[tuple[str, str, str], Any] = {}
_chart_start_time: dict[str, datetime] = {}


async def _wait_for_write_queue(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]], tenant_id: str
) -> None:
    global _chart_start_time
    await wait_until_task_queue_empty()
    _chart_start_time[tenant_id] = datetime.now(timezone.utc) + timedelta(seconds=1)


async def _patch_chart(
    http_tenant: AsyncClient,
    prefix: str,
    chart_id: str,
    chart: schemas.CustomChartUpdate,
    assert_code: int = 200,
    skip_preview: bool = False,
) -> None:
    chart_json = chart.model_dump(exclude_defaults=True)
    if not isinstance(chart.series, schemas.Missing):
        series = [s.model_dump() for s in chart.series]
        chart_json["series"] = series
        for s in chart_json["series"]:
            s["filters"]["session"] = [
                str(session_id) for session_id in s["filters"]["session"]
            ]
            if s.get("id"):
                s["id"] = str(s["id"])
    if not isinstance(chart.section_id, schemas.Missing):
        chart_json["section_id"] = str(chart.section_id)
    if chart_json.get("common_filters") is not None:
        if chart_json["common_filters"].get("session"):
            chart_json["common_filters"]["session"] = [
                str(session_id)
                for session_id in chart_json["common_filters"]["session"]
            ]
    response = await http_tenant.patch(
        f"{prefix}/{chart_id}",
        json=chart_json,
    )
    assert response.status_code == assert_code

    if not skip_preview:
        now = datetime.now(timezone.utc) + timedelta(seconds=1)
        response = await http_tenant.post(
            f"{prefix}/{chart_id}",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        chart_resp = response.json()
        chart_preview = schemas.CustomChartCreate(
            series=[schemas.CustomChartSeriesCreate(**s) for s in chart_resp["series"]]
            if isinstance(chart.series, schemas.Missing)
            else chart.series,
            common_filters=(
                schemas.CustomChartSeriesFilters(**chart_resp["common_filters"])
                if chart_resp["common_filters"]
                else None
            )
            if isinstance(chart.common_filters, schemas.Missing)
            else chart.common_filters,
            title="foo",
            chart_type="line",
        )
        await _assert_chart_preview_response(
            http_tenant, prefix, chart_preview, assert_code
        )


def convert_bucket_info_to_list_of_timestamps(
    bucket_info: schemas.CustomChartsRequest,
) -> list[str]:
    curr_time = bucket_info.start_time
    timestamps: list[str] = []
    if bucket_info.end_time is None:
        raise ValueError("end_time must be set")
    while curr_time < bucket_info.end_time:
        bucket_time = curr_time.replace(microsecond=0)
        timestamps.append(bucket_time.strftime("%Y-%m-%dT%H:%M:%S"))
        curr_time += timedelta(
            minutes=bucket_info.stride.minutes,
            hours=bucket_info.stride.hours,
            days=bucket_info.stride.days,
        )
    return timestamps


async def _assert_multi_chart(
    http_tenant: AsyncClient,
    tenant_id: str,
    prefix: str,
    chart_id: str | None = None,
    assertions: dict[str, Any] = {},
    data: list[list[Any]] | None = None,
    bucket_info: schemas.CustomChartsRequest | None = None,
    series_ids: list[str] | None = None,
    section_id: str | None = None,
    after_index: int | None = None,
    does_not_contain_section_id: list[str] | None = None,
    does_not_contain_chart_id: list[str] | None = None,
    headers: dict[str, str] | None = None,
    tag_value_id: list[str] | None = None,
    ignore_cache: bool = False,
) -> None:
    now = (
        (datetime.now(timezone.utc) + timedelta(seconds=1))
        if not _chart_start_time.get(tenant_id)
        else _chart_start_time[tenant_id]
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second

    if not _chart_start_time.get(tenant_id):
        _chart_start_time[tenant_id] = now

    # Create a cache key from the request parameters
    request_payload = {
        "start_time": (now - timedelta(hours=10)).isoformat()
        if bucket_info is None
        else bucket_info.start_time.isoformat(),
        "end_time": now.isoformat()
        if bucket_info is None or bucket_info.end_time is None
        else bucket_info.end_time.isoformat(),
        "stride": {
            "hours": 1,
        }
        if bucket_info is None
        else {
            f"{get_timedelta_unit(bucket_info.stride)}s": get_timedelta_value(
                bucket_info.stride
            ),
        },
        "after_index": after_index,
        "tag_value_id": tag_value_id,
    }
    # Use id(http_tenant) to differentiate between different client instances
    cache_key = (tenant_id, prefix, str(request_payload))

    # Check if we have a cached response
    if cache_key in _chart_request_cache and not ignore_cache:
        response_data = _chart_request_cache[cache_key]
        response_headers = _chart_request_headers[cache_key]
    else:
        # Perform the actual request if no cache hit
        response = await http_tenant.post(prefix, json=request_payload)
        assert response.status_code == 200
        response_data = response.json()
        response_headers = response.headers
        # Cache the response
        _chart_request_cache[cache_key] = response_data
        _chart_request_headers[cache_key] = response.headers

    # Process the response data (cached or new)
    sections = response_data["sections"]
    if does_not_contain_section_id:
        for s_id in does_not_contain_section_id:
            assert s_id not in [s["id"] for s in sections]
    all_charts = []
    for section in sections:
        all_charts.extend(section["charts"])
    if does_not_contain_chart_id:
        for c_id in does_not_contain_chart_id:
            assert c_id not in [c["id"] for c in all_charts]
    chart = next((s for s in all_charts if s["id"] == chart_id), None)
    if chart_id is not None and chart is None:
        raise ValueError(f"Chart with id {chart_id} not found")
    if chart is not None:
        for key, value in assertions.items():
            assert chart[key] == value
    if headers is not None:
        for key, value in headers.items():
            assert response_headers[key] == value
    if section_id is not None:
        curr_section: dict = next(
            (s for s in sections if chart_id in [c["id"] for c in s["charts"]]), {}
        )
        assert curr_section.get("id") == section_id
    if data is not None and bucket_info is not None and series_ids is not None:
        timestamps = convert_bucket_info_to_list_of_timestamps(bucket_info)
        for datum, series_id in zip(data, series_ids):
            assert len(
                [c for c in chart["data"] if c["series_id"] == series_id]
            ) == len(datum)
            for d, t in zip(datum, timestamps):
                bucket = next(
                    (
                        b
                        for b in chart["data"]
                        if b["timestamp"] == t and b["series_id"] == series_id
                    ),
                    None,
                )
                assert bucket is not None
                assert bucket["value"] == d
                assert bucket["series_id"] == series_id
    elif (
        bucket_info is not None
        or series_ids is not None
        or data is not None
        and not (bucket_info is None and series_ids is None and data is None)
    ):
        raise ValueError(
            "data, bucket_info, and series_ids must all be set or all be None"
        )


async def _assert_chart_preview_response(
    http_tenant: AsyncClient,
    prefix: str,
    chart: schemas.CustomChartCreate,
    assert_code: int = 200,
) -> None:
    "Ensure that the chart preview endpoint returns the same status code as it would if you were creating the chart"

    chart_json: dict = {
        "chart": {
            "series": [s.model_dump() for s in chart.series],
            "common_filters": chart.common_filters.model_dump()
            if chart.common_filters
            else None,
        },
        "bucket_info": {
            "start_time": (
                datetime.now(timezone.utc) - timedelta(hours=10)
            ).isoformat(),
            "end_time": datetime.now(timezone.utc).isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    }
    for s in chart_json["chart"]["series"]:
        if s["filters"].get("session"):
            s["filters"]["session"] = [
                str(session_id) for session_id in s["filters"]["session"]
            ]
        s["id"] = str(uuid4())
    if chart_json["chart"]["common_filters"] is not None:
        if chart_json["chart"]["common_filters"].get("session"):
            chart_json["chart"]["common_filters"]["session"] = [
                str(session_id)
                for session_id in chart_json["chart"]["common_filters"]["session"]
            ]
    response = await http_tenant.post(
        f"{prefix}/preview",
        json=chart_json,
    )
    assert response.status_code == assert_code


async def _assert_chart(
    http_tenant: AsyncClient,
    tenant_id: str,
    prefix: str,
    chart_id: str,
    assertions: dict[str, Any],
    data: list[list[Any]] | None = None,
    bucket_info: schemas.CustomChartsRequest | None = None,
    series_ids: list[str] | None = None,
    section_id: str | None = None,
    ignore_cache: bool = False,
) -> None:
    await _assert_single_chart(
        http_tenant, prefix, chart_id, assertions, data, bucket_info, series_ids
    )
    await _assert_multi_chart(
        http_tenant,
        tenant_id,
        prefix,
        chart_id,
        assertions,
        data,
        bucket_info,
        series_ids,
        section_id,
        ignore_cache=ignore_cache,
    )


async def _assert_single_chart(
    http_tenant: AsyncClient,
    prefix: str,
    chart_id: str,
    assertions: dict[str, Any],
    data: list[list[Any]] | None = None,
    bucket_info: schemas.CustomChartsRequest | None = None,
    series_ids: list[str] | None = None,
) -> None:
    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    response = await http_tenant.post(
        f"{prefix}/{chart_id}",
        json={
            "start_time": (now - timedelta(hours=10)).isoformat()
            if bucket_info is None
            else bucket_info.start_time.isoformat(),
            "end_time": now.isoformat()
            if bucket_info is None or bucket_info.end_time is None
            else bucket_info.end_time.isoformat(),
            "stride": {
                "hours": 1,
            }
            if bucket_info is None
            else {
                f"{get_timedelta_unit(bucket_info.stride)}s": get_timedelta_value(
                    bucket_info.stride
                ),
            },
        },
    )
    assert response.status_code == 200
    chart = response.json()
    for key, value in assertions.items():
        assert chart[key] == value
    if data is not None and bucket_info is not None and series_ids is not None:
        timestamps = convert_bucket_info_to_list_of_timestamps(bucket_info)
        for datum, series_id in zip(data, series_ids):
            assert len(
                [c for c in chart["data"] if c["series_id"] == series_id]
            ) == len(datum)
            for d, t in zip(datum, timestamps):
                bucket = next(
                    (
                        b
                        for b in chart["data"]
                        if b["timestamp"] == t and b["series_id"] == series_id
                    ),
                    None,
                )
                assert bucket is not None
                assert bucket["series_id"] == series_id
                assert bucket["value"] == d, f"Iteration failed for {t} {series_id=}"

        # test chart preview here as well
        response = await http_tenant.post(
            f"{prefix}/preview",
            json={
                "chart": {
                    "series": chart["series"],
                    "common_filters": chart["common_filters"],
                },
                "bucket_info": {
                    "start_time": (now - timedelta(hours=10)).isoformat()
                    if bucket_info is None
                    else bucket_info.start_time.isoformat(),
                    "end_time": now.isoformat()
                    if bucket_info is None or bucket_info.end_time is None
                    else bucket_info.end_time.isoformat(),
                    "stride": {
                        "hours": 1,
                    }
                    if bucket_info is None
                    else {
                        f"{get_timedelta_unit(bucket_info.stride)}s": get_timedelta_value(
                            bucket_info.stride
                        ),
                    },
                },
            },
        )
        assert response.status_code == 200
        chart = response.json()
        timestamps = convert_bucket_info_to_list_of_timestamps(bucket_info)
        for datum, series_id in zip(data, series_ids):
            assert len(
                [c for c in chart["data"] if c["series_id"] == series_id]
            ) == len(datum)
            for d, t in zip(datum, timestamps):
                bucket = next(
                    (
                        b
                        for b in chart["data"]
                        if b["timestamp"] == t and b["series_id"] == series_id
                    ),
                    None,
                )
                assert bucket is not None
                assert bucket["series_id"] == series_id
                assert bucket["value"] == d, f"Iteration failed for {t} {series_id=}"
    elif (
        bucket_info is not None
        or series_ids is not None
        or data is not None
        and not (bucket_info is None and series_ids is None and data is None)
    ):
        raise ValueError(
            "data, bucket_info, and series_ids must all be set or all be None"
        )


async def _patch_section(
    http_tenant: AsyncClient,
    prefix: str,
    section_id: str,
    section: schemas.CustomChartsSectionUpdate,
) -> None:
    section_json = section.model_dump()
    response = await http_tenant.patch(
        f"{prefix}/section/{section_id}",
        json=section_json,
    )
    assert response.status_code == 200


async def _assert_section(
    http_tenant: AsyncClient, prefix: str, section_id: str, assertions: dict[str, Any]
) -> None:
    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    response = await http_tenant.post(
        prefix,
        json={
            "start_time": (now - timedelta(hours=10)).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200
    sections = response.json()["sections"]
    section = next(s for s in sections if s["id"] == section_id)
    for key, value in assertions.items():
        assert section[key] == value

    # verify single section here as well
    response = await http_tenant.post(
        f"{prefix}/section/{section_id}",
        json={
            "start_time": (now - timedelta(hours=10)).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200
    section = response.json()
    for key, value in assertions.items():
        assert section[key] == value


async def _create_chart(
    http_tenant: AsyncClient,
    prefix: str,
    chart: schemas.CustomChartCreate,
    assert_code: int = 200,
    skip_preview: bool = False,
) -> dict[str, Any]:
    series = [s.model_dump() for s in chart.series]
    chart_json = chart.model_dump()
    chart_json["series"] = series
    for s in chart_json["series"]:
        if s["filters"].get("session"):
            s["filters"]["session"] = [
                str(session_id) for session_id in s["filters"]["session"]
            ]
    chart_json["section_id"] = (
        str(chart.section_id) if chart.section_id is not None else None
    )
    if chart_json["common_filters"] is not None:
        if chart_json["common_filters"].get("session"):
            chart_json["common_filters"]["session"] = [
                str(session_id)
                for session_id in chart_json["common_filters"]["session"]
            ]
    response = await http_tenant.post(
        f"{prefix}/create",
        json=chart_json,
    )
    assert response.status_code == assert_code, response.text

    if not skip_preview:
        # We should throw the same status code on the preview endpoint as the creation endpoint - anything disallowed on one should be on the other
        await _assert_chart_preview_response(http_tenant, prefix, chart, assert_code)

    return response.json()


async def _create_section(
    http_tenant: AsyncClient, prefix: str, section: schemas.CustomChartsSectionCreate
) -> str:
    response = await http_tenant.post(
        f"{prefix}/section",
        json=section.model_dump(),
    )
    assert response.status_code == 200
    return response.json()["id"]


async def _delete_chart(http_tenant: AsyncClient, prefix: str, chart_id: str) -> None:
    response = await http_tenant.delete(
        f"{prefix}/{chart_id}",
    )
    assert response.status_code == 200


async def _delete_section(
    http_tenant: AsyncClient, prefix: str, section_id: str
) -> None:
    response = await http_tenant.delete(
        f"{prefix}/section/{section_id}",
    )
    assert response.status_code == 200


async def _clear_all_charts(http_tenant: AsyncClient, prefix: str) -> None:
    while True:
        response = await http_tenant.post(
            prefix,
            json={
                "start_time": (
                    datetime.now(timezone.utc) - timedelta(hours=8)
                ).isoformat(),
                "end_time": datetime.now(timezone.utc).isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        sections = response.json()["sections"]
        if not sections:
            break
        for section in sections:
            await _delete_section(
                http_tenant, prefix, section["id"]
            )  # Will cascade delete charts


async def _create_session(http_tenant: AsyncClient) -> str:
    session_id = str(uuid4())
    response = await http_tenant.post(
        "/sessions",
        json={"name": random_lower_string(), "id": session_id},
    )
    assert response.status_code == 200
    return session_id


async def _assert_chart_in_section(
    http_tenant: AsyncClient,
    prefix: str,
    section_id: str,
    chart_id: str,
) -> None:
    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    response = await http_tenant.post(
        prefix,
        json={
            "start_time": (now - timedelta(hours=10)).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200
    sections = response.json()["sections"]
    section = next(s for s in sections if s["id"] == section_id)
    assert chart_id in [c["id"] for c in section["charts"]]


async def _run_common_tests(
    client: AsyncClient,
    tenant_id: str,
    prefix: str,
    wait_until_task_queue_empty: Callable[[], Awaitable[None]],
    chart_name: str,
    chart_description: str,
    series_name: str,
) -> tuple[list[dict[str, Any]], str, str, str, str, str]:
    """
    Runs tests common to both workspace and org scoped charts, resulting in 2 charts & sections and 8 runs.
    Returns new_runs, chart_id, other_chart_id, section_id, new_section_id, session_id
    """
    session_id = str(uuid4())
    response = await client.post(
        "/sessions",
        json={"name": random_lower_string(), "id": session_id},
    )
    assert response.status_code == 200, response.text

    session_id_2 = str(uuid4())
    response = await client.post(
        "/sessions",
        json={"name": random_lower_string(), "id": session_id_2},
    )
    assert response.status_code == 200, response.text

    response = await client.post(
        f"{prefix}/create",
        json={
            "title": random_lower_string(),
            "description": "test",
            "index": 0,
            "chart_type": "line",
            "metadata": {"key": "value"},
            "section_id": str(uuid4()),
            "series": [
                {
                    "name": random_lower_string(),
                    "filters": {"session": [session_id]},
                    "metric": "run_count",
                }
            ],
        },
    )
    assert response.status_code == 404, response.text  # Section not found

    response = await client.post(
        f"{prefix}/create",
        json={
            "title": random_lower_string(),
            "description": "test",
            "index": 0,
            "chart_type": "line",
            "metadata": {"key": "value"},
            "series": [
                {
                    "name": random_lower_string(),
                    "filters": {
                        "session": [str(uuid4())],  # invalid session
                        "filter": "eq(is_root, true)",
                    },
                    "metric": "run_count",
                }
            ],
        },
    )
    assert response.status_code == 404, response.text

    response = await client.post(
        f"{prefix}/create",
        json={
            "title": random_lower_string(),
            "description": "test",
            "index": 0,
            "chart_type": "line",
            "metadata": {"key": "value"},
            "series": [
                {
                    "name": random_lower_string(),
                    "filters": {
                        "session": [session_id],
                        "filter": "eq(is_root, true",  # invalid filter
                    },
                    "metric": "run_count",
                }
            ],
        },
    )
    assert response.status_code == 400, response.text

    response = await client.post(
        f"{prefix}/create",
        json={
            "title": random_lower_string(),
            "description": "test",
            "chart_type": "line",
            "metadata": {"key": "value"},
            "series": [
                {
                    "name": random_lower_string(),
                    "filters": {
                        "session": [session_id_2],
                        "filter": "eq(is_root, true)",
                    },
                    "metric": "median_tokens",
                }
            ],
        },
    )
    assert response.status_code == 200, response.text
    section_id = response.json()["section_id"]
    other_chart_id = response.json()["id"]

    response = await client.post(
        f"{prefix}/create",
        json={
            "title": chart_name,
            "description": chart_description,
            "chart_type": "line",
            "metadata": {"foo": "bar"},
            "section_id": None,
            "index": None,
            "series": [
                {
                    "name": series_name,
                    "filters": {
                        "session": [session_id],
                        "filter": "eq(is_root, true)",
                        "trace_filter": None,
                        "tree_filter": None,
                    },
                    "metric": "run_count",
                }
            ],
        },
    )
    assert response.status_code == 200, response.text
    section_id_2 = response.json()["section_id"]
    assert section_id == section_id_2
    assert response.json()["index"] == 1
    chart_id = response.json()["id"]

    # Create the runs
    new_runs = []
    for i in range(9):
        if i == 5:
            continue
        run_id = uuid4()
        start_time = datetime.now(timezone.utc) - timedelta(hours=i)
        end_time = start_time + timedelta(seconds=i)
        new_runs.append(
            {
                "name": f"AgentExecutor{i}",
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "Agent"},
                "inputs": {"input": f"input{i}"},
                "session_id": session_id,
                "run_type": "chain",
                "id": str(run_id),
                "trace_id": str(run_id),
                "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
            },
        )

    response = await client.post(
        "/runs/batch",
        json={
            "post": new_runs,
        },
    )
    assert response.status_code == 202, response.text
    await _wait_for_write_queue(wait_until_task_queue_empty, tenant_id)

    assert len(new_runs) == 8

    # make sure you can't fetch more than 200 buckets
    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    start_time = now - timedelta(hours=4)
    response = await client.post(
        prefix,
        json={
            "start_time": start_time.isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "minutes": 1,
            },
        },
    )
    assert response.status_code == 200, response.text

    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    start_time = now - timedelta(hours=10)
    response = await client.post(
        prefix,
        json={
            "start_time": start_time.isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200, response.text
    sections = response.json()["sections"]
    assert len(sections) == 1
    charts = sections[0]["charts"]
    assert len(charts) == 2
    chart = next(c for c in charts if c["id"] == chart_id)
    assert chart["title"] == chart_name
    assert chart["description"] == chart_description
    assert chart["chart_type"] == "line"
    assert chart["metadata"] == {"foo": "bar"}
    assert chart["index"] == 1
    series = chart["series"]
    assert len(series) == 1
    series = series[0]
    assert series["name"] == series_name
    assert series["filters"] == {
        "session": [session_id],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert series["metric"] == "run_count"
    data = chart["data"]
    assert len(data) == 10

    bucket_values: list[Any] = [0, 1, 1, 1, 0, 1, 1, 1, 1, 1]
    await _assert_chart(
        client,
        tenant_id,
        prefix,
        chart_id,
        {},
        [bucket_values],
        schemas.CustomChartsRequest(
            start_time=start_time,
            end_time=now,
            stride=schemas.TimedeltaInput(hours=1),
        ),
        [series["id"]],
        ignore_cache=True,
    )

    other_chart = next(c for c in charts if c["id"] != chart_id)
    assert other_chart["index"] == 0
    assert other_chart["id"] == other_chart_id
    assert other_chart["metadata"] == {"key": "value"}
    assert other_chart["chart_type"] == "line"
    assert other_chart["description"] == "test"
    series = other_chart["series"]
    assert len(series) == 1
    series = series[0]
    assert series["filters"] == {
        "session": [session_id_2],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert series["metric"] == "median_tokens"
    data = other_chart["data"]

    bucket_values = [0] * 10
    await _assert_chart(
        client,
        tenant_id,
        prefix,
        other_chart_id,
        {},
        [bucket_values],
        schemas.CustomChartsRequest(
            start_time=start_time,
            end_time=now,
            stride=schemas.TimedeltaInput(hours=1),
        ),
        [series["id"]],
    )

    await _patch_chart(
        client,
        prefix,
        other_chart_id,
        schemas.CustomChartUpdate(
            series=[
                {
                    "name": series_name,
                    "filters": {
                        "session": [session_id_2],
                        "filter": "eq(is_root, true)",
                        "trace_filter": None,
                        "tree_filter": None,
                    },
                    "metric": "run_count",
                }
            ]
        ),
    )

    response = await client.post(
        prefix,
        json={
            "start_time": start_time.isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )

    charts = response.json()["sections"][0]["charts"]
    other_chart = next(c for c in charts if c["id"] != chart_id)
    assert other_chart["index"] == 0
    assert other_chart["id"] == other_chart_id
    assert other_chart["metadata"] == {"key": "value"}
    assert other_chart["chart_type"] == "line"
    assert other_chart["description"] == "test"
    series = other_chart["series"]
    assert len(series) == 1
    series = series[0]
    assert series["filters"] == {
        "session": [session_id_2],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert series["metric"] == "run_count"
    data = other_chart["data"]

    bucket_values = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    await _assert_chart(
        client,
        tenant_id,
        prefix,
        other_chart_id,
        {},
        [bucket_values],
        schemas.CustomChartsRequest(
            start_time=start_time,
            end_time=now,
            stride=schemas.TimedeltaInput(hours=1),
        ),
        [series["id"]],
        ignore_cache=True,
    )

    await _patch_chart(
        client,
        prefix,
        other_chart_id,
        schemas.CustomChartUpdate(
            series=[
                {
                    "name": series_name,
                    "filters": {
                        "session": [session_id_2],
                        "filter": "eq(is_root, true)",
                        "trace_filter": None,
                        "tree_filter": None,
                    },
                    "metric": "latency_p99",
                }
            ]
        ),
    )

    response = await client.post(
        prefix,
        json={
            "start_time": start_time.isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )

    charts = response.json()["sections"][0]["charts"]
    other_chart = next(c for c in charts if c["id"] != chart_id)
    assert other_chart["index"] == 0
    assert other_chart["id"] == other_chart_id
    assert other_chart["metadata"] == {"key": "value"}
    assert other_chart["chart_type"] == "line"
    assert other_chart["description"] == "test"
    series = other_chart["series"]
    assert len(series) == 1
    series = series[0]
    assert series["filters"] == {
        "session": [session_id_2],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert series["metric"] == "latency_p99"
    data = other_chart["data"]

    bucket_values = [None, None, None, None, None, None, None, None, None, None]
    await _assert_chart(
        client,
        tenant_id,
        prefix,
        other_chart_id,
        {},
        [bucket_values],
        schemas.CustomChartsRequest(
            start_time=start_time,
            end_time=now,
            stride=schemas.TimedeltaInput(hours=1),
        ),
        [series["id"]],
        ignore_cache=True,
    )

    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    response = await client.post(
        prefix,
        json={
            "start_time": (now - timedelta(hours=1)).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "minutes": 3,
            },
        },
    )

    assert response.status_code == 200
    sections = response.json()["sections"]
    assert len(sections) == 1
    charts = sections[0]["charts"]
    assert len(charts) == 2
    chart = next(c for c in charts if c["id"] == chart_id)
    assert chart["title"] == chart_name
    assert chart["description"] == chart_description
    assert chart["chart_type"] == "line"
    assert chart["metadata"] == {"foo": "bar"}
    assert chart["index"] == 1
    series = chart["series"]
    assert len(series) == 1
    series = series[0]
    assert series["name"] == series_name
    assert series["filters"] == {
        "session": [session_id],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert series["metric"] == "run_count"
    data = chart["data"]
    assert len(data) == 20

    response = await client.post(
        f"{prefix}/section",
        json={
            "title": random_lower_string(),
            "description": "test",
            "index": 1,
        },
    )
    assert response.status_code == 200
    new_section_id = response.json()["id"]

    new_chart_name = random_lower_string()
    new_chart_description = random_lower_string()
    new_series_name = random_lower_string()
    response = await client.post(
        f"{prefix}/create",
        json={
            "title": new_chart_name,
            "description": new_chart_description,
            "chart_type": "line",
            "section_id": new_section_id,
            "series": [
                {
                    "name": new_series_name,
                    "filters": {
                        "session": [session_id_2],
                        "filter": "eq(is_root, true)",
                    },
                    "metric": "latency_p99",
                }
            ],
        },
    )
    assert response.status_code == 200
    assert new_section_id == response.json()["section_id"]
    new_chart_id = response.json()["id"]

    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    response = await client.post(
        prefix,
        json={
            "start_time": (now - timedelta(hours=1)).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "minutes": 3,
            },
        },
    )

    assert response.status_code == 200
    sections = response.json()["sections"]
    assert len(sections) == 2
    section = next(s for s in sections if s["id"] == new_section_id)
    charts = section["charts"]
    assert len(charts) == 1
    chart = next(c for c in charts if c["id"] == new_chart_id)
    assert chart["title"] == new_chart_name
    assert chart["description"] == new_chart_description
    assert chart["chart_type"] == "line"
    assert chart["metadata"] is None
    assert chart["index"] == 0
    series = chart["series"]
    assert len(series) == 1
    series = series[0]
    assert series["name"] == new_series_name
    assert series["filters"] == {
        "session": [session_id_2],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert series["metric"] == "latency_p99"
    data = chart["data"]
    assert len(data) == 20

    # PATCH the chart to have two series, both with new ids
    patch_series_name = random_lower_string()
    response = await client.patch(
        f"{prefix}/{chart_id}",
        json={
            "series": [
                {
                    "name": series_name,
                    "filters": {
                        "session": [session_id],
                        "filter": "eq(is_root, true)",
                    },
                    "metric": "run_count",
                },
                {
                    "name": patch_series_name,
                    "filters": {
                        "session": [session_id],
                        "filter": "and(eq(is_root, true), lte(latency, 5))",
                    },
                    "metric": "latency_p99",
                },
            ],
        },
    )
    assert response.status_code == 200

    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    start_time = now - timedelta(hours=10)
    response = await client.post(
        prefix,
        json={
            "start_time": (start_time).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200
    sections = response.json()["sections"]
    assert len(sections) == 2
    section = next(s for s in sections if s["id"] == section_id)
    charts = section["charts"]
    assert len(charts) == 2
    chart = next(c for c in charts if c["id"] == chart_id)
    assert chart["title"] == chart_name
    assert chart["description"] == chart_description
    assert chart["chart_type"] == "line"
    assert chart["metadata"] == {"foo": "bar"}
    assert chart["index"] == 1
    series = chart["series"]
    assert len(series) == 2
    root_series = next(s for s in series if s["name"] == series_name)
    assert root_series["filters"] == {
        "session": [session_id],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert root_series["metric"] == "run_count"

    latency_series = next(s for s in series if s["name"] == patch_series_name)
    assert latency_series["filters"] == {
        "session": [session_id],
        "filter": "and(eq(is_root, true), lte(latency, 5))",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert latency_series["metric"] == "latency_p99"
    data = chart["data"]
    assert len(data) == 20  # because two series now

    bucket_values_1 = [0, 1, 1, 1, 0, 1, 1, 1, 1, 1]
    bucket_values_2 = [
        None,
        None,
        None,
        None,
        None,
        4,
        3,
        2,
        1,
        0,
    ]  # the first three runs have latency > 5
    await _assert_chart(
        client,
        tenant_id,
        prefix,
        chart_id,
        {},
        [bucket_values_1, bucket_values_2],
        schemas.CustomChartsRequest(
            start_time=start_time,
            end_time=now,
            stride=schemas.TimedeltaInput(hours=1),
        ),
        [root_series["id"], latency_series["id"]],
        ignore_cache=True,
    )

    patch_series_name = random_lower_string()
    response = await client.patch(
        f"{prefix}/{chart_id}",
        json={
            "common_filters": {"filter": "gte(latency, 2)"},
            "series": [
                {
                    "name": series_name,
                    "filters": {
                        "session": [session_id],
                        "filter": "eq(is_root, true)",
                    },
                    "metric": "run_count",
                },
                {
                    "name": patch_series_name,
                    "filters": {
                        "session": [session_id],
                        "filter": "and(eq(is_root, true), lte(latency, 5))",
                    },
                    "metric": "latency_p99",
                },
            ],
        },
    )
    assert response.status_code == 200

    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    start_time = now - timedelta(hours=10)
    response = await client.post(
        prefix,
        json={
            "start_time": (start_time).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200
    sections = response.json()["sections"]
    assert len(sections) == 2
    section = next(s for s in sections if s["id"] == section_id)
    charts = section["charts"]
    assert len(charts) == 2
    chart = next(c for c in charts if c["id"] == chart_id)
    assert chart["title"] == chart_name
    assert chart["description"] == chart_description
    assert chart["chart_type"] == "line"
    assert chart["metadata"] == {"foo": "bar"}
    assert chart["index"] == 1
    series = chart["series"]
    assert len(series) == 2
    root_series = next(s for s in series if s["name"] == series_name)
    assert root_series["filters"] == {
        "session": [session_id],
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert chart["common_filters"] == {
        "filter": "gte(latency, 2)",
        "session": None,
        "trace_filter": None,
        "tree_filter": None,
    }
    assert root_series["metric"] == "run_count"

    latency_series = next(s for s in series if s["name"] == patch_series_name)
    assert latency_series["filters"] == {
        "session": [session_id],
        "filter": "and(eq(is_root, true), lte(latency, 5))",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert latency_series["metric"] == "latency_p99"
    data = chart["data"]
    assert len(data) == 20  # because two series now

    bucket_values_1 = [0, 1, 1, 1, 0, 1, 1, 1, 0, 0]
    bucket_values_2 = [
        None,
        None,
        None,
        None,
        None,
        4,
        3,
        2,
        None,
        None,
    ]  # the last two runs have latency < 2
    await _assert_chart(
        client,
        tenant_id,
        prefix,
        chart_id,
        {},
        [bucket_values_1, bucket_values_2],
        schemas.CustomChartsRequest(
            start_time=start_time,
            end_time=now,
            stride=schemas.TimedeltaInput(hours=1),
        ),
        [root_series["id"], latency_series["id"]],
        ignore_cache=True,
    )

    # Test moving charts around by index
    response = await client.patch(
        f"{prefix}/{chart_id}",
        json={
            "index": 0,
        },
    )
    assert response.status_code == 200, response.text

    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    response = await client.post(
        prefix,
        json={
            "start_time": (now - timedelta(hours=10)).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200
    sections = response.json()["sections"]
    assert len(sections) == 2
    section = next(s for s in sections if s["id"] == section_id)
    charts = section["charts"]
    assert len(charts) == 2
    chart = next(c for c in charts if c["id"] == chart_id)
    assert chart["title"] == chart_name
    assert chart["description"] == chart_description
    assert chart["chart_type"] == "line"
    assert chart["metadata"] == {"foo": "bar"}
    assert chart["index"] == 0
    other_chart = next(c for c in charts if c["id"] != chart_id)
    assert other_chart["index"] == 1

    response = await client.patch(
        f"{prefix}/{chart_id}",
        json={
            "index": 1,
        },
    )
    assert response.status_code == 200

    now = (
        datetime.now(timezone.utc) + timedelta(seconds=1)
    )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
    response = await client.post(
        prefix,
        json={
            "start_time": (now - timedelta(hours=10)).isoformat(),
            "end_time": now.isoformat(),
            "stride": {
                "hours": 1,
            },
        },
    )
    assert response.status_code == 200
    sections = response.json()["sections"]
    assert len(sections) == 2
    section = next(s for s in sections if s["id"] == section_id)
    charts = section["charts"]
    assert len(charts) == 2
    chart = next(c for c in charts if c["id"] == chart_id)
    assert chart["title"] == chart_name
    assert chart["description"] == chart_description
    assert chart["chart_type"] == "line"
    assert chart["metadata"] == {"foo": "bar"}
    assert chart["index"] == 1
    other_chart = next(c for c in charts if c["id"] != chart_id)
    assert other_chart["index"] == 0
    other_chart_id = other_chart["id"]

    return (
        new_runs,
        str(chart_id),
        str(other_chart_id),
        str(section_id),
        str(new_section_id),
        session_id,
    )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_chart(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a workspace-scoped chart can be created and read."""
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[False]
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        http_tenant_one = authed_client.client

        series_422 = [
            # Invalid metric
            {
                "name": random_lower_string(),
                "filters": {"filter": "eq(is_root, true)"},
                "metric": random_lower_string(),
            },
            # invalid filters
            {
                "name": random_lower_string(),
                "filters": {"key": "value"},
                "metric": "run_count",
            },
            # invalid filters
            {
                "name": random_lower_string(),
                "filters": {"filter": "eq(asdf, true)"},
                "metric": "run_count",
            },
            # missing session within filters
            {
                "name": random_lower_string(),
                "filters": {"filter": "eq(is_root, true)"},
                "metric": "run_count",
            },
            # empty session array within filters
            {
                "name": random_lower_string(),
                "filters": {"filter": "eq(is_root, true)", "session": []},
                "metric": "run_count",
            },
        ]
        for s in series_422:
            response = await http_tenant_one.post(
                f"{prefix}/create",
                json={
                    "title": random_lower_string(),
                    "description": "test",
                    "index": 0,
                    "chart_type": "line",
                    "metadata": {"key": "value"},
                    "section_id": str(uuid4()),
                    "series": [s],
                },
            )
            assert response.status_code == 422

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        (
            new_runs,
            chart_id,
            other_chart_id,
            section_id,
            new_section_id,
            session_id,
        ) = await _run_common_tests(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            wait_until_task_queue_empty,
            chart_name,
            chart_description,
            series_name,
        )

        # Test feedback charts, which are only enabled for workspace-scoped charts
        # need to test:
        # feedback
        # feedback_score_avg
        # feedback_values

        feedback_scores = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
        feedback_values = [
            "blue",
            "blue",
            "red",
            "blue",
            "green",
            "red",
            "blue",
            "blue",
        ]
        for i, run in enumerate(new_runs):
            response = await http_tenant_one.post(
                "/feedback",
                json={
                    "run_id": run["id"],
                    "key": "correctness",
                    "score": feedback_scores[i],
                },
            )
            assert response.status_code == 200
            response = await http_tenant_one.post(
                "/feedback",
                json={
                    "run_id": run["id"],
                    "key": "color",
                    "value": feedback_values[i],
                },
            )
            assert response.status_code == 200
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )

        patch_series_name = random_lower_string()
        other_patch_series_name = random_lower_string()
        third_patch_series_name = random_lower_string()
        response = await http_tenant_one.patch(
            f"{prefix}/{chart_id}",
            json={
                "common_filters": None,
                "series": [
                    {
                        "name": patch_series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        "metric": "feedback",
                        "feedback_key": "correctness",
                    },
                    {
                        "name": other_patch_series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        "metric": "feedback_values",
                        "feedback_key": "color",
                    },
                    {
                        "name": third_patch_series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        "metric": "feedback_score_avg",
                        "feedback_key": "correctness",
                    },
                ],
            },
        )
        assert response.status_code == 200

        now = (
            datetime.now(timezone.utc) + timedelta(seconds=1)
        )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
        start_time = now - timedelta(hours=10)
        response = await http_tenant_one.post(
            prefix,
            json={
                "start_time": start_time.isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        sections = response.json()["sections"]
        assert len(sections) == 2
        section = next(s for s in sections if s["id"] == section_id)
        charts = section["charts"]
        assert len(charts) == 2
        chart = next(c for c in charts if c["id"] == chart_id)
        assert chart["title"] == chart_name
        assert chart["description"] == chart_description
        assert chart["chart_type"] == "line"
        assert chart["metadata"] == {"foo": "bar"}
        assert chart["index"] == 1
        series = chart["series"]
        assert len(series) == 3
        root_series = next(s for s in series if s["name"] == patch_series_name)
        assert root_series["filters"] == {
            "session": [session_id],
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert root_series["metric"] == "feedback"
        assert root_series["feedback_key"] == "correctness"

        color_series = next(s for s in series if s["name"] == other_patch_series_name)
        assert color_series["filters"] == {
            "session": [session_id],
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert color_series["metric"] == "feedback_values"
        assert color_series["feedback_key"] == "color"

        avg_series = next(s for s in series if s["name"] == third_patch_series_name)
        assert avg_series["filters"] == {
            "session": [session_id],
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert avg_series["metric"] == "feedback_score_avg"
        assert avg_series["feedback_key"] == "correctness"

        data = chart["data"]
        assert len(data) == 30

        correctness_values = [
            {
                "correctness": {
                    "n": 0,
                    "avg": None,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.8,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.7,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.6,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 0,
                    "avg": None,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.5,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.4,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.3,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.2,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
            {
                "correctness": {
                    "n": 1,
                    "avg": 0.1,
                    "stdev": 0.0,
                    "errors": 0,
                    "values": {},
                },
            },
        ]
        color_values = [
            {
                "color": {
                    "n": 0,
                    "values": {},
                    "avg": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"blue": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"blue": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"red": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
            {
                "color": {
                    "n": 0,
                    "values": {},
                    "avg": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"green": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"blue": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"red": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"blue": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
            {
                "color": {
                    "n": 1,
                    "values": {"blue": 1},
                    "errors": 0,
                    "avg": None,
                    "stdev": None,
                },
            },
        ]
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {},
            [correctness_values, color_values, correctness_values],
            schemas.CustomChartsRequest(
                start_time=start_time,
                end_time=now,
                stride=schemas.TimedeltaInput(hours=1),
            ),
            [root_series["id"], color_series["id"], avg_series["id"]],
            section_id,
        )

        # Test moving sections around
        new_section_name = random_lower_string()
        response = await http_tenant_one.patch(
            f"{prefix}/section/{str(new_section_id)}",
            json={
                "title": new_section_name,
                "index": 0,
            },
        )
        assert response.status_code == 200

        response = await http_tenant_one.post(
            prefix,
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        sections = response.json()["sections"]
        assert len(sections) == 2
        section = next(s for s in sections if s["id"] == new_section_id)
        assert section["title"] == new_section_name
        assert section["index"] == 0
        assert section["description"] == "test"

        section = next(s for s in sections if s["id"] == section_id)
        assert section["title"] == "Dashboard 1"
        assert section["index"] == 1
        assert section["description"] is None

        response = await http_tenant_one.patch(
            f"{prefix}/section/{str(new_section_id)}",
            json={
                "index": 1,
            },
        )
        assert response.status_code == 200

        response = await http_tenant_one.post(
            prefix,
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        sections = response.json()["sections"]
        assert len(sections) == 2
        section = next(s for s in sections if s["id"] == new_section_id)
        assert section["title"] == new_section_name
        assert section["index"] == 1
        assert section["description"] == "test"

        section = next(s for s in sections if s["id"] == section_id)
        assert section["title"] == "Dashboard 1"
        assert section["index"] == 0  #
        assert section["description"] is None

        # Test deleting a chart
        response = await http_tenant_one.delete(
            f"{prefix}/{str(other_chart_id)}",
        )
        assert response.status_code == 200

        now = (
            datetime.now(timezone.utc) + timedelta(seconds=1)
        )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
        response = await http_tenant_one.post(
            prefix,
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        sections = response.json()["sections"]
        assert len(sections) == 2
        section = next(s for s in sections if s["id"] == section_id)
        charts = section["charts"]
        assert len(charts) == 1
        assert charts[0]["id"] == chart_id
        chart = charts[0]
        assert chart["title"] == chart_name
        assert chart["description"] == chart_description
        assert chart["chart_type"] == "line"
        assert chart["metadata"] == {"foo": "bar"}
        assert chart["index"] == 0
        series = chart["series"]
        assert len(series) == 3
        root_series = next(s for s in series if s["name"] == patch_series_name)
        assert root_series["filters"] == {
            "session": [session_id],
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert root_series["metric"] == "feedback"
        assert root_series["feedback_key"] == "correctness"

        color_series = next(s for s in series if s["name"] == other_patch_series_name)
        assert color_series["filters"] == {
            "session": [session_id],
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert color_series["metric"] == "feedback_values"
        assert color_series["feedback_key"] == "color"

        avg_series = next(s for s in series if s["name"] == third_patch_series_name)
        assert avg_series["filters"] == {
            "session": [session_id],
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert avg_series["metric"] == "feedback_score_avg"
        assert avg_series["feedback_key"] == "correctness"

        data = chart["data"]
        assert len(data) == 30

        # Test deleting a section
        response = await http_tenant_one.delete(
            f"{prefix}/section/{str(section_id)}",
        )
        assert response.status_code == 200

        response = await http_tenant_one.post(
            prefix,
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        sections = response.json()["sections"]
        assert len(sections) == 1
        section = sections[0]
        assert section["id"] == new_section_id
        assert section["title"] == new_section_name
        assert section["index"] == 0
        assert section["description"] == "test"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("org_scoped", [True, False])
async def test_chart_indices(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    org_scoped: bool,
) -> None:
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[org_scoped]
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        http_tenant_one = authed_client.client
        await _clear_all_charts(http_tenant_one, prefix)
        session_id = await _create_session(http_tenant_one)
        chart_1_create = schemas.CustomChartCreate(
            title="chart_1",
            description="test",
            chart_type="line",
            metadata={"key": "value"},
            index=10,
            series=[
                schemas.CustomChartSeriesCreate(
                    name="series_1",
                    filters={
                        "session": [session_id],
                        "filter": "eq(is_root, true)",
                    },
                    metric="run_count",
                ),
            ],
        )
        chart_1 = await _create_chart(http_tenant_one, prefix, chart_1_create)
        chart_1_id = chart_1["id"]
        section_1_id = chart_1["section_id"]
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 0,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )

        await _patch_chart(
            http_tenant_one, prefix, chart_1_id, schemas.CustomChartUpdate(index=5)
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 0,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
            ignore_cache=True,
        )

        chart_2_create = schemas.CustomChartCreate(
            title="chart_2",
            description="test2",
            chart_type="line",
            metadata={"key2": "value2"},
            section_id=section_1_id,
            index=0,
            series=[
                schemas.CustomChartSeriesCreate(
                    name="series_2",
                    filters={
                        "session": [str(session_id)],
                        "filter": "eq(is_root, true)",
                    },
                    metric="run_count",
                ),
            ],
        )
        chart_2 = await _create_chart(http_tenant_one, prefix, chart_2_create)
        chart_2_id = chart_2["id"]
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {"index": 0},
            ignore_cache=True,
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {"index": 1},
        )

        chart_3_create = schemas.CustomChartCreate(
            title="chart_3",
            description="test3",
            chart_type="bar",
            metadata={"key3": "value3"},
            section_id=section_1_id,
            series=[
                schemas.CustomChartSeriesCreate(
                    name="series_3",
                    filters={
                        "session": [str(session_id)],
                        "filter": "eq(is_root, true)",
                    },
                    metric="run_count",
                ),
            ],
        )
        chart_3 = await _create_chart(http_tenant_one, prefix, chart_3_create)
        chart_3_id = chart_3["id"]
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {"index": 2},
            ignore_cache=True,
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {"index": 0},
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {"index": 1},
        )

        chart_4_create = schemas.CustomChartCreate(
            title="chart_4",
            description="test4",
            chart_type="line",
            metadata={"key4": "value4"},
            section_id=section_1_id,
            index=100,
            series=[
                schemas.CustomChartSeriesCreate(
                    name="series_4",
                    filters={
                        "session": [str(session_id)],
                        "filter": "eq(is_root, true)",
                    },
                    metric="run_count",
                ),
            ],
        )
        chart_4 = await _create_chart(http_tenant_one, prefix, chart_4_create)
        chart_4_id = chart_4["id"]
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 3,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 2,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 0,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 1,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )

        await _patch_chart(
            http_tenant_one, prefix, chart_4_id, schemas.CustomChartUpdate(index=1)
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 1,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 3,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 0,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 2,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )

        await _patch_chart(
            http_tenant_one, prefix, chart_3_id, schemas.CustomChartUpdate(index=0)
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 2,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 0,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 1,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 3,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )

        await _patch_chart(
            http_tenant_one, prefix, chart_3_id, schemas.CustomChartUpdate(index=100)
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 1,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 3,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 0,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 2,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )

        section_2_create = schemas.CustomChartsSectionCreate(
            title="section_2",
            description="new_section",
            index=1,
        )
        section_2_id = await _create_section(http_tenant_one, prefix, section_2_create)
        await _assert_section(
            http_tenant_one,
            prefix,
            section_2_id,
            {"index": 1, "title": "section_2", "description": "new_section"},
        )

        section_3_create = schemas.CustomChartsSectionCreate(
            title="section_3",
            description="new_section",
            index=100,
        )
        section_3_id = await _create_section(http_tenant_one, prefix, section_3_create)
        await _assert_section(
            http_tenant_one,
            prefix,
            section_3_id,
            {"index": 2, "title": "section_3", "description": "new_section"},
        )

        section_4_create = schemas.CustomChartsSectionCreate(
            title="section_4",
            description="new_section",
            index=1,
        )
        section_4_id = await _create_section(http_tenant_one, prefix, section_4_create)
        await _assert_section(
            http_tenant_one,
            prefix,
            section_4_id,
            {"index": 1, "title": "section_4", "description": "new_section"},
        )
        await _assert_section(
            http_tenant_one,
            prefix,
            section_2_id,
            {"index": 2, "title": "section_2", "description": "new_section"},
        )
        await _assert_section(
            http_tenant_one,
            prefix,
            section_3_id,
            {"index": 3, "title": "section_3", "description": "new_section"},
        )
        await _assert_section(
            http_tenant_one,
            prefix,
            section_1_id,
            {"index": 0, "title": "Dashboard 1", "description": None},
        )

        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_1_id,
            schemas.CustomChartUpdate(section_id=section_2_id),
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 1,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_1_id, chart_4_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 2,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_1_id, chart_3_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 0,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_1_id, chart_2_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 0,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_1_id
        )

        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_3_id,
            schemas.CustomChartUpdate(section_id=section_2_id),
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 1,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_1_id, chart_4_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 1,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_3_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 0,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_1_id, chart_2_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 0,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_1_id
        )

        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_2_id,
            schemas.CustomChartUpdate(section_id=section_2_id),
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 0,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_1_id, chart_4_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 1,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_3_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 2,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_2_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 0,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_1_id
        )

        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_4_id,
            schemas.CustomChartUpdate(section_id=section_2_id, index=1),
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_4_id,
            {
                "index": 1,
                "title": "chart_4",
                "description": "test4",
                "chart_type": "line",
                "metadata": {"key4": "value4"},
            },
            ignore_cache=True,
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_4_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_3_id,
            {
                "index": 2,
                "title": "chart_3",
                "description": "test3",
                "chart_type": "bar",
                "metadata": {"key3": "value3"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_3_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_2_id,
            {
                "index": 3,
                "title": "chart_2",
                "description": "test2",
                "chart_type": "line",
                "metadata": {"key2": "value2"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_2_id
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_1_id,
            {
                "index": 0,
                "title": "chart_1",
                "description": "test",
                "chart_type": "line",
                "metadata": {"key": "value"},
            },
        )
        await _assert_chart_in_section(
            http_tenant_one, prefix, section_2_id, chart_1_id
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("org_scoped", [True, False])
async def test_chart_caps(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    org_scoped: bool,
) -> None:
    """Test max number of series and charts per section."""
    OVER_SIZE = 11
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[org_scoped]
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        http_tenant_one = authed_client.client
        session_id = await _create_session(http_tenant_one)
        section_id = await _create_section(
            http_tenant_one,
            prefix,
            schemas.CustomChartsSectionCreate(
                title="section_1",
                description="test",
            ),
        )
        await _assert_section(
            http_tenant_one,
            prefix,
            section_id,
            {"title": "section_1", "description": "test", "index": 0},
        )

        section_id_2 = await _create_section(
            http_tenant_one,
            prefix,
            schemas.CustomChartsSectionCreate(
                title="section_2",
                description="test2",
            ),
        )
        await _assert_section(
            http_tenant_one,
            prefix,
            section_id_2,
            {"title": "section_2", "description": "test2", "index": 1},
        )

        # 4 series should pass for org-scoped only
        assert_code = 200 if org_scoped else 422
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate.model_construct(
                **{
                    "title": "chart_1",
                    "description": "test",
                    "chart_type": "line",
                    "metadata": {"key": "value"},
                    "section_id": section_id,
                    "series": [
                        schemas.CustomChartSeriesCreate(
                            name=f"series_{i}",
                            filters={
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            metric="run_count",
                        )
                        for i in range(OVER_SIZE)
                    ],
                }
            ),
            assert_code=assert_code,
        )
        # cleanup if necessary
        if org_scoped:
            await _delete_chart(http_tenant_one, prefix, chart["id"])

        # Create 10 charts with 19 total series (when fetching, the max series is 20, so this sets us up later)
        section_1_charts = []
        for i in range(10):
            chart = await _create_chart(
                http_tenant_one,
                prefix,
                schemas.CustomChartCreate(
                    title=f"section1_title_{i}",
                    description=f"section1_description_{i}",
                    chart_type="line",
                    metadata={"key": f"section1_value_{i}"},
                    section_id=section_id,
                    series=[
                        schemas.CustomChartSeriesCreate(
                            name=f"section1_series_{i}_1",
                            filters={
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            metric="run_count",
                        ),
                        schemas.CustomChartSeriesCreate(
                            name=f"section1_series_{i}_2",
                            filters={
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            metric="run_count",
                        ),
                    ]
                    if i != 9
                    else [
                        schemas.CustomChartSeriesCreate(
                            name=f"section1_series_{i}_1",
                            filters={
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            metric="run_count",
                        )
                    ],
                ),
            )
            section_1_charts.append(chart)

        # make sure you can't patch the chart to have more than 3 series
        if not org_scoped:
            response = await http_tenant_one.patch(
                f"{prefix}/{section_1_charts[0]['id']}",
                json={
                    "series": [
                        {
                            "name": f"series_{i}",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            "metric": "run_count",
                        }
                        for i in range(OVER_SIZE)
                    ],
                },
            )
            assert response.status_code == 422

        # Next chart should throw a 400 since we're at the max number of charts
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=random_lower_string(),
                description="test",
                chart_type="line",
                metadata={"key": "value"},
                section_id=section_id,
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=random_lower_string(),
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        metric="run_count",
                    ),
                ],
            ),
            400,
            True,  # skip preview because this is a valid chart
        )

        # When we don't pass a section id it auto-adds to the first section so this will throw a 400 too
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=random_lower_string(),
                description="test",
                chart_type="line",
                metadata={"key": "value"},
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=random_lower_string(),
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        metric="run_count",
                    ),
                ],
            ),
            400,
            True,  # skip preview because this is a valid chart
        )

        section_2_charts = []
        # add to new section
        new_chart_name = "section2_title_0"
        new_section_chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=new_chart_name,
                description="section2_description_0",
                chart_type="line",
                metadata={"key": "section2_value_0"},
                section_id=section_id_2,
                series=[
                    schemas.CustomChartSeriesCreate(
                        name="section2_series_0_1",
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name="section2_series_0_2",
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        metric="run_count",
                    ),
                ],
            ),
        )
        section_2_charts.append(new_section_chart)

        # Try moving chart to the full section: should fail
        await _patch_chart(
            http_tenant_one,
            prefix,
            new_section_chart["id"],
            schemas.CustomChartUpdate(section_id=section_id),
            400,
            True,  # skip preview because this is a valid chart
        )
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            new_section_chart["id"],
            {
                "index": 0,
                "title": new_chart_name,
                "description": "section2_description_0",
                "chart_type": "line",
                "metadata": {"key": "section2_value_0"},
            },
            None,
            None,
            None,
            section_id_2,
            ignore_cache=True,
        )

        for j in range(9):
            i = j + 1
            chart = await _create_chart(
                http_tenant_one,
                prefix,
                schemas.CustomChartCreate(
                    title=f"section2_title_{i}",
                    description=f"section2_description_{i}",
                    chart_type="line",
                    metadata={"key": f"section2_value_{i}"},
                    section_id=section_id_2,
                    series=[
                        schemas.CustomChartSeriesCreate(
                            name=f"section2_series_{i}_1",
                            filters={
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            metric="run_count",
                        ),
                        schemas.CustomChartSeriesCreate(
                            name=f"section2_series_{i}_2",
                            filters={
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            metric="run_count",
                        ),
                    ],
                ),
            )
            section_2_charts.append(chart)

        # Now add a third section, which will be off the first page
        section_id_3 = await _create_section(
            http_tenant_one,
            prefix,
            schemas.CustomChartsSectionCreate(
                title="section_3",
                description="test3",
            ),
        )
        await _assert_section(
            http_tenant_one,
            prefix,
            section_id_3,
            {"title": "section_3", "description": "test3", "index": 2},
        )

        third_section_chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title="THIRD_SECTION_TITLE",
                description="asdf",
                chart_type="line",
                metadata={"key": "value"},
                section_id=section_id_3,
                series=[
                    schemas.CustomChartSeriesCreate(
                        name="section3_series_1_1",
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name="section3_series_1_2",
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                        },
                        metric="run_count",
                    ),
                ],
            ),
        )

        for i, chart in enumerate(section_1_charts):
            series = (
                [
                    {
                        "id": next(
                            s
                            for s in section_1_charts[i]["series"]
                            if s["name"] == f"section1_series_{i}_1"
                        )["id"],
                        "name": f"section1_series_{i}_1",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                    {
                        "id": next(
                            s
                            for s in section_1_charts[i]["series"]
                            if s["name"] == f"section1_series_{i}_2"
                        )["id"],
                        "name": f"section1_series_{i}_2",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                ]
                if i != 9
                else [
                    {
                        "id": next(
                            s
                            for s in section_1_charts[i]["series"]
                            if s["name"] == f"section1_series_{i}_1"
                        )["id"],
                        "name": f"section1_series_{i}_1",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                ]
            )
            await _assert_multi_chart(
                http_tenant_one,
                str(authed_client.auth.tenant_id),
                prefix,
                chart["id"],
                {
                    "index": i,
                    "title": f"section1_title_{i}",
                    "description": f"section1_description_{i}",
                    "chart_type": "line",
                    "metadata": {"key": f"section1_value_{i}"},
                    "series": series,
                },
                None,
                None,
                None,
                section_id,
                None,
                does_not_contain_section_id=[section_id_3],
                does_not_contain_chart_id=[third_section_chart["id"]],
                headers={"X-Pagination-Total": "2"},
                ignore_cache=i == 0,
            )

        for i, chart in enumerate(section_2_charts):
            await _assert_multi_chart(
                http_tenant_one,
                str(authed_client.auth.tenant_id),
                prefix,
                chart["id"],
                {
                    "index": i,
                    "title": f"section2_title_{i}",
                    "description": f"section2_description_{i}",
                    "chart_type": "line",
                    "metadata": {"key": f"section2_value_{i}"},
                    "series": [
                        {
                            "id": next(
                                s
                                for s in section_2_charts[i]["series"]
                                if s["name"] == f"section2_series_{i}_1"
                            )["id"],
                            "name": f"section2_series_{i}_1",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                                "trace_filter": None,
                                "tree_filter": None,
                            },
                            "metric": "run_count",
                            "feedback_key": None,
                            "workspace_id": None,
                            "group_by": None,
                        },
                        {
                            "id": next(
                                s
                                for s in section_2_charts[i]["series"]
                                if s["name"] == f"section2_series_{i}_2"
                            )["id"],
                            "name": f"section2_series_{i}_2",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                                "trace_filter": None,
                                "tree_filter": None,
                            },
                            "metric": "run_count",
                            "feedback_key": None,
                            "workspace_id": None,
                            "group_by": None,
                        },
                    ],
                },
                None,
                None,
                None,
                section_id_2,
                None,
                does_not_contain_section_id=[section_id_3],
                does_not_contain_chart_id=[third_section_chart["id"]],
                headers={"X-Pagination-Total": "2"},
            )

        await _assert_multi_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            third_section_chart["id"],
            {
                "index": 0,
                "title": "THIRD_SECTION_TITLE",
                "description": "asdf",
                "chart_type": "line",
                "metadata": {"key": "value"},
                "series": [
                    {
                        "id": next(
                            s
                            for s in third_section_chart["series"]
                            if s["name"] == "section3_series_1_1"
                        )["id"],
                        "name": "section3_series_1_1",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                    {
                        "id": next(
                            s
                            for s in third_section_chart["series"]
                            if s["name"] == "section3_series_1_2"
                        )["id"],
                        "name": "section3_series_1_2",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                ],
            },
            None,
            None,
            None,
            section_id_3,
            after_index=1,
            does_not_contain_section_id=[section_id, section_id_2],
            does_not_contain_chart_id=[
                chart["id"] for chart in section_1_charts + section_2_charts
            ],
            headers={"X-Pagination-Total": "2"},
        )

        # Test tagging chart sections

        key1 = random_lower_string()
        response = await http_tenant_one.post(
            "/workspaces/current/tag-keys",
            json={"key": key1},
        )

        assert response.status_code == 200
        key1_id = response.json()["id"]

        value1 = random_lower_string()
        response = await http_tenant_one.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value1},
        )

        assert response.status_code == 200
        value1_id = response.json()["id"]

        value2 = random_lower_string()
        response = await http_tenant_one.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value2},
        )

        assert response.status_code == 200
        value2_id = response.json()["id"]

        # test taging sections (only relevant for workspace-scoped since we don't have org-scoped tags)
        if not org_scoped:
            responses_taggings = await asyncio.gather(
                http_tenant_one.post(
                    "/workspaces/current/taggings",
                    json={
                        "resource_type": "dashboard",
                        "resource_id": section_id,
                        "tag_value_id": value1_id,
                    },
                ),
                http_tenant_one.post(
                    "/workspaces/current/taggings",
                    json={
                        "resource_type": "dashboard",
                        "resource_id": section_id_2,
                        "tag_value_id": value2_id,
                    },
                ),
                http_tenant_one.post(
                    "/workspaces/current/taggings",
                    json={
                        "resource_type": "dashboard",
                        "resource_id": section_id_3,
                        "tag_value_id": value1_id,
                    },
                ),
            )

            assert all(
                response.status_code == 200 for response in responses_taggings
            ), (
                f"Failed taggings: {[(r.status_code, r.text) for r in responses_taggings]}"
            )

            all_charts = section_1_charts + section_2_charts + [third_section_chart]
            await _assert_multi_chart(
                http_tenant_one,
                str(authed_client.auth.tenant_id),
                prefix,
                None,
                {},
                None,
                None,
                None,
                None,
                None,
                does_not_contain_section_id=[section_id, section_id_2, section_id_3],
                does_not_contain_chart_id=[c["id"] for c in all_charts],
                headers={"X-Pagination-Total": "-1"},
                tag_value_id=[
                    value1_id,
                    value2_id,
                ],  # if we pass in both, no charts should be returned (because neither have both tags)
                ignore_cache=True,
            )

            for i, chart in enumerate(section_1_charts):
                series = (
                    [
                        {
                            "id": next(
                                s
                                for s in section_1_charts[i]["series"]
                                if s["name"] == f"section1_series_{i}_1"
                            )["id"],
                            "name": f"section1_series_{i}_1",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                                "trace_filter": None,
                                "tree_filter": None,
                            },
                            "metric": "run_count",
                            "feedback_key": None,
                            "workspace_id": None,
                            "group_by": None,
                        },
                        {
                            "id": next(
                                s
                                for s in section_1_charts[i]["series"]
                                if s["name"] == f"section1_series_{i}_2"
                            )["id"],
                            "name": f"section1_series_{i}_2",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                                "trace_filter": None,
                                "tree_filter": None,
                            },
                            "metric": "run_count",
                            "feedback_key": None,
                            "workspace_id": None,
                            "group_by": None,
                        },
                    ]
                    if i != 9
                    else [
                        {
                            "id": next(
                                s
                                for s in section_1_charts[i]["series"]
                                if s["name"] == f"section1_series_{i}_1"
                            )["id"],
                            "name": f"section1_series_{i}_1",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                                "trace_filter": None,
                                "tree_filter": None,
                            },
                            "metric": "run_count",
                            "feedback_key": None,
                            "workspace_id": None,
                            "group_by": None,
                        },
                    ]
                )
                await _assert_multi_chart(
                    http_tenant_one,
                    str(authed_client.auth.tenant_id),
                    prefix,
                    chart["id"],
                    {
                        "index": i,
                        "title": f"section1_title_{i}",
                        "description": f"section1_description_{i}",
                        "chart_type": "line",
                        "metadata": {"key": f"section1_value_{i}"},
                        "series": series,
                    },
                    None,
                    None,
                    None,
                    section_id,
                    None,
                    does_not_contain_section_id=[section_id_2],
                    does_not_contain_chart_id=[c["id"] for c in section_2_charts],
                    headers={"X-Pagination-Total": "2"},
                    tag_value_id=[value1_id],
                )

            for i, chart in enumerate(section_2_charts):
                await _assert_multi_chart(
                    http_tenant_one,
                    str(authed_client.auth.tenant_id),
                    prefix,
                    chart["id"],
                    {
                        "index": i,
                        "title": f"section2_title_{i}",
                        "description": f"section2_description_{i}",
                        "chart_type": "line",
                        "metadata": {"key": f"section2_value_{i}"},
                        "series": [
                            {
                                "id": next(
                                    s
                                    for s in section_2_charts[i]["series"]
                                    if s["name"] == f"section2_series_{i}_1"
                                )["id"],
                                "name": f"section2_series_{i}_1",
                                "filters": {
                                    "session": [session_id],
                                    "filter": "eq(is_root, true)",
                                    "trace_filter": None,
                                    "tree_filter": None,
                                },
                                "metric": "run_count",
                                "feedback_key": None,
                                "workspace_id": None,
                                "group_by": None,
                            },
                            {
                                "id": next(
                                    s
                                    for s in section_2_charts[i]["series"]
                                    if s["name"] == f"section2_series_{i}_2"
                                )["id"],
                                "name": f"section2_series_{i}_2",
                                "filters": {
                                    "session": [session_id],
                                    "filter": "eq(is_root, true)",
                                    "trace_filter": None,
                                    "tree_filter": None,
                                },
                                "metric": "run_count",
                                "feedback_key": None,
                                "workspace_id": None,
                                "group_by": None,
                            },
                        ],
                    },
                    None,
                    None,
                    None,
                    section_id_2,
                    None,
                    does_not_contain_section_id=[section_id, section_id_3],
                    does_not_contain_chart_id=[third_section_chart["id"]]
                    + [c["id"] for c in section_1_charts],
                    headers={"X-Pagination-Total": "1"},
                    tag_value_id=[value2_id],
                )

            await _assert_multi_chart(
                http_tenant_one,
                str(authed_client.auth.tenant_id),
                prefix,
                third_section_chart["id"],
                {
                    "index": 0,
                    "title": "THIRD_SECTION_TITLE",
                    "description": "asdf",
                    "chart_type": "line",
                    "metadata": {"key": "value"},
                    "series": [
                        {
                            "id": next(
                                s
                                for s in third_section_chart["series"]
                                if s["name"] == "section3_series_1_1"
                            )["id"],
                            "name": "section3_series_1_1",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                                "trace_filter": None,
                                "tree_filter": None,
                            },
                            "metric": "run_count",
                            "feedback_key": None,
                            "workspace_id": None,
                            "group_by": None,
                        },
                        {
                            "id": next(
                                s
                                for s in third_section_chart["series"]
                                if s["name"] == "section3_series_1_2"
                            )["id"],
                            "name": "section3_series_1_2",
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                                "trace_filter": None,
                                "tree_filter": None,
                            },
                            "metric": "run_count",
                            "feedback_key": None,
                            "workspace_id": None,
                            "group_by": None,
                        },
                    ],
                },
                None,
                None,
                None,
                section_id_3,
                after_index=None,
                does_not_contain_section_id=[section_id_2],
                does_not_contain_chart_id=[chart["id"] for chart in section_2_charts],
                headers={"X-Pagination-Total": "2"},
                tag_value_id=[value1_id],
            )

            response = await http_tenant_one.get(
                f"{prefix}/section",
                params={"limit": 2, "sort_by": "created_at", "sort_by_desc": False},
            )
            assert response.status_code == 200
            sections = response.json()
            assert len(sections) == 2
            assert sections[0]["id"] == section_id
            assert sections[0]["title"] == "section_1"
            assert sections[0]["description"] == "test"
            assert sections[0]["index"] == 0

            assert sections[1]["id"] == section_id_2
            assert sections[1]["title"] == "section_2"
            assert sections[1]["description"] == "test2"
            assert sections[1]["index"] == 1

            assert response.headers["X-Pagination-Total"] == "3"

            response = await http_tenant_one.get(
                f"{prefix}/section",
                params={
                    "limit": 2,
                    "offset": 2,
                    "sort_by": "created_at",
                    "sort_by_desc": False,
                },
            )
            assert response.status_code == 200
            sections = response.json()
            assert len(sections) == 1
            assert sections[0]["id"] == section_id_3
            assert sections[0]["title"] == "section_3"
            assert sections[0]["description"] == "test3"
            assert sections[0]["index"] == 2

            assert response.headers["X-Pagination-Total"] == "3"

            response = await http_tenant_one.get(
                f"{prefix}/section", params={"title_contains": "3"}
            )
            assert response.status_code == 200
            sections = response.json()
            assert len(sections) == 1
            assert sections[0]["id"] == section_id_3
            assert sections[0]["title"] == "section_3"
            assert sections[0]["description"] == "test3"
            assert sections[0]["index"] == 2

            assert response.headers["X-Pagination-Total"] == "1"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_chart(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[False]
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        http_tenant_one = authed_client.client
        await _clear_all_charts(http_tenant_one, prefix)
        session_id = await _create_session(http_tenant_one)

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        response = await http_tenant_one.post(
            f"{prefix}/create",
            json={
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "section_id": None,
                "index": None,
                "series": [
                    {
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": 'and(eq(feedback_key, "correctness"), eq(feedback_score, 0))',
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "feedback_score_avg",
                        "feedback_key": "correctness",
                    }
                ],
            },
        )
        assert response.status_code == 200
        section_id = response.json()["section_id"]
        assert response.json()["index"] == 0
        chart_id = response.json()["id"]
        series_id = response.json()["series"][0]["id"]

        # Create the runs
        new_runs = []
        for i in range(20):  # 2 runs per bucket
            run_id = uuid4()
            start_time = datetime.now(timezone.utc) - timedelta(minutes=30 * i)
            end_time = start_time + timedelta(seconds=i)
            new_runs.append(
                {
                    "name": f"AgentExecutor{i}",
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Agent"},
                    "inputs": {"input": f"input{i}"},
                    "session_id": session_id,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
                },
            )

        response = await http_tenant_one.post(
            "/runs/batch",
            json={
                "post": new_runs,
            },
        )
        assert response.status_code == 202
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )

        feedback_scores_1 = [0, 0, 1, 0, 1, 1, 0, 0, 1, 0]  # for readability
        feedback_scores_2 = [1, 0, 1, 0, 1, 1, 0, 1, 0, 0]
        for i, run in enumerate(new_runs):
            response = await http_tenant_one.post(
                "/feedback",
                json={
                    "run_id": run["id"],
                    "key": "correctness",
                    "score": feedback_scores_1.pop()
                    if i % 2 == 0
                    else feedback_scores_2.pop(),
                },
            )
            assert response.status_code == 200
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )

        now = (
            datetime.now(timezone.utc) + timedelta(seconds=1)
        )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
        start_time = now - timedelta(hours=10)
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
            },
            [
                [
                    {
                        "correctness": {
                            "avg": 0,
                            "stdev": 0,
                            "n": 1,
                            "errors": 0,
                            "values": {},
                        }
                    },
                    {
                        "correctness": {
                            "avg": 0,
                            "stdev": 0,
                            "n": 2,
                            "errors": 0,
                            "values": {},
                        }
                    },
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {
                        "correctness": {
                            "avg": 0,
                            "stdev": 0,
                            "n": 2,
                            "errors": 0,
                            "values": {},
                        }
                    },
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {
                        "correctness": {
                            "avg": 0,
                            "stdev": 0,
                            "n": 2,
                            "errors": 0,
                            "values": {},
                        }
                    },
                    {
                        "correctness": {
                            "avg": 0,
                            "stdev": 0,
                            "n": 1,
                            "errors": 0,
                            "values": {},
                        }
                    },
                    {
                        "correctness": {
                            "avg": 0,
                            "stdev": 0,
                            "n": 1,
                            "errors": 0,
                            "values": {},
                        }
                    },
                    {
                        "correctness": {
                            "avg": 0,
                            "stdev": 0,
                            "n": 2,
                            "errors": 0,
                            "values": {},
                        }
                    },
                ]
            ],
            schemas.CustomChartsRequest(
                start_time=start_time,
                end_time=now,
                stride=schemas.TimedeltaInput(hours=1),
            ),
            [series_id],
            section_id,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_value_chart(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[False]
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        http_tenant_one = authed_client.client
        await _clear_all_charts(http_tenant_one, prefix)
        session_id = await _create_session(http_tenant_one)

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        response = await http_tenant_one.post(
            f"{prefix}/create",
            json={
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "section_id": None,
                "index": None,
                "series": [
                    {
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "feedback_score_avg",
                        "feedback_key": "correctness",
                    }
                ],
            },
        )
        assert response.status_code == 200
        section_id = response.json()["section_id"]
        assert response.json()["index"] == 0
        chart_id = response.json()["id"]
        series_id = response.json()["series"][0]["id"]

        # Create the runs
        new_runs = []
        alternative_score_run_ids: list[str | UUID] = []
        for i in range(10):
            if i == 2 or i == 6:
                for j in range(20):
                    run_id = uuid4()
                    start_time = datetime.now(timezone.utc) - timedelta(minutes=60 * i)
                    end_time = start_time + timedelta(seconds=i)
                    new_runs.append(
                        {
                            "name": f"AgentExecutor_{j}_{i}",
                            "start_time": start_time.isoformat(),
                            "end_time": end_time.isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Agent"},
                            "inputs": {"input": f"input_{j}_{i}"},
                            "session_id": session_id,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
                        },
                    )
            elif i == 3:
                for j in range(3):
                    run_id = uuid4()
                    alternative_score_run_ids.append(run_id)
                    start_time = datetime.now(timezone.utc) - timedelta(minutes=60 * i)
                    end_time = start_time + timedelta(seconds=i)
                    new_runs.append(
                        {
                            "name": f"AgentExecutor_{j}_{i}",
                            "start_time": start_time.isoformat(),
                            "end_time": end_time.isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Agent"},
                            "inputs": {"input": f"input_{j}_{i}"},
                            "session_id": session_id,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
                        },
                    )

        response = await http_tenant_one.post(
            "/runs/batch",
            json={
                "post": new_runs,
            },
        )
        assert response.status_code == 202
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )

        alternative_feedback_values_2 = [
            "1",
            "9",
            "9",
        ]

        feedback_values_1 = [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
        ]  # for readability
        feedback_values_2 = ["1", "2", "3", "4", "5", "1", "2", "3", "4", "5"]
        curr_feedback_values_1 = feedback_values_1.copy()
        curr_feedback_values_2 = feedback_values_2.copy()
        feedback_coroutines = []
        alternative_score_run_ids = [
            str(run_id) for run_id in alternative_score_run_ids
        ]
        for i, run in enumerate(new_runs):
            if run["id"] in alternative_score_run_ids:
                feedback_coroutines.append(
                    http_tenant_one.post(
                        "/feedback",
                        json={
                            "run_id": run["id"],
                            "key": "correctness",
                            "value": alternative_feedback_values_2.pop(),
                        },
                    )
                )
            else:
                feedback_coroutines.append(
                    http_tenant_one.post(
                        "/feedback",
                        json={
                            "run_id": run["id"],
                            "key": "correctness",
                            "value": curr_feedback_values_1.pop()
                            if i % 2 == 0
                            else curr_feedback_values_2.pop(),
                        },
                    )
                )
            if not curr_feedback_values_1:
                curr_feedback_values_1 = feedback_values_1.copy()
            if not curr_feedback_values_2:
                curr_feedback_values_2 = feedback_values_2.copy()

        responses = await asyncio.gather(*feedback_coroutines)
        assert all(response.status_code == 200 for response in responses)
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )

        now = (
            datetime.now(timezone.utc) + timedelta(seconds=1)
        )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
        start_time = now - timedelta(hours=10)
        values_for_bucket = {
            "1": 3,
            "2": 3,
            "3": 3,
            "4": 3,
            "5": 3,
        }
        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
            },
            [
                [
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {
                        "correctness": {
                            "avg": None,
                            "stdev": None,
                            "n": 20,
                            "errors": 0,
                            "values": values_for_bucket,
                        }
                    },
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {
                        "correctness": {
                            "avg": None,
                            "stdev": None,
                            "n": 3,
                            "errors": 0,
                            "values": {"1": 1},
                        }
                    },
                    {
                        "correctness": {
                            "avg": None,
                            "stdev": None,
                            "n": 20,
                            "errors": 0,
                            "values": values_for_bucket,
                        }
                    },
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                    {"correctness": {"avg": None, "n": 0, "values": {}}},
                ]
            ],
            schemas.CustomChartsRequest(
                start_time=start_time,
                end_time=now,
                stride=schemas.TimedeltaInput(hours=1),
            ),
            [series_id],
            section_id,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_common_filters(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[False]
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        http_tenant_one = authed_client.client
        await _clear_all_charts(http_tenant_one, prefix)

        session_id = await _create_session(http_tenant_one)
        session_id_2 = await _create_session(http_tenant_one)
        session_id_3 = await _create_session(http_tenant_one)

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id],
                    filter="eq(is_root, true)",
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    }
                ],
                "common_filters": {
                    "session": [session_id],
                    "filter": "eq(is_root, true)",
                    "trace_filter": None,
                    "tree_filter": None,
                },
            },
        )

        # sessions don't match
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id_2],
                    filter=None,
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
            422,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id],
                    filter=None,
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    }
                ],
                "common_filters": {
                    "session": [session_id],
                    "filter": None,
                    "trace_filter": None,
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        # filter invalid
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id],
                    filter="lte(latency_p50, 10)",
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
            422,
        )

        # filter invalid (conflicting)
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=None,
                    filter="eq(is_root, true)",
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, false)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
            422,
        )

        # filter invalid
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=None,
                    filter="eq(asdf, true)",
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, false)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
            422,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id],
                    filter="lte(latency, 10)",
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    }
                ],
                "common_filters": {
                    "session": [session_id],
                    "filter": "lte(latency, 10)",
                    "trace_filter": None,
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        # filter invalid
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id],
                    filter=None,
                    trace_filter="lte(latency_p50, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
            422,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id],
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    }
                ],
                "common_filters": {
                    "session": [session_id],
                    "filter": None,
                    "trace_filter": "lte(latency, 10)",
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id],
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
            422,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id, session_id_2],
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    }
                ],
                "common_filters": {
                    "session": [session_id, session_id_2],
                    "filter": None,
                    "trace_filter": "lte(latency, 10)",
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=None,
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    )
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    }
                ],
                "common_filters": {
                    "session": None,
                    "filter": None,
                    "trace_filter": "lte(latency, 10)",
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id, session_id_2],
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": None,
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": None,
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                    {
                        "id": chart["series"][1]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                    {
                        "id": chart["series"][2]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                ],
                "common_filters": {
                    "session": [session_id, session_id_2],
                    "filter": None,
                    "trace_filter": "lte(latency, 10)",
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        # missing session in the first series should fail if workspace scoped
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=None,
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": None,
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                ],
            ),
            422,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=None,
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id_3],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id_3],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                    {
                        "id": chart["series"][1]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                    {
                        "id": chart["series"][2]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id, session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                ],
                "common_filters": {
                    "session": None,
                    "filter": None,
                    "trace_filter": "lte(latency, 10)",
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        random_session_id = uuid4()
        await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[random_session_id],
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": None,
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                ],
            ),
            404,
        )

        chart_name = random_lower_string()
        chart_description = random_lower_string()
        series_name = random_lower_string()
        chart = await _create_chart(
            http_tenant_one,
            prefix,
            schemas.CustomChartCreate(
                title=chart_name,
                description=chart_description,
                chart_type="line",
                metadata={"foo": "bar"},
                section_id=None,
                index=None,
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id_3],
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=series_name,
                        filters={
                            "session": None,
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        metric="run_count",
                    ),
                ],
            ),
        )
        chart_id = chart["id"]

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": None,
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                ],
                "common_filters": {
                    "session": [session_id_3],
                    "filter": None,
                    "trace_filter": "lte(latency, 10)",
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )

        # missing session should fail if workspace scoped
        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_id,
            schemas.CustomChartUpdate(
                common_filters=schemas.CustomChartSeriesFilters(
                    session=None,
                    filter=None,
                    trace_filter="lte(latency, 10)",
                    tree_filter=None,
                )
            ),
            422,
        )

        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_id,
            schemas.CustomChartUpdate(
                series=[
                    schemas.CustomChartSeriesUpdate(
                        name=series_name,
                        filters=schemas.CustomChartSeriesFilters(
                            session=[session_id_2],
                            filter="eq(is_root, true)",
                            trace_filter=None,
                            tree_filter=None,
                        ),
                        metric="run_count",
                    ),
                ]
            ),
            422,
        )

        random_chart_id = uuid4()
        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_id,
            schemas.CustomChartUpdate(
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[random_chart_id],
                    filter="eq(is_root, true)",
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesUpdate(
                        name=series_name,
                        filters=schemas.CustomChartSeriesFilters(
                            session=[random_chart_id],
                            filter="eq(is_root, true)",
                            trace_filter=None,
                            tree_filter=None,
                        ),
                        metric="run_count",
                    ),
                ],
            ),
            404,
        )

        await _patch_chart(
            http_tenant_one,
            prefix,
            chart_id,
            schemas.CustomChartUpdate(
                common_filters=schemas.CustomChartSeriesFilters(
                    session=[session_id_2],
                    filter="eq(is_root, true)",
                    trace_filter=None,
                    tree_filter=None,
                ),
                series=[
                    schemas.CustomChartSeriesUpdate(
                        id=chart["series"][0]["id"],
                        name=series_name,
                        filters=schemas.CustomChartSeriesFilters(
                            session=[session_id_2],
                            filter="eq(is_root, true)",
                            trace_filter=None,
                            tree_filter=None,
                        ),
                        metric="run_count",
                    ),
                ],
            ),
        )

        await _assert_chart(
            http_tenant_one,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": chart_name,
                "description": chart_description,
                "chart_type": "line",
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "id": chart["series"][0]["id"],
                        "name": series_name,
                        "filters": {
                            "session": [session_id_2],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "feedback_key": None,
                        "workspace_id": None,
                        "group_by": None,
                    },
                ],
                "common_filters": {
                    "session": [session_id_2],
                    "filter": "eq(is_root, true)",
                    "trace_filter": None,
                    "tree_filter": None,
                },
            },
            ignore_cache=True,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_charts(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test an organization-scoped chart for trace counts."""
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[True]

    # Create runs in a separate org to make sure they aren't counted
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        client = authed_client.client
        session_id = await _create_session(client)

        # Feedback metrics are not allowed in org-scoped charts
        for metric in [
            schemas.CustomChartMetric.feedback,
            schemas.CustomChartMetric.feedback_score_avg,
            schemas.CustomChartMetric.feedback_values,
        ]:
            await _create_chart(
                client,
                prefix,
                schemas.CustomChartCreate(
                    title=random_lower_string(),
                    description="test",
                    chart_type="line",
                    metadata={"key": "value"},
                    series=[
                        schemas.CustomChartSeriesCreate(
                            name=random_lower_string(),
                            filters={
                                "filter": "eq(is_root, true)",
                            },
                            metric=metric,
                            feedback_key="correctness",
                        ),
                    ],
                ),
                assert_code=422,
            )

        series_422 = [
            # Invalid metric
            {
                "name": random_lower_string(),
                "filters": {"filter": "eq(is_root, true)"},
                "metric": random_lower_string(),
            },
            # invalid filters
            {
                "name": random_lower_string(),
                "filters": {"key": "value"},
                "metric": "run_count",
            },
            # invalid filters
            {
                "name": random_lower_string(),
                "filters": {"filter": "eq(asdf, true)"},
                "metric": "run_count",
            },
            # trace filter
            {
                "name": random_lower_string(),
                "filters": {"trace_filter": "eq(is_root, true)"},
                "metric": "run_count",
                "trace_filter": "eq(asdf, true)",
            },
            # tree filter
            {
                "name": random_lower_string(),
                "filters": {"tree_filter": "eq(is_root, true)"},
                "metric": "run_count",
                "trace_filter": "eq(asdf, true)",
            },
        ]
        for s in series_422:
            response = await client.post(
                f"{prefix}/create",
                json={
                    "title": random_lower_string(),
                    "description": "test",
                    "index": 0,
                    "chart_type": "line",
                    "metadata": {"key": "value"},
                    "section_id": str(uuid4()),
                    "series": [s],
                },
            )
            assert response.status_code == 422

        await _run_common_tests(
            client,
            str(authed_client.auth.tenant_id),
            prefix,
            wait_until_task_queue_empty,
            random_lower_string(),
            random_lower_string(),
            random_lower_string(),
        )

        # Workspace charts should be able to coexist with org charts
        await _run_common_tests(
            client,
            str(authed_client.auth.tenant_id),
            ORG_SCOPED_TO_ENDPOINT_PREFIX[False],
            wait_until_task_queue_empty,
            random_lower_string(),
            random_lower_string(),
            random_lower_string(),
        )

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.org_auth

        # Create a chart for run counts by workspace
        title = random_lower_string()
        chart_type = "line"
        description = "my org chart"
        series_name = random_lower_string()
        response = await client.post(
            f"{prefix}/create",
            json={
                "title": title,
                "description": description,
                "chart_type": chart_type,
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "name": series_name,
                        "filters": {
                            "filter": "eq(is_root, true)",
                        },
                        "metric": "run_count",
                    }
                ],
            },
        )
        assert response.status_code == 200, response.text
        res = response.json()
        chart_id = res["id"]

        # Create runs in all new workspaces
        workspaces = await create_workspaces(client, auth.organization_id, 2)
        auths: list[AuthInfo] = []
        headers: list[dict] = []
        for workspace in workspaces:
            authinfo = AuthInfo(
                user_id=auth.user_id,
                user_email=auth.user_email,
                tenant_id=workspace.id,
                organization_id=auth.organization_id,
                tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
            )
            auths.append(authinfo)
            h = await setup_headers_for_auth(
                db_asyncpg, authinfo, use_api_key=use_api_key
            )
            headers.append(h)
        sessions: list[str] = []
        runs_per_ws = 20
        buckets = runs_per_ws / 2
        for auth_headers in headers:
            async with aclient_for_headers(auth_headers) as aclient:
                session_id = await _create_session(aclient)
                sessions.append(session_id)
                new_runs = []
                for i in range(runs_per_ws):  # 2 runs per bucket
                    run_id = uuid4()
                    start_time = datetime.now(timezone.utc) - timedelta(minutes=30 * i)
                    end_time = start_time + timedelta(seconds=i)
                    new_runs.append(
                        {
                            "name": f"AgentExecutor{i}",
                            "start_time": start_time.isoformat(),
                            "end_time": end_time.isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Agent"},
                            "inputs": {"input": f"input{i}"},
                            "session_id": session_id,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
                        },
                    )

                response = await aclient.post(
                    "/runs/batch",
                    json={
                        "post": new_runs,
                    },
                )
                assert response.status_code == 202
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )

        # Read chart data
        now = (
            datetime.now(timezone.utc) + timedelta(seconds=1)
        )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
        start_time = now - timedelta(hours=buckets)
        custom_charts, _ = await models.charts.fetch.fetch_custom_charts(
            schemas.CustomChartsRequestInternal(
                start_time=start_time,
                end_time=now,
                stride=schemas.TimedeltaInput(hours=1),
                access_scope=schemas.AccessScope.organization,
            ),
            auth,
        )
        sections = custom_charts.sections
        assert len(sections) == 1
        charts = sections[0].charts
        assert len(charts) == 1
        chart = next(c for c in charts if str(c.id) == chart_id)
        assert chart.metadata == {"foo": "bar"}
        assert chart.index == 0
        series = chart.series
        assert len(series) == 1
        series = series[0]
        assert series.name == series_name
        assert series.filters is not None
        assert series.filters.model_dump() == {
            "session": None,
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert series.metric == "run_count"
        assert series.feedback_key is None
        data = chart.data
        assert len(data) == buckets

        # Chart should include runs in all workspaces in org
        bucket_values_cross_ws: list[int] = [
            int(runs_per_ws * len(workspaces) / buckets)
        ] * int(buckets)
        await _assert_chart(
            client,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": title,
                "description": description,
                "chart_type": chart_type,
            },
            [bucket_values_cross_ws],
            schemas.CustomChartsRequest(
                start_time=start_time,
                end_time=now,
                stride=schemas.TimedeltaInput(hours=1),
            ),
            [str(series.id)],
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_chart_by_ws(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test an organization-scoped chart for trace counts by workspace."""
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[True]

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        org_auth = authed_client.org_auth

        # Should not be able to create more series than the max
        await _create_chart(
            client,
            prefix,
            schemas.CustomChartCreate(
                title=random_lower_string(),
                description="test",
                chart_type="line",
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=f"series_{i}",
                        filters={
                            "filter": "eq(is_root, true)",
                        },
                        metric="run_count",
                    )
                    for i in range(config.settings.CHARTS_MAX_SERIES_ORG + 1)
                ],
            ),
            assert_code=422,
        )

        # Create additional workspaces
        workspaces = await create_workspaces(client, org_auth.organization_id, 2)
        auths: list[AuthInfo] = []
        headers: list[dict] = []
        for workspace in workspaces:
            authinfo = AuthInfo(
                user_id=org_auth.user_id,
                user_email=org_auth.user_email,
                tenant_id=workspace.id,
                organization_id=org_auth.organization_id,
                tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
            )
            auths.append(authinfo)
            h = await setup_headers_for_auth(
                db_asyncpg, authinfo, use_api_key=use_api_key
            )
            headers.append(h)

        # Create a chart for run counts by workspace for all workspaces
        title = random_lower_string()
        chart_type = "line"
        description = "my org chart"
        series_name = random_lower_string()
        response = await client.post(
            f"{prefix}/create",
            json={
                "title": title,
                "description": description,
                "chart_type": chart_type,
                "metadata": {"foo": "bar"},
                "series": [
                    {
                        "name": series_name,
                        "filters": {
                            "filter": "eq(is_root, true)",
                        },
                        "metric": "run_count",
                        "workspace_id": str(a.tenant_id),
                    }
                    for a in auths + [auth]
                ],
            },
        )
        assert response.status_code == 200, response.text
        res = response.json()
        chart_id = res["id"]

        # Create runs in the new workspaces
        sessions: list[str] = []
        runs_per_ws = 20
        buckets = int(runs_per_ws / 2)
        for auth_headers in headers:
            async with aclient_for_headers(auth_headers) as aclient:
                session_id = await _create_session(aclient)
                sessions.append(session_id)
                new_runs = []
                for i in range(runs_per_ws):  # 2 runs per bucket
                    run_id = uuid4()
                    start_time = datetime.now(timezone.utc) - timedelta(minutes=30 * i)
                    end_time = start_time + timedelta(seconds=i)
                    new_runs.append(
                        {
                            "name": f"AgentExecutor{i}",
                            "start_time": start_time.isoformat(),
                            "end_time": end_time.isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Agent"},
                            "inputs": {"input": f"input{i}"},
                            "session_id": session_id,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
                        },
                    )

                response = await aclient.post(
                    "/runs/batch",
                    json={
                        "post": new_runs,
                    },
                )
                assert response.status_code == 202
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )

        # Read chart data
        now = (
            datetime.now(timezone.utc) + timedelta(seconds=1)
        )  # add a second to avoid flakiness in case one of the runs was submitted during the current second
        start_time = now - timedelta(hours=buckets)
        custom_charts, _ = await models.charts.fetch.fetch_custom_charts(
            schemas.CustomChartsRequestInternal(
                start_time=start_time,
                end_time=now,
                stride=schemas.TimedeltaInput(hours=1),
                access_scope=schemas.AccessScope.organization,
            ),
            org_auth,
        )
        sections = custom_charts.sections
        assert len(sections) == 1
        charts = sections[0].charts
        assert len(charts) == 1
        chart = next(c for c in charts if str(c.id) == chart_id)
        assert chart.metadata == {"foo": "bar"}
        assert chart.index == 0
        series = chart.series
        # One series for each workspace
        assert len(series) == 3
        for s in series:
            assert s.name == series_name
            assert s.workspace_id is not None
            assert s.filters is not None
            assert s.filters.model_dump() == {
                "session": None,
                "filter": "eq(is_root, true)",
                "trace_filter": None,
                "tree_filter": None,
            }
            assert s.metric == "run_count"
            assert s.feedback_key is None
        assert set(a.tenant_id for a in auths + [auth]) == set(
            [s.workspace_id for s in series]
        )
        data = chart.data
        assert len(data) == buckets * len(series)

        # Chart should include runs in all workspaces in org
        bucket_values_cross_ws: list[int] = [int(runs_per_ws / buckets)] * int(buckets)
        await _assert_chart(
            client,
            str(authed_client.auth.tenant_id),
            prefix,
            chart_id,
            {
                "title": title,
                "description": description,
                "chart_type": chart_type,
            },
            [bucket_values_cross_ws],
            schemas.CustomChartsRequest(
                start_time=start_time,
                end_time=now,
                stride=schemas.TimedeltaInput(hours=1),
            ),
            [str(s.id) for s in series],
        )


async def _verify_cron_charts(
    ws_infos: list[schemas.WorkspaceInfoBare],
    org_auth: OrgAuthInfo,
    buckets: int = 10,
):
    now = datetime.now(timezone.utc) + timedelta(seconds=1)
    start_time = now - timedelta(hours=buckets)
    custom_charts, _ = await models.charts.fetch.fetch_custom_charts(
        schemas.CustomChartsRequestInternal(
            start_time=start_time,
            end_time=now,
            stride=schemas.TimedeltaInput(hours=1),
            access_scope=schemas.AccessScope.organization,
        ),
        org_auth,
    )
    sections = custom_charts.sections
    assert len(sections) == 1
    section = sections[0]
    assert section.title == chart_constants.ORG_USAGE_SECTION_TITLE
    assert section.index == 0
    charts = section.charts
    assert len(charts) == 2

    # One series for the org
    chart_for_org = next(
        c for c in charts if c.title == chart_constants.ORG_USAGE_TITLE
    )
    assert chart_for_org.index == 0
    series = chart_for_org.series
    assert len(series) == 1
    org_series = series[0]
    assert org_series.workspace_id is None
    assert chart_constants.ORG_USAGE_SERIES_NAME == org_series.name
    assert org_series.filters is not None
    assert org_series.filters.model_dump() == {
        "session": None,
        "filter": "eq(is_root, true)",
        "trace_filter": None,
        "tree_filter": None,
    }
    assert org_series.metric == "run_count"
    assert org_series.feedback_key is None
    data = chart_for_org.data
    assert len(data) == buckets

    # One series for each workspace
    chart_by_ws = next(
        c for c in charts if c.title == chart_constants.ORG_USAGE_BY_WS_TITLE
    )
    assert chart_by_ws.index == 1
    series = chart_by_ws.series
    assert len(series) == len(ws_infos)
    for s in series:
        assert s.workspace_id is not None
        ws = next((w for w in ws_infos if w.id == s.workspace_id), None)
        assert ws is not None
        assert ws.display_name == s.name
        assert s.filters is not None
        assert s.filters.model_dump() == {
            "session": None,
            "filter": "eq(is_root, true)",
            "trace_filter": None,
            "tree_filter": None,
        }
        assert s.metric == "run_count"
        assert s.feedback_key is None
    data = chart_by_ws.data
    assert len(data) == buckets * len(series)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_charts_cron(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test the auto-created organization-scoped chart for trace counts by workspace."""
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, org_config_updates={"enable_org_usage_charts": True}
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        org_auth = authed_client.org_auth

        ws_name = await db_asyncpg.fetchval(
            "select display_name from tenants where id = $1", auth.tenant_id
        )
        ws_info = schemas.WorkspaceInfoBare(id=auth.tenant_id, display_name=ws_name)

        # Should have chart created for single workspace
        await sync_org_charts(filter_org_ids=[org_auth.organization_id])
        await _verify_cron_charts([ws_info], org_auth)

        # Re-syncing should be a no-op, updating with the same information
        await sync_org_charts(filter_org_ids=[org_auth.organization_id])
        await _verify_cron_charts([ws_info], org_auth)

        # Create additional workspaces
        workspaces = await create_workspaces(client, org_auth.organization_id, 2)
        ws_infos: list[schemas.WorkspaceInfoBare] = [ws_info]
        for workspace in workspaces:
            ws_infos.append(
                schemas.WorkspaceInfoBare(
                    id=workspace.id, display_name=workspace.display_name
                )
            )

        # Verify additional chart series
        await sync_org_charts(filter_org_ids=[org_auth.organization_id])
        await _verify_cron_charts(ws_infos, org_auth)


def _simple_run(
    run_id: UUID | str,
    start_time: datetime,
    end_time: datetime,
    session_id: UUID | str,
) -> dict[str, Any]:
    dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
    return {
        "name": "AgentExecutor",
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "extra": {"foo": "bar"},
        "error": None,
        "dotted_order": dotted_order,
        "id": str(run_id),
        "trace_id": str(run_id),
        "run_type": "tool",
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"input": "foo"},
        "session_id": str(session_id),
        "outputs": None,
    }


def _assert_dashboard_structure(dashboard: dict, *, session_id):
    assert dashboard["session_id"] == session_id
    assert dashboard["charts"] == []  # no root‑level charts
    assert len(dashboard["sub_sections"]) == 6

    subs_by_title = {s["title"]: s for s in dashboard["sub_sections"]}

    # Volume
    volume = subs_by_title["Volume"]
    assert {c["id"] for c in volume["charts"]} == {
        "chart-trace-count",
        "chart-trace-latency",
        "chart-error-rate-per-run",
    }

    # Latency
    latency = subs_by_title["LLM Calls"]
    assert {c["id"] for c in latency["charts"]} == {
        "chart-llm-call-count",
        "chart-llm-call-latency",
    }

    # Cost & Tokens
    cost = subs_by_title["Cost & Tokens"]
    assert {c["id"] for c in cost["charts"]} == {
        "chart-total-cost",
        "chart-cost-per-trace",
        "chart-completion-token-count",
        "chart-completion-tokens-per-trace",
        "chart-input-token-count",
        "chart-input-tokens-per-trace",
    }

    # Feedback
    feedback = subs_by_title["Feedback Scores"]
    assert len(feedback["charts"]) == 5

    # Tools
    tools = subs_by_title["Tools"]
    assert [c["id"] for c in tools["charts"]] == [
        "chart-tools-run-count",
        "chart-tools-latency",
        "chart-tools-error-rate",
    ]

    # Runs
    runs = subs_by_title["Run Types"]
    assert [c["id"] for c in runs["charts"]] == [
        "chart-runs-run-count",
        "chart-runs-latency",
        "chart-runs-error-rate",
    ]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_prebuilt_session_dashboard(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    prefix = ORG_SCOPED_TO_ENDPOINT_PREFIX[False]
    now = datetime.now(timezone.utc) + timedelta(seconds=1)
    run_start_time = datetime.now(timezone.utc) - timedelta(seconds=60)
    run_end_time = datetime.now(timezone.utc) - timedelta(seconds=30)

    def sort_dashboard_data(d):
        for sub_section in d["sub_sections"]:
            for chart in sub_section["charts"]:
                chart["data"] = sorted(
                    chart["data"], key=lambda x: (x["series_id"], x["timestamp"])
                )
        return d

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        session_id = await _create_session(client)
        dashboard_create = schemas.CustomChartsSectionCreate(
            title="dashboard_2",
            description="new_dashboard",
            index=1,
        )
        dashboard_id = await _create_section(client, prefix, dashboard_create)

        # Set the session_id (this is not yet possible from the API)
        await db_asyncpg.execute(
            """
            UPDATE custom_chart_section SET session_id = $1 WHERE id = $2 
            """,
            session_id,
            dashboard_id,
        )

        response = await client.delete(
            f"{prefix}/section/{dashboard_id}",
        )
        # Can't delete dashboards with a session_id
        assert response.status_code == 400

        # post a run
        run_id = uuid4()
        runs_post = _simple_run(run_id, run_start_time, run_end_time, session_id)

        # post feedback
        feedback_data = []

        # Add 2 objects for keys 1-4
        for key in range(1, 5):
            for _ in range(2):
                feedback_data.append(
                    {
                        "id": str(uuid4()),
                        "run_id": str(run_id),
                        "trace_id": str(run_id),
                        "key": f"feedback_key_{key}",
                        "score": 1,
                    }
                )
        # Add 2 categorical feedback objects for key 5
        for _ in range(2):
            feedback_data.append(
                {
                    "id": str(uuid4()),
                    "run_id": str(run_id),
                    "trace_id": str(run_id),
                    "key": "feedback_key_5",
                    "value": "foo",
                }
            )
        # Add 1 object for key 6
        feedback_data.append(
            {
                "id": str(uuid4()),
                "run_id": str(run_id),
                "trace_id": str(run_id),
                "key": "feedback_key_6",
                "score": 1,
            }
        )

        await post_runs(
            "/runs/multipart",
            client,
            post=[runs_post],
            patch_runs=[],
            feedback=feedback_data,
        )

        await wait_until_task_queue_empty()

        # Grab the session-scoped prebuilt dashboard
        response = await client.post(
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (now - timedelta(hours=3)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        dashboard = response.json()
        _assert_dashboard_structure(dashboard, session_id=session_id)

        run_id_2 = uuid4()
        runs_2_post = _simple_run(run_id_2, run_start_time, run_end_time, session_id)

        # post feedback
        feedback_data_2 = []

        # Add 2 objects for keys 1-4
        for key in range(1, 5):
            for _ in range(2):
                feedback_data_2.append(
                    {
                        "id": str(uuid4()),
                        "run_id": str(run_id_2),
                        "trace_id": str(run_id_2),
                        "key": f"feedback_key_{key}",
                        "score": 1,
                    }
                )

        await post_runs(
            "/runs/multipart",
            client,
            post=[runs_2_post],
            patch_runs=[],
            feedback=feedback_data_2,
        )

        await wait_until_task_queue_empty()

        # Test the cache
        # Fuzz the start/end times by 1 second to ensure rounding is happening
        response = await client.post(
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (
                    now - timedelta(hours=3) + timedelta(seconds=1)
                ).isoformat(),
                "end_time": (now + timedelta(seconds=1)).isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        dashboard_cached = response.json()
        assert sort_dashboard_data(dashboard) == sort_dashboard_data(dashboard_cached)

        async with aconnect_sse(
            client,
            "POST",
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (
                    now - timedelta(hours=3) + timedelta(seconds=1)
                ).isoformat(),
                "end_time": (now + timedelta(seconds=1)).isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        ) as sse:
            patch: list[list[dict]] = []
            async for event in sse.aiter_sse():
                if event.event == "data":
                    patch.append(event.json()["patch"])
                elif event.event == "end":
                    pass
                else:
                    assert False, f"Unexpected event type {event}"

            raw_patched_dict = jsonpatch.apply_patch(
                {}, [op for ops in patch for op in ops]
            )

            dashboard_streamed = schemas.CustomChartsSection(
                **raw_patched_dict
            ).model_dump(mode="json")

            assert sort_dashboard_data(dashboard_streamed) == sort_dashboard_data(
                dashboard
            )

        # Test that fuzzing start/end times by more than a minute doesn't hit the cache
        response = await client.post(
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (
                    now - timedelta(hours=3) + timedelta(minutes=2)
                ).isoformat(),
                "end_time": (now + timedelta(minutes=2)).isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        dashboard_not_cached = response.json()
        assert dashboard != dashboard_not_cached

        # Grab the session-scoped prebuilt dashboard with None end_time
        response = await client.post(
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (now - timedelta(hours=3)).isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 200
        dashboard = response.json()
        _assert_dashboard_structure(dashboard, session_id=session_id)

        # Get the session-scoped prebuilt dashboard via list endpoint
        response = await client.get(
            f"{prefix}/section",
            params={"limit": 1, "sort_by": "created_at", "sort_by_desc": False},
        )
        assert response.status_code == 200
        assert response.json()[0]["session_id"] == session_id

        response = await client.delete(
            f"/sessions/{session_id}",
        )
        assert response.status_code == 202

        response = await client.post(
            f"{prefix}/section/{dashboard_id}",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        # Dashboard should no longer exist
        assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_fetch_section(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    snapshot: SnapshotAssertion,
) -> None:
    path = ORG_SCOPED_TO_ENDPOINT_PREFIX[False]
    now = datetime.now(timezone.utc) + timedelta(seconds=1)
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        http_tenant_one = authed_client.client
        await _clear_all_charts(http_tenant_one, path)
        session_id = await _create_session(http_tenant_one)

        # Create the runs
        new_runs: list = []
        feedbacks: list = []
        now = datetime.fromisoformat("2025-04-16T20:49:34")
        for i in range(20):  # 2 runs per bucket
            run_id = uuid4()
            start_time = now - timedelta(minutes=30 * i, seconds=1)
            end_time = start_time + timedelta(seconds=i)
            new_runs.append(
                {
                    "name": f"AgentExecutor{i}",
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "extra": {"metadata": {"foo": str(i % 5)}},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Agent"},
                    "inputs": {"input": f"input{i}"},
                    "session_id": session_id,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
                },
            )
            feedbacks.extend(
                [
                    {
                        "id": str(uuid4()),
                        "trace_id": str(run_id),
                        "run_id": str(run_id),
                        "key": "correctness",
                        "score": i / 20,
                    },
                    {
                        "id": str(uuid4()),
                        "trace_id": str(run_id),
                        "run_id": str(run_id),
                        "key": "color",
                        "value": ["blue", "red", "green", "yellow"][i % 4],
                    },
                ]
            )
        await post_runs(
            "/runs/multipart|go", http_tenant_one, post=new_runs, feedback=feedbacks
        )
        # response = await http_tenant_one.post("/runs/batch", json={"post": new_runs})
        # assert response.status_code == 202
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )
        for feedback in feedbacks:
            fetched = await models.feedback.fetch.fetch_feedback(
                UUID(feedback["id"]), authed_client.auth
            )
            assert feedback["key"] == fetched["key"]
            if "value" in feedback:
                assert feedback["value"] == fetched["value"]
            if "score" in feedback:
                assert float(feedback["score"]) == float(fetched["score"])

        # Create dashbaord
        dashboard_create = schemas.CustomChartsSectionCreate(title="test", index=0)
        section_id = await _create_section(http_tenant_one, path, dashboard_create)

        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Trace Count",
                "description": "",
                "chart_type": "line",
                "section_id": section_id,
                "index": 0,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                    }
                ],
            },
        )
        assert response.status_code == 200
        assert response.json()["index"] == 0

        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Avg Latency",
                "description": "",
                "chart_type": "line",
                "section_id": section_id,
                "index": 0,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "latency_avg",
                    }
                ],
            },
        )
        assert response.status_code == 200
        assert response.json()["index"] == 0

        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Feeback: correctness",
                "description": "",
                "chart_type": "line",
                "section_id": section_id,
                "index": 1,
                "series": [
                    {
                        "name": "avg score",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "feedback_values",
                        "feedback_key": "correctness",
                    }
                ],
            },
        )
        assert response.status_code == 200
        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Feeback: color",
                "description": "",
                "chart_type": "line",
                "section_id": section_id,
                "index": 2,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "feedback_values",
                        "feedback_key": "color",
                    }
                ],
            },
        )
        assert response.status_code == 200

        response = await http_tenant_one.post(
            f"{path}/section/{section_id}",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {"hours": 1},
            },
        )
        assert response.status_code == 200
        data = _replace_ids(response.json())
        assert any(
            c["series"][0]["metric"] == "feedback_values" for c in data["charts"]
        )
        for chart in data["charts"]:
            if chart["series"][0]["metric"] == "feedback_values":
                total_feedbacks = 0
                for x in chart["data"]:
                    key, stats = list(x["value"].items())[0]
                    if key == "color":
                        curr_feedbacks = sum(stats["values"].values())
                        assert stats["n"] == curr_feedbacks
                    total_feedbacks += stats["n"]
                assert total_feedbacks == len(new_runs)
        assert data == snapshot(name="no_group_by", matcher=_json_matcher), (
            "no_group_by snapshot failed"
        )

        response = await http_tenant_one.post(
            f"{path}/section/{section_id}",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": (now + timedelta(seconds=1)).isoformat(),
                "stride": {"hours": 1},
                "group_by": {"attribute": "metadata", "path": "foo"},
            },
        )
        assert response.status_code == 200
        data = _replace_ids(response.json())
        assert any(
            c["series"][0]["metric"] == "feedback_values" for c in data["charts"]
        )
        for chart in data["charts"]:
            if chart["series"][0]["metric"] == "feedback_values":
                total_feedbacks = 0
                for x in chart["data"]:
                    key, stats = list(x["value"].items())[0]
                    if key == "color":
                        curr_feedbacks = sum(stats["values"].values())
                        assert stats["n"] == curr_feedbacks
                    total_feedbacks += stats["n"]
                assert total_feedbacks == len(new_runs)
        assert data == snapshot(name="group_by_metadata", matcher=_json_matcher), (
            "group_by_metadata snapshot failed"
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_fetch_prebuilt(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    snapshot: SnapshotAssertion,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        http_tenant_one = authed_client.client
        session_id = await _create_session(http_tenant_one)

        # Create the runs
        new_runs: list = []
        feedbacks: list = []
        now = datetime.fromisoformat("2025-04-16T20:49:34")
        for i in range(20):  # 2 runs per bucket
            run_id = uuid4()
            start_time = now - timedelta(minutes=30 * i, seconds=1)
            end_time = start_time + timedelta(seconds=i)
            new_runs.append(
                {
                    "name": f"AgentExecutor{i}",
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "extra": {"metadata": {"foo": str(i % 5)}},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Agent"},
                    "inputs": {"input": f"input{i}"},
                    "session_id": session_id,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{str(run_id)}",
                },
            )
            feedbacks.extend(
                [
                    {
                        "id": str(uuid4()),
                        "trace_id": str(run_id),
                        "run_id": str(run_id),
                        "key": "correctness",
                        "score": i / 20,
                    },
                    {
                        "id": str(uuid4()),
                        "trace_id": str(run_id),
                        "run_id": str(run_id),
                        "key": "color",
                        "value": ["blue", "red", "green", "yellow"][i % 4],
                    },
                ]
            )
        await post_runs(
            "/runs/multipart|go", http_tenant_one, post=new_runs, feedback=feedbacks
        )
        # response = await http_tenant_one.post("/runs/batch", json={"post": new_runs})
        # assert response.status_code == 202
        await _wait_for_write_queue(
            wait_until_task_queue_empty, str(authed_client.auth.tenant_id)
        )
        for feedback in feedbacks:
            fetched = await models.feedback.fetch.fetch_feedback(
                UUID(feedback["id"]), authed_client.auth
            )
            assert feedback["key"] == fetched["key"]
            if "value" in feedback:
                assert feedback["value"] == fetched["value"]
            if "score" in feedback:
                assert float(feedback["score"]) == float(fetched["score"])

        response = await http_tenant_one.post(
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {"hours": 1},
            },
        )
        assert response.status_code == 200
        # data = _replace_ids(response.json())
        # assert data == snapshot(name="no_group_by", matcher=_json_matcher), (
        #     "no_group_by snapshot failed"
        # )

        response = await http_tenant_one.post(
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": (now + timedelta(seconds=1)).isoformat(),
                "stride": {"hours": 1},
                "group_by": {"attribute": "metadata", "path": "foo"},
            },
        )
        assert response.status_code == 200
        # data = _replace_ids(response.json())
        # assert data == snapshot(name="group_by_metadata", matcher=_json_matcher), (
        #     "group_by_metadata snapshot failed"
        # )


def _replace_ids(data: Any) -> Any:
    if isinstance(data, dict):
        new_data = {}
        for k, v in data.items():
            if k == "id" or k.endswith("_id") and v and isinstance(v, str):
                split_id = v.split(":")
                v = ":".join(["uuid_placeholder", *split_id[1:]])
            elif k == "session":
                v = ["uuid_placeholder"]
            elif k == "description":
                v = "description_placeholder"
            new_data[k] = _replace_ids(v)
        return new_data
    elif isinstance(data, list):
        return [_replace_ids(x) for x in data]
    else:
        return data


def _json_matcher(data: Any, path: tuple) -> Any:
    if isinstance(data, dict):
        return {k: data[k] for k in sorted(data)}
    elif isinstance(data, list):
        return sorted(data, key=lambda x: orjson.dumps(x, option=orjson.OPT_SORT_KEYS))
    else:
        return data


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_series_group_by_crud(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    path = ORG_SCOPED_TO_ENDPOINT_PREFIX[False]
    now = datetime.now(timezone.utc) + timedelta(seconds=1)
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        http_tenant_one = authed_client.client
        await _clear_all_charts(http_tenant_one, path)
        session_id = await _create_session(http_tenant_one)

        # Incorrect group by format should fail
        # 1. Incorrect attribute
        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Trace Count",
                "description": "",
                "chart_type": "line",
                "index": 0,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "group_by": {"attribute": "name2"},
                    }
                ],
            },
        )
        assert response.status_code == 422
        # 2. Incorrect path
        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Trace Count",
                "description": "",
                "chart_type": "line",
                "index": 0,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "group_by": {"attribute": "name", "path": "foo"},
                    }
                ],
            },
        )
        assert response.status_code == 422

        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Trace Count",
                "description": "",
                "chart_type": "line",
                "index": 0,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "group_by": {"attribute": "name"},
                    }
                ],
            },
        )
        assert response.status_code == 200
        assert response.json()["index"] == 0
        section_id_1 = response.json()["id"]

        response = await http_tenant_one.post(
            f"{path}/{section_id_1}",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": (now + timedelta(seconds=1)).isoformat(),
                "stride": {"hours": 1},
            },
        )
        assert response.status_code == 200
        section = response.json()
        assert section["series"][0]["group_by"] == {
            "attribute": "name",
            "path": None,
            "set_by": None,
        }

        # Can't have multiple series when one has a group by
        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Trace Count",
                "description": "",
                "chart_type": "line",
                "index": 0,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "group_by": {"attribute": "name"},
                    },
                    {
                        "name": "run count2",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                    },
                ],
            },
        )
        assert response.status_code == 422

        response = await http_tenant_one.post(
            f"{path}/create",
            json={
                "title": "Trace Count",
                "description": "",
                "chart_type": "line",
                "index": 0,
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                    },
                    {
                        "name": "run count2",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                    },
                ],
            },
        )
        assert response.status_code == 200
        assert response.json()["index"] == 0
        section_id_2 = response.json()["id"]

        response = await http_tenant_one.patch(
            f"{path}/{section_id_2}",
            json={
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "group_by": {"attribute": "name"},
                    },
                    {
                        "name": "run count2",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                    },
                ],
            },
        )
        assert response.status_code == 422

        response = await http_tenant_one.patch(
            f"{path}/{section_id_2}",
            json={
                "series": [
                    {
                        "name": "run count",
                        "filters": {
                            "session": [session_id],
                            "filter": "eq(is_root, true)",
                            "trace_filter": None,
                            "tree_filter": None,
                        },
                        "metric": "run_count",
                        "group_by": {"attribute": "name"},
                    },
                ],
            },
        )
        assert response.status_code == 200

        response = await http_tenant_one.post(
            f"{path}/{section_id_2}",
            json={
                "start_time": (now - timedelta(hours=10)).isoformat(),
                "end_time": (now + timedelta(seconds=1)).isoformat(),
                "stride": {"hours": 1},
            },
        )
        assert response.status_code == 200
        section = response.json()
        assert section["series"][0]["group_by"] == {
            "attribute": "name",
            "path": None,
            "set_by": None,
        }


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_top_k_feedback_cached(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    from app.models.charts.cached_fetch import cached_fetch_top_k_feedback_key_types

    now = datetime.now(timezone.utc)
    run_start_time = now - timedelta(minutes=60)
    run_end_time = now - timedelta(minutes=30)

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        session_id = await _create_session(client)

        # post a run
        run_id = uuid4()
        runs_post = _simple_run(run_id, run_start_time, run_end_time, session_id)

        # post feedback
        feedback_data = []

        # Add 2 objects for keys 1-3
        for key in range(1, 4):
            for _ in range(2):
                feedback_data.append(
                    {
                        "id": str(uuid4()),
                        "run_id": str(run_id),
                        "trace_id": str(run_id),
                        "key": f"feedback_key_{key}",
                        "score": 1,
                    }
                )
        # Add 2 categorical feedback objects for key 4
        for _ in range(2):
            feedback_data.append(
                {
                    "id": str(uuid4()),
                    "run_id": str(run_id),
                    "trace_id": str(run_id),
                    "key": "feedback_key_4",
                    "value": "foo",
                }
            )

        await post_runs(
            "/runs/multipart",
            client,
            post=[runs_post],
            patch_runs=[],
            feedback=feedback_data,
        )

        await wait_until_task_queue_empty()

        top_k_stats = await cached_fetch_top_k_feedback_key_types(
            authed_client.auth.tenant_id,
            now - timedelta(hours=3),
            now,
            session_id,
            auth=authed_client.auth,
        )

        # post another run
        run_id_2 = uuid4()
        runs_post_2 = _simple_run(run_id_2, run_start_time, run_end_time, session_id)

        # post feedback
        feedback_data_2 = []

        # Add 2 objects for keys 1-3
        for key in range(1, 4):
            for _ in range(2):
                feedback_data_2.append(
                    {
                        "id": str(uuid4()),
                        "run_id": str(run_id_2),
                        "trace_id": str(run_id_2),
                        "key": f"feedback_key_{key}",
                        "score": 1,
                    }
                )
        # Add 2 categorical feedback objects for key 5
        for _ in range(2):
            feedback_data_2.append(
                {
                    "id": str(uuid4()),
                    "run_id": str(run_id_2),
                    "trace_id": str(run_id_2),
                    "key": "feedback_key_5",
                    "value": "foo",
                }
            )

        await post_runs(
            "/runs/multipart",
            client,
            post=[runs_post_2],
            patch_runs=[],
            feedback=feedback_data_2,
        )

        await wait_until_task_queue_empty()

        # Test the cache
        cached_top_k_stats = await cached_fetch_top_k_feedback_key_types(
            authed_client.auth.tenant_id,
            now - timedelta(hours=3),
            now,
            session_id,
            auth=authed_client.auth,
        )
        assert cached_top_k_stats == top_k_stats

        new_cached_top_k_stats = await cached_fetch_top_k_feedback_key_types(
            authed_client.auth.tenant_id,
            now - timedelta(hours=3) - timedelta(minutes=2),
            now - timedelta(minutes=2),
            session_id,
            auth=authed_client.auth,
        )
        assert new_cached_top_k_stats != top_k_stats
