"""Test Prompt Webhooks endpoints."""

import datetime
from dataclasses import asdict
from uuid import uuid4

import asyncpg
import pytest

from app.hub.schema import CommitWithLookups
from app.schemas import PromptWebhookPayload
from app.tests.utils import fresh_tenant_client, random_lower_string

pytestmark = pytest.mark.anyio


@pytest.fixture
async def webhook_payload() -> dict:
    """Create a test webhook payload."""
    return {
        "url": f"https://example.com/webhook/{random_lower_string()}",
        "include_prompts": [str(uuid4()), str(uuid4())],
        "exclude_prompts": [str(uuid4())],
        "headers": {"Authorization": "Bearer test-token"},
        "triggers": ["commit"],
    }


@pytest.fixture
async def commit() -> CommitWithLookups:
    """Create a test commit."""
    return CommitWithLookups(
        id="123e4567-e89b-12d3-a456-************",
        repo_id="123e4567-e89b-12d3-a456-************",
        commit_hash="123e4567-e89b-12d3-a456-************",
        manifest={},
        created_at=datetime.datetime(2021, 1, 1, 0, 0, 0),
        full_name="John Doe",
        updated_at=datetime.datetime(2021, 1, 1, 0, 0, 0),
        example_run_ids=[],
        num_downloads=0,
        num_views=0,
    )


async def test_create_prompt_webhook(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    webhook_payload: dict,
) -> None:
    """Test creating a prompt webhook."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        response = await authed_client.client.post(
            "/prompt-webhooks", json=webhook_payload
        )
        assert response.status_code == 200
        data = response.json()
        assert data["url"] == webhook_payload["url"]
        assert data["include_prompts"] == webhook_payload["include_prompts"]
        assert data["exclude_prompts"] == webhook_payload["exclude_prompts"]
        assert data["headers"] == webhook_payload["headers"]
        assert data["triggers"] == webhook_payload["triggers"]


async def test_list_prompt_webhooks(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    webhook_payload: dict,
) -> None:
    """Test listing prompt webhooks."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Create a webhook first
        create_response = await authed_client.client.post(
            "/prompt-webhooks", json=webhook_payload
        )
        assert create_response.status_code == 200
        created_webhook = create_response.json()

        # List webhooks
        response = await authed_client.client.get("/prompt-webhooks")
        assert response.status_code == 200
        data = response.json()

        # Find our created webhook in the list
        found_webhook = next(
            (w for w in data if w["id"] == created_webhook["id"]), None
        )
        assert found_webhook is not None
        assert found_webhook["url"] == webhook_payload["url"]
        assert found_webhook["include_prompts"] == webhook_payload["include_prompts"]
        assert found_webhook["exclude_prompts"] == webhook_payload["exclude_prompts"]
        assert found_webhook["headers"] == webhook_payload["headers"]
        assert found_webhook["triggers"] == webhook_payload["triggers"]


async def test_get_prompt_webhook(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    webhook_payload: dict,
) -> None:
    """Test getting a specific prompt webhook."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Create a webhook first
        create_response = await authed_client.client.post(
            "/prompt-webhooks", json=webhook_payload
        )
        assert create_response.status_code == 200
        webhook_id = create_response.json()["id"]

        # Get the webhook
        response = await authed_client.client.get(f"/prompt-webhooks/{webhook_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == webhook_id
        assert data["url"] == webhook_payload["url"]


async def test_get_nonexistent_prompt_webhook(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test getting a non-existent prompt webhook."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        response = await authed_client.client.get(f"/prompt-webhooks/{uuid4()}")
        assert response.status_code == 404


async def test_update_prompt_webhook(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    webhook_payload: dict,
) -> None:
    """Test updating a prompt webhook."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Create a webhook first
        create_response = await authed_client.client.post(
            "/prompt-webhooks", json=webhook_payload
        )
        assert create_response.status_code == 200
        webhook_id = create_response.json()["id"]

        # Update the webhook
        update_payload = {
            "url": f"https://example.com/webhook/{random_lower_string()}",
            "headers": {"Authorization": "Bearer new-token"},
        }
        response = await authed_client.client.patch(
            f"/prompt-webhooks/{webhook_id}", json=update_payload
        )
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == webhook_id
        assert data["url"] == update_payload["url"]
        assert data["headers"] == update_payload["headers"]
        # Original values should be preserved
        assert data["include_prompts"] == webhook_payload["include_prompts"]
        assert data["exclude_prompts"] == webhook_payload["exclude_prompts"]
        assert data["triggers"] == webhook_payload["triggers"]


async def test_delete_prompt_webhook(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    webhook_payload: dict,
) -> None:
    """Test deleting a prompt webhook."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Create a webhook first
        create_response = await authed_client.client.post(
            "/prompt-webhooks", json=webhook_payload
        )
        assert create_response.status_code == 200
        webhook_id = create_response.json()["id"]

        # Delete the webhook
        response = await authed_client.client.delete(f"/prompt-webhooks/{webhook_id}")
        assert response.status_code == 204

        # Verify it's deleted
        get_response = await authed_client.client.get(f"/prompt-webhooks/{webhook_id}")
        assert get_response.status_code == 404


async def test_create_prompt_webhook_validation(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test validation when creating a prompt webhook."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Test missing required field
        invalid_payload = {
            "include_prompts": ["prompt1"],
            "headers": {"Authorization": "Bearer test-token"},
        }
        response = await authed_client.client.post(
            "/prompt-webhooks", json=invalid_payload
        )
        assert response.status_code == 422

        # Test invalid endpoint URL
        invalid_payload = {
            "url": "not-a-url",
            "include_prompts": ["prompt1"],
            "headers": {"Authorization": "Bearer test-token"},
        }
        response = await authed_client.client.post(
            "/prompt-webhooks", json=invalid_payload
        )
        assert response.status_code == 422


async def test_test_prompt_webhook(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    webhook_payload: dict,
    commit: CommitWithLookups,
) -> None:
    """Test testing a prompt webhook."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        payload_dict = PromptWebhookPayload(
            prompt_id=str(commit.repo_id),
            prompt_name="test-prompt",
            manifest=commit.manifest,
            commit_hash=commit.commit_hash,
            created_at=commit.created_at.isoformat(),
            created_by=commit.full_name or "",
        )

        request_payload = {
            "webhook": webhook_payload,
            "payload": asdict(payload_dict),
        }

        response = await authed_client.client.post(
            "/prompt-webhooks/test", json=request_payload
        )
        assert response.status_code == 200


async def test_prompt_webhook_limit(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    webhook_payload: dict,
) -> None:
    """Test that creating webhooks beyond the limit fails."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Create one webhook (the limit is 1)
        response = await authed_client.client.post(
            "/prompt-webhooks", json=webhook_payload
        )
        assert response.status_code == 200

        # Try to create one more webhook
        response = await authed_client.client.post(
            "/prompt-webhooks", json=webhook_payload
        )
        assert response.status_code == 400
