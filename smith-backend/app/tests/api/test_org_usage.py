"""Tests for the organization usage endpoint."""

import datetime
import json as json_lib
from contextlib import asynccontextmanager
from typing import Any, Dict, Optional
from unittest.mock import patch

import httpx
import pytest
from lc_config.service_communication_settings import ServiceName

from app.tests.utils import fresh_tenant_client


class MockBeaconClient:
    """Mock client for beacon service."""

    def __init__(self, should_fail: bool = False):
        self.should_fail = should_fail

    async def post(
        self,
        url: str,
        json: Any | None = None,
        **kwargs,
    ) -> httpx.Response:
        """Mock post method for beacon client."""

        if self.should_fail:
            return httpx.Response(
                status_code=500,
                content=b'{"error": "Internal Server Error"}',
                request=httpx.Request(method="POST", url=url),
            )

        # Return mock usage data matching schemas.OrgUsage structure
        usage_data = [
            {
                "customer_id": "customer-123",
                "billable_metric_id": "metric-1",
                "billable_metric_name": "api_calls",
                "start_timestamp": "2025-05-01T00:00:00Z",
                "end_timestamp": "2025-05-02T00:00:00Z",
                "value": None,
                "groups": {"tenant-1": 100, "tenant-2": 50},
            },
            {
                "customer_id": "customer-123",
                "billable_metric_id": "metric-2",
                "billable_metric_name": "tokens",
                "start_timestamp": "2025-05-01T00:00:00Z",
                "end_timestamp": "2025-05-02T00:00:00Z",
                "value": None,
                "groups": {"tenant-1": 3000, "tenant-2": 2000},
            },
        ]

        return httpx.Response(
            status_code=200,
            content=json_lib.dumps(usage_data).encode(),
            request=httpx.Request(method="POST", url=url),
        )


@pytest.mark.asyncio
async def test_get_org_usage_self_hosted_success(db_asyncpg, use_api_key):
    """Test get_org_usage when IS_SELF_HOSTED is True with successful beacon response."""
    mock_beacon = MockBeaconClient(should_fail=False)

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up test parameters
        starting_on = datetime.datetime(2025, 5, 1, tzinfo=datetime.timezone.utc)
        ending_before = datetime.datetime(2025, 5, 10, tzinfo=datetime.timezone.utc)

        # Create a patched service client that yields our mock beacon client
        @asynccontextmanager
        async def get_service_client_patched(
            service_name: ServiceName,
            headers: Optional[Dict[str, str]] = None,
        ):
            assert service_name == ServiceName.BEACON
            yield mock_beacon

        # Mock the IS_SELF_HOSTED setting and beacon client
        with (
            patch("app.config.settings.IS_SELF_HOSTED", True),
            patch(
                "app.models.organizations.usage.get_service_client",
                get_service_client_patched,
            ),
        ):
            # Make the request
            response = await client.get(
                "/orgs/current/billing/usage",
                params={
                    "starting_on": starting_on.isoformat(),
                    "ending_before": ending_before.isoformat(),
                    "on_current_plan": "true",
                },
            )

            # Verify the response
            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)
            assert len(data) == 2

            # Verify the structure of the response data
            for item in data:
                assert "customer_id" in item
                assert "billable_metric_id" in item
                assert "billable_metric_name" in item
                assert "start_timestamp" in item
                assert "end_timestamp" in item
                assert "groups" in item
                assert isinstance(item["groups"], dict)


@pytest.mark.asyncio
async def test_get_org_usage_self_hosted_error(db_asyncpg, use_api_key):
    """Test get_org_usage when IS_SELF_HOSTED is True with beacon error response."""
    mock_beacon = MockBeaconClient(should_fail=True)

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up test parameters
        starting_on = datetime.datetime(2025, 5, 1, tzinfo=datetime.timezone.utc)
        ending_before = datetime.datetime(2025, 5, 10, tzinfo=datetime.timezone.utc)

        # Create a patched service client that yields our mock beacon client
        @asynccontextmanager
        async def get_service_client_patched(
            service_name: ServiceName,
            headers: Optional[Dict[str, str]] = None,
        ):
            assert service_name == ServiceName.BEACON
            yield mock_beacon

        # Mock the IS_SELF_HOSTED setting and beacon client
        with (
            patch("app.config.settings.IS_SELF_HOSTED", True),
            patch(
                "app.models.organizations.usage.get_service_client",
                get_service_client_patched,
            ),
        ):
            # Make the request
            response = await client.get(
                "/orgs/current/billing/usage",
                params={
                    "starting_on": starting_on.isoformat(),
                    "ending_before": ending_before.isoformat(),
                    "on_current_plan": "true",
                },
            )

            # Verify the response - should be an error
            assert response.status_code == 502
            assert "Failed to fetch usage" in response.json()["detail"]
