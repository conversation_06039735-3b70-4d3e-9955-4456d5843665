from lc_config.settings import (
    ENV_FILE_PATH,
    SECRETS_DIR_PATH,
    shared_settings,
)

from app.config import Settings


def test_postgres_protocol(monkeypatch):
    monkeypatch.setenv("POSTGRES_DATABASE_URI", "**************************/envdb")
    LANGCHAIN_ENV = shared_settings.LANGCHAIN_ENV
    settings = Settings(  # type: ignore
        LANGCHAIN_ENV=LANGCHAIN_ENV,
        _env_file=ENV_FILE_PATH,  # type: ignore
        _secrets_dir=SECRETS_DIR_PATH,  # type: ignore
    )
    assert (
        settings.SQLALCHEMY_DATABASE_URI == "postgresql+psycopg://u:p@remote:5432/envdb"
    )
    assert settings.ASYNCPG_DATABASE_URI == "****************************/envdb"

    monkeypatch.setenv("POSTGRES_DATABASE_URI", "****************************/envdb")
    LANGCHAIN_ENV = shared_settings.LANGCHAIN_ENV
    settings = Settings(  # type: ignore
        LANGCHAIN_ENV=LANGCHAIN_ENV,
        _env_file=ENV_FILE_PATH,  # type: ignore
        _secrets_dir=SECRETS_DIR_PATH,  # type: ignore
    )
    assert (
        settings.SQLALCHEMY_DATABASE_URI == "postgresql+psycopg://u:p@remote:5432/envdb"
    )
    assert settings.ASYNCPG_DATABASE_URI == "****************************/envdb"
