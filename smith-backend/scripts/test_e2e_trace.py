import asyncio
import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

import jwt
from lc_config.service_communication_settings import ServiceName
from lc_database.database import asyncpg_conn
from lc_database.service_client import ServiceClient, get_service_client

# JWT Info
JWT_TOKEN_EXPIRY: int = 60 * 60  # 1 hour expiry
JWT_SECRET = os.environ.get("X_SERVICE_AUTH_JWT_SECRET")

SERVICE_NAME: str = "unspecified"
X_SERVICE_KEY_HEADER = "X-Service-Key"


async def test_langsmith_e2e_trace() -> bool:
    """
    Run end-to-end test of LangSmith tracing functionality.
    Creates a parent and child run, simulates an LLM response, and validates the trace.

    Returns:
        bool: True if test passes, False if any errors occur
    """
    try:
        # Fetch tenant and org ids from postgres.
        async with asyncpg_conn() as db:
            tenant_id, org_id = await db.fetchrow(
                "SELECT id, organization_id FROM tenants"
            )
            if not tenant_id or not org_id:
                raise Exception("ERROR: No tenant or org id found in postgres")

        # Use tenant and org ids + JWT secret from environment to generate a JWT token
        jwt_token = generate_jwt_token(str(tenant_id), str(org_id))
        headers = {X_SERVICE_KEY_HEADER: jwt_token}

        # Create service clients that will be reused throughout the test
        async with (
            get_service_client(
                service_name=ServiceName.GO, headers=headers
            ) as go_client,
            get_service_client(
                service_name=ServiceName.BACKEND, headers=headers
            ) as backend_client,
        ):
            await validate_jwt_token(go_client)

            # Create parent run - Chat Pipeline
            parent_run_id = uuid4()
            await post_run(
                go_client,
                parent_run_id,
                "Chat Pipeline",
                "chain",
                {"question": "What is a LangSmith trace?"},
            )

            # Create child run - LLM Call
            child_run_id = uuid4()
            await post_run(
                go_client,
                child_run_id,
                "OpenAI Call",
                "llm",
                {
                    "messages": [
                        {"role": "user", "content": "What is a LangSmith trace?"}
                    ]
                },
                parent_run_id,
            )
            print("INFO: Created parent and child runs", flush=True)

            # Simulate LLM response
            llm_response: Dict[str, Any] = {
                "generations": [
                    {
                        "text": "A LangSmith trace is a detailed record of the execution of a LangChain application. It captures inputs, outputs, and metadata at each step of your language model pipeline, allowing you to monitor, debug, and optimize your application's performance. Traces show you the complete flow of data through your chains and agents, making it easier to understand how your application processes information and makes decisions.",
                        "generation_info": {"finish_reason": "stop", "logprobs": None},
                    }
                ],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 8,
                        "completion_tokens": 89,
                        "total_tokens": 97,
                    },
                    "model_name": "gpt-3.5-turbo",
                },
            }

            # Patch child run with LLM response
            await patch_run(go_client, child_run_id, llm_response)

            # Patch parent run with final response
            await patch_run(
                go_client,
                parent_run_id,
                {
                    "answer": llm_response["generations"][0]["text"],
                    "source_documents": [],
                },
            )
            print("INFO: Patched parent and child runs", flush=True)

            await asyncio.sleep(5)
            parent_run = await get_run(backend_client, parent_run_id)
            child_run = await get_run(backend_client, child_run_id)

            validate_trace(parent_run, child_run, str(child_run_id))
            validate_parent_run(
                parent_run,
                "What is a LangSmith trace?",
                llm_response["generations"][0]["text"],
                llm_response["llm_output"]["token_usage"]["total_tokens"],
            )
            validate_child_run(
                child_run,
                "What is a LangSmith trace?",
                llm_response["generations"][0]["text"],
                llm_response["llm_output"]["token_usage"]["total_tokens"],
            )

            print("INFO: Test passed.", flush=True)
            return True

    except Exception as e:
        print(f"ERROR: Error during run creation: {str(e)}")
        return False


def validate_parent_run(
    parent_run, question: str, answer: str, num_tokens: int
) -> None:
    """
    Validate the parent run data matches expected values.

    Args:
        parent_run: Run data to validate
        question: Expected input question
        answer: Expected output answer
        num_tokens: Expected token count

    Raises:
        Exception: If validation fails
    """
    if parent_run["inputs"]["question"] != question:
        raise Exception("Parent run question does not match input question")
    if parent_run["outputs"]["answer"] != answer:
        raise Exception("Parent run answer does not match output answer")
    if parent_run["total_tokens"] != num_tokens:
        raise Exception("Parent run total tokens does not match input total tokens")


def validate_child_run(child_run, question: str, answer: str, num_tokens: int) -> None:
    """
    Validate the child run data matches expected values.

    Args:
        child_run: Run data to validate
        question: Expected input question
        answer: Expected output answer
        num_tokens: Expected token count

    Raises:
        Exception: If validation fails
    """
    if child_run["inputs"]["messages"][0]["content"] != question:
        raise Exception("Child run question does not match input question")
    if child_run["outputs"]["generations"][0]["text"] != answer:
        raise Exception("Child run answer does not match output answer")
    if child_run["total_tokens"] != num_tokens:
        raise Exception("Child run total tokens does not match input total tokens")


def validate_trace(parent_run, child_run, child_run_id: str) -> None:
    """
    Validate the relationship between parent and child runs.

    Args:
        parent_run: Parent run data
        child_run: Child run data
        child_run_id: Expected child run ID

    Raises:
        Exception: If validation fails
    """
    if parent_run["trace_id"] is None or parent_run["trace_id"] == "":
        raise Exception("Parent run has no trace id")

    if child_run_id not in parent_run["child_run_ids"]:
        raise Exception("Child run id not in parent run child run ids")

    if child_run["trace_id"] is None or child_run["trace_id"] == "":
        raise Exception("Child run has no trace id")

    if child_run["trace_id"] != parent_run["trace_id"]:
        raise Exception("Child run trace id does not match parent run trace id")

    if child_run["parent_run_id"] != parent_run["id"]:
        raise Exception("Child run parent run id does not match parent run id")


async def validate_jwt_token(client: ServiceClient) -> None:
    """
    Validate a JWT token using the platform backend service.

    Args:
        client: ServiceClient instance for making the validation request

    Raises:
        Exception: If token validation fails
    """
    response = await client.get("/internal/verify")
    if response.status_code != 200:
        print(f"INFO: Failed to validate JWT token: {response.status_code}", flush=True)
        raise Exception(f"ERROR: Failed to validate JWT token: {response.status_code}")


def generate_jwt_token(tenant_id: str, org_id: str) -> str:
    exp_datetime = datetime.now(tz=timezone.utc) + timedelta(seconds=JWT_TOKEN_EXPIRY)
    exp = int(exp_datetime.timestamp())
    payload = {
        "sub": SERVICE_NAME,
        "exp": exp,
    }

    payload["tenant_id"] = tenant_id
    payload["organization_id"] = org_id
    payload["identity_permissions"] = ["runs:create"]
    token = jwt.encode(
        payload,
        JWT_SECRET,
        algorithm="HS256",
    )
    return token


async def get_run(
    client: ServiceClient, run_id: UUID, max_retries: int = 15, retry_delay: int = 2
):
    """
    Get a run with retries for 404s.

    Args:
        client: ServiceClient instance for making the request
        run_id: UUID of the run to fetch
        max_retries: Maximum number of retry attempts
        retry_delay: Delay in seconds between retries

    Returns:
        Dict containing the run data

    Raises:
        Exception: If run cannot be fetched after max retries
    """
    attempt = 0
    while attempt < max_retries:
        response = await client.get(f"/runs/{str(run_id)}")

        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            attempt += 1
            if attempt < max_retries:
                print(
                    f"INFO: Run not found (attempt {attempt}/{max_retries}), retrying in {retry_delay} seconds...",
                    flush=True,
                )
                await asyncio.sleep(retry_delay)
            continue
        else:
            raise Exception(
                f"Failed to get run: {response.status_code} - {response.text}"
            )

    raise Exception(
        f"Run not found after {max_retries} attempts ({max_retries * retry_delay} seconds)"
    )


async def post_run(
    client: ServiceClient,
    run_id: UUID,
    name: str,
    run_type: str,
    inputs,
    parent_id: Optional[Any] = None,
) -> None:
    """
    Create a new run.

    Args:
        client: ServiceClient instance for making the request
        run_id: UUID for the new run
        name: Name of the run
        run_type: Type of run (e.g. 'chain', 'llm')
        inputs: Input data for the run
        parent_id: Optional UUID of parent run

    Raises:
        Exception: If run creation fails
    """
    data = {
        "id": str(run_id),
        "name": name,
        "run_type": run_type,
        "inputs": inputs,
        "start_time": datetime.now(timezone.utc).isoformat(),
    }
    if parent_id is not None:
        data["parent_run_id"] = str(parent_id)

    response = await client.post(
        "/runs",
        json=data,
    )
    if response.status_code != 202:
        raise Exception(
            f"Failed to create run: {response.status_code} - {response.text}"
        )


async def patch_run(client: ServiceClient, run_id: UUID, outputs) -> None:
    """
    Update an existing run with outputs.

    Args:
        client: ServiceClient instance for making the request
        run_id: UUID of the run to update
        outputs: Output data to add to the run

    Raises:
        Exception: If run update fails
    """
    response = await client.patch(
        f"/runs/{str(run_id)}",
        json={
            "outputs": outputs,
            "end_time": datetime.now(timezone.utc).isoformat(),
        },
    )

    if response.status_code != 202:
        raise Exception(
            f"Failed to patch run: {response.status_code} - {response.text}"
        )


def main() -> None:
    """Entry point for the script."""
    success = asyncio.run(test_langsmith_e2e_trace())
    exit(0 if success else 1)


if __name__ == "__main__":
    main()
