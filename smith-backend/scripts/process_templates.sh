#!/bin/bash

set -e

CLICKHOUSE_CLUSTER=${CLICKHOUSE_CLUSTER:-}
TEMPLATE_DIR=${TEMPLATE_DIR:-"clickhouse/migrations"}

# Determine ON_CLUSTER clause and engine types
if [ -n "$CLICKHOUSE_CLUSTER" ]; then
    ON_CLUSTER="ON CLUSTER $CLICKHOUSE_CLUSTER"

    # Use replicated engines
    ENGINE="ReplicatedMergeTree('/clickhouse/tables/{shard}/{database}/{table}', '{replica}')"
    REPLACING_ENGINE_TEMPLATE="ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{table}', '{replica}', %s)"
    AGGREGATING_ENGINE="ReplicatedAggregatingMergeTree('/clickhouse/tables/{shard}/{database}/{table}', '{replica}')"
else
    ON_CLUSTER=""

    # Default engines
    ENGINE=${ENGINE:-"MergeTree()"}
    REPLACING_ENGINE_TEMPLATE="ReplacingMergeTree(%s)"
    AGGREGATING_ENGINE=${AGGREGATING_ENGINE:-"AggregatingMergeTree()"}
fi

# Export variables needed in Perl script
export REPLACING_ENGINE_TEMPLATE

process_placeholders() {
    local content="$1"

    # Replace {{ON_CLUSTER}}
    content="${content//\{\{ON_CLUSTER\}\}/$ON_CLUSTER}"
    # Replace {{ENGINE}}
    content="${content//\{\{ENGINE\}\}/$ENGINE}"
    # Replace {{AGGREGATING_ENGINE}}
    content="${content//\{\{AGGREGATING_ENGINE\}\}/$AGGREGATING_ENGINE}"
    # Replace {{TABLE_SUFFIX}}
    content="${content//\{\{TABLE_SUFFIX\}\}/$TABLE_SUFFIX}"

    # Use Perl to process {{REPLACING_ENGINE(args)}} placeholders
    content=$(echo "$content" | perl -pe '
        my $repl_engine_template = $ENV{"REPLACING_ENGINE_TEMPLATE"};
        # Replace {{REPLACING_ENGINE(args)}}
        s/\{\{REPLACING_ENGINE\(([^}]*)\)\}\}/sprintf($repl_engine_template, $1)/ge;
    ')
    echo "$content"
}

# Process each template file into a temporary directory
PROCESSED_DIR=$(mktemp -d)

for template_file in "$TEMPLATE_DIR"/*.sql; do
    processed_file="$PROCESSED_DIR/$(basename "$template_file")"

    # Read the template content
    content=$(cat "$template_file")

    # Process placeholders
    content=$(process_placeholders "$content")

    # Write the processed content to the processed file
    echo "$content" > "$processed_file"
done

# Output the processed directory path
echo "$PROCESSED_DIR"
