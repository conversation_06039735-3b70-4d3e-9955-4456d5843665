import http from 'k6/http';
import { sleep } from 'k6';
import { uuidv4 } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';
import { Trend, Counter, Rate } from 'k6/metrics';

// Custom metrics
const requestDuration = new Trend('request_duration');
const successfulRequests = new Counter('successful_requests');
const failedRequests = new Counter('failed_requests');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const serverErrors = new Counter('server_errors');
const clientErrors = new Counter('client_errors');

// Scale testing configurations
const SCALE_CONFIGS = {
  baseline: {
    vus: 10,     // Current baseline virtual users
    rps: 600     // Current baseline requests per second
  },
  '4x': {
    vus: 40,     // 4x the baseline VUs
    rps: 2800    // Target 2.8k RPS
  },
  '20x': {
    vus: 200,    // 20x the baseline VUs
    rps: 15000   // Target 15k RPS
  }
};

// Get scale level from environment variables
const SCALE_LEVEL = __ENV.SCALE_LEVEL || 'baseline';
const CONFIG = SCALE_CONFIGS[SCALE_LEVEL];

if (!CONFIG) {
  throw new Error(`Invalid scale level: ${SCALE_LEVEL}. Must be one of: ${Object.keys(SCALE_CONFIGS).join(', ')}`);
}

export const options = {
  scenarios: {
    multipart_test: {
      executor: 'ramping-vus',
      startVUs: 1,
      stages: [
        { duration: __ENV.RAMP_UP_DURATION || '2m', target: CONFIG.vus },
        { duration: __ENV.STEADY_DURATION || '5m', target: CONFIG.vus },
        { duration: __ENV.RAMP_DOWN_DURATION || '1m', target: 0 }
      ],
    },
  },
  thresholds: {
    'request_duration': [
      { threshold: 'p(50)<1000', abortOnFail: false },  // 50th percentile < 1000ms
      { threshold: 'p(95)<2000', abortOnFail: false },  // 95th percentile < 2000ms
      { threshold: 'p(99)<5000', abortOnFail: false }   // 99th percentile < 5000ms
    ],
    'error_rate': [
      { threshold: 'rate<0.005', abortOnFail: false }  // 0.5% error rate allowed
    ],
    'timeout_errors': [
      { threshold: 'count<100', abortOnFail: false }
    ],
    'server_errors': [
      { threshold: 'count<50', abortOnFail: false }
    ]
  }
};

// Error categorization helper
function categorizeError(response) {
  if (response.status === 0 || response.status === 408) {
    timeoutErrors.add(1);
    console.error(`Timeout error for multipart request: ${response.request.url}`);
  } else if (response.status >= 500) {
    serverErrors.add(1);
    console.error(`Server error ${response.status} for multipart request: ${response.request.url}`);
  } else if (response.status >= 400) {
    clientErrors.add(1);
    console.error(`Client error ${response.status} for multipart request: ${response.request.url}`);
  }
}

export const customOptions = {
  session_name: __ENV.SESSION_NAME || `k6-multipart-${SCALE_LEVEL}-test`,
  batchSize: parseInt(__ENV.BATCH_SIZE || 5),
  apikey: __ENV.LANGCHAIN_API_KEY,
  baseurl: __ENV.LANGCHAIN_ENDPOINT || "https://beta.api.smith.langchain.com",
  payloadSize: parseInt(__ENV.DATA_SIZE || 25000),
  useZstd: __ENV.USE_ZSTD || "",
  usePlatformBackend: __ENV.USE_PLATFORM_BACKEND || "",
};

function buildRepeatedPayload(size) {
  const block = Array(200)
    .fill()
    .map(() => Math.random().toString(36).substring(2, 8))
    .join('');
  let result = '';
  while (result.length < size) {
    result += block;
  }
  return result.slice(0, size);
}

const largePayload = buildRepeatedPayload(customOptions.payloadSize);

function formatDottedOrder(runId) {
  const now = new Date();
  const year = now.getUTCFullYear();
  const month = String(now.getUTCMonth() + 1).padStart(2, '0');
  const day = String(now.getUTCDate()).padStart(2, '0');
  const hours = String(now.getUTCHours()).padStart(2, '0');
  const minutes = String(now.getUTCMinutes()).padStart(2, '0');
  const seconds = String(now.getUTCSeconds()).padStart(2, '0');
  const millis = String(now.getUTCMilliseconds()).padStart(3, '0');
  const micros = '000';
  return `${year}${month}${day}T${hours}${minutes}${seconds}${millis}${micros}Z${runId}`;
}

function createParentRun(runId) {
  const now = new Date();
  return {
    id: runId,
    trace_id: runId,
    session_name: customOptions.session_name,
    dotted_order: formatDottedOrder(runId),
    name: "Parent Chain Run",
    run_type: "chain",
    start_time: new Date(now.getTime() - 100).toISOString(), // 100 milliseconds ago
    end_time: now.toISOString()
  };
}

function createChildRun(parent) {
  const runId = uuidv4();
  const now = new Date();
  return {
    id: runId,
    trace_id: parent.trace_id,
    session_name: parent.session_name,
    parent_run_id: parent.id,
    dotted_order: parent.dotted_order + "." + formatDottedOrder(runId),
    name: "Child LLM Run",
    run_type: "llm",
    start_time: new Date(now.getTime() - 50).toISOString(), // 50 milliseconds ago
    end_time: now.toISOString()
  };
}

// Builds a multipart/form-data string for an array of runs (post operation).
function buildMultipartPayload(runs) {
  const boundary = "K6BOUNDARY-" + uuidv4();
  let parts = [];

  function addPart(name, content) {
    const jsonContent = JSON.stringify(content);
    const part = [
      `--${boundary}`,
      `Content-Disposition: form-data; name="${name}"`,
      'Content-Type: application/json',
      `Content-Length: ${jsonContent.length}`,
      '',
      jsonContent
    ].join('\r\n');
    parts.push(part);
  }

  runs.forEach(run => {
    // Create mainData without spread operator
    const mainData = {
      id: run.id,
      trace_id: run.trace_id,
      session_name: run.session_name,
      parent_run_id: run.parent_run_id,
      dotted_order: run.dotted_order,
      name: run.name,
      run_type: run.run_type,
      start_time: run.start_time
    };

    addPart(`post.${run.id}`, mainData);

    if (run.inputs) addPart(`post.${run.id}.inputs`, run.inputs);
    if (run.outputs) addPart(`post.${run.id}.outputs`, run.outputs);
    if (run.events) addPart(`post.${run.id}.events`, run.events);
  });

  parts.push(`--${boundary}--`);
  return {
    body: parts.join('\r\n') + '\r\n',
    boundary
  };
}

export default function () {
  const runId = uuidv4();
  const parentRun = createParentRun(runId);
  const childRuns = Array.from({ length: customOptions.batchSize }, () => createChildRun(parentRun));
  const allRuns = [parentRun, ...childRuns];

  // Set inputs separately for multipart structure
  parentRun.inputs = { input: largePayload };
  childRuns.forEach(child => {
    child.inputs = { input: "What's the capital of Canada?" };
  });

  // Log payload for debugging (only on first iteration to avoid spam)
  if (__ITER === 0) {
    console.log('Multipart Test Configuration:', {
      parentRunId: parentRun.id,
      sessionName: parentRun.session_name,
      childRunCount: childRuns.length,
      batchSize: customOptions.batchSize,
      payloadSize: customOptions.payloadSize,
      endpoint: customOptions.baseurl,
      useZstd: customOptions.useZstd
    });
  }

  const { body, boundary } = buildMultipartPayload(allRuns);
  const headers = {
    "Content-Type": `multipart/form-data; boundary=${boundary}`,
    "X-API-Key": customOptions.apikey,
  };

  const res = http.post(`${customOptions.baseurl}${customOptions.usePlatformBackend ? '/v1' : ''}/runs/multipart`, body, {
    headers,
    compression: customOptions.useZstd.toLowerCase() === "true" ? "zstd" : "",
  });

  // Track metrics
  requestDuration.add(res.timings.duration);
  
  if (res.status === 202) {
    successfulRequests.add(1);
  } else {
    failedRequests.add(1);
    categorizeError(res);
  }
  
  errorRate.add(res.status !== 202);

  if (res.status !== 202) {
    console.log(`Multipart request duration: ${res.timings.duration} ms | status: ${res.status} | body: ${res.body}`);
  } else {
    console.log(`Multipart request duration: ${res.timings.duration} ms | status: ${res.status}`);
  }
  
  // Dynamic sleep based on configured RPS target
  const sleepTime = 1000 / CONFIG.rps;
  sleep(sleepTime / 1000); // Convert to seconds for k6
}

// Log test configuration on startup
console.log('Multipart Test Configuration:', {
  scaleLevel: SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetRPS: CONFIG.rps,
  rampUp: __ENV.RAMP_UP_DURATION || '2m',
  steady: __ENV.STEADY_DURATION || '5m',
  rampDown: __ENV.RAMP_DOWN_DURATION || '1m',
  endpoint: customOptions.baseurl,
  batchSize: customOptions.batchSize,
  payloadSize: customOptions.payloadSize,
  useZstd: customOptions.useZstd
});
