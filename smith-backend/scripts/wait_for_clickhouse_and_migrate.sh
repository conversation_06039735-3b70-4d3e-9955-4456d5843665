#!/bin/bash
set -e

# Initialize variables
CLICKHOUSE_HOST=${CLICKHOUSE_HOST:-localhost}
CLICKHOUSE_PORT=${CLICKHOUSE_PORT:-8123}
CLICKHOUSE_TLS=${CLICKHOUSE_TLS:-false}
CLICKHOUSE_USER=${CLICKHOUSE_USER:-default}
CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-default}
CLICKHOUSE_CLUSTER=${CLICKHOUSE_CLUSTER:-}
TABLE_SUFFIX=${TABLE_SUFFIX:-}
TEMPLATE_DIR=${TEMPLATE_DIR:-"clickhouse/migrations"}

# Determine ON_CLUSTER clause and engine types
if [ -n "$CLICKHOUSE_CLUSTER" ]; then
    ON_CLUSTER="ON CLUSTER $CLICKHOUSE_CLUSTER"
    ENGINE="ReplicatedMergeTree('/clickhouse/tables/{shard}/{database}/{table}', '{replica}')"
    REPLACING_ENGINE_TEMPLATE="ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{table}', '{replica}', %s)"
    AGGREGATING_ENGINE="ReplicatedAggregatingMergeTree('/clickhouse/tables/{shard}/{database}/{table}', '{replica}')"
    CLICKHOUSE_MIGRATION_TABLE_ENGINE=${CLICKHOUSE_MIGRATION_TABLE_ENGINE:-ReplicatedMergeTree}
else
    ON_CLUSTER=""
    ENGINE="MergeTree()"
    REPLACING_ENGINE_TEMPLATE="ReplacingMergeTree(%s)"
    AGGREGATING_ENGINE="AggregatingMergeTree()"
    CLICKHOUSE_MIGRATION_TABLE_ENGINE=${CLICKHOUSE_MIGRATION_TABLE_ENGINE:-MergeTree}
fi

# Function to process template placeholders
process_placeholders() {
    local content="$1"
    # Replace {{ON_CLUSTER}}
    content="${content//\{\{ON_CLUSTER\}\}/$ON_CLUSTER}"
    # Replace {{ENGINE}}
    content="${content//\{\{ENGINE\}\}/$ENGINE}"
    # Replace {{AGGREGATING_ENGINE}}
    content="${content//\{\{AGGREGATING_ENGINE\}\}/$AGGREGATING_ENGINE}"
    # Replace {{TABLE_SUFFIX}}
    content="${content//\{\{TABLE_SUFFIX\}\}/$TABLE_SUFFIX}"

    # Process {{REPLACING_ENGINE(args)}} placeholders using bash
    local result="$content"
    while [[ "$result" =~ \{\{REPLACING_ENGINE\(([^}]*)\)\}\} ]]; do
        local args="${BASH_REMATCH[1]}"
        local replacement=$(printf "$REPLACING_ENGINE_TEMPLATE" "$args")
        result="${result/\{\{REPLACING_ENGINE\($args\)\}\}/$replacement}"
    done
    echo "$result"
}

# Function to URL encode a string
urlencode() {
    local length="${#1}"
    for (( i = 0; i < length; i++ )); do
        local c="${1:i:1}"
        case $c in
            [a-zA-Z0-9.~_-]) printf "$c" ;;
            *) printf '%%%02X' "'$c" ;;
        esac
    done
}

# Function to check if ClickHouse is ready
check_clickhouse_ready() {
    # Determine the protocol based on CLICKHOUSE_TLS
    if [ "$CLICKHOUSE_TLS" = "true" ]; then
        protocol="https"
    else
        protocol="http"
    fi

    # Construct the URL
    url="${protocol}://${CLICKHOUSE_HOST}:${CLICKHOUSE_PORT}/?query=SELECT%201"

    # Use curl to send the request with headers
    http_status=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "X-ClickHouse-User: $CLICKHOUSE_USER" \
        -H "X-ClickHouse-Key: $CLICKHOUSE_PASSWORD" \
        "$url")

    [ "$http_status" -eq 200 ]
    return $?
}

# Wait for ClickHouse to be ready
timeout=60
start_time=$(date +%s)
while ! check_clickhouse_ready; do
    current_time=$(date +%s)
    elapsed_time=$((current_time - start_time))
    if [ $elapsed_time -ge $timeout ]; then
        echo "Timeout reached. ClickHouse is not ready."
        exit 1
    fi
    echo "Waiting for ClickHouse to be ready..."
    sleep 5
done
echo "ClickHouse is ready."

# Process templates
PROCESSED_DIR=$(mktemp -d)
trap "rm -rf '$PROCESSED_DIR'" EXIT

for template_file in "$TEMPLATE_DIR"/*.sql; do
    processed_file="$PROCESSED_DIR/$(basename "$template_file")"
    content=$(cat "$template_file")
    content=$(process_placeholders "$content")
    echo "$content" > "$processed_file"
done

# URL encode the ClickHouse password
encoded_password=$(urlencode "${CLICKHOUSE_PASSWORD}")

# Build the base database URL
database_url="clickhouse://${CLICKHOUSE_HOST}:${CLICKHOUSE_NATIVE_PORT}?username=${CLICKHOUSE_USER}&password=${encoded_password}&database=${CLICKHOUSE_DB}&x-multi-statement=true&x-migrations-table-engine=${CLICKHOUSE_MIGRATION_TABLE_ENGINE}&secure=${CLICKHOUSE_TLS}"

# Append x-cluster-name if CLICKHOUSE_CLUSTER is set
if [ -n "$CLICKHOUSE_CLUSTER" ]; then
    database_url="${database_url}&x-cluster-name=${CLICKHOUSE_CLUSTER}"
fi

# Run the migration using the processed files
migrate -source "file://${PROCESSED_DIR}" -database "$database_url" up
