"""add_new_models

Revision ID: 80c4659cbe9a
Revises: 87d070f2f3a3
Create Date: 2025-06-02 12:02:11.828546

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "80c4659cbe9a"
down_revision = "87d070f2f3a3"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time, prompt_cost_details, completion_cost_details)
        VALUES
            (53, 'claude-4-sonnet', '^claude-4-sonnet(-\d{8}|-latest)?$', 0.000003, 0.000015, NULL, '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}', NULL),
            (54, 'amazon_bedrock_claude-4-sonnet', ' ^anthropic\.claude-4-sonnet(-\d{8})?(-v\d+)?(:[\d.]+)?$', 0.000003, 0.000015, NULL, '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}', NULL),
            (55, 'claude-4-opus', '^claude-4-opus(-\d{8}|-latest)?$', 0.000015, 0.000075, NULL, '{"cache_read": 0.0000015, "ephemeral_5m_input_tokens": 0.00001875, "ephemeral_1hr_input_tokens": 0.00003}', NULL),
            (56, 'amazon_bedrock_claude-4-opus', '^anthropic\.claude-4-opus(-\d{8})?(-v\d+)?(:[\d.]+)?$', 0.000015, 0.000075, NULL, '{"cache_read": 0.0000015, "ephemeral_5m_input_tokens": 0.00001875, "ephemeral_1hr_input_tokens": 0.00003}', NULL),
            (57, 'gemini-2.5-flash-preview-05-20', '^(models\/)?gemini-2\.5-flash-preview-05-20$', 0.00000015, 0.0000006, NULL, '{"cache_read": 0.0000000375, "audio": 0.000001}', '{"reasoning": 0.0000035}')
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DELETE FROM model_price_map
        WHERE (priority_order, name) IN (
            (53, 'claude-4-sonnet'),
            (54, 'amazon_bedrock_claude-4-sonnet'),
            (55, 'claude-4-opus'),
            (56, 'amazon_bedrock_claude-4-opus'),
            (57, 'gemini-2.5-flash-preview-05-20')
        );
        """
    )
