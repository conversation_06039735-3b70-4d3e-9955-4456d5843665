"""claude 4 price table name fixes

Revision ID: 463c96ebec5d
Revises: 80c4659cbe9a
Create Date: 2025-06-03 10:03:12.507334

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "463c96ebec5d"
down_revision = "80c4659cbe9a"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET name = 'claude-sonnet-4', match_pattern = '^claude-sonnet-4(-\\d{8}|-latest)?$'
        WHERE name = 'claude-4-sonnet';
        
        UPDATE model_price_map
        SET name = 'amazon_bedrock_claude-sonnet-4', match_pattern = '^anthropic\\.claude-sonnet-4(-\\d{8})?(-v\\d+)?(:[\d.]+)?$'
        WHERE name = 'amazon_bedrock_claude-4-sonnet';
        
        UPDATE model_price_map
        SET name = 'claude-opus-4', match_pattern = '^claude-opus-4(-\\d{8}|-latest)?$'
        WHERE name = 'claude-4-opus';
        
        UPDATE model_price_map
        SET name = 'amazon_bedrock_claude-opus-4', match_pattern = '^anthropic\\.claude-opus-4(-\\d{8})?(-v\\d+)?(:[\d.]+)?$'
        WHERE name = 'amazon_bedrock_claude-4-opus';
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET name = 'claude-4-sonnet', match_pattern = '^claude-4-sonnet(-\\d{8}|-latest)?$'
        WHERE name = 'claude-sonnet-4';
        
        UPDATE model_price_map
        SET name = 'amazon_bedrock_claude-4-sonnet', match_pattern = '^anthropic\\.claude-4-sonnet(-\\d{8})?(-v\\d+)?(:[\d.]+)?$'
        WHERE name = 'amazon_bedrock_claude-sonnet-4';
        
        UPDATE model_price_map
        SET name = 'claude-4-opus', match_pattern = '^claude-4-opus(-\\d{8}|-latest)?$'
        WHERE name = 'claude-opus-4';
        
        UPDATE model_price_map
        SET name = 'amazon_bedrock_claude-4-opus', match_pattern = '^anthropic\\.claude-4-opus(-\\d{8})?(-v\\d+)?(:[\d.]+)?$'
        WHERE name = 'amazon_bedrock_claude-opus-4';
        """
    )
