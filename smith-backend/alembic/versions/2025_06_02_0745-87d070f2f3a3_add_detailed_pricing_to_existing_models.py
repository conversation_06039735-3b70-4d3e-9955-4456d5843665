"""add_detailed_pricing_to_existing_models

Revision ID: 87d070f2f3a3
Revises: 7bf1f8819960
Create Date: 2025-06-02 07:45:03.301583

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "87d070f2f3a3"
down_revision = "7bf1f8819960"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET
        prompt_cost_details = CASE
            WHEN name = 'gpt-4o' THEN '{"cache_read": 0.00000125}'::jsonb
            WHEN name = 'gpt-4o-2024-05-13' OR name = 'gpt-4o-2024-08-06' OR name = 'gpt-4o-2024-11-20' THEN '{"cache_read": 0.00000125}'::jsonb
            WHEN name = 'gpt-4o-mini' THEN '{"cache_read": 0.000000075}'::jsonb
            WHEN name = 'gpt-4o-mini-2024-07-18' THEN '{"cache_read": 0.000000075}'::jsonb
            WHEN name = 'o1-preview' THEN '{"cache_read": 0.0000075}'::jsonb
            WHEN name = 'o1-preview-2024-09-12' THEN '{"cache_read": 0.0000075}'::jsonb
            WHEN name = 'o1-mini' THEN '{"cache_read": 0.00000055}'::jsonb
            WHEN name = 'o1-mini-2024-09-12' THEN '{"cache_read": 0.00000055}'::jsonb
            WHEN name = 'o1-2024-12-17' THEN '{"cache_read": 0.0000075}'::jsonb
            WHEN name = 'o1' THEN '{"cache_read": 0.0000075}'::jsonb
            WHEN name = 'o3-mini-2025-01-31' THEN '{"cache_read": 0.00000055}'::jsonb
            WHEN name = 'o3-mini' THEN '{"cache_read": 0.00000055}'::jsonb
            WHEN name = 'gpt-4.5-preview' THEN '{"cache_read": 0.0000375}'::jsonb
            WHEN name = 'gpt-4.5-preview-2025-02-27' THEN '{"cache_read": 0.0000375}'::jsonb
            WHEN name = 'o3' THEN '{"cache_read": 0.0000025}'::jsonb
            WHEN name = 'o4-mini' THEN '{"cache_read": 0.000000275}'::jsonb
            WHEN name = 'gpt-4.1' THEN '{"cache_read": 0.0000005}'::jsonb
            WHEN name = 'gpt-4.1-2025-04-14' THEN '{"cache_read": 0.0000005}'::jsonb
            WHEN name = 'gpt-4.1-mini' THEN '{"cache_read": 0.0000001}'::jsonb
            WHEN name = 'gpt-4.1-mini-2025-04-14' THEN '{"cache_read": 0.0000001}'::jsonb
            WHEN name = 'gpt-4.1-nano' THEN '{"cache_read": 0.000000025}'::jsonb
            WHEN name = 'gpt-4.1-nano-2025-04-14' THEN '{"cache_read": 0.000000025}'::jsonb


            WHEN name = 'claude-3-7-sonnet' THEN '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}'::jsonb
            WHEN name = 'amazon_bedrock_claude-3-7-sonnet' THEN '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}'::jsonb
            WHEN name = 'claude-3-5-sonnet' THEN '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}'::jsonb
            WHEN name = 'amazon_bedrock_claude-3-5-sonnet' THEN '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}'::jsonb
            WHEN name = 'claude-3-5-haiku' THEN '{"cache_read": 0.00000008, "ephemeral_5m_input_tokens": 0.000001, "ephemeral_1hr_input_tokens": 0.0000016}'::jsonb
            WHEN name = 'amazon_bedrock_claude-3-5-haiku' THEN '{"cache_read": 0.00000008, "ephemeral_5m_input_tokens": 0.000001, "ephemeral_1hr_input_tokens": 0.0000016}'::jsonb
            WHEN name = 'claude-3-haiku' THEN '{"cache_read": 0.00000003, "ephemeral_5m_input_tokens": 0.0000003, "ephemeral_1hr_input_tokens": 0.0000005}'::jsonb
            WHEN name = 'amazon_bedrock_claude-3-haiku' THEN '{"cache_read": 0.00000003, "ephemeral_5m_input_tokens": 0.0000003, "ephemeral_1hr_input_tokens": 0.0000005}'::jsonb
            WHEN name = 'claude-3-opus' THEN '{"cache_read": 0.0000015, "ephemeral_5m_input_tokens": 0.00001875, "ephemeral_1hr_input_tokens": 0.00003}'::jsonb
            WHEN name = 'amazon_bedrock_claude-3-opus' THEN '{"cache_read": 0.0000015, "ephemeral_5m_input_tokens": 0.00001875, "ephemeral_1hr_input_tokens": 0.00003}'::jsonb
            WHEN name = 'claude-opus-4' THEN '{"cache_read": 0.0000015, "ephemeral_5m_input_tokens": 0.00001875, "ephemeral_1hr_input_tokens": 0.00003}'::jsonb
            WHEN name = 'amazon_bedrock_claude-opus-4' THEN '{"cache_read": 0.0000015, "ephemeral_5m_input_tokens": 0.00001875, "ephemeral_1hr_input_tokens": 0.00003}'::jsonb
            WHEN name = 'claude-sonnet-4' THEN '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}'::jsonb
            WHEN name = 'amazon_bedrock_claude-sonnet-4' THEN '{"cache_read": 0.0000003, "ephemeral_5m_input_tokens": 0.00000375, "ephemeral_1hr_input_tokens": 0.000006}'::jsonb

            WHEN name = 'gemini-1.5-pro-latest' THEN '{"cache_read": 0.0000003125}'::jsonb
            WHEN name = 'gemini-1.5-pro' THEN '{"cache_read": 0.0000003125}'::jsonb
            WHEN name = 'gemini-1.5-flash-8b-latest' THEN '{"cache_read": 0.00000001}'::jsonb
            WHEN name = 'gemini-1.5-flash-8b' THEN '{"cache_read": 0.00000001}'::jsonb
            WHEN name = 'gemini-1.5-flash-latest' THEN '{"cache_read": 0.00000001875}'::jsonb
            WHEN name = 'gemini-1.5-flash' THEN '{"cache_read": 0.00000001875}'::jsonb
            WHEN name = 'gemini-2.0-flash' THEN '{"cache_read": 0.000000025, "audio": 0.0000007}'::jsonb
            WHEN name = 'gemini-2.5-pro-preview-03-25' THEN '{"cache_read": 0.00000031}'::jsonb
        END;
        """
    )


def downgrade() -> None:
    pass
