"""add_bedrock_pricing

Revision ID: 45fa5945f3c0
Revises: 507a3a2ffbdc
Create Date: 2025-05-16 15:55:04.583539

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "45fa5945f3c0"
down_revision = "507a3a2ffbdc"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    """This will allow model names like: anthropic.claude-3-haiku-20240307-v1:0"""
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time, provider)
        VALUES
            (40, 'amazon_bedrock_claude-3-7-sonnet', '^anthropic\.claude-3-7-sonnet(-\d{8})?(-v\d+)?(:[\d.]+)?$', 0.000003, 0.000015, NULL, 'amazon_bedrock'),
            (39, 'amazon_bedrock_claude-3-5-sonnet', '^anthropic\.claude-3-5-sonnet(-\d{8})?(-v\d+)?(:[\d.]+)?$', 0.000003, 0.000015, NULL, 'amazon_bedrock'),
            (38, 'amazon_bedrock_claude-3-5-haiku', '^anthropic\.claude-3-5-haiku(-\d{8})?(-v\d+)?(:[\d.]+)?$', 0.0000008, 0.000004, NULL, 'amazon_bedrock'),
            (37, 'amazon_bedrock_claude-3-haiku', '^anthropic\.claude-3-haiku(-\d{8})?(-v\d+)?(:[\d.]+)?$', 0.00000025, 0.00000125, NULL, 'amazon_bedrock'),
            (36, 'amazon_bedrock_claude-3-opus', '^anthropic\.claude-3-opus(-\d{8})?(-v\d+)?(:[\d.]+)?$', 0.000015, 0.000075, NULL, 'amazon_bedrock')
        """
    )


def downgrade() -> None:
    pass
