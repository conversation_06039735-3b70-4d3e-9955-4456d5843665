"""Add feedback configs indexes

Revision ID: 4aab817bd6fc
Revises: 463c96ebec5d
Create Date: 2025-05-30 12:01:39.144861

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "4aab817bd6fc"
down_revision = "463c96ebec5d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute("""
    CREATE INDEX IF NOT EXISTS idx_feedback_configs_tenant_id
    ON feedback_configs (tenant_id);
    """)

    op.execute("""
    CREATE INDEX IF NOT EXISTS idx_feedback_configs_tenant_key_modified
    ON feedback_configs (tenant_id, feedback_key, modified_at DESC);
    """)


def downgrade() -> None:
    op.execute("""
    DROP INDEX IF EXISTS idx_feedback_configs_tenant_id;
    DROP INDEX IF EXISTS idx_feedback_configs_tenant_key_modified;
    """)
