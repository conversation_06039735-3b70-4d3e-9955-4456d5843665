"""token cost details

Revision ID: 7bf1f8819960
Revises: 1ec4cb04992d
Create Date: 2025-05-23 12:27:09.291146

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "7bf1f8819960"
down_revision = "1ec4cb04992d"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE model_price_map 
        ADD COLUMN IF NOT EXISTS prompt_cost_details JSONB,
        ADD COLUMN IF NOT EXISTS completion_cost_details JSONB;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE model_price_map 
        DROP COLUMN IF EXISTS prompt_cost_details,
        DROP COLUMN IF EXISTS completion_cost_details;
        """
    )
