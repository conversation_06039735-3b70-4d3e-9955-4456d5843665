CREATE USER ext_retool identified with SHA256_PASSWORD BY 'secret';

GRANT SELECT, INSERT, UPDATE ON tenants TO ext_retool;
GRANT SELECT, INSERT, UPDATE ON organizations TO ext_retool;
GRANT SELECT, INSERT, UPDATE ON self_hosted_customers TO ext_retool;
GRANT SELECT, INSERT, UPDATE ON self_hosted_licenses TO ext_retool;
GRANT SELECT ON host_projects TO ext_retool;
GRANT SELECT ON host_revisions TO ext_retool;
GRANT SELECT ON identities TO ext_retool;
GRANT SELECT ON users TO ext_retool;
