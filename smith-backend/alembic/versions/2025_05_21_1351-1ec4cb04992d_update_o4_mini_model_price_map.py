"""Update o4-mini model price map

Revision ID: 1ec4cb04992d
Revises: c1616eeed193
Create Date: 2025-05-21 13:51:59.801875

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "1ec4cb04992d"
down_revision = "c1616eeed193"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Adds support for date-suffixed model versions
    # Matches e.g. o4-mini, o4-mini-2025-04-16
    op.execute(
        """
        UPDATE model_price_map
        SET match_pattern = '^o4-mini(-\d{4}-\d{2}-\d{2})?$'
        WHERE name = 'o4-mini' AND tenant_id IS NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET match_pattern = '^o4-mini$'
        WHERE name = 'o4-mini' AND tenant_id IS NULL;
        """
    )
