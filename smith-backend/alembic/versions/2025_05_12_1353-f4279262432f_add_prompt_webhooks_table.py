"""add prompt webhooks table

Revision ID: f4279262432f
Revises: ec1ef07f00b1
Create Date: 2025-05-12 13:53:41.530926

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f4279262432f"
down_revision = "45fa5945f3c0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        create table prompt_webhooks (
            id uuid primary key default gen_random_uuid(),
            tenant_id uuid not null references tenants(id) on delete cascade,
            include_prompts jsonb,
            exclude_prompts jsonb,
            url varchar(2048) not null,
            headers jsonb,
            triggers jsonb,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
        );"""
    )


def downgrade() -> None:
    op.execute("drop table if exists prompt_webhooks;")
