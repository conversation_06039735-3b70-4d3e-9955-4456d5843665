"""Create index on remote_metrics.tags.langgraph.platform.tenant_id.

Revision ID: 507a3a2ffbdc
Revises: ec1ef07f00b1
Create Date: 2025-05-15 14:40:16.503530

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "507a3a2ffbdc"
down_revision = "ec1ef07f00b1"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        "CREATE INDEX idx_remote_metrics_tags_tenant_id ON remote_metrics ((tags ->> 'langgraph.platform.tenant_id'))"
    )


def downgrade() -> None:
    op.execute("DROP INDEX IF EXISTS idx_remote_metrics_tags_tenant_id")
