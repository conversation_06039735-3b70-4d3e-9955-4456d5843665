"""Add o3 model price map

Revision ID: c1616eeed193
Revises: 7de8ad4f3c9a
Create Date: 2025-05-21 13:29:02.502542

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "c1616eeed193"
down_revision = "7de8ad4f3c9a"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Matches e.g. o3, o3-2025-04-16
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time)
        VALUES
            (52, 'o3', '^o3(-\d{4}-\d{2}-\d{2})?$', 0.00001, 0.00004, NULL)
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DELETE FROM model_price_map WHERE name = 'o3' AND tenant_id IS NULL
        """
    )
