"""create feedback_configs table (Postgres)

Revision ID: 7de8ad4f3c9a
Revises: f4279262432f
Create Date: 2025-05-09 11:54:00.000000
"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "7de8ad4f3c9a"
down_revision = "f4279262432f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute("""
    CREATE TABLE IF NOT EXISTS feedback_configs (
        tenant_id UUID NOT NULL
            REFERENCES tenants(id) ON DELETE CASCADE,
        feedback_key TEXT NOT NULL,
        feedback_config JSONB NOT NULL DEFAULT '{}'::jsonb,
        modified_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        is_lower_score_better BOOLEAN NOT NULL DEFAULT FALSE,
        CONSTRAINT pk_feedback_configs PRIMARY KEY (tenant_id, feedback_key)
    );
    """)


def downgrade() -> None:
    op.execute("""
    DROP TABLE IF EXISTS feedback_configs;
    """)
