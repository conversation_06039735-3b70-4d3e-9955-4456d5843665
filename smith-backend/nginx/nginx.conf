events {
    worker_connections 1024;
}

http {
    server {
        listen       1980;
        listen  [::]:1980;
        server_name  localhost;
        client_max_body_size 25M;
        proxy_read_timeout 300;
        proxy_connect_timeout 60;
        proxy_send_timeout 300;

        add_header Content-Security-Policy "frame-ancestors 'self'" always;

        location / {
            root   /tmp/build;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        location = /health {
            access_log off;
            return 200;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /tmp/build;
        }

        # Playground Routes
         location ~ /api/v1/playground/ {
            rewrite /api/v1/playground/(.*) /playground/$1  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:3001;
        }

         # Platform Backend Routes
         location ~ /api/v1/platform/ {
            rewrite /api/v1/platform/(.*) /v1/platform/$1  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }

         location ~ /api/v1/otel/ {
            rewrite /api/v1/otel/(.*) /otel/$1  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }
         location = /api/v1/runs/multipart {
            rewrite /api/v1/runs/multipart /runs/multipart  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }
         location ~ /api/v1/feedback/batch {
            rewrite /api/v1/feedback/batch /feedback/batch  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }
         location = /api/v1/info {
            rewrite /api/v1/info /info  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }

         location ~ /api/v1/auth {
            rewrite /api/v1/(.*) /$1  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }

          location ~ /api/public/download {
            rewrite /api/(.*) /$1  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }

         location ~ /api/v1/traces/ {
            rewrite /api/v1/traces/(.*) /traces/$1  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:8080;
         }

        # Backend Routes
        location ~ /api/v1 {
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:1984;
        }

        location ~ /api {
            rewrite /api/(.*) /api/v1/$1  break;
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:1984;
        }

         # Handle docs while we do rewrite til v8
        location = /api/docs {
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:1984;
        }

        location = /api/redoc {
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:1984;
        }

        location = /api/openapi.json {
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://host.docker.internal:1984;
        }
    }
}
