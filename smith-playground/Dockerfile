# Use the official lightweight Python Alpine image.
# https://hub.docker.com/_/python
FROM python:3.11-alpine AS builder

# Install necessary packages using apk
RUN apk add --no-cache bash gcc libffi-dev g++ make openssl-dev rust cargo go build-base python3-dev geos geos-dev git curl-dev cmake ninja samurai apache-arrow-dev

RUN apk add --no-cache build-base bash git zlib-dev openssl-dev && \
    git clone https://github.com/edenhill/librdkafka.git && \
    cd librdkafka && \
    ./configure && \
    make && \
    make install

# Note: this is run by bake from the root of the repo, not this folder

WORKDIR /code/smith-playground

ENV POETRY_VERSION=1.5.1

ENV CFLAGS="-Wno-error=incompatible-pointer-types"

# Copy poetry configuration files
COPY ./smith-playground/pyproject.toml ./smith-playground/poetry.lock* ./

# Install Poetry and project dependencies
RUN pip3 install poetry=="$POETRY_VERSION" && \
    poetry config virtualenvs.create false && \
    poetry install --without dev,test --no-interaction --no-ansi --no-root --no-directory && \
    python -c "import compileall; compileall.compile_path(maxlevels=1000, legacy=True)"

WORKDIR /code

# Copy environment files
COPY ./.env* ./
RUN rm ./.env.local_dev* ./.env.local_test && \
    mkdir secrets

WORKDIR /code/smith-playground

# Copy utility directories
COPY ./lc_config ../lc_config
COPY ./lc_database ../lc_database
COPY ./lc_logging ../lc_logging
COPY ./lc_metrics ../lc_metrics

COPY ./smith-playground/playground ./playground

# Finish installing our project, then remove Poetry since we no longer need it.
# All the dependencies should have already been installed.
RUN poetry install --without dev,test --no-interaction --no-ansi && \
    pip uninstall poetry -y

# Compile Python files and remove source files
RUN python -c "import compileall; compileall.compile_path(maxlevels=1000, legacy=True)" && \
    find ./playground -type f -name "*.py" -delete && \
    find /usr/local/lib/python3.11/site-packages -not -path '/usr/local/lib/python3.11/site-packages/ddtrace/*' -type f -name '*.py' -delete

FROM python:3.11-alpine

# Update packages and install required utilities
RUN apk update && apk upgrade && apk add --no-cache openssl libgcc libstdc++ curl-dev

# Install Kafka
RUN apk add --no-cache build-base bash git zlib-dev openssl-dev && \
    git clone https://github.com/edenhill/librdkafka.git && \
    cd librdkafka && \
    ./configure && \
    make && \
    make install

# Enable Python stack traces on segfaults https://stackoverflow.com/a/29246977
ENV PYTHONFAULTHANDLER=1

# Ensure Python does not buffer output, which is recommended when running inside a container.
ENV PYTHONUNBUFFERED=True

ENV LOG_LEVEL=info

# Get the files we need from the previous build:
# - our source code
# - our dependencies
# - the uvicorn, ddtrace-run binaries
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /code /code
COPY --from=builder /usr/local/bin/uvicorn /usr/local/bin/uvicorn
COPY --from=builder /usr/local/bin/ddtrace-run /usr/local/bin/ddtrace-run

WORKDIR /code/smith-playground

# Remove pip and setuptools for security
RUN pip uninstall pip -y && \
    find /usr/local/lib/python3.11/site-packages -type d -name 'setuptools*' -print0 | xargs -0 rm -r

# Start the application
CMD uvicorn playground.main:app --host 0.0.0.0 --port $PORT --log-level $LOG_LEVEL --loop uvloop --http httptools --no-access-log
