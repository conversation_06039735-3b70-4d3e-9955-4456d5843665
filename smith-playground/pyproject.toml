[tool.poetry]
name = "smith-playground"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
packages = [{ include = "playground" }]

[tool.poetry.dependencies]
python = "^3.11"
lc-config = { path = "../lc_config", develop = true }
lc-database = {path = "../lc_database", develop = true}
lc-logging = { path = "../lc_logging", develop = true }
lc-metrics = { path = "../lc_metrics", develop = true }
langchain-core = "^0.3.56"
langchain-aws = "^0.2.17"
langchain-groq = "^0.3.1"
langchain-mistralai = "^0.2.9"
langchain-google-vertexai = "^2.0.21"
langchain-anthropic = "^0.3.12"
langchain-google-genai = "^2.1.4"
langchain-openai = "^0.3.14"
langchain-fireworks = "^0.2.8"
langchain-xai = "^0.2.2"
langchain-deepseek = "^0.1.3"
langserve = "^0.3.1"
google-crc32c = "1.5.0"
shapely = "2.0.6"
# Temp hack
openai = ">=1.60.2"
groq = ">=0.15"
cryptography = "^44.0.1"
truststore = "^0.10.1"
httpcore = {git = "https://github.com/langchain-ai/httpcore"}


[tool.poetry.group.dev.dependencies]
poethepoet = "^0.21.1"
freezegun = "^1.4.0"
rich = "^13.7.1"


[tool.poetry.group.test.dependencies]
pytest = "^8"
pytest-watcher = "^0.3.4"
httpx-sse = "^0.4.0"
pytest-xdist = "^3.6.1"
pytest-asyncio = "^0.25.3"


[tool.poetry.group.lint.dependencies]
ruff = "^0.11.2"
types-toml = "^********"


[tool.poetry.group.typing.dependencies]
mypy = "^1.3.0"
pandas-stubs = "^2.0.2.230605"
types-requests = "<********"
types-redis = "^********"
types-python-dateutil = "^2.8.19.20240106"

[tool.ruff]
lint.select = [
  "E",  # pycodestyle
  "F",  # pyflakes
  "I",  # isort
]
lint.ignore = ["E501"]
target-version = "py311"

[tool.mypy]
ignore_missing_imports = "True"
follow_imports = "skip"
exclude = ["notebooks"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe.tasks]
_format-ruff-check = "ruff check --fix ."
_format-ruff-format = "ruff format ."
format = ["_format-ruff-check", "_format-ruff-format"]

_lint-mypy = "mypy ."
_lint-ruff-version="ruff version"
_lint-ruff-check = "ruff check ."
_lint-ruff-format = "ruff format --check ."
lint = ["_lint-ruff-version", "_lint-mypy", "_lint-ruff-check", "_lint-ruff-format"]

[tool.pytest.ini_options]
asyncio_mode = "auto"
