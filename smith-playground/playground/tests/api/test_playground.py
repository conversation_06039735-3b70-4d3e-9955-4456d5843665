"""Test playground endpoints"""

import os
from uuid import uuid4

import pytest
from fastapi.testclient import TestClient
from langchain_core.load import load
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate

from playground.main import app

pytestmark = pytest.mark.anyio

chain = {
    "lc": 1,
    "type": "constructor",
    "id": ["langchain", "schema", "runnable", "RunnableSequence"],
    "kwargs": {
        "first": {
            "lc": 1,
            "type": "constructor",
            "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
            "kwargs": {
                "input_variables": ["topic"],
                "messages": [
                    {
                        "lc": 1,
                        "type": "constructor",
                        "id": [
                            "langchain",
                            "prompts",
                            "chat",
                            "HumanMessagePromptTemplate",
                        ],
                        "kwargs": {
                            "prompt": {
                                "lc": 1,
                                "type": "constructor",
                                "id": [
                                    "langchain",
                                    "prompts",
                                    "prompt",
                                    "PromptTemplate",
                                ],
                                "kwargs": {
                                    "input_variables": ["topic"],
                                    "template": "Help me score this response: {topic}",
                                    "template_format": "f-string",
                                },
                                "name": "PromptTemplate",
                            }
                        },
                    }
                ],
            },
            "name": "ChatPromptTemplate",
        },
        "last": {
            "lc": 1,
            "type": "constructor",
            "id": ["langchain", "chat_models", "openai", "ChatOpenAI"],
            "kwargs": {
                "model_name": "gpt-3.5-turbo",
                "temperature": 0.7,
                "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]},
                "max_retries": 2,
                "n": 1,
            },
            "name": "ChatOpenAI",
        },
    },
    "name": "RunnableSequence",
}

placeholder_chain = {
    "lc": 1,
    "type": "constructor",
    "id": ["langchain", "schema", "runnable", "RunnableSequence"],
    "kwargs": {
        "first": {
            "lc": 1,
            "type": "constructor",
            "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
            "kwargs": {
                "input_variables": [],
                "optional_variables": ["messages"],
                "partial_variables": {"messages": []},
                "messages": [
                    {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "prompts", "chat", "MessagesPlaceholder"],
                        "kwargs": {"variable_name": "messages", "optional": True},
                    }
                ],
            },
            "name": "ChatPromptTemplate",
        },
        "last": {
            "lc": 1,
            "type": "constructor",
            "id": ["langchain", "chat_models", "openai", "ChatOpenAI"],
            "kwargs": {
                "model_name": "gpt-4o-mini",
                "temperature": 0.7,
                "openai_api_key": {"lc": 1, "type": "secret", "id": ["OPENAI_API_KEY"]},
                "max_retries": 2,
                "n": 1,
            },
            "name": "ChatOpenAI",
        },
    },
    "name": "RunnableSequence",
}


tool_schema = {
    "type": "object",
    "title": "Grade",
    "required": ["score"],
    "properties": {"score": {"type": "integer", "description": "Provide the score:"}},
    "description": "Grade the quiz based upon the above criteria with a score.",
}
PROMPT = ChatPromptTemplate.from_template("Help me score this response: {topic}")


def test_ping():
    client = TestClient(app)
    response = client.get("/ok")
    assert response.status_code == 200, response.json()
    assert response.json() == {"ok": True}


@pytest.mark.parametrize("tool", [None, tool_schema])
@pytest.mark.parametrize("repetitions", [1, 2])
def test_invoke(tool, repetitions, client):
    body = {
        "manifest": chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "input": {"topic": "asdfafd"},
        "options": {},
        "run_id": None,
        "repo_id": None,
        "repetitions": repetitions,
    }
    if tool:
        body["tools"] = [tool]
    response = client.post("/playground/invoke", json=body)
    assert response.status_code == 200, response.json()
    assert response.json()
    assert len(response.json()) == repetitions
    for item in response.json():
        assert "outputs" in item
        outputs = item["outputs"]
        assert "output" in outputs
        message: AIMessage = load(outputs["output"])
        assert isinstance(
            message,
            AIMessage,
        )
        if tool:
            assert message.tool_calls
        else:
            assert message.content


@pytest.mark.parametrize("tool", [None, tool_schema])
def test_batch(tool, client):
    body = {
        "manifest": chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "input": [{"topic": "asdfafd"}] * 2,
        "options": [{}] * 2,
        "run_id": None,
        "repo_id": None,
    }
    if tool:
        body["tools"] = [tool]
    response = client.post("/playground/batch", json=body)
    assert response.status_code == 200, response.json()
    assert response.json()
    assert len(response.json()) == 2
    for item in response.json():
        assert "outputs" in item

        outputs = item["outputs"]
        assert "output" in outputs
        message: AIMessage = load(outputs["output"])
        assert isinstance(
            message,
            AIMessage,
        )
        if tool:
            assert message.tool_calls
        else:
            assert message.content


@pytest.mark.parametrize("tool", [None, tool_schema])
def test_batch_partial_error(tool, client):
    """Test batch where one input fails."""
    body = {
        "manifest": chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "input": [{"topic": "asdfafd"}, {"topic": "too long input " * 16_000}],
        "options": [{}] * 2,
        "run_id": None,
        "repo_id": None,
    }
    if tool:
        body["tools"] = [tool]
    response = client.post("/playground/batch", json=body)
    assert response.status_code == 200, response.json()
    response_json = response.json()
    assert response_json
    assert len(response_json) == 2

    successful_response, error_response = response_json
    assert "outputs" in successful_response
    outputs = successful_response["outputs"]
    assert "output" in outputs
    message: AIMessage = load(outputs["output"])
    assert isinstance(message, AIMessage)
    if tool:
        assert message.tool_calls
    else:
        assert message.content

    assert error_response.get("error")
    assert error_response["error"].startswith("BadRequestError")
    assert not error_response.get("ouputs")


def test_examples_batch_partial_error(client):
    example_1_uuid = str(uuid4())
    example_2_uuid = str(uuid4())
    example_1_rep_1 = {
        "inputs": {"topic": "asdfafd"},
        "id": example_1_uuid,
        "run_id": str(uuid4()),
    }
    example_1_rep_2 = {
        "inputs": {"topic": "asdfafd"},
        "id": example_1_uuid,
        "run_id": str(uuid4()),
    }
    example_2_rep_1 = {
        "inputs": {"topic": "too long input " * 16_000},
        "id": example_2_uuid,
        "run_id": str(uuid4()),
    }
    example_2_rep_2 = {
        "inputs": {"topic": "too long input " * 16_000},
        "id": example_2_uuid,
        "run_id": str(uuid4()),
    }
    examples = [example_1_rep_1, example_1_rep_2, example_2_rep_1, example_2_rep_2]
    examples_by_run_id = {example["run_id"]: example for example in examples}
    body = {
        "manifest": chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "examples": examples,
        "project_name": "foo",
        "project_id": str(uuid4()),
        "options": {},
    }
    response = client.post("/internal/playground/examples/batch", json=body)
    assert response.status_code == 200, response.json()
    assert response.json()
    assert len(response.json()) == 4
    for item in response.json()[:2]:
        assert item["id"] in examples_by_run_id
        run_id = item["id"]
        assert item["reference_example_id"] == examples_by_run_id[run_id]["id"]
        assert item["inputs"] == examples_by_run_id[run_id]["inputs"]

        assert "outputs" in item
        outputs = item["outputs"]
        assert "output" in outputs
        message = outputs["output"]
        assert message["content"]
        assert message["type"] == "ai"
    for item in response.json()[2:]:
        assert item["id"] in examples_by_run_id
        run_id = item["id"]
        assert item["reference_example_id"] == examples_by_run_id[run_id]["id"]
        assert item["inputs"] == examples_by_run_id[run_id]["inputs"]

        assert item.get("error")
        assert item["error"].startswith("BadRequestError")
        assert not item.get("ouputs")


def test_examples_batch(client):
    example_1_uuid = str(uuid4())
    example_2_uuid = str(uuid4())
    example_1_rep_1 = {
        "inputs": {"topic": "asdfafd"},
        "id": example_1_uuid,
        "run_id": str(uuid4()),
    }
    example_1_rep_2 = {
        "inputs": {"topic": "asdfafd"},
        "id": example_1_uuid,
        "run_id": str(uuid4()),
    }
    example_2_rep_1 = {
        "inputs": {"topic": "asdfafd"},
        "id": example_2_uuid,
        "run_id": str(uuid4()),
    }
    example_2_rep_2 = {
        "inputs": {"topic": "asdfafd"},
        "id": example_2_uuid,
        "run_id": str(uuid4()),
    }
    examples = [example_1_rep_1, example_1_rep_2, example_2_rep_1, example_2_rep_2]
    examples_by_run_id = {example["run_id"]: example for example in examples}
    body = {
        "manifest": chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "examples": examples,
        "project_name": "foo",
        "project_id": str(uuid4()),
        "options": {},
    }
    response = client.post("/internal/playground/examples/batch", json=body)
    assert response.status_code == 200, response.json()
    assert response.json()
    assert len(response.json()) == 4
    for item in response.json():
        assert item["id"] in examples_by_run_id
        run_id = item["id"]
        assert item["reference_example_id"] == examples_by_run_id[run_id]["id"]
        assert item["inputs"] == examples_by_run_id[run_id]["inputs"]

        assert "outputs" in item
        outputs = item["outputs"]
        assert "output" in outputs
        message = outputs["output"]
        assert message["content"]
        assert message["type"] == "ai"


@pytest.mark.parametrize("tool", [None, tool_schema])
def test_stream(tool, client):
    body = {
        "manifest": chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "input": [{"topic": "asdfafd"}],
        "options": {},
        "run_id": None,
        "repo_id": None,
    }
    if tool:
        body["tools"] = [tool]
    response = client.stream("POST", "/playground/stream", json=body)
    with response as stream:
        assert stream.status_code == 200
        chunks = []
        for chunk in stream.iter_lines():
            if chunk:
                chunks.append(chunk)
        assert chunks


@pytest.mark.parametrize(
    "model_id",
    [
        ["langchain", "chat_models", "openai", "ChatOpenAI"],
        ["langchain", "chat_models", "mistralai", "ChatMistralAI"],
        ["langchain", "chat_models", "groq", "ChatGroq"],
        ["langsmith", "playground", "ChatCustomModel"],
    ],
)
def test_model_discovery(client, model_id):
    body = {
        "id": model_id,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY", ""),
            "MISTRAL_API_KEY": os.environ.get("MISTRAL_API_KEY", ""),
            "GROQ_API_KEY": os.environ.get("GROQ_API_KEY", ""),
            "url": "http://localhost:7232/chat",
        },
    }
    response = client.post("/playground/models", json=body)
    assert response.status_code == 200, response.json()
    assert response.json()
    for model in response.json():
        assert "label" in model
        assert "value" in model


def test_examples_stored_serialized_messages(client):
    stored_messages_example = {
        "inputs": {
            "messages": [
                {
                    "type": "system",
                    "data": {
                        "content": "you're a helpful assistant",
                        "additional_kwargs": {"foo": "bar"},
                    },
                },
                {
                    "type": "human",
                    "data": {
                        "content": [
                            {
                                "type": "text",
                                "text": "what does existence precedes essence mean",
                            }
                        ],
                        "name": "echo",
                    },
                },
            ],
        },
        "id": str(uuid4()),
        "run_id": str(uuid4()),
    }
    serialized_messages_example = {
        "inputs": {
            "messages": [
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "SystemMessage"],
                    "kwargs": {
                        "content": "You're an amazing at theoretical computer science"
                    },
                },
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": [
                            {
                                "type": "text",
                                "text": "Give me your best guess: do you suspect that P does or does not equal NP?",
                            }
                        ]
                    },
                },
            ],
        },
        "id": str(uuid4()),
        "run_id": str(uuid4()),
    }
    examples = [stored_messages_example, serialized_messages_example]
    examples_by_run_id = {example["run_id"]: example for example in examples}
    body = {
        "manifest": placeholder_chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "examples": examples,
        "project_name": "foo",
        "project_id": str(uuid4()),
        "options": {},
    }
    response = client.post("/internal/playground/examples/batch", json=body)
    assert response.status_code == 200, response.json()
    assert response.json()
    assert len(response.json()) == 2
    for item in response.json():
        assert item["id"] in examples_by_run_id
        run_id = item["id"]
        assert item["reference_example_id"] == examples_by_run_id[run_id]["id"]

        assert "outputs" in item
        outputs = item["outputs"]
        assert "output" in outputs
        message = outputs["output"]
        assert message["content"]
        assert message["type"] == "ai"

    expected_stored_message_inputs = [
        {
            "type": "system",
            "content": "you're a helpful assistant",
            "foo": "bar",
        },
        {
            "type": "human",
            "content": [
                {
                    "type": "text",
                    "text": "what does existence precedes essence mean",
                }
            ],
            "name": "echo",
        },
    ]

    expected_serialized_message_inputs = [
        {
            "content": "You're an amazing at theoretical computer science",
            "additional_kwargs": {},
            "response_metadata": {},
            "type": "system",
            "name": None,
            "id": None,
        },
        {
            "content": [
                {
                    "type": "text",
                    "text": "Give me your best guess: do you suspect that P does or does not equal NP?",
                }
            ],
            "additional_kwargs": {},
            "response_metadata": {},
            "type": "human",
            "name": None,
            "id": None,
            "example": False,
        },
    ]
    assert response.json()[0]["inputs"]["messages"] == expected_stored_message_inputs
    assert (
        response.json()[1]["inputs"]["messages"] == expected_serialized_message_inputs
    )


def test_validate(client):
    body = {
        "manifest": placeholder_chain,
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "options": {},
    }
    response = client.post("/internal/playground/validate", json=body)
    assert response.status_code == 200, response.json()


def test_validate_fails(client):
    body = {
        "manifest": {"first": ["langchain", "schema", "badSequence"]},
        "secrets": {
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
        },
        "options": {},
    }
    response = client.post("/internal/playground/validate", json=body)
    assert response.status_code == 400
    assert response.json()
    assert (
        "Failed to validate chain: 400: Chain is not a runnable"
        in response.json()["detail"]
    )


def test_use_or_fallback_to_workspace_secrets(client):
    body = {
        "manifest": chain,
        "secrets": {},
        "input": {"topic": "asdfafd"},
        "options": {},
        "run_id": None,
        "repo_id": None,
        "use_or_fallback_to_workspace_secrets": True,
    }
    response = client.post("/playground/invoke", json=body)
    assert response.status_code == 200, response.json()
    assert response.json()
    assert len(response.json()) == 1
    for item in response.json():
        assert "outputs" in item
        outputs = item["outputs"]
        assert "output" in outputs
        message: AIMessage = load(outputs["output"])
        assert isinstance(
            message,
            AIMessage,
        )
        assert message.content
