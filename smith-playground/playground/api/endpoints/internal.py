import asyncio
from itertools import chain as chain_iterable
from typing import Any, Union
from uuid import UUID, uuid4

import httpx
import orj<PERSON>
import structlog
from fastapi import Depends, Header, HTTPException, Response
from langchain_core.load import dumps
from langchain_core.runnables.utils import Output
from lc_config.service_communication_settings import ServiceName
from lc_database import api_router
from lc_database.queue.serializer import ORJSONSerializer
from lc_database.service_client import get_service_client
from lc_database.stream import jsonpatch_sse_stream
from lc_database.tracer import LocalTracer
from sse_starlette import EventSourceResponse

from playground.models.playground.schema import (
    PlaygroundExample,
    PlaygroundExamplesRequestSchema,
    PlaygroundRequestSchema,
)
from playground.models.playground.utils import (
    convert_run_to_serializable,
    format_example_inputs,
    load_chain_and_bind_tools,
    set_runnable_config,
)

router = api_router.TrailingSlashRouter()

logger = structlog.get_logger(__name__)


# Dependency to extract headers
def get_internal_auth_headers(
    x_service_key: str = Header(None),
    x_tenant_id: str = Header(None),
) -> dict[str, str]:
    auth_headers = {}
    if x_service_key:
        auth_headers["x-service-key"] = x_service_key
    if x_tenant_id:
        auth_headers["x-tenant-id"] = x_tenant_id
    # Return headers for use in route handlers
    return auth_headers


@router.post("/validate")
async def internal_validate_handler(
    request: PlaygroundRequestSchema,
    internal_auth_headers: dict[str, str] = Depends(get_internal_auth_headers),
):
    try:
        _ = await load_chain_and_bind_tools(request, internal_auth_headers)
        return {"success": True}
    except Exception as e:
        logger.error("Failed to validate chain", error=e)
        raise HTTPException(
            status_code=400, detail=f"Failed to validate chain: {str(e)}"
        )


@router.post("/examples/batch")
async def internal_batch_examples_handler(
    request: PlaygroundExamplesRequestSchema,
    internal_auth_headers: dict[str, str] = Depends(get_internal_auth_headers),
) -> Output:
    examples = request.examples
    inputs = [format_example_inputs(e.inputs) for e in examples]
    attachments = [e.attachments for e in examples]
    combined_inputs: list[dict | list] = []
    for i in range(len(inputs)):
        curr_input = inputs[i]
        curr_attachments = attachments[i]
        # This condition should always be true but makes linter happy
        if isinstance(curr_input, dict) and curr_attachments:
            combined_inputs.append({**curr_input, **curr_attachments})
        else:
            combined_inputs.append(curr_input)
    chain = await load_chain_and_bind_tools(request, internal_auth_headers)
    tracers = [
        LocalTracer(
            project_name=request.project_name or "",  # should never be None
            example_id=e.id,
        )
        for e in examples
    ]
    logger.info("Tracers", tracers=tracers)
    runnable_config = set_runnable_config(request)
    try:
        resp = await chain.abatch(
            combined_inputs,
            config=[
                {**runnable_config, "callbacks": [t], "run_id": e.run_id}
                for t, e in zip(tracers, examples)
            ],
            return_exceptions=True,
        )
        logger.info("resp", resp=resp)
        runs_to_return = list(
            chain_iterable.from_iterable(
                [
                    convert_run_to_serializable(v)
                    for v in t.queue.values()
                    if v["parent_run_id"] is None
                ]
                for t in tracers
            )
        )
        for run in runs_to_return:
            run["session_id"] = str(request.project_id)
        return Response(
            content=ORJSONSerializer.dumps(runs_to_return),
            media_type="application/json",
        )
    except Exception as e:
        logger.error("Failed to batch invoke chain", error=e)
        runs_to_return = list(
            chain_iterable.from_iterable(
                [
                    convert_run_to_serializable(v)
                    for v in t.queue.values()
                    if v["parent_run_id"] is None
                ]
                for t in tracers
            )
        )
        for run in runs_to_return:
            run["session_id"] = str(request.project_id)
        if runs_to_return:
            return Response(
                content=ORJSONSerializer.dumps(runs_to_return),
                media_type="application/json",
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to batch invoke chain")
    finally:
        try:
            async with get_service_client(ServiceName.BACKEND) as client:
                await asyncio.gather(
                    *(
                        t.upsert_runs_with_client(client, headers=internal_auth_headers)
                        for t in tracers
                    )
                )
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                raise HTTPException(status_code=429, detail="Rate limit exceeded")
            else:
                # Log error but still return response
                logger.error("Failed to upsert runs: %s", e)


@router.post("/examples/stream")
async def internal_examples_stream_handler(
    request: PlaygroundExamplesRequestSchema,
    internal_auth_headers: dict[str, str] = Depends(get_internal_auth_headers),
) -> EventSourceResponse:
    chain = await load_chain_and_bind_tools(request, internal_auth_headers)
    runnable_config = set_runnable_config(request)

    async def process_example(example: PlaygroundExample, run_id: UUID):
        tracer = LocalTracer(
            project_name=request.project_name or f"Playground {uuid4()}",
            example_id=example.id,
        )
        config = {**runnable_config, "callbacks": [tracer], "run_id": run_id}
        inputs = format_example_inputs(example.inputs)
        attachments = example.attachments

        is_first = True
        try:
            # This condition should always be true but makes linter happy
            if isinstance(inputs, dict) and attachments:
                combined_inputs: Union[dict, Any] = {**inputs, **attachments}
            else:
                combined_inputs = inputs
            async for chunk in chain.astream(combined_inputs, config=config):
                if is_first:
                    if run := tracer.queue.get(run_id):
                        run = run.copy()
                        if not run.get("outputs"):
                            run["outputs"] = [chunk]
                        else:
                            run["outputs"] = [run["outputs"]]
                        run = convert_run_to_serializable(run)
                        run["session_id"] = str(request.project_id)
                        serialized_run = ORJSONSerializer.dumps(run)
                        is_first = False
                        yield {
                            "op": "replace",
                            "path": f"/runs/{example.id}",
                            "value": orjson.Fragment(serialized_run),
                        }
                else:
                    yield {
                        "op": "add",
                        "path": f"/runs/{example.id}/outputs/-",
                        "value": orjson.Fragment(dumps(chunk)),
                    }
            # yield the full run
            run = tracer.queue.get(run_id)
            run = run.copy()
            run = convert_run_to_serializable(run)
            run["session_id"] = str(request.project_id)
            serialized_run = ORJSONSerializer.dumps(run)
            yield {
                "op": "replace",
                "path": f"/runs/{example.id}",
                "value": orjson.Fragment(serialized_run),
            }
        except Exception as e:
            if run := tracer.queue.get(run_id):
                run = run.copy()
                run["outputs"] = []
                run = convert_run_to_serializable(run)
                run["session_id"] = str(request.project_id)
                serialized_run = ORJSONSerializer.dumps(run)
                yield {
                    "op": "replace",
                    "path": f"/runs/{example.id}",
                    "value": orjson.Fragment(serialized_run),
                }
            else:
                yield {
                    "op": "replace",
                    "path": f"/runs/{example.id}",
                    "value": orjson.Fragment(
                        ORJSONSerializer.dumps(
                            {
                                "error": str(e),
                                "session_id": str(request.project_id),
                                "reference_example_id": str(example.id),
                            }
                        )
                    ),
                }
        finally:
            logger.info(
                "internal auth headers", internal_auth_headers=internal_auth_headers
            )
            try:
                async with get_service_client(ServiceName.BACKEND) as client:
                    await tracer.upsert_runs_with_client(
                        client, headers=internal_auth_headers
                    )
            except httpx.HTTPStatusError as e:
                if e.response.status_code != 429:
                    logger.error("Failed to upsert runs", error=e)

    async def _stream_results():
        gens = [
            process_example(example, example.run_id) for example in request.examples
        ]
        nexts = {asyncio.create_task(anext(gen, None)): gen for gen in gens}
        while nexts:
            done, _ = await asyncio.wait(nexts, return_when=asyncio.FIRST_COMPLETED)
            for task in done:
                gen = nexts.pop(task)
                if chunk := task.result():
                    nexts[asyncio.create_task(anext(gen, None))] = gen
                    yield [chunk]

    return EventSourceResponse(jsonpatch_sse_stream(_stream_results()))
