import datetime
import json
from typing import Any, Dict, List, Union
from uuid import UUID

import orjson
import structlog
from cryptography.fernet import Fernet
from fastapi import HTTPException
from google.oauth2 import service_account
from langchain_core.language_models import BaseChatModel
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.load import load
from langchain_core.prompt_values import ChatPromptValue
from langchain_core.prompts import (
    AIMessagePromptTemplate,
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain_core.prompts.dict import DictPromptTemplate
from langchain_core.prompts.structured import StructuredPrompt
from langchain_core.rate_limiters import InMemoryRateLimiter
from langchain_core.runnables import (
    Runnable,
    RunnableBinding,
    RunnableConfig,
    RunnableLambda,
    RunnableSequence,
)
from langchain_google_vertexai import ChatVertexAI
from lc_config.service_communication_settings import ServiceName
from lc_database.service_client import get_service_client
from lc_database.tracer import LocalTracer

from playground.models.playground.no_trace import NoTrace
from playground.models.playground.schema import (
    PlaygroundRequestSchema,
)
from playground.models.playground.tls import try_set_client_for_provider

logger = structlog.get_logger(__name__)

_SERIALIZED_MESSAGE_IDS: list = [
    "AIMessage",
    "AIMessageChunk",
    "ChatMessage",
    "ChatMessageChunk",
    "FunctionMessage",
    "FunctionMessageChunk",
    "HumanMessage",
    "HumanMessageChunk",
    "SystemMessage",
    "SystemMessageChunk",
    "ToolMessage",
    "ToolMessageChunk",
    "RemoveMessage",
]

IMPORT_MAPPING_OVERRIDES = {
    ("langsmith", "playground", "ChatCustomModel"): (
        "playground",
        "models",
        "playground",
        "custom",
        "ChatCustomModel",
    ),
    ("langsmith", "playground", "CustomModel"): (
        "playground",
        "models",
        "playground",
        "custom",
        "CustomModel",
    ),
    ("langchain", "chat_models", "fake", "FakeMessagesListChatModel"): (
        "playground",
        "models",
        "playground",
        "fake",
        "FakeMessagesListChatModel",
    ),
    ("langchain", "chat_models", "fake", "FakeStreamingMessagesListChatModel"): (
        "playground",
        "models",
        "playground",
        "fake",
        "FakeStreamingMessagesListChatModel",
    ),
}


async def load_chain_and_bind_tools(request: PlaygroundRequestSchema, auth_headers):
    return await _load_chain_and_bind_tools(request, auth_headers)


async def _load_chain_and_bind_tools(request: PlaygroundRequestSchema, auth_headers):
    # Remove playground constructor
    if request.manifest.get("id") and "PromptPlayground" in request.manifest["id"]:
        request.manifest["id"] = ["langchain", "schema", "runnable", "RunnableSequence"]
    try:
        # If request.use_or_fallback_to_workspace_secrets is True, fetch workspace secrets and overwrite the secrets parameter:
        if request.use_or_fallback_to_workspace_secrets:
            # create random secret to use as encryption key
            secret_key = Fernet.generate_key()
            fernet = Fernet(secret_key)
            async with get_service_client(ServiceName.BACKEND) as client:
                secrets_resp = await client.post(
                    "/internal/workspaces/current/encrypted-secrets",
                    headers=auth_headers,
                    json={"secret_key": secret_key.decode()},
                )
                secrets_resp.raise_for_status()
                encrypted_resp = secrets_resp.json()
                encrypted_secrets = encrypted_resp["encrypted_secrets"].encode()
                decrypted_secrets = fernet.decrypt(encrypted_secrets).decode()
                secrets = orjson.loads(decrypted_secrets)
                # request.secrets will only contain values if the check in usePlaygroundSubmit shows that the user
                # selected browser secrets, but there were some missing so we will fallback to the workspace secrets
                # otherwise, request.secrets is just {}
                secrets_dict = request.secrets | {
                    secret["key"]: secret["value"] for secret in secrets
                }
                request.secrets = secrets_dict
        chain = _set_google_creds_and_load(
            request.manifest, request.secrets, request.requests_per_second
        )
        if isinstance(chain, RunnableSequence) and isinstance(
            chain.first, StructuredPrompt
        ):
            if not chain.middle and isinstance(
                chain.last, (BaseLanguageModel, RunnableBinding)
            ):
                # we know this is a funkily constructed StructuredPrompt sequence that didn't
                # serialize an output parser with it, so we need to manually pipe the
                # prompt and the model together
                first = chain.first
                last = chain.last
                chain = first | last
        if (
            isinstance(chain, RunnableSequence)
            and isinstance(chain.first, ChatPromptTemplate)
            and request.repo_handle
        ):
            metadata = {
                "metadata": {
                    "lc_hub_repo": request.repo_handle,
                    "lc_hub_commit_hash": request.commit,
                    "lc_hub_owner": request.owner,
                }
            }
            chain.first = chain.first.with_config(metadata)
    except Exception as e:
        # Failed to load chain, raise HTTPException
        logger.error("Failed to load chain", error=e)
        raise HTTPException(status_code=400, detail=str(e))
    if chain is None:
        raise HTTPException(status_code=400, detail="Failed to load chain")
    if not isinstance(chain, Runnable):
        raise HTTPException(status_code=400, detail="Chain is not a runnable")
    # Try to load tools from options
    if request.tools:
        try:
            # Check if last object in chain is a model and if so, add tool calls
            tool_kwargs = {
                k: getattr(request, k)
                for k in ("tools", "tool_choice", "parallel_tool_calls")
                if hasattr(request, k) and getattr(request, k) is not None
            }
            if isinstance(chain, RunnableSequence) and hasattr(
                chain.last, "bind_tools"
            ):
                chain.last = chain.last.bind_tools(**tool_kwargs)
            elif hasattr(chain, "bind_tools"):
                chain = chain.bind_tools(**tool_kwargs)
            else:
                logger.error(
                    "Failed to bind tools to last object in chain",
                    last_object=chain.last,
                )
        except Exception as e:
            logger.error("Failed to bind tools to last object in chain", error=e)
            raise HTTPException(
                status_code=400, detail=f"Unable to pass tools to model. Error:\n{e}"
            )
    try_set_client_for_provider(chain)
    chain = _inject_attachments(chain)
    return chain


def get_tracer_for_request(request: PlaygroundRequestSchema) -> LocalTracer:
    return LocalTracer(
        project_name="playground" if not request.project_name else request.project_name
    )


def set_runnable_config(
    request: PlaygroundRequestSchema,
    tracer: LocalTracer | None = None,
) -> RunnableConfig:
    runnable_config = request.options.copy()
    tags = []
    if request.run_id:
        tags.append(f"playground:${request.run_id}")
    if tracer:
        runnable_config["callbacks"] = [tracer]
        if "tags" in runnable_config:
            runnable_config["tags"].extend(tags)
        else:
            runnable_config["tags"] = tags
    return runnable_config


def convert_run_to_serializable(run: dict) -> dict:
    new_run = run.copy()
    if isinstance(new_run.get("id"), UUID):
        new_run["id"] = str(new_run["id"])
    if isinstance(new_run.get("start_time"), datetime.datetime):
        new_run["start_time"] = new_run["start_time"].isoformat()
    if isinstance(new_run.get("end_time"), datetime.datetime):
        new_run["end_time"] = new_run["end_time"].isoformat()
    if isinstance(new_run.get("parent_run_id"), UUID):
        new_run["parent_run_id"] = str(new_run["parent_run_id"])
    if isinstance(new_run.get("reference_example_id"), UUID):
        new_run["reference_example_id"] = str(new_run["reference_example_id"])
    if isinstance(new_run.get("session_id"), UUID):
        new_run["session_id"] = str(new_run["session_id"])
    if isinstance(new_run.get("trace_id"), UUID):
        new_run["trace_id"] = str(new_run["trace_id"])
    return new_run


def _set_google_creds_and_load(
    serialized: dict, secrets: dict, requests_per_second: int | None
) -> Any:
    vertex_err: Any = None
    if "GOOGLE_VERTEX_AI_WEB_CREDENTIALS" in secrets:
        try:
            creds_dict = json.loads(secrets["GOOGLE_VERTEX_AI_WEB_CREDENTIALS"])
            secrets["GOOGLE_VERTEX_AI_WEB_CREDENTIALS"] = (
                service_account.Credentials.from_service_account_info(creds_dict)
            )
        except json.JSONDecodeError as e:
            # Not clear this is a json error based on default msg.
            msg = f"{type(e)}: {e.msg}"
            vertex_err = json.JSONDecodeError(msg, e.doc, e.pos)
        except Exception as e:
            vertex_err = e
    chain = load(
        serialized,
        secrets_map=secrets,
        secrets_from_env=False,
        additional_import_mappings=IMPORT_MAPPING_OVERRIDES,
        valid_namespaces=["langsmith", "langchain_community", "langchain_deepseek"],
    )
    if requests_per_second is not None:
        chat_model = _find_chat_model(chain)
        chat_model.rate_limiter = InMemoryRateLimiter(
            requests_per_second=requests_per_second
        )
    if vertex_err and isinstance(_find_chat_model(chain), ChatVertexAI):
        raise vertex_err
    return chain


def _find_chat_model(chain: Any) -> BaseChatModel:
    if isinstance(chain, BaseChatModel):
        return chain
    elif isinstance(chain, RunnableSequence):
        for step in chain.steps:
            if isinstance(step, BaseChatModel):
                return step
            elif isinstance(step, RunnableBinding):
                if isinstance(step.bound, BaseChatModel):
                    return step.bound
    raise ValueError("Unable to find chat model")


def format_example_inputs(
    inputs: Union[dict, list], *, depth: int = 0, max_depth: int = 100
) -> Union[dict, list]:
    if depth >= max_depth:
        return inputs
    elif isinstance(inputs, list):
        new_list = []
        for e in inputs:
            if isinstance(e, (list, dict)):
                new_list.append(format_example_inputs(e, depth=depth + 1))
            else:
                new_list.append(e)
        return new_list
    elif _is_stored_message(inputs):
        return _format_stored_message(inputs)
    elif _is_serialized_message(inputs):
        return load(inputs)
    else:
        new_dict = {}
        for k, v in inputs.items():
            if isinstance(v, (list, dict)):
                new_dict[k] = format_example_inputs(v, depth=depth + 1)
            else:
                new_dict[k] = v
        return new_dict


def _format_stored_message(o: Any) -> Any:
    "Return msg format supported by langchain_core.message.utils.convert_to_messages()."
    if _is_stored_message(o):
        additional_kwargs = o["data"].get("additional_kwargs", {}) or {}
        data = {
            k: v for k, v in o["data"].items() if k not in ("role", "additional_kwargs")
        }
        o = {k: v for k, v in o.items() if k != "data"}
        return {**o, **data, **additional_kwargs}
    else:
        return o


def _is_stored_message(o: Any) -> bool:
    return (
        isinstance(o, dict)
        and "data" in o
        and ("type" in o or "role" in o)
        and "content" in o["data"]
    )


def _is_serialized_message(o: Any) -> bool:
    return (
        isinstance(o, dict)
        and all(k in o for k in ("id", "lc", "kwargs", "type"))
        and o["type"] == "constructor"
        and o["id"][-1] in _SERIALIZED_MESSAGE_IDS
    )


_INJECT_ALL_ATTACHMENTS_PROMPT_VARIABLE = (
    "__langsmith_playground_inject_all_attachments__"
)


def _inject_attachments(chain: Runnable) -> Runnable:
    if not isinstance(chain, RunnableSequence):
        return chain

    # Find first ChatPromptTemplate in sequence
    prompt_steps = [
        (i, step)
        for i, step in enumerate(chain.steps)
        if isinstance_or_boundinstance(step, ChatPromptTemplate)
    ]
    if not prompt_steps:
        return chain

    prompt_index, original_prompt = prompt_steps[0]
    messages = original_prompt.messages

    def prompt_with_attachments(inputs: dict) -> ChatPromptValue:
        attachments = {
            k: v
            for k, v in inputs.items()
            if isinstance(v, dict) and "mime_type" in v and "data" in v
        }

        inputs = {
            k: v["data"]
            if (
                k in original_prompt.input_variables
                and isinstance(v, dict)
                and "mime_type" in v
                and "data" in v
            )
            else v
            for k, v in inputs.items()
        }

        if (
            _INJECT_ALL_ATTACHMENTS_PROMPT_VARIABLE
            not in original_prompt.input_variables
        ):
            prompt_value = original_prompt.invoke(inputs)
            return prompt_value

        formatted_messages = []
        for i, msg in enumerate(messages):
            message_prompt = msg.prompt

            # Only inject into message templates that expect attachments
            if not (
                isinstance(
                    msg,
                    (
                        HumanMessagePromptTemplate,
                        AIMessagePromptTemplate,
                        SystemMessagePromptTemplate,
                    ),
                )
                and _INJECT_ALL_ATTACHMENTS_PROMPT_VARIABLE in msg.input_variables
            ):
                message_prompt = message_prompt.partial(
                    __langsmith_playground_inject_all_attachments__=""
                )
                formatted_messages.append(
                    message_prompt.invoke(
                        inputs,
                        callbacks=[NoTrace()],
                    ).to_messages()[i]
                )
                continue

            if isinstance(message_prompt, list) and any(
                isinstance(pt, DictPromptTemplate)
                and _INJECT_ALL_ATTACHMENTS_PROMPT_VARIABLE in pt.input_variables
                for pt in message_prompt
            ):
                prompt = original_prompt.partial(
                    __langsmith_playground_inject_all_attachments__=_INJECT_ALL_ATTACHMENTS_PROMPT_VARIABLE
                )
            else:
                prompt = original_prompt.partial(
                    __langsmith_playground_inject_all_attachments__=""
                )

            msg_to_inject = prompt.invoke(inputs).to_messages()[i]
            contents = _attachments_to_openai_content_blocks(attachments)

            if isinstance(msg_to_inject.content, str):
                msg_to_inject.content = [
                    {"type": "text", "text": msg_to_inject.content}
                ]
            else:
                msg_to_inject.content = [
                    block
                    for block in msg_to_inject.content
                    if not _is_placeholder_block(block)
                ]

            msg_to_inject.content.extend(contents)
            formatted_messages.append(msg_to_inject)

        return ChatPromptValue(messages=formatted_messages)

    return RunnableSequence(
        *chain.steps[:prompt_index],
        RunnableLambda(prompt_with_attachments),
        *chain.steps[prompt_index + 1 :],
    )


def _is_placeholder_block(block: Any) -> bool:
    """Check if a content block is a placeholder that should be replaced."""
    if not isinstance(block, dict):
        return False

    placeholder_values = ("", _INJECT_ALL_ATTACHMENTS_PROMPT_VARIABLE)

    # Image URL format
    if block.get("type") == "image_url":
        return (
            "image_url" in block
            and "url" in block["image_url"]
            and block["image_url"]["url"] in placeholder_values
        ) or (
            "url" in block
            and isinstance(block["url"], str)
            and block["url"] in placeholder_values
        )

    # File format
    if block.get("type") == "file":
        return (
            "file" in block
            and "file_data" in block["file"]
            and block["file"]["file_data"] in placeholder_values
        ) or (
            "data" in block
            and isinstance(block["data"], str)
            and block["data"] in placeholder_values
        )

    # Audio format
    if block.get("type") == "input_audio":
        return (
            "input_audio" in block
            and "data" in block["input_audio"]
            and block["input_audio"]["data"] in placeholder_values
        ) or (
            "data" in block
            and isinstance(block["data"], str)
            and block["data"] in placeholder_values
        )

    return False


def _attachments_to_openai_content_blocks(
    attachments: Dict[str, Dict[str, Any]],
) -> List[Dict[str, Any]]:
    contents = []
    supported_attachment_types = ["image/", "application/pdf", "audio/mp3", "audio/wav"]

    for k, v in attachments.items():
        if not isinstance(v, dict) or "mime_type" not in v or "data" not in v:
            continue

        mime_type = v["mime_type"]
        data = v["data"]

        if not any(mime_type.startswith(t) for t in supported_attachment_types):
            msg = (
                "Playground currently only supports image, PDF, and audio attachments."
            )
            raise ValueError(msg)

        if mime_type.startswith("image/"):
            contents.append({"type": "image_url", "image_url": {"url": data}})
        elif mime_type.startswith("application/pdf"):
            contents.append(
                {"type": "file", "file": {"filename": k, "file_data": data}}
            )
        elif mime_type.startswith("audio/"):
            # For audio files, we want to use the raw base64 data without the prefix
            base64_data = data.split(",")[1] if data.startswith("data:audio/") else data
            contents.append(
                {
                    "type": "input_audio",
                    "input_audio": {
                        "data": base64_data,
                        "format": mime_type.split("/")[1],
                    },
                }
            )

    return contents


def isinstance_or_boundinstance(obj: Any, type_: type) -> bool:
    return (
        isinstance(obj, type_)
        or isinstance(obj, RunnableBinding)
        and isinstance(obj.bound, type_)
    )
