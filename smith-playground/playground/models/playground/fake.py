from operator import itemgetter
from typing import Any, Iterator, List, Optional

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import AIMessageChunk, BaseMessage
from langchain_core.output_parsers import JsonOutputToolsParser
from langchain_core.outputs import ChatGeneration, ChatGenerationChunk, ChatResult


class FakeMessagesListChatModel(BaseChatModel):
    """Fake ChatModel for testing purposes."""

    error: Optional[str] = None
    responses: List[BaseMessage]
    sleep: Optional[float] = None
    i: int = 0

    a_great_secret: str

    @property
    def lc_secrets(self) -> dict[str, str]:
        return {"a_great_secret": "A_GREAT_SECRET"}

    def _generate(
        self,
        *args,
        **kwargs: Any,
    ) -> ChatResult:
        if self.error:
            raise ValueError(self.error)
        response = self.responses[self.i]
        if self.i < len(self.responses) - 1:
            self.i += 1
        else:
            self.i = 0
        generation = ChatGeneration(message=response)
        return ChatResult(generations=[generation])

    def with_structured_output(
        self, *args, **kwargs: Any
    ) -> "FakeMessagesListChatModel":
        return self | JsonOutputToolsParser(first_tool_only=True) | itemgetter("args")

    @property
    def _llm_type(self) -> str:
        return "fake-messages-list-chat-model"

    @classmethod
    def get_lc_namespace(cls):
        return ["langchain", "chat_models", "fake"]

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Whether the object is serializable by langchain.

        Default is False.
        """
        return True


class FakeStreamingMessagesListChatModel(FakeMessagesListChatModel):
    """Fake ChatModel that supports streaming for testing purposes."""

    chunk_size: int = 1

    def _stream(
        self,
        *args,
        **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        if self.error:
            raise ValueError(self.error)

        response = self.responses[self.i]
        if self.i < len(self.responses) - 1:
            self.i += 1
        else:
            self.i = 0

        # Get the content from the response
        content = response.content

        # Stream the content in chunks
        for i in range(0, len(content), self.chunk_size):
            chunk_text = content[i : i + self.chunk_size]
            chunk = AIMessageChunk(content=chunk_text)
            yield ChatGenerationChunk(message=chunk)

    @classmethod
    def get_lc_namespace(cls):
        """Get the namespace of the langchain object."""
        return ["langchain", "chat_models", "fake"]
