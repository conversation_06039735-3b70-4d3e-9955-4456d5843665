import { defineConfig, devices } from '@playwright/test';
import path from 'path';

import { config } from 'dotenv';
config({ path: path.resolve(__dirname, '.env') });

export default defineConfig({
  testDir: './src',
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: 3,
  reporter: 'html',
  timeout: 300000,
  use: {
    baseURL: process.env.BASE_URL,
    // bypassCSP: true,
    trace: process.env.CI ? "retain-on-failure" : 'on',
    video: process.env.CI ? "retain-on-failure" : "on",
    actionTimeout: 20000,
    screenshot: process.env.CI ? "only-on-failure" : 'on',
  },
  expect: {
    timeout: 10000,
  },
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        permissions: ["clipboard-read", 'clipboard-write'],
     },
    },
  ],
});
