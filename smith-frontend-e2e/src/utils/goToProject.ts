import { Page, expect } from '@playwright/test';

export const goToProject = async (page: Page, projectName: string) => {
  await page.goto('/projects');
  await expect.soft(page.getByTestId('run-count-chip').first()).toBeVisible();
  await page.getByPlaceholder('Search by name...').fill(projectName);
  await expect(page.getByTestId('run-count-chip')).toHaveCount(1);
  await expect(page.getByRole('progressbar')).not.toBeVisible();
  await page.getByRole('row', { name: projectName }).click();
  await expect
    .soft(page.getByRole('heading', { name: projectName }))
    .toBeVisible();
};
