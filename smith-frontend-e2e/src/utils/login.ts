import { expect } from '@playwright/test';
import { BASE_URL, EKS_TEST_ACCOUNT_PASSWORD } from './constants';

export async function loginAndEnterInviteCode(page, user) {
  if (BASE_URL !== 'https://eks.smith.langchain.dev') {
    if (!user) throw new Error('User not created');

    // login
    await page.goto('/');

    await page.getByText(`Already have an account? Log in`).click();
    await page.getByText(`Log in with email`).click();

    await page.getByPlaceholder('Your email address').fill(user.email);
    await page.getByPlaceholder('Your password').fill(user.password);
    await page.getByRole('button', { name: 'Continue', exact: true }).click();
  } else {
    await page.goto('/');
    await page.getByRole('button', { name: 'Log in via SSO' }).click();
    await page.getByRole('textbox', { name: '<PERSON>rna<PERSON>' }).fill('testguy');
    await page
      .getByRole('textbox', { name: 'Password' })
      .fill(EKS_TEST_ACCOUNT_PASSWORD);
    await page.getByRole('button', { name: 'submit' }).click();
  }

  await page.getByRole('button', { name: 'Get started' }).click();
  await page.getByRole('button', { name: /^Skip/ }).click();
  await expect(page.locator('p').filter({ hasText: 'Home' })).toBeVisible({
    timeout: 30000,
  });
}
