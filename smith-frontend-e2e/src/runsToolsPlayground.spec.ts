import { expect, test } from '@playwright/test';
import { randomUUID } from 'crypto';
import { Client } from 'langsmith';
import { createAndCopyApiKey, deleteApi<PERSON>ey } from './utils/apiKey';
import {
  ANTHROPIC_API_KEY,
  AZURE_OPENAI_API_KEY,
  BASE_URL,
  ENDPOINT_URL,
  GROQ_API_KEY,
  OPENAI_API_KEY,
  VERTEX_AI_API_CREDS,
} from './utils/constants';
import { expectMaybeTruncatedTextToInclude } from './utils/expectMaybeTruncatedTextToInclude';
import { loginAndEnterInviteCode } from './utils/login';
import {
  CreateSupabaseUser,
  createSupabaseUser,
  deleteUser,
} from './utils/supabase';

const playgroundConsolidateFFLifted = true;

let user: CreateSupabaseUser | null = null;
test.beforeAll(async () => {
  if (BASE_URL === 'https://eks.smith.langchain.dev') {
    console.debug('Skipping user creation');
    return;
  }
  user = await createSupabaseUser('playground');
  console.debug('Created user', user.id);
});

test.afterAll(async () => {
  if (user != null) {
    console.debug('Deleting user', user.id);
    await deleteUser(user.id);
  }
});

/**
 * This tests saving a tool prompt from propmts, ensuring that the tool
 * shows up in OpenAI, Anthropic, VertexAI, and Groq, and ensuring that the tool
 * shows up when we open those from runs
 */
test('Playground', async ({ page }) => {
  if (playgroundConsolidateFFLifted) {
    await loginAndEnterInviteCode(page, user);
    const apiKey = await createAndCopyApiKey(page);

    // create project
    const projectName = `${Math.floor(
      Math.random() * 100
    )}-test-dataset-${randomUUID()}`;
    const client = new Client({ apiKey, apiUrl: ENDPOINT_URL });
    await client.createProject({ projectName: projectName });

    // create a new prompt
    await page.goto('/prompts');
    // wait 1 second for the page to load
    await page.waitForTimeout(1000);
    await page.getByRole('button', { name: 'Prompt', exact: true }).click();

    // go into playground view
    await expect
      .soft(page.getByRole('heading', { name: `Playground` }))
      .toBeVisible();

    // populate api key
    await page.getByRole('button', { name: 'Secrets & API Keys' }).click();
    await page
      .getByTestId('playground-secret-input-OPENAI_API_KEY')
      .getByRole('textbox')
      .fill(OPENAI_API_KEY);

    /// create the prompt
    const humanMessage =
      'Use tool "reverseString" and fill with parameters targetString = {targetString}. Do not type anything else';
    await page
      .getByTestId('editable-message-1')
      .locator('[contenteditable="true"]')
      .fill(humanMessage);
    await page.waitForTimeout(500);
    // create a tool
    await page.getByTestId('playground-add-tool').click();

    const toolName = 'reverseString';
    await page
      .getByTestId('schema-title-input')
      .getByRole('textbox')
      .fill(toolName);

    const toolDescription = 'This is a tool to reverse a string';
    await page
      .getByTestId('schema-description-input')
      .getByRole('textbox')
      .fill(toolDescription);

    // add a param
    await page.locator('input[value="new0"]').fill('targetString');
    await page
      .locator('textarea[placeholder="Description of argument..."]')
      .fill('The string that you want to reverse');
    await page.waitForTimeout(500);
    // save the tool
    await page.getByTestId('schema-modal-save-button').click();
    await page.locator('.MuiModalClose-root').click();

    // expect tool title to have changed to tools since we added 1
    await expect(page.getByTestId('playground-add-tools')).toBeVisible();

    // save the prompt
    await page.getByRole('button', { name: 'Save' }).click();

    const promptName = `test-prompt-${randomUUID()}`;
    const parentInput = page.getByTestId('prompt-name-input');
    const input = parentInput.locator('input');
    await input.fill(promptName);

    await page.getByRole('button', { name: 'Save' }).click();
    await expect.soft(page.getByText(promptName).nth(0)).toBeVisible();

    // fill in the input
    await page
      .locator('textarea[placeholder="Enter variable value..."]')
      .fill('hello');
    await page.waitForTimeout(500);

    await page.getByRole('button', { name: 'Start' }).click();

    // check if it's in runs
    await page.getByTestId('playground-stats-link').click();
    await expect
      .soft(page.getByTestId(`run-tree-node-ChatOpenAI`))
      .toBeVisible();
    await page.getByTestId('run-tree-node-ChatOpenAI').click();

    await expect
      .soft(page.getByTestId('run-input-component-chat-0'))
      .toContainText('You are a chatbot.');
    await expect
      .soft(page.getByTestId('run-input-component-chat-1'))
      .toContainText(
        'humanUse tool "reverseString" and fill with parameters targetString = hello. Do not type anything else'
      );

    // Test playground here:
    await page.locator('[data-testid="playground-button"]').click();

    // expect the tool name to be in the list
    await page.getByTestId('playground-add-tools').click();
    await expectMaybeTruncatedTextToInclude(
      page.getByTestId('tool-modal-tools-list'),
      toolName
    );
    await page.locator('.MuiModalClose-root').click();

    // run again
    await page.getByRole('button', { name: 'Start' }).click();

    // check if the output contains the tool
    await expect
      .soft(page.getByTestId(`tool_call_function_name_${toolName}`))
      .toBeVisible();

    const openSettings = async () => {
      await page.getByTestId('prompt-settings-button').click();
    };

    // set proxy provider
    await openSettings();
    await page.getByRole('button', { name: 'Use Proxy Provider' }).click();
    await page.getByPlaceholder('Base URL').fill('www.google.com');
    await page.getByTestId('CloseIcon').click();

    // start again and expect error because the base url is not a valid url
    await page.getByRole('button', { name: 'Start' }).click();

    await expect.soft(page.getByRole('alert')).toBeVisible();

    // click on model settings
    await openSettings();

    const clickProviderSelect = async (label) => {
      await page.getByTestId('provider-select-button').click();
      await page.getByRole('option', { name: label }).click();
    };

    const closeSettings = async () => {
      await page.getByTestId('CloseIcon').click();
    };

    // set to false when our AzureOpenAI provider starts supporting tools
    const skipAzureOpenAI = true;

    // change to AzuerOpenAI as the provider
    let provider = 'Azure OpenAI';

    if (!skipAzureOpenAI) {
      await clickProviderSelect(provider);

      // fill in the deployment details
      const deploymentNameInput = page.getByLabel('Deployment Name');
      await deploymentNameInput.fill('gpt-4');

      const azureEndpointInput = page.getByLabel('Azure Endpoint');
      await azureEndpointInput.fill(
        'https://langchain-azureopenai.openai.azure.com/'
      );

      const apiVersionInput = page.getByLabel('API Version');
      await apiVersionInput.fill('2023-06-01-preview');

      // close model settings
      await page.getByRole('button').getByText(provider).nth(0).click();

      // open secrets
      await page.getByRole('button', { name: 'Secrets & API Keys' }).click();
      await page
        .getByTestId('playground-secret-input-AZURE_OPENAI_API_KEY')
        .getByRole('textbox')
        .fill(AZURE_OPENAI_API_KEY);

      // close secrets
      await page.getByRole('button', { name: 'Secrets & API Keys' }).click();

      // run again
      await page.getByRole('button', { name: 'Start' }).click();

      // check if the output contains the tool
      await expect
        .soft(page.getByTestId(`tool_call_function_name_${toolName}`))
        .toBeVisible();

      // click on model settings
      await page.getByRole('button').getByText(provider).click();

      // click on provider select
      await page.getByRole('button').getByText(provider).nth(1).click();
    }
    // change to Anthropic as the provider
    provider = 'Anthropic';
    await clickProviderSelect(provider);

    // verify that the default model is claude-3-7-sonnet
    const defaultAnthropicModel = 'claude-3-7-sonnet';
    await expect
      .soft(page.getByRole('button').getByText(defaultAnthropicModel).nth(0))
      .toBeVisible();

    // close model settings
    await closeSettings();

    // open secrets
    await page.getByRole('button', { name: 'Secrets & API Keys' }).click();
    await page
      .getByTestId('playground-secret-input-ANTHROPIC_API_KEY')
      .getByRole('textbox')
      .fill(ANTHROPIC_API_KEY);

    // close secrets
    await page.getByRole('button', { name: 'Secrets & API Keys' }).click();

    // run again
    await page.getByRole('button', { name: 'Start' }).click();

    // check if the output contains the tool
    await expect
      .soft(page.getByTestId(`tool_call_function_name_${toolName}`))
      .toBeVisible();

    // Go back to the runs page
    await page.getByTestId('playground-stats-link').click();
    await expect
      .soft(page.getByTestId(`run-tree-node-ChatAnthropic`))
      .toBeVisible();
    await page.getByTestId('run-tree-node-ChatAnthropic').click();
    await page.locator('[data-testid="playground-button"]').click();

    // Click the "Run" button
    await page.getByRole('button', { name: 'Start' }).click();

    // Check if there is a tool in the output
    await expect
      .soft(page.getByTestId(`tool_call_function_name_${toolName}`))
      .toBeVisible();

    // click on model settings
    await openSettings();

    // change to VertexAI as the provider
    provider = 'Vertex AI';
    await clickProviderSelect(provider);

    await closeSettings();

    // open secrets
    await page.getByRole('button', { name: 'Secrets & API Keys' }).click();

    const credentialsJson = JSON.parse(VERTEX_AI_API_CREDS);
    await page.getByLabel('Service Account Private Key').setInputFiles({
      name: 'vertex-ai-key.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify(credentialsJson)),
    });

    // close secrets
    await page.getByRole('button', { name: 'Secrets & API Keys' }).click();

    // run again
    await page.getByRole('button', { name: 'Start' }).click();

    // check if the output contains the tool
    await expect
      .soft(page.getByTestId(`tool_call_function_name_${toolName}`))
      .toBeVisible({ timeout: 30_000 });

    // Go back to the runs page
    await page.getByTestId('playground-stats-link').click();
    await expect
      .soft(page.getByTestId(`run-tree-node-ChatVertexAI`))
      .toBeVisible();
    await page.getByTestId('run-tree-node-ChatVertexAI').click();
    await page.locator('[data-testid="playground-button"]').click();

    // Click the "Run" button
    await page.getByRole('button', { name: 'Start' }).click();

    // wait for 1 second to make sure that the tool from previously is gone
    await page.waitForTimeout(1000);

    // Check if there is a tool in the output
    await expect
      .soft(page.getByTestId(`tool_call_function_name_${toolName}`))
      .toBeVisible();

    // click on model settings
    // await openSettings();

    // // change to Groq as the provider
    // provider = 'Groq';
    // await clickProviderSelect(provider);

    // // change the temperature slider
    // const temperatureInput = page
    //   .getByTestId('Temperature-input')
    //   .locator('input');
    // await temperatureInput.fill('0.1');
    // await expect(temperatureInput).toHaveValue('0.1');

    // // close model settings
    // await closeSettings();

    // // open secrets
    // await page.getByRole('button', { name: 'Secrets & API Keys' }).click();
    // await page
    //   .getByTestId('playground-secret-input-GROQ_API_KEY')
    //   .getByRole('textbox')
    //   .fill(GROQ_API_KEY);

    // // close secrets
    // await page.getByRole('button', { name: 'Secrets & API Keys' }).click();

    // // run again
    // await page.getByRole('button', { name: 'Start' }).click();

    // // check if the output contains the tool
    // await expect
    //   .soft(page.getByTestId(`tool_call_function_name_${toolName}`))
    //   .toBeVisible();

    // // Go back to the runs page
    // await page.getByTestId('playground-stats-link').click();
    // await expect.soft(page.getByTestId(`run-tree-node-ChatGroq`)).toBeVisible();
    // await page.getByTestId('run-tree-node-ChatGroq').click();
    // await page.locator('[data-testid="playground-button"]').click();

    // // Click the "Run" button
    // await page.getByRole('button', { name: 'Start' }).click();

    // // wait for 1 second to make sure that the tool from previously is gone
    // await page.waitForTimeout(1000);

    // // Check if there is a tool in the output
    // const toolCallElement = await page.getByTestId(
    //   `tool_call_function_name_${toolName}`
    // );

    // await expect(toolCallElement).toBeVisible();

    // // click on model settings
    // await openSettings();
    // await expect(temperatureInput).toHaveValue('0.1');

    // await closeSettings();

    await deleteApiKey(page);
  }
});
