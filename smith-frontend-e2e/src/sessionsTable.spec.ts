import { Page, expect, test } from '@playwright/test';
import { randomUUID } from 'crypto';
import { Client, Feedback } from 'langsmith';
import { createAndCopyApiKey, deleteApiKey } from './utils/apiKey';
import { BASE_URL, ENDPOINT_URL, OPENAI_API_KEY } from './utils/constants';
import { navigateToDatasetsAndDeleteDataset } from './utils/delete';
import { loginAndEnterInviteCode } from './utils/login';
import {
  CreateSupabaseUser,
  createSupabaseUser,
  deleteUser,
} from './utils/supabase';
import { waitForWriteQueue } from './utils/waitForWriteQueue';

const NUMBER_OF_SESSIONS = 11;
const NUMBER_OF_RUNS_PER_SESSION = 3;
const NUMBER_OF_SESSIONS_TO_CHECK_RUN_COUNT = 3;
const NUMBER_OF_SESSION_DETAILS_PAGES_TO_TEST = 2;
const formatter = new Intl.NumberFormat('en-US', { maximumFractionDigits: 2 });

let user: CreateSupabaseUser | null = null;
test.beforeAll(async () => {
  if (BASE_URL === 'https://eks.smith.langchain.dev') {
    console.debug('Skipping user creation');
    return;
  }
  user = await createSupabaseUser('sessionsTableTest');
  console.debug('Created user', user.id, user.email);
});

test.afterAll(async () => {
  if (user != null) {
    console.debug('Deleting user', user.id);
    await deleteUser(user.id);
  }
});

class SimpleSession {
  id: string;
  name: string;
  latencyValues: number[] = [];
  latency_p50: number = 0;
  latency_p99: number = 0;
  error_rate: number = 0;
  creation_time_index: number;
  average_feedback: number = 0;
  runs: Array<SimpleRun> = [];

  constructor(id: string, name: string, creation_time_index: number) {
    this.id = id;
    this.name = name;
    this.creation_time_index = creation_time_index;
  }

  addRun(run: SimpleRun) {
    const latency = run.end_time - run.start_time;
    this.latencyValues.push(latency);

    this.latency_p50 = quantile(0.5, this.latencyValues);
    this.latency_p99 = quantile(0.99, this.latencyValues);
    this.runs.push(run);

    const numErrors = this.runs.filter((run) => run.error).length;
    this.error_rate = numErrors / this.runs.length;
    const totalFeedback = this.runs.reduce(
      (acc, run) => acc + Math.round(run.feedback * 100),
      0
    );
    this.average_feedback = totalFeedback / this.runs.length / 100;
    this.average_feedback = Number(this.average_feedback.toFixed(5));
  }
}

class SimpleRun {
  start_time: number = 0;
  end_time: number = 0;
  error: boolean = false;
  feedback: number = 0;
  id: string;
  name: string;
  example_id: string;

  constructor(
    id: string,
    start_time: number,
    end_time: number,
    error: boolean,
    feedback: number,
    name: string,
    example_id: string
  ) {
    this.start_time = start_time;
    this.end_time = end_time;
    this.error = error;
    this.feedback = feedback;
    this.id = id;
    this.name = name;
    this.example_id = example_id;
  }

  getExampleDisplayText() {
    return `#${this.example_id.slice(0, 4)}`;
  }
}

function quantile(q: number, latencyValues: number[]) {
  const arrCopy = latencyValues.slice();
  const sorted = arrCopy.sort((a, b) => a - b);
  const pos = (sorted.length - 1) * q;
  const base = Math.floor(pos);
  const rest = pos - base;
  if (sorted[base + 1] !== undefined) {
    return sorted[base] + rest * (sorted[base + 1] - sorted[base]);
  } else {
    return sorted[base];
  }
}

test.fixme('Sessions table clickhouse features', async ({ page }) => {
  await loginAndEnterInviteCode(page, user);
  const apiKey = await createAndCopyApiKey(page);

  // create dataset
  await page.goto('/datasets');
  const datasetName = `${Math.floor(
    Math.random() * 100
  )}-test-dataset-${randomUUID()}`;
  await expect(page.getByText('There are no datasets.')).toBeVisible();
  await page.getByRole('button', { name: 'New Dataset' }).click();
  await page.getByRole('button', { name: 'Create empty dataset' }).click();
  await page.getByPlaceholder('Type name...').clear();
  await page.getByPlaceholder('Type name...').fill(datasetName);
  await page.getByRole('button', { name: 'Create' }).click();
  await expect
    .soft(page.getByRole('heading', { name: datasetName }))
    .toBeVisible();

  const client = new Client({ apiKey, apiUrl: ENDPOINT_URL });
  // Create the examples based on NUMBER_OF_RUNS_PER_SESSION (same number of examples as runs)
  const exampleIds: Array<string> = [];
  for (let i = 0; i < NUMBER_OF_RUNS_PER_SESSION; i++) {
    const exampleInput = (Math.random() + 1).toString(36).substring(7);
    const exampleOutput = (Math.random() + 1).toString(36).substring(7);
    const exampleId = await createExampleFromDatasetPage(
      page,
      exampleInput,
      exampleOutput,
      client
    );
    exampleIds.push(exampleId);
  }

  //get the dataset id:
  const pageUrl = await page.url();
  const datasetIdWithQueryParam = pageUrl.split('/').pop();
  const datasetId = datasetIdWithQueryParam?.split('?').shift();

  const sessions: Array<SimpleSession> = [];

  for (let i = 0; i < NUMBER_OF_SESSIONS; i++) {
    const sessionName = `Session ${i}${(Math.random() + 1)
      .toString(36)
      .substring(7)}`; // random string
    const newProj = await client.createProject({
      projectName: sessionName,
      referenceDatasetId: datasetId,
    });

    const session = new SimpleSession(newProj.id, sessionName, i);
    sessions.push(session);
    await page.waitForTimeout(500); // ensure that project 1 has an earlier creation time, etc
  }

  const currentSecond = Math.floor(new Date().getTime() / 1000);
  const runs: Array<SimpleRun> = [];
  const runCoroutines: Promise<void>[] = [];
  for (let i = 0; i < NUMBER_OF_SESSIONS * NUMBER_OF_RUNS_PER_SESSION; i++) {
    const id = randomUUID();
    const runName = `Run ${i}${(Math.random() + 1).toString(36).substring(7)}`;
    const end_time = Math.floor(Math.random() * 1000) + currentSecond;
    const sessionNumber = Math.floor(i / NUMBER_OF_RUNS_PER_SESSION);
    const countWithinSession = i % NUMBER_OF_RUNS_PER_SESSION;
    const hasError = Math.random() < 0.5;
    let feedbackToTwoDecimalPlaces = Math.round(Math.random() * 100) / 100;

    const simpleRun = new SimpleRun(
      id,
      currentSecond,
      end_time,
      hasError,
      feedbackToTwoDecimalPlaces,
      runName,
      exampleIds[countWithinSession]
    );
    runs.push(simpleRun);
    sessions[sessionNumber].addRun(simpleRun);

    runCoroutines.push(
      client.createRun({
        id,
        name: runName,
        inputs: { input: randomUUID() },
        outputs: { output: randomUUID() },
        run_type: 'chain',
        project_name: sessions[sessionNumber].name,
        start_time: currentSecond,
        end_time: end_time,
        reference_example_id: exampleIds[countWithinSession],
        error: hasError ? 'ERROR' : undefined,
      })
    );
  }
  await Promise.all(runCoroutines);

  for (
    let i = sessions.length - 1;
    i > 0;
    i -= Math.floor(sessions.length / NUMBER_OF_SESSIONS_TO_CHECK_RUN_COUNT)
  ) {
    const currSession = sessions[i];
    await page.goto('/projects/p/' + currSession.id);
    await waitForWriteQueue(page, [
      { rowName: datasetName, numRuns: NUMBER_OF_RUNS_PER_SESSION },
    ]);
    // Go back to the last page
    await page.goBack();
  }

  const coroutines: Promise<Feedback>[] = [];
  for (const currRun of runs) {
    const runId = currRun.id;
    coroutines.push(
      client.createFeedback(runId, 'Cool', {
        score: currRun.feedback,
        eager: true,
      })
    );
  }
  await Promise.all(coroutines);

  // because of async insert, we need to wait a bit before reloading the page
  await page.waitForTimeout(5000);
  await page.reload();

  await page.getByRole('tab', { name: 'Experiments', exact: true }).click();

  // First, sort the sessions by creation time, DESCENDING
  sessions.sort((a, b) => b.creation_time_index - a.creation_time_index);
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Next, sort the sessions by creation time, ASCENDING
  await page
    .locator('th')
    .getByText('Creation Time')
    .getByTestId('sort-arrow-down-icon')
    .click();
  sessions.sort((a, b) => a.creation_time_index - b.creation_time_index);
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by error rate, DESCENDING
  await page
    .locator('th')
    .getByText('Error Rate')
    .getByTestId('sort-arrows-up-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.error_rate === b.error_rate) return clickHouseUuidSort(b.id, a.id);
    return b.error_rate - a.error_rate;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by error rate, ASCENDING
  await page
    .locator('th')
    .getByText('Error Rate')
    .getByTestId('sort-arrow-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.error_rate === b.error_rate) return clickHouseUuidSort(a.id, b.id);
    return a.error_rate - b.error_rate;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by latency p50, DESCENDING
  await page
    .locator('th')
    .getByText('P50 Latency')
    .getByTestId('sort-arrows-up-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.latency_p50 === b.latency_p50) return clickHouseUuidSort(b.id, a.id);
    return b.latency_p50 - a.latency_p50;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by latency p50, ASCENDING
  await page
    .locator('th')
    .getByText('P50 Latency')
    .getByTestId('sort-arrow-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.latency_p50 === b.latency_p50) return clickHouseUuidSort(a.id, b.id);
    return a.latency_p50 - b.latency_p50;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by latency p99, DESCENDING
  await page
    .locator('th')
    .getByText('P99 Latency')
    .getByTestId('sort-arrows-up-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.latency_p99 === b.latency_p99) return clickHouseUuidSort(b.id, a.id);
    return b.latency_p99 - a.latency_p99;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by latency p99, ASCENDING
  await page
    .locator('th')
    .getByText('P99 Latency')
    .getByTestId('sort-arrow-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.latency_p99 === b.latency_p99) return clickHouseUuidSort(a.id, b.id);
    return a.latency_p99 - b.latency_p99;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by "Cool" feedback score, DESCENDING
  await page
    .locator('th', {
      has: page.locator('[data-testid="sessions-table-header-feedback-cool"]'),
    })
    .getByTestId('sort-arrows-up-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.average_feedback === b.average_feedback)
      return clickHouseUuidSort(b.id, a.id);
    return b.average_feedback - a.average_feedback;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  // Sort by "Cool" feedback score, ASCENDING
  await page
    .locator('th', {
      has: page.locator('[data-testid="sessions-table-header-feedback-cool"]'),
    })
    .getByTestId('sort-arrow-down-icon')
    .click();
  sessions.sort((a, b) => {
    if (a.average_feedback === b.average_feedback)
      return clickHouseUuidSort(a.id, b.id);
    return a.average_feedback - b.average_feedback;
  });
  await checkOrderOfSessionTableAndNextPage(page, sessions);

  sessions.sort((a, b) => b.creation_time_index - a.creation_time_index);

  // Since we have all these great sessions with runs set up, let's also test the session details page
  let idx = 0;
  for (const currSession of sessions) {
    if (idx >= NUMBER_OF_SESSION_DETAILS_PAGES_TO_TEST) break; // unnecessary to check all the sessions, just takes forever
    await page.goto('/datasets/' + datasetId);
    await page.getByRole('row', { name: currSession.name }).click();
    await page.waitForLoadState();
    await expect(page.getByRole('progressbar')).toHaveCount(0);
    await page.getByRole('link', { name: currSession.name }).click();
    await expect
      .soft(page.getByTestId('project-stats-run-count'))
      .toHaveText(`${NUMBER_OF_RUNS_PER_SESSION}`);
    await expect
      .soft(page.getByTestId('project-stats-error-rate'))
      .toHaveText(`${Math.round(currSession.error_rate * 100)}%`);
    await expect
      .soft(page.getByTestId('project-stats-latency-p50'))
      .toHaveText(`P50: ${currSession.latency_p50.toFixed(2)}s`);
    await expect
      .soft(page.getByTestId('project-stats-latency-p99'))
      .toHaveText(`P99: ${currSession.latency_p99.toFixed(2)}s`);
    await expect
      .soft(page.getByTestId('project-stats-feedback-chip-cool'))
      .toContainText(`${formatter.format(currSession.average_feedback)}`);
    for (const run of currSession.runs) {
      await expect(
        page.getByRole('row', { name: run.getExampleDisplayText() })
      ).toBeVisible();
      await expect(
        page
          .getByRole('row', { name: run.getExampleDisplayText() })
          .getByTestId('runs-table-feedback-chip-cool')
      ).toContainText(`${formatter.format(run.feedback)}`);
      if (run.error) {
        await expect(
          page
            .getByRole('row', { name: run.getExampleDisplayText() })
            .getByTestId('run-status-icon-error')
        ).toBeVisible();
      } else {
        await expect(
          page
            .getByRole('row', { name: run.getExampleDisplayText() })
            .getByTestId('run-status-icon-success')
        ).toBeVisible();
      }
      await expect(
        page
          .getByRole('row', { name: run.getExampleDisplayText() })
          .getByTestId('runs-table-latency-chip')
      ).toContainText(`${(run.end_time - run.start_time).toFixed(2)}s`);
    }

    // Test filtering
    const runsCopy = currSession.runs.slice();
    // Sort by latency
    runsCopy.sort(
      (a, b) => b.end_time - b.start_time - (a.end_time - a.start_time)
    );
    const middleRunByLatency = runsCopy[Math.floor(runsCopy.length / 2)];
    const middleRunLatency = Math.floor(
      middleRunByLatency.end_time - middleRunByLatency.start_time
    );

    const runsWithSmallerLatency = runsCopy.filter(
      (run) => run.end_time - run.start_time < middleRunLatency
    );
    const runsWithLargerLatency = runsCopy.filter(
      (run) => run.end_time - run.start_time >= middleRunLatency
    );

    await page.getByRole('button', { name: '1 filter' }).click();
    await page.getByText('API Key', { exact: true }).click();

    const secretInputRef = page
      .getByTestId('evaluator-secret-input-OPENAI_API_KEY')
      .getByRole('textbox');

    await secretInputRef.fill(OPENAI_API_KEY);
    await secretInputRef.press('Enter');
    await expect(secretInputRef).not.toBeAttached();

    await page
      .getByPlaceholder('eg. runs longer than 2s')
      .fill(`gte(latency, "${middleRunLatency}s")`);
    await page.getByPlaceholder('eg. runs longer than 2s').press('Enter');
    await page.getByRole('button', { name: '2 filter' }).click();

    await checkProjectStats(
      page,
      runsWithLargerLatency,
      runsWithSmallerLatency
    );

    // Sort by feedback
    runsCopy.sort((a, b) => b.feedback - a.feedback);
    const middleRunByFeedback = runsCopy[Math.floor(runsCopy.length / 2)];
    const middleRunFeedback =
      Math.round(middleRunByFeedback.feedback * 100) / 100; // round to two decimal places

    const runsWithSmallerFeedback = runsCopy.filter(
      (run) => run.feedback < middleRunFeedback
    );
    const runsWithLargerFeedback = runsCopy.filter(
      (run) => run.feedback >= middleRunFeedback
    );

    await page.getByRole('button', { name: '2 filters' }).click();
    await page.getByTestId('remove-filter-button').nth(1).click();
    await page.getByTestId('remove-filter-button').first().click();
    await page.getByRole('button', { name: 'Add filter' }).click();
    await page.getByRole('menuitem', { name: 'Feedback Score' }).click();
    await page.getByRole('option', { name: 'cool' }).click();
    await page.getByRole('button', { name: 'is' }).nth(1).click();
    await page.getByRole('menuitem', { name: '>=' }).click();
    await page.getByPlaceholder('Score').fill(middleRunFeedback.toFixed(2));
    await page.getByPlaceholder('Score').press('Enter');
    await page.getByRole('button', { name: '1 filter' }).click();

    await checkProjectStats(
      page,
      runsWithLargerFeedback,
      runsWithSmallerFeedback
    );

    const runsWithError = runsCopy.filter((run) => run.error);
    const runsWithoutError = runsCopy.filter((run) => !run.error);

    await page.getByRole('button', { name: '1 filter' }).click();
    await page.getByTestId('remove-filter-button').click();
    await page.getByRole('button', { name: 'Advanced filters' }).click();
    await page
      .getByPlaceholder('eg. eq(status, "error")')
      .fill(`eq(status, "success")`);
    await page.getByPlaceholder('eg. eq(status, "error")').press('Enter');
    await page.getByRole('button', { name: '1 filter' }).click();

    await checkProjectStats(page, runsWithoutError, runsWithError);

    idx++;
  }

  // Delete projects
  sessions.forEach(async (session) => {
    await client.deleteProject({ projectId: session.id });
  });

  // Delete dataset
  await navigateToDatasetsAndDeleteDataset(page, datasetName, true);

  await deleteApiKey(page);
});

const checkOrderOfSessionTableAndNextPage = async (
  page: Page,
  sessions: SimpleSession[]
) => {
  for (let i = 0; i < 10 && i < sessions.length; i++) {
    //First page
    const session = sessions[i];
    await expect(page.getByRole('row').nth(i + 1)).toContainText(session.name); // 0-indexed but the header counts as a row
    await expect(
      page
        .getByRole('row')
        .nth(i + 1)
        .getByTestId('latency-p50-chip')
    ).toHaveText(session.latency_p50.toFixed(2) + 's');
    await expect(
      page
        .getByRole('row')
        .nth(i + 1)
        .getByTestId('latency-p99-chip')
    ).toHaveText(session.latency_p99.toFixed(2) + 's');
    await expect(
      page
        .getByRole('row')
        .nth(i + 1)
        .getByTestId('run-count-chip')
    ).toHaveText(session.runs.length.toString());
    try {
      await expect(
        page
          .getByRole('row')
          .nth(i + 1)
          .getByTestId('feedback-stats-cool-chip')
      ).toHaveText(session.average_feedback.toFixed(2));
    } catch (error) {
      throw new Error(
        `Incorrect feedback score for session ${
          session.name
        } which has feedbacks ${session.runs
          .map((run) => run.feedback)
          .join(', ')}. Expected ${session.average_feedback.toFixed(
          2
        )} based on an avg of ${session.average_feedback}`
      );
    }
    await expect(
      page
        .getByRole('row')
        .nth(i + 1)
        .getByTestId('error-rate-chip')
    ).toHaveText(`${Math.round(session.error_rate * 100)}%`);
  }

  if (NUMBER_OF_SESSIONS > 10) {
    await page.getByTestId('next-page-button').click();
    for (let i = 10; i < 20 && i < sessions.length; i++) {
      //Second page
      const session = sessions[i];
      await expect(page.getByRole('row').nth(i - 9)).toContainText(
        session.name
      ); // 0-indexed but the header counts as a row
      await expect(
        page
          .getByRole('row')
          .nth(i - 9)
          .getByTestId('latency-p50-chip')
      ).toHaveText(session.latency_p50.toFixed(2) + 's');
      await expect(
        page
          .getByRole('row')
          .nth(i - 9)
          .getByTestId('latency-p99-chip')
      ).toHaveText(session.latency_p99.toFixed(2) + 's');
      await expect(
        page
          .getByRole('row')
          .nth(i - 9)
          .getByTestId('run-count-chip')
      ).toHaveText(session.runs.length.toString());
      try {
        await expect(
          page
            .getByRole('row')
            .nth(i - 9)
            .getByTestId('feedback-stats-cool-chip')
        ).toHaveText(session.average_feedback.toFixed(2));
      } catch (error) {
        throw new Error(
          `Incorrect feedback score for session ${
            session.name
          } which has feedbacks ${session.runs
            .map((run) => run.feedback)
            .join(', ')}. Expected ${session.average_feedback.toFixed(
            2
          )} based on an avg of ${session.average_feedback}`
        );
      }
      await expect(
        page
          .getByRole('row')
          .nth(i - 9)
          .getByTestId('error-rate-chip')
      ).toHaveText(`${Math.round(session.error_rate * 100)}%`);
    }
    await page.getByTestId('previous-page-button').click();
  }
};

// Clickhouse sorts UUIDs weird. See https://michcioperz.com/wiki/clickhouse-uuid-ordering/
function clickHouseUuidSort(a, b) {
  // Split the UUIDs into parts A and B
  let partsA = a.split('-');
  let partsB = b.split('-');

  // Construct group B (the last three parts of the UUID)
  let groupB_A = partsA[3] + partsA[4];
  let groupB_B = partsB[3] + partsB[4];

  // Compare group B first
  if (groupB_A < groupB_B) return -1;
  if (groupB_A > groupB_B) return 1;

  // If group B is equal, construct group A (the first three parts of the UUID)
  let groupA_A = partsA[0] + partsA[1] + partsA[2];
  let groupA_B = partsB[0] + partsB[1] + partsB[2];

  // Compare group A
  if (groupA_A < groupA_B) return -1;
  if (groupA_A > groupA_B) return 1;

  // If both groups are equal, return 0
  return 0;
}

// Assuming we are already on the dataset page, create an example
async function createExampleFromDatasetPage(
  page: Page,
  exampleInput: string,
  exampleOutput: string,
  client: Client
): Promise<string> {
  let pageUrl = page.url();
  let match = pageUrl.match(/\/datasets\/([^\/?]*)/);
  expect(match).not.toBeNull();
  const datasetId = match![1];
  const example = await client.createExample(
    { input: exampleInput },
    { output: exampleOutput },
    {
      datasetId,
    }
  );

  return example.id;
}

// Check project stats given a list of included and excluded runs
async function checkProjectStats(
  page: Page,
  includedRuns: Array<SimpleRun>,
  excludedRuns: Array<SimpleRun>
) {
  for (const run of excludedRuns) {
    await expect(
      page.getByRole('row', { name: run.getExampleDisplayText() })
    ).not.toBeVisible();
  }
  for (const run of includedRuns) {
    await expect(
      page.getByRole('row', { name: run.getExampleDisplayText() })
    ).toBeVisible();
  }
  if (includedRuns.length > 0) {
    // occasionally there will be no runs, for example if they all error
    const p50LatencyOfDisplayedRuns = quantile(
      0.5,
      includedRuns.map((run) => run.end_time - run.start_time)
    );
    await expect
      .soft(page.getByTestId('project-stats-latency-p50'))
      .toHaveText(`P50: ${p50LatencyOfDisplayedRuns.toFixed(2)}s`);
    const p99LatencyOfDisplayedRuns = quantile(
      0.99,
      includedRuns.map((run) => run.end_time - run.start_time)
    );
    await expect
      .soft(page.getByTestId('project-stats-latency-p99'))
      .toHaveText(`P99: ${p99LatencyOfDisplayedRuns.toFixed(2)}s`);
    const errorRateOfDisplayedRuns =
      includedRuns.filter((run) => run.error).length / includedRuns.length;
    await expect
      .soft(page.getByTestId('project-stats-error-rate'))
      .toHaveText(`${Math.round(errorRateOfDisplayedRuns * 100)}%`);

    const totalFeedback = includedRuns.reduce(
      (acc, run) => acc + Math.round(run.feedback * 100),
      0
    );
    const averageFeedbackOfDisplayedRuns =
      totalFeedback / includedRuns.length / 100;
    await expect
      .soft(page.getByTestId('project-stats-feedback-chip-cool'))
      .toContainText(`${formatter.format(averageFeedbackOfDisplayedRuns)}`);
    await expect
      .soft(page.getByTestId('project-stats-run-count'))
      .toHaveText(`${includedRuns.length}`);
  } else {
    await expect
      .soft(page.getByTestId('project-stats-run-count'))
      .toHaveText(`0`);
  }
}
