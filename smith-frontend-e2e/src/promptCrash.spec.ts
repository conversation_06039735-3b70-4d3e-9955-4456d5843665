import { expect, test } from '@playwright/test';
import { BASE_URL, ENDPOINT_URL } from './utils/constants';
import { loginAndEnterInviteCode } from './utils/login';
import { createAndCopyApiKey } from './utils/apiKey';
import { Client } from 'langsmith';
import { CreateSupabaseUser } from './utils/supabase';
import { createSupabaseUser, deleteUser } from './utils/supabase';
import { allPromptCrashManifests } from './constants/promptCrash';

let user: CreateSupabaseUser | null = null;
let client: Client | null = null;
let sharedPage: any = null;
let sharedContext: any = null;

test.beforeAll(async ({ browser }) => {
  if (BASE_URL === 'https://eks.smith.langchain.dev') {
    console.debug('Skipping user creation');
    return;
  }
  
  // Create a new context and page manually and keep them alive
  sharedContext = await browser.newContext();
  sharedPage = await sharedContext.newPage();
  
  user = await createSupabaseUser('playground-crash');
  console.debug('Created user', user.id);

  await loginAndEnterInviteCode(sharedPage, user);
  const apiKey = await createAndCopyApiKey(sharedPage);
  client = new Client({ apiKey, apiUrl: ENDPOINT_URL });
});

test.afterAll(async () => {
  if (user != null) {
    console.debug('Deleting user', user.id);
    await deleteUser(user.id);
  }
  
  // Close the shared context and page after all tests are done
  if (sharedContext) {
    await sharedContext.close();
  }
});

for (const promptCrashManifest of allPromptCrashManifests) {
  const { name, description, manifest } = promptCrashManifest;
  test.describe(name, () => {
    test(`${description}`, async () => {
      // Use the shared page that's already logged in
      const page = sharedPage;

      await client?.pushPrompt(name, {
        object: manifest,
      });

      // Go to the playground
      await page.goto('/prompts');

      // Click on the prompt that was just created
      await page.getByText(name).click();

      // click on the commits tab
      await page.getByRole('tab', { name: 'Commits' }).click();

      // click on the 'edit in playground' button
      await page.getByRole('button', { name: 'Edit in playground' }).click();

      // Click on the model config button
      await page.getByTestId('prompt-settings-button').click();

      // Click on the save as button to engage in an action in this modal
      await page.getByRole('button', { name: 'Save as' }).click();

      await expect(
        page.getByText('New configuration', { exact: true })
      ).toBeVisible();

      // Click escape to close the save modal, click escape again to close the settings modal
      await page.keyboard.press('Escape');
      await page.keyboard.press('Escape');
    });
  });
}
