import { expect, test } from '@playwright/test';
import { randomUUID } from 'crypto';
import { Client } from 'langsmith';
import { createAndCopyApiKey, deleteApiKey } from './utils/apiKey';
import { BASE_URL, ENDPOINT_URL } from './utils/constants';
import {
  navigateToDatasetsAndDeleteDataset,
  navigateToProjectsAndDeleteProject,
} from './utils/delete';
import { loginAndEnterInviteCode } from './utils/login';
import {
  CreateSupabaseUser,
  createSupabaseUser,
  deleteUser,
} from './utils/supabase';
import { waitForWriteQueue } from './utils/waitForWriteQueue';

let user: CreateSupabaseUser | null = null;
test.beforeAll(async () => {
  if (BASE_URL === 'https://eks.smith.langchain.dev') {
    console.debug('Skipping user creation');
    return;
  }
  user = await createSupabaseUser('publicDatasetsTest');
  console.debug('Created user', user.id);
});

test.afterAll(async () => {
  if (user != null) {
    console.debug('Deleting user', user.id);
    await deleteUser(user.id);
  }
});

test('Test Public Dataset Sharing ', async ({ page, context }) => {
  await loginAndEnterInviteCode(page, user);
  const apiKey = await createAndCopyApiKey(page);

  // create dataset
  await page.goto('/datasets');
  const datasetName = `${Math.floor(
    Math.random() * 100
  )}-test-dataset-${randomUUID()}`;
  await expect(page.getByText('There are no datasets.')).toBeVisible();
  await page.getByRole('button', { name: 'New Dataset' }).click();
  await page.getByRole('button', { name: 'Create empty dataset' }).click();
  await page.getByPlaceholder('Type name...').clear();
  await page.getByPlaceholder('Type name...').fill(datasetName);
  await page.getByRole('button', { name: 'Create' }).click();
  await expect
    .soft(page.getByRole('heading', { name: datasetName }))
    .toBeVisible();

  await page.getByRole('button', { name: 'Example' }).click();

  const example1Input = 'ASDFEXAMPLE1INPUT';
  const example1Output = 'ASDFEXAMPLE1OUTPUT';
  await page.getByTestId('example-crud-inputs').getByRole('textbox').clear();
  await page
    .getByTestId('example-crud-inputs')
    .getByRole('textbox')
    .fill(`{ "input": "${example1Input}"}`);
  await page.getByTestId('run-details-output').getByRole('textbox').clear();
  await page
    .getByTestId('run-details-output')
    .getByRole('textbox')
    .fill(`{ "output": "${example1Output}"}`);
  await page.waitForTimeout(400);
  await page.getByRole('button', { name: 'Submit' }).click();
  await expect
    .soft(page.getByText('Add Example to Key-Value Dataset'))
    .not.toBeVisible();
  await page
    .getByRole('row', { name: example1Input })
    .click({ position: { x: 100, y: 10 } });
  await expect.soft(page.getByTestId('split-view-pane')).toBeVisible();
  await expect(
    page
      .getByTestId('run-details-input')
      .getByTestId('foreign-data-tree-leaf-input')
  ).toContainText(example1Input);

  // get the example id:
  let pageUrl = await page.url();
  const exampleId = pageUrl.split('=').pop();

  await page.getByTestId('split-view-pane-close-button').click();

  const example2Input = 'QWERTYEXAMPLE2INPUT';
  const example2Output = 'QWERTYEXAMPLE2OUTPUT';
  await page.getByRole('button', { name: 'Example' }).click();
  await page.getByRole('option', { name: 'Add Example' }).click();
  await page.getByTestId('example-crud-inputs').getByRole('textbox').clear();
  await page
    .getByTestId('example-crud-inputs')
    .getByRole('textbox')
    .fill(`{ "input": "${example2Input}"}`);
  await page.getByTestId('run-details-output').getByRole('textbox').clear();
  await page
    .getByTestId('run-details-output')
    .getByRole('textbox')
    .fill(`{ "output": "${example2Output}"}`);
  await page.waitForTimeout(400);
  await page.getByRole('button', { name: 'Submit' }).click();
  await expect
    .soft(page.getByText('Add Example to Key-Value Dataset'))
    .not.toBeVisible();
  await page
    .getByRole('row', { name: example2Input })
    .click({ position: { x: 100, y: 10 } });
  await expect.soft(page.getByTestId('split-view-pane')).toBeVisible();
  await expect(
    page
      .getByTestId('run-details-input')
      .getByTestId('foreign-data-tree-leaf-input')
  ).toContainText(example2Input);

  // get the example id:
  pageUrl = await page.url();
  const example2Id = pageUrl.split('=').pop();

  await page.getByTestId('split-view-pane-close-button').click();

  //get the dataset id:
  pageUrl = await page.url();
  const datasetIdWithQueryParam = pageUrl.split('/').pop();
  const datasetId = datasetIdWithQueryParam?.split('?').shift();

  // Create a test run and log some runs
  const runName = 'Example Run';
  const childRunName = 'Child Run';
  const runOutput2 = 'THIS IS A RUN OUTPUT';
  const runOutput1 = 'ANOTHA OUTPUT';
  const runOutput3 = 'WE GONNA HAVE A THIRD OUTPUT';
  const runOutput4 = 'AND A FOURTH!! WOO!';
  const projectName = `PROJECT ${randomUUID()}`;
  const projectName2 = `PROJECT #2 ${randomUUID()}`;
  const childRunInput = 'something else';
  const childRunOutput = 'another other thing';

  const client = new Client({ apiKey, apiUrl: ENDPOINT_URL });
  const project = await client.createProject({
    projectName: projectName,
    referenceDatasetId: datasetId,
  });
  const project2 = await client.createProject({
    projectName: projectName2,
    referenceDatasetId: datasetId,
  });

  const runId1 = randomUUID();
  const runId2 = randomUUID();
  const runId3 = randomUUID();
  const runId4 = randomUUID();
  const runId5 = randomUUID();
  await client.createRun({
    id: runId1,
    name: runName,
    inputs: { input: example1Input },
    outputs: { output: runOutput1 },
    run_type: 'chain',
    project_name: project2.name,
    end_time: new Date().getTime() / 1000,
    reference_example_id: exampleId,
  });
  await client.createRun({
    id: runId5,
    parent_run_id: runId1,
    name: childRunName,
    inputs: { input: childRunInput },
    outputs: { output: childRunOutput },
    run_type: 'chain',
    project_name: project2.name,
    end_time: new Date().getTime() / 1000,
    reference_example_id: exampleId,
  });
  await client.createRun({
    id: runId3,
    name: runName,
    inputs: { input: example2Input },
    outputs: { output: runOutput3 },
    run_type: 'chain',
    project_name: project2.name,
    end_time: new Date().getTime() / 1000,
    reference_example_id: example2Id,
  });
  await client.createRun({
    id: runId2,
    name: runName,
    inputs: { input: example1Input },
    outputs: { output: runOutput2 },
    run_type: 'chain',
    project_name: project.name,
    end_time: new Date().getTime() / 1000,
    reference_example_id: exampleId,
  });
  await client.createRun({
    id: runId4,
    name: runName,
    inputs: { input: example2Input },
    outputs: { output: runOutput4 },
    run_type: 'chain',
    project_name: project.name,
    end_time: new Date().getTime() / 1000 + 30, // Give it a latency of 30 seconds
    reference_example_id: example2Id,
  });

  //Go to the project page and wait for the run to be submitted -
  // doesn't auto-refresh on the comparison view so we need to go to the project page to wait for the write queue
  await page.goto('/projects/p/' + project.id);
  await waitForWriteQueue(page, [{ rowName: datasetName, numRuns: 2 }]);
  //Go back to the last page
  await page.goBack();

  await page.goto('/projects/p/' + project2.id);
  await waitForWriteQueue(page, [{ rowName: datasetName, numRuns: 2 }]);
  //Go back to the last page
  await page.goBack();

  await client.createFeedback(runId1, 'Cool', { score: 1.0, eager: true });
  await client.createFeedback(runId2, 'Cool', { score: 0.0, eager: true });
  await client.createFeedback(runId3, 'Cool', { score: 0.0, eager: true });
  await client.createFeedback(runId4, 'Cool', { score: 1.0, eager: true });

  // Share the dataset publicly
  await page.getByRole('main').getByLabel('More').click();
  await page.getByRole('option', { name: 'Share Dataset' }).click();
  await page.getByTestId('copy-link-button').click();
  await expect(page.getByRole('button', { name: 'Copied' })).toBeVisible();

  // Access the clipboard
  const clipboardText = await page.evaluate('navigator.clipboard.readText()');
  const privateDatasetUrl = await page.url();
  await page.goto(clipboardText as string);

  await page.getByRole('tab', { name: 'Examples' }).click();

  // Test linked runs section
  await page.getByText(example1Input).click({ position: { x: 100, y: 10 } });

  await expect(
    page
      .getByTestId('run-details-input')
      .getByTestId('foreign-data-tree-leaf-input')
  ).toContainText(example1Input);
  await expect(
    page
      .getByTestId('run-details-output')
      .getByTestId('foreign-data-tree-leaf-output')
  ).toContainText(example1Output);
  await page.getByRole('tab', { name: 'Linked Runs' }).click();
  await expect.soft(page.getByRole('row', { name: runOutput1 })).toBeVisible();
  await expect.soft(page.getByRole('row', { name: runOutput2 })).toBeVisible();
  await expect
    .soft(page.getByRole('row', { name: runOutput3 }))
    .not.toBeVisible();
  await expect
    .soft(page.getByRole('row', { name: runOutput4 }))
    .not.toBeVisible();
  await page.getByText(runOutput2).click();
  await expect.soft(page.getByTestId('split-view-pane')).toBeVisible();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${example1Input}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(runOutput2);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();
  await page.getByTestId('split-view-pane-close-button').click();
  await page.getByText(runOutput1).click();
  await expect.soft(page.getByTestId('split-view-pane')).toBeVisible();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${example1Input}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(runOutput1);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();
  await page.getByTestId(`run-tree-node-${childRunName}`).click();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${childRunInput}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(childRunOutput);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();
  await page.getByTestId(`run-tree-node-${runName}`).click();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${example1Input}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(runOutput1);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();

  //Test the run page as well, as opposed to just the split pane
  await page.getByTestId('split-view-pane-expand-button').click();
  await expect.soft(page.getByTestId('split-view-pane')).not.toBeVisible();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${example1Input}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(runOutput1);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();
  await page.getByTestId(`run-tree-node-${childRunName}`).click();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${childRunInput}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(childRunOutput);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();
  await page.getByTestId(`run-tree-node-${runName}`).click();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${example1Input}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(runOutput1);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();

  //Test breadcrumb
  await page.getByTestId(`run-tree-node-${childRunName}`).click();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${childRunInput}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(childRunOutput);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();
  await page.getByTestId(`breadcrumb-${runName}`).click();
  await expect
    .soft(
      page.locator(
        `[data-testid="run-input-component-kv"]:has-text("${example1Input}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page
        .getByTestId('run-details-output')
        .getByTestId('foreign-data-tree-leaf-output')
    )
    .toContainText(runOutput1);
  await expect
    .soft(
      page.locator(
        `[data-testid="reference-output-component"]:has-text("${example1Output}")`
      )
    )
    .toBeVisible();
  await expect(page.getByTestId(`breadcrumb-${datasetName}`)).toBeVisible();
  const href = await page
    .getByTestId(`breadcrumb-${datasetName}`)
    .getAttribute('href');
  await page.goto(href ?? '/');
  await page.goto(pageUrl);
  await page.getByRole('tab', { name: 'Experiments', exact: true }).click();
  await expect.soft(page.getByRole('row', { name: projectName })).toBeVisible();

  // In comparison view:
  await page.getByRole('row', { name: projectName }).click();
  await expect
    .soft(page.getByRole('button', { name: 'Compare' }))
    .toBeVisible();
  await expect
    .soft(page.locator('h2').filter({ hasText: projectName }))
    .toBeVisible();
  await expect
    .soft(
      page.getByTestId('truncatedOutputElement').filter({ hasText: runOutput2 })
    )
    .toBeVisible();

  await expect
    .soft(page.locator(`input[value="${projectName2}"]`))
    .not.toBeVisible();
  await page.getByRole('button', { name: 'Compare' }).click();
  await page.getByRole('option', { name: projectName2 }).click();
  await page.keyboard.press('Escape');
  await expect(
    page.getByRole('heading', { name: 'Comparing 2 Experiments' })
  ).toBeVisible();
  await expect
    .soft(page.locator(`input[value="${projectName2}"]`))
    .toBeVisible();

  //test comparison view filtering
  await expect
    .soft(
      page.getByTestId('truncatedOutputElement').filter({ hasText: runOutput2 })
    )
    .toBeVisible();
  await page
    .getByRole('cell', { name: projectName })
    .getByTestId('comparison-view-filter-button')
    .click();
  await expect.soft(page.getByRole('progressbar')).toHaveCount(0);
  await page.getByText('Latency < 10s').click();
  await page
    .getByRole('cell', { name: projectName })
    .getByTestId('comparison-view-filter-button')
    .click();
  await expect.soft(page.getByText('Latency < 10s')).not.toBeVisible();
  await expect
    .soft(
      page.getByTestId('truncatedOutputElement').filter({ hasText: runOutput2 })
    )
    .toBeVisible();
  await expect
    .soft(
      page.getByTestId('truncatedOutputElement').filter({ hasText: runOutput4 })
    )
    .toBeVisible();

  await page.getByText(example1Output, { exact: true }).hover();
  await page
    .getByRole('cell', { name: example1Output })
    .getByLabel('Expand detailed view')
    .click();
  await expect.soft(page.getByTestId('split-view-pane')).toBeVisible();
  await expect
    .soft(
      page.locator(
        `[data-testid="output-detail-code-element-kv"]:has-text("${runOutput2}")`
      )
    )
    .toBeVisible();
  await page.getByTestId('split-view-pane-close-button').click();

  await page
    .getByRole('cell', { name: projectName })
    .getByTestId('comparison-view-filter-button')
    .click();
  await page.getByText('Latency < 10s').click();
  await page.getByText('Add filter').click();
  const filterInput = page.getByPlaceholder('Enter filter value');
  await filterInput.click();
  await filterInput.fill('>0');
  await filterInput.press('Enter');
  await page
    .getByRole('cell', { name: projectName })
    .getByTestId('comparison-view-filter-button')
    .click();
  await expect.soft(page.getByText('Add filter')).not.toBeVisible();
  await expect
    .soft(
      page.getByTestId('truncatedOutputElement').filter({ hasText: runOutput4 })
    )
    .toBeVisible();
  await expect
    .soft(
      page.getByTestId('truncatedOutputElement').filter({ hasText: runOutput2 })
    )
    .toBeVisible();

  await page
    .getByTestId('truncatedOutputElement')
    .filter({ hasText: runOutput4 })
    .hover();
  await page
    .getByRole('cell', { name: runOutput4 })
    .getByLabel('Expand detailed view')
    .click();
  await expect.soft(page.getByTestId('split-view-pane')).toBeVisible();
  await expect
    .soft(
      page.locator(
        `[data-testid="output-detail-code-element-kv"]:has-text("${runOutput4}")`
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        `[data-testid="output-detail-code-element-kv"]:has-text("${runOutput3}")`
      )
    )
    .toBeVisible();
  await page.getByTestId('split-view-pane-close-button').click();

  await page.goto(privateDatasetUrl);
  await page.getByTestId('dataset-more-button').click();
  await page.getByRole('option', { name: 'Public Dataset' }).click();
  await page.getByRole('button', { name: 'Unshare' }).click();
  await page.getByRole('option', { name: 'Share Dataset' }).click();
  await page.waitForTimeout(1000);

  //Make sure the unshare worked
  await page.goto(clipboardText as string);
  await page.waitForLoadState();
  await expect(page.getByLabel('Annotation Queues')).toBeVisible(); //Currently don't show a 404, so just wait until the page reloads
  await expect.soft(page.getByRole('progressbar')).toHaveCount(0);
  await expect.soft(page.getByTestId('dataset-more-button')).not.toBeVisible();
  await expect(
    page.getByRole('tab', { name: 'Experiments', exact: true })
  ).not.toBeVisible();
  await expect(page.getByText(datasetName)).not.toBeVisible();

  // Go back to private page to delete the dataset
  await page.goto(privateDatasetUrl);
  await page.getByRole('tab', { name: 'Experiments', exact: true }).click();

  // Delete projects
  await page.reload();
  await navigateToProjectsAndDeleteProject(
    page,
    projectName,
    false,
    projectName2
  );
  pageUrl = page.url();
  const urlWithoutQueryParams = pageUrl.split('?')[0];
  await page.goto(urlWithoutQueryParams);
  await navigateToProjectsAndDeleteProject(
    page,
    projectName2,
    false,
    projectName
  );

  // Delete dataset
  await navigateToDatasetsAndDeleteDataset(page, datasetName, true);

  await deleteApiKey(page);
});
