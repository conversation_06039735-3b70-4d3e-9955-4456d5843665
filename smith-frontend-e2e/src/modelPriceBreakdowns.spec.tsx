import { expect, test } from '@playwright/test';
import { BASE_URL } from './utils/constants';
import { loginAndEnterInviteCode } from './utils/login';
import {
  CreateSupabaseUser,
  createSupabaseUser,
  deleteUser,
} from './utils/supabase';

let user: CreateSupabaseUser | null = null;
test.beforeAll(async () => {
  if (BASE_URL === 'https://eks.smith.langchain.dev') {
    console.debug('Skipping user creation');
    return;
  }
  user = await createSupabaseUser('model-price-breakdowns-test');
  console.debug('Created user', user.id);
});

test.afterAll(async () => {
  if (user != null) {
    console.debug('Deleting user', user.id);
    await deleteUser(user.id);
  }
});

test('model price breakdowns', async ({ page }) => {
  await loginAndEnterInviteCode(page, user);

  await page.goto('/');

  await page.getByRole('link', { name: 'Settings', exact: true }).click();
  await page.getByRole('link', { name: 'Models' }).click();
  await page.getByRole('button', { name: 'Model', exact: true }).click();
  await page.getByRole('textbox', { name: 'Model Name' }).click();
  await page.getByRole('textbox', { name: 'Model Name' }).fill('my_model');
  await page
    .getByRole('textbox', { name: 'Prompt Price / 1M Tokens' })
    .fill('1');
  await page.getByRole('button', { name: '+ Add Breakdown' }).first().click();
  await page.getByRole('textbox', { name: 'text' }).fill('audio');
  await page.getByRole('textbox', { name: '0.00' }).fill('2');
  await page
    .getByRole('textbox', { name: 'Completion Price / 1M Tokens' })
    .fill('1.5');
  await page.getByRole('button', { name: '+ Add Breakdown' }).click();
  await page
    .getByRole('row', { name: '$ 0' })
    .getByPlaceholder('text')
    .fill('photos');
  await page
    .getByRole('row', { name: 'photos $' })
    .getByPlaceholder('0.00')
    .fill('3');
  await page.getByRole('button', { name: '+ Token Type' }).nth(1).click();
  await page
    .getByRole('row', { name: '$ 0' })
    .getByPlaceholder('text')
    .fill('video');
  await page
    .getByRole('row', { name: 'video $' })
    .getByPlaceholder('0.00')
    .fill('10');
  await page.getByRole('button', { name: 'Create' }).click();

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.50' })
    .getByRole('img')
    .first()
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.00")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("audio"):has-text("$2.00")')
  ).toBeVisible();
  //unhover the tooltip
  await page.mouse.move(0, 0);

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.50' })
    .getByRole('img')
    .nth(1)
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.50")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("photos"):has-text("$3.00")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("video"):has-text("$10.00")')
  ).toBeVisible();

  await page.getByRole('button', { name: 'Edit' }).click();
  await page.getByRole('button', { name: 'Delete breakdown' }).first().click();
  await page.getByRole('button', { name: 'Update' }).click();

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.50' })
    .getByRole('img')
    .first()
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.00")')
  ).toBeVisible();
  await page.mouse.move(0, 0);

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.50' })
    .getByRole('img')
    .nth(1)
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.50")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("photos"):has-text("$3.00")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("video"):has-text("$10.00")')
  ).toBeVisible();

  await page.getByRole('button', { name: 'Edit' }).click();
  await page.getByRole('row', { name: 'video $' }).getByRole('button').click();
  await page.getByRole('button', { name: 'Update' }).click();

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.' })
    .getByRole('img')
    .first()
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.00")')
  ).toBeVisible();
  await page.mouse.move(0, 0);

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.' })
    .getByRole('img')
    .nth(1)
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.50")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("photos"):has-text("$3.00")')
  ).toBeVisible();

  await page.getByRole('button', { name: 'Edit' }).click();
  await page.getByRole('button', { name: '+ Add Breakdown' }).click();
  await page
    .getByRole('row', { name: '$ 0' })
    .getByPlaceholder('text')
    .fill('audio');
  await page
    .getByRole('row', { name: 'audio $' })
    .getByPlaceholder('0.00')
    .fill('20');
  await page.getByRole('button', { name: 'Update' }).click();

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.' })
    .getByRole('img')
    .first()
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.00")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("audio"):has-text("$20.00")')
  ).toBeVisible();
  await page.mouse.move(0, 0);

  await page
    .getByRole('row', { name: 'my_model ^my_model$ $1.00 $1.' })
    .getByRole('img')
    .nth(1)
    .hover();
  await page
    .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
    .waitFor();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("default"):has-text("$1.50")')
  ).toBeVisible();
  await expect(
    page
      .getByRole('tooltip', { name: 'Price Breakdown Token Type' })
      .locator('tr:has-text("photos"):has-text("$3.00")')
  ).toBeVisible();
});
