import { expect, test } from '@playwright/test';
import { randomUUID } from 'crypto';
import { Client } from 'langsmith';
import { createAndCopyApiKey, deleteApiKey } from './utils/apiKey';
import { BASE_URL, ENDPOINT_URL } from './utils/constants';
import {
  navigateToDatasetsAndDeleteDataset,
  navigateToProjectsAndDeleteProject,
} from './utils/delete';
import { loginAndEnterInviteCode } from './utils/login';
import { retry } from './utils/retry';
import {
  CreateSupabaseUser,
  createSupabaseUser,
  deleteUser,
} from './utils/supabase';
import { waitForWriteQueue } from './utils/waitForWriteQueue';
import { goToProject } from './utils/goToProject';

let user: CreateSupabaseUser | null = null;
test.beforeAll(async () => {
  if (BASE_URL === 'https://eks.smith.langchain.dev') {
    console.debug('Skipping user creation');
    return;
  }
  user = await createSupabaseUser('project');
  console.debug('Created user', user.id);
});

test.afterAll(async () => {
  if (user != null) {
    console.debug('Deleting user', user.id);
    await deleteUser(user.id);
  }
});

test('New Project ', async ({ page }) => {
  await loginAndEnterInviteCode(page, user);
  const apiKey = await createAndCopyApiKey(page);

  // create project
  const projectName = `${Math.floor(
    Math.random() * 100
  )}-test-dataset-${randomUUID()}`;
  const client = new Client({ apiKey, apiUrl: ENDPOINT_URL });
  await client.createProject({ projectName: projectName });

  // log some runs to the project
  const runName = 'Example Run';
  const firstChildRunName = 'firstChildRunName';
  const secondChildRunName = 'secondChildRunName';
  const secondTopLevelRunName = 'secondTopLevelRunName';
  const runId = randomUUID();
  const firstChildRunId = randomUUID();
  const parentRunInput = 'parentRunInput';
  const firstChildRunInput = 'firstChildRunInput';
  const secondChildRunInput = 'secondChildRunInput';
  const secondTopLevelRunInput = 'secondTopLevelRunInput';
  await client.createRun({
    id: runId,
    name: runName,
    inputs: { input: parentRunInput },
    run_type: 'chain',
    project_name: projectName,
    start_time: (Date.now() + 60000) / 1000,
    end_time: (Date.now() + 78000) / 1000,
  });
  await client.createRun({
    id: firstChildRunId,
    name: firstChildRunName,
    inputs: { input: firstChildRunInput },
    run_type: 'chain',
    project_name: projectName,
    parent_run_id: runId,
    start_time: (Date.now() + 62000) / 1000,
    end_time: (Date.now() + 73000) / 1000,
  });
  await client.createRun({
    name: secondChildRunName,
    inputs: { input: secondChildRunInput },
    run_type: 'chain',
    project_name: projectName,
    parent_run_id: firstChildRunId,
    start_time: (Date.now() + 64000) / 1000,
    end_time: (Date.now() + 65000) / 1000,
  });
  await client.createRun({
    name: secondTopLevelRunName,
    inputs: { input: secondTopLevelRunInput },
    run_type: 'chain',
    project_name: projectName,
    start_time: (Date.now() + 68_000) / 1000,
    end_time: (Date.now() + 70_000) / 1000,
  });

  // check if the run is logged
  await goToProject(page, projectName);

  await page.getByRole('tab', { name: 'Runs' }).click();
  await waitForWriteQueue(page, [
    { rowName: runName, numRuns: 1 },
    { rowName: secondTopLevelRunInput, numRuns: 1 },
  ]);

  await retry({
    loop: async () => {
      await expect
        .soft(page.getByRole('row', { name: secondTopLevelRunName }))
        .toBeVisible();
      await page.getByRole('row', { name: secondTopLevelRunName }).click();
    },
    description: 'Waiting for row with first run to appear',
    onFailure: async () => {
      await page.waitForTimeout(5000);
      await page.reload();
    },
  });

  //test diffing
  await page.getByRole('button', { name: 'Compare' }).click();
  await page.getByRole('row', { name: runName }).click();

  await page.getByTestId('diff-toggle').click();

  await retry({
    loop: async () => {
      // Check that the diff'ed text is highlighted
      await expect(
        page
          .getByTestId('split-view-pane')
          .locator('span')
          .filter({ hasText: 'parent' })
          .first()
      ).toHaveAttribute('class', /highlight-added/);
    },
    description: 'Waiting for diff to show',
    onFailure: async () => {
      await page.waitForTimeout(5000);
    },
    retries: 10,
  });

  await page.getByRole('button', { name: 'Stop comparing' }).click();

  await page
    .getByTestId('split-view-pane')
    .getByRole('button', { name: 'Add to' })
    .click();
  await page.getByRole('button', { name: 'Add to Dataset' }).click();

  await expect.soft(page.getByText('Add Example to dataset')).toBeVisible();
  await page.getByText('New Dataset').click();
  await expect.soft(page.getByTestId('pane-header-New Dataset')).toBeVisible();
  await page.getByRole('button', { name: 'Clear input schema' }).click();
  await page.getByRole('tab', { name: 'Output schema' }).click();
  await page.getByRole('button', { name: 'Clear output schema' }).click();
  const datasetName = `EXAMPLEDATASET${randomUUID()}`;
  await page.getByPlaceholder('Type name...').clear();
  await page.getByPlaceholder('Type name...').fill(datasetName);
  await page.getByRole('button', { name: 'Create' }).click();
  await expect(
    page.getByTestId('example-crud-inputs').getByText(secondTopLevelRunInput)
  ).toBeVisible();
  await expect
    .soft(page.getByTestId('pane-header-New Dataset'))
    .not.toBeVisible();
  const autocompleteInput = await page
    .getByTestId('dataset-picker')
    .locator('input')
    .inputValue();
  await expect.soft(autocompleteInput).toBe(datasetName);
  await page.getByRole('button', { name: 'Submit' }).click();
  await page.getByTestId('split-view-pane-close-button').click();
  await expect.soft(page.getByTestId('in-dataset-icon')).toBeVisible();

  await navigateToProjectsAndDeleteProject(page, projectName, true);

  // Delete dataset
  await navigateToDatasetsAndDeleteDataset(page, datasetName, true);

  await deleteApiKey(page);
});
