import { expect, test } from '@playwright/test';
import { BASE_URL, OPENAI_API_KEY } from './utils/constants';
import {
  CreateSupabaseUser,
  createSupabaseUser,
  deleteUser,
} from './utils/supabase';
import { randomUUID } from 'crypto';
import { loginAndEnterInviteCode } from './utils/login';

let user: CreateSupabaseUser | null = null;
test.beforeAll(async () => {
  if (BASE_URL === 'https://eks.smith.langchain.dev') {
    console.debug('Skipping user creation');
    return;
  }
  user = await createSupabaseUser('playground');
  console.debug('Created user', user.id);
});

test.afterAll(async () => {
  if (user != null) {
    console.debug('Deleting user', user.id);
    await deleteUser(user.id);
  }
});

test('Prompts', async ({ page }) => {
  await loginAndEnterInviteCode(page, user);

  // go to prompts page
  await page.goto('/prompts');

  // create new prompt in playground with unique name
  await expect.soft(page.getByText('Start creating')).toBeVisible();
  await page
    .getByRole('button', { name: 'Prompt', exact: true })
    .nth(0)
    .click();
  await expect
    .soft(
      page
        .getByRole('heading', { name: 'Playground' })
        .filter({ hasText: 'Iterate on and test prompts.' })
    )
    .toBeVisible();

  // enter api key
  await page.getByRole('button', { name: 'Secrets & API Keys' }).click();
  await page
    .getByTestId('playground-secret-input-OPENAI_API_KEY')
    .getByRole('textbox')
    .fill(OPENAI_API_KEY);

  // add output schema
  await page.getByLabel('Force output to follow a specific schema').click();
  await expect.soft(page.getByText('Add Output Schema')).toBeVisible();
  await page.getByTestId('schema-modal-save-button').click();
  // remove output schema
  await page.getByLabel('Remove output schema').click();
  // add it back
  await page.getByLabel('Force output to follow a specific schema').click();
  await page.getByTestId('schema-modal-save-button').click();

  // save prompt
  await page.getByText('Save').click();

  const structuredPrompt = `structured-test-prompt-${randomUUID()}`;
  const structuredParentInput = page.getByTestId('prompt-name-input');
  const structuredInput = structuredParentInput.locator('input');
  await structuredInput.fill(structuredPrompt);

  await page.getByRole('button', { name: 'Save' }).click();

  await expect
    .soft(
      page
        .getByRole('heading', { name: 'Playground' })
        .filter({ hasText: 'Iterate on and test prompts.' })
    )
    .toBeVisible();
  const textContent = await page
    .getByPlaceholder('Enter variable value...')
    .textContent();
  await expect.soft(textContent === '' || textContent === null).toBeTruthy();

  // write another prompt
  await page.goto('/playground');

  // run a prompt
  const inputMessage = 'How old is the universe?';
  await page.getByPlaceholder('Enter variable value...').fill(inputMessage);
  await page.getByRole('button', { name: 'Start' }).click();

  // save prompt
  // we have to wait for the run to complete for the save button to be enabled, annotate button shows up then
  await expect(page.getByRole('button', { name: 'Annotate' })).toBeVisible({
    timeout: 30000,
  });
  await page.getByText('Save').click();

  const promptName = `test-prompt-${randomUUID()}`;
  const parentInput = page.getByTestId('prompt-name-input');
  const input = parentInput.locator('input');
  await input.fill(promptName);

  await page.getByRole('button', { name: 'Save' }).click();

  // click on the link in the toast message that says "view in the prompt hub"
  await page.getByRole('status').getByRole('link').click();

  await expect
    .soft(page.getByRole('heading', { name: promptName }))
    .toBeVisible();

  // go to the prompts page
  await page.goto('/prompts');

  // Wait for the prompt to appear in the list and be visible
  const prompt = page.getByRole('row', { name: promptName });
  await expect(prompt).toBeVisible();

  const promptVisibility = 'Private';
  // click on a sub element of the prompt row to open the prompt since clicks are intercepted at the row level
  await prompt
    .getByRole('cell')
    .filter({ hasText: promptVisibility })
    .getByText(promptVisibility)
    .click();

  await expect
    .soft(page.getByRole('heading', { name: promptName }))
    .toBeVisible();
});
