import json
import os
import re
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ValidationError, ValidationInfo, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from lc_config import constants
from lc_config.base_service_settings import BaseServiceSettings
from lc_config.env import ENV_FILE_PATH, LANGCHAIN_ENV, SECRETS_DIR_PATH
from lc_config.logging_settings import LoggingSettings
from lc_config.service_communication_settings import ServiceCommunicationSettings
from lc_config.tenant_config import OrganizationConfig, TenantConfig


class ReplicaLimits(BaseModel):
    min: int = Field(..., gt=0)
    max: int = Field(..., gt=0)


class SharedSettings(
    BaseServiceSettings, ServiceCommunicationSettings, LoggingSettings, BaseSettings
):
    """Shared settings for the monorepo."""

    model_config = SettingsConfigDict(extra="ignore")

    AUTH_TYPE: str = "supabase"
    API_KEY_SALT: str = ""
    SUPABASE_JWT_SECRET: str | None = None
    SUPABASE_SSO_EMAIL_VERIFICATION_JWT_SECRET: str | None = None
    SUPABASE_SSO_EMAIL_VERIFICATION_JWT_EXPIRATION_SECONDS: int = 24 * 60 * 60  # 1 day
    SUPABASE_ACCESS_TOKEN: str | None = None
    SUPABASE_PROJECT_REF: str | None = None
    SINGLETON_TENANT_ID: UUID | None = None
    GCP_PROJECT_ID: str | None = None
    LANGSMITH_LICENSE_KEY: str
    PHONE_HOME_ENABLED: bool = False

    @field_validator("PHONE_HOME_ENABLED", mode="before")
    @classmethod
    def phone_home_enabled(cls, v: Optional[str], info: ValidationInfo) -> bool:
        if isinstance(v, str):
            return v.lower() == "true"
        return info.data.get("LANGSMITH_LICENSE_KEY", "").startswith("lcl_")

    LICENSE_VERIFY_INTERVAL_HOURS: int = 24
    MAX_SIZE_POST_BODY_FIELD_KB: int = 25_000  # 25MB
    MAX_SIZE_INDEXED_FIELD_KB: int = 500  # 500KB
    MAX_TOKEN_CALCULATION_KB_SIZE: int = 1_000  # 1MB
    TOKEN_CALCULATION_TIMEOUT_SEC: int = 15
    TRACE_TIER_TTL_DURATION_SEC_MAP: dict[str, int] = {
        "longlived": 60 * 60 * 24 * 400,
        "shortlived": 60 * 60 * 24 * 14,
    }
    # optional fields for tracing of query gen runs
    QUERY_GEN_TRACING_SESSION_NAME: str | None = None
    QUERY_GEN_TRACING_TENANT_ID: UUID | None = None

    # onboarding emails
    ONBOARDING_EMAILS_CRON: str = "*/30 * * * *"  # run every 30 minutes
    ONBOARDING_EMAILS_JOB_TIMEOUT_SEC: int = 30
    ONBOARDING_EMAILS_JOB_LOCK_KEY: str = "onboarding-emails-job"
    ONBOARDING_EMAILS_LOCK_TIMEOUT_SEC: int = 30
    ONBOARDING_EMAILS_ID_LIST: List[str] = [
        "onboarding-intro",
        "onboarding-playground",
        "onboarding-prompt-hub",
        "onboarding-evaluations",
        "onboarding-annotation-queues",
        "onboarding-rules",
        "onboarding-production",
    ]
    ONBOARDING_EMAILS_MESSAGE_STREAM: str = "langsmith-onboarding-drip-feed"
    ONBOARDING_EMAILS_FROM: str = "Nick @ LangChain <<EMAIL>>"
    ENABLE_ONBOARDING_EMAILS: bool = False
    ONBOARDING_EMAILS_CREATION_BUFFER: timedelta = timedelta(hours=5)
    MIN_TIME_SINCE_LAST_ONBOARDING_EMAIL: timedelta = timedelta(
        days=1, hours=23, minutes=59
    )
    MAX_TIME_SINCE_LAST_ONBOARDING_EMAIL: timedelta = timedelta(days=3)

    # bulk export
    BULK_EXPORT_MAX_CONCURRENT_RUNS: int = 5
    ENABLE_BULK_EXPORT_CRON: bool = True
    BULK_EXPORT_CRON: str = "* * * * *"  # run every minute
    BULK_EXPORT_JOB_TIMEOUT_SEC: int = 30
    BULK_EXPORT_WORKFLOW_TIMEOUT_SEC: int = 48 * 3600  # 48 hours
    BULK_EXPORT_JOB_RETRIES: int = 10
    BULK_EXPORT_JOB_RETRY_DELAY_SEC: int = 30
    BULK_EXPORT_PARTITION_TARGET_SIZE: int = 50_000
    DATA_EXPORT_RUN_LIMIT: int = 500
    DATA_EXPORT_START_BATCH_SIZE: int = 500
    DATA_EXPORT_LOCK_TIMEOUT_SEC: int = 30
    DATA_EXPORT_MAX_BATCH_SIZE: int = 30_000
    DATA_EXPORT_TARGET_SIZE_KB: int = 10_000  # 10MB
    DATA_EXPORT_MAX_BATCH_PAYLOAD_SIZE_KB: int = 100_000  # 100MB
    DATA_EXPORT_FORCE_GC: bool = False
    MAX_CONCURRENT_BULK_EXPORTS_PER_WS: int = 3
    BULK_EXPORT_CLICKHOUSE_CLIENT: str = "internal_analytics_slow"

    @field_validator("QUERY_GEN_TRACING_TENANT_ID", mode="before")
    @classmethod
    def validate_query_gen_tracing_tenant_id(
        cls, v: UUID | None, info: ValidationInfo
    ) -> UUID | None:
        if v is not None:
            if info.data.get("QUERY_GEN_TRACING_SESSION_NAME") is None:
                raise ValueError(
                    "QUERY_GEN_TRACING_SESSION_NAME must be set if QUERY_GEN_TRACING_TENANT_ID is set"
                )
        return v

    SHARED_TENANT_DEFAULT_CONFIG: TenantConfig = TenantConfig()
    SHARED_ORG_DEFAULT_CONFIG: OrganizationConfig = OrganizationConfig(
        max_identities=10,
        max_workspaces=3,
        can_use_rbac=False,
        can_add_seats=True,
    )
    PERSONAL_ORG_DEFAULT_CONFIG: OrganizationConfig = OrganizationConfig(
        max_identities=1,
        max_workspaces=1,
        can_use_rbac=False,
        can_add_seats=False,
    )
    DEFAULT_WORKSPACE_NAME: str = "Workspace 1"
    DEFAULT_TAG_KEYS: list[tuple] = [
        ("Application", "Used to distinguish different applications"),
        ("Environment", "Used to distinguish different environments"),
    ]

    # OAuth fields
    OAUTH_CLIENT_ID: str | None = None
    OAUTH_ISSUER_URL: str | None = None
    # If your Oauth provider supports dynamic discovery, you can leave this blank. Only specify this to override the discovery.
    OAUTH_METADATA_URL: str | None = None

    @field_validator("OAUTH_CLIENT_ID", mode="before")
    @classmethod
    def get_client_id(cls, v: str, info: ValidationInfo):
        auth_type = info.data.get("AUTH_TYPE")
        if auth_type == "oauth":
            if not v:
                raise ValueError("For OAuth, OAUTH_CLIENT_ID must be set")
        return v

    @field_validator("OAUTH_METADATA_URL", mode="before")
    @classmethod
    def get_metadata_url(cls, v: str, info: ValidationInfo):
        auth_type = info.data.get("AUTH_TYPE")
        if auth_type == "oauth":
            if not info.data.get("OAUTH_ISSUER_URL") and not v:
                raise ValueError(
                    "For OAuth, OAUTH_ISSUER_URL or OAUTH_METADATA_URL must be set"
                )
        if v:
            return v
        return f"{info.data.get('OAUTH_ISSUER_URL')}/.well-known/openid-configuration"

    @field_validator("GCP_PROJECT_ID")
    @classmethod
    def validate_gcp_project_id(cls, v: str, info: ValidationInfo) -> str:
        langchain_env = info.data.get("LANGCHAIN_ENV")
        assert langchain_env is not None
        if not langchain_env.startswith("local") and v is None:
            raise ValueError("GCP_PROJECT_ID is required for deployed envs")
        return v

    @field_validator("AUTH_TYPE")
    @classmethod
    def validate_auth_type(cls, v: str, info: ValidationInfo) -> str:
        if v not in ["supabase", "oauth", "none", "mixed"]:
            raise ValueError("Invalid auth type")
        langchain_env = info.data.get("LANGCHAIN_ENV")
        assert langchain_env is not None
        if langchain_env.startswith("production") and v in ["none", "mixed"]:
            raise ValueError("Invalid auth type for production environment")
        return v

    @field_validator("API_KEY_SALT")
    @classmethod
    def validate_api_key_salt(cls, v: str, info: ValidationInfo) -> str:
        auth_type = info.data.get("AUTH_TYPE")
        langchain_env = info.data.get("LANGCHAIN_ENV")
        assert langchain_env is not None
        if auth_type != "none" or langchain_env.startswith("production"):
            if not v:
                raise ValueError("API_KEY_SALT is required for api_key auth")
        return v

    @field_validator("SUPABASE_JWT_SECRET")
    @classmethod
    def validate_supabase_jwt_secret(cls, v: str, info: ValidationInfo) -> str:
        auth_type = info.data.get("AUTH_TYPE")
        if auth_type == "supabase" and v is None:
            raise ValueError("SUPABASE_JWT_SECRET is required for supabase auth")
        return v

    @field_validator("SUPABASE_SSO_EMAIL_VERIFICATION_JWT_SECRET")
    @classmethod
    def set_supabase_sso_jwt_secret(cls, v: str, info: ValidationInfo) -> str:
        """Set the SUPABASE_SSO_EMAIL_VERIFICATION_JWT_SECRET to the SUPABASE_JWT_SECRET if it is not set"""
        if info.data.get("SUPABASE_JWT_SECRET") and not v:
            return info.data.get("SUPABASE_JWT_SECRET")
        return v

    @field_validator("SINGLETON_TENANT_ID")
    @classmethod
    def validate_singleton_tenant_id(
        cls, v: UUID | None, info: ValidationInfo
    ) -> UUID | None:
        auth_type = info.data.get("AUTH_TYPE")
        if auth_type == "none":
            return constants.STATIC_SINGLETON_TENANT_ID
        else:
            return None

    # Database settings
    POSTGRES_SCHEMA: str = "public"
    # PGbouncer config must be above ASYNCPG_DATABASE_URI, ordering matters for field validators
    POSTGRES_DATABASE_URI: str | None = None
    PGBOUNCER_DATABASE_URI: str | None = None
    SQLALCHEMY_DATABASE_URI: str | None = None
    # Retry for up to 3 minutes 18s: 18 1 second delays + 18 attempts of 10s each
    ALEMBIC_NUM_RETRIES: int = 18
    ALEMBIC_RETRY_DELAY_SEC: int = 1
    ALEMBIC_LOCK_TIMEOUT_SEC: int = 10
    ALEMBIC_PER_MIGRATION_LOCK_TIMEOUT_MS: int = 200
    ALEMBIC_PER_MIGRATION_STMT_TIMEOUT_MS: int = 250
    ASYNCPG_DATABASE_URI: str | None = None
    # If enabled, prefix all DB queries with audit log context variables using SQL comments.
    # These will show up in pgaudit logs. This does NOT automatically enable pgaudit, which
    # must be enabled separately.
    AUDIT_LOGS_ENABLED: bool = False
    ASYNCPG_LOCK_TIMEOUT_MS: int = 5000

    # Statement cache size is 0 if using PGBouncer
    ASYNCPG_STATEMENT_CACHE_SIZE: int = 100
    ASYNCPG_POOL_MIN_SIZE: int = 1
    ASYNCPG_POOL_MAX_SIZE: int = 50
    ASYNCPG_POOL_MAX_INACTIVE_CONNECTION_LIFETIME_SEC: int = 30
    ASYNCPG_POOL_TIMEOUT_SEC: int = 60
    POSTGRES_DEFAULT_STATEMENT_TIMEOUT_SEC: int = 5 * 60  # 5 minutes
    REDIS_DATABASE_URI: str | None
    REDIS_CACHE_DATABASE_URI: str | None = None
    REDIS_CLUSTER_DATABASE_URIS: list[str] | None = None
    REDIS_CLUSTER_DISCOVER_IP: str | None = None
    REDIS_CLUSTER_ENABLED: bool = False
    REDIS_CLUSTER_IAM_AUTH_ENABLED: bool = False
    REDIS_CLUSTER_ALERTS_ENABLED: bool = False
    REDIS_CLUSTER_DISCOVER_PORT: int = 6379
    REDIS_CLUSTER_RETRIES: int = 5
    REDIS_CLUSTER_IAM_AUTH_TOKEN_CACHE_BUFFER: int = 5  # minutes
    REDIS_CLUSTER_INGESTION_GLOBAL_ENABLED: bool = False
    REDIS_CLUSTER_INGESTION_TENANT_IDS: list[str] | None = []
    # Dual write to Redis cluster
    REDIS_CLUSTER_DUAL_WRITE_ENABLED: bool = False

    # TODO: Deprecate this once migrated
    # REDIS_SHARD_URIS defaults to {"node-0": <REDIS_DATABASE_URI>}.
    REDIS_SHARD_URIS: dict[str, str] | None = None
    REDIS_SHARDING_OVERRIDE_WORKSPACES: dict[
        str, str
    ] = {}  # workspace ID -> node name (node-1)
    REDIS_SHARDING_ENABLED: bool = False  # global FF for all sharding
    REDIS_SHARDED_READS_ENABLED: bool = False  # FF for sharded reads specifically
    REDIS_SHARDED_QUEUES_ENABLED: bool = False  # FF for enqueueing SAQ jobs to shards
    REDIS_SHARDED_WRITES_ENABLED: bool = False  # FF for main write operation to shard
    REDIS_SHARDED_TIME_ENABLED: bool = True  # FF for time operation to shard
    REDIS_MIN_VERSION: str = "6.0.0"
    # Only available in >= 6.2.0 (remove when we bump min version)
    REDIS_USE_SMISMEMBER: bool | None = None
    REDIS_MAX_CONNECTIONS: int = 50
    REDIS_CACHE_MAX_CONNECTIONS: int = 50
    REDIS_SOCKET_TIMEOUT_SEC: int = 5
    FF_ENABLE_LOCK_RENEWAL: bool = True
    REDIS_FEEDBACK_UPDATE_TTL: int = 120  # 2 minutes
    TENANT_TRACER_SESSIONS_CACHE_TTL: int = 60 * 60 * 4  # 4 hours
    REDIS_LOCK_RENEWAL_DIVISOR: int = 2
    REDIS_JOB_TTL: int = -1

    MAX_TRACE_LIMIT: int = 25_000

    @field_validator("REDIS_SHARD_URIS")
    @classmethod
    def default_redis_shard_uris(
        cls, v: dict[str, str] | None, info: ValidationInfo
    ) -> dict[str, str] | None:
        if info.data.get("REDIS_DATABASE_URI") and not v:
            return {"node-0": info.data.get("REDIS_DATABASE_URI")}
        return v

    @field_validator("REDIS_USE_SMISMEMBER")
    @classmethod
    def redis_use_smismember(
        cls, v: dict[str, str] | None, info: ValidationInfo
    ) -> bool:
        redis_min_version = info.data.get("REDIS_MIN_VERSION")
        if redis_min_version:
            major, minor, _ = redis_min_version.split(".")
            return int(major) > 6 or (int(major) == 6 and int(minor) >= 2)
        return False

    @field_validator("REDIS_CLUSTER_ENABLED")
    @classmethod
    def validate_redis_cluster_enabled(cls, v: bool, info: ValidationInfo) -> bool:
        if (
            v
            and not info.data.get("REDIS_CLUSTER_DATABASE_URIS")
            and not info.data.get("REDIS_CLUSTER_DISCOVER_IP")
        ):
            raise ValueError(
                "REDIS_CLUSTER_DATABASE_URIS or REDIS_CLUSTER_DISCOVER_IP must be set when REDIS_CLUSTER_ENABLED is True"
            )
        return v

    # Ingest Project Creation
    INGEST_PROJECT_CREATION_SEMAPHORE: int = 10

    # worker settings
    MAX_ASYNC_JOBS_PER_WORKER: int = 10
    ASYNC_WORKER_SEMAPHORE: int = 20
    ASYNC_GRACEFUL_SHUTDOWN_SECS: int = 25
    INGESTION_QUEUE: str = "default"
    ADHOC_QUEUE: str = "adhoc"
    EXPORT_QUEUE: str = "export"
    RUN_RULES_QUEUE: str = "rules"
    UPGRADES_QUEUE: str = "upgrades"
    HOST_QUEUE: str = "host"
    SINGLE_WORKER_QUEUE: str = "default"
    # These queues will not be processed by the single worker.
    # Only relevant in self-hosted, since we don't use single worker in cloud.
    SEPARATE_QUEUES_WITH_SINGLE_WORKER: list[str] = []
    DELETE_QUEUE_DELAY_SEC: int = 75
    # For SAQ workers to read from sharded Redis
    SAQ_REDIS_NODE_NAME: str | None = None

    # Clickhouse settings
    CLICKHOUSE_HOST: str | None = None
    CLICKHOUSE_PORT: int | None = None
    CLICKHOUSE_USER: str | None = None
    CLICKHOUSE_PASSWORD: str | None = None
    CLICKHOUSE_DB: str | None = None
    CLICKHOUSE_2_HOST: str | None = None
    CLICKHOUSE_2_PORT: int | None = None
    CLICKHOUSE_2_USER: str | None = None
    CLICKHOUSE_2_PASSWORD: str | None = None
    CLICKHOUSE_2_DB: str | None = None
    CLICKHOUSE_TLS: bool = False
    CLICKHOUSE_MAX_CONNECTIONS: int = 100
    CLICKHOUSE_MAX_KEEPLIVE_CONNECTIONS: int = 20
    CLICKHOUSE_KEEPALIVE_EXPIRY: float = 9.0
    CLICKHOUSE_CONNECT_TIMEOUT: float = 5.0
    CLICKHOUSE_READ_TIMEOUT: float = 30.0
    CLICKHOUSE_WRITE_TIMEOUT: float = 30.0
    CLICKHOUSE_POOL_TIMEOUT: float = 15.0
    CLICKHOUSE_FE_TIMEOUT: float = 15
    CLICKHOUSE_STATS_TIMEOUT: float = 25.0
    CLICKHOUSE_STATS_MAX_THREADS: int = 10
    CLICKHOUSE_GROUPS_MAX_THREADS: int = 14
    CLICKHOUSE_DATA_EXPORT_MAX_THREADS: int = 8
    CLICKHOUSE_IN_APP_ANALYTICS_HOST: str | None = None
    CLICKHOUSE_IN_APP_ANALYTICS_PORT: int | None = None
    CLICKHOUSE_INTERNAL_ANALYTICS_HOST: str | None = None
    CLICKHOUSE_INTERNAL_ANALYTICS_PORT: int | None = None
    CLICKHOUSE_IN_APP_STATS_HOST: str | None = None
    CLICKHOUSE_IN_APP_STATS_PORT: int | None = None

    CLICKHOUSE_FETCH_RUNS_TIMEOUT: float = 30
    CLICKHOUSE_FETCH_RUNS_MAX_IDS: int = 3_000
    CLICKHOUSE_CLUSTER: str | None = None

    TENANT_EXTENDED_CLICKHOUSE_RESOURCES: list[str] = []
    EXTENDED_CLICKHOUSE_STATS_TIMEOUT: float = 45
    EXTENDED_CLICKHOUSE_STATS_MAX_THREADS: int = 18

    # Feature flags for disabling tracer session deletes
    FF_DISABLE_TRACER_SESSION_DELETES: bool = False
    FF_TRACER_SESSION_DELETE_BATCHING: bool = False
    TRACER_SESSION_DELETE_BATCH_SIZE: int = 6000
    TRACER_SESSION_DELETE_MAX_QUERY_SIZE: int = 6000
    TRACER_SESSION_BATCH_DELETE_CRON: str | None = (
        "0 15 * * 6"  # Run once at 7am PST on Saturday
    )
    TRACER_SESSION_DELETE_JOB_TIMEOUT_SEC: int = 600  # 10 minutes
    TRACER_SESSION_DELETE_SORTED_SET_KEY: str = "session_deletes"

    # Feature flags for run deletes
    RUN_DELETE_SORTED_SET_KEY: str = "run_deletes"
    ENABLE_RUN_DELETE_CRON: bool = False
    RUN_DELETE_CRON: str | None = "0 16 * * 0"  # Run once at 8am PST on Sunday
    RUN_DELETE_JOB_TIMEOUT_SEC: int = 900  # 15 minutes
    RUN_DELETE_BATCH_SIZE: int = 6000
    RUN_DELETE_MAX_QUERY_SIZE: int = 6000
    RUN_DELETE_MAX_RUNS: int = 1000
    RUN_DELETE_S3_ASSETS_DELAY_SEC: int = 0  # No delay

    # ---- run-delete window & re-enqueue ----
    RUN_DELETE_REENQUEUE_DELAY_SEC: int = 1_800  # 30 min
    # hard window: 07:00 Saturday → 22:00 Sunday (PST)
    RUN_DELETE_WINDOW_START_WEEKDAY: int = 5
    RUN_DELETE_WINDOW_START_HR: int = 7
    RUN_DELETE_WINDOW_END_WEEKDAY: int = 6
    RUN_DELETE_WINDOW_END_HR: int = 22

    # join-table cleanup runs the next day
    RUN_TRACE_ID_CLEANUP_DELAY_SEC: int = 43_200  # 12 h
    FF_REENQUEUE_TRACER_SESSION_DELAY: int = 30  # minutes

    # Feature flag for enabling multi-service clickhouse clients
    FF_CLICKHOUSE_USE_MULTISERVICE: bool = False
    FF_CH_IN_APP_ANALYTICS: bool = False
    FF_CH_INTERNAL_ANALYTICS: bool = False
    FF_CH_IN_APP_STATS: bool = False
    # for slow queries
    CLICKHOUSE_SLOW_MAX_KEEPLIVE_CONNECTIONS: int = 5
    CLICKHOUSE_SLOW_MAX_CONNECTIONS: float = 100
    CLICKHOUSE_SLOW_READ_TIMEOUT: float = 125.0
    CLICKHOUSE_SLOW_WRITE_TIMEOUT: float = 125.0

    # for updating TTL merge settings
    ENABLE_CLICKHOUSE_MERGE_SETTINGS_CRON: bool = False
    # Turn on merge settings at 14:00 UTC / 7:00 AM PST Sunday
    CLICKHOUSE_MERGE_SETTINGS_ON_CRON: str = "0 14 * * 0"
    # Turn off merge settings at 23:00 UTC / 4:00 PM PST Sunday
    CLICKHOUSE_MERGE_SETTINGS_OFF_CRON: str = "0 23 * * 0"
    CLICKHOUSE_MERGE_CRON_TIMEOUT_SEC: int = 900

    ### Begin - ClickHouse Autoscale Settings ###
    CLICKHOUSE_CLOUD_API_BASE_URL: str = "https://api.clickhouse.cloud"
    CLICKHOUSE_API_KEY_ID: str | None = None
    CLICKHOUSE_API_KEY_SECRET: str | None = None
    AUTOSCALE_CLICKHOUSE_CRON: str = "*/10 * * * *"
    AUTOSCALE_CLICKHOUSE_TIMEOUT_SEC: int = 60

    # Hash for holding multiple clickhouse cluster confog
    CLICKHOUSE_CLUSTERS: Dict[str, Dict[str, Any]] = Field(default_factory=dict)

    @field_validator("CLICKHOUSE_CLUSTERS", mode="before")
    @classmethod
    def parse_and_resolve_clusters(cls, raw: str | Dict[str, Any]) -> Dict[str, Any]:
        def resolve(val: str) -> str:
            match = re.fullmatch(r"\$\((\w+)\)", val)
            return os.environ.get(match.group(1), "") if match else val

        if isinstance(raw, str):
            try:
                config = json.loads(raw)
            except Exception as e:
                raise ValueError(f"Invalid JSON in CLICKHOUSE_CLUSTERS: {e}")
        elif isinstance(raw, dict):
            config = raw
        else:
            raise TypeError("CLICKHOUSE_CLUSTERS must be a JSON string or dict")

        for cluster_name, cluster_cfg in config.items():
            for k in ["api_key_id", "api_key_secret"]:
                if k in cluster_cfg:
                    cluster_cfg[k] = resolve(cluster_cfg[k])
            # Validate replica_limits
            try:
                replica_limits = cluster_cfg.get("replica_limits", {})
                validated_limits = ReplicaLimits(**replica_limits)
                cluster_cfg["replica_limits"] = validated_limits.model_dump()
            except ValidationError as ve:
                raise ValueError(
                    f"Invalid replica_limits for cluster '{cluster_name}': {ve}"
                )

        return config

    ### End - ClickHouse Autoscale Settings ###

    # for creating/syncing org charts for self-hosted customers
    #   that have organizations.config.enable_org_usage_charts set to true in the DB
    ENABLE_ORG_CHARTS_CRON: bool = False
    ORG_CHARTS_CRON: str = "*/5 * * * *"  # every 5 minutes
    ORG_CHARTS_JOB_TIMEOUT_SEC: int = 60

    # Payment
    STRIPE_SECRET_KEY: str | None = None
    METRONOME_API_KEY: str | None = None

    # New Plans
    METRONOME_FREE_PLAN_ID: str | None = None
    METRONOME_DEV_PLAN_ID: str | None = None
    METRONOME_PLUS_PLAN_ID: str | None = None
    METRONOME_STARTUP_PLAN_ID: str | None = None
    METRONOME_PARTNER_PLAN_ID: str | None = None
    METRONOME_PREMIER_PLAN_ID: str | None = None

    # Old Plans
    METRONOME_DEV_LEGACY_PLAN_ID: str | None = None
    METRONOME_PLUS_LEGACY_PLAN_ID: str | None = None
    METRONOME_ENTERPRISE_LEGACY_PLAN_ID: str | None = None

    # Metronome Configuration
    FF_PAYMENT_ENABLED: bool = False
    FF_USE_PAID_PLANS: bool = False
    ORG_METRONOME_CACHE_EXPIRY_SEC: int = 60
    METRONOME_CONFIG_CACHE_EXPIRY_SEC: int = 15
    PLAN_APPROVAL_VALIDITY_DAYS: int = 180
    DEFAULT_TRACE_TIER: str = "shortlived"

    # Upgrades configuration
    FF_TRACE_TIERS_ENABLED: bool = False
    FF_UPGRADE_TRACE_TIER_ENABLED: bool = False
    TRACE_UPGRADE_CHUNK_SIZE: int = 25
    TRACE_RUN_UPGRADE_LIMIT: int = 20_000  # Limit to 20k runs per trace for upgrades
    TRACE_UPGRADE_MAX_SPREAD_SEC: int = 120
    CH_UPGRADE_BATCH_LIMIT: int = 2500
    CH_UPGRADE_LOOKBACK_MINUTES: int = 240  # 4 hour lookback
    CH_UPGRADE_QUEUE_DELAY_SEC: int = 2 * 60
    CH_UPGRADE_BATCH_DELAY_SEC: int = 10
    CH_UPGRADE_LOCK_TIMEOUT: int = 5
    CH_UPGRADE_MAX_MEMORY_USAGE: int = 10_000_000_000  # 10GB
    CH_UPGRADE_MAX_EXECUTION_TIME: int = 300

    KAFKA_ENABLED_TENANTS: List[str] = []
    KAFKA_ALL_TENANTS_ENABLED: bool = False
    # WARNING: This will disable upserts to Clickhouse for these tenants - only use when Kafka Connect is enabled
    KAFKA_SKIP_INGEST_CH_WRITE: bool = False
    KAFKA_BOOTSTRAP_SERVERS: str | None = None
    KAFKA_SSL: bool = False
    KAFKA_SASL_PLAIN_USERNAME: str | None = None
    KAFKA_SASL_PLAIN_PASSWORD: str | None = None
    KAFKA_COMPRESSION_TYPE: str | None = None
    KAFKA_MAX_BATCH_SIZE: int = 16384
    KAFKA_LINGER_MS: int = 0
    KAFKA_MAX_REQUEST_SIZE: int = 1048576
    KAFKA_RUNS_TOPIC: str = "runs"
    KAFKA_FEEDBACKS_TOPIC: str = "feedbacks"

    # Quickwit API Indexing
    QUICKWIT_INDEXING_URL: str | None = None
    QUICKWIT_TIMEOUT: float = 60.0
    QUICKWIT_RUNS_INDEX: str = "runs"
    QUICKWIT_RUNS_INDEX_LONG_TTL: str = "runs-long-ttl"

    # Enables Quickwit indexing globally in this env (does not consult QUICKWIT_INDEXING_ENABLED_TENANTS list if set)
    QUICKWIT_INDEXING_ENABLED_ALL: bool = False
    # Enables Quickwit indexing per tenant
    QUICKWIT_INDEXING_ENABLED_TENANTS: List[str] = []

    # Configures Quickwit indexing max (10MB)
    QUICKWIT_INDEXING_MAX_PAYLOAD_SIZE: int = 10_000_000

    # Only use this for testing (blocks ingestion until data is written in index, )
    QUICKWIT_INDEXING_FORCE_COMMIT: bool = False
    # This returns the error in the response w/ perf impact but useful in case of doc ingest rejections
    QUICKWIT_DETAILED_RESPONSE: bool = True

    # Enable Quickwit search ingestion via Kafka
    QUICKWIT_INDEXING_KAFKA_ENABLED: bool = False
    QUICKWIT_INDEXING_KAFKA_RUNS_TOPIC: str = "runs-search"

    # Quickwit API Search endpoint
    QUICKWIT_SEARCH_URL: str | None = None
    # Enables Quickwit-powered full text search features
    QUICKWIT_SEARCH_ENABLED: bool = False
    # Enable Quickwit search query output in logs
    QUICKWIT_SEARCH_QUERY_LOGS: bool = False
    # Enable Quickwit search for runs using the optimized hydrate_runs method
    QUICKWIT_SEARCH_USE_HYDRATE_RUNS: bool = False

    # Configures multiplier for fetch across multiple indexes (with subsequent deduping)
    QUICKWIT_SEARCH_PAGE_MULTIPLIER: int = 2

    ## Run stats group by functionality
    FF_RUN_STATS_GROUP_BY_ENABLED_TENANTS: list[str] = []
    # Enable run stats group by for all tenants.
    # Overrides FF_RUN_STATS_GROU_BY_ENABLED_TENANTS.
    FF_RUN_STATS_GROUP_BY_ENABLED_ALL: bool = False

    ## Tracer session default dashboard endpoint
    FF_TRACER_SESSION_DEFAULT_DASHBOARD_ENABLED_TENANTS: list[str] = []
    # Enable new default dashboard endpoint for all tenants
    # Overrides FF_TRACER_SESSION_DEFAULT_DASHBOARD_ENABLED_TENANTS
    FF_TRACER_SESSION_DEFAULT_DASHBOARD_ENABLED_ALL: bool = False

    FF_USE_PG_FOR_FEEDBACK_UPSERT_ENABLED_TENANTS: list[UUID] = []
    # Enable new default dashboard endpoint for all tenants
    # Overrides FF_USE_PG_FOR_FEEDBACK_UPSERTS_ENABLED_TENANTS
    FF_USE_PG_FOR_FEEDBACK_UPSERT_ENABLED_ALL: bool = False
    PG_BATCH_SIZE: int = 1000
    ## Feedback fetching from Postgres
    FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_TENANTS: list[UUID] = []
    # Override FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_TENANTS
    FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL: bool = False

    FF_USE_PG_FOR_FEEDBACK_CONFIGS_UPSERT_ENABLED_TENANTS: list[UUID] = []
    # Overrides FF_USE_PG_FOR_FEEDBACK_CONFIGS_UPSERT_ENABLED_TENANTS
    FF_USE_PG_FOR_FEEDBACK_CONFIGS_UPSERT_ENABLED_ALL: bool = False
    # WARNING: This will disable upserts to Clickhouse for these tenants - only use when postgres for feedback configs is enabled
    FEEDBACK_CONFIG_SKIP_INGEST_CH_WRITE: bool = False

    FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_TENANTS: list[UUID] = []
    # Overrides FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_TENANTS
    FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_ALL: bool = False

    # Transaction Processing
    TRANSACTION_INTERVAL_SEC: int = 60 * 60
    TRANSACTION_PROCESSING_DELAY_SEC: int = 15 * 60
    # Set the lock/timeout so that we skip at most one run of the cron job
    TRANSACTION_PROCESSING_LOCK_TIMEOUT_SEC: int = 29 * 60
    TRANSACTION_PROCESSING_JOB_TIMEOUT_SEC: int = 29 * 60
    # Run every 15 min
    TRANSACTION_PROCESSING_CRON: str = "*/15 * * * *"
    FF_PROCESS_BILLING_TRANSACTIONS: bool = True

    # Usage Reporting
    METRONOME_REPORTING_BATCH_SIZE: int = 100  # Batch size for reporting to Metronome
    MAX_TRANSACTION_SEND_ATTEMPTS: int = 10
    FF_METRONOME_TRACE_REPORTING_ENABLED: bool = False
    FF_METRONOME_NODES_REPORTING_ENABLED: bool = False
    METRONOME_TRACE_REPORTING_CRON: str = "*/15 * * * *"  # Run every 15 minutes
    METRONOME_TRACE_REPORTING_JOB_TIMEOUT_SEC: int = 14 * 60
    METRONOME_TRACE_ERROR_RETRY_CRON: str = "*/15 * * * *"  # Run every 15 minutes
    METRONOME_TRACE_ERROR_RETRY_JOB_TIMEOUT_SEC: int = 14 * 60
    METRONOME_NODES_REPORTING_CRON: str = "*/15 * * * *"  # Run every 15 minutes
    METRONOME_NODES_REPORTING_JOB_TIMEOUT_SEC: int = 14 * 60
    METRONOME_NODES_ERROR_RETRY_CRON: str = "*/15 * * * *"  # Run every 15 minutes
    METRONOME_NODES_ERROR_RETRY_JOB_TIMEOUT_SEC: int = 14 * 60
    LONGLIVED_TRACE_PROCESSING_ENABLED: bool = False

    NODES_REPORTING_LIMIT: int = 10000  # Limit for nodes fetching from remote_metrics

    FF_METRONOME_SEAT_REPORTING_ENABLED: bool = False
    FF_METRONOME_BATCH_SEAT_REPORTING_ENABLED: bool = False
    METRONOME_SEAT_REPORTING_ORG_BATCH_SIZE: int = (
        100  # Batch size for recording seat transactions in DB
    )
    METRONOME_SEAT_REPORTING_CRON: str = "*/5 * * * *"  # Run every 5 minutes
    METRONOME_SEAT_REPORTING_JOB_TIMEOUT_SEC: int = 4 * 60

    METRONOME_SEAT_ERROR_RETRY_CRON: str = "*/15 * * * *"  # Run every 15 minutes
    METRONOME_SEAT_ERROR_RETRY_JOB_TIMEOUT_SEC: int = 14 * 60
    METRONOME_SEAT_TRANSACTION_PROCESSING_DELAY_SEC: int = 60
    METRONOME_SEAT_HEARTBEAT_CRON: str = "0 14 * * *"  # run once per day at 2pm UTC
    METRONOME_SEAT_HEARTBEAT_JOB_TIMEOUT_SEC: int = 59 * 60

    # Run Rules
    RUN_RULES_CRON: str | None = "*/1 * * * *"  # Run every minute
    RUN_RULES_SCHEDULER_SEMAPHORE: int = 50
    RUN_RULES_MAX_MINUTES_TO_PROCESS: int = 5  # maximum of 5 minutes time span
    RUN_RULES_MAX_BACKFILL_MINUTES_TO_PROCESS: int = (
        60 * 24 * 5
    )  # maximum of 5 days time span
    RUN_RULES_BACKFILL_PROJECT_LIMIT: int = (
        2_000_000  # Limit number of applicable runs that can be backfilled
    )
    RUN_RULES_BACKFILL_FOR_EXTENDED_RETENTION_PROJECT_LIMIT: int = 10_000_000  # Limit number of applicable runs that can be backfilled with extended retention
    RUN_RULES_QUERY_LIMIT: int = (
        1500  # Limit number of records to limit. Note will return less due to merges.
    )
    RUN_RULES_AUTH_CACHE_TTL_SEC: int = 60 * 5  # 5 minutes
    RUN_RULES_MAX_THREADS: int = 4
    RUN_RULES_MAX_MEMORY: int = 40_000_000_000  # 40GB
    RUN_RULES_QUERY_TIMEOUT_SEC: int = 60  # 60 seconds
    RUN_RULES_SPREAD_SEC: int = 60
    RUN_RULES_QUERY_LOOKBACK_SEC: int = 30  # lookback for stale data
    RUN_RULES_CURSOR_CATCHUP_SEC: int = (
        30 * 60
    )  # use cursor if run more than 30 minutes behind
    RUN_RULES_FILTERING_ENABLED: bool = True
    RUN_RULES_FILTERING_TIMERANGE_MINUTES: int = 120
    RUN_RULES_FILTERING_MAX_MEMORY: int = 4_000_000_000  # 4GB
    RUN_RULES_MAX_RETRIES: int = (
        60  # max retries for run rules, process for 60 minutes before moving on
    )
    RUN_RULES_APPLY_TIMEOUT_SEC: int = 20 * 60  # 20 minutes
    RUN_RULES_LOCK_TIMEOUT_SEC: int = 10 * 60  # 10 minutes
    RUN_RULES_LOCK_RENEWAL_SEC: int = 30  # 30 seconds
    RUN_RULES_START_TIME_HOUR_DELTA: int = 24  # 1 day
    RUN_RULES_TARGET_CLICKHOUSE_CLIENT: str = "internal_analytics_slow"
    RUN_RULES_CODE_EXECUTE_BATCH_SIZE: int = 200
    RUN_RULES_CODE_EXECUTE_CONCURRENCY: int = 10
    MAX_PLAYGROUND_SYNC_EVALS: int = 10
    PLAYGROUND_STREAM_EVALUATOR_BATCH_SIZE: int = 5

    STATS_CACHE_TTL_SEC: int = 60 * 30  # 30 minutes
    STATS_MAX_RANGE_MINUTES: int = 60 * 24 * 14 + 15  # 14 days + 15 min
    STATS_USE_SESSION_AGGREGATION: bool = False
    STATS_USE_SORTED_SESSION_AGGREGATION: bool = False
    FF_USE_APPROX_STATS: bool = False
    STATS_AGGREGATION_TENANT_IDS: list[str] = []
    FF_USE_TOPK_AGGREGATED_MERGE_TREE: bool = False
    TOPK_AGGREGATED_MERGE_TREE_PROD_TENANT_IDS: list[str] = []

    CHARTS_CACHE_TTL_SEC: int = 60
    CHARTS_MAX_SERIES_WS: int = 10  # Max series per chart for workspace-scoped charts
    CHARTS_MAX_SERIES_ORG: int = 20  # Max series per chart for org-scoped charts
    CUSTOM_CHART_MAX_POINTS: int = 8640  # Max points per custom chart

    USER_DEFINED_USAGE_LIMIT_REJECT_EXPIRY_SEC: int = 60
    RATE_LIMIT_REJECT_EXPIRY_SEC: int = 60

    # Trace Prefetch
    TRACE_PREFETCH_ENABLED_TENANT_IDS: list[str] = []

    # Alerts matching settings
    METRICS_RETENTION_MINUTES: int = 5  # how long to keep a minute's worth of raw metrics for an alert rule in redis for aggregation
    AGGREGATE_ALERT_RULE_DELAY_MINUTES: int = 2  # wait time before aggregating an alert rule's minute worth of metrics and checking if an alert should fire
    AGGREGATE_ALERT_RULE_LOCK_TIMEOUT_SECONDS: int = (
        60  # timeout for the lock on an alert rule's minute worth of metrics
    )
    ALERTS_CACHE_TTL_SEC: int = 60 * 10  # 10 minutes
    TENANTS_ALERTS_FEATURE_ENABLED: list[str] = []
    LOGGED_ALERT_TENANTS: list[str] = []

    ENABLE_EVALUATE_EXPIRED_EXPERIMENTS: bool = True

    @field_validator("FF_PAYMENT_ENABLED")
    @classmethod
    def validate_ff_payment_enabled(cls, v: bool, info: ValidationInfo) -> bool:
        if v:
            if not info.data.get("FF_USE_PAID_PLANS"):
                for key in [
                    "METRONOME_DEV_LEGACY_PLAN_ID",
                    "METRONOME_PLUS_LEGACY_PLAN_ID",
                    "METRONOME_ENTERPRISE_LEGACY_PLAN_ID",
                    "METRONOME_API_KEY",
                    "STRIPE_SECRET_KEY",
                ]:
                    if not info.data.get(key):
                        raise ValueError(
                            f"FF_PAYMENT_ENABLED is set to True but {key} is not set"
                        )
            else:
                for key in [
                    "METRONOME_API_KEY",
                    "STRIPE_SECRET_KEY",
                    "METRONOME_DEV_PLAN_ID",
                    "METRONOME_PLUS_PLAN_ID",
                    "METRONOME_FREE_PLAN_ID",
                ]:
                    if not info.data.get(key):
                        raise ValueError(
                            f"FF_PAYMENT_ENABLED is set to True but {key} is not set"
                        )
        return v

    BLOB_STORAGE_ENGINE: str = "S3"
    S3_ACCESS_KEY: str | None = None
    S3_ACCESS_KEY_SECRET: str | None = None
    S3_API_URL: str | None = None
    S3_PROFILE: str | None = None
    AWS_REGION: str | None = "us-east-1"
    S3_PRESIGNED_URL_VALIDITY_TIME_SECONDS: int = 7200  # 2 hours
    S3_BUCKET_NAME: str = "static-assets"
    S3_SINGLE_REGION_BUCKET_NAME: str = ""
    FF_SINGLE_REGION_BUCKET_ENABLED_TENANTS: list[str] = []
    S3_TRACE_TIER_PREFIX_MAP: dict[str, str] = {
        "longlived": "ttl_l",
        "shortlived": "ttl_s",
    }
    S3_MAX_CONNECTIONS: int = 5000
    S3_KEEPALIVE: bool = True
    S3_CONNECT_TIMEOUT: float = 2.0
    S3_READ_TIMEOUT: float = 15.0
    BLOB_STORAGE_FETCH_SEMAPHORE: int = 100
    CHARTS_STATS_SEMAPHORE: int = 10
    FF_S3_STORAGE_ENABLED: bool = False
    FF_BLOB_STORAGE_ENABLED: bool = False
    FF_MIN_S3_STORAGE_SIZE_KB: int = 0  # keep default at 0 for self hosted + hybrid (Deprecated - use FF_MIN_BLOB_STORAGE_SIZE_KB instead)
    MIN_BLOB_STORAGE_SIZE_KB: int = 0  # keep default at 0 for self hosted and hybrid
    MAX_EXTRA_KEY_LENGTH: int = 500  # 500 chars
    MAX_EXTRA_KEYS: int = 250  # 250 keys
    FF_S3_URL_STORAGE_ENABLED: bool = False
    FF_CH_SEARCH_ENABLED: bool = True
    KV_SEARCH_MAX_KEYS: int = 100
    KV_SEARCH_MAX_LENGTH: int = 250
    KV_SEARCH_MAX_ARRAY_INDEXED: int = 10
    RULES_TRIGGER_SEMAPHORE: int = 10
    PLAYGROUND_BYPASS_AUTH_ENABLED: bool = False

    FETCH_RUNS_OPTIMIZED_TENANTS: list[str] = []
    FETCH_RUNS_API_MAX_THREADS: int = 8
    FF_USE_PK_ORDER_BY: bool = False

    FF_USE_MIN_MAX_TIME_FILTERED_QUERY: bool = False

    @field_validator("FF_BLOB_STORAGE_ENABLED")
    @classmethod
    def respect_s3_storage_enabled(cls, v: bool, info: ValidationInfo) -> bool:
        if info.data.get("FF_S3_STORAGE_ENABLED") and not v:
            return True
        return v

    @field_validator("MIN_BLOB_STORAGE_SIZE_KB")
    @classmethod
    def respect_s3_min_storage_size(cls, v: int, info: ValidationInfo) -> int:
        if info.data.get("FF_MIN_S3_STORAGE_SIZE_KB") and not v:
            return info.data.get("FF_MIN_S3_STORAGE_SIZE_KB")
        return v

    @field_validator("FF_S3_URL_STORAGE_ENABLED")
    @classmethod
    def validate_ff_s3_url_storage_enabled(cls, v: bool, info: ValidationInfo) -> bool:
        if v:
            if info.data.get("BLOB_STORAGE_ENGINE") == "S3" and not info.data.get(
                "S3_API_URL"
            ):
                raise ValueError(
                    "S3_API_URL is required when FF_S3_URL_STORAGE_ENABLED is True and BLOB_STORAGE_ENGINE is S3"
                )
        return v

    @field_validator("QUICKWIT_INDEXING_ENABLED_ALL")
    @classmethod
    def validate_quickwit_indexing_enabled(cls, v: bool, info: ValidationInfo) -> bool:
        if v:
            if not info.data.get("QUICKWIT_INDEXING_URL"):
                raise ValueError(
                    "QUICKWIT_INDEXING_URL is required when QUICKWIT_INDEXING_ENABLED_ALL is True"
                )
        return v

    @field_validator("QUICKWIT_SEARCH_ENABLED")
    @classmethod
    def validate_quickwit_search_enabled(cls, v: bool, info: ValidationInfo) -> bool:
        if v:
            if not info.data.get("QUICKWIT_SEARCH_URL"):
                raise ValueError(
                    "QUICKWIT_SEARCH_URL is required when QUICKWIT_SEARCH_ENABLED is True"
                )
        return v

    @field_validator("QUICKWIT_INDEXING_KAFKA_ENABLED")
    @classmethod
    def validate_quickwit_indexing_kafka_enabled(
        cls, v: bool, info: ValidationInfo
    ) -> bool:
        if v:
            if not info.data.get("QUICKWIT_INDEXING_KAFKA_RUNS_TOPIC"):
                raise ValueError(
                    "QUICKWIT_INDEXING_KAFKA_RUNS_TOPIC is required when QUICKWIT_INDEXING_KAFKA_ENABLED is True"
                )
        return v

    # This is only used by alembic migrations
    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info: ValidationInfo) -> Any:
        if isinstance(v, str):
            return v
        database_uri = info.data.get("POSTGRES_DATABASE_URI", "")
        stripped_uri = database_uri.replace("postgresql://", "").replace(
            "postgres://", ""
        )
        return f"postgresql+psycopg://{stripped_uri}"

    @field_validator("ASYNCPG_DATABASE_URI", mode="before")
    @classmethod
    def assemble_asyncpg_db_connection(
        cls, v: Optional[str], info: ValidationInfo
    ) -> Any:
        # Override with PGBOUNCER_DATABASE_URI if set
        if info.data.get("PGBOUNCER_DATABASE_URI"):
            database_uri = info.data.get("PGBOUNCER_DATABASE_URI")
            stripped_uri = database_uri.replace("postgresql://", "").replace(
                "postgres://", ""
            )
            return f"postgresql://{stripped_uri}"
        elif isinstance(v, str):
            return v
        database_uri = info.data.get("POSTGRES_DATABASE_URI", "")
        stripped_uri = database_uri.replace("postgresql://", "").replace(
            "postgres://", ""
        )
        return f"postgresql://{stripped_uri}"

    # Settings shared between the queue and host backend
    PRIVATE_GITHUB_BUILD_TRIGGER_ID: str = ""
    GITHUB_APP_PEM_SECRET_NAME: str = "hosted-langserve-github-app-pem"
    # TODO: use full trigger name instead of ID + location and move to deployments
    PRIVATE_GITHUB_BUILD_TRIGGER_LOCATION: str = "us-central1"
    GITHUB_APP_ID: str = ""

    FF_ORG_CREATION_DISABLED: bool = False
    FF_PERSONAL_ORGS_DISABLED: bool = False

    SERVED_DATASET_ENABLED: bool = False

    ELASTIC_URL: str | None = None
    ELASTIC_API_KEY: str | None = None
    ELASTIC_CLOUD_ID: str | None = None
    ELASTIC_TIMEOUT: int = 60
    ELASTIC_MAX_RETRIES: int = 3

    @field_validator("SERVED_DATASET_ENABLED")
    @classmethod
    def validate_served_dataset_elastic(cls, v: bool, info: ValidationInfo) -> bool:
        if v:
            if info.data.get("SERVED_DATASET_ENABLED"):
                using_url = info.data.get("ELASTIC_URL") is not None
                using_cloud_id = (
                    info.data.get("ELASTIC_CLOUD_ID") is not None
                    and info.data.get("ELASTIC_API_KEY") is not None
                )
                if using_url and using_cloud_id:
                    raise ValueError(
                        "Both ELASTIC_URL and ELASTIC_CLOUD_ID + ELASTIC_API_KEY are set"
                    )

        return v

    SYNC_SERVED_DATASETS_CRON_ENABLED: bool = True
    SYNC_SERVED_DATASETS_CRON: str = "*/5 * * * *"
    SYNC_SERVED_DATASETS_JOB_TIMEOUT_SEC: int = 4 * 60

    # Running over dataset from playground - note that batch size can be up to 30x as large if repetitions are used
    PLAYGROUND_RUN_OVER_DATASET_EXAMPLE_BATCH_SIZE: int = 20
    PLAYGROUND_RUN_OVER_DATASET_NUM_STREAMING: int = 20
    MAX_RUNTIME_OF_TRIGGER_RUN_RULES_PLAYGROUND_SEC: int = 300
    MIN_SECS_BETWEEN_TRIGGER_RUN_RULES_PLAYGROUND: int = 10
    POLL_INTERVAL_FOR_RUNS_PERSISTED_PLAYGROUND: int = 1

    RUN_DATASET_ID_CACHE_TTL: int = 60 * 30  # 30 mins

    TRIGGER_RUN_RULES_LOCK_PREFIX: str = "trigger_run_rules_lock_"
    TRIGGER_RUN_RULES_LOCK_TIMEOUT_SEC: int = 10
    RUN_RULES_TRIGGER_LIMIT: int = 10

    ENABLE_SELF_HOSTED_BILLING_CUSTOMER_CRON: bool = False
    SELF_HOSTED_BILLING_CUSTOMER_CRON: str = "*/5 * * * *"
    SELF_HOSTED_BILLING_CUSTOMER_JOB_TIMEOUT_SEC: int = 4 * 60

    FF_PYTHON_PLAYGROUND_PCT: float = 0

    HOST_WORKER_HEARTBEAT_CRON_ENABLED: bool = True
    HOST_WORKER_RECONCILIATION_CRON_ENABLED: bool = True
    HOST_WORKER_DELETE_UNUSED_PROJECTS_CRON_ENABLED: bool = True
    HOST_WORKER_TENANT_ID: UUID | None = None
    HOST_WORKER_EXTERNAL_ENABLED: bool = False
    HOST_WORKER_LANGSMITH_API_KEY: str | None = None

    # ~25 Redis commands per run, so 40 * 25 = 1000 length pipeline
    REDIS_TRANSACTION_RUNS_CHUNK_SIZE: int = 40

    DOWNLOAD_EXPERIMENT_RESULTS_LIMIT: int = 1000

    TOKEN_STATS_START_TIME_BUFFER_MS: int = 1000

    # Prompt Webhooks
    MAX_CONCURRENCY_PROMPT_WEBHOOKS: int = 25


shared_settings = SharedSettings(  # type: ignore
    LANGCHAIN_ENV=LANGCHAIN_ENV,
    _env_file=ENV_FILE_PATH,  # type: ignore
    _secrets_dir=SECRETS_DIR_PATH,  # type: ignore
)
