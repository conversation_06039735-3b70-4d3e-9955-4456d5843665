from pydantic_settings import BaseSettings, SettingsConfigDict

from lc_config.env import ENV_FILE_PATH


class OrganizationConfig(BaseSettings):
    """Organization level configuration. May include any field that exists in tenant config and additional fields."""

    # BaseSettings default fails on extra fields, so we need to set it to ignore.
    # Also allow overriding defaults with DEFAULT_ORG_FEATURE_X env vars.
    model_config = SettingsConfigDict(
        env_file=ENV_FILE_PATH, extra="ignore", env_prefix="DEFAULT_ORG_FEATURE_"
    )

    max_identities: int = 5
    """
    The maximum number of identities allowed in this org.
    """

    max_workspaces: int = 1
    """
    The maximum number of workspaces allowed in this org. -1 means no limit.
    """

    can_use_rbac: bool = False
    """
    Whether this org can create new users using roles (such as read only)
    based on their plan.
    """

    can_add_seats: bool = True
    """
    Whether this org can invite new users based on their plan.
    """

    startup_plan_approval_date: str | None = None
    """
    The date when the org was approved for the startup plan.
    This is in YYYY-MM-DD format.
    """

    partner_plan_approval_date: str | None = None
    """
    DEPRECATED

    The date when the org was approved for the partner plan.
    This is in YYYY-MM-DD format.
    """

    premier_plan_approval_date: str | None = None
    """
    The date when the org was approved for the partner plan.
    This is in YYYY-MM-DD format.
    """

    can_disable_public_sharing: bool = False
    """
    Whether this org can disable public sharing of resources like traces, datasets, and prompts.
    """

    can_serve_datasets: bool = False
    """
    Whether datasets can be served for few shot prompting.
    """

    can_use_langgraph_cloud: bool = False
    """
    Whether this org can use LangGraph Platform.
    """

    max_langgraph_cloud_deployments: int = 3
    """
    The maximum number of LangGraph Platform deployments allowed for this org.
    """

    max_free_langgraph_cloud_deployments: int = 0
    """
    The maximum number of free LangGraph Platform deployments allowed for this org.
    """

    can_use_saml_sso: bool = False
    """
    Whether this org can configure SAML SSO.
    """

    can_use_bulk_export: bool = False
    """
    Whether this org can create bulk exports.
    """

    use_python_playground_service: bool = False
    """
    Whether this org can use the new python playground service.
    """

    show_updated_sidenav: bool = False
    """
    Show updated side nav to users in this org.
    """

    show_updated_resource_tags: bool = False
    """
    Show updated resource tags to users in this org.
    """

    kv_dataset_message_support: bool = True
    """
    Whether to use the new messages experience for KV datasets.
    """

    show_playground_prompt_canvas: bool = False
    """
    Whether to show the playground prompt canvas.
    """

    allow_custom_iframes: bool = False
    """
    Whether to allow custom iframes for trace rendering.
    """

    enable_langgraph_pricing: bool = False
    """
    Whether to show Agent marketplace in Langgraph tab.
    """

    enable_thread_view_playground: bool = False
    """
    Whether to allow opening top-level thread views in the playground.
    """

    enable_org_usage_charts: bool = False
    """
    Whether to enable organization usage charts.
    """

    enable_select_all_traces: bool = False
    """
    Whether to enable the "select all traces" button in the runs table.
    """

    use_exact_search_for_prompts: bool = False
    """
    Whether to use exact search for prompts.
    """

    langgraph_deploy_own_cloud_enabled: bool = False
    """
    Whether the org can deploy LangGraph cloud to their own cloud.
    """

    prompt_optimization_jobs_enabled: bool = False
    """
    Whether the org can use the prompt optimization jobs feature.
    """

    enable_k8s_vanilla_platform: bool = False
    """
    Whether the org can use the vanilla k8s platform for langgraph cloud.
    """

    demo_lgp_new_graph_enabled: bool = False
    """
    Whether demo page for creating new graphs is enabled.
    """

    datadog_rum_session_sample_rate: int = 20
    """
    The sample rate for Datadog RUM sessions.
    """

    langgraph_remote_reconciler_enabled: bool = False
    """
    Whether the org has a remote reconciler deployed.
    """

    langsmith_alerts_poc_enabled: bool = True
    """
    Whether the alerts POC is enabled.
    """

    tenant_skip_topk_facets: bool = False
    """
    Whether the tenant should skip topk facets.
    """

    lgp_templates_enabled: bool = False
    """
    Whether the LGP templates are enabled.
    """

    langsmith_alerts_legacy_poc_enabled: bool = False
    """
    Whether the alerts legacy POC is enabled.
    """

    langsmith_experimental_search_enabled: bool = False
    """
    Whether the experimental search is enabled.
    """

    enable_align_evaluators: bool = False
    """
    Whether the align evaluators flow is enabled.
    """

    max_prompt_webhooks: int = 1
    """
    The maximum number of prompt webhooks allowed for this org.
    """

    playground_evaluator_strategy: str | None = "sync"
    """
    The strategy for running evaluators in the playground.
    Options are "cron", "background", or "sync".
    """

    enable_monthly_usage_charts: bool = False
    """
    Whether monthly usage charts should be shown
    """

    enable_studio_experiments: bool = False
    """
    Whether experiments in studio should be shown.
    """


class TenantConfig(BaseSettings):
    """
    While this is called TenantConfig, when it is used for checking config values, it is the fully resolved config
    with organization config values set as well (along with Metronome config values).

    Defaults set here are the defaults for tenants.config in the database.
    Organization-specific config should be None by default here and set in the OrganizationConfig class.
    """

    # BaseSettings default fails on extra fields, so we need to set it to ignore.
    # Also allow overriding defaults with DEFAULT_FEATURE_X env vars.
    model_config = SettingsConfigDict(
        env_file=ENV_FILE_PATH, extra="ignore", env_prefix="DEFAULT_FEATURE_"
    )

    max_identities: int | None = None
    """
    The maximum number of identities allowed in this tenant.
    """

    max_hourly_tracing_requests: int = 105_000
    """
    The maximum number of tracing requests allowed per hour in this tenant.

    2 reqs per run (POST + PATCH) + some buffer for retries means this is the right
    number for 50k runs per hour.
    """

    max_hourly_tracing_bytes: int = 2_000_000_000
    """
    The maximum total size (in bytes) of tracing payloads per hour sent by this tenant.
    """

    max_monthly_total_unique_traces: int = 10_000_000
    """
    The maximum total number of root runs per month in this tenant. This is the metric
    that we will actually bill on.
    """

    max_events_ingested_per_minute: int = 20_000
    """
    The maximum number of ingestion events allowed per minute in this tenant.

    This is the measured the same way as the 'tracing requests' limit for max_hourly_tracing_requests.
    """

    max_run_rules: int = 100
    """
    The maximum number of run rules allowed for this tenant.
    """

    can_use_rbac: bool | None = None
    """
    Whether this tenant can create new users using roles (such as read only)
    based on their plan.
    """

    can_add_seats: bool | None = None
    """
    Whether this tenant can invite new users based on their plan.
    """

    startup_plan_approval_date: str | None = None
    """
    The date when the tenant was approved for the startup plan.
    This is in YYYY-MM-DD format.
    """

    partner_plan_approval_date: str | None = None
    """
    DEPRECATED

    The date when the tenant was approved for the premier plan.
    This is in YYYY-MM-DD format.
    """

    premier_plan_approval_date: str | None = None
    """
    The date when the tenant was approved for the premier plan.
    This is in YYYY-MM-DD format.
    """

    organization_config: OrganizationConfig | None = None
    """
    The corresponding organization's config, for accessing organization-specific settings.
    """

    datadog_rum_session_sample_rate: int = 20
    """
    The sample rate for Datadog RUM sessions.
    """
